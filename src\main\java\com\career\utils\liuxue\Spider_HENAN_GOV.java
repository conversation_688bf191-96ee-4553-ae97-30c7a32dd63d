package com.career.utils.liuxue;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

public class Spider_HENAN_GOV {

	// 河南人事考试网抓取网页
	public static StringBuffer rlsbj_cq_gov(String URL, int pageNumber) {
		Map<String, String> headers = new HashMap<>();
		StringBuffer SQL = new StringBuffer();

		System.out.println(URL);

		
		headers.put("cookie", "yfx_c_g_u_id_10000026=_ck24091817592417838813705671731; yfx_c_g_u_id_10000001=_ck24092022160914298795130080946; yfx_f_l_v_t_10000001=f_t_1726841769419__r_t_1726841769419__v_t_1726841769419__r_c_0; yfx_key_10000001=; yfx_c_g_u_id_10000059=_ck24092022410115452119633554812; yfx_f_l_v_t_10000059=f_t_1726843261541__r_t_1726843261541__v_t_1726843261541__r_c_0; yfx_key_10000026=; yfx_f_l_v_t_10000026=f_t_1726653564779__r_t_1728434866461__v_t_1728434866461__r_c_2; yfx_c_g_u_id_10000056=_ck24101913440519433967513769503; yfx_f_l_v_t_10000056=f_t_1729316645943__r_t_1729316645943__v_t_1729316645943__r_c_0; yfx_key_10000056=; Hm_lvt_2a9e164be5a6157303ad600d17321c4a=**********; HMACCOUNT=F9E89F99DE4FFF7E; Hm_lvt_ce11e99a3efa74e33f5ff7d68026cffe=**********; JSESSIONID=419E708BC31397C028CA3328B99E460E; Hm_lpvt_2a9e164be5a6157303ad600d17321c4a=**********; Hm_lpvt_ce11e99a3efa74e33f5ff7d68026cffe=**********");
		headers.put("host", "ywzl.hrss.henan.gov.cn");
		headers.put("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
		headers.put("Referrer Policy", "strict-origin-when-cross-origin");
		String resultPageList = HttpSendUtils2.get(URL, headers);
		//System.out.println(resultPageList);
		Document documentList = Jsoup.parse(resultPageList);

		Elements aiticleList = documentList.getElementsByClass("xin2zuo");

		Elements items = aiticleList.get(0).getElementsByTag("a");
		Elements trs = aiticleList.get(0).getElementsByTag("tr");

		for (int i = 0; i < trs.size(); i++) {
			Element trEl = trs.get(i);
			Elements tds = trEl.getElementsByTag("td");
			if(tds.size() < 3) {
				continue;
			}
			
			Element tdDate = tds.get(2);
			String date = tdDate.text();
			
			Element tdA = tds.get(1);
			Elements as = tdA.getElementsByTag("a");
			Element item = as.get(0);
			
			String title = item.text();
			String href = "https://ywzl.hrss.henan.gov.cn/" + item.attr("href");
			System.out.println(href);
			if (title.indexOf("2024") != -1 && title.indexOf("公示") != -1) {
				try {
					Document detailPage = Jsoup.parse(HttpSendUtils2.get(href, headers));
					Element ue_table = detailPage.getElementById("zwxinx");
	
					Elements tbody = ue_table.getElementsByTag("tbody");
					Elements tableTR = tbody.get(tbody.size() - 1).getElementsByTag("tr");
					
					int zeroIndex = 0;
					Elements tableTR0TD = tableTR.get(0).getElementsByTag("td");
					if(tableTR0TD.size() < 3) {
						zeroIndex++;
						tableTR0TD = tableTR.get(1).getElementsByTag("td");
					}
					if(tableTR0TD.size() < 3) {
						zeroIndex++;
						tableTR0TD = tableTR.get(2).getElementsByTag("td");
					}
					if(tableTR0TD.size() < 3) {
						zeroIndex++;
						tableTR0TD = tableTR.get(3).getElementsByTag("td");
					}
					
					
					Elements tableTDTitle = tableTR0TD;// 标题
					HashMap<Integer, String> titleMap = new HashMap<>();
	
					int indexYX = -1,indexDW = -1,indexXM = -1, indexXB = -1, indexXH = -1, indexXL = -1,indexZY = -1;
					
					for (int k = 0; k < tableTDTitle.size(); k++) {
						titleMap.put(k, tableTDTitle.get(k).text());
						System.out.println(tableTDTitle.get(k).text());
						if(tableTDTitle.get(k).text().indexOf("毕业院校") != -1 || tableTDTitle.get(k).text().indexOf("毕业学校") != -1) {
							indexYX = k;
						}
						if(tableTDTitle.get(k).text().indexOf("拟聘") != -1 || tableTDTitle.get(k).text().indexOf("岗位") != -1 || tableTDTitle.get(k).text().indexOf("报考") != -1 || tableTDTitle.get(k).text().indexOf("单位及岗位") != -1 || tableTDTitle.get(k).text().indexOf("招聘单位") != -1 || tableTDTitle.get(k).text().indexOf("招聘岗位") != -1) {
							indexDW = k;
						}
						if(tableTDTitle.get(k).text().indexOf("姓名") != -1) {
							indexXM = k;
						}
						if(tableTDTitle.get(k).text().indexOf("性别") != -1) {
							indexXB = k;
						}
						if(tableTDTitle.get(k).text().indexOf("学历") != -1 || tableTDTitle.get(k).text().indexOf("学历学位及专业") != -1) {
							indexXL = k;
						}

						if(tableTDTitle.get(k).text().indexOf("序") != -1) {
							indexXH = k;
						}
					}
					
					if(indexYX >= 0 && indexDW >= 0) {

					}else {
						System.out.println(href);
						continue;
					}
	
					String groupID = UUID.randomUUID().toString();
					for (int k = zeroIndex; k < tableTR.size(); k++) {
						Element tr = tableTR.get(k);
						Elements tableTD = tr.getElementsByTag("td");
						String oneRecord = UUID.randomUUID().toString();
						
						
						String xh = indexXH > 0 ? tableTD.get(indexXH).text() : null;
						String xm = indexXM > 0 ? tableTD.get(indexXM).text() : null;
						String xb = indexXB > 0 ? tableTD.get(indexXB).text() : null;
						String xl = indexXL > 0 ? tableTD.get(indexXL).text() : null;
	
						String yxzy = tableTD.get(indexYX).text();
						String dwgw = tableTD.get(indexDW).text();
						
						SQL.append(
								"insert into career_shiye_gov_data(onrecord_id, group_id, yxzy, dwgw, xl, xm, xb, xh, publish_dt, url_link, title, province) values('"+oneRecord+"','"
										+ groupID + "','" + yxzy + "','" + dwgw + "','"+xl+"','"+xm+"','"+xb+"','"+xh+"','" + date + "','"
										+ href + "','" + title + "','河南');\r\n");

						
					}
				}catch(Exception ex) {
					ex.printStackTrace();
					System.out.println("ERR: "+href);
				}
			}
		}
		return SQL;
	}

	public static void main(String[] args) {
		StringBuffer SQL = new StringBuffer();
		for (int i = 0; i < 91; ) {
			SQL.append(rlsbj_cq_gov("https://ywzl.hrss.henan.gov.cn/viewCmsCac.do?cacId=4aef1408279926e601279e541f3717aa&offset="+i+"&",i));
			i += 30;
		}

		writeTempFile(new File("F://就业报告//henan/PAGE_1015.txt"), SQL);

	}
	
	private static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
}
