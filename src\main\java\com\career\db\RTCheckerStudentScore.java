package com.career.db;

public class RTCheckerStudentScore {
	private String rt_pc;
    private String rt_options_xk;
    private String rt_wy;
    private int rt_score_chinese;
    private int rt_score_math;
    private int rt_score_foreign_language;
    private int rt_score_xk1;
    private int rt_score_xk2;
    private int rt_score_xk3;
    private int rt_total_score;
    private int rt_ranking;
    private int rt_score_ext;
    private String rt_score_ext_remark;
    
	public String getRt_pc() {
		return rt_pc;
	}
	public void setRt_pc(String rt_pc) {
		this.rt_pc = rt_pc;
	}
	public String getRt_options_xk() {
		return rt_options_xk;
	}
	public void setRt_options_xk(String rt_options_xk) {
		this.rt_options_xk = rt_options_xk;
	}
	public String getRt_wy() {
		return rt_wy;
	}
	public void setRt_wy(String rt_wy) {
		this.rt_wy = rt_wy;
	}
	public int getRt_score_chinese() {
		return rt_score_chinese;
	}
	public void setRt_score_chinese(int rt_score_chinese) {
		this.rt_score_chinese = rt_score_chinese;
	}
	public int getRt_score_math() {
		return rt_score_math;
	}
	public void setRt_score_math(int rt_score_math) {
		this.rt_score_math = rt_score_math;
	}
	public int getRt_score_foreign_language() {
		return rt_score_foreign_language;
	}
	public void setRt_score_foreign_language(int rt_score_foreign_language) {
		this.rt_score_foreign_language = rt_score_foreign_language;
	}
	public int getRt_score_xk1() {
		return rt_score_xk1;
	}
	public void setRt_score_xk1(int rt_score_xk1) {
		this.rt_score_xk1 = rt_score_xk1;
	}
	public int getRt_score_xk2() {
		return rt_score_xk2;
	}
	public void setRt_score_xk2(int rt_score_xk2) {
		this.rt_score_xk2 = rt_score_xk2;
	}
	public int getRt_score_xk3() {
		return rt_score_xk3;
	}
	public void setRt_score_xk3(int rt_score_xk3) {
		this.rt_score_xk3 = rt_score_xk3;
	}
	public int getRt_total_score() {
		return rt_total_score;
	}
	public void setRt_total_score(int rt_total_score) {
		this.rt_total_score = rt_total_score;
	}
	public int getRt_ranking() {
		return rt_ranking;
	}
	public void setRt_ranking(int rt_ranking) {
		this.rt_ranking = rt_ranking;
	}
	public int getRt_score_ext() {
		return rt_score_ext;
	}
	public void setRt_score_ext(int rt_score_ext) {
		this.rt_score_ext = rt_score_ext;
	}
	public String getRt_score_ext_remark() {
		return rt_score_ext_remark;
	}
	public void setRt_score_ext_remark(String rt_score_ext_remark) {
		this.rt_score_ext_remark = rt_score_ext_remark;
	}
    
    
}
