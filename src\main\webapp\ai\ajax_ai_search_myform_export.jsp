<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="com.career.utils.*,com.career.db.*,com.career.utils.excel.*,com.career.utils.excel.model.*,java.util.*,java.io.*"%>
<%@include file="/WEB-INF/include/_session_ai.jsp"%>

<%
AiCard aiCard = (AiCard)session.getAttribute(ZyzdCache.SES_KEY_AI_BASE_CARD); 
AiJDBC aiJDBC = new AiJDBC();
ZyzdJDBC zyzdJDBC = new ZyzdJDBC();
LhyJDBC lhyJDBC = new LhyJDBC();

// 接收参数
String batch_id = Tools.trim(request.getParameter("batch_id"));
String pc = Tools.trim(request.getParameter("pc"));

// 添加详细日志
Tools.println(" === 志愿表导出开始 ===");
Tools.println(" === 接收到的参数 - batch_id: " + batch_id);
Tools.println(" === 接收到的参数 - pc: " + pc);

if(Tools.isEmpty(batch_id)){
    Tools.println(" === 错误：批次ID为空");
    response.setContentType("application/json; charset=UTF-8");
    out.print("{\"success\":false,\"message\":\"批次ID不能为空\"}");
    return;
}

if(Tools.isEmpty(pc)){
    Tools.println(" === 错误：批次信息为空");
    response.setContentType("application/json; charset=UTF-8");
    out.print("{\"success\":false,\"message\":\"批次信息不能为空\"}");
    return;
}

ZyzdProvinceConfig provinceConfig = ZyzdCache.getProvinceConfig(aiCard.getC_prov());
String provinceTableName = provinceConfig.getP_table_code();
String provinceName = provinceConfig.getP_name();
String xkCode = XKCombineUtils.getXKCodeByStudentSelection(aiCard.getC_xk());
int LATEST_JH_YEAR = provinceConfig.getLatest_year_jh();

// 解析批次信息
Tools.println(" === 开始解析批次信息，原始pc: " + pc);

// 先检查是否包含分隔符
if(!pc.contains("__")){
    Tools.println(" === 错误：批次信息不包含分隔符'__'");
    response.setContentType("application/json; charset=UTF-8");
    out.print("{\"success\":false,\"message\":\"批次信息格式错误，缺少分隔符'__'，实际接收到：" + pc + "\"}");
    return;
}

// 使用直接的字符串分割
String[] pc_parts = pc.split("__");
Tools.println(" === 直接分割结果数组大小: " + pc_parts.length);
for(int i = 0; i < pc_parts.length; i++){
    Tools.println(" === 直接分割结果[" + i + "]: '" + pc_parts[i] + "'");
}

if(pc_parts.length != 2){
    Tools.println(" === 错误：直接分割格式错误，期望2个部分，实际得到" + pc_parts.length + "个部分");
    response.setContentType("application/json; charset=UTF-8");
    out.print("{\"success\":false,\"message\":\"批次信息格式错误，期望格式：pc_code__pc_name，实际接收到：" + pc + "，分割后数组大小：" + pc_parts.length + "\"}");
    return;
}

String pc_code = pc_parts[0].trim();
String pc_name = pc_parts[1].trim();

Tools.println(" === 解析成功 - pc_code: '" + pc_code + "'");
Tools.println(" === 解析成功 - pc_name: '" + pc_name + "'");

// 验证解析结果
if(Tools.isEmpty(pc_code) || Tools.isEmpty(pc_name)){
    Tools.println(" === 错误：解析后的pc_code或pc_name为空");
    response.setContentType("application/json; charset=UTF-8");
    out.print("{\"success\":false,\"message\":\"批次信息解析后为空，pc_code: '" + pc_code + "', pc_name: '" + pc_name + "'\"}");
    return;
}

// 获取用户已填报的志愿列表
List<AiTbForm> formList = null;
if(provinceConfig.getForm_type() == 2){
    formList = aiJDBC.getAllMakerFormFor96ByBatchId(provinceTableName, batch_id);
}else{
    formList = aiJDBC.getAllMakerFormByBatchId(provinceTableName, batch_id);
}

if(formList == null || formList.size() == 0){
    response.setContentType("application/json; charset=UTF-8");
    out.print("{\"success\":false,\"message\":\"未找到已填报的志愿数据\"}");
    return;
}

// 根据已填报的志愿查询对应的计划数据
List<JHBean> allJhDataList = new ArrayList<>();
Set<String> processedKeys = new HashSet<>(); // 防重复

Tools.println(" === 开始处理 " + formList.size() + " 个志愿");

for(int i = 0; i < formList.size(); i++){
    AiTbForm form = formList.get(i);
    Tools.println(" === 处理第 " + (i+1) + " 个志愿");
    Tools.println(" === 志愿信息 - 院校: " + form.getYxmc() + "(" + form.getYxdm() + ")");
    Tools.println(" === 志愿信息 - 专业: " + form.getZymc() + "(" + form.getZydm() + ")");
    Tools.println(" === 志愿信息 - 专业组: " + form.getZyz());
    Tools.println(" === 志愿信息 - 专业原名: " + form.getZymc_org());
    
    try {
        // 构建唯一键，防止重复查询相同的院校专业组合
        String uniqueKey = form.getYxdm() + "_" + form.getZydm() + "_" + form.getZyz() + "_" + pc_code;
        Tools.println(" === 唯一键: " + uniqueKey);
        
        if(processedKeys.contains(uniqueKey)){
            Tools.println(" === 跳过重复的院校专业组合");
            continue;
        }
        processedKeys.add(uniqueKey);
        
        // 使用LhyJDBC新方法查询
        Tools.println(" === 调用LhyJDBC.searchJhDataByFormInfo方法");
        Tools.println(" === 查询参数: jhYear=" + LATEST_JH_YEAR + ", sfCode=" + provinceTableName + 
                      ", xkCode=" + xkCode + ", pcCode=" + pc_code + ", pcName=" + pc_name + 
                      ", yxdm=" + form.getYxdm() + ", zydm=" + form.getZydm() + ", zyz=" + form.getZyz());
        
        List<JHBean> jhDataList = lhyJDBC.searchJhDataByFormInfo(LATEST_JH_YEAR, provinceTableName, xkCode,
                pc_code, pc_name, form.getYxdm(), 
                form.getZydm(), form.getZyz());
        
        int matchCount = jhDataList.size();
        Tools.println(" === 第 " + (i+1) + " 个志愿匹配到 " + matchCount + " 条计划数据");
        
        for(JHBean jhBean : jhDataList){
            allJhDataList.add(jhBean);
            Tools.println(" === 添加匹配记录: " + jhBean.getYxmc() + " - " + jhBean.getZymc());
        }
    } catch (Exception e) {
        Tools.println("查询第 " + (i+1) + " 个志愿对应计划数据异常: " + e.getMessage());
        e.printStackTrace();
        // 继续处理下一个志愿，不中断整个流程
    }
}

Tools.println(" === 总共匹配到 " + allJhDataList.size() + " 条计划数据");

// 检查数据量限制
if(allJhDataList.size() > 1000){
    response.setContentType("application/json; charset=UTF-8");
    out.print("{\"success\":false,\"message\":\"查询结果超过1000条记录，不支持导出。当前结果：" + allJhDataList.size() + "条\"}");
    return;
}

if(allJhDataList.size() == 0){
    response.setContentType("application/json; charset=UTF-8");
    out.print("{\"success\":false,\"message\":\"未找到对应的计划数据，请检查已填报的志愿信息\"}");
    return;
}

// 转换为Excel导出格式
List<JHBeanForm> exportList = new ArrayList<>();
for(JHBean bean : allJhDataList){
    JHBeanForm form = new JHBeanForm();
    
    // 基本信息
    form.setId(bean.getId());
    form.setSf(bean.getSf());
    form.setNf(String.valueOf(bean.getNf()));
    form.setYxdm(bean.getYxdm());
    form.setYxmc(bean.getYxmc());
    form.setYxmc_org(bean.getYxmc_org());
    form.setZydm(bean.getZydm());
    form.setZymc(bean.getZymc());
    form.setZymc_org(bean.getZymc_org());
    form.setZyz(bean.getZyz());
    form.setZyml(bean.getZyml());
    form.setZnzy(bean.getZnzy());
    form.setZybz(bean.getZybz());
    form.setPc(bean.getPc());
    form.setPc_code(bean.getPc_code());
    form.setXk(bean.getXk());
    form.setZx(bean.getZx());
    form.setLqpc(bean.getLqpc());
    
    // 当前年度数据
    form.setJhs(bean.getJhs());
    form.setFee(bean.getFee());
    form.setXz(bean.getXz());
    form.setZdf(bean.getZdf());
    form.setZdfwc(bean.getZdfwc());
    
    // A年数据
    form.setJhs_a(bean.getJhs_a());
    form.setXz_a(bean.getXz_a());
    form.setFee_a(bean.getFee_a());
    form.setZdf_a(bean.getZdf_a());
    form.setZdfwc_a(bean.getZdfwc_a());
    form.setZgf_a(bean.getZgf_a());
    form.setZgfwc_a(bean.getZgfwc_a());
    form.setPjf_a(bean.getPjf_a());
    form.setPjfwc_a(bean.getPjfwc_a());
    
    // B年数据
    form.setJhs_b(bean.getJhs_b());
    form.setZdf_b(bean.getZdf_b());
    form.setZdfwc_b(bean.getZdfwc_b());
    form.setZgf_b(bean.getZgf_b());
    form.setZgfwc_b(bean.getZgfwc_b());
    form.setPjf_b(bean.getPjf_b());
    form.setPjfwc_b(bean.getPjfwc_b());
    
    // C年数据
    form.setJhs_c(bean.getJhs_c());
    form.setZdf_c(bean.getZdf_c());
    form.setZdfwc_c(bean.getZdfwc_c());
    form.setZgf_c(bean.getZgf_c());
    form.setZgfwc_c(bean.getZgfwc_c());
    form.setPjf_c(bean.getPjf_c());
    form.setPjfwc_c(bean.getPjfwc_c());
    
    // 趋势数据
    form.setQsf_a(bean.getQsf_a());
    form.setQsf_b(bean.getQsf_b());
    form.setQsf_c(bean.getQsf_c());
    form.setQsf(bean.getQsf());
    
    // 院校详细信息
    form.setInd_nature(bean.getInd_nature());
    form.setInd_catg(bean.getInd_catg());
    form.setYxsf(bean.getYxsf());
    form.setYxcs(bean.getYxcs());
    form.setYx_tags(bean.getYx_tags());
    form.setYx_tags_all(bean.getYx_tags_all());
    form.setCnt_company(bean.getCnt_company());
    form.setCnt_employ(bean.getCnt_employ());
    form.setCnt_grad(bean.getCnt_grad());
    form.setIs_hz(bean.getIs_hz());
    form.setIs_first(bean.getIs_first());
    form.setYcwc(bean.getYcwc());
    
    exportList.add(form);
}

try {
    // 创建Excel导出器
    ExcelExporter<JHBeanForm> exporter = new ExcelExporter<>(JHBeanForm.class);
    exporter.setFileHeader(provinceName + LATEST_JH_YEAR + "年志愿填报大数据(智能版)  数据由 @全国高考云数据平台收集整理");
    
    // 生成文件名：我的志愿表_批次名_时间戳.xlsx
    String timestamp = new java.text.SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
    String fileName = "我的志愿表_" + pc_name + "_" + timestamp + ".xlsx";
    
    // 导出到文件
    String servletContextPath = getServletContext().getRealPath("/");
    File outputFile = new File(servletContextPath, fileName);
    
    Tools.println(" === 志愿表计划数据导出文件: " + outputFile.getPath());
    
    try (FileOutputStream fos = new FileOutputStream(outputFile)) {
        exporter.export(exportList, fos);
    }
    
    // 计算文件大小
    long fileSize = outputFile.length();
    String formattedFileSize = fileSize < 1024 ? fileSize + "B" : 
                              fileSize < 1024*1024 ? String.format("%.1fKB", fileSize/1024.0) :
                              String.format("%.1fMB", fileSize/(1024.0*1024.0));
    
    // 生成时间
    String generateTime = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
    
    Tools.println(" === 文件大小: " + formattedFileSize);
    Tools.println(" === 记录数量: " + exportList.size());
    Tools.println(" === 志愿数量: " + formList.size());
    Tools.println(" === 生成时间: " + generateTime);
    Tools.println(" === 下载URL: " + request.getContextPath() + "/" + fileName);
    
    // 返回JSON响应
    response.setContentType("application/json; charset=UTF-8");
    
    // 构建JSON响应
    StringBuilder jsonResponse = new StringBuilder();
    jsonResponse.append("{");
    jsonResponse.append("\"success\":true,");
    jsonResponse.append("\"message\":\"导出成功\",");
    jsonResponse.append("\"fileName\":\"").append(fileName).append("\",");
    jsonResponse.append("\"fileSize\":\"").append(formattedFileSize).append("\",");
    jsonResponse.append("\"recordCount\":").append(exportList.size()).append(",");
    jsonResponse.append("\"formCount\":").append(formList.size()).append(",");
    jsonResponse.append("\"generateTime\":\"").append(generateTime).append("\",");
    jsonResponse.append("\"downloadUrl\":\"").append(request.getContextPath()).append("/").append(fileName).append("\"");
    jsonResponse.append("}");
    
    out.print(jsonResponse.toString());
    
} catch (Exception e) {
    Tools.println(" === 导出异常: " + e.getMessage());
    e.printStackTrace();
    
    response.setContentType("application/json; charset=UTF-8");
    out.print("{\"success\":false,\"message\":\"导出失败：" + e.getMessage() + "\"}");
}
%> 