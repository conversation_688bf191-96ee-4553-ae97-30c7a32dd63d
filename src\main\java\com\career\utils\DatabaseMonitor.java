package com.career.utils;

import com.career.db.DatabaseUtils;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 数据库连接监控工具
 * 用于监控数据库连接状态，预防连接数耗尽问题
 * 
 * 主要功能：
 * 1. 实时监控连接池状态
 * 2. 检测连接泄漏
 * 3. 数据库健康检查
 * 4. 性能指标收集
 * 
 * <AUTHOR> Admin
 * @version 1.0
 */
public class DatabaseMonitor {
    
    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    private static volatile boolean isMonitoring = false;
    
    // 监控配置 - 针对更大连接池优化
    private static final long MONITOR_INTERVAL_SECONDS = 30;  // 监控间隔30秒
    private static final double CONNECTION_WARNING_THRESHOLD_PERCENT = 0.75; // 连接数警告阈值(75%)
    private static final double CONNECTION_CRITICAL_THRESHOLD_PERCENT = 0.90; // 连接数严重警告阈值(90%)
    
    /**
     * 启动数据库监控
     */
    public static void startMonitoring() {
        if (isMonitoring) {
            System.out.println("数据库监控已经在运行中");
            return;
        }
        
        isMonitoring = true;
        
        scheduler.scheduleAtFixedRate(() -> {
            try {
                performHealthCheck();
                checkConnectionPoolStatus();
                checkDatabaseConnectivity();
            } catch (Exception e) {
                System.err.println("数据库监控执行出错: " + e.getMessage());
                e.printStackTrace();
            }
        }, 0, MONITOR_INTERVAL_SECONDS, TimeUnit.SECONDS);
        
        System.out.println("数据库监控已启动，监控间隔: " + MONITOR_INTERVAL_SECONDS + "秒");
    }
    
    /**
     * 停止数据库监控
     */
    public static void stopMonitoring() {
        if (!isMonitoring) {
            System.out.println("数据库监控未在运行");
            return;
        }
        
        isMonitoring = false;
        scheduler.shutdown();
        
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        System.out.println("数据库监控已停止");
    }
    
    /**
     * 执行健康检查
     */
    private static void performHealthCheck() {
        boolean isHealthy = DatabaseUtils.isDatabaseHealthy();
        if (!isHealthy) {
            System.err.println("⚠️ 数据库健康检查失败！");
            // 可以在这里添加告警逻辑
        }
    }
    
    /**
     * 检查连接池状态
     */
    private static void checkConnectionPoolStatus() {
        String status = DatabaseUtils.getConnectionPoolStatus();
        System.out.println("📊 " + status);
        
        // 解析连接数并检查是否超过阈值
        // 这里简化处理，实际应该解析具体的连接数
        if (status.contains("活跃连接")) {
            // 可以添加更详细的连接数解析和告警逻辑
            checkConnectionThresholds(status);
        }
    }
    
    /**
     * 检查连接数阈值
     */
    private static void checkConnectionThresholds(String status) {
        // 简化的阈值检查逻辑
        // 实际应用中应该解析具体的连接数值
        if (status.contains("活跃连接: 1") && status.contains("5")) { // 示例：15个以上连接
            System.out.println("⚠️ 警告：数据库连接数较高，请注意连接泄漏");
        }
        if (status.contains("活跃连接: 1") && status.contains("8")) { // 示例：18个以上连接
            System.err.println("🚨 严重警告：数据库连接数过高，可能即将耗尽！");
        }
    }
    
    /**
     * 检查数据库连通性
     */
    private static void checkDatabaseConnectivity() {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtils.getConnection();
            ps = conn.prepareStatement("SELECT 1 as test_connection");
            rs = ps.executeQuery();
            
            if (rs.next() && rs.getInt("test_connection") == 1) {
                // 连接正常，不输出信息避免日志过多
            } else {
                System.err.println("⚠️ 数据库连通性测试异常");
            }
            
        } catch (SQLException e) {
            System.err.println("🚨 数据库连通性测试失败: " + e.getMessage());
            
            // 分析具体的错误类型
            analyzeConnectionError(e);
            
        } finally {
            DatabaseUtils.closeAllResources(rs, ps, conn);
        }
    }
    
    /**
     * 分析连接错误
     */
    private static void analyzeConnectionError(SQLException e) {
        String errorMessage = e.getMessage().toLowerCase();
        
        if (errorMessage.contains("communications") && errorMessage.contains("socket")) {
            System.err.println("💡 诊断：可能是连接数耗尽或网络问题");
            System.err.println("   建议：1. 检查连接池配置 2. 检查网络连通性 3. 检查MySQL max_connections设置");
        } else if (errorMessage.contains("timeout")) {
            System.err.println("💡 诊断：连接超时");
            System.err.println("   建议：1. 增加连接超时时间 2. 检查数据库负载");
        } else if (errorMessage.contains("access denied")) {
            System.err.println("💡 诊断：认证失败");
            System.err.println("   建议：检查用户名密码配置");
        } else {
            System.err.println("💡 诊断：未知数据库错误，错误码: " + e.getErrorCode());
        }
    }
    
    /**
     * 手动执行一次完整的诊断
     */
    public static void runDiagnostics() {
        System.out.println("=== 数据库诊断报告 ===");
        System.out.println("时间: " + new java.util.Date());
        
        // 1. 连接池状态
        System.out.println("\n1. 连接池状态:");
        System.out.println(DatabaseUtils.getConnectionPoolStatus());
        
        // 2. 数据库健康检查
        System.out.println("\n2. 数据库健康状态:");
        boolean isHealthy = DatabaseUtils.isDatabaseHealthy();
        System.out.println("健康状态: " + (isHealthy ? "✅ 正常" : "❌ 异常"));
        
        // 3. 连接测试
        System.out.println("\n3. 连接测试:");
        testConnectionPerformance();
        
        // 4. 系统资源
        System.out.println("\n4. 系统资源:");
        printSystemResources();
        
        System.out.println("\n=== 诊断完成 ===");
    }
    
    /**
     * 测试连接性能
     */
    private static void testConnectionPerformance() {
        long startTime = System.currentTimeMillis();
        Connection conn = null;
        
        try {
            conn = DatabaseUtils.getConnection();
            long connectionTime = System.currentTimeMillis() - startTime;
            System.out.println("连接获取时间: " + connectionTime + "ms");
            
            if (connectionTime > 1000) {
                System.out.println("⚠️ 连接获取时间较长，可能存在性能问题");
            }
            
        } catch (SQLException e) {
            System.err.println("连接测试失败: " + e.getMessage());
        } finally {
            DatabaseUtils.closeConnection(conn);
        }
    }
    
    /**
     * 打印系统资源信息
     */
    private static void printSystemResources() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        System.out.println("JVM内存使用:");
        System.out.println("  已用内存: " + formatBytes(usedMemory));
        System.out.println("  空闲内存: " + formatBytes(freeMemory));
        System.out.println("  总内存: " + formatBytes(totalMemory));
        System.out.println("  最大内存: " + formatBytes(maxMemory));
        
        double memoryUsagePercent = (double) usedMemory / maxMemory * 100;
        System.out.println("  内存使用率: " + String.format("%.1f%%", memoryUsagePercent));
        
        if (memoryUsagePercent > 80) {
            System.out.println("⚠️ 内存使用率较高，可能影响数据库连接性能");
        }
    }
    
    /**
     * 格式化字节数
     */
    private static String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.1f KB", bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return String.format("%.1f MB", bytes / (1024.0 * 1024));
        return String.format("%.1f GB", bytes / (1024.0 * 1024 * 1024));
    }
    
    /**
     * 检查监控状态
     */
    public static boolean isMonitoring() {
        return isMonitoring;
    }
}
