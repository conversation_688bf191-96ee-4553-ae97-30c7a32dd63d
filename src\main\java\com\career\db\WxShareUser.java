package com.career.db;

import java.util.Date;

public class WxShareUser {
	
	private String s_id;
	private String s_passwd;
	private String qrscene;
	private String qr_url;
	private String qr_phone;
	private String qr_name;
	private String boss_sid;
	private String boss_name;
	private Date lastLoginTm;
	private String base_card_id;
	private String remark;
	
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getBase_card_id() {
		return base_card_id;
	}
	public void setBase_card_id(String base_card_id) {
		this.base_card_id = base_card_id;
	}
	public Date getLastLoginTm() {
		return lastLoginTm;
	}
	public void setLastLoginTm(Date lastLoginTm) {
		this.lastLoginTm = lastLoginTm;
	}
	public String getS_id() {
		return s_id;
	}
	public void setS_id(String s_id) {
		this.s_id = s_id;
	}
	public String getS_passwd() {
		return s_passwd;
	}
	public void setS_passwd(String s_passwd) {
		this.s_passwd = s_passwd;
	}
	public String getQrscene() {
		return qrscene;
	}
	public void setQrscene(String qrscene) {
		this.qrscene = qrscene;
	}
	public String getQr_url() {
		return qr_url;
	}
	public void setQr_url(String qr_url) {
		this.qr_url = qr_url;
	}
	public String getQr_phone() {
		return qr_phone;
	}
	public void setQr_phone(String qr_phone) {
		this.qr_phone = qr_phone;
	}
	public String getQr_name() {
		return qr_name;
	}
	public void setQr_name(String qr_name) {
		this.qr_name = qr_name;
	}

	public String getBoss_sid() {
		return boss_sid;
	}
	public void setBoss_sid(String boss_sid) {
		this.boss_sid = boss_sid;
	}
	public String getBoss_name() {
		return boss_name;
	}
	public void setBoss_name(String boss_name) {
		this.boss_name = boss_name;
	}
	
}
