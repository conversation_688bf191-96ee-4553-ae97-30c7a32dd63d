package com.career.db;

public class YxzyStatistics {

	private int id;
    private String group_id;
    private String table_id;
    private String item_id;
    private int item_sort;
    private String column_title;
    private String column_title_standare;
    private String column_value;
    private int column_sort;
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getGroup_id() {
		return group_id;
	}
	public void setGroup_id(String group_id) {
		this.group_id = group_id;
	}
	public String getTable_id() {
		return table_id;
	}
	public void setTable_id(String table_id) {
		this.table_id = table_id;
	}
	public String getItem_id() {
		return item_id;
	}
	public void setItem_id(String item_id) {
		this.item_id = item_id;
	}
	public int getItem_sort() {
		return item_sort;
	}
	public void setItem_sort(int item_sort) {
		this.item_sort = item_sort;
	}
	public String getColumn_title() {
		return column_title;
	}
	public void setColumn_title(String column_title) {
		this.column_title = column_title;
	}
	public String getColumn_title_standare() {
		return column_title_standare;
	}
	public void setColumn_title_standare(String column_title_standare) {
		this.column_title_standare = column_title_standare;
	}
	public String getColumn_value() {
		return column_value;
	}
	public void setColumn_value(String column_value) {
		this.column_value = column_value;
	}
	public int getColumn_sort() {
		return column_sort;
	}
	public void setColumn_sort(int column_sort) {
		this.column_sort = column_sort;
	}
    
    
}
