package com.career.utils;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Random;
import java.util.UUID;

import com.career.db.CityBean;
import com.career.db.JDBC;
import com.career.db.ZyzdJDBC;

public class DealZDKS_ALL {

	
	static HashMap<String, String> xk_code_map = new HashMap<>();
	static {
		xk_code_map.put("物理", "14387D");
		xk_code_map.put("理科", "14387D");
		xk_code_map.put("历史", "WLRKQG");
		xk_code_map.put("文科", "WLRKQG");
		xk_code_map.put("综合", "123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");
	}
	
	public static int randomINT() {
		Random random = new Random();
		int min = 480;
		int max = 560;
		int randomInteger = random.nextInt(max - min + 1) + min;
		return randomInteger;
	}
	
	public static void mainx(String args[]) throws Exception {
		StringBuffer SQL = new StringBuffer();
		for(int i=1000;i<10000;i++) {
			String scaID = String.valueOf(i);
			if(scaID.indexOf("0") != -1) {
				continue;
			}
			if(scaID.indexOf("00") != -1 || scaID.indexOf("44") != -1 || scaID.indexOf("444") != -1 || scaID.indexOf("0000") != -1 || scaID.indexOf("4444") != -1 || scaID.indexOf("111") != -1 || scaID.indexOf("222") != -1 || scaID.indexOf("333") != -1 || scaID.indexOf("545") != -1 || scaID.indexOf("656") != -1 || scaID.indexOf("767") != -1 || scaID.indexOf("888") != -1 || scaID.indexOf("999") != -1) {
				continue;
			}
			
			if(scaID.charAt(1) == '0' || scaID.charAt(3) == '0') {
				continue;
			}
			
			String rd = (String.valueOf(Math.random()).substring(3))+(String.valueOf(Math.random()).substring(3))+(String.valueOf(Math.random()).substring(3));
			String passwd = "";
			for(int k = 0;k<rd.length();k++) {
				if(rd.charAt(k) == '0' || rd.charAt(k) == '4') {
					continue;
				}
				passwd += rd.charAt(k);
				if(passwd.length() > 3) {
					break;
				}
			}
			SQL.append("INSERT INTO `base_card` (`C_ID`, `C_PASSWD`, `C_PROV`, `C_CITY`, `C_SCORE`, `C_SCORE_ORG`, `C_XK`, `C_CREATE`, `C_ACTIVE`, `C_LAST_LOGIN`, `C_YEAR`, `C_STATUS`, `C_REMARK`, `C_PHONE`, `C_ORDER_ID`, `C_MD`, `C_OPEN_ID`, `C_ADMIN`, `C_DESC`, `C_SYS_IND`, `C_EXPIRE`, `C_ALLOWED_PROV`, `C_VERSION`, `C_BOSS_ID`, `C_NICKNAME`, `C_AGENT`, `C_INTRODUCE`, `C_AGENT_TM`, `C_INTRODUCE_TM`, `WX_QRSCENE`, `TOKEN_ID`, `C_LAST_MODIFIED`, `C_FORM_DNLD_CNT`) VALUES ('E"+i+"', '"+passwd+"', NULL, NULL, 0, 0, NULL, now(), NULL, NULL, 0, 1, '2025年新卡', NULL, NULL, NULL, NULL, NULL, NULL, 'F', NULL, 'ONLY', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);\r\n");
		}
		writeTempFile(new File("F://诊断考试换算//Etest_NC1_.txt"), SQL);
	}
	
	
	//体验卡
	public static void main3333(String args[]) throws Exception {
		
		HashMap<String, String> Map = new HashMap<>();
		StringBuffer SQL = new StringBuffer();
		int cnt = 0;
		for(int i=10000;i<15999;i++) {
			String scaID = String.valueOf(i);
			if(scaID.indexOf("00") != -1) {
				continue;
			}
			if(scaID.indexOf("11") != -1) {
				continue;
			}
			if(scaID.indexOf("00") != -1 || scaID.indexOf("44") != -1 || scaID.indexOf("444") != -1 || scaID.indexOf("0000") != -1 || scaID.indexOf("4444") != -1 || scaID.indexOf("111") != -1 || scaID.indexOf("222") != -1 || scaID.indexOf("333") != -1 || scaID.indexOf("545") != -1 || scaID.indexOf("656") != -1 || scaID.indexOf("767") != -1 || scaID.indexOf("888") != -1 || scaID.indexOf("999") != -1) {
				continue;
			}
			
			if(scaID.charAt(1) == '0' || scaID.charAt(3) == '0') {
				continue;
			}
			
			scaID = "W" + scaID;
			
			String sub_prefix_number = genRandomPasswd(3);
			
			String[] firstChar = new String[] {"E","W","S","K","T","H","A","B","H","H","T","H","E","W","S","K","T","H","A","B"};
			int rdd = (int)(Math.random() * 10);
			String sub_id_prefix = firstChar[rdd] + sub_prefix_number;
			
			if(Map.containsKey(sub_id_prefix)) {
				continue;
			}
			Map.put(sub_id_prefix,"1");

			cnt++;
		}
		System.out.println(cnt);
		writeTempFile(new File("F://诊断考试换算//Etest_NC2_.txt"), SQL);
	}
	
	
	public static void main23(String args[]) throws Exception {
		
		HashMap<String, String> Map = new HashMap<>();
		StringBuffer SQL = new StringBuffer();
		int cnt = 0;
		for(int i=60000;i<69999;i++) {
			String scaID = String.valueOf(i);
			if(scaID.indexOf("00") != -1) {
				continue;
			}
			if(scaID.indexOf("11") != -1) {
				continue;
			}
			if(scaID.indexOf("00") != -1 || scaID.indexOf("44") != -1 || scaID.indexOf("444") != -1 || scaID.indexOf("0000") != -1 || scaID.indexOf("4444") != -1 || scaID.indexOf("111") != -1 || scaID.indexOf("222") != -1 || scaID.indexOf("333") != -1 || scaID.indexOf("545") != -1 || scaID.indexOf("656") != -1 || scaID.indexOf("767") != -1 || scaID.indexOf("888") != -1 || scaID.indexOf("999") != -1) {
				continue;
			}
			
			if(scaID.charAt(1) == '0' || scaID.charAt(3) == '0') {
				continue;
			}
			
			scaID = "C" + scaID;
			
			String sub_prefix_number = genRandomPasswd(3);
			
			String[] firstChar = new String[] {"E","W","S","K","T","H","A","B","H","H","T","H","E","W","S","K","T","H","A","B"};
			int rdd = (int)(Math.random() * 10);
			String sub_id_prefix = firstChar[rdd] + sub_prefix_number;
			
			if(Map.containsKey(sub_id_prefix)) {
				continue;
			}
			Map.put(sub_id_prefix,"1");
			
			for(int x = 11 ;x < 15; x++) {
				SQL.append("INSERT INTO `zyzd2023`.`lhy_card` (`c_id`, `p_c_id`, `c_passwd`, `c_status`, `c_level`, `c_type`, `create_tm`,sys_ind,c_auto_ind,c_function) VALUES ('"+(sub_id_prefix + x )+"', '"+scaID+"', '"+genRandomPasswd(4)+"', 1, 1, 1, now(),2,12,'TB,SD,XZ,ZD,JZ');");
			}
			SQL.append("INSERT INTO `zyzd2023`.`lhy_card` (`c_id`, `c_sub_prefix`, `c_passwd`, `c_status`, `c_level`, `c_type`, `create_tm`,sys_ind,c_auto_ind,c_function) VALUES ('"+scaID+"', '"+sub_id_prefix+"', '"+genRandomPasswd(4)+"', 1, 2, 1, now(),2,11,'TB,XZ,ZD,JZ,SH,ZJGL');");
			cnt++;
		}
		System.out.println(cnt);
		writeTempFile(new File("F://诊断考试换算//Etest_NC2_.txt"), SQL);
	}
	
	private static String genRandomPasswd(int length) {
		String rd = (String.valueOf(Math.random()).substring(3))+(String.valueOf(Math.random()).substring(3))+(String.valueOf(Math.random()).substring(3));
		String passwd = "";
		for(int k = 0;k<rd.length();k++) {
			if(rd.charAt(k) == '0' || rd.charAt(k) == '4') {
				continue;
			}
			passwd += rd.charAt(k);
			if(passwd.length() > length) {
				break;
			}
		}
		return passwd;
	}
	
	public static void main3x(String args[]) throws Exception {
		for(int i=0;i<40000;i++) {
			new ZyzdJDBC().testInsert();
			Thread.sleep(100);
		}
	}
	
	public static void main(String args[]) throws Exception {
		Iterator<String> it = JDBC.HM_PROVINCE.keySet().iterator();
		StringBuffer SQL = new StringBuffer();
		System.out.println("xxx");
		int i = 901129;
		while(it.hasNext()) {
			i++;
			String passwd = genRandomPasswd(5);
			String key = it.next();
			String value = JDBC.HM_PROVINCE.get(key);
			String cnName = JDBC.HM_PROVINCE_CODE_NAME.get(key);
			System.out.println("INSERT INTO `dx_agent` (`agent_id`, `a_passwd`, `create_tm`, `active_tm`, `last_login_tm`, `expire_tm`, `remark`, `status`, `rpt_point_cnt`) "
					+ "VALUES ('"+i+"', '"+passwd+"', NOW(), null,  null, NULL, NULL, 1, 200);\r\n"
					+ "");
			
		}
		
		
		writeTempFile(new File("F://诊断考试换算//TABt_NCx223234XX3_.txt"), SQL);
	}
	
	public static void main2(String args[]) throws Exception {
		System.out.println(UUID.randomUUID().toString());
		StringBuffer SQL = new StringBuffer();
		
		File fileFolder = new File("F:\\外发短视频");
		File[] files = fileFolder.listFiles();
		for(int i=0;i<files.length;i++) {
			File el = files[i];
			System.out.println(el);
			File[] fileSubs = el.listFiles();
			for(int k=0;k<fileSubs.length;k++) {
				File fileSub = fileSubs[k];
				String fileName = fileSub.getName();
				fileName = fileName.replaceAll("bandicam ", "LX2B_com_");
				String newFileName = el.getAbsolutePath()+"\\"+fileName;
				fileSub.renameTo(new File(newFileName));
			}
		}
		
		
		/**
		for(int i=5000;i<6999;i++) {
			String scaID = String.valueOf(i);
			if(scaID.indexOf("0") != -1 || scaID.indexOf("4") != -1) {
				//continue;
			}
			if(scaID.charAt(1) == scaID.charAt(2)) {
				continue;
			}
			
			char[] sxss = {'2','3','4','5','6','7','8','9','A','B','E','F','G','H','K','R','S','T','X','W'};
			
			scaID = "SCA" + scaID;
			
			
			String rd = (String.valueOf(Math.random()).substring(3))+(String.valueOf(Math.random()).substring(3))+(String.valueOf(Math.random()).substring(3));
			String passwd = "";
			for(int k = 0;k<rd.length();k++) {
				if(rd.charAt(k) == '0' || rd.charAt(k) == '4') {
					continue;
				}
				passwd += rd.charAt(k);
				if(passwd.length() > 3) {
					break;
				}
			}
			SQL.append("INSERT INTO `base_card` (`C_ID`, `C_PASSWD`, `C_PROV`, `C_CITY`, `C_SCORE`, `C_XK`, `C_CREATE`, `C_ACTIVE`, `C_LAST_LOGIN`, `C_YEAR`, `C_STATUS`, `C_REMARK`, `C_PHONE`, `C_ORDER_ID`, `C_MD`, `C_OPEN_ID`, `C_ADMIN`, `C_DESC`, `C_SYS_IND`, `C_EXPIRE`,`C_ALLOWED_PROV`) VALUES ('"+scaID+"', '"+passwd+"', NULL, NULL, 0, NULL, NOW(), NULL, NULL, 0, 1, '在线支付专用', NULL, NULL, NULL, NULL, NULL, NULL, 'SCA', NULL,'ONLY');\r\n"
				+ "");
		}
		writeTempFile(new File("F://诊断考试换算//SCAtest_NC1_.txt"), SQL);
		**/
		/**
		StringBuffer SQL = new StringBuffer();
		
		for(int i=1;i<5000;i++) {

			
			char[] sxss = {'2','3','4','5','6','7','8','H','K','R','9','2','3','4','5','6','7','8','9','A','B','E','F','G','T','2','3','4','5','6','7','8','2','3','4','5','6','7','8','2','3','4','5','6','7','8','X','W','2','3','4','5','6','7','8'};
			//char[] sxss = {'2','3','4','5','6','7','8','1','2','3','4','5','6','7','8','2','3','4','5','6','1','7','8','2','3','4','5','6','7','8','2','3','4','5','6','7','8'};
			
			String passwd = "";
			for(int k = 0;k<9;k++) {
				int t = (int)(Math.random() * sxss.length - 1);

				passwd += sxss[t];
				if(passwd.length() > 4) {
					break;
				}
			}
			SQL.append("INSERT INTO `zyzd_invitation_code` (`ivt_id`, `ivt_type`, `create_tm`) VALUES ('"+passwd+"', 1, now());\r\n"
				+ "");
		}
		writeTempFile(new File("F://诊断考试换算//SCAtest_NC2_.txt"), SQL);*/
	}
	
	
	
	
	
	public static void s5() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		
		zdscore.setZk_ls(150);
		zdscore.setZk_wl(150);
		zdscore.setBen2_ls(406);//47
		zdscore.setBen2_wl(407); //430
		zdscore.setBen1_ls(534); //540
		zdscore.setBen1_wl(508); //496
		
		zdscore.setBen985_ls(588);//
		zdscore.setBen985_wl(615);//
		
		zdscore.setBenQB_ls(636);
		zdscore.setBenQB_wl(670);
		
		zdscore.setZGF_ls(670);
		zdscore.setZGF_wl(700);
		
		zdscore.setMF_ls(690);
		zdscore.setMF_wl(730);
		
		
		pcxscore.setZk_ls(150);
		pcxscore.setZk_wl(150);
		pcxscore.setBen2_ls(457);
		pcxscore.setBen2_wl(459);
		pcxscore.setBen1_ls(529);
		pcxscore.setBen1_wl(539);
		
		pcxscore.setBen985_ls(582);
		pcxscore.setBen985_wl(618);
		
		pcxscore.setBenQB_ls(639);
		pcxscore.setBenQB_wl(696);
		
		pcxscore.setZGF_ls(650);
		pcxscore.setZGF_wl(710);
		
		pcxscore.setMF_ls(660);
		pcxscore.setMF_wl(710);
		
		runExcept33("S5", "宜宾", "2025一诊", zdscore, pcxscore);
	}
	
	public static void h4() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		
		zdscore.setZk_ls(180);
		zdscore.setZk_wl(180);
		zdscore.setBen2_ls(293);//357
		zdscore.setBen2_wl(298); //301
		zdscore.setBen1_ls(437); //427  - 12
		zdscore.setBen1_wl(448); //429   - 25
		
		zdscore.setBen985_ls(530);//520
		zdscore.setBen985_wl(560);//
		
		zdscore.setBenQB_ls(612);
		zdscore.setBenQB_wl(680);
		
		zdscore.setZGF_ls(630);
		zdscore.setZGF_wl(710);
		
		zdscore.setMF_ls(630);
		zdscore.setMF_wl(710);
		
		
		pcxscore.setZk_ls(160);
		pcxscore.setZk_wl(160);
		pcxscore.setBen2_ls(410);
		pcxscore.setBen2_wl(360);
		pcxscore.setBen1_ls(486);
		pcxscore.setBen1_wl(480);
		
		pcxscore.setBen985_ls(593);
		pcxscore.setBen985_wl(598);
		
		pcxscore.setBenQB_ls(674);
		pcxscore.setBenQB_wl(704);
		
		pcxscore.setZGF_ls(674);
		pcxscore.setZGF_wl(704);
		
		pcxscore.setMF_ls(674);
		pcxscore.setMF_wl(704);
		
		runExcept33("H4", null, "九师联盟11月联考", zdscore, pcxscore);
	}
	
	
	public static void c1() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		
		zdscore.setZk_ls(180);
		zdscore.setZk_wl(180);
		zdscore.setBen2_ls(392);//421
		zdscore.setBen2_wl(367); //412
		zdscore.setBen1_ls(457); //493
		zdscore.setBen1_wl(445); //476
		
		zdscore.setBen985_ls(550);//
		zdscore.setBen985_wl(525);//
		
		zdscore.setBenQB_ls(635);
		zdscore.setBenQB_wl(670);
		
		zdscore.setZGF_ls(672);
		zdscore.setZGF_wl(685);
		
		zdscore.setMF_ls(680);
		zdscore.setMF_wl(700);
		
		
		pcxscore.setZk_ls(180);
		pcxscore.setZk_wl(180);
		pcxscore.setBen2_ls(428);
		pcxscore.setBen2_wl(427);
		pcxscore.setBen1_ls(506);
		pcxscore.setBen1_wl(499);
		
		pcxscore.setBen985_ls(587);
		pcxscore.setBen985_wl(571);
		
		pcxscore.setBenQB_ls(673);
		pcxscore.setBenQB_wl(699);
		
		pcxscore.setZGF_ls(673);
		pcxscore.setZGF_wl(699);
		
		pcxscore.setMF_ls(673);
		pcxscore.setMF_wl(699);
		
		runExcept33("C1", null, "高三11月调研", zdscore, pcxscore);
	}
	
	public static void c1_2() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		
		zdscore.setZk_ls(180);
		zdscore.setZk_wl(180);
		zdscore.setBen2_ls(401);//415
		zdscore.setBen2_wl(391); //391
		zdscore.setBen1_ls(470); //482
		zdscore.setBen1_wl(450); //457
		
		zdscore.setBen985_ls(552);//
		zdscore.setBen985_wl(510);//26971
		
		zdscore.setBenQB_ls(640);
		zdscore.setBenQB_wl(600);
		
		zdscore.setZGF_ls(670);
		zdscore.setZGF_wl(660);
		
		zdscore.setMF_ls(680);
		zdscore.setMF_wl(720);
		
		
		pcxscore.setZk_ls(180);
		pcxscore.setZk_wl(180);
		pcxscore.setBen2_ls(407);
		pcxscore.setBen2_wl(406);
		pcxscore.setBen1_ls(480);
		pcxscore.setBen1_wl(468);
		
		pcxscore.setBen985_ls(567);
		pcxscore.setBen985_wl(525);
		
		pcxscore.setBenQB_ls(643);
		pcxscore.setBenQB_wl(614);
		
		pcxscore.setZGF_ls(653);
		pcxscore.setZGF_wl(665);
		
		pcxscore.setMF_ls(653);
		pcxscore.setMF_wl(673);
		
		runExcept33("C1", null, "康德二诊X", zdscore, pcxscore);
	}
	
	
	public static void s52() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		
		zdscore.setZk_ls(180);
		zdscore.setZk_wl(180);
		zdscore.setBen2_ls(441);
		zdscore.setBen2_wl(410);
		zdscore.setBen1_ls(517);
		zdscore.setBen1_wl(499);
		
		zdscore.setBen985_ls(560);//
		zdscore.setBen985_wl(582);//
		
		zdscore.setBenQB_ls(610);
		zdscore.setBenQB_wl(670);
		
		zdscore.setZGF_ls(637);
		zdscore.setZGF_wl(680);
		
		zdscore.setMF_ls(640);
		zdscore.setMF_wl(690);
		
		
		pcxscore.setZk_ls(180);
		pcxscore.setZk_wl(180);
		pcxscore.setBen2_ls(458);
		pcxscore.setBen2_wl(433);
		pcxscore.setBen1_ls(527);
		pcxscore.setBen1_wl(520);
		
		pcxscore.setBen985_ls(579);
		pcxscore.setBen985_wl(600);
		
		pcxscore.setBenQB_ls(639);
		pcxscore.setBenQB_wl(698);
		
		pcxscore.setZGF_ls(640);
		pcxscore.setZGF_wl(700);
		
		pcxscore.setMF_ls(640);
		pcxscore.setMF_wl(700);
		
		runExcept33("S5", "自贡", "二诊", zdscore, pcxscore);
	}
	
	
	public static void n1() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		
		zdscore.setZk_ls(180);
		zdscore.setZk_wl(180);
		zdscore.setBen2_ls(321);
		zdscore.setBen2_wl(299);
		zdscore.setBen1_ls(399);
		zdscore.setBen1_wl(388);
		
		zdscore.setBen985_ls(465);//
		zdscore.setBen985_wl(470);//
		
		zdscore.setBenQB_ls(585);
		zdscore.setBenQB_wl(650);
		
		zdscore.setZGF_ls(610);
		zdscore.setZGF_wl(670);
		
		zdscore.setMF_ls(615);
		zdscore.setMF_wl(675);
		
		
		pcxscore.setZk_ls(255);
		pcxscore.setZk_wl(255);
		pcxscore.setBen2_ls(379);
		pcxscore.setBen2_wl(333);
		pcxscore.setBen1_ls(468);
		pcxscore.setBen1_wl(434);
		
		pcxscore.setBen985_ls(522);
		pcxscore.setBen985_wl(505);
		
		pcxscore.setBenQB_ls(637);
		pcxscore.setBenQB_wl(674);
		
		pcxscore.setZGF_ls(637);
		pcxscore.setZGF_wl(674);
		
		pcxscore.setMF_ls(637);
		pcxscore.setMF_wl(674);
		
		runExcept33("N1", "呼和浩特", "一模", zdscore, pcxscore);
	}
	
	public static void g1() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		
		zdscore.setZk_ls(180);
		zdscore.setZk_wl(180);
		zdscore.setBen2_ls(413);
		zdscore.setBen2_wl(340);
		zdscore.setBen1_ls(472);
		zdscore.setBen1_wl(443);
		
		zdscore.setBen985_ls(557);//
		zdscore.setBen985_wl(525);//
		
		zdscore.setBenQB_ls(630);
		zdscore.setBenQB_wl(660);
		
		zdscore.setZGF_ls(650);
		zdscore.setZGF_wl(670);
		
		zdscore.setMF_ls(655);
		zdscore.setMF_wl(675);
		
		
		pcxscore.setZk_ls(180);
		pcxscore.setZk_wl(180);
		pcxscore.setBen2_ls(420);
		pcxscore.setBen2_wl(337);
		pcxscore.setBen1_ls(488);
		pcxscore.setBen1_wl(433);
		
		pcxscore.setBen985_ls(547);
		pcxscore.setBen985_wl(515);
		
		pcxscore.setBenQB_ls(631);
		pcxscore.setBenQB_wl(665);
		
		pcxscore.setZGF_ls(631);
		pcxscore.setZGF_wl(665);
		
		pcxscore.setMF_ls(631);
		pcxscore.setMF_wl(665);
		
		runExcept33("G1", null, "九省联考", zdscore, pcxscore);
	}
	
	
	public static void x1() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		
		zdscore.setZk_ls(180);
		zdscore.setZk_wl(180);
		zdscore.setBen2_ls(349);
		zdscore.setBen2_wl(292);
		zdscore.setBen1_ls(461);
		zdscore.setBen1_wl(405);
		
		zdscore.setBen985_ls(515);//
		zdscore.setBen985_wl(496);//
		
		zdscore.setBenQB_ls(615);
		zdscore.setBenQB_wl(650);
		
		zdscore.setZGF_ls(630);
		zdscore.setZGF_wl(670);
		
		zdscore.setMF_ls(630);
		zdscore.setMF_wl(670);
		
		
		pcxscore.setZk_ls(180);
		pcxscore.setZk_wl(180);
		pcxscore.setBen2_ls(354);
		pcxscore.setBen2_wl(285);
		pcxscore.setBen1_ls(458);
		pcxscore.setBen1_wl(396);
		
		pcxscore.setBen985_ls(504);
		pcxscore.setBen985_wl(481);
		
		pcxscore.setBenQB_ls(594);
		pcxscore.setBenQB_wl(655);
		
		pcxscore.setZGF_ls(594);
		pcxscore.setZGF_wl(655);
		
		pcxscore.setMF_ls(594);
		pcxscore.setMF_wl(655);
		
		runExcept33("X1", null, "九省联考", zdscore, pcxscore);
	}
	
	public static void h6() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		
		zdscore.setZk_ls(180);
		zdscore.setZk_wl(180);
		zdscore.setBen2_ls(387);
		zdscore.setBen2_wl(360);
		zdscore.setBen1_ls(435);
		zdscore.setBen1_wl(423);
		
		zdscore.setBen985_ls(571);//
		zdscore.setBen985_wl(560);//
		
		zdscore.setBenQB_ls(616);
		zdscore.setBenQB_wl(639);
		
		zdscore.setZGF_ls(650);
		zdscore.setZGF_wl(675);
		
		zdscore.setMF_ls(665);
		zdscore.setMF_wl(680);
		
		
		pcxscore.setZk_ls(180);
		pcxscore.setZk_wl(180);
		pcxscore.setBen2_ls(428);
		pcxscore.setBen2_wl(415);
		pcxscore.setBen1_ls(482);
		pcxscore.setBen1_wl(477);
		
		pcxscore.setBen985_ls(600);
		pcxscore.setBen985_wl(600);
		
		pcxscore.setBenQB_ls(636);
		pcxscore.setBenQB_wl(670);
		
		pcxscore.setZGF_ls(636);
		pcxscore.setZGF_wl(670);
		
		pcxscore.setMF_ls(636);
		pcxscore.setMF_wl(670);
		
		runExcept33("H6", null, "三湘名校联盟", zdscore, pcxscore);
	}
	
	public static void h5() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		
		zdscore.setZk_ls(150);
		zdscore.setZk_wl(150);
		zdscore.setBen2_ls(374);
		zdscore.setBen2_wl(369);
		zdscore.setBen1_ls(479);
		zdscore.setBen1_wl(461);
		
		zdscore.setBen985_ls(554);//
		zdscore.setBen985_wl(554);//
		
		zdscore.setBenQB_ls(634);
		zdscore.setBenQB_wl(663);
		
		zdscore.setZGF_ls(670);
		zdscore.setZGF_wl(680);
		
		zdscore.setMF_ls(670);
		zdscore.setMF_wl(680);
		
		
		pcxscore.setZk_ls(150);
		pcxscore.setZk_wl(150);
		pcxscore.setBen2_ls(432);
		pcxscore.setBen2_wl(437);
		pcxscore.setBen1_ls(530);
		pcxscore.setBen1_wl(525);
		
		pcxscore.setBen985_ls(590);
		pcxscore.setBen985_wl(592);
		
		pcxscore.setBenQB_ls(657);
		pcxscore.setBenQB_wl(685);
		
		pcxscore.setZGF_ls(657);
		pcxscore.setZGF_wl(685);
		
		pcxscore.setMF_ls(657);
		pcxscore.setMF_wl(685);
		
		runExcept33("H5", null, "圆创联考", zdscore, pcxscore);
	}
	
	
	public static void h2() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		
		zdscore.setZk_ls(180);
		zdscore.setZk_wl(180);

		pcxscore.setZk_wl(180);
		pcxscore.setZk_ls(180);
		
		zdscore.setBen2_ls(375); 
		zdscore.setBen1_ls(460);
		zdscore.setBen985_ls(563);
		zdscore.setBenQB_ls(623);
		zdscore.setZGF_ls(639);
		zdscore.setMF_ls(650);

		zdscore.setBen2_wl(395);
		zdscore.setBen1_wl(485);
		zdscore.setBen985_wl(565);
		zdscore.setBenQB_wl(678);
		zdscore.setZGF_wl(691);
		zdscore.setMF_wl(700);
		
		pcxscore.setBen2_ls(430);
		pcxscore.setBen1_ls(495);
		pcxscore.setBen985_ls(590);
		pcxscore.setBenQB_ls(656);
		pcxscore.setZGF_ls(656);
		pcxscore.setMF_ls(656);
		
		pcxscore.setBen1_wl(439);
		pcxscore.setBen2_wl(492);
		pcxscore.setBen985_wl(573);
		pcxscore.setBenQB_wl(673);
		pcxscore.setZGF_wl(673);
		pcxscore.setMF_wl(673);
		
		runExcept33("H2", "唐山", "一模", zdscore, pcxscore);
	}
	
	
	public static void j2() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		
		zdscore.setZk_ls(180);
		zdscore.setZk_wl(180);
		zdscore.setBen2_ls(477); //历史二本
		zdscore.setBen2_wl(472); //物理二本
		zdscore.setBen1_ls(520); //历史一本
		zdscore.setBen1_wl(511); //物理一本
		
		
		zdscore.setBen985_ls(635);//历史985
		zdscore.setBen985_wl(636);//物理985
		
		zdscore.setBenQB_ls(686);
		zdscore.setBenQB_wl(690);
		
		zdscore.setZGF_ls(695);
		zdscore.setZGF_wl(700);
		
		zdscore.setMF_ls(700);
		zdscore.setMF_wl(710);
		
		
		pcxscore.setZk_ls(180);
		pcxscore.setZk_wl(180);
		pcxscore.setBen2_ls(474);
		pcxscore.setBen2_wl(448);
		pcxscore.setBen1_ls(527);
		pcxscore.setBen1_wl(512);
		
		pcxscore.setBen985_ls(597);
		pcxscore.setBen985_wl(594);
		
		pcxscore.setBenQB_ls(663);
		pcxscore.setBenQB_wl(690);
		
		pcxscore.setZGF_ls(663);
		pcxscore.setZGF_wl(690);
		
		pcxscore.setMF_ls(663);
		pcxscore.setMF_wl(690);
		//一本到985差80~100
		//985到清北差 40~60
		runExcept33("J2", "宿迁", "一模", zdscore, pcxscore);
	}
	
	
	public static void y1() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		
		zdscore.setZk_ls(180);
		zdscore.setZk_wl(180);
		zdscore.setBen2_ls(440); //历史二本 440
		zdscore.setBen2_wl(330); //物理二本 330
		zdscore.setBen1_ls(460); //历史一本 460
		zdscore.setBen1_wl(410); //物理一本 410
		
		
		zdscore.setBen985_ls(499);//历史985  558
		zdscore.setBen985_wl(508);//物理985  510
		
		zdscore.setBenQB_ls(546);     //578
		zdscore.setBenQB_wl(600);     //570
		
		zdscore.setZGF_ls(586);
		zdscore.setZGF_wl(610);
		
		zdscore.setMF_ls(586);
		zdscore.setMF_wl(620);
		
		
		pcxscore.setZk_ls(180);
		pcxscore.setZk_wl(180);
		pcxscore.setBen2_ls(465);
		pcxscore.setBen2_wl(405);
		pcxscore.setBen1_ls(530);
		pcxscore.setBen1_wl(485);
		
		pcxscore.setBen985_ls(591);
		pcxscore.setBen985_wl(585);
		
		pcxscore.setBenQB_ls(642);
		pcxscore.setBenQB_wl(681);
		
		pcxscore.setZGF_ls(656);
		pcxscore.setZGF_wl(694);
		
		pcxscore.setMF_ls(667);
		pcxscore.setMF_wl(698);
		//云南
		runExcept33("Y1", null , "统测第二次", zdscore, pcxscore);
	}
	
	public static void s2() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		
		zdscore.setZk_ls(180);
		zdscore.setZk_wl(180);
		zdscore.setBen2_ls(390); //历史二本366
		zdscore.setBen2_wl(379); //物理二本328
		zdscore.setBen1_ls(485); //历史一本448
		zdscore.setBen1_wl(471); //物理一本405
		
		
		zdscore.setBen985_ls(525);//历史985  
		zdscore.setBen985_wl(561);//物理985  
		
		zdscore.setBenQB_ls(615);      
		zdscore.setBenQB_wl(660);      
		
		zdscore.setZGF_ls(640);        
		zdscore.setZGF_wl(690);        
		 
		zdscore.setMF_ls(670);         
		zdscore.setMF_wl(700);
		
		
		pcxscore.setZk_ls(180);
		pcxscore.setZk_wl(180);
		pcxscore.setBen2_ls(418);
		pcxscore.setBen2_wl(396);
		pcxscore.setBen1_ls(490);
		pcxscore.setBen1_wl(480);
		
		pcxscore.setBen985_ls(524);
		pcxscore.setBen985_wl(566);
		
		pcxscore.setBenQB_ls(624);
		pcxscore.setBenQB_wl(674);
		
		pcxscore.setZGF_ls(634);
		pcxscore.setZGF_wl(679);
		
		pcxscore.setMF_ls(634);
		pcxscore.setMF_wl(679);
		//山西
		runExcept33("S2", null , "九师10月联考", zdscore, pcxscore);
	}
	
	
	public static void g2() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		
		zdscore.setZk_ls(180);
		zdscore.setZk_wl(180);
		zdscore.setBen2_ls(396); //历史二本396
		zdscore.setBen2_wl(408); //物理二本408
		zdscore.setBen1_ls(501); //历史一本501
		zdscore.setBen1_wl(493); //物理一本493

		zdscore.setBen985_ls(573);//历史985  
		zdscore.setBen985_wl(585);//物理985  
		
		zdscore.setBenQB_ls(645);      
		zdscore.setBenQB_wl(655);      
		
		zdscore.setZGF_ls(660);        
		zdscore.setZGF_wl(680);        
		 
		zdscore.setMF_ls(680);         
		zdscore.setMF_wl(699);

	
		pcxscore.setZk_ls(200);
		pcxscore.setZk_wl(200);
		pcxscore.setBen2_ls(433);
		pcxscore.setBen2_wl(439);
		pcxscore.setBen1_ls(540);
		pcxscore.setBen1_wl(539);

		pcxscore.setBen985_ls(589);
		pcxscore.setBen985_wl(594);
		
		pcxscore.setBenQB_ls(670);
		pcxscore.setBenQB_wl(693);
		
		pcxscore.setZGF_ls(670);
		pcxscore.setZGF_wl(693);
		
		pcxscore.setMF_ls(670);
		pcxscore.setMF_wl(693);
	
		//广东
		runExcept33("G2", "惠州" , "一模", zdscore, pcxscore);
	}
	
	
	public static void g3() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		
		zdscore.setZk_ls(180);
		zdscore.setZk_wl(180);
		zdscore.setBen2_ls(382); //历史二本
		zdscore.setBen2_wl(350); //物理二本
		zdscore.setBen1_ls(503); //历史一本
		zdscore.setBen1_wl(484); //物理一本

		zdscore.setBen985_ls(575);//历史985  
		zdscore.setBen985_wl(570);//物理985  
		
		zdscore.setBenQB_ls(640);      
		zdscore.setBenQB_wl(677);      
		
		zdscore.setZGF_ls(656);        
		zdscore.setZGF_wl(694);        
		 
		zdscore.setMF_ls(656);         
		zdscore.setMF_wl(694);

	
		pcxscore.setZk_ls(180);
		pcxscore.setZk_wl(180);
		pcxscore.setBen2_ls(428);
		pcxscore.setBen2_wl(347);
		pcxscore.setBen1_ls(528);
		pcxscore.setBen1_wl(475);

		pcxscore.setBen985_ls(592);
		pcxscore.setBen985_wl(554);
		
		pcxscore.setBenQB_ls(666);
		pcxscore.setBenQB_wl(681);
		
		pcxscore.setZGF_ls(666);
		pcxscore.setZGF_wl(681);
		
		pcxscore.setMF_ls(666);
		pcxscore.setMF_wl(681);
	
		//广西
		runExcept33("G3", null , "九省联考", zdscore, pcxscore);
	}
	
	
	public static void j1() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		
		zdscore.setZk_ls(180);
		zdscore.setZk_wl(180);
		zdscore.setBen2_ls(302); //历史二本
		zdscore.setBen2_wl(315); //物理二本
		zdscore.setBen1_ls(479); //历史一本
		zdscore.setBen1_wl(455); //物理一本

		zdscore.setBen985_ls(526);//历史985  
		zdscore.setBen985_wl(538);//物理985  
		
		zdscore.setBenQB_ls(613);      
		zdscore.setBenQB_wl(669);      
		
		zdscore.setZGF_ls(626);        
		zdscore.setZGF_wl(681);        
		 
		zdscore.setMF_ls(656);         
		zdscore.setMF_wl(694);

	
		pcxscore.setZk_ls(180);
		pcxscore.setZk_wl(180);
		pcxscore.setBen2_ls(341);
		pcxscore.setBen2_wl(292);
		pcxscore.setBen1_ls(485);
		pcxscore.setBen1_wl(464);

		pcxscore.setBen985_ls(511);
		pcxscore.setBen985_wl(520);
		
		pcxscore.setBenQB_ls(609);
		pcxscore.setBenQB_wl(672);
		
		pcxscore.setZGF_ls(609);
		pcxscore.setZGF_wl(672);
		
		pcxscore.setMF_ls(609);
		pcxscore.setMF_wl(672);
	
		//吉林
		runExcept33("J1", "长春" , "一模", zdscore, pcxscore);
	}
	
	
	public static void f1() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		
		zdscore.setZk_ls(180);
		zdscore.setZk_wl(180);
		zdscore.setBen2_ls(376); //历史二本
		zdscore.setBen2_wl(392); //物理二本
		zdscore.setBen1_ls(448); //历史一本
		zdscore.setBen1_wl(487); //物理一本

		zdscore.setBen985_ls(550);//历史985  
		zdscore.setBen985_wl(571);//物理985  
		
		zdscore.setBenQB_ls(625);      
		zdscore.setBenQB_wl(675);      
		
		zdscore.setZGF_ls(650);        
		zdscore.setZGF_wl(690);        
		 
		zdscore.setMF_ls(670);         
		zdscore.setMF_wl(700);

	
		pcxscore.setZk_ls(220);
		pcxscore.setZk_wl(220);
		pcxscore.setBen2_ls(431);
		pcxscore.setBen2_wl(449);
		pcxscore.setBen1_ls(519);
		pcxscore.setBen1_wl(538);
		

		pcxscore.setBen985_ls(587);
		pcxscore.setBen985_wl(612);
		
		pcxscore.setBenQB_ls(657);
		pcxscore.setBenQB_wl(692);
		
		pcxscore.setZGF_ls(657);
		pcxscore.setZGF_wl(692);
		
		pcxscore.setMF_ls(657);
		pcxscore.setMF_wl(692);
	
		//福建
		runExcept33("F1", "漳州" , "第一次质检", zdscore, pcxscore);
	}
	
	
	public static void s3() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		
		zdscore.setZk_ls(180);
		zdscore.setZk_wl(180);
		zdscore.setBen2_ls(379); //历史二本 379
		zdscore.setBen2_wl(300); //物理二本 300
		zdscore.setBen1_ls(479); //历史一本 479
		zdscore.setBen1_wl(390); //物理一本 390

		zdscore.setBen985_ls(542);//历史985  
		zdscore.setBen985_wl(495);//物理985  
		
		zdscore.setBenQB_ls(645);      
		zdscore.setBenQB_wl(665);      
		
		zdscore.setZGF_ls(660);        
		zdscore.setZGF_wl(680);        
		 
		zdscore.setMF_ls(670);         
		zdscore.setMF_wl(690);

	
		pcxscore.setZk_ls(180);
		pcxscore.setZk_wl(180);
		pcxscore.setBen2_ls(403);
		pcxscore.setBen2_wl(336);
		pcxscore.setBen1_ls(489);
		pcxscore.setBen1_wl(443);

		pcxscore.setBen985_ls(544);
		pcxscore.setBen985_wl(530);
		
		pcxscore.setBenQB_ls(641);
		pcxscore.setBenQB_wl(687);
		
		pcxscore.setZGF_ls(641);
		pcxscore.setZGF_wl(687);
		
		pcxscore.setMF_ls(641);
		pcxscore.setMF_wl(687);
	
		//陕西
		runExcept33("S3", "渭南" , "一模", zdscore, pcxscore);
	}
	
	public static void j3() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		
		zdscore.setZk_ls(180);
		zdscore.setZk_wl(180);
		zdscore.setBen2_ls(437); //历史二本 380
		zdscore.setBen2_wl(432); //物理二本 295
		zdscore.setBen1_ls(522); //历史一本 480
		zdscore.setBen1_wl(507); //物理一本 390

		zdscore.setBen985_ls(575);//历史985  
		zdscore.setBen985_wl(567);//物理985  
		
		zdscore.setBenQB_ls(640);      
		zdscore.setBenQB_wl(670);      
		
		zdscore.setZGF_ls(660);        
		zdscore.setZGF_wl(680);        
		 
		zdscore.setMF_ls(665);         
		zdscore.setMF_wl(690);

	
		pcxscore.setZk_ls(180);
		pcxscore.setZk_wl(180);
		pcxscore.setBen2_ls(472);
		pcxscore.setBen2_wl(445);
		pcxscore.setBen1_ls(533);
		pcxscore.setBen1_wl(518);

		pcxscore.setBen985_ls(579);
		pcxscore.setBen985_wl(569);
		
		pcxscore.setBenQB_ls(638);
		pcxscore.setBenQB_wl(670);
		
		pcxscore.setZGF_ls(638);
		pcxscore.setZGF_wl(670);
		
		pcxscore.setMF_ls(638);
		pcxscore.setMF_wl(670);
	
		//江西
		runExcept33("J3", "南昌" , "一模", zdscore, pcxscore);
	}
	
	//采样分换算
	public static void j3_2() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		
		zdscore.setZk_ls(180);
		zdscore.setZk_wl(180);
		zdscore.setBen2_ls(400); //历史二本 380
		zdscore.setBen2_wl(431); //物理二本 295
		zdscore.setBen1_ls(497); //历史一本 480
		zdscore.setBen1_wl(507); //物理一本 390

		zdscore.setBen985_ls(540);//历史985  
		zdscore.setBen985_wl(580);//物理985  
		
		zdscore.setBenQB_ls(599);      
		zdscore.setBenQB_wl(647);      
		
		zdscore.setZGF_ls(665);        
		zdscore.setZGF_wl(695);        
		 
		zdscore.setMF_ls(670);         
		zdscore.setMF_wl(710);

	
		pcxscore.setZk_ls(180);
		pcxscore.setZk_wl(180);
		pcxscore.setBen2_ls(421);
		pcxscore.setBen2_wl(406);
		pcxscore.setBen1_ls(500);
		pcxscore.setBen1_wl(481);

		pcxscore.setBen985_ls(540);
		pcxscore.setBen985_wl(562);
		
		pcxscore.setBenQB_ls(599);
		pcxscore.setBenQB_wl(647);
		
		pcxscore.setZGF_ls(656);
		pcxscore.setZGF_wl(689);
		
		pcxscore.setMF_ls(656);
		pcxscore.setMF_wl(689);
	
		//江西
		runExcept33("J3", null , "九省联考", zdscore, pcxscore);
	}
	
	public static void a1() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		
		zdscore.setZk_ls(180);
		zdscore.setZk_wl(180);
		zdscore.setBen2_ls(460); //历史二本366
		zdscore.setBen2_wl(435); //物理二本328
		zdscore.setBen1_ls(509); //历史一本448
		zdscore.setBen1_wl(475); //物理一本405

		zdscore.setBen985_ls(580);//历史985  
		zdscore.setBen985_wl(565);//物理985  
		
		zdscore.setBenQB_ls(645);      
		zdscore.setBenQB_wl(655);      
		
		zdscore.setZGF_ls(660);        
		zdscore.setZGF_wl(680);        
		 
		zdscore.setMF_ls(680);         
		zdscore.setMF_wl(699);
		
		/**
		pcxscore.setZk_ls(200);
		pcxscore.setZk_wl(200);
		pcxscore.setBen2_ls(409);
		pcxscore.setBen2_wl(423);
		pcxscore.setBen1_ls(512);
		pcxscore.setBen1_wl(535);

		pcxscore.setBen985_ls(585);
		pcxscore.setBen985_wl(630);
		
		pcxscore.setBenQB_ls(624);
		pcxscore.setBenQB_wl(665);
		
		pcxscore.setZGF_ls(640);
		pcxscore.setZGF_wl(690);
		
		pcxscore.setMF_ls(640);
		pcxscore.setMF_wl(690);
		*/
		
		
	
		pcxscore.setZk_ls(200);
		pcxscore.setZk_wl(200);
		pcxscore.setBen2_ls(440);
		pcxscore.setBen2_wl(427);
		pcxscore.setBen1_ls(495);
		pcxscore.setBen1_wl(482);

		pcxscore.setBen985_ls(580);
		pcxscore.setBen985_wl(586);
		
		pcxscore.setBenQB_ls(648);
		pcxscore.setBenQB_wl(689);
		
		pcxscore.setZGF_ls(648);
		pcxscore.setZGF_wl(689);
		
		pcxscore.setMF_ls(648);
		pcxscore.setMF_wl(689);
	
		//安徽
		runExcept33("A1", "合肥" , "一模", zdscore, pcxscore);
	}
	
	public static void h3() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		
		zdscore.setZk_ls(185);
		zdscore.setZk_wl(185);
		zdscore.setBen2_ls(397);
		zdscore.setBen2_wl(379);
		zdscore.setBen1_ls(488);
		zdscore.setBen1_wl(456);
		
		zdscore.setBen985_ls(569);//
		zdscore.setBen985_wl(574);//
		
		zdscore.setBenQB_ls(633);
		zdscore.setBenQB_wl(660);
		
		zdscore.setZGF_ls(650);
		zdscore.setZGF_wl(690);
		
		zdscore.setMF_ls(660);
		zdscore.setMF_wl(690);
		
		
		pcxscore.setZk_ls(185);
		pcxscore.setZk_wl(185);
		pcxscore.setBen2_ls(428);
		pcxscore.setBen2_wl(396);
		pcxscore.setBen1_ls(521);
		pcxscore.setBen1_wl(511);
		
		pcxscore.setBen985_ls(589);
		pcxscore.setBen985_wl(596);
		
		pcxscore.setBenQB_ls(656);
		pcxscore.setBenQB_wl(696);
		
		pcxscore.setZGF_ls(656);
		pcxscore.setZGF_wl(696);
		
		pcxscore.setMF_ls(700);
		pcxscore.setMF_wl(710);
		
		runExcept33("H3", null, "九师联盟11月联考", zdscore, pcxscore);
	}
	
	
	public static void l1() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		
		zdscore.setZk_ls(180);
		zdscore.setZk_wl(180);
		zdscore.setBen2_ls(437);
		zdscore.setBen2_wl(396);
		zdscore.setBen1_ls(550);
		zdscore.setBen1_wl(480);
		
		zdscore.setBen985_ls(611);//
		zdscore.setBen985_wl(570);//
		
		zdscore.setBenQB_ls(655);
		zdscore.setBenQB_wl(642);
		
		zdscore.setZGF_ls(657);
		zdscore.setZGF_wl(685);
		
		zdscore.setMF_ls(665);
		zdscore.setMF_wl(690);
		
		
		pcxscore.setZk_ls(180);
		pcxscore.setZk_wl(180);
		pcxscore.setBen2_ls(484);
		pcxscore.setBen2_wl(460);
		pcxscore.setBen1_ls(582);
		pcxscore.setBen1_wl(534);
		
		pcxscore.setBen985_ls(627);
		pcxscore.setBen985_wl(605);
		
		pcxscore.setBenQB_ls(664);
		pcxscore.setBenQB_wl(662);
		
		pcxscore.setZGF_ls(667);
		pcxscore.setZGF_wl(696);
		
		pcxscore.setMF_ls(667);
		pcxscore.setMF_wl(696);
		
		/**
		pcxscore.setZk_ls(180);
		pcxscore.setZk_wl(180);
		pcxscore.setBen2_ls(404);
		pcxscore.setBen2_wl(360);
		pcxscore.setBen1_ls(495);
		pcxscore.setBen1_wl(494);
		
		pcxscore.setBen985_ls(580);
		pcxscore.setBen985_wl(574);
		
		pcxscore.setBenQB_ls(668);
		pcxscore.setBenQB_wl(697);
		
		pcxscore.setZGF_ls(668);
		pcxscore.setZGF_wl(697);
		
		pcxscore.setMF_ls(668);
		pcxscore.setMF_wl(697);
		**/
		
		runExcept33("L1", "沈阳", "一模", zdscore, pcxscore);
	}
	
	public static void g4() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		
		zdscore.setZk_ls(180);
		zdscore.setZk_wl(180);
		zdscore.setBen2_ls(463);
		zdscore.setBen2_wl(365);
		zdscore.setBen1_ls(536);
		zdscore.setBen1_wl(457);
		
		zdscore.setBen985_ls(599);//
		zdscore.setBen985_wl(562);//
		
		zdscore.setBenQB_ls(658);
		zdscore.setBenQB_wl(677);
		
		zdscore.setZGF_ls(680);
		zdscore.setZGF_wl(690);
		
		zdscore.setMF_ls(680);
		zdscore.setMF_wl(690);
		
		
		pcxscore.setZk_ls(180);
		pcxscore.setZk_wl(180);
		pcxscore.setBen2_ls(477);
		pcxscore.setBen2_wl(371);
		pcxscore.setBen1_ls(545);
		pcxscore.setBen1_wl(459);
		
		pcxscore.setBen985_ls(607);
		pcxscore.setBen985_wl(565);
		
		pcxscore.setBenQB_ls(667);
		pcxscore.setBenQB_wl(684);
		
		pcxscore.setZGF_ls(679);
		pcxscore.setZGF_wl(684);
		
		pcxscore.setMF_ls(679);
		pcxscore.setMF_wl(684);
		
		runExcept33("G4", null, "九省联考", zdscore, pcxscore);
	}
	
	
	public static void s1() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		
		zdscore.setZk_ls(280);
		zdscore.setZk_wl(280);
		
		zdscore.setBen2_ls(392); 
		zdscore.setBen2_wl(392);
		zdscore.setBen1_ls(460);  
		zdscore.setBen1_wl(460);
		
		zdscore.setBen985_ls(564);//
		zdscore.setBen985_wl(564);//
		
		zdscore.setBenQB_ls(622);
		zdscore.setBenQB_wl(622);
		
		zdscore.setZGF_ls(640);
		zdscore.setZGF_wl(640);
		
		zdscore.setMF_ls(670);
		zdscore.setMF_wl(670);
		
		
		pcxscore.setZk_ls(280);
		pcxscore.setZk_wl(280);
		pcxscore.setBen2_ls(443);
		pcxscore.setBen2_wl(443);
		pcxscore.setBen1_ls(520);
		pcxscore.setBen1_wl(520);
		
		pcxscore.setBen985_ls(574);
		pcxscore.setBen985_wl(574);
		
		pcxscore.setBenQB_ls(689);
		pcxscore.setBenQB_wl(689);
		
		pcxscore.setZGF_ls(698);
		pcxscore.setZGF_wl(698);
		
		pcxscore.setMF_ls(698);
		pcxscore.setMF_wl(698);
		
		run33("S1", "济南", "一模", zdscore, pcxscore);
	}
	
	
	public static void z1() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		
		zdscore.setZk_ls(270);
		zdscore.setZk_wl(270);
		
		zdscore.setBen2_ls(461); 
		zdscore.setBen2_wl(461); 
		zdscore.setBen1_ls(556); 
		zdscore.setBen1_wl(556);
		
		zdscore.setBen985_ls(605);// 
		zdscore.setBen985_wl(605);// 
		
		zdscore.setBenQB_ls(656); 
		zdscore.setBenQB_wl(656);
		
		zdscore.setZGF_ls(693);
		zdscore.setZGF_wl(693);
		
		zdscore.setMF_ls(710);
		zdscore.setMF_wl(710);
		
		
		pcxscore.setZk_ls(274);
		pcxscore.setZk_wl(274);
		pcxscore.setBen2_ls(488);
		pcxscore.setBen2_wl(488);
		pcxscore.setBen1_ls(594);
		pcxscore.setBen1_wl(594);
		
		pcxscore.setBen985_ls(626);
		pcxscore.setBen985_wl(626);
		
		pcxscore.setBenQB_ls(663); //浙大线
		pcxscore.setBenQB_wl(663);
		
		pcxscore.setZGF_ls(703);
		pcxscore.setZGF_wl(703);
		
		pcxscore.setMF_ls(703);
		pcxscore.setMF_wl(703);
		
		run33("Z1", "金华", "一模", zdscore, pcxscore);
	}
	
	
	//非3+3考区, CITY为NULL表示为所有城市生成
	public static void runExcept33(String sfcode, String city, String zdpc, ZDScore zdscore, PCXScore pcxscore) {
		String provinceCode = JDBC.HM_PROVINCE.get(sfcode); //S5_SC
		String provinceName = JDBC.HM_PROVINCE_CODE_NAME.get(sfcode); //四川
		List<CityBean> cityList = JDBC.HM_PROVINCE_CITY.get(provinceName);
		
		StringBuffer SQL = new StringBuffer();
		
		List<String> listResult = new ArrayList<>();

		if(city != null) {
			cityList = new ArrayList<>();
			CityBean cityBean = new CityBean();
			cityBean.setCityNameExt(city);
			cityList.add(cityBean);
		}
		
		for(CityBean element : cityList) {
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "理科", zdscore.getZk_wl(), zdscore.getBen2_wl(), pcxscore.getZk_wl(), pcxscore.getBen2_wl()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "理科", zdscore.getBen2_wl(), zdscore.getBen1_wl(), pcxscore.getBen2_wl(), pcxscore.getBen1_wl()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "理科", zdscore.getBen1_wl(), zdscore.getBen985_wl(), pcxscore.getBen1_wl(), pcxscore.getBen985_wl()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "理科", zdscore.getBen985_wl(), zdscore.getBenQB_wl(), pcxscore.getBen985_wl(), pcxscore.getBenQB_wl()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "理科", zdscore.getBenQB_wl(), zdscore.getZGF_wl(), pcxscore.getBenQB_wl(), pcxscore.getZGF_wl()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "理科", zdscore.getZGF_wl(), zdscore.getMF_wl(), pcxscore.getZGF_wl(), pcxscore.getMF_wl()));
			
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "文科", zdscore.getZk_ls(), zdscore.getBen2_ls(), pcxscore.getZk_ls(), pcxscore.getBen2_ls()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "文科", zdscore.getBen2_ls(), zdscore.getBen1_ls(), pcxscore.getBen2_ls(), pcxscore.getBen1_ls()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "文科", zdscore.getBen1_ls(), zdscore.getBen985_ls(), pcxscore.getBen1_ls(), pcxscore.getBen985_ls()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "文科", zdscore.getBen985_ls(), zdscore.getBenQB_ls(), pcxscore.getBen985_ls(), pcxscore.getBenQB_ls()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "文科", zdscore.getBenQB_ls(), zdscore.getZGF_ls(), pcxscore.getBenQB_ls(), pcxscore.getZGF_ls()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "文科", zdscore.getZGF_ls(), zdscore.getMF_ls(), pcxscore.getZGF_ls(), pcxscore.getMF_ls()));
		}
		
		for(String str : listResult) {
			SQL.append(str);
		}
		
		writeTempFile(new File("F://诊断考试换算//M999YXX_NC1_"+provinceCode+"_"+zdpc+".txt"), SQL);
	}
	
	public static void run33(String sfcode, String city, String zdpc, ZDScore zdscore, PCXScore pcxscore) {
		String provinceCode = JDBC.HM_PROVINCE.get(sfcode); //S5_SC
		String provinceName = JDBC.HM_PROVINCE_CODE_NAME.get(sfcode); //四川
		List<CityBean> cityList = JDBC.HM_PROVINCE_CITY.get(provinceName);
		
		StringBuffer SQL = new StringBuffer();
		
		List<String> listResult = new ArrayList<>();

		if(city != null) {
			cityList = new ArrayList<>();
			CityBean cityBean = new CityBean();
			cityBean.setCityNameExt(city);
			cityList.add(cityBean);
		}
		
		for(CityBean element : cityList) {
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "综合", zdscore.getZk_wl(), zdscore.getBen2_wl(), pcxscore.getZk_wl(), pcxscore.getBen2_wl()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "综合", zdscore.getBen2_wl(), zdscore.getBen1_wl(), pcxscore.getBen2_wl(), pcxscore.getBen1_wl()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "综合", zdscore.getBen1_wl(), zdscore.getBen985_wl(), pcxscore.getBen1_wl(), pcxscore.getBen985_wl()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "综合", zdscore.getBen985_wl(), zdscore.getBenQB_wl(), pcxscore.getBen985_wl(), pcxscore.getBenQB_wl()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "综合", zdscore.getBenQB_wl(), zdscore.getZGF_wl(), pcxscore.getBenQB_wl(), pcxscore.getZGF_wl()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "综合", zdscore.getZGF_wl(), zdscore.getMF_wl(), pcxscore.getZGF_wl(), pcxscore.getMF_wl()));
		}
		
		for(String str : listResult) {
			SQL.append(str);
		}
		
		writeTempFile(new File("F://诊断考试换算//SD_"+provinceCode+"_"+zdpc+"+"+city+".txt"), SQL);
	}
	
	public static List<String> runConvertLatest(String sf, String city, String zdpc, String kl, int scoreZDFrom, int scoreZDTo, int gaokao23Start, int gaokao23To) {
		List<String> list = new ArrayList<>();
		String provinceCode = JDBC.HM_PROVINCE_CODE.get(sf);
		double newStart = gaokao23Start;
		for(int i = scoreZDFrom;i < scoreZDTo;i++) {
			newStart = Arith.add(newStart, Arith.div(gaokao23To - gaokao23Start, scoreZDTo - scoreZDFrom)); 
			if(newStart > gaokao23To) {
				newStart = gaokao23To;
			}
			list.add("INSERT INTO "+provinceCode+"_zdks_score_convert(nf, sf, city, ZDPC, KL, KL_CODE, SCORE_FROM, SCORE_TO) VALUES(2025, '"+sf+"', '"+city+"', '"+zdpc+"', '"+kl+"', '"+xk_code_map.get(kl)+"' , "+i+","+Math.round(newStart)+" );\r\n");
			
		}
		
		return list;
		
	}
	
	public static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
}
