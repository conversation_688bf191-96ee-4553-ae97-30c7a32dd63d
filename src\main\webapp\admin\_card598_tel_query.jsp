<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page import="com.zsdwf.utils.*,com.zsdwf.db.*,java.util.*" %>

<%@include file="/WEB-INF/include/_session_admin_ajax.jsp" %>
<%
YGCardBean card = (YGCardBean)session.getAttribute("SES_ADMIN");
String tel = Tools.trim(request.getParameter("tel"));
String orderID = Tools.trim(request.getParameter("orderID"));


if(Tools.isEmpty(tel)){
	tel = "%";
}

if(Tools.isEmpty(orderID)){
	orderID = "%";
}

YGJDBC jdbc = new YGJDBC();
List<BaseCardBean> list = jdbc.searchCardsByPhoneAndOrderId(tel, orderID, card.getId()); 

for(int i=0;i<list.size();i++){
	BaseCardBean bean = list.get(i);
%>
<tr><td><%=bean.getId() %></td><td><%=bean.getPasswd() %></td><td><%=bean.getPhone() %></td><td><%=bean.getExt() %></td><td><%=bean.getCtype() %></td><td><%=Tools.getDateTm(bean.getActive()) %></td><td><%=bean.getAgent() %></td></tr>         
<%
}

%>



		