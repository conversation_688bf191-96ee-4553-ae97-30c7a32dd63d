package com.career.utils.report;

import com.career.db.*;
import com.career.utils.*;
import java.util.*;

/**
 * 录取概率评估器
 * 基于志愿表单和预测趋势分计算录取概率，预测录取结果
 * 
 * 使用示例：
 * <pre>
 * // 1. 创建评估上下文
 * FormReviewEvaluator.FormReviewContext context = new FormReviewEvaluator.FormReviewContext(...);
 * 
 * // 2. 执行录取概率评估
 * AdmissionProbabilityResult result = AdmissionProbabilityEvaluator.evaluateAdmissionProbability(context);
 * 
 * // 3. 获取评估结果
 * List&lt;VolunteerAdmissionResult&gt; volunteerResults = result.getVolunteerResults();
 * List&lt;AdmissionPrediction&gt; predictions = result.getAdmissionPredictions();
 * Map&lt;String, MajorProbabilityInfo&gt; majorData = result.getMajorProbabilityMap();
 * </pre>
 * 
 * <AUTHOR> Team
 * @version 2.0
 */
public class AdmissionProbabilityEvaluator {
    
    // ==================== 录取概率阈值配置 ====================
    /** 录取概率临界值：70%以上认为可录取 */
    public static final double ADMISSION_THRESHOLD = 70.0;
    /** 最高录取概率：99% */
    public static final double MAX_ADMISSION_PROBABILITY = 99.0;
    /** 最低录取概率：1% */
    public static final double MIN_ADMISSION_PROBABILITY = 1.0;
    /** 概率计算正分数上限：20分 */
    public static final int POSITIVE_SCORE_LIMIT = 20;
    /** 概率计算负分数下限：-25分 */
    public static final int NEGATIVE_SCORE_LIMIT = -25;
    /** 最大录取预测数量：显示前3个录取结果 */
    public static final int MAX_ADMISSION_PREDICTIONS = 3;
    
    // ==================== 概率计算配置 ====================
    /** 概率计算基础值：50% */
    public static final double PROBABILITY_BASE_VALUE = 50.0;
    /** 概率计算分数系数：每分影响5%概率 */
    public static final double PROBABILITY_SCORE_COEFFICIENT = 5.0;
    
    /**
     * 主要评估方法：计算所有志愿的录取概率并预测录取结果
     * 
     * @param context 表单审核上下文
     * @return 录取概率评估结果
     */
    public static AdmissionProbabilityResult evaluateAdmissionProbability(FormReviewEvaluator.FormReviewContext context) {
        Tools.println("[evaluateAdmissionProbability] 开始录取概率评估");
        
        if (context == null || context.getSuperFormList() == null || context.getSuperFormMain() == null) {
            Tools.println("[evaluateAdmissionProbability] 上下文数据不完整，返回空结果");
            return new AdmissionProbabilityResult();
        }
        
        List<SuperForm> superFormList = context.getSuperFormList();
        int studentScore = context.getSuperFormMain().getScore_cj();
        
        Tools.println("[evaluateAdmissionProbability] 考生成绩: " + studentScore + ", 志愿表单数量: " + superFormList.size());
        
        // 确保加载详细计划数据
        if (!context.isPlanDataLoaded()) {
            context.loadDetailedPlanData();
        }
        
        AdmissionProbabilityResult result = new AdmissionProbabilityResult();
        result.setStudentScore(studentScore);
        result.setTotalVolunteers(superFormList.size());
        
        // 步骤1：计算所有专业的录取概率数据
        Map<String, MajorProbabilityInfo> majorProbabilityMap = calculateAllMajorProbabilities(context, superFormList, studentScore);
        result.setMajorProbabilityMap(majorProbabilityMap);
        
        // 步骤2：基于专业数据计算志愿录取结果
        List<VolunteerAdmissionResult> volunteerResults = calculateVolunteerAdmissionProbabilities(context, superFormList, studentScore, majorProbabilityMap);
        result.setVolunteerResults(volunteerResults);
        
        // 步骤3：预测录取结果
        List<AdmissionPrediction> predictions = predictAdmissionResults(volunteerResults);
        result.setAdmissionPredictions(predictions);
        
        // 步骤4：生成统计信息
        generateStatistics(result, volunteerResults);
        
        Tools.println("[evaluateAdmissionProbability] 录取概率评估完成，有效志愿: " + volunteerResults.size() + ", 预测录取: " + predictions.size() + ", 专业数据: " + majorProbabilityMap.size());
        return result;
    }
    
    /**
     * 计算所有志愿的录取概率
     */
    private static List<VolunteerAdmissionResult> calculateVolunteerAdmissionProbabilities(
            FormReviewEvaluator.FormReviewContext context, 
            List<SuperForm> superFormList, 
            int studentScore,
            Map<String, MajorProbabilityInfo> majorProbabilityMap) {
        
        List<VolunteerAdmissionResult> volunteerResults = new ArrayList<>();
        Set<String> processedVolunteers = new HashSet<>();  // 用于去重的志愿集合（学校代码+专业组代码）
        int volunteerNumber = 1;  // 使用去重后的志愿序号
        
        Tools.println("[calculateVolunteerAdmissionProbabilities] 开始计算录取概率，总专业数: " + superFormList.size());
        
        for (int i = 0; i < superFormList.size(); i++) {
            SuperForm superForm = superFormList.get(i);
            
            // 构建志愿唯一标识（学校代码+专业组代码）
            String volunteerKey = superForm.getYxdm() + "_" + (superForm.getZyz() != null ? superForm.getZyz() : "");
            
            // 如果该志愿已经处理过，跳过（避免重复计算同一个志愿的多个专业）
            if (processedVolunteers.contains(volunteerKey)) {
                continue;
            }
            processedVolunteers.add(volunteerKey);
            
            VolunteerAdmissionResult volunteerResult = calculateSingleVolunteerAdmission(context, superForm, studentScore, volunteerNumber, majorProbabilityMap);
            
            if (volunteerResult != null) {
                volunteerResults.add(volunteerResult);
            }
            
            volunteerNumber++;  // 递增去重后的志愿序号
        }
        
        Tools.println("[calculateVolunteerAdmissionProbabilities] 录取概率计算完成，处理了 " + volunteerResults.size() + " 个志愿");
        return volunteerResults;
    }
    
    /**
     * 计算单个志愿的录取概率
     */
    private static VolunteerAdmissionResult calculateSingleVolunteerAdmission(
            FormReviewEvaluator.FormReviewContext context, 
            SuperForm superForm, 
            int studentScore, 
            int volunteerNumber,
            Map<String, MajorProbabilityInfo> majorProbabilityMap) {
        
        String yxdm = superForm.getYxdm();
        String zyz = superForm.getZyz();
        String yxmc = superForm.getYxmc_org();
        String filledMajorCode = superForm.getZydm();
        String adjustZy = superForm.getAdjust_zy(); // 获取服从调剂选项
        boolean isObedience = "1".equals(adjustZy); // 1表示服从调剂
        
        // 数据验证和调试信息
        Tools.println("[calculateSingleVolunteerAdmission] 处理志愿" + volunteerNumber + 
                     " - 院校代码: " + yxdm + ", 院校名称: " + yxmc + ", 专业组: " + zyz + 
                     ", 填报专业代码: " + filledMajorCode + ", 服从调剂: " + isObedience);
        
        // 获取用户选择的专业录取概率数据
        List<MajorAdmissionInfo> selectedMajorInfoList = getMajorInfoListFromCache(context, yxdm, zyz, studentScore, majorProbabilityMap);
        
        if (selectedMajorInfoList.isEmpty()) {
            Tools.println("[calculateSingleVolunteerAdmission] 志愿 " + volunteerNumber + " 无有效专业数据");
            return null;
        }
        
        Tools.println("[calculateSingleVolunteerAdmission] 志愿" + volunteerNumber + " 获取到 " + selectedMajorInfoList.size() + " 个专业数据");
        for (MajorAdmissionInfo info : selectedMajorInfoList) {
            Tools.println("[calculateSingleVolunteerAdmission] - 专业: " + info.getZymc() + " (代码: " + info.getZydm() + ", 概率: " + String.format("%.2f", info.getAdmissionProbability()) + "%)");
        }
        
        VolunteerAdmissionResult volunteerResult = new VolunteerAdmissionResult();
        volunteerResult.setVolunteerNumber(volunteerNumber);
        volunteerResult.setYxdm(yxdm);
        volunteerResult.setYxmc(yxmc);
        volunteerResult.setZyz(zyz);
        volunteerResult.setFilledMajor(superForm.getZymc());
        volunteerResult.setFilledMajorCode(filledMajorCode);
        volunteerResult.setObedience(isObedience); // 设置服从调剂标志
        
        // 【重要修改】：首先检查用户填报的专业录取概率
        MajorAdmissionInfo filledMajorInfo = findFilledMajorInfo(selectedMajorInfoList, filledMajorCode);
        
        Tools.println("[calculateSingleVolunteerAdmission] 志愿" + volunteerNumber + " 查找填报专业 " + filledMajorCode + " 结果: " + (filledMajorInfo != null ? "找到" : "未找到"));
        
        if (filledMajorInfo != null) {
            double filledMajorProbability = filledMajorInfo.getAdmissionProbability();
            volunteerResult.setFilledMajorProbability(filledMajorProbability);
            volunteerResult.setFilledMajorPredictedScore(filledMajorInfo.getPredictedScore());
            volunteerResult.setFilledMajorHasData(filledMajorInfo.isHasData());
            
            Tools.println("[calculateSingleVolunteerAdmission] *** 关键判断 *** 志愿" + volunteerNumber + 
                         " 填报专业 " + filledMajorInfo.getZymc() + " (代码: " + filledMajorCode + ")" +
                         " 录取概率: " + String.format("%.2f", filledMajorProbability) + "%, 阈值: " + ADMISSION_THRESHOLD + "%");
            
                    // 【修正逻辑】：首先检查用户选择的所有专业，找到录取概率最高的专业
        MajorAdmissionInfo bestUserMajor = null;
        double bestProbability = 0.0;
        
        // 遍历用户选择的所有专业，找到最佳专业
        for (MajorAdmissionInfo info : selectedMajorInfoList) {
            if (info.getAdmissionProbability() > bestProbability) {
                bestProbability = info.getAdmissionProbability();
                bestUserMajor = info;
            }
        }
        
        Tools.println("[calculateSingleVolunteerAdmission] *** 用户选择专业中最佳录取概率: " + 
                     (bestUserMajor != null ? bestUserMajor.getZymc() + " " + String.format("%.2f", bestProbability) + "%" : "无") + " ***");
        
        // 如果用户选择的最佳专业≥70%，设置为正常录取
        if (bestUserMajor != null && bestProbability >= ADMISSION_THRESHOLD) {
            Tools.println("[calculateSingleVolunteerAdmission] *** 用户选择的专业 " + bestUserMajor.getZymc() + 
                         " 录取概率≥" + ADMISSION_THRESHOLD + "%，设置为正常录取 ***");
            volunteerResult.setAdmissionType(AdmissionType.NORMAL);
            volunteerResult.setAdmissionProbability(bestProbability);
            volunteerResult.setAdmittedMajor(bestUserMajor.getZymc());
            volunteerResult.setAdmittedMajorCode(bestUserMajor.getZydm());
            volunteerResult.setScoreDifference(studentScore - bestUserMajor.getPredictedScore());
            return volunteerResult;
        } else {
            Tools.println("[calculateSingleVolunteerAdmission] *** 用户选择的所有专业录取概率都<" + ADMISSION_THRESHOLD + 
                         "%，检查是否服从调剂 ***");
            
            // 只有在服从调剂的情况下，才检查专业组内其他专业
            if (!isObedience) {
                Tools.println("[calculateSingleVolunteerAdmission] *** 志愿" + volunteerNumber + " 用户不服从调剂，设置为无法录取 ***");
                volunteerResult.setAdmissionType(AdmissionType.NONE);
                volunteerResult.setAdmissionProbability(filledMajorProbability);
                volunteerResult.setAdmittedMajor(filledMajorInfo.getZymc());
                volunteerResult.setAdmittedMajorCode(filledMajorInfo.getZydm());
                volunteerResult.setScoreDifference(studentScore - filledMajorInfo.getPredictedScore());
                return volunteerResult;
            }
        }
        } else {
            Tools.println("[calculateSingleVolunteerAdmission] *** 警告 *** 志愿" + volunteerNumber + " 未找到填报专业 " + filledMajorCode + " 的录取概率数据");
        }
        
        // 【服从调剂逻辑】：只有当用户选择的所有专业都<70%且服从调剂时才执行
        Tools.println("[calculateSingleVolunteerAdmission] *** 志愿" + volunteerNumber + " 用户选择服从调剂，检查专业组其他专业 ***");
        
        // 在专业组的其他专业中查找调剂机会（排除用户已选择的专业）
        List<MajorAdmissionInfo> allMajorInfoList = getAllMajorInfoListFromFullData(context, yxdm, zyz, studentScore);
        MajorAdmissionInfo bestObedienceMajor = findBestObedienceAdjustmentMajor(allMajorInfoList, selectedMajorInfoList);
        
        if (bestObedienceMajor != null && bestObedienceMajor.getAdmissionProbability() >= ADMISSION_THRESHOLD) {
            Tools.println("[calculateSingleVolunteerAdmission] *** 志愿" + volunteerNumber + " 找到服从调剂专业: " + 
                         bestObedienceMajor.getZymc() + " (概率: " + String.format("%.2f", bestObedienceMajor.getAdmissionProbability()) + 
                         "%)，设置为服从调剂录取 ***");
            
            volunteerResult.setAdmissionType(AdmissionType.OBEDIENCE_ADJUSTMENT);
            volunteerResult.setAdmissionProbability(bestObedienceMajor.getAdmissionProbability());
            volunteerResult.setAdmittedMajor(bestObedienceMajor.getZymc());
            volunteerResult.setAdmittedMajorCode(bestObedienceMajor.getZydm());
            volunteerResult.setObedienceAdjustmentMajorProbability(bestObedienceMajor.getAdmissionProbability());
            volunteerResult.setObedienceAdjustmentMajorPredictedScore(bestObedienceMajor.getPredictedScore());
            volunteerResult.setScoreDifference(studentScore - bestObedienceMajor.getPredictedScore());
            
            return volunteerResult;
        } else {
            Tools.println("[calculateSingleVolunteerAdmission] *** 志愿" + volunteerNumber + " 未找到符合条件的服从调剂专业，设置为无法录取 ***");
            volunteerResult.setAdmissionType(AdmissionType.NONE);
        }
        
        // 最终设置
        volunteerResult.setAdmissionProbability(filledMajorInfo != null ? filledMajorInfo.getAdmissionProbability() : 0.0);
        if (filledMajorInfo != null) {
            volunteerResult.setScoreDifference(studentScore - filledMajorInfo.getPredictedScore());
        } else {
            volunteerResult.setScoreDifference(0);
        }
        
        return volunteerResult;
    }

    
    /**
     * 从缓存的专业概率数据中获取专业组内所有专业的录取概率列表
     * 
     * @param context FormReviewContext
     * @param yxdm 院校代码
     * @param zyz 专业组代码
     * @param studentScore 考生成绩
     * @param majorProbabilityMap 专业录取概率数据映射
     * @return 专业录取概率列表
     */
    private static List<MajorAdmissionInfo> getMajorInfoListFromCache(
            FormReviewEvaluator.FormReviewContext context, 
            String yxdm, String zyz, int studentScore,
            Map<String, MajorProbabilityInfo> majorProbabilityMap) {
        
        List<MajorAdmissionInfo> majorInfoList = new ArrayList<>();
        
        // 遍历缓存的专业数据，筛选出属于该专业组的专业
        for (MajorProbabilityInfo majorInfo : majorProbabilityMap.values()) {
            if (Tools.trim(majorInfo.getYxdm()).equals(Tools.trim(yxdm)) && 
                Tools.trim(majorInfo.getZyz() != null ? majorInfo.getZyz() : "").equals(Tools.trim(zyz != null ? zyz : ""))) {
                
                // 将MajorProbabilityInfo转换为MajorAdmissionInfo
                MajorAdmissionInfo admissionInfo = new MajorAdmissionInfo();
                admissionInfo.setYxdm(majorInfo.getYxdm());
                admissionInfo.setYxmc(majorInfo.getYxmc());
                admissionInfo.setZydm(majorInfo.getMajorCode());
                admissionInfo.setZymc(majorInfo.getMajorName());
                admissionInfo.setZyz(majorInfo.getZyz());
                admissionInfo.setPredictedScore(majorInfo.getPredictedScore());
                admissionInfo.setAdmissionProbability(majorInfo.getAdmissionProbability());
                admissionInfo.setHasData(majorInfo.isHasData());
                Tools.println("[******getMajorInfoListFromCache******] 志愿下每个专业的录取概率" + 
                        " - 院校代码: " + majorInfo.getYxdm() + ", 院校名称: " + majorInfo.getYxmc() + ", 专业组: " + majorInfo.getZyz() + ", 专业代码: " + majorInfo.getMajorCode() + " -> 录取概率: " + admissionInfo.getAdmissionProbability());
                majorInfoList.add(admissionInfo);
            }
        }
        
        // 按录取概率降序排序
        majorInfoList.sort((a, b) -> Double.compare(b.getAdmissionProbability(), a.getAdmissionProbability()));
        
        return majorInfoList;
    }
    
    /**
     * 查找填报专业的录取概率信息
     */
    private static MajorAdmissionInfo findFilledMajorInfo(
            List<MajorAdmissionInfo> majorInfoList, String filledMajorCode) {
        
        Tools.println("[findFilledMajorInfo] 查找填报专业代码: '" + filledMajorCode + "'");
        Tools.println("[findFilledMajorInfo] 专业列表中有 " + majorInfoList.size() + " 个专业:");
        
        String trimmedFilledMajorCode = Tools.trim(filledMajorCode);
        
        // 第一轮：精确匹配
        for (MajorAdmissionInfo info : majorInfoList) {
            String infoMajorCode = Tools.trim(info.getZydm());
            
            Tools.println("[findFilledMajorInfo] - 检查专业: " + info.getZymc() + " (代码: '" + infoMajorCode + "')");
            Tools.println("[findFilledMajorInfo] - 精确匹配: '" + infoMajorCode + "' vs '" + trimmedFilledMajorCode + "' = " + infoMajorCode.equals(trimmedFilledMajorCode));
            
            if (infoMajorCode.equals(trimmedFilledMajorCode)) {
                Tools.println("[findFilledMajorInfo] 精确匹配成功: " + info.getZymc() + " (代码: " + info.getZydm() + ")");
                return info;
            }
        }
        
        // 第二轮：忽略大小写匹配
        for (MajorAdmissionInfo info : majorInfoList) {
            String infoMajorCode = Tools.trim(info.getZydm());
            
            if (infoMajorCode.equalsIgnoreCase(trimmedFilledMajorCode)) {
                Tools.println("[findFilledMajorInfo] 忽略大小写匹配成功: " + info.getZymc() + " (代码: " + info.getZydm() + ")");
                return info;
            }
        }
        
        // 第三轮：包含匹配（处理可能的前缀后缀问题）
        for (MajorAdmissionInfo info : majorInfoList) {
            String infoMajorCode = Tools.trim(info.getZydm());
            
            if (infoMajorCode.contains(trimmedFilledMajorCode) || trimmedFilledMajorCode.contains(infoMajorCode)) {
                Tools.println("[findFilledMajorInfo] 包含匹配成功: " + info.getZymc() + " (代码: " + info.getZydm() + ")");
                return info;
            }
        }
        
        Tools.println("[findFilledMajorInfo] 所有匹配方式都失败，未找到专业代码: '" + filledMajorCode + "'");
        Tools.println("[findFilledMajorInfo] 可用的专业代码列表:");
        for (MajorAdmissionInfo info : majorInfoList) {
            Tools.println("[findFilledMajorInfo] - '" + info.getZydm() + "' -> " + info.getZymc());
        }
        
        return null;
    }
    
    /**
     * 查找与填报专业代码相似的专业（用于处理专业代码不匹配的情况）
     */
    private static MajorAdmissionInfo findSimilarMajor(
            List<MajorAdmissionInfo> majorInfoList, String filledMajorCode) {
        
        String trimmedFilledMajorCode = Tools.trim(filledMajorCode);
        Tools.println("[findSimilarMajor] 查找与专业代码 '" + trimmedFilledMajorCode + "' 相似的专业");
        
        // 查找录取概率最高的专业作为备选
        MajorAdmissionInfo bestMajor = null;
        for (MajorAdmissionInfo info : majorInfoList) {
            if (bestMajor == null || info.getAdmissionProbability() > bestMajor.getAdmissionProbability()) {
                bestMajor = info;
            }
        }
        
        if (bestMajor != null) {
            Tools.println("[findSimilarMajor] 找到最佳备选专业: " + bestMajor.getZymc() + 
                         " (代码: " + bestMajor.getZydm() + ", 概率: " + String.format("%.2f", bestMajor.getAdmissionProbability()) + "%)");
        }
        
        return bestMajor;
    }
    
    /**
     * 预测录取结果
     * 修改后的算法：按志愿顺序（而不是录取概率）选择前3个能录取的志愿
     */
    private static List<AdmissionPrediction> predictAdmissionResults(List<VolunteerAdmissionResult> volunteerResults) {
        List<AdmissionPrediction> finalPredictions = new ArrayList<>();
        Set<String> processedSchoolGroups = new HashSet<>();  // 已处理的学校+专业组组合
        
        Tools.println("[predictAdmissionResults] 开始预测录取结果，总志愿数: " + volunteerResults.size());
        
        // 按志愿序号排序（确保按志愿顺序处理）
        volunteerResults.sort((v1, v2) -> Integer.compare(v1.getVolunteerNumber(), v2.getVolunteerNumber()));
        
        int predictionRank = 1;
        
        // 按志愿顺序遍历，选择前3个能录取的志愿
        for (VolunteerAdmissionResult volunteerResult : volunteerResults) {
            // 只考虑可录取的志愿（包括服从调剂录取）
            if (volunteerResult.getAdmissionType() != AdmissionType.NONE && 
                volunteerResult.getAdmissionProbability() >= ADMISSION_THRESHOLD) {
                
                // 创建学校+专业组的唯一标识
                String schoolGroupKey = volunteerResult.getYxmc() + "_" + volunteerResult.getZyz();
                
                // 检查是否已经处理过这个学校+专业组的组合
                if (!processedSchoolGroups.contains(schoolGroupKey)) {
                    processedSchoolGroups.add(schoolGroupKey);
                    
                    AdmissionPrediction prediction = new AdmissionPrediction();
                    prediction.setPredictionRank(predictionRank);
                    prediction.setVolunteerNumber(volunteerResult.getVolunteerNumber());
                    prediction.setYxmc(volunteerResult.getYxmc());
                    prediction.setAdmittedMajor(volunteerResult.getAdmittedMajor());
                    prediction.setAdmissionType(volunteerResult.getAdmissionType());
                    prediction.setAdmissionProbability(volunteerResult.getAdmissionProbability());
                    prediction.setPredictionDescription(generatePredictionDescription(prediction));
                    
                    finalPredictions.add(prediction);
                    
                    String admissionTypeDesc = "";
                    switch (volunteerResult.getAdmissionType()) {
                        case NORMAL:
                            admissionTypeDesc = "正常录取";
                            break;
                        case OBEDIENCE_ADJUSTMENT:
                            admissionTypeDesc = "服从调剂录取";
                            break;
                    }
                    
                    Tools.println("[predictAdmissionResults] 录取预测 " + predictionRank + " -> 志愿" + 
                                 volunteerResult.getVolunteerNumber() + " " + volunteerResult.getYxmc() + 
                                 " " + admissionTypeDesc + " 录取概率: " + String.format("%.2f", volunteerResult.getAdmissionProbability()) + "%");
                    
                    predictionRank++;
                    
                    // 达到3个预测结果时停止
                    if (predictionRank > MAX_ADMISSION_PREDICTIONS) {
                        break;
                    }
                } else {
                    Tools.println("[predictAdmissionResults] 跳过重复的学校+专业组: " + schoolGroupKey + 
                                 " (志愿" + volunteerResult.getVolunteerNumber() + ")");
                }
            }
        }
        
        Tools.println("[predictAdmissionResults] 录取预测完成，按志愿顺序选择了 " + finalPredictions.size() + " 个预测结果");
        return finalPredictions;
    }
    
    /**
     * 生成预测描述
     */
    private static String generatePredictionDescription(AdmissionPrediction prediction) {
        return String.format("第%d概率录取 - %s %s (%.1f%%)", 
                           prediction.getPredictionRank(),
                           prediction.getYxmc(), 
                           prediction.getAdmittedMajor(),
                           prediction.getAdmissionProbability());
    }
    
    /**
     * 生成统计信息
     */
    private static void generateStatistics(AdmissionProbabilityResult result, List<VolunteerAdmissionResult> volunteerResults) {
        int totalVolunteers = volunteerResults.size();
        int admittableVolunteers = 0;
        int normalAdmissionCount = 0;
        int obedienceAdjustmentAdmissionCount = 0; // 新增：服从调剂录取计数
        int noAdmissionCount = 0;
        
        double totalProbability = 0.0;
        double maxProbability = 0.0;
        
        for (VolunteerAdmissionResult volunteerResult : volunteerResults) {
            switch (volunteerResult.getAdmissionType()) {
                case NORMAL:
                    normalAdmissionCount++;
                    admittableVolunteers++;
                    break;
                case OBEDIENCE_ADJUSTMENT:
                    obedienceAdjustmentAdmissionCount++;
                    admittableVolunteers++;
                    break;
                case NONE:
                    noAdmissionCount++;
                    break;
            }
            
            totalProbability += volunteerResult.getAdmissionProbability();
            maxProbability = Math.max(maxProbability, volunteerResult.getAdmissionProbability());
        }
        
        // 设置可录取志愿数（正常录取和服从调剂录取的总和）
        result.setAdmittableVolunteers(admittableVolunteers);
        // 设置正常录取志愿数
        result.setNormalAdmissionCount(normalAdmissionCount);
        // 设置服从调剂录取志愿数
        result.setObedienceAdjustmentAdmissionCount(obedienceAdjustmentAdmissionCount);
        // 设置无法录取志愿数
        result.setNoAdmissionCount(noAdmissionCount);
        // 设置平均录取概率：如果总志愿数大于0，则为总概率除以总志愿数，否则为0.0
        result.setAverageProbability(totalVolunteers > 0 ? totalProbability / totalVolunteers : 0.0);
        // 设置最高录取概率
        result.setMaxProbability(maxProbability);
        // 设置录取率：如果总志愿数大于0，则为可录取志愿数除以总志愿数，否则为0.0
        double admissionRate = totalVolunteers > 0 ? (double) admittableVolunteers / totalVolunteers : 0.0;
        result.setAdmissionRate(admissionRate);
        
        Tools.println("[generateStatistics] 统计完成 - 总志愿: " + totalVolunteers + 
                     ", 可录取: " + admittableVolunteers + " (正常: " + normalAdmissionCount + 
                     ", 服从调剂: " + obedienceAdjustmentAdmissionCount + 
                     ", 无法录取: " + noAdmissionCount + ")");
    }
    
    /**
     * 计算所有专业的录取概率数据
     * 
     * @param context FormReviewContext
     * @param superFormList 志愿表单列表
     * @param studentScore 考生成绩
     * @return 专业代码 -> 录取概率信息的映射
     */
    private static Map<String, MajorProbabilityInfo> calculateAllMajorProbabilities(
            FormReviewEvaluator.FormReviewContext context, 
            List<SuperForm> superFormList, 
            int studentScore) {
        
        Tools.println("[calculateAllMajorProbabilities] 开始计算所有专业的录取概率数据");
        
        if (superFormList == null || superFormList.isEmpty()) {
            Tools.println("[calculateAllMajorProbabilities] 志愿表单为空，返回空结果");
            return new HashMap<>();
        }
        
        Map<String, MajorProbabilityInfo> majorProbabilityMap = new HashMap<>();
        Map<String, List<MajorAdmissionInfo>> groupDataCache = new HashMap<>();  // 缓存专业组数据
        
        // 遍历所有专业，计算每个专业的录取概率
        for (SuperForm superForm : superFormList) {
            String majorCode = superForm.getZydm();
            String majorName = superForm.getZymc();
            String yxdm = superForm.getYxdm();
            String yxmc = superForm.getYxmc_org();
            String zyz = superForm.getZyz();
            
            // 跳过已经计算过的专业
            if (majorProbabilityMap.containsKey(majorCode)) {
                continue;
            }
            
            // 构建专业组缓存key
            String groupKey = yxdm + "_" + (zyz != null ? zyz : "");
            
            // 获取或缓存专业组数据
            List<MajorAdmissionInfo> majorInfoList = groupDataCache.get(groupKey);
            if (majorInfoList == null) {
                majorInfoList = getMajorAdmissionInfoList(context, yxdm, zyz, studentScore);
                groupDataCache.put(groupKey, majorInfoList);
            }
            
            // 查找该专业的录取概率信息
            MajorAdmissionInfo targetMajorInfo = null;
            for (MajorAdmissionInfo info : majorInfoList) {
                if (Tools.trim(info.getZydm()).equals(Tools.trim(majorCode))) {
                    targetMajorInfo = info;
                    break;
                }
            }
            
            // 创建专业录取概率信息
            if (targetMajorInfo != null) {
                double probability = targetMajorInfo.getAdmissionProbability();
                String typeDescription = getMajorAdmissionTypeDescription(probability);
                int scoreDiff = studentScore - targetMajorInfo.getPredictedScore();
                
                MajorProbabilityInfo majorInfo = new MajorProbabilityInfo(
                    majorCode, majorName, yxdm, yxmc, zyz,
                    probability, typeDescription, scoreDiff, 
                    targetMajorInfo.getPredictedScore(), targetMajorInfo.isHasData()
                );
                
                majorProbabilityMap.put(majorCode, majorInfo);
            } else {
                // 没有找到专业数据时，使用默认值
                String typeDescription = getMajorAdmissionTypeDescription(PROBABILITY_BASE_VALUE);
                
                MajorProbabilityInfo majorInfo = new MajorProbabilityInfo(
                    majorCode, majorName, yxdm, yxmc, zyz,
                    PROBABILITY_BASE_VALUE, typeDescription, 0, 0, false
                );
                
                majorProbabilityMap.put(majorCode, majorInfo);
                
                Tools.println("[calculateAllMajorProbabilities] 专业" + majorCode + "(" + majorName + 
                             ") 无历史数据，使用默认概率: " + PROBABILITY_BASE_VALUE + "%");
            }
        }
        
        Tools.println("[calculateAllMajorProbabilities] 专业录取概率数据计算完成，共处理 " + majorProbabilityMap.size() + " 个专业，缓存了 " + groupDataCache.size() + " 个专业组");
        return majorProbabilityMap;
    }

    
    // ==================== 录取概率计算工具方法 ====================
    
    /**
     * 计算录取概率
     * 基于考生成绩与预测趋势分的差值进行线性插值计算
     * 
     * @param studentScore 考生成绩
     * @param predictedScore 预测趋势分
     * @param hasData 是否有三年数据
     * @return 录取概率（0-100）
     */
    public static double calculateAdmissionProbability(int studentScore, int predictedScore, boolean hasData) {
        if (!hasData || predictedScore <= 0) {
//            Tools.println("[calculateAdmissionProbability] 三年无数据或预测分数无效，返回0%概率");
            return 0.0; // 三年无数据或预测分数无效
        }
        
        // X = 考生成绩 - 预测趋势分
        int scoreDiff = studentScore - predictedScore;
//        Tools.println("[calculateAdmissionProbability] 考生成绩: " + studentScore + ", 预测趋势分: " + predictedScore + ", 分数差: " + scoreDiff);
        
        // 边界情况处理
        if (scoreDiff >= POSITIVE_SCORE_LIMIT) {
//            Tools.println("[calculateAdmissionProbability] 分数差≥" + POSITIVE_SCORE_LIMIT + "，返回" + MAX_ADMISSION_PROBABILITY + "%概率");
            return MAX_ADMISSION_PROBABILITY; // 最高概率
        }
        
        if (scoreDiff <= NEGATIVE_SCORE_LIMIT) {
//            Tools.println("[calculateAdmissionProbability] 分数差≤" + NEGATIVE_SCORE_LIMIT + "，返回" + MIN_ADMISSION_PROBABILITY + "%概率");
            return MIN_ADMISSION_PROBABILITY; // 最低概率
        }
        
        if (scoreDiff == 0) {
//            Tools.println("[calculateAdmissionProbability] 分数差=0，返回" + ADMISSION_THRESHOLD + "%概率");
            return ADMISSION_THRESHOLD; // 临界概率
        }
        
        // 线性插值计算
        double probability;
        if (scoreDiff > 0) {
            // 正数区间：0到POSITIVE_SCORE_LIMIT之间，概率从ADMISSION_THRESHOLD%到MAX_ADMISSION_PROBABILITY%
            probability = ADMISSION_THRESHOLD + (MAX_ADMISSION_PROBABILITY - ADMISSION_THRESHOLD) * scoreDiff / POSITIVE_SCORE_LIMIT;
        } else {
            // 负数区间：NEGATIVE_SCORE_LIMIT到0之间，概率从MIN_ADMISSION_PROBABILITY%到ADMISSION_THRESHOLD%
            probability = MIN_ADMISSION_PROBABILITY + (ADMISSION_THRESHOLD - MIN_ADMISSION_PROBABILITY) * (scoreDiff - NEGATIVE_SCORE_LIMIT) / (-NEGATIVE_SCORE_LIMIT);
        }
        
        // 确保概率在合理范围内
        probability = Math.max(MIN_ADMISSION_PROBABILITY, Math.min(MAX_ADMISSION_PROBABILITY, probability));
        
//        Tools.println("[calculateAdmissionProbability] 线性插值计算结果: " + probability + "%");
        return probability;
    }
    
    /**
     * 检查JHBean是否有有效的三年数据
     * 
     * @param bean JHBean对象
     * @return 是否有三年数据
     */
    public static boolean hasThreeYearData(JHBean bean) {
        if (bean == null) return false;
        
        // 检查是否有至少一年的有效数据
        return bean.getQsf_a() > 0 || bean.getQsf_b() > 0 || bean.getQsf_c() > 0;
    }
    
    /**
     * 获取专业组内所有专业的录取概率
     * 
     * @param context FormReviewContext
     * @param yxdm 院校代码
     * @param zyz 专业组代码
     * @param studentScore 考生成绩
     * @return 专业录取概率列表
     */
    public static List<MajorAdmissionInfo> getMajorAdmissionInfoList(
            FormReviewEvaluator.FormReviewContext context, String yxdm, String zyz, int studentScore) {
        
        List<MajorAdmissionInfo> majorInfoList = new ArrayList<>();
        
        // 🎯 优先使用用户选择的专业数据进行录取概率分析
        List<JHBean> planData = context.getDataByEvaluationType(yxdm, zyz, FormReviewEvaluator.EvaluationType.PERSONAL_ANALYSIS);
        @SuppressWarnings("unused")
		String dataSource = "完整专业组数据";
        
        if (planData != null && !planData.isEmpty() && context.hasSelectedMajorData()) {
            dataSource = "用户选择专业数据";
//            Tools.println("[getMajorAdmissionInfoList] ✅ 使用用户选择专业数据进行录取概率分析，数量: " + planData.size());
        } else {
            // 降级使用完整专业组数据
            planData = context.getPlanData(yxdm, zyz);
            Tools.println("[getMajorAdmissionInfoList] ⚠️ 降级使用完整专业组数据，数量: " + planData.size());
        }
        
        for (JHBean bean : planData) {
            MajorAdmissionInfo info = new MajorAdmissionInfo();
            info.setYxdm(bean.getYxdm());
            info.setYxmc(bean.getYxmc_org());
            info.setZydm(bean.getZydm());
            info.setZymc(bean.getZymc_org());
            info.setZyz(bean.getZyz());
            info.setPredictedScore(bean.getQsf());
            
            boolean hasData = hasThreeYearData(bean);
            double probability = calculateAdmissionProbability(studentScore, bean.getQsf(), hasData);
            
            info.setAdmissionProbability(probability);
            info.setHasData(hasData);
            
            majorInfoList.add(info);
        }
        
        // 按录取概率降序排序
        majorInfoList.sort((a, b) -> Double.compare(b.getAdmissionProbability(), a.getAdmissionProbability()));
        
//        Tools.println("[getMajorAdmissionInfoList] 录取概率分析完成，数据源: " + dataSource + "，专业数量: " + majorInfoList.size());
        return majorInfoList;
    }
    
    /**
     * 专业录取信息类
     */
    public static class MajorAdmissionInfo {
        private String yxdm;              // 院校代码
        private String yxmc;              // 院校名称
        private String zydm;              // 专业代码
        private String zymc;              // 专业名称
        private String zyz;               // 专业组代码
        private int predictedScore;       // 预测趋势分
        private double admissionProbability; // 录取概率
        private boolean hasData;          // 是否有数据
        
        // Getters and Setters
        public String getYxdm() { return yxdm; }
        public void setYxdm(String yxdm) { this.yxdm = yxdm; }
        
        public String getYxmc() { return yxmc; }
        public void setYxmc(String yxmc) { this.yxmc = yxmc; }
        
        public String getZydm() { return zydm; }
        public void setZydm(String zydm) { this.zydm = zydm; }
        
        public String getZymc() { return zymc; }
        public void setZymc(String zymc) { this.zymc = zymc; }
        
        public String getZyz() { return zyz; }
        public void setZyz(String zyz) { this.zyz = zyz; }
        
        public int getPredictedScore() { return predictedScore; }
        public void setPredictedScore(int predictedScore) { this.predictedScore = predictedScore; }
        
        public double getAdmissionProbability() { return admissionProbability; }
        public void setAdmissionProbability(double admissionProbability) { this.admissionProbability = admissionProbability; }
        
        public boolean isHasData() { return hasData; }
        public void setHasData(boolean hasData) { this.hasData = hasData; }
    }
    
    
    // ==================== 录取类型枚举 ====================
    
    /**
     * 录取类型枚举
     */
    public enum AdmissionType {
        NORMAL("正常录取"),           // 填报专业录取概率≥70%
        OBEDIENCE_ADJUSTMENT("服从调剂录取"), // 专业组内所有专业中录取概率≥70%（用户未选择的专业）
        NONE("无法录取");             // 所有专业录取概率<70%
        
        private final String description;
        
        AdmissionType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    // ==================== 结果类定义 ====================
    
    /**
     * 录取概率评估结果
     */
    public static class AdmissionProbabilityResult {
        private int studentScore;                              // 考生成绩
        private int totalVolunteers;                           // 总志愿数
        private int admittableVolunteers;                      // 可录取志愿数
        private int normalAdmissionCount;                      // 正常录取数
        private int obedienceAdjustmentAdmissionCount;          // 服从调剂录取数
        private int noAdmissionCount;                         // 无法录取数
        private double averageProbability;                     // 平均录取概率
        private double maxProbability;                         // 最高录取概率
        private double admissionRate;                          // 录取率
        
        private List<VolunteerAdmissionResult> volunteerResults;  // 所有志愿结果
        private List<AdmissionPrediction> admissionPredictions;   // 录取预测（前3个）
        
        // 新增：专业级别的录取概率数据
        private Map<String, MajorProbabilityInfo> majorProbabilityMap;  // 专业代码 -> 录取概率信息

        public AdmissionProbabilityResult() {
            this.volunteerResults = new ArrayList<>();
            this.admissionPredictions = new ArrayList<>();
            this.majorProbabilityMap = new HashMap<>();
        }
        
        // Getters and Setters
        public int getStudentScore() { return studentScore; }
        public void setStudentScore(int studentScore) { this.studentScore = studentScore; }
        
        public int getTotalVolunteers() { return totalVolunteers; }
        public void setTotalVolunteers(int totalVolunteers) { this.totalVolunteers = totalVolunteers; }
        
        public int getAdmittableVolunteers() { return admittableVolunteers; }
        public void setAdmittableVolunteers(int admittableVolunteers) { this.admittableVolunteers = admittableVolunteers; }
        
        public int getNormalAdmissionCount() { return normalAdmissionCount; }
        public void setNormalAdmissionCount(int normalAdmissionCount) { this.normalAdmissionCount = normalAdmissionCount; }
        
        public int getObedienceAdjustmentAdmissionCount() { return obedienceAdjustmentAdmissionCount; }
        public void setObedienceAdjustmentAdmissionCount(int obedienceAdjustmentAdmissionCount) { this.obedienceAdjustmentAdmissionCount = obedienceAdjustmentAdmissionCount; }
        
        public int getNoAdmissionCount() { return noAdmissionCount; }
        public void setNoAdmissionCount(int noAdmissionCount) { this.noAdmissionCount = noAdmissionCount; }
        
        public double getAverageProbability() { return averageProbability; }
        public void setAverageProbability(double averageProbability) { this.averageProbability = averageProbability; }
        
        public double getMaxProbability() { return maxProbability; }
        public void setMaxProbability(double maxProbability) { this.maxProbability = maxProbability; }
        
        public double getAdmissionRate() { return admissionRate; }
        public void setAdmissionRate(double admissionRate) { this.admissionRate = admissionRate; }
        
        public List<VolunteerAdmissionResult> getVolunteerResults() { return volunteerResults; }
        public void setVolunteerResults(List<VolunteerAdmissionResult> volunteerResults) { this.volunteerResults = volunteerResults; }
        
        public List<AdmissionPrediction> getAdmissionPredictions() { return admissionPredictions; }
        public void setAdmissionPredictions(List<AdmissionPrediction> admissionPredictions) { this.admissionPredictions = admissionPredictions; }
        
        /**
         * 根据志愿序号获取录取预测信息
         * @param volunteerNumber 志愿序号
         * @return 录取预测信息，如果没有找到则返回null
         */
        public AdmissionPrediction getAdmissionPredictionByVolunteerNumber(int volunteerNumber) {
            if (admissionPredictions == null) {
                return null;
            }
            for (AdmissionPrediction prediction : admissionPredictions) {
                if (prediction.getVolunteerNumber() == volunteerNumber) {
                    return prediction;
                }
            }
            return null;
        }
        
        /**
         * 检查指定志愿是否在录取预测中
         * @param volunteerNumber 志愿序号
         * @return 是否在录取预测中
         */
        public boolean hasAdmissionPrediction(int volunteerNumber) {
            return getAdmissionPredictionByVolunteerNumber(volunteerNumber) != null;
        }
        
        public Map<String, MajorProbabilityInfo> getMajorProbabilityMap() { return majorProbabilityMap; }
        public void setMajorProbabilityMap(Map<String, MajorProbabilityInfo> majorProbabilityMap) { this.majorProbabilityMap = majorProbabilityMap; }
        
        /**
         * 根据专业代码获取录取概率信息
         */
        public MajorProbabilityInfo getMajorProbabilityInfo(String majorCode) {
            return majorProbabilityMap.get(majorCode);
        }
        
        /**
         * 根据专业代码获取录取概率
         */
        public double getMajorAdmissionProbability(String majorCode) {
            MajorProbabilityInfo info = majorProbabilityMap.get(majorCode);
            return info != null ? info.getAdmissionProbability() : 50.0;
        }
        
        /**
         * 根据专业代码获取录取类型描述
         */
        public String getMajorAdmissionTypeDescription(String majorCode) {
            MajorProbabilityInfo info = majorProbabilityMap.get(majorCode);
            return info != null ? info.getAdmissionTypeDescription() : "暂无数据";
        }
        
        /**
         * 根据专业代码获取分数差
         */
        public int getMajorScoreDifference(String majorCode) {
            MajorProbabilityInfo info = majorProbabilityMap.get(majorCode);
            return info != null ? info.getScoreDifference() : 0;
        }
    }
    
    /**
     * 单个志愿录取结果
     */
    public static class VolunteerAdmissionResult {
        private int volunteerNumber;                    // 志愿序号（1-45）
        private String yxdm;                           // 院校代码
        private String yxmc;                           // 院校名称
        private String zyz;                            // 专业组代码
        private String filledMajor;                    // 填报专业名称
        private String filledMajorCode;                // 填报专业代码
        private double filledMajorProbability;         // 填报专业录取概率
        private int filledMajorPredictedScore;         // 填报专业预测分数
        private boolean filledMajorHasData;            // 填报专业是否有数据
        
        private AdmissionType admissionType;           // 录取类型
        private double admissionProbability;           // 录取概率
        private String admittedMajor;                  // 录取专业名称
        private String admittedMajorCode;              // 录取专业代码
        
        // 调剂录取相关信息
        private double obedienceAdjustmentMajorProbability; // 服从调剂专业录取概率
        private int obedienceAdjustmentMajorPredictedScore; // 服从调剂专业预测分数
        
        // 分数差信息
        private int scoreDifference;                   // 考生成绩与录取专业预测分的差值
        
        // 新增：服从调剂相关信息
        private boolean obedience;                     // 是否服从调剂
        
        // Getters and Setters
        public int getVolunteerNumber() { return volunteerNumber; }
        public void setVolunteerNumber(int volunteerNumber) { this.volunteerNumber = volunteerNumber; }
        
        public String getYxdm() { return yxdm; }
        public void setYxdm(String yxdm) { this.yxdm = yxdm; }
        
        public String getYxmc() { return yxmc; }
        public void setYxmc(String yxmc) { this.yxmc = yxmc; }
        
        public String getZyz() { return zyz; }
        public void setZyz(String zyz) { this.zyz = zyz; }
        
        public String getFilledMajor() { return filledMajor; }
        public void setFilledMajor(String filledMajor) { this.filledMajor = filledMajor; }
        
        public String getFilledMajorCode() { return filledMajorCode; }
        public void setFilledMajorCode(String filledMajorCode) { this.filledMajorCode = filledMajorCode; }
        
        public double getFilledMajorProbability() { return filledMajorProbability; }
        public void setFilledMajorProbability(double filledMajorProbability) { this.filledMajorProbability = filledMajorProbability; }
        
        public int getFilledMajorPredictedScore() { return filledMajorPredictedScore; }
        public void setFilledMajorPredictedScore(int filledMajorPredictedScore) { this.filledMajorPredictedScore = filledMajorPredictedScore; }
        
        public boolean isFilledMajorHasData() { return filledMajorHasData; }
        public void setFilledMajorHasData(boolean filledMajorHasData) { this.filledMajorHasData = filledMajorHasData; }
        
        public AdmissionType getAdmissionType() { return admissionType; }
        public void setAdmissionType(AdmissionType admissionType) { this.admissionType = admissionType; }
        
        public double getAdmissionProbability() { return admissionProbability; }
        public void setAdmissionProbability(double admissionProbability) { this.admissionProbability = admissionProbability; }
        
        public String getAdmittedMajor() { return admittedMajor; }
        public void setAdmittedMajor(String admittedMajor) { this.admittedMajor = admittedMajor; }
        
        public String getAdmittedMajorCode() { return admittedMajorCode; }
        public void setAdmittedMajorCode(String admittedMajorCode) { this.admittedMajorCode = admittedMajorCode; }
        
        public double getObedienceAdjustmentMajorProbability() { return obedienceAdjustmentMajorProbability; }
        public void setObedienceAdjustmentMajorProbability(double obedienceAdjustmentMajorProbability) { this.obedienceAdjustmentMajorProbability = obedienceAdjustmentMajorProbability; }
        
        public int getObedienceAdjustmentMajorPredictedScore() { return obedienceAdjustmentMajorPredictedScore; }
        public void setObedienceAdjustmentMajorPredictedScore(int obedienceAdjustmentMajorPredictedScore) { this.obedienceAdjustmentMajorPredictedScore = obedienceAdjustmentMajorPredictedScore; }
        
        public int getScoreDifference() { return scoreDifference; }
        public void setScoreDifference(int scoreDifference) { this.scoreDifference = scoreDifference; }
        
        public boolean isObedience() { return obedience; }
        public void setObedience(boolean obedience) { this.obedience = obedience; }
    }
    
    /**
     * 录取预测结果
     */
    public static class AdmissionPrediction {
        private int predictionRank;                    // 预测排名（1-3）
        private int volunteerNumber;                   // 志愿序号
        private String yxmc;                          // 院校名称
        private String admittedMajor;                 // 录取专业
        private AdmissionType admissionType;          // 录取类型
        private double admissionProbability;          // 录取概率
        private String predictionDescription;         // 预测描述
        
        // Getters and Setters
        public int getPredictionRank() { return predictionRank; }
        public void setPredictionRank(int predictionRank) { this.predictionRank = predictionRank; }
        
        public int getVolunteerNumber() { return volunteerNumber; }
        public void setVolunteerNumber(int volunteerNumber) { this.volunteerNumber = volunteerNumber; }
        
        public String getYxmc() { return yxmc; }
        public void setYxmc(String yxmc) { this.yxmc = yxmc; }
        
        public String getAdmittedMajor() { return admittedMajor; }
        public void setAdmittedMajor(String admittedMajor) { this.admittedMajor = admittedMajor; }
        
        public AdmissionType getAdmissionType() { return admissionType; }
        public void setAdmissionType(AdmissionType admissionType) { this.admissionType = admissionType; }
        
        public double getAdmissionProbability() { return admissionProbability; }
        public void setAdmissionProbability(double admissionProbability) { this.admissionProbability = admissionProbability; }
        
        public String getPredictionDescription() { return predictionDescription; }
        public void setPredictionDescription(String predictionDescription) { this.predictionDescription = predictionDescription; }
    }
    

    
    /**
     * 获取专业的录取类型描述
     * 注意：专业级别只有"正常录取"和"无法录取"两种状态
     * "服从调剂录取"是志愿级别的概念，不适用于单个专业
     */
    public static String getMajorAdmissionTypeDescription(double probability) {
    	if (probability >= ADMISSION_THRESHOLD) {
            return "正常录取";
        } else {
            return "无法录取";
        }
    }
    
    /**
     * 获取录取类型描述
     */
    public static String getAdmissionTypeDescription(AdmissionType admissionType) {
        if (admissionType == null) {
            return "未知";
        }
        switch (admissionType) {
            case NORMAL:
                return "正常录取";
            case OBEDIENCE_ADJUSTMENT:
                return "服从调剂录取";
            case NONE:
                return "无法录取";
            default:
                return "未知";
        }
    }
    
    /**
     * 专业录取概率信息类
     * 用于存储每个专业的录取概率相关数据
     */
    public static class MajorProbabilityInfo {
        private String majorCode;                    // 专业代码
        private String majorName;                    // 专业名称
        private String yxdm;                        // 院校代码
        private String yxmc;                        // 院校名称
        private String zyz;                         // 专业组代码
        private double admissionProbability;        // 录取概率
        private String admissionTypeDescription;    // 录取类型描述
        private int scoreDifference;                // 分数差
        private int predictedScore;                 // 预测分数
        private boolean hasData;                    // 是否有历史数据
        
        public MajorProbabilityInfo() {}
        
        public MajorProbabilityInfo(String majorCode, String majorName, String yxdm, String yxmc, String zyz,
                                   double admissionProbability, String admissionTypeDescription, 
                                   int scoreDifference, int predictedScore, boolean hasData) {
            this.majorCode = majorCode;
            this.majorName = majorName;
            this.yxdm = yxdm;
            this.yxmc = yxmc;
            this.zyz = zyz;
            this.admissionProbability = admissionProbability;
            this.admissionTypeDescription = admissionTypeDescription;
            this.scoreDifference = scoreDifference;
            this.predictedScore = predictedScore;
            this.hasData = hasData;
        }
        
        // Getters and Setters
        public String getMajorCode() { return majorCode; }
        public void setMajorCode(String majorCode) { this.majorCode = majorCode; }
        
        public String getMajorName() { return majorName; }
        public void setMajorName(String majorName) { this.majorName = majorName; }
        
        public String getYxdm() { return yxdm; }
        public void setYxdm(String yxdm) { this.yxdm = yxdm; }
        
        public String getYxmc() { return yxmc; }
        public void setYxmc(String yxmc) { this.yxmc = yxmc; }
        
        public String getZyz() { return zyz; }
        public void setZyz(String zyz) { this.zyz = zyz; }
        
        public double getAdmissionProbability() { return admissionProbability; }
        public void setAdmissionProbability(double admissionProbability) { this.admissionProbability = admissionProbability; }
        
        public String getAdmissionTypeDescription() { return admissionTypeDescription; }
        public void setAdmissionTypeDescription(String admissionTypeDescription) { this.admissionTypeDescription = admissionTypeDescription; }
        
        public int getScoreDifference() { return scoreDifference; }
        public void setScoreDifference(int scoreDifference) { this.scoreDifference = scoreDifference; }
        
        public int getPredictedScore() { return predictedScore; }
        public void setPredictedScore(int predictedScore) { this.predictedScore = predictedScore; }
        
        public boolean isHasData() { return hasData; }
        public void setHasData(boolean hasData) { this.hasData = hasData; }
    }

    /**
     * 从完整专业组数据中获取所有专业的录取概率列表（用于服从调剂）
     * 
     * @param context FormReviewContext
     * @param yxdm 院校代码
     * @param zyz 专业组代码
     * @param studentScore 考生成绩
     * @return 专业录取概率列表
     */
    private static List<MajorAdmissionInfo> getAllMajorInfoListFromFullData(
            FormReviewEvaluator.FormReviewContext context, 
            String yxdm, String zyz, int studentScore) {
        
        List<MajorAdmissionInfo> allMajorInfoList = new ArrayList<>();
        
        // 获取专业组内所有专业的完整数据
        List<JHBean> fullPlanData = context.getPlanData(yxdm, zyz);
        
        if (fullPlanData != null && !fullPlanData.isEmpty()) {
            for (JHBean bean : fullPlanData) {
                MajorAdmissionInfo admissionInfo = new MajorAdmissionInfo();
                admissionInfo.setYxdm(bean.getYxdm());
                admissionInfo.setYxmc(bean.getYxmc_org());
                admissionInfo.setZydm(bean.getZydm());
                admissionInfo.setZymc(bean.getZymc_org());
                admissionInfo.setZyz(bean.getZyz());
                admissionInfo.setPredictedScore(bean.getQsf());
                
                boolean hasData = hasThreeYearData(bean);
                double probability = calculateAdmissionProbability(studentScore, bean.getQsf(), hasData);
                
                admissionInfo.setAdmissionProbability(probability);
                admissionInfo.setHasData(hasData);
                
                allMajorInfoList.add(admissionInfo);
            }
            
            // 按录取概率降序排序
            allMajorInfoList.sort((a, b) -> Double.compare(b.getAdmissionProbability(), a.getAdmissionProbability()));
        }
        
        Tools.println("[getAllMajorInfoListFromFullData] 获取专业组 " + yxdm + "_" + zyz + " 所有专业数据: " + allMajorInfoList.size() + " 个");
        return allMajorInfoList;
    }
    
    /**
     * 从用户选择的专业中查找最佳调剂专业（排除已填报的专业）
     * 
     * @param selectedMajorInfoList 用户选择的专业信息
     * @param filledMajorCode 用户填报的专业代码
     * @return 最佳调剂专业
     */
    private static MajorAdmissionInfo findBestObedienceAdjustmentMajorFromUserSelected(
            List<MajorAdmissionInfo> selectedMajorInfoList, 
            String filledMajorCode) {
        
        Tools.println("[findBestObedienceAdjustmentMajorFromUserSelected] 在用户选择的专业中查找调剂机会");
        Tools.println("[findBestObedienceAdjustmentMajorFromUserSelected] 排除填报专业代码: " + filledMajorCode);
        
        String trimmedFilledMajorCode = Tools.trim(filledMajorCode);
        MajorAdmissionInfo bestMajor = null;
        
        for (MajorAdmissionInfo major : selectedMajorInfoList) {
            String majorCode = Tools.trim(major.getZydm());
            
            // 排除用户填报的专业
            if (majorCode.equals(trimmedFilledMajorCode)) {
                Tools.println("[findBestObedienceAdjustmentMajorFromUserSelected] 跳过填报专业: " + major.getZymc() + " (代码: " + majorCode + ")");
                continue;
            }
            
            Tools.println("[findBestObedienceAdjustmentMajorFromUserSelected] 检查调剂专业: " + major.getZymc() + 
                         " (代码: " + majorCode + ", 概率: " + String.format("%.2f", major.getAdmissionProbability()) + "%)");
            
            // 找到录取概率最高的专业
            if (bestMajor == null || major.getAdmissionProbability() > bestMajor.getAdmissionProbability()) {
                bestMajor = major;
                Tools.println("[findBestObedienceAdjustmentMajorFromUserSelected] 更新最佳调剂专业: " + bestMajor.getZymc());
            }
        }
        
        if (bestMajor != null) {
            Tools.println("[findBestObedienceAdjustmentMajorFromUserSelected] 找到最佳调剂专业: " + bestMajor.getZymc() + 
                         " (代码: " + bestMajor.getZydm() + ", 概率: " + String.format("%.2f", bestMajor.getAdmissionProbability()) + "%)");
        } else {
            Tools.println("[findBestObedienceAdjustmentMajorFromUserSelected] 未找到符合条件的调剂专业");
        }
        
        return bestMajor;
    }
    
    /**
     * 查找最佳服从调剂专业（排除用户已选择的专业）
     * 
     * @param allMajorInfoList 专业组内所有专业信息
     * @param selectedMajorInfoList 用户选择的专业信息
     * @return 最佳服从调剂专业
     */
    private static MajorAdmissionInfo findBestObedienceAdjustmentMajor(
            List<MajorAdmissionInfo> allMajorInfoList, 
            List<MajorAdmissionInfo> selectedMajorInfoList) {
        
        // 收集用户已选择的专业代码
        Set<String> selectedMajorCodes = new HashSet<>();
        for (MajorAdmissionInfo selectedInfo : selectedMajorInfoList) {
            selectedMajorCodes.add(Tools.trim(selectedInfo.getZydm()));
        }
        
        MajorAdmissionInfo bestObedienceAdjustment = null;
        
        for (MajorAdmissionInfo info : allMajorInfoList) {
            String majorCode = Tools.trim(info.getZydm());
            
            // 排除用户已选择的专业
            if (selectedMajorCodes.contains(majorCode)) {
                continue;
            }
            
            // 查找录取概率≥70%的专业
            if (info.getAdmissionProbability() >= ADMISSION_THRESHOLD) {
                if (bestObedienceAdjustment == null || info.getAdmissionProbability() > bestObedienceAdjustment.getAdmissionProbability()) {
                    bestObedienceAdjustment = info;
                    
                    Tools.println("[findBestObedienceAdjustmentMajor] 发现服从调剂专业: " + 
                                 info.getZymc() + " (代码: " + info.getZydm() + ", 概率: " + 
                                 String.format("%.2f", info.getAdmissionProbability()) + "%)");
                }
            }
        }
        
        if (bestObedienceAdjustment != null) {
            Tools.println("[findBestObedienceAdjustmentMajor] 最佳服从调剂专业: " + 
                         bestObedienceAdjustment.getZymc() + " (概率: " + 
                         String.format("%.2f", bestObedienceAdjustment.getAdmissionProbability()) + "%)");
        } else {
            Tools.println("[findBestObedienceAdjustmentMajor] 未找到符合条件的服从调剂专业");
        }
        
        return bestObedienceAdjustment;
    }

} 