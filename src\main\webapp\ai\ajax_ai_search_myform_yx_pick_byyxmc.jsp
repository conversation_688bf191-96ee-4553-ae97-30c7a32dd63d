<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page import="com.career.utils.*,com.career.db.*,java.util.*" %>

<%

String yxmc_org = Tools.trim(request.getParameter("yxmc_org"));
String selected_zyz = Tools.trim(request.getParameter("selected_zyz"));

ZyzdFormJDBC zyzdFormJDBC = new ZyzdFormJDBC();
LhyJDBC lhyJDBC = new LhyJDBC();
ZyzdJDBC zyzdJDBC = new ZyzdJDBC();

ZyzdUniversityBean zyzdUniversityBean = ZyzdCache.getUniversity(yxmc_org);

if(zyzdUniversityBean == null || Tools.isEmpty(yxmc_org)){
	out.print("ERR:NO_DATA"); 
	return; 
}

zyzdUniversityBean = zyzdUniversityBean == null ? new ZyzdUniversityBean() : zyzdUniversityBean;
ZyzdBaseUniversityJz zyzdBaseUniversityJz = ZyzdCache.getUniversityJz(yxmc_org);
zyzdBaseUniversityJz = zyzdBaseUniversityJz == null ? new ZyzdBaseUniversityJz() : zyzdBaseUniversityJz;
List<YxzyStatisticsYxzy> yxzyStatisticsYxzyList = zyzdJDBC.getYyzdBaseYxzyStatisticsYxzyByYxmcOrgWithGroup(yxmc_org);
List<ZyzdMajorRanking> ruankeRanking = zyzdJDBC.getRankingMajorRuanKe(yxmc_org);

List<YxzyStatisticsMain> yxzyStatisticsMainList = zyzdJDBC.getYxzyStatisticsMainAll();
HashMap<String, String> yxzyStatisticsMainListTableMap = new HashMap<>();
for(YxzyStatisticsMain main : yxzyStatisticsMainList){
	yxzyStatisticsMainListTableMap.put(main.getTable_id(), main.getTable_name());
}

List<ZyzdBaseRankJYB4MajorBean> jyb4List = zyzdJDBC.getZyzdBaseRankJYB4Major(yxmc_org);

List<JiuyeBean> jiuyeZyList = zyzdJDBC.pickJiuyeSummaryBySchoolName(yxmc_org);
%>

<div class="modal-header">
    <h6 class="modal-title" id="exampleModalXlLabel">
    	<span class="text-dark fw-bold" style="font-size:16px;"><%=yxmc_org %></span> 
		<span class="badge bg-primary ms-2 fs-6">
			
		</span>
		
    </h6>
    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
</div>


<!-- 模态框主体 -->
<div class="modal-body">
    <div class="card-body">
         <ul class="nav nav-tabs tab-style-1 d-sm-flex d-block" role="tablist">
             <li class="nav-item" role="presentation">
                 <a class="nav-link active" data-bs-toggle="tab" aria-current="page" data-bs-target="#mm_tb_form_maual_yx_pick_desc_id" href="#mm_tb_form_maual_yx_pick_desc_id" aria-selected="true" role="tab">院校介绍</a>
             </li>
             <li class="nav-item" role="presentation">
                 <a class="nav-link" data-bs-toggle="tab" data-bs-target="#mm_tb_form_maual_yx_pick_ranking_id" href="#mm_tb_form_maual_yx_pick_ranking_id" aria-selected="false" role="tab" tabindex="-1">软科排名</a>
             </li>
             <li class="nav-item" role="presentation">
                 <a class="nav-link" data-bs-toggle="tab" data-bs-target="#mm_tb_form_maual_yx_pick_4rranking_id" href="#mm_tb_form_maual_yx_pick_4rranking_id" aria-selected="false" role="tab" tabindex="-1">第四轮学科排名</a>
             </li>
             <li class="nav-item" role="presentation">
                 <a class="nav-link" data-bs-toggle="tab" data-bs-target="#mm_tb_form_maual_yx_pick_jy_id" href="#mm_tb_form_maual_yx_pick_jy_id" aria-selected="false" role="tab" tabindex="-1">就业</a>
             </li>
             <li class="nav-item" role="presentation">
                 <a class="nav-link" data-bs-toggle="tab" data-bs-target="#mm_tb_form_maual_yx_pick_xs_id" href="#mm_tb_form_maual_yx_pick_xs_id" aria-selected="false" role="tab" tabindex="-1">学术成果</a>
             </li>
             
         </ul> 
         <div class="tab-content">
             <div class="tab-pane active show" id="mm_tb_form_maual_yx_pick_desc_id" role="tabpanel">  
                 <div class="text-muted"><%=Tools.view(zyzdUniversityBean.getInfo_descp()) %>...</div>
                 <div style="margin-top:10px;">
                 	<table class="table text-nowrap"> 
                                        <tbody>
                                        	<%if("1".equals("2")){ %>
                                        	<tr>
                                                <td colspan="2">
                                                	<div class="container mt-5">
													    <ul class="timeline list-unstyled">
													        <%
													       		
													            List<UniversityPeriod> periods = Tools.parseUniversityPeriods(zyzdUniversityBean.getInfo_addr());
													
													            for (UniversityPeriod period : periods) { 
													        %>
													        <li>
													            <div class="timeline-time text-end">
													                <span class="date"><%= period.getStart_time_text() %></span>
													                <span class="time"><%= period.getEnd_time_text() %></span>
													            </div>
													            <div class="timeline-icon">
													                <a href="javascript:void(0);"></a>
													            </div>
													            <div class="timeline-body">
													                <div class="d-flex align-items-top timeline-main-content flex-wrap mt-0">
													                    <div class="avatar avatar-md online me-3 avatar-rounded mt-sm-0 mt-4">
													                        <img alt="avatar" src="../assets/images/faces/4.jpg">
													                    </div>
													                    <div class="flex-fill">
													                        <div class="d-flex align-items-center">
													                            <div class="mt-sm-0 mt-2">
													                                <p class="mb-0 fs-14 fw-semibold"><%= period.getName() %></p>
													                                <p class="mb-0 text-muted"><%= period.getStart_year() %> - <%= period.getEnd_year() %></p>
													                            </div>
													                            <div class="ms-auto">
													                                <span class="float-end badge bg-light text-muted timeline-badge">
													                                    <%= period.getStart_time_text() %>
													                                </span>
													                            </div>
													                        </div>
													                    </div>
													                </div>
													            </div>
													         </li>
													        <% } %>
													    </ul>
													</div>
                                                </td>
                                            </tr>
                                            <%} %>
                                            <tr>
                                                <th scope="row"><b style="font-size:14px">2025年最新招生简章<span style="color:blue;">(权威官方版)</span></b></th>
                                                <td><a href="<%=zyzdBaseUniversityJz.getJz_url() %>" target="_blank"><b style="color:red;">点此查看</b></a></td>
                                            </tr>
                                            <tr>
                                                <th scope="row"><b style="font-size:14px">学校介绍/院系设置<span style="color:blue;">(权威官方版)</span></b></th>
                                                <td><a href="<%=zyzdBaseUniversityJz.getYx_url() %>" target="_blank"><b style="color:red;">点此进入</b></a></td>
                                            </tr> 
                                              
                                        </tbody>
                                    </table>
                 </div>
             </div>
             
             <div class="tab-pane" id="mm_tb_form_maual_yx_pick_ranking_id" role="tabpanel">
                 <div class="text-muted">
                 	<%
		           	for(int i=0;i<ruankeRanking.size();i++){ 
		           		ZyzdMajorRanking ranking = ruankeRanking.get(i);
		           	%>
		          <div class="accordion-item">
		              <h2 class="accordion-header" id="RANKING_headingcustomicon1_<%=i%>">
		                  <button onclick="MM_ranking_explore('RANKING_relation_univ_<%=i%>','<%=ranking.getYxmc() %>', '<%=ranking.getZymc() %>', '<%=ranking.getRanking() %>');" class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#RANKING_collapsecustomicon1_<%=i%>" aria-expanded="false" aria-controls="RANKING_collapsecustomicon1_<%=i%>"><%=Tools.viewForLimitLength(ranking.getZymc(), 20)  %>&nbsp;&nbsp;&nbsp;<b style="color:red;"><%=ranking.getLevel()%></b>&nbsp;&nbsp;
		                      
		                      <span class="badge bg-primary-transparent" style="font-size:16px;"><span style="font-size:12px;font-weight:normal;color:gray;">排名:</span> <%=ranking.getRanking()%></span>
		                       
		                      <span class="text-success"><i class="fa fa-caret-up mx-1"></i></span>
		                  </button>
		              </h2>
		              <div id="RANKING_collapsecustomicon1_<%=i%>" class="accordion-collapse collapse" aria-labelledby="RANKING_headingcustomicon1_<%=i%>" data-bs-parent="#RANKING_accordioncustomicon1Example">
		                  <div class="accordion-body" id="RANKING_relation_univ_<%=i%>" style="background-color:#cfe2ff;padding:0px;">
		   
							</div>
		                     </div>
		                 </div>
		                 <%} %>
                 </div>
             </div>
             
             <div class="tab-pane" id="mm_tb_form_maual_yx_pick_4rranking_id" role="tabpanel">
                 <div class="text-muted">
                 	<%
		           	for(int i=0;i<jyb4List.size();i++){ 
		           		ZyzdBaseRankJYB4MajorBean major = jyb4List.get(i);
		           	%>
		          <div class="accordion-item">
												       <h2 class="accordion-header" id="RANKING_headingcustomicon1_<%=i%>">
												           <button onclick="MM_TB_ranking_jyb4_major('RANKING_JYB4_relation_univ_<%=i%>','<%=major.getYxmc()%>', '<%=major.getZymc()%>', '<%=major.getSort()%>');"
												               class="accordion-button collapsed" type="button"
												               data-bs-toggle="collapse"
												               data-bs-target="#RANKING_collapsecustomicon1_<%=i%>"
												               aria-expanded="false"
												               aria-controls="RANKING_collapsecustomicon1_<%=i%>">
												               <%=Tools.viewForLimitLength(major.getZymc(), 20)%>&nbsp;&nbsp;&nbsp;
												               <b style="color: red;"><%=major.getRankingLevel()%></b>&nbsp;&nbsp;
												               <span class="text-success">
												                   <i class="fa fa-caret-up mx-1"></i>
												               </span>
												           </button>
												       </h2>
												       
												       <div id="RANKING_collapsecustomicon1_<%=i%>"
												           class="accordion-collapse collapse"
												           aria-labelledby="RANKING_headingcustomicon1_<%=i%>"
												           data-bs-parent="#JYB4_accordioncustomicon1">
												           <div class="accordion-body" id="RANKING_JYB4_relation_univ_<%=i%>"
												               style="background-color: #cfe2ff; padding: 0px;">
												           </div>
												       </div>
												    </div>
		                 <%} %>
                 </div>
             </div>
             
             <div class="tab-pane" id="mm_tb_form_maual_yx_pick_jy_id" role="tabpanel">
                 <div class="accordion accordion-flush" id="P_MM_form_accordionExample">
					            <%
					            for(int i=0;i<jiuyeZyList.size();i++){ 
					                JiuyeBean bean = jiuyeZyList.get(i);
					                int cnt = bean.getCnt();
					                String COUNT = "<span class='badge bg-secondary ms-2'><i class='bi bi-emoji-neutral me-1'></i>D</span><span class='ms-1 text-muted' style='font-size:10px;'>(人数较少)</span>";
					                if(cnt > 9 && cnt < 100){
					                    COUNT = "<span class='badge bg-dark ms-2'><i class='bi bi-emoji-smile me-1'></i>C</span><span class='ms-1 text-muted' style='font-size:10px;'>(努力有机会)</span>";
					                }else if(cnt >= 100 & cnt < 1000){
					                    COUNT = "<span class='badge bg-primary ms-2'><i class='bi bi-award-fill me-1'></i>B</span><span class='ms-1 text-muted' style='font-size:10px;'>(稳定就业)</span>";
					                }else if(cnt >= 1000){
					                    COUNT = "<span class='badge bg-danger ms-2'><i class='bi bi-trophy-fill me-1'></i>A</span><span class='ms-1 text-muted' style='font-size:10px;'>(就业率超99%)</span>";
					                }
					            %>
					            <div class="accordion-item">
					                <h2 class="accordion-header" id="P_MM_form_heading<%=i %>">
					                    <button class="accordion-button collapsed d-flex align-items-center" type="button"
					                        data-bs-toggle="collapse"
					                        onclick="MM_TB_jy_explore('P_MM_form_collapse_content_<%=i %>','<%=yxmc_org %>', '<%=bean.getSsgs() %>', '<%=bean.getSshy() %>');"
					                        data-bs-target="#P_MM_form_collapse<%=i %>"
					                        aria-expanded="false"
					                        aria-controls="P_MM_form_collapse<%=i %>">
					                        <!-- 行业图标 -->
					                        <i class="bi bi-briefcase-fill text-info me-2" style="font-size:1.2rem;"></i>
					                        <span class="fw-bold text-primary me-2"><%=Tools.viewForLimitLength(bean.getSshy(), 20) %></span>
					                        <span class="text-muted me-2" style="font-size:12px;">
					                            <i class="bi bi-building me-1"></i>
					                            <%=Tools.viewForLimitLength(bean.getSsgs(), 20) %>
					                        </span>
					                        <%=COUNT %>
					                    </button>
					                </h2>
					                <div id="P_MM_form_collapse<%=i %>" class="accordion-collapse collapse"
					                    aria-labelledby="P_MM_form_heading<%=i %>"
					                    data-bs-parent="#P_MM_form_accordionExample">
					                    <div class="accordion-body" id="P_MM_form_collapse_content_<%=i %>"></div>
					                </div>
					            </div>
					            <%} %>
					        </div>
             </div>
             <div class="tab-pane" id="mm_tb_form_maual_yx_pick_xs_id" role="tabpanel">
                 <div class="text-muted">
                 	<table class="table text-wrap">
                 			<thead>
                 				<tr class="table-danger"> 
	                 				<td>名称</td>
	                 				<td>包含院校</td>
	                 				<td>操作</td>
                 				</tr>
                 			</thead> 
                                        <tbody>
                                        	<%
									            for (YxzyStatisticsYxzy bean : yxzyStatisticsYxzyList) { 
									            	String table_name = yxzyStatisticsMainListTableMap.get(bean.getTable_id());
									            	if(Tools.isEmpty(table_name)){
									            		continue;
									            	}
									        %>
                                        	<tr>
                                                <td>
                                                	<%=Tools.viewForLimitLength(table_name, 25) %>
                                                </td>
                                                <td>
                                                	<%=Tools.viewForLimitLength(bean.getYxmc_org(), 15) %>
                                                </td>
                                                <td>
                                                	<a onclick="MM_pick_yx_xs('<%=bean.getTable_id() %>', '<%=yxmc_org %>');">查看</a>
                                                </td>
                                            </tr>
                                            <tr id="MM_yxzy_statistics_yxzy_id_<%=bean.getTable_id() %>" style="display:none;" class="MM_yxzy_statistics_yxzy_id">
                                            	<td colspan="3"></td>
                                            </tr>
                                            <%} %>
                                        </tbody>
                                    </table>
                 </div>
             </div>
         </div>
     </div>
</div>                                                   