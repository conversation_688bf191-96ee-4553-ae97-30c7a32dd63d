package com.career.utils.excel.example;

import com.career.utils.Tools;
import com.career.utils.excel.ExcelExporter;
import com.career.utils.excel.model.MajorGroupEnrollmentForm;
import com.career.utils.excel.model.MajorEnrollmentForm;
import com.career.utils.excel.model.ArtJhForm;
import com.career.utils.excel.model.JHBeanOrgForm;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Excel导出示例类
 * 演示如何使用ExcelExporter导出ArtJhForm和JHBeanOrgForm数据
 */
public class ExcelExportExample {

    public static void main(String[] args) {
    	
    	// 院校 + 专业 
    	
//    	exportMajorEnrollmentForm();
    	
    	
    	// 院校 + 专业组
    	
//    	exportMajorGroupEnrollmentForm();

        System.out.println("开始测试Excel导出功能...");

        // 测试ArtJhForm导出
        testArtJhFormExport();

        // 测试JHBeanOrgForm导出
        testJHBeanOrgFormExport();

        System.out.println("Excel导出功能测试完成！");
    }
    
    
    @SuppressWarnings("unused")
	private static void exportMajorGroupEnrollmentForm() {
        try {
	        
        	// 创建导出器
	        ExcelExporter<MajorGroupEnrollmentForm> exporter = new ExcelExporter<>(MajorGroupEnrollmentForm.class);
	        
	        // 设置文件头
	        exporter.setFileHeader("四川2025志愿填报大数据（专家版）");
	        
	        // 准备数据
	        List<MajorGroupEnrollmentForm> dataList = createProSampleData();
	        
	        // 导出Excel
	        FileOutputStream fileOut = new FileOutputStream("D:/WorkSpace/lx2b/Notes/Data Tables/2025年志愿填报大数据-院校-专业组版.xlsx");
	        exporter.export(dataList, fileOut);
	        fileOut.close();
	        
	        Tools.println("Excel导出成功，文件已保存为：2025年志愿填报大数据-院校专业版.xlsx");
        
	    } catch (IOException e) {
	        e.printStackTrace();
	    }
    }
    
    @SuppressWarnings("unused")
	private static void exportMajorEnrollmentForm() {
        try {
	        // 创建导出器
	        ExcelExporter<MajorEnrollmentForm> exporter = new ExcelExporter<>(MajorEnrollmentForm.class);
	        
	        // 设置文件头
	        exporter.setFileHeader("重庆2025志愿填报大数据（专家版）");
	        
	        // 准备数据
	        List<MajorEnrollmentForm> dataList = createSampleData();
	        
	        // 导出Excel
	        FileOutputStream fileOut = new FileOutputStream("D:/WorkSpace/lx2b/Notes/Data Tables/2025年志愿填报大数据-院校专业版.xlsx");
	        exporter.export(dataList, fileOut);
	        fileOut.close();
	        
	        Tools.println("Excel导出成功，文件已保存为：2025年志愿填报大数据-院校专业版.xlsx");
        
	    } catch (IOException e) {
	        e.printStackTrace();
	    }
    }
    
    
    /**
     * 创建示例数据
     */
    private static List<MajorEnrollmentForm> createSampleData() {
        List<MajorEnrollmentForm> dataList = new ArrayList<>();

        // 添加示例数据 1
        MajorEnrollmentForm form1 = new MajorEnrollmentForm();
        form1.setMajorCategory("计算机科学与技术");
        // 2025年数据
        form1.setPlanCountCurrent(120);
        form1.setEnrollmentCountCurrent(118);
        form1.setLowestScoreCurrent(620);   
        form1.setLowestRankCurrent(5000);
        form1.setAvgScoreCurrent(645);   
        form1.setAvgRankCurrent(3500);
        form1.setHighestScoreCurrent(680);   
        form1.setHighestRankCurrent(1000);
        form1.setTrendScoreCurrent(9);   
        form1.setEquivalentDifferenceCurrent(23);   

        // 2024年数据
        form1.setPlanCountPrevious(110);
        form1.setEnrollmentCountPrevious(110);
        form1.setLowestScorePrevious(610);   
        form1.setLowestRankPrevious(5500);
        form1.setAvgScorePrevious(640);   
        form1.setAvgRankPrevious(4000);
        form1.setHighestScorePrevious(675);   
        form1.setHighestRankPrevious(1200);
        form1.setTrendScorePrevious(8);   
        form1.setEquivalentDifferencePrevious(20);   

        // 2023年数据
        form1.setPlanCountBeforeLast(100);
        form1.setEnrollmentCountBeforeLast(100);
        form1.setLowestScoreBeforeLast(600);   
        form1.setLowestRankBeforeLast(6000);
        form1.setAvgScoreBeforeLast(630);   
        form1.setAvgRankBeforeLast(4500);
        form1.setHighestScoreBeforeLast(670);   
        form1.setHighestRankBeforeLast(1500);
        form1.setTrendScoreBeforeLast(8);   
        form1.setEquivalentDifferenceBeforeLast(19);   

        // 院校基础信息
        form1.setProvince("重庆");
        form1.setCity("重庆");
        form1.setUniversityTag("985");
        form1.setUniversityLevelTag("一流大学");
        form1.setRenameInfo("无");
        form1.setMajorTransferInfo("允许");
        form1.setUniversityProvince("重庆");
        form1.setCityLevelTag("一线城市");
        form1.setEducationLevel("本科");
        form1.setAffiliatedUnit("教育部");
        form1.setUniversityType("综合");
        form1.setPublicPrivateNature("公办");
        form1.setPostgraduateRate(30);
        form1.setUniversityRanking("全国前10");
        form1.setMasterProgramCount(50);
        form1.setMasterPrograms("计算机科学与技术, 软件工程, ...等");
        form1.setPhdProgramCount(20);
        form1.setPhdPrograms("计算机科学与技术, 软件工程, ...等");
        form1.setAdmissionRules2024("详见官网");

        // 专业基础信息
        form1.setRuankeRating("A+");
        form1.setRuankeRanking("全国前5");
        form1.setDisciplineEvaluation("A");
        form1.setMajorLevel("国家一流专业");
        form1.setHasMasterProgram("是");
        form1.setHasPhDProgram("是");

        dataList.add(form1);

        // 添加第二个示例数据
        MajorEnrollmentForm form2 = new MajorEnrollmentForm();
        form2.setMajorCategory("人工智能");
        // 2025年数据
        form2.setPlanCountCurrent(80);
        form2.setEnrollmentCountCurrent(80);
        form2.setLowestScoreCurrent(640);   
        form2.setLowestRankCurrent(3000);
        form2.setAvgScoreCurrent(655);   
        form2.setAvgRankCurrent(2500);
        form2.setHighestScoreCurrent(690);   
        form2.setHighestRankCurrent(500);
        form2.setTrendScoreCurrent(9);   
        form2.setEquivalentDifferenceCurrent(25);   

        // 2024年数据
        form2.setPlanCountPrevious(70);
        form2.setEnrollmentCountPrevious(70);
        form2.setLowestScorePrevious(635);   
        form2.setLowestRankPrevious(3200);
        form2.setAvgScorePrevious(650);   
        form2.setAvgRankPrevious(2700);
        form2.setHighestScorePrevious(685);   
        form2.setHighestRankPrevious(600);
        form2.setTrendScorePrevious(9);   
        form2.setEquivalentDifferencePrevious(24);   

        // 2023年数据
        form2.setPlanCountBeforeLast(65);
        form2.setEnrollmentCountBeforeLast(65);
        form2.setLowestScoreBeforeLast(630);   
        form2.setLowestRankBeforeLast(3500);
        form2.setAvgScoreBeforeLast(648);   
        form2.setAvgRankBeforeLast(2800);
        form2.setHighestScoreBeforeLast(680);   
        form2.setHighestRankBeforeLast(800);
        form2.setTrendScoreBeforeLast(9);   
        form2.setEquivalentDifferenceBeforeLast(23);   

        // 院校基础信息
        form2.setProvince("重庆");
        form2.setCity("重庆");
        form2.setUniversityTag("985");
        form2.setUniversityLevelTag("一流大学");
        form2.setRenameInfo("无");
        form2.setMajorTransferInfo("允许");
        form2.setUniversityProvince("重庆");
        form2.setCityLevelTag("一线城市");
        form2.setEducationLevel("本科");
        form2.setAffiliatedUnit("教育部");
        form2.setUniversityType("综合");
        form2.setPublicPrivateNature("公办");
        form2.setPostgraduateRate(35);
        form2.setUniversityRanking("全国前15");
        form2.setMasterProgramCount(45);
        form2.setMasterPrograms("人工智能, 软件工程, ...等");
        form2.setPhdProgramCount(18);
        form2.setPhdPrograms("人工智能, 软件工程, ...等");
        form2.setAdmissionRules2024("详见官网");

        // 专业基础信息
        form2.setRuankeRating("A");
        form2.setRuankeRanking("全国前10");
        form2.setDisciplineEvaluation("B+");
        form2.setMajorLevel("省级一流专业");
        form2.setHasMasterProgram("是");
        form2.setHasPhDProgram("是");

        dataList.add(form2);

        return dataList;
    }
    
    
    private static List<MajorGroupEnrollmentForm> createProSampleData() {
    	
        List<MajorGroupEnrollmentForm> dataList = new ArrayList<>();

        MajorGroupEnrollmentForm form1 = new MajorGroupEnrollmentForm();
        
        form1.setMajorCategory("计算机科学与技术");
        form1.setMajorGroupAdmittedCountPrevious(125);
        form1.setMajorGroupLowestScorePrevious(615);
        form1.setMajorGroupLowestRankPrevious(5200);
        form1.setPlanCountCurrent(120);
        form1.setEnrollmentCountCurrent(118);
        form1.setLowestScoreCurrent(620);
        form1.setLowestRankCurrent(5000);
        form1.setAvgScoreCurrent(645);
        form1.setAvgRankCurrent(3500);
        form1.setHighestScoreCurrent(680);
        form1.setHighestRankCurrent(1000);
        form1.setPlanCountPrevious(110);
        form1.setEnrollmentCountPrevious(110);
        form1.setLowestScorePrevious(610);
        form1.setLowestRankPrevious(5500);
        form1.setAvgScorePrevious(640);
        form1.setAvgRankPrevious(4000);
        form1.setHighestScorePrevious(675);
        form1.setHighestRankPrevious(1200);
        form1.setPlanCountBeforeLast(100);
        form1.setEnrollmentCountBeforeLast(100);
        form1.setLowestScoreBeforeLast(600);
        form1.setLowestRankBeforeLast(6000);
        form1.setAvgScoreBeforeLast(630);
        form1.setAvgRankBeforeLast(4500);
        form1.setHighestScoreBeforeLast(670);
        form1.setHighestRankBeforeLast(1500);
        form1.setTrendScoreCurrent(9);
        form1.setTrendScorePrevious(8);
        form1.setTrendScoreBeforeLast(8);
        form1.setEquivalentDifferenceCurrent(23);
        form1.setEquivalentDifferencePrevious(20);
        form1.setEquivalentDifferenceBeforeLast(19);
        form1.setProvince("重庆");
        form1.setCity("重庆");
        form1.setUniversityTag("985");
        form1.setUniversityLevelTag("一流大学");
        form1.setRenameInfo("无");
        form1.setMajorTransferInfo("允许");
        form1.setUniversityProvince("重庆");
        form1.setCityLevelTag("一线城市");
        form1.setEducationLevel("本科");
        form1.setAffiliatedUnit("教育部");
        form1.setUniversityType("综合");
        form1.setPublicPrivateNature("公办");
        form1.setPostgraduateRate(30);
        form1.setUniversityRanking("全国前10");
        form1.setMasterProgramCount(50);
        form1.setMasterPrograms("计算机科学与技术, 软件工程, ...等");
        form1.setPhdProgramCount(20);
        form1.setPhdPrograms("计算机科学与技术, 软件工程, ...等");
        form1.setAdmissionRules2024("详见官网");
        form1.setRuankeRating("A+");
        form1.setRuankeRanking("全国前5");
        form1.setDisciplineEvaluation("A");
        form1.setMajorLevel("国家一流专业");
        form1.setHasMasterProgram("是");
        form1.setHasPhDProgram("是");
        dataList.add(form1);

        MajorGroupEnrollmentForm form2 = new MajorGroupEnrollmentForm();
        form2.setMajorCategory("人工智能");
        form2.setMajorGroupAdmittedCountPrevious(85);
        form2.setMajorGroupLowestScorePrevious(630);
        form2.setMajorGroupLowestRankPrevious(3300);
        form2.setPlanCountCurrent(80);
        form2.setEnrollmentCountCurrent(80);
        form2.setLowestScoreCurrent(640);
        form2.setLowestRankCurrent(3000);
        form2.setAvgScoreCurrent(655);
        form2.setAvgRankCurrent(2500);
        form2.setHighestScoreCurrent(690);
        form2.setHighestRankCurrent(500);
        form2.setPlanCountPrevious(70);
        form2.setEnrollmentCountPrevious(70);
        form2.setLowestScorePrevious(635);
        form2.setLowestRankPrevious(3200);
        form2.setAvgScorePrevious(650);
        form2.setAvgRankPrevious(2700);
        form2.setHighestScorePrevious(685);
        form2.setHighestRankPrevious(600);
        form2.setPlanCountBeforeLast(65);
        form2.setEnrollmentCountBeforeLast(65);
        form2.setLowestScoreBeforeLast(630);
        form2.setLowestRankBeforeLast(3500);
        form2.setAvgScoreBeforeLast(648);
        form2.setAvgRankBeforeLast(2800);
        form2.setHighestScoreBeforeLast(680);
        form2.setHighestRankBeforeLast(800);
        form2.setTrendScoreCurrent(9);
        form2.setTrendScorePrevious(9);
        form2.setTrendScoreBeforeLast(9);
        form2.setEquivalentDifferenceCurrent(25);
        form2.setEquivalentDifferencePrevious(24);
        form2.setEquivalentDifferenceBeforeLast(23);
        form2.setProvince("重庆");
        form2.setCity("重庆");
        form2.setUniversityTag("985");
        form2.setUniversityLevelTag("一流大学");
        form2.setRenameInfo("无");
        form2.setMajorTransferInfo("允许");
        form2.setUniversityProvince("重庆");
        form2.setCityLevelTag("一线城市");
        form2.setEducationLevel("本科");
        form2.setAffiliatedUnit("教育部");
        form2.setUniversityType("综合");
        form2.setPublicPrivateNature("公办");
        form2.setPostgraduateRate(35);
        form2.setUniversityRanking("全国前15");
        form2.setMasterProgramCount(45);
        form2.setMasterPrograms("人工智能, 软件工程, ...等");
        form2.setPhdProgramCount(18);
        form2.setPhdPrograms("人工智能, 软件工程, ...等");
        form2.setAdmissionRules2024("详见官网");
        form2.setRuankeRating("A");
        form2.setRuankeRanking("全国前10");
        form2.setDisciplineEvaluation("B+");
        form2.setMajorLevel("省级一流专业");
        form2.setHasMasterProgram("是");
        form2.setHasPhDProgram("是");
        dataList.add(form2);

        MajorGroupEnrollmentForm form3 = new MajorGroupEnrollmentForm();
        form3.setMajorCategory("软件工程");
        form3.setMajorGroupAdmittedCountPrevious(100);
        form3.setMajorGroupLowestScorePrevious(605);
        form3.setMajorGroupLowestRankPrevious(5800);
        form3.setPlanCountCurrent(100);
        form3.setEnrollmentCountCurrent(98);
        form3.setLowestScoreCurrent(615);
        form3.setLowestRankCurrent(5300);
        form3.setAvgScoreCurrent(638);
        form3.setAvgRankCurrent(3800);
        form3.setHighestScoreCurrent(675);
        form3.setHighestRankCurrent(1100);
        form3.setPlanCountPrevious(90);
        form3.setEnrollmentCountPrevious(90);
        form3.setLowestScorePrevious(600);
        form3.setLowestRankPrevious(6200);
        form3.setAvgScorePrevious(630);
        form3.setAvgRankPrevious(4300);
        form3.setHighestScorePrevious(665);
        form3.setHighestRankPrevious(1400);
        form3.setPlanCountBeforeLast(85);
        form3.setEnrollmentCountBeforeLast(85);
        form3.setLowestScoreBeforeLast(595);
        form3.setLowestRankBeforeLast(6500);
        form3.setAvgScoreBeforeLast(625);
        form3.setAvgRankBeforeLast(4800);
        form3.setHighestScoreBeforeLast(660);
        form3.setHighestRankBeforeLast(1600);
        form3.setTrendScoreCurrent(8);
        form3.setTrendScorePrevious(8);
        form3.setTrendScoreBeforeLast(7);
        form3.setEquivalentDifferenceCurrent(21);
        form3.setEquivalentDifferencePrevious(19);
        form3.setEquivalentDifferenceBeforeLast(17);
        form3.setProvince("重庆");
        form3.setCity("重庆");
        form3.setUniversityTag("985");
        form3.setUniversityLevelTag("一流大学");
        form3.setRenameInfo("无");
        form3.setMajorTransferInfo("允许");
        form3.setUniversityProvince("重庆");
        form3.setCityLevelTag("一线城市");
        form3.setEducationLevel("本科");
        form3.setAffiliatedUnit("教育部");
        form3.setUniversityType("综合");
        form3.setPublicPrivateNature("公办");
        form3.setPostgraduateRate(28);
        form3.setUniversityRanking("全国前12");
        form3.setMasterProgramCount(48);
        form3.setMasterPrograms("软件工程, 计算机科学与技术, ...等");
        form3.setPhdProgramCount(15);
        form3.setPhdPrograms("软件工程, 计算机科学与技术, ...等");
        form3.setAdmissionRules2024("详见官网");
        form3.setRuankeRating("A");
        form3.setRuankeRanking("全国前8");
        form3.setDisciplineEvaluation("A-");
        form3.setMajorLevel("国家一流专业");
        form3.setHasMasterProgram("是");
        form3.setHasPhDProgram("是");
        dataList.add(form3);

        return dataList;
    }

    /**
     * 测试ArtJhForm导出
     */
    public static void testArtJhFormExport() {
        try {
            // 创建ExcelExporter实例
            ExcelExporter<ArtJhForm> exporter = new ExcelExporter<>(ArtJhForm.class);
            exporter.setFileHeader("艺术类招生数据报表");

            // 创建测试数据
            List<ArtJhForm> dataList = createTestArtJhData();

            // 导出到文件
            try (FileOutputStream out = new FileOutputStream("艺术招生数据.xlsx")) {
                exporter.export(dataList, out);
                System.out.println("艺术招生数据导出成功！");
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试JHBeanOrgForm导出
     */
    public static void testJHBeanOrgFormExport() {
        try {
            // 创建ExcelExporter实例
            ExcelExporter<JHBeanOrgForm> exporter = new ExcelExporter<>(JHBeanOrgForm.class);
            exporter.setFileHeader("招生计划综合数据报表");

            // 创建测试数据
            List<JHBeanOrgForm> dataList = createTestJHBeanOrgData();

            // 导出到文件
            try (FileOutputStream out = new FileOutputStream("招生计划综合数据.xlsx")) {
                exporter.export(dataList, out);
                System.out.println("招生计划综合数据导出成功！");
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 创建ArtJhForm测试数据
     */
    private static List<ArtJhForm> createTestArtJhData() {
        List<ArtJhForm> dataList = new ArrayList<>();

        // 创建第一条测试数据
        ArtJhForm form1 = new ArtJhForm();
        form1.setYxdm(10001);
        form1.setYxmc("北京大学");
        form1.setPc("本科一批");
        form1.setXk("艺术(文)");
        form1.setYsfl("美术统考");
        form1.setZymc("绘画");
        form1.setZydm("130402");
        form1.setZkfx("油画方向");
        form1.setZkfx_ext("无");
        form1.setBhzy("无");
        form1.setXz(4);
        form1.setXf("10000");
        form1.setJhs(30);
        form1.setZybz("需要色盲色弱检查");
        form1.setZhcj(580.5);
        form1.setZyfs(240.0);
        form1.setWc(120);
        form1.setZgbm("教育部");
        form1.setYxcs("北京");
        form1.setCsbq("一线城市");
        form1.setPc_code("本科");
        form1.setInd_catg("综合类");
        form1.setInd_nature("公办");
        form1.setGxcc("985工程");
        form1.setBc("北大");
        form1.setBjzyjh("基础学科拔尖学生培养计划");
        form1.setXygm("原北京大学");
        form1.setGx_zzy("允许转专业");
        form1.setGx_ssd(200);
        form1.setGx_sszy("理学、工学、医学等");
        form1.setGx_bsd(180);
        form1.setGx_bszy("数学、物理、化学等");
        form1.setGx_byl2023(30.5);
        form1.setGx_pm("全国第1名");

        dataList.add(form1);

        // 添加第二条测试数据
        ArtJhForm form2 = new ArtJhForm();
        form2.setYxdm(10002);
        form2.setYxmc("清华大学");
        form2.setPc("本科一批");
        form2.setXk("艺术(理)");
        form2.setYsfl("设计统考");
        form2.setZymc("环境设计");
        form2.setZydm("130503");
        form2.setZkfx("景观方向");
        form2.setZkfx_ext("无");
        form2.setBhzy("无");
        form2.setXz(5);
        form2.setXf("12000");
        form2.setJhs(25);
        form2.setZybz("无");
        form2.setZhcj(600.0);
        form2.setZyfs(260.0);
        form2.setWc(80);
        form2.setZgbm("教育部");
        form2.setYxcs("北京");
        form2.setCsbq("一线城市");
        form2.setPc_code("本科");
        form2.setInd_catg("理工类");
        form2.setInd_nature("公办");
        form2.setGxcc("985工程");
        form2.setBc("清华");
        form2.setBjzyjh("国家级实验教学示范中心");
        form2.setXygm("原清华大学");
        form2.setGx_zzy("严格限制转专业");
        form2.setGx_ssd(150);
        form2.setGx_sszy("建筑学、城乡规划、艺术学等");
        form2.setGx_bsd(120);
        form2.setGx_bszy("建筑学、城乡规划、艺术学等");
        form2.setGx_byl2023(25.0);
        form2.setGx_pm("全国第2名");

        dataList.add(form2);

        return dataList;
    }

    /**
     * 创建JHBeanOrgForm测试数据
     */
    private static List<JHBeanOrgForm> createTestJHBeanOrgData() {
        List<JHBeanOrgForm> dataList = new ArrayList<>();

        // 创建第一条测试数据
        JHBeanOrgForm form1 = new JHBeanOrgForm();
        form1.setId(1);
        form1.setSf("浙江");
        form1.setNf("2025");
        form1.setYxdm("10001");
        form1.setYxmc("北京大学");
        form1.setYxmc_org("北京大学");
        form1.setZydm("080901");
        form1.setZymc("计算机科学与技术");
        form1.setZymc_org("计算机科学与技术");
        form1.setZyml("工学");
        form1.setZnzy("计算机类");
        form1.setPc("本科一批");
        form1.setXk("理科");
        form1.setJhs("100");
        form1.setFee("6000");
        form1.setXz("物理,化学");
        form1.setZdf(650);
        form1.setZdfwc(1200);

        // 设置年度数据
        form1.setLqrs_2024("50");
        form1.setFee_2024("6000");
        form1.setXz_2024("物理,化学");
        form1.setJhs_2024("55");
        form1.setZdf_2024("650");
        form1.setZdfwc_2024("1200");
        form1.setZgf_2024("680");
        form1.setZgfwc_2024("500");
        form1.setPjf_2024("665");
        form1.setPjfwc_2024("800");

        form1.setLqrs_2023("48");
        form1.setFee_2023("6000");
        form1.setJhs_2023("50");
        form1.setZdf_2023("645");
        form1.setZdfwc_2023("1300");
        form1.setZgf_2023("675");
        form1.setZgfwc_2023("600");
        form1.setPjf_2023("660");
        form1.setPjfwc_2023("900");

        form1.setLqrs_2022("45");
        form1.setJhs_2022("48");
        form1.setZdf_2022("640");
        form1.setZdfwc_2022("1400");
        form1.setZgf_2022("670");
        form1.setZgfwc_2022("700");
        form1.setPjf_2022("655");
        form1.setPjfwc_2022("1000");

        form1.setPjf_2021("650");
        form1.setPjfwc_2021("1100");

        // 趋势数据
        form1.setQsf_a(660);
        form1.setQsf_b(655);
        form1.setQsf_c(650);
        form1.setQsf(652);

        // 院校详细信息
        form1.setZyzjhs("5");
        form1.setXzyx("否");
        form1.setZyzlqrs("120");
        form1.setZyzzdf("630");
        form1.setZyzzdfwc("2000");
        form1.setZyz("工科实验班");
        form1.setZyz_desc("涵盖计算机、软件等专业");
        form1.setZybz("无");
        form1.setPc_code("A批次");
        form1.setPc_desc("提前批次");
        form1.setXk_code("01");
        form1.setXk_code_org("理");
        form1.setZx("普通招生");
        form1.setXk_desc("理工类");
        form1.setLqpc("本科提前批");
        form1.setInd_nature("公办");
        form1.setInd_catg("综合类");
        form1.setYxsf("北京");
        form1.setYxcs("北京市");
        form1.setYx_tags("985,211,双一流");
        form1.setYx_tags_all("综合类,985,211,双一流,重点大学");
        form1.setCnt_company(1000);
        form1.setCnt_employ(100000);
        form1.setCnt_grad(0.95f);
        form1.setIs_hz(1);
        form1.setIs_first(0);
        form1.setZnzys(10);
        form1.setZnzyls(5);
        form1.setZnjhs(200);
        form1.setYcwc(0);
        form1.setZsjz_2024("已完成招生");

        // 专业详细信息
        form1.setInfo_yxtag("一流大学A类");
        form1.setInfo_yxsp("全国顶尖水平");
        form1.setInfo_yxgmhzzs("规模宏大，招生计划充足");
        form1.setInfo_zzy("可转专业，限制较少");
        form1.setInfo_cssp("一线城市，教育资源丰富");
        form1.setInfo_lsdw("历史悠久，享誉国内外");
        form1.setInfo_lx("研究型大学");
        form1.setInfo_gsxz("教育部直属");
        form1.setInfo_byl("就业率高，深造率高");
        form1.setInfo_yxpm("全国前三");
        form1.setInfo_qxsszys("暂无");
        form1.setInfo_qxsszy("暂无");
        form1.setInfo_qxbszys("暂无");
        form1.setInfo_qxbszy("暂无");
        form1.setInfozy_rkpj("A+");
        form1.setInfozy_rkpm("全国第1名");
        form1.setInfozy_xkpg("A+");
        form1.setInfozy_zysp("信息技术类");
        form1.setInfozy_ssd("有");
        form1.setInfozy_bsd("有");

        dataList.add(form1);

        // 可以添加更多测试数据...
        return dataList;
    }
}
   