package com.career.utils;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.cookie.Cookie;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import com.career.utils.guowang.DealWithGuoWang;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class HttpSendUtils {

	public static String post(String url, Map<String, String> params, Map<String, String> headers) {
		CloseableHttpClient client = HttpClients.createDefault();
		HttpPost httpPost = new HttpPost(url);
		UrlEncodedFormEntity entity;
		List<NameValuePair> paramPairs = new ArrayList<NameValuePair>();
		if (params != null) {
			for (Map.Entry<String, String> en : params.entrySet()) {
				paramPairs.add(new BasicNameValuePair(en.getKey(), en.getValue()));
			}
		}
		if (headers != null) {
			for (Map.Entry<String, String> en : headers.entrySet()) {
				httpPost.setHeader(en.getKey(), en.getValue());
			}
		}
		try {
			entity = new UrlEncodedFormEntity(paramPairs, "UTF-8");
			httpPost.setEntity(entity);
			HttpResponse resp = client.execute(httpPost);
			HttpEntity respEntity = resp.getEntity();
			if (null != respEntity) {
				return EntityUtils.toString(respEntity, "UTF-8");
			}

		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		} catch (ClientProtocolException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			try {
				client.close();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return null;
	}

	public static String get(String url, Map<String, String> headers) {
		CloseableHttpClient client = HttpClients.createDefault();
		HttpGet httpGet = new HttpGet(url);
		if (headers != null) {
			for (Map.Entry<String, String> en : headers.entrySet()) {
				httpGet.setHeader(en.getKey(), en.getValue());
			}
		}
		try {
			HttpResponse resp = client.execute(httpGet);
			HttpEntity respEntity = resp.getEntity();
			if (null != respEntity) {
				return EntityUtils.toString(respEntity, "UTF-8");
			}
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		} catch (ClientProtocolException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			try {
				client.close();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return null;
	}
	
	public Cookie Cookie(){
        String url = "http://L.xueya63.com";
        BasicCookieStore cookieStore = new BasicCookieStore();
        Cookie cookie = null;  
        try {
	        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
	        HttpPost post = new HttpPost(url);
	        List<NameValuePair> list = new ArrayList<>();
	        list.add(new BasicNameValuePair("",""));
	 
	        post.addHeader("content-Ttpe","application/json; charset=UTF-8");
	        post.setEntity(new UrlEncodedFormEntity(list,"UTF-8"));
	        CloseableHttpResponse httpResponse = httpClient.execute(post);
	        List<Cookie> cookies = cookieStore.getCookies();
	        cookie =cookies.get(0);
	        httpClient.close();
        }catch(Exception ex) {
        	ex.printStackTrace();
        }
        return  cookie;
    }
	
	//抓取掌上考研的数据
	public static void runn2_1(int schoolCode) {
		Map<String, String> headers = new HashMap<>();
		String res = HttpSendUtils.get("https://static.kaoyan.cn/json/school/"+schoolCode+"/info.json", headers);
		File file = new File("E://kaoyan//"+schoolCode);
		if(!file.exists()) {
			try {
				file.mkdirs();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		writeTempFile(new File(file, "info_"+schoolCode+"_data.txt"), new StringBuffer(res));
		System.out.println(res);
	}
	
	//抓取掌上考研的复试分数线数据
	public static void runn2_2(int year, int schoolCode) {
		for(int degreeType=1;degreeType<=2;degreeType++) { //degreeType
			for(int pageNumber = 1; pageNumber<=25;pageNumber++) {
				Map<String, String> headers = new HashMap<>();
				String url = "https://static.kaoyan.cn/json/score/"+year+"/"+schoolCode+"/"+degreeType+"/"+pageNumber+".json";
				String res = HttpSendUtils.get(url, headers);
				
				System.out.println(url+", "+res.substring(0,20));
				if(res.indexOf("<Code>NoSuchKey</Code>") != -1) {
					break;
				}
				
				try {
					Thread.sleep(100);
				} catch (InterruptedException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				
				File file = new File("E://kaoyan//"+schoolCode);
				if(!file.exists()) {
					try {
						file.mkdirs();
					} catch (Exception e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
				writeTempFile(new File(file, "runn2_2_"+year+"_"+schoolCode+"_"+degreeType+"_"+pageNumber+"_history_data.txt"), new StringBuffer(res));
				
			}
		}
	}
	
	//抓取掌上考研的复试分数线数据
		public static void runn2_2_Patch(int year, int schoolCode, int endPageNumber) {
			for(int degreeType=1;degreeType<=2;degreeType++) { //degreeType
				for(int pageNumber = 11; pageNumber<= endPageNumber;pageNumber++) {
					Map<String, String> headers = new HashMap<>();
					String url = "https://static.kaoyan.cn/json/score/"+year+"/"+schoolCode+"/"+degreeType+"/"+pageNumber+".json";
					String res = HttpSendUtils.get(url, headers);
					
					System.out.println(url+", "+res.substring(0,20));
					if(res.indexOf("<Code>NoSuchKey</Code>") != -1) {
						break;
					}
					File file = new File("E://kaoyan//"+schoolCode);
					if(!file.exists()) {
						try {
							file.mkdirs();
						} catch (Exception e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
					}
					writeTempFile(new File(file, "runn2_2_"+year+"_"+schoolCode+"_"+degreeType+"_"+pageNumber+"_history_data.txt"), new StringBuffer(res));
					
				}
			}
		}
	
	//抓取掌上考研的招生专业数据（双一六建设数据）
	public static void runn2_3(int schoolCode) {
		Map<String, String> headers = new HashMap<>();
		String res = HttpSendUtils.get("https://static.kaoyan.cn/json/query/query_plan/"+schoolCode+".json", headers);
		File file = new File("E://kaoyan//"+schoolCode);
		if(!file.exists()) {
			try {
				file.mkdirs();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		writeTempFile(new File(file, "runn2_3_"+schoolCode+"history_data.txt"), new StringBuffer(res));
		System.out.println(res.substring(0,20));
	}
	
	//拉2024计划
	public static void runn2_2_JH(int schoolCode, int year) {
		Map<String, String> headers = new HashMap<>();
		Map<String, String> params = new HashMap<>();
		params.put("school_id", String.valueOf(schoolCode));
		params.put("keyword", "");
		params.put("page", "1");
		params.put("limit", "3000");
		params.put("is_apply", "2");
		params.put("recruit_type", "");
		params.put("year", String.valueOf(year));
	
		String res = HttpSendUtils.post("https://api.kaoyan.cn/pc/school/planList", params, headers);
		
		File file = new File("E://kaoyan//"+schoolCode);
		if(!file.exists()) {
			try {
				file.mkdirs();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		writeTempFile(new File(file, "runn2_2_JH_"+schoolCode+"_"+year+".txt"), new StringBuffer(res));
		System.out.println(res.substring(0,20));
	}
	
	
///	
	//调剂
	public static void runn2_4(int schoolCode) {
		Map<String, String> headers = new HashMap<>();
		Map<String, String> params = new HashMap<>();
		params.put("school_id", String.valueOf(schoolCode));
		params.put("keyword", "");
		params.put("page", "1");
		params.put("limit", "2000");
	
		String res = HttpSendUtils.post("https://api.kaoyan.cn/pc/adjust/schoolAdjustList", params, headers);
		
		File file = new File("E://kaoyan//"+schoolCode);
		if(!file.exists()) {
			try {
				file.mkdirs();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		writeTempFile(new File(file, "runn2_4_"+schoolCode+"adjust_data.txt"), new StringBuffer(res));
		System.out.println(res.substring(0,20));
	}
	
	//调剂2
		public static void runn2_42() {
			Map<String, String> headers = new HashMap<>();
			headers.put("Referrer Policy", "strict-origin-when-cross-origin");
			headers.put("Remote Address", "127.0.0.1:7890");
			headers.put(":authority", "api.kaoyan.cn");
			headers.put("Accept", "application/json, text/plain, */*");
			
			headers.put("Origin", "https://www.kaoyan.cn");
			headers.put("Referer","https://www.kaoyan.cn/");
			headers.put("Sec-Ch-Ua","\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"");
			headers.put("Sec-Ch-Ua-Mobile","?0");
			headers.put("Sec-Ch-Ua-Platform","Windows");
			headers.put("Sec-Fetch-Dest","empty");
			headers.put("Sec-Fetch-Mode","cors");
			headers.put("Sec-Fetch-Site","same-site");
			headers.put("User-Agent","Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
				
			
			
			Map<String, String> params = new HashMap<>();
			params.put("auto_sign", "8727456fc23639dc06feca922be911b563e7d1b18967db92e1a770ed91a791961c2e7603dc209c888470de11fa2869faa772aed06f51ae4c4cf9390949c8647f378de5978dad760fbc28c19e6f11f787");
			params.put("degree_type", "");
			params.put("keyword", "");
			params.put("level1", "");
			params.put("level2", "");
			params.put("limit", "10");
			params.put("page", "1");
			params.put("year", "");
		
			String res = HttpSendUtils.post("https://api.kaoyan.cn/pc/adjust/adjustList", params, headers);
			
			File file = new File("E://kaoyan//");
			if(!file.exists()) {
				try {
					file.mkdirs();
				} catch (Exception e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
			writeTempFile(new File(file, "runn2_42_adjust_list_test.txt"), new StringBuffer(res));
			System.out.println(res.substring(0,20));
		}
	
	//https://api.kaoyan.cn/pc/special/specialList
//专业

	public static void runn2_5() {
		Map<String, String> headers = new HashMap<>();
		Map<String, String> params = new HashMap<>();
		params.put("degree_type", "1");
		params.put("level1", "");
		params.put("page", "1");
		params.put("limit", "3000");
		params.put("level2", "");
		params.put("special_name", "");
	
		String res = HttpSendUtils.post("https://api.kaoyan.cn/pc/special/specialList", params, headers);
		
		File file = new File("E://kaoyan//");
		if(!file.exists()) {
			try {
				file.mkdirs();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		writeTempFile(new File(file, "runn2_5_major2_ZHUANYE.txt"), new StringBuffer(res));
		System.out.println(res.substring(0,20));
	}
	
	//抓取国家线
	public static void runn2_6(int schoolCode) {
		Map<String, String> headers = new HashMap<>();
		String res = HttpSendUtils.get("https://static.kaoyan.cn/json/query/query_gjx_score.json", headers);
		File file = new File("E://kaoyan//"+schoolCode);
		if(!file.exists()) {
			try {
				file.mkdirs();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		writeTempFile(new File(file, "runn2_6_"+schoolCode+"_desc.txt"), new StringBuffer(res));
		System.out.println(res.substring(0,20));
	}
	
	
	//拉学校标签
		public static void runn2_7() {
			Map<String, String> headers = new HashMap<>();
			Map<String, String> params = new HashMap<>();
			params.put("feature", "");
			params.put("province_id", "");
			params.put("school_name", "");
			params.put("type", "");
			params.put("limit", "3000");
			params.put("page", "1");
		
			String res = HttpSendUtils.post("https://api.kaoyan.cn/pc/school/schoolList", params, headers);
			
			File file = new File("E://kaoyan//");
			if(!file.exists()) {
				try {
					file.mkdirs();
				} catch (Exception e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
			writeTempFile(new File(file, "runn2_7_SCHOOL_TAG.txt"), new StringBuffer(res));
			System.out.println(res.substring(0,20));
		}
	
	
	public static void runn2_7(int schoolID) {
		Map<String, String> headers = new HashMap<>();
		String res = HttpSendUtils.get("https://api.zjzw.cn/web/api/?e_sort=zslx_rank,min&e_sorttype=desc,desc&local_province_id=&local_type_id=1&page=1&school_id="+schoolID+"&size=10&uri=apidata/api/gk/score/province&year=2023&signsafe=7ff79fbbabfe61358b9612a0b76eb302", headers);
		File file = new File("E://gaokao//"+schoolID );
		if(!file.exists()) {
			try {
				file.mkdirs();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		writeTempFile(new File(file, "runn2_7_"+schoolID+"_desc.txt"), new StringBuffer(res));
		System.out.println(schoolID +" - " +res.substring(0,20));
		
	}
	
	//抓取中公的省级公务员页面
	public static void runn(int i) {
		//
		/**
		 * 
		 * for(int i=1; i<= 2987;i++) { //692
			runn(i);
			try {
				Thread.sleep(500);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		}
		 * 
		 */
		
		Map<String, String> headers = new HashMap<>();
		headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
		headers.put("Cookie", "Hm_lvt_89058dc85076e76bc971f6b58d30b5e8=1700832699,1701134686,1701307779,1702214910; JSESSIONID=EC53AEC17F5D79D0816FB194B3D921D0; Hm_lpvt_89058dc85076e76bc971f6b58d30b5e8=1703386280");
		headers.put("Set-Cookie", "");
		String res = HttpSendUtils.get("https://cq.offcn.com/zw/2024/zw"+i+".html", headers);
		writeTempFile(new File("E://chongqing//"+(i%10)+"//ZGJY_zw"+i+".txt"), new StringBuffer(res));
		System.out.println(i);
	}
	
	
	
	public static void runYIFENYIDUAN(int year, int subj, String ProvinceId, String pname) {
		//https://tapi.cnjzy.net/api/Public/LineRankList
		Map<String, String> headers = new HashMap<>();
		Map<String, String> params = new HashMap<>();
		params.put("ProvinceId", ProvinceId);
		params.put("SubjectType", String.valueOf(subj));
		params.put("Year", String.valueOf(year));
	
		headers.put("Referrer Policy", "strict-origin-when-cross-origin");
		headers.put("Remote Address", "127.0.0.1:7890");
		headers.put(":authority", "api.kaoyan.cn");
		headers.put("Accept", "application/json, text/plain, */*");
		headers.put("Accept-Encoding", "gzip, deflate, br");
		headers.put("Accept-Language", "zh-CN,zh;q=0.9");
		
		headers.put("Connection", "keep-alive");
		headers.put("Origin", "https://t.cnjzy.net");
		headers.put("Referer","https://t.cnjzy.net/");
		headers.put("Host","tapi.cnjzy.net");
		headers.put("Sec-Ch-Ua","\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"");
		headers.put("Sec-Ch-Ua-Mobile","?0");
		headers.put("Sec-Ch-Ua-Platform","Windows");
		headers.put("Sec-Fetch-Dest","empty");
		headers.put("Sec-Fetch-Mode","cors");
		headers.put("X-Powered-By","ASP.NET");
		headers.put("Sec-Fetch-Site","same-site");
		headers.put("Token","110cd5bdcfd141be90467db9661be51e");
		headers.put("User-Agent","Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
		
		String res = HttpSendUtils.post("https://tapi.cnjzy.net/api/Public/LineRankList", params, headers);
		
		File file = new File("E://JZY//");
		if(!file.exists()) {
			try {
				file.mkdirs();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		writeTempFile(new File(file, pname + "_"+ProvinceId + "_" + year + "_" + subj + ".txt"), new StringBuffer(res));
		System.out.println(res.substring(0,20));
		
	}
	
	
	public static void runOFFCNPager(int pageNumber) {
		//https://tapi.cnjzy.net/api/Public/LineRankList
		Map<String, String> headers = new HashMap<>();
		Map<String, String> params = new HashMap<>();
		params.put("category_ids[]", "2");
		params.put("category_ids[]", "9");
		params.put("category_ids[]", "10");
		params.put("category_ids[]", "11");
		params.put("category_ids[]", "12");
		params.put("category_ids[]", "13");
		params.put("category_ids[]", "14");
		params.put("category_ids[]", "15");
		params.put("category_ids[]", "16");
		params.put("category_ids[]", "17");
		params.put("category_ids[]", "18");
		params.put("category_ids[]", "19");
		params.put("page_size", "50");
		params.put("page", String.valueOf(pageNumber));
	

		headers.put(":Request URL", "https://ajax.eoffcn.com/api/article/getArticle");
		headers.put(":Request Method", "POST");
		
		headers.put("Remote Address", "**************:443");
		headers.put("Referrer Policy", "no-referrer-when-downgrade");
		headers.put(":method", "POST");
		headers.put(":authority", "ajax.eoffcn.com");
		headers.put(":path", "/api/article/getArticle");
		headers.put("Accept", "application/json, text/javascript, */*; q=0.01");
		headers.put("Accept-Encoding", "gzip, deflate, br");
		headers.put("Accept-Language", "zh-CN,zh;q=0.9");
		headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
		
		headers.put("Origin", "https://www.eoffcn.com");
		headers.put("Referer","https://www.eoffcn.com/kszx/");
		headers.put("Host","tapi.cnjzy.net");
		headers.put("Sec-Ch-Ua","\"Not A(Brand\";v=\"99\", \"Google Chrome\";v=\"121\", \"Chromium\";v=\"121\"");
		headers.put("Sec-Ch-Ua-Mobile","?0");
		headers.put("Sec-Ch-Ua-Platform","\"Windows\"");
		
		headers.put("Sec-Fetch-Dest","empty");
		headers.put("Sec-Fetch-Mode","cors");
		headers.put("Sec-Fetch-Site","same-site");
		

		headers.put("acw_tc","0b328f3a17067081288306758ed6daa613894dae1ef459974a20cf26fa6938");
		
		headers.put("User-Agent","Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
		
		String res = HttpSendUtils.post("https://ajax.eoffcn.com/api/article/getArticle", params, headers);
		
		File file = new File("E://offcn//");
		if(!file.exists()) {
			try {
				file.mkdirs();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		writeTempFile(new File(file, "runOFFCN_"+pageNumber+".txt"), new StringBuffer(res));
		
	}
	
	
	public static String runOFFCNArticle(int no) {
		Map<String, String> headers = new HashMap<>();
		//headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
		//headers.put("Cookie", "Hm_lvt_89058dc85076e76bc971f6b58d30b5e8=1700832699,1701134686,1701307779,1702214910; JSESSIONID=EC53AEC17F5D79D0816FB194B3D921D0; Hm_lpvt_89058dc85076e76bc971f6b58d30b5e8=1703386280");
		headers.put("Set-Cookie", "mantis8961=c028200804cf46c48e267401cee4ce2e@8961; _ga=GA1.2.2068939227.1703398827; _qddaz=QD.vdlbsf.5wvw5v.lqj3oqbg; Hm_lvt_a6adf98bf5f7dd3d72872cf8b3535543=1706529707,1706704699; Hm_lvt_caaba5ffed655930fa7f43efc22fbd1e=1706529707,1706704699; Hm_lvt_3b55befee519827f19523fc1585b132c=1706529707,1706704699; Hm_lvt_a50bb98b162401cba9c89f1aa21a0b95=1706529707,1706704699; mantis_lp8961=https://www.eoffcn.com/zti/2023/sk/gsgwy53/?wt.mc_id=gwy_mbd_gbks_232623_pc&bd_vid=11432718196815647411; mantis_lp_id8961=lp:f180d57a50494bea87d7fe90e93ee4f8@8961; mantisrf8961=%257B%2522ad%2522%253A%2522baidu%2522%252C%2522source%2522%253A%2522referer%2522%252C%2522type%2522%253A%2522sem%2522%257D; _gid=GA1.2.68598622.1706704699; acw_tc=6eb974a317067078311377903e67fc8eed583d1cec2f9375a49e0b152f; www_session=eyJpdiI6IkFheHU3aEhLVmNCOFwvSlc3SU44ZGhBPT0iLCJ2YWx1ZSI6IkFscEdlNFc4M0ltVnlBczhFTkY3VGtERTJGNUI4R3NUVHpuOEhEOWxxWXg2dWtRQlwvZXVBRjVSeTJrTlE5TEJzIiwibWFjIjoiYjk1YmIxOGZlNmNkNTIzZDVhZjc4NjllNmUxZTM5M2YwNWUyODJiMjU3NDIxZTk2MjY5YzQwYzdkMDY2MDk4NyJ9; _qdda=3-1.1myr47; _qddab=3-gesyjj.ls1tsavo; XSRF-TOKEN=eyJpdiI6Ik1WVm5rQ3ZEalBuWitlWjB6SFhBYVE9PSIsInZhbHVlIjoiZmY1OUlDZk5lVEVScDRnQWhTZmpWTkNRZ2o3VnNEYjRpV2hiRjBiZzZjQW5veHprT09MXC9pVnFwQjcrNUtuK2YiLCJtYWMiOiIxOTZiZGUyOWI1ZTZmN2JlNGE3YzIwOGYwMzJiYTk0NzlmMzlhZDRhYTUzMDBkMjM0OTgyMDJjZDcwYjQ5NzdkIn0%3D; www_news_session=eyJpdiI6InFwamN4dzRqR0ZjQ1hMeFRzN0U2Znc9PSIsInZhbHVlIjoiZVBJMkN3NXM5QURqOGc5eVVVVEg1d3h1T1FKaDdJWk1kXC9teFpzYkgwNzMrSDJkS3NFWUkwSVh2cDdGd3JMVDIiLCJtYWMiOiI0MzY4YmJjNjFjODkxOTM0NzRjNTI1ZGE3YWZiMzhjOTM5N2MyM2U0MjcxNDk4ZWJhODM1YTUzODk1NzY5MjI4In0%3D; Hm_lpvt_a6adf98bf5f7dd3d72872cf8b3535543=1706709520; Hm_lpvt_3b55befee519827f19523fc1585b132c=1706709520; Hm_lpvt_a50bb98b162401cba9c89f1aa21a0b95=1706709520; _ga_HVCRMGQJM7=GS1.2.1706707831.5.1.1706709519.0.0.0; Hm_lpvt_caaba5ffed655930fa7f43efc22fbd1e=1706709520");
		String res = HttpSendUtils.get("https://www.eoffcn.com/kszx/detail/"+no+".html", headers);
		
		if(res == null || res.indexOf("<title>404错误页面_中公网校</title>") != -1) {
			return null;
		}
		
		String folder = String.valueOf(no % 1000); 
		System.out.println("runOFFCN_"+no+".txt");
		File file = new File("E://offcn//"+folder+"//");
		if(!file.exists()) {
			try {
				file.mkdirs();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		writeTempFile(new File(file, "runOFFCN_"+no+".txt"), new StringBuffer(res));
		return res;
	}
	
	
	private static void writeTempFile(File file, StringBuffer sb) {
	    try {
	      BufferedWriter bw = new BufferedWriter(new FileWriter(file));
	      bw.write(sb.toString());
	      bw.flush();
	      bw.close();
	    } catch (IOException e) {
	      e.printStackTrace();
	    } 
	  }
	//https://www.xunlugaokao.com/convert_exam_scores/0?exam_name=2023%E5%B1%8A%E6%B3%B8%E5%B7%9E%E4%B8%80%E8%AF%8A&exam_score=551&student_type=%E7%90%86%E7%A7%91
	public static String test(String aredID, String wl, String exam, int score) {
		System.out.println(score+"");
		Map<String, String> params = new HashMap<>();
		Map<String, String> headers = new HashMap<>();
		params.put("areaId", aredID);
		params.put("wenLi", wl);
		params.put("exam", exam);
		params.put("score", String.valueOf(score));
		headers.put("Content-Type", "application/json;charset=UTF-8");
		headers.put("Set-Cookie", "JSESSIONID=0AB8A0851D75C8D3EB15E8CE340B3C0A");
		String res = HttpSendUtils.post("http://l.xueya63.com/diagnose/data/score?_=1703604900236", params, headers);
		System.out.println(res+"");
		
		int start = res.indexOf("convertScore") + "convertScore\":\"".length();
		String cvt = res.substring(start, start + 3);
		System.out.println(aredID+","+wl+","+exam+","+score + " - " + cvt);
		return cvt;
	}
	
	public static String testXUNLU(int score) {
		Map<String, String> headers = new HashMap<>();
		//headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
		//headers.put("Cookie", "Hm_lvt_89058dc85076e76bc971f6b58d30b5e8=1700832699,1701134686,1701307779,1702214910; JSESSIONID=EC53AEC17F5D79D0816FB194B3D921D0; Hm_lpvt_89058dc85076e76bc971f6b58d30b5e8=1703386280");
		headers.put("Set-Cookie", "_gaokao_session=qJ%2BRRHgxAFdDcczo1GfTg533WGIvcczT%2Fii5EvYdx9EzQnc4PKeFWsBMoj72lcTmnVTXaY7eSBfUN2Z9i%2BQGgMlwfE19hVZ7TEsfyRmYVsmewVn1lbsaVq9PQm3%2BXHsmKQFjcBPefpyEq3dfXm2OEPGtceKOQbpYVsPgfJelzGPYkAXGBKxM91%2FYXu7vgHqWYu0sAbMNsjeQeE%2FTW31tdAiJ9hSouFEe8%2Brwxp%2FWG6OrdtY80bfGP5trB1pf0RAd6UIIiqst5C3OU2ZwNrn6yC1O3TvDA7o3OYI79HiuZq4Mpc30lkJBnNiYDl6evQZ7caM%2BZIgastEOsuvZP09dcIB4GQyxgVWSRbQI%2FbZqmhY8tzUSFltDqkRw2Usuex70ksRnOXGD3FLj%2B0D%2F9JgNyDn9yhswdWzkOK%2B8hFpJGuPXZij9P%2B594T97hzuFB74VZMCMEB0RVKOdR8pKlb6%2BcOlO%2FnjN%2FGjDwxwb%2B3vxi4T1lhqM5CAjlAW3a0UyE2db%2FRZJ8%2BqT6FxM%2BhZHVkXphVtB5gPGM0Hs4CBSWWDI%2FOd1M0kEHqm%2FYOqtiYORAelZKgppO99c5Rulzq2JVHaCzbZdhkhWqCuvi3%2FP1pCVVBXO1mi9BhDQnZoKBxfrub2v8lXQQgCrWnpFrJFyCZWRDu1CFgxbSZfDSso7uw7b5sG0RBQG96AsBmz7KciErhYEnab6VHxtMCfNHFfwqvzrJnl2Ny2QzFSB3USXuEqRt%2BSITSGIhtb286LGgHHW4s5ZgIA7ALQD72zNh8P2%2Fv7ZfZ1AHFTJ5b%2BQmJ9ICWrYgtPAQQg%3D--XZ%2BzXpv2qaB8OzM1--GirXIsmmHxTARpG7dnD02w%3D%3D; path=/; HttpOnly\r\n"
				+ "\r\n"
				+ "");
		String res = HttpSendUtils.get("https://www.xunlugaokao.com/convert_exam_scores/0?exam_name=2024%E5%B1%8A%E8%B5%84%E9%98%B3%E4%B8%80%E8%AF%8A&exam_score="+score+"&student_type=%E6%96%87%E7%A7%91", headers);
		
		System.out.println(""+score + " - " + res);
		return res;
	}
	
	public static void runXUNLU() {
		List<String> strList = new ArrayList<>();
		for(int i=150; i<= 700;i++) { //692
			String xx = testXUNLU(i);
			strList.add("#"+ i + "," + xx + "*");
			try {
				Thread.sleep(100);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		StringBuffer sb = new StringBuffer();
		for(String x : strList) {
			sb.append(x + "\r\n");
		}
		writeTempFile(new File("E://ZY-W1.txt"), sb);
	}

	
	public static void run() {
		List<String> strList = new ArrayList<>();
		for(int i=180; i<= 660;i++) { //692
			String xx = test("5115","1","1", i);
			strList.add("#"+ i + "," + xx + "*");
			try {
				Thread.sleep(100);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		StringBuffer sb = new StringBuffer();
		for(String x : strList) {
			sb.append(x + "\r\n");
		}
		writeTempFile(new File("E://zdksMY_W0.txt"), sb);
		strList.clear();
		strList = new ArrayList<>();
		
		for(int i=180; i<= 703;i++) { //692
			String xx = test("5115","5","1", i);
			strList.add("#"+ i + "," + xx + "*");
			try {
				Thread.sleep(100);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		sb = new StringBuffer();
		for(String x : strList) {
			sb.append(x + "\r\n");
		}
		writeTempFile(new File("E://zdksDZ_WL1.txt"), sb);
	}
	
	
	//循证 //{"ret_code":"1000","ret_msg":"","data":{"rank":"26407","fenshu":591},"total_lines":1}
	public static String test2(String aredID, String wl, String exam, int score) {
		System.out.println(score+"");
		Map<String, String> params = new HashMap<>();
		Map<String, String> headers = new HashMap<>();
		params.put("city", aredID);
		params.put("km", wl);
		params.put("zd", exam);
		params.put("fen", String.valueOf(score));
		headers.put("Content-Type", "application/json, text/javascript, */*; q=0.01");
		//headers.put("Set-Cookie", "PHPSESSID=diemm7lqt60qtktjnvrkfb0g24");
		headers.put("Set-Cookie", "PHPSESSID=diemm7lqt60qtktjnvrkfb0g24; userlogin=dNnnzKmqUD0Khhp2hNZwVj34z_x_gYXj_i_aqV1aAkS8JXA6E8U%3D; subject=%E6%96%87%E7%A7%91; score=550; km=1; ztype=1; rank=10591; valueType=T; pcType=0; zyType=%E6%9C%AC%E7%A7%91%E6%8F%90%E5%89%8D%E6%89%B9; pc=0");
		String res = HttpSendUtils.post("http://www.xzgk.net/Api/getwc?", params, headers);
		System.out.println(res+"");
		
		int start = res.indexOf("fenshu") + "fenshu\":".length();
		String cvt = res.substring(start, start + 3);
		System.out.println(aredID+","+wl+","+exam+","+score + " - " + cvt);
		return cvt;
	}
	
	//执教 //{"ret_code":"1000","ret_msg":"","data":{"rank":"26407","fenshu":591},"total_lines":1}
		public static void testZHIJIAO(int schoolID, int pageindex, List<String> SQL) {

			Map<String, String> params = new HashMap<>();
			Map<String, String> headers = new HashMap<>();
			params.put("pageindex", String.valueOf(pageindex));
			params.put("sid", String.valueOf(schoolID));
			headers.put("Accept", "application/json, text/javascript, */*; q=0.01");
			
			String res = HttpSendUtils.post("https://www.sczjw.com.cn/tools/sczjw.ashx?action=score_line", params, headers);
			System.out.println(res+"");
			
			//{"code":0,"msg":"获取成功","data":[{"sid":"14","sname":"成都农业科技职业学院","category":"财经商贸类","type":"中职","major":"市场营销","score":"205","zscount":"15","year":"2019","exam_form":"文化+技能测试"},{"sid":"14","sname":"成都农业科技职业学院","category":"旅游服务一类","type":"中职","major":"旅游管理","score":"281","zscount":"30","year":"2019","exam_form":"文化+技能测试"},{"sid":"14","sname":"成都农业科技职业学院","category":"财经商贸类","type":"中职","major":"物流管理","score":"193","zscount":"20","year":"2019","exam_form":"文化+技能测试"},{"sid":"14","sname":"成都农业科技职业学院","category":"土木水利类","type":"中职","major":"工程造价","score":"203","zscount":"50","year":"2019","exam_form":"文化+技能测试"},{"sid":"14","sname":"成都农业科技职业学院","category":"土木水利类","type":"中职","major":"建筑装饰工程技术","score":"209","zscount":"50","year":"2019","exam_form":"文化+技能测试"},{"sid":"14","sname":"成都农业科技职业学院","category":"土木水利类","type":"中职","major":"物业管理","score":"254","zscount":"20","year":"2019","exam_form":"文化+技能测试"},{"sid":"14","sname":"成都农业科技职业学院","category":"土木水利类","type":"中职","major":"建筑工程技术","score":"322","zscount":"50","year":"2019","exam_form":"文化+技能测试"},{"sid":"14","sname":"成都农业科技职业学院","category":"农林牧渔类","type":"中职","major":"水产养殖技术","score":"411","zscount":"25","year":"2019","exam_form":"文化+技能测试"},{"sid":"14","sname":"成都农业科技职业学院","category":"农林牧渔类","type":"中职","major":"畜牧兽医","score":"447","zscount":"70","year":"2019","exam_form":"文化+技能测试"},{"sid":"14","sname":"成都农业科技职业学院","category":"农林牧渔类","type":"中职","major":"休闲农业","score":"394","zscount":"40","year":"2019","exam_form":"文化+技能测试"},{"sid":"14","sname":"成都农业科技职业学院","category":"农林牧渔类","type":"中职","major":"园林技术","score":"443","zscount":"30","year":"2019","exam_form":"文化+技能测试"},{"sid":"14","sname":"成都农业科技职业学院","category":"农林牧渔类","type":"中职","major":"园艺技术","score":"434","zscount":"40","year":"2019","exam_form":"文化+技能测试"},{"sid":"14","sname":"成都农业科技职业学院","category":"土木水利类","type":"中职","major":"园林工程技术","score":"258","zscount":"50","year":"2019","exam_form":"文化+技能测试"},{"sid":"14","sname":"成都农业科技职业学院","category":"农林牧渔类","type":"中职","major":"种子生产与经营","score":"324","zscount":"20","year":"2019","exam_form":"文化+技能测试"},{"sid":"14","sname":"成都农业科技职业学院","category":"农林牧渔类","type":"中职","major":"农产品加工与质量检测","score":"442","zscount":"20","year":"2019","exam_form":"文化+技能测试"}],"totle":4}
			
			int startIndexSID = -1;
			while((startIndexSID = res.indexOf("sname")) != -1) {
				res = res.substring(startIndexSID);
				String sname = res.substring(res.indexOf("sname") + 8, res.indexOf("\",\"category\":\""));
				String category = res.substring(res.indexOf("category") + 11, res.indexOf("\",\"type\":\""));
				String type = res.substring(res.indexOf("type") + 7, res.indexOf("\",\"major\":\""));
				String major = res.substring(res.indexOf("major") + 8, res.indexOf("\",\"score\":\""));
				String score = res.substring(res.indexOf("score") + 8, res.indexOf("\",\"zscount\":\""));
				String zscount = res.substring(res.indexOf("zscount") + 10, res.indexOf("\",\"year\":\""));
				String year = res.substring(res.indexOf("year") + 7, res.indexOf("\",\"exam_form\":\""));
				String exam_form = res.substring(res.indexOf("exam_form") + 12, res.indexOf("\"}"));
				res = res.substring(20);
				SQL.add("insert into s5_sc_dz_self_temp(yxmc, nf, zsfw, zymc, ksfx, zdf, zsrs, lqlx) values('"+sname+"','"+year+"','"+category+"','"+major+"','"+exam_form+"','"+score+"','"+zscount+"','"+type+"');\r\n");
			}
			
		}
		
		public static void testZHIJIAOPage(int schoolID) {
			Map<String, String> headers = new HashMap<>();
			String res = HttpSendUtils.get("https://www.sczjw.com.cn/school.html?sid=" +schoolID , headers);
			File file = new File("E://danzhao2024//" );
			if(!file.exists()) {
				try {
					file.mkdirs();
				} catch (Exception e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
			writeTempFile(new File(file, "testZHIJIAOPage_"+schoolID+".txt"), new StringBuffer(res));
			
		}
		
		
		//https://www.stats.gov.cn/sj/tjbz/tjyqhdmhcxhfdm/2023/65.html
		
		public static void testGetCity(int cityID) {
			Map<String, String> headers = new HashMap<>();
			String res = HttpSendUtils.get("https://www.stats.gov.cn/sj/tjbz/tjyqhdmhcxhfdm/2023/"+cityID+".html", headers);
			if(res == null || res.indexOf("<title>404 Not Found</title>") != -1) {
				return;
			}
			File file = new File("E://city2024//" );
			if(!file.exists()) {
				try {
					file.mkdirs();
				} catch (Exception e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
			writeTempFile(new File(file, "testGetCity_"+cityID+".txt"), new StringBuffer(res));
			
		}
		
		
		//
		
		public static void testGetCity(String cityID) {
			cityID = "X1onLKe3N5lvX32Flt99I8Rp2uLgH9";
			Map<String, String> headers = new HashMap<>();
			
			headers.put("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
			headers.put("Cookie", "ASP.NET_SessionId=bw3d224kvhkv2r0wo5qyovum; yzmcode=8R66; Hm_lvt_10fdbfb351346526e18e6995afa7ee08=1706202251; Hm_lpvt_10fdbfb351346526e18e6995afa7ee08=1706202475");
			headers.put("Host", "www.zhiliaojy.com");
			headers.put("Referer", "http://www.zhiliaojy.com/admin/newselect_school_center.aspx");
			
			String res = HttpSendUtils.get("http://www.zhiliaojy.com/admin/zxxiangqing.aspx?dataid="+cityID, headers);
			if(res == null || res.indexOf("<title>404 Not Found</title>") != -1) {
				return;
			}
			File file = new File("E://zhiliaojy.com//" );
			if(!file.exists()) {
				try {
					file.mkdirs();
				} catch (Exception e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
			writeTempFile(new File(file, "s_"+cityID+".txt"), new StringBuffer(res));
			
		}
		
		
		public static void testGuoJiaDianWang() {
			Map<String, String> headers = new HashMap<>();
			
			headers.put("Referrer", "https://zhaopin.sgcc.com.cn/sgcchr/static/unitPart.html?bullet_id=09649edd2d8c4feabccb7907b677dbc2");
			headers.put("Cookie", "PW9ydXnjjO8XS=60JSrDV9hEqwoloC34BIrEdOEfgy4S56gVaZr.rfgdjE3xXpcQ2aQm7swhYvsNrlSAZtAEnN_POI2.M8dNdCkKuq; Hm_lvt_70db3bdcae52b494918dcc1cad9333c4=1706890718; Hm_lpvt_70db3bdcae52b494918dcc1cad9333c4=1706890781; PW9ydXnjjO8XT=0bfb9.O97Htw2_jDJIySXpOEZfuolOqoa.H1l6TQ1nsryFXQLBSOqJ7N1YMt7h4w1pOBW_WziyCkzAqb_kBvzX.7ZRvRLPbadX_biVHUcN16yVpJmqOGzgxxNu4F5H0DBgMz5KvZR62IUommd5HMe1gi8ie2vanFxYYtTMPY7OeqF.LI8CsD3Hp51tyf63J3k.FurO6WtRaki9YHTY4OcXSXpyl8jSbFKOAtGag7ZgbxrkGVobWN9LB8B4Q0Zn1oRiBy_T0gp00Req6UUSy3guJDWpgafEMPG1UJdfsRiB749i0DoGRTjq.uIzzyidJhzmAHGeyDxgoWCMdLUICWcs.sxivPG.GyqeoSKv4v2nq6G3CZ5FO3Wga3GpaquCpKwY2bVjkamlh3EzBgR0NTDTG");
			headers.put("Host", "zhaopin.sgcc.com.cn");
			headers.put("Referrer Policy", "strict-origin-when-cross-origin");
			
			List<String> list = null;
			try {
				list = DealWithGuoWang.deal();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			for(String x : list) {
				String res = HttpSendUtils.get("https://zhaopin.sgcc.com.cn/sgcchr/html/bulletin/" + x, headers);
				File file = new File("E://dianwang2024//" );
				if(!file.exists()) {
					try {
						file.mkdirs();
					} catch (Exception e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
				writeTempFile(new File(file, "gw_"+x+".txt"), new StringBuffer(res));
			}
			
			
			File file = new File("E://goujiadianwang.com//" );
			if(!file.exists()) {
				try {
					file.mkdirs();
				} catch (Exception e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
			
			//writeTempFile(new File(file, "s_"+cityID+".txt"), new StringBuffer(res));
			
		}
		
		
		
		public static void runZHIJIAO() {
			
			List<String> SQL = new ArrayList<String>();
			for(int i=149;i<600;i++) {
				testZHIJIAOPage(i);
				for(int ix=0;ix<10;ix++) {
					//testZHIJIAO(i,ix, SQL);
				}
			}
			
			StringBuffer sb = new StringBuffer();
			//for(int i=0;i<SQL.size();i++) {
				//sb.append(SQL.get(i));
			//}
			
			//writeTempFile(new File("E://danzhao2024//DZ_SELF_DATA.txt"), sb);
		}
	
	public static void run2() {
		List<String> strList = new ArrayList<>();
		for(int i=180; i<= 660;i++) { //692
			String xx = test2("dz","2","1", i);
			strList.add("#"+ i + "," + xx + "*");
			try {
				Thread.sleep(2000);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		StringBuffer sb = new StringBuffer();
		for(String x : strList) {
			sb.append(x + "\r\n");
		}
		writeTempFile(new File("E://zdksDZ_W1.txt"), sb);
		strList.clear();
		strList = new ArrayList<>();
		
		for(int i=180; i<= 703;i++) { //692
			String xx = test2("dz","1","1", i);
			strList.add("#"+ i + "," + xx + "*");
			try {
				Thread.sleep(100);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		sb = new StringBuffer();
		for(String x : strList) {
			sb.append(x + "\r\n");
		}
		writeTempFile(new File("E://zdksDZ_L1.txt"), sb);
	}

	public static void main(String[] args) {
		//for(int i = 1281638; i > 1281438;i--) {
			//runOFFCNArticle(i);
		//}
		testGuoJiaDianWang();
	}
}
