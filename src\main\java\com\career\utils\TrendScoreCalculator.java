package com.career.utils;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import com.career.db.ZDKSRank;
import com.career.db.ZyzdJDBC;

public class TrendScoreCalculator {
	
	private final TreeMap<Integer, Integer> rankScoreMap; // key: rank, value: score
    
    public TrendScoreCalculator(Map<Integer, Integer> rankScoreData) {
        this.rankScoreMap = new TreeMap<>(rankScoreData);
    }
    
    /**
     * 将历史位次转换为今年的等效位次
     * @param historicalRanks 过去三年的位次数组
     * @return 今年的等效位次数组
     */
    public int[] convertToCurrentYearRanks(int[] historicalRanks) {
        int[] currentRanks = new int[historicalRanks.length];
        for (int i = 0; i < historicalRanks.length; i++) {
            currentRanks[i] = findClosestRank(historicalRanks[i]);
        }
        return currentRanks;
    }
    
    /**
     * 在今年的位次数据中找到与历史位次最接近的位次
     * @param historicalRank 历史位次
     * @return 今年最接近的位次
     */
    private int findClosestRank(int historicalRank) {
        // 处理边界情况
        if (rankScoreMap.isEmpty()) {
            throw new IllegalStateException("Rank score data is empty");
        }
        
        // 查找小于等于历史位次的最大键
        Map.Entry<Integer, Integer> floorEntry = rankScoreMap.floorEntry(historicalRank);
        // 查找大于等于历史位次的最小键
        Map.Entry<Integer, Integer> ceilingEntry = rankScoreMap.ceilingEntry(historicalRank);
        
        // 处理只有一边有值的情况
        if (floorEntry == null) {
            return ceilingEntry.getKey();
        }
        if (ceilingEntry == null) {
            return floorEntry.getKey();
        }
        
        // 比较两边哪个更接近
        int floorDiff = historicalRank - floorEntry.getKey();
        int ceilingDiff = ceilingEntry.getKey() - historicalRank;
        
        return (floorDiff <= ceilingDiff) ? floorEntry.getKey() : ceilingEntry.getKey();
    }
    
    /**
     * 根据位次获取对应的分数
     * @param rank 位次
     * @return 分数
     */
    public int getScoreByRank(int rank) {
        Integer score = rankScoreMap.get(rank);
        if (score == null) {
            score = 0;//throw new IllegalArgumentException("Rank not found: " + rank);
        }
        return score;
    }
    
    public static void main(String args[]) {
    	ZyzdJDBC zyzdJDBC = new ZyzdJDBC();
    	List<ZDKSRank> list = zyzdJDBC.getAllZdksRankByKL(2025, "陕西", "1");
    	Map<Integer, Integer> rankScoreData = new TreeMap<Integer, Integer>();
    	for(ZDKSRank rank : list) {
    		rankScoreData.put(rank.getWc(), rank.getScore());
    	}
    	
    	TrendScoreCalculator calculator = new TrendScoreCalculator(rankScoreData);
    	
        
        // 学校过去三年位次
        int[] historicalRanks = {11866, 8713, 7758};
        
        // 转换为今年等效位次
        int[] currentRanks = calculator.convertToCurrentYearRanks(historicalRanks);
        
        // 输出结果
        System.out.println("今年等效位次:");
        for (int rank : currentRanks) {
            System.out.println("位次: " + rank + ", 分数: " + calculator.getScoreByRank(rank));
        }
    	
    }
}
