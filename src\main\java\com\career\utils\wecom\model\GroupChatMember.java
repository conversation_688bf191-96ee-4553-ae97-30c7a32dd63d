package com.career.utils.wecom.model;

import java.util.Date;
import java.text.SimpleDateFormat;

/**
 * 企业微信客户群成员信息模型
 */
public class GroupChatMember {
    // 所属群ID (对应 wecom_chat_id)
    private String chatId;
    // 成员ID (对应 wecom_member_userid)
    private String userId;
    // 成员类型: 1-企业成员, 2-外部联系人 (对应 wecom_member_type)
    private int type;
    // 入群时间 (对应 wecom_join_time)
    private Date joinTime;
    // 加入方式: 1-由群成员邀请入群, 2-由二维码扫码入群, 3-管理员导入 (对应 wecom_join_scene)
    private Integer joinScene;
    // 在群里的昵称 (对应 wecom_group_nickname)
    private String groupNickname;
    // 邀请人成员ID (对应 wecom_invitor_userid)
    private String invitorUserId;
    
    private String wecom_saas_id;
    
    // 群版本号
    private String groupVersion;
    
    // 成员关系同步时间戳 (用于增量同步)
    private Date memberSyncTime;
    
    public String getWecom_saas_id() {
		return wecom_saas_id;
	}

	public void setWecom_saas_id(String wecom_saas_id) {
		this.wecom_saas_id = wecom_saas_id;
	}

	public String getChatId() {
        return chatId;
    }
    
    public void setChatId(String chatId) {
        this.chatId = chatId;
    }
    
    public String getUserId() {
        return userId;
    }
    
    public void setUserId(String userId) {
        this.userId = userId;
    }
    
    public int getType() {
        return type;
    }
    
    public void setType(int type) {
        this.type = type;
    }
    
    public Date getJoinTime() {
        return joinTime;
    }
    
    public void setJoinTime(Date joinTime) {
        this.joinTime = joinTime;
    }
    
    public Integer getJoinScene() {
        return joinScene;
    }
    
    public void setJoinScene(Integer joinScene) {
        this.joinScene = joinScene;
    }
    
    public String getGroupNickname() {
        return groupNickname;
    }
    
    public void setGroupNickname(String groupNickname) {
        this.groupNickname = groupNickname;
    }
    
    public String getInvitorUserId() {
        return invitorUserId;
    }
    
    public void setInvitorUserId(String invitorUserId) {
        this.invitorUserId = invitorUserId;
    }
    
    public String getGroupVersion() {
        return groupVersion;
    }

    public void setGroupVersion(String groupVersion) {
        this.groupVersion = groupVersion;
    }
    
    public Date getMemberSyncTime() {
        return memberSyncTime;
    }
    
    public void setMemberSyncTime(Date memberSyncTime) {
        this.memberSyncTime = memberSyncTime;
    }

    @Override
    public String toString() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedTime = joinTime != null ? sdf.format(joinTime) : "N/A";
        String formattedSyncTime = memberSyncTime != null ? sdf.format(memberSyncTime) : "N/A";

        return "群ID: " + chatId +
               ", 成员ID: " + userId +
               ", 类型: " + (type == 1 ? "企业成员" : "外部联系人") +
               ", 昵称: " + groupNickname +
               ", 加入时间: " + formattedTime +
               ", 加入方式: " + joinScene +
               ", 邀请人ID: " + (invitorUserId != null ? invitorUserId : "无") +
               ", 同步时间: " + formattedSyncTime;
    }
} 