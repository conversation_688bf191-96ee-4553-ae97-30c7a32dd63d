package com.career.utils.spider;

import java.io.*;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.career.utils.HttpSendUtils;
import com.career.utils.Tools;
import com.career.utils.liuxue.HttpSendUtils2;

public class Spider_ShiYou {
	
	// 中石油
		public static StringBuffer shiyou()throws Exception {
			StringBuffer SQL = new StringBuffer();
			
			
			//
			
			Map<String, String> headers = new HashMap<>();
			String URL = "https://zhaopin.cnpc.com.cn/web/notificationAnnouncement/showInfoByPost";
			System.out.println(URL);
			
			Map<String, String> params = new HashMap<>();
			params.put("unifyId", "8b8b6c9e92dd87dc019424cde16e5632");
			params.put("postId", "13867390");
			params.put("unifyType", "4");
			params.put("emstatus", "0601");
			
			headers.put("referer", "https://zhaopin.cnpc.com.cn/web/tzggShow.html?");
			headers.put("Accept", "*/*");
			headers.put("Accept-Encoding", "gzip, deflate, br, zstd");
			headers.put("Accept-Language", "zh-CN,zh;q=0.9");
			headers.put("Host", "zhaopin.cnpc.com.cn");
			headers.put("cookie", "JSESSIONID=3028DFE2DF6E0C9B5032624109DC3D89; __jsluid_s=3b9741a1bdf0d2791fc687770b9229eb; yfx_c_g_u_id_10000001=_ck24120321562413767556471201147; GahMaMhTvKpTS=60agA5aaLV.ttxh424MfQHcbUwfHs0WVm0ZqcxjXTyo7dT0o4d90RGeXrXtpR52wBZRkwXFz5yFp1HPaqwRG9qYA; yfx_c_g_u_id_10000004=_ck24120321563510157556337195159; yfx_f_l_v_t_10000001=f_t_1733234184371__r_t_1735548484732__v_t_1735548484732__r_c_2; yfx_f_l_v_t_10000004=f_t_1733234195016__r_t_1735799387205__v_t_1735799387205__r_c_5; GahMaMhTvKpTT=0cICLMmLUjdrztOReuYV2ernz.fsMd3yefl2.kfX6mBdw1Gz_usCEZa6Qhz90gAcSp6KM4BVHGEr._4JshdzVf5A89eaUcTswO4sre5GQCXANaszo4.JYD5bkvuBCDsddYccBiTLV5lpXtbp0N1x2ZmU8uxjx_JEiKxjRSgVx_jRj1F4jwt_JNHOF.vbJMJgURL4fND774WnDwH.V9eTXjYhRg96Y7sKT8jClQgv07vI4_igrzVtzyPC00UxY8rH2QPiskyvCzL5MmXiZ9JK.tnJ3OkeEFb.gkgg1cJXjq8O6QXE5NWACQBPl9iDmmvx9WyQa4rWFTGFlLV3rpjH3.rH57aXan_85KS_rrwYI_3YL4AcBwxOb7BzA2nJoyTZt4O8knBLb29_4ML_FRFKSStt1G_JtmL4_iFXZJfzsqt20klFylLNnaXt85otlMGp9ER2S4krDQ_isRgT9zTsJ6a");
			headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36");

			String resultPageList = HttpSendUtils.post(URL, params, headers);
				
			Tools.println(resultPageList);
			return SQL;
		}
		
		public static StringBuffer shiyou2()throws Exception {
			StringBuffer SQL = new StringBuffer();
			
			
			//
			
			Map<String, String> headers = new HashMap<>();
			String URL = "https://dz580api.zk789.cn/zy580/major/list/";
			System.out.println(URL);
			
			
			
			Map<String, String> params = new HashMap<>();
			
			headers.put("Accept","application/json, text/plain, */*");
					headers.put("Accept-Encoding","gzip, deflate, br, zstd");
							headers.put("Accept-Language","zh-CN,zh;q=0.9");
									headers.put("Cache-Control","no-cache");
											headers.put("Connection","keep-alive");
			headers.put("Content-Type","application/json");
			headers.put("Host","dz580api.zk789.cn");
			headers.put("Origin","https://dz580.zk789.cn");
				headers.put("Referer","https://dz580.zk789.cn/");
				headers.put("Sec-Fetch-Dest","empty");
			headers.put("Sec-Fetch-Mode","cors");
			headers.put("Sec-Fetch-Site","same-site");
			headers.put("User-Agent","Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
			headers.put("X-Requested-With","XMLHttpRequest");
			headers.put("accessToken","eyJ0eXBlIjoiSldUIiwiYWxnIjoiSFMyNTYiLCJ0eXAiOiJKV1QifQ.eyJleHAiOjE3NDM4MTQ3NjIsInVzZXJJZCI6ImJhMTQxMDk0OTIyMzQ4In0.G_7dU4kp19G67w30baF7tpj5PGT2Z453GIvt_jfJ5mQ");
			headers.put("appId","P01023001");
			headers.put("client","PC");
			headers.put("module","ZY");
			headers.put("project","P01023");
			headers.put("sec-ch-ua","\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"");
					headers.put("sec-ch-ua-mobile","?0");
					headers.put("sec-ch-ua-platform","\"Windows\"");
				headers.put("sign","888f307bda3982999c0156030a738a35");

			

			String resultPageList = HttpSendUtils.post(URL, params, headers);
				
			Tools.println(resultPageList);
			return SQL;
		}
		
		
		static String u(String tt) {
			String en_key = "eFPIKDkOCHio8sVfprqdxt0jEw9gMb";
			String result = tt.toLowerCase() + '&' + en_key.toLowerCase();
			return result;
		}
		
		static String get_u_sign(String data) {
			String sign_data = u(data);
			String encodedString = Base64.getEncoder().encodeToString(sign_data.getBytes());
		    return calculateMD5(encodedString);
		}
		
		private static String calculateMD5(String originalString) {
	        try {
	            // 创建MD5加密实例
	            MessageDigest md = MessageDigest.getInstance("MD5");
	 
	            // 执行加密操作
	            byte[] messageDigest = md.digest(originalString.getBytes());
	 
	            // 将得到的散列值转换为十六进制
	            StringBuilder sb = new StringBuilder();
	            for (byte b : messageDigest) {
	                sb.append(String.format("%02x", b));
	            }
	 
	            // 返回MD5散列值
	            return sb.toString();
	        } catch (NoSuchAlgorithmException e) {
	            throw new RuntimeException("MD5加密算法不可用", e);
	        }
	    }

		

		public static void main(String[] args) {
			try {
				shiyou2();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		private static void writeTempFile(File file, StringBuffer sb) {
			try {
				BufferedWriter bw = new BufferedWriter(new FileWriter(file));
				bw.write(sb.toString());
				bw.flush();
				bw.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}

}
