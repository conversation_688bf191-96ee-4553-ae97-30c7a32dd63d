// Decompiled by Jad v1.5.8g. Copyright 2001 <PERSON>.
// Jad home page: http://www.kpdus.com/jad.html
// Decompiler options: packimports(3) 
// Source File Name:   SchoolMarjorCommonBean.java

package com.zsdwf.db;

import java.io.*;

public class SchoolMarjorCommonBean
{

    public SchoolMarjorCommonBean()
    {
        yxmc = null;
        zymc = null;
        yxdm = null;
        zydm = null;
        zyfl = null;
        pc = null;
        xk = null;
        zdf = null;
        zdfwc = null;
        pjf = null;
        pjfwc = null;
        zgf = null;
        zybz = null;
        yxbz = null;
        sf = null;
        zdf20 = null;
        zdf21 = null;
        zdf22 = null;
        zdfwc20 = null;
        zdfwc21 = null;
        zdfwc22 = null;
        cs = null;
        qy = null;
        jxsj = null;
        lsy = null;
        is985 = null;
        is211 = null;
        isSyl = null;
        isQj = null;
        gjzdxk = null;
        ssd = null;
        bsd = null;
        z<PERSON><PERSON> = null;
        zdmj = null;
        yxlx = null;
        bxlx = null;
        bxxz = null;
        gfwz = null;
        jj = null;
        qs = null;
        usnews = null;
        xyh = null;
        wsl = null;
        rk = null;
        vrUrl = null;
        logoUrl = null;
        kuapc = null;
        hyhp = null;
        hymx = null;
        ybws = null;
        recordCnt = 0;
        curPageNum = 0;
        yxbyl = null;
    }

    public String getZdfwc20()
    {
        return zdfwc20;
    }

    public void setZdfwc20(String zdfwc20)
    {
        this.zdfwc20 = zdfwc20;
    }

    public String getZdfwc21()
    {
        return zdfwc21;
    }

    public void setZdfwc21(String zdfwc21)
    {
        this.zdfwc21 = zdfwc21;
    }

    public String getZdfwc22()
    {
        return zdfwc22;
    }

    public void setZdfwc22(String zdfwc22)
    {
        this.zdfwc22 = zdfwc22;
    }

    public String getZdf20()
    {
        return zdf20;
    }

    public void setZdf20(String zdf20)
    {
        this.zdf20 = zdf20;
    }

    public String getZdf21()
    {
        return zdf21;
    }

    public void setZdf21(String zdf21)
    {
        this.zdf21 = zdf21;
    }

    public String getZdf22()
    {
        return zdf22;
    }

    public void setZdf22(String zdf22)
    {
        this.zdf22 = zdf22;
    }

    public String getYxbz()
    {
        return yxbz;
    }

    public void setYxbz(String yxbz)
    {
        this.yxbz = yxbz;
    }

    public String getKuapc()
    {
        return kuapc;
    }

    public void setKuapc(String kuapc)
    {
        this.kuapc = kuapc;
    }

    public String getSf()
    {
        return sf;
    }

    public void setSf(String sf)
    {
        this.sf = sf;
    }

    public String getYxdm()
    {
        return yxdm;
    }

    public void setYxdm(String yxdm)
    {
        this.yxdm = yxdm;
    }

    public String getZydm()
    {
        return zydm;
    }

    public void setZydm(String zydm)
    {
        this.zydm = zydm;
    }

    public int getCurPageNum()
    {
        return curPageNum;
    }

    public void setCurPageNum(int curPageNum)
    {
        this.curPageNum = curPageNum;
    }

    public int getRecordCnt()
    {
        return recordCnt;
    }

    public void setRecordCnt(int recordCnt)
    {
        this.recordCnt = recordCnt;
    }

    public String getHyhp()
    {
        return hyhp;
    }

    public void setHyhp(String hyhp)
    {
        this.hyhp = hyhp;
    }

    public String getHymx()
    {
        return hymx;
    }

    public void setHymx(String hymx)
    {
        this.hymx = hymx;
    }

    public String getYbws()
    {
        return ybws;
    }

    public void setYbws(String ybws)
    {
        this.ybws = ybws;
    }

    public String getZyfl()
    {
        return zyfl;
    }

    public void setZyfl(String zyfl)
    {
        this.zyfl = zyfl;
    }

    public String getYxbyl()
    {
        return yxbyl;
    }

    public void setYxbyl(String yxbyl)
    {
        this.yxbyl = yxbyl;
    }

    public String getPjf()
    {
        return pjf;
    }

    public void setPjf(String pjf)
    {
        this.pjf = pjf;
    }

    public String getPjfwc()
    {
        return pjfwc;
    }

    public void setPjfwc(String pjfwc)
    {
        this.pjfwc = pjfwc;
    }

    public String getZgf()
    {
        return zgf;
    }

    public void setZgf(String zgf)
    {
        this.zgf = zgf;
    }

    public String getCs()
    {
        return cs;
    }

    public void setCs(String cs)
    {
        this.cs = cs;
    }

    public String getQy()
    {
        return qy;
    }

    public void setQy(String qy)
    {
        this.qy = qy;
    }

    public String getJxsj()
    {
        return jxsj;
    }

    public void setJxsj(String jxsj)
    {
        this.jxsj = jxsj;
    }

    public String getLsy()
    {
        return lsy;
    }

    public void setLsy(String lsy)
    {
        this.lsy = lsy;
    }

    public String getIs985()
    {
        return is985;
    }

    public void setIs985(String is985)
    {
        this.is985 = is985;
    }

    public String getIs211()
    {
        return is211;
    }

    public void setIs211(String is211)
    {
        this.is211 = is211;
    }

    public String getIsSyl()
    {
        return isSyl;
    }

    public void setIsSyl(String isSyl)
    {
        this.isSyl = isSyl;
    }

    public String getIsQj()
    {
        return isQj;
    }

    public void setIsQj(String isQj)
    {
        this.isQj = isQj;
    }

    public String getGjzdxk()
    {
        return gjzdxk;
    }

    public void setGjzdxk(String gjzdxk)
    {
        this.gjzdxk = gjzdxk;
    }

    public String getSsd()
    {
        return ssd;
    }

    public void setSsd(String ssd)
    {
        this.ssd = ssd;
    }

    public String getBsd()
    {
        return bsd;
    }

    public void setBsd(String bsd)
    {
        this.bsd = bsd;
    }

    public String getZdsys()
    {
        return zdsys;
    }

    public void setZdsys(String zdsys)
    {
        this.zdsys = zdsys;
    }

    public String getZdmj()
    {
        return zdmj;
    }

    public void setZdmj(String zdmj)
    {
        this.zdmj = zdmj;
    }

    public String getYxlx()
    {
        return yxlx;
    }

    public void setYxlx(String yxlx)
    {
        this.yxlx = yxlx;
    }

    public String getBxlx()
    {
        return bxlx;
    }

    public void setBxlx(String bxlx)
    {
        this.bxlx = bxlx;
    }

    public String getBxxz()
    {
        return bxxz;
    }

    public void setBxxz(String bxxz)
    {
        this.bxxz = bxxz;
    }

    public String getGfwz()
    {
        return gfwz;
    }

    public void setGfwz(String gfwz)
    {
        this.gfwz = gfwz;
    }

    public String getJj()
    {
        return jj;
    }

    public void setJj(String jj)
    {
        this.jj = jj;
    }

    public String getQs()
    {
        return qs;
    }

    public void setQs(String qs)
    {
        this.qs = qs;
    }

    public String getUsnews()
    {
        return usnews;
    }

    public void setUsnews(String usnews)
    {
        this.usnews = usnews;
    }

    public String getXyh()
    {
        return xyh;
    }

    public void setXyh(String xyh)
    {
        this.xyh = xyh;
    }

    public String getWsl()
    {
        return wsl;
    }

    public void setWsl(String wsl)
    {
        this.wsl = wsl;
    }

    public String getRk()
    {
        return rk;
    }

    public void setRk(String rk)
    {
        this.rk = rk;
    }

    public String getVrUrl()
    {
        return vrUrl;
    }

    public void setVrUrl(String vrUrl)
    {
        this.vrUrl = vrUrl;
    }

    public String getLogoUrl()
    {
        return logoUrl;
    }

    public void setLogoUrl(String logoUrl)
    {
        this.logoUrl = logoUrl;
    }

    public String getYxmc()
    {
        return yxmc;
    }

    public void setYxmc(String yxmc)
    {
        this.yxmc = yxmc;
    }

    public String getZymc()
    {
        return zymc;
    }

    public void setZymc(String zymc)
    {
        this.zymc = zymc;
    }

    public String getPc()
    {
        return pc;
    }

    public void setPc(String pc)
    {
        this.pc = pc;
    }

    public String getXk()
    {
        return xk;
    }

    public void setXk(String xk)
    {
        this.xk = xk;
    }

    public String getZdf()
    {
        return zdf;
    }

    public void setZdf(String zdf)
    {
        this.zdf = zdf;
    }

    public String getZdfwc()
    {
        return zdfwc;
    }

    public void setZdfwc(String zdfwc)
    {
        this.zdfwc = zdfwc;
    }

    public String getZybz()
    {
        return zybz;
    }

    public void setZybz(String zybz)
    {
        this.zybz = zybz;
    }

    public static void main(String args[])
    {
        String xx = "\u5317\u65B9\u6C11\u65CF\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u5408\u80A5\u5DE5\u4E1A\u5927\u5B66\uFF09\u3001\u9752\u5C9B\u519C\u4E1A\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E2D\u56FD\u6D77\u6D0B\u5927\u5B66\uFF09\u3001\u5B81\u590F\u7406\u5DE5\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E1C\u5317\u5927\u5B66\u3001\u6D59\u6C5F\u5DE5\u4E1A\u5927\u5B66\u3001\u9655\u897F\u5E08\u8303\u5927\u5B66\u7B49\uFF09\u3001\u94F6\u5DDD\u80FD\u6E90\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u798F\u5DDE\u5927\u5B66\u7B49\uFF09\u3001\u5E7F\u897F\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u534E\u5357\u7406\u5DE5\u5927\u5B66\uFF09\u3001\u4E91\u5357\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u590D\u65E6\u5927\u5B66\uFF09\u3001\u5C71\u4E1C\u5DE5\u827A\u7F8E\u672F\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u5C71\u4E1C\u5927\u5B66\uFF09\u3001\u5409\u9996\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E2D\u5C71\u5927\u5B66\uFF09\u3001\u6CB3\u897F\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u590D\u65E6\u5927\u5B66\uFF09\u3001\u65B0\u7586\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u897F\u5B89\u4EA4\u901A\u5927\u5B66\uFF09\u3001\u6CC9\u5DDE\u5E08\u8303\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u53F0\u6E7E\u8F85\u4EC1\u5927\u5B66\u3001\u53F0\u6E7E\u9F99\u534E\u79D1\u6280\u5927\u5B66\u7B49\uFF09\u3001\u4E95\u5188\u5C71\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u540C\u6D4E\u5927\u5B66\u3001\u53A6\u95E8\u5927\u5B66\u7B49\uFF09\u3001\u4E2D\u5C71\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u9999\u6E2F\u5927\u5B66\uFF09\u3001\u897F\u85CF\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u897F\u5357\u4EA4\u901A\u5927\u5B66\uFF09\u3001\u91CD\u5E86\u4E09\u5CE1\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E1C\u5357\u5927\u5B66\uFF09\u3001\u8D35\u5DDE\u5E08\u8303\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u53A6\u95E8\u5927\u5B66\uFF09\u3001\u897F\u5317\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u897F\u5317\u5DE5\u4E1A\u5927\u5B66-\u897F\u5B89\u7535\u5B50\u79D1\u6280\u5927\u5B66\uFF09\u3001\u897F\u5B89\u7535\u5B50\u79D1\u6280\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u897F\u5317\u5DE5\u4E1A\u5927\u5B66-\u897F\u5317\u5927\u5B66\uFF09\u3001\u5B89\u5FBD\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u673A\u6784\uFF1A\u4E2D\u79D1\u9662\u52A8\u7269\u7814\u7A76\u6240\uFF09\u3001\u897F\u5357\u4EA4\u901A\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u673A\u6784\uFF1A\u4E2D\u79D1\u9662\u9AD8\u80FD\u7269\u7406\u7814\u7A76\u6240\uFF09\u3001\u897F\u5357\u8D22\u7ECF\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u7535\u5B50\u79D1\u6280\u5927\u5B66\uFF09\u3001\u5317\u4EAC\u5916\u56FD\u8BED\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E2D\u56FD\u653F\u6CD5\u5927\u5B66\uFF09\u3001\u5929\u6D25\u533B\u79D1\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u5929\u6D25\u5927\u5B66\uFF09\u3001\u5357\u4EAC\u4FE1\u606F\u5DE5\u7A0B\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E2D\u56FD\u79D1\u5B66\u9662\u5927\u5B66\uFF09\u3001\u897F\u5357\u79D1\u6280\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u673A\u6784\uFF1A\u4E2D\u56FD\u5DE5\u7A0B\u7269\u7406\u7814\u7A76\u9662\u7B49\uFF09\u3001\u534E\u5357\u519C\u4E1A\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u6FB3\u95E8\u79D1\u6280\u5927\u5B66\uFF09\u3001\u4E0A\u6D77\u4E2D\u533B\u836F\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u673A\u6784\uFF1A\u4E0A\u6D77\u4EA4\u901A\u5927\u5B66\u3001\u534E\u4E1C\u5E08\u8303\u5927\u5B66\u3001\u4E2D\u56FD\u4E2D\u533B\u79D1\u5B66\u9662\uFF09\u3001\u9ED1\u9F99\u6C5F\u5DE5\u7A0B\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u673A\u6784\uFF1A\u4E2D\u5174\u901A\u8BAF\u80A1\u4EFD\u6709\u9650\u516C\u53F8\uFF09\u3001\u798F\u5EFA\u5DE5\u7A0B\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u53F0\u6E7E\u9AD8\u6821\u7FA4\uFF09\u3001\u95FD\u5357\u5E08\u8303\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u53F0\u6E7E\u9AD8\u6821\u7FA4\uFF09\u3001\u4E2D\u56FD\u653F\u6CD5\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u5317\u4EAC\u5916\u56FD\u8BED\u5927\u5B66\uFF09\u3001\u7535\u5B50\u79D1\u6280\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u897F\u5357\u8D22\u7ECF\u5927\u5B66\uFF09\u3001\u4E50\u5C71\u5E08\u8303\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u6B66\u6C49\u5927\u5B66\uFF09\u3001\u4E2D\u56FD\u6D88\u9632\u6551\u63F4\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E2D\u56FD\u6C11\u7528\u822A\u7A7A\u98DE\u884C\u5B66\u9662\uFF09\u3001\u91CD\u5E86\u533B\u79D1\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u897F\u5357\u5927\u5B66\u6216\u590D\u65E6\u5927\u5B66\uFF09\u3001\u957F\u6C5F\u5E08\u8303\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u5C71\u4E1C\u79D1\u6280\u5927\u5B66\uFF09\u3001\u91CD\u5E86\u4E09\u5CE1\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E1C\u5357\u5927\u5B66\uFF09\u3001\u5929\u6C34\u5E08\u8303\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u897F\u5357\u4EA4\u901A\u5927\u5B66\uFF09\u3001\u5170\u5DDE\u6587\u7406\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E2D\u56FD\u4F20\u5A92\u5927\u5B66\uFF09\u3001\u5170\u5DDE\u8D22\u7ECF\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E2D\u592E\u8D22\u7ECF\u5927\u6216\u5BF9\u5916\u7ECF\u6D4E\u8D38\u6613\u5927\u5B66\uFF09\u3001\u534E\u5357\u519C\u4E1A\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u6FB3\u95E8\u79D1\u6280\u5927\u5B66\uFF09\u3001\u5E7F\u897F\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u534E\u5357\u7406\u5DE5\u5927\u5B66\uFF09\u3001\u8D35\u5DDE\u533B\u79D1\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u5317\u4EAC\u534F\u548C\u533B\u5B66\u9662\uFF09\u3001\u9075\u4E49\u533B\u79D1\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u5E7F\u5DDE\u533B\u79D1\u5927\u5B66\uFF09\u3001\u8D35\u5DDE\u5DE5\u7A0B\u5E94\u7528\u6280\u672F\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u897F\u5357\u5927\u5B66\u6216\u4E2D\u56FD\u77FF\u4E1A\u5927\u5B66\uFF09\u3001\u516D\u76D8\u6C34\u5E08\u8303\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E0A\u6D77\u5DE5\u7A0B\u6280\u672F\u5927\u5B66\uFF09\u3001\u6CB3\u5357\u5DE5\u4E1A\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u53F0\u6E7E\u4E2D\u539F\u5927\u5B66\uFF09\u3001\u547C\u548C\u6D69\u7279\u6C11\u65CF\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E2D\u592E\u6C11\u65CF\u5927\u5B66\uFF09\u3001\u5317\u65B9\u6C11\u65CF\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u5408\u80A5\u5DE5\u4E1A\u5927\u5B66\uFF09\u3001\u65B0\u7586\u5DE5\u7A0B\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u5317\u4EAC\u79D1\u6280\u5927\u5B66\u3001\u5317\u4EAC\u90AE\u7535\u5927\u5B66\u3001\u4E2D\u56FD\u77FF\u4E1A\u5927\u5B66\uFF09\u3001\u65B0\u7586\u7406\u5DE5\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u6D59\u6C5F\u5DE5\u4E1A\u5927\u5B66\uFF09\u3001\u65B0\u7586\u79D1\u6280\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u6CB3\u5317\u79D1\u6280\u5927\u5B66\uFF09\u3001\u65B0\u7586\u5E08\u8303\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u534E\u4E1C\u5E08\u8303\u5927\u5B66\u3001\u6CB3\u5317\u5E08\u8303\u5927\u5B66\u3001\u897F\u5317\u5E08\u8303\u5927\u5B66\u3001\u6D59\u6C5F\u5E08\u8303\u5927\u5B66\u3001\u5927\u8FDE\u7406\u5DE5\u5927\u5B66\uFF09\u3001\u4E2D\u56FD\u77F3\u6CB9\u5927\u5B66\uFF08\u5317\u4EAC\uFF09\u514B\u62C9\u739B\u4F9D\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u7535\u5B50\u79D1\u6280\u5927\u5B66\uFF09\u3001\u4E0A\u6D77\u4E2D\u533B\u836F\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E0A\u6D77\u4EA4\u901A\u5927\u5B66\u3001\u534E\u4E1C\u5E08\u8303\u5927\u5B66\uFF09\u3001\u6D59\u6C5F\u8B66\u5BDF\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u534E\u4E1C\u653F\u6CD5\u5927\u5B66\uFF09\u3001\u4E91\u5357\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E2D\u56FD\u79D1\u5B66\u9662\uFF09";
        try
        {
            BufferedWriter bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(new File("D://a.txt"))));
            String x[] = xx.split("\uFF09\u3001");
            int i = 1;
            String args1[];
            int k = (args1 = x).length;
            for(int j = 0; j < k; j++)
            {
                String e = args1[j];
                if(e.indexOf("\u8054\u5408") == -1)
                {
                    System.out.println((new StringBuilder(String.valueOf(i++))).append(" -ERR- ").append(e).toString());
                    bw.write((new StringBuilder(String.valueOf(i++))).append(" -ERR- ").append(e).append("\r\n").toString());
                } else
                {
                    String prefix = e.substring(0, e.indexOf("\u8054\u5408") - 1);
                    String subfix = e.substring(e.indexOf("\u8054\u5408") + 7);
                    System.out.println((new StringBuilder(" -- ")).append(prefix).append(" -- ").append(subfix).toString());
                    bw.write((new StringBuilder("insert into alhpy values('")).append(prefix).append("' ,'").append(subfix).append("');\r\n").toString());
                }
            }

            bw.flush();
            bw.close();
        }
        catch(Exception e1)
        {
            e1.printStackTrace();
        }
    }

    private String yxmc;
    private String zymc;
    private String yxdm;
    private String zydm;
    private String zyfl;
    private String pc;
    private String xk;
    private String zdf;
    private String zdfwc;
    private String pjf;
    private String pjfwc;
    private String zgf;
    private String zybz;
    private String yxbz;
    private String sf;
    private String zdf20;
    private String zdf21;
    private String zdf22;
    private String zdfwc20;
    private String zdfwc21;
    private String zdfwc22;
    private String cs;
    private String qy;
    private String jxsj;
    private String lsy;
    private String is985;
    private String is211;
    private String isSyl;
    private String isQj;
    private String gjzdxk;
    private String ssd;
    private String bsd;
    private String zdsys;
    private String zdmj;
    private String yxlx;
    private String bxlx;
    private String bxxz;
    private String gfwz;
    private String jj;
    private String qs;
    private String usnews;
    private String xyh;
    private String wsl;
    private String rk;
    private String vrUrl;
    private String logoUrl;
    private String kuapc;
    private String hyhp;
    private String hymx;
    private String ybws;
    private int recordCnt;
    private int curPageNum;
    private int nf;
    private String yxbyl;
	public int getNf() {
		return nf;
	}

	public void setNf(int nf) {
		this.nf = nf;
	}
    
}
