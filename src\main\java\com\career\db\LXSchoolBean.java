package com.career.db;

public class LXSchoolBean {

	private String ad_count;
	private String address;
	private String aps_slug;
	private String area;
	private String count;
	private String country;
	private String cover_image;
	private String gpa;
	private String gre;
	private String logo_image;
	private String qs_rank;
	private String rating;
	private String rating_count;
	private String region;
	private String releasing_result;
	private String school_name;
	private String school_name_cn;
	private String short_name;
	private String summary;
	private String toefl;
	private String ietls;
	private String usnews_rank;
	private String website;
	private String cost;
	private String worldRankName;
	private int worldRankValue;
	private String localRankName;
	private int localRankValue;
	private String idOuter;
	private String nature;
	private String majorHot;
	private String majorExcellent;
	private String yxTese;
	
	public String getMajorHot() {
		return majorHot;
	}
	public void setMajorHot(String majorHot) {
		this.majorHot = majorHot;
	}
	public String getMajorExcellent() {
		return majorExcellent;
	}
	public void setMajorExcellent(String majorExcellent) {
		this.majorExcellent = majorExcellent;
	}
	public String getYxTese() {
		return yxTese;
	}
	public void setYxTese(String yxTese) {
		this.yxTese = yxTese;
	}
	public String getNature() {
		return nature;
	}
	public void setNature(String nature) {
		this.nature = nature;
	}
	public String getIdOuter() {
		return idOuter;
	}
	public void setIdOuter(String idOuter) {
		this.idOuter = idOuter;
	}
	public String getWorldRankName() {
		return worldRankName;
	}
	public void setWorldRankName(String worldRankName) {
		this.worldRankName = worldRankName;
	}
	public int getWorldRankValue() {
		return worldRankValue;
	}
	public void setWorldRankValue(int worldRankValue) {
		this.worldRankValue = worldRankValue;
	}
	public String getLocalRankName() {
		return localRankName;
	}
	public void setLocalRankName(String localRankName) {
		this.localRankName = localRankName;
	}
	public int getLocalRankValue() {
		return localRankValue;
	}
	public void setLocalRankValue(int localRankValue) {
		this.localRankValue = localRankValue;
	}
	public String getIetls() {
		return ietls;
	}
	public void setIetls(String ietls) {
		this.ietls = ietls;
	}

	private int recordSUM;
	
	public int getRecordSUM() {
		return recordSUM;
	}
	public void setRecordSUM(int recordSUM) {
		this.recordSUM = recordSUM;
	}
	public String getCost() {
		return cost;
	}
	public void setCost(String cost) {
		this.cost = cost;
	}
	public String getAd_count() {
		return ad_count;
	}
	public void setAd_count(String ad_count) {
		this.ad_count = ad_count;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public String getAps_slug() {
		return aps_slug;
	}
	public void setAps_slug(String aps_slug) {
		this.aps_slug = aps_slug;
	}
	public String getArea() {
		return area;
	}
	public void setArea(String area) {
		this.area = area;
	}
	public String getCount() {
		return count;
	}
	public void setCount(String count) {
		this.count = count;
	}
	public String getCountry() {
		return country;
	}
	public void setCountry(String country) {
		this.country = country;
	}
	public String getCover_image() {
		return cover_image;
	}
	public void setCover_image(String cover_image) {
		this.cover_image = cover_image;
	}
	public String getGpa() {
		return gpa;
	}
	public void setGpa(String gpa) {
		this.gpa = gpa;
	}
	public String getGre() {
		return gre;
	}
	public void setGre(String gre) {
		this.gre = gre;
	}
	public String getLogo_image() {
		return logo_image;
	}
	public void setLogo_image(String logo_image) {
		this.logo_image = logo_image;
	}
	public String getQs_rank() {
		return qs_rank;
	}
	public void setQs_rank(String qs_rank) {
		this.qs_rank = qs_rank;
	}
	public String getRating() {
		return rating;
	}
	public void setRating(String rating) {
		this.rating = rating;
	}
	public String getRating_count() {
		return rating_count;
	}
	public void setRating_count(String rating_count) {
		this.rating_count = rating_count;
	}
	public String getRegion() {
		return region;
	}
	public void setRegion(String region) {
		this.region = region;
	}
	public String getReleasing_result() {
		return releasing_result;
	}
	public void setReleasing_result(String releasing_result) {
		this.releasing_result = releasing_result;
	}
	public String getSchool_name() {
		return school_name;
	}
	public void setSchool_name(String school_name) {
		this.school_name = school_name;
	}
	public String getSchool_name_cn() {
		return school_name_cn;
	}
	public void setSchool_name_cn(String school_name_cn) {
		this.school_name_cn = school_name_cn;
	}
	public String getShort_name() {
		return short_name;
	}
	public void setShort_name(String short_name) {
		this.short_name = short_name;
	}
	public String getSummary() {
		return summary;
	}
	public void setSummary(String summary) {
		this.summary = summary;
	}
	public String getToefl() {
		return toefl;
	}
	public void setToefl(String toefl) {
		this.toefl = toefl;
	}
	public String getUsnews_rank() {
		return usnews_rank;
	}
	public void setUsnews_rank(String usnews_rank) {
		this.usnews_rank = usnews_rank;
	}
	public String getWebsite() {
		return website;
	}
	public void setWebsite(String website) {
		this.website = website;
	}
	
	public String generateSQL() {
		summary = summary == null ? "" : summary.replaceAll("'", " ");
		address = address == null ? "" : address.replaceAll("'", " ");
		aps_slug = aps_slug == null ? "" : aps_slug.replaceAll("'", " ");
		school_name = school_name == null ? "" : school_name.replaceAll("'", " ");
		region = region == null ? "" : region.replaceAll("'", " ");
		
		String SQL = "insert into career_lx_school_ext(cost,ad_count, address, aps_slug, area, count, country, cover_image, gpa, gre, logo_image, qs_rank, rating, rating_count, region, releasing_result, school_name, school_name_cn, short_name, summary, toefl, usnews_rank, website) " + 
	"values('"+cost+"','"+ad_count+"','" +address+"','" +aps_slug+"','" +area+"','" +count+"','" +country+"','" +cover_image+"','" +gpa+"','" +gre+"','" +logo_image+"','" +qs_rank+"','" +rating+"','" +rating_count+"','" +region+"','" +releasing_result+"','" +school_name+"','" +school_name_cn+"','" +short_name+"','" +summary+"','" +toefl+"','" +usnews_rank+"','" +website+"');";
		return SQL;
	}
	
}
