package com.career.utils.excel.model;

import com.career.utils.excel.annotation.ExcelColumn;
import java.io.Serializable;

public class JHBeanOrgForm implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    // 常量字符串用于注解 - 一级表头
    private static final String BASIC_INFO_HEADER = "基本信息";
    private static final String YEAR_2024_HEADER = "2024年数据";
    private static final String YEAR_2023_HEADER = "2023年数据";
    private static final String YEAR_2022_HEADER = "2022年数据";
    private static final String YEAR_2021_HEADER = "2021年数据";
    private static final String TREND_HEADER = "趋势数据";
    private static final String UNIVERSITY_DETAIL_HEADER = "院校详细信息";
    private static final String MAJOR_DETAIL_HEADER = "专业详细信息";

    // 默认构造器
    public JHBeanOrgForm() {
    }

    // ===== 基本信息 =====
    @ExcelColumn(name = "序号", sort = 1, width = 10, parent = BASIC_INFO_HEADER, level = 1)
    private Integer id;
    
    @ExcelColumn(name = "省份", sort = 2, width = 12, parent = BASIC_INFO_HEADER, level = 1)
    private String sf;
    
    @ExcelColumn(name = "年份", sort = 3, width = 12, parent = BASIC_INFO_HEADER, level = 1)
    private String nf;
    
    @ExcelColumn(name = "院校代码", sort = 4, width = 12, parent = BASIC_INFO_HEADER, level = 1)
    private String yxdm;
    
    @ExcelColumn(name = "院校名称", sort = 5, width = 20, parent = BASIC_INFO_HEADER, level = 1)
    private String yxmc;
    
    @ExcelColumn(name = "院校名称（原始）", sort = 6, width = 20, parent = BASIC_INFO_HEADER, level = 1)
    private String yxmc_org;
    
    @ExcelColumn(name = "专业代码", sort = 7, width = 12, parent = BASIC_INFO_HEADER, level = 1)
    private String zydm;
    
    @ExcelColumn(name = "专业名称", sort = 8, width = 20, parent = BASIC_INFO_HEADER, level = 1)
    private String zymc;
    
    @ExcelColumn(name = "专业名称（原始）", sort = 9, width = 20, parent = BASIC_INFO_HEADER, level = 1)
    private String zymc_org;
    
    @ExcelColumn(name = "专业门类", sort = 10, width = 15, parent = BASIC_INFO_HEADER, level = 1)
    private String zyml;
    
    @ExcelColumn(name = "组类专业", sort = 11, width = 15, parent = BASIC_INFO_HEADER, level = 1)
    private String znzy;
    
    @ExcelColumn(name = "批次", sort = 12, width = 12, parent = BASIC_INFO_HEADER, level = 1)
    private String pc;
    
    @ExcelColumn(name = "科类", sort = 13, width = 12, parent = BASIC_INFO_HEADER, level = 1)
    private String xk;
    
    @ExcelColumn(name = "计划数", sort = 14, width = 12, parent = BASIC_INFO_HEADER, level = 1)
    private String jhs;
    
    @ExcelColumn(name = "学费", sort = 15, width = 12, parent = BASIC_INFO_HEADER, level = 1)
    private String fee;
    
    @ExcelColumn(name = "选考科目", sort = 16, width = 15, parent = BASIC_INFO_HEADER, level = 1)
    private String xz;
    
    @ExcelColumn(name = "最低分", sort = 17, width = 12, parent = BASIC_INFO_HEADER, level = 1)
    private Integer zdf;
    
    @ExcelColumn(name = "最低分位次", sort = 18, width = 15, parent = BASIC_INFO_HEADER, level = 1)
    private Integer zdfwc;

    // ===== 2024年数据 =====
    @ExcelColumn(name = "录取人数", sort = 19, width = 12, parent = YEAR_2024_HEADER, level = 1)
    private String lqrs_2024;
    
    @ExcelColumn(name = "学费", sort = 20, width = 12, parent = YEAR_2024_HEADER, level = 1)
    private String fee_2024;
    
    @ExcelColumn(name = "选考科目", sort = 21, width = 15, parent = YEAR_2024_HEADER, level = 1)
    private String xz_2024;
    
    @ExcelColumn(name = "计划数", sort = 22, width = 12, parent = YEAR_2024_HEADER, level = 1)
    private String jhs_2024;
    
    @ExcelColumn(name = "最低分", sort = 23, width = 12, parent = YEAR_2024_HEADER, level = 1)
    private String zdf_2024;
    
    @ExcelColumn(name = "最低分位次", sort = 24, width = 15, parent = YEAR_2024_HEADER, level = 1)
    private String zdfwc_2024;
    
    @ExcelColumn(name = "最高分", sort = 25, width = 12, parent = YEAR_2024_HEADER, level = 1)
    private String zgf_2024;
    
    @ExcelColumn(name = "最高分位次", sort = 26, width = 15, parent = YEAR_2024_HEADER, level = 1)
    private String zgfwc_2024;
    
    @ExcelColumn(name = "平均分", sort = 27, width = 12, parent = YEAR_2024_HEADER, level = 1)
    private String pjf_2024;
    
    @ExcelColumn(name = "平均分位次", sort = 28, width = 15, parent = YEAR_2024_HEADER, level = 1)
    private String pjfwc_2024;

    // ===== 2023年数据 =====
    @ExcelColumn(name = "录取人数", sort = 29, width = 12, parent = YEAR_2023_HEADER, level = 1)
    private String lqrs_2023;
    
    @ExcelColumn(name = "学费", sort = 30, width = 12, parent = YEAR_2023_HEADER, level = 1)
    private String fee_2023;
    
    @ExcelColumn(name = "计划数", sort = 31, width = 12, parent = YEAR_2023_HEADER, level = 1)
    private String jhs_2023;
    
    @ExcelColumn(name = "最低分", sort = 32, width = 12, parent = YEAR_2023_HEADER, level = 1)
    private String zdf_2023;
    
    @ExcelColumn(name = "最低分位次", sort = 33, width = 15, parent = YEAR_2023_HEADER, level = 1)
    private String zdfwc_2023;
    
    @ExcelColumn(name = "最高分", sort = 34, width = 12, parent = YEAR_2023_HEADER, level = 1)
    private String zgf_2023;
    
    @ExcelColumn(name = "最高分位次", sort = 35, width = 15, parent = YEAR_2023_HEADER, level = 1)
    private String zgfwc_2023;
    
    @ExcelColumn(name = "平均分", sort = 36, width = 12, parent = YEAR_2023_HEADER, level = 1)
    private String pjf_2023;
    
    @ExcelColumn(name = "平均分位次", sort = 37, width = 15, parent = YEAR_2023_HEADER, level = 1)
    private String pjfwc_2023;

    // ===== 2022年数据 =====
    @ExcelColumn(name = "录取人数", sort = 38, width = 12, parent = YEAR_2022_HEADER, level = 1)
    private String lqrs_2022;
    
    @ExcelColumn(name = "计划数", sort = 39, width = 12, parent = YEAR_2022_HEADER, level = 1)
    private String jhs_2022;
    
    @ExcelColumn(name = "最低分", sort = 40, width = 12, parent = YEAR_2022_HEADER, level = 1)
    private String zdf_2022;
    
    @ExcelColumn(name = "最低分位次", sort = 41, width = 15, parent = YEAR_2022_HEADER, level = 1)
    private String zdfwc_2022;
    
    @ExcelColumn(name = "最高分", sort = 42, width = 12, parent = YEAR_2022_HEADER, level = 1)
    private String zgf_2022;
    
    @ExcelColumn(name = "最高分位次", sort = 43, width = 15, parent = YEAR_2022_HEADER, level = 1)
    private String zgfwc_2022;
    
    @ExcelColumn(name = "平均分", sort = 44, width = 12, parent = YEAR_2022_HEADER, level = 1)
    private String pjf_2022;
    
    @ExcelColumn(name = "平均分位次", sort = 45, width = 15, parent = YEAR_2022_HEADER, level = 1)
    private String pjfwc_2022;

    // ===== 2021年数据 =====
    @ExcelColumn(name = "平均分", sort = 46, width = 12, parent = YEAR_2021_HEADER, level = 1)
    private String pjf_2021;
    
    @ExcelColumn(name = "平均分位次", sort = 47, width = 15, parent = YEAR_2021_HEADER, level = 1)
    private String pjfwc_2021;

    // ===== 趋势数据 =====
    @ExcelColumn(name = "趋势等位分A", sort = 48, width = 15, parent = TREND_HEADER, level = 1)
    private Integer qsf_a;
    
    @ExcelColumn(name = "趋势等位分B", sort = 49, width = 15, parent = TREND_HEADER, level = 1)
    private Integer qsf_b;
    
    @ExcelColumn(name = "趋势等位分C", sort = 50, width = 15, parent = TREND_HEADER, level = 1)
    private Integer qsf_c;
    
    @ExcelColumn(name = "趋势等位分（预测）", sort = 51, width = 18, parent = TREND_HEADER, level = 1)
    private Integer qsf;

    // ===== 院校详细信息 =====
    @ExcelColumn(name = "组类专业计划数", sort = 52, width = 15, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private String zyzjhs;
    
    @ExcelColumn(name = "新增院校", sort = 53, width = 12, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private String xzyx;
    
    @ExcelColumn(name = "组类录取人数", sort = 54, width = 15, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private String zyzlqrs;
    
    @ExcelColumn(name = "组类最低分", sort = 55, width = 12, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private String zyzzdf;
    
    @ExcelColumn(name = "组类最低分位次", sort = 56, width = 18, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private String zyzzdfwc;
    
    @ExcelColumn(name = "志愿组", sort = 57, width = 12, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private String zyz;
    
    @ExcelColumn(name = "志愿组描述", sort = 58, width = 15, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private String zyz_desc;
    
    @ExcelColumn(name = "专业备注", sort = 59, width = 15, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private String zybz;
    
    @ExcelColumn(name = "批次代码", sort = 60, width = 12, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private String pc_code;
    
    @ExcelColumn(name = "批次描述", sort = 61, width = 15, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private String pc_desc;
    
    @ExcelColumn(name = "科类代码", sort = 62, width = 12, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private String xk_code;
    
    @ExcelColumn(name = "科类代码（原始）", sort = 63, width = 15, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private String xk_code_org;
    
    @ExcelColumn(name = "招收类型", sort = 64, width = 12, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private String zx;
    
    @ExcelColumn(name = "科类描述", sort = 65, width = 15, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private String xk_desc;
    
    @ExcelColumn(name = "录取批次", sort = 66, width = 12, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private String lqpc;
    
    @ExcelColumn(name = "院校性质", sort = 67, width = 12, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private String ind_nature;
    
    @ExcelColumn(name = "院校类型", sort = 68, width = 12, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private String ind_catg;
    
    @ExcelColumn(name = "院校省份", sort = 69, width = 12, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private String yxsf;
    
    @ExcelColumn(name = "院校城市", sort = 70, width = 12, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private String yxcs;
    
    @ExcelColumn(name = "院校标签", sort = 71, width = 15, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private String yx_tags;
    
    @ExcelColumn(name = "所有院校标签", sort = 72, width = 20, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private String yx_tags_all;
    
    @ExcelColumn(name = "企业数量", sort = 73, width = 12, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private Integer cnt_company;
    
    @ExcelColumn(name = "员工数量", sort = 74, width = 12, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private Integer cnt_employ;
    
    @ExcelColumn(name = "毕业率", sort = 75, width = 12, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private Float cnt_grad;
    
    @ExcelColumn(name = "是否合作", sort = 76, width = 12, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private Integer is_hz;
    
    @ExcelColumn(name = "是否首次", sort = 77, width = 12, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private Integer is_first;
    
    @ExcelColumn(name = "组内专业数", sort = 78, width = 12, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private Integer znzys;
    
    @ExcelColumn(name = "组内专业类数", sort = 79, width = 15, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private Integer znzyls;
    
    @ExcelColumn(name = "组内计划数", sort = 80, width = 12, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private Integer znjhs;
    
    @ExcelColumn(name = "异常位次", sort = 81, width = 12, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private Integer ycwc;
    
    @ExcelColumn(name = "2024年招生进展", sort = 82, width = 18, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private String zsjz_2024;

    // ===== 专业详细信息 =====
    @ExcelColumn(name = "院校标签信息", sort = 83, width = 15, parent = MAJOR_DETAIL_HEADER, level = 1)
    private String info_yxtag;
    
    @ExcelColumn(name = "院校省份信息", sort = 84, width = 15, parent = MAJOR_DETAIL_HEADER, level = 1)
    private String info_yxsp;
    
    @ExcelColumn(name = "院校规模和招生信息", sort = 85, width = 20, parent = MAJOR_DETAIL_HEADER, level = 1)
    private String info_yxgmhzzs;
    
    @ExcelColumn(name = "辅助专业信息", sort = 86, width = 15, parent = MAJOR_DETAIL_HEADER, level = 1)
    private String info_zzy;
    
    @ExcelColumn(name = "城市省份信息", sort = 87, width = 15, parent = MAJOR_DETAIL_HEADER, level = 1)
    private String info_cssp;
    
    @ExcelColumn(name = "历史档案信息", sort = 88, width = 15, parent = MAJOR_DETAIL_HEADER, level = 1)
    private String info_lsdw;
    
    @ExcelColumn(name = "类型信息", sort = 89, width = 12, parent = MAJOR_DETAIL_HEADER, level = 1)
    private String info_lx;
    
    @ExcelColumn(name = "公司性质信息", sort = 90, width = 15, parent = MAJOR_DETAIL_HEADER, level = 1)
    private String info_gsxz;
    
    @ExcelColumn(name = "毕业率信息", sort = 91, width = 12, parent = MAJOR_DETAIL_HEADER, level = 1)
    private String info_byl;
    
    @ExcelColumn(name = "院校排名信息", sort = 92, width = 15, parent = MAJOR_DETAIL_HEADER, level = 1)
    private String info_yxpm;
    
    @ExcelColumn(name = "缺失硕士专业信息", sort = 93, width = 18, parent = MAJOR_DETAIL_HEADER, level = 1)
    private String info_qxsszys;
    
    @ExcelColumn(name = "缺失硕士专业", sort = 94, width = 15, parent = MAJOR_DETAIL_HEADER, level = 1)
    private String info_qxsszy;
    
    @ExcelColumn(name = "缺失博士专业信息", sort = 95, width = 18, parent = MAJOR_DETAIL_HEADER, level = 1)
    private String info_qxbszys;
    
    @ExcelColumn(name = "缺失博士专业", sort = 96, width = 15, parent = MAJOR_DETAIL_HEADER, level = 1)
    private String info_qxbszy;
    
    @ExcelColumn(name = "专业人口评价", sort = 97, width = 15, parent = MAJOR_DETAIL_HEADER, level = 1)
    private String infozy_rkpj;
    
    @ExcelColumn(name = "专业人口排名", sort = 98, width = 15, parent = MAJOR_DETAIL_HEADER, level = 1)
    private String infozy_rkpm;
    
    @ExcelColumn(name = "专业学科评估", sort = 99, width = 15, parent = MAJOR_DETAIL_HEADER, level = 1)
    private String infozy_xkpg;
    
    @ExcelColumn(name = "专业省份信息", sort = 100, width = 15, parent = MAJOR_DETAIL_HEADER, level = 1)
    private String infozy_zysp;
    
    @ExcelColumn(name = "专业硕士点", sort = 101, width = 12, parent = MAJOR_DETAIL_HEADER, level = 1)
    private String infozy_ssd;
    
    @ExcelColumn(name = "专业博士点", sort = 102, width = 12, parent = MAJOR_DETAIL_HEADER, level = 1)
    private String infozy_bsd;

    // 构造函数
    public JHBeanOrgForm(Integer id, String sf, String nf, String yxdm, String yxmc) {
        this.id = id;
        this.sf = sf;
        this.nf = nf;
        this.yxdm = yxdm;
        this.yxmc = yxmc;
    }

    // ===== 完整的Getter和Setter方法 =====
    
    // 基本信息字段
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    
    public String getSf() { return sf; }
    public void setSf(String sf) { this.sf = sf; }
    
    public String getNf() { return nf; }
    public void setNf(String nf) { this.nf = nf; }
    
    public String getYxdm() { return yxdm; }
    public void setYxdm(String yxdm) { this.yxdm = yxdm; }
    
    public String getYxmc() { return yxmc; }
    public void setYxmc(String yxmc) { this.yxmc = yxmc; }
    
    public String getYxmc_org() { return yxmc_org; }
    public void setYxmc_org(String yxmc_org) { this.yxmc_org = yxmc_org; }
    
    public String getZydm() { return zydm; }
    public void setZydm(String zydm) { this.zydm = zydm; }
    
    public String getZymc() { return zymc; }
    public void setZymc(String zymc) { this.zymc = zymc; }
    
    public String getZymc_org() { return zymc_org; }
    public void setZymc_org(String zymc_org) { this.zymc_org = zymc_org; }
    
    public String getZyml() { return zyml; }
    public void setZyml(String zyml) { this.zyml = zyml; }
    
    public String getZnzy() { return znzy; }
    public void setZnzy(String znzy) { this.znzy = znzy; }
    
    public String getPc() { return pc; }
    public void setPc(String pc) { this.pc = pc; }
    
    public String getXk() { return xk; }
    public void setXk(String xk) { this.xk = xk; }
    
    public String getJhs() { return jhs; }
    public void setJhs(String jhs) { this.jhs = jhs; }
    
    public String getFee() { return fee; }
    public void setFee(String fee) { this.fee = fee; }
    
    public String getXz() { return xz; }
    public void setXz(String xz) { this.xz = xz; }
    
    public Integer getZdf() { return zdf; }
    public void setZdf(Integer zdf) { this.zdf = zdf; }
    
    public Integer getZdfwc() { return zdfwc; }
    public void setZdfwc(Integer zdfwc) { this.zdfwc = zdfwc; }

    // 2024年数据字段
    public String getLqrs_2024() { return lqrs_2024; }
    public void setLqrs_2024(String lqrs_2024) { this.lqrs_2024 = lqrs_2024; }
    
    public String getFee_2024() { return fee_2024; }
    public void setFee_2024(String fee_2024) { this.fee_2024 = fee_2024; }
    
    public String getXz_2024() { return xz_2024; }
    public void setXz_2024(String xz_2024) { this.xz_2024 = xz_2024; }
    
    public String getJhs_2024() { return jhs_2024; }
    public void setJhs_2024(String jhs_2024) { this.jhs_2024 = jhs_2024; }
    
    public String getZdf_2024() { return zdf_2024; }
    public void setZdf_2024(String zdf_2024) { this.zdf_2024 = zdf_2024; }
    
    public String getZdfwc_2024() { return zdfwc_2024; }
    public void setZdfwc_2024(String zdfwc_2024) { this.zdfwc_2024 = zdfwc_2024; }
    
    public String getZgf_2024() { return zgf_2024; }
    public void setZgf_2024(String zgf_2024) { this.zgf_2024 = zgf_2024; }
    
    public String getZgfwc_2024() { return zgfwc_2024; }
    public void setZgfwc_2024(String zgfwc_2024) { this.zgfwc_2024 = zgfwc_2024; }
    
    public String getPjf_2024() { return pjf_2024; }
    public void setPjf_2024(String pjf_2024) { this.pjf_2024 = pjf_2024; }
    
    public String getPjfwc_2024() { return pjfwc_2024; }
    public void setPjfwc_2024(String pjfwc_2024) { this.pjfwc_2024 = pjfwc_2024; }

    // 2023年数据字段
    public String getLqrs_2023() { return lqrs_2023; }
    public void setLqrs_2023(String lqrs_2023) { this.lqrs_2023 = lqrs_2023; }
    
    public String getFee_2023() { return fee_2023; }
    public void setFee_2023(String fee_2023) { this.fee_2023 = fee_2023; }
    
    public String getJhs_2023() { return jhs_2023; }
    public void setJhs_2023(String jhs_2023) { this.jhs_2023 = jhs_2023; }
    
    public String getZdf_2023() { return zdf_2023; }
    public void setZdf_2023(String zdf_2023) { this.zdf_2023 = zdf_2023; }
    
    public String getZdfwc_2023() { return zdfwc_2023; }
    public void setZdfwc_2023(String zdfwc_2023) { this.zdfwc_2023 = zdfwc_2023; }
    
    public String getZgf_2023() { return zgf_2023; }
    public void setZgf_2023(String zgf_2023) { this.zgf_2023 = zgf_2023; }
    
    public String getZgfwc_2023() { return zgfwc_2023; }
    public void setZgfwc_2023(String zgfwc_2023) { this.zgfwc_2023 = zgfwc_2023; }
    
    public String getPjf_2023() { return pjf_2023; }
    public void setPjf_2023(String pjf_2023) { this.pjf_2023 = pjf_2023; }
    
    public String getPjfwc_2023() { return pjfwc_2023; }
    public void setPjfwc_2023(String pjfwc_2023) { this.pjfwc_2023 = pjfwc_2023; }

    // 2022年数据字段
    public String getLqrs_2022() { return lqrs_2022; }
    public void setLqrs_2022(String lqrs_2022) { this.lqrs_2022 = lqrs_2022; }
    
    public String getJhs_2022() { return jhs_2022; }
    public void setJhs_2022(String jhs_2022) { this.jhs_2022 = jhs_2022; }
    
    public String getZdf_2022() { return zdf_2022; }
    public void setZdf_2022(String zdf_2022) { this.zdf_2022 = zdf_2022; }
    
    public String getZdfwc_2022() { return zdfwc_2022; }
    public void setZdfwc_2022(String zdfwc_2022) { this.zdfwc_2022 = zdfwc_2022; }
    
    public String getZgf_2022() { return zgf_2022; }
    public void setZgf_2022(String zgf_2022) { this.zgf_2022 = zgf_2022; }
    
    public String getZgfwc_2022() { return zgfwc_2022; }
    public void setZgfwc_2022(String zgfwc_2022) { this.zgfwc_2022 = zgfwc_2022; }
    
    public String getPjf_2022() { return pjf_2022; }
    public void setPjf_2022(String pjf_2022) { this.pjf_2022 = pjf_2022; }
    
    public String getPjfwc_2022() { return pjfwc_2022; }
    public void setPjfwc_2022(String pjfwc_2022) { this.pjfwc_2022 = pjfwc_2022; }

    // 2021年数据字段
    public String getPjf_2021() { return pjf_2021; }
    public void setPjf_2021(String pjf_2021) { this.pjf_2021 = pjf_2021; }
    
    public String getPjfwc_2021() { return pjfwc_2021; }
    public void setPjfwc_2021(String pjfwc_2021) { this.pjfwc_2021 = pjfwc_2021; }

    // 趋势数据字段
    public Integer getQsf_a() { return qsf_a; }
    public void setQsf_a(Integer qsf_a) { this.qsf_a = qsf_a; }
    
    public Integer getQsf_b() { return qsf_b; }
    public void setQsf_b(Integer qsf_b) { this.qsf_b = qsf_b; }
    
    public Integer getQsf_c() { return qsf_c; }
    public void setQsf_c(Integer qsf_c) { this.qsf_c = qsf_c; }
    
    public Integer getQsf() { return qsf; }
    public void setQsf(Integer qsf) { this.qsf = qsf; }

    // 院校详细信息字段
    public String getZyzjhs() { return zyzjhs; }
    public void setZyzjhs(String zyzjhs) { this.zyzjhs = zyzjhs; }
    
    public String getXzyx() { return xzyx; }
    public void setXzyx(String xzyx) { this.xzyx = xzyx; }
    
    public String getZyzlqrs() { return zyzlqrs; }
    public void setZyzlqrs(String zyzlqrs) { this.zyzlqrs = zyzlqrs; }
    
    public String getZyzzdf() { return zyzzdf; }
    public void setZyzzdf(String zyzzdf) { this.zyzzdf = zyzzdf; }
    
    public String getZyzzdfwc() { return zyzzdfwc; }
    public void setZyzzdfwc(String zyzzdfwc) { this.zyzzdfwc = zyzzdfwc; }
    
    public String getZyz() { return zyz; }
    public void setZyz(String zyz) { this.zyz = zyz; }
    
    public String getZyz_desc() { return zyz_desc; }
    public void setZyz_desc(String zyz_desc) { this.zyz_desc = zyz_desc; }
    
    public String getZybz() { return zybz; }
    public void setZybz(String zybz) { this.zybz = zybz; }
    
    public String getPc_code() { return pc_code; }
    public void setPc_code(String pc_code) { this.pc_code = pc_code; }
    
    public String getPc_desc() { return pc_desc; }
    public void setPc_desc(String pc_desc) { this.pc_desc = pc_desc; }
    
    public String getXk_code() { return xk_code; }
    public void setXk_code(String xk_code) { this.xk_code = xk_code; }
    
    public String getXk_code_org() { return xk_code_org; }
    public void setXk_code_org(String xk_code_org) { this.xk_code_org = xk_code_org; }
    
    public String getZx() { return zx; }
    public void setZx(String zx) { this.zx = zx; }
    
    public String getXk_desc() { return xk_desc; }
    public void setXk_desc(String xk_desc) { this.xk_desc = xk_desc; }
    
    public String getLqpc() { return lqpc; }
    public void setLqpc(String lqpc) { this.lqpc = lqpc; }
    
    public String getInd_nature() { return ind_nature; }
    public void setInd_nature(String ind_nature) { this.ind_nature = ind_nature; }
    
    public String getInd_catg() { return ind_catg; }
    public void setInd_catg(String ind_catg) { this.ind_catg = ind_catg; }
    
    public String getYxsf() { return yxsf; }
    public void setYxsf(String yxsf) { this.yxsf = yxsf; }
    
    public String getYxcs() { return yxcs; }
    public void setYxcs(String yxcs) { this.yxcs = yxcs; }
    
    public String getYx_tags() { return yx_tags; }
    public void setYx_tags(String yx_tags) { this.yx_tags = yx_tags; }
    
    public String getYx_tags_all() { return yx_tags_all; }
    public void setYx_tags_all(String yx_tags_all) { this.yx_tags_all = yx_tags_all; }
    
    public Integer getCnt_company() { return cnt_company; }
    public void setCnt_company(Integer cnt_company) { this.cnt_company = cnt_company; }
    
    public Integer getCnt_employ() { return cnt_employ; }
    public void setCnt_employ(Integer cnt_employ) { this.cnt_employ = cnt_employ; }
    
    public Float getCnt_grad() { return cnt_grad; }
    public void setCnt_grad(Float cnt_grad) { this.cnt_grad = cnt_grad; }
    
    public Integer getIs_hz() { return is_hz; }
    public void setIs_hz(Integer is_hz) { this.is_hz = is_hz; }
    
    public Integer getIs_first() { return is_first; }
    public void setIs_first(Integer is_first) { this.is_first = is_first; }
    
    public Integer getZnzys() { return znzys; }
    public void setZnzys(Integer znzys) { this.znzys = znzys; }
    
    public Integer getZnzyls() { return znzyls; }
    public void setZnzyls(Integer znzyls) { this.znzyls = znzyls; }
    
    public Integer getZnjhs() { return znjhs; }
    public void setZnjhs(Integer znjhs) { this.znjhs = znjhs; }
    
    public Integer getYcwc() { return ycwc; }
    public void setYcwc(Integer ycwc) { this.ycwc = ycwc; }
    
    public String getZsjz_2024() { return zsjz_2024; }
    public void setZsjz_2024(String zsjz_2024) { this.zsjz_2024 = zsjz_2024; }

    // 专业详细信息字段
    public String getInfo_yxtag() { return info_yxtag; }
    public void setInfo_yxtag(String info_yxtag) { this.info_yxtag = info_yxtag; }
    
    public String getInfo_yxsp() { return info_yxsp; }
    public void setInfo_yxsp(String info_yxsp) { this.info_yxsp = info_yxsp; }
    
    public String getInfo_yxgmhzzs() { return info_yxgmhzzs; }
    public void setInfo_yxgmhzzs(String info_yxgmhzzs) { this.info_yxgmhzzs = info_yxgmhzzs; }
    
    public String getInfo_zzy() { return info_zzy; }
    public void setInfo_zzy(String info_zzy) { this.info_zzy = info_zzy; }
    
    public String getInfo_cssp() { return info_cssp; }
    public void setInfo_cssp(String info_cssp) { this.info_cssp = info_cssp; }
    
    public String getInfo_lsdw() { return info_lsdw; }
    public void setInfo_lsdw(String info_lsdw) { this.info_lsdw = info_lsdw; }
    
    public String getInfo_lx() { return info_lx; }
    public void setInfo_lx(String info_lx) { this.info_lx = info_lx; }
    
    public String getInfo_gsxz() { return info_gsxz; }
    public void setInfo_gsxz(String info_gsxz) { this.info_gsxz = info_gsxz; }
    
    public String getInfo_byl() { return info_byl; }
    public void setInfo_byl(String info_byl) { this.info_byl = info_byl; }
    
    public String getInfo_yxpm() { return info_yxpm; }
    public void setInfo_yxpm(String info_yxpm) { this.info_yxpm = info_yxpm; }
    
    public String getInfo_qxsszys() { return info_qxsszys; }
    public void setInfo_qxsszys(String info_qxsszys) { this.info_qxsszys = info_qxsszys; }
    
    public String getInfo_qxsszy() { return info_qxsszy; }
    public void setInfo_qxsszy(String info_qxsszy) { this.info_qxsszy = info_qxsszy; }
    
    public String getInfo_qxbszys() { return info_qxbszys; }
    public void setInfo_qxbszys(String info_qxbszys) { this.info_qxbszys = info_qxbszys; }
    
    public String getInfo_qxbszy() { return info_qxbszy; }
    public void setInfo_qxbszy(String info_qxbszy) { this.info_qxbszy = info_qxbszy; }
    
    public String getInfozy_rkpj() { return infozy_rkpj; }
    public void setInfozy_rkpj(String infozy_rkpj) { this.infozy_rkpj = infozy_rkpj; }
    
    public String getInfozy_rkpm() { return infozy_rkpm; }
    public void setInfozy_rkpm(String infozy_rkpm) { this.infozy_rkpm = infozy_rkpm; }
    
    public String getInfozy_xkpg() { return infozy_xkpg; }
    public void setInfozy_xkpg(String infozy_xkpg) { this.infozy_xkpg = infozy_xkpg; }
    
    public String getInfozy_zysp() { return infozy_zysp; }
    public void setInfozy_zysp(String infozy_zysp) { this.infozy_zysp = infozy_zysp; }
    
    public String getInfozy_ssd() { return infozy_ssd; }
    public void setInfozy_ssd(String infozy_ssd) { this.infozy_ssd = infozy_ssd; }
    
    public String getInfozy_bsd() { return infozy_bsd; }
    public void setInfozy_bsd(String infozy_bsd) { this.infozy_bsd = infozy_bsd; }
} 