package com.career.utils.excel.model;

import com.career.utils.excel.annotation.ExcelColumn;

import java.io.Serializable;


public class MajorEnrollmentForm implements Serializable {
    
    private static final long serialVersionUID = 1L;
	
	// 常量字符串用于注解 - 一级表头
    private static final String CURRENT_YEAR_HEADER = "2025年专业录取数据";
    private static final String PREVIOUS_YEAR_HEADER = "2024年专业录取数据";
    private static final String BEFORE_LAST_YEAR_HEADER = "2023年专业录取数据";
    private static final String TREND_SCORE_HEADER = "趋势分";
    private static final String EQUIVALENT_DIFF_HEADER = "等效差";


    // 默认构造器
    public MajorEnrollmentForm() {
    }

    // 基本字段: 专业类
    @ExcelColumn(name = "专业类", sort = 1, width = 20, level = 0, rowspan = 2)
    private String majorCategory;
    

    // ===== 2025年专业录取数据 =====
    @ExcelColumn(name = "计划人数", sort = 2, width = 12, parent = CURRENT_YEAR_HEADER, level = 1)
    private Integer planCountCurrent;
    
    @ExcelColumn(name = "录取人数", sort = 3, width = 12, parent = CURRENT_YEAR_HEADER, level = 1)
    private Integer enrollmentCountCurrent;
    
    @ExcelColumn(name = "最低分", sort = 4, width = 12, parent = CURRENT_YEAR_HEADER, level = 1)
    private Integer lowestScoreCurrent;
    
    @ExcelColumn(name = "最低分位次", sort = 5, width = 15, parent = CURRENT_YEAR_HEADER, level = 1)
    private Integer lowestRankCurrent;
    
    @ExcelColumn(name = "平均分", sort = 6, width = 12, parent = CURRENT_YEAR_HEADER, level = 1)
    private Integer avgScoreCurrent;
    
    @ExcelColumn(name = "平均分位次", sort = 7, width = 15, parent = CURRENT_YEAR_HEADER, level = 1)
    private Integer avgRankCurrent;
    
    @ExcelColumn(name = "最高分", sort = 8, width = 12, parent = CURRENT_YEAR_HEADER, level = 1)
    private Integer highestScoreCurrent;
    
    @ExcelColumn(name = "最高分位次", sort = 9, width = 15, parent = CURRENT_YEAR_HEADER, level = 1)
    private Integer highestRankCurrent;

    // ===== 2024年专业录取数据 =====
    @ExcelColumn(name = "计划人数", sort = 10, width = 12, parent = PREVIOUS_YEAR_HEADER, level = 1)
    private Integer planCountPrevious;
    
    @ExcelColumn(name = "录取人数", sort = 11, width = 12, parent = PREVIOUS_YEAR_HEADER, level = 1)
    private Integer enrollmentCountPrevious;
    
    @ExcelColumn(name = "最低分", sort = 12, width = 12, parent = PREVIOUS_YEAR_HEADER, level = 1)
    private Integer lowestScorePrevious;
    
    @ExcelColumn(name = "最低分位次", sort = 13, width = 15, parent = PREVIOUS_YEAR_HEADER, level = 1)
    private Integer lowestRankPrevious;
    
    @ExcelColumn(name = "平均分", sort = 14, width = 12, parent = PREVIOUS_YEAR_HEADER, level = 1)
    private Integer avgScorePrevious;
    
    @ExcelColumn(name = "平均分位次", sort = 15, width = 15, parent = PREVIOUS_YEAR_HEADER, level = 1)
    private Integer avgRankPrevious;
    
    @ExcelColumn(name = "最高分", sort = 16, width = 12, parent = PREVIOUS_YEAR_HEADER, level = 1)
    private Integer highestScorePrevious;
    
    @ExcelColumn(name = "最高分位次", sort = 17, width = 15, parent = PREVIOUS_YEAR_HEADER, level = 1)
    private Integer highestRankPrevious;

    // ===== 2023年专业录取数据 =====
    @ExcelColumn(name = "计划人数", sort = 18, width = 12, parent = BEFORE_LAST_YEAR_HEADER, level = 1)
    private Integer planCountBeforeLast;
    
    @ExcelColumn(name = "录取人数", sort = 19, width = 12, parent = BEFORE_LAST_YEAR_HEADER, level = 1)
    private Integer enrollmentCountBeforeLast;
    
    @ExcelColumn(name = "最低分", sort = 20, width = 12, parent = BEFORE_LAST_YEAR_HEADER, level = 1)
    private Integer lowestScoreBeforeLast;
    
    @ExcelColumn(name = "最低分位次", sort = 21, width = 15, parent = BEFORE_LAST_YEAR_HEADER, level = 1)
    private Integer lowestRankBeforeLast;
    
    @ExcelColumn(name = "平均分", sort = 22, width = 12, parent = BEFORE_LAST_YEAR_HEADER, level = 1)
    private Integer avgScoreBeforeLast;
    
    @ExcelColumn(name = "平均分位次", sort = 23, width = 15, parent = BEFORE_LAST_YEAR_HEADER, level = 1)
    private Integer avgRankBeforeLast;
    
    @ExcelColumn(name = "最高分", sort = 24, width = 12, parent = BEFORE_LAST_YEAR_HEADER, level = 1)
    private Integer highestScoreBeforeLast;
    
    @ExcelColumn(name = "最高分位次", sort = 25, width = 15, parent = BEFORE_LAST_YEAR_HEADER, level = 1)
    private Integer highestRankBeforeLast;

    // ===== 趋势分 =====
    @ExcelColumn(name = "今年", sort = 26, width = 12, parent = TREND_SCORE_HEADER, level = 1)
    private Integer trendScoreCurrent;
    
    @ExcelColumn(name = "去年", sort = 27, width = 15, parent = TREND_SCORE_HEADER, level = 1)
    private Integer trendScorePrevious;
    
    @ExcelColumn(name = "前年", sort = 28, width = 15, parent = TREND_SCORE_HEADER, level = 1)
    private Integer trendScoreBeforeLast;

    // ===== 等效差 =====
    @ExcelColumn(name = "今年", sort = 29, width = 12, parent = EQUIVALENT_DIFF_HEADER, level = 1)
    private Integer equivalentDifferenceCurrent;
    
    @ExcelColumn(name = "去年", sort = 30, width = 15, parent = EQUIVALENT_DIFF_HEADER, level = 1)
    private Integer equivalentDifferencePrevious;
    
    @ExcelColumn(name = "前年", sort = 31, width = 15, parent = EQUIVALENT_DIFF_HEADER, level = 1)
    private Integer equivalentDifferenceBeforeLast;

    // 院校基础信息
    @ExcelColumn(name = "所在省", sort = 32, width = 15, parent = "院校基础信息", level = 1)
    private String province;
    
    @ExcelColumn(name = "城市", sort = 33, width = 15, parent = "院校基础信息", level = 1)
    private String city;
    
    @ExcelColumn(name = "院校标签", sort = 34, width = 15, parent = "院校基础信息", level = 1)
    private String universityTag;
    
    @ExcelColumn(name = "院校水平标签", sort = 35, width = 20, parent = "院校基础信息", level = 1)
    private String universityLevelTag;
    
    @ExcelColumn(name = "更名合并转设", sort = 36, width = 20, parent = "院校基础信息", level = 1)
    private String renameInfo;
    
    @ExcelColumn(name = "转专业情况", sort = 37, width = 20, parent = "院校基础信息", level = 1)
    private String majorTransferInfo;
    
    @ExcelColumn(name = "所在省", sort = 38, width = 15, parent = "院校基础信息", level = 1)
    private String universityProvince;
    
    @ExcelColumn(name = "城市水平标签", sort = 39, width = 20, parent = "院校基础信息", level = 1)
    private String cityLevelTag;
    
    @ExcelColumn(name = "本科/专科", sort = 40, width = 15, parent = "院校基础信息", level = 1)
    private String educationLevel;
    
    @ExcelColumn(name = "隶属单位", sort = 41, width = 20, parent = "院校基础信息", level = 1)
    private String affiliatedUnit;
    
    @ExcelColumn(name = "类型", sort = 42, width = 15, parent = "院校基础信息", level = 1)
    private String universityType;
    
    @ExcelColumn(name = "公私性质", sort = 43, width = 15, parent = "院校基础信息", level = 1)
    private String publicPrivateNature;
    
    @ExcelColumn(name = "保研率", sort = 44, width = 15, parent = "院校基础信息", level = 1)
    private Integer postgraduateRate;
    
    @ExcelColumn(name = "院校排名", sort = 45, width = 15, parent = "院校基础信息", level = 1)
    private String universityRanking;
    
    @ExcelColumn(name = "全校硕士专业数", sort = 46, width = 20, parent = "院校基础信息", level = 1)
    private Integer masterProgramCount;
    
    @ExcelColumn(name = "全校硕士士专业", sort = 47, width = 20, parent = "院校基础信息", level = 1)
    private String masterPrograms;
    
    @ExcelColumn(name = "全校博士专业数", sort = 48, width = 20, parent = "院校基础信息", level = 1)
    private Integer phdProgramCount;
    
    @ExcelColumn(name = "全校博士士专业", sort = 49, width = 20, parent = "院校基础信息", level = 1)
    private String phdPrograms;
    
    @ExcelColumn(name = "2024招生章程", sort = 50, width = 20, parent = "院校基础信息", level = 1)
    private String admissionRules2024;

    // 专业基础信息
    @ExcelColumn(name = "软科评级", sort = 51, width = 15, parent = "专业基础信息", level = 1)
    private String ruankeRating;
    
    @ExcelColumn(name = "软科排名", sort = 52, width = 15, parent = "专业基础信息", level = 1)
    private String ruankeRanking;
    
    @ExcelColumn(name = "学科评估", sort = 53, width = 15, parent = "专业基础信息", level = 1)
    private String disciplineEvaluation;
    
    @ExcelColumn(name = "专业水平", sort = 54, width = 15, parent = "专业基础信息", level = 1)
    private String majorLevel;
    
    @ExcelColumn(name = "本专业硕士点", sort = 55, width = 20, parent = "专业基础信息", level = 1)
    private String hasMasterProgram;
    
    @ExcelColumn(name = "本专业博士点", sort = 56, width = 20, parent = "专业基础信息", level = 1)
    private String hasPhDProgram;

    // 全参数构造器
    public MajorEnrollmentForm(String majorCategory,
                              // 第1年数据 (当年)
                              Integer planCount1, Integer enrollmentCount1,
                              Integer lowestScore1, Integer lowestRank1,
                              Integer avgScore1, Integer avgRank1,
                              Integer highestScore1, Integer highestRank1,
                              Integer trendScore1, Integer equivalentDifference1,
                              
                              // 第2年数据 (去年)
                              Integer planCount2, Integer enrollmentCount2,
                              Integer lowestScore2, Integer lowestRank2,
                              Integer avgScore2, Integer avgRank2,
                              Integer highestScore2, Integer highestRank2,
                              Integer trendScore2, Integer equivalentDifference2,
                              
                              // 第3年数据 (前年)
                              Integer planCount3, Integer enrollmentCount3,
                              Integer lowestScore3, Integer lowestRank3,
                              Integer avgScore3, Integer avgRank3,
                              Integer highestScore3, Integer highestRank3,
                              Integer trendScore3, Integer equivalentDifference3,
                              
                              // 院校基础信息
                              String province, String city, String universityTag, String universityLevelTag, String renameInfo, 
                              String majorTransferInfo, String universityProvince, String cityLevelTag, String educationLevel, 
                              String affiliatedUnit, String universityType, String publicPrivateNature, Integer postgraduateRate, 
                              String universityRanking, Integer masterProgramCount, String masterPrograms, Integer phdProgramCount, 
                              String phdPrograms, String admissionRules2024, String ruankeRating, String ruankeRanking, 
                              String disciplineEvaluation, String majorLevel, String hasMasterProgram, String hasPhDProgram) {
        // 设置专业类别
        this.majorCategory = majorCategory;
        
        // 设置当年数据 (2025年)
        this.planCountCurrent = planCount1;
        this.enrollmentCountCurrent = enrollmentCount1;
        this.lowestScoreCurrent = lowestScore1;
        this.lowestRankCurrent = lowestRank1;
        this.avgScoreCurrent = avgScore1;
        this.avgRankCurrent = avgRank1;
        this.highestScoreCurrent = highestScore1;
        this.highestRankCurrent = highestRank1;
        this.trendScoreCurrent = trendScore1;
        this.equivalentDifferenceCurrent = equivalentDifference1;
        
        // 设置去年数据 (2024年)
        this.planCountPrevious = planCount2;
        this.enrollmentCountPrevious = enrollmentCount2;
        this.lowestScorePrevious = lowestScore2;
        this.lowestRankPrevious = lowestRank2;
        this.avgScorePrevious = avgScore2;
        this.avgRankPrevious = avgRank2;
        this.highestScorePrevious = highestScore2;
        this.highestRankPrevious = highestRank2;
        this.trendScorePrevious = trendScore2;
        this.equivalentDifferencePrevious = equivalentDifference2;
        
        // 设置前年数据 (2023年)
        this.planCountBeforeLast = planCount3;
        this.enrollmentCountBeforeLast = enrollmentCount3;
        this.lowestScoreBeforeLast = lowestScore3;
        this.lowestRankBeforeLast = lowestRank3;
        this.avgScoreBeforeLast = avgScore3;
        this.avgRankBeforeLast = avgRank3;
        this.highestScoreBeforeLast = highestScore3;
        this.highestRankBeforeLast = highestRank3;
        this.trendScoreBeforeLast = trendScore3;
        this.equivalentDifferenceBeforeLast = equivalentDifference3;
        
        // 设置院校基础信息
        this.province = province;
        this.city = city;
        this.universityTag = universityTag;
        this.universityLevelTag = universityLevelTag;
        this.renameInfo = renameInfo;
        this.majorTransferInfo = majorTransferInfo;
        this.universityProvince = universityProvince;
        this.cityLevelTag = cityLevelTag;
        this.educationLevel = educationLevel;
        this.affiliatedUnit = affiliatedUnit;
        this.universityType = universityType;
        this.publicPrivateNature = publicPrivateNature;
        this.postgraduateRate = postgraduateRate;
        this.universityRanking = universityRanking;
        this.masterProgramCount = masterProgramCount;
        this.masterPrograms = masterPrograms;
        this.phdProgramCount = phdProgramCount;
        this.phdPrograms = phdPrograms;
        this.admissionRules2024 = admissionRules2024;
        
        // 设置专业基础信息
        this.ruankeRating = ruankeRating;
        this.ruankeRanking = ruankeRanking;
        this.disciplineEvaluation = disciplineEvaluation;
        this.majorLevel = majorLevel;
        this.hasMasterProgram = hasMasterProgram;
        this.hasPhDProgram = hasPhDProgram;
    }

	public String getMajorCategory() {
		return majorCategory;
	}

	public void setMajorCategory(String majorCategory) {
		this.majorCategory = majorCategory;
	}

	public Integer getPlanCountCurrent() {
		return planCountCurrent;
	}

	public void setPlanCountCurrent(Integer planCountCurrent) {
		this.planCountCurrent = planCountCurrent;
	}

	public Integer getEnrollmentCountCurrent() {
		return enrollmentCountCurrent;
	}

	public void setEnrollmentCountCurrent(Integer enrollmentCountCurrent) {
		this.enrollmentCountCurrent = enrollmentCountCurrent;
	}

	public Integer getLowestScoreCurrent() {
		return lowestScoreCurrent;
	}

	public void setLowestScoreCurrent(Integer lowestScoreCurrent) {
		this.lowestScoreCurrent = lowestScoreCurrent;
	}

	public Integer getLowestRankCurrent() {
		return lowestRankCurrent;
	}

	public void setLowestRankCurrent(Integer lowestRankCurrent) {
		this.lowestRankCurrent = lowestRankCurrent;
	}

	public Integer getAvgScoreCurrent() {
		return avgScoreCurrent;
	}

	public void setAvgScoreCurrent(Integer avgScoreCurrent) {
		this.avgScoreCurrent = avgScoreCurrent;
	}

	public Integer getAvgRankCurrent() {
		return avgRankCurrent;
	}

	public void setAvgRankCurrent(Integer avgRankCurrent) {
		this.avgRankCurrent = avgRankCurrent;
	}

	public Integer getHighestScoreCurrent() {
		return highestScoreCurrent;
	}

	public void setHighestScoreCurrent(Integer highestScoreCurrent) {
		this.highestScoreCurrent = highestScoreCurrent;
	}

	public Integer getHighestRankCurrent() {
		return highestRankCurrent;
	}

	public void setHighestRankCurrent(Integer highestRankCurrent) {
		this.highestRankCurrent = highestRankCurrent;
	}

	public Integer getPlanCountPrevious() {
		return planCountPrevious;
	}

	public void setPlanCountPrevious(Integer planCountPrevious) {
		this.planCountPrevious = planCountPrevious;
	}

	public Integer getEnrollmentCountPrevious() {
		return enrollmentCountPrevious;
	}

	public void setEnrollmentCountPrevious(Integer enrollmentCountPrevious) {
		this.enrollmentCountPrevious = enrollmentCountPrevious;
	}

	public Integer getLowestScorePrevious() {
		return lowestScorePrevious;
	}

	public void setLowestScorePrevious(Integer lowestScorePrevious) {
		this.lowestScorePrevious = lowestScorePrevious;
	}

	public Integer getLowestRankPrevious() {
		return lowestRankPrevious;
	}

	public void setLowestRankPrevious(Integer lowestRankPrevious) {
		this.lowestRankPrevious = lowestRankPrevious;
	}

	public Integer getAvgScorePrevious() {
		return avgScorePrevious;
	}

	public void setAvgScorePrevious(Integer avgScorePrevious) {
		this.avgScorePrevious = avgScorePrevious;
	}

	public Integer getAvgRankPrevious() {
		return avgRankPrevious;
	}

	public void setAvgRankPrevious(Integer avgRankPrevious) {
		this.avgRankPrevious = avgRankPrevious;
	}

	public Integer getHighestScorePrevious() {
		return highestScorePrevious;
	}

	public void setHighestScorePrevious(Integer highestScorePrevious) {
		this.highestScorePrevious = highestScorePrevious;
	}

	public Integer getHighestRankPrevious() {
		return highestRankPrevious;
	}

	public void setHighestRankPrevious(Integer highestRankPrevious) {
		this.highestRankPrevious = highestRankPrevious;
	}

	public Integer getPlanCountBeforeLast() {
		return planCountBeforeLast;
	}

	public void setPlanCountBeforeLast(Integer planCountBeforeLast) {
		this.planCountBeforeLast = planCountBeforeLast;
	}

	public Integer getEnrollmentCountBeforeLast() {
		return enrollmentCountBeforeLast;
	}

	public void setEnrollmentCountBeforeLast(Integer enrollmentCountBeforeLast) {
		this.enrollmentCountBeforeLast = enrollmentCountBeforeLast;
	}

	public Integer getLowestScoreBeforeLast() {
		return lowestScoreBeforeLast;
	}

	public void setLowestScoreBeforeLast(Integer lowestScoreBeforeLast) {
		this.lowestScoreBeforeLast = lowestScoreBeforeLast;
	}

	public Integer getLowestRankBeforeLast() {
		return lowestRankBeforeLast;
	}

	public void setLowestRankBeforeLast(Integer lowestRankBeforeLast) {
		this.lowestRankBeforeLast = lowestRankBeforeLast;
	}

	public Integer getAvgScoreBeforeLast() {
		return avgScoreBeforeLast;
	}

	public void setAvgScoreBeforeLast(Integer avgScoreBeforeLast) {
		this.avgScoreBeforeLast = avgScoreBeforeLast;
	}

	public Integer getAvgRankBeforeLast() {
		return avgRankBeforeLast;
	}

	public void setAvgRankBeforeLast(Integer avgRankBeforeLast) {
		this.avgRankBeforeLast = avgRankBeforeLast;
	}

	public Integer getHighestScoreBeforeLast() {
		return highestScoreBeforeLast;
	}

	public void setHighestScoreBeforeLast(Integer highestScoreBeforeLast) {
		this.highestScoreBeforeLast = highestScoreBeforeLast;
	}

	public Integer getHighestRankBeforeLast() {
		return highestRankBeforeLast;
	}

	public void setHighestRankBeforeLast(Integer highestRankBeforeLast) {
		this.highestRankBeforeLast = highestRankBeforeLast;
	}

	public Integer getTrendScoreCurrent() {
		return trendScoreCurrent;
	}

	public void setTrendScoreCurrent(Integer trendScoreCurrent) {
		this.trendScoreCurrent = trendScoreCurrent;
	}

	public Integer getTrendScorePrevious() {
		return trendScorePrevious;
	}

	public void setTrendScorePrevious(Integer trendScorePrevious) {
		this.trendScorePrevious = trendScorePrevious;
	}

	public Integer getTrendScoreBeforeLast() {
		return trendScoreBeforeLast;
	}

	public void setTrendScoreBeforeLast(Integer trendScoreBeforeLast) {
		this.trendScoreBeforeLast = trendScoreBeforeLast;
	}

	public Integer getEquivalentDifferenceCurrent() {
		return equivalentDifferenceCurrent;
	}

	public void setEquivalentDifferenceCurrent(Integer equivalentDifferenceCurrent) {
		this.equivalentDifferenceCurrent = equivalentDifferenceCurrent;
	}

	public Integer getEquivalentDifferencePrevious() {
		return equivalentDifferencePrevious;
	}

	public void setEquivalentDifferencePrevious(Integer equivalentDifferencePrevious) {
		this.equivalentDifferencePrevious = equivalentDifferencePrevious;
	}

	public Integer getEquivalentDifferenceBeforeLast() {
		return equivalentDifferenceBeforeLast;
	}

	public void setEquivalentDifferenceBeforeLast(Integer equivalentDifferenceBeforeLast) {
		this.equivalentDifferenceBeforeLast = equivalentDifferenceBeforeLast;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getUniversityTag() {
		return universityTag;
	}

	public void setUniversityTag(String universityTag) {
		this.universityTag = universityTag;
	}

	public String getUniversityLevelTag() {
		return universityLevelTag;
	}

	public void setUniversityLevelTag(String universityLevelTag) {
		this.universityLevelTag = universityLevelTag;
	}

	public String getRenameInfo() {
		return renameInfo;
	}

	public void setRenameInfo(String renameInfo) {
		this.renameInfo = renameInfo;
	}

	public String getMajorTransferInfo() {
		return majorTransferInfo;
	}

	public void setMajorTransferInfo(String majorTransferInfo) {
		this.majorTransferInfo = majorTransferInfo;
	}

	public String getUniversityProvince() {
		return universityProvince;
	}

	public void setUniversityProvince(String universityProvince) {
		this.universityProvince = universityProvince;
	}

	public String getCityLevelTag() {
		return cityLevelTag;
	}

	public void setCityLevelTag(String cityLevelTag) {
		this.cityLevelTag = cityLevelTag;
	}

	public String getEducationLevel() {
		return educationLevel;
	}

	public void setEducationLevel(String educationLevel) {
		this.educationLevel = educationLevel;
	}

	public String getAffiliatedUnit() {
		return affiliatedUnit;
	}

	public void setAffiliatedUnit(String affiliatedUnit) {
		this.affiliatedUnit = affiliatedUnit;
	}

	public String getUniversityType() {
		return universityType;
	}

	public void setUniversityType(String universityType) {
		this.universityType = universityType;
	}

	public String getPublicPrivateNature() {
		return publicPrivateNature;
	}

	public void setPublicPrivateNature(String publicPrivateNature) {
		this.publicPrivateNature = publicPrivateNature;
	}

	public Integer getPostgraduateRate() {
		return postgraduateRate;
	}

	public void setPostgraduateRate(Integer postgraduateRate) {
		this.postgraduateRate = postgraduateRate;
	}

	public String getUniversityRanking() {
		return universityRanking;
	}

	public void setUniversityRanking(String universityRanking) {
		this.universityRanking = universityRanking;
	}

	public Integer getMasterProgramCount() {
		return masterProgramCount;
	}

	public void setMasterProgramCount(Integer masterProgramCount) {
		this.masterProgramCount = masterProgramCount;
	}

	public String getMasterPrograms() {
		return masterPrograms;
	}

	public void setMasterPrograms(String masterPrograms) {
		this.masterPrograms = masterPrograms;
	}

	public Integer getPhdProgramCount() {
		return phdProgramCount;
	}

	public void setPhdProgramCount(Integer phdProgramCount) {
		this.phdProgramCount = phdProgramCount;
	}

	public String getPhdPrograms() {
		return phdPrograms;
	}

	public void setPhdPrograms(String phdPrograms) {
		this.phdPrograms = phdPrograms;
	}

	public String getAdmissionRules2024() {
		return admissionRules2024;
	}

	public void setAdmissionRules2024(String admissionRules2024) {
		this.admissionRules2024 = admissionRules2024;
	}

	public String getRuankeRating() {
		return ruankeRating;
	}

	public void setRuankeRating(String ruankeRating) {
		this.ruankeRating = ruankeRating;
	}

	public String getRuankeRanking() {
		return ruankeRanking;
	}

	public void setRuankeRanking(String ruankeRanking) {
		this.ruankeRanking = ruankeRanking;
	}

	public String getDisciplineEvaluation() {
		return disciplineEvaluation;
	}

	public void setDisciplineEvaluation(String disciplineEvaluation) {
		this.disciplineEvaluation = disciplineEvaluation;
	}

	public String getMajorLevel() {
		return majorLevel;
	}

	public void setMajorLevel(String majorLevel) {
		this.majorLevel = majorLevel;
	}

	public String getHasMasterProgram() {
		return hasMasterProgram;
	}

	public void setHasMasterProgram(String hasMasterProgram) {
		this.hasMasterProgram = hasMasterProgram;
	}

	public String getHasPhDProgram() {
		return hasPhDProgram;
	}

	public void setHasPhDProgram(String hasPhDProgram) {
		this.hasPhDProgram = hasPhDProgram;
	}

}