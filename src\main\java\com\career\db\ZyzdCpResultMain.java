package com.career.db;

import java.util.Date;

import com.career.utils.BaseBean;

public class ZyzdCpResultMain extends BaseBean{
	
	public static void main(String args[]) {
		printBeanProperties(new ZyzdCpResultMain());
	}

	private String rm_id;
	private String m_id;
	private String m_name;
	private String descp;
	private String c_id;
	private float score_total;
	private Date create_tm;
	private int m_type;
	
	private String stu_xm;
	private String stu_yxmc;
	private String stu_nj;
	
	private String stu_xk;

	private int stu_cj_yw;
	private int stu_cj_sx;
	private int stu_cj_wy;
	private int stu_cj_xk_a;
	private int stu_cj_xk_b;
	private int stu_cj_xk_c;
	
	
	public String getStu_xk() {
		return stu_xk;
	}
	public void setStu_xk(String stu_xk) {
		this.stu_xk = stu_xk;
	}
	
	public int getM_type() {
		return m_type;
	}
	public String getStu_xm() {
		return stu_xm;
	}
	public void setStu_xm(String stu_xm) {
		this.stu_xm = stu_xm;
	}
	public String getStu_yxmc() {
		return stu_yxmc;
	}
	public void setStu_yxmc(String stu_yxmc) {
		this.stu_yxmc = stu_yxmc;
	}
	public String getStu_nj() {
		return stu_nj;
	}
	public void setStu_nj(String stu_nj) {
		this.stu_nj = stu_nj;
	}
	public int getStu_cj_yw() {
		return stu_cj_yw;
	}
	public void setStu_cj_yw(int stu_cj_yw) {
		this.stu_cj_yw = stu_cj_yw;
	}
	public int getStu_cj_sx() {
		return stu_cj_sx;
	}
	public void setStu_cj_sx(int stu_cj_sx) {
		this.stu_cj_sx = stu_cj_sx;
	}
	public int getStu_cj_wy() {
		return stu_cj_wy;
	}
	public void setStu_cj_wy(int stu_cj_wy) {
		this.stu_cj_wy = stu_cj_wy;
	}
	public int getStu_cj_xk_a() {
		return stu_cj_xk_a;
	}
	public void setStu_cj_xk_a(int stu_cj_xk_a) {
		this.stu_cj_xk_a = stu_cj_xk_a;
	}
	public int getStu_cj_xk_b() {
		return stu_cj_xk_b;
	}
	public void setStu_cj_xk_b(int stu_cj_xk_b) {
		this.stu_cj_xk_b = stu_cj_xk_b;
	}
	public int getStu_cj_xk_c() {
		return stu_cj_xk_c;
	}
	public void setStu_cj_xk_c(int stu_cj_xk_c) {
		this.stu_cj_xk_c = stu_cj_xk_c;
	}
	public void setM_type(int m_type) {
		this.m_type = m_type;
	}
	public String getM_name() {
		return m_name;
	}
	public void setM_name(String m_name) {
		this.m_name = m_name;
	}

	public String getDescp() {
		return descp;
	}
	public void setDescp(String descp) {
		this.descp = descp;
	}
	public String getRm_id() {
		return rm_id;
	}
	public void setRm_id(String rm_id) {
		this.rm_id = rm_id;
	}
	public String getC_id() {
		return c_id;
	}
	public void setC_id(String c_id) {
		this.c_id = c_id;
	}

	public String getM_id() {
		return m_id;
	}
	public void setM_id(String m_id) {
		this.m_id = m_id;
	}
	public float getScore_total() {
		return score_total;
	}
	public void setScore_total(float score_total) {
		this.score_total = score_total;
	}
	public Date getCreate_tm() {
		return create_tm;
	}
	public void setCreate_tm(Date create_tm) {
		this.create_tm = create_tm;
	}
	
	
}
