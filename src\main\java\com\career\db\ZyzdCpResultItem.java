package com.career.db;

import com.career.utils.BaseBean;

public class ZyzdCpResultItem extends BaseBean{
	
	public static void main(String args[]) {
		printBeanProperties(new ZyzdCpResultItem());
	}
	private int id;
	private int item_no;
	private String m_id;
	private String rm_id;
	private String item_name;
	private String item_selected;
	private float item_result;
	private int group_no;
	private String group_name;
	private int group_sub_no;
	private String group_sub_name;
	private float ext_avg;
	private float ext_avg_final;
	
	public int getGroup_sub_no() {
		return group_sub_no;
	}
	public void setGroup_sub_no(int group_sub_no) {
		this.group_sub_no = group_sub_no;
	}
	public String getGroup_sub_name() {
		return group_sub_name;
	}
	public void setGroup_sub_name(String group_sub_name) {
		this.group_sub_name = group_sub_name;
	}
	public float getExt_avg_final() {
		return ext_avg_final;
	}
	public void setExt_avg_final(float ext_avg_final) {
		this.ext_avg_final = ext_avg_final;
	}
	public float getExt_avg() {
		return ext_avg;
	}
	public void setExt_avg(float ext_avg) {
		this.ext_avg = ext_avg;
	}
	public int getGroup_no() {
		return group_no;
	}
	public void setGroup_no(int group_no) {
		this.group_no = group_no;
	}
	public String getGroup_name() {
		return group_name;
	}
	public void setGroup_name(String group_name) {
		this.group_name = group_name;
	}
	public String getItem_name() {
		return item_name;
	}
	public void setItem_name(String item_name) {
		this.item_name = item_name;
	}
	public String getRm_id() {
		return rm_id;
	}
	public void setRm_id(String rm_id) {
		this.rm_id = rm_id;
	}
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public int getItem_no() {
		return item_no;
	}
	public void setItem_no(int item_no) {
		this.item_no = item_no;
	}
	public String getM_id() {
		return m_id;
	}
	public void setM_id(String m_id) {
		this.m_id = m_id;
	}
	public String getItem_selected() {
		return item_selected;
	}
	public void setItem_selected(String item_selected) {
		this.item_selected = item_selected;
	}
	public float getItem_result() {
		return item_result;
	}
	public void setItem_result(float item_result) {
		this.item_result = item_result;
	}
	
	
}
