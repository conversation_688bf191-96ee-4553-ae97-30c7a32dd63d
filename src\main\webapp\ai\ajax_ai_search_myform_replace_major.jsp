<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page import="com.career.utils.*,com.career.db.*,java.util.*" %>

<%@include file="/WEB-INF/include/_session_ajax_ai.jsp"%>
<%
AiCard aiCard = (AiCard)session.getAttribute(ZyzdCache.SES_KEY_AI_BASE_CARD); 
AiJDBC aiJDBC = new AiJDBC();
ZyzdJDBC zyzdJDBC = new ZyzdJDBC();

ZyzdProvinceConfig provinceConfig = ZyzdCache.getProvinceConfig(aiCard.getC_prov());
String provinceTableName = provinceConfig.getP_table_code();
String provinceName = provinceConfig.getP_name();

int LATEST_JH_YEAR = provinceConfig.getLatest_year_jh(); 

int jh_version = provinceConfig.getJh_version();


AiTbFormMain aiTbFormMain = (AiTbFormMain)session.getAttribute("temp_for_replace_AiTbFormMain");
AiTbForm aiTbForm = (AiTbForm)session.getAttribute("temp_for_replace_AiTbForm");


String selected_zymc_ids = Tools.trim(request.getParameter("selectedZymcValues")); //计划的ID
HashSet<String> idsSets = Tools.getSetByStrSplit(selected_zymc_ids, ",");

List<JHBean> selectedList = aiJDBC.getJHByIds(LATEST_JH_YEAR, provinceTableName, idsSets);
if(selectedList.size() == 0){
	out.print("ERR:NOT_FOUND"); 
	return;
}


if(provinceConfig.getForm_type() == 2){
	aiJDBC.deleteMakerFormWithId(aiTbForm.getId(), provinceTableName);
}else{
	aiJDBC.deleteMakerForm(aiTbFormMain.getBatch_id(), aiTbForm.getSeq_no_yx(), provinceTableName);
}

List<AiTbForm> batch_insert_sql_bean = new ArrayList<>();
for(int i=0;i<selectedList.size();i++){
	JHBean bean = selectedList.get(i);
	AiTbForm newUnivForm = new AiTbForm();
	newUnivForm.setBatch_id(aiTbFormMain.getBatch_id());
	newUnivForm.setBatch_id_org(aiTbFormMain.getBatch_id());
	newUnivForm.setSeq_no_yx(aiTbForm.getSeq_no_yx());
	newUnivForm.setYxdm(bean.getYxdm());
	newUnivForm.setYxmc(bean.getYxmc());
	newUnivForm.setYxbz(null);
	newUnivForm.setZyz(bean.getZyz());
	newUnivForm.setSeq_no_zy(i+1);
	newUnivForm.setZybz(bean.getZybz());
	newUnivForm.setZymc(bean.getZymc());
	newUnivForm.setZydm(bean.getZydm());
	
	newUnivForm.setZymc_org(bean.getZymc_org());
	newUnivForm.setYxmc_org(bean.getYxmc_org());
	
	newUnivForm.setZdf_a(Tools.getInt(bean.getZdf_a()));
	newUnivForm.setZdfwc_a(Tools.getInt(bean.getZdfwc_a()));
	newUnivForm.setPjf_a(Tools.getInt(bean.getPjf_a()));
	newUnivForm.setPjfwc_a(Tools.getInt(bean.getPjfwc_a()));
	
	newUnivForm.setZdf_b(Tools.getInt(bean.getZdf_b()));
	newUnivForm.setZdfwc_b(Tools.getInt(bean.getZdfwc_b()));
	newUnivForm.setPjf_b(Tools.getInt(bean.getPjf_b()));
	newUnivForm.setPjfwc_b(Tools.getInt(bean.getPjfwc_b()));
	
	newUnivForm.setZdf_c(Tools.getInt(bean.getZdf_c()));
	newUnivForm.setZdfwc_c(Tools.getInt(bean.getZdfwc_c()));
	newUnivForm.setPjf_c(Tools.getInt(bean.getPjf_c()));
	newUnivForm.setPjfwc_c(Tools.getInt(bean.getPjfwc_c()));
	
	newUnivForm.setJhs_a(Tools.getIntWithDefaultZero(bean.getJhs_a()));
	newUnivForm.setJhs_b(Tools.getIntWithDefaultZero(bean.getJhs_b()));
	newUnivForm.setJhs_c(Tools.getIntWithDefaultZero(bean.getJhs_c()));

	newUnivForm.setZgf_a(Tools.getIntWithDefaultZero(bean.getZgf_a()));
	newUnivForm.setZgfwc_a(Tools.getIntWithDefaultZero(bean.getZgfwc_a()));
	newUnivForm.setZgf_b(Tools.getIntWithDefaultZero(bean.getZgf_b()));
	newUnivForm.setZgfwc_b(Tools.getIntWithDefaultZero(bean.getZgfwc_b()));
	newUnivForm.setZgf_c(Tools.getIntWithDefaultZero(bean.getZgf_c()));
	newUnivForm.setZgfwc_c(Tools.getIntWithDefaultZero(bean.getZgfwc_c()));
	newUnivForm.setJhs(Tools.getIntWithDefaultZero(bean.getJhs()));
	newUnivForm.setFee(bean.getFee());
	
	batch_insert_sql_bean.add(newUnivForm);
}

aiJDBC.insertMakerFormByBatch(provinceTableName, batch_insert_sql_bean);


%>



		