// Decompiled by Jad v1.5.8g. Copyright 2001 <PERSON>.
// Jad home page: http://www.kpdus.com/jad.html
// Decompiler options: packimports(3) 
// Source File Name:   MarjorTsBean.java

package com.career.db;

import java.io.*;

public class MarjorTsBean
{

    public MarjorTsBean()
    {
        yxmc = null;
        zymc = null;
        xz = null;
        cc = null;
        ts = null;
        zydl = null;
    }

    public String getYxmc()
    {
        return yxmc;
    }

    public void setYxmc(String yxmc)
    {
        this.yxmc = yxmc;
    }

    public String getZymc()
    {
        return zymc;
    }

    public void setZymc(String zymc)
    {
        this.zymc = zymc;
    }

    public String getXz()
    {
        return xz;
    }

    public void setXz(String xz)
    {
        this.xz = xz;
    }

    public String getCc()
    {
        return cc;
    }

    public void setCc(String cc)
    {
        this.cc = cc;
    }

    public String getTs()
    {
        return ts;
    }

    public void setTs(String ts)
    {
        this.ts = ts;
    }

    public String getZydl()
    {
        return zydl;
    }

    public void setZydl(String zydl)
    {
        this.zydl = zydl;
    }

    public static void main(String args[])
    {
        String xx = "\u5317\u65B9\u6C11\u65CF\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u5408\u80A5\u5DE5\u4E1A\u5927\u5B66\uFF09\u3001\u9752\u5C9B\u519C\u4E1A\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E2D\u56FD\u6D77\u6D0B\u5927\u5B66\uFF09\u3001\u5B81\u590F\u7406\u5DE5\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E1C\u5317\u5927\u5B66\u3001\u6D59\u6C5F\u5DE5\u4E1A\u5927\u5B66\u3001\u9655\u897F\u5E08\u8303\u5927\u5B66\u7B49\uFF09\u3001\u94F6\u5DDD\u80FD\u6E90\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u798F\u5DDE\u5927\u5B66\u7B49\uFF09\u3001\u5E7F\u897F\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u534E\u5357\u7406\u5DE5\u5927\u5B66\uFF09\u3001\u4E91\u5357\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u590D\u65E6\u5927\u5B66\uFF09\u3001\u5C71\u4E1C\u5DE5\u827A\u7F8E\u672F\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u5C71\u4E1C\u5927\u5B66\uFF09\u3001\u5409\u9996\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E2D\u5C71\u5927\u5B66\uFF09\u3001\u6CB3\u897F\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u590D\u65E6\u5927\u5B66\uFF09\u3001\u65B0\u7586\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u897F\u5B89\u4EA4\u901A\u5927\u5B66\uFF09\u3001\u6CC9\u5DDE\u5E08\u8303\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u53F0\u6E7E\u8F85\u4EC1\u5927\u5B66\u3001\u53F0\u6E7E\u9F99\u534E\u79D1\u6280\u5927\u5B66\u7B49\uFF09\u3001\u4E95\u5188\u5C71\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u540C\u6D4E\u5927\u5B66\u3001\u53A6\u95E8\u5927\u5B66\u7B49\uFF09\u3001\u4E2D\u5C71\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u9999\u6E2F\u5927\u5B66\uFF09\u3001\u897F\u85CF\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u897F\u5357\u4EA4\u901A\u5927\u5B66\uFF09\u3001\u91CD\u5E86\u4E09\u5CE1\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E1C\u5357\u5927\u5B66\uFF09\u3001\u8D35\u5DDE\u5E08\u8303\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u53A6\u95E8\u5927\u5B66\uFF09\u3001\u897F\u5317\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u897F\u5317\u5DE5\u4E1A\u5927\u5B66-\u897F\u5B89\u7535\u5B50\u79D1\u6280\u5927\u5B66\uFF09\u3001\u897F\u5B89\u7535\u5B50\u79D1\u6280\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u897F\u5317\u5DE5\u4E1A\u5927\u5B66-\u897F\u5317\u5927\u5B66\uFF09\u3001\u5B89\u5FBD\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u673A\u6784\uFF1A\u4E2D\u79D1\u9662\u52A8\u7269\u7814\u7A76\u6240\uFF09\u3001\u897F\u5357\u4EA4\u901A\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u673A\u6784\uFF1A\u4E2D\u79D1\u9662\u9AD8\u80FD\u7269\u7406\u7814\u7A76\u6240\uFF09\u3001\u897F\u5357\u8D22\u7ECF\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u7535\u5B50\u79D1\u6280\u5927\u5B66\uFF09\u3001\u5317\u4EAC\u5916\u56FD\u8BED\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E2D\u56FD\u653F\u6CD5\u5927\u5B66\uFF09\u3001\u5929\u6D25\u533B\u79D1\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u5929\u6D25\u5927\u5B66\uFF09\u3001\u5357\u4EAC\u4FE1\u606F\u5DE5\u7A0B\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E2D\u56FD\u79D1\u5B66\u9662\u5927\u5B66\uFF09\u3001\u897F\u5357\u79D1\u6280\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u673A\u6784\uFF1A\u4E2D\u56FD\u5DE5\u7A0B\u7269\u7406\u7814\u7A76\u9662\u7B49\uFF09\u3001\u534E\u5357\u519C\u4E1A\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u6FB3\u95E8\u79D1\u6280\u5927\u5B66\uFF09\u3001\u4E0A\u6D77\u4E2D\u533B\u836F\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u673A\u6784\uFF1A\u4E0A\u6D77\u4EA4\u901A\u5927\u5B66\u3001\u534E\u4E1C\u5E08\u8303\u5927\u5B66\u3001\u4E2D\u56FD\u4E2D\u533B\u79D1\u5B66\u9662\uFF09\u3001\u9ED1\u9F99\u6C5F\u5DE5\u7A0B\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u673A\u6784\uFF1A\u4E2D\u5174\u901A\u8BAF\u80A1\u4EFD\u6709\u9650\u516C\u53F8\uFF09\u3001\u798F\u5EFA\u5DE5\u7A0B\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u53F0\u6E7E\u9AD8\u6821\u7FA4\uFF09\u3001\u95FD\u5357\u5E08\u8303\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u53F0\u6E7E\u9AD8\u6821\u7FA4\uFF09\u3001\u4E2D\u56FD\u653F\u6CD5\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u5317\u4EAC\u5916\u56FD\u8BED\u5927\u5B66\uFF09\u3001\u7535\u5B50\u79D1\u6280\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u897F\u5357\u8D22\u7ECF\u5927\u5B66\uFF09\u3001\u4E50\u5C71\u5E08\u8303\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u6B66\u6C49\u5927\u5B66\uFF09\u3001\u4E2D\u56FD\u6D88\u9632\u6551\u63F4\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E2D\u56FD\u6C11\u7528\u822A\u7A7A\u98DE\u884C\u5B66\u9662\uFF09\u3001\u91CD\u5E86\u533B\u79D1\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u897F\u5357\u5927\u5B66\u6216\u590D\u65E6\u5927\u5B66\uFF09\u3001\u957F\u6C5F\u5E08\u8303\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u5C71\u4E1C\u79D1\u6280\u5927\u5B66\uFF09\u3001\u91CD\u5E86\u4E09\u5CE1\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E1C\u5357\u5927\u5B66\uFF09\u3001\u5929\u6C34\u5E08\u8303\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u897F\u5357\u4EA4\u901A\u5927\u5B66\uFF09\u3001\u5170\u5DDE\u6587\u7406\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E2D\u56FD\u4F20\u5A92\u5927\u5B66\uFF09\u3001\u5170\u5DDE\u8D22\u7ECF\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E2D\u592E\u8D22\u7ECF\u5927\u6216\u5BF9\u5916\u7ECF\u6D4E\u8D38\u6613\u5927\u5B66\uFF09\u3001\u534E\u5357\u519C\u4E1A\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u6FB3\u95E8\u79D1\u6280\u5927\u5B66\uFF09\u3001\u5E7F\u897F\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u534E\u5357\u7406\u5DE5\u5927\u5B66\uFF09\u3001\u8D35\u5DDE\u533B\u79D1\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u5317\u4EAC\u534F\u548C\u533B\u5B66\u9662\uFF09\u3001\u9075\u4E49\u533B\u79D1\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u5E7F\u5DDE\u533B\u79D1\u5927\u5B66\uFF09\u3001\u8D35\u5DDE\u5DE5\u7A0B\u5E94\u7528\u6280\u672F\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u897F\u5357\u5927\u5B66\u6216\u4E2D\u56FD\u77FF\u4E1A\u5927\u5B66\uFF09\u3001\u516D\u76D8\u6C34\u5E08\u8303\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E0A\u6D77\u5DE5\u7A0B\u6280\u672F\u5927\u5B66\uFF09\u3001\u6CB3\u5357\u5DE5\u4E1A\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u53F0\u6E7E\u4E2D\u539F\u5927\u5B66\uFF09\u3001\u547C\u548C\u6D69\u7279\u6C11\u65CF\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E2D\u592E\u6C11\u65CF\u5927\u5B66\uFF09\u3001\u5317\u65B9\u6C11\u65CF\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u5408\u80A5\u5DE5\u4E1A\u5927\u5B66\uFF09\u3001\u65B0\u7586\u5DE5\u7A0B\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u5317\u4EAC\u79D1\u6280\u5927\u5B66\u3001\u5317\u4EAC\u90AE\u7535\u5927\u5B66\u3001\u4E2D\u56FD\u77FF\u4E1A\u5927\u5B66\uFF09\u3001\u65B0\u7586\u7406\u5DE5\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u6D59\u6C5F\u5DE5\u4E1A\u5927\u5B66\uFF09\u3001\u65B0\u7586\u79D1\u6280\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u6CB3\u5317\u79D1\u6280\u5927\u5B66\uFF09\u3001\u65B0\u7586\u5E08\u8303\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u534E\u4E1C\u5E08\u8303\u5927\u5B66\u3001\u6CB3\u5317\u5E08\u8303\u5927\u5B66\u3001\u897F\u5317\u5E08\u8303\u5927\u5B66\u3001\u6D59\u6C5F\u5E08\u8303\u5927\u5B66\u3001\u5927\u8FDE\u7406\u5DE5\u5927\u5B66\uFF09\u3001\u4E2D\u56FD\u77F3\u6CB9\u5927\u5B66\uFF08\u5317\u4EAC\uFF09\u514B\u62C9\u739B\u4F9D\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u7535\u5B50\u79D1\u6280\u5927\u5B66\uFF09\u3001\u4E0A\u6D77\u4E2D\u533B\u836F\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E0A\u6D77\u4EA4\u901A\u5927\u5B66\u3001\u534E\u4E1C\u5E08\u8303\u5927\u5B66\uFF09\u3001\u6D59\u6C5F\u8B66\u5BDF\u5B66\u9662\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u534E\u4E1C\u653F\u6CD5\u5927\u5B66\uFF09\u3001\u4E91\u5357\u5927\u5B66\uFF08\u8054\u5408\u57F9\u517B\u5927\u5B66\uFF1A\u4E2D\u56FD\u79D1\u5B66\u9662\uFF09";
        try
        {
            BufferedWriter bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(new File("D://a.txt"))));
            String x[] = xx.split("\uFF09\u3001");
            int i = 1;
            String args1[];
            int k = (args1 = x).length;
            for(int j = 0; j < k; j++)
            {
                String e = args1[j];
                if(e.indexOf("\u8054\u5408") == -1)
                {
                    System.out.println((new StringBuilder(String.valueOf(i++))).append(" -ERR- ").append(e).toString());
                    bw.write((new StringBuilder(String.valueOf(i++))).append(" -ERR- ").append(e).append("\r\n").toString());
                } else
                {
                    String prefix = e.substring(0, e.indexOf("\u8054\u5408") - 1);
                    String subfix = e.substring(e.indexOf("\u8054\u5408") + 7);
                    System.out.println((new StringBuilder(" -- ")).append(prefix).append(" -- ").append(subfix).toString());
                    bw.write((new StringBuilder("insert into alhpy values('")).append(prefix).append("' ,'").append(subfix).append("');\r\n").toString());
                }
            }

            bw.flush();
            bw.close();
        }
        catch(Exception e1)
        {
            e1.printStackTrace();
        }
    }

    private String yxmc;
    private String zymc;
    private String xz;
    private String cc;
    private String ts;
    private String zydl;
}
