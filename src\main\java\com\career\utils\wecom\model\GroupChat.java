package com.career.utils.wecom.model;

import java.util.Date;

/**
 * 企业微信客户群信息模型
 */
public class GroupChat {
    // 客户群ID (对应 wecom_chat_id)
    private String chatId;
    // 群名 (对应 wecom_name)
    private String name;
    // 群主ID (对应 wecom_owner_userid)
    private String owner;
    // 群创建时间 (对应 wecom_create_time)
    private Date createTime;
    // 群成员数量 (对应 wecom_member_count)
    private Integer memberCount;
    // 群成员版本信息 (对应 wecom_member_version)
    private String memberVersion;
    // 记录创建时间 (对应 wecom_created_at)
    private Date createdAt;
    // 记录更新时间 (对应 wecom_updated_at)
    private Date updatedAt;
    // SaaS ID (对应 wecom_saas_id)
    private String wecom_saas_id;
    
    // 群组同步时间戳 (用于增量同步)
    private Date groupSyncTime;

    // 构造函数
    public GroupChat() {
    }
    
    // Getter和Setter方法
    public String getChatId() {
        return chatId;
    }
    
    public void setChatId(String chatId) {
        this.chatId = chatId;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getOwner() {
        return owner;
    }
    
    public void setOwner(String owner) {
        this.owner = owner;
    }
    
    public Date getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    
    public Integer getMemberCount() {
        return memberCount;
    }
    
    public void setMemberCount(Integer memberCount) {
        this.memberCount = memberCount;
    }

    public String getMemberVersion() {
        return memberVersion;
    }
    
    public void setMemberVersion(String memberVersion) {
        this.memberVersion = memberVersion;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getWecom_saas_id() {
        return wecom_saas_id;
    }

    public void setWecom_saas_id(String wecom_saas_id) {
        this.wecom_saas_id = wecom_saas_id;
    }

    // 兼容性方法，兼容原来的saasId
    public String getSaasId() {
        return wecom_saas_id;
    }

    public void setSaasId(String saasId) {
        this.wecom_saas_id = saasId;
    }
    
    public Date getGroupSyncTime() {
        return groupSyncTime;
    }
    
    public void setGroupSyncTime(Date groupSyncTime) {
        this.groupSyncTime = groupSyncTime;
    }
    
    @Override
    public String toString() {
        return "群ID: " + chatId +
               ", 群名: " + name +
               ", 群主: " + owner +
               ", 成员数量: " + (memberCount != null ? memberCount : "N/A") +
               ", 群成员版本: " + memberVersion +
               ", SaaS ID: " + wecom_saas_id +
               ", 同步时间: " + (groupSyncTime != null ? groupSyncTime : "N/A");
    }
} 