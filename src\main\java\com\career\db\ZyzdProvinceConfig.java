package com.career.db;

import java.util.Date;

import com.career.utils.Tools;
import com.career.utils.ZyzdCache;

public class ZyzdProvinceConfig {
	
	private String p_code;
	private String p_table_code;
	private String p_name;
	private String p_view_name;
	private int latest_year_jh;
	private int latest_year_yx;
	private int latest_year_zy;
	private int form_type;
	private int form_bk_cnt;
	private int form_zk_cnt;
	private int form_bk_major_cnt;
	private int form_zk_major_cnt;
	private String form_bk_rule;
	private String form_zk_rule;;
	private int form_zjbk_cnt;
	private String form_zjbk_rule;
	private int form_zjbk_major_cnt;
	private int open_ind;
	private int pcx_bk_ls;
	private int pcx_bk_wl;
	private int open_status_lhy;
	private int jh_version;
	private Date tb_start_tm;
	 
	public Date getTb_start_tm() {
		return tb_start_tm;
	}
	public void setTb_start_tm(Date tb_start_tm) {
		this.tb_start_tm = tb_start_tm;
	}
	public int getForm_zjbk_cnt() {
		return form_zjbk_cnt;
	}
	public void setForm_zjbk_cnt(int form_zjbk_cnt) {
		this.form_zjbk_cnt = form_zjbk_cnt;
	}
	public String getForm_zjbk_rule() {
		return form_zjbk_rule;
	}
	public void setForm_zjbk_rule(String form_zjbk_rule) {
		this.form_zjbk_rule = form_zjbk_rule;
	}
	public int getForm_zjbk_major_cnt() {
		return form_zjbk_major_cnt;
	}
	public void setForm_zjbk_major_cnt(int form_zjbk_major_cnt) {
		this.form_zjbk_major_cnt = form_zjbk_major_cnt;
	}
	public int getJh_version() {
		return jh_version;
	}
	public void setJh_version(int jh_version) {
		this.jh_version = jh_version;
	}
	public int getOpen_status_lhy() {
		return open_status_lhy;
	}
	public void setOpen_status_lhy(int open_status_lhy) {
		this.open_status_lhy = open_status_lhy;
	}
	public int getPcx_bk_wl() {
		return pcx_bk_wl;
	}
	public void setPcx_bk_wl(int pcx_bk_wl) {
		this.pcx_bk_wl = pcx_bk_wl;
	}

	public int getPcx_bk_ls() {
		return pcx_bk_ls;
	}
	public void setPcx_bk_ls(int pcx_bk_ls) {
		this.pcx_bk_ls = pcx_bk_ls;
	}
	public int getForm_bk_major_cnt() {
		return form_bk_major_cnt;
	}
	public void setForm_bk_major_cnt(int form_bk_major_cnt) {
		this.form_bk_major_cnt = form_bk_major_cnt;
	}
	public int getForm_zk_major_cnt() {
		return form_zk_major_cnt;
	}
	public void setForm_zk_major_cnt(int form_zk_major_cnt) {
		this.form_zk_major_cnt = form_zk_major_cnt;
	}
	public String getP_code() {
		return p_code;
	}
	public void setP_code(String p_code) {
		this.p_code = p_code;
	}
	public String getP_table_code() {
		return p_table_code;
	}
	public void setP_table_code(String p_table_code) {
		this.p_table_code = p_table_code;
	}
	public String getP_name() {
		return p_name;
	}
	public void setP_name(String p_name) {
		this.p_name = p_name;
	}
	public String getP_view_name() {
		return p_view_name;
	}
	public void setP_view_name(String p_view_name) {
		this.p_view_name = p_view_name;
	}
	public int getLatest_year_jh() {
		return latest_year_jh;
	}
	public void setLatest_year_jh(int latest_year_jh) {
		this.latest_year_jh = latest_year_jh;
	}
	public int getLatest_year_yx() {
		return latest_year_yx;
	}
	public void setLatest_year_yx(int latest_year_yx) {
		this.latest_year_yx = latest_year_yx;
	}
	public int getLatest_year_zy() {
		return latest_year_zy;
	}
	public void setLatest_year_zy(int latest_year_zy) {
		this.latest_year_zy = latest_year_zy;
	}
	public int getForm_type() {
		return form_type;
	}
	public void setForm_type(int form_type) {
		this.form_type = form_type;
	}
	public int getForm_bk_cnt() {
		return form_bk_cnt;
	}
	public void setForm_bk_cnt(int form_bk_cnt) {
		this.form_bk_cnt = form_bk_cnt;
	}
	public int getForm_zk_cnt() {
		return form_zk_cnt;
	}
	public void setForm_zk_cnt(int form_zk_cnt) {
		this.form_zk_cnt = form_zk_cnt;
	}
	public String getForm_bk_rule() {
		return form_bk_rule;
	}
	public void setForm_bk_rule(String form_bk_rule) {
		this.form_bk_rule = form_bk_rule;
	}
	public String getForm_zk_rule() {
		return form_zk_rule;
	}
	public void setForm_zk_rule(String form_zk_rule) {
		this.form_zk_rule = form_zk_rule;
	}
	public int getOpen_ind() {
		return open_ind;
	}
	public void setOpen_ind(int open_ind) {
		this.open_ind = open_ind;
	}
	
	public int getCal_FormCnt(String pc_code, ZyzdProvincePcConfig special) {
		if(special != null) {
			return special.getForm_cnt();
		}else {
			if(pc_code.equals("本科")) {
				return this.form_bk_cnt;
			}else if(pc_code.equals("专科")){
				return this.form_zk_cnt;
			}else {
				return this.form_zjbk_cnt;
			}
		}
	}
	
	public int getCal_MajorCnt(String pc_code, ZyzdProvincePcConfig special) {
		if(special != null) {
			return special.getMajor_cnt();
		}else {
			if(pc_code.equals("本科")) {
				return this.form_bk_major_cnt;
			}else if(pc_code.equals("专科")){
				return this.form_zk_major_cnt;
			}else {
				return this.form_zjbk_major_cnt;
			}
		}
	}
	
	public String getCal_FormRule(String pc_code, ZyzdProvincePcConfig special) {
		if(special != null) {
			return special.getForm_rule();
		}else {
			if(pc_code.equals("本科")) {
				return this.form_bk_rule;
			}else if(pc_code.equals("专科")){
				return this.form_zk_rule;
			}else {
				return this.form_zjbk_rule;
			}
		}
	}
	
	public int getCal_FormCnt(String pc_code) {
		return getCal_FormCnt(pc_code, null);
	}
	
	public int getCal_MajorCnt(String pc_code) {
		return getCal_MajorCnt(pc_code, null); 
	}
	
	public String getCal_FormRule(String pc_code) {
		return getCal_FormRule(pc_code, null);
	}
	
	public String getCal_MajorCatgNameInCatgTable(String pc_code) {
		return ZyzdCache.getMajorccCodeByJhpc(pc_code);
	}

}
