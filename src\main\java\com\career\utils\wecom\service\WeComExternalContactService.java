package com.career.utils.wecom.service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.career.utils.Tools;
import com.career.utils.wecom.WeComHttpClient;
import com.career.utils.wecom.WeComTokenManager;
import com.career.utils.wecom.config.WeComConfig;
import com.career.utils.wecom.db.WeComJDBC;
import com.career.utils.wecom.exception.WeComApiException;
import com.career.utils.wecom.exception.WeComApiException.NoExternalContactPermissionException;
import com.career.utils.wecom.model.FollowerInfo;
import com.career.utils.wecom.model.TagInfo;
import com.career.utils.wecom.model.WeComCustomer;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;

/**
 * 企业微信外部联系人（客户）管理服务类
 */
public class WeComExternalContactService {

    private static final String GET_CUSTOMER_LIST_URL = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/list";
    private static final String GET_CUSTOMER_DETAIL_URL = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get";
    
    // 数据库操作实例
    private static WeComJDBC weComJDBC = new WeComJDBC();

    /**
     * 获取指定成员的客户列表
     * @param accessToken 访问令牌
     * @param userId 企业成员ID
     * @return 客户ID列表
     * @throws WeComApiException 当API调用失败时抛出，对于84061错误码会包装成特殊异常
     */
    public static List<String> getCustomerIdsByUserId(String accessToken, String userId) throws WeComApiException {
        List<String> externalUserIds = new ArrayList<>();

        String url = String.format("%s?access_token=%s&userid=%s", GET_CUSTOMER_LIST_URL, accessToken, userId);

        try {
            JSONObject response = WeComHttpClient.doGetWithRetry(url);

            // 根据配置输出API响应原始数据
            if (WeComConfig.isLogApiRawJsonResponse()) {
                Tools.println("=== 获取客户列表API原始响应数据 ===");
                Tools.println("成员ID: " + userId);
                Tools.println("请求URL: " + url);
                Tools.println("原始响应: " + response.toString());
                Tools.println("=====================================");
            }

            // 检查API调用是否成功
            int errcode = response.getIntValue("errcode");
            if (errcode != 0) {
                String errmsg = response.getString("errmsg");
                if (errcode == 84061) {
                    // 84061: not external contact - 该成员没有外部联系人权限
                    throw new NoExternalContactPermissionException(userId, errcode, getDetailedErrorMessage(errcode, errmsg));
                } else {
                    // 其他错误码
                    throw WeComApiException.create(errcode, getDetailedErrorMessage(errcode, errmsg));
                }
            }

            JSONArray externalUserIdList = response.getJSONArray("external_userid");
            if (externalUserIdList != null) {
                for (int i = 0; i < externalUserIdList.size(); i++) {
                    externalUserIds.add(externalUserIdList.getString(i));
                }
            }

            return externalUserIds;
        } catch (WeComApiException e) {
            throw e; // 重新抛出WeComApiException
        } catch (Exception e) {
            throw new WeComApiException("获取客户列表失败，成员ID: " + userId + ", 错误信息: " + e.getMessage(), e);
        }
    }

     /**
     * 获取企业微信所有客户信息（支持增量抓取和去重）
     * @param corpId 企业ID
     * @param corpSecret 应用的Secret
     * @param userId 企业成员UserID，如果为null则获取所有成员的客户
     * @param useIncrementalFetch 是否使用增量抓取模式
     * @param saasId SaaS ID
     * @return 客户信息列表
     */
    public static List<WeComCustomer> fetchAllCustomers(String corpId, String corpSecret, String userId, boolean useIncrementalFetch, String saasId) {
        List<WeComCustomer> customers = new ArrayList<>();

        try {
            Tools.println("开始抓取外部联系人，增量模式: " + useIncrementalFetch + ", 去重检查: " + WeComConfig.isEnableExternalContactDedup());
            
            // 1. 获取access_token
            String accessToken = WeComTokenManager.getAccessToken(corpId, corpSecret);

            // 2. 如果userId为null，则获取所有成员的客户
            List<String> userIds = new ArrayList<>();
            if (userId == null || userId.trim().isEmpty()) {
                // 调用内部成员Service获取所有成员ID
                userIds = WeComInternalContactService.getAllUserIds(corpId, corpSecret, 1L);
                Tools.println("获取到企业成员数量: " + userIds.size());
            } else {
                userIds.add(userId);
            }

            // 3. 收集所有需要获取的外部联系人ID，并统计前置检查结果
            List<String> allExternalUserIds = new ArrayList<>();
            int totalMembers = userIds.size();
            int membersWithExternalContacts = 0;
            int membersWithoutExternalContacts = 0;
            int membersWithErrors = 0;
            
            Tools.println("=== 阶段2: 同步外部联系人 ===");
            
            for (String uid : userIds) {
                try {
                    List<String> externalUserIds = getCustomerIdsByUserId(accessToken, uid);
                    Tools.println("成员 " + uid + " 的客户数量: " + externalUserIds.size());
                    allExternalUserIds.addAll(externalUserIds);
                    membersWithExternalContacts++;
                } catch (NoExternalContactPermissionException e) {
                    // 84061错误：该成员没有外部联系人权限，这是正常情况
                    Tools.println("成员 " + uid + " 没有外部联系人权限，跳过同步");
                    membersWithoutExternalContacts++;
                } catch (WeComApiException e) {
                    // 其他API错误
                    Tools.println("获取成员客户列表失败，成员ID: " + uid + ", 错误码: " + e.getErrorCode() + ", 错误信息: " + e.getMessage());
                    membersWithErrors++;
                } catch (Exception e) {
                    // 未知错误
                    Tools.println("获取成员客户列表时发生未知错误，成员ID: " + uid + ", 错误信息: " + e.getMessage());
                    membersWithErrors++;
                }
            }
            
            // 输出统计信息
            Tools.println("=====================================");
            Tools.println("外部联系人权限检查统计:");
            Tools.println("  总成员数: " + totalMembers);
            Tools.println("  有外部联系人权限: " + membersWithExternalContacts + " 个");
            Tools.println("  无外部联系人权限: " + membersWithoutExternalContacts + " 个");
            Tools.println("  获取失败: " + membersWithErrors + " 个");
            if (totalMembers > 0) {
                Tools.println("  权限覆盖率: " + String.format("%.1f%%", (membersWithExternalContacts * 100.0 / totalMembers)));
            }
            Tools.println("=====================================");
            
            // 去重处理
            Set<String> uniqueExternalUserIds = new HashSet<>(allExternalUserIds);
            Tools.println("去重后的外部联系人总数: " + uniqueExternalUserIds.size() + " (原始总数: " + allExternalUserIds.size() + ")");
            
            // 4. 如果启用去重，先查询已存在的数据
            Map<String, WeComCustomer> existingContacts = null;
            if (WeComConfig.isEnableExternalContactDedup()) {
                List<String> externalUserIdsList = new ArrayList<>(uniqueExternalUserIds);
                existingContacts = weComJDBC.getWeComExternalContactsByIds(externalUserIdsList);
                Tools.println("数据库中已存在的外部联系人数量: " + existingContacts.size());
            }

            // 5. 根据增量模式和去重配置决定要获取的客户
            List<String> customersToFetch = new ArrayList<>();
            int skippedCount = 0;
            
            for (String externalUserId : uniqueExternalUserIds) {
                boolean shouldFetch = true;
                
                if (useIncrementalFetch && WeComConfig.isEnableExternalContactDedup() && existingContacts != null) {
                    WeComCustomer existingContact = existingContacts.get(externalUserId);
                    if (existingContact != null) {
                        // 已存在，检查是否需要更新
                        if (needsContactUpdate(existingContact)) {
                            // 需要更新，添加到抓取列表
                            customersToFetch.add(externalUserId);
                        } else {
                            // 不需要更新，跳过
                            customers.add(existingContact);
                            skippedCount++;
                            shouldFetch = false;
                        }
                    }
                }
                
                if (shouldFetch && !customersToFetch.contains(externalUserId)) {
                    customersToFetch.add(externalUserId);
                }
            }
            
            Tools.println("需要从API获取的外部联系人数量: " + customersToFetch.size() + ", 跳过数量: " + skippedCount);

            // 6. 批量获取客户详细信息
            int processedCount = 0;
            int successCount = 0;
            int error40096Count = 0;
            int otherErrorCount = 0;
            
            for (String externalUserId : customersToFetch) {
                processedCount++;
                
                // 显示进度
                if (processedCount % 50 == 0 || processedCount == customersToFetch.size()) {
                    Tools.println("抓取进度: " + processedCount + "/" + customersToFetch.size() + 
                            " (成功: " + successCount + ", 40096错误: " + error40096Count + ", 其他错误: " + otherErrorCount + ")");
                }
                
                try {
                    WeComCustomer customer = getCustomerDetailWithEnhancedErrorHandling(accessToken, externalUserId, saasId);
                    if (customer != null) {
                        customers.add(customer);
                        successCount++;
                    }
                } catch (WeComApiException e) {
                    if (isError40096(e)) {
                        error40096Count++;
                        if (WeComConfig.isLog40096Details()) {
                            Tools.println("40096错误 - 外部联系人ID: " + externalUserId + ", 详情: " + e.getMessage());
                        }
                    } else {
                        otherErrorCount++;
                        Tools.println("获取客户详情失败，客户ID: " + externalUserId + ", 错误信息: " + e.getMessage());
                    }
                } catch (Exception e) {
                    otherErrorCount++;
                    Tools.println("获取客户详情失败，客户ID: " + externalUserId + ", 错误信息: " + e.getMessage());
                }
            }

            Tools.println("外部联系人抓取完成 - 总处理: " + processedCount + 
                    ", 成功: " + successCount + 
                    ", 40096错误: " + error40096Count + 
                    ", 其他错误: " + otherErrorCount + 
                    ", 最终返回数量: " + customers.size());

        } catch (Exception e) {
            throw new WeComApiException("获取企业微信客户信息失败: " + e.getMessage(), e);
        }

        return customers;
    }
    
    /**
     * 获取企业微信所有客户信息（保持向后兼容的重载方法）
     * @param corpId 企业ID
     * @param corpSecret 应用的Secret
     * @param userId 企业成员UserID，如果为null则获取所有成员的客户
     * @return 客户信息列表
     */
    public static List<WeComCustomer> fetchAllCustomers(String corpId, String corpSecret, String userId) {
        // 使用配置文件中的默认设置
        return fetchAllCustomers(corpId, corpSecret, userId, WeComConfig.isDefaultSyncModeIncremental(), null);
    }

    /**
     * 获取企业微信首个客户信息（用于测试）
     * @param corpId 企业ID
     * @param corpSecret 应用的Secret
     * @return 首个客户信息，如果没有客户则返回null
     */
    public static WeComCustomer fetchFirstCustomer(String corpId, String corpSecret) {
        try {
            // 1. 获取access_token
            String accessToken = WeComTokenManager.getAccessToken(corpId, corpSecret);

            // 2. 获取所有成员ID
            List<String> userIds = WeComInternalContactService.getAllUserIds(corpId, corpSecret, 1L);
            if (userIds.isEmpty()) {
                Tools.println("没有找到企业成员");
                return null;
            }

            // 3. 遍历成员，找到第一个有外部联系人的成员
            for (String userId : userIds) {
                try {
                    List<String> externalUserIds = getCustomerIdsByUserId(accessToken, userId);
                    
                    if (!externalUserIds.isEmpty()) {
                        // 4. 获取第一个客户的详细信息
                        String externalUserId = externalUserIds.get(0);
                        WeComCustomer customer = getCustomerDetail(accessToken, externalUserId);
                        
                        Tools.println("获取首个客户成功，来自成员 " + userId + ": " + customer.getName());
                        return customer;
                    } else {
                        Tools.println("成员 " + userId + " 有外部联系人权限但没有客户");
                    }
                } catch (NoExternalContactPermissionException e) {
                    // 该成员没有外部联系人权限，继续尝试下一个成员
                    Tools.println("成员 " + userId + " 没有外部联系人权限，继续尝试下一个成员");
                } catch (WeComApiException e) {
                    Tools.println("获取成员 " + userId + " 的客户列表失败，错误码: " + e.getErrorCode() + "，继续尝试下一个成员");
                }
            }
            
            Tools.println("所有成员都没有外部联系人或无权限");
            return null;

        } catch (Exception e) {
            Tools.println("获取首个客户失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取客户详细信息，增强错误处理
     * @param accessToken 访问令牌
     * @param externalUserId 外部联系人ID
     * @param saasId SaaS ID
     * @return 客户详细信息
     * @throws WeComApiException API异常
     */
    public static WeComCustomer getCustomerDetailWithEnhancedErrorHandling(String accessToken, String externalUserId, String saasId) throws WeComApiException {
        int retryCount = 0;
        int maxRetryCount = WeComConfig.getMaxRetryCount();
        
        Exception lastException = null;
        
        while (retryCount <= maxRetryCount) {
            try {
                WeComCustomer customer = getCustomerDetail(accessToken, externalUserId);
                if (customer != null) {
                    customer.setWecom_saas_id(saasId);
                }
                return customer;
                
            } catch (WeComApiException e) {
                lastException = e;
                
                // 检查错误码
                if (isError40096(e)) {
                    // 40096 错误：不存在的客户，根据配置决定是否重试
                    if (WeComConfig.isSkipRetryOn40096()) {
                        throw e; // 跳过重试，直接抛出异常
                    }
                    
                    // 40096错误重试次数可能与其他错误不同
                    if (retryCount >= WeComConfig.getSpecialErrorRetryCount()) {
                        throw e;
                    }
                } else {
                    // 其他错误使用常规重试逻辑
                    if (retryCount >= maxRetryCount) {
                        throw e;
                    }
                }
                
                retryCount++;
                
                // 计算重试间隔（指数退避）
                long waitTime = (long) Math.pow(2, retryCount) * 1000; // 2^n 秒
                try {
                    Thread.sleep(waitTime);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new WeComApiException("重试被中断", ie);
                }
                
            } catch (Exception e) {
                lastException = e;
                
                if (retryCount >= maxRetryCount) {
                    throw new WeComApiException("获取客户详情失败，重试次数已用完", e);
                }
                
                retryCount++;
                try {
                    Thread.sleep(1000 * retryCount); // 线性退避
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new WeComApiException("重试被中断", ie);
                }
            }
        }
        
        // 如果到这里，说明重试次数用完了
        if (lastException instanceof WeComApiException) {
            throw (WeComApiException) lastException;
        } else {
            throw new WeComApiException("获取客户详情失败，重试次数已用完", lastException);
        }
    }

    /**
     * 获取客户详细信息（保持向后兼容）
     * @param accessToken 访问令牌
     * @param externalUserId 客户ID
     * @return 客户信息，如果获取失败或信息不完整则返回null
     * @throws WeComApiException 当API调用失败时抛出，特别是对于错误码84061（not external contact），将抛出异常以便上层调用可以特殊处理
     */
    public static WeComCustomer getCustomerDetail(String accessToken, String externalUserId) throws WeComApiException {
        try {
            String url = String.format("%s?access_token=%s&external_userid=%s", GET_CUSTOMER_DETAIL_URL, accessToken, externalUserId);
            JSONObject response = WeComHttpClient.doGetWithRetry(url);

            // 根据配置输出API响应原始数据
            if (WeComConfig.isLogApiRawJsonResponse()) {
                Tools.println("=== 获取客户详情API原始响应数据 ===");
                Tools.println("客户ID: " + externalUserId);
                Tools.println("请求URL: " + url);
                Tools.println("原始响应: " + response.toString());
                Tools.println("=====================================");
            }

            int errcode = response.getIntValue("errcode");
            if (errcode != 0) {
                throw WeComApiException.create(errcode, getDetailedErrorMessage(errcode, response.getString("errmsg")));
            }

            // 提取外部联系人信息
            JSONObject externalContactJson = response.getJSONObject("external_contact");
            if (externalContactJson == null) {
                throw new WeComApiException("获取外部联系人 " + externalUserId + " 详情成功，但返回的数据不包含外部联系人信息");
            }

            // 构建客户对象
            WeComCustomer customer = new WeComCustomer();
            customer.setExternalUserId(externalContactJson.getString("external_userid"));
            customer.setName(externalContactJson.getString("name"));
            customer.setAvatar(externalContactJson.getString("avatar"));
            customer.setType(externalContactJson.getIntValue("type"));
            customer.setGender(externalContactJson.getIntValue("gender"));
            
            // 设置跟进人信息和加入时间
            JSONArray followUserArray = response.getJSONArray("follow_user");
            if (followUserArray != null && !followUserArray.isEmpty()) {
                customer.setFollowUsersJson(followUserArray.toJSONString());
                
                // 查找最早的加入时间
                Long earliestCreateTime = null;
                for (int i = 0; i < followUserArray.size(); i++) {
                    JSONObject followUser = followUserArray.getJSONObject(i);
                    Long createtime = followUser.getLong("createtime");
                    if (createtime != null) {
                        if (earliestCreateTime == null || createtime < earliestCreateTime) {
                            earliestCreateTime = createtime;
                        }
                    }
                }
                
                if (earliestCreateTime != null) {
                    customer.setJoinTime(new java.sql.Timestamp(earliestCreateTime * 1000));
                } else {
                    // 如果没有找到createtime，使用当前时间作为默认值
                    customer.setJoinTime(new java.sql.Timestamp(System.currentTimeMillis()));
                }
            } else {
                // 如果没有follow_user信息，joinTime设置为当前时间
                 customer.setJoinTime(new java.sql.Timestamp(System.currentTimeMillis()));
            }

            return customer;
            
        } catch (WeComApiException e) {
            throw e;
        } catch (Exception e) {
            throw new WeComApiException("获取外部联系人 " + externalUserId + " 详情时发生系统错误: " + e.getMessage(), e);
        }
    }

    /**
     * 判断是否为40096错误
     * @param e 异常对象
     * @return 是否为40096错误
     */
    private static boolean isError40096(WeComApiException e) {
        return e.getErrorCode() == 40096;
    }
    
    /**
     * 判断外部联系人是否需要更新
     * @param existingContact 已存在的外部联系人
     * @return 是否需要更新
     */
    private static boolean needsContactUpdate(WeComCustomer existingContact) {
        // 检查跟进人信息是否为空
        if (existingContact.getFollowUsersJson() == null || existingContact.getFollowUsersJson().trim().isEmpty()) {
            return true;
        }
        
        // 检查其他关键信息是否完整
        if (existingContact.getName() == null || existingContact.getName().trim().isEmpty()) {
            return true;
        }
        
        // 检查更新时间，如果超过一定时间可以考虑重新获取
        // 这里可以根据业务需求添加时间判断逻辑
        
        return false;
    }
    
    /**
     * 根据错误码获取详细的错误信息
     * @param errcode 错误码
     * @param errmsg 原始错误信息
     * @return 详细的错误信息描述
     */
    private static String getDetailedErrorMessage(int errcode, String errmsg) {
        switch (errcode) {
            case 40001:
                return "不合法的访问令牌 (access_token)，请检查令牌是否有效或已过期";
            case 42001:
                return "访问令牌已过期，请刷新访问令牌";
            case 45009:
                return "接口调用超过频率限制，请稍后再试";
            case 84061:
                return "不是外部联系人，可能是内部成员或非企业微信用户";
            case 84064:
                return "非法的外部联系人userid，请检查ID是否正确";
            case 40096:
                return "invalid external userid - 无效的外部联系人ID，该ID可能已失效或不存在";
            case 301002:
                return "无权限访问该用户，请检查应用权限设置";
            case 301004:
                return "无该成员，请确认企业成员ID是否正确";
            default:
                return errmsg;
        }
    }

    /**
     * 获取外部联系人ID列表（通过获取所有成员的客户列表）
     * @param corpId 企业ID
     * @param corpSecret 应用的Secret
     * @return 外部联系人ID列表
     */
    public static List<String> getExternalContactList(String corpId, String corpSecret) {
        List<String> allExternalUserIds = new ArrayList<>();
        
        try {
            Tools.println("开始获取外部联系人ID列表");
            
            // 1. 获取access_token
            String accessToken = WeComTokenManager.getAccessToken(corpId, corpSecret);

            // 2. 获取所有成员ID
            List<String> userIds = WeComInternalContactService.getAllUserIds(corpId, corpSecret, 1L);
            Tools.println("获取到企业成员数量: " + userIds.size());

            // 3. 收集所有需要获取的外部联系人ID，并统计权限检查结果
            int totalMembers = userIds.size();
            int membersWithExternalContacts = 0;
            int membersWithoutExternalContacts = 0;
            int membersWithErrors = 0;
            
            for (String uid : userIds) {
                try {
                    List<String> externalUserIds = getCustomerIdsByUserId(accessToken, uid);
                    Tools.println("成员 " + uid + " 的客户数量: " + externalUserIds.size());
                    allExternalUserIds.addAll(externalUserIds);
                    membersWithExternalContacts++;
                } catch (NoExternalContactPermissionException e) {
                    // 84061错误：该成员没有外部联系人权限，这是正常情况
                    Tools.println("成员 " + uid + " 没有外部联系人权限，跳过");
                    membersWithoutExternalContacts++;
                } catch (WeComApiException e) {
                    // 其他API错误
                    Tools.println("获取成员客户列表失败，成员ID: " + uid + ", 错误码: " + e.getErrorCode() + ", 错误信息: " + e.getMessage());
                    membersWithErrors++;
                } catch (Exception e) {
                    // 未知错误
                    Tools.println("获取成员客户列表时发生未知错误，成员ID: " + uid + ", 错误信息: " + e.getMessage());
                    membersWithErrors++;
                }
            }
            
            // 输出统计信息
            Tools.println("外部联系人权限检查统计:");
            Tools.println("  总成员数: " + totalMembers);
            Tools.println("  有外部联系人权限: " + membersWithExternalContacts + " 个");
            Tools.println("  无外部联系人权限: " + membersWithoutExternalContacts + " 个");
            Tools.println("  获取失败: " + membersWithErrors + " 个");
            if (totalMembers > 0) {
                Tools.println("  权限覆盖率: " + String.format("%.1f%%", (membersWithExternalContacts * 100.0 / totalMembers)));
            }
            
            // 去重处理
            Set<String> uniqueExternalUserIds = new HashSet<>(allExternalUserIds);
            Tools.println("去重后的外部联系人总数: " + uniqueExternalUserIds.size() + " (原始总数: " + allExternalUserIds.size() + ")");
            
            return new ArrayList<>(uniqueExternalUserIds);

        } catch (Exception e) {
            throw new WeComApiException("获取外部联系人ID列表失败: " + e.getMessage(), e);
        }
    }

}