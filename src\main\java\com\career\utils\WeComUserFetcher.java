package com.career.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class WeComUserFetcher {
    
    private static final String GET_TOKEN_URL = "https://qyapi.weixin.qq.com/cgi-bin/gettoken";
    private static final String GET_USER_LIST_URL = "https://qyapi.weixin.qq.com/cgi-bin/user/list";
    
    /**
     * 获取企业所有成员的UserID列表
     * @param corpId 企业ID
     * @param corpSecret 通讯录同步应用的Secret
     * @param departmentId 部门ID，从该部门开始递归获取
     * @return 成员UserID列表
     */
    public static List<String> getAllUserIds(String corpId, String corpSecret, Long departmentId) {
        List<String> userIds = new ArrayList<>();
        
        try {
            // 1. 获取access_token
            String accessToken = getAccessToken(corpId, corpSecret);
            if (accessToken == null) {
                throw new RuntimeException("获取access_token失败");
            }

            // 2. 获取成员列表
            String url = String.format("%s?access_token=%s&department_id=%d&fetch_child=1", 
                                     GET_USER_LIST_URL, accessToken, departmentId);
            String response = doGetRequest(url);
            JSONObject json = JSON.parseObject(response);
            
            if (json.getInteger("errcode") == 0) {
                JSONArray userList = json.getJSONArray("userlist");
                for (int i = 0; i < userList.size(); i++) {
                    userIds.add(userList.getJSONObject(i).getString("userid"));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("获取企业微信成员列表失败: " + e.getMessage());
        }

        return userIds;
    }
    
    private static String getAccessToken(String corpId, String corpSecret) throws IOException {
        String url = String.format("%s?corpid=%s&corpsecret=%s", GET_TOKEN_URL, corpId, corpSecret);
        String response = doGetRequest(url);
        JSONObject json = JSON.parseObject(response);
        return json.getInteger("errcode") == 0 ? json.getString("access_token") : null;
    }
    
    private static String doGetRequest(String url) throws IOException {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet httpGet = new HttpGet(url);
            HttpResponse response = httpClient.execute(httpGet);
            return EntityUtils.toString(response.getEntity());
        }
    }

    public static void main(String[] args) {
        // 使用示例
        String corpId = "你的企业ID";
        String corpSecret = "你的通讯录同步应用的Secret";
        Long departmentId = 1L; // 从根部门开始获取
        
        List<String> userIds = getAllUserIds(corpId, corpSecret, departmentId);
        System.out.println("获取到成员数量: " + userIds.size());
        for (String userId : userIds) {
            System.out.println("成员UserID: " + userId);
        }
    }
}