package com.career.utils.excel.model;

import com.career.utils.excel.annotation.ExcelColumn;

import java.io.Serializable;

public class ArtJhForm implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    // 常量字符串用于注解 - 一级表头
    private static final String BASIC_INFO_HEADER = "基本信息";
    private static final String ADMISSION_INFO_HEADER = "招生信息";
    private static final String SCORE_INFO_HEADER = "成绩信息";
    private static final String UNIVERSITY_INFO_HEADER = "院校信息";
    private static final String MAJOR_INFO_HEADER = "专业信息";

    // 默认构造器
    public ArtJhForm() {
    }

    // ===== 基本信息 =====
    @ExcelColumn(name = "学校代号", sort = 1, width = 12, parent = BASIC_INFO_HEADER, level = 1)
    private Integer yxdm;
    
    @ExcelColumn(name = "学校名称", sort = 2, width = 20, parent = BASIC_INFO_HEADER, level = 1)
    private String yxmc;
    
    @ExcelColumn(name = "批次", sort = 3, width = 12, parent = BASIC_INFO_HEADER, level = 1)
    private String pc;
    
    @ExcelColumn(name = "文理", sort = 4, width = 12, parent = BASIC_INFO_HEADER, level = 1)
    private String xk;

    // ===== 招生信息 =====
    @ExcelColumn(name = "统考类", sort = 5, width = 12, parent = ADMISSION_INFO_HEADER, level = 1)
    private String ysfl;
    
    @ExcelColumn(name = "招生专业", sort = 6, width = 20, parent = ADMISSION_INFO_HEADER, level = 1)
    private String zymc;
    
    @ExcelColumn(name = "专业代码", sort = 7, width = 15, parent = ADMISSION_INFO_HEADER, level = 1)
    private String zydm;
    
    @ExcelColumn(name = "招考方向", sort = 8, width = 20, parent = ADMISSION_INFO_HEADER, level = 1)
    private String zkfx;
    
    @ExcelColumn(name = "包含招考方向", sort = 9, width = 20, parent = ADMISSION_INFO_HEADER, level = 1)
    private String zkfx_ext;
    
    @ExcelColumn(name = "包含专业", sort = 10, width = 20, parent = ADMISSION_INFO_HEADER, level = 1)
    private String bhzy;
    
    @ExcelColumn(name = "学制", sort = 11, width = 10, parent = ADMISSION_INFO_HEADER, level = 1)
    private Integer xz;
    
    @ExcelColumn(name = "学费", sort = 12, width = 12, parent = ADMISSION_INFO_HEADER, level = 1)
    private String xf;
    
    @ExcelColumn(name = "计划数", sort = 13, width = 12, parent = ADMISSION_INFO_HEADER, level = 1)
    private Integer jhs;
    
    @ExcelColumn(name = "备注", sort = 14, width = 20, parent = ADMISSION_INFO_HEADER, level = 1)
    private String zybz;

    // ===== 成绩信息 =====
    @ExcelColumn(name = "综合成绩", sort = 15, width = 15, parent = SCORE_INFO_HEADER, level = 1)
    private Double zhcj;
    
    @ExcelColumn(name = "专业分数", sort = 16, width = 15, parent = SCORE_INFO_HEADER, level = 1)
    private Double zyfs;
    
    @ExcelColumn(name = "位次", sort = 17, width = 12, parent = SCORE_INFO_HEADER, level = 1)
    private Integer wc;

    // ===== 院校信息 =====
    @ExcelColumn(name = "主管部门", sort = 18, width = 15, parent = UNIVERSITY_INFO_HEADER, level = 1)
    private String zgbm;
    
    @ExcelColumn(name = "所在地", sort = 19, width = 15, parent = UNIVERSITY_INFO_HEADER, level = 1)
    private String yxcs;
    
    @ExcelColumn(name = "城市标签", sort = 20, width = 15, parent = UNIVERSITY_INFO_HEADER, level = 1)
    private String csbq;
    
    @ExcelColumn(name = "办学层次", sort = 21, width = 15, parent = UNIVERSITY_INFO_HEADER, level = 1)
    private String pc_code;
    
    @ExcelColumn(name = "院校类型", sort = 22, width = 15, parent = UNIVERSITY_INFO_HEADER, level = 1)
    private String ind_catg;
    
    @ExcelColumn(name = "院校性质", sort = 23, width = 15, parent = UNIVERSITY_INFO_HEADER, level = 1)
    private String ind_nature;
    
    @ExcelColumn(name = "高校层次", sort = 24, width = 15, parent = UNIVERSITY_INFO_HEADER, level = 1)
    private String gxcc;
    
    @ExcelColumn(name = "别称", sort = 25, width = 20, parent = UNIVERSITY_INFO_HEADER, level = 1)
    private String bc;
    
    @ExcelColumn(name = "拔尖卓越计划", sort = 26, width = 20, parent = UNIVERSITY_INFO_HEADER, level = 1)
    private String bjzyjh;
    
    @ExcelColumn(name = "学校共建及更名合并", sort = 27, width = 25, parent = UNIVERSITY_INFO_HEADER, level = 1)
    private String xygm;

    // ===== 专业信息 =====
    @ExcelColumn(name = "转专业情况", sort = 28, width = 20, parent = MAJOR_INFO_HEADER, level = 1)
    private String gx_zzy;
    
    @ExcelColumn(name = "全校硕士专业数", sort = 29, width = 18, parent = MAJOR_INFO_HEADER, level = 1)
    private Integer gx_ssd;
    
    @ExcelColumn(name = "全校硕士专业", sort = 30, width = 25, parent = MAJOR_INFO_HEADER, level = 1)
    private String gx_sszy;
    
    @ExcelColumn(name = "全校博士专业数", sort = 31, width = 18, parent = MAJOR_INFO_HEADER, level = 1)
    private Integer gx_bsd;
    
    @ExcelColumn(name = "全校博士专业", sort = 32, width = 25, parent = MAJOR_INFO_HEADER, level = 1)
    private String gx_bszy;
    
    @ExcelColumn(name = "2023届保研率", sort = 33, width = 15, parent = MAJOR_INFO_HEADER, level = 1)
    private Double gx_byl2023;
    
    @ExcelColumn(name = "高校排名", sort = 34, width = 15, parent = MAJOR_INFO_HEADER, level = 1)
    private String gx_pm;

    // 构造函数
    public ArtJhForm(Integer yxdm, String yxmc, String pc, String xk, String ysfl, String zymc, 
                     String zydm, String zkfx, String zkfx_ext, String bhzy, Integer xz, String xf, 
                     Integer jhs, String zybz, Double zhcj, Double zyfs, Integer wc, String zgbm, 
                     String yxcs, String csbq, String pc_code, String ind_catg, String ind_nature, 
                     String gxcc, String bc, String bjzyjh, String xygm, String gx_zzy, Integer gx_ssd, 
                     String gx_sszy, Integer gx_bsd, String gx_bszy, Double gx_byl2023, String gx_pm) {
        this.yxdm = yxdm;
        this.yxmc = yxmc;
        this.pc = pc;
        this.xk = xk;
        this.ysfl = ysfl;
        this.zymc = zymc;
        this.zydm = zydm;
        this.zkfx = zkfx;
        this.zkfx_ext = zkfx_ext;
        this.bhzy = bhzy;
        this.xz = xz;
        this.xf = xf;
        this.jhs = jhs;
        this.zybz = zybz;
        this.zhcj = zhcj;
        this.zyfs = zyfs;
        this.wc = wc;
        this.zgbm = zgbm;
        this.yxcs = yxcs;
        this.csbq = csbq;
        this.pc_code = pc_code;
        this.ind_catg = ind_catg;
        this.ind_nature = ind_nature;
        this.gxcc = gxcc;
        this.bc = bc;
        this.bjzyjh = bjzyjh;
        this.xygm = xygm;
        this.gx_zzy = gx_zzy;
        this.gx_ssd = gx_ssd;
        this.gx_sszy = gx_sszy;
        this.gx_bsd = gx_bsd;
        this.gx_bszy = gx_bszy;
        this.gx_byl2023 = gx_byl2023;
        this.gx_pm = gx_pm;
    }

    // Getter和Setter方法
    public Integer getYxdm() {
        return yxdm;
    }

    public void setYxdm(Integer yxdm) {
        this.yxdm = yxdm;
    }

    public String getYxmc() {
        return yxmc;
    }

    public void setYxmc(String yxmc) {
        this.yxmc = yxmc;
    }

    public String getPc() {
        return pc;
    }

    public void setPc(String pc) {
        this.pc = pc;
    }

    public String getXk() {
        return xk;
    }

    public void setXk(String xk) {
        this.xk = xk;
    }

    public String getYsfl() {
        return ysfl;
    }

    public void setYsfl(String ysfl) {
        this.ysfl = ysfl;
    }

    public String getZymc() {
        return zymc;
    }

    public void setZymc(String zymc) {
        this.zymc = zymc;
    }

    public String getZydm() {
        return zydm;
    }

    public void setZydm(String zydm) {
        this.zydm = zydm;
    }

    public String getZkfx() {
        return zkfx;
    }

    public void setZkfx(String zkfx) {
        this.zkfx = zkfx;
    }

    public String getZkfx_ext() {
        return zkfx_ext;
    }

    public void setZkfx_ext(String zkfx_ext) {
        this.zkfx_ext = zkfx_ext;
    }

    public String getBhzy() {
        return bhzy;
    }

    public void setBhzy(String bhzy) {
        this.bhzy = bhzy;
    }

    public Integer getXz() {
        return xz;
    }

    public void setXz(Integer xz) {
        this.xz = xz;
    }

    public String getXf() {
        return xf;
    }

    public void setXf(String xf) {
        this.xf = xf;
    }

    public Integer getJhs() {
        return jhs;
    }

    public void setJhs(Integer jhs) {
        this.jhs = jhs;
    }

    public String getZybz() {
        return zybz;
    }

    public void setZybz(String zybz) {
        this.zybz = zybz;
    }

    public Double getZhcj() {
        return zhcj;
    }

    public void setZhcj(Double zhcj) {
        this.zhcj = zhcj;
    }

    public Double getZyfs() {
        return zyfs;
    }

    public void setZyfs(Double zyfs) {
        this.zyfs = zyfs;
    }

    public Integer getWc() {
        return wc;
    }

    public void setWc(Integer wc) {
        this.wc = wc;
    }

    public String getZgbm() {
        return zgbm;
    }

    public void setZgbm(String zgbm) {
        this.zgbm = zgbm;
    }

    public String getYxcs() {
        return yxcs;
    }

    public void setYxcs(String yxcs) {
        this.yxcs = yxcs;
    }

    public String getCsbq() {
        return csbq;
    }

    public void setCsbq(String csbq) {
        this.csbq = csbq;
    }

    public String getPc_code() {
        return pc_code;
    }

    public void setPc_code(String pc_code) {
        this.pc_code = pc_code;
    }

    public String getInd_catg() {
        return ind_catg;
    }

    public void setInd_catg(String ind_catg) {
        this.ind_catg = ind_catg;
    }

    public String getInd_nature() {
        return ind_nature;
    }

    public void setInd_nature(String ind_nature) {
        this.ind_nature = ind_nature;
    }

    public String getGxcc() {
        return gxcc;
    }

    public void setGxcc(String gxcc) {
        this.gxcc = gxcc;
    }

    public String getBc() {
        return bc;
    }

    public void setBc(String bc) {
        this.bc = bc;
    }

    public String getBjzyjh() {
        return bjzyjh;
    }

    public void setBjzyjh(String bjzyjh) {
        this.bjzyjh = bjzyjh;
    }

    public String getXygm() {
        return xygm;
    }

    public void setXygm(String xygm) {
        this.xygm = xygm;
    }

    public String getGx_zzy() {
        return gx_zzy;
    }

    public void setGx_zzy(String gx_zzy) {
        this.gx_zzy = gx_zzy;
    }

    public Integer getGx_ssd() {
        return gx_ssd;
    }

    public void setGx_ssd(Integer gx_ssd) {
        this.gx_ssd = gx_ssd;
    }

    public String getGx_sszy() {
        return gx_sszy;
    }

    public void setGx_sszy(String gx_sszy) {
        this.gx_sszy = gx_sszy;
    }

    public Integer getGx_bsd() {
        return gx_bsd;
    }

    public void setGx_bsd(Integer gx_bsd) {
        this.gx_bsd = gx_bsd;
    }

    public String getGx_bszy() {
        return gx_bszy;
    }

    public void setGx_bszy(String gx_bszy) {
        this.gx_bszy = gx_bszy;
    }

    public Double getGx_byl2023() {
        return gx_byl2023;
    }

    public void setGx_byl2023(Double gx_byl2023) {
        this.gx_byl2023 = gx_byl2023;
    }

    public String getGx_pm() {
        return gx_pm;
    }

    public void setGx_pm(String gx_pm) {
        this.gx_pm = gx_pm;
    }
} 