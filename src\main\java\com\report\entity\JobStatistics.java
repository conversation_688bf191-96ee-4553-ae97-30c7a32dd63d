package com.report.entity;

/**
 * 就业统计数据实体类
 * 用于存储从career_jy_all_2024表查询的就业统计信息
 */
public class JobStatistics {
    
    private String groupName;    // 就业方向/行业分组
    private String lsy;          // 学历要求
    private int count;           // 人数统计
    
    public JobStatistics() {
    }
    
    public JobStatistics(String groupName, String lsy, int count) {
        this.groupName = groupName;
        this.lsy = lsy;
        this.count = count;
    }
    
    // Getters and Setters
    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getLsy() {
        return lsy;
    }

    public void setLsy(String lsy) {
        this.lsy = lsy;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }
    
    @Override
    public String toString() {
        return "JobStatistics{" +
                "groupName='" + groupName + '\'' +
                ", lsy='" + lsy + '\'' +
                ", count=" + count +
                '}';
    }
} 