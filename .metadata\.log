!SESSION 2025-07-14 15:43:30.373 -----------------------------------------------
eclipse.buildId=4.34.0.20241128-0756
java.version=21.0.5
java.vendor=Eclipse Adoptium
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=zh_CN
Framework arguments:  -product org.eclipse.epp.package.jee.product
Command-line arguments:  -os win32 -ws win32 -arch x86_64 -product org.eclipse.epp.package.jee.product

!ENTRY ch.qos.logback.classic 1 0 2025-07-14 15:43:33.142
!MESSAGE Activated before the state location was initialized. Retry after the state location is initialized.

!ENTRY ch.qos.logback.classic 1 0 2025-07-14 15:44:17.635
!MESSAGE Logback config file: D:\WorkSpace\LX2B\zsdwf\.metadata\.plugins\org.eclipse.m2e.logback\logback.2.7.0.20241001-1350.xml

!ENTRY org.eclipse.egit.ui 2 0 2025-07-14 15:44:21.396
!MESSAGE Warning: The environment variable HOME is not set. The following directory will be used to store the Git
user global configuration and to define the default location to store repositories: 'C:\Users\<USER>\WorkSpace\LX2B\zsdwf\.metadata\.plugins\org.eclipse.m2e.logback\logback.2.7.0.20241001-1350.xml

!ENTRY org.eclipse.egit.ui 2 0 2025-07-14 15:46:53.385
!MESSAGE Warning: The environment variable HOME is not set. The following directory will be used to store the Git
user global configuration and to define the default location to store repositories: 'C:\Users\<USER>