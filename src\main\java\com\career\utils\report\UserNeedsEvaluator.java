package com.career.utils.report;

import com.career.db.*;
import com.career.utils.*;
import com.career.utils.report.kit.FormReviewUtils;

import java.util.*;

/**
 * 用户需求评估器
 * 负责基于用户需求数据进行志愿表单的适配性评估
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class UserNeedsEvaluator {
    
    // ==================== 用户需求匹配配置 ====================
    /** 个人适配性A级阈值：90%以上匹配率 */
    public static final double PERSONAL_FITNESS_A = 0.9;
    /** 个人适配性B级阈值：70%以上匹配率 */
    public static final double PERSONAL_FITNESS_B = 0.7;
    /** 个人适配性C级阈值：50%以上匹配率 */
    public static final double PERSONAL_FITNESS_C = 0.5;
    
    /** 经济适配性A级阈值：80%以上可承受 */
    public static final double ECONOMIC_FITNESS_A = 0.8;
    /** 经济适配性B级阈值：60%以上可承受 */
    public static final double ECONOMIC_FITNESS_B = 0.6;
    /** 经济适配性C级阈值：40%以上可承受 */
    public static final double ECONOMIC_FITNESS_C = 0.4;
    
    /** 地域偏好匹配A级阈值：70%以上匹配 */
    public static final double GEOGRAPHIC_PREFERENCE_A = 0.7;
    /** 地域偏好匹配B级阈值：50%以上匹配 */
    public static final double GEOGRAPHIC_PREFERENCE_B = 0.5;
    /** 地域偏好匹配C级阈值：30%以上匹配 */
    public static final double GEOGRAPHIC_PREFERENCE_C = 0.3;
    
    /** 专业偏好精确匹配A级阈值：30%以上精确匹配 */
    public static final double MAJOR_PREFERENCE_EXACT_A = 0.3;
    /** 专业偏好精确匹配B级阈值：10%以上精确匹配 */
    public static final double MAJOR_PREFERENCE_EXACT_B = 0.1;
    /** 专业偏好总匹配A级阈值：60%以上总匹配率 */
    public static final double MAJOR_PREFERENCE_TOTAL_A = 0.6;
    /** 专业偏好总匹配B级阈值：40%以上总匹配率 */
    public static final double MAJOR_PREFERENCE_TOTAL_B = 0.4;
    /** 专业偏好总匹配C级阈值：20%以上总匹配率 */
    public static final double MAJOR_PREFERENCE_TOTAL_C = 0.2;
    /** 专业类别匹配权重：类别匹配的权重系数 */
    public static final double MAJOR_CATEGORY_MATCH_WEIGHT = 0.5;
    
    /** 身体条件适配A级阈值：95%以上适配 */
    public static final double PHYSICAL_FITNESS_A = 0.95;
    /** 身体条件适配B级阈值：85%以上适配 */
    public static final double PHYSICAL_FITNESS_B = 0.85;
    /** 身体条件适配C级阈值：70%以上适配 */
    public static final double PHYSICAL_FITNESS_C = 0.7;
    
    // ==================== 安全性辅助方法 ====================
    
    /**
     * 安全获取字符串，防止空指针
     */
    private static String safeGetString(String value) {
        return value != null ? value.trim() : "";
    }

    
    /**
     * 11. 个人适配性评估
     * 基于用户的个人特征评估志愿填报的适配性
     * 重构后解决了少数民族专项计划覆盖性别限制的问题
     */
    public static String evaluatePersonalFitness(FormReviewEvaluator.FormReviewContext context) {
        Tools.println("[evaluatePersonalFitness] 开始个人适配性评估");
        
        RtFillNeedsInfoBean needsData = context.getUserNeedsData();
        List<SuperForm> superFormList = context.getSuperFormList();
        
        if (needsData == null || superFormList == null || superFormList.isEmpty()) {
            Tools.println("[evaluatePersonalFitness] 用户需求数据或志愿表单为空，返回C级");
            return FormReviewEvaluator.GRADE_C;
        }
        
        String gender = safeGetString(needsData.getRt_gender());
        String ethnicity = safeGetString(needsData.getRt_ethnicity());
        String eligibleSpecialPlans = safeGetString(needsData.getRt_is_eligible_for_special_plans());
        
        Tools.println("[evaluatePersonalFitness] 用户信息 - 性别: " + gender + ", 民族: " + ethnicity + ", 符合专项计划: " + eligibleSpecialPlans);
        
        int matchCount = 0;
        int genderRestrictedCount = 0;
        int minorityBonusCount = 0;
        int totalForms = superFormList.size();
        
        for (SuperForm superForm : superFormList) {
            PersonalFitnessResult fitnessResult = evaluateSingleFormPersonalFitness(
                context, superForm, gender, ethnicity, eligibleSpecialPlans);
            
            if (fitnessResult.isMatch()) {
                matchCount++;
            }
            
            if (fitnessResult.hasGenderRestriction()) {
                genderRestrictedCount++;
            }
            
            if (fitnessResult.hasMinorityBonus()) {
                minorityBonusCount++;
            }
        }
        
        double matchRate = (double) matchCount / totalForms;
        Tools.println("[evaluatePersonalFitness] 匹配统计 - 总志愿: " + totalForms + 
                     ", 匹配: " + matchCount + ", 性别限制: " + genderRestrictedCount + 
                     ", 少数民族加分: " + minorityBonusCount + ", 匹配率: " + matchRate);
        
        String grade = determinePersonalFitnessGrade(matchRate);
        
        Tools.println("[evaluatePersonalFitness] 个人适配性评估结果: " + grade + 
                     " (阈值A:" + PERSONAL_FITNESS_A + ", B:" + PERSONAL_FITNESS_B + ", C:" + PERSONAL_FITNESS_C + ")");
        return grade;
    }
    
    
    /**
     * 12. 经济适配性评估
     * 基于用户经济条件评估院校学费的适配性
     */
    public static String evaluateEconomicFitness(FormReviewEvaluator.FormReviewContext context) {
        Tools.println("[evaluateEconomicFitness] 开始经济适配性评估");
        
        RtFillNeedsInfoBean needsData = context.getUserNeedsData();
        List<SuperForm> superFormList = context.getSuperFormList();
        
        if (needsData == null || superFormList == null || superFormList.isEmpty()) {
            Tools.println("[evaluateEconomicFitness] 用户需求数据或志愿表单为空，返回C级");
            return FormReviewEvaluator.GRADE_C;
        }

        String budget = safeGetString(needsData.getRt_university_budget());
        String acceptSinoForeign = safeGetString(needsData.getRt_accept_sino_foreign());
        String acceptPrivate = safeGetString(needsData.getRt_accept_private());
        
        Tools.println("[evaluateEconomicFitness] 经济条件 - 预算: " + budget + ", 接受中外合作: " + acceptSinoForeign + ", 接受民办: " + acceptPrivate);
        
        int affordableCount = 0;
        int totalCount = 0;
        
        for (SuperForm superForm : superFormList) {
            List<JHBean> selectedPlanData = context.getSelectedMajorPlanData(superForm.getYxdm(), superForm.getZyz(), Tools.trim(superForm.getZydm()));
            for (JHBean bean : selectedPlanData) {
                totalCount++;
                
                String fee = bean.getFee();
                String collegeType = FormReviewUtils.getCollegeType(bean);
                
                boolean isAffordable = FormReviewUtils.isAffordable(fee, budget, collegeType, acceptSinoForeign, acceptPrivate);
                if (isAffordable) {
                    affordableCount++;
                } else {
                    // Tools.println("[evaluateEconomicFitness] 院校 " + bean.getYxmc() + " 专业 " + bean.getZymc_org() + " 学费 " + fee + " 类型 " + collegeType + " 超出预算");
                }
            }
        }
        
        if (totalCount == 0) {
            Tools.println("[evaluateEconomicFitness] 无有效院校专业数据，返回C级");
            return FormReviewEvaluator.GRADE_C;
        }
        
        double affordableRate = (double) affordableCount / totalCount;
        Tools.println("[evaluateEconomicFitness] 可承受院校专业数: " + affordableCount + "/" + totalCount + ", 可承受率: " + affordableRate);
        
        String grade;
        if (affordableRate >= ECONOMIC_FITNESS_A) {
            grade = FormReviewEvaluator.GRADE_A;
        } else if (affordableRate >= ECONOMIC_FITNESS_B) {
            grade = FormReviewEvaluator.GRADE_B;
        } else if (affordableRate >= ECONOMIC_FITNESS_C) {
            grade = FormReviewEvaluator.GRADE_C;
        } else {
            grade = FormReviewEvaluator.GRADE_D;
        }
        
        Tools.println("[evaluateEconomicFitness] 经济适配性评估结果: " + grade + " (阈值A:" + ECONOMIC_FITNESS_A + ", B:" + ECONOMIC_FITNESS_B + ", C:" + ECONOMIC_FITNESS_C + ")");
        return grade;
    }
    
    /**
     * 13. 地域偏好匹配度评估
     * 基于用户意向城市和省份评估志愿填报的地域匹配度
     */
    public static String evaluateGeographicPreference(FormReviewEvaluator.FormReviewContext context) {
        Tools.println("[evaluateGeographicPreference] 开始地域偏好匹配度评估");
        
        RtFillNeedsInfoBean needsData = context.getUserNeedsData();
        List<SuperForm> superFormList = context.getSuperFormList();
        
        if (needsData == null || superFormList == null || superFormList.isEmpty()) {
            Tools.println("[evaluateGeographicPreference] 用户需求数据或志愿表单为空，返回C级");
            return FormReviewEvaluator.GRADE_C;
        }
        
        Set<String> preferredCities = FormReviewUtils.parseJsonStringToSet(needsData.getRt_preferred_cities());
        Set<String> preferredProvinces = FormReviewUtils.parseJsonStringToSet(needsData.getRt_preferred_provinces());
        
        Tools.println("[evaluateGeographicPreference] 偏好城市: " + preferredCities + ", 偏好省份: " + preferredProvinces);
        
        if (preferredCities.isEmpty() && preferredProvinces.isEmpty()) {
            Tools.println("[evaluateGeographicPreference] 无地域偏好信息，返回C级");
            return FormReviewEvaluator.GRADE_C; // 没有偏好信息
        }
        
        int matchCount = 0;
        int totalCount = superFormList.size();
        
        for (SuperForm superForm : superFormList) {
            String universityProvince = FormReviewUtils.getUniversityProvince(superForm);
            String universityCity = FormReviewUtils.getUniversityCity(superForm);
            
            boolean isMatch = false;
            String matchReason = "";
            
            // 检查城市偏好
            if (!preferredCities.isEmpty() && !Tools.isEmpty(universityCity)) {
                for (String preferredCity : preferredCities) {
                    if (universityCity.contains(preferredCity) || preferredCity.contains(universityCity)) {
                        isMatch = true;
                        matchReason = "城市匹配: " + preferredCity;
                        break;
                    }
                }
            }
            
            // 检查省份偏好
            if (!isMatch && !preferredProvinces.isEmpty() && !Tools.isEmpty(universityProvince)) {
                for (String preferredProvince : preferredProvinces) {
                    if (universityProvince.contains(preferredProvince) || preferredProvince.contains(universityProvince)) {
                        isMatch = true;
                        matchReason = "省份匹配: " + preferredProvince;
                        break;
                    }
                }
            }
            
            if (isMatch) {
                matchCount++;
                // Tools.println("[evaluateGeographicPreference] 院校 " + superForm.getYxdm() + " 地域匹配: " + matchReason);
            }
        }
        
        double matchRate = (double) matchCount / totalCount;
        Tools.println("[evaluateGeographicPreference] 地域匹配院校数: " + matchCount + "/" + totalCount + ", 匹配率: " + matchRate);
        
        String grade;
        if (matchRate >= GEOGRAPHIC_PREFERENCE_A) {
            grade = FormReviewEvaluator.GRADE_A;
        } else if (matchRate >= GEOGRAPHIC_PREFERENCE_B) {
            grade = FormReviewEvaluator.GRADE_B;
        } else if (matchRate >= GEOGRAPHIC_PREFERENCE_C) {
            grade = FormReviewEvaluator.GRADE_C;
        } else {
            grade = FormReviewEvaluator.GRADE_D;
        }
        
        Tools.println("[evaluateGeographicPreference] 地域偏好匹配评估结果: " + grade + " (阈值A:" + GEOGRAPHIC_PREFERENCE_A + ", B:" + GEOGRAPHIC_PREFERENCE_B + ", C:" + GEOGRAPHIC_PREFERENCE_C + ")");
        return grade;
    }
    
    /**
     * 14. 专业偏好匹配度评估 - 重构版本
     * 🎯 核心修复：基于用户实际选择的专业进行偏好匹配分析，而非整个专业组
     * 
     * 评估逻辑：
     * 1. 优先使用用户选择专业数据（精准分析）
     * 2. 降级使用志愿表单数据（基础分析）
     */
    public static String evaluateMajorPreference(FormReviewEvaluator.FormReviewContext context) {
        Tools.println("[evaluateMajorPreference] 🎯 开始专业偏好匹配度评估 - 重构版本");
        
        RtFillNeedsInfoBean needsData = context.getUserNeedsData();
        List<SuperForm> superFormList = context.getSuperFormList();
        
        if (needsData == null || superFormList == null || superFormList.isEmpty()) {
            Tools.println("[evaluateMajorPreference] 用户需求数据或志愿表单为空，返回C级");
            return FormReviewEvaluator.GRADE_C;
        }
        
        Set<String> preferredMajors = FormReviewUtils.parseJsonStringToSet(needsData.getRt_preferred_majors());
        String majorCategory = needsData.getRt_major_category();
        String majorSubcategory = needsData.getRt_major_subcategory();
        
        Tools.println("[evaluateMajorPreference] 偏好专业: " + preferredMajors + ", 专业类别: " + majorCategory + ", 专业子类: " + majorSubcategory);
        
        if (preferredMajors.isEmpty() && Tools.isEmpty(majorCategory)) {
            Tools.println("[evaluateMajorPreference] 无专业偏好信息，返回C级");
            return FormReviewEvaluator.GRADE_C; // 没有专业偏好信息
        }
        
        int exactMatchCount = 0;  // 精确匹配
        int categoryMatchCount = 0; // 类别匹配
        int totalCount = 0;
        String dataSource = "";
        
        dataSource = "志愿表单数据";
        totalCount = superFormList.size();
//        Tools.println("[evaluateMajorPreference] ⚠️ 降级使用志愿表单数据进行分析，数量: " + totalCount);
        
        for (SuperForm superForm : superFormList) {
            if (Tools.isEmpty(superForm.getZymc_org())) continue;
            
            String majorName = superForm.getZymc_org();
            boolean exactMatch = false;
            boolean categoryMatch = false;
            
            // 检查精确专业匹配
            if (!preferredMajors.isEmpty()) {
                for (String preferredMajor : preferredMajors) {
                    if (majorName.contains(preferredMajor) || preferredMajor.contains(majorName)) {
                        exactMatch = true;
                        // Tools.println("[evaluateMajorPreference] 专业 " + majorName + " 精确匹配偏好 " + preferredMajor);
                        break;
                    }
                }
            }
            // 检查专业类别匹配
            if (!exactMatch && !Tools.isEmpty(majorCategory)) {
                String actualCategory = FormReviewUtils.getMajorCategory(superForm, context.getSuperFormMain().getPc_code());
                if (!Tools.isEmpty(actualCategory)) {
                    if (actualCategory.contains(majorCategory) || majorCategory.contains(actualCategory)) {
                        categoryMatch = true;
                        // Tools.println("[evaluateMajorPreference] 专业 " + majorName + " 类别匹配: " + actualCategory + " <-> " + majorCategory);
                    }
                }
            }
            
            if (exactMatch) {
                exactMatchCount++;
            } else if (categoryMatch) {
                categoryMatchCount++;
            }
        }
        
        double exactMatchRate = (double) exactMatchCount / totalCount;
        double categoryMatchRate = (double) categoryMatchCount / totalCount;
        double totalMatchRate = exactMatchRate + categoryMatchRate * MAJOR_CATEGORY_MATCH_WEIGHT; // 类别匹配权重较低
        
//        Tools.println("[evaluateMajorPreference] 🎯 数据源: " + dataSource);
//        Tools.println("[evaluateMajorPreference] 匹配统计 - 精确匹配: " + exactMatchCount + "(" + exactMatchRate + "), 类别匹配: " + categoryMatchCount + "(" + categoryMatchRate + "), 总匹配率: " + totalMatchRate);
        
        String grade;
        if (exactMatchRate >= MAJOR_PREFERENCE_EXACT_A || totalMatchRate >= MAJOR_PREFERENCE_TOTAL_A) {
            grade = FormReviewEvaluator.GRADE_A;
        } else if (exactMatchRate >= MAJOR_PREFERENCE_EXACT_B || totalMatchRate >= MAJOR_PREFERENCE_TOTAL_B) {
            grade = FormReviewEvaluator.GRADE_B;
        } else if (totalMatchRate >= MAJOR_PREFERENCE_TOTAL_C) {
            grade = FormReviewEvaluator.GRADE_C;
        } else {
            grade = FormReviewEvaluator.GRADE_D;
        }
        
        Tools.println("[evaluateMajorPreference] 专业偏好匹配评估结果: " + grade + " (精确匹配阈值A:" + MAJOR_PREFERENCE_EXACT_A + ", B:" + MAJOR_PREFERENCE_EXACT_B + "; 总匹配阈值A:" + MAJOR_PREFERENCE_TOTAL_A + ", B:" + MAJOR_PREFERENCE_TOTAL_B + ", C:" + MAJOR_PREFERENCE_TOTAL_C + ")");
        return grade;
    }
    
    /**
     * 15. 身体条件适配性评估 - 重构版本
     * 🎯 核心修复：基于用户实际选择的专业进行身体条件适配分析，而非整个专业组
     * 
     * 评估逻辑：
     * 1. 优先使用用户选择专业数据（精准分析）
     * 2. 降级使用志愿表单数据（基础分析）
     */
    public static String evaluatePhysicalRequirements(FormReviewEvaluator.FormReviewContext context) { 
        Tools.println("[evaluatePhysicalRequirements] 🎯 开始身体条件适配性评估 - 重构版本");
        
        RtFillNeedsInfoBean needsData = context.getUserNeedsData();
        List<SuperForm> superFormList = context.getSuperFormList();
        
        if (needsData == null || superFormList == null || superFormList.isEmpty()) {
            Tools.println("[evaluatePhysicalRequirements] 用户需求数据或志愿表单为空，返回C级");
            return FormReviewEvaluator.GRADE_C;
        }
        
        String physicalExam = needsData.getRt_physical_exam();
        String height = needsData.getRt_height();
        String bodyType = needsData.getRt_body_type();
        
        Tools.println("[evaluatePhysicalRequirements] 身体条件 - 体检结果: " + physicalExam + ", 身高: " + height + ", 体型: " + bodyType);
        
        int suitableCount = 0;
        int totalCount = 0;
        int restrictionCount = 0;
        String dataSource = "";
        
        dataSource = "志愿表单数据";
        totalCount = superFormList.size();
        Tools.println("[evaluatePhysicalRequirements] ⚠️ 降级使用志愿表单数据进行分析，数量: " + totalCount);
        
        for (SuperForm superForm : superFormList) {
            boolean isSuitable = true;
            String majorName = superForm.getZymc();
            String restrictions = "";
            
            if (!Tools.isEmpty(majorName)) {
                
                // 检查身高要求
                if (!Tools.isEmpty(height) && FormReviewUtils.hasHeightRequirement(majorName, height)) {
                    isSuitable = false;
                    restrictions += "身高要求;";
                }
                
                // 检查色盲色弱限制
                if (!Tools.isEmpty(physicalExam) && FormReviewUtils.hasColorVisionRequirement(majorName, physicalExam)) {
                    isSuitable = false;
                    restrictions += "视力要求;";
                }
                
                // 检查身体条件要求
                if (!Tools.isEmpty(bodyType) && FormReviewUtils.hasPhysicalRequirement(majorName, bodyType)) {
                    isSuitable = false;
                    restrictions += "身体条件;";
                }
                
                if (!isSuitable) {
                    restrictionCount++;
                    // Tools.println("[evaluatePhysicalRequirements] 专业 " + majorName + " 不适合，限制条件: " + restrictions);
                }
            }
            
            if (isSuitable) {
                suitableCount++;
            }
        }
        
        double suitableRate = (double) suitableCount / totalCount;
        Tools.println("[evaluatePhysicalRequirements] 🎯 数据源: " + dataSource);
        Tools.println("[evaluatePhysicalRequirements] 适合的专业数: " + suitableCount + "/" + totalCount + ", 适配率: " + suitableRate + ", 受限专业数: " + restrictionCount);
        
        String grade;
        if (suitableRate >= PHYSICAL_FITNESS_A) {
            grade = FormReviewEvaluator.GRADE_A;
        } else if (suitableRate >= PHYSICAL_FITNESS_B) {
            grade = FormReviewEvaluator.GRADE_B;
        } else if (suitableRate >= PHYSICAL_FITNESS_C) {
            grade = FormReviewEvaluator.GRADE_C;
        } else {
            grade = FormReviewEvaluator.GRADE_D;
        }
        
        Tools.println("[evaluatePhysicalRequirements] 身体条件适配性评估结果: " + grade + " (阈值A:" + PHYSICAL_FITNESS_A + ", B:" + PHYSICAL_FITNESS_B + ", C:" + PHYSICAL_FITNESS_C + ")");
        return grade;
    }
    
    
    

    /**
     * 评估单个志愿表单的个人适配性
     * @param context 评估上下文
     * @param superForm 志愿表单
     * @param gender 用户性别
     * @param ethnicity 用户民族
     * @param eligibleSpecialPlans 是否符合专项计划
     * @return 个人适配性评估结果
     */
    private static PersonalFitnessResult evaluateSingleFormPersonalFitness(
            FormReviewEvaluator.FormReviewContext context, SuperForm superForm, 
            String gender, String ethnicity, String eligibleSpecialPlans) {
        
        PersonalFitnessResult result = new PersonalFitnessResult();
        
        // 1. 检查硬性性别限制（不可逾越）
        if (!Tools.isEmpty(gender)) {
            List<JHBean> planData = context.getPlanData(superForm.getYxdm(), superForm.getZyz());
            boolean hasGenderRestriction = false;
            
            for (JHBean bean : planData) {
                String majorName = bean.getZymc_org();
                if (!Tools.isEmpty(majorName)) {
                    if (FormReviewUtils.hasGenderRestriction(majorName, gender)) {
                        hasGenderRestriction = true;
                        result.setHasGenderRestriction(true);
                        // Tools.println("[evaluatePersonalFitness] 院校 " + superForm.getYxdm() + 
                        //              " 专业组 " + superForm.getZyz() + " 中专业 " + majorName + 
                        //              " 存在性别限制，用户性别: " + gender);
                        break;
                    }
                }
            }
            
            // 如果存在硬性性别限制，直接标记为不匹配
            if (hasGenderRestriction) {
                result.setMatch(false);
                return result;
            }
        }
        
        // 2. 检查少数民族专项计划加分（仅在没有硬性限制的情况下生效）
        boolean hasMinorityBonus = false;
        if (!Tools.isEmpty(ethnicity) && "是".equals(eligibleSpecialPlans) && !"汉族".equals(ethnicity)) {
            // 检查当前志愿是否属于少数民族专项计划
            if (isMinoritySpecialPlan(context, superForm)) {
                hasMinorityBonus = true;
                result.setHasMinorityBonus(true);
                // Tools.println("[evaluatePersonalFitness] 院校 " + superForm.getYxdm() + 
                //              " 专业组 " + superForm.getZyz() + " 属于少数民族专项计划，用户民族: " + ethnicity);
            }
        }
        
        // 3. 最终匹配判断
        result.setMatch(true); // 没有硬性限制则认为匹配
        
        return result;
    }
    
    
    /**
     * 检查志愿是否属于少数民族专项计划
     * @param context 评估上下文
     * @param superForm 志愿表单
     * @return 是否属于少数民族专项计划
     */
    private static boolean isMinoritySpecialPlan(FormReviewEvaluator.FormReviewContext context, SuperForm superForm) {

        List<JHBean> planData = context.getPlanData(superForm.getYxdm(), superForm.getZyz());
        for (JHBean bean : planData) {
            // 检查计划备注或其他字段是否包含少数民族专项计划标识
            // 院校名称 + 专业名称 + 专业备注
        	String remark = bean.getYxmc() + bean.getZymc() + bean.getZybz() ;
            
            if (!Tools.isEmpty(remark) && 
                (remark.contains("少数民族") || remark.contains("民族专项") || remark.contains("民族班") || remark.contains("定向"))) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 根据匹配率确定个人适配性等级
     * @param matchRate 匹配率
     * @return 适配性等级
     */
    private static String determinePersonalFitnessGrade(double matchRate) {
        if (matchRate >= PERSONAL_FITNESS_A) {
            return FormReviewEvaluator.GRADE_A;
        } else if (matchRate >= PERSONAL_FITNESS_B) {
            return FormReviewEvaluator.GRADE_B;
        } else if (matchRate >= PERSONAL_FITNESS_C) {
            return FormReviewEvaluator.GRADE_C;
        } else {
            return FormReviewEvaluator.GRADE_D;
        }
    }
    
    /**
     * 个人适配性评估结果内部类
     */
    private static class PersonalFitnessResult {
        private boolean isMatch = false;
        private boolean hasGenderRestriction = false;
        private boolean hasMinorityBonus = false;
        
        public boolean isMatch() { return isMatch; }
        public void setMatch(boolean match) { this.isMatch = match; }
        
        public boolean hasGenderRestriction() { return hasGenderRestriction; }
        public void setHasGenderRestriction(boolean hasGenderRestriction) { 
            this.hasGenderRestriction = hasGenderRestriction; 
        }
        
        public boolean hasMinorityBonus() { return hasMinorityBonus; }
        public void setHasMinorityBonus(boolean hasMinorityBonus) { 
            this.hasMinorityBonus = hasMinorityBonus; 
        }
    }
} 