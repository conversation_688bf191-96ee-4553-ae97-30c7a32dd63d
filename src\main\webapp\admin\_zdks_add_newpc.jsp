<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ page import="com.zsdwf.utils.*,com.career.db.*,com.zsdwf.db.*,com.career.utils.*,java.util.*,com.career.utils.Tools,com.career.db.DealZdksSQL,com.career.db.CityBean,com.zsdwf.db.JDBC"%>

<%@include file="/WEB-INF/include/_session_admin_ajax.jsp"%>
<%
String newpc = Tools.trim(request.getParameter("newpc"));
String prov = Tools.trim(request.getParameter("prov"));

if(Tools.isEmpty(newpc) || Tools.isEmpty(prov)){
	out.print("ERR:LOGIN:REDIRECT-X_LOGIN");
	return;
}

com.career.db.ZyzdJDBC jdbc = new com.career.db.ZyzdJDBC();
ZDPCBean bean = jdbc.getLatestZdksProvPC(prov); 
if(bean == null){
	out.print("ERR:LOGIN:REDIRECT-X_LOGIN");
	return;
}

bean.setZdpc(newpc);
bean.setSort(bean.getSort()+1);
jdbc.insertZdksPc(bean);
%> 




