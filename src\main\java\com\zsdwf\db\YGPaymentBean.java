package com.zsdwf.db;

import java.util.Date;

public class YGPaymentBean {
	
	private String id;
	private String openId;
	private String prepayId;
	private Date createTm;
	private Date paidTm;
	private int status;
	private int amt;
	private int item;
	private String itemDesc;
	private String c_id_org;
	private String agent_id_org;
	private String shared_id_org;
	private String cardId;
	private String cardPass;
	private String phone;
	
	
	public String getShared_id_org() {
		return shared_id_org;
	}
	public void setShared_id_org(String shared_id_org) {
		this.shared_id_org = shared_id_org;
	}
	public String getAgent_id_org() {
		return agent_id_org;
	}
	public void setAgent_id_org(String agent_id_org) {
		this.agent_id_org = agent_id_org;
	}
	public String getItemDesc() {
		return itemDesc;
	}
	public void setItemDesc(String itemDesc) {
		this.itemDesc = itemDesc;
	}
	public String getC_id_org() {
		return c_id_org;
	}
	public void setC_id_org(String c_id_org) {
		this.c_id_org = c_id_org;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getCardId() {
		return cardId;
	}
	public void setCardId(String cardId) {
		this.cardId = cardId;
	}
	public String getCardPass() {
		return cardPass;
	}
	public void setCardPass(String cardPass) {
		this.cardPass = cardPass;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getOpenId() {
		return openId;
	}
	public void setOpenId(String openId) {
		this.openId = openId;
	}
	public String getPrepayId() {
		return prepayId;
	}
	public void setPrepayId(String prepayId) {
		this.prepayId = prepayId;
	}
	public Date getCreateTm() {
		return createTm;
	}
	public void setCreateTm(Date createTm) {
		this.createTm = createTm;
	}
	public Date getPaidTm() {
		return paidTm;
	}
	public void setPaidTm(Date paidTm) {
		this.paidTm = paidTm;
	}
	public int getStatus() {
		return status;
	}
	public void setStatus(int status) {
		this.status = status;
	}
	public int getAmt() {
		return amt;
	}
	public void setAmt(int amt) {
		this.amt = amt;
	}
	public int getItem() {
		return item;
	}
	public void setItem(int item) {
		this.item = item;
	}

}
