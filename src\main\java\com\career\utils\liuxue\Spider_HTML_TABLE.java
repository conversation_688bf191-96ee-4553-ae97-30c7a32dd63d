package com.career.utils.liuxue;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

public class Spider_HTML_TABLE {

	// 针对HTML中嵌入表格的网站抓取(兵器装备集团)
	public static StringBuffer csgs(String URL, int pageNumber) {
		Map<String, String> headers = new HashMap<>();
		StringBuffer SQL = new StringBuffer();

		System.out.println(URL);
		
		try {
			BufferedReader br = new BufferedReader(new FileReader("C:\\Users\\<USER>\\Desktop\\新媒体 必选\\X.txt"));
			String b = null;
			while((b = br.readLine()) != null) {
				SQL.append(b);
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		String resultPageList = SQL.toString();//HttpSendUtils2.get(URL, headers);
		// System.out.println(resultPageList);
		Document documentList = Jsoup.parse(resultPageList);

		//Elements MsoNormalTable = documentList.getElementsByClass("c_l_c_06_5");

		Elements tr = documentList.getElementsByTag("div");
		SQL = new StringBuffer(); 
		boolean start = false;
		for (int i = 0; i < tr.size(); i++) {
			Element item = tr.get(i);
			
			if(item.text().trim().indexOf("：") != -1) {
				System.out.println(item.text());
				String[] nx = item.text().split("：");
				String yx = nx[0];
				String zys = nx[1];
				String[] zy = zys.split("、");
				
				for(int k = 0;k<zy.length;k++) {
					SQL.append("insert into TMP_XD(yxmc, zymc, pc, sf, nf) values('"+yx+"','"+zy[k]+"','第二批', '山东省', 2025);\r\n");
				}
				
				
			}
		}
		return SQL;
	}

	public static void main(String[] args) {
		StringBuffer SQL = csgs("https://www.chinagwy.org/html/gdzk/shandong/202410/67_650608.html", 1);
		
		writeTempFile(new File("F://就业报告//CQ/PAGE_0906222.txt"), SQL);
	}
	
	private static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
}
