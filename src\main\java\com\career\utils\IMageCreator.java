package com.career.utils;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import javax.imageio.ImageIO;

import com.career.db.CareerJY;
import com.career.db.ZyzdJDBC;
import com.career.db.ZyzdUniversityBean;
import com.career.utils.Tools;

import java.awt.image.BufferedImage;
import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.image.WritableRaster;
import java.io.File;
import java.io.IOException;

public class IMageCreator {
	
	private static int getStrLength(Graphics g, String str) {
		char[] strChar = str.toCharArray();
		int strWith = g.getFontMetrics().charsWidth(strChar, 0, strChar.length);
		return strWith;
	}
	
	public static void main(String args[]) {
		ZyzdJDBC jdbc = new ZyzdJDBC();
		String group_name = "国家电网";
		String lsy = "国网河北省电力有限公司";
		int jynf = 2024;
		int yxnf = 2024;
		String provTable = "S5_SC";
		
		try {
			//BufferedImage image_one = ImageIO.read(new File("/Users/<USER>/Downloads/4fe6710f614ec22fae45da0dd251e822.jpeg"));
			BufferedImage image_one = new BufferedImage(1200,2000,BufferedImage.TYPE_INT_RGB);
			int width = image_one.getWidth();
			int height = image_one.getHeight();
			
			System.out.println(width);
			System.out.println(height);
			Graphics2D g2 = image_one.createGraphics();
			g2.setColor(Color.white);
			g2.fillRect(0, 0, width, height);
			int tableXinit = 50;
			int tableYinit = 50;
			int heightPerOneLine = 40;
			
			String tilte = String.valueOf(group_name+"（"+lsy+"）"+jynf+"年拟聘统计");
			
			Font font0 = new Font("黑体",Font.BOLD,18);
			g2.setColor(Color.DARK_GRAY);
			g2.setFont(font0);
			
			int titleLength = getStrLength(g2, tilte);
			int titleX = width/2 - titleLength/2;
			
			g2.drawString(tilte, titleX, tableYinit);
			
			tableYinit += 50;
			
			///header
			Font header_font1 = new Font("黑体",Font.BOLD,14);
			g2.setColor(Color.DARK_GRAY);
			g2.setFont(header_font1);
			g2.drawString("院校名称", tableXinit, tableYinit);
			
			tableXinit += 225;
			Font header_font2 = new Font("黑体",Font.BOLD,14);
			g2.setColor(Color.DARK_GRAY);
			g2.setFont(header_font2);
			g2.drawString("拟聘", tableXinit, tableYinit);


			tableXinit += 70;
			Font header_font3 = new Font("黑体",Font.BOLD,14);
			g2.setColor(Color.DARK_GRAY);
			g2.setFont(header_font3);
			g2.drawString(String.valueOf(yxnf)+"理科", tableXinit, tableYinit);

			tableXinit += 30;
			
			tableXinit += 60;
			Font header_font5 = new Font("黑体",Font.BOLD,14);
			g2.setColor(Color.DARK_GRAY);
			g2.setFont(header_font5);
			g2.drawString(String.valueOf(yxnf)+"文科", tableXinit, tableYinit);

			tableXinit += 30;
			
			tableXinit += 60;
			Font header_font7 = new Font("黑体",Font.BOLD,14);
			g2.setColor(Color.DARK_GRAY);
			g2.setFont(header_font7);
			g2.drawString("王牌专业", tableXinit, tableYinit);
			
			
			g2.drawLine(50, tableYinit+10, width - 100, tableYinit+10);
			tableYinit += heightPerOneLine;
			
			////
			
			List<CareerJY> list = jdbc.pickJiuyeYxByGroupAndLsy(jynf, group_name, lsy, 1);
			HashSet<String> yxmcSets = new HashSet<>();
			for(CareerJY vo : list) {
				yxmcSets.add(vo.getYxmc());
			}

			HashMap<String, CareerJY> wlVOMap = jdbc.getScore(provTable,yxnf,yxmcSets, "1");
			HashMap<String, CareerJY> lsVOMap = jdbc.getScore(provTable,yxnf,yxmcSets, "W");
			HashMap<String, CareerJY> rkVOMap = jdbc.getRk(yxmcSets);
			
			for(CareerJY vo : list) {
				ZyzdUniversityBean school = ZyzdCache.getUniversity(vo.getYxmc());
				String honor = "";
				if(school != null) {
					if(!Tools.isEmpty(school.getIssyl())) {
						honor = "双一流";
					}
					if(!Tools.isEmpty(school.getIs211())) {
						honor = "211";
					}
					if(!Tools.isEmpty(school.getIs985())) {
						honor = "985";
					}
				}
				
				CareerJY wlVO = wlVOMap.get(vo.getYxmc());
				wlVO = (wlVO == null ? new CareerJY():wlVO);
				CareerJY lsVO = lsVOMap.get(vo.getYxmc());
				lsVO = (lsVO == null ? new CareerJY():lsVO);
				CareerJY rkVO = rkVOMap.get(vo.getYxmc());
				rkVO = (rkVO == null ? new CareerJY():rkVO);
				
				tableXinit = 50;
				heightPerOneLine = 40;
				
				Font font1 = new Font("黑体",Font.PLAIN,18);
				g2.setColor(Color.BLACK);
				g2.setFont(font1);
				if(vo.getYxmc().length() > 12) {
					heightPerOneLine = 60;
					g2.drawString(vo.getYxmc().substring(0,10), tableXinit, tableYinit);
					g2.drawString(vo.getYxmc().substring(10), tableXinit, tableYinit + heightPerOneLine/2);
				}else {
					g2.drawString(vo.getYxmc(), tableXinit, tableYinit);
					if(!Tools.isEmpty(honor)) {
						int len = getStrLength(g2, vo.getYxmc());
						Font font1_1 = new Font("黑体",Font.PLAIN,12);
						g2.setColor(Color.gray);
						g2.setFont(font1_1);
						g2.drawString(honor, tableXinit + len + 5, tableYinit);
					}
				}
				
				tableXinit += 225;
				Font font2 = new Font("黑体",Font.PLAIN,14);
				g2.setColor(Color.red);
				g2.setFont(font2);
				g2.drawString(String.valueOf(vo.getCnt())+"人", tableXinit, tableYinit);


				tableXinit += 70;
				Font font3 = new Font("黑体",Font.BOLD,16);
				g2.setColor(Color.DARK_GRAY);
				g2.setFont(font3);
				if(Tools.getInt(wlVO.getExt_zdf()) < 10) {
					g2.drawString(String.valueOf("--"), tableXinit, tableYinit);
				}else {
					g2.drawString(String.valueOf(Tools.getInt(wlVO.getExt_zdf())), tableXinit, tableYinit);
				}

				tableXinit += 30;
				Font font4 = new Font("黑体",Font.PLAIN,14);
				g2.setColor(Color.gray);
				g2.setFont(font4);
				if(Tools.getInt(wlVO.getExt_zdf()) < 10) {
					
				}else {
					g2.drawString(wlVO.getExt_zdf(), tableXinit, tableYinit);
				}
				
				tableXinit += 60;
				Font font5 = new Font("黑体",Font.BOLD,16);
				g2.setColor(Color.DARK_GRAY);
				g2.setFont(font5);
				if(Tools.getInt(lsVO.getExt_zdf()) < 10) {
					g2.drawString(String.valueOf("--"), tableXinit, tableYinit);
				}else {
					g2.drawString(String.valueOf(Tools.getInt(lsVO.getExt_zdf())), tableXinit, tableYinit);
				}
				

				tableXinit += 30;
				Font font6 = new Font("黑体",Font.PLAIN,14);
				g2.setColor(Color.gray);
				g2.setFont(font6);
				if(Tools.getInt(lsVO.getExt_zdf()) < 10) {
					
				}else {
					g2.drawString(lsVO.getExt_zdf(), tableXinit, tableYinit);
				}

				tableXinit += 60;
				Font font7 = new Font("黑体",Font.PLAIN,18);
				g2.setColor(Color.BLUE);
				g2.setFont(font7);
				String zymc = String.valueOf(Tools.viewForLimitLength(rkVO.getExt_rk_zymc(), 9));
				int zymcLength = getStrLength(g2, zymc);
				if(Tools.isEmpty(rkVO.getExt_rk_zymc())) {
					g2.drawString("--", tableXinit, tableYinit);
				}else {
					g2.drawString(zymc, tableXinit, tableYinit);
				}
				
				tableXinit += zymcLength + 5;
				Font font9 = new Font("黑体",Font.PLAIN,14);
				g2.setColor(Color.gray);
				g2.setFont(font9);
				if(Tools.isEmpty(rkVO.getExt_rk_zymc())) {
					
				}else {
					g2.drawString(String.valueOf(rkVO.getExt_rk_level()), tableXinit, tableYinit);
				}
				
				if(heightPerOneLine == 60) {
					g2.drawLine(50, tableYinit+heightPerOneLine/2+10, width - 100, tableYinit+heightPerOneLine/2+10);
					tableYinit += heightPerOneLine+10;
				}else {
					g2.drawLine(50, tableYinit+10, width - 100, tableYinit+10);
					tableYinit += heightPerOneLine;
				}
				
				
				//System.out.println(vo.getYxmc()+"->"+vo.getCount()+" >> " + Tools.getInt(wlVO.getExt_zdf())+","+wlVO.getZdfwc()+" | " +lsVO.getZdf()+","+lsVO.getZdfwc());
			}
			
			g2.dispose();
			ImageIO.write(image_one, "jpeg", new File("D:\\zd\\t2.jpg"));
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		/**
		
		*/
	}

}
