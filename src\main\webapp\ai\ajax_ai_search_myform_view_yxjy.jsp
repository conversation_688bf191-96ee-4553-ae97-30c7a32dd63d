<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ page import="com.career.utils.*,com.career.db.*,java.util.*"%>

<%

ZyzdJDBC zyzdJDBC = new ZyzdJDBC();

String selected_dwmc = Tools.trim(request.getParameter("selected_dwmc"));
String selected_group = Tools.trim(request.getParameter("selected_group"));
String selected_yxmc_org = Tools.trim(request.getParameter("selected_yxmc_org"));

List<CareerJY> jiuyeList = zyzdJDBC.pickJiuyeYxByDW(selected_group, selected_dwmc, 1);

HashMap<String, ZyzdFollow> HM_FOLLOW_YX = (HashMap<String, ZyzdFollow>)session.getAttribute(ZyzdCache.SES_KEY_FOLLOW_YX);
%>

<div class="card-body" style="padding:0px;">
  <div class="table-responsive">
      <table class="table text-wrap table-bordered border-primary">
          <thead>
			<tr style="background-color:#cfe2ff"> 
			    <th scope="col" class="text-center" style="width:70px;">
			        <i class="bi bi-list-ol me-1"></i>排序
			    </th>
			    <th scope="col">
			        <i class="bi bi-bank2 me-1 text-primary"></i>院校名称
			    </th>
			    <th scope="col">
			        <i class="bi bi-people-fill me-1 text-info"></i>规模
			    </th>
			</tr>
          </thead>
          <tbody>
          		<%
				for(int i=0;i<jiuyeList.size();i++){ 
					CareerJY element = jiuyeList.get(i);
					int cnt = element.getCnt();
					String COUNT = "<b style='font-size:12px;color:gray;'>D</b>";
					if(cnt > 9 && cnt < 100){
						COUNT = "<b style='font-size:12px;color:#000;'>C</b>";
					}else if(cnt >= 100 & cnt < 1000){
						COUNT = "<b style='font-size:12px;color:blue;'>B</b>";
					}else if(cnt >= 1000){
						COUNT = "<b style='font-size:12px;color:red;'>A</b>";
					}
				%>
              <tr <%=element.getYxmc().equals(selected_yxmc_org) ? "style='background-color:#B0C4DE'" : "" %>>
				<td class="text-center align-middle">
				    <span class="badge rounded-circle bg-info text-white d-inline-flex justify-content-center align-items-center"
				          style="width:1.5rem; height:1.5rem; font-size:1rem;">
				        <%= (i + 1) %>
				    </span>
				</td>
    			  <td>
    			  	  <%=Tools.viewForLimitLength(element.getYxmc(), 20) %>
                  </td>
                  <td>
                  	  <%=COUNT %>
                  </td>
                  
              </tr>
              <%} %>
            </tbody>
        </table>
    </div>
</div>



