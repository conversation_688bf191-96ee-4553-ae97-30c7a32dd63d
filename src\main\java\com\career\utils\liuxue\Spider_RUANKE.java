package com.career.utils.liuxue;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.UUID;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.career.db.JDBC;
import com.career.utils.HttpSendUtils;
import com.career.utils.Tools;

public class Spider_RUANKE {
	
	// 重庆人事考试网抓取网页
		public static StringBuffer shanghairanking_cn_ZHUANYE() {
			Map<String, String> headers = new HashMap<>();
			StringBuffer SQL = new StringBuffer();


			String resultPageList = HttpSendUtils2.get("https://www.shanghairanking.cn/api/pub/v1/bcmr/rank?year=2024&majorCode=080214T", headers);
			// System.out.println(resultPageList);
			JSONObject object = JSONObject.parseObject(resultPageList);
			JSONObject JSONObject = object.getJSONObject("data");
			JSONArray JSONArray = JSONObject.getJSONArray("majors");
			for(int i=0;i<JSONArray.size();i++) {
				JSONObject element = JSONArray.getJSONObject(i);
				
				String id = element.getString("id");
				String pid = element.getString("pid");
				String name = element.getString("name");
				String code = element.getString("code");
				int ordNo = element.getIntValue("ordNo");
				String remark = element.getString("remark");
				
				SQL.append(
						"insert into base_major_ruanke(id,pid,name,code,ordno,remark,univPublished,level) values('"+id+"','"
								+ pid + "','" + name + "','" + code + "',"+ordNo+",'"+remark+"',NULL,'1');\r\n");
				
				
				JSONArray newArray = element.getJSONArray("children");
				for(int ix=0;ix<newArray.size();ix++) {
					JSONObject newElement = newArray.getJSONObject(ix);
					
					String id1 = newElement.getString("id");
					String pid1 = newElement.getString("pid");
					String name1 = newElement.getString("name");
					String code1 = newElement.getString("code");
					int ordNo1 = newElement.getIntValue("ordNo");
					String remark1 = newElement.getString("remark");
					
					SQL.append(
							"insert into base_major_ruanke(id,pid,name,code,ordno,remark,univPublished,level) values('"+id1+"','"
									+ pid1 + "','" + name1 + "','" + code1 + "',"+ordNo1+",'"+remark1+"',NULL,'2');\r\n");
					
					JSONArray newArray2 = newElement.getJSONArray("children");
					for(int ixx=0;ixx<newArray2.size();ixx++) {
						JSONObject newElement2 = newArray2.getJSONObject(ixx);
						
						String id2 = newElement2.getString("id");
						String pid2 = newElement2.getString("pid");
						String name2 = newElement2.getString("name");
						String code2 = newElement2.getString("code");
						int ordNo2 = newElement2.getIntValue("ordNo");
						String remark2 = newElement2.getString("remark");
						String univPublished2 = newElement2.getString("univPublished");
						
						SQL.append(
								"insert into base_major_ruanke(id,pid,name,code,ordno,remark,univPublished,level) values('"+id2+"','"
										+ pid2 + "','" + name2 + "','" + code2 + "',"+ordNo2+",'"+remark2+"','"+univPublished2+"','3');\r\n");
					}
				}
				
			}
			
			
			writeTempFile(new File("F://就业报告//RUANKE/PAGE_1015.txt"), SQL);
			
			return SQL;
		}
		
		public static StringBuffer shanghairanking_cn_XUEKE() {
			Map<String, String> headers = new HashMap<>();
			StringBuffer SQL = new StringBuffer();

			String resultPageList = HttpSendUtils2.get("https://www.shanghairanking.cn/_nuxt/static/1728726515/rankings/bcsr/2024/0802/payload.js", headers);
			// System.out.println(resultPageList);
			JSONObject object = JSONObject.parseObject(resultPageList);
			JSONObject JSONObject = object.getJSONObject("data");
			JSONArray JSONArray = JSONObject.getJSONArray("majors");
			for(int i=0;i<JSONArray.size();i++) {
				JSONObject element = JSONArray.getJSONObject(i);
				
				String id = element.getString("id");
				String pid = element.getString("pid");
				String name = element.getString("name");
				String code = element.getString("code");
				int ordNo = element.getIntValue("ordNo");
				String remark = element.getString("remark");
				
				SQL.append(
						"insert into base_major_ruanke(id,pid,name,code,ordno,remark,univPublished,level) values('"+id+"','"
								+ pid + "','" + name + "','" + code + "',"+ordNo+",'"+remark+"',NULL,'1');\r\n");
				
				
				JSONArray newArray = element.getJSONArray("children");
				for(int ix=0;ix<newArray.size();ix++) {
					JSONObject newElement = newArray.getJSONObject(ix);
					
					String id1 = newElement.getString("id");
					String pid1 = newElement.getString("pid");
					String name1 = newElement.getString("name");
					String code1 = newElement.getString("code");
					int ordNo1 = newElement.getIntValue("ordNo");
					String remark1 = newElement.getString("remark");
					
					SQL.append(
							"insert into base_major_ruanke(id,pid,name,code,ordno,remark,univPublished,level) values('"+id1+"','"
									+ pid1 + "','" + name1 + "','" + code1 + "',"+ordNo1+",'"+remark1+"',NULL,'2');\r\n");
					
					JSONArray newArray2 = newElement.getJSONArray("children");
					for(int ixx=0;ixx<newArray2.size();ixx++) {
						JSONObject newElement2 = newArray2.getJSONObject(ixx);
						
						String id2 = newElement2.getString("id");
						String pid2 = newElement2.getString("pid");
						String name2 = newElement2.getString("name");
						String code2 = newElement2.getString("code");
						int ordNo2 = newElement2.getIntValue("ordNo");
						String remark2 = newElement2.getString("remark");
						String univPublished2 = newElement2.getString("univPublished");
						
						SQL.append(
								"insert into base_major_ruanke(id,pid,name,code,ordno,remark,univPublished,level) values('"+id2+"','"
										+ pid2 + "','" + name2 + "','" + code2 + "',"+ordNo2+",'"+remark2+"','"+univPublished2+"','3');\r\n");
					}
				}
				
			}
			
			
			writeTempFile(new File("F://就业报告//RUANKE/PAGE_1015.txt"), SQL);
			
			return SQL;
		}
		
		public static StringBuffer shanghairanking_cn_XUEKE_world(String RS) {
			Map<String, String> headers = new HashMap<>();
			StringBuffer SQL = new StringBuffer();
			try {
				Thread.sleep(200);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			
			String resultPageList = HttpSendUtils2.get("https://www.shanghairanking.cn/_nuxt/static/1733455775/rankings/gras/2024/"+RS+"/payload.js", headers);
			int indexOfPrefix = resultPageList.indexOf("ranking:") - 7;
			int indexOfSubfix = resultPageList.indexOf("return {");
			//System.out.println("RS->"+RS+", indexOfPrefix->"+indexOfPrefix+",indexOfSubfix->"+indexOfSubfix);
			String content = resultPageList.substring(indexOfPrefix, indexOfSubfix);
			
			int varPrefix = resultPageList.indexOf("function(");
			int varSubfix = resultPageList.indexOf("{");
			String varDef = resultPageList.substring(varPrefix+"function(".length(), varSubfix-1);
			System.out.println(varDef);
			
			String variable = content.substring(0,2);
			System.out.println(variable);
			SQL.append("<script>var "+varDef+"; \r\n var " + variable +" = [];\r\n");
			
			SQL.append(content);
			SQL.append("\r\n;</script>");
			
			
			SQL.append("<textarea style=\"width:600px;height:500px;\" id=\"xx\">\r\n"
					+ "	\r\n"
					+ "</textarea>\r\n"
					+ "\r\n"
					+ "<script>\r\n"
					+ "var SQLstr = \"\";\r\n"
					+ "var index = 1;\r\n"
					+ "for(var i=0;i<"+variable+".length;i++){\r\n"
					+ "	var xgx = "+variable+"[i];\r\n"
					+ "	SQLstr += \"insert into lx_ruanke_temp_major_rank(m,x,y,l,e,s,z) values('"+RS+"','\"+xgx.univNameCn+\"','\"+xgx.univCode+\"','\"+xgx.univLogo+\"','\"+xgx.univUpEn+\"','\"+xgx.score+\"',\"+index+++\"); \";\r\n"
					+ "}\r\n"
					+ "document.getElementById(\"xx\").value = SQLstr;\r\n"
					+ "</script>\r\n"
					+ "");
			
			
			writeTempFile(new File("F://就业报告//RUANKE//world_major//PAGE_world_"+RS+".html"), SQL);
			return SQL;
		}
		
		public static StringBuffer shanghairanking_cn_yx(String RS) {
			Map<String, String> headers = new HashMap<>();
			StringBuffer SQL = new StringBuffer();
			try {
				Thread.sleep(200);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			
			String resultPageList = HttpSendUtils2.get("https://www.shanghairanking.cn/rankings/bcur/2025", headers);
			int indexOfPrefix = resultPageList.indexOf("ranking:") - 7;
			int indexOfSubfix = resultPageList.indexOf("return {");
			//System.out.println("RS->"+RS+", indexOfPrefix->"+indexOfPrefix+",indexOfSubfix->"+indexOfSubfix);
			String content = resultPageList.substring(indexOfPrefix, indexOfSubfix);
			
			int varPrefix = resultPageList.indexOf("function(");
			int varSubfix = resultPageList.indexOf("{");
			String varDef = resultPageList.substring(varPrefix+"function(".length(), varSubfix-1);
			System.out.println(varDef);
			
			String variable = content.substring(0,2);
			System.out.println(variable);
			SQL.append("<script>var "+varDef+"; \r\n var " + variable +" = [];\r\n");
			
			SQL.append(content);
			SQL.append("\r\n;</script>");
			
			
			SQL.append("<textarea style=\"width:600px;height:500px;\" id=\"xx\">\r\n"
					+ "	\r\n"
					+ "</textarea>\r\n"
					+ "\r\n"
					+ "<script>\r\n"
					+ "var SQLstr = \"\";\r\n"
					+ "var index = 1;\r\n"
					+ "for(var i=0;i<"+variable+".length;i++){\r\n"
					+ "	var xgx = "+variable+"[i];\r\n"
					+ "	SQLstr += \"insert into lx_ruanke_temp_major_rank(m,x,y,l,e,s,z) values('"+RS+"','\"+xgx.univNameCn+\"','\"+xgx.univCode+\"','\"+xgx.univLogo+\"','\"+xgx.univUpEn+\"','\"+xgx.score+\"',\"+index+++\"); \";\r\n"
					+ "}\r\n"
					+ "document.getElementById(\"xx\").value = SQLstr;\r\n"
					+ "</script>\r\n"
					+ "");
			
			
			writeTempFile(new File("F://就业报告//RUANKE//world_major//PAGE_world_"+RS+".html"), SQL);
			return SQL;
		}
		
		
		public static StringBuffer shanghairanking_cn_rankget() {
			Map<String, String> headers = new HashMap<>();
			StringBuffer SQL = new StringBuffer();


			String resultPageList = HttpSendUtils2.get("https://www.shanghairanking.cn/api/pub/v1/bcmr/rank?year=2024&majorCode=080214T", headers);
			// System.out.println(resultPageList);
			JSONObject object = JSONObject.parseObject(resultPageList);
			JSONObject JSONObject = object.getJSONObject("data");
			JSONArray JSONArray = JSONObject.getJSONArray("majors");
			for(int i=0;i<JSONArray.size();i++) {
				JSONObject element = JSONArray.getJSONObject(i);
				
				String id = element.getString("id");
				String pid = element.getString("pid");
				String name = element.getString("name");
				String code = element.getString("code");
				int ordNo = element.getIntValue("ordNo");
				String remark = element.getString("remark");
				
				
				JSONArray newArray = element.getJSONArray("children");
				for(int ix=0;ix<newArray.size();ix++) {
					JSONObject newElement = newArray.getJSONObject(ix);
					
					String id1 = newElement.getString("id");
					String pid1 = newElement.getString("pid");
					String name1 = newElement.getString("name");
					String code1 = newElement.getString("code");
					int ordNo1 = newElement.getIntValue("ordNo");
					String remark1 = newElement.getString("remark");
					
					
					JSONArray newArray2 = newElement.getJSONArray("children");
					for(int ixx=0;ixx<newArray2.size();ixx++) {
						JSONObject newElement2 = newArray2.getJSONObject(ixx);
						
						String id2 = newElement2.getString("id");
						String pid2 = newElement2.getString("pid");
						String name2 = newElement2.getString("name");
						String code2 = newElement2.getString("code");
						int ordNo2 = newElement2.getIntValue("ordNo");
						String remark2 = newElement2.getString("remark");
						String univPublished2 = newElement2.getString("univPublished");
						
						String URL = "https://www.shanghairanking.cn/api/pub/v1/bcmr/rank?year=2024&majorCode=" + code2;
						System.out.println(URL);
						String result = HttpSendUtils2.get(URL, headers);
						// System.out.println(resultPageList);
						JSONObject resultObject = JSONObject.parseObject(result);
						JSONObject data = resultObject.getJSONObject("data");
						JSONArray rankings = data.getJSONArray("rankings");
						
						for(int ixxx=0;ixxx<rankings.size();ixxx++) {
							JSONObject rank = rankings.getJSONObject(ixxx);
							String univCode = rank.getString("univCode");
							String univNameCn = rank.getString("univNameCn");
							String univUp = rank.getString("univUp");
							String univLogo = rank.getString("univLogo");
							String univLikeCount = rank.getString("univLikeCount");
							String province = rank.getString("province");
							String city = rank.getString("city");
							String ranking = rank.getString("ranking");
							String grade = rank.getString("grade");
							String score = rank.getString("score");
							String liked = rank.getString("liked");
							String inbound = rank.getString("inbound");
							
							SQL.append(
									"insert into ruanke_major_rank(univCode,univNameCn,univUp,univLogo,univLikeCount,province,city,ranking,grade,score,liked,inbound,major_code,rank_year) values('"+univCode+"','"
											+ univNameCn + "','" + univUp + "','" + univLogo + "','"+univLikeCount+"','"+province+"','"+city+"','"+ranking+"','"+grade+"','"+score+"','"+liked+"','"+inbound+"','"+code2+"',2024);\r\n");
							
					}
					}
				}
				
			}
			
			
			writeTempFile(new File("F://就业报告//RUANKE/PAGE_1016.txt"), SQL);
			
			return SQL;
		}

		public static void main(String[] args) {

			for(int i = 501; i<=515;i++) {
				String RS = "RS0" + i;
				
				String ssss = ""
						+ "RS0405"
						+ "RS0222";
				
				if(ssss.indexOf(RS) == -1) {
					//continue;
				}
				try {
				shanghairanking_cn_XUEKE_world(RS);
				}catch(Exception ex) {
					Tools.println("ERR----->"+RS);
				}
			}
		}
		
		private static void writeTempFile(File file, StringBuffer sb) {
			try {
				BufferedWriter bw = new BufferedWriter(new FileWriter(file));
				bw.write(sb.toString());
				bw.flush();
				bw.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}

}
