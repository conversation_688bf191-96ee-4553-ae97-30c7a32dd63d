package com.career.utils.liuxue;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

public class Spider_CQ_GOV {

	// 重庆人事考试网抓取网页
	public static StringBuffer rlsbj_cq_gov(String URL, int pageNumber) {
		Map<String, String> headers = new HashMap<>();
		StringBuffer SQL = new StringBuffer();

		System.out.println(URL);

		String resultPageList = HttpSendUtils2.get(URL, headers);
		// System.out.println(resultPageList);
		Document documentList = Jsoup.parse(resultPageList);

		Elements aiticleList = documentList.getElementsByClass("rsj-list1");

		Elements items = aiticleList.get(0).getElementsByTag("a");
		Elements spans = aiticleList.get(0).getElementsByTag("span");

		for (int i = 0; i < items.size(); i++) {
			Element item = items.get(i);
			Element span = spans.get(i);
			String title = item.text();
			String date = span.text();
			String href = "https://rlsbj.cq.gov.cn/zwxx_182/gsxx" + item.attr("href").substring(1);
			
			if(!date.equals("2024-10-21")) {
				continue;
			}
			
			//检查href存不存在
			

			if (title.indexOf("2024") != -1 && title.indexOf("拟聘人员公示") != -1) {

				Document detailPage = Jsoup.parse(HttpSendUtils2.get(href, headers));
				Elements ue_table = detailPage.getElementsByClass("ue_table");

				Elements tbody = ue_table.get(0).getElementsByTag("tbody");
				Elements tableTR = tbody.get(0).getElementsByTag("tr");
				Elements tableTDTitle = tableTR.get(0).getElementsByTag("td");// 标题
				HashMap<Integer, String> titleMap = new HashMap<>();


				int indexYX = -1,indexDW = -1,indexXM = -1, indexXB = -1, indexXH = -1, indexXL = -1;
				
				for (int k = 0; k < tableTDTitle.size(); k++) {
					titleMap.put(k, tableTDTitle.get(k).text());
					if(tableTDTitle.get(k).text().indexOf("毕业院校") != -1) {
						indexYX = k;
					}
					if(tableTDTitle.get(k).text().indexOf("拟聘") != -1) {
						indexDW = k;
					}
					if(tableTDTitle.get(k).text().indexOf("姓名") != -1) {
						indexXM = k;
					}
					if(tableTDTitle.get(k).text().indexOf("性别") != -1) {
						indexXB = k;
					}
					if(tableTDTitle.get(k).text().indexOf("学历") != -1) {
						indexXL = k;
					}
					if(tableTDTitle.get(k).text().indexOf("序") != -1) {
						indexXH = k;
					}
				}
				
				if(indexYX >= 0 && indexDW >= 0 && indexXM >= 0 && indexXB >= 0 && indexXH >= 0 && indexXL >= 0) {

				}else {
					System.out.println(href);
					continue;
				}
				
				

				String groupID = UUID.randomUUID().toString();
				for (int k = 1; k < tableTR.size(); k++) {
					Element tr = tableTR.get(k);
					Elements tableTD = tr.getElementsByTag("td");
					String oneRecord = UUID.randomUUID().toString();
					

					String xh = tableTD.get(indexXH).text();
					String xm = tableTD.get(indexXM).text();
					String xb = tableTD.get(indexXB).text();
					String xl = tableTD.get(indexXL).text();
					String yxzy = tableTD.get(indexYX).text();
					String dwgw = tableTD.get(indexDW).text();
					
					SQL.append(
							"insert into career_shiye_gov_data(onrecord_id, group_id, yxzy, dwgw, xl, xm, xb, xh, publish_dt, url_link, title, province) values('"+oneRecord+"','"
									+ groupID + "','" + yxzy + "','" + dwgw + "','"+xl+"','"+xm+"','"+xb+"','"+xh+"','" + date + "','"
									+ href + "','" + title + "','重庆');\r\n");

				}

			}
		}
		return SQL;
	}

	public static void main(String[] args) {
		StringBuffer SQL = new StringBuffer();
		for (int i = 7; i < 8; i++) {
			if (i == 0) {
				SQL.append(rlsbj_cq_gov("https://rlsbj.cq.gov.cn/zwxx_182/gsxx/index.html",i));
			} else {
				SQL.append(rlsbj_cq_gov("https://rlsbj.cq.gov.cn/zwxx_182/gsxx/index_"+i+".html",i));
			}
		}

		writeTempFile(new File("F://就业报告//CQ/PAGE_20250101_2.txt"), SQL);

	}
	
	private static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
}
