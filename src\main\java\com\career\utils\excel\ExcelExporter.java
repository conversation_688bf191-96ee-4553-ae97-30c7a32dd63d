package com.career.utils.excel;

import com.career.utils.excel.annotation.ExcelColumn;
import com.career.utils.excel.style.ExcelStyle;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.util.*;
import java.security.MessageDigest;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;


public class ExcelExporter<T> {
    private final Class<T> clazz;
    private ExcelStyle excelStyle;
    private String fileHeader;

    public ExcelExporter(Class<T> clazz) {
        this.clazz = clazz;
        this.fileHeader = null; // 默认没有文件头
    }
    
    /**
     * 设置文件头
     */
    public void setFileHeader(String fileHeader) {
        this.fileHeader = fileHeader;
    }

    /**
     * 导出Excel
     */
    public void export(List<T> dataList, OutputStream out) throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {
            // 初始化样式
            this.excelStyle = new ExcelStyle(workbook);
            
            Sheet sheet = workbook.createSheet("Sheet1");
            
            // 获取所有带ExcelColumn注解的字段
            List<Field> fields = getAnnotatedFields();
            
            // 分析表头结构
            HeaderInfo headerInfo = analyzeHeaderStructure(fields);
            
            int rowOffset = 0;
            // 如果有文件头，则添加文件头
            if (fileHeader != null && !fileHeader.isEmpty()) {
                createFileHeader(sheet, fields.size());
                rowOffset = 1; 
            }
            
            // 创建表头 (从偏移行开始)
            createHeader(sheet, headerInfo, rowOffset);
            
            // 填充数据 (考虑行偏移)
            fillData(sheet, dataList, fields, headerInfo.getHeaderRowCount() + rowOffset);
            
            // 自动调整列宽
            autoSizeColumns(sheet);
            
            // 写入输出流
            workbook.write(out);
        }
    }

    /**
     * 创建文件头
     */
    private void createFileHeader(Sheet sheet, int columnCount) {
        // 创建文件头行
        Row headerRow = sheet.createRow(0);
        // 设置更高的行高
        headerRow.setHeightInPoints(50);
        
        // 创建文件头单元格
        Cell cell = headerRow.createCell(0);
        cell.setCellValue(fileHeader);
        cell.setCellStyle(excelStyle.getFileHeaderStyle());
        
        // 合并单元格
        if (columnCount > 1) {
            CellRangeAddress region = new CellRangeAddress(0, 0, 0, columnCount - 1);
            sheet.addMergedRegion(region);
        }
    }

    /**
     * 分析表头结构
     */
    private HeaderInfo analyzeHeaderStructure(List<Field> fields) {
        HeaderInfo headerInfo = new HeaderInfo();  
        
        Map<String, List<Field>> parentGroups = new HashMap<>();
        int maxLevel = 0;
        
        // 第一次遍历，找出所有的父级表头和最大层级
        for (Field field : fields) {
            ExcelColumn column = field.getAnnotation(ExcelColumn.class);
            if (column.hidden()) {
                continue;
            }
            
            // 更新最大层级
            maxLevel = Math.max(maxLevel, column.level());
            
            // 如果有父表头，则添加到分组中
            String parent = column.parent();
            if (!parent.isEmpty()) {
                if (!parentGroups.containsKey(parent)) {
                    parentGroups.put(parent, new ArrayList<>());
                }
                parentGroups.get(parent).add(field);
            }
        }
        
        headerInfo.setHeaderRowCount(maxLevel + 1);
        headerInfo.setParentGroups(parentGroups);
        
        // 计算每个列在最终表格中的索引位置
        Map<Field, Integer> columnIndices = new HashMap<>();
        int currentIndex = 0;
        
        for (Field field : fields) {
            ExcelColumn column = field.getAnnotation(ExcelColumn.class);
            if (column.hidden()) {
                continue;
            }
            
            columnIndices.put(field, currentIndex++);
        }
        
        headerInfo.setColumnIndices(columnIndices);
        
        return headerInfo;
    }

    /**
     * 创建表头
     */
    private void createHeader(Sheet sheet, HeaderInfo headerInfo, int rowOffset) {
        // 获取所有带ExcelColumn注解的字段
        List<Field> fields = getAnnotatedFields();
        // 使用 analyzeHeaderStructure 计算的实际表头行数
        int headerRowCount = headerInfo.getHeaderRowCount(); 
        
        // 创建所有表头行，考虑行偏移
        Row[] headerRows = new Row[headerRowCount];
        for (int i = 0; i < headerRowCount; i++) {
            headerRows[i] = sheet.createRow(i + rowOffset);
            // 设置表头行高
            headerRows[i].setHeightInPoints(25); // 增加行高以提高可读性
        }

        // 首先收集所有需要合并的叶子节点
        Map<Integer, Integer> leafColumnMap = new HashMap<>(); // 字段到列索引的映射
        Map<String, List<Integer>> parentGroups = new HashMap<>(); // 父级分组
        int colIndex = 0;

        // 第一遍：处理所有叶子节点并记录列索引，同时按父级分组
        for (int i = 0; i < fields.size(); i++) {
            Field field = fields.get(i);
            ExcelColumn column = field.getAnnotation(ExcelColumn.class);
            if (column.hidden()) {
                continue;
            }

            // 记录列索引
            leafColumnMap.put(i, colIndex);

            // 记录到父级分组
            if (!column.parent().isEmpty()) {
                parentGroups.computeIfAbsent(column.parent(), k -> new ArrayList<>()).add(colIndex);
            }

            colIndex++;
        }

        // 第二遍：处理父级分组表头（第一行）
        for (Map.Entry<String, List<Integer>> entry : parentGroups.entrySet()) {
            String groupName = entry.getKey();
            List<Integer> columns = new ArrayList<>(entry.getValue());

            if (columns.isEmpty()) {
                continue;
            }

            // 对列索引排序
            columns.sort(Integer::compareTo);

            // 找出连续的区域
            List<CellRangeAddress> parentRanges = new ArrayList<>();
            int start = columns.get(0);
            int prev = start;

            for (int i = 1; i < columns.size(); i++) {
                if (columns.get(i) != prev + 1) {
                    // 使用绝对行索引确保没有空行
                    int firstRow = rowOffset;
                    parentRanges.add(new CellRangeAddress(firstRow, firstRow, start, prev));
                    start = columns.get(i);
                }
                prev = columns.get(i);
            }
            
            // 添加最后一个区域
            int firstRow = rowOffset;
            parentRanges.add(new CellRangeAddress(firstRow, firstRow, start, prev));

            // 创建父级表头
            for (CellRangeAddress range : parentRanges) {
                Cell parentCell = headerRows[0].createCell(range.getFirstColumn());
                
                // 解析动态标题
                String actualGroupName = resolveDynamicHeader(groupName);
                parentCell.setCellValue(actualGroupName);
                
                // 根据表头名称使用不同样式
                if (actualGroupName.contains("年")) { 
                    parentCell.setCellStyle(excelStyle.getYearHeaderStyle()); // 年度表头使用红色
                } else {
                    parentCell.setCellStyle(excelStyle.getHeaderStyle()); // 其他使用默认表头样式
                }

                try {
                    sheet.addMergedRegion(range);
                } catch (IllegalStateException e) {
                    System.err.println("警告: 无法合并父级区域 " + groupName +
                                     " 在列 " + range.getFirstColumn() + "-" + range.getLastColumn() +
                                     ": " + e.getMessage());
                }
            }
        }

        // 第三遍：处理叶子节点表头
        for (Map.Entry<Integer, Integer> entry : leafColumnMap.entrySet()) {
            Field field = fields.get(entry.getKey());
            ExcelColumn column = field.getAnnotation(ExcelColumn.class);
            int colIdx = entry.getValue();

            // 根据level创建在不同的行
            int rowIdx = column.level();
            if (rowIdx >= headerRowCount) {
                rowIdx = headerRowCount - 1; // 确保不会超过表头行数
            }
            
            // 处理特殊情况：如果parent不为空，那么该列应该显示在第二行
            if (column.parent() != null && !column.parent().isEmpty()) {
                rowIdx = 1; // 第二行
            }
            
            // 创建单元格
            Cell cell = headerRows[rowIdx].createCell(colIdx);
            cell.setCellValue(column.name());
            
            // "专业类"使用特殊样式
            if (column.name().equals("专业类")) {
                cell.setCellStyle(excelStyle.getHeaderStyle());
            } else {
                cell.setCellStyle(excelStyle.getSubHeaderStyle()); // 子表头使用淡蓝色
            }

            // 如果是顶级表头且需要垂直合并单元格
            if (column.level() == 0 && column.rowspan() > 1) {
                // 创建垂直合并区域
                CellRangeAddress region = new CellRangeAddress(
                    rowOffset, rowOffset + column.rowspan() - 1, 
                    colIdx, colIdx
                );
                
                try {
                    sheet.addMergedRegion(region);
                } catch (IllegalStateException e) {
                    System.err.println("警告: 无法合并单元格 " + column.name() + 
                                     " 在列 " + colIdx + 
                                     ": " + e.getMessage());
                }
            }

            // 设置列宽
            sheet.setColumnWidth(colIdx, column.width() * 256);
        }
    }

    /**
     * 填充数据
     */
    private void fillData(Sheet sheet, List<T> dataList, List<Field> fields, int headerRowCount) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }
        
        // 数据行紧接在表头后面开始
        int rowIndex = headerRowCount;

        for (T data : dataList) {
            Row row = sheet.createRow(rowIndex);
            // 设置数据行高
            row.setHeightInPoints(18); // 适当增加数据行高
            int colIndex = 0;
            
            // 判断是否为偶数行（从0开始计算，所以行索引为奇数时是偶数行）
            boolean isEvenRow = (rowIndex - headerRowCount) % 2 == 1;

            try {
                for (Field field : fields) {
                    ExcelColumn column = field.getAnnotation(ExcelColumn.class);
                    if (column.hidden()) {
                        continue;
                    }

                    field.setAccessible(true);
                    Object value = field.get(data);
                    
                    Cell cell = row.createCell(colIndex++);
                    
                    // 根据数据类型和行类型（奇/偶）设置不同的单元格样式
                    if (value != null) {
                        if (value instanceof Integer) {
                            cell.setCellValue(((Number) value).doubleValue());
                            cell.setCellStyle(isEvenRow ? excelStyle.getEvenRowNumberStyle() : excelStyle.getNumberStyle());
                        } else if (value instanceof Double) {
                            cell.setCellValue(((Number) value).doubleValue());
                            cell.setCellStyle(isEvenRow ? excelStyle.getEvenRowDecimalStyle() : excelStyle.getDecimalStyle());
                        } else if (value instanceof Date) {
                            cell.setCellValue((Date) value);
                            cell.setCellStyle(isEvenRow ? excelStyle.getEvenRowDataStyle() : excelStyle.getDataStyle());
                        } else if (value instanceof Boolean) {
                            cell.setCellValue((Boolean) value);
                            cell.setCellStyle(isEvenRow ? excelStyle.getEvenRowDataStyle() : excelStyle.getDataStyle());
                        } else {
                            cell.setCellValue(value.toString());
                            cell.setCellStyle(isEvenRow ? excelStyle.getEvenRowDataStyle() : excelStyle.getDataStyle());
                        }
                    } else {
                        cell.setCellStyle(isEvenRow ? excelStyle.getEvenRowDataStyle() : excelStyle.getDataStyle());
                    }
                }
            } catch (IllegalAccessException e) {
                throw new RuntimeException("Error filling Excel data", e);
            }
            
            rowIndex++; // 行索引递增
        }
    }

    /**
     * 自动调整列宽
     */
    private void autoSizeColumns(Sheet sheet) {
        int numCols = sheet.getRow(0).getLastCellNum();
        for (int i = 0; i < numCols; i++) {
            sheet.autoSizeColumn(i);
            // 设置最小列宽
            int currentWidth = sheet.getColumnWidth(i);
            int minWidth = 12 * 256; // 至少12个字符宽
            
            // 确保所有列至少是最小宽度，但专业类列需要更宽
            if (i == 0) { // 第一列是专业类
                sheet.setColumnWidth(i, Math.max(currentWidth, 20 * 256)); // 更宽的专业类列
            } else {
                sheet.setColumnWidth(i, Math.max(currentWidth, minWidth));
            }
            
            // 对于百分比字段和分数字段，稍微加宽
            if (sheet.getRow(1) != null && sheet.getRow(1).getCell(i) != null) {
                String cellValue = sheet.getRow(1).getCell(i).getStringCellValue();
                if (cellValue.contains("率") || cellValue.contains("分")) {
                    sheet.setColumnWidth(i, Math.max(currentWidth, 15 * 256));
                }
            }
        }
    }

    /**
     * 获取带注解的字段
     */
    private List<Field> getAnnotatedFields() {
        List<Field> fields = new ArrayList<>();
        for (Field field : clazz.getDeclaredFields()) {
            if (field.isAnnotationPresent(ExcelColumn.class)) {
                fields.add(field);
            }
        }
        // 按照sort属性排序
        fields.sort(Comparator.comparingInt(f -> f.getAnnotation(ExcelColumn.class).sort()));
        return fields;
    }
    
    /**
     * 解析动态标题占位符
     * @param headerPlaceholder 标题占位符（如 "CURRENT_YEAR_HEADER"）
     * @return 实际的标题文本
     */
    private String resolveDynamicHeader(String headerPlaceholder) {
        // 检查是否是动态年份标题占位符
        if ("CURRENT_YEAR_HEADER".equals(headerPlaceholder) ||
            "YEAR_A_HEADER".equals(headerPlaceholder) ||
            "YEAR_B_HEADER".equals(headerPlaceholder) ||
            "YEAR_C_HEADER".equals(headerPlaceholder)) {
            
            try {
                // 使用反射调用JHBeanForm的getHeaderMapping方法
                Class<?> jhBeanFormClass = Class.forName("com.career.utils.excel.model.JHBeanForm");
                java.lang.reflect.Method getHeaderMappingMethod = jhBeanFormClass.getMethod("getHeaderMapping");
                @SuppressWarnings("unchecked")
                Map<String, String> headerMapping = (Map<String, String>) getHeaderMappingMethod.invoke(null);
                
                String actualHeader = headerMapping.get(headerPlaceholder);
                if (actualHeader != null) {
                    return actualHeader;
                }
            } catch (Exception e) {
                System.err.println("警告: 无法解析动态标题 " + headerPlaceholder + ": " + e.getMessage());
            }
        }
        
        // 如果不是动态标题或解析失败，返回原始值
        return headerPlaceholder;
    }
    
    /**
     * 表头信息类，用于存储表头结构分析结果
     */
    private static class HeaderInfo {
        private int headerRowCount; // 表头行数
        private Map<String, List<Field>> parentGroups; // 父级表头分组
        private Map<Field, Integer> columnIndices; // 字段对应的列索引
        
        public int getHeaderRowCount() {
            return headerRowCount;
        }
        
        public void setHeaderRowCount(int headerRowCount) {
            this.headerRowCount = headerRowCount;
        }
        
        @SuppressWarnings("unused")
		public Map<String, List<Field>> getParentGroups() {
            return parentGroups;
        }
        
        public void setParentGroups(Map<String, List<Field>> parentGroups) {
            this.parentGroups = parentGroups;
        }
        
        @SuppressWarnings("unused")
		public Map<Field, Integer> getColumnIndices() {
            return columnIndices;
        }
        
        public void setColumnIndices(Map<Field, Integer> columnIndices) {
            this.columnIndices = columnIndices;
        }
    }

    /**
     * 导出Excel到本地文件
     * @param dataList 数据列表
     * @param servletContextPath Servlet上下文路径
     * @param queryParams 查询参数，用于生成文件名
     * @return 文件信息对象
     */
    public ExcelFileInfo exportToFile(List<T> dataList, String servletContextPath, String queryParams) throws IOException {
        // 生成文件名
        String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
        String hashCode = generateHashCode(queryParams);
        String fileName = "export_" + hashCode + "_" + timestamp + ".xlsx";
        String filePath = servletContextPath + fileName;
        
        // 创建文件
        java.io.File file = new java.io.File(filePath);
        
        try (java.io.FileOutputStream fos = new java.io.FileOutputStream(file);
             Workbook workbook = new XSSFWorkbook()) {
            
            // 初始化样式
            this.excelStyle = new ExcelStyle(workbook);
            
            Sheet sheet = workbook.createSheet("Sheet1");
            
            // 获取所有带ExcelColumn注解的字段
            List<Field> fields = getAnnotatedFields();
            
            // 分析表头结构
            HeaderInfo headerInfo = analyzeHeaderStructure(fields);
            
            int rowOffset = 0;
            // 如果有文件头，则添加文件头
            if (fileHeader != null && !fileHeader.isEmpty()) {
                createFileHeader(sheet, fields.size());
                rowOffset = 1; 
            }
            
            // 创建表头 (从偏移行开始)
            createHeader(sheet, headerInfo, rowOffset);
            
            // 填充数据 (考虑行偏移)
            fillData(sheet, dataList, fields, headerInfo.getHeaderRowCount() + rowOffset);
            
            // 自动调整列宽
            autoSizeColumns(sheet);
            
            // 写入文件
            workbook.write(fos);
        }
        
        // 返回文件信息
        ExcelFileInfo fileInfo = new ExcelFileInfo();
        fileInfo.setFileName(fileName);
        fileInfo.setFilePath(filePath);
        fileInfo.setFileSize(file.length());
        fileInfo.setGenerateTime(new Date());
        fileInfo.setRecordCount(dataList.size());
        
        return fileInfo;
    }
    
    /**
     * 生成查询参数的哈希码
     */
    private String generateHashCode(String queryParams) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(queryParams.getBytes(StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            for (byte b : hash) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString().substring(0, 8); // 取前8位
        } catch (Exception e) {
            return String.valueOf(queryParams.hashCode());
        }
    }

    /**
     * Excel文件信息类
     */
    public static class ExcelFileInfo {
        private String fileName;
        private String filePath;
        private long fileSize;
        private Date generateTime;
        private int recordCount;
        
        public String getFileName() {
            return fileName;
        }
        
        public void setFileName(String fileName) {
            this.fileName = fileName;
        }
        
        public String getFilePath() {
            return filePath;
        }
        
        public void setFilePath(String filePath) {
            this.filePath = filePath;
        }
        
        public long getFileSize() {
            return fileSize;
        }
        
        public void setFileSize(long fileSize) {
            this.fileSize = fileSize;
        }
        
        public Date getGenerateTime() {
            return generateTime;
        }
        
        public void setGenerateTime(Date generateTime) {
            this.generateTime = generateTime;
        }
        
        public int getRecordCount() {
            return recordCount;
        }
        
        public void setRecordCount(int recordCount) {
            this.recordCount = recordCount;
        }
        
        /**
         * 格式化文件大小
         */
        public String getFormattedFileSize() {
            if (fileSize < 1024) {
                return fileSize + " B";
            } else if (fileSize < 1024 * 1024) {
                return String.format("%.1f KB", fileSize / 1024.0);
            } else {
                return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
            }
        }
        
        /**
         * 格式化生成时间
         */
        public String getFormattedGenerateTime() {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return sdf.format(generateTime);
        }
    }
} 