package com.career.db.career;

public class MajorPlan {

	private String spe_id;
	private String year;
	private String depart_id;
	private String special_code;
	private String special_name;
	private String recruit_type_name;
	private String depart_name;
	private String degree_type;
	private String research_area;
	private String recruit_number;
	private String degree_type_name;
	private String is_statistic_direction;
	private String plan_id;
	private String remark;
	private String school_id;
	private String school_name;
	private int avg_diff_total;
	
	
	public int getAvg_diff_total() {
		return avg_diff_total;
	}
	public void setAvg_diff_total(int avg_diff_total) {
		this.avg_diff_total = avg_diff_total;
	}
	public String getSchool_id() {
		return school_id;
	}
	public void setSchool_id(String school_id) {
		this.school_id = school_id;
	}
	public String getSchool_name() {
		return school_name;
	}
	public void setSchool_name(String school_name) {
		this.school_name = school_name;
	}
	public String getSpe_id() {
		return spe_id;
	}
	public void setSpe_id(String spe_id) {
		this.spe_id = spe_id;
	}
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}
	public String getDepart_id() {
		return depart_id;
	}
	public void setDepart_id(String depart_id) {
		this.depart_id = depart_id;
	}
	public String getSpecial_code() {
		return special_code;
	}
	public void setSpecial_code(String special_code) {
		this.special_code = special_code;
	}
	public String getSpecial_name() {
		return special_name;
	}
	public void setSpecial_name(String special_name) {
		this.special_name = special_name;
	}
	public String getRecruit_type_name() {
		return recruit_type_name;
	}
	public void setRecruit_type_name(String recruit_type_name) {
		this.recruit_type_name = recruit_type_name;
	}
	public String getDepart_name() {
		return depart_name;
	}
	public void setDepart_name(String depart_name) {
		this.depart_name = depart_name;
	}
	public String getDegree_type() {
		return degree_type;
	}
	public void setDegree_type(String degree_type) {
		this.degree_type = degree_type;
	}
	public String getResearch_area() {
		return research_area;
	}
	public void setResearch_area(String research_area) {
		this.research_area = research_area;
	}
	public String getRecruit_number() {
		return recruit_number;
	}
	public void setRecruit_number(String recruit_number) {
		this.recruit_number = recruit_number;
	}
	public String getDegree_type_name() {
		return degree_type_name;
	}
	public void setDegree_type_name(String degree_type_name) {
		this.degree_type_name = degree_type_name;
	}
	public String getIs_statistic_direction() {
		return is_statistic_direction;
	}
	public void setIs_statistic_direction(String is_statistic_direction) {
		this.is_statistic_direction = is_statistic_direction;
	}
	public String getPlan_id() {
		return plan_id;
	}
	public void setPlan_id(String plan_id) {
		this.plan_id = plan_id;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	
	public String generateSQL(int school_id) {
		String SQL = "insert into career_university_major_plan(school_id, spe_id, plan_year, depart_id, special_code, special_name, recruit_type_name, depart_name, degree_type, research_area, recruit_number, degree_type_name, is_statistic_direction, plan_id, remark) values(" + school_id + ", " + spe_id
				+ ", " + year + "," + depart_id + ",'" + special_code + "','" + special_name + "','" + recruit_type_name + "','" + depart_name + "'," + degree_type +", '" + research_area + "', "+recruit_number+",'"+degree_type_name+"',"+is_statistic_direction+", '"+plan_id+"','"+remark+"');";
		return SQL;
	}
	
}
