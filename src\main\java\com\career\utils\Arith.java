package com.career.utils;

import java.math.BigDecimal;

public class Arith{
	 
	 
	 
    //默认除法运算精度
 
    private static final int DEF_DIV_SCALE = 10;
 
 
 
    //这个类不能实例化
 
    private Arith(){
 
    }
 
 
 
 
 
    /**
     * 提供精确的加法运算。
     * @paramv1 被加数
     * @paramv2 加数
     *@return 两个参数的和
     */
 
    public static double add(double v1,double v2){
 
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
 
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
 
        return b1.add(b2).doubleValue();
 
    }
 
 
 
    /**
     * 提供精确的减法运算。
     * @paramv1 被减数
     * @paramv2 减数
     *@return 两个参数的差
     */
 
    public static double sub(double v1,double v2){
 
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
 
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
 
        return b1.subtract(b2).doubleValue();
 
    }
 
 
 
    /**
     * 提供精确的乘法运算。
     * @paramv1 被乘数
     * @paramv2 乘数
     *@return 两个参数的积
     */
 
    public static double mul(double v1,double v2){
 
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
 
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
 
        return b1.multiply(b2).doubleValue();
 
    }
 
 
 
    /**
     * 提供（相对）精确的除法运算，当发生除不尽的情况时，精确到
     * 小数点以后10位，以后的数字四舍五入。
     * @paramv1 被除数
     * @paramv2 除数
     *@return 两个参数的商
     */
 
    public static double div(double v1,double v2){
 
        return div(v1,v2,DEF_DIV_SCALE);
 
    }
 
 
 
    /**
     * 提供（相对）精确的除法运算。当发生除不尽的情况时，由scale参数指
     * 定精度，以后的数字四舍五入。
     * @paramv1 被除数
     * @paramv2 除数
     * @paramscale 表示表示需要精确到小数点以后几位。
     *@return 两个参数的商
     */
 
    public static double div(double v1,double v2,int scale){
 
        if(scale<0){
 
            throw new IllegalArgumentException(
 
                "Thescale must be a positive integer or zero");
 
        }
 
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
 
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
 
        return b1.divide(b2,scale,BigDecimal.ROUND_HALF_UP).doubleValue();
 
    }
 
 
 
    /**
     * 提供精确的小数位四舍五入处理。
     * @paramv 需要四舍五入的数字
     * @paramscale 小数点后保留几位
     *@return 四舍五入后的结果
     */
 
    public static double round(double v,int scale){
 
        if(scale<0){
 
            throw new IllegalArgumentException(
 
                "Thescale must be a positive integer or zero");
 
        }
 
        BigDecimal b = new BigDecimal(Double.toString(v));
 
        return b.setScale(scale,BigDecimal.ROUND_HALF_UP).doubleValue();
 
    }
 
};