package com.career.utils.zsky;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

public class DealWithZSGK {

	public static void main(String args[]) throws Exception {
		dealWithMajorCourse();
	}

	public static void dealWithInfo() throws Exception {
		StringBuffer SQL = new StringBuffer();
		for (int i = 2; i <= 1600; i++) {// 1574
			try {
				BufferedReader bw = new BufferedReader(
						new FileReader(new File("E://kaoyan//" + i + "//info_" + i + "_data.txt")));
				StringBuffer sb = new StringBuffer();
				String str = null;
				while ((str = bw.readLine()) != null) {
					sb.append(str);
				}
				ResultInfoBean resultBean = (ResultInfoBean) JSONObject.parseObject(sb.toString(), ResultInfoBean.class);
				SchoolInfo info = resultBean.getData();
				if (info == null) {
					continue;
				}
				SQL.append(info.generateSQL()+"\r\n");
				
				
			} catch (Exception ex) {
			}

		}

		writeTempFile(new File("E://kaoyan//KYschoolinfoSQL.txt"), SQL);
	}

	public static void dealWithMajorInfo() throws Exception {
		StringBuffer SQL = new StringBuffer();
		
		for (int i = 2; i <= 1600; i++) {// 1574
			try {
				BufferedReader bw = new BufferedReader(
						new FileReader(new File("E://kaoyan//" + i + "//runn2_3_"+i+"history_data.txt")));
				StringBuffer sb = new StringBuffer();
				String str = null;
				while ((str = bw.readLine()) != null) {
					sb.append(str);
				}
				ResultMajorBean bean = (ResultMajorBean) JSONObject.parseObject(sb.toString(), ResultMajorBean.class);
				SchoolMajor info = bean.getData();
				if (info == null) {
					continue;
				}
				
				SchoolMajorFirstClass[] elements = info.getFirst_class();
				if(elements != null && elements.length > 0) {
					for(SchoolMajorFirstClass infoBean : elements) {
						SQL.append(infoBean.generateSQL(i, info.getSyl_jianshe())+"\r\n");
					}
				}
				
				
			} catch (Exception ex) {
			}

		}

		writeTempFile(new File("E://kaoyan//KYSchoolMajorSQL.txt"), SQL);
	}
	
	
	public static void dealWithMajorDataHistory() throws Exception {
		StringBuffer SQL = new StringBuffer();
		HashSet<String> hs = new HashSet<>();
		for (int i = 2; i <= 1600; i++) {
			try {
				File file = new File("E://kaoyan//" + i + "//");
				File[] fileList = file.listFiles();
				if(fileList == null) {
					continue;
				}
				for(File xx : fileList) {
					if(xx.getName().startsWith("runn2_2_2023")) {
						//System.out.println(xx.getName());
						
						BufferedReader bw = new BufferedReader(new FileReader(xx));
						StringBuffer sb = new StringBuffer();
						String str = null;
						while ((str = bw.readLine()) != null) {
							sb.append(str);
						}
						ResultMajorHistoryBean bean = (ResultMajorHistoryBean) JSONObject.parseObject(sb.toString(), ResultMajorHistoryBean.class);
						MajorHistory info = bean.getData();
						if (info == null) {
							continue;
						}

						MajorHistoryItem[] elements = info.getItem();
						if(elements != null && elements.length > 0) {
							for(MajorHistoryItem item : elements) {
								SQL.append(item.generateSQL(i)+"\r\n");
							}
						}
						
					}
				}
			} catch (Exception ex) {
				ex.printStackTrace();
			}

		}

		
		writeTempFile(new File("E://kaoyan//KYSchoolMajorDataSQL.txt"), SQL);
	}
	
	public static void dealWithMajorAdjustWithEachSchool() throws Exception {
		StringBuffer SQL = new StringBuffer();
		
		for (int i = 2; i <= 1600; i++) {
			try {
				BufferedReader bw = new BufferedReader(
						new FileReader(new File("E://kaoyan//" + i + "//runn2_4_"+i+"adjust_data.txt")));
				StringBuffer sb = new StringBuffer();
				String str = null;
				while ((str = bw.readLine()) != null) {
					sb.append(str);
				}
				ResultAdjustBean bean = (ResultAdjustBean) JSONObject.parseObject(sb.toString(), ResultAdjustBean.class);
				MajorAdjust info = bean.getData();
				if (info == null) {
					continue;
				}
				
				MajorAdjustItem[] elements = info.getData();
				if(elements != null && elements.length > 0) {
					for(MajorAdjustItem item : elements) {
						SQL.append(item.generateSQL()+"\r\n");
					}
				}
				
				
			} catch (Exception ex) {
			}

		}

		writeTempFile(new File("E://kaoyan//KYSchoolMajorAdjust2SQL.txt"), SQL);
	}
	
	public static void dealWithMajorAdjustWithOneFile() throws Exception {
		StringBuffer SQL = new StringBuffer();
		String[] files = new String[] {"runn2_42_adjust_list.txt"}; 
		for (int i = 0; i < files.length; i++) {
			try {
				BufferedReader bw = new BufferedReader(
						new FileReader(new File("E://kaoyan//"+files[i])));
				StringBuffer sb = new StringBuffer();
				String str = null;
				while ((str = bw.readLine()) != null) {
					sb.append(str);
				}
				ResultAdjustBean bean = (ResultAdjustBean) JSONObject.parseObject(sb.toString(), ResultAdjustBean.class);
				MajorAdjust info = bean.getData();
				if (info == null) {
					continue;
				}
				
				MajorAdjustItem[] elements = info.getData();
				if(elements != null && elements.length > 0) {
					for(MajorAdjustItem item : elements) {
						SQL.append(item.generateSQL()+"\r\n");
					}
				}
				
				
			} catch (Exception ex) {
				ex.printStackTrace();
			}

		}

		writeTempFile(new File("E://kaoyan//KYSchoolMajorAdjustALLSQL_v3.txt"), SQL);
	}

	
	public static void dealWithMajor() throws Exception {
		StringBuffer SQL = new StringBuffer();
		String[] files = new String[] {"runn2_5_major2_ZHUANYE.txt","runn2_5_major2_XUESHU.txt"}; 
		for (int i = 0; i < files.length; i++) {
			try {
				BufferedReader bw = new BufferedReader(
						new FileReader(new File("E://kaoyan//" + files[i])));
				StringBuffer sb = new StringBuffer();
				String str = null;
				while ((str = bw.readLine()) != null) {
					sb.append(str);
				}
				ResultMajorBean2 bean = (ResultMajorBean2) JSONObject.parseObject(sb.toString(), ResultMajorBean2.class);
				Major info = bean.getData();
				if (info == null) {
					continue;
				}
				
				MajorItem[] elements = info.getData();
				if(elements != null && elements.length > 0) {
					for(MajorItem item : elements) {
						SQL.append(item.generateSQL()+"\r\n");
					}
				}
				
				
			} catch (Exception ex) {
			}

		}

		writeTempFile(new File("E://kaoyan//KYMajorSQL.txt"), SQL);
	}
	
	
	
	public static void dealWithMajorPlan() throws Exception {
		StringBuffer SQL = new StringBuffer();
		for(int year = 2023;year >= 2022;year--) {
			int ext = 0;
			for (int i = 2; i <= 1574; i++) {
				try {
					BufferedReader bw = new BufferedReader(
							new FileReader(new File("E://kaoyan//" + i + "//runn2_2_JH_"+i+"_"+year+".txt")));
					StringBuffer sb = new StringBuffer();
					String str = null;
					while ((str = bw.readLine()) != null) {
						sb.append(str);
					}
					ResultPlanBean bean = (ResultPlanBean) JSONObject.parseObject(sb.toString(), ResultPlanBean.class);
					MajorPlan info = bean.getData();
					if (info == null) {
						continue;
					}
					
					MajorPlanItem[] elements = info.getData();
					if(elements != null && elements.length > 0) {
						for(MajorPlanItem item : elements) {
							SQL.append(item.generateSQL(i)+"\r\n");
							ext++;
						}
					}
					
					
				} catch (Exception ex) {
				}
	
			}
			System.out.println(year+","+ext);
			
		}
		
		writeTempFile(new File("E://kaoyan//KYPlanSQL_BATCH_2023_2022.txt"), SQL);
	}
	
	
	
	public static void dealWithGJX() {
		StringBuffer SQL = new StringBuffer();
		String[] files = new String[] {"KY_GJX.txt"}; 
		for(int year = 2023;year >= 2020;year--) {
			for (int i = 0; i < files.length; i++) {
				try {
					BufferedReader bw = new BufferedReader(
							new FileReader(new File("E://kaoyan//" + files[i])));
					StringBuffer sb = new StringBuffer();
					String str = null;
					while ((str = bw.readLine()) != null) {
						sb.append(str);
					}
					JSONObject bean = JSONObject.parseObject(sb.toString());
					JSONObject beanData = (JSONObject)bean.getJSONObject("data");
					JSONArray array = (JSONArray)beanData.getJSONArray(String.valueOf(year));
					
					for(int x =0;x<array.size();x++) {
						JSONObject xxxx = array.getJSONObject(x);
						GJXItem gJXItem = JSONObject.parseObject(xxxx.toString(), GJXItem.class);
						SQL.append(gJXItem.generateSQL()+"\r\n");
						System.out.println(gJXItem.getId());
					}
					
					
				} catch (Exception ex) {
					ex.printStackTrace();
				}
	
			}
		}

		writeTempFile(new File("E://kaoyan//KYGJXSQL.txt"), SQL);
	}
	
	
	public static void dealWithSchoolTAG() {
		StringBuffer SQL = new StringBuffer();
		String[] files = new String[] {"runn2_7_SCHOOL_TAG.txt"}; 
		for (int i = 0; i < files.length; i++) {
			try {
				BufferedReader bw = new BufferedReader(
						new FileReader(new File("E://kaoyan//" + files[i])));
				StringBuffer sb = new StringBuffer();
				String str = null;
				while ((str = bw.readLine()) != null) {
					sb.append(str);
				}
				JSONObject bean = JSONObject.parseObject(sb.toString());
				JSONObject beanData = (JSONObject)bean.getJSONObject("data");
				JSONArray array = (JSONArray)beanData.getJSONArray("data");
				
				for(int x =0;x<array.size();x++) {
					JSONObject xxxx = array.getJSONObject(x);
					SchoolTagItem item = JSONObject.parseObject(xxxx.toString(), SchoolTagItem.class);
					SQL.append(item.generateSQL()+"\r\n");
				}
				
				
			} catch (Exception ex) {
				ex.printStackTrace();
			}
		}

		writeTempFile(new File("E://kaoyan//KYSCHOOL_TAGSQL.txt"), SQL);
	}
	
	
	public static void dealWithMajorDesc() {
		StringBuffer SQL = new StringBuffer();
		String[] files = new String[] {"majorInfo.txt"}; 
		for (int i = 0; i < files.length; i++) {
			try {
				BufferedReader bw = new BufferedReader(
						new FileReader(new File("E://kaoyan//" + files[i])));
				StringBuffer sb = new StringBuffer();
				String str = null;
				while ((str = bw.readLine()) != null) {
					sb.append(str);
				}
				JSONObject bean = JSONObject.parseObject(sb.toString());
				JSONObject beanData = (JSONObject)bean.getJSONObject("data");
				Iterator<String> keyStr = beanData.keySet().iterator();
				while(keyStr.hasNext()) {
					String key = keyStr.next();
					String value = beanData.getString(key);
					System.out.println(key+" - "+value);
					SQL.append("INSERT INTO career_major_desc(major_id, major_desc) values('"+key+"','"+value+"');\r\n");
				}
				
				
				
				
			} catch (Exception ex) {
				ex.printStackTrace();
			}
		}

		writeTempFile(new File("E://kaoyan//KYMAJOR_DESCSQL.txt"), SQL);
	}
	
	
	public static void dealWithMajorCourse() {
		StringBuffer SQL = new StringBuffer();
		String[] files = new String[] {"majorCourse.txt"}; 
		for (int i = 0; i < files.length; i++) {
			try {
				BufferedReader bw = new BufferedReader(
						new FileReader(new File("E://kaoyan//" + files[i])));
				StringBuffer sb = new StringBuffer();
				String str = null;
				while ((str = bw.readLine()) != null) {
					sb.append(str);
				}
				JSONObject bean = JSONObject.parseObject(sb.toString());
				JSONObject beanData = (JSONObject)bean.getJSONObject("data");
				Iterator<String> keyStr = beanData.keySet().iterator();
				while(keyStr.hasNext()) {
					String key = keyStr.next();
					JSONObject value = beanData.getJSONObject(key);
					Iterator<String> keyStrValue = value.keySet().iterator();
					while(keyStrValue.hasNext()) {
						String key2 = keyStrValue.next();
						JSONObject value2 = value.getJSONObject(key2);
						SQL.append("INSERT INTO career_major_course(course_name, major_id) values('"+key2+"','"+key+"');\r\n");
					}
					
					
					
					//
				}
				
				
				
				
			} catch (Exception ex) {
				ex.printStackTrace();
			}
		}

		writeTempFile(new File("E://kaoyan//KYMAJOR_COURSESQL.txt"), SQL);
	}
	
	
	private static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	//

	private static String removeTag(String html) {

		// 定义要匹配的正则表达式模式
		Pattern pattern = Pattern.compile("<[^>]+>", Pattern.CASE_INSENSITIVE);

		// 创建 Matcher 对象并进行匹配操作
		Matcher matcher = pattern.matcher(html);

		// 将匹配到的 HTML 标签替换为空字符串
		String result = matcher.replaceAll("").replaceAll("&nbsp;", "");

		return result.trim();
	}

}
