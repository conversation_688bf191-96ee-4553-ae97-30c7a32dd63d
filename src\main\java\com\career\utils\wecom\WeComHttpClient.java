package com.career.utils.wecom;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.career.utils.wecom.exception.WeComApiException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * 企业微信HTTP客户端工具类
 * 优化连接池管理和错误处理
 */
public class WeComHttpClient {
    private static final Logger LOGGER = Logger.getLogger(WeComHttpClient.class.getName());

    // 连接超时时间(毫秒)
    private static final int CONNECT_TIMEOUT = 5000;
    // 请求超时时间(毫秒)
    private static final int REQUEST_TIMEOUT = 10000;
    // Socket超时时间(毫秒)
    private static final int SOCKET_TIMEOUT = 15000;
    // 最大重试次数
    private static final int MAX_RETRY_TIMES = 3;
    // 重试间隔时间(毫秒)
    private static final long RETRY_INTERVAL = 1000;
    // 最大连接数
    private static final int MAX_TOTAL_CONNECTIONS = 200;
    // 每个路由(域名)的最大连接数
    private static final int MAX_ROUTE_CONNECTIONS = 40;
    
    // HTTP连接池管理器
    private static final PoolingHttpClientConnectionManager connectionManager;
    // HTTP客户端实例
    private static final CloseableHttpClient httpClient;
    
    // 初始化HTTP连接池
    static {
        // 创建连接池管理器
        connectionManager = new PoolingHttpClientConnectionManager();
        // 设置整个连接池的最大连接数
        connectionManager.setMaxTotal(MAX_TOTAL_CONNECTIONS);
        // 设置每个路由的最大连接数
        connectionManager.setDefaultMaxPerRoute(MAX_ROUTE_CONNECTIONS);
        
        // 创建默认请求配置
        RequestConfig defaultRequestConfig = RequestConfig.custom()
                .setConnectTimeout(CONNECT_TIMEOUT)
                .setSocketTimeout(SOCKET_TIMEOUT)
                .setConnectionRequestTimeout(REQUEST_TIMEOUT)
                .build();
        
        // 创建HTTP客户端
        httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(defaultRequestConfig)
                .evictExpiredConnections()
                .evictIdleConnections(60, TimeUnit.SECONDS)
                .build();
        
        // 添加JVM关闭钩子，确保资源释放
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                LOGGER.info("关闭HTTP连接池...");
                httpClient.close();
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "关闭HTTP连接池时出错", e);
            }
        }));
    }

    /**
     * 发送GET请求并返回JSON对象，带有重试机制
     * @param url 请求URL
     * @return JSON对象
     */
    public static JSONObject doGetWithRetry(String url) {
        Exception lastException = null;
        
        for (int i = 0; i < MAX_RETRY_TIMES; i++) {
            try {
                LOGGER.fine("执行GET请求: " + url + ", 尝试次数: " + (i + 1));
                String response = doGet(url);
                JSONObject jsonObject = JSON.parseObject(response);
                
                // 检查API返回的错误码
                int errorCode = jsonObject.getIntValue("errcode");
                if (errorCode == 0) {
                    return jsonObject;
                } else if (shouldRetry(errorCode, url)) {
                    // 对于可重试的错误码，等待后重试
                    LOGGER.warning("企业微信API返回可重试错误: " + errorCode + ", 将在 " + RETRY_INTERVAL + "ms 后重试");
                    Thread.sleep(RETRY_INTERVAL);
                    continue;
                } else {
                    // 对于不可重试的错误码，直接抛出异常
                    throw WeComApiException.create(errorCode, "企业微信API调用失败，错误码: " + errorCode + 
                                               ", 错误信息: " + jsonObject.getString("errmsg"));
                }
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "请求企业微信API时发生IO异常, 将尝试重试", e);
                lastException = e;
                try {
                    Thread.sleep(RETRY_INTERVAL);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                lastException = e;
                break;
            }
        }
        
        // 如果重试多次后仍然失败
        if (lastException != null) {
            if (lastException instanceof IOException) {
                throw new WeComApiException.NetworkException("企业微信API调用失败，重试" + MAX_RETRY_TIMES + "次后仍未成功", lastException);
            } else {
                throw new WeComApiException("企业微信API调用失败，重试" + MAX_RETRY_TIMES + "次后仍未成功", lastException);
            }
        }
        
        throw new WeComApiException("企业微信API调用失败，未知原因");
    }

    /**
     * 发送POST请求并返回JSON对象，带有重试机制
     * @param url 请求URL
     * @param jsonData JSON格式请求体
     * @return JSON对象
     */
    public static JSONObject doPostWithRetry(String url, String jsonData) {
        Exception lastException = null;
        
        for (int i = 0; i < MAX_RETRY_TIMES; i++) {
            try {
                LOGGER.fine("执行POST请求: " + url + ", 尝试次数: " + (i + 1));
                String response = doPost(url, jsonData);
                JSONObject jsonObject = JSON.parseObject(response);
                
                // 检查API返回的错误码
                int errorCode = jsonObject.getIntValue("errcode");
                if (errorCode == 0) {
                    return jsonObject;
                } else if (shouldRetry(errorCode, url)) {
                    // 对于可重试的错误码，等待后重试
                    LOGGER.warning("企业微信API返回可重试错误: " + errorCode + ", 将在 " + RETRY_INTERVAL + "ms 后重试");
                    Thread.sleep(RETRY_INTERVAL);
                    continue;
                } else {
                    // 对于不可重试的错误码，直接抛出异常
                    throw WeComApiException.create(errorCode, "企业微信API调用失败，错误码: " + errorCode + 
                                               ", 错误信息: " + jsonObject.getString("errmsg"));
                }
            } catch (IOException e) {
                LOGGER.log(Level.WARNING, "请求企业微信API时发生IO异常, 将尝试重试", e);
                lastException = e;
                try {
                    Thread.sleep(RETRY_INTERVAL);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                lastException = e;
                break;
            }
        }
        
        // 如果重试多次后仍然失败
        if (lastException != null) {
            if (lastException instanceof IOException) {
                throw new WeComApiException.NetworkException("企业微信API调用失败，重试" + MAX_RETRY_TIMES + "次后仍未成功", lastException);
            } else {
                throw new WeComApiException("企业微信API调用失败，重试" + MAX_RETRY_TIMES + "次后仍未成功", lastException);
            }
        }
        
        throw new WeComApiException("企业微信API调用失败，未知原因");
    }
    
    /**
     * 判断是否应该重试
     * @param errorCode 错误码
     * @param url 请求的URL
     * @return 是否应该重试
     */
    private static boolean shouldRetry(int errorCode, String url) {
        // 42001: access_token过期
        if (errorCode == 42001) {
            // 提取access_token参数
            String accessToken = extractAccessTokenFromUrl(url);
            if (accessToken != null) {
                LOGGER.warning("检测到access_token已过期，标记token为过期状态: " + accessToken.substring(0, 6) + "...");
                // 通知TokenManager标记token为过期状态
                WeComTokenManager.markTokenAsExpired(accessToken);
            }
            return true;
        }
        
        // 40001: 获取access_token时Secret错误，或者access_token无效
        // 40014: 不合法的access_token
        // -1: 系统繁忙
        return errorCode == -1 || errorCode == 40001 || errorCode == 40014;
    }
    
    /**
     * 从URL中提取access_token参数
     * @param url 请求URL
     * @return access_token值，如果不存在则返回null
     */
    private static String extractAccessTokenFromUrl(String url) {
        if (url == null || url.isEmpty()) {
            return null;
        }
        
        try {
            int tokenIndex = url.indexOf("access_token=");
            if (tokenIndex != -1) {
                // 提取token参数值
                String tokenPart = url.substring(tokenIndex + 13); // 13是"access_token="的长度
                int endIndex = tokenPart.indexOf("&");
                if (endIndex != -1) {
                    return tokenPart.substring(0, endIndex);
                } else {
                    return tokenPart;
                }
            }
        } catch (Exception e) {
            LOGGER.warning("从URL提取access_token失败: " + e.getMessage());
        }
        
        return null;
    }

    /**
     * 判断是否应该重试
     * @param errorCode 错误码
     * @return 是否应该重试
     */
    private static boolean shouldRetry(int errorCode) {
        // 复用包含URL参数的方法
        return shouldRetry(errorCode, null);
    }

    /**
     * 执行GET请求
     */
    private static String doGet(String url) throws IOException {
        HttpGet httpGet = new HttpGet(url);
        
        try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != 200) {
                LOGGER.warning("HTTP请求返回非200状态码: " + statusCode);
            }
            return EntityUtils.toString(response.getEntity(), "UTF-8");
        }
    }

    /**
     * 执行POST请求
     */
    private static String doPost(String url, String jsonData) throws IOException {
        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader("Content-Type", "application/json");
        
        if (jsonData != null) {
            StringEntity entity = new StringEntity(jsonData, "UTF-8");
            httpPost.setEntity(entity);
        }
        
        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != 200) {
                LOGGER.warning("HTTP请求返回非200状态码: " + statusCode);
            }
            return EntityUtils.toString(response.getEntity(), "UTF-8");
        }
    }
} 