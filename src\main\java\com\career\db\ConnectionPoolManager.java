package com.career.db;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * 数据库连接池管理器
 * 使用HikariCP连接池解决连接数耗尽问题
 * 
 * 主要功能：
 * 1. 统一管理数据库连接
 * 2. 自动连接回收和重用
 * 3. 连接泄漏检测
 * 4. 性能监控
 * 
 * <AUTHOR> Admin
 * @version 1.0
 */
public class ConnectionPoolManager {
    
    private static volatile HikariDataSource dataSource;
    private static final Object lock = new Object();
    
    // 连接池配置参数 - 针对16000连接上限优化
    private static final int MAXIMUM_POOL_SIZE = 200;          // 最大连接数 (可根据应用实例数调整)
    private static final int MINIMUM_IDLE = 60;                // 最小空闲连接数
    private static final long CONNECTION_TIMEOUT = 30000;      // 连接超时时间(毫秒)
    private static final long IDLE_TIMEOUT = 300000;           // 空闲连接超时时间(毫秒) - 5分钟
    private static final long MAX_LIFETIME = 1200000;          // 连接最大生命周期(毫秒) - 20分钟
    private static final long LEAK_DETECTION_THRESHOLD = 30000; // 连接泄漏检测阈值(毫秒) - 30秒

    // 动态配置支持
    private static final String POOL_SIZE_PROPERTY = "db.pool.maxSize";
    private static final String MIN_IDLE_PROPERTY = "db.pool.minIdle";
    
    /**
     * 获取数据源实例（单例模式）
     */
    private static DataSource getDataSource() {
        if (dataSource == null) {
            synchronized (lock) {
                if (dataSource == null) {
                    initializeDataSource();
                }
            }
        }
        return dataSource;
    }
    
    /**
     * 初始化数据源
     */
    private static void initializeDataSource() {
        try {
            HikariConfig config = new HikariConfig();

            // 基本连接配置
            config.setJdbcUrl(JDBCConstants.URL);
            config.setUsername(JDBCConstants.USER);
            config.setPassword(JDBCConstants.PASSWD);
            config.setDriverClassName("com.mysql.cj.jdbc.Driver");

            // 动态连接池大小配置
            int maxPoolSize = getConfiguredPoolSize();
            int minIdle = getConfiguredMinIdle();

            config.setMaximumPoolSize(maxPoolSize);
            config.setMinimumIdle(minIdle);

            System.out.println("连接池配置 - 最大连接数: " + maxPoolSize + ", 最小空闲: " + minIdle);
            
            // 超时配置
            config.setConnectionTimeout(CONNECTION_TIMEOUT);
            config.setIdleTimeout(IDLE_TIMEOUT);
            config.setMaxLifetime(MAX_LIFETIME);
            
            // 连接泄漏检测
            config.setLeakDetectionThreshold(LEAK_DETECTION_THRESHOLD);
            
            // 连接池名称
            config.setPoolName("ZyzdHikariCP");
            
            // 连接测试查询
            config.setConnectionTestQuery("SELECT 1");
            
            // 性能优化配置
            config.addDataSourceProperty("cachePrepStmts", "true");
            config.addDataSourceProperty("prepStmtCacheSize", "250");
            config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
            config.addDataSourceProperty("useServerPrepStmts", "true");
            config.addDataSourceProperty("rewriteBatchedStatements", "true");
            
            // 创建数据源
            dataSource = new HikariDataSource(config);
            
            System.out.println("数据库连接池初始化成功 - 最大连接数: " + MAXIMUM_POOL_SIZE);
            
        } catch (Exception e) {
            System.err.println("数据库连接池初始化失败: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Failed to initialize connection pool", e);
        }
    }
    
    /**
     * 获取数据库连接
     * @return 数据库连接
     * @throws SQLException 获取连接失败时抛出
     */
    public static Connection getConnection() throws SQLException {
        try {
            Connection conn = getDataSource().getConnection();
            if (conn == null) {
                throw new SQLException("Failed to obtain connection from pool");
            }
            return conn;
        } catch (SQLException e) {
            System.err.println("获取数据库连接失败: " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * 关闭连接（实际上是归还到连接池）
     * @param connection 要关闭的连接
     */
    public static void closeConnection(Connection connection) {
        if (connection != null) {
            try {
                connection.close(); // HikariCP会自动归还到池中
            } catch (SQLException e) {
                System.err.println("关闭数据库连接时出错: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 获取连接池状态信息
     * @return 连接池状态字符串
     */
    public static String getPoolStatus() {
        if (dataSource != null) {
            return String.format(
                "连接池状态 - 活跃连接: %d, 空闲连接: %d, 总连接: %d, 等待连接: %d",
                dataSource.getHikariPoolMXBean().getActiveConnections(),
                dataSource.getHikariPoolMXBean().getIdleConnections(),
                dataSource.getHikariPoolMXBean().getTotalConnections(),
                dataSource.getHikariPoolMXBean().getThreadsAwaitingConnection()
            );
        }
        return "连接池未初始化";
    }
    
    /**
     * 关闭连接池
     */
    public static void shutdown() {
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
            System.out.println("数据库连接池已关闭");
        }
    }
    
    /**
     * 检查连接池健康状态
     * @return true表示健康，false表示有问题
     */
    public static boolean isHealthy() {
        if (dataSource == null || dataSource.isClosed()) {
            return false;
        }

        try (Connection conn = getConnection()) {
            return conn != null && !conn.isClosed();
        } catch (SQLException e) {
            System.err.println("连接池健康检查失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 获取配置的连接池大小
     * 支持系统属性和环境变量配置
     */
    private static int getConfiguredPoolSize() {
        // 1. 优先使用系统属性
        String poolSizeStr = System.getProperty(POOL_SIZE_PROPERTY);
        if (poolSizeStr != null) {
            try {
                int configuredSize = Integer.parseInt(poolSizeStr);
                return validatePoolSize(configuredSize);
            } catch (NumberFormatException e) {
                System.err.println("无效的连接池大小配置: " + poolSizeStr);
            }
        }

        // 2. 使用环境变量
        poolSizeStr = System.getenv("DB_POOL_MAX_SIZE");
        if (poolSizeStr != null) {
            try {
                int configuredSize = Integer.parseInt(poolSizeStr);
                return validatePoolSize(configuredSize);
            } catch (NumberFormatException e) {
                System.err.println("无效的环境变量连接池大小: " + poolSizeStr);
            }
        }

        // 3. 使用默认值
        return MAXIMUM_POOL_SIZE;
    }

    /**
     * 获取配置的最小空闲连接数
     */
    private static int getConfiguredMinIdle() {
        String minIdleStr = System.getProperty(MIN_IDLE_PROPERTY);
        if (minIdleStr != null) {
            try {
                int configuredMinIdle = Integer.parseInt(minIdleStr);
                return Math.max(1, Math.min(configuredMinIdle, getConfiguredPoolSize() / 2));
            } catch (NumberFormatException e) {
                System.err.println("无效的最小空闲连接数配置: " + minIdleStr);
            }
        }

        minIdleStr = System.getenv("DB_POOL_MIN_IDLE");
        if (minIdleStr != null) {
            try {
                int configuredMinIdle = Integer.parseInt(minIdleStr);
                return Math.max(1, Math.min(configuredMinIdle, getConfiguredPoolSize() / 2));
            } catch (NumberFormatException e) {
                System.err.println("无效的环境变量最小空闲连接数: " + minIdleStr);
            }
        }

        return MINIMUM_IDLE;
    }

    /**
     * 验证连接池大小配置
     */
    private static int validatePoolSize(int configuredSize) {
        // 最小值检查
        if (configuredSize < 5) {
            System.out.println("连接池大小过小，调整为最小值: 5");
            return 5;
        }

        // 最大值检查 (考虑数据库16000连接上限，为其他应用预留空间)
        if (configuredSize > 1000) {
            System.out.println("连接池大小过大，调整为最大值: 1000");
            return 1000;
        }

        return configuredSize;
    }

    /**
     * 获取推荐的连接池配置
     * 基于系统资源和预期负载
     */
    public static String getRecommendedConfiguration() {
        Runtime runtime = Runtime.getRuntime();
        int availableProcessors = runtime.availableProcessors();
        long maxMemory = runtime.maxMemory();

        // 基于CPU核心数的推荐配置
        int recommendedPoolSize;
        if (availableProcessors <= 2) {
            recommendedPoolSize = 50;   // 小型应用
        } else if (availableProcessors <= 4) {
            recommendedPoolSize = 100;  // 中型应用
        } else if (availableProcessors <= 8) {
            recommendedPoolSize = 200;  // 大型应用
        } else {
            recommendedPoolSize = 300;  // 高性能应用
        }

        // 基于内存的调整
        long memoryGB = maxMemory / (1024 * 1024 * 1024);
        if (memoryGB < 2) {
            recommendedPoolSize = Math.min(recommendedPoolSize, 50);
        } else if (memoryGB < 4) {
            recommendedPoolSize = Math.min(recommendedPoolSize, 100);
        }

        return String.format(
            "推荐配置 (CPU核心: %d, 内存: %dGB):\n" +
            "  最大连接数: %d\n" +
            "  最小空闲: %d\n" +
            "  配置方法: -D%s=%d -D%s=%d",
            availableProcessors, memoryGB, recommendedPoolSize, recommendedPoolSize / 4,
            POOL_SIZE_PROPERTY, recommendedPoolSize,
            MIN_IDLE_PROPERTY, recommendedPoolSize / 4
        );
    }
}
