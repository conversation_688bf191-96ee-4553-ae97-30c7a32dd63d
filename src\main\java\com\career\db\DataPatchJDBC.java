package com.career.db;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.career.db.career.MajorPlan;
import com.career.utils.ResultVO;
import com.career.utils.SQLLogUtils;
import com.career.utils.Tools;
import com.career.utils.ZyzdCache;
import com.zsdwf.db.YGPaymentBean;


public class DataPatchJDBC {
	
	static String URL = JDBCConstants.URL;
	static String USER = JDBCConstants.USER;
	static String PASSWD = JDBCConstants.PASSWD;
	public static int PAGE_ROW_CNT = 20;
	public static int PAGE_ROW_CNT_SPEC = 15;
	public static int PAGE_ROW_CNT_TEN = 10;
	
	static {
		try {
			Class.forName("com.mysql.cj.jdbc.Driver");
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
	
	
	
	public static void mainXX(String args[]) {
		try {
			
			int result = (int)Tools.qs_yc(623, 619, 623);
			System.out.println(result);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	
	public static void main(String args[]) {
		try {
			String sfName = "重庆";
			//generateHZBXSql(sfName);
			//generateTagUpdateSql(sfName);
			//generateZnInfoUpdateSql(sfName);
			calQsf(sfName);
			calQsfYc(sfName);
			//updateJHX_table(sfName);
			//calQsfYc(sfName);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}	
	
	public static void updateJHX_table(String sfName) throws Exception {
		Iterator<String> it = JDBC.HM_PROVINCE.keySet().iterator();
		StringBuffer SQL = new StringBuffer();
		System.out.println("xxx");
		int i = 0;
		while(it.hasNext()) {
			i++;
			String key = it.next();
			String value = JDBC.HM_PROVINCE.get(key);
			String cnName = JDBC.HM_PROVINCE_CODE_NAME.get(key);
			
			if(sfName != null && !sfName.equals(cnName)) {
				continue;
			}
			
			SQL.append("UPDATE "+value+"_jh_2025 x LEFT JOIN zyzd_base_university y ON x.yxmc_org = y.yxmc SET x.yx_tags = y.yx_tags, x.yx_tags_all = y.yx_tags_all, x.ind_catg = y.ind_catg, x.ind_nature = y.ind_nature;\r\n"
					+ "DROP TABLE if EXISTS "+value+"_jh_2025x;\r\n"
					+ "CREATE TABLE `"+value+"_jh_2025x` (\r\n"
					+ "	`id` INT(10) NOT NULL AUTO_INCREMENT,\r\n"
					+ "	`sf` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`nf` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`yxdm` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`yxmc` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`yxmc_org` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`zys` INT(10) NULL DEFAULT NULL COMMENT '志愿geshu',\r\n"
					+ "	`znzy` TEXT NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`zyzjhs` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`zyzzdf` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`zyzzdfwc` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`zyz` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`pc` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`pc_code` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`xk` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`xk_code` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`xk_code_org` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`zx` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`jhs` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`lqpc` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`ind_nature` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`ind_catg` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`yxsf` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`yxcs` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci', \r\n"
					+ "	`yx_tags` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`yx_tags_all` TEXT DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`yx_tag_all_for_search` TEXT DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`zdf_2024` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`zdf_2023` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`zdf_2022` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`zdfwc_2024` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`zdfwc_2023` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`zdfwc_2022` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`zgf_2024` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`zgf_2023` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`zgf_2022` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`zgfwc_2024` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`zgfwc_2023` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`zgfwc_2022` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`pjf_2024` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`pjf_2023` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`pjf_2022` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`pjfwc_2024` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`pjfwc_2023` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`pjfwc_2022` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
					+ "	`cnt_company` INT(10) NULL DEFAULT NULL,\r\n"
					+ "	`cnt_employ` INT(10) NULL DEFAULT NULL,\r\n"
					+ "	`znzyls` INT(10) NULL DEFAULT NULL,\r\n"
					+ "	`cnt_grad` FLOAT NULL DEFAULT NULL,\r\n"
					+ "	PRIMARY KEY (`id`) USING BTREE\r\n"
					+ ")\r\n"
					+ "COMMENT='计划'\r\n"
					+ "COLLATE='utf8_general_ci'\r\n"
					+ "ENGINE=InnoDB\r\n"
					+ "ROW_FORMAT=DYNAMIC\r\n"
					+ "AUTO_INCREMENT=11;\r\n"
					+ "\r\n"
					+ "DELETE FROM "+value+"_jh_2025x;\r\n"
					+ " INSERT into "+value+"_jh_2025x (\r\n"
					+ "	sf,\r\n"
					+ "	nf,\r\n"
					+ "	yxdm,\r\n"
					+ "	yxmc,\r\n"
					+ "	yxmc_org,\r\n"
					+ "	zys ,\r\n"
					+ "	znzy ,\r\n"
					+ "	zyzjhs,\r\n"
					+ "	zyzzdf,\r\n"
					+ "	zyzzdfwc,\r\n"
					+ "	zyz,\r\n"
					+ "	pc,\r\n"
					+ "	pc_code,\r\n"
					+ "	xk,\r\n"
					+ "	xk_code,\r\n"
					+ "	xk_code_org,\r\n"
					+ "	zx,\r\n"
					+ "	lqpc,\r\n"
					+ "	ind_nature ,\r\n"
					+ "	ind_catg ,\r\n"
					+ "	yxsf ,\r\n"
					+ "	yxcs ,\r\n"
					+ "	yx_tags ,\r\n"
					+ "	znzyls ,\r\n"
					+ "	cnt_company ,\r\n"
					+ "	cnt_employ ,\r\n"
					+ "	cnt_grad,\r\n"
					+ "	zdf_2024,\r\n"
					+ "	zdf_2023,\r\n"
					+ "	zdf_2022,\r\n"
					+ "	zdfwc_2024,\r\n"
					+ "	zdfwc_2023,\r\n"
					+ "	zdfwc_2022,\r\n"
					+ "	zgf_2024,\r\n"
					+ "	zgf_2023,\r\n"
					+ "	zgf_2022,\r\n"
					+ "	zgfwc_2024,\r\n"
					+ "	zgfwc_2023,\r\n"
					+ "	zgfwc_2022,\r\n"
					+ "	pjf_2024,\r\n"
					+ "	pjf_2023,\r\n"
					+ "	pjf_2022,\r\n"
					+ "	pjfwc_2024,\r\n"
					+ "	pjfwc_2023,\r\n"
					+ "	pjfwc_2022\r\n"
					+ ")SELECT\r\n"
					+ "	sf,\r\n"
					+ "	nf,\r\n"
					+ "	yxdm,\r\n"
					+ "	yxmc,\r\n"
					+ "	yxmc_org,\r\n"
					+ "	COUNT(*) as zys ,\r\n"
					+ "	znzy ,\r\n"
					+ "	zyzjhs,\r\n"
					+ "	zyzzdf,\r\n"
					+ "	MAX(zdfwc) AS zyzzdfwc,\r\n"
					+ "	zyz,\r\n"
					+ "	pc,\r\n"
					+ "	pc_code,\r\n"
					+ "	xk,\r\n"
					+ "	xk_code,\r\n"
					+ "	xk_code_org,\r\n"
					+ "	zx,\r\n"
					+ "	lqpc,\r\n"
					+ "	ind_nature ,\r\n"
					+ "	ind_catg ,\r\n"
					+ "	yxsf ,\r\n"
					+ "	yxcs ,\r\n"
					+ "	yx_tags ,\r\n"
					+ "	min(znzyls),\r\n"
					+ "	cnt_company ,\r\n"
					+ "	cnt_employ ,\r\n"
					+ "	cnt_grad,\r\n"
					+ "	min(zdf_2024) AS zdf_2024,\r\n"
					+ "	min(zdf_2023) AS zdf_2023,\r\n"
					+ "	min(zdf_2022) AS zdf_2022,\r\n"
					+ "	max(zdfwc_2024) AS zdfwc_2024,\r\n"
					+ "	max(zdfwc_2023) AS zdfwc_2023,\r\n"
					+ "	max(zdfwc_2022) AS zdfwc_2022,\r\n"
					+ "	min(zgf_2024) AS zdf_2024,\r\n"
					+ "	min(zgf_2023) AS zdf_2023,\r\n"
					+ "	min(zgf_2022) AS zdf_2022,\r\n"
					+ "	max(zgfwc_2024) AS zdfwc_2024,\r\n"
					+ "	max(zgfwc_2023) AS zdfwc_2023,\r\n"
					+ "	max(zgfwc_2022) AS zdfwc_2022,\r\n"
					+ "	min(pjf_2024) AS pjf_2024,\r\n"
					+ "	min(pjf_2023) AS pjf_2023,\r\n"
					+ "	min(pjf_2022) AS pjf_2022,\r\n"
					+ "	max(pjfwc_2024) AS pjfwc_2024,\r\n"
					+ "	max(pjfwc_2023) AS pjfwc_2023,\r\n"
					+ "	max(pjfwc_2022) AS pjfwc_2022\r\n"
					+ "	\r\n"
					+ "	FROM "+value+"_jh_2025\r\n"
					+ "	GROUP BY \r\n"
					+ "	sf,\r\n"
					+ "	nf,\r\n"
					+ "	yxdm,\r\n"
					+ "	yxmc,\r\n"
					+ "	yxmc_org,\r\n"
					+ "	znzy ,\r\n"
					+ "	zyzjhs,\r\n"
					+ "	zyzzdf,\r\n"
					+ "	zyzzdfwc,\r\n"
					+ "	zyz,\r\n"
					+ "	pc,\r\n"
					+ "	pc_code,\r\n"
					+ "	xk,\r\n"
					+ "	xk_code,\r\n"
					+ "	xk_code_org,\r\n"
					+ "	zx,\r\n"
					+ "	lqpc,\r\n"
					+ "	ind_nature ,\r\n"
					+ "	ind_catg ,\r\n"
					+ "	yxsf ,\r\n"
					+ "	yxcs ,\r\n"
					+ "	yx_tags ,\r\n"
					+ "	cnt_company ,\r\n"
					+ "	cnt_employ ,\r\n"
					+ "	cnt_grad;\r\n"
					+ "	\r\n"
					+ "UPDATE "+value+"_jh_2025x x LEFT JOIN zyzd_base_university y ON x.yxmc_org = y.yxmc SET x.yx_tags = y.yx_tags, x.yx_tags_all = y.yx_tags_all, x.ind_catg = y.ind_catg, x.ind_nature = y.ind_nature;\r\n"
					+ "SELECT * FROM "+value+"_jh_2025x;\r\n"
					+ "\r\n"

					+ "");
			
		}
		
		writeTempFile(new File("F://诊断考试换算//TABt_NCx223234XX_.txt"), SQL);
	}
	
	/**
	 * 针对中外合作办学的字段更新，生成SQL，跑一次。
	 */
	public static void generateHZBXSql(String sfName) {
		Iterator<String> it = JDBC.HM_PROVINCE.keySet().iterator();
		StringBuffer SQL = new StringBuffer();
		System.out.println("START LINE");
		while(it.hasNext()) {
			
			String key = it.next();
			String value = JDBC.HM_PROVINCE.get(key);
			String cnName = JDBC.HM_PROVINCE_CODE_NAME.get(key);
			if(sfName != null && !sfName.equals(cnName)) {
				continue;
			}
			System.out.println("UPDATE "+value+"_jh_2025 x SET x.is_hz = 0;\r\n"
					+ "UPDATE "+value+"_jh_2025 x SET x.is_hz = 1 WHERE x.yxmc LIKE '%中外%' OR x.yxmc LIKE '%合作%' OR x.zymc LIKE '%中外%' OR x.zymc LIKE '%合作%' OR x.zybz LIKE '%中外%' OR x.zybz LIKE '%合作%' or x.yxmc LIKE '%高收费%' OR x.zymc LIKE '%高收费%' OR x.zybz LIKE '%高收费%' OR x.ind_nature LIKE '%合作%'OR x.ind_nature LIKE '%中外%';\r\n"
					+ "");
			
		}
	}
	
	/**
	 * 更新所有省的院校标签
	 */
	public static void generateTagUpdateSql(String sfName) {
		Iterator<String> it = JDBC.HM_PROVINCE.keySet().iterator();
		StringBuffer SQL = new StringBuffer();
		System.out.println("START LINE");
		while(it.hasNext()) {
			
			String key = it.next();
			String value = JDBC.HM_PROVINCE.get(key);
			String cnName = JDBC.HM_PROVINCE_CODE_NAME.get(key);
			if(sfName != null && !sfName.equals(cnName)) {
				continue;
			}
			System.out.println("UPDATE "+value+"_jh_2025 x LEFT JOIN zyzd_base_university y ON x.yxmc_org = y.yxmc SET x.yx_tags_all = y.yx_tags_all;");
			
		}
	}
	
	/**
	 * 更新所有省的组内专业数，组内专业类数，组内计划数
	 * 
	DELETE FROM original_data_znjhs;
	DELETE FROM original_data_znZYLs;
	DELETE FROM original_data_znZYs;
	INSERT INTO original_data_znjhs
	SELECT x.xk,x.pc, x.pc_code, x.yxdm, x.yxmc, x.zyz, SUM(CAST(COALESCE(x.jhs,'0') AS SIGNED)) FROM s5_sc_jh_2025 x GROUP BY x.xk,x.pc, x.pc_code, x.yxdm, x.yxmc, x.zyz;
	INSERT INTO original_data_znZYLs
	SELECT x.xk,x.pc, x.pc_code,x.yxdm, x.yxmc, x.zyz, count(distinct x.zyml) AS cnt FROM s5_sc_jh_2025 x GROUP BY x.xk,x.pc, x.pc_code,x.yxdm, x.yxmc, x.zyz;
	INSERT INTO original_data_znZYs
	SELECT x.xk,x.pc, x.pc_code,x.yxdm, x.yxmc, x.zyz, count(distinct x.zydm) AS cnt FROM s5_sc_jh_2025 x GROUP BY x.xk,x.pc, x.pc_code,x.yxdm, x.yxmc, x.zyz;
	UPDATE s5_sc_jh_2025 x LEFT JOIN original_data_znjhs y ON x.xk = y.xk AND x.pc = y.pc AND x.pc_code = y.pc_code AND x.yxdm = y.yxdm AND x.yxmc = y.yxmc AND x.zyz = y.zyz SET x.znJHs = y.cnt;
	UPDATE s5_sc_jh_2025 x LEFT JOIN original_data_znZYLs y ON x.xk = y.xk AND x.pc = y.pc AND x.pc_code = y.pc_code AND x.yxdm = y.yxdm AND x.yxmc = y.yxmc AND x.zyz = y.zyz SET x.znZYLs = y.cnt;
	UPDATE s5_sc_jh_2025 x LEFT JOIN original_data_znZYs y ON x.xk = y.xk AND x.pc = y.pc AND x.pc_code = y.pc_code AND x.yxdm = y.yxdm AND x.yxmc = y.yxmc AND x.zyz = y.zyz SET x.znZYs = y.cnt;
	 * 
	 */
	public static void generateZnInfoUpdateSql(String sfName) {
		Iterator<String> it = JDBC.HM_PROVINCE.keySet().iterator();
		StringBuffer SQL = new StringBuffer();
		System.out.println("START LINE");
		while(it.hasNext()) {
			
			String key = it.next();
			String value = JDBC.HM_PROVINCE.get(key);
			String cnName = JDBC.HM_PROVINCE_CODE_NAME.get(key);
			
			if(sfName != null && !sfName.equals(cnName)) {
				continue;
			}
			
			System.out.println("DELETE FROM original_data_znjhs;DELETE FROM original_data_znZYLs;DELETE FROM original_data_znZYs;\r\n"
					+ "INSERT INTO original_data_znjhs SELECT x.xk,x.pc, x.pc_code, x.yxdm, x.yxmc, x.zyz, SUM(CAST(COALESCE(x.jhs,'0') AS SIGNED)) FROM "+value+"_jh_2025 x WHERE (x.jhs <> '') GROUP BY x.xk,x.pc, x.pc_code, x.yxdm, x.yxmc, x.zyz;\r\n"
					+ "INSERT INTO original_data_znZYLs SELECT x.xk,x.pc, x.pc_code,x.yxdm, x.yxmc, x.zyz, count(distinct x.zyml) AS cnt FROM "+value+"_jh_2025 x GROUP BY x.xk,x.pc, x.pc_code,x.yxdm, x.yxmc, x.zyz;\r\n"
					+ "INSERT INTO original_data_znZYs SELECT x.xk,x.pc, x.pc_code,x.yxdm, x.yxmc, x.zyz, count(distinct x.zydm) AS cnt FROM "+value+"_jh_2025 x GROUP BY x.xk,x.pc, x.pc_code,x.yxdm, x.yxmc, x.zyz;\r\n"
					+ "UPDATE "+value+"_jh_2025 x SET x.znjhs = 0,x.znzyls = 0, x.znzys = 0;\r\n"
					+ "UPDATE "+value+"_jh_2025 x LEFT JOIN original_data_znjhs y ON x.xk = y.xk AND x.pc = y.pc AND x.pc_code = y.pc_code AND x.yxdm = y.yxdm AND x.yxmc = y.yxmc AND x.zyz = y.zyz SET x.znjhs = y.cnt;\r\n"
					+ "UPDATE "+value+"_jh_2025 x LEFT JOIN original_data_znZYLs y ON x.xk = y.xk AND x.pc = y.pc AND x.pc_code = y.pc_code AND x.yxdm = y.yxdm AND x.yxmc = y.yxmc AND x.zyz = y.zyz SET x.znzyls = y.cnt;\r\n"
					+ "UPDATE "+value+"_jh_2025 x LEFT JOIN original_data_znZYs y ON x.xk = y.xk AND x.pc = y.pc AND x.pc_code = y.pc_code AND x.yxdm = y.yxdm AND x.yxmc = y.yxmc AND x.zyz = y.zyz SET x.znzys = y.cnt;");
			
		}
	}
	
	/**
	 * 计算趋势分，更新数据库（批量操作版本）
	 * @param sfName 省份名称
	 * @throws SQLException 
	 */
	public static void calQsf(String sfName) throws SQLException {
	    ZyzdJDBC zyzdJDBC = new ZyzdJDBC();
	    Iterator<String> it = JDBC.HM_PROVINCE.keySet().iterator();
	    System.out.println("START LINE");
	    int LATEST_JH_YEAR = 2025;
	    Connection conn = null;
	    
	    try {
	        conn = DatabaseUtils.getConnection();
	        
	        while(it.hasNext()) {
	            String key = it.next();
	            String value = JDBC.HM_PROVINCE.get(key);
	            String cnName = JDBC.HM_PROVINCE_CODE_NAME.get(key);
	            String LK_XK_CODE = "1";
	            String WK_XK_CODE = "W";
	            
	            if(sfName != null && !sfName.equals(cnName)) {
	                continue;
	            }
	            
	            System.out.println("execute: " + cnName);
	            
	            List<ZDKSRank> listLK2025 = zyzdJDBC.getAllZdksRankByKL(LATEST_JH_YEAR, cnName, LK_XK_CODE);	
	            List<ZDKSRank> listWK2025 = zyzdJDBC.getAllZdksRankByKL(LATEST_JH_YEAR, cnName, WK_XK_CODE);			
	            
	            // 处理2022-2024年的数据
	            for(int pastYear = LATEST_JH_YEAR - 3; pastYear <= LATEST_JH_YEAR - 1; pastYear++) {
	                List<ZDKSRank> lk = zyzdJDBC.getAllZdksRankByKL(pastYear, cnName, LK_XK_CODE);
	                List<ZDKSRank> wk = zyzdJDBC.getAllZdksRankByKL(pastYear, cnName, WK_XK_CODE);
	                
	                HashMap<Integer, Integer> MAP_LK = mapScoresByRank(lk, listLK2025);
	                HashMap<Integer, Integer> MAP_WL = mapScoresByRank(wk, listWK2025);
	                
	                // 确定年份后缀
	                String abc = getYearSuffix(pastYear, LATEST_JH_YEAR);
	                
	                // 批量更新理科数据
	                batchUpdateQsf(conn, value, abc, MAP_LK, LK_XK_CODE, pastYear);
	                
	                // 批量更新文科数据
	                batchUpdateQsf(conn, value, abc, MAP_WL, WK_XK_CODE, pastYear);
	            }
	            
	            // 提交事务
	            System.out.println("完成省份：" + cnName);
	        }
	        
	    } catch (SQLException e) {
	        e.printStackTrace();
	    } finally {
	        if (conn != null) {
	            try {
	                
	                conn.close();
	            } catch (SQLException closeEx) {
	                closeEx.printStackTrace();
	            }
	        }
	    }
	}

	/**
	 * 批量更新趋势分数据
	 * @param conn 数据库连接
	 * @param tablePrefix 表前缀
	 * @param yearSuffix 年份后缀 (a/b/c)
	 * @param scoreMap 分数映射关系
	 * @param xkCode 学科代码
	 * @param pastYear 历史年份
	 * @throws SQLException
	 */
	private static void batchUpdateQsf(Connection conn, String tablePrefix, String yearSuffix, 
	                                  HashMap<Integer, Integer> scoreMap, String xkCode, int pastYear) throws SQLException {
	    
	    if (scoreMap.isEmpty()) {
	        return;
	    }
	    System.out.println("tablePrefix->"+tablePrefix);
	    String updateSql = "UPDATE " + tablePrefix + "_jh_2025 SET qsf_" + yearSuffix + " = ? " +
	                      "WHERE xk_code_org LIKE ? AND zdf_" + pastYear + " = ?";
	    
	    PreparedStatement pstmt = null;
	    try {
	        pstmt = conn.prepareStatement(updateSql);
	        
	        for (Map.Entry<Integer, Integer> entry : scoreMap.entrySet()) {
	            Integer pastScore = entry.getKey();
	            Integer score2025 = entry.getValue();
	            
	            pstmt.setInt(1, score2025);
	            pstmt.setString(2, "%" + xkCode + "%");
	            pstmt.setInt(3, pastScore);
	            pstmt.addBatch();
	        }
	        
	        // 执行批量更新
	        SQLLogUtils.printSQL(pstmt);
	        int[] updateCounts = pstmt.executeBatch();
	        Tools.println("批量更新完成，影响行数：" + updateCounts.length + "，学科：" + xkCode + "，年份：" + yearSuffix);
	        
	    } finally {
	        if (pstmt != null) {
	            pstmt.close();
	        }
	    }
	}

	/**
	 * 根据历史年份获取年份后缀
	 * @param pastYear 历史年份
	 * @param latestYear 最新年份
	 * @return 年份后缀 (a/b/c)
	 */
	private static String getYearSuffix(int pastYear, int latestYear) {
	    int yearDiff = latestYear - pastYear;
	    switch (yearDiff) {
	        case 3: return "c"; 
	        case 2: return "b"; 
	        case 1: return "a"; 
	        default: return "";
	    }
	}
	
	
	/**
	 * 更新趋势分预测
	 * @throws SQLException 
	 */
	public static void calQsfYc(String name) throws SQLException {
		ZyzdJDBC zyzdJDBC = new ZyzdJDBC();
		Iterator<String> it = JDBC.HM_PROVINCE.keySet().iterator();
		StringBuffer SQL = new StringBuffer();
		System.out.println("START LINE");
		int LATEST_JH_YEAR = 2025;
		Connection conn = null;
		
		conn = DatabaseUtils.getConnection();
		PreparedStatement pstmt = null;
		
		while(it.hasNext()) {
			String key = it.next();
			String value = JDBC.HM_PROVINCE.get(key);
			String cnName = JDBC.HM_PROVINCE_CODE_NAME.get(key);
			
			if(name != null) {
				if(!name.equals(cnName)) {
					continue;
				}
			}
			
			Tools.println("execute:"+cnName);
			
			String updateSql = "UPDATE " + value + "_jh_"+LATEST_JH_YEAR+" x SET x.qsf = ? WHERE x.id = ?";
			pstmt = conn.prepareStatement(updateSql);
			
			List<JHBean> allJHBean = loadAllJhData(LATEST_JH_YEAR, value);
			for(JHBean bean : allJHBean) {
				int scoreQSA = bean.getQsf_a();
				int scoreQSB = bean.getQsf_b();
				int scoreQSC = bean.getQsf_c();
				int result = (int)Tools.qs_yc(scoreQSA, scoreQSB, scoreQSC);
				//SQL.append("update "+value+"_jh_2025 x set x.qsf =" + result + " where x.id = " + bean.getId() + ";\r\n");
				
				// 准备批量更新的 SQL 语句
				pstmt.setInt(1, result);
                pstmt.setInt(2, bean.getId());
                pstmt.addBatch();

			}
			// 执行批量更新
			SQLLogUtils.printSQL(pstmt);
            pstmt.executeLargeBatch();
            pstmt.close();
		}
	}
	
	public static HashMap<Integer, Integer> mapScoresByRank(List<ZDKSRank> list2022, List<ZDKSRank> list2025) {
        HashMap<Integer, Integer> scoreMap = new HashMap<>();
        
        // 遍历2022年的数据
        for (ZDKSRank rank2022 : list2022) {
            int score2022 = rank2022.getScore();
            int wc2022 = rank2022.getWc();
            
            // 初始化最小差值和对应的2025年分数
            int minDiff = Integer.MAX_VALUE;
            int closestScore2025 = 0;
            
            // 遍历2025年的数据，寻找最接近的位次
            for (ZDKSRank rank2025 : list2025) {
                int wc2025 = rank2025.getWc();
                int currentDiff = Math.abs(wc2025 - wc2022);
                
                // 如果找到更接近的位次，更新结果
                if (currentDiff < minDiff) {
                    minDiff = currentDiff;
                    closestScore2025 = rank2025.getScore();
                }
            }
            
            // 将2022年分数映射到找到的2025年分数
            scoreMap.put(score2022, closestScore2025);
        }
        
        return scoreMap;
    }
	
	
	public List<JHBean> searchJhData(int jhYear, String sfCode, String xkCode, String pc, String pc_code) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SEARCH_CONDITION = "SELECT * FROM " + sfCode + "_jh_" + jhYear + " x WHERE x.xk = x.pc = ? and x.pc_code = ? group by " ;
			
			ps = conn.prepareStatement(SEARCH_CONDITION); 
			int index = 1;
			ps.setString(index++, "%"+xkCode+"%");
			ps.setString(index++, pc);
			ps.setString(index++, pc_code);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL("searchJhData>>>>>>>>", ps);
			JHBean bean = null;
			while (rs.next()) {
				list.add(ZyzdFormJDBC.setJHBeanAll(dataYear, rs));
			}
			
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public static List<JHBean> loadAllJhData(int jhYear, String sfCode) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SEARCH_CONDITION = "SELECT * FROM " + sfCode + "_jh_" + jhYear ;
			
			ps = conn.prepareStatement(SEARCH_CONDITION); 
			rs = ps.executeQuery();
			SQLLogUtils.printSQL("loadJhData>>>>>>>>", ps);
			while (rs.next()) {
				list.add(ZyzdFormJDBC.setJHBeanAll(dataYear, rs));
			}
			
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public static void insert_zyzd_base_yxzy_statistics(List<YxzyStatistics> insertSQL) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into zyzd_base_yxzy_statistics(group_id, table_id, item_id, item_sort, column_title, column_title_standare, column_value, column_sort) values(?,?,?,?,?,?,?,?);";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			for(YxzyStatistics form : insertSQL) {
				int index = 1;
				ps.setString(index++, form.getGroup_id());
                ps.setString(index++, form.getTable_id());
                ps.setString(index++, form.getItem_id());
                ps.setInt(index++, form.getItem_sort());
                ps.setString(index++, form.getColumn_title());
                ps.setString(index++, form.getColumn_title_standare());
                ps.setString(index++, form.getColumn_value());
                ps.setInt(index++, form.getColumn_sort());
				ps.addBatch();
			}
			ps.executeLargeBatch();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public static List<String> insert_zyzd_base_yxzy_statistics_yxzy() {
        List<String> sqlStatements = new ArrayList<>();
        Set<String> uniquePairs = new HashSet<>(); // 用于存储唯一的(A, B)组合
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        StringBuffer SQL = new StringBuffer();

        try {
            // 获取数据库连接
            conn = DatabaseUtils.getConnection();

            // 查询column_title_standare为“院校名称”的所有记录
            String querySQL = "SELECT item_id, column_value FROM zyzd_base_yxzy_statistics WHERE column_title_standare = '学校名称' OR column_title_standare = '专业名称';";
            ps = conn.prepareStatement(querySQL);

            rs = ps.executeQuery();

            // 处理结果集
            while (rs.next()) {
                String item_id = rs.getString("item_id");
                String column_value = rs.getString("column_value");
                // 按逗号或顿号分割字符串
                String[] values = column_value.split("[，,]");
                for (String value : values) {
                    String trimmedValue = Tools.trim(value);
                    // 检查(A, B)组合是否已经存在
                    String pair = trimmedValue + "_" + item_id;
                    if (!uniquePairs.contains(pair)) {
                        uniquePairs.add(pair); // 添加到集合中
                        // 生成SQL语句
                        String sql = String.format("INSERT INTO zyzd_base_yxzy_statistics_yxzy(yxzy, item_id) VALUES('%s', '%s');", trimmedValue, item_id);
                        sqlStatements.add(sql);
                        SQL.append(sql);
                    }
                }
            }
            
            
            writeTempFile(new File("D:\\base_data\\YXZY_SQL.txt"), SQL);
        } catch (Exception ex) {
            ex.printStackTrace(); // 打印异常信息
        } finally {
            // 关闭所有连接
            closeAllConnection(conn, ps, rs);
        }

        return sqlStatements;
    }
	
	
	private static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	
	
	/**
	 * 计算趋势分，更新数据库
	 * @param insertSQL
	 * @throws SQLException 
	 */
	public static void calQsf_ORG(String sfName) throws SQLException {
		ZyzdJDBC zyzdJDBC = new ZyzdJDBC();
		Iterator<String> it = JDBC.HM_PROVINCE.keySet().iterator();
		StringBuffer SQL = new StringBuffer();
		System.out.println("START LINE");
		int LATEST_JH_YEAR = 2025;
		List<String> sqlList = new ArrayList<>();
		Connection conn = null;
		
		
		
		while(it.hasNext()) {
			
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			conn.setAutoCommit(false);
			String key = it.next();
			String value = JDBC.HM_PROVINCE.get(key);
			String cnName = JDBC.HM_PROVINCE_CODE_NAME.get(key);
			String LK_XK_CODE = "1";
			String WK_XK_CODE = "W";
			if(sfName != null && !sfName.equals(cnName)) {
				continue;
			}
			
			List<ZDKSRank> listLK2025 = zyzdJDBC.getAllZdksRankByKL(LATEST_JH_YEAR, cnName, LK_XK_CODE);	
			List<ZDKSRank> listWK2025 = zyzdJDBC.getAllZdksRankByKL(LATEST_JH_YEAR, cnName, WK_XK_CODE);			
			
			//2022年
			for(int pastYear = LATEST_JH_YEAR - 3; pastYear <= LATEST_JH_YEAR - 1; pastYear++) {
				List<ZDKSRank> lk = zyzdJDBC.getAllZdksRankByKL(pastYear, cnName, LK_XK_CODE);
				List<ZDKSRank> wk = zyzdJDBC.getAllZdksRankByKL(pastYear, cnName, WK_XK_CODE);
				
				HashMap<Integer, Integer> MAP_LK = mapScoresByRank(lk, listLK2025);
				HashMap<Integer, Integer> MAP_WL = mapScoresByRank(wk, listWK2025);
				
				Iterator<Integer> itKeys = MAP_LK.keySet().iterator();
				while(itKeys.hasNext()) {
					Integer past = itKeys.next();
					Integer score2025 = MAP_LK.get(past);
					
					String abc = "";
					if(pastYear == LATEST_JH_YEAR - 3) {
						abc = "c";
					}else if(pastYear == LATEST_JH_YEAR - 2) {
						abc = "b";
					}else if(pastYear == LATEST_JH_YEAR - 1) {
						abc = "a";
					}
					SQL.append("update "+value+"_jh_2025 x set x.qsf_" + abc + " = "+score2025.intValue() + " where x.xk_code_org like '%"+LK_XK_CODE+"%' and x.zdf_" + pastYear + " = " + past.intValue()+";\r\n");
				}
				
				itKeys = MAP_WL.keySet().iterator();
				while(itKeys.hasNext()) {
					Integer past = itKeys.next();
					Integer score2025 = MAP_WL.get(past);
					
					String abc = "";
					if(pastYear == LATEST_JH_YEAR - 3) {
						abc = "c";
					}else if(pastYear == LATEST_JH_YEAR - 2) {
						abc = "b";
					}else if(pastYear == LATEST_JH_YEAR - 1) {
						abc = "a";
					}
					SQL.append("update "+value+"_jh_2025 x set x.qsf_" + abc + " = "+score2025.intValue() + " where x.xk_code_org like '%"+WK_XK_CODE+"%' and x.zdf_" + pastYear + " = " + past.intValue()+";\r\n");
				}
				
			}
			
			writeTempFile(new File("D:\\base_data\\YXZY_SQL_QSF.txt"), SQL);
		}
	}

	
	
	private static void closeAllConnection(Connection conn, PreparedStatement ps, ResultSet rs) {
		/**
		try {
			if (rs != null)
				rs.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (ps != null)
				ps.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (conn != null)
				conn.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		rs = null;
		ps = null;
		conn = null;
		*/
		DatabaseUtils.closeAllResources(rs, ps, conn);
	}
}
