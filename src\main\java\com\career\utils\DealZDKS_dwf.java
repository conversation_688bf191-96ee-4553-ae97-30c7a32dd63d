package com.career.utils;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Random;

import com.career.db.CityBean;
import com.career.db.JDBC;

public class DealZDKS_dwf {

	static HashMap<String, String> xk_code_map = new HashMap<>();
	static {
		xk_code_map.put("物理", "14387D");
		xk_code_map.put("理科", "14387D");
		xk_code_map.put("历史", "WLRKQG");
		xk_code_map.put("文科", "WLRKQG");
		xk_code_map.put("综合", "123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");
	}
	
	
	public static void main(String args[]) throws Exception {
		

		StringBuffer SQL = new StringBuffer();
		List<ZyzdProvince> list = ZyzdCache.getAllProvince();
		for(ZyzdProvince x : list) {
			for(int i = 2024; i<=2024;i++) {
			String tableName = x.getTableName() + "_" + i;
			if(tableName.startsWith("C1") || tableName.startsWith("C1")) {
				continue;
			}
			
			SQL.append("ALTER TABLE `"+tableName+"`\r\n"
					+ "	ADD COLUMN `ind_nature` VARCHAR(255) NULL DEFAULT NULL AFTER `yxcs`;\r\n"
					+ "	\r\n"
					+ "ALTER TABLE `"+tableName+"`\r\n"
					+ "	ADD COLUMN `ind_catg` VARCHAR(255) NULL DEFAULT NULL AFTER `ind_nature`;\r\n"
					+ "	\r\n"
					+ "ALTER TABLE `"+tableName+"`\r\n"
					+ "	ADD COLUMN `cnt_grad` FLOAT NULL DEFAULT NULL AFTER `ind_catg`;\r\n"
					+ "\r\n"
					+ "ALTER TABLE `"+tableName+"`\r\n"
					+ "	ADD COLUMN `cnt_company` INT NULL DEFAULT NULL AFTER `cnt_grad`;\r\n"
					+ "	\r\n"
					+ "ALTER TABLE `"+tableName+"`\r\n"
					+ "	ADD COLUMN `cnt_employ` INT(10) NULL DEFAULT NULL AFTER `cnt_company`;\r\n"
					+ "	\r\n"
					+ "ALTER TABLE `"+tableName+"`\r\n"
					+ "	ADD COLUMN `lqpc` VARCHAR(50) NULL DEFAULT NULL AFTER `cnt_employ`;\r\n"
					+ "	\r\n"
					+ "ALTER TABLE `"+tableName+"`\r\n"
					+ "	ADD COLUMN `lqpc_ext` VARCHAR(50) NULL DEFAULT NULL AFTER `lqpc`;\r\n");

			
				SQL.append("UPDATE "+tableName+" x LEFT JOIN zyzd_base_university y ON x.yxmc_org = y.yxmc SET x.ind_nature = y.ind_nature, x.ind_catg = y.ind_catg, x.cnt_grad = y.cnt_grad, x.cnt_company = y.cnt_company, x.cnt_employ = y.cnt_employ;\r\n");
				
			
			
			//SQL.append("UPDATE "+tableName+"x x LEFT JOIN zyzd_base_university y ON x.yxmc_org = y.yxmc SET x.yxsf = y.position_sf, x.yxcs=y.position_cs;\r\n");
			SQL.append("UPDATE "+tableName+" x LEFT JOIN zyzd_base_university y ON x.yxmc_org = y.yxmc SET x.yxsf = y.position_sf, x.yxcs=y.position_cs;\r\n");
			
			
			
			}
			
		}
		
		System.out.println(SQL);
		
	}
	
	public static void runExcept33(String sfcode, String city, String zdpc, ZDScore zdscore, PCXScore pcxscore) {
		String provinceCode = JDBC.HM_PROVINCE.get(sfcode); //S5_SC
		String provinceName = JDBC.HM_PROVINCE_CODE_NAME.get(sfcode); //四川
		List<CityBean> cityList = JDBC.HM_PROVINCE_CITY.get(provinceName);
		
		StringBuffer SQL = new StringBuffer();
		
		List<String> listResult = new ArrayList<>();

		if(city != null) {
			cityList = new ArrayList<>();
			CityBean cityBean = new CityBean();
			cityBean.setCityNameExt(city);
			cityList.add(cityBean);
		}
		
		for(CityBean element : cityList) {
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "理科", zdscore.getZk_wl(), zdscore.getZk2_wl(), pcxscore.getZk_wl(), pcxscore.getZk2_wl()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "理科", zdscore.getZk2_wl(), zdscore.getZk3_wl(), pcxscore.getZk2_wl(), pcxscore.getZk3_wl()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "理科", zdscore.getZk3_wl(), zdscore.getBen2_wl(), pcxscore.getZk3_wl(), pcxscore.getBen2_wl()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "理科", zdscore.getBen2_wl(), zdscore.getBen1_wl(), pcxscore.getBen2_wl(), pcxscore.getBen1_wl()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "理科", zdscore.getBen1_wl(), zdscore.getBen211_wl(), pcxscore.getBen1_wl(), pcxscore.getBen211_wl()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "理科", zdscore.getBen211_wl(), zdscore.getBen985_wl(), pcxscore.getBen211_wl(), pcxscore.getBen985_wl()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "理科", zdscore.getBen985_wl(), zdscore.getBenc9_wl(), pcxscore.getBen985_wl(), pcxscore.getBenc9_wl()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "理科", zdscore.getBenc9_wl(), zdscore.getBenQB_wl(), pcxscore.getBenc9_wl(), pcxscore.getBenQB_wl()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "理科", zdscore.getBenQB_wl(), zdscore.getZGF_wl(), pcxscore.getBenQB_wl(), pcxscore.getZGF_wl()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "理科", zdscore.getZGF_wl(), zdscore.getMF_wl(), pcxscore.getZGF_wl(), pcxscore.getMF_wl()));
			
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "文科", zdscore.getZk_ls(), zdscore.getZk2_ls(), pcxscore.getZk_ls(), pcxscore.getZk2_ls()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "文科", zdscore.getZk2_ls(), zdscore.getZk3_ls(), pcxscore.getZk2_ls(), pcxscore.getZk3_ls()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "文科", zdscore.getZk3_ls(), zdscore.getBen2_ls(), pcxscore.getZk3_ls(), pcxscore.getBen2_ls()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "文科", zdscore.getBen2_ls(), zdscore.getBen1_ls(), pcxscore.getBen2_ls(), pcxscore.getBen1_ls()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "文科", zdscore.getBen1_ls(), zdscore.getBen211_ls(), pcxscore.getBen1_ls(), pcxscore.getBen211_ls()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "文科", zdscore.getBen211_ls(), zdscore.getBen985_ls(), pcxscore.getBen211_ls(), pcxscore.getBen985_ls()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "文科", zdscore.getBen985_ls(), zdscore.getBenc9_ls(), pcxscore.getBen985_ls(), pcxscore.getBenc9_wl()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "文科", zdscore.getBenc9_ls(), zdscore.getBenQB_ls(), pcxscore.getBenc9_ls(), pcxscore.getBenQB_ls()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "文科", zdscore.getBenQB_ls(), zdscore.getZGF_ls(), pcxscore.getBenQB_ls(), pcxscore.getZGF_ls()));
			listResult.addAll(runConvertLatest(provinceName, element.getCityNameExt(), zdpc, "文科", zdscore.getZGF_ls(), zdscore.getMF_ls(), pcxscore.getZGF_ls(), pcxscore.getMF_ls()));
		}
		
		for(String str : listResult) {
			SQL.append(str);
		}
		
		writeTempFile(new File("F://诊断考试换算//SC_GK_"+provinceCode+"_"+zdpc+".txt"), SQL);
	}
	
	public static void s5_2() {
		ZDScore zdscore = new ZDScore(); 
		PCXScore pcxscore = new PCXScore(); 
		

		zdscore.setZk_ls(180);
		zdscore.setZk_wl(296);
		zdscore.setZk2_ls(180);
		zdscore.setZk2_wl(407);
		zdscore.setZk3_ls(180);
		zdscore.setZk3_wl(476);
		
		zdscore.setBen2_ls(464);//47
		zdscore.setBen2_wl(542); //430
		zdscore.setBen1_ls(536); //540
		zdscore.setBen1_wl(627); //496
		
		zdscore.setBen211_ls(588);//
		zdscore.setBen211_wl(704);//
		zdscore.setBen985_ls(588);//
		zdscore.setBen985_wl(765);//
		zdscore.setBenc9_ls(588);//
		zdscore.setBenc9_wl(802);//
		
		zdscore.setBenQB_ls(650);
		zdscore.setBenQB_wl(850);
		
		zdscore.setZGF_ls(660);
		zdscore.setZGF_wl(906);
		
		zdscore.setMF_ls(685);
		zdscore.setMF_wl(942);
		
		
		pcxscore.setZk_ls(150);
		pcxscore.setZk_wl(150);
		pcxscore.setZk2_ls(150);
		pcxscore.setZk2_wl(247);
		pcxscore.setZk3_ls(150);
		pcxscore.setZk3_wl(335);
		
		pcxscore.setBen2_ls(458);
		pcxscore.setBen2_wl(412);
		pcxscore.setBen1_ls(527);
		pcxscore.setBen1_wl(492);
		
		pcxscore.setBen211_ls(579);
		pcxscore.setBen211_wl(565);
		pcxscore.setBen985_ls(579);
		pcxscore.setBen985_wl(617);
		pcxscore.setBenc9_ls(579);
		pcxscore.setBenc9_wl(642);
		
		pcxscore.setBenQB_ls(639);
		pcxscore.setBenQB_wl(666);
		
		pcxscore.setZGF_ls(639);
		pcxscore.setZGF_wl(686);
		
		pcxscore.setMF_ls(639);
		pcxscore.setMF_wl(695);
		
		runExcept33("S5", "成都", "等位分2", zdscore, pcxscore);
	}
	
	
	public static List<String> runConvertLatest(String sf, String city, String zdpc, String kl, int scoreZDFrom, int scoreZDTo, int gaokao23Start, int gaokao23To) {
		List<String> list = new ArrayList<>();
		String provinceCode = JDBC.HM_PROVINCE_CODE.get(sf);
		double newStart = gaokao23Start;
		for(int i = scoreZDFrom;i < scoreZDTo;i++) {
			newStart = Arith.add(newStart, Arith.div(gaokao23To - gaokao23Start, scoreZDTo - scoreZDFrom)); 
			if(newStart > gaokao23To) {
				newStart = gaokao23To;
			}
			list.add("INSERT INTO "+provinceCode+"_zdks_score_convert(nf, sf, city, ZDPC, KL, KL_CODE, SCORE_FROM, SCORE_TO) VALUES(2024, '"+sf+"', '"+city+"', '"+zdpc+"', '"+kl+"', '"+xk_code_map.get(kl)+"' , "+i+","+Math.round(newStart)+" );\r\n");
			
		}
		
		return list;
		
	}
	
	public static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
}
