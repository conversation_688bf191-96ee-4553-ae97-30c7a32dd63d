package com.career.db;

import java.util.Date;

public class CardBean {
	private String id;
	private String saas_id;
	private int c_f_level;
	
	private String passwd;
	private String prov;
	private String city;
	private String xk;
	private int score;
	private int score_org;
	private int form_dnld_cnt;

	public int getScore_org() {
		return score_org;
	}
	public void setScore_org(int score_org) {
		this.score_org = score_org;
	}
	private Date create;
	private Date active;
	private Date lastLogin;
	private Date expire;
	private int year;
	private int status;
	private int review_ai_cnt;
	private int review_maual_cnt;
	private int remote_tb_cnt;
	private String allowed_menu_page;
	
	private String remark;
	private String phone;
	private String orderId;
	private String md;
	private String openID;
	private String admin;
	private String sysInd;
	private String desc;
	private String allowedProv;
	private String version;
	private String bossId;
	private String nickName;
	private String introduce;
	private Date introduceTm;
	private String agent;
	private Date agentTm;
	private String tokenId;
	private Date last_modified;

	public String getAllowed_menu_page() {
		return allowed_menu_page;
	}
	public void setAllowed_menu_page(String allowed_menu_page) {
		this.allowed_menu_page = allowed_menu_page;
	}
	public int getReview_ai_cnt() {
		return review_ai_cnt;
	}
	public void setReview_ai_cnt(int review_ai_cnt) {
		this.review_ai_cnt = review_ai_cnt;
	}
	public int getReview_maual_cnt() {
		return review_maual_cnt;
	}
	public void setReview_maual_cnt(int review_maual_cnt) {
		this.review_maual_cnt = review_maual_cnt;
	}
	public int getRemote_tb_cnt() {
		return remote_tb_cnt;
	}
	public void setRemote_tb_cnt(int remote_tb_cnt) {
		this.remote_tb_cnt = remote_tb_cnt;
	}
	public int getC_f_level() {
		return c_f_level;
	}
	public void setC_f_level(int c_f_level) {
		this.c_f_level = c_f_level;
	}
	public String getSaas_id() {
		return saas_id;
	}
	public void setSaas_id(String saas_id) {
		this.saas_id = saas_id;
	}
	public int getForm_dnld_cnt() {
		return form_dnld_cnt;
	}
	public void setForm_dnld_cnt(int form_dnld_cnt) {
		this.form_dnld_cnt = form_dnld_cnt;
	}
	public Date getLast_modified() {
		return last_modified;
	}
	public void setLast_modified(Date last_modified) {
		this.last_modified = last_modified;
	}
	public String getTokenId() {
		return tokenId;
	}
	public void setTokenId(String tokenId) {
		this.tokenId = tokenId;
	}
	public String getAgent() {
		return agent;
	}
	public void setAgent(String agent) {
		this.agent = agent;
	}
	public Date getAgentTm() {
		return agentTm;
	}
	public void setAgentTm(Date agentTm) {
		this.agentTm = agentTm;
	}
	public Date getIntroduceTm() {
		return introduceTm;
	}
	public void setIntroduceTm(Date introduceTm) {
		this.introduceTm = introduceTm;
	}
	public String getBossId() {
		return bossId;
	}
	public void setBossId(String bossId) {
		this.bossId = bossId;
	}
	public String getNickName() {
		return nickName;
	}
	public void setNickName(String nickName) {
		this.nickName = nickName;
	}
	public String getIntroduce() {
		return introduce;
	}
	public void setIntroduce(String introduce) {
		this.introduce = introduce;
	}
	public String getVersion() {
		return version;
	}
	public void setVersion(String version) {
		this.version = version;
	}
	public String getAllowedProv() {
		return allowedProv;
	}
	public void setAllowedProv(String allowedProv) {
		this.allowedProv = allowedProv;
	}
	public Date getExpire() {
		return expire;
	}
	public void setExpire(Date expire) {
		this.expire = expire;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public int getScore() {
		return score;
	}
	public void setScore(int score) {
		this.score = score;
	}
	public String getPasswd() {
		return passwd;
	}
	public void setPasswd(String passwd) {
		this.passwd = passwd;
	}
	public String getProv() {
		return prov;
	}
	public void setProv(String prov) {
		this.prov = prov;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public String getXk() {
		return xk;
	}
	public void setXk(String xk) {
		this.xk = xk;
	}
	public Date getCreate() {
		return create;
	}
	public void setCreate(Date create) {
		this.create = create;
	}
	public Date getActive() {
		return active;
	}
	public void setActive(Date active) {
		this.active = active;
	}
	public Date getLastLogin() {
		return lastLogin;
	}
	public void setLastLogin(Date lastLogin) {
		this.lastLogin = lastLogin;
	}
	public int getYear() {
		return year;
	}
	public void setYear(int year) {
		this.year = year;
	}
	public int getStatus() {
		return status;
	}
	public void setStatus(int status) {
		this.status = status;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getOrderId() {
		return orderId;
	}
	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}
	public String getMd() {
		return md;
	}
	public void setMd(String md) {
		this.md = md;
	}

	public String getOpenID() {
		return openID;
	}
	public void setOpenID(String openID) {
		this.openID = openID;
	}
	public String getAdmin() {
		return admin;
	}
	public void setAdmin(String admin) {
		this.admin = admin;
	}
	public String getSysInd() {
		return sysInd;
	}
	public void setSysInd(String sysInd) {
		this.sysInd = sysInd;
	}
	public String getDesc() {
		return desc;
	}
	public void setDesc(String desc) {
		this.desc = desc;
	}

}
