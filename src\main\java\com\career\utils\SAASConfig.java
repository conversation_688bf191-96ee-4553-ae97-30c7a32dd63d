package com.career.utils;

public class SAASConfig {
	
	private String saas_id;
	private String company_name;
	private String server_name;
	private String page_header_title;
	private String page_title;
	private String page_slogan;
	private String page_sys_short_name;
	private String page_icon_url;
	private String page_logo_name;
	private String color_left_menu;
	private String color_top_header;
	private String admin_id;
	private String admin_passwd;
	private String pay_cert_uri;
	private String card_open_sf;
	private String saas_uion_id;
	private int card_restrict;
	

	public String getSaas_uion_id() {
		return saas_uion_id;
	}
	public void setSaas_uion_id(String saas_uion_id) {
		this.saas_uion_id = saas_uion_id;
	}
	public String getCard_open_sf() {
		return card_open_sf;
	}
	public void setCard_open_sf(String card_open_sf) {
		this.card_open_sf = card_open_sf;
	}
	public String getPay_cert_uri() {
		return pay_cert_uri;
	}
	public void setPay_cert_uri(String pay_cert_uri) {
		this.pay_cert_uri = pay_cert_uri;
	}
	public String getAdmin_id() {
		return admin_id;
	}
	public void setAdmin_id(String admin_id) {
		this.admin_id = admin_id;
	}
	public String getAdmin_passwd() {
		return admin_passwd;
	}
	public void setAdmin_passwd(String admin_passwd) {
		this.admin_passwd = admin_passwd;
	}
	public int getCard_restrict() {
		return card_restrict;
	}
	public void setCard_restrict(int card_restrict) {
		this.card_restrict = card_restrict;
	}
	public String getSaas_id() {
		return saas_id;
	}
	public void setSaas_id(String saas_id) {
		this.saas_id = saas_id;
	}
	public String getCompany_name() {
		return company_name;
	}
	public void setCompany_name(String company_name) {
		this.company_name = company_name;
	}
	public String getColor_left_menu() {
		return color_left_menu;
	}
	public void setColor_left_menu(String color_left_menu) {
		this.color_left_menu = color_left_menu;
	}
	public String getColor_top_header() {
		return color_top_header;
	}
	public void setColor_top_header(String color_top_header) {
		this.color_top_header = color_top_header;
	}
	public String getServer_name() {
		return server_name;
	}
	public void setServer_name(String server_name) {
		this.server_name = server_name;
	}
	public String getPage_header_title() {
		return page_header_title;
	}
	public void setPage_header_title(String page_header_title) {
		this.page_header_title = page_header_title;
	}
	public String getPage_title() {
		return page_title;
	}
	public void setPage_title(String page_title) {
		this.page_title = page_title;
	}
	public String getPage_slogan() {
		return page_slogan;
	}
	public void setPage_slogan(String page_slogan) {
		this.page_slogan = page_slogan;
	}
	public String getPage_sys_short_name() {
		return page_sys_short_name;
	}
	public void setPage_sys_short_name(String page_sys_short_name) {
		this.page_sys_short_name = page_sys_short_name;
	}
	public String getPage_icon_url() {
		return page_icon_url;
	}
	public void setPage_icon_url(String page_icon_url) {
		this.page_icon_url = page_icon_url;
	}
	public String getPage_logo_name() {
		return page_logo_name;
	}
	public void setPage_logo_name(String page_logo_name) {
		this.page_logo_name = page_logo_name;
	}

}
