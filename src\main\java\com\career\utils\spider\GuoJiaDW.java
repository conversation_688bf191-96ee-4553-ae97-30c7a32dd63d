package com.career.utils.spider;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.career.utils.HttpSendUtils;

public class GuoJiaDW {
	
	
	//爬取国家电网每篇文章的表格数据
	public static void runxxxx() throws Exception {
		BufferedReader bw = new BufferedReader(new FileReader("D:\\国家电网爬虫\\2025第二批\\page1.txt"));
		StringBuffer universityContent = new StringBuffer();
		String str = null;
		while ((str = bw.readLine()) != null) {
			universityContent.append(str);
		}
		
		JSONObject object = JSONObject.parseObject(universityContent.toString());
		JSONObject objectretdata = object.getJSONObject("retdata");
		JSONObject recr_dynas_data = objectretdata.getJSONObject("recr_dynas_data");
	
		JSONArray array = recr_dynas_data.getJSONArray("recr_dynas");
		for(int x = 0; x < array.size(); x++) {
			
			JSONObject each = array.getJSONObject(x);
			String dynamic_id = each.getString("dynamic_id");
			String dynamic_title = each.getString("dynamic_title");
			String pub_times = each.getString("pub_times");
			//String filename = dynamic_id + "[" + dynamic_title+"["+pub_times;
			String filename = dynamic_title;
			if(!pub_times.equals("2025-04-22")) {
				continue;
			}
			
			Map<String, String> headers = new HashMap<>();
			Map<String, String> params = new HashMap<>();
	

			
	
			headers.put("Accept", "application/json, text/javascript, */*; q=0.01");
			headers.put("Accept-Encoding", "gzip, deflate, br, zstd");
			headers.put("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
			headers.put("Content-Type", "application/json;charset=utf-8");
			headers.put("Cache-Control", "no-cache");
			headers.put("Connection", "keep-alive");
			headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
			headers.put("X-Requested-With", "XMLHttpRequest");
			headers.put("Referer", "https://zhaopin.sgcc.com.cn/sgcchr/static/unitPart.html?bullet_id=480e0bde58634d8fa96cd7a6613ad02f&particulars=flase");
			headers.put("Cookie", "PW9ydXnjjO8XS=60abeMZbhNbBWRNi2hyfp32UB3dRoh5PEK12eiwEWmjK4bVv2bCaL9YrMVuFyaqrwLzlwapiBS5qwfCGXSi1WSRG; PW9ydXnjjO8XT=0CELCVSZERYJTgsm9a7HmsutsXBw3l6Dg9pd__3z_54q0QW46HRuX5H5.JBENy8BxTpKlHbkXNN4qAru3ab_yBEajvGq_mOG_xQ_pPRDO3G4ViJhiYwY35aiCfSXr7C.ZjYwuWrfLtUxJvmLRmZi3nU6zAELX2_NJd5XIfWly48JwxUP.moY8qF7cYKHTsssLGUyp7zlwFckKybJ92nAX.8dDSyFVpoXK8YZCaTN5g6KaYNSOMziBGE8y9mwWktZOVfR6RCYYS_yNv9ql2vpFH87eaNLwrMlUNZl_Qat_.bUY6Cm4YwO1vGRXXNSYiTISDPY_CUAJJ7M7Fy3znfoKlGGCnMfXuPr4noTvaCJABmzd8qtGkFRGoO4QO4eO25_5dvZYWBNwh7_NKTllSc8NYA");
						//https://zhaopin.sgcc.com.cn/sgcchr/html/bulletin/16db98efa4ab4b49ba6a69aaa9df8d0f?lh6RWoWu=09I.NIGlqEm0f0QV9t5D9Fw6QvFF5hArdcrXFAWWPg.HAkUyob4EQWojEaWBfgGEnCfGK1lSGvHsNciwG4SIVJYlWfeO5961YhG._C4BM00EVvzxBOH9A3G
			String url = "https://zhaopin.sgcc.com.cn/sgcchr/html/bulletin/"+dynamic_id+"?lh6RWoWu=09I.NIGlqEm0f0QV9t5D9Fw6QvFF5hArdcrXFAWWPg.HAkUyob4EQWojEaWBfgGEnCfGK1lSGvHsNciwG4SIVJYlWfeO5961YhG._C4BM00EVvzxBOH9A3G";
			String res22 = HttpSendUtils.get(url, headers);
			System.out.println(">>>:>"+url);
			StringBuffer sbb = new StringBuffer();
			sbb.append(res22);
			writeTempFile(new File("D:\\国家电网爬虫\\2025第二批\\page1\\" + filename + ".txt"), sbb);
		}
	}
	
	public static String format(File file) throws Exception {
		
		System.out.println(file);
		
		BufferedReader bw = new BufferedReader(new FileReader(file));
		StringBuffer universityContent = new StringBuffer();
		String str = null;
		while ((str = bw.readLine()) != null) {
			universityContent.append(str);
		}
		
		String xx = universityContent.toString().substring(universityContent.toString().indexOf("\"bullet_type\":\"4\",\"content\":\"") + 29, universityContent.toString().indexOf("\",\"end_date\":\"\",\"pub_time\""));
		
		
		//JSONObject object = JSONObject.parseObject(universityContent.toString().replaceAll("'", "\'"));
		
		//JSONObject retdata = object.getJSONObject("retdata");
		//JSONObject page_data = retdata.getJSONObject("page_data");
		//JSONObject content = page_data.getJSONObject("content");
		//System.out.println(xx);
		return xx;
	}
	
	public static String format2(File file) throws Exception {
		BufferedReader bw = new BufferedReader(new FileReader(file));
		StringBuffer universityContent = new StringBuffer();
		String str = null;
		while ((str = bw.readLine()) != null) {
			universityContent.append(str);
		}
		StringBuffer sb = new StringBuffer();
		Document document = Jsoup.parse(universityContent.toString());
		
        Elements list = document.getElementsByTag("tbody");
        Elements tbody = list.get(0).getElementsByTag("tr");
        for(int i=0;i<tbody.size();i++) {
        	Elements elements = tbody.get(i).getElementsByTag("td");
        	String xm = elements.get(0).text();
        	String xb = elements.get(1).text();
        	String yxmc = elements.get(2).text();
        	String tel = elements.get(3).text();
        	xm = xm.replaceAll("\\n", "");
        	xb = xb.replaceAll("\\n", "");
        	yxmc = yxmc.replaceAll("\\n", "");
        	tel = tel.replaceAll("\\n", "");
        	//System.out.println(xm+"-"+yxmc);
        	sb.append("INSERT INTO T_FORMAT_2_X(xm,xb,yxmc,tel,src) values('"+xm+"','"+xb+"','"+yxmc+"','"+tel+"','"+file.getName()+"');\r\n");
        }
		
		return sb.toString();
	}
	
	public static String format3(File file) throws Exception {
		BufferedReader bw = new BufferedReader(new FileReader(file));
		StringBuffer universityContent = new StringBuffer();
		String str = null;
		while ((str = bw.readLine()) != null) {
			universityContent.append(str);
		}
		StringBuffer sb = new StringBuffer();
		Document document = Jsoup.parse(universityContent.toString());
		
        Elements list = document.getElementsByTag("tbody");
        if(list.size() > 1) {
        	for(int k=1;k<list.size();k++) {
		        Elements tbody = list.get(k).getElementsByTag("tr");
		        for(int i=0;i<tbody.size();i++) {
		        	Elements elements = tbody.get(i).getElementsByTag("td");
		        	String xm = elements.get(0).text();
		        	String xb = elements.get(1).text();
		        	String yxmc = elements.get(2).text();
		        	String tel = elements.get(3).text();
		        	xm = xm.replaceAll("\\n", "");
		        	xb = xb.replaceAll("\\n", "");
		        	yxmc = yxmc.replaceAll("\\n", "");
		        	tel = tel.replaceAll("\\n", "");
		        	//System.out.println(xm+"-"+yxmc);
		        	sb.append("INSERT INTO T_FORMAT_2_X(xm,xb,yxmc,tel,src,jh) values('"+xm+"','"+xb+"','"+yxmc+"','"+tel+"','"+file.getName()+"','ext');\r\n");
		        }
        	}
        }
		
		return sb.toString();
	}
	
	public static void main(String args[]) throws Exception {
		runxxxx();
	}

	private static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
}
