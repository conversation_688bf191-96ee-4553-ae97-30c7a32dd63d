package com.career.utils.spider;

import java.io.*;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.career.utils.HttpSendUtils;
import com.career.utils.Tools;
import com.career.utils.liuxue.HttpSendUtils2;

public class Spider_HEZUOBANXUE {
	
	 static StringBuffer exception = new StringBuffer();
	
	// 教育部中外合作办学
		public static StringBuffer doit(String URL)throws Exception {
			StringBuffer SQL = new StringBuffer();
			Map<String, String> headers = new HashMap<>();
			String resultPageList = HttpSendUtils2.get(URL, headers);
			//System.out.println(URL);
			if(resultPageList.indexOf("数据库中不存在该信息！")!=-1) {
				exception.append("NOTFOUND:"+URL+"\r\n");
				return SQL;
			}
			Document documentList = Jsoup.parse(resultPageList);
			Element maincontent = documentList.getElementsByClass("maincontent").get(0);
			//System.out.println(maincontent.size());
			try {
				Elements tr = maincontent.getElementsByTag("tr");
				Element trRow1 = tr.get(0);
				Element trRow2 = tr.get(1);
				Element trRow3 = tr.get(2);
				Element trRow4 = tr.get(3);
				Element trRow5 = tr.get(4);
				Element trRow6 = tr.get(5);
				Element trRow7 = tr.get(6);
				Element trRow8 = tr.get(7);
				Element trRow9 = tr.get(8);
				Element trRow10 = tr.get(9);
				Element trRow11 = tr.get(10);
				Element trRow12 = tr.get(11);
				Element trRow13 = tr.get(12);
				Element trRow14 = tr.get(13);
				String sbj_name = trRow1.getElementsByTag("td").get(1).text();
				String sbj_addr = trRow2.getElementsByTag("td").get(1).text();
				if(trRow3.getElementsByTag("td").get(0).text().indexOf("法定代表人") != -1) {
					trRow3 = tr.get(3);
					trRow4 = tr.get(4);
					trRow5 = tr.get(5);
					trRow6 = tr.get(6);
					trRow7 = tr.get(7);
					trRow8 = tr.get(8);
					trRow9 = tr.get(9);
					trRow10 = tr.get(10);
					trRow11 = tr.get(11);
					trRow12 = tr.get(12);
					trRow13 = tr.get(13);
					trRow14 = tr.get(14);
				}
				String yxmc_zf = trRow3.getElementsByTag("td").get(1).text();//中方
				String yxmc_wf = trRow4.getElementsByTag("td").get(0).text();//外方
				String bxcc = trRow5.getElementsByTag("td").get(1).text(); //办学层次和类别
				String zsrs = trRow6.getElementsByTag("td").get(1).text(); //每期招生人数
				String qsnf = trRow6.getElementsByTag("td").get(3).text(); //招生起止年份
				String zsfs = trRow7.getElementsByTag("td").get(1).text(); //招生方式
				
				String zymc = trRow8.getElementsByTag("td").get(1).text(); //开设专业或课程
				Elements containTable = trRow8.getElementsByTag("td").get(1).getElementsByTag("tr");
				int offsetCnt = containTable.size();
				if(containTable != null && containTable.size() > 0) { //多个专业
					for(Element ex : containTable) {
						//Elements td = ex.getElementsByTag("td");
						//String zymc = td.get(0).text();
						//String zydm = td.get(2).text();
						//System.out.println("zydm:" + zydm);
					}
				}
				
				
				if(offsetCnt == 1) {
					trRow9 = tr.get(9);
					trRow10 = tr.get(10);
					trRow11 = tr.get(11);
					trRow12 = tr.get(12);
					trRow13 = tr.get(13);
					trRow14 = tr.get(14);
				}else if(offsetCnt == 2) {
					trRow9 = tr.get(10);
					trRow10 = tr.get(11);
					trRow11 = tr.get(12);
					trRow12 = tr.get(13);
					trRow13 = tr.get(14);
					trRow14 = tr.get(15);
				}else if(offsetCnt == 3) {
					trRow9 = tr.get(11);
					trRow10 = tr.get(12);
					trRow11 = tr.get(13);
					trRow12 = tr.get(14);
					trRow13 = tr.get(15);
					trRow14 = tr.get(16);
				}
				
				
				String bfzs_zf = null, bfzs_wf = null, zsbh = null, yxq = null;
				String row9Line = trRow9.getElementsByTag("td").get(0).text();
				//System.out.println("row9Line->"+trRow9.text());
				if(row9Line.indexOf("原开设") != -1) {
					bfzs_zf = trRow10.getElementsByTag("td").get(1).text(); //颁发证书
					bfzs_wf = trRow11.getElementsByTag("td").get(0).text(); //颁发证书
					//System.out.println("Y->"+bfzs_zf);

					zsbh = trRow13.getElementsByTag("td").get(1).text(); //批准书编号
					yxq = trRow14.getElementsByTag("td").get(1).text(); //有效期
				}else {
					bfzs_zf = trRow9.getElementsByTag("td").get(1).text(); //颁发证书
					bfzs_wf = trRow10.getElementsByTag("td").get(0).text(); //颁发证书
					//System.out.println("X->"+bfzs_zf);

					zsbh = trRow12.getElementsByTag("td").get(1).text(); //批准书编号
					yxq = trRow13.getElementsByTag("td").get(1).text(); //有效期
				}
				
				
				//System.out.println(URL+"-> "+sbj_name+"->"+sbj_addr+","+yxmc_zf+"="+yxmc_wf+","+bxcc+","+zsrs+","+qsnf+","+zsfs+","+zymc+","+bfzs_zf+","+bfzs_wf);
				
				SQL.append("insert into zyzd_base_hezuo_in(url,sbj_name, sbj_addr, yxmc_zf, yxmc_wf, bxcc, bxgm, zsnf, zsfs, zymc, bfzs_zf, bfzs_wf, zsbh, yxq) values('"+URL+"','"+sbj_name+"', '"+sbj_addr+"', '"+yxmc_zf+"', '"+yxmc_wf+"', '"+bxcc+"', '"+zsrs+"', '"+qsnf+"', '"+zsfs+"', '"+zymc+"', '"+bfzs_zf+"', '"+bfzs_wf+"','"+zsbh+"','"+yxq+"');\r\n");
				
				
			}catch(Exception ex) {
				ex.printStackTrace();
				exception.append("ERR:"+URL+"\r\n");
			}

			return SQL;
		}

		
		public static void doit_withErrorPage()throws Exception {
			File file = new File("E:\\kaogong\\exp1225_2.txt");
			BufferedReader br = new BufferedReader(new FileReader(file));
			String line = null;
			while((line = br.readLine()) != null) {
				if(line.startsWith("ERR:")) {
					String rs = doit(line.substring(4)).toString();
					System.out.println(rs);
				}
			}
			
		}
		

		public static void main(String[] args) {
			/**
			StringBuffer SQL = new StringBuffer();
			try {
				for(int i=1000;i<4000;i++) {
					String rs = doit("https://www.crs.jsj.edu.cn/aproval/detail/" + i).toString();
					if(!Tools.isEmpty(rs)) {
						SQL.append(rs + "\r\n");
					}
				}
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			writeTempFile(new File("E:\\kaogong\\SQL1225_2.txt"), SQL);
			writeTempFile(new File("E:\\kaogong\\exp1225_2.txt"), exception);
			*/
			
			try {
				doit_withErrorPage();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		private static void writeTempFile(File file, StringBuffer sb) {
			try {
				BufferedWriter bw = new BufferedWriter(new FileWriter(file));
				bw.write(sb.toString());
				bw.flush();
				bw.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}

}
