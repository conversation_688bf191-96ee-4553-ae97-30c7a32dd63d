package com.career.utils.wecom;

import com.alibaba.fastjson2.JSONObject;
import com.career.utils.wecom.exception.WeComApiException;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * 企业微信Token管理器，负责获取、缓存和自动刷新access_token
 */
public class WeComTokenManager {
    private static final Logger LOGGER = Logger.getLogger(WeComTokenManager.class.getName());
    private static final String GET_TOKEN_URL = "https://qyapi.weixin.qq.com/cgi-bin/gettoken";
    
    // token缓存，key为corpId:corpSecret，value为TokenInfo对象
    private static final ConcurrentHashMap<String, TokenInfo> TOKEN_CACHE = new ConcurrentHashMap<>();
    
    // 刷新令牌的调度器
    private static final ScheduledExecutorService TOKEN_REFRESH_SCHEDULER = Executors.newScheduledThreadPool(1, r -> {
        Thread thread = new Thread(r, "WeComTokenRefresher");
        thread.setDaemon(true); // 设置为守护线程，不阻止JVM退出
        return thread;
    });
    
    // 初始化令牌定时刷新任务
    static {
        // 每分钟检查一次需要刷新的令牌
        TOKEN_REFRESH_SCHEDULER.scheduleAtFixedRate(
            WeComTokenManager::refreshExpiredTokens, 
            1, 1, TimeUnit.MINUTES
        );
        
        // 添加JVM关闭钩子，确保资源释放
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                LOGGER.info("关闭令牌刷新调度器...");
                TOKEN_REFRESH_SCHEDULER.shutdown();
                if (!TOKEN_REFRESH_SCHEDULER.awaitTermination(5, TimeUnit.SECONDS)) {
                    TOKEN_REFRESH_SCHEDULER.shutdownNow();
                }
            } catch (InterruptedException e) {
                TOKEN_REFRESH_SCHEDULER.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }));
    }
    
    /**
     * 获取access_token，优先从缓存中获取，如果不存在或已过期则重新请求
     * @param corpId 企业ID
     * @param corpSecret 应用的Secret
     * @return access_token
     */
    public static String getAccessToken(String corpId, String corpSecret) {
        if (corpId == null || corpId.isEmpty() || corpSecret == null || corpSecret.isEmpty()) {
            throw new IllegalArgumentException("企业ID或Secret不能为空");
        }
        
        String cacheKey = buildCacheKey(corpId, corpSecret);
        TokenInfo tokenInfo = TOKEN_CACHE.get(cacheKey);
        
        // 如果token不存在或即将过期（10分钟内），则重新获取
        long currentTime = System.currentTimeMillis();
        if (tokenInfo == null || currentTime >= tokenInfo.expireTime - 600000) { // 提前10分钟刷新
            synchronized (WeComTokenManager.class) {
                // 双重检查锁定
                tokenInfo = TOKEN_CACHE.get(cacheKey);
                if (tokenInfo == null || currentTime >= tokenInfo.expireTime - 600000) {
                    tokenInfo = requestNewToken(corpId, corpSecret);
                    TOKEN_CACHE.put(cacheKey, tokenInfo);
                    LOGGER.info("已更新access_token，企业ID: " + corpId + ", 将在 " + 
                               new java.util.Date(tokenInfo.expireTime) + " 过期");
                }
            }
        }
        
        return tokenInfo.accessToken;
    }
    
    /**
     * 检查指定企业ID的令牌是否即将过期
     * @param corpId 企业ID
     * @return 如果令牌不存在或将在10分钟内过期则返回true，否则返回false
     */
    public static boolean isTokenNearExpiry(String corpId) {
        if (corpId == null || corpId.isEmpty()) {
            return true; // 如果企业ID为空，视为需要刷新
        }
        
        // 查找匹配企业ID的所有缓存项
        for (Map.Entry<String, TokenInfo> entry : TOKEN_CACHE.entrySet()) {
            String key = entry.getKey();
            if (key.startsWith(corpId + ":")) {
                TokenInfo tokenInfo = entry.getValue();
                // 如果将在10分钟内过期，则返回true
                long currentTime = System.currentTimeMillis();
                if (currentTime >= tokenInfo.expireTime - 600000) {
                    LOGGER.info("令牌即将过期，企业ID: " + corpId + ", 当前时间: " + 
                             new java.util.Date(currentTime) + ", 过期时间: " + 
                             new java.util.Date(tokenInfo.expireTime));
                    return true;
                }
                return false; // 找到了有效的令牌
            }
        }
        
        return true; // 没有找到匹配的令牌，需要刷新
    }
    
    /**
     * 标记指定accessToken为已过期
     * 用于主动处理42001错误
     * @param accessToken 需要标记为过期的access_token
     * @return 是否成功标记
     */
    public static boolean markTokenAsExpired(String accessToken) {
        if (accessToken == null || accessToken.isEmpty()) {
            return false;
        }
        
        // 查找匹配的Token
        for (Map.Entry<String, TokenInfo> entry : TOKEN_CACHE.entrySet()) {
            TokenInfo tokenInfo = entry.getValue();
            if (tokenInfo.accessToken.equals(accessToken)) {
                // 将过期时间设为当前时间，强制下次获取时刷新
                tokenInfo.expireTime = System.currentTimeMillis();
                LOGGER.info("已标记access_token为过期状态: " + accessToken.substring(0, 6) + "...");
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 强制刷新指定企业ID和Secret的令牌
     * @param corpId 企业ID
     * @param corpSecret 应用的Secret
     * @return 新的access_token
     */
    public static String refreshAccessToken(String corpId, String corpSecret) {
        if (corpId == null || corpId.isEmpty() || corpSecret == null || corpSecret.isEmpty()) {
            throw new IllegalArgumentException("企业ID或Secret不能为空");
        }
        
        String cacheKey = buildCacheKey(corpId, corpSecret);
        
        // 先检查是否有缓存的旧token信息
        TokenInfo oldToken = TOKEN_CACHE.get(cacheKey);
        if (oldToken != null) {
            LOGGER.info("强制刷新token，旧token: " + oldToken.accessToken.substring(0, 6) + "..., " +
                       "过期时间: " + new java.util.Date(oldToken.expireTime));
        }
        
        // 请求新令牌
        TokenInfo tokenInfo = requestNewToken(corpId, corpSecret);
        
        // 更新缓存
        TOKEN_CACHE.put(cacheKey, tokenInfo);
        LOGGER.info("已强制刷新access_token，企业ID: " + corpId + 
                   ", 新token: " + tokenInfo.accessToken.substring(0, 6) + "..., " +
                   "将在 " + new java.util.Date(tokenInfo.expireTime) + " 过期");
        
        return tokenInfo.accessToken;
    }
    
    /**
     * 定期检查并刷新即将过期的令牌
     */
    private static void refreshExpiredTokens() {
        try {
            long currentTime = System.currentTimeMillis();
            // 提前15分钟刷新即将过期的令牌
            long refreshThreshold = currentTime + 15 * 60 * 1000;
            
            LOGGER.fine("正在检查需要刷新的access_token...");
            
            for (Map.Entry<String, TokenInfo> entry : TOKEN_CACHE.entrySet()) {
                String cacheKey = entry.getKey();
                TokenInfo tokenInfo = entry.getValue();
                
                // 如果令牌即将过期，则刷新
                if (tokenInfo.expireTime <= refreshThreshold) {
                    try {
                        LOGGER.info("正在刷新即将过期的access_token，密钥: " + cacheKey);
                        
                        // 解析cacheKey获取corpId和corpSecret
                        String[] parts = cacheKey.split(":");
                        if (parts.length != 2) {
                            LOGGER.warning("无效的缓存密钥格式: " + cacheKey);
                            continue;
                        }
                        
                        String corpId = parts[0];
                        String corpSecret = parts[1];
                        
                        // 请求新令牌并更新缓存
                        TokenInfo newTokenInfo = requestNewToken(corpId, corpSecret);
                        TOKEN_CACHE.put(cacheKey, newTokenInfo);
                        
                        LOGGER.info("已刷新access_token，企业ID: " + corpId + ", 旧过期时间: " + 
                                   new java.util.Date(tokenInfo.expireTime) + ", 新过期时间: " + 
                                   new java.util.Date(newTokenInfo.expireTime));
                    } catch (Exception e) {
                        LOGGER.log(Level.WARNING, "刷新access_token时出错，密钥: " + cacheKey, e);
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "刷新令牌过程中发生未预期异常", e);
        }
    }
    
    /**
     * 清除指定企业和应用的令牌缓存
     * 用于在遇到令牌相关错误时强制刷新
     */
    public static void invalidateToken(String corpId, String corpSecret) {
        String cacheKey = buildCacheKey(corpId, corpSecret);
        TokenInfo removed = TOKEN_CACHE.remove(cacheKey);
        if (removed != null) {
            LOGGER.info("已清除企业ID " + corpId + " 的access_token缓存");
        }
    }
    
    /**
     * 构建缓存Key
     */
    private static String buildCacheKey(String corpId, String corpSecret) {
        return corpId + ":" + corpSecret;
    }
    
    /**
     * 请求新的access_token
     */
    private static TokenInfo requestNewToken(String corpId, String corpSecret) {
        String url = String.format("%s?corpid=%s&corpsecret=%s", GET_TOKEN_URL, corpId, corpSecret);
        
        try {
            LOGGER.fine("请求新的access_token: " + url);
            JSONObject response = WeComHttpClient.doGetWithRetry(url);
            
            int errorCode = response.getIntValue("errcode");
            if (errorCode != 0) {
                String errorMsg = response.getString("errmsg");
                throw new WeComApiException(errorCode, "获取access_token失败，错误码: " + errorCode + ", 错误信息: " + errorMsg);
            }
            
            String accessToken = response.getString("access_token");
            if (accessToken == null || accessToken.isEmpty()) {
                throw new WeComApiException("获取access_token成功，但返回的token为空");
            }
            
            // 提前5分钟过期以避免临界问题
            long expireTime = System.currentTimeMillis() + (response.getLongValue("expires_in") - 300) * 1000L;
            
            return new TokenInfo(accessToken, expireTime, corpId, corpSecret);
        } catch (WeComApiException e) {
            throw e;
        } catch (Exception e) {
            throw new WeComApiException("获取access_token时发生异常", e);
        }
    }
    
    /**
     * Token信息内部类
     */
    private static class TokenInfo {
        // access_token值
        private final String accessToken;
        // 过期时间戳(毫秒)
        private long expireTime;
        // 企业ID
        private final String corpId;
        // 应用Secret
        private final String corpSecret;
        
        public TokenInfo(String accessToken, long expireTime, String corpId, String corpSecret) {
            this.accessToken = accessToken;
            this.expireTime = expireTime;
            this.corpId = corpId;
            this.corpSecret = corpSecret;
        }
    }
} 