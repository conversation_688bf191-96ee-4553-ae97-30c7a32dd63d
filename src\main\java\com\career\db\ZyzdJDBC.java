package com.career.db;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;

import com.career.db.career.MajorPlan;
import com.career.utils.ResultVO;
import com.career.utils.SAASConfig;
import com.career.utils.SQLLogUtils;
import com.career.utils.Tools;
import com.career.utils.ZyzdCache;
import com.career.utils.ZyzdProvince;
import com.zsdwf.db.YGPaymentBean;

import cn.hutool.core.lang.UUID;


public class ZyzdJDBC {
	
	static String URL = JDBCConstants.URL;
	static String USER = JDBCConstants.USER;
	static String PASSWD = JDBCConstants.PASSWD;
	public static int PAGE_ROW_CNT = 20;
	public static int PAGE_ROW_CNT_SPEC = 15;
	
	static {
		try {
			Class.forName("com.mysql.cj.jdbc.Driver");
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
	
	public void insertZyzdFormBatchIdAll(String batch_id, String sf, String where, String temp_acc, String temp_pas) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			
			String SQL = "insert into zyzd_form_batch_id_all(batch_id, sf, from_where, student_acc_id, student_acc_pas, create_tm) values(?,?,?,?,?,now())";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			ps.setString(2, sf);
			ps.setString(3, where);
			ps.setString(4, temp_acc);
			ps.setString(5, temp_pas);
			ps.executeUpdate();
			SQLLogUtils.printSQL("insertZyzdFormBatchIdAll", ps);
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void insertZyzdFormBatchIdAll(String batch_id, String sf, String where) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			
			String SQL = "insert into zyzd_form_batch_id_all(batch_id, sf, from_where, create_tm) values(?,?,?,now())";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			ps.setString(2, sf);
			ps.setString(3, where);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	
	public void resetZyzdFormBatchIdAllStudentAcct(String batch_id, String acct_id, String acct_pass) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "update zyzd_form_batch_id_all set student_acc_id = ?, student_acc_pas = ? where batch_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, acct_id);
			ps.setString(2, acct_pass);
			ps.setString(3, batch_id);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	private SAASConfig setSAASConfig(ResultSet rs) throws SQLException {
		SAASConfig saasConfig = new SAASConfig();
        saasConfig.setSaas_id(rs.getString("saas_id"));
        saasConfig.setServer_name(rs.getString("server_name"));
        saasConfig.setCompany_name(rs.getString("company_name"));
        saasConfig.setPage_header_title(rs.getString("page_header_title"));
        saasConfig.setPage_title(rs.getString("page_title"));
        saasConfig.setPage_slogan(rs.getString("page_slogan"));
        saasConfig.setPage_sys_short_name(rs.getString("page_sys_short_name"));
        saasConfig.setPage_icon_url(rs.getString("page_icon_url"));
        saasConfig.setPage_logo_name(rs.getString("page_logo_name"));
        saasConfig.setColor_left_menu(rs.getString("color_left_menu"));
        saasConfig.setColor_top_header(rs.getString("color_top_header"));
        saasConfig.setAdmin_id(rs.getString("admin_id"));
        saasConfig.setAdmin_passwd(rs.getString("admin_passwd"));
        saasConfig.setPay_cert_uri(rs.getString("pay_cert_uri"));
        saasConfig.setCard_open_sf(rs.getString("card_open_sf"));
        saasConfig.setSaas_uion_id(rs.getString("saas_uion_id"));
        saasConfig.setCard_restrict(rs.getInt("card_restrict"));
        return saasConfig;
	}
	
	public List<SAASConfig> getAllActiveSAASConfig() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SAASConfig> list = new ArrayList<>();
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_saas_config ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			SAASConfig saasConfig = null;
			while (rs.next()) {
                list.add(setSAASConfig(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<SAASConfig> getAllActiveSAASConfig(String saas_uion_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SAASConfig> list = new ArrayList<>();
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_saas_config x where x.saas_uion_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, saas_uion_id);
			rs = ps.executeQuery();
			SAASConfig saasConfig = null;
			while (rs.next()) {
                list.add(setSAASConfig(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public int getSaasArrangeCardCntForToday(HashSet<String> saasIdSets) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		SAASConfig saasConfig = null;
		try {
			String saasIdSetsSQL = saasIdSets.size() == 0 ? "" : "and saas_id in (" + Tools.getSQLQueryin(saasIdSets) + ")";
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT count(*) as cnt FROM base_card WHERE DATE_FORMAT(C_INTRODUCE_TM, '%Y-%m-%d') = DATE_FORMAT(NOW(), '%Y-%m-%d') " + saasIdSetsSQL;
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				return rs.getInt("cnt");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public SAASConfig getSAASConfigById(String saas_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		SAASConfig saasConfig = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_saas_config WHERE saas_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, saas_id);
			rs = ps.executeQuery();
			while (rs.next()) {
				saasConfig = setSAASConfig(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return saasConfig;
	}
	
	public SAASConfig getSAASConfigByServerName(String server_name) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		SAASConfig saasConfig = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_saas_config WHERE server_name = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, server_name);
			rs = ps.executeQuery();
			while (rs.next()) {
				saasConfig = setSAASConfig(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return saasConfig;
	}
	
	public ZyzdFormBatchIdAll getZyzdFormBatchIdAllByBatchId(String batch_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		ZyzdFormBatchIdAll bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_form_batch_id_all WHERE batch_id = ? ";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			SQLLogUtils.printSQL(ps);
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = new ZyzdFormBatchIdAll();
				bean.setBatch_id(rs.getString("batch_id"));
				bean.setSf(rs.getString("sf"));
				bean.setFrom_where(rs.getString("from_where"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				bean.setStudent_acc_id(rs.getString("student_acc_id"));
				bean.setStudent_acc_pas(rs.getString("student_acc_pas"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}
	
	public List<ZyzdFormBatchIdAll> getZyzdFormBatchIdAllByAcct(String student_acc_id, String student_acc_pass) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdFormBatchIdAll> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_form_batch_id_all WHERE student_acc_id = ? and student_acc_pas = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, student_acc_id);
			ps.setString(2, student_acc_pass);
			rs = ps.executeQuery();
			ZyzdFormBatchIdAll bean = null;
			while (rs.next()) {
				bean = new ZyzdFormBatchIdAll();
				bean.setBatch_id(rs.getString("batch_id"));
				bean.setSf(rs.getString("sf"));
				bean.setFrom_where(rs.getString("from_where"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				bean.setStudent_acc_id(rs.getString("student_acc_id"));
				bean.setStudent_acc_pas(rs.getString("student_acc_pas"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public int getZyzdCheckerViewTimesByToday(String order_id, String from_where) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdFormBatchIdAll> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT count(*) as cnt FROM zyzd_checker_view_times WHERE order_id = ? and from_where = ? AND DATE(create_tm) = CURDATE();";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, order_id);
			ps.setString(2, from_where);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("cnt");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public void insertZyzdCheckerViewTimes(String order_id, String from_where) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into zyzd_checker_view_times(order_id, from_where, create_tm) values(?,?,now())";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, order_id);
			ps.setString(2, from_where);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public List<YxzyStatisticsYxzy> getYyzdBaseYxzyStatisticsYxzyByYxmcOrg(String yxmc_org) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<YxzyStatisticsYxzy> list = new ArrayList<>();
        try {
            conn = DatabaseUtils.getConnection();
            String SQL = "SELECT * FROM zyzd_base_yxzy_statistics_yxzy WHERE yxmc_org = ?";
            System.out.println(SQL); // 打印SQL语句
            ps = conn.prepareStatement(SQL);
            ps.setString(1, yxmc_org);
            rs = ps.executeQuery();
            YxzyStatisticsYxzy bean = null;
            while (rs.next()) {
                bean = new YxzyStatisticsYxzy();
                bean.setYxmc_org(rs.getString("yxmc_org"));
                bean.setZymc_org(rs.getString("zymc_org"));
                bean.setTable_id(rs.getString("table_id"));
                bean.setGroup_id(rs.getString("group_id"));
                bean.setTable_name(rs.getString("table_name"));
                bean.setGroup_name(rs.getString("group_name"));
                list.add(bean);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        return list;
    }
	
	public List<YxzyStatisticsYxzy> getYyzdBaseYxzyStatisticsYxzyByYxmcOrgWithGroup(String yxmc_org) {
        Connection conn = null;
        PreparedStatement ps = null; 
        ResultSet rs = null;
        List<YxzyStatisticsYxzy> list = new ArrayList<>();
        try {
            conn = DatabaseUtils.getConnection();
            String SQL = "SELECT table_id,table_name, yxmc_org, COUNT(*) as cnt FROM zyzd_base_yxzy_statistics_yxzy WHERE yxmc_org like ? GROUP BY table_id,table_name, yxmc_org";
            ps = conn.prepareStatement(SQL);
            ps.setString(1, "%" + yxmc_org + "%");
            rs = ps.executeQuery();
            SQLLogUtils.printSQL(ps);  
            YxzyStatisticsYxzy bean = null;
            while (rs.next()) {
                bean = new YxzyStatisticsYxzy();
                bean.setTable_id(rs.getString("table_id"));
                bean.setTable_name(rs.getString("table_name"));
                bean.setYxmc_org(rs.getString("yxmc_org")); 
                bean.setExt_cnt(rs.getInt("cnt"));
                list.add(bean);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        return list;
    }
	
	
	public List<YxzyStatisticsYxzy> getYyzdBaseYxzyStatisticsYxzyByYxmcOrg(String yxmc_org, String zymc_org) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<YxzyStatisticsYxzy> list = new ArrayList<>();
        try {
            conn = DatabaseUtils.getConnection();
            String SQL = "SELECT * FROM zyzd_base_yxzy_statistics_yxzy WHERE yxmc_org = ? and zymc_org = ?";
            System.out.println(SQL); // 打印SQL语句
            ps = conn.prepareStatement(SQL);
            ps.setString(1, yxmc_org);
            ps.setString(2, zymc_org);
            rs = ps.executeQuery();
            YxzyStatisticsYxzy bean = null;
            while (rs.next()) {
                bean = new YxzyStatisticsYxzy();
                bean.setYxmc_org(rs.getString("yxmc_org"));
                bean.setZymc_org(rs.getString("zymc_org"));
                bean.setTable_id(rs.getString("table_id"));
                bean.setGroup_id(rs.getString("group_id"));
                bean.setTable_name(rs.getString("table_name"));
                list.add(bean);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        return list;
    }
	
	public List<YxzyStatistics> getYxzyStatisticsByGroupId(String group_id) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<YxzyStatistics> list = new ArrayList<>();
        try {
            conn = DatabaseUtils.getConnection();
            String SQL = "SELECT * FROM zyzd_base_yxzy_statistics WHERE group_id = ? order by item_sort desc, column_sort desc";
            System.out.println(SQL); // 打印SQL语句
            ps = conn.prepareStatement(SQL);
            ps.setString(1, group_id);
            rs = ps.executeQuery();
            YxzyStatistics bean = null;
            while (rs.next()) {
                bean = new YxzyStatistics();
                bean.setId(rs.getInt("id"));
                bean.setGroup_id(rs.getString("group_id"));
                bean.setTable_id(rs.getString("table_id"));
                bean.setItem_id(rs.getString("item_id"));
                bean.setItem_sort(rs.getInt("item_sort"));
                bean.setColumn_title(rs.getString("column_title"));
                bean.setColumn_title_standare(rs.getString("column_title_standare"));
                bean.setColumn_value(rs.getString("column_value"));
                bean.setColumn_sort(rs.getInt("column_sort"));
                list.add(bean);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        return list;
    }
	
	public List<YxzyStatistics> getYxzyStatisticsByTableId(String table_id) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<YxzyStatistics> list = new ArrayList<>();
        try {
            conn = DatabaseUtils.getConnection();
            String SQL = "SELECT * FROM zyzd_base_yxzy_statistics WHERE table_id = ? order by item_sort desc, column_sort desc";
            ps = conn.prepareStatement(SQL);
            ps.setString(1, table_id);
            rs = ps.executeQuery();
            SQLLogUtils.printSQL(ps);
            YxzyStatistics bean = null;
            while (rs.next()) {
                bean = new YxzyStatistics();
                bean.setId(rs.getInt("id"));
                bean.setGroup_id(rs.getString("group_id"));
                bean.setTable_id(rs.getString("table_id"));
                bean.setItem_id(rs.getString("item_id"));
                bean.setItem_sort(rs.getInt("item_sort"));
                bean.setColumn_title(rs.getString("column_title"));
                bean.setColumn_title_standare(rs.getString("column_title_standare"));
                bean.setColumn_value(rs.getString("column_value"));
                bean.setColumn_sort(rs.getInt("column_sort"));
                list.add(bean);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        return list;
    }
	
	public HashMap<String, String> getYxzyStatisticsColumnByTableId(String table_id) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        HashMap<String, String> COLUMN_MAP = new LinkedHashMap<>();
        try {
            conn = DatabaseUtils.getConnection();
            String SQL = "SELECT column_title,column_sort FROM zyzd_base_yxzy_statistics WHERE table_id = ? group by column_title,column_sort order by column_sort desc";
            ps = conn.prepareStatement(SQL);
            ps.setString(1, table_id); 
            rs = ps.executeQuery();
            SQLLogUtils.printSQL(ps);
            while (rs.next()) {
            	String column_tile = rs.getString("column_title");
                COLUMN_MAP.put(column_tile, column_tile);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        return COLUMN_MAP;
    }
	
	public ZyzdProvincePcConfig getZyzdProvincePcConfigByPCodeAndPcCode(String sf_code, String pc_code) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<ZyzdProvincePcConfig> list = new ArrayList<>();
        try {
            conn = DatabaseUtils.getConnection();
            String SQL = "SELECT * FROM zyzd_province_pc_config WHERE p_code = ? AND pc_code = ?";
            System.out.println(SQL); // 打印SQL语句
            ps = conn.prepareStatement(SQL);
            ps.setString(1, sf_code);
            ps.setString(2, pc_code);
            rs = ps.executeQuery();
            ZyzdProvincePcConfig bean = null;
            while (rs.next()) {
                bean = new ZyzdProvincePcConfig();
                bean.setP_code(rs.getString("p_code"));
                bean.setPc_code(rs.getString("pc_code"));
                bean.setForm_cnt(rs.getInt("form_cnt"));
                bean.setForm_rule(rs.getString("form_rule"));
                bean.setMajor_cnt(rs.getInt("major_cnt"));
                bean.setForm_type(rs.getInt("form_type"));
                return bean;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        return null;
    }
	
	public List<YxzyStatisticsMain> getYxzyStatisticsMainByTableName(String table_name) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<YxzyStatisticsMain> list = new ArrayList<>();
        try {
            conn = DatabaseUtils.getConnection();
            String SQL = "SELECT * FROM zyzd_base_yxzy_statistics_main WHERE table_name = ? order by group_sort desc, table_sort desc";
            System.out.println(SQL); // 打印SQL语句
            ps = conn.prepareStatement(SQL);
            ps.setString(1, table_name);
            rs = ps.executeQuery();
            YxzyStatisticsMain bean = null;
            while (rs.next()) {
                bean = new YxzyStatisticsMain();
                bean.setTable_id(rs.getString("table_id"));
                bean.setTable_name(rs.getString("table_name"));
                bean.setTable_sort(rs.getInt("table_sort"));
                bean.setGroup_id(rs.getString("group_id"));
                bean.setGroup_name(rs.getString("group_name"));
                bean.setGroup_sort(rs.getInt("group_sort"));
                list.add(bean);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        return list;
    }
	
	public List<YxzyStatisticsMain> getYxzyStatisticsMainByGroupName(String group_name) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<YxzyStatisticsMain> list = new ArrayList<>();
        try {
            conn = DatabaseUtils.getConnection();
            String SQL = "SELECT * FROM zyzd_base_yxzy_statistics_main WHERE table_name = ? order by group_sort desc, table_sort desc";
            System.out.println(SQL); // 打印SQL语句
            ps = conn.prepareStatement(SQL);
            ps.setString(1, group_name);
            rs = ps.executeQuery();
            YxzyStatisticsMain bean = null;
            while (rs.next()) {
                bean = new YxzyStatisticsMain();
                bean.setTable_id(rs.getString("table_id"));
                bean.setTable_name(rs.getString("table_name"));
                bean.setTable_sort(rs.getInt("table_sort"));
                bean.setGroup_id(rs.getString("group_id"));
                bean.setGroup_name(rs.getString("group_name"));
                bean.setGroup_sort(rs.getInt("group_sort"));
                list.add(bean);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        return list;
    }
	
	public List<YxzyStatisticsMain> getYxzyStatisticsMainAll() {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<YxzyStatisticsMain> list = new ArrayList<>();
        try {
            conn = DatabaseUtils.getConnection();
            String SQL = "SELECT * FROM zyzd_base_yxzy_statistics_main";
            System.out.println(SQL); // 打印SQL语句
            ps = conn.prepareStatement(SQL);
            rs = ps.executeQuery();
            YxzyStatisticsMain bean = null;
            while (rs.next()) {
                bean = new YxzyStatisticsMain();
                bean.setTable_id(rs.getString("table_id"));
                bean.setTable_name(rs.getString("table_name"));
                bean.setTable_sort(rs.getInt("table_sort"));
                bean.setGroup_id(rs.getString("group_id"));
                bean.setGroup_name(rs.getString("group_name"));
                bean.setGroup_sort(rs.getInt("group_sort"));
                list.add(bean);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        return list;
    }
	
	public WxShare getWxshareByOpenid(String openid) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		WxShare bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM wx_share WHERE wx_open_id = ? ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, openid);
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = new WxShare();
				bean.setWx_open_id(rs.getString("wx_open_id"));
				bean.setShare_qrscene(rs.getString("share_qrscene"));
				bean.setCreateTm(rs.getTimestamp("create_tm"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}
	
	public List<ZyzdNewsPush> getNewsPush(String c_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdNewsPush> list = new ArrayList<>();
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_news_push WHERE c_id = ? and expire_tm > now() order by create_tm ASC";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, c_id);
			rs = ps.executeQuery();
			ZyzdNewsPush bean = null;
			while (rs.next()) {
				bean = new ZyzdNewsPush();
				bean.setC_id(rs.getString("c_id"));
				bean.setId(rs.getInt("id"));
				bean.setNews_content(rs.getString("news_content"));
				bean.setNews_title(rs.getString("news_title"));
				bean.setNews_type(rs.getInt("news_type"));
				bean.setNews_url(rs.getString("news_url"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<String> getPcs(int jhYear, String sfTable) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<String> list = new ArrayList<>();
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT distinct pc FROM "+sfTable+"_jh_" + jhYear + " WHERE pc is not null and (lqpc is null or lqpc <> '普通类')";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(rs.getString("pc"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<String> getAllPcs(int jhYear, String sfTable) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<String> list = new ArrayList<>();
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT distinct pc FROM "+sfTable+"_jh_" + jhYear + " WHERE pc is not null ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(rs.getString("pc"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public ResultVO getJHForTspc(int jhYear, String sfCode, String xkCode, HashSet<String> univSf, HashSet<String> zymlSets, String pc, String yxmc_org, int scoreFrom, int scoreTo, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		ResultVO resultVO = new ResultVO();
		List<JHBean> list = new ArrayList<>();
		int dataYear = jhYear-1;
		try {
			String andYXSFSQL = univSf.size() == 0 ? "" : "and yxsf in (" + Tools.getSQLQueryin(univSf) + ")";
			String zymlInSQl = zymlSets.size() == 0 ? "" : " and zyml in ("+Tools.getSQLQueryin(zymlSets)+")";
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.xk_code like ? and pc = ? AND (lqpc is null or lqpc <> '普通类') AND x.yxmc like ? "+andYXSFSQL + zymlInSQl +" and (x.zdf between ? and ? or x.zdf is null) ORDER BY x.yxmc, x.zyz, x.zdf DESC limit ?,?";
			String SELECT_CONDITION_CNT = "SELECT count(*) as cnt FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.xk_code like ? and pc = ? AND (lqpc is null or lqpc <> '普通类') AND x.yxmc like ? "+andYXSFSQL + zymlInSQl +" and (x.zdf between ? and ? or x.zdf is null) ORDER BY x.yxmc, x.zyz, x.zdf DESC";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, "%" + xkCode + "%");
			ps.setString(2, pc);
			ps.setString(3, "%" + yxmc_org + "%");
			ps.setInt(4, scoreFrom);
			ps.setInt(5, scoreTo);
			ps.setInt(6, (pageNumber - 1) * PAGE_ROW_CNT); // 1开始
			ps.setInt(7, PAGE_ROW_CNT);

			rs = ps.executeQuery();
			JHBean bean = null;
			while (rs.next()) {
				list.add(ZyzdFormJDBC.setJHBeanAll(dataYear, rs));
			}
			
			ps.close();
			ps = null;
			rs.close();
			rs = null;
			ps = conn.prepareStatement(SELECT_CONDITION_CNT);
			Tools.println(SELECT_CONDITION_CNT);
			ps.setString(1, "%" + xkCode + "%");
			ps.setString(2, pc);
			ps.setString(3, "%" + yxmc_org + "%");
			ps.setInt(4, scoreFrom);
			ps.setInt(5, scoreTo);

			int recordCnt = 0;
			rs = ps.executeQuery();
			while (rs.next()) {
				recordCnt = rs.getInt("cnt");
			}
			
			resultVO.setCurrentPage(pageNumber);
			resultVO.setRecordCnt(recordCnt);
			resultVO.setResult(list);
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return resultVO;
	}
	
	public void deleteNewsPush(int id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "DELETE FROM zyzd_news_push WHERE id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, id);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public List<WxShare> getWxshareByAgent(String share_qrscene) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<WxShare> list = new ArrayList<>();
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM wx_share WHERE share_qrscene = ? ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, share_qrscene);
			rs = ps.executeQuery();
			WxShare bean = null;
			while (rs.next()) {
				bean = new WxShare();
				bean.setWx_open_id(rs.getString("wx_open_id"));
				bean.setShare_qrscene(rs.getString("share_qrscene"));
				bean.setCreateTm(rs.getTimestamp("create_tm"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public String getWxSubscribe(String openid) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		WxShare bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM wx_subscribe WHERE wx_open_id = ? ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, openid);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getString("wx_open_id");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public void insertWxSubscribe(String openid) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into wx_subscribe(wx_open_id, create_tm) values(?,now())";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, openid);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void deleteWxSubscribe(String openid) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "DELETE FROM wx_subscribe WHERE wx_open_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, openid);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public String getWxshareQRCodeURLViaScene(String qrscene_code) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT qr_url FROM wx_share_user WHERE qrscene = ? ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, qrscene_code);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getString("qr_url");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public String getWxshareQRCodeURLViaSid(String s_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT qr_url FROM wx_share_user WHERE s_id = ? ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, s_id);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getString("qr_url");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public WxShareUser getWxshareAccount(String sid, String passwd) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		WxShareUser bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM wx_share_user WHERE s_id = ? and s_passwd = ? ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, sid);
			ps.setString(2, passwd);
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = setWxShareAccount(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}
	
	public WxShareUser getWxshareAccountByBaseCardId(String base_card_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		WxShareUser bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM wx_share_user WHERE base_card_id = ? ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, base_card_id);
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = setWxShareAccount(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}
	
	public boolean updateWxshareAccount(WxShareUser bean) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE wx_share_user SET last_login_tm = now() WHERE s_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, bean.getS_id());
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	private WxShareUser setWxShareAccount(ResultSet rs) throws Exception{
		WxShareUser bean = new WxShareUser();
		bean.setBoss_name(rs.getString("boss_name"));
		bean.setBoss_sid(rs.getString("boss_sid"));
		bean.setQr_name(rs.getString("qr_name"));
		bean.setQr_phone(rs.getString("qr_phone"));
		bean.setQr_url(rs.getString("qr_url"));
		bean.setQrscene(rs.getString("qrscene"));
		bean.setS_id(rs.getString("s_id"));
		bean.setS_passwd(rs.getString("s_passwd"));
		bean.setBase_card_id(rs.getString("base_card_id"));
		bean.setLastLoginTm(rs.getTimestamp("last_login_tm"));
		bean.setRemark(rs.getString("remark"));
		return bean;
	}
	
	public void insertWxShare(WxShare wxShare) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into wx_share(wx_open_id, share_qrscene, create_tm) values(?,?,now())";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, wxShare.getWx_open_id());
			ps.setString(2, wxShare.getShare_qrscene());
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void insertWxShareDuplicate(WxShare wxShare) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		WxShare bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into wx_share_duplicate(wx_open_id, share_qrscene, create_tm) values(?,?,now())";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, wxShare.getWx_open_id());
			ps.setString(2, wxShare.getShare_qrscene());
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public ZDKSRank getZDKSWCByScore(int year, String sf, String kl, int score) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		ZDKSRank bean = null;
		try {
			ZyzdProvince zyzdProvince = ZyzdCache.getUserCardProvinceName(sf);
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+zyzdProvince.getTableName()+"_zdks_rank  WHERE nf = ? and sf = ? and kl_code like ? and score = ?";
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, year);
			ps.setString(2, sf);
			ps.setString(3, "%"+kl+"%");
			ps.setInt(4, score);
			SQLLogUtils.printSQL(ps);
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = new ZDKSRank();
				bean.setCnt(rs.getInt("cnt"));
				bean.setFd(rs.getString("fd"));
				bean.setKl(rs.getString("kl"));
				bean.setNf(rs.getInt("nf"));
				bean.setScore(rs.getInt("SCORE"));
				bean.setSf(rs.getString("sf"));
				bean.setWc(rs.getInt("wc"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}
	
	public ZDKSRank getHighestOrLowestScoreRank(int year, String sf, String kl, boolean highest) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		ZDKSRank bean = null;
		try {
			ZyzdProvince zyzdProvince = ZyzdCache.getUserCardProvinceName(sf);
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+zyzdProvince.getTableName()+"_zdks_rank  WHERE nf = ? and sf = ? and kl_code like ? ORDER BY score "+ (highest ? "DESC":"ASC") +" limit 1";
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, year);
			ps.setString(2, sf);
			ps.setString(3, "%"+kl+"%");
			SQLLogUtils.printSQL(ps);
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = new ZDKSRank();
				bean.setCnt(rs.getInt("cnt"));
				bean.setFd(rs.getString("fd"));
				bean.setKl(rs.getString("kl"));
				bean.setNf(rs.getInt("nf"));
				bean.setScore(rs.getInt("SCORE"));
				bean.setSf(rs.getString("sf"));
				bean.setWc(rs.getInt("wc"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}
	
	
	public List<CareerJYJobREQ> getJiuyeREQByZyCatg(String zymc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CareerJYJobREQ> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM career_jy_job_req WHERE (req_major_name = ? or req_major_catg = ?) and salary <> '面议' and salary_cvt > 0 and display_ind = 1 ORDER BY RAND() LIMIT " +PAGE_ROW_CNT_SPEC;
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, zymc);
			ps.setString(2, zymc);
			rs = ps.executeQuery();
			CareerJYJobREQ bean = null;
			while (rs.next()) {
				bean = new CareerJYJobREQ();
				bean.setCompanyName(rs.getString("company"));
				bean.setEducation(rs.getString("req_education"));
				bean.setFirstPublishTime(rs.getString("first_publish_time"));
				bean.setIndustryName(rs.getString("industry_name"));
				bean.setJobSummary(rs.getString("job_summary"));
				bean.setJobtitle(rs.getString("job_title"));
				bean.setMajorCatg(rs.getString("req_major_catg"));
				bean.setMajorName(rs.getString("req_major_name"));
				bean.setProperty(rs.getString("property"));
				bean.setSalary(rs.getString("salary"));
				bean.setSalaryCvt(rs.getInt("salary_cvt"));
				bean.setSalaryCount(rs.getString("salary_count"));
				bean.setWorkCity(rs.getString("work_city"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JiuyeBean> pickJiuyeByZyName(String zymcOrg) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<JiuyeBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT group_name, lsy, zymc, COUNT(*) as ct from career_jy_all_2024 x WHERE x.zymc LIKE ? GROUP BY group_name, lsy, zymc ORDER BY COUNT(*) DESC LIMIT " + PAGE_ROW_CNT_SPEC;
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + zymcOrg + "%");
			rs = ps.executeQuery();
			JiuyeBean bean = null;
			while (rs.next()) {
				bean = new JiuyeBean();
				bean.setZymc(rs.getString("zymc"));
				bean.setCnt(rs.getInt("ct"));
				bean.setSshy(rs.getString("group_name"));
				bean.setSsgs(rs.getString("lsy"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdMajorRanking> getRankingMajorRuanKe(String yxmc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdMajorRanking> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT *  FROM zyzd_base_rank_rk_major WHERE yxmc = ? ORDER BY sort, ranking";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yxmc);
			rs = ps.executeQuery();
			ZyzdMajorRanking bean = null;
			while (rs.next()) {
				bean = new ZyzdMajorRanking();
				bean.setRanking(rs.getInt("ranking"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZymc(rs.getString("zymc"));
				bean.setLevel(rs.getString("ranking_level"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdMajorRanking> getRankingByMajorAndRangeRuanKe(String zymc, int rank) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdMajorRanking> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_base_rank_rk_major WHERE zymc = ? and ranking between ? and ? ORDER BY sort, ranking";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, zymc);
			ps.setInt(2, rank - 10);
			ps.setInt(3, rank + 5);
			rs = ps.executeQuery();
			ZyzdMajorRanking bean = null;
			while (rs.next()) {
				bean = new ZyzdMajorRanking();
				bean.setRanking(rs.getInt("ranking"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZymc(rs.getString("zymc"));
				bean.setLevel(rs.getString("ranking_level"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdMajorRanking> getRankingUnivRuanKe(String provinceTable, int yxTableYear, String xk_code, String zymc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdMajorRanking> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.zymc, x.yxmc, x.ranking, min(y.zdf) as zdf, max(y.zdfwc) as zdfwc FROM zyzd_base_rank_rk_major x left join " + provinceTable + "_" + yxTableYear + "x y on x.yxmc = y.yxmc_org and y.xk_code like ? WHERE x.zymc = ? GROUP BY x.zymc, x.yxmc, x.ranking ORDER BY x.ranking";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + xk_code + "%");
			ps.setString(2, zymc);
			rs = ps.executeQuery();
			ZyzdMajorRanking bean = null;
			while (rs.next()) {
				bean = new ZyzdMajorRanking();
				bean.setRanking(rs.getInt("ranking"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZymc(rs.getString("zymc"));
				bean.setExt_zdf(rs.getString("zdf"));
				bean.setExt_zdfwc(rs.getString("zdfwc"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdMajorRanking> getRankingUnivRuanKeWithJH(String sfTable, int jhYear, String xk_code, String pc, String pc_code, String zymc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdMajorRanking> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.zymc, x.yxmc, x.ranking, min(y.zdf_"+(jhYear-1)+") as zdf_a, max(y.zdfwc_"+(jhYear-1)+") as zdfwc_a, min(y.zdf_"+(jhYear-2)+") as zdf_b, max(y.zdfwc_"+(jhYear-2)+") as zdfwc_b, min(y.zdf_"+(jhYear-3)+") as zdf_c, max(y.zdfwc_"+(jhYear-3)+") as zdfwc_c FROM zyzd_base_rank_rk_major x left join " + sfTable + "_jh_" + jhYear + " y ON x.yxmc = y.yxmc_org and y.xk_code like ? and y.pc = ? and y.pc_code = ? WHERE x.zymc = ? GROUP BY x.zymc, x.yxmc, x.ranking ORDER BY x.ranking";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + xk_code + "%");
			ps.setString(2, pc);
			ps.setString(3, pc_code);
			ps.setString(4, zymc);
			rs = ps.executeQuery();
			ZyzdMajorRanking bean = null;
			while (rs.next()) {
				bean = new ZyzdMajorRanking();
				bean.setRanking(rs.getInt("ranking"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZymc(rs.getString("zymc"));
				bean.setExt_zdf_a(rs.getString("zdf_a"));
				bean.setExt_zdf_b(rs.getString("zdf_b"));
				bean.setExt_zdf_c(rs.getString("zdf_c"));
				bean.setExt_zdfwc_a(rs.getString("zdfwc_a"));
				bean.setExt_zdfwc_b(rs.getString("zdfwc_b"));
				bean.setExt_zdfwc_c(rs.getString("zdfwc_c"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdMajorRanking> getRankingUnivRuanKeByZyml(String provinceTable, int yxTableYear, String xk_code, String zyml) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdMajorRanking> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.zymc, x.yxmc, x.ranking, min(y.zdf) as zdf, max(y.zdfwc) as zdfwc FROM zyzd_base_rank_rk_major x left join " + provinceTable + "_" + yxTableYear + "x y on x.yxmc = y.yxmc_org and y.xk_code like ? WHERE x.zyml = ? GROUP BY x.zymc, x.yxmc, x.ranking ORDER BY x.ranking";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + xk_code + "%");
			ps.setString(2, zyml);
			rs = ps.executeQuery();
			ZyzdMajorRanking bean = null;
			while (rs.next()) {
				bean = new ZyzdMajorRanking();
				bean.setRanking(rs.getInt("ranking"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZymc(rs.getString("zymc"));
				bean.setExt_zdf(rs.getString("zdf"));
				bean.setExt_zdfwc(rs.getString("zdfwc"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdMajorRanking> getRankingUnivRuanKeByZyml_ZY(String provinceTable, int yxTableYear, String xk_code, String zyml) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdMajorRanking> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.zymc, x.yxmc, x.ranking, min(y.zdf) as zdf, max(y.zdfwc) as zdfwc FROM zyzd_base_rank_rk_major x left join " + provinceTable + "_" + yxTableYear + " y on x.yxmc = y.yxmc_org and x.zymc = y.zymc_org and y.xk_code like ? WHERE x.zyml = ? GROUP BY x.zymc, x.yxmc, x.ranking ORDER BY x.ranking";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + xk_code + "%");
			ps.setString(2, zyml);
			rs = ps.executeQuery();
			ZyzdMajorRanking bean = null;
			while (rs.next()) {
				bean = new ZyzdMajorRanking();
				bean.setRanking(rs.getInt("ranking"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZymc(rs.getString("zymc"));
				bean.setExt_zdf(rs.getString("zdf"));
				bean.setExt_zdfwc(rs.getString("zdfwc"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdMajorRanking> getRankingUnivRuanKeRelated(String provinceTable, int yxTableYear, String xk_code, HashSet<String> zymlSets) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdMajorRanking> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.zymc, x.yxmc, x.ranking, min(y.zdf) as zdf, max(y.zdfwc) as zdfwc FROM zyzd_base_rank_rk_major x left join " + provinceTable + "_" + yxTableYear + "x y on x.yxmc = y.yxmc_org and y.xk_code like ? WHERE x.zyml in ("+Tools.getSQLQueryin(zymlSets)+") GROUP BY x.zymc, x.yxmc, x.ranking ORDER BY x.ranking";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + xk_code + "%");
			rs = ps.executeQuery();
			ZyzdMajorRanking bean = null;
			while (rs.next()) {
				bean = new ZyzdMajorRanking();
				bean.setRanking(rs.getInt("ranking"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZymc(rs.getString("zymc"));
				bean.setExt_zdf(rs.getString("zdf"));
				bean.setExt_zdfwc(rs.getString("zdfwc"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdMajorRanking> getRankingUnivRuanKeRelated_ZY(String provinceTable, int yxTableYear, String xk_code, HashSet<String> zymlSets) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdMajorRanking> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.zymc, x.yxmc, x.ranking, min(y.zdf) as zdf, max(y.zdfwc) as zdfwc FROM zyzd_base_rank_rk_major x left join " + provinceTable + "_" + yxTableYear + " y on x.yxmc = y.yxmc_org and x.zymc = y.zymc_org and y.xk_code like ? WHERE x.zyml in ("+Tools.getSQLQueryin(zymlSets)+") GROUP BY x.zymc, x.yxmc, x.ranking ORDER BY x.ranking";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + xk_code + "%");
			rs = ps.executeQuery();
			ZyzdMajorRanking bean = null;
			while (rs.next()) {
				bean = new ZyzdMajorRanking();
				bean.setRanking(rs.getInt("ranking"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZymc(rs.getString("zymc"));
				bean.setExt_zdf(rs.getString("zdf"));
				bean.setExt_zdfwc(rs.getString("zdfwc"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdMajorRanking> getRankingUnivRuanKe_ZY(String provinceTable, int yxTableYear, String xk_code, String zymc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdMajorRanking> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.zymc, x.yxmc, x.ranking, min(y.zdf) as zdf, max(y.zdfwc) as zdfwc FROM zyzd_base_rank_rk_major x left join " + provinceTable + "_" + yxTableYear + " y on x.yxmc = y.yxmc_org and x.zymc = y.zymc_org and y.xk_code like ? WHERE x.zymc = ? GROUP BY x.zymc, x.yxmc, x.ranking ORDER BY x.ranking";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + xk_code + "%");
			ps.setString(2, zymc);
			rs = ps.executeQuery();
			ZyzdMajorRanking bean = null;
			while (rs.next()) {
				bean = new ZyzdMajorRanking();
				bean.setRanking(rs.getInt("ranking"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZymc(rs.getString("zymc"));
				bean.setExt_zdf(rs.getString("zdf"));
				bean.setExt_zdfwc(rs.getString("zdfwc"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<SchoolMarjorCommonBean> getYxByHyInFormPage(String sfTable, int yxTableYear,String xk_code, HashSet<String> vocOneSets, HashSet<String> sfSets, String key_words, String tag, int score_from, int score_to, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolMarjorCommonBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String yxsfInSQl = sfSets.size() == 0 ? "" : "and y.yxsf in ("+Tools.getSQLQueryin(sfSets)+")";
			String SQL = "SELECT x.yxmc, MIN(y.zdf) as zdf, MAX(y.zdfwc) as zdfwc, COUNT(*) as cnt from career_jy_all_2024 x,"+sfTable+"_"+yxTableYear+"x y,zyzd_career_vocation z WHERE (y.zdf between ? and ?) and y.xk_code like ? and (y.yxmc like ?) "+yxsfInSQl+" and y.yx_tags like ? and x.yxmc = y.yxmc_org and x.group_name = z.v_name and z.p_name IN ("+Tools.getSQLQueryin(vocOneSets)+") GROUP BY x.yxmc ORDER BY COUNT(*) DESC LIMIT ?,?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, score_from);
			ps.setInt(2, score_to);
			ps.setString(3, "%" + xk_code + "%");
			ps.setString(4, "%" + key_words + "%");
			ps.setString(5, "%" + tag + "%");
			ps.setInt(6, (pageNumber - 1) * PAGE_ROW_CNT);
			ps.setInt(7, PAGE_ROW_CNT);
			rs = ps.executeQuery();
			SchoolMarjorCommonBean bean = null;
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc"));
				bean.setZdf(rs.getString("zdf"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setCnt_employ(rs.getInt("cnt"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public int getYxByHyInFormPage_SUM(String sfTable, int yxTableYear, String xk_code, HashSet<String> vocOneSets, HashSet<String> sfSets, String key_words, String tag, int score_from, int score_to) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String yxsfInSQl = sfSets.size() == 0 ? "" : "and y.yxsf in ("+Tools.getSQLQueryin(sfSets)+")";
			String SQL = "SELECT count(distinct x.yxmc) as cnt from career_jy_all_2024 x,"+sfTable+"_"+yxTableYear+"x y,zyzd_career_vocation z WHERE (y.zdf between ? and ?) and y.xk_code like ? and (y.yxmc like ?) "+yxsfInSQl+" and y.yx_tags like ? and x.yxmc = y.yxmc_org and x.group_name = z.v_name and z.p_name IN ("+Tools.getSQLQueryin(vocOneSets)+")";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, score_from);
			ps.setInt(2, score_to);
			ps.setString(3, "%" + xk_code + "%");
			ps.setString(4, "%" + key_words + "%");
			ps.setString(5, "%" + tag + "%");
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("cnt");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	
	
	public HashMap<String, List<SchoolBean>> listJiuyeZDFByYXMC(HashSet<String> inYXMC, String xk_code, String provinceTable, int yxTableYear, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, List<SchoolBean>> MAP = new HashMap<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT distinct yxmc_org, zdf, zdfwc, pc FROM "+provinceTable+"_"+yxTableYear+"x x WHERE x.yxmc_org IN ("+Tools.getSQLQueryin(inYXMC)+") AND xk_code LIKE ? GROUP BY yxmc_org, pc, zdf, zdfwc order by yxmc_org, zdf desc ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%"+xk_code+"%");
			rs = ps.executeQuery();
			
			while (rs.next()) {
				String yxmc = rs.getString("yxmc_org");
				List<SchoolBean> list = null;
				
				if(MAP.containsKey(yxmc)){
					list = MAP.get(yxmc);
				}else {
					list = new ArrayList<>();
				}
				
				SchoolBean schoolBean = new SchoolBean();
				schoolBean.setZdf(rs.getString("zdf"));
				schoolBean.setZdfwc(rs.getString("zdfwc"));
				schoolBean.setPc(rs.getString("pc"));
				
				list.add(schoolBean);
				
				MAP.put(yxmc, list);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return MAP;
	}
	
	public List<SchoolMarjorCommonBean> pickMajorBySchoolName(String provinceTableName, String yxmcOrg, String xkCode, int givingYear) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolMarjorCommonBean> list = new ArrayList<>();
		String xkQuery = "";
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM " + provinceTableName + "_" + givingYear + " x WHERE yxmc_org = ? and xk_code like ? order by yxmc, zdf desc";
			Tools.println(SQL+","+yxmcOrg+","+xkCode);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yxmcOrg);
			ps.setString(2, "%" + xkCode + "%");
			rs = ps.executeQuery();
			SchoolMarjorCommonBean bean = null;
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setZdf(rs.getString("zdf"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZymc_org(rs.getString("zymc_org"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setPc(rs.getString("pc"));
				bean.setXk(rs.getString("xk"));
				bean.setZx(rs.getString("zx"));
				bean.setZybz(rs.getString("zybz"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<SchoolMarjorCommonBean> pickMajorBySchoolNameAndMajorName(String provinceTableName, String yxmcOrg, String zymcOrg, String xkCode, int givingYear) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolMarjorCommonBean> list = new ArrayList<>();
		String xkQuery = "";
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM " + provinceTableName + "_" + givingYear + " x WHERE yxmc_org = ? and zymc_org = ? and xk_code like ? order by yxmc, zdf desc";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yxmcOrg);
			ps.setString(2, zymcOrg);
			ps.setString(3, "%" + xkCode + "%");
			rs = ps.executeQuery();
			SchoolMarjorCommonBean bean = null;
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setZdf(rs.getString("zdf"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZymc_org(rs.getString("zymc_org"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setPjf(rs.getString("pjf"));
				bean.setPjfwc(rs.getString("pjfwc"));
				bean.setPc(rs.getString("pc"));
				bean.setXk(rs.getString("xk"));
				bean.setZx(rs.getString("zx"));
				bean.setZybz(rs.getString("zybz"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<SchoolMarjorCommonBean> pickMajorBySchoolNameAndMajorCatg(String provinceTableName, String yxmcOrg, String zyml, String xkCode, int givingYear) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolMarjorCommonBean> list = new ArrayList<>();
		String xkQuery = "";
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM " + provinceTableName + "_" + givingYear + " x WHERE yxmc_org = ? and zyml = ? and xk_code like ? order by yxmc, zdf desc";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yxmcOrg);
			ps.setString(2, zyml);
			ps.setString(3, "%" + xkCode + "%");
			rs = ps.executeQuery();
			SchoolMarjorCommonBean bean = null;
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setZdf(rs.getString("zdf"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZymc_org(rs.getString("zymc_org"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setPjf(rs.getString("pjf"));
				bean.setPjfwc(rs.getString("pjfwc"));
				bean.setPc(rs.getString("pc"));
				bean.setXk(rs.getString("xk"));
				bean.setZx(rs.getString("zx"));
				bean.setZybz(rs.getString("zybz"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdBlock> getBlockedYxZyzByCid(String c_id, String sfTableName) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdBlock> list = new ArrayList<>();
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+sfTableName+"_form_user_block x WHERE c_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, c_id);
			rs = ps.executeQuery();
			ZyzdBlock bean = null;
			while (rs.next()) {
				bean = new ZyzdBlock();
				bean.setC_id(rs.getString("c_id"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZyz(rs.getString("zyz"));
				bean.setId(rs.getInt("id"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdFollow> getFollowYxByCid(String c_id, String sfTableName) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdFollow> list = new ArrayList<>();
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+sfTableName+"_form_user_follow x WHERE c_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, c_id);
			rs = ps.executeQuery();
			ZyzdFollow bean = null;
			while (rs.next()) {
				bean = new ZyzdFollow();
				bean.setC_id(rs.getString("c_id"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setId(rs.getInt("id"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdFollow> getFollowByYxmc(String yxmc_org, String sfTableName) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdFollow> list = new ArrayList<>();
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+sfTableName+"_form_user_follow x WHERE yxmc_org = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yxmc_org);
			rs = ps.executeQuery();
			ZyzdFollow bean = null;
			while (rs.next()) {
				bean = new ZyzdFollow();
				bean.setC_id(rs.getString("c_id"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setId(rs.getInt("id"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public int getFollowYxCntByCid(String c_id, String sfTableName) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT count(*) as ct FROM "+sfTableName+"_form_user_follow x WHERE c_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, c_id);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("ct");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public int getFollowZyCntByCid(String c_id, String sfTableName) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT count(*) as ct FROM zyzd_user_follow_zy x WHERE c_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, c_id);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("ct");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public int checkFollowYxExist(String c_id, String yxmc_org, String sfTableName) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT count(*) as ct FROM "+sfTableName+"_form_user_follow x WHERE c_id = ? and yxmc_org = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, c_id);
			ps.setString(2, yxmc_org);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("ct");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public void addFollowYx(String c_id, String yxmc_org, String sfTableName) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "INSERT INTO "+sfTableName+"_form_user_follow(c_id, yxmc_org, create_tm) VALUES(? ,? , now())";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, c_id);
			ps.setString(2, yxmc_org);
			ps.executeUpdate();
		} catch (Exception ex) {
			//ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void addBlockedYxZyz(String c_id, String yxmc, String zyz, String sfTableName) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "INSERT INTO "+sfTableName+"_form_user_block(c_id, yxmc, zyz, create_tm) VALUES(? ,? ,? , now())";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, c_id);
			ps.setString(2, yxmc);
			ps.setString(3, zyz);
			ps.executeUpdate();
		} catch (Exception ex) {
			//ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void deleteBlockedYxZyz(String c_id, String yxmc, String zyz, String sfTableName) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String zyzSQL = Tools.isEmpty(zyz) ? "and zyz is null" : "and zyz = ?";
			String SQL = "delete from "+sfTableName+"_form_user_block where c_id = ? and yxmc = ? " + zyzSQL;
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, c_id);
			ps.setString(2, yxmc);
			if(!Tools.isEmpty(zyz)) {
				ps.setString(3, zyz);
			}
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void deleteFollowYx(String c_id, String yxmc_org, String sfTableName) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from "+sfTableName+"_form_user_follow where c_id = ? and yxmc_org = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, c_id);
			ps.setString(2, yxmc_org);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public int getFollowCntByYxmc(String yxmc_org, String sfTableName) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT count(*) as ct FROM "+sfTableName+"_form_user_follow x WHERE yxmc_org = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yxmc_org);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("ct");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public void deleteMakerFormMainAll(int nf, String sfCode, String cid, int fType) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from "+sfCode+"_form_maker_main where c_id = ? and nf = ? and f_type = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.setInt(2, nf);
			ps.setInt(3, fType);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void deleteMakerFormMainAll(String sfCode, String cid) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from "+sfCode+"_form_maker_main where c_id = ?";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			SQLLogUtils.printSQL(ps);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void deleteMakerFormAllWithCid(String sfCode, String c_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from "+sfCode+"_form_maker where batch_id_org in (select batch_id from "+sfCode+"_form_maker_main where c_id = ?)";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, c_id);
			SQLLogUtils.printSQL(ps);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void deleteMakerFormMain(int nf, String sfCode, String cid, String pc, String pc_code, int fType) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from "+sfCode+"_form_maker_main where c_id = ? and pc = ? and pc_code = ? and f_type = ? and nf = ?";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.setString(2, pc);
			ps.setString(3, pc_code);
			ps.setInt(4, fType);
			ps.setInt(5, nf);
			SQLLogUtils.printSQL(ps);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void updateMakerFormMainFno(int nf, String sfCode, String batch_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "update "+sfCode+"_form_maker_main set f_no = id where batch_id = ? and nf = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			ps.setInt(2, nf);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void insertMakerFormMain(String sfCode, ZyzdFormMain zyzdFormMain) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into "+sfCode+"_form_maker_main(batch_id, score_cj, score_wc, score_xk, f_no, c_id, selected_zymc, pc, pc_code, nf, f_type,status, create_tm) VALUES(?,?,?,?,?,?,?,?,?,?,?,1,now())";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, zyzdFormMain.getBatch_id());
			ps.setInt(2, zyzdFormMain.getScore_cj());
			ps.setInt(3, zyzdFormMain.getScore_wc());
			ps.setString(4, zyzdFormMain.getScore_xk());
			ps.setString(5, zyzdFormMain.getF_no());
			ps.setString(6, zyzdFormMain.getC_id());
			ps.setString(7, zyzdFormMain.getSelected_zymc());
			ps.setString(8, zyzdFormMain.getPc());
			ps.setString(9, zyzdFormMain.getPc_code());
			ps.setInt(10, zyzdFormMain.getNf());
			ps.setInt(11, zyzdFormMain.getF_type());
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void deleteMakerForm(String batch_id, int seq_no_yx, String sfCode) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from "+sfCode+"_form_maker where batch_id_org = ? and seq_no_yx = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			ps.setInt(2, seq_no_yx);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void deleteMakerFormById(int id , String sfCode) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from "+sfCode+"_form_maker where id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, id);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public ZyzdForm getMakerFormById(int id , String sfCode) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "select * from "+sfCode+"_form_maker where id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, id);
			rs = ps.executeQuery();
			while (rs.next()) {
				return setMakerFormAll(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public void deleteMakerFormWithBatchId(String batch_id, String sfCode) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from "+sfCode+"_form_maker where batch_id_org = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void deleteMakerFormMainByUser(String c_id, String pc, String pc_code, String sfCode, int fType) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from "+sfCode+"_form_maker_main where c_id = ? and pc = ? and pc_code = ? and f_type = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, c_id);
			ps.setString(2, pc);
			ps.setString(3, pc_code);
			ps.setInt(4, fType);
			SQLLogUtils.printSQL(ps);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void deleteMakerFormMainByBatchId(String batch_id, String sfCode) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from "+sfCode+"_form_maker_main where batch_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void adjustMakerFormYxNoWithBatchId(String sfCode, List<ZyzdForm> updateList) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "update "+sfCode+"_form_maker set seq_no_yx = ? where id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			for(ZyzdForm form : updateList) {
				ps.setInt(1, form.getSeq_no_yx());
				ps.setInt(2, form.getId());
				ps.addBatch();
			}
			ps.executeBatch();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void increaseMakerFormYxNoWithBatchId(String sfCode, String batch_id, int fromSeqYxNo) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "update "+sfCode+"_form_maker set seq_no_yx = seq_no_yx - 1 where batch_id_org = ? and seq_no_yx >= ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			ps.setInt(2, fromSeqYxNo);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void adjustMakerFormYxNoWithBatchIddForDrag(String sfCode, List<ZyzdForm> updateList) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "update "+sfCode+"_form_maker set seq_no_yx = ? where id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			for(ZyzdForm form : updateList) {
				ps.setInt(1, form.getSeq_no_yx());
				ps.setInt(2, form.getId());
				ps.addBatch();
			}
			ps.executeBatch();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void adjustMakerFormZyNoWithId(String sfCode, List<ZyzdForm> updateList) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "update "+sfCode+"_form_maker set seq_no_zy = ? where id = ?";
			ps = conn.prepareStatement(SQL);
			for(ZyzdForm form : updateList) {
				ps.setInt(1, form.getSeq_no_zy());
				ps.setInt(2, form.getId());
				ps.addBatch();
			}
			SQLLogUtils.printSQL(ps);
			ps.executeBatch();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	// 根据group_id、table_id和item_id查询前5条和后5条记录
    public List<YxzyStatistics> getSurroundingItems(String group_id, String table_id, String item_id) {
        List<YxzyStatistics> result = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;

        try {
            // 获取数据库连接
            conn = DatabaseUtils.getConnection();

            // 首先查询item_id对应的item_sort值
            String querySortSQL = "SELECT item_sort FROM zyzd_base_yxzy_statistics WHERE group_id = ? AND table_id = ? AND item_id = ?;";
            ps = conn.prepareStatement(querySortSQL);
            ps.setString(1, group_id);
            ps.setString(2, table_id);
            ps.setString(3, item_id);
            rs = ps.executeQuery();

            int targetItemSort = -1;
            if (rs.next()) {
                targetItemSort = rs.getInt("item_sort");
            }

            // 查询前5条和后5条记录
            String querySurroundingSQL = "SELECT * FROM zyzd_base_yxzy_statistics WHERE group_id = ? AND table_id = ? AND item_sort BETWEEN ? AND ? ORDER BY item_sort DESC;";
            ps = conn.prepareStatement(querySurroundingSQL);

            // 计算范围
            int lowerBound = targetItemSort - 5;
            int upperBound = targetItemSort + 5;

            ps.setString(1, group_id);
            ps.setString(2, table_id);
            ps.setInt(3, lowerBound);
            ps.setInt(4, upperBound);

            rs = ps.executeQuery();

            // 处理结果集
            while (rs.next()) {
                YxzyStatistics bean = new YxzyStatistics();
                bean.setId(rs.getInt("id"));
                bean.setGroup_id(rs.getString("group_id"));
                bean.setTable_id(rs.getString("table_id"));
                bean.setItem_id(rs.getString("item_id"));
                bean.setItem_sort(rs.getInt("item_sort"));
                bean.setColumn_title(rs.getString("column_title"));
                bean.setColumn_title_standare(rs.getString("column_title_standare"));
                bean.setColumn_value(rs.getString("column_value"));
                bean.setColumn_sort(rs.getInt("column_sort"));
                result.add(bean);
            }
        } catch (Exception ex) {
            ex.printStackTrace(); // 打印异常信息
        } finally {
            // 关闭所有连接
            closeAllConnection(conn, ps, rs);
        }

        return result;
    }
    
 // 根据group_id、table_id和item_id查询前5条和后5条记录
    public List<YxzyStatistics> getStatisticsItem(String item_id) {
        List<YxzyStatistics> result = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;

        try {
            // 获取数据库连接
            conn = DatabaseUtils.getConnection();

            // 首先查询item_id对应的item_sort值
            String querySortSQL = "SELECT item_sort FROM zyzd_base_yxzy_statistics WHERE item_id = ?;";
            ps = conn.prepareStatement(querySortSQL);
            ps.setString(1, item_id);
            rs = ps.executeQuery();

            int targetItemSort = -1;
            while (rs.next()) {
            	YxzyStatistics bean = new YxzyStatistics();
                bean.setId(rs.getInt("id"));
                bean.setGroup_id(rs.getString("group_id"));
                bean.setTable_id(rs.getString("table_id"));
                bean.setItem_id(rs.getString("item_id"));
                bean.setItem_sort(rs.getInt("item_sort"));
                bean.setColumn_title(rs.getString("column_title"));
                bean.setColumn_title_standare(rs.getString("column_title_standare"));
                bean.setColumn_value(rs.getString("column_value"));
                bean.setColumn_sort(rs.getInt("column_sort"));
                result.add(bean);
            }

        } catch (Exception ex) {
            ex.printStackTrace(); // 打印异常信息
        } finally {
            // 关闭所有连接
            closeAllConnection(conn, ps, rs);
        }

        return result;
    }
	
	public void insertMakerForm(String sfCode, ZyzdForm form) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into "+sfCode+"_form_maker(batch_id, batch_id_org, seq_no_yx, yxmc, yxbz, yxdm, zyz, seq_no_zy, zymc, zybz, zydm, zdf_a, zdf_b, zdf_c, zdfwc_a, zdfwc_b, zdfwc_c, pjf_a, pjf_b, pjf_c, pjfwc_a, pjfwc_b, pjfwc_c, yxmc_org, zymc_org, jhs_a, jhs_b, jhs_c, zgf_a, zgf_b, zgf_c, zgfwc_a, zgfwc_b, zgfwc_c, jhs, fee) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
			//Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			int index = 1;
			ps.setString(index++, form.getBatch_id());
			ps.setString(index++, form.getBatch_id_org());
			ps.setInt(index++, form.getSeq_no_yx());
			ps.setString(index++, form.getYxmc());
			ps.setString(index++, form.getYxbz());
			ps.setString(index++, form.getYxdm());
			ps.setString(index++, form.getZyz());
			ps.setInt(index++, form.getSeq_no_zy());
			ps.setString(index++, form.getZymc());
			ps.setString(index++, form.getZybz());
			ps.setString(index++, form.getZydm());

			ps.setInt(index++, form.getZdf_a());
			ps.setInt(index++, form.getZdf_b());
			ps.setInt(index++, form.getZdf_c());
			
			ps.setInt(index++, form.getZdfwc_a());
			ps.setInt(index++, form.getZdfwc_b());
			ps.setInt(index++, form.getZdfwc_c());
			
			ps.setInt(index++, form.getPjf_a());
			ps.setInt(index++, form.getPjf_b());
			ps.setInt(index++, form.getPjf_c());
			
			ps.setInt(index++, form.getPjfwc_a());
			ps.setInt(index++, form.getPjfwc_b());
			ps.setInt(index++, form.getPjfwc_c());
			
			ps.setString(index++, form.getYxmc_org());
			ps.setString(index++, form.getZymc_org());
			
			ps.setInt(index++, form.getJhs_a());
			ps.setInt(index++, form.getJhs_b());
			ps.setInt(index++, form.getJhs_c());
			
			ps.setInt(index++, form.getZgf_a());
			ps.setInt(index++, form.getZgf_b());
			ps.setInt(index++, form.getZgf_c());
			
			ps.setInt(index++, form.getZgfwc_a());
			ps.setInt(index++, form.getZgfwc_b());
			ps.setInt(index++, form.getZgfwc_c());
			ps.setInt(index++, form.getJhs());
			ps.setString(index++, form.getFee());
			
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void insertMakerFormByBatch(String sfCode, List<ZyzdForm> formList) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into "+sfCode+"_form_maker(batch_id, batch_id_org, seq_no_yx, yxmc, yxbz, yxdm, zyz, seq_no_zy, zymc, zybz, zydm, zdf_a, zdf_b, zdf_c, zdfwc_a, zdfwc_b, zdfwc_c, pjf_a, pjf_b, pjf_c, pjfwc_a, pjfwc_b, pjfwc_c, yxmc_org, zymc_org, jhs_a, jhs_b, jhs_c, zgf_a, zgf_b, zgf_c, zgfwc_a, zgfwc_b, zgfwc_c, jhs, fee) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
			//Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			
			for(ZyzdForm form : formList) {
				int index = 1;
				ps.setString(index++, form.getBatch_id());
				ps.setString(index++, form.getBatch_id_org());
				ps.setInt(index++, form.getSeq_no_yx());
				ps.setString(index++, form.getYxmc());
				ps.setString(index++, form.getYxbz());
				ps.setString(index++, form.getYxdm());
				ps.setString(index++, form.getZyz());
				ps.setInt(index++, form.getSeq_no_zy());
				ps.setString(index++, form.getZymc());
				ps.setString(index++, form.getZybz());
				ps.setString(index++, form.getZydm());
	
				ps.setInt(index++, form.getZdf_a());
				ps.setInt(index++, form.getZdf_b());
				ps.setInt(index++, form.getZdf_c());
				
				ps.setInt(index++, form.getZdfwc_a());
				ps.setInt(index++, form.getZdfwc_b());
				ps.setInt(index++, form.getZdfwc_c());
				
				ps.setInt(index++, form.getPjf_a());
				ps.setInt(index++, form.getPjf_b());
				ps.setInt(index++, form.getPjf_c());
				
				ps.setInt(index++, form.getPjfwc_a());
				ps.setInt(index++, form.getPjfwc_b());
				ps.setInt(index++, form.getPjfwc_c());
				
				ps.setString(index++, form.getYxmc_org());
				ps.setString(index++, form.getZymc_org());
				
				ps.setInt(index++, form.getJhs_a());
				ps.setInt(index++, form.getJhs_b());
				ps.setInt(index++, form.getJhs_c());
				
				ps.setInt(index++, form.getZgf_a());
				ps.setInt(index++, form.getZgf_b());
				ps.setInt(index++, form.getZgf_c());
				
				ps.setInt(index++, form.getZgfwc_a());
				ps.setInt(index++, form.getZgfwc_b());
				ps.setInt(index++, form.getZgfwc_c());
				ps.setInt(index++, form.getJhs());
				ps.setString(index++, form.getFee());
				
				ps.addBatch();
			}
			ps.executeBatch();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public List<ZyzdForm> getMakerForm(String sfCode, String batch_id_org) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdForm> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+sfCode+"_form_maker x WHERE batch_id_org = ? ORDER BY seq_no_yx, seq_no_zy";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				list.add(setMakerFormAll(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdForm> getMakerFormWithJHInfo(int nf, String sfCode, String batch_id_org, String pc, String pc_code, String xk_code) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = nf - 1;
		List<ZyzdForm> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.batch_id, x.id, x.batch_id_org, x.seq_no_yx, x.seq_no_zy, x.yxdm, jh.yxmc, x.zyz, x.zydm, jh.zymc, "
					+ "jh.zybz, jh.zymc_org, jh.yxmc_org, jh.jhs, jh.fee, jh.xz,  "
					+ "jh.qsf_a, jh.qsf_b, jh.qsf_c, jh.qsf, "
					+ "jh.zdf_"+(dataYear)+" AS zdf_a, jh.zdf_"+(dataYear - 1)+" AS zdf_b, jh.zdf_"+(dataYear-2)+" AS zdf_c, "
					+ "jh.zdfwc_"+(dataYear)+" AS zdfwc_a, jh.zdfwc_"+(dataYear - 1)+" AS zdfwc_b, jh.zdfwc_"+(dataYear-2)+" AS zdfwc_c, "
					+ "jh.zgf_"+(dataYear)+" AS zgf_a, jh.zgf_"+(dataYear - 1)+" AS zgf_b, jh.zgf_"+(dataYear-2)+" AS zgf_c, "
					+ "jh.zgfwc_"+(dataYear)+" AS zgfwc_a, jh.zgfwc_"+(dataYear - 1)+" AS zgfwc_b, jh.zgfwc_"+(dataYear-2)+" AS zgfwc_c, "
					+ "jh.pjf_"+(dataYear)+" AS pjf_a, jh.pjf_"+(dataYear-1)+" AS pjf_b, jh.pjf_"+(dataYear-2)+" AS pjf_c, "
					+ "jh.pjfwc_"+(dataYear)+" AS pjfwc_a, jh.pjfwc_"+(dataYear-1)+" AS pjfwc_b, jh.pjfwc_"+(dataYear-2)+" AS pjfwc_c, "
					+ "jh.lqrs_"+(dataYear)+" AS lqrs_a, jh.lqrs_"+(dataYear-1)+" AS lqrs_b, jh.lqrs_"+(dataYear-2)+" AS lqrs_c, "
					+ "jh.jhs_"+(dataYear)+" AS jhs_a, jh.jhs_"+(dataYear-1)+" AS jhs_b, jh.jhs_"+(dataYear-2)+" AS jhs_c "
					+ "FROM "+sfCode+"_form_maker x LEFT JOIN "+sfCode+"_jh_"+nf+" jh "
					+ "ON x.yxdm = jh.yxdm AND x.zydm = jh.zydm AND x.zyz = jh.zyz AND jh.pc = ? AND jh.pc_code = ? AND jh.xk_code like ? "
					+ "WHERE x.batch_id_org = ? ORDER BY x.seq_no_yx, x.seq_no_zy";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%" + xk_code + "%");
			ps.setString(4, batch_id_org); 
			rs = ps.executeQuery();
			ZyzdForm bean = null;
			SQLLogUtils.printSQL(" === getMakerFormMainByBatchId() : ", ps);
			while (rs.next()) {
				bean = setMakerFormAll(rs);
				bean.setExt_jh_qsf_a(rs.getInt("qsf_a"));
				bean.setExt_jh_qsf_b(rs.getInt("qsf_b"));
				bean.setExt_jh_qsf_c(rs.getInt("qsf_c"));
				bean.setExt_jh_qsf(rs.getInt("qsf"));
				bean.setLqrs_a(rs.getInt("lqrs_a"));
				bean.setLqrs_b(rs.getInt("lqrs_b"));
				bean.setLqrs_c(rs.getInt("lqrs_c"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	private static ZyzdForm setMakerFormAll(ResultSet rs) throws Exception {
		return LhyJDBC.setZyzdForm(rs);
	}
	
	public List<ZyzdForm> getMakerForm(String sfCode, String batch_id_org, int seq_no_yx) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdForm> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+sfCode+"_form_maker x WHERE batch_id_org = ? and seq_no_yx = ? ORDER BY seq_no_yx, seq_no_zy";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			ps.setInt(2, seq_no_yx);
			rs = ps.executeQuery();
			ZyzdForm bean = null;
			while (rs.next()) {
				list.add(setMakerFormAll(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdForm> getMakerForm(String sfCode, String batch_id_org, String yxmc, String zyz) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdForm> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String zyzSQL = Tools.isEmpty(zyz) ? "and zyz is null" : "and zyz = ?";
			String SQL = "SELECT * FROM "+sfCode+"_form_maker x WHERE batch_id_org = ? and yxmc = ? "+zyzSQL+" ORDER BY seq_no_yx, seq_no_zy";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			ps.setString(2, yxmc);
			if(!Tools.isEmpty(zyz)) {
				ps.setString(3, zyz);
			}
			rs = ps.executeQuery();
			ZyzdForm bean = null;
			while (rs.next()) {
				list.add(setMakerFormAll(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdFormMain> getMakerFormMainWithPc(int nf, String sf, String order_id, int fType, String pc, String pc_code) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdFormMain> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+sf+"_form_maker_main x WHERE c_id = ? and nf = ? and f_type = ? and pc = ? and pc_code = ? and status > 0 ORDER BY create_tm desc";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, order_id);
			ps.setInt(2, nf);
			ps.setInt(3, fType);
			ps.setString(4, pc);
			ps.setString(5, pc_code);
			SQLLogUtils.printSQL(ps);
			rs = ps.executeQuery();
			ZyzdFormMain bean = null;
			while (rs.next()) {
				bean = setZyzdFormMain(rs);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public List<ZyzdFormMain> getMakerFormMain(int nf, String sf, String makerID, int fType) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdFormMain> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+sf+"_form_maker_main x WHERE c_id = ? and nf = ? and f_type = ? ORDER BY create_tm desc";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, makerID);
			ps.setInt(2, nf);
			ps.setInt(3, fType);
			rs = ps.executeQuery();
			ZyzdFormMain bean = null;
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				bean = setZyzdFormMain(rs);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdFormMain> getMakerFormMain(int nf, String sf, String makerID) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdFormMain> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+sf+"_form_maker_main x WHERE c_id = ? and nf = ? ORDER BY create_tm desc";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, makerID);
			ps.setInt(2, nf);
			rs = ps.executeQuery();
			ZyzdFormMain bean = null;
			while (rs.next()) {
				bean = setZyzdFormMain(rs);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public ZyzdFormMain getMakerFormMainByFno(String sfCode, String fno) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+sfCode+"_form_maker_main x WHERE f_no = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, fno);
			rs = ps.executeQuery();
			ZyzdFormMain bean = null;
			while (rs.next()) {
				return setZyzdFormMain(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public List<ZyzdFormMain> getAllSQLLinkMakerFormMain(int nf, String sf) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdFormMain> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+sf+"_form_maker_main_sqllink x ORDER BY create_tm desc";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			ZyzdFormMain bean = null;
			while (rs.next()) {
				bean = setZyzdFormMain(rs);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	/**
	 * 测试
	 */
	public void testInsert() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "INSERT INTO tb_s5_sc_form(batch_id, seq_no_yx, seq_no_zy, yxdm, zydm, zyz, create_tm) VALUES (?, ?, ?, ?, ?, ?, NOW())";
			ps = conn.prepareStatement(SQL);
			String uuid = UUID.randomUUID().toString();
			for(int i=1;i<=45;i++) {
				for(int ix=1;ix<=6;ix++) {
					ps.setString(1, uuid);
					ps.setInt(2, i);
					ps.setInt(3, ix);
					ps.setString(4, "5012");
					ps.setString(5, "01");
					ps.setString(6, "101");
					ps.addBatch();
				}
			}
			ps.executeBatch();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public LhyCardSchoolStudent getLhyCardSchoolStudent(String c_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM lhy_card_school_student x WHERE base_c_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, c_id);
			rs = ps.executeQuery();
			LhyCardSchoolStudent bean = null;
			while (rs.next()) {
				bean = new LhyCardSchoolStudent();
				bean.setBase_c_id(c_id);
				bean.setClass_name(rs.getString("class_name"));
				bean.setSchool_name(rs.getString("school_name"));
				bean.setStudent_name(rs.getString("student_name"));
				bean.setLhy_c_id(rs.getString("lhy_c_id"));
				bean.setLhy_c_parent_id(rs.getString("lhy_c_parent_id"));
			}
			return bean;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public ZyzdFormMain getMakerFormMainByBatchId(String sfCode, String batchId) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+sfCode+"_form_maker_main x WHERE batch_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batchId);
			rs = ps.executeQuery();
			ZyzdFormMain bean = null;
			while (rs.next()) {
				return setZyzdFormMain(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	private ZyzdFormMain setZyzdFormMain(ResultSet rs) throws SQLException{
		ZyzdFormMain bean = new ZyzdFormMain(); 
		bean.setBatch_id(rs.getString("batch_id"));
		bean.setC_id(rs.getString("c_id"));
		bean.setPc(rs.getString("pc"));
		bean.setPc_code(rs.getString("pc_code"));
		bean.setChecker_id(rs.getString("last_checker_id"));
		bean.setChecker_name(rs.getString("last_checker_name"));
		bean.setChecker_remark(rs.getString("last_checker_remark"));
		bean.setJoined_checker_cnt(rs.getInt("joined_checker_cnt"));
		bean.setCreate_tm(rs.getTimestamp("create_tm"));
		bean.setF_no(rs.getString("f_no"));
		bean.setLast_update_tm(rs.getTimestamp("last_update_tm"));
		bean.setScore_cj(rs.getInt("score_cj"));
		bean.setScore_wc(rs.getInt("score_wc"));
		bean.setNf(rs.getInt("nf"));
		bean.setScore_xk(rs.getString("score_xk"));
		bean.setSelected_zymc(rs.getString("selected_zymc"));
		bean.setF_type(rs.getInt("f_type"));
		return bean;
	} 
	
	public List<ZyzdForm> getCheckerForm(String sf, String batch_id_org) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdForm> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+sf+"_form_checker x WHERE batch_id_org = ? ORDER BY create_tm desc, seq_no_yx, seq_no_zy";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			rs = ps.executeQuery();
			ZyzdForm bean = null;
			while (rs.next()) {
				bean = new ZyzdForm();
				bean.setBatch_id(rs.getString("batch_id"));
				bean.setBatch_id_org(rs.getString("batch_id_org"));
				bean.setSeq_no_yx(rs.getInt("seq_no_yx"));
				bean.setSeq_no_zy(rs.getInt("seq_no_zy"));
				bean.setYxbz(rs.getString("yxbz"));
				bean.setYxdm(rs.getString("yxdm"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZybz(rs.getString("zybz"));
				bean.setZydm(rs.getString("zydm"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZyz(rs.getString("zyz"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public HashMap<String, ZDKSRank> getAllTongWF(String sf, String kl, int givingWC) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, ZDKSRank> BEAN_MAP = new HashMap<>();
		try {
			ZyzdProvince zyzdProvince = ZyzdCache.getUserCardProvinceName(sf);
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+zyzdProvince.getTableName()+"_zdks_rank  WHERE  sf = ? and kl_code LIKE ? and (WC-CNT) < ? AND WC >= ? ";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, sf);
			ps.setString(2, "%"+kl+"%");
			ps.setInt(3, givingWC);
			ps.setInt(4, givingWC);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				ZDKSRank bean = new ZDKSRank();
				bean.setCnt(rs.getInt("cnt"));
				bean.setFd(rs.getString("fd"));
				bean.setKl(rs.getString("kl"));
				bean.setNf(rs.getInt("nf"));
				bean.setScore(rs.getInt("SCORE"));
				bean.setSf(rs.getString("sf"));
				bean.setWc(rs.getInt("wc"));
				BEAN_MAP.put(String.valueOf(bean.getNf()), bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return BEAN_MAP;
	}
	
	public List<ZyzdFormMain> getCheckerFormMain(String sf, String makerID) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdFormMain> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+sf+"_form_checker_main x WHERE c_id = ? ORDER BY create_tm desc, seq_no_yx, seq_no_zy";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, makerID);
			rs = ps.executeQuery();
			ZyzdFormMain bean = null;
			while (rs.next()) {
				bean = new ZyzdFormMain();
				bean.setBatch_id(rs.getString("batch_id"));
				bean.setBatch_id_org(rs.getString("batch_id_org"));
				bean.setC_id(rs.getString("c_id"));
				bean.setChecker_id(rs.getString("checker_id"));
				bean.setChecker_name(rs.getString("checker_name"));
				bean.setChecker_remark(rs.getString("checker_remark"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				bean.setF_no(rs.getString("f_no"));
				bean.setLast_update_tm(rs.getTimestamp("last_update_tm"));
				bean.setScore_cj(rs.getInt("score_cj"));
				bean.setScore_wc(rs.getInt("score_wc"));
				bean.setScore_xk(rs.getString("score_xk"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdProvPcx> getPcx(String sf, int nf) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdProvPcx> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_prov_pcx x WHERE x.sf = ? and x.nf = ? order by pcx_sort asc";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, sf);
			ps.setInt(2, nf);
			rs = ps.executeQuery();
			ZyzdProvPcx pcx = null;
			while (rs.next()) {
				pcx = new ZyzdProvPcx();
				pcx.setNf(rs.getInt("nf"));
				pcx.setOfficial_name(rs.getString("official_name"));
				pcx.setPcx_wl(rs.getInt("pcx_wl"));
				pcx.setPcx_ls(rs.getInt("pcx_ls"));
				pcx.setPcx_name(rs.getString("pcx_name"));
				pcx.setSf(rs.getString("sf"));
				list.add(pcx);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public HashMap<String, ZyzdProvPcx> getAllPcxByYear(int nf) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, ZyzdProvPcx> MAPS = new HashMap<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_prov_pcx x WHERE x.nf = ? order by pcx_sort asc";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, nf);
			rs = ps.executeQuery();
			ZyzdProvPcx pcx = null;
			while (rs.next()) {
				pcx = new ZyzdProvPcx();
				pcx.setNf(rs.getInt("nf"));
				pcx.setOfficial_name(rs.getString("official_name"));
				pcx.setPcx_wl(rs.getInt("pcx_wl"));
				pcx.setPcx_ls(rs.getInt("pcx_ls"));
				pcx.setPcx_name(rs.getString("pcx_name"));
				pcx.setSf(rs.getString("sf"));
				MAPS.put(pcx.getSf() + "_" +pcx.getOfficial_name(), pcx);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return MAPS;
	}
	
	public List<ZyzdScoreConvert> getExistRecord(String sfName) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdScoreConvert> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String provinceCode = JDBC.HM_PROVINCE_CODE.get(sfName);
			
			String SQL = "SELECT nf,sf,city,zdpc,count(*) as cnt, history_id,hxb_id FROM "+provinceCode+"_zdks_score_convert x WHERE x.sf = ? GROUP BY nf,sf,city,zdpc,history_id,hxb_id order by nf desc";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, sfName);
			rs = ps.executeQuery();
			ZyzdScoreConvert score = null;
			while (rs.next()) {
				score = new ZyzdScoreConvert();
				score.setNf(rs.getInt("nf"));
				score.setSf(rs.getString("sf"));
				score.setCity(rs.getString("city"));
				score.setExt_count(rs.getInt("cnt"));
				score.setZdpc(rs.getString("zdpc"));
				score.setHistory_id(rs.getString("history_id"));
				score.setHxb_id(rs.getString("hxb_id"));
				list.add(score);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdUniversityDataBean> pickSchoolByMajorNameWithGivingScore(String provinceTableName, String zymcOrg, String xkCode,int scoreFrom, int scoreTo, int givingYear) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdUniversityDataBean> list = new ArrayList<>();
		String xkQuery = "";
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM " + provinceTableName + "_" + givingYear + " x WHERE zymc_org = ? and xk_code like ? and zdf between ? and ? order by zdf desc";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, zymcOrg);
			ps.setString(2, "%" + xkCode + "%");
			ps.setInt(3, scoreFrom);
			ps.setInt(4, scoreTo);
			rs = ps.executeQuery();
			ZyzdUniversityDataBean sb = null;
			while (rs.next()) {
				sb = new ZyzdUniversityDataBean();
				sb.setYxmc(rs.getString("yxmc"));
				sb.setYxdm(rs.getString("yxdm"));
				sb.setYxbz(rs.getString("zybz"));
				sb.setExt_zymc(rs.getString("zymc"));
				sb.setExt_zymc_org(rs.getString("zymc_org"));
				sb.setYxmc_org(rs.getString("yxmc_org"));
				sb.setNf(rs.getInt("nf"));
				sb.setXk(rs.getString("xk"));
				sb.setXk_code(rs.getString("xk_code"));
				sb.setPc(rs.getString("pc"));
				sb.setZyz(rs.getString("zyz"));
				sb.setZx(rs.getString("zx"));
				sb.setZdf(rs.getString("zdf"));
				sb.setZdfwc(rs.getString("zdfwc"));
				sb.setYxsf(rs.getString("yxsf"));
				sb.setYxcs(rs.getString("yxcs"));
				sb.setInd_nature(rs.getString("ind_nature"));
				sb.setInd_catg(rs.getString("ind_catg"));
				sb.setCnt_grad(rs.getFloat("cnt_grad"));
				sb.setCnt_company(rs.getInt("cnt_company"));
				sb.setCnt_employ(rs.getInt("cnt_employ"));
				list.add(sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdUniversityDataBean> pickSchoolByMajorNameWithGivingScoreAndRelationMajor(String provinceTableName, HashSet<String> relationZyml, String xkCode,int scoreFrom, int scoreTo, int givingYear) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdUniversityDataBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String zymlSQL = relationZyml.size() == 0 ? "" : "zyml in ("+Tools.getSQLQueryin(relationZyml)+") and";
			String SQL = "SELECT * FROM " + provinceTableName + "_" + givingYear + " x WHERE "+zymlSQL+" xk_code like ? and zdf between ? and ? order by zdf desc";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + xkCode + "%");
			ps.setInt(2, scoreFrom);
			ps.setInt(3, scoreTo);
			rs = ps.executeQuery();
			ZyzdUniversityDataBean sb = null;
			while (rs.next()) {
				sb = new ZyzdUniversityDataBean();
				sb.setYxmc(rs.getString("yxmc"));
				sb.setYxdm(rs.getString("yxdm"));
				sb.setExt_zymc(rs.getString("zymc"));
				sb.setExt_zymc_org(rs.getString("zymc_org"));
				sb.setExt_zyz(rs.getString("zyz"));
				sb.setYxbz(rs.getString("zybz"));
				sb.setYxmc_org(rs.getString("yxmc_org"));
				sb.setNf(rs.getInt("nf"));
				sb.setXk(rs.getString("xk"));
				sb.setXk_code(rs.getString("xk_code"));
				sb.setPc(rs.getString("pc"));
				sb.setZyz(rs.getString("zyz"));
				sb.setZx(rs.getString("zx"));
				sb.setZdf(rs.getString("zdf"));
				sb.setZdfwc(rs.getString("zdfwc"));
				sb.setYxsf(rs.getString("yxsf"));
				sb.setYxcs(rs.getString("yxcs"));
				sb.setInd_nature(rs.getString("ind_nature"));
				sb.setInd_catg(rs.getString("ind_catg"));
				sb.setCnt_grad(rs.getFloat("cnt_grad"));
				sb.setCnt_company(rs.getInt("cnt_company"));
				sb.setCnt_employ(rs.getInt("cnt_employ"));
				list.add(sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdUniversityDataBean> pickMajorWithGivingYxdmAndZydmAndPc(int nf, String provinceTableName, String xkCode, String yxdm, String zydm, String pc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdUniversityDataBean> list = new ArrayList<>();
		String xkQuery = "";
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM " + provinceTableName + "_" + nf + " x WHERE xk_code like ? and yxdm = ? and zydm = ? and pc = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + xkCode + "%");
			ps.setString(2, yxdm);
			ps.setString(3, zydm);
			ps.setString(4, pc);
			rs = ps.executeQuery();
			ZyzdUniversityDataBean sb = null;
			while (rs.next()) {
				sb = new ZyzdUniversityDataBean();
				sb.setYxmc(rs.getString("yxmc"));
				sb.setYxdm(rs.getString("yxdm"));
				sb.setYxbz(rs.getString("zybz"));
				sb.setYxmc_org(rs.getString("yxmc_org"));
				sb.setNf(rs.getInt("nf"));
				sb.setXk(rs.getString("xk"));
				sb.setXk_code(rs.getString("xk_code"));
				sb.setPc(rs.getString("pc"));
				sb.setZyz(rs.getString("zyz"));
				sb.setZx(rs.getString("zx"));
				sb.setZdf(rs.getString("zdf"));
				sb.setZdfwc(rs.getString("zdfwc"));
				sb.setYxsf(rs.getString("yxsf"));
				sb.setYxcs(rs.getString("yxcs"));
				sb.setInd_nature(rs.getString("ind_nature"));
				sb.setInd_catg(rs.getString("ind_catg"));
				sb.setCnt_grad(rs.getFloat("cnt_grad"));
				sb.setCnt_company(rs.getInt("cnt_company"));
				sb.setCnt_employ(rs.getInt("cnt_employ"));
				list.add(sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public HashMap<String, ZyzdUniversityBean> getAllUniversities() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, ZyzdUniversityBean> schoolMap = new HashMap<>();

		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_base_university";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			ZyzdUniversityBean bean = null;
			while (rs.next()) {
				bean = new ZyzdUniversityBean();

				bean.setYxmc(rs.getString("yxmc"));
				bean.setCnt_laboratory(rs.getString("cnt_laboratory"));
				bean.setCnt_landarea(rs.getString("cnt_landarea"));
				bean.setCnt_master(rs.getString("cnt_master"));
				bean.setCnt_phd(rs.getString("cnt_phd"));
				bean.setInd_catg(rs.getString("ind_catg"));
				bean.setInd_nature(rs.getString("ind_nature"));
				bean.setInd_type(rs.getString("ind_type"));
				bean.setInfo_addr(rs.getString("info_addr"));
				bean.setInfo_descp(rs.getString("info_descp"));
				bean.setInfo_found_time(rs.getString("info_found_time"));
				bean.setInfo_logo(rs.getString("info_logo"));
				bean.setInfo_tel(rs.getString("info_tel"));
				bean.setInfo_website(rs.getString("info_website"));
				bean.setIs211(rs.getString("is211"));
				bean.setIs985(rs.getString("is985"));
				bean.setIsqj(rs.getString("isqj"));
				bean.setIssyl(rs.getString("issyl"));
				bean.setLsy(rs.getString("lsy"));
				bean.setPosition_cs(rs.getString("position_cs"));
				bean.setPosition_qy(rs.getString("position_qy"));
				bean.setPosition_sf(rs.getString("position_sf"));
				bean.setRanking_qs(rs.getString("ranking_qs"));
				bean.setRanking_rk(rs.getString("ranking_rk"));
				bean.setRanking_usnews(rs.getString("ranking_usnews"));
				bean.setRanking_wsl(rs.getString("ranking_wsl"));
				bean.setRanking_xyh(rs.getString("ranking_xyh"));
				bean.setYxmc_used(rs.getString("yxmc_used"));
				bean.setCnt_grad(rs.getFloat("cnt_grad"));
				bean.setCnt_company(rs.getInt("cnt_company"));
				bean.setCnt_employ(rs.getInt("cnt_employ"));
				bean.setYx_tags(rs.getString("yx_tags"));
				bean.setYx_tags_all(rs.getString("yx_tags_all"));

				schoolMap.put(bean.getYxmc(), bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return schoolMap;
	}
	
	public HashMap<String, ZyzdBaseUniversityJz> getAllUniversitiesJz() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, ZyzdBaseUniversityJz> schoolMap = new HashMap<>();

		try {
			conn = DatabaseUtils.getConnection();
            String SQL = "SELECT * FROM zyzd_base_university_jz";
            System.out.println(SQL);
            ps = conn.prepareStatement(SQL);
            rs = ps.executeQuery();
            ZyzdBaseUniversityJz bean = null;
            while (rs.next()) {
                bean = new ZyzdBaseUniversityJz();
                bean.setYxmc(rs.getString("yxmc"));
                bean.setYxmc_org(rs.getString("yxmc_org"));
                bean.setYxsf(rs.getString("yxsf"));
                bean.setZgbm(rs.getString("zgbm"));
                bean.setYxcc(rs.getString("yxcc"));
                bean.setJz_url(rs.getString("jz_url"));
                bean.setYx_url(rs.getString("yx_url"));
                bean.setLogo_url(rs.getString("logo_url"));

                schoolMap.put(bean.getYxmc(), bean);
            }
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return schoolMap;
	}
	
	public HashMap<String, String> getAllYbEbCache() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, String> schoolMap = new HashMap<>();

		try {
			conn = DatabaseUtils.getConnection();
            String SQL = "SELECT * FROM zyzd_base_university_ybeb x ";
            System.out.println(SQL);
            ps = conn.prepareStatement(SQL);
            rs = ps.executeQuery();
            ZyzdBaseUniversityYbeb bean = null;
            while (rs.next()) {
                bean = new ZyzdBaseUniversityYbeb();
                bean.setYxmc_org(rs.getString("yxmc_org"));
                bean.setZymc_org(rs.getString("zymc_org"));
                bean.setSf(rs.getString("sf"));
                bean.setYbeb(rs.getString("ybeb"));
                String yerb = bean.getSf() + "__" + bean.getYxmc_org() +"__" + bean.getZymc_org();
                schoolMap.put(yerb, bean.getYbeb());
            }
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return schoolMap;
	}
	
	
	//临时用下
	public HashMap<String, ZyzdUniversityDataBean> getZDKSConvertResultCondition_byGivingYxmc(int year, String sfCode, String xkCode, HashSet<String> univNameIn) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, ZyzdUniversityDataBean> MAP = new HashMap<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_" + year + "x x WHERE x.xk_code like ? and x.yxmc in ("+Tools.getSQLQueryin(univNameIn)+")";
			
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, "%" + xkCode + "%");

			rs = ps.executeQuery();
			ZyzdUniversityDataBean sb = null; 
			while (rs.next()) {
				sb = new ZyzdUniversityDataBean();
				sb.setYxmc(rs.getString("yxmc"));
				sb.setYxdm(rs.getString("yxdm"));
				sb.setYxbz(rs.getString("yxbz"));
				sb.setYxmc_org(rs.getString("yxmc_org"));
				sb.setNf(rs.getInt("nf"));
				sb.setXk(rs.getString("xk"));
				sb.setXk_code(rs.getString("xk_code"));
				sb.setPc(rs.getString("pc"));
				sb.setZyz(rs.getString("zyz"));
				sb.setZx(rs.getString("zx"));
				sb.setZdf(rs.getString("zdf"));
				sb.setZdfwc(rs.getString("zdfwc"));
				sb.setYxsf(rs.getString("yxsf"));
				sb.setYxcs(rs.getString("yxcs"));
				sb.setInd_nature(rs.getString("ind_nature"));
				sb.setInd_catg(rs.getString("ind_catg"));
				sb.setCnt_grad(rs.getFloat("cnt_grad"));
				sb.setCnt_company(rs.getInt("cnt_company"));
				sb.setCnt_employ(rs.getInt("cnt_employ"));
				MAP.put(sb.getYxmc(),sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return MAP;
	}
	
	public HashMap<String, ZyzdUniversityDataBean> getResultCondition_byGivingYxmc(int year, String sfCode, String xkCode, String lqpc, HashSet<String> univNameIn) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, ZyzdUniversityDataBean> MAP = new HashMap<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT x.nf,x.pc,  x.yxmc,x.yxmc_org, x.zyz, x.xk, x.zx, min(x.zdf) as zdf, max(x.zdfwc) as zdfwc FROM "+sfCode+"_" + year + "x x WHERE x.xk_code like ? and x.yxmc_org in ("+Tools.getSQLQueryin(univNameIn)+") and x.lqpc like ? group by x.nf,x.pc, x.yxmc,x.yxmc_org, x.zyz, x.xk, x.zx";
			String ORDER_CONDITION = " ORDER BY min(x.zdf) DESC";
			Tools.println(SELECT_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, "%" + xkCode + "%");
			ps.setString(2, "%" + lqpc + "%");

			rs = ps.executeQuery();
			ZyzdUniversityDataBean sb = null; 
			while (rs.next()) {
				sb = new ZyzdUniversityDataBean();
				sb.setYxmc(rs.getString("yxmc"));
				sb.setYxmc_org(rs.getString("yxmc_org"));
				sb.setNf(rs.getInt("nf"));
				sb.setZyz(rs.getString("zyz"));
				sb.setZdf(rs.getString("zdf"));
				sb.setZdfwc(rs.getString("zdfwc"));
				sb.setXk(rs.getString("xk"));
				sb.setZx(rs.getString("zx"));
				sb.setPc(rs.getString("pc"));
				MAP.put(sb.getYxmc()+sb.getPc()+sb.getZyz(), sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return MAP;
	}
	
	
	public List<ZyzdUniversityDataBean> getZDKSConvertResultCondition_inYXSearchPage(int year, String sfCode, String xkCode, String univName, HashSet<String> univNameSet, HashSet<String> univCatg, HashSet<String> univSf, String univNature, String lqpc, int orderPriority, int scoreFrom, int scoreTo, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdUniversityDataBean> list = new ArrayList<ZyzdUniversityDataBean>();
		try {
			
			conn = DatabaseUtils.getConnection();
			String andSQL = univNameSet.size() == 0 ? "" : "and yxmc_org in (" + Tools.getSQLQueryin(univNameSet) + ")";
			String andYXLXSQL = univCatg.size() == 0 ? "" : "and ind_catg in (" + Tools.getSQLQueryin(univCatg) + ")";
			String andYXSFSQL = univSf.size() == 0 ? "" : "and yxsf in (" + Tools.getSQLQueryin(univSf) + ")";
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_" + year + "x x WHERE (x.ind_nature like ?) "+andYXLXSQL+" "+andYXSFSQL+" and x.xk_code like ? and (x.yxmc like ? " + andSQL + ") and x.lqpc like ? and x.zdf between ? and ? ";
			String ORDER_CONDITION = " ORDER BY x.zdf DESC";
			if(orderPriority == 2) {
				ORDER_CONDITION = " ORDER BY x.cnt_employ DESC";
			}else if(orderPriority == 3) {
				ORDER_CONDITION = " ORDER BY x.cnt_grad DESC";
			}else {
				
			}
			String LIMIT_CONDITION = " limit ?,?";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION + LIMIT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION + LIMIT_CONDITION);
			ps.setString(1, "%" + univNature + "%");
			ps.setString(2, "%" + xkCode + "%");
			ps.setString(3, "%" + univName + "%");
			ps.setString(4, "%" + lqpc + "%");
			ps.setInt(5, scoreFrom);
			ps.setInt(6, scoreTo);

			ps.setInt(7, (pageNumber - 1) * PAGE_ROW_CNT); // 1开始
			ps.setInt(8, PAGE_ROW_CNT);

			rs = ps.executeQuery();
			ZyzdUniversityDataBean sb = null; 
			while (rs.next()) {
				sb = new ZyzdUniversityDataBean();
				sb.setYxmc(rs.getString("yxmc"));
				sb.setYxdm(rs.getString("yxdm"));
				sb.setYxbz(rs.getString("yxbz"));
				sb.setYxmc_org(rs.getString("yxmc_org"));
				sb.setNf(rs.getInt("nf"));
				sb.setXk(rs.getString("xk"));
				sb.setXk_code(rs.getString("xk_code"));
				sb.setPc(rs.getString("pc"));
				sb.setZyz(rs.getString("zyz"));
				sb.setZx(rs.getString("zx"));
				sb.setZdf(rs.getString("zdf"));
				sb.setZdfwc(rs.getString("zdfwc"));
				sb.setInd_nature(rs.getString("ind_nature"));
				sb.setInd_catg(rs.getString("ind_catg"));
				sb.setCnt_grad(rs.getFloat("cnt_grad"));
				sb.setCnt_company(rs.getInt("cnt_company"));
				sb.setCnt_employ(rs.getInt("cnt_employ"));
				list.add(sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdUniversityDataBean> getResultCondition_inFormSearchPage(int year, String sfCode, String xkCode, String univName, HashSet<String> univNameSet, HashSet<String> univCatg, HashSet<String> univSf, String univNature, String lqpc, String tag, int orderPriority, int scoreFrom, int scoreTo, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdUniversityDataBean> list = new ArrayList<ZyzdUniversityDataBean>();
		try {
			
			conn = DatabaseUtils.getConnection();
			String andSQL = univNameSet.size() == 0 ? "" : "and yxmc_org in (" + Tools.getSQLQueryin(univNameSet) + ")";
			String andYXLXSQL = univCatg.size() == 0 ? "" : "and ind_catg in (" + Tools.getSQLQueryin(univCatg) + ")";
			String andYXSFSQL = univSf.size() == 0 ? "" : "and yxsf in (" + Tools.getSQLQueryin(univSf) + ")";
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_" + year + "x x WHERE (x.ind_nature like ?) "+andYXLXSQL+" "+andYXSFSQL+" and x.xk_code like ? and (x.yxmc like ? " + andSQL + ") and x.lqpc like ? and x.yx_tags like ? and x.zdf between ? and ? ";
			String ORDER_CONDITION = " ORDER BY x.zdf DESC";
			if(orderPriority == 2) {
				ORDER_CONDITION = " ORDER BY x.cnt_employ DESC";
			}else if(orderPriority == 3) {
				ORDER_CONDITION = " ORDER BY x.cnt_grad DESC";
			}else {
				
			}
			String LIMIT_CONDITION = " limit ?,?";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION + LIMIT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION + LIMIT_CONDITION);
			ps.setString(1, "%" + univNature + "%");
			ps.setString(2, "%" + xkCode + "%");
			ps.setString(3, "%" + univName + "%");
			ps.setString(4, "%" + lqpc + "%");
			ps.setString(5, "%" + tag + "%");
			ps.setInt(6, scoreFrom);
			ps.setInt(7, scoreTo);

			ps.setInt(8, (pageNumber - 1) * PAGE_ROW_CNT); // 1开始
			ps.setInt(9, PAGE_ROW_CNT);

			rs = ps.executeQuery();
			ZyzdUniversityDataBean sb = null; 
			while (rs.next()) {
				sb = new ZyzdUniversityDataBean();
				sb.setYxmc(rs.getString("yxmc"));
				sb.setYxdm(rs.getString("yxdm"));
				sb.setYxbz(rs.getString("yxbz"));
				sb.setYxmc_org(rs.getString("yxmc_org"));
				sb.setNf(rs.getInt("nf"));
				sb.setXk(rs.getString("xk"));
				sb.setXk_code(rs.getString("xk_code"));
				sb.setPc(rs.getString("pc"));
				sb.setZyz(rs.getString("zyz"));
				sb.setZx(rs.getString("zx"));
				sb.setZdf(rs.getString("zdf"));
				sb.setZdfwc(rs.getString("zdfwc"));
				sb.setInd_nature(rs.getString("ind_nature"));
				sb.setInd_catg(rs.getString("ind_catg"));
				sb.setCnt_grad(rs.getFloat("cnt_grad"));
				sb.setCnt_company(rs.getInt("cnt_company"));
				sb.setCnt_employ(rs.getInt("cnt_employ"));
				list.add(sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public List<ZyzdUniversityDataBean> getYxByGivingYxmc(int year, String sfCode, String xkCode, HashSet<String> yxmcOrgSet) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdUniversityDataBean> list = new ArrayList<ZyzdUniversityDataBean>();
		try {
			conn = DatabaseUtils.getConnection();
			String andSQL = yxmcOrgSet.size() == 0 ? "" : "and yxmc_org in (" + Tools.getSQLQueryin(yxmcOrgSet) + ")";
			String SELECT_CONDITION = "SELECT yxmc_org, max(zdf) as zdf, min(zdfwc) as zdfwc FROM "+sfCode+"_" + year + "x x WHERE x.xk_code like ? " + andSQL +" GROUP BY yxmc_org ";
			String ORDER_CONDITION = " order by max(zdf) desc ";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, "%" + xkCode + "%");
			
			rs = ps.executeQuery();
			ZyzdUniversityDataBean sb = null; 
			while (rs.next()) {
				sb = new ZyzdUniversityDataBean();
				sb.setYxmc_org(rs.getString("yxmc_org"));
				sb.setZdf(rs.getString("zdf"));
				sb.setZdfwc(rs.getString("zdfwc"));
				list.add(sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdUniversityDataBean> getFllowedYx(int year, String sfCode, String xkCode, String cid) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdUniversityDataBean> list = new ArrayList<ZyzdUniversityDataBean>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "select x.yxmc_org, max(y.zdf) as zdf, min(y.zdfwc) as zdfwc from "+sfCode+"_form_user_follow x left join "+sfCode+"_"+year+"x y on x.yxmc_org = y.yxmc_org where y.xk_code like ? and x.c_id = ? and y.lqpc = '普通类' group by x.yxmc_org ";
			String ORDER_CONDITION = " order by max(zdf) desc ";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, "%" + xkCode + "%");
			ps.setString(2, cid);
			
			rs = ps.executeQuery();
			ZyzdUniversityDataBean sb = null; 
			while (rs.next()) {
				sb = new ZyzdUniversityDataBean();
				sb.setYxmc_org(rs.getString("yxmc_org"));
				sb.setZdf(rs.getString("zdf"));
				sb.setZdfwc(rs.getString("zdfwc"));
				list.add(sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdUniversityDataBean> getYxByGivingYxmc(int year, String sfCode, String xkCode, String givingYxmc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdUniversityDataBean> list = new ArrayList<ZyzdUniversityDataBean>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT yxdm, yxmc,zyz, zdf, zdfwc FROM "+sfCode+"_" + year + "x x WHERE x.xk_code like ? and yxmc like ? ";
			String ORDER_CONDITION = " order by zdf desc ";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, "%" + xkCode + "%");
			ps.setString(2, "%" + givingYxmc + "%");
			rs = ps.executeQuery();
			ZyzdUniversityDataBean sb = null; 
			while (rs.next()) {
				sb = new ZyzdUniversityDataBean();
				sb.setYxdm(rs.getString("yxdm"));
				sb.setYxmc(rs.getString("yxmc"));
				sb.setZyz(rs.getString("zyz"));
				sb.setZdf(rs.getString("zdf"));
				sb.setZdfwc(rs.getString("zdfwc"));
				list.add(sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdHezuoInBean> getHezuobanxueIn(String givingYxmc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdHezuoInBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM zyzd_base_hezuo_in x WHERE x.yxmc_zf_org = ? ";
			String ORDER_CONDITION = " order by yxmc_wf_org ASC ";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, givingYxmc);
			rs = ps.executeQuery();
			ZyzdHezuoInBean sb = null; 
			while (rs.next()) {
				sb = new ZyzdHezuoInBean();
				sb.setYxmc_zf_org(rs.getString("yxmc_zf_org"));
				sb.setYxmc_wf_org(rs.getString("yxmc_wf_org"));
				sb.setBxcc(rs.getString("bxcc"));
				sb.setBfzs_zf(rs.getString("bfzs_zf"));
				sb.setBfzs_wf(rs.getString("bfzs_wf"));
				sb.setBxgm(rs.getString("bxgm"));
				sb.setSbj_addr(rs.getString("sbj_addr"));
				sb.setSbj_name(rs.getString("sbj_name"));
				sb.setUrl(rs.getString("url"));
				sb.setYxq(rs.getString("yxq"));
				sb.setZsbh(rs.getString("zsbh"));
				sb.setZsfs(rs.getString("zsfs"));
				sb.setZsnf(rs.getString("zsnf"));
				sb.setZymc(rs.getString("zymc"));
				list.add(sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdUniversityDataBean> getZDKSConvertResultCondition(int year, String sfCode, String xkCode, String univName, String univCatg, String univSf, String univNature, String lqpc, int orderPriority, int scoreFrom, int scoreTo, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdUniversityDataBean> list = new ArrayList<ZyzdUniversityDataBean>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_" + year + "x x WHERE (x.ind_nature like ?) and (x.ind_catg like ? or x.ind_catg is null) and (x.yxsf like ? or x.yxsf is null) and x.xk_code like ? and x.yxmc like ? and x.lqpc like ? and x.zdf between ? and ? ";
			String ORDER_CONDITION = " ORDER BY x.zdf DESC";
			if(orderPriority == 2) {
				ORDER_CONDITION = " ORDER BY x.cnt_employ DESC";
			}else if(orderPriority == 3) {
				ORDER_CONDITION = " ORDER BY x.cnt_grad DESC";
			}else {
				
			}
			String LIMIT_CONDITION = " limit ?,?";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION + LIMIT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION + LIMIT_CONDITION);
			ps.setString(1, "%" + univNature + "%");
			ps.setString(2, "%" + univCatg + "%");
			ps.setString(3, "%" + univSf + "%");
			ps.setString(4, "%" + xkCode + "%");
			ps.setString(5, "%" + univName + "%");
			ps.setString(6, "%" + lqpc + "%");
			ps.setInt(7, scoreFrom);
			ps.setInt(8, scoreTo);

			ps.setInt(9, (pageNumber - 1) * PAGE_ROW_CNT); // 1开始
			ps.setInt(10, PAGE_ROW_CNT);

			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(ps);
			
			ZyzdUniversityDataBean sb = null; 
			while (rs.next()) {
				sb = new ZyzdUniversityDataBean();
				sb.setYxmc(rs.getString("yxmc"));
				sb.setYxdm(rs.getString("yxdm"));
				sb.setYxbz(rs.getString("yxbz"));
				sb.setYxmc_org(rs.getString("yxmc_org"));
				sb.setNf(rs.getInt("nf"));
				sb.setXk(rs.getString("xk"));
				sb.setXk_code(rs.getString("xk_code"));
				sb.setPc(rs.getString("pc"));
				sb.setZyz(rs.getString("zyz"));
				sb.setZx(rs.getString("zx"));
				sb.setZdf(rs.getString("zdf"));
				sb.setZdfwc(rs.getString("zdfwc"));
				sb.setInd_nature(rs.getString("ind_nature"));
				sb.setInd_catg(rs.getString("ind_catg"));
				sb.setCnt_grad(rs.getFloat("cnt_grad"));
				sb.setCnt_company(rs.getInt("cnt_company"));
				sb.setCnt_employ(rs.getInt("cnt_employ"));
				list.add(sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdUniversityDataBean> getZDKSConvertResultConditionWithoutPager(int year, String sfCode, String xkCode, String univName, String univCatg, String univSf, String univNature, String lqpc, int orderPriority, int scoreFrom, int scoreTo) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdUniversityDataBean> list = new ArrayList<ZyzdUniversityDataBean>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_" + year + "x x WHERE (x.ind_nature like ?) and (x.ind_catg like ? or x.ind_catg is null) and (x.yxsf like ? or x.yxsf is null) and x.xk_code like ? and x.yxmc like ? and x.lqpc like ? and x.zdf between ? and ? ";
			String ORDER_CONDITION = " ORDER BY x.zdf DESC";
			if(orderPriority == 2) {
				ORDER_CONDITION = " ORDER BY x.cnt_employ DESC";
			}else if(orderPriority == 3) {
				ORDER_CONDITION = " ORDER BY x.cnt_grad DESC";
			}else {
				
			}
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION );
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION );
			ps.setString(1, "%" + univNature + "%");
			ps.setString(2, "%" + univCatg + "%");
			ps.setString(3, "%" + univSf + "%");
			ps.setString(4, "%" + xkCode + "%");
			ps.setString(5, "%" + univName + "%");
			ps.setString(6, "%" + lqpc + "%");
			ps.setInt(7, scoreFrom);
			ps.setInt(8, scoreTo);

			rs = ps.executeQuery();
			ZyzdUniversityDataBean sb = null; 
			while (rs.next()) {
				sb = new ZyzdUniversityDataBean();
				sb.setYxmc(rs.getString("yxmc"));
				sb.setYxdm(rs.getString("yxdm"));
				sb.setYxbz(rs.getString("yxbz"));
				sb.setYxmc_org(rs.getString("yxmc_org"));
				sb.setNf(rs.getInt("nf"));
				sb.setXk(rs.getString("xk"));
				sb.setXk_code(rs.getString("xk_code"));
				sb.setPc(rs.getString("pc"));
				sb.setZyz(rs.getString("zyz"));
				sb.setZx(rs.getString("zx"));
				sb.setZdf(rs.getString("zdf"));
				sb.setZdfwc(rs.getString("zdfwc"));
				sb.setInd_nature(rs.getString("ind_nature"));
				sb.setInd_catg(rs.getString("ind_catg"));
				sb.setCnt_grad(rs.getFloat("cnt_grad"));
				sb.setCnt_company(rs.getInt("cnt_company"));
				sb.setCnt_employ(rs.getInt("cnt_employ"));
				list.add(sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdUniversityDataBean> getYxZyzForForm(int year, String sfCode, String xkCode, String yxmcOrg, String lqpc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdUniversityDataBean> list = new ArrayList<ZyzdUniversityDataBean>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT x.nf, x.pc, x.yxmc,x.yxmc_org, x.zyz, x.xk, x.zx, min(x.zdf) as zdf, max(x.zdfwc) as zdfwc FROM "+sfCode+"_" + year + "x x WHERE x.xk_code like ? and x.yxmc_org = ? and x.lqpc like ? group by x.nf,x.pc,x.yxmc,x.yxmc_org, x.zyz, x.xk, x.zx";
			String ORDER_CONDITION = " ORDER BY min(x.zdf) DESC";
			Tools.println(SELECT_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, "%" + xkCode + "%");
			ps.setString(2, yxmcOrg);
			ps.setString(3, "%" + lqpc + "%");

			rs = ps.executeQuery();
			ZyzdUniversityDataBean sb = null; 
			while (rs.next()) {
				sb = new ZyzdUniversityDataBean();
				sb.setYxmc(rs.getString("yxmc"));
				sb.setYxmc_org(rs.getString("yxmc_org"));
				sb.setNf(rs.getInt("nf"));
				sb.setZyz(rs.getString("zyz"));
				sb.setZdf(rs.getString("zdf"));
				sb.setZdfwc(rs.getString("zdfwc"));
				sb.setXk(rs.getString("xk"));
				sb.setZx(rs.getString("zx"));
				sb.setPc(rs.getString("pc"));
				list.add(sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdUniversityDataBean> getZDKSConvertResultCondition_withoutCondition(int year, String sfCode, String xkCode, String univName) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdUniversityDataBean> list = new ArrayList<ZyzdUniversityDataBean>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_" + year + "x x WHERE x.xk_code like ? and x.yxmc like ?";
			String ORDER_CONDITION = " ORDER BY x.zdf DESC";

			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, "%" + xkCode + "%");
			ps.setString(2, "%" + univName + "%");

			rs = ps.executeQuery();
			ZyzdUniversityDataBean sb = null; 
			while (rs.next()) {
				sb = new ZyzdUniversityDataBean();
				sb.setYxmc(rs.getString("yxmc"));
				sb.setYxdm(rs.getString("yxdm"));
				sb.setYxbz(rs.getString("yxbz"));
				sb.setYxmc_org(rs.getString("yxmc_org"));
				sb.setNf(rs.getInt("nf"));
				sb.setXk(rs.getString("xk"));
				sb.setXk_code(rs.getString("xk_code"));
				sb.setPc(rs.getString("pc"));
				sb.setZyz(rs.getString("zyz"));
				sb.setZx(rs.getString("zx"));
				sb.setZdf(rs.getString("zdf"));
				sb.setZdfwc(rs.getString("zdfwc"));
				sb.setInd_nature(rs.getString("ind_nature"));
				sb.setInd_catg(rs.getString("ind_catg"));
				sb.setCnt_grad(rs.getFloat("cnt_grad"));
				sb.setCnt_company(rs.getInt("cnt_company"));
				sb.setCnt_employ(rs.getInt("cnt_employ"));
				list.add(sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	//临时用下
	public HashMap<String, SchoolMarjorCommonBean> getZySearchResultCondition_byGivingYxmc(int year, String sfCode, String xkCode, HashSet<String> univNameIn, HashSet<String> zyNameIn) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, SchoolMarjorCommonBean> MAP = new HashMap<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_" + year + " x WHERE x.xk_code like ? and x.yxmc in ("+Tools.getSQLQueryin(univNameIn)+") and x.zymc in ("+Tools.getSQLQueryin(zyNameIn)+")";
			
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, "%" + xkCode + "%");

			rs = ps.executeQuery();
			SchoolMarjorCommonBean bean = null;
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setNf(rs.getInt("nf"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setZdf(rs.getString("zdf"));
				bean.setZydm(rs.getString("zydm"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZymc_org(rs.getString("zymc_org"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setPjf(rs.getString("pjf"));
				bean.setPjfwc(rs.getString("pjfwc"));
				bean.setZgf(rs.getString("zgf"));
				bean.setZybz(rs.getString("zybz"));
				bean.setPc(rs.getString("pc"));
				bean.setXk(rs.getString("xk"));

				
				MAP.put(bean.getYxmc() + bean.getZymc(), bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return MAP;
	}
	
	public List<SchoolMarjorCommonBean> getZySearchResultCondition(int year, String sfCode, String xkCode, HashSet<String> zymc, HashSet<String> univSf, String univNature, int scoreFrom, int scoreTo, int orderPriority, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolMarjorCommonBean> list = new ArrayList<>();
		try {
			String zymcInSQL = zymc.size() == 0 ? "" : "and x.zymc_org in ("+Tools.getSQLQueryin(zymc)+")";
			String yxsfInSQl = univSf.size() == 0 ? "" : "and x.yxsf in ("+Tools.getSQLQueryin(univSf)+")";
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_" + year + " x WHERE (x.ind_nature like ?) "+yxsfInSQl+" and x.xk_code like ? "+ zymcInSQL + " and x.zdf between ? and ? ";
			String ORDER_CONDITION = " ORDER BY x.zdf DESC";
			if(orderPriority == 2) {
				ORDER_CONDITION = " ORDER BY x.cnt_employ DESC";
			}else if(orderPriority == 3) {
				ORDER_CONDITION = " ORDER BY x.cnt_grad DESC";
			}else {
				
			}
			String LIMIT_CONDITION = " limit ?,?";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION + LIMIT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION + LIMIT_CONDITION);
			ps.setString(1, "%" + univNature + "%");
			ps.setString(2, "%" + xkCode + "%");
			ps.setInt(3, scoreFrom);
			ps.setInt(4, scoreTo);

			ps.setInt(5, (pageNumber - 1) * PAGE_ROW_CNT); // 1开始
			ps.setInt(6, PAGE_ROW_CNT);

			rs = ps.executeQuery();
			SchoolMarjorCommonBean bean = null;
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setNf(rs.getInt("nf"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setZdf(rs.getString("zdf"));
				bean.setZydm(rs.getString("zydm"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZymc_org(rs.getString("zymc_org"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setPjf(rs.getString("pjf"));
				bean.setPjfwc(rs.getString("pjfwc"));
				bean.setZgf(rs.getString("zgf"));
				bean.setZybz(rs.getString("zybz"));
				bean.setPc(rs.getString("pc"));
				bean.setXk(rs.getString("xk"));
				bean.setInd_nature(rs.getString("ind_nature"));
				bean.setInd_catg(rs.getString("ind_catg"));
				bean.setCnt_grad(rs.getFloat("cnt_grad"));
				bean.setCnt_company(rs.getInt("cnt_company"));
				bean.setCnt_employ(rs.getInt("cnt_employ"));
				
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<SchoolMarjorCommonBean> getZyBySelectedForm_YxmcAndZyz(int year, String sfCode, String xkCode, String yxmc, String zyz, String pc_code) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolMarjorCommonBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String zyzSQL = Tools.isEmpty(zyz)? "" : "and zyz = ?";
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_" + year + " x WHERE x.lqpc = '普通类' and xk_code like ? and x.yxmc = ? and pc_code = ? " + zyzSQL;
			String ORDER_CONDITION = " ORDER BY x.zdf DESC";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, "%"+xkCode+"%");
			ps.setString(2, yxmc);
			ps.setString(3, pc_code);
			if(!Tools.isEmpty(zyzSQL)){
				ps.setString(4, zyz);
			}
			rs = ps.executeQuery();
			SchoolMarjorCommonBean bean = null;
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setNf(rs.getInt("nf"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setZdf(rs.getString("zdf"));
				bean.setZydm(rs.getString("zydm"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZymc_org(rs.getString("zymc_org"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setPjf(rs.getString("pjf"));
				bean.setPjfwc(rs.getString("pjfwc"));
				bean.setZgf(rs.getString("zgf"));
				bean.setZybz(rs.getString("zybz"));
				bean.setZyml(rs.getString("zyml"));
				bean.setPc(rs.getString("pc"));
				bean.setPc_code(rs.getString("pc_code"));
				bean.setXk(rs.getString("xk"));
				bean.setZyz(rs.getString("zyz"));
				bean.setInd_nature(rs.getString("ind_nature"));
				bean.setInd_catg(rs.getString("ind_catg"));
				bean.setCnt_grad(rs.getFloat("cnt_grad"));
				bean.setCnt_company(rs.getInt("cnt_company"));
				bean.setCnt_employ(rs.getInt("cnt_employ"));
				
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public List<SchoolMarjorCommonBean> getZyBySelectedForm_zyml(int year, String sfCode, String xkCode, HashSet<String> zymlSets, String pc_code, int zdf_from, int zdf_to) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolMarjorCommonBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String zymlSQL = zymlSets.size() == 0 ? "" : "and x.zyml in ("+Tools.getSQLQueryin(zymlSets)+")";
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_" + year + " x WHERE x.lqpc = '普通类' and xk_code like ? and (zdf between ? and ?) and pc_code = ? " + zymlSQL;
			String ORDER_CONDITION = " ORDER BY x.zdf DESC";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION+","+pc_code+","+zdf_from+","+zdf_to);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, "%"+xkCode+"%");
			ps.setInt(2, zdf_from);
			ps.setInt(3, zdf_to);
			ps.setString(4, pc_code);
			rs = ps.executeQuery();
			SchoolMarjorCommonBean bean = null;
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setNf(rs.getInt("nf"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setZdf(rs.getString("zdf"));
				bean.setZydm(rs.getString("zydm"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZymc_org(rs.getString("zymc_org"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setPjf(rs.getString("pjf"));
				bean.setPjfwc(rs.getString("pjfwc"));
				bean.setZgf(rs.getString("zgf"));
				bean.setZybz(rs.getString("zybz"));
				bean.setZyml(rs.getString("zyml"));
				bean.setPc(rs.getString("pc"));
				bean.setPc_code(rs.getString("pc_code"));
				bean.setXk(rs.getString("xk"));
				bean.setZyz(rs.getString("zyz"));
				bean.setInd_nature(rs.getString("ind_nature"));
				bean.setInd_catg(rs.getString("ind_catg"));
				bean.setCnt_grad(rs.getFloat("cnt_grad"));
				bean.setCnt_company(rs.getInt("cnt_company"));
				bean.setCnt_employ(rs.getInt("cnt_employ"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<SchoolMarjorCommonBean> getZyBySelectedForm(int year, String sfCode, String xkCode, String pc_code, int zdf_from, int zdf_to) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolMarjorCommonBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String zymlSQL = "";
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_" + year + " x WHERE x.lqpc = '普通类' and xk_code like ? and (zdf between ? and ?) and pc_code = ? " + zymlSQL;
			String ORDER_CONDITION = " ORDER BY x.zdf DESC";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION+","+pc_code+","+zdf_from+","+zdf_to);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, "%"+xkCode+"%");
			ps.setInt(2, zdf_from);
			ps.setInt(3, zdf_to);
			ps.setString(4, pc_code);
			rs = ps.executeQuery();
			SchoolMarjorCommonBean bean = null;
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setNf(rs.getInt("nf"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setZdf(rs.getString("zdf"));
				bean.setZydm(rs.getString("zydm"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZymc_org(rs.getString("zymc_org"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setPjf(rs.getString("pjf"));
				bean.setPjfwc(rs.getString("pjfwc"));
				bean.setZgf(rs.getString("zgf"));
				bean.setZybz(rs.getString("zybz"));
				bean.setZyml(rs.getString("zyml"));
				bean.setPc(rs.getString("pc"));
				bean.setPc_code(rs.getString("pc_code"));
				bean.setXk(rs.getString("xk"));
				bean.setZyz(rs.getString("zyz"));
				bean.setInd_nature(rs.getString("ind_nature"));
				bean.setInd_catg(rs.getString("ind_catg"));
				bean.setCnt_grad(rs.getFloat("cnt_grad"));
				bean.setCnt_company(rs.getInt("cnt_company"));
				bean.setCnt_employ(rs.getInt("cnt_employ"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<SchoolMarjorCommonBean> getZyBySelectedForm_zyml_notin(int year, String sfCode, String xkCode, HashSet<String> zymlSets, String pc_code, int zdf_from, int zdf_to) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolMarjorCommonBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String zymlSQL = zymlSets.size() == 0 ? "" : "and x.zyml not in ("+Tools.getSQLQueryin(zymlSets)+")";
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_" + year + " x WHERE x.lqpc = '普通类' and xk_code like ? and (zdf between ? and ?) and pc_code = ? " + zymlSQL;
			String ORDER_CONDITION = " ORDER BY x.zdf DESC";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION+","+pc_code+","+zdf_from+","+zdf_to);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, "%"+xkCode+"%");
			ps.setInt(2, zdf_from);
			ps.setInt(3, zdf_to);
			ps.setString(4, pc_code);
			rs = ps.executeQuery();
			SchoolMarjorCommonBean bean = null;
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setNf(rs.getInt("nf"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setZdf(rs.getString("zdf"));
				bean.setZydm(rs.getString("zydm"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZymc_org(rs.getString("zymc_org"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setPjf(rs.getString("pjf"));
				bean.setPjfwc(rs.getString("pjfwc"));
				bean.setZgf(rs.getString("zgf"));
				bean.setZybz(rs.getString("zybz"));
				bean.setZyml(rs.getString("zyml"));
				bean.setPc(rs.getString("pc"));
				bean.setPc_code(rs.getString("pc_code"));
				bean.setXk(rs.getString("xk"));
				bean.setZyz(rs.getString("zyz"));
				bean.setInd_nature(rs.getString("ind_nature"));
				bean.setInd_catg(rs.getString("ind_catg"));
				bean.setCnt_grad(rs.getFloat("cnt_grad"));
				bean.setCnt_company(rs.getInt("cnt_company"));
				bean.setCnt_employ(rs.getInt("cnt_employ"));
				
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<SchoolMarjorCommonBean> getZySearchResultConditionZyml(int year, String sfCode, String xkCode, HashSet<String> zyml, HashSet<String> univSf, String univNature, int scoreFrom, int scoreTo, int orderPriority, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolMarjorCommonBean> list = new ArrayList<>();
		try {
			String zymlInSQL = zyml.size() == 0 ? "" : "and x.zyml in ("+Tools.getSQLQueryin(zyml)+")";
			String yxsfInSQl = univSf.size() == 0 ? "" : "and x.yxsf in ("+Tools.getSQLQueryin(univSf)+")";
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_" + year + " x WHERE x.lqpc = '普通类' and (x.ind_nature like ?) "+yxsfInSQl+" and x.xk_code like ? "+ zymlInSQL + " and x.zdf between ? and ? ";
			String ORDER_CONDITION = " ORDER BY x.zdf DESC";
			if(orderPriority == 2) {
				ORDER_CONDITION = " ORDER BY x.cnt_employ DESC";
			}else if(orderPriority == 3) {
				ORDER_CONDITION = " ORDER BY x.cnt_grad DESC";
			}else {
				
			}
			String LIMIT_CONDITION = " limit ?,?";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION + LIMIT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION + LIMIT_CONDITION);
			ps.setString(1, "%" + univNature + "%");
			ps.setString(2, "%" + xkCode + "%");
			ps.setInt(3, scoreFrom);
			ps.setInt(4, scoreTo);

			ps.setInt(5, (pageNumber - 1) * PAGE_ROW_CNT); // 1开始
			ps.setInt(6, PAGE_ROW_CNT);

			rs = ps.executeQuery();
			SchoolMarjorCommonBean bean = null;
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setNf(rs.getInt("nf"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setZdf(rs.getString("zdf"));
				bean.setZydm(rs.getString("zydm"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZymc_org(rs.getString("zymc_org"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setPjf(rs.getString("pjf"));
				bean.setPjfwc(rs.getString("pjfwc"));
				bean.setZgf(rs.getString("zgf"));
				bean.setZybz(rs.getString("zybz"));
				bean.setZyml(rs.getString("zyml"));
				bean.setPc(rs.getString("pc"));
				bean.setXk(rs.getString("xk"));
				bean.setZyz(rs.getString("zyz"));
				bean.setInd_nature(rs.getString("ind_nature"));
				bean.setInd_catg(rs.getString("ind_catg"));
				bean.setCnt_grad(rs.getFloat("cnt_grad"));
				bean.setCnt_company(rs.getInt("cnt_company"));
				bean.setCnt_employ(rs.getInt("cnt_employ"));
				
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<SchoolMarjorCommonBean> getZySearchResultConditionZymlForForm(int year, String sfCode, String xkCode, HashSet<String> zyml, HashSet<String> univSf, String univNature,String key_words, String tag, int scoreFrom, int scoreTo, int orderPriority, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolMarjorCommonBean> list = new ArrayList<>();
		try {
			String zymlInSQL = zyml.size() == 0 ? "" : "and x.zyml in ("+Tools.getSQLQueryin(zyml)+")";
			String yxsfInSQl = univSf.size() == 0 ? "" : "and x.yxsf in ("+Tools.getSQLQueryin(univSf)+")";
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_" + year + " x WHERE x.lqpc = '普通类' and (x.ind_nature like ?) "+yxsfInSQl+" and x.xk_code like ? "+ zymlInSQL + " and (x.yx_tags like ?) and (x.yxmc like ? or x.zymc like ?) and x.zdf between ? and ? ";
			String ORDER_CONDITION = " ORDER BY x.zdf DESC";
			if(orderPriority == 2) {
				ORDER_CONDITION = " ORDER BY x.cnt_employ DESC";
			}else if(orderPriority == 3) {
				ORDER_CONDITION = " ORDER BY x.cnt_grad DESC";
			}else {
				
			}
			String LIMIT_CONDITION = " limit ?,?";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION + LIMIT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION + LIMIT_CONDITION);
			ps.setString(1, "%" + univNature + "%");
			ps.setString(2, "%" + xkCode + "%");
			ps.setString(3, "%" + tag + "%");
			ps.setString(4, "%" + key_words + "%");
			ps.setString(5, "%" + key_words + "%");
			ps.setInt(6, scoreFrom);
			ps.setInt(7, scoreTo);
			ps.setInt(8, (pageNumber - 1) * PAGE_ROW_CNT); // 1开始
			ps.setInt(9, PAGE_ROW_CNT);

			rs = ps.executeQuery();
			SchoolMarjorCommonBean bean = null;
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setNf(rs.getInt("nf"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setZdf(rs.getString("zdf"));
				bean.setZydm(rs.getString("zydm"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZymc_org(rs.getString("zymc_org"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setPjf(rs.getString("pjf"));
				bean.setPjfwc(rs.getString("pjfwc"));
				bean.setZgf(rs.getString("zgf"));
				bean.setZybz(rs.getString("zybz"));
				bean.setZyml(rs.getString("zyml"));
				bean.setPc(rs.getString("pc"));
				bean.setXk(rs.getString("xk"));
				bean.setZyz(rs.getString("zyz"));
				bean.setInd_nature(rs.getString("ind_nature"));
				bean.setInd_catg(rs.getString("ind_catg"));
				bean.setCnt_grad(rs.getFloat("cnt_grad"));
				bean.setCnt_company(rs.getInt("cnt_company"));
				bean.setCnt_employ(rs.getInt("cnt_employ"));
				
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public int getZySearchResultCondition_SUM(int year, String sfCode, String xkCode, HashSet<String> zymc, HashSet<String> univSf, String univNature, int scoreFrom, int scoreTo) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			String zymcInSQL = zymc.size() == 0 ? "" : "and x.zymc_org in ("+Tools.getSQLQueryin(zymc)+")";
			String yxsfInSQl = univSf.size() == 0 ? "" : "and x.yxsf in ("+Tools.getSQLQueryin(univSf)+")";
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT COUNT(*) AS CNT FROM "+sfCode+"_" + year + " x WHERE (x.ind_nature like ?) "+yxsfInSQl+" and x.xk_code like ? "+ zymcInSQL +" and x.zdf between ? and ? ";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, "%" + univNature + "%");
			ps.setString(2, "%" + xkCode + "%");
			ps.setInt(3, scoreFrom);
			ps.setInt(4, scoreTo);

			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("CNT");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public int getZySearchResultConditionRelated_SUM(int year, String sfCode, String xkCode, HashSet<String> zyml, HashSet<String> univSf, String univNature, int scoreFrom, int scoreTo) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			String zymlInSQL = zyml.size() == 0 ? "" : "and x.zyml in ("+Tools.getSQLQueryin(zyml)+")";
			String yxsfInSQl = univSf.size() == 0 ? "" : "and x.yxsf in ("+Tools.getSQLQueryin(univSf)+")";
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT COUNT(*) AS CNT FROM "+sfCode+"_" + year + " x WHERE x.lqpc = '普通类' and (x.ind_nature like ?) "+yxsfInSQl+" and x.xk_code like ? "+ zymlInSQL +" and x.zdf between ? and ? ";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, "%" + univNature + "%");
			ps.setString(2, "%" + xkCode + "%");
			ps.setInt(3, scoreFrom);
			ps.setInt(4, scoreTo);

			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("CNT");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public int getZySearchResultConditionRelatedForForm_SUM(int year, String sfCode, String xkCode, HashSet<String> zyml, HashSet<String> univSf, String univNature, String key_words, String tag, int scoreFrom, int scoreTo) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			String zymlInSQL = zyml.size() == 0 ? "" : "and x.zyml in ("+Tools.getSQLQueryin(zyml)+")";
			String yxsfInSQl = univSf.size() == 0 ? "" : "and x.yxsf in ("+Tools.getSQLQueryin(univSf)+")";
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT COUNT(*) AS CNT FROM "+sfCode+"_" + year + " x WHERE x.lqpc = '普通类' and (x.ind_nature like ?) "+yxsfInSQl+" and x.xk_code like ? "+ zymlInSQL +" and x.yx_tags like ? and (x.yxmc like ? or x.zymc like ?) and x.zdf between ? and ? ";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, "%" + univNature + "%");
			ps.setString(2, "%" + xkCode + "%");
			ps.setString(3, "%" + tag + "%");
			ps.setString(4, "%" + key_words + "%");
			ps.setString(5, "%" + key_words + "%");
			ps.setInt(6, scoreFrom);
			ps.setInt(7, scoreTo);

			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("CNT");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public int getZDKSConvertResultCondition_SUM(int year, String sfCode, String xkCode, String univName, String univCatg, String univSf, String univNature, String lqpc, int orderPriority, int scoreFrom, int scoreTo) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT COUNT(*) AS CNT FROM "+sfCode+"_" + year + "x x WHERE (x.ind_nature like ?) and (x.ind_catg like ? or x.ind_catg is null) and (x.yxsf like ? or x.yxsf is null) and x.xk_code like ? and x.yxmc like ? and x.lqpc like ? and x.zdf between ? and ? ";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, "%" + univNature + "%");
			ps.setString(2, "%" + univCatg + "%");
			ps.setString(3, "%" + univSf + "%");
			ps.setString(4, "%" + xkCode + "%");
			ps.setString(5, "%" + univName + "%");
			ps.setString(6, "%" + lqpc + "%");
			ps.setInt(7, scoreFrom);
			ps.setInt(8, scoreTo);

			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("CNT");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public int getZDKSConvertResultCondition_SUM_inYXSearchPage(int year, String sfCode, String xkCode, String univName, HashSet<String> univNameSet, HashSet<String> univCatg, HashSet<String> univSf, String univNature, String lqpc, int orderPriority, int scoreFrom, int scoreTo) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String andSQL = univNameSet.size() == 0 ? "" : "and yxmc_org in (" + Tools.getSQLQueryin(univNameSet) + ")";
			String andYXLXSQL = univCatg.size() == 0 ? "" : "and ind_catg in (" + Tools.getSQLQueryin(univCatg) + ")";
			String andYXSFSQL = univSf.size() == 0 ? "" : "and yxsf in (" + Tools.getSQLQueryin(univSf) + ")";
			String SELECT_CONDITION = "SELECT COUNT(*) AS CNT FROM "+sfCode+"_" + year + "x x WHERE (x.ind_nature like ?) "+andYXLXSQL+" "+andYXSFSQL+" and x.xk_code like ? and (x.yxmc like ? "+andSQL+") and x.lqpc like ? and x.zdf between ? and ? ";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, "%" + univNature + "%");
			ps.setString(2, "%" + xkCode + "%");
			ps.setString(3, "%" + univName + "%");
			ps.setString(4, "%" + lqpc + "%");
			ps.setInt(5, scoreFrom);
			ps.setInt(6, scoreTo);

			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("CNT");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public int getZDKSConvertResultCondition_SUM_inFormSearchPage(int year, String sfCode, String xkCode, String univName, HashSet<String> univNameSet, HashSet<String> univCatg, HashSet<String> univSf, String univNature, String lqpc, String tag, int orderPriority, int scoreFrom, int scoreTo) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String andSQL = univNameSet.size() == 0 ? "" : "and yxmc_org in (" + Tools.getSQLQueryin(univNameSet) + ")";
			String andYXLXSQL = univCatg.size() == 0 ? "" : "and ind_catg in (" + Tools.getSQLQueryin(univCatg) + ")";
			String andYXSFSQL = univSf.size() == 0 ? "" : "and yxsf in (" + Tools.getSQLQueryin(univSf) + ")";
			String SELECT_CONDITION = "SELECT COUNT(*) AS CNT FROM "+sfCode+"_" + year + "x x WHERE (x.ind_nature like ?) "+andYXLXSQL+" "+andYXSFSQL+" and x.xk_code like ? and (x.yxmc like ? "+andSQL+") and x.lqpc like ? and x.yx_tags like ? and x.zdf between ? and ? ";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, "%" + univNature + "%");
			ps.setString(2, "%" + xkCode + "%");
			ps.setString(3, "%" + univName + "%");
			ps.setString(4, "%" + lqpc + "%");
			ps.setString(5, "%" + tag + "%");
			ps.setInt(6, scoreFrom);
			ps.setInt(7, scoreTo);

			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("CNT");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public ZyzdUniversityBean getZDKSConvertResultCondition_SUM_FOR_EMPLOY(int year, String sfCode, String xkCode, String univName, String univCatg, String univSf, String univNature, String lqpc, int orderPriority, int scoreFrom, int scoreTo) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		ZyzdUniversityBean bean = new ZyzdUniversityBean();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT SUM(cnt_company) as cnt_company, SUM(cnt_employ) AS cnt_employ FROM "+sfCode+"_" + year + "x x WHERE (x.ind_nature like ?) and (x.ind_catg like ? or x.ind_catg is null) and (x.yxsf like ? or x.yxsf is null) and x.xk_code like ? and x.yxmc like ? and x.lqpc like ? and x.zdf between ? and ? ";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, "%" + univNature + "%");
			ps.setString(2, "%" + univCatg + "%");
			ps.setString(3, "%" + univSf + "%");
			ps.setString(4, "%" + xkCode + "%");
			ps.setString(5, "%" + univName + "%");
			ps.setString(6, "%" + lqpc + "%");
			ps.setInt(7, scoreFrom);
			ps.setInt(8, scoreTo);

			rs = ps.executeQuery();
			while (rs.next()) {
				bean.setCnt_company(rs.getInt("cnt_company"));
				bean.setCnt_employ(rs.getInt("cnt_employ"));
				
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}
	
	
	public static int BLOCK_PARAM_CNT_FOR_CUSTOMER = 15;
	public static int BLOCK_PARAM_CNT_FOR_SCA = 25;
	public int checkBlockCount(String cid, String action) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		YGCardBean bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT count(*) as ct FROM zdks_block WHERE C_ID = ? and FROM_ACTION = ? and DATE_FORMAT(CREATE_DT, '%Y%m%d%H%i') = DATE_FORMAT(now(), '%Y%m%d%H%i')";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.setString(2, action);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("ct");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return -1;
	}
	
	public int checkBlockCountPerOneDay(String cid, String action) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		YGCardBean bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT count(*) as ct FROM zdks_block WHERE C_ID = ? and FROM_ACTION = ? and DATE_FORMAT(CREATE_DT, '%Y%m%d') = DATE_FORMAT(now(), '%Y%m%d')";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.setString(2, action);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("ct");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return -1;
	}
	
	public boolean deleteSysVisitLog(String cid) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "DELETE FROM sys_trans_visit WHERE c_id = ? AND VISIT_TM < (unix_timestamp(NOW()) - 60 * 60)";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}

	public int getLatest30SecsVisitLogCount(String cid) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT COUNT(1) as ct FROM sys_trans_visit WHERE c_id = ? AND VISIT_TM > (unix_timestamp(NOW()) - 10)";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			rs = ps.executeQuery();
			if (rs.next())
				return rs.getInt(1);
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public boolean updateCard(CardBean bean) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE base_card SET C_PROV = ?, C_SCORE = ?, C_XK = ?, C_YEAR = ?, C_ACTIVE = ?, C_STATUS = ?, C_LAST_LOGIN = ?, C_CITY = ?, C_SCORE_ORG = ? WHERE C_ID = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, bean.getProv());
			ps.setInt(2, bean.getScore());
			ps.setString(3, bean.getXk());
			ps.setInt(4, bean.getYear());
			if (bean.getActive() == null) {
				bean.setActive(new Date());
			}
			ps.setTimestamp(5, new Timestamp(bean.getActive().getTime()));
			ps.setInt(6, bean.getStatus());
			ps.setTimestamp(7, new Timestamp(bean.getLastLogin().getTime()));
			ps.setString(8, bean.getCity());
			ps.setInt(9, bean.getScore_org());
			ps.setString(10, bean.getId());
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean updateCardWithToken(CardBean bean) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE base_card SET C_PROV = ?, C_SCORE = ?, C_XK = ?, C_YEAR = ?, C_ACTIVE = ?, C_STATUS = ?, C_LAST_LOGIN = ?, C_CITY = ?, C_SCORE_ORG = ?, TOKEN_ID = ? WHERE C_ID = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, bean.getProv());
			ps.setInt(2, bean.getScore());
			ps.setString(3, bean.getXk());
			ps.setInt(4, bean.getYear());
			if (bean.getActive() == null) {
				bean.setActive(new Date());
			}
			ps.setTimestamp(5, new Timestamp(bean.getActive().getTime()));
			ps.setInt(6, bean.getStatus());
			ps.setTimestamp(7, new Timestamp(bean.getLastLogin().getTime()));
			ps.setString(8, bean.getCity());
			ps.setInt(9, bean.getScore_org());
			ps.setString(10, bean.getTokenId());
			ps.setString(11, bean.getId());
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean updateCardPhoneOnly(String cid, String phone, String desc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE base_card SET C_PHONE = ?, C_DESC = ? WHERE C_ID = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, phone);
			ps.setString(2, desc);
			ps.setString(3, cid);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean updateCardPhoneAndSaasIdOnly(String cid, String phone, String saas_id, String desc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE base_card SET C_PHONE = ?, C_DESC = ?, saas_id = ? WHERE C_ID = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, phone);
			ps.setString(2, desc);
			ps.setString(3, saas_id);
			ps.setString(4, cid);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean updateCardLastModifiedOnly(String cid) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE base_card SET C_LAST_MODIFIED = now() WHERE C_ID = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean updateCardPhoneAndIntro(String cid, String phone, String desc, String introductAgentId) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE base_card SET C_PHONE = ?, C_DESC = ?, C_INTRODUCE = ?, C_INTRODUCE_TM = now() WHERE C_ID = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, phone);
			ps.setString(2, desc);
			ps.setString(3, introductAgentId);
			ps.setString(4, cid);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean updateCardDescOnly(String cid, String desc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE base_card SET C_DESC = ? WHERE C_ID = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, desc);
			ps.setString(2, cid);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean updateCardFormTrialToFormal(String cid) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE base_card SET C_SYS_IND = 'FU' WHERE C_ID = ? ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean updateCardDescAndSysindAndExpireTm(String cid, String desc, String allowedProv) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE base_card SET C_DESC = ?, C_ALLOWED_PROV = ?, C_EXPIRE = ?   WHERE C_ID = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, desc);
			ps.setString(2, allowedProv);
			ps.setTimestamp(3, new Timestamp(Tools.getOffsetDT(new Date(), 365).getTime()));
			ps.setString(4, cid);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean updateCardSysindOnly(String cid, String sysInd) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE base_card SET C_SYS_IND = ? WHERE C_ID = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, sysInd);
			ps.setString(2, cid);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean updateCardExpireOnly(String cid, Date expire) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE base_card SET C_EXPIRE = ? WHERE C_ID = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setTimestamp(1, new Timestamp(expire.getTime()));
			ps.setString(2, cid);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean updateCardPartion(String cid, String agent, Date expire) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE base_card SET C_AGENT = ?, C_AGENT_TM = NOW(), C_EXPIRE = ? WHERE C_ID = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, agent);
			ps.setTimestamp(2, new Timestamp(expire.getTime()));
			ps.setString(3, cid);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean updateCardIntroduceInfoViaAgentInfo(String cid) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE base_card SET C_INTRODUCE = C_AGENT, C_INTRODUCE_TM = C_AGENT_TM WHERE C_ID = ? and C_INTRODUCE IS NULL";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public CardBean getCardByIDandPasswd(String id, String passwd) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		CardBean bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM base_card WHERE C_ID = ? and C_PASSWD = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, id.toUpperCase());
			ps.setString(2, passwd.toUpperCase());
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = getCardBean(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}
	
	public CardBean getCardByIDandPasswdWithSaasId(String id, String passwd) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		CardBean bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM base_card WHERE C_ID = ? and C_PASSWD = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, id.toUpperCase());
			ps.setString(2, passwd.toUpperCase());
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = getCardBean(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}
	
	public String getProvinceCodeOfFormMakerMain(String batch_id, String from_where) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		CardBean bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_form_batch_id_all WHERE batch_id = ? AND from_where = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			ps.setString(2, from_where);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getString("sf");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public String getCardTokenById(String id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		CardBean bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT TOKEN_ID FROM base_card WHERE C_ID = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, id.toUpperCase());
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getString("TOKEN_ID");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public String insertTokenDuplicate(String c_id, String ip) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		CardBean bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into zyzd_token_duplicate(c_id, ip, create_tm) values(?,?,now())";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, c_id);
			ps.setString(2, ip);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getString("TOKEN_ID");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	
	public ZyzdPromotionRuleForUpgrade getPromotionRuleForUpgrade(String fid) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		ZyzdPromotionRuleForUpgrade bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_promotion_rule_for_upgrade WHERE f_id = ? ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, fid.toUpperCase());
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = new ZyzdPromotionRuleForUpgrade();
				bean.setBuy_price(rs.getInt("buy_price"));
				bean.setF_id(rs.getString("f_id"));
				return bean;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public CardBean getCardByID(String id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		CardBean bean = null;
		if(Tools.isEmpty(id)) {
			return null;
		}
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM base_card WHERE C_ID = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, id.toUpperCase());
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = getCardBean(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}
	
	public CardBean getCardidByWxQrscene(String qrscene) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		CardBean bean = null;
		if(Tools.isEmpty(qrscene)) {
			return null;
		}
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT *  FROM base_card WHERE WX_QRSCENE = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, qrscene);
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = getCardBean(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}
	
	public WxShareUser getWxSharedUserViaID(String MD5sid) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		WxShareUser bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM wx_share_user WHERE s_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, MD5sid);
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = setWxShareAccount(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}
	
	public ZyzdScoreIncreaseConfig getScoreIncreaseConfig(String catg, int score) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_xxl_score_increase_config x where x.score_catg = ? and x.score_span_from <= ? and x.score_span_to >= ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, catg);
			ps.setInt(2, score);
			ps.setInt(3, score);
			ZyzdScoreIncreaseConfig bean = null;
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = new ZyzdScoreIncreaseConfig();
				bean.setScore_catg(rs.getString("score_catg"));
				bean.setScore_span(rs.getString("score_span"));
				bean.setScore_span_from(rs.getInt("score_span_from"));
				bean.setScore_span_to(rs.getInt("score_span_to"));
				bean.setScore_increase(rs.getFloat("score_increase"));
				bean.setScore_increase_max(rs.getFloat("score_increase_max"));
				bean.setScore_increase_max_to(rs.getFloat("score_increase_max_to"));
				return bean;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public HashMap<String, List<ZyzdScoreIncreaseConfig>> getScoreIncreaseConfigMap() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, List<ZyzdScoreIncreaseConfig>> MAP = new HashMap<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_xxl_score_increase_config x";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ZyzdScoreIncreaseConfig bean = null;
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = new ZyzdScoreIncreaseConfig();
				bean.setScore_catg(rs.getString("score_catg"));
				bean.setScore_span(rs.getString("score_span"));
				bean.setScore_span_from(rs.getInt("score_span_from"));
				bean.setScore_span_to(rs.getInt("score_span_to"));
				bean.setScore_increase(rs.getFloat("score_increase"));
				bean.setScore_increase_max(rs.getFloat("score_increase_max"));
				bean.setScore_increase_max_to(rs.getFloat("score_increase_max_to"));
				if(MAP.containsKey(bean.getScore_catg())) {
					List<ZyzdScoreIncreaseConfig> list = MAP.get(bean.getScore_catg());
					list.add(bean);
				}else {
					List<ZyzdScoreIncreaseConfig> list = new ArrayList<>();
					list.add(bean);
					MAP.put(bean.getScore_catg(), list);
				}
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return MAP;
	}
	
	public HashMap<String, List<ZyzdCpIncreaseConfig>> getCpIncreaseConfigMap() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, List<ZyzdCpIncreaseConfig>> MAP = new HashMap<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_xxl_cp_increase_config x";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ZyzdCpIncreaseConfig bean = null;
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = new ZyzdCpIncreaseConfig();
				bean.setCp_catg(rs.getString("cp_catg"));
				bean.setCp_span(rs.getString("cp_span"));
				bean.setCp_span_from(rs.getFloat("cp_span_from"));
				bean.setCp_span_to(rs.getFloat("cp_span_to"));
				bean.setCp_increase(rs.getFloat("cp_increase")); 
				if(MAP.containsKey(bean.getCp_catg())) {
					List<ZyzdCpIncreaseConfig> list = MAP.get(bean.getCp_catg());
					list.add(bean);
				}else {
					List<ZyzdCpIncreaseConfig> list = new ArrayList<>();
					list.add(bean);
					MAP.put(bean.getCp_catg(), list);
				}
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return MAP;
	}
	
	public HashMap<String, List<ZyzdIncreaseDifConfig>> getIncreaseDifConfigMap() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, List<ZyzdIncreaseDifConfig>> MAP = new HashMap<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_xxl_increase_dif_config x";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ZyzdIncreaseDifConfig bean = null;
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = new ZyzdIncreaseDifConfig();
				bean.setDif_catg(rs.getString("dif_catg"));
				bean.setDif_span(rs.getString("dif_span"));
				bean.setDif_span_from(rs.getFloat("dif_span_from"));
				bean.setDif_span_to(rs.getFloat("dif_span_to"));
				bean.setDif_rate(rs.getFloat("dif_rate")); 
				if(MAP.containsKey(bean.getDif_catg())) {
					List<ZyzdIncreaseDifConfig> list = MAP.get(bean.getDif_catg());
					list.add(bean);
				}else {
					List<ZyzdIncreaseDifConfig> list = new ArrayList<>();
					list.add(bean);
					MAP.put(bean.getDif_catg(), list);
				}
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return MAP;
	}
	
	public ZyzdCpIncreaseConfig getCpIncreaseConfig(String catg, float score) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_xxl_cp_increase_config x where x.cp_catg = ? and x.cp_span_from <= ? and x.cp_span_to > ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, catg);
			ps.setFloat(2, score);
			ps.setFloat(3, score);
			ZyzdCpIncreaseConfig bean = null;
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = new ZyzdCpIncreaseConfig();
				bean.setCp_catg(rs.getString("cp_catg"));
				bean.setCp_span(rs.getString("cp_span"));
				bean.setCp_span_from(rs.getFloat("cp_span_from"));
				bean.setCp_span_to(rs.getFloat("cp_span_to"));
				bean.setCp_increase(rs.getFloat("cp_increase")); 
				return bean;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public List<ZyzdProvinceConfig> getAllProvinceConfig() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdProvinceConfig> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_province_config order by sort DESC";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ZyzdProvinceConfig bean = null;
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = new ZyzdProvinceConfig();
				bean.setForm_type(rs.getInt("form_type"));
				
				bean.setForm_bk_cnt(rs.getInt("form_bk_cnt"));
				bean.setForm_bk_rule(rs.getString("form_bk_rule"));
				bean.setForm_bk_major_cnt(rs.getInt("form_bk_major_cnt"));
				
				bean.setForm_zk_cnt(rs.getInt("form_zk_cnt"));
				bean.setForm_zk_rule(rs.getString("form_zk_rule"));
				bean.setForm_zk_major_cnt(rs.getInt("form_zk_major_cnt"));
				
				bean.setForm_zjbk_cnt(rs.getInt("form_zjbk_cnt"));
				bean.setForm_zjbk_rule(rs.getString("form_zjbk_rule"));
				bean.setForm_zjbk_major_cnt(rs.getInt("form_zjbk_major_cnt"));
				
				bean.setLatest_year_jh(rs.getInt("latest_year_jh"));
				bean.setLatest_year_yx(rs.getInt("latest_year_yx"));
				bean.setLatest_year_zy(rs.getInt("latest_year_zy"));
				
				bean.setOpen_ind(rs.getInt("open_ind"));
				bean.setOpen_status_lhy(rs.getInt("open_status_lhy"));
				bean.setP_code(rs.getString("p_code"));
				bean.setP_table_code(rs.getString("p_table_code"));
				bean.setP_name(rs.getString("p_name"));
				bean.setP_view_name(rs.getString("p_view_name"));
				bean.setPcx_bk_wl(rs.getInt("pcx_bk_wl"));
				bean.setPcx_bk_ls(rs.getInt("pcx_bk_ls"));
				bean.setOpen_status_lhy(rs.getInt("open_status_lhy"));
				bean.setJh_version(rs.getInt("jh_version"));
				bean.setTb_start_tm(rs.getTimestamp("tb_start_tm"));
				
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	/**
	 * 拉取具体每个批次配置
	 * @param sfCode
	 * @param pcCode
	 * @param pc
	 * @return
	 */
	public ZyzdProvincePcConfig getProvincePcConfigByParams(String sfCode, String pcCode, String pc) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	        conn = DatabaseUtils.getConnection();
	        // 构造 SQL 查询语句
	        String SQL = "SELECT * FROM zyzd_province_pc_config WHERE pc_code = ? AND pc = ? AND p_code = ?";
	        Tools.println(SQL);
	        ps = conn.prepareStatement(SQL);
	        // 设置查询参数
	        ps.setString(1, pcCode);
	        ps.setString(2, pc);
	        ps.setString(3, sfCode);
	        rs = ps.executeQuery();
	        SQLLogUtils.printSQL(ps);
	        while (rs.next()) {
	            ZyzdProvincePcConfig bean = new ZyzdProvincePcConfig();
	            bean.setP_code(rs.getString("p_code"));
	            bean.setPc(rs.getString("pc"));
	            bean.setPc_code(rs.getString("pc_code"));
	            bean.setForm_cnt(rs.getInt("form_cnt"));
	            bean.setMajor_cnt(rs.getInt("major_cnt"));
	            bean.setForm_rule(rs.getString("form_rule"));
	            bean.setForm_type(rs.getInt("form_type"));
	            bean.setNormal_pc(rs.getInt("normal_pc"));
	            return bean;
	        }
	    } catch (Exception ex) {
	        ex.printStackTrace();
	    } finally {
	        closeAllConnection(conn, ps, rs);
	    }
	    return null;
	}
	
	public List<WxShareUser> getWxSharedUserViaBossID(String bossid) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<WxShareUser> list = new ArrayList<>();
		WxShareUser bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM wx_share_user WHERE boss_sid = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, bossid);
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = setWxShareAccount(rs);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public HashMap<String, Integer> getWxSharedUserIntroCnt(HashSet<String> sidQrsceneSets) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, Integer> MAP = new HashMap<>();
		WxShareUser bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT concat('F',share_qrscene), count(*) as cnt FROM wx_share WHERE share_qrscene in ("+Tools.getSQLQueryin(sidQrsceneSets)+") GROUP BY share_qrscene";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			while (rs.next()) {
				String qrscene = rs.getString("share_qrscene");
				int cnt = rs.getInt("cnt");
				MAP.put(qrscene, cnt);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return MAP;
	}
	
	public HashMap<String, Integer> getWxSharedUserPaymentCnt(HashSet<String> sidQrsceneSets) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, Integer> MAP = new HashMap<>();
		WxShareUser bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT SHARED_ID_ORG, count(*) as cnt FROM wx_payment WHERE STATUS = 1 and SHARED_ID_ORG in ("+Tools.getSQLQueryin(sidQrsceneSets)+") GROUP BY SHARED_ID_ORG";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			while (rs.next()) {
				String qrscene = rs.getString("SHARED_ID_ORG");
				int cnt = rs.getInt("cnt");
				MAP.put(qrscene, cnt);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return MAP;
	}
	
	private CardBean getCardBean(ResultSet rs) throws Exception{
		CardBean bean = new CardBean();
		bean.setId(rs.getString("C_ID"));
		bean.setPasswd(rs.getString("C_PASSWD"));
		bean.setPhone(rs.getString("C_PHONE"));
		bean.setProv(rs.getString("C_PROV"));
		bean.setCity(rs.getString("C_CITY"));
		bean.setXk(rs.getString("C_XK"));
		bean.setScore(rs.getInt("C_SCORE"));
		bean.setScore_org(rs.getInt("C_SCORE_ORG"));
		bean.setYear(rs.getInt("C_YEAR"));
		bean.setStatus(rs.getInt("C_STATUS"));
		bean.setCreate(rs.getTimestamp("C_CREATE"));
		bean.setActive(rs.getTimestamp("C_ACTIVE"));
		bean.setLastLogin(rs.getTimestamp("C_LAST_LOGIN"));
		bean.setOpenID(rs.getString("C_OPEN_ID"));
		bean.setSysInd(rs.getString("C_SYS_IND"));
		bean.setExpire(rs.getTimestamp("C_EXPIRE"));
		bean.setAllowedProv(rs.getString("C_ALLOWED_PROV"));
		bean.setVersion(rs.getString("C_VERSION"));
		bean.setAdmin(rs.getString("C_ADMIN"));
		bean.setOrderId(rs.getString("C_ORDER_ID"));
		bean.setDesc(rs.getString("C_DESC"));
		bean.setRemark(rs.getString("C_REMARK"));
		bean.setBossId(rs.getString("C_BOSS_ID"));
		bean.setNickName(rs.getString("C_NICKNAME"));
		bean.setIntroduce(rs.getString("C_INTRODUCE"));
		bean.setIntroduceTm(rs.getTimestamp("C_INTRODUCE_TM"));
		bean.setAgent(rs.getString("C_AGENT"));
		bean.setAgentTm(rs.getTimestamp("C_AGENT_TM"));
		bean.setLast_modified(rs.getTimestamp("C_LAST_MODIFIED"));
		bean.setTokenId(rs.getString("TOKEN_ID"));
		bean.setForm_dnld_cnt(rs.getInt("C_FORM_DNLD_CNT"));
		bean.setC_f_level(rs.getInt("c_f_level")); 
		bean.setReview_ai_cnt(rs.getInt("review_ai_cnt")); 
		bean.setReview_maual_cnt(rs.getInt("review_maual_cnt")); 
		bean.setRemote_tb_cnt(rs.getInt("remote_tb_cnt")); 
		bean.setSaas_id(rs.getString("saas_id"));
		bean.setAllowed_menu_page(rs.getString("allowed_menu_page"));
		return bean;
	}
	
	public List<String> getArmyDw(int CNT) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<String> ARMY_LIST = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT dw_view, COUNT(*) FROM career_jy_req_army_2025 x GROUP BY x.dw_view ORDER BY COUNT(*) DESC LIMIT 0, " + CNT;
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			while (rs.next()) {
				ARMY_LIST.add(rs.getString("dw_view"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return ARMY_LIST;
	}
	
	public List<ZyzdArmyMajor> getArmyMajorByDwViewAndProvince(String dw_view, String province, String zymcColumnName, int viewCnt) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdArmyMajor> ARMY_LIST = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x."+zymcColumnName+", COUNT(distinct dw) as cnt, COUNT(*) as sum FROM career_jy_req_army_major_2025 x WHERE x."+zymcColumnName+" IS NOT NULL and x.province like ? and x.dw_view like ? GROUP BY x."+zymcColumnName+" ORDER BY COUNT(distinct dw) DESC, COUNT(*) DESC LIMIT 0, " + viewCnt;
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + province + "%");
			ps.setString(2, "%" + dw_view + "%");
			rs = ps.executeQuery();
			ZyzdArmyMajor major = null;
			while (rs.next()) {
				major = new ZyzdArmyMajor();
				major.setZymc(rs.getString(zymcColumnName));
				major.setCnt(rs.getInt("cnt"));
				major.setSum(rs.getInt("sum"));
				ARMY_LIST.add(major);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return ARMY_LIST;
	}
	
	public List<ZyzdArmy> getArmyDwByMajorAndProvince(String zymc, String dw_view, String province, String zymcColumnName, int viewCnt) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, List<ZyzdArmyMajor>> ARMY_MAP = new HashMap<>();
		List<ZyzdArmy> ARMY_LIST = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.dw, SUM(rs) as cnt FROM career_jy_req_army_2025 x WHERE x."+zymcColumnName+" like ? and x.province like ? and x.dw_view like ? GROUP BY x.dw ORDER BY SUM(rs) DESC limit 0," + viewCnt;
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + zymc + "%");
			ps.setString(2, "%" + province + "%");
			ps.setString(3, "%" + dw_view + "%");
			rs = ps.executeQuery();
			ZyzdArmy major = null;
			while (rs.next()) {
				major = new ZyzdArmy();
				major.setDw(rs.getString("dw"));
				major.setRs(rs.getInt("cnt"));
				//String key = 
				ARMY_LIST.add(major);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return ARMY_LIST;
	}
	
	public static void main(String args[]) {
		ZyzdJDBC jdbc = new ZyzdJDBC();
		jdbc.dealArmyMajor("zymc_diploma");
		jdbc.dealArmyMajor("zymc_bachelor");
		jdbc.dealArmyMajor("zymc_master");
	}
	
	public void dealArmyMajor(String xl_column) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<String> ARMY_LIST = new ArrayList<>();
		StringBuffer SQL_insert = new StringBuffer();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT dw,dw_view,province, x."+xl_column+" FROM career_jy_req_army_2025 x where x." + xl_column + " IS NOT NULL";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			while (rs.next()) {
				String dw_view = rs.getString("dw_view");
				String dw = rs.getString("dw");
				String province = rs.getString("province");
				String zymc = rs.getString(xl_column);
				
				if(!Tools.isEmpty(zymc)){
					System.out.println(zymc);
					String withoutNumbersAndLetters = zymc.replaceAll("[0-9a-zA-Z]", "");
			        System.out.println(withoutNumbersAndLetters);
			        String[] arr = withoutNumbersAndLetters.split(",");
			        for(String x : arr) {
			        	SQL_insert.append("insert into career_jy_req_army_major_2025(dw,dw_view,province, "+xl_column+") values('"+dw+"','"+dw_view+"','"+province+"','"+x.trim()+"');\r\n");
			        }
					
				}
				
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		
		writeTempFile(new File("F://诊断考试换算//QC_xygh"+xl_column+".txt"), SQL_insert);
	}
	
	public static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	public String getMD5Cardid(String cid) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		CardBean bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT MD5(C_ID) as c_id FROM base_card WHERE C_ID = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getString("c_id");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public List<CardBean> getTrialCardByPhone(String phone) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CardBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM base_card x WHERE x.C_PHONE = ? AND C_SYS_IND = 'TRIAL' and x.C_STATUS = 1";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, phone);
			rs = ps.executeQuery();
			CardBean bean = null;
			while (rs.next()) {
				bean = getCardBean(rs);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdInvitationCode> getInvitationCodeByUserid(String user_cid) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdInvitationCode> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_invitation_code x WHERE x.user_c_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, user_cid);
			rs = ps.executeQuery();
			ZyzdInvitationCode bean = null;
			while (rs.next()) {
				bean = new ZyzdInvitationCode();
				bean.setActive_tm(rs.getTimestamp("active_tm"));
				bean.setAgent_c_id(rs.getString("agent_c_id"));
				bean.setAgent_child_c_id(rs.getString("agent_child_c_id"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				bean.setIvt_id(rs.getString("ivt_id"));
				bean.setIvt_type(rs.getInt("ivt_type"));
				bean.setUser_c_id(rs.getString("user_c_id"));
				bean.setUser_phone(rs.getString("user_phone"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdInvitationCode> getInvitationCodeByAgent(String agent_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdInvitationCode> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_invitation_code x WHERE x.agent_c_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, agent_id);
			rs = ps.executeQuery();
			ZyzdInvitationCode bean = null;
			while (rs.next()) {
				bean = new ZyzdInvitationCode();
				bean.setActive_tm(rs.getTimestamp("active_tm"));
				bean.setAgent_c_id(rs.getString("agent_c_id"));
				bean.setAgent_child_c_id(rs.getString("agent_child_c_id"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				bean.setIvt_id(rs.getString("ivt_id"));
				bean.setIvt_type(rs.getInt("ivt_type"));
				bean.setUser_c_id(rs.getString("user_c_id"));
				bean.setUser_phone(rs.getString("user_phone"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdInvitationCode> getInvitationCodeByAgent(String agent_id, boolean used) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdInvitationCode> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_invitation_code x WHERE x.agent_c_id = ? and user_c_id " + (used ? " is not null" : " is null");
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, agent_id);
			rs = ps.executeQuery();
			ZyzdInvitationCode bean = null;
			while (rs.next()) {
				bean = new ZyzdInvitationCode();
				bean.setActive_tm(rs.getTimestamp("active_tm"));
				bean.setAgent_c_id(rs.getString("agent_c_id"));
				bean.setAgent_child_c_id(rs.getString("agent_child_c_id"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				bean.setIvt_id(rs.getString("ivt_id"));
				bean.setIvt_type(rs.getInt("ivt_type"));
				bean.setUser_c_id(rs.getString("user_c_id"));
				bean.setUser_phone(rs.getString("user_phone"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdInvitationCode> getInvitationCodeByAgent(String agent_id, boolean used, int viewCnt) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdInvitationCode> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.*,y.C_SYS_IND as sys_ind, y.C_LAST_LOGIN as last_login, y.C_SCORE_ORG as score_org, y.C_XK as xk, y.C_PROV as prov FROM zyzd_invitation_code x LEFT JOIN base_card y ON x.user_c_id = y.C_ID WHERE x.agent_c_id = ? and user_c_id " + (used ? " is not null" : " is null") + " order by active_tm desc limit 0, " + viewCnt;
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, agent_id);
			rs = ps.executeQuery();
			ZyzdInvitationCode bean = null;
			while (rs.next()) {
				bean = new ZyzdInvitationCode();
				bean.setActive_tm(rs.getTimestamp("active_tm"));
				bean.setAgent_c_id(rs.getString("agent_c_id"));
				bean.setAgent_child_c_id(rs.getString("agent_child_c_id"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				bean.setIvt_id(rs.getString("ivt_id"));
				bean.setIvt_type(rs.getInt("ivt_type"));
				bean.setUser_c_id(rs.getString("user_c_id"));
				bean.setUser_phone(rs.getString("user_phone"));
				
				bean.setExt_last_login(rs.getTimestamp("last_login"));
				bean.setExt_prov(rs.getString("prov"));
				bean.setExt_score_org(rs.getInt("score_org"));
				bean.setExt_xk(rs.getString("xk"));
				bean.setExt_sys_ind(rs.getString("sys_ind"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdInvitationCode> generateUnusedInvitationCodeForAgent(int cnt) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdInvitationCode> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_invitation_code x WHERE x.user_c_id IS NULL AND x.ivt_type = 1 AND x.agent_c_id IS null ORDER BY RAND() LIMIT 0 ," + cnt;
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			ZyzdInvitationCode bean = null;
			while (rs.next()) {
				bean = new ZyzdInvitationCode();
				bean.setActive_tm(rs.getTimestamp("active_tm"));
				bean.setAgent_c_id(rs.getString("agent_c_id"));
				bean.setAgent_child_c_id(rs.getString("agent_child_c_id"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				bean.setIvt_id(rs.getString("ivt_id"));
				bean.setIvt_type(rs.getInt("ivt_type"));
				bean.setUser_c_id(rs.getString("user_c_id"));
				bean.setUser_phone(rs.getString("user_phone"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public ZyzdInvitationCode getInvitationCodeById(String invitation_code) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_invitation_code x WHERE x.ivt_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, invitation_code);
			rs = ps.executeQuery();
			ZyzdInvitationCode bean = null;
			while (rs.next()) {
				bean = new ZyzdInvitationCode();
				bean.setActive_tm(rs.getTimestamp("active_tm"));
				bean.setAgent_c_id(rs.getString("agent_c_id"));
				bean.setAgent_child_c_id(rs.getString("agent_child_c_id"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				bean.setIvt_id(rs.getString("ivt_id"));
				bean.setIvt_type(rs.getInt("ivt_type"));
				bean.setUser_c_id(rs.getString("user_c_id"));
				bean.setUser_phone(rs.getString("user_phone"));
				return bean;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public boolean updateInvitationCode(String id, String user_cid, String user_phone) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE zyzd_invitation_code SET user_c_id = ?,user_phone = ?,active_tm = now() WHERE ivt_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, user_cid);
			ps.setString(2, user_phone);
			ps.setString(3, id);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean updateInvitationCode(String id, String user_cid, String user_phone, String childId, String agentId) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE zyzd_invitation_code SET user_c_id = ?,user_phone = ?, agent_child_c_id = ?, agent_c_id = ?, active_tm = now() WHERE ivt_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, user_cid);
			ps.setString(2, user_phone);
			ps.setString(3, childId);
			ps.setString(4, agentId);
			ps.setString(5, id);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean updateInvitationCodeAgentId(String agent_id, HashSet<String> ivtIDs) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE zyzd_invitation_code SET create_tm = now(), agent_c_id = ? WHERE agent_c_id is null and ivt_id in ("+Tools.getSQLQueryin(ivtIDs)+")";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, agent_id);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public List<ZyzdSharedArticle> getLatestSharedArticle(int count) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdSharedArticle> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_shared_article x WHERE x.status = 1 ORDER BY create_tm DESC LIMIT " + count;
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			ZyzdSharedArticle bean = null;
			while (rs.next()) {
				bean = new ZyzdSharedArticle();
				bean.setA_content(rs.getString("a_content"));
				bean.setA_id(rs.getString("a_id"));
				bean.setA_title(rs.getString("a_title"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				bean.setDisplay_tm(rs.getTimestamp("display_tm"));
				bean.setStatus(rs.getInt("status"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<CardBean> getUnusedSCA(String sca_prefix, int count) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CardBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM base_card x WHERE x.C_STATUS = 1 and x.c_desc is null and x.C_ACTIVE is null and C_ID LIKE ? ORDER BY C_ID LIMIT " + count;
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%"+sca_prefix+"%");
			rs = ps.executeQuery();
			CardBean bean = null;
			while (rs.next()) {
				bean = getCardBean(rs);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	
	public ZyzdSharedArticle getArticle(String article_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		ZyzdSharedArticle bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_shared_article x WHERE x.status = 1 AND a_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, article_id);
			rs = ps.executeQuery();
			
			while (rs.next()) {
				bean = new ZyzdSharedArticle();
				bean.setA_content(rs.getString("a_content"));
				bean.setA_summary(rs.getString("a_summary"));
				bean.setA_id(rs.getString("a_id"));
				bean.setA_title(rs.getString("a_title"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				bean.setDisplay_tm(rs.getTimestamp("display_tm"));
				bean.setStatus(rs.getInt("status"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}
	
	public List<CardBean> getRandomUnusedTrialCard(int randomCnt) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CardBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM base_card x WHERE x.C_SYS_IND = 'TRIAL' AND C_PHONE IS NULL AND C_AGENT IS NULL AND C_DESC IS NULL ORDER BY RAND() LIMIT " + randomCnt;
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			CardBean bean = null;
			while (rs.next()) {
				bean = getCardBean(rs);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<CardBean> getIntroCardByCid(String intro_id, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CardBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM base_card x WHERE x.C_INTRODUCE = ? ORDER BY C_INTRODUCE_TM DESC LIMIT ?, ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, intro_id);
			ps.setInt(2, (pageNumber - 1) * PAGE_ROW_CNT);
			ps.setInt(3, PAGE_ROW_CNT);
			rs = ps.executeQuery();
			CardBean bean = null;
			while (rs.next()) {
				bean = getCardBean(rs);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<CardBean> getAgentCardByCid(String agent_id, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CardBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM base_card x WHERE x.C_AGENT = ? ORDER BY C_LAST_LOGIN DESC LIMIT ?, ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, agent_id);
			ps.setInt(2, (pageNumber - 1) * PAGE_ROW_CNT);
			ps.setInt(3, PAGE_ROW_CNT);
			rs = ps.executeQuery();
			CardBean bean = null;
			while (rs.next()) {
				bean = getCardBean(rs);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public CardBean getRandomUnusedWebPay98Card() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM base_card x WHERE x.C_SYS_IND = 'FP' AND C_PHONE IS NULL AND C_AGENT IS NULL AND C_DESC IS NULL ORDER BY RAND() LIMIT 1";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			CardBean bean = null;
			while (rs.next()) {
				bean = getCardBean(rs);
				return bean;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public List<YGPaymentBean> getWxPaymentByAgentId(String admin_id, int pageNumber) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    List<YGPaymentBean> list = new ArrayList<>();
	    
	    try {
	      conn = DatabaseUtils.getConnection();
	      String SQL = "SELECT * FROM wx_payment WHERE AGENT_ID_ORG = ? and STATUS in (0,1) ORDER BY STATUS DESC,WX_PAID_TM DESC, CREATE_TM DESC LIMIT ?,?";
	      Tools.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, admin_id);
	      ps.setInt(2, (pageNumber - 1) * PAGE_ROW_CNT);
		  ps.setInt(3, PAGE_ROW_CNT);
	      rs = ps.executeQuery();
	      YGPaymentBean bean = null;
	      while (rs.next()) {
	    	bean = new YGPaymentBean();
	        bean.setId(rs.getString("P_ID"));
	        bean.setOpenId(rs.getString("WX_OPEN_ID"));
	        bean.setPrepayId(rs.getString("WX_PREPAY_ID"));
	        bean.setCreateTm(rs.getTimestamp("CREATE_TM"));
	        bean.setPaidTm(rs.getTimestamp("WX_PAID_TM"));
	        bean.setStatus(rs.getInt("STATUS"));
	        bean.setAmt(rs.getInt("AMT"));
	        bean.setItem(rs.getInt("ITEM"));
	        bean.setCardId(rs.getString("CARD_ID"));
	        bean.setCardPass(rs.getString("CARD_PASS"));
	        bean.setPhone(rs.getString("PHONE"));
	        list.add(bean);
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return list;
	}
	
	public List<YGPaymentBean> getWxPaymentByIntroId(String intro_id, int pageNumber) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    List<YGPaymentBean> list = new ArrayList<>();
	    
	    try {
	      conn = DatabaseUtils.getConnection();
	      String SQL = "SELECT * FROM wx_payment WHERE SHARED_ID_ORG = ? and STATUS in (0,1) ORDER BY STATUS DESC,WX_PAID_TM DESC, CREATE_TM DESC LIMIT ?,?";
	      Tools.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, intro_id);
	      ps.setInt(2, (pageNumber - 1) * PAGE_ROW_CNT);
		  ps.setInt(3, PAGE_ROW_CNT);
	      rs = ps.executeQuery();
	      YGPaymentBean bean = null;
	      while (rs.next()) {
	    	bean = new YGPaymentBean();
	        bean.setId(rs.getString("P_ID"));
	        bean.setOpenId(rs.getString("WX_OPEN_ID"));
	        bean.setPrepayId(rs.getString("WX_PREPAY_ID"));
	        bean.setCreateTm(rs.getTimestamp("CREATE_TM"));
	        bean.setPaidTm(rs.getTimestamp("WX_PAID_TM"));
	        bean.setStatus(rs.getInt("STATUS"));
	        bean.setAmt(rs.getInt("AMT"));
	        bean.setItem(rs.getInt("ITEM"));
	        bean.setCardId(rs.getString("CARD_ID"));
	        bean.setCardPass(rs.getString("CARD_PASS"));
	        bean.setPhone(rs.getString("PHONE"));
	        list.add(bean);
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return list;
	}
	
	public CardBean getRandomUnusedWebPay698998Card() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM base_card x WHERE x.C_SYS_IND = 'SCAP' AND C_PHONE IS NULL AND C_AGENT IS NULL AND C_DESC IS NULL ORDER BY RAND() LIMIT 1";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			CardBean bean = null;
			while (rs.next()) {
				bean = getCardBean(rs);
				return bean;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public CardBean getRandomUnusedWebPayCheckerCard() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM base_card x WHERE x.C_SYS_IND = 'SCAP' AND C_PHONE IS NULL AND C_AGENT IS NULL AND C_DESC IS NULL ORDER BY RAND() LIMIT 1";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			CardBean bean = null;
			while (rs.next()) {
				bean = getCardBean(rs);
				return bean;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public int getUnusedScaCardCntForWebPay() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT COUNT(*) as cnt FROM base_card x WHERE x.C_SYS_IND = 'SCAP' AND C_PHONE IS NULL AND C_AGENT IS NULL AND C_DESC IS NULL";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			CardBean bean = null;
			while (rs.next()) {
				bean = getCardBean(rs);
				return rs.getInt("cnt");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public int getUnusedUserCardCntForWebPay() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT COUNT(*) as cnt FROM base_card x WHERE x.C_SYS_IND = 'FP' AND C_PHONE IS NULL AND C_AGENT IS NULL AND C_DESC IS NULL";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			CardBean bean = null;
			while (rs.next()) {
				bean = getCardBean(rs);
				return rs.getInt("cnt");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public List<CardBean> getBaseCardByDesc(String desc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CardBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM base_card x WHERE x.c_desc = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, desc);
			rs = ps.executeQuery();
			CardBean bean = null;
			while (rs.next()) {
				bean = getCardBean(rs);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<GzdzCard> getGzdzCardByBatch(String batch_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<GzdzCard> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM gzdz_card x WHERE x.batch_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			rs = ps.executeQuery();
			GzdzCard bean = null;
			while (rs.next()) {
				bean = new GzdzCard();
				bean.setC_id(rs.getString("c_id"));
				bean.setC_passwd(rs.getString("c_passwd"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public boolean arrayBaseCardForAgent(String agentId, String desc, String cardIdPrefix, String saas_id, int count, int f_level, Date exp_date) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE base_card x SET x.C_DESC = ?, x.C_INTRODUCE = ?, x.C_AGENT = ?, saas_id = ? ,c_f_level = ?, C_EXPIRE = ?, x.C_AGENT_TM = now(), x.C_INTRODUCE_TM = now() WHERE x.C_PROV IS NULL AND x.C_ACTIVE IS NULL AND x.C_DESC IS NULL AND x.C_ID LIKE '"+cardIdPrefix+"%' ORDER BY rand () LIMIT " +count ;
			ps = conn.prepareStatement(SQL);
			ps.setString(1, desc);
			ps.setString(2, agentId);
			ps.setString(3, agentId);
			ps.setString(4, saas_id);
			ps.setInt(5, f_level);
			ps.setTimestamp(6, exp_date == null ? null : new Timestamp(exp_date.getTime()));
			SQLLogUtils.printSQL(ps);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean updateBaseCardForSaasSfAndReviewCnt(String batchId, String sf_code, int maual_review_cnt) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "update base_card set C_PROV = ?, review_maual_cnt = ? where C_DESC = ?" ;
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, sf_code);
			ps.setInt(2, maual_review_cnt);
			ps.setString(3, batchId);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean updateBaseCardWC(String c_id, int wc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "update base_card set C_SCORE = ?  where C_ID = ?" ;
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, wc);
			ps.setString(2, c_id);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean arrayGzdzCardForAgent(String agent_id, String batch_id, String desc, String cardIdPrefix, int count) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE gzdz_card x SET x.descp = ?, x.batch_id = ?, x.agent_id = ? WHERE x.kl IS NULL AND x.score < 10 AND x.descp IS NULL and c_id LIKE '"+cardIdPrefix+"%' ORDER BY rand () LIMIT " +count ;
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, desc);
			ps.setString(2, batch_id);
			ps.setString(3, agent_id);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public List<BaseCardSales> getBaseCardSalesInfo(String agent_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<BaseCardSales> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String agent_id_SQL = Tools.isEmpty(agent_id)? "1 = 1" : "agent_id = ?";
			String SQL = "SELECT * FROM base_card_sales x WHERE "+agent_id_SQL+" ORDER BY x.create_tm DESC LIMIT 0,50"; 
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			if(!Tools.isEmpty(agent_id)) {
				ps.setString(1, agent_id);
			}
			rs = ps.executeQuery();
			BaseCardSales bean = null;
			while (rs.next()) {
				bean = new BaseCardSales();
				bean.setAgent_id(rs.getString("agent_id"));
				bean.setBatch_id(rs.getString("batch_id"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				bean.setRemark(rs.getString("remark"));
				bean.setSale_cnt(rs.getInt("sale_cnt"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public boolean insertBaseCardSalesInfo(String batchId, String agentId, String desc, int count) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "INSERT INTO base_card_sales(batch_id, agent_id ,remark, sale_cnt, create_tm) VALUES(?, ?, ?, ?, now())" ;
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batchId);
			ps.setString(2, agentId);
			ps.setString(3, desc);
			ps.setInt(4, count);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}


	public boolean insertBlockCount(String cid, String action, String spec) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into zdks_block(FROM_ACTION, SPEC_VALUE, C_ID, CREATE_DT) values(?,?,?,NOW())";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, action);
			ps.setString(2, spec);
			ps.setString(3, cid);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean insertZdksConvertHistory(ZyzdZdksConvertHistory bean) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into zyzd_zdks_convert_history(batch_id, nf, sf, city, zdks_name, pcx_name, pcx_wl_zd, pcx_wl_gk, pcx_ls_zd, pcx_ls_gk, hxb, create_tm) values(?,?,?,?,?,?,?,?,?,?,?,NOW())";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, bean.getBatch_id());
			ps.setInt(2, bean.getNf());
			ps.setString(3, bean.getSf());
			ps.setString(4, bean.getCity());
			ps.setString(5, bean.getZdks_name());
			ps.setString(6, bean.getPcx_name());
			ps.setInt(7, bean.getPcx_wl_zd());
			ps.setInt(8, bean.getPcx_wl_gk());
			ps.setInt(9, bean.getPcx_ls_zd());
			ps.setInt(10, bean.getPcx_ls_gk());
			ps.setString(11, bean.getHxb());
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public HashMap<String, String> getAllZYMCbyJiuyeREQ() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, String> MAP = new HashMap<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT req_major_name FROM career_jy_job_req WHERE salary <> '面议' and display_ind = 1 group by req_major_name";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			while (rs.next()) {
				MAP.put(rs.getString("req_major_name"), rs.getString("req_major_name"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return MAP;
	}
	
	public List<MajorPlan> getKaoyanInfoByUniv(String yxmcOrg) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MajorPlan> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT school_name, special_name, special_code, degree_type_name, max(recruit_number) as plan_cnt, avg(data_patch_total_diff_2023) as avg from career_university_major_plan_2024 x WHERE x.school_name = ? and x.recruit_type_name = '全日制' group by school_name, special_name,special_code, degree_type_name order by avg(data_patch_total_diff_2023) desc limit 0, 15";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yxmcOrg);
			rs = ps.executeQuery();
			MajorPlan bean = null;
			while (rs.next()) {
				bean = new MajorPlan();
				bean.setSchool_name(rs.getString("school_name"));
				bean.setSpecial_name(rs.getString("special_name"));
				bean.setSpecial_code(rs.getString("special_code"));
				bean.setDegree_type_name(rs.getString("degree_type_name"));
				bean.setAvg_diff_total(rs.getInt("avg"));
				bean.setRecruit_number(String.valueOf(rs.getInt("plan_cnt")));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<MajorPlan> getKaoyanInfoByMajorResearchArea(String yxmcOrg) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MajorPlan> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT school_name, special_name, special_code, degree_type_name, research_area from career_university_major_plan_2024 x WHERE x.school_name = ? and x.recruit_type_name = '全日制'";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yxmcOrg);
			rs = ps.executeQuery();
			MajorPlan bean = null;
			while (rs.next()) {
				bean = new MajorPlan();
				bean.setSchool_name(rs.getString("school_name"));
				bean.setSpecial_name(rs.getString("special_name"));
				bean.setSpecial_code(rs.getString("special_code"));
				bean.setDegree_type_name(rs.getString("degree_type_name"));
				bean.setResearch_area(rs.getString("research_area"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<MajorPlan> getLiuXueMajorByUniv(String yxmcOrg) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MajorPlan> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.cmajr_news, COUNT(*) as cnt FROM career_lx_cases x where x.cuniv_news = ? group by x.cmajr_news ORDER BY COUNT(*) DESC LIMIT 0,10";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yxmcOrg);
			rs = ps.executeQuery();
			MajorPlan bean = null;
			while (rs.next()) {
				bean = new MajorPlan();
				bean.setSpecial_name(rs.getString("cmajr_news"));
				bean.setRecruit_number(String.valueOf(rs.getInt("cnt")));
				list.add(bean); 
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<MajorPlan> getLiuXueOverseaUnivByUniv(String yxmcOrg) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MajorPlan> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.euniv_news, COUNT(*) as cnt FROM career_lx_cases x where x.cuniv_news = ? group by x.euniv_news ORDER BY COUNT(*) DESC LIMIT 0,10";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yxmcOrg);
			rs = ps.executeQuery();
			MajorPlan bean = null;
			while (rs.next()) {
				bean = new MajorPlan();
				bean.setSchool_name(rs.getString("euniv_news"));
				bean.setRecruit_number(String.valueOf(rs.getInt("cnt")));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JiuyeBean> pickJiuyeSummaryBySchoolName(String school) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<JiuyeBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT group_name, lsy, COUNT(*) as ct from career_jy_all_2024 x WHERE x.yxmc LIKE ? GROUP BY group_name, lsy ORDER BY COUNT(*) DESC LIMIT 15";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + school + "%");
			rs = ps.executeQuery();
			JiuyeBean bean = null;
			while (rs.next()) {
				bean = new JiuyeBean();
				bean.setDw(rs.getString("lsy"));
				bean.setCnt(rs.getInt("ct"));
				bean.setSshy(rs.getString("group_name"));
				bean.setSsgs(rs.getString("lsy"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JiuyeBean> pickJiuyeSummaryByMajorName(String major) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<JiuyeBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT group_name, lsy, COUNT(*) as ct from career_jy_all_2024 x WHERE x.zymc LIKE ? GROUP BY group_name, lsy ORDER BY COUNT(*) DESC LIMIT 15";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + major + "%");
			rs = ps.executeQuery();
			JiuyeBean bean = null;
			while (rs.next()) {
				bean = new JiuyeBean();
				bean.setDw(rs.getString("lsy"));
				bean.setCnt(rs.getInt("ct"));
				bean.setSshy(rs.getString("group_name"));
				bean.setSsgs(rs.getString("lsy"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JiuyeBean> getAllGroupNameAndLsy(int nf) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<JiuyeBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT group_name, lsy, COUNT(*) as ct from career_jy_all_"+nf+" x GROUP BY group_name, lsy ORDER BY COUNT(*) DESC";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			JiuyeBean bean = null;
			while (rs.next()) {
				bean = new JiuyeBean();
				bean.setDw(rs.getString("lsy"));
				bean.setCnt(rs.getInt("ct"));
				bean.setSshy(rs.getString("group_name"));
				bean.setSsgs(rs.getString("lsy"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JiuyeBean> pickJiuyeSummaryByZyml(HashSet<String> zymlSets) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<JiuyeBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT group_name, lsy, COUNT(*) as ct from career_jy_all_2024 x WHERE x.zymc in (select m_zymc from zyzd_base_major where m_catg_two in ("+Tools.getSQLQueryin(zymlSets)+")) GROUP BY group_name, lsy ORDER BY COUNT(*) DESC LIMIT 15";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			JiuyeBean bean = null;
			while (rs.next()) {
				bean = new JiuyeBean();
				bean.setDw(rs.getString("lsy"));
				bean.setCnt(rs.getInt("ct"));
				bean.setSshy(rs.getString("group_name"));
				bean.setSsgs(rs.getString("lsy"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public HashMap<String, String> getAllZYCatgbyJiuyeREQ() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, String> MAP = new HashMap<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT req_major_catg FROM career_jy_job_req WHERE salary <> '面议' and display_ind = 1 group by req_major_catg";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			while (rs.next()) {
				MAP.put(rs.getString("req_major_catg"), rs.getString("req_major_catg"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return MAP;
	}
	
	public List<CareerJY> pickJiuyeYxByDW(String group_name, String lsy, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CareerJY> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT yxmc, COUNT(*) as ct FROM career_jy_all_2024 x WHERE x.group_name = ? AND x.lsy = ? GROUP BY x.yxmc ORDER BY COUNT(*) desc limit ?,?;";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, group_name);
			ps.setString(2, lsy);

			ps.setInt(3, (pageNumber - 1) * 10);
			ps.setInt(4, 10);
			rs = ps.executeQuery();
			CareerJY bean = null;
			while (rs.next()) {
				bean = new CareerJY();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setCnt(rs.getInt("ct"));
				list.add(bean);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<CareerJY> pickJiuyeYxByGroupAndLsy(String group_name_view, String lsy_view, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CareerJY> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			if(!Tools.isEmpty(lsy_view)) {
				String SQL = "SELECT yxmc, COUNT(*) as ct FROM career_jy_all_2024 x WHERE x.group_name_view = ? AND x.lsy_view = ? GROUP BY x.yxmc ORDER BY COUNT(*) desc limit ?,?;";
				Tools.println(SQL);
				ps = conn.prepareStatement(SQL);
				ps.setString(1, group_name_view);
				ps.setString(2, lsy_view);
				ps.setInt(3, (pageNumber - 1) * PAGE_ROW_CNT_SPEC);
				ps.setInt(4, PAGE_ROW_CNT_SPEC);
			}else {
				String SQL = "SELECT yxmc, COUNT(*) as ct FROM career_jy_all_2024 x WHERE x.group_name_view = ? GROUP BY x.yxmc ORDER BY COUNT(*) desc limit ?,?;";
				Tools.println(SQL);
				ps = conn.prepareStatement(SQL);
				ps.setString(1, group_name_view);
				ps.setInt(2, (pageNumber - 1) * PAGE_ROW_CNT_SPEC);
				ps.setInt(3, PAGE_ROW_CNT_SPEC);
			}
			rs = ps.executeQuery();
			CareerJY bean = null;
			while (rs.next()) {
				bean = new CareerJY();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setCnt(rs.getInt("ct"));
				list.add(bean);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<CareerJY> pickJiuyeYxByGroupAndLsy(int jynf, String group_name_view, String lsy_view, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CareerJY> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			if(!Tools.isEmpty(lsy_view)) {
				String SQL = "SELECT yxmc, COUNT(*) as ct FROM career_jy_all_"+jynf+" x WHERE x.group_name = ? AND x.lsy = ? GROUP BY x.yxmc ORDER BY COUNT(*) desc limit ?,?;";
				Tools.println(SQL);
				ps = conn.prepareStatement(SQL);
				ps.setString(1, group_name_view);
				ps.setString(2, lsy_view);
				ps.setInt(3, (pageNumber - 1) * PAGE_ROW_CNT_SPEC);
				ps.setInt(4, PAGE_ROW_CNT_SPEC);
			}else {
				String SQL = "SELECT yxmc, COUNT(*) as ct FROM career_jy_all_"+jynf+" x WHERE x.group_name = ? GROUP BY x.yxmc ORDER BY COUNT(*) desc limit ?,?;";
				Tools.println(SQL);
				ps = conn.prepareStatement(SQL);
				ps.setString(1, group_name_view);
				ps.setInt(2, (pageNumber - 1) * PAGE_ROW_CNT_SPEC);
				ps.setInt(3, PAGE_ROW_CNT_SPEC);
			}
			rs = ps.executeQuery();
			CareerJY bean = null;
			while (rs.next()) {
				bean = new CareerJY();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setCnt(rs.getInt("ct"));
				list.add(bean);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public HashMap<String, CareerJY> getScore(String sf, int nf, HashSet<String> yxmcSets, String xk_code) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, CareerJY> beanMap = new HashMap<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT yxmc_org, min(zdf) as zdf, max(zdfwc) as zdfwc from "+sf+"_"+nf+"x where yxmc_org in ("+Tools.getSQLQueryin(yxmcSets)+") and xk_code like ? group by yxmc_org";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%"+xk_code+"%");
			rs = ps.executeQuery();
			CareerJY bean = null;
			while (rs.next()) {
				bean = new CareerJY();
				bean.setExt_zdf(String.valueOf(rs.getInt("zdf")));
				bean.setExt_zdfwc(String.valueOf(rs.getInt("zdfwc")));
				bean.setYxmc(rs.getString("yxmc_org"));
				beanMap.put(bean.getYxmc(), bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return beanMap;
	}

	

	public HashMap<String, CareerJY> getRk(HashSet<String> yxmcSets) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, CareerJY> beanMap = new HashMap<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "select * from zyzd_base_rank_rk_major x WHERE x.yxmc in ("+Tools.getSQLQueryin(yxmcSets)+") ORDER BY x.yxmc, x.ranking";
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			CareerJY bean = null;
			while (rs.next()) {
				String yxmc = rs.getString("yxmc");
				if(!beanMap.containsKey(yxmc)) {
					bean = new CareerJY();
					bean.setExt_rk_no(rs.getString("ranking"));
					bean.setExt_rk_level(rs.getString("ranking_level"));
					bean.setExt_rk_zymc(rs.getString("zymc"));
					beanMap.put(yxmc, bean);
				}
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return beanMap;

	}
	
	//内部
	public List<CareerJY> pickJiuyeYxByGroupAndLsyForInternel(int nf, String group_name_view, String lsy_view, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CareerJY> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			if(!Tools.isEmpty(lsy_view)) {
				String SQL = "SELECT yxmc, COUNT(*) as ct FROM career_jy_all_"+nf+" x WHERE x.group_name = ? AND x.lsy = ? GROUP BY x.yxmc ORDER BY COUNT(*) desc limit ?,?;";
				Tools.println(SQL);
				ps = conn.prepareStatement(SQL);
				ps.setString(1, group_name_view);
				ps.setString(2, lsy_view);
				ps.setInt(3, (pageNumber - 1) * 30);
				ps.setInt(4, 30);
			}else {
				String SQL = "SELECT yxmc, COUNT(*) as ct FROM career_jy_all_"+nf+" x WHERE x.group_name = ? GROUP BY x.yxmc ORDER BY COUNT(*) desc limit ?,?;";
				Tools.println(SQL);
				ps = conn.prepareStatement(SQL);
				ps.setString(1, group_name_view);
				ps.setInt(2, (pageNumber - 1) * 30);
				ps.setInt(3, 30);
			}
			rs = ps.executeQuery();
			CareerJY bean = null;
			while (rs.next()) {
				bean = new CareerJY();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setCnt(rs.getInt("ct"));
				list.add(bean);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	//内部
	public List<CareerJY> pickJiuyeYxByGroupAndLsyForInternel_sum_xl(int nf, String group_name_view, String lsy_view) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CareerJY> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			if(!Tools.isEmpty(lsy_view)) {
				String SQL = "SELECT xl, COUNT(*) as ct FROM career_jy_all_"+nf+" x WHERE x.group_name = ? AND x.lsy = ? GROUP BY x.xl";
				Tools.println(SQL);
				ps = conn.prepareStatement(SQL);
				ps.setString(1, group_name_view);
				ps.setString(2, lsy_view);
			}else {
				String SQL = "SELECT xl, COUNT(*) as ct FROM career_jy_all_"+nf+" x WHERE x.group_name = ? GROUP BY x.xl";
				Tools.println(SQL);
				ps = conn.prepareStatement(SQL);
				ps.setString(1, group_name_view);
			}
			rs = ps.executeQuery();
			CareerJY bean = null;
			while (rs.next()) {
				bean = new CareerJY();
				bean.setXl(rs.getString("xl"));
				bean.setCnt(rs.getInt("ct"));
				list.add(bean);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	//内部
	public List<CareerJY> pickJiuyeYxByGroupAndLsyForInternel_sum_xb(int nf, String group_name_view, String lsy_view) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CareerJY> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			if(!Tools.isEmpty(lsy_view)) {
				String SQL = "SELECT xb, COUNT(*) as ct FROM career_jy_all_"+nf+" x WHERE x.group_name = ? AND x.lsy = ? GROUP BY x.xb";
				Tools.println(SQL);
				ps = conn.prepareStatement(SQL);
				ps.setString(1, group_name_view);
				ps.setString(2, lsy_view);
			}else {
				String SQL = "SELECT xb, COUNT(*) as ct FROM career_jy_all_"+nf+" x WHERE x.group_name = ? GROUP BY x.xb";
				Tools.println(SQL);
				ps = conn.prepareStatement(SQL);
				ps.setString(1, group_name_view);
			}
			rs = ps.executeQuery();
			CareerJY bean = null;
			while (rs.next()) {
				bean = new CareerJY();
				bean.setXb(rs.getString("xb"));
				bean.setCnt(rs.getInt("ct"));
				list.add(bean);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<CareerJY> pickJiuyeYxByGroupAndLsyForInternel_sum_src(int nf, String group_name_view, String lsy_view) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CareerJY> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			if(!Tools.isEmpty(lsy_view)) {
				String SQL = "SELECT src, COUNT(*) as ct FROM career_jy_all_"+nf+" x WHERE x.group_name = ? AND x.lsy = ? GROUP BY x.src";
				Tools.println(SQL);
				ps = conn.prepareStatement(SQL);
				ps.setString(1, group_name_view);
				ps.setString(2, lsy_view);
			}else {
				String SQL = "SELECT src, COUNT(*) as ct FROM career_jy_all_"+nf+" x WHERE x.group_name = ? GROUP BY x.src";
				Tools.println(SQL);
				ps = conn.prepareStatement(SQL);
				ps.setString(1, group_name_view);
			}
			rs = ps.executeQuery();
			CareerJY bean = null;
			while (rs.next()) {
				bean = new CareerJY();
				bean.setSrc(rs.getString("src"));
				bean.setCnt(rs.getInt("ct"));
				list.add(bean);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<CareerJY> pickJiuyeYxByGroupAndLsyForInternel_sum_groupname(int nf, String group_name_view) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CareerJY> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT lsy, COUNT(*) as ct FROM career_jy_all_"+nf+" x WHERE x.group_name = ? GROUP BY x.lsy order by count(*) desc";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, group_name_view);
			rs = ps.executeQuery();
			CareerJY bean = null;
			while (rs.next()) {
				bean = new CareerJY();
				bean.setLsy(rs.getString("lsy"));
				bean.setCnt(rs.getInt("ct"));
				list.add(bean);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<CareerJY> pickJiuyeZyByGroupAndLsyForInternel(int nf, String group_name_view, String lsy_view, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CareerJY> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			if(!Tools.isEmpty(lsy_view)) {
				String SQL = "SELECT zymc, COUNT(*) as ct FROM career_jy_all_"+nf+" x WHERE x.group_name = ? AND x.lsy = ? GROUP BY x.zymc ORDER BY COUNT(*) desc limit ?,?;";
				Tools.println(SQL);
				ps = conn.prepareStatement(SQL);
				ps.setString(1, group_name_view);
				ps.setString(2, lsy_view);
				ps.setInt(3, (pageNumber - 1) * 30);
				ps.setInt(4, 30);
			}else {
				String SQL = "SELECT zymc, COUNT(*) as ct FROM career_jy_all_"+nf+" x WHERE x.group_name = ? GROUP BY x.zymc ORDER BY COUNT(*) desc limit ?,?;";
				Tools.println(SQL);
				ps = conn.prepareStatement(SQL);
				ps.setString(1, group_name_view);
				ps.setInt(2, (pageNumber - 1) * 30);
				ps.setInt(3, 30);
			}
			rs = ps.executeQuery();
			CareerJY bean = null;
			while (rs.next()) {
				bean = new CareerJY();
				bean.setZymc(rs.getString("zymc"));
				bean.setCnt(rs.getInt("ct"));
				list.add(bean);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<CareerJY> getJiuyeLsyByGroup(String group_name_view) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CareerJY> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT lsy_view, SUM(emp_cnt) as ct FROM career_jy_all_2024 x WHERE x.group_name_view = ? GROUP BY lsy_view having COUNT(*) > 20 ORDER BY COUNT(*) DESC ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL); 
			ps.setString(1, group_name_view);
			rs = ps.executeQuery();
			CareerJY bean = null;
			while (rs.next()) {
				bean = new CareerJY();
				bean.setGroup_name_view(group_name_view);
				bean.setLsy_view(rs.getString("lsy_view"));
				bean.setCnt(rs.getInt("ct"));
				list.add(bean);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<CareerJY> getJiuyeYxmcByGroup(int jhYear, String sfTable, String group_name_view, String pc, String pc_code, String xk_code, int studentWC, int limit) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CareerJY> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.yxmc FROM career_jy_all_2024 x left join "+sfTable+"_JH_"+jhYear+" y ON x.yxmc = y.yxmc_org AND y.zdfwc_2024 < ? WHERE y.pc = ? and y.pc_code = ? and y.xk_code like ? and x.group_name_view = ? GROUP BY x.yxmc ORDER BY SUM(emp_cnt) DESC LIMIT "+limit;
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL); 
			ps.setInt(1, studentWC);
			ps.setString(2, pc);
			ps.setString(3, pc_code);
			ps.setString(4, "%"+xk_code+"%");
			ps.setString(5, group_name_view);
			rs = ps.executeQuery();
			CareerJY bean = null;
			while (rs.next()) {
				bean = new CareerJY();
				bean.setGroup_name_view(group_name_view);
				bean.setYxmc(rs.getString("yxmc"));
				list.add(bean);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<CareerJY> getJiuyeZymcByGroup(String group_name_view, int limit) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CareerJY> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT zymc, SUM(emp_cnt) as ct FROM career_jy_all_2024 x WHERE x.group_name_view = ? GROUP BY zymc ORDER BY COUNT(*) DESC LIMIT "+limit;
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL); 
			ps.setString(1, group_name_view);
			rs = ps.executeQuery();
			CareerJY bean = null;
			while (rs.next()) {
				bean = new CareerJY();
				bean.setGroup_name_view(group_name_view);
				bean.setZymc(rs.getString("zymc"));
				bean.setCnt(rs.getInt("ct"));
				list.add(bean);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public ZDKSRank getZDKSNearestScoreByWC(int year, String sf, String kl, int givingWC) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		ZDKSRank bean = null;
		try {
			ZyzdProvince zyzdProvince = ZyzdCache.getUserCardProvinceName(sf);
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+zyzdProvince.getTableName()+"_zdks_rank  WHERE nf = ? and sf = ? and kl_code like ? and WC >= ? order by WC ASC LIMIT 1";
			Tools.printlnRanking(SQL+","+year+","+kl+","+givingWC);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, year);
			ps.setString(2, sf);
			ps.setString(3, "%"+kl+"%");
			ps.setInt(4, givingWC);
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = new ZDKSRank();
				bean.setCnt(rs.getInt("cnt"));
				bean.setFd(rs.getString("fd"));
				bean.setKl(rs.getString("kl"));
				bean.setNf(rs.getInt("nf"));
				bean.setScore(rs.getInt("SCORE"));
				bean.setSf(rs.getString("sf"));
				bean.setWc(rs.getInt("wc"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}
	
	
	public List<ZDKSRank> getAllZdksRankByKL(int year, String sfName, String kl) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZDKSRank> list = new ArrayList<>();
		try {
			ZyzdProvince zyzdProvince = ZyzdCache.getUserCardProvinceName(sfName);
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+zyzdProvince.getTableName()+"_zdks_rank WHERE nf = ? and sf = ? and kl_code like ? order by WC ASC";
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, year);
			ps.setString(2, sfName);
			ps.setString(3, "%"+kl+"%");
			SQLLogUtils.printSQL(ps);
			rs = ps.executeQuery();
			ZDKSRank bean = null;
			while (rs.next()) {
				bean = new ZDKSRank();
				bean.setCnt(rs.getInt("cnt"));
				bean.setFd(rs.getString("fd"));
				bean.setKl(rs.getString("kl"));
				bean.setNf(rs.getInt("nf"));
				bean.setScore(rs.getInt("SCORE"));
				bean.setSf(rs.getString("sf"));
				bean.setWc(rs.getInt("wc"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	public void addAssessResult(ZyzdAssessResult assessBean) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into zyzd_assess_result(assess_name, c_id, create_tm, assess_result, assess_from, id) VALUES(?,?,now(),?, ?, ?)";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, assessBean.getAssess_name());
			ps.setString(2, assessBean.getC_id());
			ps.setString(3, assessBean.getAssess_result());
			ps.setString(4, assessBean.getAssess_from());
			ps.setString(5, assessBean.getId());
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public List<ZyzdAssessQuestion> getAssessQuestionByName(String assessName) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdAssessQuestion> beanList = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_assess_question WHERE assess_name = ? order by sort ASC";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, assessName);
			rs = ps.executeQuery();
			ZyzdAssessQuestion bean = null;
			while (rs.next()) {
				bean = new ZyzdAssessQuestion();
				bean.setAssess_name(rs.getString("assess_name"));
				bean.setDescp(rs.getString("descp"));
				bean.setQ_no(rs.getInt("q_no"));
				bean.setSort(rs.getInt("sort"));
				bean.setView_no(rs.getString("view_no"));
				bean.setClassify(rs.getString("classify"));
				bean.setAnswer_response(rs.getString("answer_response"));
				beanList.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return beanList;
	}
	
	public List<ZyzdAssessQuestionOption> getAssessQuestionOptionByName(String assessName) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdAssessQuestionOption> beanList = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_assess_question_option WHERE assess_name = ? order by q_no, sort ASC";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, assessName);
			rs = ps.executeQuery();
			ZyzdAssessQuestionOption bean = null;
			while (rs.next()) {
				bean = new ZyzdAssessQuestionOption();
				bean.setAssess_name(rs.getString("assess_name"));
				bean.setOption_name(rs.getString("option_name"));
				bean.setOption_result(rs.getString("option_result"));
				bean.setQ_no(rs.getInt("q_no"));
				bean.setSort(rs.getInt("sort"));
				
				beanList.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return beanList;
	}
	
	public ZyzdCpMain getZyzdCpMainById(String cp_m_id) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		ZyzdCpMain bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_cp_main WHERE m_id = ? order by sort ASC";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cp_m_id);
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = new ZyzdCpMain();
				bean.setM_id(rs.getString("m_id"));
				bean.setM_name(rs.getString("m_name"));
				bean.setDescp(rs.getString("descp"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}
	
	private static ZyzdCpItem setZyzdCpItem(ResultSet rs) throws SQLException {
		ZyzdCpItem bean = new ZyzdCpItem();
		bean.setId(rs.getInt("id"));
		bean.setM_id(rs.getString("m_id"));
		bean.setItem_no(rs.getInt("item_no"));
		bean.setItem_name(rs.getString("item_name"));
		bean.setGroup_no(rs.getInt("group_no"));
		bean.setGroup_name(rs.getString("group_name"));
		bean.setGroup_sub_no(rs.getInt("group_sub_no"));
		bean.setGroup_sub_name(rs.getString("group_sub_name"));
		bean.setItem_type(rs.getInt("item_type"));
		bean.setItem_option_a(rs.getString("item_option_a"));
		bean.setItem_option_b(rs.getString("item_option_b"));
		bean.setItem_option_c(rs.getString("item_option_c"));
		bean.setItem_option_d(rs.getString("item_option_d"));
		bean.setItem_option_e(rs.getString("item_option_e"));
		bean.setItem_score_a(rs.getFloat("item_score_a"));
		bean.setItem_score_b(rs.getFloat("item_score_b"));
		bean.setItem_score_c(rs.getFloat("item_score_c"));
		bean.setItem_score_d(rs.getFloat("item_score_d"));
		bean.setItem_score_e(rs.getFloat("item_score_e"));
		return bean;
	}
	
	private static ZyzdCpResultItem setZyzdCpResultItem(ResultSet rs) throws SQLException {
		ZyzdCpResultItem bean = new ZyzdCpResultItem();
		bean.setId(rs.getInt("id"));
		bean.setItem_no(rs.getInt("item_no"));
		bean.setM_id(rs.getString("m_id"));
		bean.setRm_id(rs.getString("rm_id"));
		bean.setItem_name(rs.getString("item_name"));
		bean.setItem_selected(rs.getString("item_selected"));
		bean.setItem_result(rs.getFloat("item_result"));
		bean.setGroup_no(rs.getInt("group_no"));
		bean.setGroup_name(rs.getString("group_name"));
		bean.setGroup_sub_no(rs.getInt("group_sub_no"));
		bean.setGroup_sub_name(rs.getString("group_sub_name"));
		return bean;
	}
	
	public List<ZyzdCpItem> getZyzdCpItemsById(String cp_m_id) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdCpItem> beanList = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_cp_item WHERE m_id = ? and status = 1 order by group_no,item_no";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cp_m_id);
			rs = ps.executeQuery();
			while (rs.next()) {
				beanList.add(setZyzdCpItem(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return beanList;
	}
	
	public void insertCpItemResult(List<ZyzdCpResultItem> list) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into zyzd_cp_result_item(item_no,item_name, m_id, item_selected, item_result, rm_id, group_no, group_name, group_sub_no, group_sub_name) values(?,?,?,?,?,?,?,?,?,?)";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			for(ZyzdCpResultItem item : list) {
				ps.setInt(1, item.getItem_no());
				ps.setString(2, item.getItem_name());
				ps.setString(3, item.getM_id());
				ps.setString(4, item.getItem_selected());
				ps.setFloat(5, item.getItem_result());
				ps.setString(6, item.getRm_id());
				ps.setInt(7, item.getGroup_no());
				ps.setString(8, item.getGroup_name());
				ps.setInt(9, item.getGroup_sub_no());
				ps.setString(10, item.getGroup_sub_name());
				ps.addBatch();
			}
			
			ps.executeBatch();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void insertCpMainResult(ZyzdCpResultMain main) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into zyzd_cp_result_main(rm_id, c_id, m_id, m_name, descp, score_total, m_type, stu_xk, stu_xm, stu_yxmc, stu_nj, stu_cj_yw, stu_cj_sx, stu_cj_wy, stu_cj_xk_a, stu_cj_xk_b, stu_cj_xk_c, create_tm) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now())";
			ps = conn.prepareStatement(SQL);
			int index = 1;
			ps.setString(index++, main.getRm_id());
			ps.setString(index++, main.getC_id());
			ps.setString(index++, main.getM_id());
			ps.setString(index++, main.getM_name());
			ps.setString(index++, main.getDescp());
			ps.setFloat(index++, main.getScore_total());
			ps.setInt(index++, main.getM_type());
			ps.setString(index++, main.getStu_xk());
			ps.setString(index++, main.getStu_xm());
			ps.setString(index++, main.getStu_yxmc());
			ps.setString(index++, main.getStu_nj());
			ps.setInt(index++, main.getStu_cj_yw());
			ps.setInt(index++, main.getStu_cj_sx());
			ps.setInt(index++, main.getStu_cj_wy());
			ps.setInt(index++, main.getStu_cj_xk_a());
			ps.setInt(index++, main.getStu_cj_xk_b());
			ps.setInt(index++, main.getStu_cj_xk_c());
			
			ps.executeUpdate();
			
			SQLLogUtils.printSQL(" === insertCpMainResult():  ", ps); 
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public List<ZyzdCpResultMain> getZyzdCpResultMainByUser(String c_id, int m_type) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdCpResultMain> beanList = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_cp_result_main WHERE c_id = ? and m_type = ? order by create_tm DESC LIMIT 3";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, c_id);
			ps.setInt(2, m_type);
			rs = ps.executeQuery();
			
	        SQLLogUtils.printSQL(" === getZyzdCpResultMainByUser():  ", ps); 
	        
			ZyzdCpResultMain bean = null;
			while (rs.next()) {
				bean = new ZyzdCpResultMain();
				bean.setRm_id(rs.getString("rm_id"));
				bean.setM_id(rs.getString("m_id"));
				bean.setM_name(rs.getString("m_name"));
				bean.setDescp(rs.getString("descp"));
				bean.setC_id(rs.getString("c_id"));
				bean.setScore_total(rs.getFloat("score_total"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				bean.setM_type(rs.getInt("m_type"));
				
				// Added by fresh
				bean.setStu_xk(rs.getString("stu_xk"));
				bean.setStu_xm(rs.getString("stu_xm"));
				bean.setStu_yxmc(rs.getString("stu_yxmc"));
				bean.setStu_nj(rs.getString("stu_nj"));
				bean.setStu_cj_yw(rs.getInt("stu_cj_yw"));
				bean.setStu_cj_sx(rs.getInt("stu_cj_sx"));
				bean.setStu_cj_wy(rs.getInt("stu_cj_wy"));
				bean.setStu_cj_xk_a(rs.getInt("stu_cj_xk_a"));
				bean.setStu_cj_xk_b(rs.getInt("stu_cj_xk_b"));
				bean.setStu_cj_xk_c(rs.getInt("stu_cj_xk_c"));

				beanList.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return beanList;
	}
	
	public List<ZyzdCpResultItem> getZyzdCpResultItemByRmids(HashSet<String> rmids) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdCpResultItem> beanList = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_cp_result_item WHERE rm_id in ("+Tools.getSQLQueryin(rmids)+") order by create_tm ASC";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			ZyzdCpResultItem bean = null;
			while (rs.next()) {
				bean = new ZyzdCpResultItem();
				bean.setId(rs.getInt("id"));
				bean.setItem_no(rs.getInt("item_no"));
				bean.setM_id(rs.getString("m_id"));
				bean.setRm_id(rs.getString("rm_id"));
				bean.setItem_name(rs.getString("item_name"));
				bean.setItem_selected(rs.getString("item_selected"));
				bean.setItem_result(rs.getFloat("item_result"));
				bean.setGroup_no(rs.getInt("group_no"));
				bean.setGroup_name(rs.getString("group_name"));

				beanList.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return beanList;
	}
	
	public List<ZyzdCpResultItem> getZyzdCpResultItemByUser(String c_id, int m_type) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdCpResultItem> beanList = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_cp_result_item WHERE rm_id in (select rm_id from zyzd_cp_result_main where c_id = ? and m_type = ?) order by create_tm ASC";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, c_id);
			ps.setInt(2, m_type);
			rs = ps.executeQuery();
			ZyzdCpResultItem bean = null;
			while (rs.next()) {
				bean = new ZyzdCpResultItem();
				bean.setId(rs.getInt("id"));
				bean.setItem_no(rs.getInt("item_no"));
				bean.setM_id(rs.getString("m_id"));
				bean.setRm_id(rs.getString("rm_id"));
				bean.setItem_name(rs.getString("item_name"));
				bean.setItem_selected(rs.getString("item_selected"));
				bean.setItem_result(rs.getFloat("item_result"));
				bean.setGroup_no(rs.getInt("group_no"));
				bean.setGroup_name(rs.getString("group_name"));

				beanList.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return beanList;
	}
	
	public List<ZyzdCpResultItem> getZyzdCpResultItemByRmid(String rmid) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdCpResultItem> beanList = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_cp_result_item WHERE rm_id = ? order by group_no,item_no";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, rmid);
			rs = ps.executeQuery();
			while (rs.next()) {
				beanList.add(setZyzdCpResultItem(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return beanList;
	}
	
	public List<ZyzdCpResultItem> getZyzdCpResultItemGroupAvgByRmid(String rmid) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdCpResultItem> beanList = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT group_no,group_name, ROUND(avg(item_result),3) as ext_avg FROM zyzd_cp_result_item WHERE rm_id = ? GROUP BY group_no,group_name order by group_no";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, rmid);
			rs = ps.executeQuery();
			ZyzdCpResultItem bean = null;
			while (rs.next()) {
				bean = new ZyzdCpResultItem();
				bean.setGroup_no(rs.getInt("group_no"));
				bean.setGroup_name(rs.getString("group_name"));
				bean.setExt_avg(rs.getFloat("ext_avg"));

				beanList.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return beanList;
	}
	
	public List<ZyzdCpResultItem> getZyzdCpResultItemGroupSubAvgByRmid(String rmid) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdCpResultItem> beanList = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT group_no,group_name,group_sub_name, ROUND(avg(item_result),3) as ext_avg FROM zyzd_cp_result_item WHERE rm_id = ? GROUP BY group_no,group_name,group_sub_name order by group_no";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, rmid);
			rs = ps.executeQuery();
			ZyzdCpResultItem bean = null;
			while (rs.next()) {
				bean = new ZyzdCpResultItem();
				bean.setGroup_no(rs.getInt("group_no"));
				bean.setGroup_name(rs.getString("group_name"));
				bean.setGroup_sub_name(rs.getString("group_sub_name"));
				bean.setExt_avg(rs.getFloat("ext_avg"));

				beanList.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return beanList;
	}
	
	
	public List<ZyzdAssessDescribeType> getAssessDescribeTypeByAssessName(String assess_name) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdAssessDescribeType> beanList = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_assess_describe_type WHERE assess_name = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, assess_name);
			rs = ps.executeQuery();
			ZyzdAssessDescribeType bean = null;
			while (rs.next()) {
				bean = new ZyzdAssessDescribeType();
				bean.setAssess_name(rs.getString("assess_name"));
				bean.setA_type(rs.getString("a_type"));
				bean.setA_name(rs.getString("a_name"));
				bean.setAnalysis(rs.getString("analysis"));
				bean.setCommon_feature(rs.getString("common_feature"));
				bean.setOccupation_feature(rs.getString("occupation_feature"));
				bean.setId(rs.getInt("id"));

				beanList.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return beanList;
	}
	
	public ZyzdAssessDescribeType getAssessDescribeTypeByAssessNameAndType(String assess_name, String a_type) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdAssessDescribeType> beanList = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_assess_describe_type WHERE assess_name = ? and a_type = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, assess_name);
			ps.setString(2, a_type);
			rs = ps.executeQuery();
			ZyzdAssessDescribeType bean = null;
			while (rs.next()) {
				bean = new ZyzdAssessDescribeType();
				bean.setAssess_name(rs.getString("assess_name"));
				bean.setA_type(rs.getString("a_type"));
				bean.setA_name(rs.getString("a_name"));
				bean.setAnalysis(rs.getString("analysis"));
				bean.setCommon_feature(rs.getString("common_feature"));
				bean.setOccupation_feature(rs.getString("occupation_feature"));
				bean.setId(rs.getInt("id"));

				return bean;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public List<ZyzdAssessDescribeType> getAssessDescribeTypeByTid(String t_id) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdAssessDescribeType> beanList = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_assess_describe_type WHERE assess_name = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, t_id);
			rs = ps.executeQuery();
			ZyzdAssessDescribeType bean = null;
			while (rs.next()) {
				bean = new ZyzdAssessDescribeType();
				bean.setAssess_name(rs.getString("assess_name"));
				bean.setA_type(rs.getString("a_type"));
				bean.setA_name(rs.getString("a_name"));
				bean.setAnalysis(rs.getString("analysis"));
				bean.setCommon_feature(rs.getString("common_feature"));
				bean.setOccupation_feature(rs.getString("occupation_feature"));
				bean.setId(rs.getInt("id"));

				beanList.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return beanList;
	}
	
	public List<ZyzdAssessResult> getAssessByCid(String assessName, String c_id, int limitCnt) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdAssessResult> beanList = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_assess_result WHERE assess_name = ? and c_id = ? order by create_tm DESC LIMIT " + limitCnt;
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, assessName);
			ps.setString(2, c_id);
			rs = ps.executeQuery();
			ZyzdAssessResult bean = null;
			while (rs.next()) {
				bean = new ZyzdAssessResult();
				bean.setAssess_name(rs.getString("assess_name"));
				bean.setC_id(rs.getString("c_id"));
				bean.setAssess_result(rs.getString("assess_result"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				bean.setId(rs.getString("id"));
				beanList.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return beanList;
	}
	
	public ZyzdAssessResult getAssessById(String id) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_assess_result WHERE id = ? ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, id);
			rs = ps.executeQuery();
			ZyzdAssessResult bean = null;
			while (rs.next()) {
				bean = new ZyzdAssessResult();
				bean.setAssess_name(rs.getString("assess_name"));
				bean.setC_id(rs.getString("c_id"));
				bean.setAssess_result(rs.getString("assess_result"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				bean.setId(rs.getString("id"));
				return bean;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	
	public List<ZyzdAssessResult> getLhyAssessByCid(String assessName, String c_id, int limitCnt) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdAssessResult> beanList = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_assess_result WHERE assess_name = ? and c_id = ? and assess_from = 'LHY' order by create_tm DESC LIMIT " + limitCnt;
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, assessName);
			ps.setString(2, c_id);
			rs = ps.executeQuery();
			ZyzdAssessResult bean = null;
			while (rs.next()) {
				bean = new ZyzdAssessResult();
				bean.setAssess_name(rs.getString("assess_name"));
				bean.setC_id(rs.getString("c_id"));
				bean.setId(rs.getString("id"));
				bean.setAssess_result(rs.getString("assess_result"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				beanList.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return beanList;
	}
	
	public ZyzdAssessDescribe getAssessHollandResultByCode(String assessName, String result_code) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_assess_describe WHERE assess_name = ? and result_code = ? ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, assessName);
			ps.setString(2, result_code);
			rs = ps.executeQuery();
			ZyzdAssessDescribe bean = null;
			while (rs.next()) {
				bean = new ZyzdAssessDescribe();
				bean.setAssess_name(rs.getString("assess_name"));
				bean.setResult_code(rs.getString("result_code"));
				bean.setResult_name(rs.getString("result_name"));
				bean.setResult_occupation(rs.getString("result_occupation"));
				return bean;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public List<ZyzdAssessDescribeMajor> getAssessHollandResultMajorByCode(String assessName, String result_code) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdAssessDescribeMajor> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_assess_describe_major WHERE assess_name = ? and result_code = ? ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, assessName);
			ps.setString(2, result_code);
			rs = ps.executeQuery();
			ZyzdAssessDescribeMajor bean = null;
			while (rs.next()) {
				bean = new ZyzdAssessDescribeMajor();
				bean.setAssess_name(rs.getString("assess_name"));
				bean.setResult_code(rs.getString("result_code"));
				bean.setCareer_direction(rs.getString("career_direction"));
				bean.setMain_course(rs.getString("main_course"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZy_desc(rs.getString("zy_desc"));
				bean.setXk_dl(rs.getInt("xk_dl"));
				bean.setXk_hx(rs.getInt("xk_hx"));
				bean.setXk_ls(rs.getInt("xk_ls"));
				bean.setXk_sw(rs.getInt("xk_sw"));
				bean.setXk_wl(rs.getInt("xk_wl"));
				bean.setXk_zz(rs.getInt("xk_zz"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<SchoolQJJHBean> getQJbyprovinceAndYxmc(String province, String yxmc, String xk_code) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolQJJHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT *  FROM base_qjjh WHERE sf = ? and yxmc = ? and xk_code like ? order by zymc, nf desc, rwfs desc";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, province);
			ps.setString(2, yxmc);
			ps.setString(3, "%" + xk_code + "%");
			rs = ps.executeQuery();
			SchoolQJJHBean bean = null;
			while (rs.next()) {
				bean = new SchoolQJJHBean();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZymc(rs.getString("zymc"));
				bean.setNf(rs.getString("nf"));
				bean.setPcx(rs.getString("pcx"));
				bean.setRwfs(rs.getString("rwfs"));
				bean.setSf(rs.getString("sf"));
				bean.setXc(rs.getString("xc"));
				bean.setXk(rs.getString("xk"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public List<ZyzdUniversityBean> listYxBaoyanBySf(String sf) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdUniversityBean> list = new ArrayList<>();
		int recordCnt = 0;
		try {
			String SQL = "SELECT * FROM zyzd_base_university x WHERE x.position_sf = ? and (cnt_grad is not null and cnt_grad > 0) order by cnt_grad desc";
			Tools.println(SQL);
			conn = DatabaseUtils.getConnection();
			ps = conn.prepareStatement(SQL);
			ps.setString(1, sf);
			rs = ps.executeQuery();
			ZyzdUniversityBean bean = null;
			while (rs.next()) {
				bean = new ZyzdUniversityBean();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setCnt_laboratory(rs.getString("cnt_laboratory"));
				bean.setCnt_landarea(rs.getString("cnt_landarea"));
				bean.setCnt_master(rs.getString("cnt_master"));
				bean.setCnt_phd(rs.getString("cnt_phd"));
				bean.setInd_catg(rs.getString("ind_catg"));
				bean.setInd_nature(rs.getString("ind_nature"));
				bean.setInd_type(rs.getString("ind_type"));
				bean.setInfo_addr(rs.getString("info_addr"));
				bean.setInfo_descp(rs.getString("info_descp"));
				bean.setInfo_found_time(rs.getString("info_found_time"));
				bean.setInfo_logo(rs.getString("info_logo"));
				bean.setInfo_tel(rs.getString("info_tel"));
				bean.setInfo_website(rs.getString("info_website"));
				bean.setIs211(rs.getString("is211"));
				bean.setIs985(rs.getString("is985"));
				bean.setIsqj(rs.getString("isqj"));
				bean.setIssyl(rs.getString("issyl"));
				bean.setLsy(rs.getString("lsy"));
				bean.setPosition_cs(rs.getString("position_cs"));
				bean.setPosition_qy(rs.getString("position_qy"));
				bean.setPosition_sf(rs.getString("position_sf"));
				bean.setRanking_qs(rs.getString("ranking_qs"));
				bean.setRanking_rk(rs.getString("ranking_rk"));
				bean.setRanking_usnews(rs.getString("ranking_usnews"));
				bean.setRanking_wsl(rs.getString("ranking_wsl"));
				bean.setRanking_xyh(rs.getString("ranking_xyh"));
				bean.setYxmc_used(rs.getString("yxmc_used"));
				bean.setCnt_grad(rs.getFloat("cnt_grad"));
				bean.setCnt_company(rs.getInt("cnt_company"));
				bean.setCnt_employ(rs.getInt("cnt_employ"));
				list.add(bean);
			}
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<BaoyanBean> listMajorBaoyanBySf(String sf, int nf) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<BaoyanBean> list = new ArrayList<>();
		try {
			String SQL = "SELECT * FROM base_major_baoyan x WHERE sf = ?  and nf like ? order by xymc,zymc";
			Tools.println(SQL);
			conn = DatabaseUtils.getConnection();
			ps = conn.prepareStatement(SQL);
			ps.setString(1, sf);
			ps.setString(2, nf + "%");
			rs = ps.executeQuery();
			BaoyanBean bean = null;
			while (rs.next()) {
				bean = new BaoyanBean();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setQxbyrs(rs.getString("qxbyrs"));
				bean.setQxbyzs(rs.getString("qxbyzs"));
				bean.setQxbybl(rs.getString("qxbybl"));
				bean.setXymc(rs.getString("xymc"));
				bean.setXybyrs(rs.getString("xybyrs"));
				bean.setXybyzs(rs.getString("xybyzs"));
				bean.setXybybl(rs.getString("xybybl"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZybyrs(rs.getString("zybyrs"));
				bean.setZybyzs(rs.getString("zybyzs"));
				bean.setZybybl(rs.getString("zybybl"));
				bean.setSf(rs.getString("sf"));
				bean.setNf(rs.getString("nf"));
				list.add(bean);
			}
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdMajorCatgRelated> getZymlRelated(String m_catg_two) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdMajorCatgRelated> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "select * from zyzd_base_major_related x where x.m_catg_two = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, m_catg_two);
			rs = ps.executeQuery();
			ZyzdMajorCatgRelated bean = null;
			while (rs.next()) {
				bean = new ZyzdMajorCatgRelated();
				bean.setM_catg_one(rs.getString("m_catg_one"));
				bean.setM_catg_two(rs.getString("m_catg_two"));
				bean.setM_kl(rs.getString("m_kl"));
				bean.setRelated_m_catg_one(rs.getString("related_m_catg_one"));
				bean.setRelated_m_catg_two(rs.getString("related_m_catg_two"));
				
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdMajorCatgRelated> getZymlRelated_in(HashSet<String> m_catg_two) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdMajorCatgRelated> list = new ArrayList<>();
		try {
			if(m_catg_two.size() == 0) {
				return list;
			}
			conn = DatabaseUtils.getConnection();
			String SQL = "select * from zyzd_base_major_related x where x.m_catg_two in (" + Tools.getSQLQueryin(m_catg_two) + ")";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			ZyzdMajorCatgRelated bean = null;
			while (rs.next()) {
				bean = new ZyzdMajorCatgRelated();
				bean.setM_catg_one(rs.getString("m_catg_one"));
				bean.setM_catg_two(rs.getString("m_catg_two"));
				bean.setM_kl(rs.getString("m_kl"));
				bean.setRelated_m_catg_one(rs.getString("related_m_catg_one"));
				bean.setRelated_m_catg_two(rs.getString("related_m_catg_two"));
				
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	//查询专业名称对应的专业类
	public HashSet<String> getZymcBelongCatgTwo_in(HashSet<String> zymc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashSet<String> list = new HashSet<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "select distinct m_catg_two from zyzd_base_major x where x.m_zymc in (" + Tools.getSQLQueryin(zymc) + ")";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(rs.getString("m_catg_two"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdMajorCatgRelated> getZymlRelated_All() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdMajorCatgRelated> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "select * from zyzd_base_major_related x ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			ZyzdMajorCatgRelated bean = null;
			while (rs.next()) {
				bean = new ZyzdMajorCatgRelated();
				bean.setM_catg_one(rs.getString("m_catg_one"));
				bean.setM_catg_two(rs.getString("m_catg_two"));
				bean.setM_kl(rs.getString("m_kl"));
				bean.setRelated_m_catg_one(rs.getString("related_m_catg_one"));
				bean.setRelated_m_catg_two(rs.getString("related_m_catg_two"));
				
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ScoreHistory> getScoreHistory(String sfCode, String c_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ScoreHistory> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "select * from "+sfCode+"_score_history x where x.c_id = ? and x.status = 1 order by create_tm desc";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, c_id);
			rs = ps.executeQuery();
			ScoreHistory bean = null;
			while (rs.next()) {
				bean = new ScoreHistory();
				bean.setC_id(rs.getString("c_id"));
				bean.setScore_from(rs.getString("score_from"));
				bean.setXk(rs.getString("xk"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				bean.setId(rs.getInt("id"));
				bean.setS_sum(rs.getInt("s_sum"));
				bean.setS_yw(rs.getInt("s_yw"));
				bean.setS_sx(rs.getInt("s_sx"));
				bean.setS_yy(rs.getInt("s_yy"));
				bean.setS_xk_a(rs.getInt("s_xk_a"));
				bean.setS_xk_b(rs.getInt("s_xk_b"));
				bean.setS_xk_c(rs.getInt("s_xk_c"));
				bean.setStatus(rs.getInt("status"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public void addScoreHistory(String sfCode, ScoreHistory bean) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into "+sfCode+"_score_history(xk, c_id, score_from, create_tm, s_sum, s_yw, s_sx, s_yy, s_xk_a, s_xk_b, s_xk_c, status) VALUES(?,?,?,now(),?,?,?,?,?,?,?,1)";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			int index = 1;
			ps.setString(index++, bean.getXk());
			ps.setString(index++, bean.getC_id());
			ps.setString(index++, bean.getScore_from());
			ps.setInt(index++, bean.getS_sum());
			ps.setInt(index++, bean.getS_yw());
			ps.setInt(index++, bean.getS_sx());
			ps.setInt(index++, bean.getS_yy());
			ps.setInt(index++, bean.getS_xk_a());
			ps.setInt(index++, bean.getS_xk_b());
			ps.setInt(index++, bean.getS_xk_c());
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void deleteScoreHistory(String sfCode, String c_id, int id) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "update "+sfCode+"_score_history set status = 2 where id = ? and c_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, id);
			ps.setString(2, c_id);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public List<String> getAllVocationCatgOne() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<String> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.p_name as p_name FROM zyzd_career_vocation x WHERE x.view_status = 1 GROUP BY x.p_name";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(rs.getString("p_name"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<CareerVocation> getAllVocation() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CareerVocation> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_career_vocation x WHERE x.view_status = 1";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			CareerVocation bean = null;
			while (rs.next()) {
				bean = new CareerVocation();
				bean.setP_name(rs.getString("p_name"));
				bean.setV_name(rs.getString("v_name"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public ResultVO searchOriginalJhData(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> yxsfSets, HashSet<String> zymlSets, HashSet<String> catgSets, HashSet<String> natureSets, 
			boolean is985, boolean is211, boolean issyl, boolean isgzd, boolean isszd, boolean isssyx, boolean isbyzg, boolean hasbsd, boolean hasssd, boolean isgjts, String keywords, int zdfwc_from, int zdfwc_to, int pageNumber) {
		
		
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		ResultVO resultVO = new ResultVO();
		try {
			conn = DatabaseUtils.getConnection();
			String sfSQL = yxsfSets.size() == 0 ? "" : " and x.yxsf in ("+Tools.getSQLQueryin(yxsfSets)+")";
			String natureSQL = natureSets.size() == 0 ? "" : " and x.ind_nature in ("+Tools.getSQLQueryin(natureSets)+")";
			String zymlSQL = zymlSets.size() == 0 ? "" : " and x.zyml in ("+Tools.getSQLQueryin(zymlSets)+")";
			String catgSQL = catgSets.size() == 0 ? "" : " and x.ind_catg in ("+Tools.getSQLQueryin(catgSets)+")";
			String is985SQL = is985 ? " and yx_tags like '%985%" : "";
			String is211SQL = is211 ? " and yx_tags like '%211%" : "";
			String issylSQL = issyl ? " and yx_tags like '%双一流%" : "";
			String isgzdSQL = isgzd ? " and yx_tags like '%%'" : "";
			String isszdSQL = isszd ? " and yx_tags like '%%'" : "";
			String isssyxSQL = isssyx ? " and yx_tags like '%%'" : "";
			String isbyzgSQL = isbyzg ? " and yx_tags like '%%'" : "";
			String isgjtsSQL = isgjts ? " and yx_tags like '%%'" : "";
			hasbsd = false;
			hasssd = false;
			String hasbsdSQL = hasbsd ? " and CAST(yx_bsd AS UNSIGNED) > 0" : "";
			String hasssdSQL = hasssd ? " and CAST(yx_ssd AS UNSIGNED) > 0" : "";
			
			String SEARCH_CONDITION = " FROM " + sfCode + "_jh_" + jhYear + " x WHERE xk_code like ? and pc = ? and pc_code = ? and (yxmc like ? or zymc like ? ) and zdfwc between ? and ? " 
					+ sfSQL + zymlSQL + natureSQL + catgSQL + is985SQL + is211SQL + issylSQL + isgzdSQL  + isszdSQL  + isssyxSQL  + isbyzgSQL  + isgjtsSQL  + hasbsdSQL  + hasssdSQL ;
			String ORDER = " ORDER BY zdfwc ASC LIMIT ?,?";
			String SELECT_RECORD = "SELECT * " + SEARCH_CONDITION + ORDER;
			String SELECT_CNT = "SELECT count(*) as cnt " + SEARCH_CONDITION;
			
			ps = conn.prepareStatement(SELECT_RECORD);
			int index = 1;
			ps.setString(index++, "%"+xkCode+"%");
			ps.setString(index++, pc);
			ps.setString(index++, pc_code);
			ps.setString(index++, "%"+keywords+"%");
			ps.setString(index++, "%"+keywords+"%");

			ps.setInt(index++, zdfwc_from);
			ps.setInt(index++, zdfwc_to);
			
			ps.setInt(index++, (pageNumber - 1) * PAGE_ROW_CNT);
			ps.setInt(index++, PAGE_ROW_CNT);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				list.add(ZyzdFormJDBC.setJHBeanAll(dataYear, rs));
			}
			
			resultVO.setResult(list);
			
			index = 1;
			ps = conn.prepareStatement(SELECT_CNT);
			ps.setString(index++, "%"+xkCode+"%");
			ps.setString(index++, pc);
			ps.setString(index++, pc_code);
			ps.setString(index++, "%"+keywords+"%");
			ps.setString(index++, "%"+keywords+"%");

			ps.setInt(index++, zdfwc_from);
			ps.setInt(index++, zdfwc_to);
			
			rs = ps.executeQuery();
			while (rs.next()) {
				resultVO.setRecordCnt(rs.getInt("cnt"));
			}
			
			resultVO.setCurrentPage(pageNumber);
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return resultVO;
	}
	
	public List<ZyzdMajorCatg> getZymlOne() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdMajorCatg> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "select * from zyzd_base_major_catg_one x order by x.sort desc";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			ZyzdMajorCatg bean = null;
			while (rs.next()) {
				bean = new ZyzdMajorCatg();
				bean.setC_cc(rs.getString("c_cc"));
				bean.setC_cc_code(rs.getString("c_cc_code"));
				bean.setC_code(rs.getString("c_code")); 
				bean.setC_descp(rs.getString("c_descp"));
				bean.setC_name(rs.getString("c_name"));
				bean.setC_sentence(rs.getString("c_sentence"));
				bean.setSub_cnt(rs.getInt("c_sub_cnt"));
				//bean.setP_c_name(rs.getString("p_c_name"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdMajorCatg> getZymlOne(String cc_code) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdMajorCatg> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "select * from zyzd_base_major_catg_one x c_cc_code = ? where x. order by x.sort desc";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cc_code);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			ZyzdMajorCatg bean = null;
			while (rs.next()) {
				bean = new ZyzdMajorCatg();
				bean.setC_cc(rs.getString("c_cc"));
				bean.setC_cc_code(rs.getString("c_cc_code"));
				bean.setC_code(rs.getString("c_code")); 
				bean.setC_descp(rs.getString("c_descp"));
				bean.setC_name(rs.getString("c_name"));
				bean.setC_sentence(rs.getString("c_sentence"));
				bean.setSub_cnt(rs.getInt("c_sub_cnt"));
				//bean.setP_c_name(rs.getString("p_c_name"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdMajorCatg> getZymlTwoByZylWithoutGivingZyml(HashSet<String> zymlSets, String cc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdMajorCatg> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_base_major_catg_two xx WHERE xx.p_c_name IN (SELECT DISTINCT p_c_name FROM zyzd_base_major_catg_two x WHERE x.c_cc = ? AND c_name IN ("+Tools.getSQLQueryin(zymlSets)+") ) AND xx.c_name NOT IN ("+Tools.getSQLQueryin(zymlSets)+")";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cc);
			rs = ps.executeQuery();
			ZyzdMajorCatg bean = null;
			while (rs.next()) {
				bean = new ZyzdMajorCatg();
				bean.setC_cc(rs.getString("c_cc"));
				bean.setC_code(rs.getString("c_code")); 
				bean.setC_descp(rs.getString("c_descp"));
				bean.setC_name(rs.getString("c_name"));
				bean.setC_sentence(rs.getString("c_sentence"));
				bean.setSub_cnt(rs.getInt("c_sub_cnt"));
				//bean.setP_c_name(rs.getString("p_c_name"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdHezuoInBean> searchHezuoIn(String sfTable, int year, HashSet<String> selectedSf, String selectedCatg, int scoreFrom, int scoreTo, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdHezuoInBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.sbj_name,x.yxmc_zf_org,x.yxmc_wf_org,x.zymc,x.bfzs_wf,x.bfzs_zf, min(y.zdf) as zdf, max(y.zdfwc) as zdfwc FROM zyzd_base_hezuo_in x LEFT JOIN "+sfTable+"_"+year+"x y ON x.yxmc_zf_org = y.yxmc_org WHERE y.lqpc = '普通类' AND x.yxmc_zf_sf IN ("+Tools.getSQLQueryin(selectedSf)+") "
					+ "AND x.zymc_catg_ones LIKE ? AND y.zdf BETWEEN ? AND ? GROUP BY x.sbj_name,x.yxmc_zf_org,x.yxmc_wf_org,x.zymc,x.bfzs_wf,x.bfzs_zf LIMIT ?,?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + selectedCatg + "%");
			ps.setInt(2, scoreFrom);
			ps.setInt(3, scoreTo);
			ps.setInt(4, (pageNumber - 1) * ZyzdJDBC.PAGE_ROW_CNT);
			ps.setInt(5, ZyzdJDBC.PAGE_ROW_CNT);
			rs = ps.executeQuery();
			ZyzdHezuoInBean bean = null;
			while (rs.next()) {
				bean = new ZyzdHezuoInBean();
				bean.setSbj_name(rs.getString("sbj_name"));
				bean.setYxmc_zf_org(rs.getString("yxmc_zf_org"));
				bean.setYxmc_wf_org(rs.getString("yxmc_wf_org"));
				bean.setZymc(rs.getString("zymc"));
				bean.setBfzs_wf(rs.getString("bfzs_wf"));
				bean.setBfzs_zf(rs.getString("bfzs_zf"));
				bean.setZdf(rs.getString("zdf"));
				bean.setZdfwc(rs.getString("zdfwc"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdHezuoInBean> getAllHezuoin() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdHezuoInBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_base_hezuo_in x ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			ZyzdHezuoInBean bean = null;
			while (rs.next()) {
				bean = new ZyzdHezuoInBean();
				bean.setId(rs.getInt("id"));
				bean.setSbj_name(rs.getString("sbj_name"));
				bean.setYxmc_zf_org(rs.getString("yxmc_zf_org"));
				bean.setYxmc_wf_org(rs.getString("yxmc_wf_org"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZymc(rs.getString("zymc"));
				bean.setBfzs_wf(rs.getString("bfzs_wf"));
				bean.setBfzs_zf(rs.getString("bfzs_zf"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	
	public List<ZyzdMajorCatg> getZymlTwo() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdMajorCatg> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "select * from zyzd_base_major_catg_two x order by x.sort desc";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			ZyzdMajorCatg bean = null;
			while (rs.next()) {
				bean = new ZyzdMajorCatg();
				bean.setC_cc(rs.getString("c_cc"));
				bean.setC_cc_code(rs.getString("c_cc_code"));
				bean.setC_code(rs.getString("c_code")); 
				bean.setC_descp(rs.getString("c_descp"));
				bean.setC_name(rs.getString("c_name"));
				bean.setC_sentence(rs.getString("c_sentence"));
				bean.setSub_cnt(rs.getInt("c_sub_cnt"));
				bean.setP_c_name(rs.getString("p_c_name"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public List<ZyzdBaseMajor> getZymlThree() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdBaseMajor> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "select * from zyzd_base_major x ORDER BY x.m_cc, x.m_catg_one, x.m_catg_two, x.sort desc";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			ZyzdBaseMajor bean = null;
			while (rs.next()) {
				bean = new ZyzdBaseMajor();
				bean.setM_cando(rs.getString("m_cando"));
				bean.setId(rs.getInt("id"));
				bean.setM_catg_one(rs.getString("m_catg_one"));
				bean.setM_catg_two(rs.getString("m_catg_two"));
				bean.setM_cc(rs.getString("m_cc"));
				bean.setM_cc_code(rs.getString("m_cc_code"));
				bean.setM_jy_area(rs.getString("m_jy_area"));
				bean.setM_jy_gw(rs.getString("m_jy_gw"));
				bean.setM_jy_hy(rs.getString("m_jy_hy"));
				bean.setM_jy_to(rs.getString("m_jy_to"));
				bean.setM_learn(rs.getString("m_learn"));
				bean.setM_ratio_jy(rs.getString("m_ratio_jy"));
				bean.setM_ratio_xb(rs.getString("m_ratio_xb"));
				bean.setM_sentense(rs.getString("m_sentense"));
				bean.setM_what(rs.getString("m_what"));
				bean.setM_xk(rs.getString("m_xk"));
				bean.setM_year_cnt(rs.getString("m_year_cnt"));
				bean.setM_zydm(rs.getString("m_zydm"));
				bean.setM_zymc(rs.getString("m_zymc"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdBaseMajor> getRelatedZymc(String zymc_keyword) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdBaseMajor> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "select * from zyzd_base_major x WHERE m_zymc like ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + zymc_keyword + "%");
			rs = ps.executeQuery();
			ZyzdBaseMajor bean = null;
			while (rs.next()) {
				bean = new ZyzdBaseMajor();
				bean.setId(rs.getInt("id"));
				bean.setM_cando(rs.getString("m_cando"));
				bean.setM_catg_one(rs.getString("m_catg_one"));
				bean.setM_catg_two(rs.getString("m_catg_two"));
				bean.setM_cc(rs.getString("m_cc"));
				bean.setM_cc_code(rs.getString("m_cc_code"));
				bean.setM_jy_area(rs.getString("m_jy_area"));
				bean.setM_jy_gw(rs.getString("m_jy_gw"));
				bean.setM_jy_hy(rs.getString("m_jy_hy"));
				bean.setM_jy_to(rs.getString("m_jy_to"));
				bean.setM_learn(rs.getString("m_learn"));
				bean.setM_ratio_jy(rs.getString("m_ratio_jy"));
				bean.setM_ratio_xb(rs.getString("m_ratio_xb"));
				bean.setM_sentense(rs.getString("m_sentense"));
				bean.setM_what(rs.getString("m_what"));
				bean.setM_xk(rs.getString("m_xk"));
				bean.setM_year_cnt(rs.getString("m_year_cnt"));
				bean.setM_zydm(rs.getString("m_zydm"));
				bean.setM_zymc(rs.getString("m_zymc"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdBaseMajor> getRelatedZymc(String cc_code, String zymc_keyword) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdBaseMajor> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "select * from zyzd_base_major x WHERE m_cc_code = ? and m_zymc like ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cc_code);
			ps.setString(2, "%" + zymc_keyword + "%");
			rs = ps.executeQuery();
			ZyzdBaseMajor bean = null;
			while (rs.next()) {
				bean = new ZyzdBaseMajor();
				bean.setId(rs.getInt("id"));
				bean.setM_cando(rs.getString("m_cando"));
				bean.setM_catg_one(rs.getString("m_catg_one"));
				bean.setM_catg_two(rs.getString("m_catg_two"));
				bean.setM_cc(rs.getString("m_cc"));
				bean.setM_cc_code(rs.getString("m_cc_code"));
				bean.setM_jy_area(rs.getString("m_jy_area"));
				bean.setM_jy_gw(rs.getString("m_jy_gw"));
				bean.setM_jy_hy(rs.getString("m_jy_hy"));
				bean.setM_jy_to(rs.getString("m_jy_to"));
				bean.setM_learn(rs.getString("m_learn"));
				bean.setM_ratio_jy(rs.getString("m_ratio_jy"));
				bean.setM_ratio_xb(rs.getString("m_ratio_xb"));
				bean.setM_sentense(rs.getString("m_sentense"));
				bean.setM_what(rs.getString("m_what"));
				bean.setM_xk(rs.getString("m_xk"));
				bean.setM_year_cnt(rs.getString("m_year_cnt"));
				bean.setM_zydm(rs.getString("m_zydm"));
				bean.setM_zymc(rs.getString("m_zymc"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public int getRelatedZyCount(HashSet<String> m_catg_two) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "select count(*) as ct from zyzd_base_major x where m_catg_two in ("+Tools.getSQLQueryin(m_catg_two)+")";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("ct");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public ZDPCBean getLatestZdksProvPC(String sf_name) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "select * from zdks_city_zdpc x where SF_NAME = ? order by sort desc limit 0,1";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, sf_name);
			rs = ps.executeQuery();
			ZDPCBean bean = null;
			while (rs.next()) {
				bean = new ZDPCBean();
				bean.setSfCode(rs.getString("SF_CODE"));
				bean.setSfName(rs.getString("SF_NAME"));
				bean.setZdpc(rs.getString("ZDPC"));
				bean.setSort(rs.getInt("SORT"));
				return bean;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public List<ZDPCBean> getAllZdksPCByProv(String sf_name) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZDPCBean> beanList = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "select * from zdks_city_zdpc x where SF_NAME = ? order by sort desc";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, sf_name);
			rs = ps.executeQuery();
			ZDPCBean bean = null;
			while (rs.next()) {
				bean = new ZDPCBean();
				bean.setSfCode(rs.getString("SF_CODE"));
				bean.setSfName(rs.getString("SF_NAME"));
				bean.setZdpc(rs.getString("ZDPC"));
				bean.setSort(rs.getInt("SORT"));
				beanList.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return beanList;
	}
	
	public void insertZdksPc(ZDPCBean bean) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into zdks_city_zdpc(SF_CODE, SF_NAME, ZDPC, SORT) values(?,?,?,?)";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, bean.getSfCode());
			ps.setString(2, bean.getSfName());
			ps.setString(3, bean.getZdpc());
			ps.setInt(4, bean.getSort());
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void unlockCardForZdksTimes(String c_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from zdks_score_history x where x.C_ID = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, c_id);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void resetCardBacktoInitStatus(String c_id, String passwd) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "update base_card x set x.C_CITY = null, C_SCORE = 0, C_SCORE_ORG = 0, C_XK = null, C_YEAR = 0 where x.C_ID = ? and x.C_PASSWD = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, c_id);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void executeBatchUpdate(List<String> sql) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			for(String x : sql) {
				ps = conn.prepareStatement(x);
				ps.executeUpdate();
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	
	private static void closeAllConnection(Connection conn, PreparedStatement ps, ResultSet rs) {
		/**
		try {
			if (rs != null)
				rs.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (ps != null)
				ps.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (conn != null)
				conn.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		rs = null;
		ps = null;
		conn = null; */
		DatabaseUtils.closeAllResources(rs, ps, conn);
	}
	
	/**
	 * 根据院校名称获取院校详情内容列表
	 * @param yxmc 院校名称
	 * @return 院校详情内容对象列表
	 */
	public List<YxDetailContentBean> getYxDetailContentInfo(String yxmc) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    
	    List<YxDetailContentBean> detailList = new ArrayList<>();
	    
	    try {
	        conn = DatabaseUtils.getConnection();
	        String SQL = "SELECT uid, yxmc, desp, ct_type FROM zyzd_yx_detail_content WHERE yxmc = ? ORDER BY ct_type";
	        Tools.println(SQL);
	        
	        ps = conn.prepareStatement(SQL);
	        ps.setString(1, yxmc);
	        rs = ps.executeQuery();
	        
	        SQLLogUtils.printSQL(" === getYxDetailContentInfo():  ", ps); 
	        
	        while(rs.next()) {
	            YxDetailContentBean bean = new YxDetailContentBean();
	            bean.setUid(rs.getInt("uid"));
	            bean.setYxmc(rs.getString("yxmc"));
	            bean.setDesp(rs.getString("desp"));
	            bean.setCt_type(rs.getString("ct_type"));
	            detailList.add(bean);
	        }
	        
	    } catch(Exception ex) {
	        ex.printStackTrace();
	    } finally {
	        closeAllConnection(conn, ps, rs);
	    }
	    
	    return detailList;
	}
	
	/**
	 * 
	 * 查询最近三年的院校招生简章
	 * 
	 * @param yxmc
	 * @param lastYear
	 * @return
	 * 
	 */
	public List<YxDetailJZBean> getYxDetailJZInfo(String yxmc, int lastYear) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    
	    List<YxDetailJZBean> jzList = new ArrayList<>();
	    
	    try {
	        conn = DatabaseUtils.getConnection();
	        String SQL = "SELECT uid, yxmc, nf, jz_title, jz_content FROM zyzd_yx_detail_jz " +
	                    "WHERE yxmc = ? AND nf >= ? AND nf <= ? " +
	                    "ORDER BY nf DESC LIMIT 3";
	        
	        Tools.println(SQL);
	        
	        ps = conn.prepareStatement(SQL);
	        ps.setString(1, yxmc);
	        ps.setInt(2, lastYear - 2);  // 起始年份
	        ps.setInt(3, lastYear);      // 当前年份
	        rs = ps.executeQuery();
	        
	        SQLLogUtils.printSQL(" === getYxDetailJZInfo():  ", ps);
	        
	        while(rs.next()) {
	            YxDetailJZBean bean = new YxDetailJZBean();
	            bean.setUid(rs.getInt("uid"));
	            bean.setYxmc(rs.getString("yxmc"));
	            bean.setNf(rs.getInt("nf"));
	            bean.setJz_title(rs.getString("jz_title"));
	            bean.setJz_content(rs.getString("jz_content"));
	            jzList.add(bean);
	        }
	        
	    } catch(Exception ex) {
	        ex.printStackTrace();
	    } finally {
	        closeAllConnection(conn, ps, rs);
	    }
	    
	    return jzList;
	}
	
	/**
	 * 
	 * 查询 第四轮学科排名
	 * 
	 * @param yxmc
	 * @return
	 * 
	 * 
	 */
	
	public List<ZyzdBaseRankJYB4MajorBean> getZyzdBaseRankJYB4Major(String yxmc) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    
	    List<ZyzdBaseRankJYB4MajorBean> majorList = new ArrayList<>();
	    
	    try {
	        conn = DatabaseUtils.getConnection();
	        String SQL = "SELECT ranking, yxmc, ranking_level, zymc, zyml, zyl, yxgm, sort " +
	                "FROM zyzd_base_rank_jyb4_major " +
	                "WHERE yxmc = ? " +
	                "ORDER BY sort ASC";
	        
	        Tools.println(SQL);
	        
	        ps = conn.prepareStatement(SQL);
	        ps.setString(1, yxmc);
	        rs = ps.executeQuery();
	        
	        SQLLogUtils.printSQL(" === getZyzdBaseRankJYB4Major():  ", ps);
	        
	        while(rs.next()) {
	            ZyzdBaseRankJYB4MajorBean bean = new ZyzdBaseRankJYB4MajorBean();
	            bean.setRanking(rs.getInt("ranking"));
	            bean.setYxmc(rs.getString("yxmc"));
	            bean.setRankingLevel(rs.getString("ranking_level"));
	            bean.setZymc(rs.getString("zymc"));
	            bean.setZyml(rs.getString("zyml"));
	            bean.setZyl(rs.getString("zyl"));
	            bean.setYxgm(rs.getString("yxgm"));
	            bean.setSort(rs.getInt("sort"));
	            majorList.add(bean);
	        }
	        
	    } catch(Exception ex) {
	        ex.printStackTrace();
	    } finally {
	        closeAllConnection(conn, ps, rs);
	    }
	    
	    return majorList;
	}
	
	/**
	 * 
	 * 查询 第四轮学科排名
	 * 
	 * @param zymc
	 * @param sort
	 * @return
	 * 
	 */
	public List<ZyzdBaseRankJYB4MajorBean> getZyzdBaseRankJYB4Major(String zymc, int sort) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    
	    List<ZyzdBaseRankJYB4MajorBean> majorList = new ArrayList<>();
	    
	    try {
	        conn = DatabaseUtils.getConnection();
	        String SQL = "SELECT ranking, yxmc, ranking_level, zymc, zyml, zyl, yxgm, sort " +
	                "FROM zyzd_base_rank_jyb4_major " +
	                "WHERE zymc = ? " +
	                "AND sort BETWEEN ? AND ? " +
	                "ORDER BY sort ASC";
	        
	        Tools.println(SQL);
	        
	        ps = conn.prepareStatement(SQL);
	        ps.setString(1, zymc);
	        ps.setInt(2, sort-1);  
	        ps.setInt(3, sort);  
	        rs = ps.executeQuery();
	        
	        SQLLogUtils.printSQL(" === getZyzdBaseRankJYB4Major():  ", ps);
	        
	        while(rs.next()) {
	            ZyzdBaseRankJYB4MajorBean bean = new ZyzdBaseRankJYB4MajorBean();
	            bean.setRanking(rs.getInt("ranking"));
	            bean.setYxmc(rs.getString("yxmc"));
	            bean.setRankingLevel(rs.getString("ranking_level"));
	            bean.setZymc(rs.getString("zymc"));
	            bean.setZyml(rs.getString("zyml"));
	            bean.setZyl(rs.getString("zyl"));
	            bean.setYxgm(rs.getString("yxgm"));
	            bean.setSort(rs.getInt("sort"));
	            majorList.add(bean);
	        }
	        
	    } catch(Exception ex) {
	        ex.printStackTrace();
	    } finally {
	        closeAllConnection(conn, ps, rs);
	    }
	    
	    return majorList;
	}
	
	/**
	 * 
	 * 霍兰德测试结果Codes动态查询出对应的专业信息
	 * 
	 * 
	 * @param assessName
	 * @param resultCodes
	 * @return
	 * 
	 * 
	 */
	public List<ZyzdAssessDescribeMajor> getAssessHollandResultMajor(String assessName, HashSet<String> resultCodes) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		List<ZyzdAssessDescribeMajor> list = new ArrayList<>();
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT DISTINCT * FROM zyzd_assess_describe_major WHERE assess_name = ? and result_code in (" + Tools.getSQLQueryin(resultCodes) + ")";
			
			ps = conn.prepareStatement(SQL);
			ps.setString(1, assessName);
			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === getAssessHollandResultMajor(String assessName, HashSet<String> resultCodes) : ", ps);
			
			ZyzdAssessDescribeMajor bean = null;
			
			while (rs.next()) {
				bean = new ZyzdAssessDescribeMajor();
				bean.setAssess_name(rs.getString("assess_name"));
				bean.setResult_code(rs.getString("result_code"));
				bean.setCareer_direction(rs.getString("career_direction"));
				bean.setMain_course(rs.getString("main_course"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZy_desc(rs.getString("zy_desc"));
				bean.setXk_dl(rs.getInt("xk_dl"));
				bean.setXk_hx(rs.getInt("xk_hx"));
				bean.setXk_ls(rs.getInt("xk_ls"));
				bean.setXk_sw(rs.getInt("xk_sw"));
				bean.setXk_wl(rs.getInt("xk_wl"));
				bean.setXk_zz(rs.getInt("xk_zz"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public ZyzdAssessResult getAssessByOrderIdAndAssessFrom(String orderId, String assessFrom, String assessName) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_assess_result x WHERE x.c_id = ? AND x.assess_from = ? and assess_name = ? ORDER BY x.create_tm DESC LIMIT 1";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, orderId);
			ps.setString(2, assessFrom);
			ps.setString(3, assessName);
			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === getAssessByOrderIdAndAssessFrom(): ", ps);
			
			ZyzdAssessResult bean = null;
			while (rs.next()) {
				bean = new ZyzdAssessResult();
				bean.setAssess_name(rs.getString("assess_name"));
				bean.setC_id(rs.getString("c_id"));
				bean.setAssess_result(rs.getString("assess_result"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				bean.setId(rs.getString("id"));
				return bean;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public List<ZdksHxb> getZdksHxb(String sf_code) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZdksHxb> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+sf_code+"_zdks_hxb x WHERE status = 1 ORDER BY x.create_tm DESC";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === getZdksHxbAll(): ", ps);
			
			ZdksHxb bean = null;
			while (rs.next()) {
				bean = new ZdksHxb();
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				bean.setId(rs.getString("id"));
				bean.setZd_desc(rs.getString("zd_desc"));
				bean.setZd_name(rs.getString("zd_name"));
				bean.setZd_hx(rs.getString("zd_hx"));
				bean.setZd_rq(rs.getString("zd_rq"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public boolean insertZdksHxb(String sf_code, ZdksHxb bean) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into "+sf_code+"_zdks_hxb(id, zd_rq, zd_name, zd_desc, zd_hx, status, create_tm) values(?,?,?,?,?,1,NOW())";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, bean.getId());
			ps.setString(2, bean.getZd_rq());
			ps.setString(3, bean.getZd_name());
			ps.setString(4, bean.getZd_desc());
			ps.setString(5, bean.getZd_hx());
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean insertZdksHxbBatch(String sf_code, List<ZdksHxb> beanList) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into "+sf_code+"_zdks_hxb(id, zd_rq, zd_name, zd_desc, zd_hx, status, create_tm) values(?,?,?,?,?,1,NOW())";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			for(ZdksHxb bean : beanList) {
				ps.setString(1, bean.getId());
				ps.setString(2, bean.getZd_rq());
				ps.setString(3, bean.getZd_name());
				ps.setString(4, bean.getZd_desc());
				ps.setString(5, bean.getZd_hx());
				ps.addBatch();
			}
			ps.executeBatch();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	
	public ZdksHxb getZdksHxbById(String sf_code, String id) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+sf_code+"_zdks_hxb x WHERE status = 1 and id = ? ORDER BY x.create_tm DESC";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, id);
			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === getZdksHxbById(): ", ps);
			
			ZdksHxb bean = null;
			while (rs.next()) {
				bean = new ZdksHxb();
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				bean.setId(rs.getString("id"));
				bean.setZd_desc(rs.getString("zd_desc"));
				bean.setZd_name(rs.getString("zd_name"));
				bean.setZd_hx(rs.getString("zd_hx"));
				bean.setZd_rq(rs.getString("zd_rq"));
				return bean;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public List<Tspc> getHistoryTspcLx(String sfName, String pc_code) { 
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<Tspc> tspcList = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT lx, count(*) as cnt FROM zyzd_tspc x WHERE sf = ? and pc_code = ? group by lx order by count(*) desc ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, sfName);
			ps.setString(2, pc_code);
			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === HistoryTspcLx(): ", ps);
			
			Tspc bean = null;
			while (rs.next()) {
				bean = new Tspc();
				bean.setLx(rs.getString("lx"));
				bean.setExt_cnt(rs.getInt("cnt"));
				tspcList.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return tspcList;
	}
	
	public ResultVO getHistoryTspcByCondition(int nf, String sfName, String xk_code, String pc_code, String lx, HashSet<String> zymlSets, HashSet<String> yxsfSets, String yxmc, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		ResultVO resultVO = new ResultVO();
		resultVO.setCurrentPage(pageNumber); 
		resultVO.setRecordCnt(0);
		List<Tspc> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String yxsfInSQl = yxsfSets.size() == 0 ? "" : " and yxsf in ("+Tools.getSQLQueryin(yxsfSets)+")";
			String zymlInSQl = zymlSets.size() == 0 ? "" : " and zyml in ("+Tools.getSQLQueryin(zymlSets)+")";
			String SQL = " FROM zyzd_tspc WHERE nf = ? and sf = ? and xk_code like ? and pc_code = ? and yxmc like ? and lx like ? " + yxsfInSQl + zymlInSQl;
			ps = conn.prepareStatement("SELECT * " + SQL +" ORDER BY zdf DESC limit ?,?");
			ps.setInt(1, nf);
			ps.setString(2, sfName);
			ps.setString(3, "%"+xk_code+"%");
			ps.setString(4, pc_code);
			ps.setString(5, "%"+yxmc+"%");
			ps.setString(6, "%"+lx+"%");
			ps.setInt(7, (pageNumber - 1) * PAGE_ROW_CNT);
			ps.setInt(8, PAGE_ROW_CNT);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			Tspc zyzd = null;
			while (rs.next()) {
				zyzd = new Tspc();
				zyzd.setNf(rs.getInt("nf"));
                zyzd.setSf(rs.getString("sf"));
                zyzd.setYxmc(rs.getString("yxmc"));
                zyzd.setYxmc_org(rs.getString("yxmc_org"));
                zyzd.setZymc_org(rs.getString("zymc_org"));
                zyzd.setYxdm(rs.getString("yxdm"));
                zyzd.setPc(rs.getString("pc"));
                zyzd.setLx(rs.getString("lx"));
                zyzd.setXk(rs.getString("xk"));
                zyzd.setZx(rs.getString("zx"));
                zyzd.setZyz(rs.getString("zyz"));
                zyzd.setZymc(rs.getString("zymc"));
                zyzd.setZydm(rs.getString("zydm"));
                zyzd.setZdf(rs.getString("zdf"));
                zyzd.setZdfwc(rs.getString("zdfwc"));
                zyzd.setZgf(rs.getString("zgf"));
                zyzd.setZgfwc(rs.getString("zgfwc"));
                zyzd.setPjf(rs.getString("pjf"));
                zyzd.setPjfwc(rs.getString("pjfwc"));
                zyzd.setJhs(rs.getString("jhs"));
                zyzd.setLqrs(rs.getString("lqrs"));
                zyzd.setBz(rs.getString("bz"));
                zyzd.setXb(rs.getString("xb"));
                zyzd.setPc_desc(rs.getString("pc_desc"));
                zyzd.setDxqx(rs.getString("dxqx"));
				list.add(zyzd);
			}
			
			ps = conn.prepareStatement("SELECT COUNT(*) AS cnt " + SQL);
			ps.setInt(1, nf);
			ps.setString(2, sfName);
			ps.setString(3, "%"+xk_code+"%");
			ps.setString(4, pc_code);
			ps.setString(5, "%"+yxmc+"%");
			ps.setString(6, "%"+lx+"%");
			rs = ps.executeQuery();
			while (rs.next()) {
				resultVO.setRecordCnt(rs.getInt("cnt"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		resultVO.setResult(list);
		return resultVO;
	}
	
	public List<ZyzdExpPackageHY> getExpPackageHYListByName(String pack_name, String cc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdExpPackageHY> list = new ArrayList<>();
		
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM zyzd_exp_package_hy x WHERE x.pack_name = ? and x.cc = ? order by sort desc";
			ps = conn.prepareStatement(SELECT_CONDITION);
			
			ps.setString(1, pack_name);
			ps.setString(2, cc);
			
			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === getExpPackageHYListByName : ", ps);
			
			while (rs.next()) {
				list.add(setZyzdExpPackageHY(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdExpPackageHY> getExpPackageHYNames(String cc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdExpPackageHY> list = new ArrayList<>();
		
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT pack_name,pack_desc,sort_pack FROM zyzd_exp_package_hy x WHERE x.cc = ? group by pack_name,pack_desc,sort_pack order by sort_pack desc, pack_name";
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, cc);
			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === getExpPackageHYNames(): ", ps);
			
			ZyzdExpPackageHY bean = null;
			while (rs.next()) {
				bean = new ZyzdExpPackageHY();
				bean.setPack_name(rs.getString("pack_name"));
				bean.setPack_desc(rs.getString("pack_desc"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	private static ZyzdExpPackageHY setZyzdExpPackageHY(ResultSet rs) throws Exception {
		ZyzdExpPackageHY bean = new ZyzdExpPackageHY();
		bean.setId(rs.getInt("id"));
		bean.setZymc(rs.getString("zymc"));
		bean.setCc(rs.getString("cc"));
		bean.setPack_name(rs.getString("pack_name"));
		bean.setPack_desc(rs.getString("pack_desc"));
		return bean;
	}
	
	public ZyzdCpResultMain getZyzdCpResultMainByRmId(String rm_id) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    ZyzdCpResultMain bean = null;
	    try {
	        conn = DatabaseUtils.getConnection();
	        String SQL = "SELECT * FROM zyzd_cp_result_main WHERE rm_id = ?";
	        ps = conn.prepareStatement(SQL);
	        ps.setString(1, rm_id);
	        rs = ps.executeQuery();
	        
	        SQLLogUtils.printSQL(" === getZyzdCpResultMainByRmId():  ", ps);
	        
	        if (rs.next()) {
	            bean = new ZyzdCpResultMain();
	            bean.setRm_id(rs.getString("rm_id"));
	            bean.setM_id(rs.getString("m_id"));
	            bean.setM_name(rs.getString("m_name"));
	            bean.setDescp(rs.getString("descp"));
	            bean.setC_id(rs.getString("c_id"));
	            bean.setScore_total(rs.getFloat("score_total"));
	            bean.setCreate_tm(rs.getTimestamp("create_tm"));
	            bean.setM_type(rs.getInt("m_type"));
	            
	            // Added by fresh
	            bean.setStu_xk(rs.getString("stu_xk"));
	            bean.setStu_xm(rs.getString("stu_xm"));
	            bean.setStu_yxmc(rs.getString("stu_yxmc"));
	            bean.setStu_nj(rs.getString("stu_nj"));
	            bean.setStu_cj_yw(rs.getInt("stu_cj_yw"));
	            bean.setStu_cj_sx(rs.getInt("stu_cj_sx"));
	            bean.setStu_cj_wy(rs.getInt("stu_cj_wy"));
	            bean.setStu_cj_xk_a(rs.getInt("stu_cj_xk_a"));
	            bean.setStu_cj_xk_b(rs.getInt("stu_cj_xk_b"));
	            bean.setStu_cj_xk_c(rs.getInt("stu_cj_xk_c"));
	        }
	    } catch (Exception ex) {
	        ex.printStackTrace();
	    } finally {
	        closeAllConnection(conn, ps, rs);
	    }
	    return bean;
	}

}
