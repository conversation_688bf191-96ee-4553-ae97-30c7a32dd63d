package com.career.utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class MD5Util {

	
	public static String getMD5(String text) {
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            messageDigest.update(text.getBytes());
            byte[] digest = messageDigest.digest();
            StringBuilder builder = new StringBuilder();
            for (byte b : digest) {
                builder.append(String.format("%02x", b & 0xff));
            }
            return builder.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return null;
    }
	
	//用这个方法，传入卡号密码就对了
	public static String getMD5(String uid, String pass) {
        try {
        	String key = "!#$$VV_+_!!@#";
            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            messageDigest.update(String.valueOf(uid + pass + key).getBytes());
            byte[] digest = messageDigest.digest();
            StringBuilder builder = new StringBuilder();
            for (byte b : digest) {
                builder.append(String.format("%02x", b & 0xff));
            }
            return builder.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return null;
    }
	
	//http://dwfcx.com/zsdwf/sc_zd/dz-redirect-autologin.jsp?uid=SCA158&pass=6657&checksum=2c6750c638e27e514e79f240b07d99a6
	public static void main(String args[]) {
		String key = "!#$$VV_+_!!@#";
		String cid = "SCA158";
		String pass = "6657";
		String checksum = getMD5(cid + pass + key);
		System.out.println(checksum);
	}
	
}
