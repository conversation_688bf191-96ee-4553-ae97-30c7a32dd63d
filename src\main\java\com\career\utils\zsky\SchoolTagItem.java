package com.career.utils.zsky;

public class SchoolTagItem {
	
	private String type_name;
	private String type_school_name;
	private String syl;
	private String school_name;
	private String type_school;
	private String is_985;
	private String province_name;
	private String is_211;
	private String province_area;
	private String school_id;
	private String is_apply;
	private String clicks;
	private String is_zihuaxian;
	private String rk_rank;
	private String is_ads;
	public String getType_name() {
		return type_name;
	}
	public void setType_name(String type_name) {
		this.type_name = type_name;
	}
	public String getType_school_name() {
		return type_school_name;
	}
	public void setType_school_name(String type_school_name) {
		this.type_school_name = type_school_name;
	}
	public String getSyl() {
		return syl;
	}
	public void setSyl(String syl) {
		this.syl = syl;
	}
	public String getSchool_name() {
		return school_name;
	}
	public void setSchool_name(String school_name) {
		this.school_name = school_name;
	}
	public String getType_school() {
		return type_school;
	}
	public void setType_school(String type_school) {
		this.type_school = type_school;
	}
	public String getIs_985() {
		return is_985;
	}
	public void setIs_985(String is_985) {
		this.is_985 = is_985;
	}
	public String getProvince_name() {
		return province_name;
	}
	public void setProvince_name(String province_name) {
		this.province_name = province_name;
	}
	public String getIs_211() {
		return is_211;
	}
	public void setIs_211(String is_211) {
		this.is_211 = is_211;
	}
	public String getProvince_area() {
		return province_area;
	}
	public void setProvince_area(String province_area) {
		this.province_area = province_area;
	}
	public String getSchool_id() {
		return school_id;
	}
	public void setSchool_id(String school_id) {
		this.school_id = school_id;
	}
	public String getIs_apply() {
		return is_apply;
	}
	public void setIs_apply(String is_apply) {
		this.is_apply = is_apply;
	}
	public String getClicks() {
		return clicks;
	}
	public void setClicks(String clicks) {
		this.clicks = clicks;
	}
	public String getIs_zihuaxian() {
		return is_zihuaxian;
	}
	public void setIs_zihuaxian(String is_zihuaxian) {
		this.is_zihuaxian = is_zihuaxian;
	}
	public String getRk_rank() {
		return rk_rank;
	}
	public void setRk_rank(String rk_rank) {
		this.rk_rank = rk_rank;
	}
	public String getIs_ads() {
		return is_ads;
	}
	public void setIs_ads(String is_ads) {
		this.is_ads = is_ads;
	}
	
	public String generateSQL() {
		String SQL = "insert into career_university_tag(type_name,type_school_name,syl,school_name,type_school,is_985,province_name,is_211,province_area,school_id,is_apply,clicks,is_zihuaxian,rk_rank,is_ads) values('" + type_name + "', '" + type_school_name
				+ "', '" + syl + "','" + school_name + "','" + type_school + "','" + is_985 + "','" + province_name + "','" + is_211 + "','" + province_area +"', '" + school_id + "', '"+is_apply+"','"+clicks+"','"+is_zihuaxian+"', '"+rk_rank+"','"+is_ads+"');";
		return SQL;
	}
	
	
	

}
