package com.report.entity;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.HashMap;

/**
 * 竞赛规划实体类
 * 基于《盘点那些值得大学生参加的高含金量竞赛》设计
 */
public class CompetitionPlan {
    
    // 按月份分类的竞赛
    private Map<String, List<CompetitionItem>> competitionsByMonth;
    
    // 按学科分类的竞赛
    private Map<String, List<CompetitionItem>> competitionsBySubject;
    
    // 高含金量推荐竞赛
    private List<CompetitionItem> recommendedCompetitions;
    
    // 教育部认可的84项顶级赛事
    private List<String> topTierCompetitions;
    
    public CompetitionPlan() {
        this.competitionsByMonth = new HashMap<>();
        this.competitionsBySubject = new HashMap<>();
        this.recommendedCompetitions = new ArrayList<>();
        this.topTierCompetitions = new ArrayList<>();
        
        initializeCompetitions();
    }
    
    /**
     * 初始化竞赛信息
     */
    private void initializeCompetitions() {
        // 初始化按月份的竞赛
        initializeMonthlyCompetitions();
        
        // 初始化按学科的竞赛
        initializeSubjectCompetitions();
        
        // 初始化推荐竞赛
        initializeRecommendedCompetitions();
    }
    
    private void initializeMonthlyCompetitions() {
        // 三月竞赛
        List<CompetitionItem> marchCompetitions = new ArrayList<>();
        marchCompetitions.add(new CompetitionItem("中国高校计算机大赛", "计算机", "3月-5月", 
            "全国性计算机类竞赛", "高", "保研加分、求职加分"));
        marchCompetitions.add(new CompetitionItem("全国大学生数学竞赛", "数学", "3月", 
            "数学类顶级竞赛，通常在3月进行决赛", "高", "保研加分、学术能力证明"));
        competitionsByMonth.put("3月", marchCompetitions);
        
        // 四月竞赛
        List<CompetitionItem> aprilCompetitions = new ArrayList<>();
        aprilCompetitions.add(new CompetitionItem("蓝桥杯全国软件和信息技术专业人才大赛", "编程", "4月", 
            "IT行业权威赛事，省赛通常在4月举行", "中高", "编程能力认证"));
        aprilCompetitions.add(new CompetitionItem("中国大学生计算机设计大赛", "计算机", "4月-7月", 
            "计算机设计类权威赛事", "中高", "创新能力体现"));
        aprilCompetitions.add(new CompetitionItem("全国大学生英语竞赛", "英语", "4月", 
            "英语能力权威认证，初赛通常在4月", "中", "语言能力证明"));
        competitionsByMonth.put("4月", aprilCompetitions);
        
        // 五月竞赛
        List<CompetitionItem> mayCompetitions = new ArrayList<>();
        mayCompetitions.add(new CompetitionItem("CCPC中国大学生程序设计竞赛总决赛", "编程", "5月", 
            "顶级的大学生程序设计竞赛之一", "极高", "算法和编程能力的绝佳证明"));
        mayCompetitions.add(new CompetitionItem("周培源大学生力学竞赛", "工程", "5月", 
            "力学专业顶级赛事", "高", "专业能力体现"));
        competitionsByMonth.put("5月", mayCompetitions);
        
        // 八月竞赛
        List<CompetitionItem> augustCompetitions = new ArrayList<>();
        augustCompetitions.add(new CompetitionItem("互联网+大学生创新创业大赛", "创业", "5月-10月", 
            "国家级创新创业赛事，总决赛通常在10月", "高", "创新创业能力"));
        augustCompetitions.add(new CompetitionItem("全国大学生电子设计竞赛", "电子", "8月", 
            "电子类权威赛事，通常在8月上旬", "高", "工程实践能力"));
        competitionsByMonth.put("8月", augustCompetitions);
        
        // 九月竞赛
        List<CompetitionItem> septemberCompetitions = new ArrayList<>();
        septemberCompetitions.add(new CompetitionItem("全国大学生数学建模竞赛", "数学", "9月", 
            "数学建模权威赛事，通常在9月上旬", "高", "数学应用能力"));
        competitionsByMonth.put("9月", septemberCompetitions);
        
        // 十月及以后竞赛
        List<CompetitionItem> octoberCompetitions = new ArrayList<>();
        octoberCompetitions.add(new CompetitionItem("ACM国际大学生程序设计竞赛", "编程", "10月-12月", 
            "程序设计国际顶级赛事区域赛阶段", "极高", "编程算法能力"));
        octoberCompetitions.add(new CompetitionItem("“挑战杯”中国大学生创业计划竞赛", "创业", "11月-12月", 
            "创业计划权威赛事，通常在奇数年举办", "高", "商业策划能力"));
        competitionsByMonth.put("10月及以后", octoberCompetitions);
    }
    
    private void initializeSubjectCompetitions() {
        // 计算机类
        List<CompetitionItem> csCompetitions = new ArrayList<>();
        csCompetitions.add(new CompetitionItem("ACM-ICPC", "编程", "全年", "国际权威编程竞赛", "极高", "算法编程能力"));
        csCompetitions.add(new CompetitionItem("蓝桥杯", "编程", "4月-5月", "国内知名编程竞赛", "中高", "编程实践能力"));
        competitionsBySubject.put("计算机", csCompetitions);
        
        // 数学类
        List<CompetitionItem> mathCompetitions = new ArrayList<>();
        mathCompetitions.add(new CompetitionItem("全国大学生数学竞赛", "数学", "10-3月", "数学能力权威认证", "高", "数学专业能力"));
        mathCompetitions.add(new CompetitionItem("全国大学生数学建模竞赛", "建模", "9月", "应用数学权威赛事", "高", "建模分析能力"));
        competitionsBySubject.put("数学", mathCompetitions);
        
        // 创新创业类
        List<CompetitionItem> innovationCompetitions = new ArrayList<>();
        innovationCompetitions.add(new CompetitionItem("互联网+", "创业", "5月-10月", "国家级创新创业平台", "高", "创新创业综合能力"));
        innovationCompetitions.add(new CompetitionItem("“挑战杯”全国大学生课外学术科技作品竞赛", "科技", "偶数年", "科技创新权威赛事", "高", "科技创新能力"));
        innovationCompetitions.add(new CompetitionItem("“挑战杯”中国大学生创业计划竞赛", "创业", "奇数年", "创业计划权威赛事", "高", "商业策划能力"));
        competitionsBySubject.put("创新创业", innovationCompetitions);
    }
    
    private void initializeRecommendedCompetitions() {
        recommendedCompetitions.add(new CompetitionItem("全国大学生数学建模竞赛", "数学", "9月", 
            "数学建模权威赛事，保研加分重要竞赛", "高", "逻辑思维、团队协作"));
        recommendedCompetitions.add(new CompetitionItem("中国国际“互联网+”大学生创新创业大赛", "创业", "5月-10月", 
            "国家级创新创业平台，影响力极大", "高", "创新思维、商业敏感度"));
        recommendedCompetitions.add(new CompetitionItem("ACM-ICPC程序设计竞赛", "编程", "10月-12月", 
            "程序设计国际最高水平赛事", "极高", "算法能力、编程技能"));
        recommendedCompetitions.add(new CompetitionItem("“挑战杯”系列竞赛", "科技/创业", "分奇偶年", 
            "科技创新与创业计划的最具权威性赛事之一", "高", "科研能力、创新精神、商业策划能力"));
    }
    
    /**
     * 竞赛项目实体
     */
    public static class CompetitionItem {
        private String name;            // 竞赛名称
        private String category;        // 竞赛类别
        private String period;          // 举办时间
        private String description;     // 竞赛描述
        private String difficulty;      // 难度等级
        private String benefits;        // 参赛收益
        private String registrationInfo; // 报名信息
        private String teamRequirement; // 团队要求
        private List<String> skills;    // 所需技能
        private boolean isRecommended;  // 是否推荐参加
        
        public CompetitionItem(String name, String category, String period, 
                             String description, String difficulty, String benefits) {
            this.name = name;
            this.category = category;
            this.period = period;
            this.description = description;
            this.difficulty = difficulty;
            this.benefits = benefits;
            this.skills = new ArrayList<>();
            this.isRecommended = false;
        }
        
        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }
        public String getPeriod() { return period; }
        public void setPeriod(String period) { this.period = period; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public String getDifficulty() { return difficulty; }
        public void setDifficulty(String difficulty) { this.difficulty = difficulty; }
        public String getBenefits() { return benefits; }
        public void setBenefits(String benefits) { this.benefits = benefits; }
        public String getRegistrationInfo() { return registrationInfo; }
        public void setRegistrationInfo(String registrationInfo) { this.registrationInfo = registrationInfo; }
        public String getTeamRequirement() { return teamRequirement; }
        public void setTeamRequirement(String teamRequirement) { this.teamRequirement = teamRequirement; }
        public List<String> getSkills() { return skills; }
        public void setSkills(List<String> skills) { this.skills = skills; }
        public boolean isRecommended() { return isRecommended; }
        public void setRecommended(boolean recommended) { isRecommended = recommended; }
    }
    
    // Getters and Setters
    public Map<String, List<CompetitionItem>> getCompetitionsByMonth() { return competitionsByMonth; }
    public void setCompetitionsByMonth(Map<String, List<CompetitionItem>> competitionsByMonth) { 
        this.competitionsByMonth = competitionsByMonth; 
    }
    
    public Map<String, List<CompetitionItem>> getCompetitionsBySubject() { return competitionsBySubject; }
    public void setCompetitionsBySubject(Map<String, List<CompetitionItem>> competitionsBySubject) { 
        this.competitionsBySubject = competitionsBySubject; 
    }
    
    public List<CompetitionItem> getRecommendedCompetitions() { return recommendedCompetitions; }
    public void setRecommendedCompetitions(List<CompetitionItem> recommendedCompetitions) { 
        this.recommendedCompetitions = recommendedCompetitions; 
    }
    
    public List<String> getTopTierCompetitions() { return topTierCompetitions; }
    public void setTopTierCompetitions(List<String> topTierCompetitions) { 
        this.topTierCompetitions = topTierCompetitions; 
    }
}