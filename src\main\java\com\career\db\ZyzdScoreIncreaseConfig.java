package com.career.db;

public class ZyzdScoreIncreaseConfig {

	private String score_span;
	private int score_span_from;
	private int score_span_to;
	private String score_catg;
	private float score_increase;
	private float score_increase_max;
	private float score_increase_max_to;

	private float ext_dif_rate;
	
	public float getExt_dif_rate() {
		return ext_dif_rate;
	}
	public void setExt_dif_rate(float ext_dif_rate) {
		this.ext_dif_rate = ext_dif_rate;
	}
	public String getScore_span() {
		return score_span;
	}
	public void setScore_span(String score_span) {
		this.score_span = score_span;
	}
	public int getScore_span_from() {
		return score_span_from;
	}
	public void setScore_span_from(int score_span_from) {
		this.score_span_from = score_span_from;
	}
	public int getScore_span_to() {
		return score_span_to;
	}
	public void setScore_span_to(int score_span_to) {
		this.score_span_to = score_span_to;
	}
	public String getScore_catg() {
		return score_catg;
	}
	public void setScore_catg(String score_catg) {
		this.score_catg = score_catg;
	}
	public float getScore_increase() {
		return score_increase;
	}
	public void setScore_increase(float score_increase) {
		this.score_increase = score_increase;
	}
	public float getScore_increase_max() {
		return score_increase_max;
	}
	public void setScore_increase_max(float score_increase_max) {
		this.score_increase_max = score_increase_max;
	}
	public float getScore_increase_max_to() {
		return score_increase_max_to;
	}
	public void setScore_increase_max_to(float score_increase_max_to) {
		this.score_increase_max_to = score_increase_max_to;
	}
	
	
}
