package com.career.db;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;

import com.career.db.career.MajorPlan;
import com.career.utils.Tools;

public class LXJdbc {
	
	static String URL = JDBCConstants.URL;
	static String USER = JDBCConstants.USER;
	static String PASSWD = JDBCConstants.PASSWD;
	

	public static HashMap<String, Integer> LX_SEARCH_CONDITION_MAP = new HashMap<>();
	public static HashMap<String, Integer> LX_OVERSEA_UNIV_CASES_CNT_MAP = new HashMap<>();
	public static HashMap<String, Integer> LX_CHINESE_UNIV_CASES_CNT_MAP = new HashMap<>();
	public static int PAGE_ROW_CNT = 20;
	public static int PAGE_ROW_CNT_SPEC = 15;
	
	static {
		try {
			Class.forName("com.mysql.cj.jdbc.Driver");
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		
		LX_SEARCH_CONDITION_MAP.put("GPA-%", 10);
		LX_SEARCH_CONDITION_MAP.put("GPA-4.0-5.0", 4);
		LX_SEARCH_CONDITION_MAP.put("GPA-3.0-3.9", 3);
		LX_SEARCH_CONDITION_MAP.put("GPA-2.0-2.9", 2);
		LX_SEARCH_CONDITION_MAP.put("GPA-2.0以下", 1);
		
		LX_SEARCH_CONDITION_MAP.put("YYSP-%", 10);
		LX_SEARCH_CONDITION_MAP.put("YYSP-雅思8+/托福110+", 5);
		LX_SEARCH_CONDITION_MAP.put("YYSP-雅思6.5-7.5/托福79-109", 4);
		LX_SEARCH_CONDITION_MAP.put("YYSP-雅思5.0-6.0/托福35-78", 3);
		LX_SEARCH_CONDITION_MAP.put("YYSP-雅思低于5.0/托福低于35", 2);
		
		LX_OVERSEA_UNIV_CASES_CNT_MAP = getLXCasesCacheForOverseaUniv();
		LX_CHINESE_UNIV_CASES_CNT_MAP = getLXCasesCacheForChineseUniv();

	}
	
	public List<LxFollow> getFollowYxByCid(String c_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LxFollow> list = new ArrayList<>();
		
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT * FROM lx_user_follow x WHERE c_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, c_id);
			rs = ps.executeQuery();
			LxFollow bean = null;
			while (rs.next()) {
				bean = new LxFollow();
				bean.setC_id(rs.getString("c_id"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setId(rs.getInt("id"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public void addFollowYx(String c_id, String yxmc_org) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "INSERT INTO lx_user_follow(c_id, yxmc_org, create_tm) VALUES(? ,? , now())";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, c_id);
			ps.setString(2, yxmc_org);
			ps.executeUpdate();
		} catch (Exception ex) {
			//ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public List<CareerJY> pickJiuyeYxByGroupAndLsy(String group_name_view, String lsy_view, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CareerJY> list = new ArrayList<>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			if(!Tools.isEmpty(lsy_view)) {
				String SQL = "SELECT yxmc, COUNT(*) as ct FROM career_jy_all_2024 x WHERE x.group_name_view = ? AND x.lsy_view = ? and x.isGN is null GROUP BY x.yxmc ORDER BY COUNT(*) desc limit ?,?;";
				Tools.println(SQL);
				ps = conn.prepareStatement(SQL);
				ps.setString(1, group_name_view);
				ps.setString(2, lsy_view);
				ps.setInt(3, (pageNumber - 1) * PAGE_ROW_CNT_SPEC);
				ps.setInt(4, PAGE_ROW_CNT_SPEC);
			}else {
				String SQL = "SELECT yxmc, COUNT(*) as ct FROM career_jy_all_2024 x WHERE x.group_name_view = ? and x.isGN is null GROUP BY x.yxmc ORDER BY COUNT(*) desc limit ?,?;";
				Tools.println(SQL);
				ps = conn.prepareStatement(SQL);
				ps.setString(1, group_name_view);
				ps.setInt(2, (pageNumber - 1) * PAGE_ROW_CNT_SPEC);
				ps.setInt(3, PAGE_ROW_CNT_SPEC);
			}
			rs = ps.executeQuery();
			CareerJY bean = null;
			while (rs.next()) {
				bean = new CareerJY();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setCnt(rs.getInt("ct"));
				list.add(bean);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<CareerJY> pickJiuyeYxByDW(String group_name, String lsy, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CareerJY> list = new ArrayList<>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT yxmc, COUNT(*) as ct FROM career_jy_all_2024 x WHERE x.group_name = ? AND x.lsy = ? and x.isGN is null GROUP BY x.yxmc ORDER BY COUNT(*) desc limit ?,?;";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, group_name);
			ps.setString(2, lsy);

			ps.setInt(3, (pageNumber - 1) * 10);
			ps.setInt(4, 10);
			rs = ps.executeQuery();
			CareerJY bean = null;
			while (rs.next()) {
				bean = new CareerJY();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setCnt(rs.getInt("ct"));
				list.add(bean);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdMajorRanking> getRankingByMajorAndRangeRuanKe(String zymc, int rank) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdMajorRanking> list = new ArrayList<>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT * FROM lx_ranking_major_rk WHERE major_name = ? and ranking between ? and ? ORDER BY ranking";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, zymc);
			ps.setInt(2, rank - 10);
			ps.setInt(3, rank + 5);
			rs = ps.executeQuery();
			ZyzdMajorRanking bean = null;
			while (rs.next()) {
				bean = new ZyzdMajorRanking();
				bean.setRanking(rs.getInt("ranking"));
				bean.setYxmc(rs.getString("univ_name_cn"));
				bean.setZymc(rs.getString("major_name"));
				bean.setCatg(rs.getString("major_catg"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdMajorRanking> getRankingMajorRuanKe(String yxmc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdMajorRanking> list = new ArrayList<>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT * FROM lx_ranking_major_rk WHERE univ_name_cn = ? ORDER BY ranking";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yxmc);
			rs = ps.executeQuery();
			ZyzdMajorRanking bean = null;
			while (rs.next()) {
				bean = new ZyzdMajorRanking();
				bean.setRanking(rs.getInt("ranking"));
				bean.setYxmc(rs.getString("univ_name_cn"));
				bean.setZymc(rs.getString("major_name"));
				bean.setCatg(rs.getString("major_catg"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<LxCountry> loadCountry() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LxCountry> list = new ArrayList<>();
		
		try {
			String SQL = "SELECT * FROM lx_country x WHERE x.view_status = 1 ORDER by c_sort DESC, c_prov_sort DESC";

			System.out.println(SQL);
			conn = DriverManager.getConnection(URL, USER, PASSWD);

			ps = conn.prepareStatement(SQL);
			
			rs = ps.executeQuery();
			LxCountry bean = null;
			while (rs.next()) {
				bean = new LxCountry();
				bean.setC_name(rs.getString("c_name"));
				bean.setC_school_cnt(rs.getInt("c_school_cnt"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public boolean updateCardLastLogin(String id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "UPDATE lx_card SET last_login = now() WHERE cardno = ?";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, id);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean updateCard(LXCard card) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "UPDATE lx_card set prov = ?, nf = ?, gpa = ?, gpa_score = ?, eng = ?, ielts = ?, toefl = ?, cell_phone = ? WHERE cardno = ?";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, card.getProv());
			ps.setInt(2, card.getNf());
			ps.setFloat(3, card.getGpa());
			ps.setInt(4, card.getGpa_score());
			ps.setFloat(5, card.getEng());
			ps.setFloat(6, card.getIelts());
			ps.setInt(7, card.getToefl());
			ps.setString(8, card.getCell_phone());
			ps.setString(9, card.getCardno());
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public LXCard getCardByIDandPasswd(String id, String passwd) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		LXCard bean = null;
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT * FROM lx_card WHERE cardno = ? and passwd = ?";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, id.toUpperCase());
			ps.setString(2, passwd.toUpperCase());
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = new LXCard();
				bean.setCardno(rs.getString("cardno"));
				bean.setPasswd(rs.getString("passwd"));
				bean.setProv(rs.getString("prov"));
				bean.setCell_phone(rs.getString("cell_phone"));
				bean.setGpa(rs.getFloat("gpa"));
				bean.setToefl(rs.getInt("toefl"));
				bean.setIelts(rs.getFloat("ielts"));
				bean.setEng(rs.getFloat("eng"));
				bean.setGpa_score(rs.getInt("gpa_score"));
				bean.setStatus(rs.getInt("status"));
				bean.setNf(rs.getInt("nf"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}
	
	public LXSchoolBean pickLXSchoolByOuterID(String outerID) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			String SQL = "SELECT * FROM career_lx_university_xdf_temp x where id_outer = ?";

			System.out.println(SQL);
			conn = DriverManager.getConnection(URL, USER, PASSWD);

			ps = conn.prepareStatement(SQL);
			ps.setString(1, outerID);
			rs = ps.executeQuery();
			LXSchoolBean bean = null;
			while (rs.next()) {
				bean = new LXSchoolBean();
				bean.setCountry(rs.getString("country"));
				bean.setRegion(rs.getString("region"));
				
				bean.setGpa(rs.getString("gpa"));
				bean.setIetls(rs.getString("ietls"));
				bean.setToefl(rs.getString("toefl"));
				
				bean.setNature(rs.getString("nature"));
				bean.setWebsite(rs.getString("website"));
				
				bean.setWorldRankName(rs.getString("worldRankName"));
				bean.setWorldRankValue(rs.getInt("worldRankValue"));
				
				bean.setLocalRankName(rs.getString("localRankName"));
				bean.setLocalRankValue(rs.getInt("localRankValue"));
				
				bean.setSchool_name(rs.getString("ename"));
				bean.setSchool_name_cn(rs.getString("cname"));
				
				bean.setSummary(rs.getString("summary"));
				bean.setMajorExcellent(rs.getString("major_excellent"));
				bean.setMajorHot(rs.getString("major_hot"));
				bean.setYxTese(rs.getString("yx_tese"));
				bean.setIdOuter(rs.getString("id_outer"));
				
				return bean;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public List<LXSchoolMajorRank> loadUniversityMajorQSRank(String selectedSchoolCnName, int year) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LXSchoolMajorRank> list = new ArrayList<>();
		try {
			String SQL = "SELECT x.*, y.major_in_china, y.major_in_china_label FROM career_lx_major_qs_rank x left join career_lx_major_qs y on x.major_cn = y.major_cn where x.yxmc = ? and x.rank_year = ? order by y.major_in_china, y.major_in_china_label";

			System.out.println(SQL);
			conn = DriverManager.getConnection(URL, USER, PASSWD);

			ps = conn.prepareStatement(SQL);
			ps.setString(1, selectedSchoolCnName);
			ps.setInt(2, year);
			
			rs = ps.executeQuery();
			LXSchoolMajorRank bean = null;
			while (rs.next()) {
				bean = new LXSchoolMajorRank();
				bean.setRankDesc(rs.getString("rank_desc"));
				bean.setRankNum(rs.getInt("rank_num"));
				bean.setRankYear(rs.getInt("rank_year"));
				bean.setMajorCn(rs.getString("major_cn"));
				bean.setYxmc(selectedSchoolCnName);
				bean.setMajorInChina(rs.getString("major_in_china"));
				bean.setMajorInChinaLabel(rs.getString("major_in_china_label"));
				
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<LXSchoolMajorRank> loadAllChinaUniversityMajorQSRank(int year) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LXSchoolMajorRank> list = new ArrayList<>();
		try {
			String SQL = "SELECT * from career_lx_major_qs_rank x WHERE x.country = 'China (Mainland)' and rank_year = ?";

			System.out.println(SQL);
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, year);
			
			rs = ps.executeQuery();
			LXSchoolMajorRank bean = null;
			while (rs.next()) {
				bean = new LXSchoolMajorRank();
				bean.setRankDesc(rs.getString("rank_desc"));
				bean.setRankNum(rs.getInt("rank_num"));
				bean.setRankYear(rs.getInt("rank_year"));
				bean.setMajorCn(rs.getString("major_cn"));
				bean.setYxmc(rs.getString("yxmc"));
				
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<LXSchoolAttrBean> pickLXSchoolAttrByOuterID(String outerID) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LXSchoolAttrBean> list = new ArrayList<>();

		try {
			String SQL = "SELECT * FROM career_lx_university_xdf_attr_temp x where x.id_outer = ? order by x.attr_type, x.attr_ind, x.attr_name";
			System.out.println(SQL);
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, outerID);
			rs = ps.executeQuery();
			LXSchoolAttrBean bean = null;
			while (rs.next()) {
				bean = new LXSchoolAttrBean();
				bean.setAttrInd(rs.getString("attr_ind"));
				bean.setAttrName(rs.getString("attr_name"));
				bean.setAttrType(rs.getString("attr_type"));
				bean.setAttrValue(rs.getString("attr_value"));
				bean.setIdOuter(rs.getString("id_outer"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public LinkedHashMap<String, Integer> getToCasesForOverseaUniv(String overseaUniv, int limitCnt) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		LinkedHashMap<String, Integer> CASES = new LinkedHashMap<>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT cuniv_news, COUNT(*) as ct FROM career_lx_cases x WHERE x.euniv_news = ? GROUP BY x.cuniv_news ORDER BY COUNT(*) desc limit 0, ?";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, overseaUniv);
			ps.setInt(2, limitCnt);
			rs = ps.executeQuery();
			while (rs.next()) {
				CASES.put(rs.getString("cuniv_news"), rs.getInt("ct"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return CASES;
	}
	
	
	public static HashMap<String, Integer> getLXCasesCacheForOverseaUniv() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, Integer> CASES = new HashMap<>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT euniv_news, COUNT(*) as ct FROM career_lx_cases x GROUP by x.euniv_news";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			while (rs.next()) {
				CASES.put(rs.getString("euniv_news"), rs.getInt("ct"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return CASES;
	}
	
	public static HashMap<String, Integer> getLXCasesCacheForChineseUniv() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, Integer> CASES = new HashMap<>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT cuniv_news, COUNT(*) as ct FROM career_lx_cases x GROUP by x.cuniv_news";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			while (rs.next()) {
				CASES.put(rs.getString("cuniv_news"), rs.getInt("ct"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return CASES;
	}
	
	
	
	public List<LXSchoolBean> listLXSchoolByCondition(String dq, String gpa, String eng, int pager) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LXSchoolBean> list = new ArrayList<>();
		
		int resultCnt = 0;
		try {
			String COUNT = "SELECT count(*) ";
			String SELECT = "SELECT * ";
			String SQL = "FROM career_lx_university_xdf_temp x where country_ext like ? and x.search_pscj <= ? and x.search_yysp <= ? ";
			String ORDER = "order by worldRankValue ASC, localRankValue ASC, ename ASC LIMIT ?,?";

			System.out.println(SQL);
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			ps = conn.prepareStatement(COUNT + SQL);
			ps.setString(1, "%" + dq + "%");
			ps.setInt(2, LX_SEARCH_CONDITION_MAP.get("GPA-" + gpa));
			ps.setInt(3, LX_SEARCH_CONDITION_MAP.get("YYSP-" + eng));
			
			rs = ps.executeQuery();
			while(rs.next()) {
				resultCnt = rs.getInt(1);
			}
			rs.close();
			rs = null;
			ps.close();
			ps = null;
			
			ps = conn.prepareStatement(SELECT + SQL + ORDER);
			ps.setString(1, "%" + dq + "%");
			ps.setInt(2, LX_SEARCH_CONDITION_MAP.get("GPA-" + gpa));
			ps.setInt(3, LX_SEARCH_CONDITION_MAP.get("YYSP-" + eng));
			
			ps.setInt(4, (pager - 1) * 15);
			ps.setInt(5, 15);
			
			rs = ps.executeQuery();
			LXSchoolBean bean = null;
			while (rs.next()) {
				bean = new LXSchoolBean();
				bean.setCountry(rs.getString("country"));
				bean.setRegion(rs.getString("region"));
				
				bean.setGpa(rs.getString("gpa"));
				bean.setIetls(rs.getString("ietls"));
				bean.setToefl(rs.getString("toefl"));
				bean.setLogo_image(rs.getString("logo"));
				
				bean.setNature(rs.getString("nature"));
				bean.setWebsite(rs.getString("website"));
				
				bean.setWorldRankName(rs.getString("worldRankName"));
				bean.setWorldRankValue(rs.getInt("worldRankValue"));
				
				bean.setLocalRankName(rs.getString("localRankName"));
				bean.setLocalRankValue(rs.getInt("localRankValue"));
				
				bean.setSchool_name(rs.getString("ename"));
				bean.setSchool_name_cn(rs.getString("cname"));
				
				bean.setSummary(rs.getString("summary"));
				bean.setMajorExcellent(rs.getString("major_excellent"));
				bean.setMajorHot(rs.getString("major_hot"));
				bean.setYxTese(rs.getString("yx_tese"));
				bean.setIdOuter(rs.getString("id_outer"));
				
				bean.setRecordSUM(resultCnt);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<LxRankingQs> loadAllChinaUniversityQsRank(int nf) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LxRankingQs> list = new ArrayList<>();
		try {
			String SQL = "SELECT * FROM lx_ranking_qs x where country = '中国（大陆）' and nf = ? ORDER by ranking ASC";

			System.out.println(SQL);
			conn = DriverManager.getConnection(URL, USER, PASSWD);

			ps = conn.prepareStatement(SQL);
			ps.setInt(1, nf);
			
			rs = ps.executeQuery();
			LxRankingQs bean = null;
			while (rs.next()) {
				bean = new LxRankingQs();
				bean.setRanking(rs.getInt("ranking"));
				bean.setRank_view(rs.getString("rank_view"));
				bean.setCountry(rs.getString("country"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public HashMap<String,LxRankingQs> loadAllChinaUniversityQsRankAsMap(int nf) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String,LxRankingQs> MAP_RANK = new HashMap<>();
		try {
			String SQL = "SELECT * FROM lx_ranking_qs x where country = '中国（大陆）' and nf = ? ORDER by ranking ASC";

			System.out.println(SQL);
			conn = DriverManager.getConnection(URL, USER, PASSWD);

			ps = conn.prepareStatement(SQL);
			ps.setInt(1, nf);
			
			rs = ps.executeQuery();
			LxRankingQs bean = null;
			while (rs.next()) {
				bean = new LxRankingQs();
				bean.setRanking(rs.getInt("ranking"));
				bean.setRank_view(rs.getString("rank_view"));
				bean.setCountry(rs.getString("country"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				MAP_RANK.put(bean.getYxmc_org(), bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return MAP_RANK;
	}
	
	public List<LxBaseDataSchool> searchSchool(int s_type, HashSet<String> s_model, int s_nys, String zyfx , HashSet<String> country, int prior, float gpa_from, float gpa_to, float eng_from, float eng_to, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LxBaseDataSchool> list = new ArrayList<>();
		try {
			String SORT = " order by s_qs_rank asc ";
			if(prior == 2) {
				SORT = " order by s_jy_china DESC,s_qs_rank ASC ";
			}else if(prior == 3) {
				SORT = " order by s_jy_oversea DESC,s_qs_rank ASC  ";
			}else {
				SORT = " order by s_qs_rank asc, s_jy_china DESC ";
			}
			String SQL = "SELECT * FROM lx_base_data_school x where country in ("+Tools.getSQLQueryin(country)+") and s_model in ("+Tools.getSQLQueryin(s_model)+") and s_type = ? and s_nys <= ? and s_zyfx like ? and (s_gpa between ? and ?) and (s_english  between ? and ?) "+SORT+" limit ?,?";
			Tools.println(SQL);
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, s_type);
			ps.setInt(2, s_nys);
			ps.setString(3, "%" + zyfx + "%");
			ps.setFloat(4, gpa_from);
			ps.setFloat(5, gpa_to);
			ps.setFloat(6, eng_from);
			ps.setFloat(7, eng_to);
			ps.setInt(8, (pageNumber - 1) * 40);
			ps.setInt(9, 40);
			
			rs = ps.executeQuery();
			LxBaseDataSchool bean = null;
			while (rs.next()) {
				bean = new LxBaseDataSchool();
				bean.setAgent_cnt(rs.getInt("agent_cnt"));
				bean.setCountry(rs.getString("country"));
				bean.setS_english(rs.getString("s_english"));
				bean.setS_gaokao(rs.getString("s_gaokao"));
				bean.setS_gpa(rs.getString("s_gpa"));
				bean.setS_jy_china(rs.getInt("s_jy_china"));
				bean.setS_jy_oversea(rs.getInt("s_jy_oversea"));
				bean.setS_qs_rank(rs.getInt("s_qs_rank"));
				bean.setS_model(rs.getString("s_model"));
				bean.setS_nys(rs.getInt("s_nys"));
				bean.setS_type(rs.getInt("s_type"));
				bean.setS_zyfx(rs.getInt("s_zyfx"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public int searchSchool_getSum(int s_type, HashSet<String> s_model, int s_nys, String zyfx , HashSet<String> country, float gpa_from, float gpa_to, float eng_from, float eng_to) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			String SQL = "SELECT count(*) as cnt FROM lx_base_data_school x where country in ("+Tools.getSQLQueryin(country)+") and s_model in ("+Tools.getSQLQueryin(s_model)+") and s_type = ? and s_nys <= ? and s_zyfx like ? and (s_gpa between ? and ?) and (s_english  between ? and ?)";
			Tools.println(SQL);
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, s_type);
			ps.setInt(2, s_nys);
			ps.setString(3, "%" + zyfx + "%");
			ps.setFloat(4, gpa_from);
			ps.setFloat(5, gpa_to);
			ps.setFloat(6, eng_from);
			ps.setFloat(7, eng_to);
			
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("cnt");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public List<MajorPlan> getHotMajorByOverseaUniv(String yxmcOrg) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MajorPlan> list = new ArrayList<>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT x.emajr_news, COUNT(*) as cnt FROM career_lx_cases x where x.euniv_news = ? group by x.emajr_news ORDER BY COUNT(*) DESC LIMIT 0,10";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yxmcOrg);
			rs = ps.executeQuery();
			MajorPlan bean = null;
			while (rs.next()) {
				bean = new MajorPlan();
				bean.setSpecial_name(rs.getString("emajr_news"));
				bean.setRecruit_number(String.valueOf(rs.getInt("cnt")));
				list.add(bean); 
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<MajorPlan> getChineseUnivByOverseaUniv(String yxmcOrg) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MajorPlan> list = new ArrayList<>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT x.cuniv_news, COUNT(*) as cnt FROM career_lx_cases x where x.euniv_news = ? group by x.cuniv_news ORDER BY COUNT(*) DESC LIMIT 0,10";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yxmcOrg);
			rs = ps.executeQuery();
			MajorPlan bean = null;
			while (rs.next()) {
				bean = new MajorPlan();
				bean.setSchool_name(rs.getString("cuniv_news"));
				bean.setRecruit_number(String.valueOf(rs.getInt("cnt")));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public List<LXShoolRank> loadAllChinaUniversityRank(String rankType, int year) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LXShoolRank> list = new ArrayList<>();
		try {
			String SQL = "SELECT * FROM career_lx_university_qs_rank x where rk_type = ? and rk_year = ? and country = 'China (Mainland)'";

			System.out.println(SQL);
			conn = DriverManager.getConnection(URL, USER, PASSWD);

			ps = conn.prepareStatement(SQL);
			ps.setString(1, rankType);
			ps.setInt(2, year);
			
			rs = ps.executeQuery();
			LXShoolRank bean = null;
			while (rs.next()) {
				bean = new LXShoolRank();
				bean.setRankDesc(rs.getString("rk_desc"));
				bean.setRankNum(rs.getInt("rk_num"));
				bean.setRankType(rs.getString("rk_type"));
				bean.setRankYear(rs.getInt("rk_year"));
				bean.setYxmc(rs.getString("yxmc_cn"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdHezuoInBean> getHezuobanxueOut(String givingOverseaYxmc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdHezuoInBean> list = new ArrayList<>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SELECT_CONDITION = "SELECT * FROM zyzd_base_hezuo_in x WHERE x.yxmc_wf_org like ? ";
			String ORDER_CONDITION = " order by yxmc_zf_org ASC ";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, "%"+givingOverseaYxmc+"%");
			rs = ps.executeQuery();
			ZyzdHezuoInBean sb = null; 
			while (rs.next()) {
				sb = new ZyzdHezuoInBean();
				sb.setYxmc_zf_org(rs.getString("yxmc_zf_org"));
				sb.setYxmc_wf_org(rs.getString("yxmc_wf_org"));
				sb.setBxcc(rs.getString("bxcc"));
				sb.setBfzs_zf(rs.getString("bfzs_zf"));
				sb.setBfzs_wf(rs.getString("bfzs_wf"));
				sb.setBxgm(rs.getString("bxgm"));
				sb.setSbj_addr(rs.getString("sbj_addr"));
				sb.setSbj_name(rs.getString("sbj_name"));
				sb.setUrl(rs.getString("url"));
				sb.setYxq(rs.getString("yxq"));
				sb.setZsbh(rs.getString("zsbh"));
				sb.setZsfs(rs.getString("zsfs"));
				sb.setZsnf(rs.getString("zsnf"));
				sb.setZymc(rs.getString("zymc"));
				list.add(sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	private static void closeAllConnection(Connection conn, PreparedStatement ps, ResultSet rs) {
		try {
			if (rs != null)
				rs.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (ps != null)
				ps.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (conn != null)
				conn.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		rs = null;
		ps = null;
		conn = null;
	}

}
