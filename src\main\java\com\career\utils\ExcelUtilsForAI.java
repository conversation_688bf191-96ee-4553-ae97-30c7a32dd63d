package com.career.utils;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.career.db.AiTbForm;
import com.career.db.CardBean;
import com.career.db.JHBean;
import com.career.db.LhyForm;
import com.career.db.ZyzdForm;
import com.career.db.ZyzdMajorRanking;
import com.career.db.ZyzdUniversityBean;
import com.career.db.ZyzdUniversityDataBean;

import cn.hutool.core.lang.UUID;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;


public class ExcelUtilsForAI {
	
	public static void main(String args[]) {
	}

	
	public static void createSearchResultExcel(String filePathAndName,  List<JHBean> dataList) {
		// 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("招生计划数据");

        // 创建标题行样式
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);

        // 创建数据行样式
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setWrapText(true);

        // 创建标题行（与HTML表格列顺序一致）
        Row headerRow = sheet.createRow(0);
        String[] headers = {
            "院校省份", "院校城市", "院校代码", "院校名称", "批次", "科类", 
            "一级学科", "专业代码", "专业名称", "学费", "学制", "专业组",
            "2024最低分", "2023最低分", "2022最低分",
            "2024平均分", "2023平均分", "2022平均分",
            "2024最高分", "2023最高分", "2022最高分",
            "2024计划", "2023计划", "2022计划"
        };

        sheet.setDefaultColumnWidth((short) 20); // 设置默认列宽
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
            // 设置列宽
            if(i == 3 || i == 8) {
            	sheet.setColumnWidth(i, 25 * 256);
            }else {
            	sheet.setColumnWidth(i, 10 * 256);
            }
        }


        // 填充数据行
        int rowNum = 1;
        for (JHBean bean : dataList) {
            Row row = sheet.createRow(rowNum++);

            int index = 0;
            // 基础信息
            createCell(row, index++, bean.getYxsf(), dataStyle);
            createCell(row, index++, bean.getYxcs(), dataStyle);
            createCell(row, index++, bean.getYxdm(), dataStyle);
            createCell(row, index++, bean.getYxmc(), dataStyle);
            createCell(row, index++, bean.getPc(), dataStyle);
            createCell(row, index++, bean.getXk(), dataStyle);
            createCell(row, index++, bean.getZyml(), dataStyle);
            createCell(row, index++, bean.getZydm(), dataStyle);
            createCell(row, index++, bean.getZymc_org(), dataStyle);
            createCell(row, index++, bean.getFee(), dataStyle);
            createCell(row, index++, bean.getXz(), dataStyle);
            createCell(row, index++, bean.getZyz(), dataStyle);
            //createCell(row, 12, bean.getZnzy(), dataStyle);

            // 最低分
            createCell(row, index++, bean.getZdf_a(), dataStyle);
            createCell(row, index++, bean.getZdf_b(), dataStyle);
            createCell(row, index++, bean.getZdf_c(), dataStyle);

            // 平均分
            createCell(row, index++, bean.getPjf_a(), dataStyle);
            createCell(row, index++, bean.getPjf_b(), dataStyle);
            createCell(row, index++, bean.getPjf_c(), dataStyle);

            // 最高分
            createCell(row, index++, bean.getZgf_a(), dataStyle);
            createCell(row, index++, bean.getZgf_b(), dataStyle);
            createCell(row, index++, bean.getZgf_c(), dataStyle);

            // 计划数
            createCell(row, index++, bean.getJhs_a(), dataStyle);
            createCell(row, index++, bean.getJhs_b(), dataStyle);
            createCell(row, index++, bean.getJhs_c(), dataStyle);

        }

        // 写入文件
        try (FileOutputStream outputStream = new FileOutputStream(filePathAndName)) {
            try {
				workbook.write(outputStream);
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
        } catch (FileNotFoundException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		} catch (IOException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		} finally {
            try {
				workbook.close();
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
        }
    }
	
	public static void createTbResultExcel(String filePathAndName, String pc, List<AiTbForm> dataList) {
		// 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("填报结果("+pc+")");

        // 创建标题行样式
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);

        // 创建数据行样式
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setWrapText(true);

        // 创建标题行（与HTML表格列顺序一致）
        Row headerRow = sheet.createRow(0);
        String[] headers = {
        		"志愿顺序", "院校代码", "院校名称", "专业顺序", "专业代码","专业名称", "学费", "专业组",
            "2024最低分", "2023最低分", "2022最低分",
            "2024平均分", "2023平均分", "2022平均分",
            "2024最高分", "2023最高分", "2022最高分",
            "2024计划", "2023计划", "2022计划"
        };

        sheet.setDefaultColumnWidth((short) 20); // 设置默认列宽
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
            // 设置列宽
            if(i == 2 || i == 5) {
            	sheet.setColumnWidth(i, 25 * 256);
            }else {
            	sheet.setColumnWidth(i, 10 * 256);
            }
        }


        // 填充数据行
        int rowNum = 1;
        for (AiTbForm bean : dataList) {
            Row row = sheet.createRow(rowNum++);

            int index = 0;
            // 基础信息
            createCell(row, index++, bean.getSeq_no_yx(), dataStyle);
            createCell(row, index++, bean.getYxdm(), dataStyle);
            createCell(row, index++, bean.getYxmc(), dataStyle);
            createCell(row, index++, bean.getSeq_no_zy(), dataStyle);
            createCell(row, index++, bean.getZydm(), dataStyle);
            createCell(row, index++, bean.getZymc_org(), dataStyle);
            createCell(row, index++, bean.getFee(), dataStyle);
            createCell(row, index++, bean.getZyz(), dataStyle);
            //createCell(row, 12, bean.getZnzy(), dataStyle);

            // 最低分
            createCell(row, index++, bean.getZdf_a(), dataStyle);
            createCell(row, index++, bean.getZdf_b(), dataStyle);
            createCell(row, index++, bean.getZdf_c(), dataStyle);

            // 平均分
            createCell(row, index++, bean.getPjf_a(), dataStyle);
            createCell(row, index++, bean.getPjf_b(), dataStyle);
            createCell(row, index++, bean.getPjf_c(), dataStyle);

            // 最高分
            createCell(row, index++, bean.getZgf_a(), dataStyle);
            createCell(row, index++, bean.getZgf_b(), dataStyle);
            createCell(row, index++, bean.getZgf_c(), dataStyle);

            // 计划数
            createCell(row, index++, bean.getJhs_a(), dataStyle);
            createCell(row, index++, bean.getJhs_b(), dataStyle);
            createCell(row, index++, bean.getJhs_c(), dataStyle);

        }

        // 写入文件
        try (FileOutputStream outputStream = new FileOutputStream(filePathAndName)) {
            try {
				workbook.write(outputStream);
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
        } catch (FileNotFoundException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		} catch (IOException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		} finally {
            try {
				workbook.close();
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
        }
    }

    private static void createCell(Row row, int column, String value, CellStyle style) {
        Cell cell = row.createCell(column);
        cell.setCellValue(value != null ? value : "");
        cell.setCellStyle(style);
    }

    private static void createCell(Row row, int column, Integer value, CellStyle style) {
        Cell cell = row.createCell(column);
        if (value != null) {
            cell.setCellValue(value);
        } else {
            cell.setCellValue("");
        }
        cell.setCellStyle(style);
    }

    private static void createCell(Row row, int column, Double value, CellStyle style) {
        Cell cell = row.createCell(column);
        if (value != null) {
            cell.setCellValue(value);
        } else {
            cell.setCellValue("");
        }
        cell.setCellStyle(style);
    }
    
    
    public static void createFormReportExcel(int jhYear, String filePathAndName, String sf, String pc, String xk, String score,String wc, String xm, List<AiTbForm> dataList, List<JHBean> existYxAllDataList) {
		// 创建一个新的工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        Tools.println(filePathAndName);
        // 创建一个工作表
        Sheet sheet = workbook.createSheet("志愿表_"+Tools.viewXK(xk)+score+"("+wc+")");
        sheet.setColumnWidth(0, 10*256);
        sheet.setColumnWidth(1, 10*256);
        sheet.setColumnWidth(2, 30*256);
        sheet.setColumnWidth(3, 10*256);
        sheet.setColumnWidth(4, 10*256);
        sheet.setColumnWidth(5, 30*256);
        sheet.setColumnWidth(6, 20*256);
        sheet.setColumnWidth(7, 20*256);
        sheet.setColumnWidth(8, 20*256);
        sheet.setColumnWidth(9, 30*256);
        
        XSSFCellStyle style_content = workbook.createCellStyle();
        style_content.setBorderBottom(BorderStyle.THIN);//下边框
        style_content.setBorderTop(BorderStyle.THIN);//上边框
        style_content.setBorderLeft(BorderStyle.THIN);//左边框
        style_content.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontBold = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontBold.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontBold = workbook.createFont();
        fontBold.setFontHeightInPoints((short) 10);
        fontBold.setBold(true);
        fontBold.setColor(IndexedColors.BLACK.getIndex());
        fontBold.setFontName("微软雅黑");
        cellStyleFontBold.setFont(fontBold);
        cellStyleFontBold.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontBold.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontBold.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontBold.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontNormal0 = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontNormal0.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontNormal0 = workbook.createFont();
        fontNormal0.setFontHeightInPoints((short) 10);
        fontNormal0.setBold(false);
        fontNormal0.setColor(IndexedColors.BLACK.getIndex());
        fontNormal0.setFontName("微软雅黑");
        cellStyleFontNormal0.setFont(fontNormal0);
        cellStyleFontNormal0.setAlignment(HorizontalAlignment.CENTER);
        cellStyleFontNormal0.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyleFontNormal0.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontNormal0.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontNormal0.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontNormal0.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontNormal2 = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontNormal2.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontNormal2 = workbook.createFont();
        fontNormal2.setFontHeightInPoints((short) 10);
        fontNormal2.setBold(false);
        fontNormal2.setColor(IndexedColors.BLACK.getIndex());
        fontNormal2.setFontName("微软雅黑");
        cellStyleFontNormal2.setFont(fontNormal2);
        cellStyleFontNormal2.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontNormal2.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontNormal2.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontNormal2.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontNormal1 = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontNormal1.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontNormal = workbook.createFont();
        fontNormal.setFontHeightInPoints((short) 10);
        fontNormal.setBold(false);
        fontNormal.setColor(IndexedColors.BLACK.getIndex());
        fontNormal.setFontName("微软雅黑");
        cellStyleFontNormal1.setFont(fontNormal);
        cellStyleFontNormal1.setFillForegroundColor(IndexedColors.LEMON_CHIFFON.getIndex());
        cellStyleFontNormal1.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontNormal1.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontNormal1.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontNormal1.setBorderRight(BorderStyle.THIN);//右边框
        

        CellStyle cellStyleFontHighLight = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontHighLight.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontHighLight = workbook.createFont();
        fontHighLight.setFontHeightInPoints((short) 10);
        fontHighLight.setBold(true);
        fontHighLight.setColor(IndexedColors.BLACK.getIndex());
        fontHighLight.setFontName("微软雅黑");
        cellStyleFontHighLight.setFont(fontHighLight);
        cellStyleFontHighLight.setAlignment(HorizontalAlignment.CENTER);
        cellStyleFontHighLight.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyleFontHighLight.setFillForegroundColor(IndexedColors.TAN.getIndex());
        cellStyleFontHighLight.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontHighLight.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontHighLight.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontHighLight.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontGreen = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontGreen.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontGreen = workbook.createFont();
        fontGreen.setFontHeightInPoints((short) 10);
        fontGreen.setBold(true);
        fontGreen.setColor(IndexedColors.GREEN.getIndex());
        fontGreen.setFontName("微软雅黑");
        cellStyleFontGreen.setFont(fontGreen);
        
        CellStyle cellStyleFontRed = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontRed.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontRed = workbook.createFont();
        fontRed.setFontHeightInPoints((short) 10);
        fontRed.setColor(IndexedColors.RED.getIndex());
        fontRed.setFontName("微软雅黑");
        cellStyleFontRed.setFont(fontRed);
        
        CellStyle cellStyleFontBlueBig = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontBlueBig.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontBlueBig = workbook.createFont();
        fontBlueBig.setFontHeightInPoints((short) 18);
        fontBlueBig.setBold(true);
        fontBlueBig.setColor(IndexedColors.ORANGE.getIndex());
        fontBlueBig.setFontName("微软雅黑");
        cellStyleFontBlueBig.setFont(fontBlueBig);
        cellStyleFontBlueBig.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontBlueBig.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontBlueBig.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontBlueBig.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontBlack14 = workbook.createCellStyle();
        cellStyleFontBlueBig.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontBlackBig14 = workbook.createFont();
        fontBlackBig14.setFontHeightInPoints((short) 14);
        fontBlackBig14.setBold(true);
        fontBlackBig14.setColor(IndexedColors.BLACK.getIndex());
        fontBlackBig14.setFontName("微软雅黑");
        cellStyleFontBlack14.setFont(fontBlackBig14);
        cellStyleFontBlack14.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        cellStyleFontBlack14.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyleFontBlack14.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontBlack14.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontBlack14.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontBlack14.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontThinBlack14 = workbook.createCellStyle();
        cellStyleFontThinBlack14.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontBlackThin14 = workbook.createFont();
        fontBlackThin14.setFontHeightInPoints((short) 14);
        fontBlackThin14.setBold(false);
        fontBlackThin14.setColor(IndexedColors.BLACK.getIndex());
        fontBlackThin14.setFontName("微软雅黑");
        cellStyleFontThinBlack14.setFont(fontBlackThin14);
        cellStyleFontThinBlack14.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        cellStyleFontThinBlack14.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyleFontThinBlack14.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontThinBlack14.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontThinBlack14.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontThinBlack14.setBorderRight(BorderStyle.THIN);//右边框
        
        
        CellStyle cellStyleFontColumnHeader = workbook.createCellStyle();
        cellStyleFontColumnHeader.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontColumnHeader = workbook.createFont();
        fontColumnHeader.setFontHeightInPoints((short) 12);
        fontColumnHeader.setBold(true);
        fontColumnHeader.setColor(IndexedColors.BLACK.getIndex());
        fontColumnHeader.setFontName("微软雅黑");
        cellStyleFontColumnHeader.setFont(fontColumnHeader);
        cellStyleFontColumnHeader.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        cellStyleFontColumnHeader.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyleFontColumnHeader.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontColumnHeader.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontColumnHeader.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontColumnHeader.setBorderRight(BorderStyle.THIN);//右边框
        

        Row rowTitle = sheet.createRow(0);
        Cell rowTitleCell1 = rowTitle.createCell(0);
        rowTitleCell1.setCellValue(jhYear + "年志愿方案-"+sf+"-"+xm+"-"+pc);
        
        CellStyle cellStyle = workbook.createCellStyle();
        CreationHelper creationHelper = workbook.getCreationHelper();
        cellStyle.setDataFormat(creationHelper.createDataFormat().getFormat("text")); // 对于文本，使用"text"格式以保持文本格式不变
        cellStyle.setAlignment(HorizontalAlignment.CENTER); // 设置水平居中对齐
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直居中对齐（如果需要）
        fontBlueBig.setFontName("微软雅黑");
        cellStyle.setFont(fontBlueBig);
        rowTitleCell1.setCellStyle(cellStyle); // 应用样式到单元格
        
        CellRangeAddress deviceCellRangeTitle = new CellRangeAddress(0, 0, 0, 9);
		sheet.addMergedRegion(deviceCellRangeTitle);
		
		
        //必须设置 否则背景色不生效
        cellStyleFontBlueBig.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        
        
        
        Row rowSubTitle = sheet.createRow(1);
        
        for(int i=0;i<2;i++) {
	        Cell rowSubTitleCell1 = rowSubTitle.createCell(i);
	        rowSubTitleCell1.setCellValue("报考批次");
	        rowSubTitleCell1.setCellStyle(cellStyleFontBlack14);
        }
        for(int i=2;i<3;i++) {
	        Cell rowSubTitleCell1 = rowSubTitle.createCell(i);
	        rowSubTitleCell1.setCellValue(pc);
	        rowSubTitleCell1.setCellStyle(cellStyleFontThinBlack14);
        }
        
        for(int i=3;i<5;i++) {
	        Cell rowSubTitleCell1 = rowSubTitle.createCell(i);
	        rowSubTitleCell1.setCellValue("选科组合");
	        rowSubTitleCell1.setCellStyle(cellStyleFontBlack14);
        }
        for(int i=5;i<6;i++) {
	        Cell rowSubTitleCell1 = rowSubTitle.createCell(i);
	        rowSubTitleCell1.setCellValue(Tools.viewXK(xk));
	        rowSubTitleCell1.setCellStyle(cellStyleFontThinBlack14);
        }
        for(int i=6;i<8;i++) {
        	Cell rowSubTitleCell1 = rowSubTitle.createCell(i);
	        rowSubTitleCell1.setCellValue("成绩");
	        rowSubTitleCell1.setCellStyle(cellStyleFontBlack14);
        }
        for(int i=8;i<10;i++) {
        	Cell rowSubTitleCell1 = rowSubTitle.createCell(i);
	        rowSubTitleCell1.setCellValue(score+"/"+wc);
	        rowSubTitleCell1.setCellStyle(cellStyleFontThinBlack14);
        }

        CellRangeAddress deviceCellRangeSubTitle1 = new CellRangeAddress(1, 1, 0, 1);
		sheet.addMergedRegion(deviceCellRangeSubTitle1);
		
		CellRangeAddress deviceCellRangeSubTitle2 = new CellRangeAddress(1, 1, 3, 4);
		sheet.addMergedRegion(deviceCellRangeSubTitle2);
		
		CellRangeAddress deviceCellRangeSubTitle3 = new CellRangeAddress(1, 1, 6, 7);
		sheet.addMergedRegion(deviceCellRangeSubTitle3);
		
		CellRangeAddress deviceCellRangeSubTitle4 = new CellRangeAddress(1, 1, 8, 9);
		sheet.addMergedRegion(deviceCellRangeSubTitle4);
 
        // 创建一个行，并在其中创建一个单元格
        Row rowOne = sheet.createRow(2);
		Cell rowOneCell1 = rowOne.createCell(0);
		rowOneCell1.setCellValue("志愿顺序");
		rowOneCell1.setCellStyle(cellStyleFontColumnHeader);
		
        Cell rowOneCell2 = rowOne.createCell(1);
        rowOneCell2.setCellValue("院校代码");
        rowOneCell2.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell3 = rowOne.createCell(2);
        rowOneCell3.setCellValue("院校名称");
        rowOneCell3.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell4 = rowOne.createCell(3);
        rowOneCell4.setCellValue("专业顺序");
        rowOneCell4.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell5 = rowOne.createCell(4);
        rowOneCell5.setCellValue("专业代码");
        rowOneCell5.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell6 = rowOne.createCell(5);
        rowOneCell6.setCellValue("专业名称");
        rowOneCell6.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell7 = rowOne.createCell(6);
        rowOneCell7.setCellValue(String.valueOf(jhYear-1)+"年最低分");
        rowOneCell7.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell8 = rowOne.createCell(7);
        rowOneCell8.setCellValue(String.valueOf(jhYear-2)+"年最低分");
        rowOneCell8.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell9 = rowOne.createCell(8);
        rowOneCell9.setCellValue(String.valueOf(jhYear-3)+"年最低分");
        rowOneCell9.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell10 = rowOne.createCell(9);
        rowOneCell10.setCellValue("排名");
        rowOneCell10.setCellStyle(cellStyleFontColumnHeader);
        
        HashMap<Integer, Integer> MAP_YX_ZYZ_SPAN = new HashMap<>(); 
        
        for(int i= 0; i < dataList.size(); i++) {
        	System.out.println("-sf->"+sf);
        	AiTbForm bean = dataList.get(i);
        	if("重庆贵州河北山东浙江辽宁青海C1G4H2L1S1Z1Q1".indexOf(sf) == -1) {
        		MAP_YX_ZYZ_SPAN.put(bean.getSeq_no_yx(), bean.getSeq_no_zy());
        	}
        	
        	System.out.println("-getYxmc->"+bean.getYxmc());
        	
	        Row row = sheet.createRow(i+3);
	        Cell cell1 = row.createCell(0);
	        cell1.setCellValue(bean.getSeq_no_yx());
	        cell1.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        
	        Cell cell2 = row.createCell(1);
	        cell2.setCellValue(bean.getYxdm());
	        cell2.setCellStyle(cellStyleFontHighLight);
	        
	        Cell cell3 = row.createCell(2);
	        cell3.setCellValue(bean.getExt_yxmc());
	        cell3.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        
	        Cell cell4 = row.createCell(3);
	        cell4.setCellValue(bean.getSeq_no_zy());
	        cell4.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);

	        Cell cell5 = row.createCell(4);
	        cell5.setCellValue(bean.getZydm());
	        cell5.setCellStyle(cellStyleFontHighLight);
	        
	        Cell cell6 = row.createCell(5);
	        cell6.setCellValue(bean.getZymc());
	        cell6.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        
	        Cell cell7 = row.createCell(6);
	        cell7.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        
	        if(bean.getZdf_a() < 10) {
	        	cell7.setCellValue("-");
	        }else {
	        	cell7.setCellValue(bean.getZdf_a()+"("+bean.getZdfwc_a()+")");
	        }
	        Cell cell8 = row.createCell(7);
	        cell8.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        
	        if(bean.getZdf_b() < 10) {
	        	cell8.setCellValue("-");
	        }else {
	        	cell8.setCellValue(bean.getZdf_b()+"("+bean.getZdfwc_b()+")");
	        }
	        Cell cell9 = row.createCell(8);
	        cell9.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        
	        if(bean.getZdf_c() < 10) {
	        	cell9.setCellValue("-");
	        }else {
	        	cell9.setCellValue(bean.getZdf_c()+"("+bean.getZdfwc_c()+")");
	        }
	        Cell cell10 = row.createCell(9);
	        cell10.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        
	        if(Tools.isEmpty(bean.getExt_ranking())) {
	        	cell10.setCellValue("-");
	        }else {
	        	cell10.setCellValue(bean.getExt_ranking()+"("+bean.getExt_level()+")");
	        }
        }
        
        int startIndex = 2;
        Iterator<Integer> its = MAP_YX_ZYZ_SPAN.keySet().iterator();
        while(its.hasNext()) {
        	int form_no = its.next();
        	int zy_count = MAP_YX_ZYZ_SPAN.get(form_no);
        	if(zy_count > 1) {
        		CellRangeAddress deviceCellRange0 = new CellRangeAddress(startIndex + 1, startIndex + zy_count, 0, 0);
        		sheet.addMergedRegion(deviceCellRange0);
        		CellRangeAddress deviceCellRange1 = new CellRangeAddress(startIndex + 1, startIndex + zy_count, 1, 1);
        		sheet.addMergedRegion(deviceCellRange1);
        		CellRangeAddress deviceCellRange2 = new CellRangeAddress(startIndex + 1, startIndex + zy_count, 2, 2);
        		sheet.addMergedRegion(deviceCellRange2);
        	}
            startIndex += zy_count;
        }
        

        if(existYxAllDataList.size() > 0) {
        	createExistYxDataAll(workbook, jhYear, existYxAllDataList);
        }
 
        // 写出到文件
        try{
        	FileOutputStream fileOut = new FileOutputStream(filePathAndName);
            workbook.write(fileOut);
            workbook.close();
        } catch (Exception e){
        	e.printStackTrace();
        }finally {
        	try {
            	workbook.close();
            } catch (IOException e){
                e.printStackTrace();
            } 
        }
    }
    
    private static void createExistYxDataAll(XSSFWorkbook workbook, int jhYear, List<JHBean> existYxAllDataList) {
		Sheet sheet = workbook.createSheet("志愿表涉及院校完整数据");
        sheet.setColumnWidth(0, 10*256);
        sheet.setColumnWidth(1, 25*256);
        sheet.setColumnWidth(2, 15*256);
        sheet.setColumnWidth(3, 10*256);
        sheet.setColumnWidth(4, 10*256);
        sheet.setColumnWidth(5, 10*256);
        sheet.setColumnWidth(6, 30*256);
        sheet.setColumnWidth(7, 25*256);
        sheet.setColumnWidth(8, 25*256);
        sheet.setColumnWidth(9, 25*256);
        
        CellStyle cellStyleFontNormal2 = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontNormal2.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontNormal2 = workbook.createFont();
        fontNormal2.setFontHeightInPoints((short) 10);
        fontNormal2.setBold(false);
        fontNormal2.setColor(IndexedColors.BLACK.getIndex());
        fontNormal2.setFontName("微软雅黑");
        cellStyleFontNormal2.setFont(fontNormal2);
        cellStyleFontNormal2.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontNormal2.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontNormal2.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontNormal2.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontNormal1 = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontNormal1.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontNormal = workbook.createFont();
        fontNormal.setFontHeightInPoints((short) 10);
        fontNormal.setBold(false);
        fontNormal.setColor(IndexedColors.BLACK.getIndex());
        fontNormal.setFontName("微软雅黑");
        cellStyleFontNormal1.setFont(fontNormal);
        cellStyleFontNormal1.setFillForegroundColor(IndexedColors.LEMON_CHIFFON.getIndex());
        cellStyleFontNormal1.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontNormal1.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontNormal1.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontNormal1.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontColumnHeader = workbook.createCellStyle();
        cellStyleFontColumnHeader.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontColumnHeader = workbook.createFont();
        fontColumnHeader.setFontHeightInPoints((short) 12);
        fontColumnHeader.setBold(true);
        fontColumnHeader.setColor(IndexedColors.BLACK.getIndex());
        fontColumnHeader.setFontName("微软雅黑");
        cellStyleFontColumnHeader.setFont(fontColumnHeader);
        cellStyleFontColumnHeader.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        cellStyleFontColumnHeader.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyleFontColumnHeader.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontColumnHeader.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontColumnHeader.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontColumnHeader.setBorderRight(BorderStyle.THIN);//右边框
        
        
        // 创建一个行，并在其中创建一个单元格
        Row rowOne = sheet.createRow(0);
        Cell rowOneCell1 = rowOne.createCell(0);
		rowOneCell1.setCellValue("院校代码");
		rowOneCell1.setCellStyle(cellStyleFontColumnHeader);
		
        Cell rowOneCell2 = rowOne.createCell(1);
        rowOneCell2.setCellValue("院校名称");
        rowOneCell2.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell3 = rowOne.createCell(2);
        rowOneCell3.setCellValue("所属区域");
        rowOneCell3.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell4 = rowOne.createCell(3);
        rowOneCell4.setCellValue("专业组");
        rowOneCell4.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell5 = rowOne.createCell(4);
        rowOneCell5.setCellValue("选科");
        rowOneCell5.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell6 = rowOne.createCell(5);
        rowOneCell6.setCellValue("专业代码");
        rowOneCell6.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell7 = rowOne.createCell(6);
        rowOneCell7.setCellValue("专业名称");
        rowOneCell7.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell8 = rowOne.createCell(7);
        rowOneCell8.setCellValue(String.valueOf(jhYear-1)+"年最低分");
        rowOneCell8.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell9 = rowOne.createCell(8);
        rowOneCell9.setCellValue(String.valueOf(jhYear-2)+"年最低分");
        rowOneCell9.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell10 = rowOne.createCell(9);
        rowOneCell10.setCellValue(String.valueOf(jhYear-3)+"年最低分");
        rowOneCell10.setCellStyle(cellStyleFontColumnHeader);
        
        int index = 0;
        HashMap<String, Integer> MAPX = new HashMap<>();
        for(int i= 0; i < existYxAllDataList.size(); i++) {
        	JHBean bean = existYxAllDataList.get(i);
        	if(!MAPX.containsKey(bean.getYxmc())) {
        		MAPX.put(bean.getYxmc(), index++);
        	}
        }
        
        for(int i= 0; i < existYxAllDataList.size(); i++) {
        	JHBean bean = existYxAllDataList.get(i);
        	CellStyle tempCell = cellStyleFontNormal1;
        	
        	int kk = MAPX.get(bean.getYxmc());
        	
        	if(kk % 2 == 0) {
        		tempCell = cellStyleFontNormal2;
        	}else {
        		tempCell = cellStyleFontNormal1;
        	}
        	
	        Row row = sheet.createRow(i+1);
	        Cell cell1 = row.createCell(0);
	        cell1.setCellValue(bean.getYxdm());
	        cell1.setCellStyle(tempCell);
	        
	        Cell cell2 = row.createCell(1);
	        cell2.setCellValue(bean.getYxmc());
	        cell2.setCellStyle(tempCell);
	        
	        Cell cell3 = row.createCell(2);
	        if(Tools.isEmpty(bean.getYxcs())) {
	        	cell3.setCellValue(bean.getYxsf());
	        }else {
	        	cell3.setCellValue(bean.getYxsf()+"."+bean.getYxcs());
	        }
	        cell3.setCellStyle(tempCell);
	        
	        Cell cell4 = row.createCell(3);
	        cell4.setCellValue(bean.getZyz());
	        cell4.setCellStyle(tempCell);
	        
	        Cell cell5 = row.createCell(4);
	        cell5.setCellValue(bean.getZx());
	        cell5.setCellStyle(tempCell);

	        Cell cell6 = row.createCell(5);
	        cell6.setCellValue(bean.getZydm());
	        cell6.setCellStyle(tempCell);
	        
	        Cell cell7 = row.createCell(6);
	        cell7.setCellValue(bean.getZymc());
	        cell7.setCellStyle(tempCell);
	        
	        Cell cell8 = row.createCell(7);
	        cell8.setCellStyle(tempCell);
	        if(Tools.getInt(bean.getZdf_a()) < 10) {
	        	cell8.setCellValue("-");
	        }else {
	        	cell8.setCellValue(bean.getZdf_a()+"("+bean.getZdfwc_a()+")");
	        }
	        Cell cell9 = row.createCell(8);
	        cell9.setCellStyle(tempCell);
	        
	        if(Tools.getInt(bean.getZdf_b()) < 10) {
	        	cell9.setCellValue("-");
	        }else {
	        	cell9.setCellValue(bean.getZdf_b()+"("+bean.getZdfwc_b()+")");
	        }
	        
	        Cell cell10 = row.createCell(9);
	        cell10.setCellStyle(tempCell);
	        
	        if(Tools.getInt(bean.getZdf_c()) < 10) {
	        	cell10.setCellValue("-");
	        }else {
	        	cell10.setCellValue(bean.getZdf_c()+"("+bean.getZdfwc_c()+")");
	        }
        }
    }
	
	
	public static void createForm96ReportExcel(int jhYear, String filePathAndName,  List<LhyForm> dataList) {
		// 创建一个新的工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        Tools.println(filePathAndName);
        // 创建一个工作表
        Sheet sheet = workbook.createSheet("志愿表");
 
        // 创建一个行，并在其中创建一个单元格
        Row rowOne = sheet.createRow(0);
		Cell rowOneCell1 = rowOne.createCell(0);
		rowOneCell1.setCellValue("志愿顺序");
        Cell rowOneCell2 = rowOne.createCell(1);
        rowOneCell2.setCellValue("院校名称");
        Cell rowOneCell3 = rowOne.createCell(2);
        rowOneCell3.setCellValue("排名");
        Cell rowOneCell4 = rowOne.createCell(3);
        rowOneCell4.setCellValue("专业名称");
        Cell rowOneCell5 = rowOne.createCell(4);
        rowOneCell5.setCellValue(String.valueOf(jhYear-1));
        Cell rowOneCell6 = rowOne.createCell(5);
        rowOneCell6.setCellValue(String.valueOf(jhYear-2));
        Cell rowOneCell7 = rowOne.createCell(6);
        rowOneCell7.setCellValue(String.valueOf(jhYear-3));
        
        for(int i= 0; i < dataList.size(); i++) {
        	LhyForm bean = dataList.get(i);
	        Row row = sheet.createRow(i+1);
	        Cell cell1 = row.createCell(0);
	        cell1.setCellValue(bean.getSeq_no_yx());
	        Cell cell2 = row.createCell(1);
	        cell2.setCellValue(bean.getYxmc());
	        Cell cell3 = row.createCell(2);
	        cell3.setCellValue(bean.getExt_ranking()+"("+bean.getExt_level()+")");
	        Cell cell4 = row.createCell(3);
	        cell4.setCellValue(bean.getZymc());
	        Cell cell5 = row.createCell(4);
	        cell5.setCellValue(bean.getZdf_a()+"("+bean.getZdfwc_a()+")");
	        Cell cell6 = row.createCell(5);
	        cell6.setCellValue(bean.getZdf_b()+"("+bean.getZdfwc_b()+")");
	        Cell cell7 = row.createCell(6);
	        cell7.setCellValue(bean.getZdf_c()+"("+bean.getZdfwc_c()+")");
        }
 
        // 写出到文件
        try{
        	FileOutputStream fileOut = new FileOutputStream(filePathAndName);
            workbook.write(fileOut);
            workbook.close();
        } catch (Exception e){
        	e.printStackTrace();
        }finally {
        	try {
            	workbook.close();
            } catch (IOException e){
                e.printStackTrace();
            } 
        }
    }

}
