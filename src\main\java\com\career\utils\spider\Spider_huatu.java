package com.career.utils.spider;

import java.io.*;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.career.utils.HttpSendUtils;
import com.career.utils.Tools;
import com.career.utils.liuxue.HttpSendUtils2;

public class Spider_huatu {
	
	 static StringBuffer exception = new StringBuffer();
	
	 //华图教育
	 public static StringBuffer huatu() {
			Map<String, String> headers = new HashMap<>();
			StringBuffer SQL = new StringBuffer();

			for(int page=1;page<=370;page++) {
				System.out.println(">>>>>"+page);
				int time = (int)(Math.random() * 15000);
				try {
					if(page % 10 == 0) { 
						Thread.sleep(time);
					}
				} catch (InterruptedException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				String pageResult = HttpSendUtils2.get("https://sc.huatu.com/skzwb/2025/search/search.php?act=geren&page=" + page, headers);
	
				Document documentList = Jsoup.parse(pageResult);
	
				Elements aiticleList = documentList.getElementsByClass("table zwssjg pc_show");
				Elements theadTh = aiticleList.get(0).getElementsByTag("thead").get(0).getElementsByTag("th");
				Elements tbodyTr = aiticleList.get(0).getElementsByTag("tbody").get(0).getElementsByTag("tr");
				
				HashMap<Integer, String> MAP_TITLE = new LinkedHashMap<>();
				for(int i=0;i<theadTh.size();i++) {
					Element th = theadTh.get(i);
					MAP_TITLE.put(i, th.text());
				}
				
				
				for(int i=0;i<tbodyTr.size();i++) {
					String uuid = UUID.randomUUID().toString();
					Element tr = tbodyTr.get(i);
					Elements tds = tr.getElementsByTag("td"); 
					for(int index = 0;index < tds.size();index++) {
						String thText = MAP_TITLE.get(index);
						Element td = tds.get(index);
						SQL.append("insert into career_job_gwy_req_org(nf, group_id, page_no, record_seq, sf, job_lx, job_type, job_title, job_descp) values(2025, '"+uuid+"', "+page+", "+(i+1)+",'四川','公务员','省考','"+thText+"','"+td.text()+"');\r\n");
					}
					
					
				}
			}
			
			
			writeTempFile(new File("F://就业报告//HUATU/PAGE_SC.txt"), SQL);
			
			return SQL;
		}

		

		public static void main(String[] args) {
			/**
			StringBuffer SQL = new StringBuffer();
			try {
				for(int i=1000;i<4000;i++) {
					String rs = doit("https://www.crs.jsj.edu.cn/aproval/detail/" + i).toString();
					if(!Tools.isEmpty(rs)) {
						SQL.append(rs + "\r\n");
					}
				}
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			writeTempFile(new File("E:\\kaogong\\SQL1225_2.txt"), SQL);
			writeTempFile(new File("E:\\kaogong\\exp1225_2.txt"), exception);
			*/
			
			try {
				huatu();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		private static void writeTempFile(File file, StringBuffer sb) {
			try {
				BufferedWriter bw = new BufferedWriter(new FileWriter(file));
				bw.write(sb.toString());
				bw.flush();
				bw.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}

}
