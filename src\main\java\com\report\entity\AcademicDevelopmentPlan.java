package com.report.entity;

import java.util.List;
import java.util.ArrayList;

/**
 * 学术发展规划实体类
 * 基于保研、考研、学术发展相关内容设计
 */
public class AcademicDevelopmentPlan {
    
    // 保研规划
    private GraduateRecommendationPlan gradRecommendationPlan;
    
    // 考研规划
    private GraduateExamPlan gradExamPlan;
    
    // 科研规划
    private ResearchPlan researchPlan;
    
    // 论文发表计划
    private List<PaperPublicationPlan> paperPlans;
    
    // 学术能力提升计划
    private List<String> academicSkillsDevelopment;
    
    public AcademicDevelopmentPlan() {
        this.gradRecommendationPlan = new GraduateRecommendationPlan();
        this.gradExamPlan = new GraduateExamPlan();
        this.researchPlan = new ResearchPlan();
        this.paperPlans = new ArrayList<>();
        this.academicSkillsDevelopment = new ArrayList<>();
    }
    
    /**
     * 保研规划
     */
    public static class GraduateRecommendationPlan {
        private List<String> requirements;          // 保研要求
        private List<String> preparationSteps;     // 准备步骤
        private List<String> timeline;             // 时间安排
        private List<String> targetUniversities;   // 目标院校
        private String gpaRequirement;            // GPA要求
        private List<String> extracurriculars;    // 课外活动要求
        
        public GraduateRecommendationPlan() {
            this.requirements = new ArrayList<>();
            this.preparationSteps = new ArrayList<>();
            this.timeline = new ArrayList<>();
            this.targetUniversities = new ArrayList<>();
            this.extracurriculars = new ArrayList<>();
            
            initializeBasicInfo();
        }
        
        private void initializeBasicInfo() {
            // 基本要求
            requirements.add("学习成绩排名前10%-30%");
            requirements.add("无挂科记录");
            requirements.add("英语四六级成绩优秀");
            requirements.add("有科研经历或竞赛获奖");
            requirements.add("有学生工作或社会实践经历");
            
            // 准备步骤
            preparationSteps.add("大一大二：保持优异成绩，积极参加竞赛");
            preparationSteps.add("大二大三：参与科研项目，发表论文");
            preparationSteps.add("大三上：准备夏令营申请材料");
            preparationSteps.add("大三下：参加夏令营，联系导师");
            preparationSteps.add("大四上：确认推免资格，完成申请");
            
            // 时间安排
            timeline.add("大三上学期（1-3月）：搜集信息，确定目标");
            timeline.add("大三下学期（3-5月）：准备夏令营申请材料，关注目标院校通知。");
            timeline.add("大三暑期（6-8月）：参加高校夏令营，争取优秀营员资格。");
            timeline.add("大四上学期（9月）：关注并参加各校预推免报名和考核。");
            timeline.add("大四上学期（9-10月）：教育部“推免服务系统”开放，进行正式报名和录取确认。");
        }
        
        // Getters and Setters
        public List<String> getRequirements() { return requirements; }
        public void setRequirements(List<String> requirements) { this.requirements = requirements; }
        public List<String> getPreparationSteps() { return preparationSteps; }
        public void setPreparationSteps(List<String> preparationSteps) { this.preparationSteps = preparationSteps; }
        public List<String> getTimeline() { return timeline; }
        public void setTimeline(List<String> timeline) { this.timeline = timeline; }
        public List<String> getTargetUniversities() { return targetUniversities; }
        public void setTargetUniversities(List<String> targetUniversities) { this.targetUniversities = targetUniversities; }
        public String getGpaRequirement() { return gpaRequirement; }
        public void setGpaRequirement(String gpaRequirement) { this.gpaRequirement = gpaRequirement; }
        public List<String> getExtracurriculars() { return extracurriculars; }
        public void setExtracurriculars(List<String> extracurriculars) { this.extracurriculars = extracurriculars; }
    }
    
    /**
     * 考研规划
     */
    public static class GraduateExamPlan {
        private List<String> preparationSteps;     // 准备步骤
        private List<String> timeline;             // 时间安排
        private List<String> subjects;             // 考试科目
        private List<String> studyPlan;            // 学习计划
        private List<String> targetUniversities;   // 目标院校
        
        public GraduateExamPlan() {
            this.preparationSteps = new ArrayList<>();
            this.timeline = new ArrayList<>();
            this.subjects = new ArrayList<>();
            this.studyPlan = new ArrayList<>();
            this.targetUniversities = new ArrayList<>();
            
            initializeBasicInfo();
        }
        
        private void initializeBasicInfo() {
            // 准备步骤
            preparationSteps.add("确定目标院校和专业");
            preparationSteps.add("了解考试科目和考试大纲");
            preparationSteps.add("制定详细的复习计划");
            preparationSteps.add("购买相关教材和资料");
            preparationSteps.add("报名参加考试");
            preparationSteps.add("全力备考复习");
            preparationSteps.add("参加初试");
            preparationSteps.add("准备复试");
            
            // 时间安排
            timeline.add("大三学年（当前-6月）：确定考研目标，复习英语、数学，开始第一轮专业课复习。");
            timeline.add("大三暑假（7-8月）：制定强化复习计划，完成各科目的系统复习。");
            timeline.add("大四上学期（9-10月）：考研大纲发布，进行网上报名和确认，开始真题演练。");
            timeline.add("大四上学期（11-12月）：进行模拟考试，查漏补缺，打印准考证，参加初试。");
            timeline.add("大四下学期（2-4月）：初试成绩公布，准备复试或联系调剂。");
            timeline.add("大四下学期（5-6月）：陆续收到录取通知书。");
            
            // 考试科目
            subjects.add("思想政治理论");
            subjects.add("英语一/英语二");
            subjects.add("数学一/数学二/数学三（理工类）");
            subjects.add("专业课（根据专业确定）");
        }
        
        // Getters and Setters
        public List<String> getPreparationSteps() { return preparationSteps; }
        public void setPreparationSteps(List<String> preparationSteps) { this.preparationSteps = preparationSteps; }
        public List<String> getTimeline() { return timeline; }
        public void setTimeline(List<String> timeline) { this.timeline = timeline; }
        public List<String> getSubjects() { return subjects; }
        public void setSubjects(List<String> subjects) { this.subjects = subjects; }
        public List<String> getStudyPlan() { return studyPlan; }
        public void setStudyPlan(List<String> studyPlan) { this.studyPlan = studyPlan; }
        public List<String> getTargetUniversities() { return targetUniversities; }
        public void setTargetUniversities(List<String> targetUniversities) { this.targetUniversities = targetUniversities; }
    }
    
    /**
     * 科研规划
     */
    public static class ResearchPlan {
        private List<String> researchAreas;        // 研究领域
        private List<String> researchProjects;     // 研究项目
        private List<String> supervisors;          // 指导老师
        private List<String> researchSkills;       // 科研技能
        private String researchGoal;              // 科研目标
        
        public ResearchPlan() {
            this.researchAreas = new ArrayList<>();
            this.researchProjects = new ArrayList<>();
            this.supervisors = new ArrayList<>();
            this.researchSkills = new ArrayList<>();
        }
        
        // Getters and Setters
        public List<String> getResearchAreas() { return researchAreas; }
        public void setResearchAreas(List<String> researchAreas) { this.researchAreas = researchAreas; }
        public List<String> getResearchProjects() { return researchProjects; }
        public void setResearchProjects(List<String> researchProjects) { this.researchProjects = researchProjects; }
        public List<String> getSupervisors() { return supervisors; }
        public void setSupervisors(List<String> supervisors) { this.supervisors = supervisors; }
        public List<String> getResearchSkills() { return researchSkills; }
        public void setResearchSkills(List<String> researchSkills) { this.researchSkills = researchSkills; }
        public String getResearchGoal() { return researchGoal; }
        public void setResearchGoal(String researchGoal) { this.researchGoal = researchGoal; }
    }
    
    /**
     * 论文发表计划
     */
    public static class PaperPublicationPlan {
        private String title;                  // 论文题目
        private String journalTarget;          // 目标期刊
        private String researchArea;           // 研究领域
        private String timeline;               // 发表时间线
        private String status;                 // 当前状态
        private List<String> coAuthors;        // 合作作者
        
        public PaperPublicationPlan() {
            this.coAuthors = new ArrayList<>();
        }
        
        // Getters and Setters
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getJournalTarget() { return journalTarget; }
        public void setJournalTarget(String journalTarget) { this.journalTarget = journalTarget; }
        public String getResearchArea() { return researchArea; }
        public void setResearchArea(String researchArea) { this.researchArea = researchArea; }
        public String getTimeline() { return timeline; }
        public void setTimeline(String timeline) { this.timeline = timeline; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public List<String> getCoAuthors() { return coAuthors; }
        public void setCoAuthors(List<String> coAuthors) { this.coAuthors = coAuthors; }
    }
    
    // Getters and Setters
    public GraduateRecommendationPlan getGradRecommendationPlan() { return gradRecommendationPlan; }
    public void setGradRecommendationPlan(GraduateRecommendationPlan gradRecommendationPlan) { 
        this.gradRecommendationPlan = gradRecommendationPlan; 
    }
    
    public GraduateExamPlan getGradExamPlan() { return gradExamPlan; }
    public void setGradExamPlan(GraduateExamPlan gradExamPlan) { this.gradExamPlan = gradExamPlan; }
    
    public ResearchPlan getResearchPlan() { return researchPlan; }
    public void setResearchPlan(ResearchPlan researchPlan) { this.researchPlan = researchPlan; }
    
    public List<PaperPublicationPlan> getPaperPlans() { return paperPlans; }
    public void setPaperPlans(List<PaperPublicationPlan> paperPlans) { this.paperPlans = paperPlans; }
    
    public List<String> getAcademicSkillsDevelopment() { return academicSkillsDevelopment; }
    public void setAcademicSkillsDevelopment(List<String> academicSkillsDevelopment) { 
        this.academicSkillsDevelopment = academicSkillsDevelopment; 
    }
}