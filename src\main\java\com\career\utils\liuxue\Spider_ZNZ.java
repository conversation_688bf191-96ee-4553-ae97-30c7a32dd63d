package com.career.utils.liuxue;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.career.utils.HttpSendUtils;

//抓取指南者留学的爬虫
public class Spider_ZNZ {

	public static void main(String args[]) throws Exception {
		// dealWithHEZUOBANXUE();
		for (int i = 10; i <= 10; i++) {
			gogo(i);
		}
	}

	// 抓取指南者留学
	public static void gogo(int page) {
		Map<String, String> headers = new HashMap<>();
		Map<String, String> params = new HashMap<>();
		StringBuffer sql1 = new StringBuffer();
		params.put("ctry", "0");
		params.put("pa", "p" + page);

		headers.put(":authority", "pc.compassedu.hk");
		headers.put(":path", "/v/offer/gainList");
		headers.put("Accept", "*/*");
		headers.put("Accept-Encoding", "gzip, deflate, br, zstd");
		headers.put("Accept-Language", "zh-CN,zh;q=0.9");
		headers.put("Cache-Control", "no-cache");
		headers.put("Connection", "keep-alive");
		headers.put("User-Agent",
				"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
		// headers.put("Cookie",
		// "PW9ydXnjjO8XS=60abeMZbhNbBWRNi2hyfp32UB3dRoh5PEK12eiwEWmjK4bVv2bCaL9YrMVuFyaqrwLzlwapiBS5qwfCGXSi1WSRG;
		// PW9ydXnjjO8XT=0J5FTTMAKarsz4pOMoe13Ox4JuuFiqvPupWWYrD5IUhCUXGuvsrpAGLX.ZBkJxcBn_Fy8Jt_syIQOkZUBlAtaGgJ5zrgYXAB_.Yy6jQX1WAthkI2Gr6R3DMiuamBYa4ro5uIiTPQ.8q56klzt7RqomLosrSIkeLLbSDFRFbPzGTR14LX1apGcXEGJpS6geR5vOSc3kfQBlnrSVJmNRuiPJvyC_Us9KgCda0djNBD.6Zra8tjMPhKv522M6TwfHZ9ppVyhWfk1x81PwdRZkLiJEvfyxU3oZCUXlU1ZKT3CNsEmKstyzzZwKDTNlGn1s8TCCiTiMPkVel7m6431R94Bmkuf2etz4ysnSEUW0OUxKnaVArvY6JX2vqeHcsSAMlkWGwV4uLkBkq273kh51UhVLa");

		String url = "https://pc.compassedu.hk/v/offer/gainList";
		String res22 = HttpSendUtils.post(url, params, headers);
		JSONObject object = JSONObject.parseObject(res22);
		JSONArray JSONArray = object.getJSONArray("result");
		for (int m = 0; m < JSONArray.size(); m++) {
			JSONObject ele = JSONArray.getJSONObject(m);
			// System.out.println(ele.toString());
			String id_news = (String) ele.getString("id_news");

			if (Integer.parseInt(id_news) <= 35621) {
				continue;
			}

			String id_univ = (String) ele.getString("id_univ");
			String title_news = (String) ele.getString("title_news");
			String cname_news = (String) ele.getString("cname_news");
			String cuniv_news = (String) ele.getString("cuniv_news");
			String cmajr_news = (String) ele.getString("cmajr_news");
			String detail_news = (String) ele.getString("detail_news");
			String time_news = (String) ele.getString("time_news");
			String appdate_new = (String) ele.getString("appdate_new");
			String univrank_news = (String) ele.getString("univrank_news");
			String time_list = (String) ele.getString("time_list");

			sql1.append(
					"insert into career_lx_cases(id_news,id_univ,title_news,cname_news,cuniv_news,cmajr_news,detail_news,time_news,appdate_new,univrank_news,time_list) values('"
							+ id_news + "','" + id_univ + "','" + title_news + "','" + cname_news + "','" + cuniv_news
							+ "','" + cmajr_news + "','" + detail_news + "','" + time_news + "','" + appdate_new + "','"
							+ univrank_news + "','" + time_list + "');\r\n");

			System.out.println(page + "->" + id_news);

			String result = HttpSendUtils.get("https://www.compassedu.hk/newst_" + id_news, headers);

			Document document = Jsoup.parse(result);

			try {
				Elements detail__module__studetail = document.getElementsByClass("detail__module__studetail");
				if (detail__module__studetail != null && detail__module__studetail.size() > 0) {
					Elements xxx = detail__module__studetail.get(0).getElementsByClass("info-item");
					String school = xxx.get(2).getElementsByClass("value center").get(0).text();
					String major = xxx.get(3).getElementsByClass("value text-href").get(0).text();
					sql1.append("insert into career_lx_cases_ext values('" + id_news + "','院校专业','" + school + "','"
							+ major + "');\r\n");
				}
			} catch (Exception ex) {
			}

			Elements valueExp = document.getElementsByClass("value-exp");
			if (valueExp != null && valueExp.size() > 0) {
				Elements valueItems = valueExp.get(0).getElementsByClass("value-item");
				for (int i = 0; i < valueItems.size(); i++) {
					String val = valueItems.get(i).text();
					sql1.append("insert into career_lx_cases_ext values('" + id_news + "','主要经历','" + (i + 1) + "','"
							+ val + "');\r\n");
				}
			}

			Elements detail__module__caseproject = document.getElementsByClass("detail__module__caseproject");
			if (detail__module__caseproject != null && detail__module__caseproject.size() > 0) {
				Elements projectList = detail__module__caseproject.get(0).getElementsByClass("project-list");
				Elements labels = projectList.get(0).getElementsByClass("label");
				Elements values = projectList.get(0).getElementsByClass("value");
				for (int i = 0; i < labels.size(); i++) {
					String label = labels.get(i).text();
					String value = values.get(i).text();
					sql1.append("insert into career_lx_cases_ext values('" + id_news + "','项目简介','" + label + "','"
							+ value + "');\r\n");

				}
			}

			Elements detail__module__limits = document.getElementsByClass("detail__module__limits");
			if (detail__module__limits != null && detail__module__limits.size() > 0) {
				Elements richtexts = detail__module__limits.get(0).getElementsByClass("richtext");
				for (int i = 0; i < richtexts.size(); i++) {
					String richtext = richtexts.get(i).text();
					sql1.append("insert into career_lx_cases_ext values('" + id_news + "','申请要求','" + (i + 1) + "','"
							+ richtext + "');\r\n");

				}
			}

			Elements detail__module__cultivate = document.getElementsByClass("detail__module__cultivate");
			if (detail__module__cultivate != null && detail__module__cultivate.size() > 0) {
				Elements richtexts = detail__module__cultivate.get(0).getElementsByClass("richtext");
				for (int i = 0; i < richtexts.size(); i++) {
					String richtext = richtexts.get(i).text();
					sql1.append("insert into career_lx_cases_ext values('" + id_news + "','培养目标','" + (i + 1) + "','"
							+ richtext + "');\r\n");
				}
			}

			Elements detail__module__language = document.getElementsByClass("detail__module__language");
			if (detail__module__language != null && detail__module__language.size() > 0) {
				Elements rowItems = detail__module__language.get(0).getElementsByClass("row-item");
				for (int i = 0; i < rowItems.size(); i++) {
					Elements text = rowItems.get(i).getElementsByClass("text");
					String mc = text.get(0).text();

					String zf = text.get(1).text();

					String xf = text.get(2).text();

					sql1.append("insert into career_lx_cases_ext values('" + id_news + "','" + mc + "','" + zf + "','"
							+ xf + "');\r\n");
				}
			}

		}

		writeTempFile(new File("G://留学案例库//SQL0929_" + page + ".txt"), sql1);
		// https://www.compassedu.hk/newst_35068
	}

	private static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
}
