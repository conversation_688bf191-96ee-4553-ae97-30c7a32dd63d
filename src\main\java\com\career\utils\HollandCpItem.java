package com.career.utils;

public class HollandCpItem implements java.util.Comparator<HollandCpItem> {

    public int compare(HollandCpItem o1, HollandCpItem o2) {
        return o1.getTotal() - o2.getTotal();//降序
    }

	private String code;
	private int total;
	private int xg_cnt;
	private int nl_cnt;
	
	public HollandCpItem(String code, int total, int xg_cnt, int nl_cnt) {
		this.code = code;
		this.total = total;
		this.xg_cnt = xg_cnt;
		this.nl_cnt = nl_cnt;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public int getTotal() {
		return total;
	}
	public void setTotal(int total) {
		this.total = total;
	}
	public int getXg_cnt() {
		return xg_cnt;
	}
	public void setXg_cnt(int xg_cnt) {
		this.xg_cnt = xg_cnt;
	}
	public int getNl_cnt() {
		return nl_cnt;
	}
	public void setNl_cnt(int nl_cnt) {
		this.nl_cnt = nl_cnt;
	}
	
	
}
