package com.career.utils.excel.model;

import com.career.utils.excel.annotation.ExcelColumn;
import java.io.Serializable;
import java.util.Calendar;

public class JHBeanForm implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    // 年份变量配置 - 动态设置
    private static int CURRENT_YEAR = Calendar.getInstance().get(Calendar.YEAR);
    private static int YEAR_A = CURRENT_YEAR - 1;
    private static int YEAR_B = CURRENT_YEAR - 2;
    private static int YEAR_C = CURRENT_YEAR - 3;
    
    // 动态年份标题获取方法
    public static String getCurrentYearHeader() {
        return CURRENT_YEAR + "年招生计划";
    }
    
    public static String getYearAHeader() {
        return YEAR_A + "年数据";
    }
    
    public static String getYearBHeader() {
        return YEAR_B + "年数据";
    }
    
    public static String getYearCHeader() {
        return YEAR_C + "年数据";
    }
    
    private static final String UNIVERSITY_DETAIL_HEADER = "院校详细信息";
    
    /**
     * 设置当前年份，会自动计算其他年份
     * @param currentYear 当前年份
     * 
     */
    public static void setCurrentYear(int currentYear) {
        CURRENT_YEAR = currentYear;
        YEAR_A = CURRENT_YEAR - 1;
        YEAR_B = CURRENT_YEAR - 2;
        YEAR_C = CURRENT_YEAR - 3;
    }
    
    /**
     * 获取当前设置的年份
     * @return 当前年份
     */
    public static int getCurrentYear() {
        return CURRENT_YEAR;
    }
    
    /**
     * 获取动态标题映射，用于Excel导出时替换占位符
     * @return 标题映射
     */
    public static java.util.Map<String, String> getHeaderMapping() {
        java.util.Map<String, String> mapping = new java.util.HashMap<>();
        mapping.put("CURRENT_YEAR_HEADER", getCurrentYearHeader());
        mapping.put("YEAR_A_HEADER", getYearAHeader());
        mapping.put("YEAR_B_HEADER", getYearBHeader());
        mapping.put("YEAR_C_HEADER", getYearCHeader());
        return mapping;
    }

    // 默认构造器
    public JHBeanForm() {
    }

    // ===== 基本信息 =====
    private Integer id;
    
    @ExcelColumn(name = "省份", sort = 1, width = 10, parent = "CURRENT_YEAR_HEADER", level = 1)
    private String sf;
    
    @ExcelColumn(name = "年份", sort = 2, width = 10, parent = "CURRENT_YEAR_HEADER", level = 1)
    private String nf;
    
    @ExcelColumn(name = "院校代码", sort = 3, width = 10, parent = "CURRENT_YEAR_HEADER", level = 1)
    private String yxdm;
    
    @ExcelColumn(name = "院校名称", sort = 4, width = 20, parent = "CURRENT_YEAR_HEADER", level = 1)
    private String yxmc;
    
    private String yxmc_org;
    
    @ExcelColumn(name = "专业代码", sort = 5, width = 10, parent = "CURRENT_YEAR_HEADER", level = 1)
    private String zydm;
  
    @ExcelColumn(name = "专业名称", sort = 6, width = 20, parent = "CURRENT_YEAR_HEADER", level = 1)
    private String zymc;
    
    private String zymc_org;
    
    @ExcelColumn(name = "专业组", sort = 7, width = 12, parent = "CURRENT_YEAR_HEADER", level = 1)
    private String zyz;
    
    @ExcelColumn(name = "专业门类", sort = 8, width = 15, parent = "CURRENT_YEAR_HEADER", level = 1)
    private String zyml;
    
    private String znzy;
    
    @ExcelColumn(name = "专业备注", sort = 9, width = 30, parent = "CURRENT_YEAR_HEADER", level = 1)
    private String zybz;
    
    @ExcelColumn(name = "批次", sort = 10, width = 10, parent = "CURRENT_YEAR_HEADER", level = 1)
    private String pc;
    
    private String pc_code;
    
    @ExcelColumn(name = "科类", sort = 11, width = 10, parent = "CURRENT_YEAR_HEADER", level = 1)
    private String xk;
    
    @ExcelColumn(name = "选科", sort = 12, width = 10, parent = "CURRENT_YEAR_HEADER", level = 1)
    private String zx;
    
    private String lqpc;

    // ===== 当前年度数据 =====
    @ExcelColumn(name = "计划数", sort = 13, width = 10, parent = "CURRENT_YEAR_HEADER", level = 1)
    private String jhs;
    
    @ExcelColumn(name = "学费", sort = 14, width = 10, parent = "CURRENT_YEAR_HEADER", level = 1)
    private String fee;
    
    @ExcelColumn(name = "学制", sort = 15, width = 10, parent = "CURRENT_YEAR_HEADER", level = 1)
    private String xz;
    
    private Integer zdf;
    
    private Integer zdfwc;

    // ===== A年数据 =====
    @ExcelColumn(name = "计划数", sort = 16, width = 10, parent = "YEAR_A_HEADER", level = 1)
    private String jhs_a;
    
    private String xz_a;
    private String fee_a;
    
    @ExcelColumn(name = "最低分", sort = 17, width = 10, parent = "YEAR_A_HEADER", level = 1)
    private String zdf_a;
    
    @ExcelColumn(name = "最低分位次", sort = 18, width = 11, parent = "YEAR_A_HEADER", level = 1)
    private String zdfwc_a;
    
    @ExcelColumn(name = "趋势等位分", sort = 19, width = 11, parent = "YEAR_A_HEADER", level = 1)
    private Integer qsf_a;
    
    private String zgf_a;
    
    private String zgfwc_a;
    
    @ExcelColumn(name = "平均分", sort = 20, width = 10, parent = "YEAR_A_HEADER", level = 1)
    private String pjf_a;
    
    @ExcelColumn(name = "平均分位次", sort = 21, width = 11, parent = "YEAR_A_HEADER", level = 1)
    private String pjfwc_a;

    // ===== B年数据 =====
    @ExcelColumn(name = "计划数", sort = 22, width = 10, parent = "YEAR_B_HEADER", level = 1)
    private String jhs_b;
    
    @ExcelColumn(name = "最低分", sort = 23, width = 10, parent = "YEAR_B_HEADER", level = 1)
    private String zdf_b;
    
    @ExcelColumn(name = "最低分位次", sort = 24, width = 11, parent = "YEAR_B_HEADER", level = 1)
    private String zdfwc_b;
    
    @ExcelColumn(name = "趋势等位分", sort = 25, width = 11, parent = "YEAR_B_HEADER", level = 1)
    private Integer qsf_b;
    
    private String zgf_b;
    
    private String zgfwc_b;
    
    @ExcelColumn(name = "平均分", sort = 26, width = 10, parent = "YEAR_B_HEADER", level = 1)
    private String pjf_b;
    
    @ExcelColumn(name = "平均分位次", sort = 27, width = 11, parent = "YEAR_B_HEADER", level = 1)
    private String pjfwc_b;

    // ===== C年数据 =====
    @ExcelColumn(name = "计划数", sort = 28, width = 10, parent = "YEAR_C_HEADER", level = 1)
    private String jhs_c;
    
    @ExcelColumn(name = "最低分", sort = 29, width = 10, parent = "YEAR_C_HEADER", level = 1)
    private String zdf_c;
    
    @ExcelColumn(name = "最低分位次", sort = 30, width = 11, parent = "YEAR_C_HEADER", level = 1)
    private String zdfwc_c;
    
    @ExcelColumn(name = "趋势等位分", sort = 31, width = 11, parent = "YEAR_C_HEADER", level = 1)
    private Integer qsf_c;
    
    private String zgf_c;
    
    private String zgfwc_c;
    
    @ExcelColumn(name = "平均分", sort = 32, width = 10, parent = "YEAR_C_HEADER", level = 1)
    private String pjf_c;
    
    @ExcelColumn(name = "平均分位次", sort = 33, width = 11, parent = "YEAR_C_HEADER", level = 1)
    private String pjfwc_c;


    // ===== 院校详细信息 =====
    @ExcelColumn(name = "院校性质", sort = 34, width = 12, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private String ind_nature;
    
    @ExcelColumn(name = "院校类型", sort = 35, width = 12, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private String ind_catg;
    
    @ExcelColumn(name = "院校省份", sort = 36, width = 12, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private String yxsf;
    
    @ExcelColumn(name = "院校城市", sort = 37, width = 12, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private String yxcs;
    
    @ExcelColumn(name = "所有院校标签", sort = 38, width = 80, parent = UNIVERSITY_DETAIL_HEADER, level = 1)
    private String yx_tags_all;
    
    
    private String yx_tags;
    private Integer cnt_company;
    private Integer cnt_employ;
    private Float cnt_grad;
    private Integer is_hz;
    private Integer is_first;
    private Integer ycwc;

    // ===== 专业详细信息 =====
    private Integer znzys;
    private Integer znzyls;
    private Integer znjhs;
    private String info_byl;
    
    private Integer qsf;

    // ===== 构造器 =====
    public JHBeanForm(Integer id, String sf, String nf, String yxdm, String yxmc) {
        this.id = id;
        this.sf = sf;
        this.nf = nf;
        this.yxdm = yxdm;
        this.yxmc = yxmc;
    }

    // ===== Getters and Setters =====
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }

    public String getSf() { return sf; }
    public void setSf(String sf) { this.sf = sf; }

    public String getNf() { return nf; }
    public void setNf(String nf) { this.nf = nf; }

    public String getYxdm() { return yxdm; }
    public void setYxdm(String yxdm) { this.yxdm = yxdm; }

    public String getYxmc() { return yxmc; }
    public void setYxmc(String yxmc) { this.yxmc = yxmc; }

    public String getYxmc_org() { return yxmc_org; }
    public void setYxmc_org(String yxmc_org) { this.yxmc_org = yxmc_org; }

    public String getZydm() { return zydm; }
    public void setZydm(String zydm) { this.zydm = zydm; }

    public String getZymc() { return zymc; }
    public void setZymc(String zymc) { this.zymc = zymc; }

    public String getZymc_org() { return zymc_org; }
    public void setZymc_org(String zymc_org) { this.zymc_org = zymc_org; }

    public String getZyz() { return zyz; }
    public void setZyz(String zyz) { this.zyz = zyz; }

    public String getZyml() { return zyml; }
    public void setZyml(String zyml) { this.zyml = zyml; }

    public String getZnzy() { return znzy; }
    public void setZnzy(String znzy) { this.znzy = znzy; }

    public String getZybz() { return zybz; }
    public void setZybz(String zybz) { this.zybz = zybz; }

    public String getPc() { return pc; }
    public void setPc(String pc) { this.pc = pc; }

    public String getPc_code() { return pc_code; }
    public void setPc_code(String pc_code) { this.pc_code = pc_code; }

    public String getXk() { return xk; }
    public void setXk(String xk) { this.xk = xk; }

    public String getZx() { return zx; }
    public void setZx(String zx) { this.zx = zx; }

    public String getLqpc() { return lqpc; }
    public void setLqpc(String lqpc) { this.lqpc = lqpc; }

    public String getJhs() { return jhs; }
    public void setJhs(String jhs) { this.jhs = jhs; }

    public String getFee() { return fee; }
    public void setFee(String fee) { this.fee = fee; }

    public String getXz() { return xz; }
    public void setXz(String xz) { this.xz = xz; }

    public Integer getZdf() { return zdf; }
    public void setZdf(Integer zdf) { this.zdf = zdf; }

    public Integer getZdfwc() { return zdfwc; }
    public void setZdfwc(Integer zdfwc) { this.zdfwc = zdfwc; }

    // A年数据
    public String getJhs_a() { return jhs_a; }
    public void setJhs_a(String jhs_a) { this.jhs_a = jhs_a; }

    public String getXz_a() { return xz_a; }
    public void setXz_a(String xz_a) { this.xz_a = xz_a; }

    public String getFee_a() { return fee_a; }
    public void setFee_a(String fee_a) { this.fee_a = fee_a; }

    public String getZdf_a() { return zdf_a; }
    public void setZdf_a(String zdf_a) { this.zdf_a = zdf_a; }

    public String getZdfwc_a() { return zdfwc_a; }
    public void setZdfwc_a(String zdfwc_a) { this.zdfwc_a = zdfwc_a; }

    public String getZgf_a() { return zgf_a; }
    public void setZgf_a(String zgf_a) { this.zgf_a = zgf_a; }

    public String getZgfwc_a() { return zgfwc_a; }
    public void setZgfwc_a(String zgfwc_a) { this.zgfwc_a = zgfwc_a; }

    public String getPjf_a() { return pjf_a; }
    public void setPjf_a(String pjf_a) { this.pjf_a = pjf_a; }

    public String getPjfwc_a() { return pjfwc_a; }
    public void setPjfwc_a(String pjfwc_a) { this.pjfwc_a = pjfwc_a; }

    // B年数据
    public String getJhs_b() { return jhs_b; }
    public void setJhs_b(String jhs_b) { this.jhs_b = jhs_b; }

    public String getZdf_b() { return zdf_b; }
    public void setZdf_b(String zdf_b) { this.zdf_b = zdf_b; }

    public String getZdfwc_b() { return zdfwc_b; }
    public void setZdfwc_b(String zdfwc_b) { this.zdfwc_b = zdfwc_b; }

    public String getZgf_b() { return zgf_b; }
    public void setZgf_b(String zgf_b) { this.zgf_b = zgf_b; }

    public String getZgfwc_b() { return zgfwc_b; }
    public void setZgfwc_b(String zgfwc_b) { this.zgfwc_b = zgfwc_b; }

    public String getPjf_b() { return pjf_b; }
    public void setPjf_b(String pjf_b) { this.pjf_b = pjf_b; }

    public String getPjfwc_b() { return pjfwc_b; }
    public void setPjfwc_b(String pjfwc_b) { this.pjfwc_b = pjfwc_b; }

    // C年数据
    public String getJhs_c() { return jhs_c; }
    public void setJhs_c(String jhs_c) { this.jhs_c = jhs_c; }

    public String getZdf_c() { return zdf_c; }
    public void setZdf_c(String zdf_c) { this.zdf_c = zdf_c; }

    public String getZdfwc_c() { return zdfwc_c; }
    public void setZdfwc_c(String zdfwc_c) { this.zdfwc_c = zdfwc_c; }

    public String getZgf_c() { return zgf_c; }
    public void setZgf_c(String zgf_c) { this.zgf_c = zgf_c; }

    public String getZgfwc_c() { return zgfwc_c; }
    public void setZgfwc_c(String zgfwc_c) { this.zgfwc_c = zgfwc_c; }

    public String getPjf_c() { return pjf_c; }
    public void setPjf_c(String pjf_c) { this.pjf_c = pjf_c; }

    public String getPjfwc_c() { return pjfwc_c; }
    public void setPjfwc_c(String pjfwc_c) { this.pjfwc_c = pjfwc_c; }

    // 趋势数据
    public Integer getQsf_a() { return qsf_a; }
    public void setQsf_a(Integer qsf_a) { this.qsf_a = qsf_a; }

    public Integer getQsf_b() { return qsf_b; }
    public void setQsf_b(Integer qsf_b) { this.qsf_b = qsf_b; }

    public Integer getQsf_c() { return qsf_c; }
    public void setQsf_c(Integer qsf_c) { this.qsf_c = qsf_c; }

	public Integer getQsf() { return qsf; }
    public void setQsf(Integer qsf) { this.qsf = qsf; }

    // 院校详细信息
    public String getInd_nature() { return ind_nature; }
    public void setInd_nature(String ind_nature) { this.ind_nature = ind_nature; }

    public String getInd_catg() { return ind_catg; }
    public void setInd_catg(String ind_catg) { this.ind_catg = ind_catg; }

    public String getYxsf() { return yxsf; }
    public void setYxsf(String yxsf) { this.yxsf = yxsf; }

    public String getYxcs() { return yxcs; }
    public void setYxcs(String yxcs) { this.yxcs = yxcs; }

    public String getYx_tags() { return yx_tags; }
    public void setYx_tags(String yx_tags) { this.yx_tags = yx_tags; }

    public String getYx_tags_all() { return yx_tags_all; }
    public void setYx_tags_all(String yx_tags_all) { this.yx_tags_all = yx_tags_all; }

    public Integer getCnt_company() { return cnt_company; }
    public void setCnt_company(Integer cnt_company) { this.cnt_company = cnt_company; }

    public Integer getCnt_employ() { return cnt_employ; }
    public void setCnt_employ(Integer cnt_employ) { this.cnt_employ = cnt_employ; }

    public Float getCnt_grad() { return cnt_grad; }
    public void setCnt_grad(Float cnt_grad) { this.cnt_grad = cnt_grad; }

    public Integer getIs_hz() { return is_hz; }
    public void setIs_hz(Integer is_hz) { this.is_hz = is_hz; }

    public Integer getIs_first() { return is_first; }
    public void setIs_first(Integer is_first) { this.is_first = is_first; }

    public Integer getYcwc() { return ycwc; }
    public void setYcwc(Integer ycwc) { this.ycwc = ycwc; }

    // 专业详细信息
    public Integer getZnzys() { return znzys; }
    public void setZnzys(Integer znzys) { this.znzys = znzys; }

    public Integer getZnzyls() { return znzyls; }
    public void setZnzyls(Integer znzyls) { this.znzyls = znzyls; }

    public Integer getZnjhs() { return znjhs; }
    public void setZnjhs(Integer znjhs) { this.znjhs = znjhs; }

    public String getInfo_byl() { return info_byl; }
    public void setInfo_byl(String info_byl) { this.info_byl = info_byl; }
}