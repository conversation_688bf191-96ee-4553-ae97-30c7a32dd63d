package com.report.entity;

import java.util.List;
import java.util.ArrayList;

/**
 * 职业准备规划实体类
 * 基于入党、奖学金、求职等职业发展内容设计
 */
public class CareerPreparationPlan {
    
    // 入党规划
    private PartyMembershipPlan partyPlan;
    
    // 奖学金规划
    private ScholarshipPlan scholarshipPlan;
    
    // 求职准备
    private JobPreparationPlan jobPlan;
    
    // 实习规划
    private InternshipPlan internshipPlan;
    
    // 职业技能发展
    private List<String> professionalSkills;
    
    // 领导力发展
    private LeadershipDevelopmentPlan leadershipPlan;
    
    public CareerPreparationPlan() {
        this.partyPlan = new PartyMembershipPlan();
        this.scholarshipPlan = new ScholarshipPlan();
        this.jobPlan = new JobPreparationPlan();
        this.internshipPlan = new InternshipPlan();
        this.professionalSkills = new ArrayList<>();
        this.leadershipPlan = new LeadershipDevelopmentPlan();
    }
    
    /**
     * 入党规划
     */
    public static class PartyMembershipPlan {
        private List<String> requirements;      // 入党要求
        private List<String> process;           // 入党流程
        private List<String> timeline;          // 时间安排
        private List<String> benefits;          // 入党益处
        private String currentStatus;           // 当前状态
        
        public PartyMembershipPlan() {
            this.requirements = new ArrayList<>();
            this.process = new ArrayList<>();
            this.timeline = new ArrayList<>();
            this.benefits = new ArrayList<>();
            
            initializeBasicInfo();
        }
        
        private void initializeBasicInfo() {
            // 入党要求
            requirements.add("年满十八岁的中国公民");
            requirements.add("承认党的纲领和章程");
            requirements.add("愿意参加党的一个组织并积极工作");
            requirements.add("执行党的决议和按期交纳党费");
            requirements.add("绩点排名在前列");
            requirements.add("态度端正，积极参与党组织活动");
            requirements.add("担任班干部或学生会干部（加分项）");
            
            // 入党流程
            process.add("1. 递交入党申请书");
            process.add("2. 团员推优");
            process.add("3. 政治考核");
            process.add("4. 确定入党积极分子");
            process.add("5. 思想汇报");
            process.add("6. 党校学习考试");
            process.add("7. 召开支部大会");
            process.add("8. 确定发展对象");
            process.add("9. 申请人谈话");
            process.add("10. 政治审查");
            process.add("11. 成为预备党员");
            process.add("12. 入党介绍人审查");
            process.add("13. 书写转正申请书");
            process.add("14. 召开支部大会");
            process.add("15. 公示转正");
            
            // 时间安排
            timeline.add("大一：递交入党申请书，争取推优");
            timeline.add("大二：成为入党积极分子，参加党校学习");
            timeline.add("大三：成为发展对象，准备转为预备党员");
            timeline.add("大四：预备党员转正");
            
            // 入党益处
            benefits.add("综测加分，有利于保研");
            benefits.add("信息优势，获得各类资讯");
            benefits.add("政治审查优势");
            benefits.add("支教保研、行政保研优先");
            benefits.add("公务员、事业单位就业优势");
        }
        
        // Getters and Setters
        public List<String> getRequirements() { return requirements; }
        public void setRequirements(List<String> requirements) { this.requirements = requirements; }
        public List<String> getProcess() { return process; }
        public void setProcess(List<String> process) { this.process = process; }
        public List<String> getTimeline() { return timeline; }
        public void setTimeline(List<String> timeline) { this.timeline = timeline; }
        public List<String> getBenefits() { return benefits; }
        public void setBenefits(List<String> benefits) { this.benefits = benefits; }
        public String getCurrentStatus() { return currentStatus; }
        public void setCurrentStatus(String currentStatus) { this.currentStatus = currentStatus; }
    }
    
    /**
     * 奖学金规划
     */
    public static class ScholarshipPlan {
        private List<ScholarshipItem> scholarships;  // 奖学金项目
        private List<String> strategies;             // 获奖策略
        private String gpaTarget;                    // GPA目标
        
        public ScholarshipPlan() {
            this.scholarships = new ArrayList<>();
            this.strategies = new ArrayList<>();
            
            initializeScholarships();
            initializeStrategies();
        }
        
        private void initializeScholarships() {
            scholarships.add(new ScholarshipItem("国家奖学金", "8000元/年", "表现优异的学生", "最高等级"));
            scholarships.add(new ScholarshipItem("国家励志奖学金", "5000元/年", "品学兼优的家庭经济困难学生", "鼓励性质"));
            scholarships.add(new ScholarshipItem("省政府奖学金", "6000元/年", "表现优异的学生", "省级认可"));
            scholarships.add(new ScholarshipItem("省政府励志奖学金", "5000元/年", "品学兼优的家庭经济困难学生", "省级鼓励"));
            scholarships.add(new ScholarshipItem("校内一等奖学金", "学校标准", "成绩排名前5%", "校内最高"));
            scholarships.add(new ScholarshipItem("校内二等奖学金", "学校标准", "成绩排名前15%", "校内第二"));
            scholarships.add(new ScholarshipItem("校内三等奖学金", "学校标准", "成绩排名前30%", "校内第三"));
        }
        
        private void initializeStrategies() {
            strategies.add("保持优异的学习成绩（GPA 3.5以上）");
            strategies.add("积极参加学科竞赛和科研项目");
            strategies.add("担任学生干部，积累工作经验");
            strategies.add("参与志愿服务和社会实践");
            strategies.add("发表学术论文或专利");
            strategies.add("获得专业技能证书");
            strategies.add("展现综合素质和创新能力");
        }
        
        public static class ScholarshipItem {
            private String name;        // 奖学金名称
            private String amount;      // 奖励金额
            private String criteria;    // 评选标准
            private String level;       // 等级
            
            public ScholarshipItem(String name, String amount, String criteria, String level) {
                this.name = name;
                this.amount = amount;
                this.criteria = criteria;
                this.level = level;
            }
            
            // Getters and Setters
            public String getName() { return name; }
            public void setName(String name) { this.name = name; }
            public String getAmount() { return amount; }
            public void setAmount(String amount) { this.amount = amount; }
            public String getCriteria() { return criteria; }
            public void setCriteria(String criteria) { this.criteria = criteria; }
            public String getLevel() { return level; }
            public void setLevel(String level) { this.level = level; }
        }
        
        // Getters and Setters
        public List<ScholarshipItem> getScholarships() { return scholarships; }
        public void setScholarships(List<ScholarshipItem> scholarships) { this.scholarships = scholarships; }
        public List<String> getStrategies() { return strategies; }
        public void setStrategies(List<String> strategies) { this.strategies = strategies; }
        public String getGpaTarget() { return gpaTarget; }
        public void setGpaTarget(String gpaTarget) { this.gpaTarget = gpaTarget; }
    }
    
    /**
     * 求职准备规划
     */
    public static class JobPreparationPlan {
        private List<String> targetIndustries;   // 目标行业
        private List<String> targetCompanies;    // 目标公司
        private List<String> skillsRequired;     // 所需技能
        private List<String> preparationSteps;   // 准备步骤
        private String resumeStatus;             // 简历状态
        
        public JobPreparationPlan() {
            this.targetIndustries = new ArrayList<>();
            this.targetCompanies = new ArrayList<>();
            this.skillsRequired = new ArrayList<>();
            this.preparationSteps = new ArrayList<>();
            
            initializeBasicInfo();
        }
        
        private void initializeBasicInfo() {
            preparationSteps.add("大三暑期（7-8月）：积极寻找暑期实习，丰富简历，部分企业秋招提前批开始。");
            preparationSteps.add("大四上学期（9-11月）：秋招黄金时期，完善简历，积极网申，参加宣讲会、招聘会。");
            preparationSteps.add("大四上学期（12月）：关注秋招补录机会，准备国考、省考。");
            preparationSteps.add("大四寒假（1-2月）：进行寒假实习，为春招做准备，总结秋招经验。");
            preparationSteps.add("大四下学期（3-5月）：春招高峰期，多渠道获取招聘信息，积极投递简历和面试。");
            preparationSteps.add("大四下学期（6月）：毕业季，办理离校手续，处理好户籍、档案、组织关系等。");
        }

        // Getters and Setters
        public List<String> getTargetIndustries() { return targetIndustries; }
        public void setTargetIndustries(List<String> targetIndustries) { this.targetIndustries = targetIndustries; }
        public List<String> getTargetCompanies() { return targetCompanies; }
        public void setTargetCompanies(List<String> targetCompanies) { this.targetCompanies = targetCompanies; }
        public List<String> getSkillsRequired() { return skillsRequired; }
        public void setSkillsRequired(List<String> skillsRequired) { this.skillsRequired = skillsRequired; }
        public List<String> getPreparationSteps() { return preparationSteps; }
        public void setPreparationSteps(List<String> preparationSteps) { this.preparationSteps = preparationSteps; }
        public String getResumeStatus() { return resumeStatus; }
        public void setResumeStatus(String resumeStatus) { this.resumeStatus = resumeStatus; }
    }
    
    /**
     * 实习规划
     */
    public static class InternshipPlan {
        private List<String> targetCompanies;    // 目标实习公司
        private List<String> internshipGoals;    // 实习目标
        private String timeline;                 // 实习时间安排
        private List<String> skillsToGain;       // 希望获得的技能
        
        public InternshipPlan() {
            this.targetCompanies = new ArrayList<>();
            this.internshipGoals = new ArrayList<>();
            this.skillsToGain = new ArrayList<>();
        }
        
        // Getters and Setters
        public List<String> getTargetCompanies() { return targetCompanies; }
        public void setTargetCompanies(List<String> targetCompanies) { this.targetCompanies = targetCompanies; }
        public List<String> getInternshipGoals() { return internshipGoals; }
        public void setInternshipGoals(List<String> internshipGoals) { this.internshipGoals = internshipGoals; }
        public String getTimeline() { return timeline; }
        public void setTimeline(String timeline) { this.timeline = timeline; }
        public List<String> getSkillsToGain() { return skillsToGain; }
        public void setSkillsToGain(List<String> skillsToGain) { this.skillsToGain = skillsToGain; }
    }
    
    /**
     * 领导力发展规划
     */
    public static class LeadershipDevelopmentPlan {
        private List<String> leadershipRoles;    // 领导岗位
        private List<String> leadershipSkills;   // 领导技能
        private List<String> developmentActivities; // 发展活动
        
        public LeadershipDevelopmentPlan() {
            this.leadershipRoles = new ArrayList<>();
            this.leadershipSkills = new ArrayList<>();
            this.developmentActivities = new ArrayList<>();
        }
        
        // Getters and Setters
        public List<String> getLeadershipRoles() { return leadershipRoles; }
        public void setLeadershipRoles(List<String> leadershipRoles) { this.leadershipRoles = leadershipRoles; }
        public List<String> getLeadershipSkills() { return leadershipSkills; }
        public void setLeadershipSkills(List<String> leadershipSkills) { this.leadershipSkills = leadershipSkills; }
        public List<String> getDevelopmentActivities() { return developmentActivities; }
        public void setDevelopmentActivities(List<String> developmentActivities) { this.developmentActivities = developmentActivities; }
    }
    
    // Getters and Setters
    public PartyMembershipPlan getPartyPlan() { return partyPlan; }
    public void setPartyPlan(PartyMembershipPlan partyPlan) { this.partyPlan = partyPlan; }
    
    public ScholarshipPlan getScholarshipPlan() { return scholarshipPlan; }
    public void setScholarshipPlan(ScholarshipPlan scholarshipPlan) { this.scholarshipPlan = scholarshipPlan; }
    
    public JobPreparationPlan getJobPlan() { return jobPlan; }
    public void setJobPlan(JobPreparationPlan jobPlan) { this.jobPlan = jobPlan; }
    
    public InternshipPlan getInternshipPlan() { return internshipPlan; }
    public void setInternshipPlan(InternshipPlan internshipPlan) { this.internshipPlan = internshipPlan; }
    
    public List<String> getProfessionalSkills() { return professionalSkills; }
    public void setProfessionalSkills(List<String> professionalSkills) { this.professionalSkills = professionalSkills; }
    
    public LeadershipDevelopmentPlan getLeadershipPlan() { return leadershipPlan; }
    public void setLeadershipPlan(LeadershipDevelopmentPlan leadershipPlan) { this.leadershipPlan = leadershipPlan; }
}