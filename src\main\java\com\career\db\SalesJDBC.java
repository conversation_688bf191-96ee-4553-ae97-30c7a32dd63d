package com.career.db;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

import com.career.utils.ResultVO;
import com.career.utils.SQLLogUtils;
import com.career.utils.Tools;

public class SalesJDBC {
	
	static String URL = JDBCConstants.URL;
	static String USER = JDBCConstants.USER;
	static String PASSWD = JDBCConstants.PASSWD;
	public static int PAGE_ROW_CNT = 20;
	public static int PAGE_ROW_CNT_SPEC = 15;
	public static int PAGE_ROW_CNT_TEN = 10;
	
	static {
		try {
			Class.forName("com.mysql.cj.jdbc.Driver");
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
	
	public static boolean insertWecomSalesStaff(WecomSalesStaff staff) {
	    Connection conn = null;
	    PreparedStatement pstmt = null;
	    try {
	        conn = DriverManager.getConnection(URL, USER, PASSWD);
	        String sql = "INSERT INTO wecom_sales_staff (staff_id, stf_passwd, stf_name, stf_gender, stf_birth_date, stf_join_date, last_login_tm, dept_id, stf_position, stf_phone, stf_email, stf_address, stf_nature, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
	        pstmt = conn.prepareStatement(sql);

	        // 设置参数值
	        pstmt.setString(1, staff.getStaff_id());
	        pstmt.setString(2, staff.getStf_passwd());
	        pstmt.setString(3, staff.getStf_name());
	        pstmt.setInt(4, staff.getStf_gender());
	        pstmt.setTimestamp(5, new java.sql.Timestamp(staff.getStf_birth_date().getTime()));
	        pstmt.setTimestamp(6, new java.sql.Timestamp(staff.getStf_join_date().getTime()));
	        pstmt.setTimestamp(7, new Timestamp(staff.getLast_login_tm().getTime()));
	        pstmt.setString(8, staff.getDept_id());
	        pstmt.setString(9, staff.getStf_position());
	        pstmt.setString(10, staff.getStf_phone());
	        pstmt.setString(11, staff.getStf_email());
	        pstmt.setString(12, staff.getStf_address());
	        pstmt.setInt(13, staff.getStf_nature());
	        pstmt.setInt(14, staff.getStatus());

	        return pstmt.executeUpdate() > 0;
	    } catch (SQLException e) {
	        e.printStackTrace();
	        return false;
	    } finally {
	        closeAllConnection(conn, pstmt, null);
	    }
	}
	
	public static boolean updateWecomSalesStaff(WecomSalesStaff staff) {
	    Connection conn = null;
	    PreparedStatement pstmt = null;
	    try {
	        conn = DriverManager.getConnection(URL, USER, PASSWD);
	        String sql = "UPDATE wecom_sales_staff SET stf_passwd=?, stf_name=?, stf_gender=?, stf_birth_date=?, stf_join_date=?, last_login_tm=?, dept_id=?, stf_position=?, stf_phone=?, stf_email=?, stf_address=?, stf_nature=?, status=? WHERE staff_id=?";
	        pstmt = conn.prepareStatement(sql);

	        // 设置参数值
	        pstmt.setString(1, staff.getStf_passwd());
	        pstmt.setString(2, staff.getStf_name());
	        pstmt.setInt(3, staff.getStf_gender());
	        pstmt.setTimestamp(4, new java.sql.Timestamp(staff.getStf_birth_date().getTime()));
	        pstmt.setTimestamp(5, new java.sql.Timestamp(staff.getStf_join_date().getTime()));
	        pstmt.setTimestamp(6, new Timestamp(staff.getLast_login_tm().getTime()));
	        pstmt.setString(7, staff.getDept_id());
	        pstmt.setString(8, staff.getStf_position());
	        pstmt.setString(9, staff.getStf_phone());
	        pstmt.setString(10, staff.getStf_email());
	        pstmt.setString(11, staff.getStf_address());
	        pstmt.setInt(12, staff.getStf_nature());
	        pstmt.setInt(13, staff.getStatus());
	        pstmt.setString(14, staff.getStaff_id());

	        return pstmt.executeUpdate() > 0;
	    } catch (SQLException e) {
	        e.printStackTrace();
	        return false;
	    } finally {
	        closeAllConnection(conn, pstmt, null);
	    }
	}
	
	public static WecomSalesCustomer getWecomSalesCustomerById(String customerId) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "SELECT * FROM wecom_sales_customer WHERE customer_id = ?";
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, customerId);
            rs = pstmt.executeQuery();

            if (rs.next()) {
                return setWecomSalesCustomerAll(rs);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, rs);
        }
        return null;
    }
	
	public static ResultVO getWecomSalesCustomersByCondition(String wecom_saas_id, String customer_nickname, HashSet<Integer> customer_roles,
			HashSet<Integer> customer_gender, HashSet<Integer> customer_type, HashSet<String> customer_sf, String customer_cs, 
			String wecom_groups, String wecom_follower, String wecom_tags, String staff_id, HashSet<Integer> new_indicator, int pageNumber) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        List<WecomSalesCustomer> customers = new ArrayList<>();
        ResultVO resultVO = new ResultVO();
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);

            String customer_roles_param = customer_roles.size() > 0 ? (" AND x.customer_roles in ("+Tools.getSQLQueryinInt(customer_roles)+")") : "";
            String customer_gender_param = customer_gender.size() > 0 ? (" AND x.customer_gender in ("+Tools.getSQLQueryinInt(customer_gender)+")") : "";
            String customer_type_param = customer_type.size() > 0 ? (" AND x.customer_type in ("+Tools.getSQLQueryinInt(customer_type)+")") : "";
            String customer_sf_param = customer_sf.size() > 0 ? (" AND x.customer_sf in ("+Tools.getSQLQueryin(customer_sf)+")") : "";
            String new_indicator_param = new_indicator.size() > 0 ? (" AND x.new_indicator in ("+Tools.getSQLQueryinInt(new_indicator)+")") : "";
            
            StringBuilder sql = new StringBuilder("FROM wecom_sales_customer x WHERE wecom_saas_id = ? and (x.customer_nickname like ? OR x.wecom_name like ? OR COALESCE(x.customer_phone1, '') like ? OR COALESCE(x.customer_phone2, '') like ?)" + customer_roles_param + customer_gender_param + customer_type_param + customer_sf_param + new_indicator_param
            				+ " AND COALESCE(x.customer_cs, '') like ? AND COALESCE(x.wecom_related_follower, '')  like ? AND COALESCE(x.wecom_related_groups,'') like ? AND COALESCE(x.wecom_related_tags, '') like ? AND COALESCE(x.staff_id,'') like ?");
            
 
            pstmt = conn.prepareStatement("SELECT * " + sql.toString() + " order by x.wecom_join_tm DESC LIMIT ?,?");   
            
            int index = 1; 
            pstmt.setString(index++, wecom_saas_id);
            pstmt.setString(index++, "%" + customer_nickname + "%");
            pstmt.setString(index++, "%" + customer_nickname + "%");
            pstmt.setString(index++, "%" + customer_nickname + "%");
            pstmt.setString(index++, "%" + customer_nickname + "%");
            pstmt.setString(index++, "%" + customer_cs + "%");
            pstmt.setString(index++, "%" + wecom_follower + "%");
            pstmt.setString(index++, "%" + wecom_groups + "%");
            pstmt.setString(index++, "%" + wecom_tags + "%");
            pstmt.setString(index++, "%" + staff_id + "%");
            pstmt.setInt(index++, (pageNumber - 1) * PAGE_ROW_CNT);
            pstmt.setInt(index++, PAGE_ROW_CNT);

            SQLLogUtils.printSQL(">>>", pstmt);
            rs = pstmt.executeQuery();
            while (rs.next()) {
                customers.add(setWecomSalesCustomerAll(rs));
            }
            
            pstmt = null; rs = null;
            pstmt = conn.prepareStatement("SELECT COUNT(*) as cnt " + sql.toString());
            index = 1;
            pstmt.setString(index++, wecom_saas_id);
            pstmt.setString(index++, "%" + customer_nickname + "%");
            pstmt.setString(index++, "%" + customer_nickname + "%");
            pstmt.setString(index++, "%" + customer_nickname + "%");
            pstmt.setString(index++, "%" + customer_nickname + "%");
            pstmt.setString(index++, "%" + customer_cs + "%");
            pstmt.setString(index++, "%" + wecom_follower + "%");
            pstmt.setString(index++, "%" + wecom_groups + "%");
            pstmt.setString(index++, "%" + wecom_tags + "%");
            pstmt.setString(index++, "%" + staff_id + "%");
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
            	resultVO.setRecordCnt(rs.getInt("cnt")); 
            }

            
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, rs);
        }
        resultVO.setCurrentPage(pageNumber);
        resultVO.setResult(customers);
        return resultVO;
    }
	
	public static List<WecomSalesOrder> getOrdersByCustomerId(String customerId) {
        List<WecomSalesOrder> orders = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "SELECT x.*, y.product_name, y.product_price FROM wecom_sales_order x left join wecom_sales_product_info y on x.product_id = y.product_id WHERE x.customer_id = ? order by x.create_tm desc";
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, customerId);
            rs = pstmt.executeQuery();

            while (rs.next()) {
                WecomSalesOrder order = new WecomSalesOrder();
                order.setOrder_id(rs.getString("order_id"));
                order.setCreate_tm(rs.getTimestamp("create_tm"));
                order.setStaff_id(rs.getString("staff_id"));
                order.setCustomer_id(rs.getString("customer_id"));
                order.setOperate_staff_id(rs.getString("operate_staff_id"));
                order.setProduct_id(rs.getString("product_id"));
                order.setOrder_price(rs.getDouble("order_price"));
                order.setOrder_remark(rs.getString("order_remark"));
                
                WecomProductInfo wecomProductInfo = new WecomProductInfo();
                wecomProductInfo.setProduct_name(rs.getString("product_name"));
                wecomProductInfo.setProduct_price(rs.getBigDecimal("product_price"));
                
                order.setExt_WecomProductInfo(wecomProductInfo);
                
                orders.add(order);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, rs);
        }
        return orders;
    }
	
	public static List<WecomSalesTags> searchTags(String sql, List<Object> params) {
        List<WecomSalesTags> results = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            pstmt = conn.prepareStatement(sql);
            int index = 1;
            for (Object param : params) {
                if (param instanceof String) {
                    pstmt.setString(index++, (String) param);
                } else if (param instanceof Integer) {
                    pstmt.setInt(index++, (Integer) param);
                }
            }
            rs = pstmt.executeQuery();
            while (rs.next()) {
                WecomSalesTags tag = new WecomSalesTags();
                tag.setTags_id(rs.getString("tags_id"));
                tag.setTags_name(rs.getString("tags_name"));
                tag.setWecom_saas_id(rs.getString("wecom_saas_id"));
                tag.setSort(rs.getInt("sort"));
                tag.setStatus(rs.getInt("status"));
                results.add(tag);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, rs);
        }
        return results;
    }
	
	public static ResultVO searchTags(String wecomSaasId, String tagsName, int status, int pageNumber) {
        List<WecomSalesTags> results = new ArrayList<>();
        ResultVO resultVO = new ResultVO();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);

            // 固定的SQL语句
            String sql = "FROM wecom_sales_tags WHERE wecom_saas_id = ? AND tags_name LIKE ? AND status = ?";
            String allSql = "SELECT * " + sql + " ORDER BY create_tm desc LIMIT ?, ?";
            String countSql = "SELECT COUNT(*) as cnt " + sql ;

            // 查询分页数据
            pstmt = conn.prepareStatement(allSql);
            int index = 1;
            pstmt.setString(index++, wecomSaasId);
            pstmt.setString(index++, "%" + tagsName + "%"); 
            pstmt.setInt(index++, status);
            pstmt.setInt(index++, (pageNumber - 1) * PAGE_ROW_CNT);
            pstmt.setInt(index++, PAGE_ROW_CNT);
            SQLLogUtils.printSQL(pstmt); 
            rs = pstmt.executeQuery();
            while (rs.next()) {
                WecomSalesTags tag = new WecomSalesTags();
                tag.setTags_id(rs.getString("tags_id"));
                tag.setTags_name(rs.getString("tags_name"));
                tag.setWecom_saas_id(rs.getString("wecom_saas_id"));
                tag.setSort(rs.getInt("sort"));
                tag.setStatus(rs.getInt("status")); 
                tag.setCreate_tm(rs.getTimestamp("create_tm"));
                results.add(tag);
            }

            // 查询总记录数
            pstmt = conn.prepareStatement(countSql);
            index = 1;
            pstmt.setString(index++, wecomSaasId);
            pstmt.setString(index++, "%" + tagsName + "%"); 
            pstmt.setInt(index++, status);
            rs = pstmt.executeQuery();
            if (rs.next()) {
                resultVO.setRecordCnt(rs.getInt("cnt"));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, rs);
        }

        resultVO.setCurrentPage(pageNumber);
        resultVO.setResult(results);
        return resultVO;
    }
	
	public static boolean checkTagExistByName(String wecomSaasId, String tagsName) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);

            // 固定的SQL语句
            String sql = "SELECT * FROM wecom_sales_tags WHERE wecom_saas_id = ? AND tags_name = ?";

            // 查询分页数据
            pstmt = conn.prepareStatement(sql);
            int index = 1; 
            pstmt.setString(index++, wecomSaasId);
            pstmt.setString(index++, tagsName); 
            SQLLogUtils.printSQL(pstmt); 
            rs = pstmt.executeQuery();
            while (rs.next()) {
                return true;
            }

        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, rs);
        }

        return false;
    }
	
	public static ResultVO getWecomGroupByCondition(String wecomSaasId, String wecom_group_name, int pageNumber) {
        List<WecomGroups> results = new ArrayList<>();
        ResultVO resultVO = new ResultVO();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);

            // 固定的SQL语句 
            String sql = "FROM wecom_groups WHERE wecom_saas_id = ? AND COALESCE(wecom_name,'')  like ? ";
            String allSql = "SELECT * " + sql + " ORDER BY wecom_created_at desc LIMIT ?, ?";
            String countSql = "SELECT COUNT(*) as cnt " + sql ; 
 
            // 查询分页数据
            pstmt = conn.prepareStatement(allSql); 
            int index = 1;
            pstmt.setString(index++, wecomSaasId);
            pstmt.setString(index++, "%"+wecom_group_name+"%");
            pstmt.setInt(index++, (pageNumber - 1) * PAGE_ROW_CNT);
            pstmt.setInt(index++, PAGE_ROW_CNT);
            SQLLogUtils.printSQL(pstmt); 
            rs = pstmt.executeQuery();
            while (rs.next()) {
            	results.add(setWecomGroupsAll(rs));
            }

            // 查询总记录数
            pstmt = conn.prepareStatement(countSql);
            index = 1;
            pstmt.setString(index++, wecomSaasId);
            pstmt.setString(index++, "%"+wecom_group_name+"%");
            rs = pstmt.executeQuery();
            if (rs.next()) {
                resultVO.setRecordCnt(rs.getInt("cnt"));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, rs);
        }

        resultVO.setCurrentPage(pageNumber);
        resultVO.setResult(results);
        return resultVO;
    }
	
	
	public static List<WecomGroups> getAllWecomGroup(String wecom_saas_id) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        List<WecomGroups> customers = new ArrayList<>();
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);

            StringBuilder sql = new StringBuilder("FROM wecom_groups WHERE wecom_saas_id = ? and COALESCE(wecom_owner_userid,'') <> '' and COALESCE(wecom_name,'') <> '' ");
            

            pstmt = conn.prepareStatement("SELECT * " + sql.toString());
            pstmt.setString(1, wecom_saas_id);
            int index = 1;
            rs = pstmt.executeQuery();
            SQLLogUtils.printSQL(">>>", pstmt);
            while (rs.next()) {
                customers.add(setWecomGroupsAll(rs));
            }
            
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, rs);
        }
        return customers;
    }
	
	public static List<WecomProductInfo> getAllProducts(String wecomSaasId) {
        List<WecomProductInfo> products = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);

            String sql = "SELECT * FROM wecom_sales_product_info WHERE wecom_saas_id = ? AND status = 1";
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, wecomSaasId);

            rs = pstmt.executeQuery();
            while (rs.next()) {
                WecomProductInfo product = new WecomProductInfo();
                product.setProduct_id(rs.getString("product_id"));
                product.setProduct_name(rs.getString("product_name"));
                product.setProduct_code(rs.getString("product_code"));
                product.setProduct_category(rs.getString("product_category"));
                product.setProduct_price(rs.getBigDecimal("product_price"));
                product.setProduct_stock(rs.getInt("product_stock"));
                product.setProduct_description(rs.getString("product_description"));
                product.setStatus(rs.getInt("status"));
                product.setCreate_tm(rs.getTimestamp("create_tm"));
                product.setUpdate_tm(rs.getTimestamp("update_tm"));

                products.add(product);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, rs);
        }
        return products;
    }
	
	public static List<WecomSalesTags> getAllTags(String wecomSaasId) {
        List<WecomSalesTags> tagList = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);

            String sql = "SELECT * FROM wecom_sales_tags WHERE wecom_saas_id = ? AND status = 1 ORDER BY sort desc, create_tm desc";
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, wecomSaasId);

            rs = pstmt.executeQuery();
            while (rs.next()) {
            	WecomSalesTags tag = new WecomSalesTags();
                tag.setTags_id(rs.getString("tags_id"));
                tag.setTags_name(rs.getString("tags_name"));
                tag.setWecom_saas_id(rs.getString("wecom_saas_id"));
                tag.setSort(rs.getInt("sort"));
                tag.setStatus(rs.getInt("status")); 
                tag.setCreate_tm(rs.getTimestamp("create_tm"));
                tagList.add(tag);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, rs);
        }
        return tagList;
    }
	
	public static WecomGroups setWecomGroupsAll(ResultSet rs) throws SQLException {
        WecomGroups wecomGroups = new WecomGroups();
        wecomGroups.setWecom_chat_id(rs.getString("wecom_chat_id"));
        wecomGroups.setWecom_name(rs.getString("wecom_name"));
        wecomGroups.setWecom_owner_userid(rs.getString("wecom_owner_userid"));
        wecomGroups.setWecom_create_time(rs.getTimestamp("wecom_create_time"));
        wecomGroups.setWecom_member_count(rs.getInt("wecom_member_count"));
        wecomGroups.setWecom_member_version(rs.getString("wecom_member_version"));
        wecomGroups.setWecom_created_at(rs.getTimestamp("wecom_created_at"));
        wecomGroups.setWecom_updated_at(rs.getTimestamp("wecom_updated_at"));
        wecomGroups.setWecom_saas_id(rs.getString("wecom_saas_id"));
        return wecomGroups;
    }
	
	public static long insertWecomSalesCustomer(WecomSalesCustomer customer) {
        Connection conn = null;
        PreparedStatement pstmt = null;
         try {
             conn = DriverManager.getConnection(URL, USER, PASSWD);
             String sql = "INSERT INTO wecom_sales_customer (customer_id, wecom_external_userid, wecom_name, wecom_type, wecom_avatar, wecom_gender, wecom_follow_users_json, wecom_join_tm, sys_create_tm, sys_update_tm, customer_nickname, customer_roles, customer_tags, staff_id, ext_staff_id, customer_gender, customer_occupation, customer_boss_ind, customer_highest_education, customer_work_year, customer_type, customer_sf, customer_cs, customer_phone1, customer_phone2, customer_email, customer_wechat1, customer_wechat2, customer_qq, status) " +
                            "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
             pstmt = conn.prepareStatement(sql);
             
             // 设置参数值
            pstmt.setString(1, customer.getCustomer_id());
     	    pstmt.setString(2, customer.getWecom_external_userid());
     	    pstmt.setString(3, customer.getWecom_name());
     	    pstmt.setInt(4, customer.getWecom_type()); 
     	    pstmt.setString(5, customer.getWecom_avatar());
     	    pstmt.setInt(6, customer.getWecom_gender());
     	    pstmt.setString(7, customer.getWecom_follow_users_json());
     	    pstmt.setTimestamp(8, new Timestamp(customer.getWecom_join_tm().getTime()));
     	    pstmt.setTimestamp(9, new Timestamp(customer.getSys_create_tm().getTime()));
     	    pstmt.setTimestamp(10, new Timestamp(customer.getSys_update_tm().getTime()));
     	    pstmt.setString(11, customer.getCustomer_nickname());
     	    pstmt.setInt(12, customer.getCustomer_roles());
     	    pstmt.setString(13, customer.getCustomer_tags());
     	    pstmt.setString(14, customer.getStaff_id());
     	    pstmt.setString(15, customer.getExt_staff_id());
     	    pstmt.setInt(16, customer.getCustomer_gender());
     	    pstmt.setString(17, customer.getCustomer_occupation());
     	    pstmt.setInt(18, customer.getCustomer_boss_ind());
     	    pstmt.setInt(19, customer.getCustomer_highest_education());
     	    pstmt.setInt(20, customer.getCustomer_work_year());
     	    pstmt.setInt(21, customer.getCustomer_type());
     	    pstmt.setString(22, customer.getCustomer_sf());
     	    pstmt.setString(23, customer.getCustomer_cs());
     	    pstmt.setString(24, customer.getCustomer_phone1());
     	    pstmt.setString(25, customer.getCustomer_phone2());
     	    pstmt.setString(26, customer.getCustomer_email());
     	    pstmt.setString(27, customer.getCustomer_wechat1());
     	    pstmt.setString(28, customer.getCustomer_wechat2());
     	    pstmt.setString(29, customer.getCustomer_qq());
     	    pstmt.setInt(30, customer.getStatus());
             
             int affectedRows = pstmt.executeUpdate();
             if (affectedRows > 0) {
                 ResultSet generatedKeys = pstmt.getGeneratedKeys();
                 if (generatedKeys.next()) {
                     return generatedKeys.getLong(1);
                 }
                 generatedKeys.close();
             }
         } catch (SQLException e) {
             e.printStackTrace();
         } finally {
             closeAllConnection(conn, pstmt, null);
        }
         return 0;
    }
	
	public static long insertWecomSalesOrder(WecomSalesOrder order) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "INSERT INTO wecom_sales_order (order_id, create_tm, staff_id, customer_id, operate_staff_id, product_id, order_price, order_remark) " +
                         "VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            pstmt = conn.prepareStatement(sql);

            // 设置参数值
            pstmt.setString(1, order.getOrder_id());
            pstmt.setTimestamp(2, new Timestamp(order.getCreate_tm().getTime()));
            pstmt.setString(3, order.getStaff_id());
            pstmt.setString(4, order.getCustomer_id());
            pstmt.setString(5, order.getOperate_staff_id());
            pstmt.setString(6, order.getProduct_id());
            pstmt.setDouble(7, order.getOrder_price());
            pstmt.setString(8, order.getOrder_remark());

            int affectedRows = pstmt.executeUpdate();
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, null);
        }
        return 0;
    }
	
	public static ResultVO searchFollowUpRecord(String staff_id, String customer_nickname, Date followUpStart, Date followUpEnd, int follow_up_type, int status, int pageNumber) {
        List<WecomSalesFollowUpRecord> results = new ArrayList<>();
        ResultVO resultVO = new ResultVO();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);

            // 固定的SQL语句
            String sql = "FROM wecom_sales_follow_up_record x left join wecom_sales_customer y on x.customer_id = y.customer_id "
            		+ "WHERE x.staff_id = ? and x.follow_up_date between ? and ? and follow_up_type = ? and x.status = ? and COALESCE(y.customer_nickname, '') like ? ";
            
            String allSql = "SELECT x.*, y.customer_nickname " + sql + " ORDER BY x.follow_up_date ASC LIMIT ?,?"; 
            // 查询总记录数的SQL语句
            String countSql = "SELECT COUNT(*) as cnt " + sql;

            // 查询分页数据
            pstmt = conn.prepareStatement(allSql); 
            int index = 1;
            pstmt.setString(index++, staff_id);
            pstmt.setTimestamp(index++, new Timestamp(followUpStart.getTime()));
            pstmt.setTimestamp(index++, new Timestamp(followUpEnd.getTime()));
            pstmt.setInt(index++, follow_up_type);
            pstmt.setInt(index++, status);
            pstmt.setString(index++, "%"+customer_nickname+"%");
            pstmt.setInt(index++, (pageNumber - 1) * PAGE_ROW_CNT);
            pstmt.setInt(index++, PAGE_ROW_CNT);
            SQLLogUtils.printSQL(pstmt);
            rs = pstmt.executeQuery();
            while (rs.next()) {
                results.add(setWecomSalesFollowUpRecordAll(rs));
            }

            // 查询总记录数
            pstmt = conn.prepareStatement(countSql);
            index = 1;
            pstmt.setString(index++, staff_id);
            pstmt.setTimestamp(index++, new Timestamp(followUpStart.getTime()));
            pstmt.setTimestamp(index++, new Timestamp(followUpEnd.getTime()));
            pstmt.setInt(index++, follow_up_type);
            pstmt.setInt(index++, status);
            pstmt.setString(index++, "%"+customer_nickname+"%");

            rs = pstmt.executeQuery();
            if (rs.next()) {
                resultVO.setRecordCnt(rs.getInt("cnt"));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, rs);
        }

        resultVO.setCurrentPage(pageNumber);
        resultVO.setResult(results);
        return resultVO;
    }
	
	public static ResultVO searchOrders(String customerNickname, String productId, int orderPriceStart, int orderPriceEnd, Date createTmStart, Date createTmEnd, int pageNumber) {
        List<WecomSalesOrder> results = new ArrayList<>();
        ResultVO resultVO = new ResultVO();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);

            // 固定的SQL语句
            String sql = "FROM wecom_sales_order x LEFT JOIN wecom_sales_customer y ON x.customer_id = y.customer_id LEFT JOIN wecom_sales_product_info z ON x.product_id = z.product_id "
            		+ "WHERE x.product_id like ? and x.order_price BETWEEN ? AND ? "
            		+ "and x.create_tm BETWEEN ? AND ? AND COALESCE(y.customer_nickname,'') like ?";
            
            // 查询分页数据
            String allSql = "SELECT x.*, y.customer_nickname, z.product_name, z.product_price " + sql + " ORDER BY x.create_tm DESC LIMIT ?, ?";
            // 查询总记录数的SQL语句
            String countSql = "SELECT COUNT(*) as cnt " + sql;

            // 查询分页数据
            pstmt = conn.prepareStatement(allSql);
            int index = 1;
            pstmt.setString(index++, "%"+productId+"%");
            pstmt.setDouble(index++, orderPriceStart);
            pstmt.setDouble(index++, orderPriceEnd);
            pstmt.setTimestamp(index++, new Timestamp(createTmStart.getTime()));
            pstmt.setTimestamp(index++, new Timestamp(createTmEnd.getTime()));
            pstmt.setString(index++, "%"+customerNickname+"%");
            pstmt.setInt(index++, (pageNumber - 1) * PAGE_ROW_CNT);
            pstmt.setInt(index++, PAGE_ROW_CNT);
            SQLLogUtils.printSQL(pstmt);
            rs = pstmt.executeQuery();
            while (rs.next()) {
                WecomSalesOrder order = new WecomSalesOrder();
                order.setOrder_id(rs.getString("order_id"));
                order.setCreate_tm(rs.getTimestamp("create_tm"));
                order.setStaff_id(rs.getString("staff_id"));
                order.setCustomer_id(rs.getString("customer_id"));
                order.setOperate_staff_id(rs.getString("operate_staff_id"));
                order.setProduct_id(rs.getString("product_id"));
                order.setOrder_price(rs.getDouble("order_price"));
                order.setOrder_remark(rs.getString("order_remark"));
                
                WecomSalesCustomer ext_WecomSalesCustomer = new WecomSalesCustomer();
                ext_WecomSalesCustomer.setCustomer_nickname(rs.getString("customer_nickname"));
                
                WecomProductInfo ext_WecomProductInfo = new WecomProductInfo();
                ext_WecomProductInfo.setProduct_name(rs.getString("product_name"));
                ext_WecomProductInfo.setProduct_price(rs.getBigDecimal("product_price"));
                
                order.setExt_WecomSalesCustomer(ext_WecomSalesCustomer);
                order.setExt_WecomProductInfo(ext_WecomProductInfo);
                results.add(order);
            }

            // 查询总记录数
            pstmt = conn.prepareStatement(countSql);
            index = 1;
            pstmt.setString(index++, "%"+productId+"%");
            pstmt.setDouble(index++, orderPriceStart);
            pstmt.setDouble(index++, orderPriceEnd);
            pstmt.setTimestamp(index++, new Timestamp(createTmStart.getTime()));
            pstmt.setTimestamp(index++, new Timestamp(createTmEnd.getTime()));
            pstmt.setString(index++, "%"+customerNickname+"%");

            rs = pstmt.executeQuery();
            if (rs.next()) {
                resultVO.setRecordCnt(rs.getInt("cnt"));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, rs);
        }

        resultVO.setCurrentPage(pageNumber);
        resultVO.setResult(results);
        return resultVO;
    }

	public static WecomSalesFollowUpRecord getFollowUpRecord(String record_id) {
        ResultVO resultVO = new ResultVO();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);

            // 固定的SQL语句
            String sql = "SELECT x.*, y.customer_nickname FROM wecom_sales_follow_up_record x LEFT JOIN wecom_sales_customer y ON x.customer_id = y.customer_id WHERE x.record_id = ?";
            // 查询分页数据
            pstmt = conn.prepareStatement(sql); 
            int index = 1;
            pstmt.setString(index++, record_id);
            SQLLogUtils.printSQL(pstmt);
            rs = pstmt.executeQuery();
            while (rs.next()) {
                return setWecomSalesFollowUpRecordAll(rs);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, rs);
        }

        return null;
    }
	
	public static boolean updateFollowUpRecord(WecomSalesFollowUpRecord followUpRecord) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "UPDATE wecom_sales_follow_up_record SET " +
                         "staff_id = ?, " +
                         "customer_id = ?, " +
                         "follow_up_date = ?, " +
                         "follow_up_method = ?, " +
                         "follow_up_notes = ?, " +
                         "next_follow_up_date = ?, " +
                         "status = ?, " +
                         "purchase_intention = ?, " +
                         "recommend_product_id = ?, " +
                         "updated_tm = ? " +
                         "WHERE record_id = ?";
            pstmt = conn.prepareStatement(sql);

            pstmt.setString(1, followUpRecord.getStaff_id());
            pstmt.setString(2, followUpRecord.getCustomer_id());
            pstmt.setTimestamp(3, new Timestamp(followUpRecord.getFollow_up_date().getTime()));
            pstmt.setInt(4, followUpRecord.getFollow_up_method());
            pstmt.setString(5, followUpRecord.getFollow_up_notes());
            
            if (followUpRecord.getNext_follow_up_date() != null) {
            	pstmt.setTimestamp(6, new Timestamp(followUpRecord.getNext_follow_up_date().getTime()));
            } else {
                pstmt.setNull(6, java.sql.Types.TIMESTAMP); 
            } 
            
            
            pstmt.setInt(7, followUpRecord.getStatus());
            pstmt.setInt(8, followUpRecord.getPurchase_intention());
            pstmt.setString(9, followUpRecord.getRecommend_product_id());
            pstmt.setTimestamp(10, new Timestamp(followUpRecord.getUpdated_tm().getTime()));
            pstmt.setString(11, followUpRecord.getRecord_id());

            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, null);
        }
        return false;
    }
	
	public static boolean deleteTag(String tag_id) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "UPDATE wecom_sales_tags SET status = 2 WHERE tags_id = ?";
            pstmt = conn.prepareStatement(sql);

            pstmt.setString(1, tag_id);
            int affectedRows = pstmt.executeUpdate();
            return affectedRows > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, null);
        }
        return false;
    }
	
	public static List<WecomSalesFollowUpRecord> searchFollowUpRecord(String customer_id) {
        ResultVO resultVO = new ResultVO();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        List<WecomSalesFollowUpRecord> list = new ArrayList<>();
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);

            // 固定的SQL语句
            String sql = "SELECT x.*, y.customer_nickname FROM wecom_sales_follow_up_record x LEFT JOIN wecom_sales_customer y ON x.customer_id = y.customer_id WHERE x.customer_id = ? ORDER BY follow_up_date DESC, create_tm DESC";
            // 查询分页数据
            pstmt = conn.prepareStatement(sql);  
            int index = 1;
            pstmt.setString(index++, customer_id);
            SQLLogUtils.printSQL(pstmt);
            rs = pstmt.executeQuery();
            while (rs.next()) {
            	list.add(setWecomSalesFollowUpRecordAll(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, rs);
        }

        return list;
    }
	
	public static long insertWecomSalesFollowUpRecord(WecomSalesFollowUpRecord record) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "INSERT INTO wecom_sales_follow_up_record (record_id, staff_id, customer_id, follow_up_date, create_tm, status, wecom_saas_id, purchase_intention, recommend_product_id, follow_up_type, follow_up_notes, follow_up_method) " +
                       "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?)";
            pstmt = conn.prepareStatement(sql);
            
            // 设置参数值
            pstmt.setString(1, record.getRecord_id());
            pstmt.setString(2, record.getStaff_id());
            pstmt.setString(3, record.getCustomer_id()); 
            pstmt.setTimestamp(4, new Timestamp(record.getFollow_up_date().getTime()));
            pstmt.setTimestamp(5, new Timestamp(record.getCreate_tm().getTime()));
            pstmt.setInt(6, record.getStatus());

            pstmt.setString(7, record.getWecom_saas_id());
            
            if (record.getPurchase_intention() != null) {
            	pstmt.setInt(8, record.getPurchase_intention());
            } else {
                pstmt.setNull(8, java.sql.Types.INTEGER); 
            } 
            
            pstmt.setString(9, record.getRecommend_product_id());
            pstmt.setInt(10, record.getFollow_up_type());
            pstmt.setString(11, record.getFollow_up_notes());
            pstmt.setInt(12, record.getFollow_up_method()); 
            SQLLogUtils.printSQL(pstmt);
            int affectedRows = pstmt.executeUpdate();
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, null);
        }
        return 0;
    }
	
	public static long insertWecomSalesTags(WecomSalesTags tag) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "INSERT INTO wecom_sales_tags (tags_id, tags_name, wecom_saas_id, create_tm, sort, status) " +
                         "VALUES (?, ?, ?, ?, ?, ?)";
            pstmt = conn.prepareStatement(sql);

            // 设置参数值
            pstmt.setString(1, tag.getTags_id());
            pstmt.setString(2, tag.getTags_name());
            pstmt.setString(3, tag.getWecom_saas_id());
            pstmt.setTimestamp(4, new Timestamp(tag.getCreate_tm().getTime()));
            pstmt.setInt(5, tag.getSort());
            pstmt.setInt(6, tag.getStatus());

            pstmt.executeUpdate();
            
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, null);
        }
        return 0;
    }

    // 更新数据方法
	public static boolean updateWecomSalesCustomer(WecomSalesCustomer customer) {
	    Connection conn = null;
	    PreparedStatement pstmt = null;
	    try {
	        conn = DriverManager.getConnection(URL, USER, PASSWD);
	        String sql = "UPDATE wecom_sales_customer SET " +
	                     "wecom_external_userid=?, " +
	                     "wecom_name=?, " +
	                     "wecom_type=?, " +
	                     "wecom_avatar=?, " +
	                     "wecom_gender=?, " +
	                     "wecom_follow_users_json=?, " +
	                     "wecom_join_tm=?, " +
	                     "wecom_related_groups=?, " +
	                     "wecom_related_follower=?, " +
	                     "wecom_related_tags=?, " +
	                     "sys_create_tm=?, " +
	                     "sys_update_tm=?, " +
	                     "customer_nickname=?, " +
	                     "customer_roles=?, " +
	                     "customer_tags=?, " +
	                     "staff_id=?, " +
	                     "ext_staff_id=?, " +
	                     "customer_gender=?, " +
	                     "customer_occupation=?, " +
	                     "customer_boss_ind=?, " +
	                     "customer_highest_education=?, " +
	                     "customer_work_year=?, " +
	                     "customer_type=?, " +
	                     "customer_sf=?, " +
	                     "customer_cs=?, " +
	                     "customer_phone1=?, " +
	                     "customer_phone2=?, " +
	                     "customer_email=?, " +
	                     "customer_wechat1=?, " +
	                     "customer_wechat2=?, " +
	                     "customer_qq=?, " +
	                     "status=?, " +
	                     "purchase_intention=?, " +
	                     "new_indicator=? " +
	                     "WHERE customer_id=?";
	        pstmt = conn.prepareStatement(sql);

	        // 设置参数值
	        pstmt.setString(1, customer.getWecom_external_userid());
	        pstmt.setString(2, customer.getWecom_name());
	        pstmt.setInt(3, customer.getWecom_type());
	        pstmt.setString(4, customer.getWecom_avatar());
	        pstmt.setInt(5, customer.getWecom_gender());
	        pstmt.setString(6, customer.getWecom_follow_users_json());
	        pstmt.setTimestamp(7, new Timestamp(customer.getWecom_join_tm().getTime()));
	        pstmt.setString(8, customer.getWecom_related_groups());
	        pstmt.setString(9, customer.getWecom_related_follower());
	        pstmt.setString(10, customer.getWecom_related_tags());
	        pstmt.setTimestamp(11, new Timestamp(customer.getSys_create_tm().getTime()));
	        pstmt.setTimestamp(12, new Timestamp(customer.getSys_update_tm().getTime()));
	        pstmt.setString(13, customer.getCustomer_nickname());
	        pstmt.setInt(14, customer.getCustomer_roles());
	        pstmt.setString(15, customer.getCustomer_tags());
	        pstmt.setString(16, customer.getStaff_id());
	        pstmt.setString(17, customer.getExt_staff_id());
	        pstmt.setInt(18, customer.getCustomer_gender());
	        pstmt.setString(19, customer.getCustomer_occupation());
	        pstmt.setInt(20, customer.getCustomer_boss_ind());
	        pstmt.setInt(21, customer.getCustomer_highest_education());
	        pstmt.setInt(22, customer.getCustomer_work_year());
	        pstmt.setInt(23, customer.getCustomer_type());
	        pstmt.setString(24, customer.getCustomer_sf());
	        pstmt.setString(25, customer.getCustomer_cs());
	        pstmt.setString(26, customer.getCustomer_phone1());
	        pstmt.setString(27, customer.getCustomer_phone2());
	        pstmt.setString(28, customer.getCustomer_email());
	        pstmt.setString(29, customer.getCustomer_wechat1());
	        pstmt.setString(30, customer.getCustomer_wechat2());
	        pstmt.setString(31, customer.getCustomer_qq());
	        pstmt.setInt(32, customer.getStatus());
	        pstmt.setInt(33, customer.getPurchase_intention());
	        pstmt.setInt(34, customer.getNew_indicator());
	        pstmt.setString(35, customer.getCustomer_id());

	        SQLLogUtils.printSQL(pstmt);
	        return pstmt.executeUpdate() > 0;
	    } catch (Exception e) {
	        e.printStackTrace();
	        return false;
	    } finally {
	        closeAllConnection(conn, pstmt, null);
	    }
	}
	
	public static WecomSalesStaff getWecomSalesStaff(String staff_id, String password) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SELECT_CONDITION = "SELECT * FROM wecom_sales_staff x WHERE x.staff_id = ? and stf_passwd = ?";
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, staff_id);
			ps.setString(2, password);
			rs = ps.executeQuery();
			while (rs.next()) {
				return setWecomSalesStaffAll(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public static List<WecomGroupsMoveHistory> getWecomGroupsMoveHistory(String wecom_external_userid) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<WecomGroupsMoveHistory> historyList = new ArrayList<>();
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String SELECT_CONDITION = "SELECT x.*, y.wecom_name as wecom_name FROM wecom_groups_move_history x left join wecom_groups y on x.wecom_chat_id = y.wecom_chat_id WHERE x.wecom_external_userid = ? ORDER BY x.create_tm desc, x.wecom_join_time desc";
            ps = conn.prepareStatement(SELECT_CONDITION);
            ps.setString(1, wecom_external_userid);
            rs = ps.executeQuery();
            while (rs.next()) {
            	WecomGroupsMoveHistory history = setWecomGroupsMoveHistoryAll(rs);
            	history.setExt_wecom_chat_group_name(rs.getString("wecom_name"));
                historyList.add(history);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        return historyList;
    }

	public static WecomSalesFollowUpRecord setWecomSalesFollowUpRecordAll(ResultSet rs) throws SQLException {
		 WecomSalesFollowUpRecord record = new WecomSalesFollowUpRecord();
	     record.setRecord_id(rs.getString("record_id"));
	     record.setStaff_id(rs.getString("staff_id"));
	     record.setCustomer_id(rs.getString("customer_id"));
	     record.setFollow_up_date(rs.getTimestamp("follow_up_date"));
	     record.setFollow_up_method(rs.getInt("follow_up_method"));
	     record.setFollow_up_notes(rs.getString("follow_up_notes"));
	     record.setNext_follow_up_date(rs.getTimestamp("next_follow_up_date"));
	     record.setStatus(rs.getInt("status"));
	     record.setFollow_up_type(rs.getInt("follow_up_type"));
	     record.setPurchase_intention(rs.getInt("purchase_intention"));
	     record.setRecommend_product_id(rs.getString("recommend_product_id"));
	     record.setCreate_tm(rs.getTimestamp("create_tm"));
	     record.setUpdated_tm(rs.getTimestamp("updated_tm"));
	     record.setExt_customer_name(rs.getString("customer_nickname"));
	     return record;
	}
	
	public static WecomGroupsMoveHistory setWecomGroupsMoveHistoryAll(ResultSet rs) throws SQLException {
	    WecomGroupsMoveHistory history = new WecomGroupsMoveHistory();
	    history.setWecom_external_userid(rs.getString("wecom_external_userid"));
	    history.setWecom_chat_id(rs.getString("wecom_chat_id"));
	    history.setMove_type(rs.getInt("move_type"));
	    history.setCreate_tm(rs.getTimestamp("create_tm"));
	    history.setWecom_join_time(rs.getTimestamp("wecom_join_time"));
	    history.setWecom_join_scene(rs.getInt("wecom_join_scene"));
	    history.setWecom_group_nickname(rs.getString("wecom_group_nickname"));
	    history.setWecom_invitor_userid(rs.getString("wecom_invitor_userid"));
	    return history;
	}
	
	public static WecomSalesStaff setWecomSalesStaffAll(ResultSet rs) throws SQLException {
	    WecomSalesStaff wecomSalesStaff = new WecomSalesStaff();
	    wecomSalesStaff.setStaff_id(rs.getString("staff_id"));
	    wecomSalesStaff.setStf_passwd(rs.getString("stf_passwd"));
	    wecomSalesStaff.setStf_name(rs.getString("stf_name"));
	    wecomSalesStaff.setStf_gender(rs.getInt("stf_gender"));
	    wecomSalesStaff.setStf_birth_date(rs.getDate("stf_birth_date"));
	    wecomSalesStaff.setStf_join_date(rs.getDate("stf_join_date"));
	    wecomSalesStaff.setLast_login_tm(rs.getTimestamp("last_login_tm"));
	    wecomSalesStaff.setDept_id(rs.getString("dept_id"));
	    wecomSalesStaff.setStf_position(rs.getString("stf_position"));
	    wecomSalesStaff.setStf_phone(rs.getString("stf_phone"));
	    wecomSalesStaff.setStf_email(rs.getString("stf_email"));
	    wecomSalesStaff.setStf_address(rs.getString("stf_address"));
	    wecomSalesStaff.setStf_nature(rs.getInt("stf_nature"));
	    wecomSalesStaff.setStatus(rs.getInt("status"));
	    wecomSalesStaff.setWecom_saas_id(rs.getString("wecom_saas_id"));
	    return wecomSalesStaff;
	}
	
	public static WecomSalesCustomer setWecomSalesCustomerAll(ResultSet rs) throws SQLException {
        WecomSalesCustomer wecomSalesCustomer = new WecomSalesCustomer();
        wecomSalesCustomer.setCustomer_id(rs.getString("customer_id"));
        wecomSalesCustomer.setWecom_external_userid(rs.getString("wecom_external_userid"));
        wecomSalesCustomer.setWecom_name(rs.getString("wecom_name"));
        wecomSalesCustomer.setWecom_type(rs.getInt("wecom_type"));
        wecomSalesCustomer.setWecom_avatar(rs.getString("wecom_avatar"));
        wecomSalesCustomer.setWecom_gender(rs.getInt("wecom_gender"));
        wecomSalesCustomer.setWecom_follow_users_json(rs.getString("wecom_follow_users_json"));
        wecomSalesCustomer.setWecom_join_tm(rs.getTimestamp("wecom_join_tm"));
        wecomSalesCustomer.setSys_create_tm(rs.getTimestamp("sys_create_tm"));
        wecomSalesCustomer.setSys_update_tm(rs.getTimestamp("sys_update_tm"));
        wecomSalesCustomer.setCustomer_nickname(rs.getString("customer_nickname"));
        wecomSalesCustomer.setCustomer_roles(rs.getInt("customer_roles"));
        wecomSalesCustomer.setCustomer_tags(rs.getString("customer_tags"));
        wecomSalesCustomer.setStaff_id(rs.getString("staff_id"));
        wecomSalesCustomer.setExt_staff_id(rs.getString("ext_staff_id"));
        wecomSalesCustomer.setCustomer_gender(rs.getInt("customer_gender"));
        wecomSalesCustomer.setCustomer_occupation(rs.getString("customer_occupation"));
        wecomSalesCustomer.setCustomer_boss_ind(rs.getInt("customer_boss_ind"));
        wecomSalesCustomer.setCustomer_highest_education(rs.getInt("customer_highest_education"));
        wecomSalesCustomer.setCustomer_work_year(rs.getInt("customer_work_year"));
        wecomSalesCustomer.setCustomer_type(rs.getInt("customer_type"));
        wecomSalesCustomer.setCustomer_sf(rs.getString("customer_sf"));
        wecomSalesCustomer.setCustomer_cs(rs.getString("customer_cs"));
        wecomSalesCustomer.setCustomer_phone1(rs.getString("customer_phone1"));
        wecomSalesCustomer.setCustomer_phone2(rs.getString("customer_phone2"));
        wecomSalesCustomer.setCustomer_email(rs.getString("customer_email"));
        wecomSalesCustomer.setCustomer_wechat1(rs.getString("customer_wechat1"));
        wecomSalesCustomer.setCustomer_wechat2(rs.getString("customer_wechat2"));
        wecomSalesCustomer.setCustomer_qq(rs.getString("customer_qq"));
        wecomSalesCustomer.setStatus(rs.getInt("status"));
        wecomSalesCustomer.setPurchase_intention(rs.getInt("purchase_intention")); 
        wecomSalesCustomer.setNew_indicator(rs.getInt("new_indicator"));
        return wecomSalesCustomer;
    }

	
	private static void closeAllConnection(Connection conn, PreparedStatement ps, ResultSet rs) {
		try {
			if (rs != null)
				rs.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (ps != null)
				ps.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (conn != null)
				conn.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		rs = null;
		ps = null;
		conn = null;
	}
}
