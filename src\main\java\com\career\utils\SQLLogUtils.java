package com.career.utils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.sql.PreparedStatement;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * SQL日志工具类
 * 用于打印带参数的PreparedStatement，方便调试
 */
public class SQLLogUtils {
    
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 从PreparedStatement中提取完整SQL
     * @param ps PreparedStatement对象
     * @return 完整SQL语句
     */
    public static String getFullSQL(PreparedStatement ps) {
        if (ps == null) {
            return "";
        }
        
        try {
            // 通过反射获取PreparedStatement的参数
            Field sqlField = null;
            String sql = "";
            Object[] params = null;
            
            // 尝试获取SQL模板
            try {
                // 对于MySQL的JDBC驱动
                sqlField = ps.getClass().getSuperclass().getDeclaredField("originalSql");
                sqlField.setAccessible(true);
                sql = (String) sqlField.get(ps);
            } catch (NoSuchFieldException e) {
                try {
                    // 对于其他JDBC驱动的可能实现
                    sqlField = ps.getClass().getDeclaredField("sql");
                    sqlField.setAccessible(true);
                    sql = (String) sqlField.get(ps);
                } catch (NoSuchFieldException ex) {
                    // 如果无法获取SQL，返回toString的结果，但去除类名前缀
                    String result = ps.toString();
                    int colonIndex = result.indexOf(": ");
                    if (colonIndex > 0) {
                        return result.substring(colonIndex + 2);
                    }
                    return result;
                }
            }
            
            // 尝试获取参数值
            List<Object> paramValues = new ArrayList<>();
            try {
                // 获取参数映射
                Field paramMapField = null;
                
                // 尝试不同的字段名称
                try {
                    paramMapField = ps.getClass().getDeclaredField("parameterValues");
                } catch (NoSuchFieldException e) {
                    try {
                        paramMapField = ps.getClass().getDeclaredField("parameterMap");
                    } catch (NoSuchFieldException ex) {
                        try {
                            paramMapField = ps.getClass().getSuperclass().getDeclaredField("parameterMap");
                        } catch (NoSuchFieldException exc) {
                            // 如果无法获取参数映射，返回原始SQL
                            return sql;
                        }
                    }
                }
                
                paramMapField.setAccessible(true);
                Object paramMap = paramMapField.get(ps);
                
                // 获取参数值
                if (paramMap instanceof Object[]) {
                    params = (Object[]) paramMap;
                    for (Object param : params) {
                        if (param != null) {
                            paramValues.add(param);
                        }
                    }
                } else if (paramMap instanceof List) {
                    List<?> paramList = (List<?>) paramMap;
                    for (Object param : paramList) {
                        if (param != null) {
                            paramValues.add(param);
                        }
                    }
                } else {
                    // 尝试通过反射调用getParameterValues方法
                    try {
                        Method getParamValuesMethod = ps.getClass().getMethod("getParameterValues");
                        getParamValuesMethod.setAccessible(true);
                        Object result = getParamValuesMethod.invoke(ps);
                        if (result instanceof Object[]) {
                            params = (Object[]) result;
                            for (Object param : params) {
                                if (param != null) {
                                    paramValues.add(param);
                                }
                            }
                        }
                    } catch (Exception e) {
                        // 无法获取参数值，返回原始SQL
                        return sql;
                    }
                }
            } catch (Exception e) {
                // 如果无法获取参数值，返回原始SQL
                return sql;
            }
            
            // 替换SQL中的占位符
            return getFullSQL(sql, paramValues.toArray());
            
        } catch (Exception e) {
            // 如果发生异常，返回PreparedStatement的toString结果，但去除类名前缀
            String result = ps.toString();
            int colonIndex = result.indexOf(": ");
            if (colonIndex > 0) {
                return result.substring(colonIndex + 2);
            }
            return result;
        }
    }
    
    /**
     * 打印完整SQL语句(替换占位符)
     * @param sql SQL模板
     * @param params SQL参数
     * @return 替换参数后的完整SQL
     */
    public static String getFullSQL(String sql, Object... params) {
        if (sql == null) {
            return "";
        }
        
        if (params == null || params.length == 0) {
            return sql;
        }
        
        StringBuilder result = new StringBuilder(sql);
        
        // 从后向前替换，避免位置偏移问题
        int paramIndex = 0;
        int questionMarkIndex;
        
        while ((questionMarkIndex = result.indexOf("?")) != -1 && paramIndex < params.length) {
            Object param = params[paramIndex++];
            String value = formatValue(param);
            
            result.replace(questionMarkIndex, questionMarkIndex + 1, value);
        }
        
        return result.toString();
    }
    
    /**
     * 格式化参数值
     * @param value 参数值
     * @return 格式化后的字符串
     */
    private static String formatValue(Object value) {
        if (value == null) {
            return "NULL";
        }
        
        // 字符串类型
        if (value instanceof String) {
            return "'" + escapeString((String)value) + "'";
        }
        
        // 日期时间类型
        if (value instanceof Date) {
            return "'" + DATE_FORMAT.format((Date)value) + "'";
        }
        
        // Timestamp类型
        if (value instanceof Timestamp) {
            return "'" + value.toString() + "'";
        }
        
        // 布尔类型
        if (value instanceof Boolean) {
            return ((Boolean)value) ? "1" : "0";
        }
        
        // 其他类型直接转字符串
        return value.toString();
    }
    
    /**
     * 转义字符串中的特殊字符
     * @param str 原始字符串
     * @return 转义后的字符串
     */
    private static String escapeString(String str) {
        if (str == null) {
            return "";
        }
        return str.replace("'", "''").replace("\\", "\\\\");
    }
    
    /**
     * 打印PreparedStatement的完整SQL
     * @param ps PreparedStatement对象
     */
    public static void printSQL(PreparedStatement ps) {
        printSQL("", ps); // 调用带tag的方法，传入空tag
    }
    
    /**
     * 打印PreparedStatement的完整SQL，同时输出标识信息
     * @param tag 标识信息
     * @param ps PreparedStatement对象
     */
    public static void printSQL(String tag, PreparedStatement ps) {
        String sql = getFullSQL(ps);
        // 去除可能存在的类名前缀
        if (sql.contains(": ")) {
            int colonIndex = sql.indexOf(": ");
            sql = sql.substring(colonIndex + 2);
        }
        String prefix = tag.isEmpty() ? "[SQL] " : "[SQL-" + tag + "] ";
        Tools.println(prefix + sql);
    }
    
    /**
     * 打印带参数的SQL
     * @param sql SQL模板
     * @param params SQL参数
     */
    public static void printSQL(String sql, Object... params) {
        Tools.println("[SQL] " + getFullSQL(sql, params));
    }
    
    /**
     * 打印带参数的SQL，同时输出标识信息
     * @param tag 标识信息
     * @param sql SQL模板
     * @param params SQL参数
     */
    public static void printSQL(String tag, String sql, Object... params) {
        Tools.println("[SQL-" + tag + "] " + getFullSQL(sql, params));
    }
}