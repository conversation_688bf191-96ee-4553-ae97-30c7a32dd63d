package com.career.db;

import java.util.Date;

public class AiCard {
	
    private String c_id;
    private String c_passwd;
    private String c_prov;
    private String c_city;
    private int c_score;
    private int c_score_wc;
    private String c_xk;
    private Date create_tm;
    private Date active_tm;
    private Date last_login_tm;
    private Date last_modified_tm;
    private Date expire_tm;
    private String remark;
    private String c_phone;
    private String c_function;
    private String c_allowed_prov;
    private String agent_id;
    private int status;
    private String c_type;
    private int cnt_download;
    private int review_ai_cnt;
    private int review_maual_cnt;

	public int getReview_ai_cnt() {
		return review_ai_cnt;
	}
	public void setReview_ai_cnt(int review_ai_cnt) {
		this.review_ai_cnt = review_ai_cnt;
	}
	public int getReview_maual_cnt() {
		return review_maual_cnt;
	}
	public void setReview_maual_cnt(int review_maual_cnt) {
		this.review_maual_cnt = review_maual_cnt;
	}
	public String getC_allowed_prov() {
		return c_allowed_prov;
	}
	public void setC_allowed_prov(String c_allowed_prov) {
		this.c_allowed_prov = c_allowed_prov;
	}
	public Date getLast_modified_tm() {
		return last_modified_tm;
	}
	public void setLast_modified_tm(Date last_modified_tm) {
		this.last_modified_tm = last_modified_tm;
	}
	public String getC_type() {
		return c_type;
	}
	public void setC_type(String c_type) {
		this.c_type = c_type;
	}
	public String getC_id() {
		return c_id;
	}
	public void setC_id(String c_id) {
		this.c_id = c_id;
	}
	public String getC_passwd() {
		return c_passwd;
	}
	public void setC_passwd(String c_passwd) {
		this.c_passwd = c_passwd;
	}
	public String getC_prov() {
		return c_prov;
	}
	public void setC_prov(String c_prov) {
		this.c_prov = c_prov;
	}
	public String getC_city() {
		return c_city;
	}
	public void setC_city(String c_city) {
		this.c_city = c_city;
	}
	public int getC_score() {
		return c_score;
	}
	public void setC_score(int c_score) {
		this.c_score = c_score;
	}
	public int getC_score_wc() {
		return c_score_wc;
	}
	public void setC_score_wc(int c_score_wc) {
		this.c_score_wc = c_score_wc;
	}
	public String getC_xk() {
		return c_xk;
	}
	public void setC_xk(String c_xk) {
		this.c_xk = c_xk;
	}
	public Date getCreate_tm() {
		return create_tm;
	}
	public void setCreate_tm(Date create_tm) {
		this.create_tm = create_tm;
	}
	public Date getActive_tm() {
		return active_tm;
	}
	public void setActive_tm(Date active_tm) {
		this.active_tm = active_tm;
	}
	public Date getLast_login_tm() {
		return last_login_tm;
	}
	public void setLast_login_tm(Date last_login_tm) {
		this.last_login_tm = last_login_tm;
	}
	public Date getExpire_tm() {
		return expire_tm;
	}
	public void setExpire_tm(Date expire_tm) {
		this.expire_tm = expire_tm;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getC_phone() {
		return c_phone;
	}
	public void setC_phone(String c_phone) {
		this.c_phone = c_phone;
	}
	public String getC_function() {
		return c_function;
	}
	public void setC_function(String c_function) {
		this.c_function = c_function;
	}
	public String getAgent_id() {
		return agent_id;
	}
	public void setAgent_id(String agent_id) {
		this.agent_id = agent_id;
	}
	public int getStatus() {
		return status;
	}
	public void setStatus(int status) {
		this.status = status;
	}
	public int getCnt_download() {
		return cnt_download;
	}
	public void setCnt_download(int cnt_download) {
		this.cnt_download = cnt_download;
	}
    
    
}
