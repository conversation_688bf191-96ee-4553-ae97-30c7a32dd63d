// Decompiled by Jad v1.5.8g. Copyright 2001 <PERSON>.
// Jad home page: http://www.kpdus.com/jad.html
// Decompiler options: packimports(3) 
// Source File Name:   MarjorBean.java

package com.career.db;

import java.io.*;

public class PlanZK{
	private String zydm;
	private String zymc;
	private String yxdm;
	private String yxmc;
	private String jhs;
	private String xf;
	private String zz_pg;
	private String bxxz;

	private String zyl;
	private int nf;
	
	public String getZyl() {
		return zyl;
	}
	public void setZyl(String zyl) {
		this.zyl = zyl;
	}
	public String getBxxz() {
		return bxxz;
	}
	public void setBxxz(String bxxz) {
		this.bxxz = bxxz;
	}
	public int getNf() {
		return nf;
	}
	public void setNf(int nf) {
		this.nf = nf;
	}
	public String getZz_pg() {
		return zz_pg;
	}
	public void setZz_pg(String zz_pg) {
		this.zz_pg = zz_pg;
	}
	public String getZydm() {
		return zydm;
	}
	public void setZydm(String zydm) {
		this.zydm = zydm;
	}
	public String getZymc() {
		return zymc;
	}
	public void setZymc(String zymc) {
		this.zymc = zymc;
	}
	public String getYxdm() {
		return yxdm;
	}
	public void setYxdm(String yxdm) {
		this.yxdm = yxdm;
	}
	public String getYxmc() {
		return yxmc;
	}
	public void setYxmc(String yxmc) {
		this.yxmc = yxmc;
	}
	public String getJhs() {
		return jhs;
	}
	public void setJhs(String jhs) {
		this.jhs = jhs;
	}
	public String getXf() {
		return xf;
	}
	public void setXf(String xf) {
		this.xf = xf;
	}
	
	
	
	
}
