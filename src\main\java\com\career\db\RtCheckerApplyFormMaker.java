package com.career.db;

public class RtCheckerApplyFormMaker {

	private int id;
	private String batch_id;
	private String batch_id_org;
	private String yxmc;
	private String yxmc_org;
	private String yxbz;
	private String yxdm;
	private String zyz;
	private String zymc;
	private String zymc_org;
	private String zybz;
	private String zydm;
	private Integer zdf_a;
	private Integer zdf_b;
	private Integer zdf_c;
	private Integer zdfwc_a;
	private Integer zdfwc_b;
	private Integer zdfwc_c;
	private Integer pjf_a;
	private Integer pjf_b;
	private Integer pjf_c;
	private Integer pjfwc_a;
	private Integer pjfwc_b;
	private Integer pjfwc_c;
	private Integer jhs_a;
	private Integer jhs_b;
	private Integer jhs_c;
	private Integer zgf_a;
	private Integer zgf_b;
	private Integer zgf_c;
	private Integer zgfwc_a;
	private Integer zgfwc_b;
	private Integer zgfwc_c;
	private Integer jhs;
	private String fee;
	private int seq_no_zy;
	private int seq_no_yx;
	
	private String adjust_zy;
	private String adjust_dx;
	
	private boolean ext_no_adjust;
	
	private int ext_waste_score;
	
	public boolean isExt_no_adjust() {
		return ext_no_adjust;
	}
	public void setExt_no_adjust(boolean ext_no_adjust) {
		this.ext_no_adjust = ext_no_adjust;
	}
	public int getExt_waste_score() {
		return ext_waste_score;
	}
	public void setExt_waste_score(int ext_waste_score) {
		this.ext_waste_score = ext_waste_score;
	}
	public String getAdjust_zy() {
		return adjust_zy;
	}
	public void setAdjust_zy(String adjust_zy) {
		this.adjust_zy = adjust_zy;
	}
	public String getAdjust_dx() {
		return adjust_dx;
	}
	public void setAdjust_dx(String adjust_dx) {
		this.adjust_dx = adjust_dx;
	}
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getBatch_id() {
		return batch_id;
	}
	public void setBatch_id(String batch_id) {
		this.batch_id = batch_id;
	}
	public String getBatch_id_org() {
		return batch_id_org;
	}
	public void setBatch_id_org(String batch_id_org) {
		this.batch_id_org = batch_id_org;
	}
	public String getYxmc() {
		return yxmc;
	}
	public void setYxmc(String yxmc) {
		this.yxmc = yxmc;
	}
	public String getYxmc_org() {
		return yxmc_org;
	}
	public void setYxmc_org(String yxmc_org) {
		this.yxmc_org = yxmc_org;
	}
	public String getYxbz() {
		return yxbz;
	}
	public void setYxbz(String yxbz) {
		this.yxbz = yxbz;
	}
	public String getYxdm() {
		return yxdm;
	}
	public void setYxdm(String yxdm) {
		this.yxdm = yxdm;
	}
	public String getZyz() {
		return zyz;
	}
	public void setZyz(String zyz) {
		this.zyz = zyz;
	}
	public String getZymc() {
		return zymc;
	}
	public void setZymc(String zymc) {
		this.zymc = zymc;
	}
	public String getZymc_org() {
		return zymc_org;
	}
	public void setZymc_org(String zymc_org) {
		this.zymc_org = zymc_org;
	}
	public String getZybz() {
		return zybz;
	}
	public void setZybz(String zybz) {
		this.zybz = zybz;
	}
	public String getZydm() {
		return zydm;
	}
	public void setZydm(String zydm) {
		this.zydm = zydm;
	}
	public Integer getZdf_a() {
		return zdf_a;
	}
	public void setZdf_a(Integer zdf_a) {
		this.zdf_a = zdf_a;
	}
	public Integer getZdf_b() {
		return zdf_b;
	}
	public void setZdf_b(Integer zdf_b) {
		this.zdf_b = zdf_b;
	}
	public Integer getZdf_c() {
		return zdf_c;
	}
	public void setZdf_c(Integer zdf_c) {
		this.zdf_c = zdf_c;
	}
	public Integer getZdfwc_a() {
		return zdfwc_a;
	}
	public void setZdfwc_a(Integer zdfwc_a) {
		this.zdfwc_a = zdfwc_a;
	}
	public Integer getZdfwc_b() {
		return zdfwc_b;
	}
	public void setZdfwc_b(Integer zdfwc_b) {
		this.zdfwc_b = zdfwc_b;
	}
	public Integer getZdfwc_c() {
		return zdfwc_c;
	}
	public void setZdfwc_c(Integer zdfwc_c) {
		this.zdfwc_c = zdfwc_c;
	}
	public Integer getPjf_a() {
		return pjf_a;
	}
	public void setPjf_a(Integer pjf_a) {
		this.pjf_a = pjf_a;
	}
	public Integer getPjf_b() {
		return pjf_b;
	}
	public void setPjf_b(Integer pjf_b) {
		this.pjf_b = pjf_b;
	}
	public Integer getPjf_c() {
		return pjf_c;
	}
	public void setPjf_c(Integer pjf_c) {
		this.pjf_c = pjf_c;
	}
	public Integer getPjfwc_a() {
		return pjfwc_a;
	}
	public void setPjfwc_a(Integer pjfwc_a) {
		this.pjfwc_a = pjfwc_a;
	}
	public Integer getPjfwc_b() {
		return pjfwc_b;
	}
	public void setPjfwc_b(Integer pjfwc_b) {
		this.pjfwc_b = pjfwc_b;
	}
	public Integer getPjfwc_c() {
		return pjfwc_c;
	}
	public void setPjfwc_c(Integer pjfwc_c) {
		this.pjfwc_c = pjfwc_c;
	}
	public Integer getJhs_a() {
		return jhs_a;
	}
	public void setJhs_a(Integer jhs_a) {
		this.jhs_a = jhs_a;
	}
	public Integer getJhs_b() {
		return jhs_b;
	}
	public void setJhs_b(Integer jhs_b) {
		this.jhs_b = jhs_b;
	}
	public Integer getJhs_c() {
		return jhs_c;
	}
	public void setJhs_c(Integer jhs_c) {
		this.jhs_c = jhs_c;
	}
	public Integer getZgf_a() {
		return zgf_a;
	}
	public void setZgf_a(Integer zgf_a) {
		this.zgf_a = zgf_a;
	}
	public Integer getZgf_b() {
		return zgf_b;
	}
	public void setZgf_b(Integer zgf_b) {
		this.zgf_b = zgf_b;
	}
	public Integer getZgf_c() {
		return zgf_c;
	}
	public void setZgf_c(Integer zgf_c) {
		this.zgf_c = zgf_c;
	}
	public Integer getZgfwc_a() {
		return zgfwc_a;
	}
	public void setZgfwc_a(Integer zgfwc_a) {
		this.zgfwc_a = zgfwc_a;
	}
	public Integer getZgfwc_b() {
		return zgfwc_b;
	}
	public void setZgfwc_b(Integer zgfwc_b) {
		this.zgfwc_b = zgfwc_b;
	}
	public Integer getZgfwc_c() {
		return zgfwc_c;
	}
	public void setZgfwc_c(Integer zgfwc_c) {
		this.zgfwc_c = zgfwc_c;
	}
	public Integer getJhs() {
		return jhs;
	}
	public void setJhs(Integer jhs) {
		this.jhs = jhs;
	}
	public String getFee() {
		return fee;
	}
	public void setFee(String fee) {
		this.fee = fee;
	}
	public int getSeq_no_zy() {
		return seq_no_zy;
	}
	public void setSeq_no_zy(int seq_no_zy) {
		this.seq_no_zy = seq_no_zy;
	}
	public int getSeq_no_yx() {
		return seq_no_yx;
	}
	public void setSeq_no_yx(int seq_no_yx) {
		this.seq_no_yx = seq_no_yx;
	}
	
    
}
