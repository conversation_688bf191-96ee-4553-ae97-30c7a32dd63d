package com.career.utils;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Stack;

import com.career.db.JDBC;

public class XKCombineUtils {
	
	public static HashMap<String, String> XK_MAP = new HashMap<>();
	private static HashMap<String, String> XK_NATIVE_MAP = new HashMap<>();
	public static Stack<String> STACK = new Stack<String>();
	
	public static String getXKCodeByCombine(String Combine) {
		return XK_MAP.get(Combine);
	}
	
	public static String getXKCodeByStudentSelection(String Combine) {
		return XK_NATIVE_MAP.get(Combine);
	}
	
	private static List<List<String>> combineXK(List<List<String>> stringList,Stack<String> stack,List<String> shu, int targ, int has, int cur) {
		if(has == targ) {
			List<String> s = new ArrayList<String>(stack);
			stringList.add(s);
		}
		for(int i=cur;i<shu.size();i++) {
			if(!stack.contains(shu.get(i))) {
				stack.add(shu.get(i));
				combineXK(stringList,stack,shu, targ, has+1, i);
				stack.pop();
			}
		}
		return stringList;
	}
	
	private static void swap(String[] shu, int targ, int cur, String tempCode) {
        if(cur == targ) {
            Iterator<String> it = STACK.iterator();
            HashSet<String> hs = new HashSet<>();
            while(it.hasNext()) {
            	hs.add((String)it.next());
            }
            if(hs.size() == STACK.size()) {
            	//System.out.println(STACK);
            	XK_MAP.put(STACK.toString(), tempCode);
            }
            return;
        }
         
        for(int i=0;i<shu.length;i++) {
        	STACK.add(shu[i]);
            swap(shu, targ, cur+1,tempCode);
            STACK.pop();
             
        }
    }
	
	
	
	private static void init() {
		String str = "物#化#生#史#政#地#技";
		//String str = "物理#化学#生物#历史#政治#地理#技术";
		List<String> xkList = Arrays.asList(str.split("#"));
		int index = 0;
		if(xkList.size() >= 3){
			List<List<String>> sList = combineXK(new ArrayList<>(),new Stack<>(),xkList,3,0,0);
			String tempCode = null;
			for (List<String> ss : sList){
				if(++index > 9) {
					tempCode = String.valueOf(((char)(index + 55)));
				}else{
					tempCode = String.valueOf(index);
				}
				String[] temp = ss.toArray(new String[0]);
				swap(temp, 3, 0, tempCode);
			}
			
			Iterator<String> itKeys = XK_MAP.keySet().iterator();
			while(itKeys.hasNext()) {
				String key = itKeys.next();
				//System.out.println(key + " - " + XK_MAP.get(key));
			}
		}
	}
	
	static {
		init();
		intNativeMap();
	}
	
	
	public static void intNativeMap() {
		XK_NATIVE_MAP.put("生物+历史+技术","S");
		XK_NATIVE_MAP.put("地理+历史+政治","W");
		XK_NATIVE_MAP.put("物理+地理+政治","D");
		XK_NATIVE_MAP.put("生物+历史+化学","G");
		XK_NATIVE_MAP.put("技术+化学+历史","M");
		XK_NATIVE_MAP.put("历史+地理+政治","W");
		XK_NATIVE_MAP.put("地理+化学+物理","4");
		XK_NATIVE_MAP.put("物理+历史+生物","6");
		XK_NATIVE_MAP.put("地理+化学+历史","L");
		XK_NATIVE_MAP.put("政治+化学+地理","N");
		XK_NATIVE_MAP.put("技术+历史+政治","X");
		XK_NATIVE_MAP.put("化学+历史+物理","2");
		XK_NATIVE_MAP.put("历史+物理+技术","C");
		XK_NATIVE_MAP.put("历史+生物+物理","6");
		XK_NATIVE_MAP.put("化学+技术+历史","M");
		XK_NATIVE_MAP.put("生物+技术+化学","J");
		XK_NATIVE_MAP.put("历史+物理+化学","2");
		XK_NATIVE_MAP.put("技术+化学+物理","5");
		XK_NATIVE_MAP.put("技术+历史+地理","Y");
		XK_NATIVE_MAP.put("地理+生物+化学","I");
		XK_NATIVE_MAP.put("技术+历史+生物","S");
		XK_NATIVE_MAP.put("地理+政治+生物","T");
		XK_NATIVE_MAP.put("地理+技术+政治","Z");
		XK_NATIVE_MAP.put("化学+物理+生物","1");
		XK_NATIVE_MAP.put("政治+物理+历史","A");
		XK_NATIVE_MAP.put("物理+技术+生物","9");
		XK_NATIVE_MAP.put("技术+政治+生物","U");
		XK_NATIVE_MAP.put("化学+技术+物理","5");
		XK_NATIVE_MAP.put("政治+历史+化学","K");
		XK_NATIVE_MAP.put("化学+政治+地理","N");
		XK_NATIVE_MAP.put("历史+化学+政治","K");
		XK_NATIVE_MAP.put("生物+地理+化学","I");
		XK_NATIVE_MAP.put("历史+地理+生物","R");
		XK_NATIVE_MAP.put("物理+生物+历史","6");
		XK_NATIVE_MAP.put("生物+政治+化学","H");
		XK_NATIVE_MAP.put("历史+政治+生物","Q");
		XK_NATIVE_MAP.put("技术+政治+地理","Z");
		XK_NATIVE_MAP.put("地理+生物+技术","V");
		XK_NATIVE_MAP.put("政治+生物+历史","Q");
		XK_NATIVE_MAP.put("物理+化学+历史","2");
		XK_NATIVE_MAP.put("政治+生物+物理","7");
		XK_NATIVE_MAP.put("技术+生物+化学","J");
		XK_NATIVE_MAP.put("地理+历史+生物","R");
		XK_NATIVE_MAP.put("化学+物理+地理","4");
		XK_NATIVE_MAP.put("物理+政治+生物","7");
		XK_NATIVE_MAP.put("地理+物理+技术","F");
		XK_NATIVE_MAP.put("地理+生物+物理","8");
		XK_NATIVE_MAP.put("物理+生物+技术","9");
		XK_NATIVE_MAP.put("生物+化学+政治","H");
		XK_NATIVE_MAP.put("生物+物理+地理","8");
		XK_NATIVE_MAP.put("物理+技术+政治","E");
		XK_NATIVE_MAP.put("历史+技术+生物","S");
		XK_NATIVE_MAP.put("政治+地理+生物","T");
		XK_NATIVE_MAP.put("物理+历史+地理","B");
		XK_NATIVE_MAP.put("技术+物理+化学","5");
		XK_NATIVE_MAP.put("历史+化学+物理","2");
		XK_NATIVE_MAP.put("物理+地理+生物","8");
		XK_NATIVE_MAP.put("地理+化学+政治","N");
		XK_NATIVE_MAP.put("物理+政治+化学","3");
		XK_NATIVE_MAP.put("生物+化学+技术","J");
		XK_NATIVE_MAP.put("生物+技术+历史","S");
		XK_NATIVE_MAP.put("化学+政治+生物","H");
		XK_NATIVE_MAP.put("政治+地理+化学","N");
		XK_NATIVE_MAP.put("历史+技术+化学","M");
		XK_NATIVE_MAP.put("政治+物理+技术","E");
		XK_NATIVE_MAP.put("技术+化学+政治","O");
		XK_NATIVE_MAP.put("物理+化学+生物","1");
		XK_NATIVE_MAP.put("物理+政治+技术","E");
		XK_NATIVE_MAP.put("政治+物理+化学","3");
		XK_NATIVE_MAP.put("政治+地理+技术","Z");
		XK_NATIVE_MAP.put("技术+地理+生物","V");
		XK_NATIVE_MAP.put("政治+化学+历史","K");
		XK_NATIVE_MAP.put("化学+历史+技术","M");
		XK_NATIVE_MAP.put("政治+历史+生物","Q");
		XK_NATIVE_MAP.put("技术+物理+生物","9");
		XK_NATIVE_MAP.put("地理+技术+物理","F");
		XK_NATIVE_MAP.put("历史+生物+技术","S");
		XK_NATIVE_MAP.put("物理+化学+政治","3");
		XK_NATIVE_MAP.put("化学+历史+地理","L");
		XK_NATIVE_MAP.put("历史+政治+地理","W");
		XK_NATIVE_MAP.put("地理+物理+生物","8");
		XK_NATIVE_MAP.put("历史+生物+化学","G");
		XK_NATIVE_MAP.put("技术+生物+物理","9");
		XK_NATIVE_MAP.put("物理+生物+地理","8");
		XK_NATIVE_MAP.put("生物+物理+技术","9");
		XK_NATIVE_MAP.put("化学+生物+技术","J");
		XK_NATIVE_MAP.put("生物+地理+物理","8");
		XK_NATIVE_MAP.put("物理+历史+政治","A");
		XK_NATIVE_MAP.put("化学+政治+历史","K");
		XK_NATIVE_MAP.put("政治+技术+地理","Z");
		XK_NATIVE_MAP.put("化学+生物+物理","1");
		XK_NATIVE_MAP.put("物理+技术+地理","F");
		XK_NATIVE_MAP.put("地理+化学+生物","I");
		XK_NATIVE_MAP.put("生物+政治+技术","U");
		XK_NATIVE_MAP.put("地理+政治+化学","N");
		XK_NATIVE_MAP.put("历史+化学+地理","L");
		XK_NATIVE_MAP.put("化学+地理+政治","N");
		XK_NATIVE_MAP.put("地理+物理+化学","4");
		XK_NATIVE_MAP.put("技术+物理+历史","C");
		XK_NATIVE_MAP.put("化学+物理+政治","3");
		XK_NATIVE_MAP.put("政治+化学+生物","H");
		XK_NATIVE_MAP.put("技术+地理+历史","Y");
		XK_NATIVE_MAP.put("技术+地理+政治","Z");
		XK_NATIVE_MAP.put("生物+地理+技术","V");
		XK_NATIVE_MAP.put("技术+化学+地理","P");
		XK_NATIVE_MAP.put("化学+物理+历史","2");
		XK_NATIVE_MAP.put("生物+政治+物理","7");
		XK_NATIVE_MAP.put("政治+生物+地理","T");
		XK_NATIVE_MAP.put("技术+政治+历史","X");
		XK_NATIVE_MAP.put("技术+物理+政治","E");
		XK_NATIVE_MAP.put("化学+地理+历史","L");
		XK_NATIVE_MAP.put("技术+地理+化学","P");
		XK_NATIVE_MAP.put("生物+技术+地理","V");
		XK_NATIVE_MAP.put("历史+物理+地理","B");
		XK_NATIVE_MAP.put("政治+地理+物理","D");
		XK_NATIVE_MAP.put("历史+技术+物理","C");
		XK_NATIVE_MAP.put("生物+化学+物理","1");
		XK_NATIVE_MAP.put("物理+地理+化学","4");
		XK_NATIVE_MAP.put("生物+历史+政治","Q");
		XK_NATIVE_MAP.put("历史+物理+政治","A");
		XK_NATIVE_MAP.put("生物+政治+地理","T");
		XK_NATIVE_MAP.put("地理+生物+政治","T");
		XK_NATIVE_MAP.put("历史+政治+物理","A");
		XK_NATIVE_MAP.put("物理+政治+历史","A");
		XK_NATIVE_MAP.put("物理+地理+技术","F");
		XK_NATIVE_MAP.put("生物+历史+地理","R");
		XK_NATIVE_MAP.put("地理+历史+技术","Y");
		XK_NATIVE_MAP.put("生物+化学+历史","G");
		XK_NATIVE_MAP.put("历史+地理+化学","L");
		XK_NATIVE_MAP.put("政治+地理+历史","W");
		XK_NATIVE_MAP.put("生物+技术+政治","U");
		XK_NATIVE_MAP.put("地理+历史+化学","L");
		XK_NATIVE_MAP.put("化学+地理+生物","I");
		XK_NATIVE_MAP.put("政治+化学+技术","O");
		XK_NATIVE_MAP.put("政治+技术+历史","X");
		XK_NATIVE_MAP.put("地理+物理+历史","B");
		XK_NATIVE_MAP.put("生物+物理+化学","1");
		XK_NATIVE_MAP.put("物理+化学+技术","5");
		XK_NATIVE_MAP.put("地理+政治+物理","D");
		XK_NATIVE_MAP.put("政治+化学+物理","3");
		XK_NATIVE_MAP.put("历史+物理+生物","6");
		XK_NATIVE_MAP.put("地理+物理+政治","D");
		XK_NATIVE_MAP.put("地理+政治+技术","Z");
		XK_NATIVE_MAP.put("技术+化学+生物","J");
		XK_NATIVE_MAP.put("地理+技术+化学","P");
		XK_NATIVE_MAP.put("技术+生物+地理","V");
		XK_NATIVE_MAP.put("技术+历史+物理","C");
		XK_NATIVE_MAP.put("物理+生物+政治","7");
		XK_NATIVE_MAP.put("技术+物理+地理","F");
		XK_NATIVE_MAP.put("政治+物理+生物","7");
		XK_NATIVE_MAP.put("技术+政治+化学","O");
		XK_NATIVE_MAP.put("政治+生物+技术","U");
		XK_NATIVE_MAP.put("政治+历史+地理","W");
		XK_NATIVE_MAP.put("政治+技术+物理","E");
		XK_NATIVE_MAP.put("地理+政治+历史","W");
		XK_NATIVE_MAP.put("物理+技术+历史","C");
		XK_NATIVE_MAP.put("物理+历史+化学","2");
		XK_NATIVE_MAP.put("历史+化学+技术","M");
		XK_NATIVE_MAP.put("化学+技术+生物","J");
		XK_NATIVE_MAP.put("技术+地理+物理","F");
		XK_NATIVE_MAP.put("政治+技术+生物","U");
		XK_NATIVE_MAP.put("化学+地理+技术","P");
		XK_NATIVE_MAP.put("地理+生物+历史","R");
		XK_NATIVE_MAP.put("地理+历史+物理","B");
		XK_NATIVE_MAP.put("生物+历史+物理","6");
		XK_NATIVE_MAP.put("历史+技术+政治","X");
		XK_NATIVE_MAP.put("化学+生物+政治","H");
		XK_NATIVE_MAP.put("化学+生物+地理","I");
		XK_NATIVE_MAP.put("历史+生物+政治","Q");
		XK_NATIVE_MAP.put("生物+技术+物理","9");
		XK_NATIVE_MAP.put("化学+技术+政治","O");
		XK_NATIVE_MAP.put("化学+历史+政治","K");
		XK_NATIVE_MAP.put("历史+生物+地理","R");
		XK_NATIVE_MAP.put("历史+政治+技术","X");
		XK_NATIVE_MAP.put("历史+化学+生物","G");
		XK_NATIVE_MAP.put("历史+政治+化学","K");
		XK_NATIVE_MAP.put("物理+化学+地理","4");
		XK_NATIVE_MAP.put("物理+地理+历史","B");
		XK_NATIVE_MAP.put("地理+技术+历史","Y");
		XK_NATIVE_MAP.put("政治+历史+物理","A");
		XK_NATIVE_MAP.put("化学+政治+物理","3");
		XK_NATIVE_MAP.put("技术+历史+化学","M");
		XK_NATIVE_MAP.put("技术+生物+政治","U");
		XK_NATIVE_MAP.put("历史+地理+技术","Y");
		XK_NATIVE_MAP.put("化学+历史+生物","G");
		XK_NATIVE_MAP.put("化学+政治+技术","O");
		XK_NATIVE_MAP.put("政治+技术+化学","O");
		XK_NATIVE_MAP.put("化学+技术+地理","P");
		XK_NATIVE_MAP.put("生物+物理+历史","6");
		XK_NATIVE_MAP.put("技术+政治+物理","E");
		XK_NATIVE_MAP.put("历史+技术+地理","Y");
		XK_NATIVE_MAP.put("物理+生物+化学","1");
		XK_NATIVE_MAP.put("生物+化学+地理","I");
		XK_NATIVE_MAP.put("历史+地理+物理","B");
		XK_NATIVE_MAP.put("物理+技术+化学","5");
		XK_NATIVE_MAP.put("政治+物理+地理","D");
		XK_NATIVE_MAP.put("化学+物理+技术","5");
		XK_NATIVE_MAP.put("物理+政治+地理","D");
		XK_NATIVE_MAP.put("生物+地理+历史","R");
		XK_NATIVE_MAP.put("化学+生物+历史","G");
		XK_NATIVE_MAP.put("物理+历史+技术","C");
		XK_NATIVE_MAP.put("地理+化学+技术","P");
		XK_NATIVE_MAP.put("政治+生物+化学","H");
		XK_NATIVE_MAP.put("生物+地理+政治","T");
		XK_NATIVE_MAP.put("技术+生物+历史","S");
		XK_NATIVE_MAP.put("地理+技术+生物","V");
		XK_NATIVE_MAP.put("化学+地理+物理","4");
		XK_NATIVE_MAP.put("生物+物理+政治","7");
		XK_NATIVE_MAP.put("生物+政治+历史","Q");
		XK_NATIVE_MAP.put("政治+历史+技术","X");
		XK_NATIVE_MAP.put("文科","W");
		XK_NATIVE_MAP.put("理科","1");
		XK_NATIVE_MAP.put("历史","W");
		XK_NATIVE_MAP.put("物理","1");
	}

	
	public static void main(String args[]) {
		//System.out.print(XKCombineUtils.getXKCodeByCombine("[史, 化, 政]"));
		/**
		 StringBuffer sb = new StringBuffer();
		 Iterator<String> keys = JDBC.HM_PROVINCE.keySet().iterator();
		 while(keys.hasNext()) {
			 String key = keys.next();
			 String value = JDBC.HM_PROVINCE.get(key);
			 
			 sb.append("ALTER TABLE `"+value+"_zdks_score_convert`	ADD COLUMN `KL_CODE` VARCHAR(50) NOT NULL AFTER `KL`;\r\n");
		 }
		 
		 writeTempFile(new File("E:XK_CVT.txt"), sb);
		 **/
		
		
		System.out.println(XKCombineUtils.getXKCodeByCombine("[物, 化, 生]"));
		System.out.println(XKCombineUtils.getXKCodeByCombine("[物, 化, 地]"));
		System.out.println(XKCombineUtils.getXKCodeByCombine("[物, 化, 政]"));
		System.out.println(XKCombineUtils.getXKCodeByCombine("[物, 生, 地]"));
		System.out.println(XKCombineUtils.getXKCodeByCombine("[物, 生, 政]"));
		System.out.println(XKCombineUtils.getXKCodeByCombine("[物, 地, 政]"));
		System.out.println("----");
		System.out.println(XKCombineUtils.getXKCodeByCombine("[史, 地, 政]"));
		System.out.println(XKCombineUtils.getXKCodeByCombine("[史, 地, 化]"));
		System.out.println(XKCombineUtils.getXKCodeByCombine("[史, 地, 生]"));
		
		System.out.println(XKCombineUtils.getXKCodeByCombine("[史, 政, 化]"));
		System.out.println(XKCombineUtils.getXKCodeByCombine("[史, 政, 生]"));
		System.out.println(XKCombineUtils.getXKCodeByCombine("[史, 化, 生]"));
		
		
		
		
		List<String> subjectCombinations = new ArrayList<>();
        
        // 添加单科组合
		subjectCombinations.add("不限");
        subjectCombinations.add("地理");
        subjectCombinations.add("化学");
        subjectCombinations.add("化学和生物");
        subjectCombinations.add("历史");
        subjectCombinations.add("历史和地理");
        subjectCombinations.add("生物");
        subjectCombinations.add("物理");
        subjectCombinations.add("物理和地理");
        subjectCombinations.add("物理和化学");
        subjectCombinations.add("物理和化学和地理");
        subjectCombinations.add("物理和化学和生物");
        subjectCombinations.add("物理和生物");
        subjectCombinations.add("物理和政治");
        subjectCombinations.add("政治");
        subjectCombinations.add("政治和历史");
        subjectCombinations.add("政治和历史和地理");
        
        for(String combinaX : subjectCombinations) {
        	String combina_standare = combinaX.replaceAll("和", "+");
			Iterator<String> its = XK_NATIVE_MAP.keySet().iterator();
			HashSet<String> sb = new HashSet<>();
			while(its.hasNext()) {
				String key = its.next();
				if(combinaX.equals("不限")) {
					sb.add(XK_NATIVE_MAP.get(key));
				}else {
					if(key.contains(combina_standare)) {
						sb.add(XK_NATIVE_MAP.get(key));
					}
				}
			}
			
			String SQL = "UPDATE tszh_jh_2025 x SET x.xk_code = '"+org.apache.commons.lang3.StringUtils.join(sb, "")+"' WHERE x.xk = '综合' and x.zx = '"+combinaX+"';";
			System.out.println(SQL);
        }
        
        
        
		Iterator<String> its = XK_NATIVE_MAP.keySet().iterator();
		HashSet<String> sb = new HashSet<>();
		while(its.hasNext()) {
			String key = its.next();
			if(key.contains("物理") || key.contains("化学") || key.contains("生物")) {
				sb.add(XK_NATIVE_MAP.get(key));
			}
		}
		
		String SQL = "UPDATE tszh_jh_2025 x SET x.xk_code = '"+org.apache.commons.lang3.StringUtils.join(sb, "")+"' WHERE x.xk = '综合' and x.zx = '物理或化学或生物';";
		System.out.println(SQL);
		
	 }

	
	
	
	public static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	
}
