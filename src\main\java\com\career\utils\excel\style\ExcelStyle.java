package com.career.utils.excel.style;

import org.apache.poi.ss.usermodel.*;

/**
 * Excel样式处理类
 * 简化设计，将样式创建和获取逻辑合并到一个类中
 */
public class ExcelStyle {
    private final Workbook workbook;
    
    // 缓存创建的样式以提高性能
    private CellStyle headerStyle;
    private CellStyle subHeaderStyle;
    private CellStyle yearHeaderStyle;
    private CellStyle dataStyle;
    private CellStyle numberStyle;
    private CellStyle decimalStyle;
    private CellStyle fileHeaderStyle;
    
    // 添加偶数行样式
    private CellStyle evenRowDataStyle;
    private CellStyle evenRowNumberStyle;
    private CellStyle evenRowDecimalStyle;
    
    public ExcelStyle(Workbook workbook) {
        this.workbook = workbook;
    }
    
    /**
     * 获取主表头样式
     */
    public CellStyle getHeaderStyle() {
        if (headerStyle == null) {
            headerStyle = createHeaderStyle();
        }
        return headerStyle;
    }
    
    /**
     * 获取子表头样式
     */
    public CellStyle getSubHeaderStyle() {
        if (subHeaderStyle == null) {
            subHeaderStyle = createSubHeaderStyle();
        }
        return subHeaderStyle;
    }
    
    /**
     * 获取年度表头样式
     */
    public CellStyle getYearHeaderStyle() {
        if (yearHeaderStyle == null) {
            yearHeaderStyle = createYearHeaderStyle();
        }
        return yearHeaderStyle;
    }
    
    /**
     * 获取数据单元格样式
     */
    public CellStyle getDataStyle() {
        if (dataStyle == null) {
            dataStyle = createDataStyle();
        }
        return dataStyle;
    }
    
    /**
     * 获取数字格式样式
     */
    public CellStyle getNumberStyle() {
        if (numberStyle == null) {
            numberStyle = createNumberStyle();
        }
        return numberStyle;
    }
    
    /**
     * 获取小数格式样式
     */
    public CellStyle getDecimalStyle() {
        if (decimalStyle == null) {
            decimalStyle = createDecimalStyle();
        }
        return decimalStyle;
    }
    
    /**
     * 获取文件头样式
     */
    public CellStyle getFileHeaderStyle() {
        if (fileHeaderStyle == null) {
            fileHeaderStyle = createFileHeaderStyle();
        }
        return fileHeaderStyle;
    }
    
    /**
     * 获取偶数行数据单元格样式
     */
    public CellStyle getEvenRowDataStyle() {
        if (evenRowDataStyle == null) {
            evenRowDataStyle = createEvenRowDataStyle();
        }
        return evenRowDataStyle;
    }
    
    /**
     * 获取偶数行数字格式样式
     */
    public CellStyle getEvenRowNumberStyle() {
        if (evenRowNumberStyle == null) {
            evenRowNumberStyle = createEvenRowNumberStyle();
        }
        return evenRowNumberStyle;
    }
    
    /**
     * 获取偶数行小数格式样式
     */
    public CellStyle getEvenRowDecimalStyle() {
        if (evenRowDecimalStyle == null) {
            evenRowDecimalStyle = createEvenRowDecimalStyle();
        }
        return evenRowDecimalStyle;
    }
    
    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle() {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 11);
        font.setColor(IndexedColors.WHITE.getIndex());
        style.setFont(font);
        
        // 设置背景色 - 深蓝色 (iPhone主题色)
        style.setFillForegroundColor(IndexedColors.ROYAL_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        // 设置边框
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        
        // 居中对齐
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        // 自动换行
        style.setWrapText(true);
        
        return style;
    }
    
    /**
     * 创建子表头样式
     */
    private CellStyle createSubHeaderStyle() {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 10);
        style.setFont(font);
        
        // 设置背景色 - 淡蓝色
        style.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        // 设置边框
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        
        // 居中对齐
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        return style;
    }
    
    /**
     * 创建年度表头样式
     */
    private CellStyle createYearHeaderStyle() {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 12);
        font.setColor(IndexedColors.WHITE.getIndex());
        style.setFont(font);
        
        // 设置背景色
        style.setFillForegroundColor(IndexedColors.ROYAL_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        // 设置边框
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        
        // 居中对齐
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        return style;
    }
    
    /** 
     * 创建文件头样式 
     */ 
    private CellStyle createFileHeaderStyle() { 
        CellStyle style = workbook.createCellStyle(); 
        Font font = workbook.createFont(); 
        font.setBold(true); 
        font.setFontHeightInPoints((short) 22); // 更大的字体 
        font.setColor(IndexedColors.RED.getIndex()); 
        style.setFont(font);

        // 设置背景色为深灰色（使用相近的预定义颜色）
        style.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        // 设置边框 
        style.setBorderBottom(BorderStyle.THIN); 
        style.setBorderTop(BorderStyle.THIN); 
        style.setBorderRight(BorderStyle.THIN); 
        style.setBorderLeft(BorderStyle.THIN); 
        
        style.setAlignment(HorizontalAlignment.LEFT); 
        style.setVerticalAlignment(VerticalAlignment.CENTER); 
        
        return style; 
    } 
    
    /**
     * 创建数据单元格样式
     */
    private CellStyle createDataStyle() {
        CellStyle style = workbook.createCellStyle();
        
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 10);
        style.setFont(font);
        
        // 设置边框
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        
        // 垂直居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        // 轻微背景色 - 浅灰色
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        return style;
    }
    
    /**
     * 创建数字格式样式
     */
    private CellStyle createNumberStyle() {
        CellStyle style = workbook.createCellStyle();
        
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 10);
        style.setFont(font);
        
        // 设置边框
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        
        // 垂直居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        // 轻微背景色 - 浅灰色
        //style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillForegroundColor(IndexedColors.LEMON_CHIFFON.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        // 设置数字格式
        style.setDataFormat(workbook.createDataFormat().getFormat("#,##0"));
        
        return style;
    }
    
    /**
     * 创建小数格式样式
     */
    private CellStyle createDecimalStyle() {
        CellStyle style = workbook.createCellStyle();
        
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 10);
        style.setFont(font);
        
        // 设置边框
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        
        // 垂直居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        // 轻微背景色 - 浅灰色
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        // 设置小数格式
        style.setDataFormat(workbook.createDataFormat().getFormat("#,##0.00"));
        
        return style;
    }
    
    /**
     * 创建偶数行数据单元格样式
     */
    private CellStyle createEvenRowDataStyle() {
        CellStyle style = workbook.createCellStyle();
        
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 10);
        style.setFont(font);
        
        // 设置边框
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        
        // 垂直居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        // 偶数行使用白色背景
        style.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        return style;
    }
    
    /**
     * 创建偶数行数字格式样式
     */
    private CellStyle createEvenRowNumberStyle() {
        CellStyle style = workbook.createCellStyle();
        
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 10);
        style.setFont(font);
        
        // 设置边框
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        
        // 垂直居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        // 偶数行使用白色背景
        style.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        // 设置数字格式
        style.setDataFormat(workbook.createDataFormat().getFormat("#,##0"));
        
        return style;
    }
    
    /**
     * 创建偶数行小数格式样式
     */
    private CellStyle createEvenRowDecimalStyle() {
        CellStyle style = workbook.createCellStyle();
        
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 10);
        style.setFont(font);
        
        // 设置边框
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        
        // 垂直居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        // 偶数行使用白色背景
        style.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        // 设置小数格式
        style.setDataFormat(workbook.createDataFormat().getFormat("#,##0.00"));
        
        return style;
    }
} 