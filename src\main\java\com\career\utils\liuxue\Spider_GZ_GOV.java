package com.career.utils.liuxue;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import com.career.utils.Tools;

public class Spider_GZ_GOV {

	// 贵州人事考试网抓取网页
	public static StringBuffer rlsbj_cq_gov(String URL, int pageNumber) {
		Map<String, String> headers = new HashMap<>();
		StringBuffer SQL = new StringBuffer();

		System.out.println(URL);

		String resultPageList = HttpSendUtils2.get(URL, headers);
		// System.out.println(resultPageList);
		Document documentList = Jsoup.parse(resultPageList);

		Elements aiticleList = documentList.getElementsByClass("right-list-box");

		Elements items = aiticleList.get(0).getElementsByTag("a");
		Elements spans = aiticleList.get(0).getElementsByTag("span");
		

		for (int i = 0; i < items.size(); i++) {
			Element item = items.get(i);
			Element span = spans.get(i);
			String title = item.text();
			String date = span.text();
			String href = item.attr("href");
	
			
			Tools.println(date);
			
			if (title.indexOf("2024") != -1 && title.indexOf("拟聘人员公示") != -1) {
				try {
					Document detailPage = Jsoup.parse(HttpSendUtils2.get(href, headers));
					Elements ue_table = detailPage.getElementsByClass("ue_table");
	
					Elements tbody = ue_table.get(0).getElementsByTag("tbody");
					Elements tableTR = tbody.get(0).getElementsByTag("tr");
					Elements tableTDTitle = tableTR.get(0).getElementsByTag("td");// 标题
					HashMap<Integer, String> titleMap = new HashMap<>();
	
					int indexYX = -1,indexDW = -1,indexXM = -1, indexXB = -1, indexXH = -1, indexXL = -1,indexZY = -1;
					
					for (int k = 0; k < tableTDTitle.size(); k++) {
						titleMap.put(k, tableTDTitle.get(k).text());
						if(tableTDTitle.get(k).text().indexOf("毕业院校") != -1 || tableTDTitle.get(k).text().indexOf("毕业学校") != -1) {
							indexYX = k;
						}
						if(tableTDTitle.get(k).text().indexOf("拟聘") != -1 || tableTDTitle.get(k).text().indexOf("报考岗位") != -1 || tableTDTitle.get(k).text().indexOf("单位及岗位") != -1 || tableTDTitle.get(k).text().indexOf("招聘单位") != -1 || tableTDTitle.get(k).text().indexOf("招聘岗位") != -1) {
							indexDW = k;
						}
						if(tableTDTitle.get(k).text().indexOf("姓名") != -1) {
							indexXM = k;
						}
						if(tableTDTitle.get(k).text().indexOf("性别") != -1) {
							indexXB = k;
						}
						if(tableTDTitle.get(k).text().indexOf("学历") != -1 || tableTDTitle.get(k).text().indexOf("学历学位及专业") != -1) {
							indexXL = k;
						}

						if(tableTDTitle.get(k).text().indexOf("序") != -1) {
							indexXH = k;
						}
					}
					
					if(indexYX >= 0 && indexDW >= 0) {

					}else {
						System.out.println(href);
						continue;
					}
	
					String groupID = UUID.randomUUID().toString();
					for (int k = 1; k < tableTR.size(); k++) {
						Element tr = tableTR.get(k);
						Elements tableTD = tr.getElementsByTag("td");
						String oneRecord = UUID.randomUUID().toString();
						
						
						String xh = indexXH > 0 ? tableTD.get(indexXH).text() : null;
						String xm = indexXM > 0 ? tableTD.get(indexXM).text() : null;
						String xb = indexXB > 0 ? tableTD.get(indexXB).text() : null;
						String xl = indexXL > 0 ? tableTD.get(indexXL).text() : null;
	
						String yxzy = tableTD.get(indexYX).text();
						String dwgw = tableTD.get(indexDW).text();
						
						SQL.append(
								"insert into career_shiye_gov_data(onrecord_id, group_id, yxzy, dwgw, xl, xm, xb, xh, publish_dt, url_link, title, province) values('"+oneRecord+"','"
										+ groupID + "','" + yxzy + "','" + dwgw + "','"+xl+"','"+xm+"','"+xb+"','"+xh+"','" + date + "','"
										+ href + "','" + title + "','贵州');\r\n");

					}
				}catch(Exception ex) {
					//System.out.println("ERR: "+href);
				}

			}
		}
		return SQL;
	}

	public static void main(String[] args) {
		StringBuffer SQL = new StringBuffer();
		for (int i = 0; i < 2; i++) {
			if (i == 0) {
				SQL.append(rlsbj_cq_gov("https://rst.guizhou.gov.cn/zwgk/zdlyxx/sydwgkzp/index.html",i));
			} else {
				SQL.append(rlsbj_cq_gov("https://rst.guizhou.gov.cn/zwgk/zdlyxx/sydwgkzp/index_"+i+".html",i));
			}
		}

		writeTempFile(new File("F://就业报告//GUIZHOU/PAGE_0101_2.txt"), SQL);

	}
	
	private static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
}
