package com.career.utils;


import java.io.*;
import java.util.Iterator;
import java.util.List;

import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.career.db.JDBC;

public class DealHistoryDataForExcel {

	public static void main(String args[]) {
		try {
			check();
			
			
//			StringBuffer SQL = new StringBuffer();
//			File file = new File("F:\\百度网盘\\高考资料");
//			File[] files = file.listFiles();
//			for(int i=0;i<files.length;i++) {
//				File ele = files[i];
//				String province = ele.getName();
//				if(province.equals("四川") || province.equals("陕西") || province.equals("重庆")) {
//					//continue;
//				}
//				
//				
//				String TABLE_PROFIX = JDBC.HM_PROVINCE_CODE.get(province);
//				//System.out.println("HM_PROVINCE_LATEST_YEAR.put(\""+TABLE_PROFIX.substring(0,2)+"\", \"_2024\");");
//				
//				
//				SQL.append(read("F:\\百度网盘\\高考资料\\"+province+"\\全国高等院校在"+province+"的录取分数线.xlsx",province));
//			}
//			
			//writeTempFile(new File("F:\\百度网盘\\out\\ALL.txt"), SQL);
			
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	public static void check() {
		Iterator<String> list = JDBC.HM_PROVINCE_CODE.values().iterator();
		while(list.hasNext()) {
			String tablePrefix = list.next();
			for(int i = 2024;i<=2024;i++) {
				//System.out.println("\r\n"
				//		+ "select * from " + tablePrefix + "_"+i+" x where x.yxmc like '%（%';");
				
				System.out.println("UPDATE "+tablePrefix+"_"+i+"x x SET x.yxmc_org = yxmc where x.yxmc like '%（%';");
			}
			
			
			
			
		}
	}
	
	
	static StringBuffer read(String fileName, String belongProvince) throws IOException {
	    // 创建一个文件输入流，用于读取指定路径的Excel文件
	    FileInputStream in = new FileInputStream(new File(fileName));
	    StringBuffer SQL = new StringBuffer();
	    String TABLE_PROFIX = JDBC.HM_PROVINCE_CODE.get(belongProvince);
	    
	    //SQL.append("ALTER TABLE IF EXISTS "+TABLE_PROFIX+"_2024x RENAME TO "+TABLE_PROFIX+"_2024x_OLD1109;\r\n");
	    
	    SQL.append("CREATE TABLE IF NOT EXISTS `"+TABLE_PROFIX+"_2024x` (\r\n"
	    		+ "	`nf` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
	    		+ "	`yxdm` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
	    		+ "	`yxmc` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
	    		+ "	`yxmc_org` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
	    		+ "	`yxsf` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
	    		+ "	`yxcs` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
	    		+ "	`pc` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
	    		+ "	`xk` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
	    		+ "	`xk_code` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
	    		+ "	`zx` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
	    		+ "	`zyz` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
	    		+ "	`zdf` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
	    		+ "	`zdfwc` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n"
	    		+ "	`yxbz` VARCHAR(1000) NULL DEFAULT NULL COLLATE 'utf8_general_ci'\r\n"
	    		+ ")\r\n"
	    		+ "COLLATE='utf8_general_ci'\r\n"
	    		+ "ENGINE=InnoDB\r\n"
	    		+ "ROW_FORMAT=DYNAMIC\r\n"
	    		+ ";\r\n"
	    		+ "");
	    
	    // 使用文件输入流创建一个XSSFWorkbook对象，该对象代表整个Excel工作簿
	    XSSFWorkbook excel = new XSSFWorkbook(in);
	    
	    // 从工作簿中获取第一个工作表，索引为0
	    XSSFSheet sheet = excel.getSheetAt(0);
	 
	    // 获取工作表中最后一行的编号
	    int lastRowNum = sheet.getLastRowNum();
	 
	    // 遍历工作表中的所有行，包括空行和有数据的行
	    int count = 0;
	    for(int i = 0; i <= lastRowNum; i++) {
	        // 获取指定编号的行
	        XSSFRow row = sheet.getRow(i);
	 
	        // 如果行不为空，则读取第一个和第二个单元格的数据
	        if(row != null) {
	            // 获取第一个单元格的数据，并转换为字符串
	            String nf = row.getCell(0).toString();
	            if(nf.indexOf("2024") == -1) {
	            	continue;
	            }
	            count++;
	            if(nf.length() > 4) {
	            	nf = nf.substring(0,4);
	            }
	            // 获取第二个单元格的数据，并转换为字符串
	            String yxmc = row.getCell(1).toString();
	            String yxsf = row.getCell(2).toString();
	            String yxcs = row.getCell(3).toString();
	            //String cellValue5 = row.getCell(4).toString();
	            String xk = row.getCell(5).toString();
	            String pc = row.getCell(6).toString();
	            String yxbz = row.getCell(7) == null ? "" :row.getCell(7).toString();
	            String zyz = row.getCell(8) == null ? "" : row.getCell(8).toString();
	            String zx = row.getCell(9) == null ? "" :row.getCell(9).toString();
	            String zdf = row.getCell(10).toString();
	            if(zdf.length() > 3) {
	            	zdf = zdf.substring(0,3);
	            }
	            String yxmc_org = yxmc;
	            String xk_code = null;
	            if(xk.indexOf("物理") != -1) {
	            	xk_code = "14387D";
	            }else{
	            	xk_code = "WLRKQG";
	            }
	            if(yxmc.indexOf("（") != -1) {
	            	if(yxmc.indexOf("华北电力大学") != -1 || yxmc.indexOf("中国地质大学") != -1 || yxmc.indexOf("中国石油大学") != -1 || yxmc.indexOf("中国矿业大") != -1) {
	            		
	            	}else {
	            		if(yxmc.equals("大连理工大学盘锦校区")) {
	            			yxmc_org = "大连理工大学";
	            		}
	            		
	            		if(yxmc.equals("中国人民大学（苏州校区）")) {
	            			yxmc_org = "中国人民大学";
	            		}
	            		
	            		if(yxmc.equals("合肥工业大学宣城校区")) {
	            			yxmc_org = "合肥工业大学";
	            		}
	            		
	            		if(yxmc.indexOf("哈尔滨工业大学（") != -1) {
	            			yxmc_org = "哈尔滨工业大学";
	            		}
	            		
	            		if(yxmc.indexOf("山东大学（") != -1) {
	            			yxmc_org = "山东大学";
	            		}
	            	}
	            }
	            
	            SQL.append("insert into "+TABLE_PROFIX+"_2024x(nf,yxmc,yxmc_org,yxsf,yxcs,xk,xk_code,pc,yxbz,zyz,zx,zdf) values('"+nf+"','"+yxmc+"','"+yxmc_org+"','"+yxsf+"','"+yxcs+"','"+xk+"','"+xk_code+"','"+pc+"','"+yxbz+"','"+zyz+"','"+zx+"','"+zdf+"');\r\n");
	            // 打印单元格的数据
	           
	        }
	    }
	    
	    SQL.append("DELETE FROM zdks_rank_tempuse;\r\n"
	    		+ "insert into zdks_rank_tempuse SELECT * FROM zdks_rank x WHERE x.SF = '"+belongProvince+"' AND x.NF = 2024;\r\n");
	    SQL.append("update "+TABLE_PROFIX+"_2024x x left join zdks_rank_tempuse y on x.zdf = y.score and x.xk_code = y.kl_code set x.zdfwc = y.wc;\r\n");
	 
	    // 关闭文件输入流，释放资源
	    in.close();
	    
	    // 关闭XSSFWorkbook对象，释放资源
	    excel.close();
	    
	    System.out.println(belongProvince + " => " + count);
	    
	    return SQL;
	}
	
	public static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
}
