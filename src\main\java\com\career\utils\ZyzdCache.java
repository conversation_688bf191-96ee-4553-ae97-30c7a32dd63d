package com.career.utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;

import com.career.db.CareerJY;
import com.career.db.CareerVocation;
import com.career.db.InputTag;
import com.career.db.ZyzdBaseMajor;
import com.career.db.ZyzdBaseUniversityAndMajorTag;
import com.career.db.ZyzdBaseUniversityJz;
import com.career.db.ZyzdFormJDBC;
import com.career.db.ZyzdJDBC;
import com.career.db.ZyzdMajorCatg;
import com.career.db.ZyzdProvinceConfig;
import com.career.db.ZyzdUniversityBean;

public class ZyzdCache {
	
	static HashMap<String, ZyzdBaseUniversityAndMajorTag> HM_ZyzdBaseUniversityTag = new LinkedHashMap<>();
	static HashMap<String, ZyzdBaseUniversityAndMajorTag> HM_ZyzdBaseUniversityAndMajorTag = new LinkedHashMap<>();
	
	static HashMap<String, ZyzdProvinceConfig> HM_PROVINCE_CONFIG = new LinkedHashMap<>();
	static HashMap<String, ZyzdUniversityBean> HM_ALL_UNIVERSITIES = new HashMap<>();
	static HashMap<String, ZyzdBaseUniversityJz> HM_ALL_UNIVERSITIES_JZ = new HashMap<>();
	static LinkedList<String> LIST_UNIV_CATG = new LinkedList<>();
	static HashMap<String, String> MM_HLD_ASSESS = new HashMap<>();
	// 专业清学校
	static HashMap<String, String> MM_MAJOR_ADMISSION_RULES = new HashMap<>();


	static HashMap<String, String> JH_PC_TO_MAJOR_CC_CODE_MAP = new HashMap<>(); //zyzd_jhpc_majorcc_map表
	
	
	static HashMap<String, ZyzdBaseMajor> HM_ZYMC_MAP = new HashMap<>();
	static HashMap<String, List<ZyzdBaseMajor>> HM_ZYMC_LIST = new HashMap<>();
	static HashMap<String, List<ZyzdMajorCatg>> HM_ZYMC_ZYL_LIST = new HashMap<>();
	static HashMap<String, ZyzdMajorCatg> HM_ZY_CATG_ONE_MAP = new HashMap<>();
	static List<String> LIST_VOCATION_ONE = new ArrayList<>();
	static List<CareerVocation> LIST_VOCATION = new ArrayList<>();
	
	public static HashMap<String,String> YBEB_YX_ZY_CACHE = new HashMap<>();
	

	public static String WECOM_SES_KEY_SALES_STAFF = "WECOM_SES_KEY_SALES_STAFF";
	
	public static String SES_KEY_FOLLOW_YX = "SES_KEY_FOLLOW_YX";
	public static String SES_KEY_66SX_FOLLOW_YX = "SES_KEY_66SX_FOLLOW_YX";
	public static String SES_LHY_KEY_FOLLOW_YX = "SES_LHY_KEY_FOLLOW_YX";
	public static String SES_LHY_KEY_FOLLOW_YXZYZ = "SES_LHY_KEY_FOLLOW_YXZYZ";
	public static String SES_LHY_KEY_FOLLOW_ZY = "SES_LHY_KEY_FOLLOW_ZY";
	public static String SES_SAAS_CONFIG = "SES_SAAS_CONFIG";
	public static String SES_OVERSEA_FOLLOW_YX = "SES_OVERSEA_FOLLOW_YX";
	public static String SES_KEY_FOLLOW_ZY = "SES_KEY_FOLLOW_ZY";
	public static String SES_KEY_T_BASE_CARD = "SES_CARD_SC";
	public static String SES_KEY_66SX_PORTAL_CARD = "SES_66SX_PORTAL_CARD";
	public static String SES_KEY_LHY_BASE_CARD = "SES_CARD_LHY";
	public static String SES_KEY_AI_BASE_CARD = "SES_KEY_AI_BASE_CARD";
	public static String SES_KEY_RT_BASE_CARD = "SES_KEY_RT_BASE_CARD";
	public static String SES_KEY_ORDER_USE_CARD = "SES_ORDER_USER_LHY";
	public static String SES_KEY_T_CUR_FORM_AI = "SES_KEY_T_CUR_FORM_AI";//设置当前志愿表form_maker_main
	public static String SES_KEY_T_CUR_FORM_MAUAL = "SES_KEY_T_CUR_FORM_MAUAL";//设置当前志愿表form_maker_main,人工填报用这个
	public static String SES_KEY_T_CUR_FORM_FAST_AI = "SES_KEY_T_CUR_FORM_FAST_AI";//设置当前志愿表form_maker_main,人工填报用这个
	public static String SES_KEY_BACKEND_BASE_CARD = "SES_CARD_BACKEND";
	public static String SES_KEY_MANAGEMENT_BASE_CARD = "SES_CARD_MANAGEMENT";
	public static String SES_KEY_IND_FAMILY_ACCT_LOGIN = "SES_KEY_IND_FAMILY_ACCT_LOGIN";
	public static String FAMILY_ACCT_PREFIX_S = "ST"; //学生账号的前缀
	public static String FAMILY_ACCT_PREFIX_F = "F"; //志愿表账号的前缀
	public static String SYS_LOGO = "http://www.lx2b.com/zsdwf/sources/qc_logo.png";
	
	
	// 2026年最新,点击调整后，当前的FORM_MAIN放入SESSION中
	public static String SES_KEY_LHY_2026_SELECTED_FORM_MAIN = "SES_KEY_LHY_T_SELECTED_FORM_MAIN";
	
	
	public static String ZY_AND_YX;

	public static int ZYZD_DOWNLOAD_D_TYPE_AI_SEARCH_RESULT_DOWNLOAD = 1;
	public static int ZYZD_DOWNLOAD_D_TYPE_AI_MYFORM_DOWNLOAD = 2;
	
	
	public static String SES_LHY_TB_ADJUST_CLICKED_FORM_MAIN_INFO = "SES_LHY_TB_ADJUST_CLICKED_FORM_MAIN_INFO"; //点击某个志愿后，进入调整志愿页面，把MAIN表存入SESSION
	public static String SES_LHY_TB_ADJUST_CLICKED_FORM_SEQ_YX_NO = "SES_LHY_TB_ADJUST_CLICKED_FORM_SEQ_YX_NO";//调整志愿，点击某个志愿的专业组后，把已选的专业组下的专业存入SESSION
	public static String SES_LHY_TB_ACTUALLY_CHANGED_FORM_SEQ_YX_NO = "SES_LHY_TB_ACTUALLY_CHANGED_FORM_SEQ_YX_NO";// 把每一次实际调整的志愿序号保存到Session中
	public static String SES_LHY_TB_RETURN_FROM_FORMMARK_CHANGED = "SES_LHY_TB_RETURN_FROM_FORMMARK_CHANGED";// 把是否来自修改志愿的状态保存到Session中
	
	public static String DB_INDICATOR_LQPC = "普通类";
	public static String DB_INDICATOR_PC_BK = "本科";
	public static String DB_INDICATOR_PC_ZK = "专科";
	public static String DB_INDICATOR_PC_ZJBK = "职教本科";
	public static String DB_INDICATOR_ZK_CC_NAME = "专科";
	public static String DB_INDICATOR_FROM_WHERE_AI = "AI";
	public static String DB_INDICATOR_FROM_WHERE_LHY = "LHY";
	public static String DB_INDICATOR_FROM_WHERE_CARD = "CARD";
	public static int SYS_INDICATOR_FORM_TYPE_AI = 1;
	public static int SYS_INDICATOR_FORM_TYPE_MAUAL = 2;
	public static int SYS_INDICATOR_FORM_TYPE_RT_CHECKER = 1;
	public static int SYS_DOWNLOAD_TYPE_FORM = 1;
	public static int SYS_DOWNLOAD_TYPE_QC_AI_FORM = 21;
	public static int SYS_DOWNLOAD_TYPE_QC_MAUAL_FORM = 22;
	public static int SYS_INDICATOR_CP_TYPE_XXL = 1;
	

	public static String DB_INDICATOR_PROV_LIMIT_ONLY = "ONLY";
	public static String DB_INDICATOR_PROV_LIMIT_ALL = "ALL";

	public static String DB_INDICATOR_TSPC_PC_CODE_ZXJH = "专项计划";
	public static String DB_INDICATOR_TSPC_PC_CODE_GSYS = "公师优师";
	public static String DB_INDICATOR_TSPC_PC_CODE_DXJS = "定向军士";
	public static String DB_INDICATOR_TSPC_PC_CODE_GAJX = "公安警校";
	
	//功能：TB,SD,XZ,ZD,JZ  (填报TB,审单SD,报告下载XZ,诊断ZD,家长账号JZ,SH审核一对一志愿)
	public static String LHY_FUNCTION_TB = "TB";
	public static String LHY_FUNCTION_XZ = "XZ";
	public static String LHY_FUNCTION_ZD = "ZD";
	public static String LHY_FUNCTION_JZ = "JZ";
	public static String LHY_FUNCTION_SD = "SD";
	public static String LHY_FUNCTION_SH = "ZJSH"; //专家审核（一对一系统审核组内专家的订单）
	public static String LHY_FUNCTION_ZJGL = "ZJGL"; //专家管理
	public static String LHY_FUNCTION_WECOM_WECOM = "WECOM";
	public static String LHY_FUNCTION_WECOM_SALES = "SALES";
	public static String LHY_FUNCTION_WECOM_SERVICE = "SERVICE";
	public static String LHY_FUNCTION_SYS_CONFIG = "SYSCONFIG";
	public static String LHY_FUNCTION_ZYBSH = "ZYBSH"; //志愿表审核(用户志愿表)
	public static String LHY_FUNCTION_JZXLY = "JZXLY"; //家长训练营
	
	public static List<String> LHY_FUNCTION_LIST = new ArrayList<>();
	
	
	public static String VERSION_SYSTEM = "v4.b24.1";
	public static String VERSION_DATA_YGQ = "v4.1028";
	public static String VERSION_DATA_KG = "v4.1120";
	public static String[] VIEW_HY_LIST = {"电气行业","铁路行业","石油/石化","烟草行业","通信/运营商","能源行业","金融/财税","银行系统","矿山安全","粮食和物资","海事局","水利部","海关系统"};
	static HashMap<String, List<CareerJY>> HM_VIEW_HY = new HashMap<>();
	
	//public static String ZY_AND_YX = "浙江,山东,河北,辽宁,重庆,贵州,青海"; //专业+院校的省
	
	public static String  CP_NAME_HOLLAND = "HOLLAND";
	public static String  CP_NAME_MBTI = "MBTI";
	public static String  CP_NAME_XXL = "XXL";
	public static String  CP_NAME_XXL_CP_M_ID = "101";
	public static int  CP_NAME_XXL_M_TYPE = 1;
	
	public static List<InputTag> yxtagList = new ArrayList<>();
	public static List<InputTag> bxxzList = new ArrayList<>();
	public static List<InputTag> specialList = new ArrayList<>();
	public static List<InputTag> znzysList = new ArrayList<>();
	
	static {
		LHY_FUNCTION_LIST.add(LHY_FUNCTION_TB);
		LHY_FUNCTION_LIST.add(LHY_FUNCTION_ZYBSH);
		LHY_FUNCTION_LIST.add(LHY_FUNCTION_JZXLY);
		LHY_FUNCTION_LIST.add(LHY_FUNCTION_SYS_CONFIG);
		LHY_FUNCTION_LIST.add(LHY_FUNCTION_WECOM_SERVICE);
		LHY_FUNCTION_LIST.add(LHY_FUNCTION_WECOM_SALES);
		LHY_FUNCTION_LIST.add(LHY_FUNCTION_WECOM_WECOM);
		LHY_FUNCTION_LIST.add(LHY_FUNCTION_ZJGL);
		LHY_FUNCTION_LIST.add(LHY_FUNCTION_SH);
		
		
		
		JH_PC_TO_MAJOR_CC_CODE_MAP.put("职教本科", "职教本科");
		JH_PC_TO_MAJOR_CC_CODE_MAP.put("本科", "本科");
		JH_PC_TO_MAJOR_CC_CODE_MAP.put("专科", "专科");
		JH_PC_TO_MAJOR_CC_CODE_MAP.put("职教本", "职教本科");
		JH_PC_TO_MAJOR_CC_CODE_MAP.put("专科（高职）", "专科");

		
		yxtagList.add(new InputTag("985", "985"));
		yxtagList.add(new InputTag("211", "211"));
		yxtagList.add(new InputTag("国重点", "国重点"));
		yxtagList.add(new InputTag("省重点", "省重点"));
		yxtagList.add(new InputTag("保研", "保研"));
		yxtagList.add(new InputTag("101计划", "101计划"));
		yxtagList.add(new InputTag("双万计划", "双万计划"));
		yxtagList.add(new InputTag("中央部属", "中央部属"));
		yxtagList.add(new InputTag("双一流", "双一流"));
		yxtagList.add(new InputTag("部省合建", "部省合建"));
		yxtagList.add(new InputTag("双高计划A档", "双高计划A档"));
		yxtagList.add(new InputTag("双高计划B档", "双高计划B档"));
		yxtagList.add(new InputTag("双高计划C档", "双高计划C档"));
		yxtagList.add(new InputTag("国示范高职", "国示范高职"));
		yxtagList.add(new InputTag("高水平学校建设单位(A档)", "高水平学校建设单位(A档)"));
		yxtagList.add(new InputTag("高水平学校建设单位(B档)", "高水平学校建设单位(B档)"));
		yxtagList.add(new InputTag("高水平学校建设单位(C档)", "高水平学校建设单位(C档)"));
		yxtagList.add(new InputTag("高水平专业群建设单位(A档)", "高水平专业群建设单位(A档)"));
		yxtagList.add(new InputTag("高水平专业群建设单位(B档)", "高水平专业群建设单位(B档)"));
		yxtagList.add(new InputTag("高水平专业群建设单位(C档)", "高水平专业群建设单位(C档)"));
		yxtagList.add(new InputTag("国骨干高职", "国骨干高职"));
		yxtagList.add(new InputTag("优质专科高职", "优质专科高职"));

		
		bxxzList.add(new InputTag("公办", "公办"));
		bxxzList.add(new InputTag("民办", "民办"));
		bxxzList.add(new InputTag("中外合作办学", "中外合作"));

		
		specialList.add(new InputTag("9999", "不限"));
		specialList.add(new InputTag("1", "1个"));
		specialList.add(new InputTag("2", "2个"));
		specialList.add(new InputTag("3", "3个"));
		specialList.add(new InputTag("4", "4个"));
		specialList.add(new InputTag("5", "5个"));
		specialList.add(new InputTag("6", "6个"));
		specialList.add(new InputTag("7", "7个"));
		specialList.add(new InputTag("8", "8个"));
		specialList.add(new InputTag("9999", "大于8个"));

		znzysList.add(new InputTag("9999", "不限"));
		znzysList.add(new InputTag("5", "小于等于5个"));
		znzysList.add(new InputTag("10", "小于等于10个"));
		znzysList.add(new InputTag("15", "小于等于15个"));
		znzysList.add(new InputTag("20", "小于等于20个"));
		znzysList.add(new InputTag("9999", "20个以上"));
		
		
			
		MM_HLD_ASSESS.put("RIA", "牙科技术员、陶工、 建筑设计员、模型工、细木工、制作链条人员");
		MM_HLD_ASSESS.put("RIS", "厨师、林务员、跳水员、潜水员、染色员、电器修理、眼镜制作、电工、纺织机器装配工、服务员、装玻璃工人、发电厂工人、焊接工");
		MM_HLD_ASSESS.put("RIE", "建筑和桥梁工程、环境工程、航空工程、公路工程、电力工程、信号工程、电话工程、一般机械工程、自动工程、矿业工程、海洋工程、交通工程技术人员、制图员、家政经济人员、计量员、农民、农场工人、农业机械操作、清洁工、无线电修理、汽车修理、手表修理、管工、线路装配工、工具仓库管理员");
		MM_HLD_ASSESS.put("RIC", "船上工作人员、接待员、杂志保管员、牙医助手、制帽工、磨坊工、石匠、机器制造、机车(火车头)制造、农业机器装配、汽车装配工、缝纫机装配工、钟表装配和检验、电动器具装配、鞋匠、锁匠、货物检验员、电梯机修工、装配工、托儿所所长、钢琴调音员、印刷工、建筑 钢铁工作、卡车司机");
		MM_HLD_ASSESS.put("RAI", "手工雕刻、玻璃雕刻、制作模型人员、家具木工、制作皮革品、手工绣花、手工钩针纺织、排字工作、印刷工作、图画雕刻、装订工");
		MM_HLD_ASSESS.put("RSE", "消防员、交通巡警、警察、门卫、理发师、房间清洁工、屠夫、锻工、开凿工人、管道安装工、出租汽车驾驶员、货物搬运工、送报员、勘探员、 娱乐场所的服务员、起卸机操作工、灭害虫者、电梯操作工、厨房助手");
		MM_HLD_ASSESS.put("RSI", "纺织工、编织工、农业学校教师、某些职业课程教师(诸如艺术、商业、技术、工艺课程)、雨衣上胶工");
		MM_HLD_ASSESS.put("REC", "抄水表员、保姆、实验室动物饲养员、动物管理员");
		MM_HLD_ASSESS.put("REI", "轮船船长、航海领航员、大副、试管实验员");
		MM_HLD_ASSESS.put("RES", "旅馆服务员、家畜饲养员、渔民、渔网修补工、水手长、收割机操作工、搬运行李工人、公园服务员、救 生员、登山导游、火车工程技术员、建筑工作、铺轨工人");
		MM_HLD_ASSESS.put("RCI", "测量员、勘测员、仪表操作者、农业工程技术、化学工程技师、民用工程技师、石油工程技师、资料室管理员、探矿工、煅烧工、烧窖工、矿工、炮手、保养工、磨床工、取样工、样品检验员、纺纱工、漂洗工、电焊工、锯木工、刨床工、制帽工、手工缝纫工、油漆工、 染色工、按摩工、木匠、农民建筑工作、电影放映员、勘测员助手");
		MM_HLD_ASSESS.put("RCS", "公共汽车驾驶员、一等水手、游泳池服务员、裁缝、建筑工作、石匠、烟囱修建工、混凝土工、电话修理工、爆炸手、 邮递员、矿工、裱糊工人、纺纱工");
		MM_HLD_ASSESS.put("RCE", "打井工、吊车驾驶员、农场工人、邮件分类员、铲车司机、拖拉机司机");
		MM_HLD_ASSESS.put("IAS", "普通经济学家、农场经济学家、财政经济学家、国际贸易经济学家、实验心理学家、工程心理学家、心理学家、哲学家、内科医生、数学家");
		MM_HLD_ASSESS.put("IAR", "人类学家、天文学家、化学家、物理学家、医学病理、动物标本剥制者、化石修复者、艺术品管理者");
		MM_HLD_ASSESS.put("ISE", "营养学家、饮食顾问、火灾检查员、邮政服务检查员");
		MM_HLD_ASSESS.put("ISC", "侦察员、电视播音室修理员、电视修理服务员、验尸室人员、编目录者、医学实验定技师、调查研究者");
		MM_HLD_ASSESS.put("ISR", "水生生物学者，昆虫学者、微生物学家、配镜师、矫正视力者、细菌学家、牙科医生、骨科医生");
		MM_HLD_ASSESS.put("ISA", "实验心理学家、普通心理学家、发展心理学家、教育心理学家、社会心理学家、临床心理学家、目标学家、皮肤病学家、精神病学家、妇产科医师、眼科医生、五官科医生、医学实验室技术专家、民航医务人员、护士");
		MM_HLD_ASSESS.put("IES", "细菌学家、生理学家、化学专家、地质专家、地理物理学专家、纺织技术专家、医院药剂师、工业药剂师、药房营业员");
		MM_HLD_ASSESS.put("IEC", "档案保管员、保险统计员");
		MM_HLD_ASSESS.put("ICR", "质量检验技术员、地质学技师、工程师、法官、图书馆技术辅导员、计算机操作员、医院听诊员、家禽检查员");
		MM_HLD_ASSESS.put("IRA", "地理学家、地质学家、声学物理学家、矿物学家、古生物学家、石油学家、地震学家、声学物理学家、气象学家、原子和分子物理学家、电学和磁学物理学家、设计审核员、人口统计学家、数学统计学家、外科医生、城市规划家、气象员");
		MM_HLD_ASSESS.put("IRS", "流体物理学家、物理海洋学家、等离子体物理学家、农业科学家、动物学家、食品科学家、园艺学家、植物学家、细菌学家、解剖学家、动物病理学家、作物病理学家、药物学家、生物化学家、生物物理学家、细胞生物学家、临床化学家、遗传学家、 分子生物学家、质量控制工程师、地理学家、兽医、放射性治疗技师");
		MM_HLD_ASSESS.put("IRE", "化验员、化学工程师、纺织工程师、食品技师、渔业技术专家、材料和测试工程师、 电气工程师、土木工程师、航空工程师、行政官员、冶金专家、原子核工程师、陶瓷工程师、地质工程师、电力工程量、口腔科医生、牙科医生");
		MM_HLD_ASSESS.put("IRC", "飞机领航员、飞行员、物理实验室技师、文献检查员、农业技术专家、生物技师、 动植物技术专家、油管检查员、工商业规划者、矿藏安全检查员、纺织品检验员、照相机修理者、工程技术员、编计算程序者、工具设计者、仪器维修工");
		MM_HLD_ASSESS.put("CRI", "簿记员、会计、记时员、铸造机操作工、打字员、按键操作工、复印机操作工");
		MM_HLD_ASSESS.put("CRS", "仓库保管员、档案管理员、缝纫工、讲述员、收款人");
		MM_HLD_ASSESS.put("CRE", "标价员、实验室工作者、广告管理员、自动打字机操作员、电动机装配工、缝纫机操作工");
		MM_HLD_ASSESS.put("CIS", "记账员、顾客服务员、报刊发行员、土地测量员、保险公司职员、会计师、估价员、邮政检查员、外贸检查员");
		MM_HLD_ASSESS.put("CIE", "打字员、统计员、支票记录员、订货员、校对员、办公室工作人员");
		MM_HLD_ASSESS.put("CIR", "校对员、工程职员、海底电报员、检修计划员、发扳员");
		MM_HLD_ASSESS.put("CSE", "接待员、通讯员、电话接线员、卖票员、旅馆服务员、私人职员、商学教师、旅游办事员");
		MM_HLD_ASSESS.put("CSR", "运货代理商、铁路职员、交通检查员、办公室通信员、薄记员、出纳员、银行财务职员");
		MM_HLD_ASSESS.put("CSA", "秘书、图书管理员、办公室办事员");
		MM_HLD_ASSESS.put("CER", "邮递员、数据处理员、办公室办事员");
		MM_HLD_ASSESS.put("CEI", "推销员、经济分析家");
		MM_HLD_ASSESS.put("CES", "银行会计、记账员、法人秘书、速记员、法院报告人");
		MM_HLD_ASSESS.put("ECI", "银行行长、审计员、信用管理员、地产管理员、商业管理员");
		MM_HLD_ASSESS.put("ECS", "信用办事员、保险人员、各类进货员、海关服务经理、售货员，购买员、会计");
		MM_HLD_ASSESS.put("ERI", "建筑物管理员、工业工程师、护士长、农场管理员、农业经营管理人员");
		MM_HLD_ASSESS.put("ERS", "仓库管理员、房屋管理员、货栈监督管理员");
		MM_HLD_ASSESS.put("ERC", "邮政局长、渔船船长、机械操作领班、木工领班、瓦工领班、驾驶员领班");
		MM_HLD_ASSESS.put("EIR", "科学、技术和有关周期出版物的管理员");
		MM_HLD_ASSESS.put("EIC", "专利代理人、鉴定人、运输服务检查员、安全检查员、废品收购人员");
		MM_HLD_ASSESS.put("EIS", "警官、侦察员、交通检验员、安全咨询员、合同管理者、商人");
		MM_HLD_ASSESS.put("EAS", "法官、律师、公证人");
		MM_HLD_ASSESS.put("EAR", "展览室管理员、舞台管理员、播音员、训兽员");
		MM_HLD_ASSESS.put("ESC", "理发师、裁判员、政府行政管理员、财政管理员、工程管理员、售货员、职业病防治、商业经理、办公室主任、人事负责人、调度员");
		MM_HLD_ASSESS.put("ESR", "家具售货员、书店售货员、公共汽车的驾驶员、日用品售货员、护士长、自然科学和工程的行政领导");
		MM_HLD_ASSESS.put("ESI", "博物馆管理员、图书馆管理员、古迹管理员、饮食业经理、地区安全服务管理员、技术服务咨询者、超级市场管理员、零售商品店店员、批发商、出租汽车服务站调度");
		MM_HLD_ASSESS.put("ESA", "博物馆馆长、报刊管理员、音乐器材售货员、广告商售画营业员、导游、（轮船或班机上的）事务长、飞机上的服务员、船员、法官、律师");
		MM_HLD_ASSESS.put("ASE", "戏剧导演、舞蹈教师、广告撰稿人，报刊、专栏作者、记者、演员、英语翻译");
		MM_HLD_ASSESS.put("ASI", "音乐教师、乐器教师、美术教师、管弦乐指挥，合唱队指挥、歌星、演奏家、哲学家、作家、广告经理、时装模特");
		MM_HLD_ASSESS.put("AER", "新闻摄影师、电视摄影师、艺术指导、录音指导、丑角演员、魔术师、木偶戏演员、骑士、跳水员");
		MM_HLD_ASSESS.put("AEI", "音乐指挥、舞台指导、电影导演");
		MM_HLD_ASSESS.put("AES", "流行歌手、舞蹈演员、电影导演、广播节目主持人、舞蹈教师、口技表演者、喜剧演员、模特");
		MM_HLD_ASSESS.put("AIS", "画家、剧作家、编辑、评论家、时装艺术大师、新闻摄影师、男演员、文学作者");
		MM_HLD_ASSESS.put("AIE", "花匠、皮衣设计师、工业产品设计师、剪影艺术家、复制雕刻品大师");
		MM_HLD_ASSESS.put("AIR", "建筑师、画家、摄影师、绘图员、雕刻家、环境美化工、包装设计师、绣花工、陶器设计师、漫画工");
		MM_HLD_ASSESS.put("SEC", "社会活动家、退伍军人服务官员、工商会事务代表、教育咨询者、宿舍管理员、旅馆经理、饮食服务管理员");
		MM_HLD_ASSESS.put("SER", "体育教练、游泳指导");
		MM_HLD_ASSESS.put("SEI", "大学校长、学院院长、医院行政管理员、历史学家、家政经济学家、职业学校教师、资料员");
		MM_HLD_ASSESS.put("SEA", "娱乐活动管理员、国外服务办事员、社会服务助理、一般咨询者、宗教教育工作者");
		MM_HLD_ASSESS.put("SCE", "部长助理、福利机构职员、生产协调人、环境卫生管理人员、戏院经理、餐馆经理、售票员");
		MM_HLD_ASSESS.put("SRI", "外科医师助手、医院服务员");
		MM_HLD_ASSESS.put("SRE", "体育教师、职业病治疗者、体育教练、专业运动员、房管员、儿童家庭教师、警察、引座员、传达员、保姆");
		MM_HLD_ASSESS.put("SRC", "护理员、护理助理、医院勤杂工、理发师、学校儿童服务人员");
		MM_HLD_ASSESS.put("SIA", "社会学家、心理咨询者、学校心理学家、政治科学家、大学或学院的系主任、大学或学院的教育学教师、大学农业教师、大学法律教师、大学工程和建筑课程的教师、 大学数学、医学、物理教师大学社会科学、生命科学教师、 研究生助教、成人教育教师");
		MM_HLD_ASSESS.put("SIE", "营养学家、饮食学家、海关检查员、安全检查员、税务稽查员、校长");
		MM_HLD_ASSESS.put("SIC", "描图员、兽医助手、诊所助理、体检检查员、娱乐指导者、监督缓刑犯的工作者、咨询人员、社会科学教师");
		MM_HLD_ASSESS.put("SIR", "理疗员、救护队工作人员、手足病医生、职业病治疗助手");
		
		
		LIST_UNIV_CATG.add("综合类");
		LIST_UNIV_CATG.add("理工类");
		LIST_UNIV_CATG.add("师范类");
		LIST_UNIV_CATG.add("财经类"); 
		LIST_UNIV_CATG.add("医药类");
		LIST_UNIV_CATG.add("体育类");
		LIST_UNIV_CATG.add("政法类");
		LIST_UNIV_CATG.add("农林类");
		LIST_UNIV_CATG.add("语言类");
		LIST_UNIV_CATG.add("艺术类");
		LIST_UNIV_CATG.add("军事类");
		LIST_UNIV_CATG.add("民族类");
                 		
		
		HM_ALL_UNIVERSITIES = getAllUniversitiesAsMap();
		HM_ALL_UNIVERSITIES_JZ = getAllUniversitiesJzAsMap();
		YBEB_YX_ZY_CACHE = getAllUniversitiesYbebAsMap();
		
		MM_MAJOR_ADMISSION_RULES.put("四川_乐山师范", "Y");
		MM_MAJOR_ADMISSION_RULES.put("四川_阿坝师范", "Y");
		MM_MAJOR_ADMISSION_RULES.put("四川_西昌学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("四川_成都锦城学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("四川_四川大学锦江学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("四川_成都文理学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("四川_四川工商学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("四川_成都师范", "Y");
		MM_MAJOR_ADMISSION_RULES.put("四川_吉利学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("四川_成都银杏酒店管理学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("四川_四川工业科技学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("四川_西南财经大学天府学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("四川_绵阳城市学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("安徽_安徽艺术学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("安徽_安徽三联学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("安徽_安徽新华学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("北京_北京城市学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("福建_福建警察学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("福建_仰恩大学", "Y");
		MM_MAJOR_ADMISSION_RULES.put("福建_福州理工学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("广东_广州软件学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("广东_广州理工学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("广东_湛江科技学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("广东_广州商学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("广东_广州华立学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("广东_东莞城市学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("广东_广东白云学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("广东_广东培正学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("广东_广东东软学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("广东_广东华商学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("广东_广州新华学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("广东_广东科技学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("广东_华南农业大学珠江学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("广东_广州城市理工学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("广西_广西农业职业技术大学", "Y");
		MM_MAJOR_ADMISSION_RULES.put("广西_北部湾大学", "Y");
		MM_MAJOR_ADMISSION_RULES.put("广西_桂林学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("广西_南宁学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("广西_广西外国语学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("贵州_贵州医科大学神奇民族医药学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("贵州_贵州商学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("贵州_贵州师范学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("贵州_黔南民族师范学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("河北_华北科技学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("河北_唐山师范学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("河北_唐山学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("河北_保定学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("河北_邢台学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("河北_河北地质大学", "Y");
		MM_MAJOR_ADMISSION_RULES.put("河北_河北金融学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("河北_衡水学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("河北_河北工程大学科信学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("河北_燕京理工学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("河南_华北水利水电大学", "Y");
		MM_MAJOR_ADMISSION_RULES.put("河南_黄淮学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("河南_信阳师范大学", "Y");
		MM_MAJOR_ADMISSION_RULES.put("河南_安阳师范学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("河南_南阳师范学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("河南_河南许昌学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("河南_豫北医学院（新乡医学院三全学院）", "Y");
		MM_MAJOR_ADMISSION_RULES.put("河南_郑州科技学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("河南_郑州升达经贸管理学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("河南_河南开封科技传媒学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("河南_郑州商学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("河南_郑州财经学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("河南_郑州工程技术学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("湖北_中国地质大学（武汉）", "Y");
		MM_MAJOR_ADMISSION_RULES.put("湖南_湖南工业大学科技学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("吉林_吉林农业科技学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("吉林_长春建筑学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("青海_青海师范大学", "Y");
		MM_MAJOR_ADMISSION_RULES.put("青海_青海大学昆仑学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("山东_青岛科技大学", "Y");
		MM_MAJOR_ADMISSION_RULES.put("山东_烟台大学", "Y");
		MM_MAJOR_ADMISSION_RULES.put("山东_山东航空学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("山东_山东德州学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("山东_济宁学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("上海_上海建桥学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("上海_上海师范大学天华学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("新疆_新疆科技学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("云南_文山学院", "Y");
		MM_MAJOR_ADMISSION_RULES.put("重庆_重庆电子科技职业大学", "Y");
	}
	
	public static String getMajorccCodeByJhpc(String pc_code) {
		if(JH_PC_TO_MAJOR_CC_CODE_MAP.size() == 0) {
			//getAllProvinceConfig();
		}
		return Tools.trim(JH_PC_TO_MAJOR_CC_CODE_MAP.get(pc_code));
	}
	
	static HashMap<String, ZyzdBaseUniversityJz> getAllUniversitiesJzAsMap() {
		return new ZyzdJDBC().getAllUniversitiesJz();
	}
	
	public static ZyzdBaseUniversityAndMajorTag getZyzdBaseUniversityTag(String yxmc_org) {
		if(HM_ZyzdBaseUniversityTag.size() <= 0) {
			List<ZyzdBaseUniversityAndMajorTag> tags = new ZyzdFormJDBC().getZyzdBaseUniversityAndMajorTagAll();
	        for (ZyzdBaseUniversityAndMajorTag tag : tags) {
	        	HM_ZyzdBaseUniversityTag.put(tag.getYxmc_org(), tag);
	        }
		}
		return HM_ZyzdBaseUniversityTag.get(yxmc_org);
	}
	
	public static ZyzdBaseUniversityAndMajorTag getZyzdBaseUniversityAndMajorTag(String yxmc_org, String zymc_org) {
		if(HM_ZyzdBaseUniversityAndMajorTag.size() <= 0) {
			List<ZyzdBaseUniversityAndMajorTag> tags = new ZyzdFormJDBC().getZyzdBaseUniversityAndMajorTagAll();
	        for (ZyzdBaseUniversityAndMajorTag tag : tags) {
	        	HM_ZyzdBaseUniversityAndMajorTag.put(tag.getYxmc_org() + "_" + tag.getZymc_org(), tag);
	        }
		}
		return HM_ZyzdBaseUniversityAndMajorTag.get(yxmc_org + "_" + zymc_org);
	}
	
	
	public static HashMap<String, String> getAllUniversitiesYbebAsMap() {
		return new ZyzdJDBC().getAllYbEbCache();
	}
	
	static HashMap<String, ZyzdUniversityBean> getAllUniversitiesAsMap() {
		return new ZyzdJDBC().getAllUniversities();
	}
	
	public static void reloadZyzdProvinceConfig() {
		HM_PROVINCE_CONFIG.clear();
		getAllProvinceConfig();
	}
	
	public static ZyzdProvinceConfig getProvinceConfig(String pcode) {
		if(HM_PROVINCE_CONFIG.size() == 0) {
			getAllProvinceConfig();
		}
		return HM_PROVINCE_CONFIG.get(pcode);
	}
	
	public static List<ZyzdProvinceConfig> getAllProvinceConfig() {
		if(HM_PROVINCE_CONFIG.size() == 0) {
			synchronized(ZyzdCache.class) {
				List<ZyzdProvinceConfig> configList = new ZyzdJDBC().getAllProvinceConfig();
				for(ZyzdProvinceConfig config : configList) {
					HM_PROVINCE_CONFIG.put(config.getP_code(), config);
				}
			}
		}
		Iterator<ZyzdProvinceConfig> it = HM_PROVINCE_CONFIG.values().iterator();
		List<ZyzdProvinceConfig> list = new ArrayList<>();
		while(it.hasNext()) {
			ZyzdProvinceConfig element = it.next();
			list.add(element);
		}
		return list;
	}
	
	
	public static ZyzdProvince getUserCardProvinceName(String provinceName) {
		List<ZyzdProvinceConfig> tempList = getAllProvinceConfig();
		for(ZyzdProvinceConfig config : tempList) {
			if(config.getP_name().equals(provinceName)) {
				 return new ZyzdProvince(config.getP_code(), config.getP_table_code(), config.getP_name(), config.getLatest_year_yx(), config.getLatest_year_zy());
			}
		}
		return null;
	}
	
	public static List<ZyzdProvince> getAllProvince() {
		List<ZyzdProvince> list = new ArrayList<>();
		List<ZyzdProvinceConfig> tempList = getAllProvinceConfig();
		for(ZyzdProvinceConfig config : tempList) {
			ZyzdProvince bean = new ZyzdProvince(config.getP_code(), config.getP_table_code(), config.getP_name(), config.getLatest_year_yx(), config.getLatest_year_zy());
			list.add(bean);
		}
		return list;
	}
	
	
	private static HashMap<String,String> duplicate_control = new HashMap<>();
	public static HashMap<String, List<ZyzdBaseMajor>> getAllMajorMap2() {
		if(HM_ZYMC_LIST.size() == 0) {
			synchronized(ZyzdCache.class) {
				List<ZyzdBaseMajor> zymcList = new ZyzdJDBC().getZymlThree();
				for(int i=0;i<zymcList.size();i++){ 
	            	ZyzdBaseMajor marjorBean = zymcList.get(i);
	            	String ZYL_KEY = marjorBean.getM_cc_code() + marjorBean.getM_catg_two();
	            	
	            	String key_zymc = ZYL_KEY + marjorBean.getM_zymc();
	            	if(duplicate_control.containsKey(key_zymc)) {
	            		continue;
	            	}
	            	duplicate_control.put(key_zymc, key_zymc);
	            	
	            	if(HM_ZYMC_LIST.containsKey(ZYL_KEY)){
	            		List<ZyzdBaseMajor> majorBeanListTemp = HM_ZYMC_LIST.get(ZYL_KEY);
	            		majorBeanListTemp.add(marjorBean);
	            	}else{
	            		List<ZyzdBaseMajor> majorBeanListTemp = new ArrayList<>();
	            		majorBeanListTemp.add(marjorBean);
	            		HM_ZYMC_LIST.put(ZYL_KEY, majorBeanListTemp);
	            	}
	            }
			}
		}
		return HM_ZYMC_LIST;
	}
	
	public static HashMap<String, List<ZyzdBaseMajor>> getAllMajorMap() {
		if(HM_ZYMC_LIST.size() == 0) {
			synchronized(ZyzdCache.class) {
				List<ZyzdBaseMajor> zymcList = new ZyzdJDBC().getZymlThree();
				for(int i=0;i<zymcList.size();i++){ 
	            	ZyzdBaseMajor marjorBean = zymcList.get(i);
	            	String ZYL_KEY = marjorBean.getM_cc_code() + marjorBean.getM_catg_one() + marjorBean.getM_catg_two();
	            	
	            	String key_zymc = ZYL_KEY + marjorBean.getM_zymc();
	            	if(duplicate_control.containsKey(key_zymc)) {
	            		continue;
	            	}
	            	duplicate_control.put(key_zymc, key_zymc);
	            	
	            	if(HM_ZYMC_LIST.containsKey(ZYL_KEY)){
	            		List<ZyzdBaseMajor> majorBeanListTemp = HM_ZYMC_LIST.get(ZYL_KEY);
	            		majorBeanListTemp.add(marjorBean);
	            	}else{
	            		List<ZyzdBaseMajor> majorBeanListTemp = new ArrayList<>();
	            		majorBeanListTemp.add(marjorBean);
	            		HM_ZYMC_LIST.put(ZYL_KEY, majorBeanListTemp);
	            	}
	            }
			}
		}
		return HM_ZYMC_LIST;
	}
	
	public static ZyzdBaseMajor getZymlByZymc(String cc_code, String zymc) {
		if(HM_ZYMC_MAP.size() == 0) {
			synchronized(ZyzdCache.class) {
				List<ZyzdBaseMajor> zymcList = new ZyzdJDBC().getZymlThree();
				for(int i=0;i<zymcList.size();i++){ 
	            	ZyzdBaseMajor marjorBean = zymcList.get(i);
	            	String ZYL_KEY = marjorBean.getM_cc_code() + marjorBean.getM_zymc();
	            	HM_ZYMC_MAP.put(ZYL_KEY, marjorBean);
	            }
			}
		}
		return HM_ZYMC_MAP.get(cc_code + zymc);
	}
	
	public static HashMap<String, List<ZyzdMajorCatg>> getAllMajorZylMap() {
		if(HM_ZYMC_ZYL_LIST.size() == 0) {
			synchronized(ZyzdCache.class) {
				List<ZyzdMajorCatg> zylList = new ZyzdJDBC().getZymlTwo();
				for(int i=0;i<zylList.size();i++){ 
                	ZyzdMajorCatg marjorBean = zylList.get(i);
                	String ZYML_KEY = marjorBean.getC_cc_code() + marjorBean.getP_c_name();
                	
                	String key_zymc = ZYML_KEY + marjorBean.getC_name()+"|";
	            	if(duplicate_control.containsKey(key_zymc)) {
	            		continue;
	            	}
	            	duplicate_control.put(key_zymc, key_zymc);
                	
                	if(HM_ZYMC_ZYL_LIST.containsKey(ZYML_KEY)){
                		List<ZyzdMajorCatg> majorBeanListTemp = HM_ZYMC_ZYL_LIST.get(ZYML_KEY);
                		majorBeanListTemp.add(marjorBean);
                	}else{
                		List<ZyzdMajorCatg> majorBeanListTemp = new ArrayList<>();
                		majorBeanListTemp.add(marjorBean);
                		HM_ZYMC_ZYL_LIST.put(ZYML_KEY, majorBeanListTemp);
                	}
                }
			}
		}
		return HM_ZYMC_ZYL_LIST;
	}
	
	/**
	 * 根据专业层次（本科、专科、职教本科）得到一级分类（工学，理学，文学）
	 * @param cc_code
	 * @return
	 */
	public static List<ZyzdMajorCatg> getMajorOneByCc(String cc_code) {
		HashMap<String, ZyzdMajorCatg> temp = getAllMajorCatgOne();
		Iterator<String> itKeys = temp.keySet().iterator();
		List<ZyzdMajorCatg> catgOneList = new ArrayList<>();
		while(itKeys.hasNext()) {
			ZyzdMajorCatg majorCatgOne = temp.get(itKeys.next());
			if(majorCatgOne.getC_cc_code().equals(cc_code)) {
				catgOneList.add(majorCatgOne);
			}
		}
		return catgOneList;
	}
	
	/**
	 * 根据专业层次（本科、专科、职教本科）和一级分类名称（工学，理学，文学）得到二级分类（计算机类，电子信息类）
	 * @param cc_code
	 * @param catgOne
	 * @return
	 */
	public static List<ZyzdMajorCatg> getMajorZylByOne(String cc_code, String catgOne) {
		HashMap<String, List<ZyzdMajorCatg>> temp = getAllMajorZylMap();
		return temp.get(cc_code + catgOne);
	}

	/**
	 * 根据专业层次（本科、专科、职教本科），
	 * 一级分类名称（工学，理学，文学），
	 * 二级分类名称（计算机类，电子信息类）得到所有三级专业
	 * @param cc_code
	 * @param catg_one
	 * @param catg_two
	 * @return
	 */
	public static List<ZyzdBaseMajor> getAllMajorMap(String cc_code, String catg_one, String catg_two) {
		getAllMajorMap();
		String ZYL_KEY = cc_code + catg_one + catg_two;
		return HM_ZYMC_LIST.get(ZYL_KEY);
	}
	
	public static HashMap<String, ZyzdMajorCatg> getAllMajorCatgOne() {
		if(HM_ZY_CATG_ONE_MAP.size() == 0) {
			synchronized(ZyzdCache.class) {
				List<ZyzdMajorCatg> zylList = new ZyzdJDBC().getZymlOne();
				for(int i=0;i<zylList.size();i++){ 
					ZyzdMajorCatg catg = zylList.get(i);
					HM_ZY_CATG_ONE_MAP.put(catg.getC_cc_code() + catg.getC_name(), catg);
                }
			}
		}
		return HM_ZY_CATG_ONE_MAP;
	}
	
	public static List<String> getAllVocation(){
		if(LIST_VOCATION_ONE == null || LIST_VOCATION_ONE.size() == 0) {
			synchronized(ZyzdCache.class) {
				LIST_VOCATION_ONE = new ZyzdJDBC().getAllVocationCatgOne();
			}
		}
		return LIST_VOCATION_ONE;
	}
	
	public static List<CareerVocation> getVocationByPname(String pname){
		if(LIST_VOCATION == null || LIST_VOCATION.size() == 0) {
			synchronized(ZyzdCache.class) {
				LIST_VOCATION = new ZyzdJDBC().getAllVocation();
			}
		}
		List<CareerVocation> cvList = new ArrayList<>();
		for(CareerVocation cv : LIST_VOCATION) {
			if(cv.getP_name().equals(pname)) {
				cvList.add(cv);
			}
		}
		return cvList;
	}
	

	public static ZyzdProvince getUserCardProvince(String code) {
		ZyzdProvinceConfig config = getProvinceConfig(code);
		if(config == null) {
			return null;
		}
		return new ZyzdProvince(config.getP_code(), config.getP_table_code(), config.getP_name(), config.getLatest_year_yx(), config.getLatest_year_zy());
	}
	
	public static ZyzdUniversityBean getUniversity(String yxmc) {
		return HM_ALL_UNIVERSITIES.get(yxmc);
	}
	
	public static ZyzdBaseUniversityJz getUniversityJz(String yxmc) {
		return HM_ALL_UNIVERSITIES_JZ.get(yxmc);
	}
	
	public static String getHLDAssessResult(String code) {
		return MM_HLD_ASSESS.get(code);
	}
	
	public static LinkedList<String> getAllUnivCatg() {
		return LIST_UNIV_CATG;
	}
	
	public static List<CareerJY> getJYLsyByGroupViewName(String hy) {
		if(HM_VIEW_HY.containsKey(hy)) {
			return HM_VIEW_HY.get(hy);
		}else {
			List<CareerJY> lsyList = new ZyzdJDBC().getJiuyeLsyByGroup(hy);
			HM_VIEW_HY.put(hy, lsyList);
			return lsyList;
		}
	}
	
	
	public static HashMap<String, String> getMM_MAJOR_ADMISSION_RULES() {
		return MM_MAJOR_ADMISSION_RULES;
	}

	public static void setMM_MAJOR_ADMISSION_RULES(HashMap<String, String> mM_MAJOR_ADMISSION_RULES) {
		MM_MAJOR_ADMISSION_RULES = mM_MAJOR_ADMISSION_RULES;
	}

	
}
