<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page import="com.zsdwf.utils.*,com.zsdwf.db.*,java.util.*" %>

<%@include file="/WEB-INF/include/_session_admin_ajax.jsp" %>
<%
YGCardBean card = (YGCardBean)session.getAttribute("SES_ADMIN");
String tel = Tools.trim(request.getParameter("tel"));
if(Tools.isEmpty(tel)){
	
}
YGJDBC jdbc = new YGJDBC();
List<YGCardBean> list = jdbc.getYGCardListByIdOrPhone(tel);

for(int i=0;i<list.size();i++){
	YGCardBean bean = list.get(i);
%>
<tr><td><%=bean.getId() %></td><td><%=bean.getPasswd() %></td><td><%=bean.getPhone() %></td><td><%=bean.getCnt() %></td><td><%=bean.getUsed() %></td><td><%=Tools.view(Tools.getDate(bean.getLastQuery()))  %></td><td><%=Tools.view(Tools.getDate(bean.getCreate())) %></td><td><%=Tools.view(Tools.getDate(bean.getUpdate())) %></td><td><%=Tools.viewForLimitLength(bean.getRemark(), 10) %></td></tr>         
<%
}
%>



		