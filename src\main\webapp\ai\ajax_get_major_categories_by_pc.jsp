<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="com.career.db.*,com.career.utils.*,java.util.*" %>

<%

// 获取从前端传入的批次代码参数
String pc_code_param = Tools.trim(request.getParameter("pc_code"));

// 从缓存中获取所有专业门类
HashMap<String, ZyzdMajorCatg> catgOneListMap = ZyzdCache.getAllMajorCatgOne();
Iterator<String> catgKeys = catgOneListMap.keySet().iterator();

// 遍历并根据批次代码过滤，输出按钮HTML
while(catgKeys.hasNext()){
    String key = catgKeys.next();
    ZyzdMajorCatg each = catgOneListMap.get(key);
    // 检查当前专业门类的cc_code是否与选中的批次代码匹配
    if(!each.getC_cc_code().equals(pc_code_param)){
        continue;
    }
%>
<button type="button"
        class="btn btn-outline-primary btn-sm px-3 py-1 MM_major_category_btn"
        style="font-size: 13px; border-radius: 15px; transition: all 0.2s ease;"
        data-category="<%=each.getC_cc_code()+"_"+each.getC_name()%>"
        onclick="MM_load_major_classes(this, '<%=each.getC_cc_code()+"_"+each.getC_name()%>')">
    <%=each.getC_name() %>
</button>
<%} %>