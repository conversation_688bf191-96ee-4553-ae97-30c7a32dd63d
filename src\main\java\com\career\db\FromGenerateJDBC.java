package com.career.db;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;

import com.career.utils.SQLLogUtils;
import com.career.utils.Tools;

public class FromGenerateJDBC {
	
	static String URL = JDBCConstants.URL;
	static String USER = JDBCConstants.USER;
	static String PASSWD = JDBCConstants.PASSWD;
	
	static {
		try {
			Class.forName("com.mysql.cj.jdbc.Driver");
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
	
	
	public List<JHBean> getZyByZymc(int jhYear, String sfCode, String xkCode, String pc_code, String pc, HashSet<String> zymcOrgSets, HashSet<String> yxsfSets, String ind_nature, int is_hz, int zdfwc_from, int zdfwc_to) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String zymcSQL = zymcOrgSets.size() == 0 ? "" : "and x.zymc_org in ("+Tools.getSQLQueryin(zymcOrgSets)+")";
			String yxsfSQL = yxsfSets.size() == 0 ? "" : "and x.yxsf in ("+Tools.getSQLQueryin(yxsfSets)+")";
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.pc_code = ? and x.pc = ? and x.xk_code like ? "+yxsfSQL+" and (x.zdfwc between ? and ?) and x.ind_nature like ? and x.is_hz <= ? " + zymcSQL;
			String ORDER_CONDITION = " ORDER BY x.zdfwc ASC";
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, pc_code);
			ps.setString(2, pc);
			ps.setString(3, "%"+xkCode+"%");
			ps.setInt(4, zdfwc_from);
			ps.setInt(5, zdfwc_to);
			ps.setString(6, "%"+ind_nature+"%");  
			ps.setInt(7, is_hz);
			SQLLogUtils.printSQL("getZyByZymc>>>",ps);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(ZyzdFormJDBC.setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JHBean> getZyByZyml(int jhYear, String sfCode, String xkCode, String pc_code, String pc, HashSet<String> zymlSets, HashSet<String> yxsfSets, String ind_nature, int is_hz, int zdfwc_from, int zdfwc_to) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String zymlSQL = zymlSets.size() == 0 ? "" : "and x.zyml in ("+Tools.getSQLQueryin(zymlSets)+")";
			String yxsfSQL = yxsfSets.size() == 0 ? "" : "and x.yxsf in ("+Tools.getSQLQueryin(yxsfSets)+")";
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.pc_code = ? and x.pc = ? and x.xk_code like ? "+yxsfSQL+" and (x.zdfwc between ? and ?) and x.ind_nature like ? and x.is_hz <= ? " + zymlSQL;
			String ORDER_CONDITION = " ORDER BY x.zdfwc ASC";
			
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, pc_code);
			ps.setString(2, pc);
			ps.setString(3, "%"+xkCode+"%");
			ps.setInt(4, zdfwc_from);
			ps.setInt(5, zdfwc_to);
			ps.setString(6, "%"+ind_nature+"%"); 
			ps.setInt(7, is_hz);
			SQLLogUtils.printSQL(ps);
			rs = ps.executeQuery();

			SQLLogUtils.printSQL(">>getZyByZyml**************>>>", ps);
			
			while (rs.next()) {
				list.add(ZyzdFormJDBC.setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JHBean> getZyByZymc(int jhYear, String sfCode, String xkCode, String pc_code, String pc, HashSet<String> zymcOrgSets, HashSet<String> yxsfSets, String ind_nature, int is_hz, int znzyls, int zdfwc_from, int zdfwc_to) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String zymcSQL = zymcOrgSets.size() == 0 ? "" : "and x.zymc_org in ("+Tools.getSQLQueryin(zymcOrgSets)+")";
			String yxsfSQL = yxsfSets.size() == 0 ? "" : "and x.yxsf in ("+Tools.getSQLQueryin(yxsfSets)+")";
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.pc_code = ? and x.pc = ? and x.xk_code like ? "+yxsfSQL+" and (x.zdfwc between ? and ?) and x.ind_nature like ? and x.is_hz <= ? and x.znzyls <= ? " + zymcSQL;
			String ORDER_CONDITION = " ORDER BY x.zdfwc ASC";
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, pc_code);
			ps.setString(2, pc);
			ps.setString(3, "%"+xkCode+"%");
			ps.setInt(4, zdfwc_from);
			ps.setInt(5, zdfwc_to);
			ps.setString(6, "%"+ind_nature+"%");  
			ps.setInt(7, is_hz);
			ps.setInt(8, znzyls);
			SQLLogUtils.printSQL("getZyByZymc>>>",ps);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(ZyzdFormJDBC.setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JHBean> getZyByZyml(int jhYear, String sfCode, String xkCode, String pc_code, String pc, HashSet<String> zymlSets, HashSet<String> yxsfSets, String ind_nature, int is_hz, int znzyls, int zdfwc_from, int zdfwc_to) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String zymlSQL = zymlSets.size() == 0 ? "" : "and x.zyml in ("+Tools.getSQLQueryin(zymlSets)+")";
			String yxsfSQL = yxsfSets.size() == 0 ? "" : "and x.yxsf in ("+Tools.getSQLQueryin(yxsfSets)+")";
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.pc_code = ? and x.pc = ? and x.xk_code like ? "+yxsfSQL+" and (x.zdfwc between ? and ?) and x.ind_nature like ? and x.is_hz <= ? and x.znzyls <= ? " + zymlSQL;
			String ORDER_CONDITION = " ORDER BY x.zdfwc ASC";
			
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, pc_code);
			ps.setString(2, pc);
			ps.setString(3, "%"+xkCode+"%");
			ps.setInt(4, zdfwc_from);
			ps.setInt(5, zdfwc_to);
			ps.setString(6, "%"+ind_nature+"%"); 
			ps.setInt(7, is_hz);
			ps.setInt(8, znzyls);
			SQLLogUtils.printSQL(ps);
			rs = ps.executeQuery();

			SQLLogUtils.printSQL(">>getZyByZyml**************>>>", ps);
			
			while (rs.next()) {
				list.add(ZyzdFormJDBC.setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JHBean> getZyByZymcWithOrderFollow(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> zymlSets, String order_id, String ind_nature, int is_hz, int zdfwc_from, int zdfwc_to) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>(); 
		try {
			conn = DatabaseUtils.getConnection();
			String zymlSQL = zymlSets.size() == 0 ? "" : "and x.zyml in ("+Tools.getSQLQueryin(zymlSets)+")";
			String SELECT_CONDITION = "SELECT x.* FROM lhy_"+ sfCode +"_form_user_follow y, "+sfCode+"_jh_" + jhYear + " x WHERE x.yxmc_org = y.yxmc_org and y.order_id = ? and x.pc = ? and x.pc_code = ? and x.xk_code like ? and (x.zdfwc between ? and ?) and x.ind_nature like ? and x.is_hz <= ? " + zymlSQL;
			String ORDER_CONDITION = " ORDER BY x.zdfwc ASC";
			
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, order_id);
			ps.setString(2, pc);
			ps.setString(3, pc_code);
			ps.setString(4, "%"+xkCode+"%");
			ps.setInt(5, zdfwc_from);
			ps.setInt(6, zdfwc_to);
			ps.setString(7, "%"+ind_nature+"%");
			ps.setInt(8, is_hz);

			SQLLogUtils.printSQL("getZyByZymcWithOrderFollow", ps);
			
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(ZyzdFormJDBC.setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JHBean> getZyByZymcWithOrderFollow(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> zymlSets, String order_id, String ind_nature, int is_hz, int znzyls, int zdfwc_from, int zdfwc_to) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>(); 
		try {
			conn = DatabaseUtils.getConnection();
			String zymlSQL = zymlSets.size() == 0 ? "" : "and x.zyml in ("+Tools.getSQLQueryin(zymlSets)+")";
			String SELECT_CONDITION = "SELECT x.* FROM lhy_"+ sfCode +"_form_user_follow y, "+sfCode+"_jh_" + jhYear + " x WHERE x.yxmc_org = y.yxmc_org and y.order_id = ? and x.pc = ? and x.pc_code = ? and x.xk_code like ? and (x.zdfwc between ? and ?) and x.ind_nature like ? and x.is_hz <= ?  and x.znzyls <= ?  " + zymlSQL;
			String ORDER_CONDITION = " ORDER BY x.zdfwc ASC";
			
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, order_id);
			ps.setString(2, pc);
			ps.setString(3, pc_code);
			ps.setString(4, "%"+xkCode+"%");
			ps.setInt(5, zdfwc_from);
			ps.setInt(6, zdfwc_to);
			ps.setString(7, "%"+ind_nature+"%");
			ps.setInt(8, is_hz);
			ps.setInt(9, znzyls);

			SQLLogUtils.printSQL("getZyByZymcWithOrderFollow", ps);
			
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(ZyzdFormJDBC.setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	//全程学业规划系统使用
	public List<JHBean> getZyByZymcWithOrderFollowForQC(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> zymlSets, String c_id, String ind_nature, int is_hz, int zdfwc_from, int zdfwc_to) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String zymlSQL = zymlSets.size() == 0 ? "" : "and x.zyml in ("+Tools.getSQLQueryin(zymlSets)+")";
			String SELECT_CONDITION = "SELECT x.* FROM "+ sfCode +"_form_user_follow y, "+sfCode+"_jh_" + jhYear + " x WHERE x.yxmc_org = y.yxmc_org and y.c_id = ? and x.pc = ? and x.pc_code = ? and x.xk_code like ? and (x.zdfwc between ? and ?) and x.ind_nature like ? and x.is_hz <= ?  " + zymlSQL;
			String ORDER_CONDITION = " ORDER BY x.zdfwc ASC";
			
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, c_id);
			ps.setString(2, pc);
			ps.setString(3, pc_code);
			ps.setString(4, "%"+xkCode+"%");
			ps.setInt(5, zdfwc_from);
			ps.setInt(6, zdfwc_to);
			ps.setString(7, "%"+ind_nature+"%");
			ps.setInt(8, is_hz);

			SQLLogUtils.printSQL("getZyByZymcWithOrderFollow", ps);
			
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(ZyzdFormJDBC.setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JHBean> getJHByYxdmAndZyz(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> yxdmSets, HashSet<String> zyzSets, HashSet<String> zymlSets) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = jhYear-1;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String yxdmSQL = yxdmSets.size() == 0 ? "" : "and x.yxdm in ("+Tools.getSQLQueryin(yxdmSets)+")";
			String zyzSQL = zyzSets.size() == 0 ? "" : " and x.zyz in ("+Tools.getSQLQueryin(zyzSets)+")";
			String zymlSQL = zymlSets.size() == 0 ? "" : " and x.zyml in ("+Tools.getSQLQueryin(zymlSets)+")";
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.pc = ? and pc_code = ? and xk_code like ? " + yxdmSQL + zyzSQL + zymlSQL;
			String ORDER_CONDITION = " ORDER BY x.zdfwc ASC";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(ZyzdFormJDBC.setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public HashMap<String, Integer> getJHCountByYxdmAndZyz(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> yxdmSets, HashSet<String> zyzSets, HashSet<String> zymlSets) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = jhYear-1;
		HashMap<String, Integer> MAP_CNT = new HashMap<>();
		try {
			conn = DatabaseUtils.getConnection();
			String yxdmSQL = yxdmSets.size() == 0 ? "" : "and x.yxdm in ("+Tools.getSQLQueryin(yxdmSets)+")";
			String zyzSQL = zyzSets.size() == 0 ? "" : " and x.zyz in ("+Tools.getSQLQueryin(zyzSets)+")";
			String zymlSQL = zymlSets.size() == 0 ? "" : " and x.zyml in ("+Tools.getSQLQueryin(zymlSets)+")";
			String SELECT_CONDITION = "SELECT x.yxdm, x.zyz, count(*) as cnt FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.pc = ? and pc_code = ? and xk_code like ? " + yxdmSQL + zyzSQL + zymlSQL;
			String ORDER_CONDITION = " GROUP BY x.yxdm, x.zyz";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			rs = ps.executeQuery();
			while (rs.next()) {
				String yxdm = Tools.trim(rs.getString("yxdm"));
				String zyz = Tools.trim(rs.getString("zyz"));
				int cnt = rs.getInt("cnt");
				MAP_CNT.put(yxdm + "_" + zyz, cnt);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return MAP_CNT;
	}
	
	/**
	 * 
	 * 根据院校名称和专业组在计划表中查询出所有专业
	 * 
	 * @param jhYear
	 * @param sfCode
	 * @param xkCode
	 * @param pc
	 * @param pc_code
	 * @param yxmcSets
	 * @param zymlSets
	 * @return
	 * 
	 */
	public List<JHBean> getJHByYxmcAndZyml(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> yxmcSets, HashSet<String> zymlSets) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = jhYear-1;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String yxmcSQL = yxmcSets.size() == 0 ? "" : "and x.yxmc in ("+Tools.getSQLQueryin(yxmcSets)+")";
			String zymlSQL = zymlSets.size() == 0 ? "" : "and x.zyml in ("+Tools.getSQLQueryin(zymlSets)+")";
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.pc = ? and pc_code = ? and xk_code like ? " + yxmcSQL + zymlSQL;
			String ORDER_CONDITION = " ORDER BY x.zdfwc ASC";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === getJHByYxmcAndZyml() : ", ps);
			
			while (rs.next()) {
				list.add(ZyzdFormJDBC.setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	private static void closeAllConnection(Connection conn, PreparedStatement ps, ResultSet rs) {
		/**
		try {
			if (rs != null)
				rs.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (ps != null)
				ps.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (conn != null)
				conn.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		rs = null;
		ps = null;
		conn = null;
		*/
		DatabaseUtils.closeAllResources(rs, ps, conn);
	}

}
