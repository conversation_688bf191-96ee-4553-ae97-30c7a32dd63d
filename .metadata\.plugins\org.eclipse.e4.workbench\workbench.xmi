<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_WMQooGCGEfC3wuS16sDcWA" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_WMQooWCGEfC3wuS16sDcWA" bindingContexts="_WMQoqmCGEfC3wuS16sDcWA">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList/>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_WMQooWCGEfC3wuS16sDcWA" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_WZGKwWCGEfC3wuS16sDcWA" label="%trimmedwindow.label.eclipseSDK" x="480" y="480" width="1024" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1752479060706"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;show_in_time/>"/>
    <tags>topLevel</tags>
    <tags>shellMaximized</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_WZGKwWCGEfC3wuS16sDcWA" selectedElement="_WZGKwmCGEfC3wuS16sDcWA" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_WZGKwmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_Wc1bQGCGEfC3wuS16sDcWA">
        <children xsi:type="advanced:Perspective" xmi:id="_Wc1bQGCGEfC3wuS16sDcWA" elementId="org.eclipse.jst.j2ee.J2EEPerspective" selectedElement="_Wc1bQWCGEfC3wuS16sDcWA" label="Java EE" iconURI="platform:/plugin/org.eclipse.jst.j2ee.ui/icons/full/cview16/j2ee_perspective.gif">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,"/>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.jst.j2ee.J2eeMainActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.debugActionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.wst.server.ui.ServersView</tags>
          <tags>persp.viewSC:org.eclipse.datatools.connectivity.DataSourceExplorerNavigator</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.BookmarkView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
          <tags>persp.viewSC:org.eclipse.wst.common.snippets.internal.ui.SnippetsView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.AllMarkersView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.mylyn.tasks.ui.views.tasks</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.PackagesView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.showIn:org.eclipse.jdt.ui.PackagesView</tags>
          <tags>persp.actionSet:org.eclipse.wst.ws.explorer.explorer</tags>
          <tags>persp.newWizSC:org.eclipse.m2e.core.wizards.Maven2ProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.wst.css.ui.internal.wizard.NewCSSWizard</tags>
          <tags>persp.newWizSC:org.eclipse.wst.jsdt.ui.NewJSWizard</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaPerspective</tags>
          <tags>persp.perspSC:org.eclipse.ui.resourcePerspective</tags>
          <tags>persp.perspSC:org.eclipse.wst.web.ui.webDevPerspective</tags>
          <tags>persp.newWizSC:org.eclipse.jst.j2ee.ui.project.facet.EarProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.servlet.ui.project.facet.WebProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.ejb.ui.project.facet.EjbProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.j2ee.jca.ui.internal.wizard.ConnectorProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.j2ee.ui.project.facet.appclient.AppClientProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.wst.web.ui.internal.wizards.SimpleWebProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jpt.ui.wizard.newJpaProject</tags>
          <tags>persp.newWizSC:org.eclipse.jst.servlet.ui.internal.wizard.AddServletWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.ejb.ui.internal.wizard.AddSessionBeanWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.ejb.ui.internal.wizard.AddMessageDrivenBeanWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jpt.ui.wizard.newEntity</tags>
          <tags>persp.newWizSC:org.eclipse.jst.ws.creation.ui.wizard.serverwizard</tags>
          <tags>persp.newWizSC:org.eclipse.wst.html.ui.internal.wizard.NewHTMLWizard</tags>
          <tags>persp.newWizSC:org.eclipse.wst.xml.ui.internal.wizards.NewXMLWizard</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.folder</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.file</tags>
          <tags>persp.actionSet:org.eclipse.wst.server.ui.internal.webbrowser.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.actionSet:org.eclipse.eclemma.ui.CoverageActionSet</tags>
          <tags>persp.showIn:org.eclipse.eclemma.ui.CoverageView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.newWizSC:org.eclipse.jst.jsp.ui.internal.wizard.NewJSPWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jpt.jpa.ui.wizard.newJpaProject</tags>
          <tags>persp.perspSC:org.eclipse.jpt.ui.jpaPerspective</tags>
          <tags>persp.editorOnboardingText:Open a file or drop files here to open them.</tags>
          <tags>persp.editorOnboardingCommand:Find Actions$$$Ctrl+3</tags>
          <tags>persp.editorOnboardingCommand:Show Key Assist$$$Ctrl+Shift+L</tags>
          <tags>persp.editorOnboardingCommand:New$$$Ctrl+N</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_Wc1bQWCGEfC3wuS16sDcWA" selectedElement="_Wc1bTmCGEfC3wuS16sDcWA" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_Wc1bQmCGEfC3wuS16sDcWA" elementId="topLeft" toBeRendered="false" containerData="2000">
              <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bQ2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigator.ProjectExplorer" toBeRendered="false" ref="_WcuGgGCGEfC3wuS16sDcWA" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bRGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.PackagesView" toBeRendered="false" ref="_WcutkGCGEfC3wuS16sDcWA" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:Java Browsing</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bRWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigator.ProjectExplorer" toBeRendered="false" ref="_WcuGgGCGEfC3wuS16sDcWA" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bRmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.TypeHierarchy" toBeRendered="false" ref="_WcutkWCGEfC3wuS16sDcWA" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:Java</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bR2CGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.DebugView" toBeRendered="false" ref="_WcutkmCGEfC3wuS16sDcWA" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:Debug</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bSGCGEfC3wuS16sDcWA" elementId="org.eclipse.search.ui.views.SearchView" toBeRendered="false" ref="_Wcutk2CGEfC3wuS16sDcWA" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bSWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesView" toBeRendered="false" ref="_WcutlGCGEfC3wuS16sDcWA" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:Git</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bSmCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.VariableView" toBeRendered="false" ref="_WcwiwmCGEfC3wuS16sDcWA" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:Debug</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bS2CGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.ExpressionView" toBeRendered="false" ref="_Wcwiw2CGEfC3wuS16sDcWA" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:Debug</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bTGCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.BreakpointView" toBeRendered="false" ref="_WcwixGCGEfC3wuS16sDcWA" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:Debug</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bTWCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.MemoryView" toBeRendered="false" ref="_WcwixWCGEfC3wuS16sDcWA" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:Debug</tags>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_Wc1bTmCGEfC3wuS16sDcWA" containerData="8000" selectedElement="_Wc1bT2CGEfC3wuS16sDcWA">
              <children xsi:type="basic:PartSashContainer" xmi:id="_Wc1bT2CGEfC3wuS16sDcWA" containerData="8000" selectedElement="_Wc1bUWCGEfC3wuS16sDcWA" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bUGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.editorss" containerData="8000" ref="_WcqcIGCGEfC3wuS16sDcWA"/>
                <children xsi:type="basic:PartStack" xmi:id="_Wc1bUWCGEfC3wuS16sDcWA" elementId="topRight" containerData="2000" selectedElement="_Wc1bUmCGEfC3wuS16sDcWA">
                  <tags>active</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bUmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.ContentOutline" ref="_WcwiwGCGEfC3wuS16sDcWA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bU2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_WcwiwWCGEfC3wuS16sDcWA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bVGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_WcwiwWCGEfC3wuS16sDcWA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_Wc1bVWCGEfC3wuS16sDcWA" elementId="bottomRight" containerData="2000" selectedElement="_Wc1bVmCGEfC3wuS16sDcWA">
                <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bVmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.ProblemView" ref="_WcvUoGCGEfC3wuS16sDcWA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bV2CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.server.ui.ServersView" ref="_WcvUoWCGEfC3wuS16sDcWA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Server</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bWGCGEfC3wuS16sDcWA" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" ref="_WcvUomCGEfC3wuS16sDcWA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Terminal</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bWWCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.connectivity.DataSourceExplorerNavigator" ref="_WcvUo2CGEfC3wuS16sDcWA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Data Management</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bWmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.PropertySheet" ref="_WcvUpGCGEfC3wuS16sDcWA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bW2CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.common.snippets.internal.ui.SnippetsView" toBeRendered="false" ref="_Wcv7sGCGEfC3wuS16sDcWA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bXGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.AllMarkersView" toBeRendered="false" ref="_Wcv7sWCGEfC3wuS16sDcWA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bXWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_Wcv7smCGEfC3wuS16sDcWA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bXmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.TaskList" toBeRendered="false" ref="_Wcv7s2CGEfC3wuS16sDcWA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bX2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.console.ConsoleView" toBeRendered="false" ref="_Wcv7tGCGEfC3wuS16sDcWA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bYGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_Wcv7tWCGEfC3wuS16sDcWA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bYWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.JavadocView" toBeRendered="false" ref="_Wcv7tmCGEfC3wuS16sDcWA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bYmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.MembersView" toBeRendered="false" ref="_Wcv7t2CGEfC3wuS16sDcWA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java Browsing</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_Wc1bY2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.SourceView" toBeRendered="false" ref="_Wcv7uGCGEfC3wuS16sDcWA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_WZGKw2CGEfC3wuS16sDcWA" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_WZGKxGCGEfC3wuS16sDcWA" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_WZFjsGCGEfC3wuS16sDcWA" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_WZGKxWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_WZFjsWCGEfC3wuS16sDcWA" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_WZGKxmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_WZGKwGCGEfC3wuS16sDcWA" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZFjsGCGEfC3wuS16sDcWA" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZFjsWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view>&#xD;&#xA;&lt;presentation currentPage=&quot;qroot&quot; restore=&quot;true&quot;/>&#xD;&#xA;&lt;standbyPart/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_Wn_iYGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.internal.introview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_Wn_iYWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.internal.introview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WZGKwGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_WcqcIGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.editorss">
      <children xsi:type="basic:PartStack" xmi:id="_WcqcIWCGEfC3wuS16sDcWA" elementId="org.eclipse.e4.primaryDataStack">
        <tags>EditorStack</tags>
        <tags>org.eclipse.e4.primaryDataStack</tags>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WcuGgGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;>&#xD;&#xA;&lt;lastRecentlyUsedFilters/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_WfGFEGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_WfGsIGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigator.ProjectExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WcutkGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.PackagesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Packages" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/packages.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.PackagesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java Browsing</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WcutkWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.TypeHierarchy" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WcutkmCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.DebugView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Wcutk2CGEfC3wuS16sDcWA" elementId="org.eclipse.search.ui.views.SearchView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WcutlGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Git</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WcvUoGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;105&quot; org.eclipse.ui.ide.markerType=&quot;105&quot; org.eclipse.ui.ide.pathField=&quot;140&quot; org.eclipse.ui.ide.resourceField=&quot;105&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;350&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_WlVP8GCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_WlVP8WCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WcvUoWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.server.ui.ServersView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Servers" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.server.ui.internal.cnf.ServersView2"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.server.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Server</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WcvUomCGEfC3wuS16sDcWA" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Terminal</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WcvUo2CGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.connectivity.DataSourceExplorerNavigator" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Data Source Explorer" iconURI="platform:/plugin/org.eclipse.datatools.connectivity.ui.dse/icons/full/cview16/enterprise_explorer.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.datatools.connectivity.ui.dse.views.DataSourceExplorerView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.datatools.connectivity.ui.dse"/>
      <tags>View</tags>
      <tags>categoryTag:Data Management</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WcvUpGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Wcv7sGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.common.snippets.internal.ui.SnippetsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Snippets" iconURI="platform:/plugin/org.eclipse.wst.common.snippets/icons/snippets_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.common.snippets.internal.ui.SnippetsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.common.snippets"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Wcv7sWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.AllMarkersView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Wcv7smCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Wcv7s2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.TaskList" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Wcv7tGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Wcv7tWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Wcv7tmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.JavadocView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Wcv7t2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.MembersView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Members" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/members.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.MembersView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java Browsing</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Wcv7uGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.SourceView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WcwiwGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <tags>active</tags>
      <tags>activeOnClose</tags>
      <menus xmi:id="_WkuL8GCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_WkuL8WCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WcwiwWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WcwiwmCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.VariableView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Wcwiw2CGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.ExpressionView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WcwixGCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.BreakpointView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WcwixWCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.MemoryView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <trimBars xmi:id="_WMQoomCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.platform">
      <children xsi:type="menu:ToolBar" xmi:id="_Wa6IoGCGEfC3wuS16sDcWA" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_Wa6IoWCGEfC3wuS16sDcWA" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_Wa6vsGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_tL50MGCGEfCG3J0YabKHdQ" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_WMt7r2CGEfC3wuS16sDcWA"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_Wa6vsWCGEfC3wuS16sDcWA" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_Wa6vsmCGEfC3wuS16sDcWA" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_Wa6vs2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.workbench.edit">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_tL6bQWCGEfCG3J0YabKHdQ" elementId="undo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/undo_edit.png" tooltip="Undo" enabled="false" command="_WMstvmCGEfC3wuS16sDcWA"/>
        <children xsi:type="menu:HandledToolItem" xmi:id="_tL7CUGCGEfCG3J0YabKHdQ" elementId="redo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/redo_edit.png" tooltip="Redo" enabled="false" command="_WMtVIWCGEfC3wuS16sDcWA"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_Wa6vtGCGEfC3wuS16sDcWA" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_Wa6vtWCGEfC3wuS16sDcWA" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_WdpTkGCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_WdKycGCGEfC3wuS16sDcWA" elementId="org.eclipse.jst.j2ee.J2eeMainActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_WddtYGCGEfC3wuS16sDcWA" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_WdXmwGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.server.ui.internal.webbrowser.actionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_Wdil4GCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.ws.explorer.explorer">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_Wa6vtmCGEfC3wuS16sDcWA" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_Wa6vt2CGEfC3wuS16sDcWA" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_Wa7WwGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_tL7pYGCGEfCG3J0YabKHdQ" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" enabled="false" type="Check" command="_WMtWN2CGEfC3wuS16sDcWA"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_Wa7WwWCGEfC3wuS16sDcWA" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_Wa7WwmCGEfC3wuS16sDcWA" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_Wa7Ww2CGEfC3wuS16sDcWA" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_Wa7WxGCGEfC3wuS16sDcWA" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_Wa7WxWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_WbuoAGCGEfC3wuS16sDcWA" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_WbvPEGCGEfC3wuS16sDcWA" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_WMQoo2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.platform" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_WMQopGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_WMQopWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_WMQopmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_WMQop2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_WqHpUGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.ide.perspectivestack(minimized)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_WMQoqGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.platform" side="Right"/>
  </children>
  <bindingTables xmi:id="_WMQoqWCGEfC3wuS16sDcWA" contributorURI="platform:/plugin/org.eclipse.platform" bindingContext="_WMQoqmCGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNA2lmCGEfC3wuS16sDcWA" keySequence="CTRL+1" command="_WMsGn2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNBdsGCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+I" command="_WMsGfWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNBdu2CGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+L" command="_WMt77mCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCrxGCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+D" command="_WMt8GmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND58mCGEfC3wuS16sDcWA" keySequence="CTRL+V" command="_WMqRZ2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNEg8mCGEfC3wuS16sDcWA" keySequence="CTRL+A" command="_WMtU8mCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFIAGCGEfC3wuS16sDcWA" keySequence="CTRL+C" command="_WMtVWWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFvHWCGEfC3wuS16sDcWA" keySequence="CTRL+X" command="_WMstxmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFvHmCGEfC3wuS16sDcWA" keySequence="CTRL+Y" command="_WMtVIWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFvIWCGEfC3wuS16sDcWA" keySequence="CTRL+Z" command="_WMstvmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNGWNGCGEfC3wuS16sDcWA" keySequence="ALT+PAGE_UP" command="_WMtVMWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNGWNWCGEfC3wuS16sDcWA" keySequence="ALT+PAGE_DOWN" command="_WMtV-2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNG9MWCGEfC3wuS16sDcWA" keySequence="SHIFT+INSERT" command="_WMqRZ2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNG9N2CGEfC3wuS16sDcWA" keySequence="ALT+F11" command="_WMq4b2CGEfC3wuS16sDcWA">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_WNIyaWCGEfC3wuS16sDcWA" keySequence="CTRL+F10" command="_WMqRjWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyc2CGEfC3wuS16sDcWA" keySequence="CTRL+INSERT" command="_WMtVWWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyhWCGEfC3wuS16sDcWA" keySequence="CTRL+PAGE_UP" command="_WMt7yWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyhmCGEfC3wuS16sDcWA" keySequence="CTRL+PAGE_DOWN" command="_WMsGqGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyiGCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+F1" command="_WMrfc2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyiWCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+F2" command="_WMtV3mCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyimCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+F3" command="_WMt7u2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZc2CGEfC3wuS16sDcWA" keySequence="SHIFT+DEL" command="_WMstxmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKnkWCGEfC3wuS16sDcWA" keySequence="ALT+/" command="_WMtWXGCGEfC3wuS16sDcWA">
      <tags>locale:zh</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_WM9zQGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.textEditorScope" bindingContext="_WMwX4mCGEfC3wuS16sDcWA">
    <bindings xmi:id="_WM_BYGCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+CR" command="_WMt7umCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WM_ocmCGEfC3wuS16sDcWA" keySequence="CTRL+BS" command="_WMpqPWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNA2k2CGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+Q" command="_WMsGhWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNBds2CGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+J" command="_WMsGd2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNBdt2CGEfC3wuS16sDcWA" keySequence="CTRL++" command="_WMtV1GCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNBdxGCGEfC3wuS16sDcWA" keySequence="CTRL+-" command="_WMtUyGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCrwGCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+C" command="_WMqRQGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCrzGCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+F" command="_WMsto2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNDS1mCGEfC3wuS16sDcWA" keySequence="ALT+CTRL+J" command="_WMsGlmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND542CGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+A" command="_WMtVe2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND572CGEfC3wuS16sDcWA" keySequence="CTRL+T" command="_WMt8MmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND5-mCGEfC3wuS16sDcWA" keySequence="CTRL+J" command="_WMqRlWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND5_mCGEfC3wuS16sDcWA" keySequence="CTRL+L" command="_WMtWdGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND6BmCGEfC3wuS16sDcWA" keySequence="CTRL+O" command="_WMtVYGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND6DmCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+/" command="_WMstr2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFICGCGEfC3wuS16sDcWA" keySequence="CTRL+D" command="_WMq4XmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFvFGCGEfC3wuS16sDcWA" keySequence="CTRL+=" command="_WMtV1GCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFvGWCGEfC3wuS16sDcWA" keySequence="ALT+CTRL+/" command="_WMt8A2CGEfC3wuS16sDcWA">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_WNFvGmCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+Y" command="_WMpqMmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNGWIGCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+DEL" command="_WMtWY2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNGWImCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+X" command="_WMtVYmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNGWI2CGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+Y" command="_WMtUwmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNGWJ2CGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+\" command="_WMtVlGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNGWK2CGEfC3wuS16sDcWA" keySequence="CTRL+DEL" command="_WMstt2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNGWLmCGEfC3wuS16sDcWA" keySequence="ALT+ARROW_UP" command="_WMt8X2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNGWMGCGEfC3wuS16sDcWA" keySequence="ALT+ARROW_DOWN" command="_WMtWCmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNGWNmCGEfC3wuS16sDcWA" keySequence="SHIFT+END" command="_WMtU1GCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNG9OGCGEfC3wuS16sDcWA" keySequence="SHIFT+HOME" command="_WMtUrGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNG9PmCGEfC3wuS16sDcWA" keySequence="END" command="_WMt712CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNG9QGCGEfC3wuS16sDcWA" keySequence="INSERT" command="_WMtVoGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNG9RmCGEfC3wuS16sDcWA" keySequence="F2" command="_WMsGqmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNHkTGCGEfC3wuS16sDcWA" keySequence="HOME" command="_WMt7_mCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNHkT2CGEfC3wuS16sDcWA" keySequence="ALT+CTRL+ARROW_UP" command="_WMt8NGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNHkU2CGEfC3wuS16sDcWA" keySequence="ALT+CTRL+ARROW_DOWN" command="_WMtU_WCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILVWCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+INSERT" command="_WMrfiGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILYWCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_WMtU2GCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILY2CGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_WMrfj2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyamCGEfC3wuS16sDcWA" keySequence="CTRL+F10" command="_WMt7tWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIycWCGEfC3wuS16sDcWA" keySequence="CTRL+END" command="_WMtWDmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIygGCGEfC3wuS16sDcWA" keySequence="CTRL+ARROW_UP" command="_WMrfcGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIygWCGEfC3wuS16sDcWA" keySequence="CTRL+ARROW_DOWN" command="_WMt8dGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyg2CGEfC3wuS16sDcWA" keySequence="CTRL+ARROW_LEFT" command="_WMtVU2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyhGCGEfC3wuS16sDcWA" keySequence="CTRL+ARROW_RIGHT" command="_WMsGgmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyh2CGEfC3wuS16sDcWA" keySequence="CTRL+HOME" command="_WMqRZWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyi2CGEfC3wuS16sDcWA" keySequence="CTRL+NUMPAD_MULTIPLY" command="_WMtWIGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyjGCGEfC3wuS16sDcWA" keySequence="CTRL+NUMPAD_ADD" command="_WMt8H2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZcGCGEfC3wuS16sDcWA" keySequence="CTRL+NUMPAD_SUBTRACT" command="_WMt7uGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZcWCGEfC3wuS16sDcWA" keySequence="CTRL+NUMPAD_DIVIDE" command="_WMrfdWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZgGCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_WMtWKWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZhWCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_WMtVomCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKnlWCGEfC3wuS16sDcWA" keySequence="SHIFT+CR" command="_WMt7_WCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WM_ocGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.contexts.window" bindingContext="_WMQoq2CGEfC3wuS16sDcWA">
    <bindings xmi:id="_WM_ocWCGEfC3wuS16sDcWA" keySequence="ALT+CTRL+SHIFT+A" command="_WMtWDGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WM_odWCGEfC3wuS16sDcWA" keySequence="ALT+CTRL+SHIFT+T" command="_WMqRjGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNAPgGCGEfC3wuS16sDcWA" keySequence="ALT+CTRL+SHIFT+L" command="_WMtVk2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNAPgWCGEfC3wuS16sDcWA" keySequence="ALT+CTRL+SHIFT+M" command="_WMt8EmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNAPkGCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+Q O" command="_WMtV6WCGEfC3wuS16sDcWA">
      <parameters xmi:id="_WNA2kGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_WNA2kWCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+Q P" command="_WMtV6WCGEfC3wuS16sDcWA">
      <parameters xmi:id="_WNA2kmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.PackageExplorer"/>
    </bindings>
    <bindings xmi:id="_WNA2l2CGEfC3wuS16sDcWA" keySequence="ALT+CTRL+B" command="_WMtV8WCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNA2mGCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+R" command="_WMt8emCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNA2mWCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+Q Q" command="_WMtV6WCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNA2mmCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+S" command="_WMtVx2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNA2m2CGEfC3wuS16sDcWA" keySequence="CTRL+3" command="_WMsGqWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNA2nGCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+T" command="_WMstxWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNA2nmCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+T" command="_WMrfeGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNA2oGCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+Q S" command="_WMtV6WCGEfC3wuS16sDcWA">
      <parameters xmi:id="_WNA2oWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_WNA2pWCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+Q T" command="_WMtV6WCGEfC3wuS16sDcWA">
      <parameters xmi:id="_WNA2pmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.TypeHierarchy"/>
    </bindings>
    <bindings xmi:id="_WNBdoGCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+U" command="_WMrfhmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNBdo2CGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+V" command="_WMt73WCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNBdpmCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+Q V" command="_WMtV6WCGEfC3wuS16sDcWA">
      <parameters xmi:id="_WNBdp2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_WNBdrGCGEfC3wuS16sDcWA" keySequence="ALT+CTRL+G" command="_WMtV3GCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNBdrWCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+W" command="_WMstxGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNBdrmCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+H" command="_WMtVS2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNBdsWCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+Q H" command="_WMtV6WCGEfC3wuS16sDcWA">
      <parameters xmi:id="_WNBdsmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_WNBdtGCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+Q J" command="_WMtV6WCGEfC3wuS16sDcWA">
      <parameters xmi:id="_WNBdtWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.JavadocView"/>
    </bindings>
    <bindings xmi:id="_WNBdtmCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+K" command="_WMrfbWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNBduGCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+Q K" command="_WMtV6WCGEfC3wuS16sDcWA">
      <parameters xmi:id="_WNBduWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.mylyn.tasks.ui.views.tasks"/>
    </bindings>
    <bindings xmi:id="_WNBdumCGEfC3wuS16sDcWA" keySequence="CTRL+," command="_WMqRbWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNBdwWCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+Q L" command="_WMtV6WCGEfC3wuS16sDcWA">
      <parameters xmi:id="_WNBdwmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_WNBdw2CGEfC3wuS16sDcWA" keySequence="CTRL+-" command="_WMt78WCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCEsGCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+N" command="_WMtVbWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCEsWCGEfC3wuS16sDcWA" keySequence="CTRL+." command="_WMt8OGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCEtmCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+O" command="_WMt8FWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCEuWCGEfC3wuS16sDcWA" keySequence="ALT+CTRL+P" command="_WMrfYWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCEu2CGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+A" command="_WMtV-WCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCEvWCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+B" command="_WMrfbmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCEwGCGEfC3wuS16sDcWA" keySequence="CTRL+#" command="_WMqRjmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCrxmCGEfC3wuS16sDcWA" keySequence="ALT+CTRL+T" command="_WMtVVGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCrx2CGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+E" command="_WMrfgGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCr0WCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+G" command="_WMt8SGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCr1GCGEfC3wuS16sDcWA" keySequence="ALT+CTRL+H" command="_WMqRg2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNDS0mCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+Q X" command="_WMtV6WCGEfC3wuS16sDcWA">
      <parameters xmi:id="_WNDS02CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_WNDS1GCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+Q Y" command="_WMtV6WCGEfC3wuS16sDcWA">
      <parameters xmi:id="_WNDS1WCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_WNDS12CGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+Q Z" command="_WMtV6WCGEfC3wuS16sDcWA">
      <parameters xmi:id="_WNDS2GCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_WND54WCGEfC3wuS16sDcWA" keySequence="CTRL+P" command="_WMt7r2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND54mCGEfC3wuS16sDcWA" keySequence="CTRL+Q" command="_WMt7wWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND56WCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+C" command="_WMtWYmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND57GCGEfC3wuS16sDcWA" keySequence="CTRL+S" command="_WMtUymCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND58GCGEfC3wuS16sDcWA" keySequence="CTRL+U" command="_WMtVGmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND58WCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+F" command="_WMt7xmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND582CGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+G" command="_WMtVvWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND59WCGEfC3wuS16sDcWA" keySequence="CTRL+W" command="_WMtVLGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND59mCGEfC3wuS16sDcWA" keySequence="CTRL+H" command="_WMtWW2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND5-2CGEfC3wuS16sDcWA" keySequence="CTRL+K" command="_WMtV9WCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND5_2CGEfC3wuS16sDcWA" keySequence="CTRL+M" command="_WMtWV2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND6AmCGEfC3wuS16sDcWA" keySequence="CTRL+N" command="_WMt8TWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNEg8GCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+?" command="_WMstl2CGEfC3wuS16sDcWA">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_WNEg8WCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+P" command="_WMtV4mCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNEg9WCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+R" command="_WMtVJmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNEg-2CGEfC3wuS16sDcWA" keySequence="CTRL+B" command="_WMqRcWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNEg_GCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+Q B" command="_WMtV6WCGEfC3wuS16sDcWA">
      <parameters xmi:id="_WNEg_WCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_WNFIAmCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+S" command="_WMtVQGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFIBWCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+T" command="_WMtVfmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFIBmCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+Q C" command="_WMtV6WCGEfC3wuS16sDcWA">
      <parameters xmi:id="_WNFIB2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_WNFICWCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+Q D" command="_WMtV6WCGEfC3wuS16sDcWA">
      <parameters xmi:id="_WNFICmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.SourceView"/>
    </bindings>
    <bindings xmi:id="_WNFID2CGEfC3wuS16sDcWA" keySequence="CTRL+E" command="_WMsttGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFIEGCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+V" command="_WMtU12CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFIEmCGEfC3wuS16sDcWA" keySequence="CTRL+F" command="_WMq4aGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFIFWCGEfC3wuS16sDcWA" keySequence="CTRL+G" command="_WMpqPmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFIF2CGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+W" command="_WMt8M2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFIGGCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+H" command="_WMstq2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFIGWCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+I" command="_WMqRj2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFIHGCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+J" command="_WMstsWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFIHmCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+K" command="_WMtUz2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFvEWCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+L" command="_WMsGmmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFvEmCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+M" command="_WMt8IGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFvE2CGEfC3wuS16sDcWA" keySequence="CTRL+=" command="_WMtUm2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFvFWCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+N" command="_WMstv2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFvG2CGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+Z" command="_WMtVRmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFvImCGEfC3wuS16sDcWA" keySequence="CTRL+_" command="_WMstm2CGEfC3wuS16sDcWA">
      <parameters xmi:id="_WNFvI2CGEfC3wuS16sDcWA" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_WNGWJGCGEfC3wuS16sDcWA" keySequence="CTRL+{" command="_WMstm2CGEfC3wuS16sDcWA">
      <parameters xmi:id="_WNGWJWCGEfC3wuS16sDcWA" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_WNGWKmCGEfC3wuS16sDcWA" keySequence="CTRL+DEL" command="_WMt79WCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNGWMWCGEfC3wuS16sDcWA" keySequence="ALT+ARROW_LEFT" command="_WMqRkWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNGWM2CGEfC3wuS16sDcWA" keySequence="ALT+ARROW_RIGHT" command="_WMtUmGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNG9M2CGEfC3wuS16sDcWA" keySequence="SHIFT+F2" command="_WMtVtWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNG9NWCGEfC3wuS16sDcWA" keySequence="SHIFT+F5" command="_WMtVA2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNG9NmCGEfC3wuS16sDcWA" keySequence="ALT+F7" command="_WMtVhmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNG9OWCGEfC3wuS16sDcWA" keySequence="ALT+F5" command="_WMtU6WCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNG9PGCGEfC3wuS16sDcWA" keySequence="F11" command="_WMt8EGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNG9PWCGEfC3wuS16sDcWA" keySequence="F12" command="_WMtWX2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNG9RWCGEfC3wuS16sDcWA" keySequence="F2" command="_WMqRcGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNG9SGCGEfC3wuS16sDcWA" keySequence="F3" command="_WMsGmGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNHkQ2CGEfC3wuS16sDcWA" keySequence="F4" command="_WMqRemCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNHkSmCGEfC3wuS16sDcWA" keySequence="F5" command="_WMtUomCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNHkTWCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+F7" command="_WMt8E2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNHkTmCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+F8" command="_WMstmmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNHkUmCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+F9" command="_WMtU5WCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNHkVWCGEfC3wuS16sDcWA" keySequence="ALT+CTRL+ARROW_LEFT" command="_WMt7wWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILUGCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+F11" command="_WMtVQ2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILUmCGEfC3wuS16sDcWA" keySequence="ALT+CTRL+ARROW_RIGHT" command="_WMrfd2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILVGCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+F12" command="_WMqRS2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILVmCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+F4" command="_WMstxGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILWmCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+F6" command="_WMtV0WCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILYmCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+X J" command="_WMtWAGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILZGCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+X M" command="_WMtU-WCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILZWCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+X A" command="_WMqRa2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILZmCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+X E" command="_WMtV22CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILZ2CGEfC3wuS16sDcWA" keySequence="CTRL+F7" command="_WMtVWmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILbWCGEfC3wuS16sDcWA" keySequence="CTRL+F8" command="_WMsGomCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyZWCGEfC3wuS16sDcWA" keySequence="CTRL+F9" command="_WMrfhWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIya2CGEfC3wuS16sDcWA" keySequence="CTRL+F11" command="_WMt722CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIycGCGEfC3wuS16sDcWA" keySequence="CTRL+F12" command="_WMrfb2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyeWCGEfC3wuS16sDcWA" keySequence="CTRL+F4" command="_WMtVLGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyfWCGEfC3wuS16sDcWA" keySequence="CTRL+F6" command="_WMrfYmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyfmCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+F7" command="_WMtWEGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyf2CGEfC3wuS16sDcWA" keySequence="ALT+CTRL+X G" command="_WMt8C2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZcmCGEfC3wuS16sDcWA" keySequence="SHIFT+DEL" command="_WMstlGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZdGCGEfC3wuS16sDcWA" keySequence="ALT+CTRL+SHIFT+ARROW_UP" command="_WMtVsGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZdWCGEfC3wuS16sDcWA" keySequence="ALT+CTRL+SHIFT+ARROW_DOWN" command="_WMt8WmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZdmCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+X X" command="_WMtVcmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZd2CGEfC3wuS16sDcWA" keySequence="ALT+CTRL+SHIFT+ARROW_RIGHT" command="_WMtVm2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZeGCGEfC3wuS16sDcWA" keySequence="CTRL+BREAK" command="_WMq4WGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZeWCGEfC3wuS16sDcWA" keySequence="ALT+X" command="_WMtVRWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZe2CGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+X O" command="_WMtVtmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZfmCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+X P" command="_WMt8KGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZf2CGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_WMtVxmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZgWCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+X Q" command="_WMsGemCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZgmCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+X R" command="_WMtVIGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZg2CGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+X T" command="_WMtU72CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZhGCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_WMstnWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZhmCGEfC3wuS16sDcWA" keySequence="ALT+CTRL+SHIFT+F12" command="_WMt8ImCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZh2CGEfC3wuS16sDcWA" keySequence="DEL" command="_WMrfaGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZimCGEfC3wuS16sDcWA" keySequence="ALT+C" command="_WMstqWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKAgmCGEfC3wuS16sDcWA" keySequence="ALT+?" command="_WMstl2CGEfC3wuS16sDcWA">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_WNKAiWCGEfC3wuS16sDcWA" keySequence="ALT+V" command="_WMtU-GCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKnkGCGEfC3wuS16sDcWA" keySequence="ALT+-" command="_WMtVbGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKnkmCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+E E" command="_WMqRk2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKnk2CGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+E G" command="_WMstzGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKnlGCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+E J" command="_WMqRZmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKnlmCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+E S" command="_WMtU82CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKnl2CGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+E T" command="_WMqRimCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKnmGCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+E L" command="_WMqRbmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKnmWCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+E N" command="_WMt8PWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKnmmCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+E P" command="_WMpqOGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKnnWCGEfC3wuS16sDcWA" keySequence="ALT+CR" command="_WMtWR2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKnnmCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+E R" command="_WMsGiGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKnn2CGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+D E" command="_WMt8ZGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKnoGCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+D A" command="_WMtWd2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKnoWCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+D R" command="_WMtVImCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKnomCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+D T" command="_WMpqS2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKno2CGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+D X" command="_WMtVF2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKnpGCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+D J" command="_WMtWLmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKnpWCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+D O" command="_WMtVX2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKnpmCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+D P" command="_WMt7qWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKnp2CGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+D Q" command="_WMtVCmCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WM_oc2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" bindingContext="_WMwYEWCGEfC3wuS16sDcWA">
    <bindings xmi:id="_WM_odGCGEfC3wuS16sDcWA" keySequence="CTRL+CR" command="_WMstp2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND56mCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+C" command="_WMtVPmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNEg-WCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+R" command="_WMtU1mCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFIDGCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+U" command="_WMtWGmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFIGmCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+I" command="_WMtUyWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNGWLGCGEfC3wuS16sDcWA" keySequence="ALT+ARROW_UP" command="_WMtV52CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNGWL2CGEfC3wuS16sDcWA" keySequence="ALT+ARROW_DOWN" command="_WMstrWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNGWN2CGEfC3wuS16sDcWA" keySequence="SHIFT+INSERT" command="_WMrfZGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNG9P2CGEfC3wuS16sDcWA" keySequence="INSERT" command="_WMtUv2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNHkRWCGEfC3wuS16sDcWA" keySequence="F4" command="_WMqRiWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILa2CGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+ARROW_UP" command="_WMtWTmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyY2CGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+ARROW_DOWN" command="_WMtU0GCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNAPgmCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.sqltools.SQLEditorScope" bindingContext="_WMwX6GCGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNAPg2CGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+P" command="_WMtWLGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNA2omCGEfC3wuS16sDcWA" keySequence="ALT+CTRL+D" command="_WMstsGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCEsmCGEfC3wuS16sDcWA" keySequence="CTRL+/" command="_WMqRRmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCEvmCGEfC3wuS16sDcWA" keySequence="ALT+CTRL+R" command="_WMt72mCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNGWIWCGEfC3wuS16sDcWA" keySequence="ALT+CTRL+X" command="_WMtVJWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZemCGEfC3wuS16sDcWA" keySequence="ALT+X" command="_WMt8aGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZi2CGEfC3wuS16sDcWA" keySequence="ALT+C" command="_WMsthWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKAhGCGEfC3wuS16sDcWA" keySequence="ALT+Q" command="_WMt7p2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKAhmCGEfC3wuS16sDcWA" keySequence="ALT+S" command="_WMtU_GCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNAPhGCGEfC3wuS16sDcWA" elementId="org.eclipse.emf.codegen.ui.jetEditorScope" bindingContext="_WMwX-GCGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNAPhWCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+P" command="_WMpqRmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCrymCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+F" command="_WMt8K2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNEg9GCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+R" command="_WMtUy2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFIAWCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+S" command="_WMtU3GCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFIBGCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+T" command="_WMt8XWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFvEGCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+L" command="_WMsGmmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILaGCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+ARROW_UP" command="_WMtVCWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyYGCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+ARROW_DOWN" command="_WMrfcWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyZmCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+ARROW_LEFT" command="_WMtUoGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIybGCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_WMq4WWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyb2CGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+PAGE_UP" command="_WMtWSWCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNAPhmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.javaEditorScope" bindingContext="_WMwX62CGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNAPh2CGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+P" command="_WMtWD2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNA2nWCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+T" command="_WMstxWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNBdqGCGEfC3wuS16sDcWA" keySequence="CTRL+7" command="_WMtVcGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNBdvGCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+M" command="_WMsGq2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCEs2CGEfC3wuS16sDcWA" keySequence="CTRL+/" command="_WMtVcGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCEwWCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+C" command="_WMtVcGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCry2CGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+F" command="_WMt7_GCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND55mCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+B" command="_WMt8Y2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND57mCGEfC3wuS16sDcWA" keySequence="CTRL+T" command="_WMtVeWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND592CGEfC3wuS16sDcWA" keySequence="CTRL+I" command="_WMtUlmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND6BWCGEfC3wuS16sDcWA" keySequence="CTRL+O" command="_WMtU9GCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND6DWCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+/" command="_WMtVG2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNEg9mCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+R" command="_WMtVJmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFIC2CGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+U" command="_WMtWNmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFIE2CGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+'" command="_WMtVjWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFvF2CGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+O" command="_WMstuWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNGWJmCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+\" command="_WMq4X2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILW2CGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+ARROW_UP" command="_WMtVLmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILXmCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_WMtU9mCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILaWCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+ARROW_UP" command="_WMtVCWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyYWCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+ARROW_DOWN" command="_WMrfcWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyZ2CGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+ARROW_LEFT" command="_WMtUoGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIybWCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_WMq4WWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyd2CGEfC3wuS16sDcWA" keySequence="CTRL+F3" command="_WMt8NmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZkGCGEfC3wuS16sDcWA" keySequence="CTRL+2 F" command="_WMt8HmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKAj2CGEfC3wuS16sDcWA" keySequence="CTRL+2 R" command="_WMtWS2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKAkWCGEfC3wuS16sDcWA" keySequence="CTRL+2 T" command="_WMtVb2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKAkmCGEfC3wuS16sDcWA" keySequence="CTRL+2 L" command="_WMq4UWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKAlGCGEfC3wuS16sDcWA" keySequence="CTRL+2 M" command="_WMtUpmCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNAPiGCGEfC3wuS16sDcWA" elementId="org.eclipse.core.runtime.xml" bindingContext="_WMwX8WCGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNAPiWCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+P" command="_WMtWYGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCrxWCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+D" command="_WMtVGWCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNAPimCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_WMwYBmCGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNAPi2CGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+P" command="_WMtUumCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCr0mCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+G" command="_WMt71mCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNDS0GCGEfC3wuS16sDcWA" keySequence="ALT+CTRL+H" command="_WMtWaWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNEg-GCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+R" command="_WMqRcGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNHkQGCGEfC3wuS16sDcWA" keySequence="F3" command="_WMt7wmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNHkRGCGEfC3wuS16sDcWA" keySequence="F4" command="_WMpqOmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILamCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+ARROW_UP" command="_WMtVf2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyYmCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+ARROW_DOWN" command="_WMtVhGCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNAPjGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.javaEditorScope" bindingContext="_WMwX42CGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNAPjWCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+P" command="_WMtVyWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNA2n2CGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+T" command="_WMtVEWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNBdq2CGEfC3wuS16sDcWA" keySequence="CTRL+7" command="_WMtVmmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCEtWCGEfC3wuS16sDcWA" keySequence="CTRL+/" command="_WMtVmmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCrwWCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+C" command="_WMtVmmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCrz2CGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+F" command="_WMq4VWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND5-GCGEfC3wuS16sDcWA" keySequence="CTRL+I" command="_WMtU2WCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND6C2CGEfC3wuS16sDcWA" keySequence="CTRL+O" command="_WMqRU2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND6D2CGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+/" command="_WMtVD2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFIDmCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+U" command="_WMstw2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFvGGCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+O" command="_WMtUrmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNGWKGCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+\" command="_WMrfZmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILXGCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+ARROW_UP" command="_WMt8fWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILX2CGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_WMrfh2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyeGCGEfC3wuS16sDcWA" keySequence="CTRL+F3" command="_WMtVXmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKAgGCGEfC3wuS16sDcWA" keySequence="CTRL+2 F" command="_WMtUu2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKAkGCGEfC3wuS16sDcWA" keySequence="CTRL+2 R" command="_WMstgGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKAk2CGEfC3wuS16sDcWA" keySequence="CTRL+2 L" command="_WMt8N2CGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNAPjmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" bindingContext="_WMwX7WCGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNAPj2CGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+P" command="_WMtVfGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCEvGCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+A" command="_WMt8dWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCrwmCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+C" command="_WMt7-mCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCr0GCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+F" command="_WMt8SWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND54GCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+>" command="_WMtWeGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND5-WCGEfC3wuS16sDcWA" keySequence="CTRL+I" command="_WMt7s2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND6DGCGEfC3wuS16sDcWA" keySequence="CTRL+O" command="_WMtVjGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND6EGCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+/" command="_WMtVa2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNGWKWCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+\" command="_WMtV3WCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNHkQmCGEfC3wuS16sDcWA" keySequence="F3" command="_WMtVeGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILXWCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+ARROW_UP" command="_WMsGdWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILYGCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_WMtVEGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILbGCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+ARROW_UP" command="_WMtWBWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyZGCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+ARROW_DOWN" command="_WMtWHGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyaGCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+ARROW_LEFT" command="_WMqRgWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIybmCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_WMtVuGCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNA2lGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.classFileEditorScope" bindingContext="_WMwX6WCGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNA2lWCGEfC3wuS16sDcWA" keySequence="CTRL+1" command="_WMt8RWCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNA2o2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.serverViewScope" bindingContext="_WMwYBWCGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNA2pGCGEfC3wuS16sDcWA" keySequence="ALT+CTRL+D" command="_WMtV-mCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCEumCGEfC3wuS16sDcWA" keySequence="ALT+CTRL+P" command="_WMtWFmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCEv2CGEfC3wuS16sDcWA" keySequence="ALT+CTRL+R" command="_WMt8OmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCrw2CGEfC3wuS16sDcWA" keySequence="ALT+CTRL+S" command="_WMsGkWCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNBdoWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.javascriptViewScope" bindingContext="_WMwYFWCGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNBdomCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+U" command="_WMsGgWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNBdr2CGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+H" command="_WMt8d2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCr02CGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+G" command="_WMtVgmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNDS0WCGEfC3wuS16sDcWA" keySequence="ALT+CTRL+H" command="_WMtWT2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFIFmCGEfC3wuS16sDcWA" keySequence="CTRL+G" command="_WMtV92CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFIHWCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+J" command="_WMtVsWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFvHGCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+Z" command="_WMtU52CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNG9NGCGEfC3wuS16sDcWA" keySequence="SHIFT+F2" command="_WMtWa2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNHkQWCGEfC3wuS16sDcWA" keySequence="F3" command="_WMtWE2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNHkRmCGEfC3wuS16sDcWA" keySequence="F4" command="_WMtVAmCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNBdpGCGEfC3wuS16sDcWA" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_WMwX4GCGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNBdpWCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+V" command="_WMtUuGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCEw2CGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+C" command="_WMtWPGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNGWLWCGEfC3wuS16sDcWA" keySequence="ALT+ARROW_UP" command="_WMpqO2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNGWMmCGEfC3wuS16sDcWA" keySequence="ALT+ARROW_RIGHT" command="_WMt8D2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNG9MGCGEfC3wuS16sDcWA" keySequence="SHIFT+INSERT" command="_WMtUuGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIycmCGEfC3wuS16sDcWA" keySequence="CTRL+INSERT" command="_WMtWPGCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNBdqWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.propertiesEditorScope" bindingContext="_WMwYGWCGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNBdqmCGEfC3wuS16sDcWA" keySequence="CTRL+7" command="_WMtVcGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCEtGCGEfC3wuS16sDcWA" keySequence="CTRL+/" command="_WMtVcGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNCEwmCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+C" command="_WMtVcGCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNBdvWCGEfC3wuS16sDcWA" elementId="org.eclipse.jst.jsp.ui.structured.text.editor.jsp.scope" bindingContext="_WMwX_WCGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNBdvmCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+M" command="_WMstjWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNEg92CGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+R" command="_WMt8JmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFIEWCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+V" command="_WMtU9WCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNBdv2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.editors.task" bindingContext="_WMwYEmCGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNBdwGCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+M" command="_WMqRUmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND562CGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+C" command="_WMtVPmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND6B2CGEfC3wuS16sDcWA" keySequence="CTRL+O" command="_WMt8LGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNEg-mCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+R" command="_WMtU1mCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFIA2CGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+S" command="_WMsty2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFIDWCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+U" command="_WMtWGmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFIG2CGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+I" command="_WMtUyWCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNCEt2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" bindingContext="_WMwYF2CGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNCEuGCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+O" command="_WMqRX2CGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNCryGCGEfC3wuS16sDcWA" elementId="org.eclipse.ant.ui.AntEditorScope" bindingContext="_WMwX92CGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNCryWCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+F" command="_WMt7_GCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNEg82CGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+R" command="_WMqReWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFvFmCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+O" command="_WMpqR2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNG9MmCGEfC3wuS16sDcWA" keySequence="SHIFT+F2" command="_WMtVj2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNG9R2CGEfC3wuS16sDcWA" keySequence="F3" command="_WMpqTmCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNCrzWCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.pdeEditorContext" bindingContext="_WMwYA2CGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNCrzmCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+F" command="_WMq4V2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND6CmCGEfC3wuS16sDcWA" keySequence="CTRL+O" command="_WMq4bmCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNDS2WCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_WMwX52CGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNDS2mCGEfC3wuS16sDcWA" keySequence="ALT+CTRL+M" command="_WMtU7GCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNDS22CGEfC3wuS16sDcWA" keySequence="ALT+CTRL+N" command="_WMt8I2CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND57WCGEfC3wuS16sDcWA" keySequence="CTRL+T" command="_WMstgWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND59GCGEfC3wuS16sDcWA" keySequence="CTRL+W" command="_WMtVqWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND6AWCGEfC3wuS16sDcWA" keySequence="CTRL+N" command="_WMtV12CGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WND55GCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.debugging" bindingContext="_WMwYB2CGEfC3wuS16sDcWA">
    <bindings xmi:id="_WND55WCGEfC3wuS16sDcWA" keySequence="CTRL+R" command="_WMtVXWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNG9OmCGEfC3wuS16sDcWA" keySequence="F7" command="_WMt8PmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNG9O2CGEfC3wuS16sDcWA" keySequence="F8" command="_WMtVmWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNHkSWCGEfC3wuS16sDcWA" keySequence="F5" command="_WMqRfmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNHkS2CGEfC3wuS16sDcWA" keySequence="F6" command="_WMtU3WCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIydmCGEfC3wuS16sDcWA" keySequence="CTRL+F2" command="_WMtWZmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyemCGEfC3wuS16sDcWA" keySequence="CTRL+F5" command="_WMt8CmCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WND552CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" bindingContext="_WMwX6mCGEfC3wuS16sDcWA">
    <bindings xmi:id="_WND56GCGEfC3wuS16sDcWA" keySequence="ALT+SHIFT+B" command="_WMt8Y2CGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WND5_GCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_WMwYCGCGEfC3wuS16sDcWA">
    <bindings xmi:id="_WND5_WCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+," command="_WMt7xWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WND6AGCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+." command="_WMtWVWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNFIFGCGEfC3wuS16sDcWA" keySequence="CTRL+G" command="_WMtWVmCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WND6A2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.DiffViewer" bindingContext="_WMwX5GCGEfC3wuS16sDcWA">
    <bindings xmi:id="_WND6BGCGEfC3wuS16sDcWA" keySequence="CTRL+O" command="_WMtVCGCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WND6CGCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" bindingContext="_WMwYFGCGEfC3wuS16sDcWA">
    <bindings xmi:id="_WND6CWCGEfC3wuS16sDcWA" keySequence="CTRL+O" command="_WMqRX2CGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNEg_mCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_WMwYGmCGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNEg_2CGEfC3wuS16sDcWA" keySequence="CTRL+C" command="_WMstiWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIygmCGEfC3wuS16sDcWA" keySequence="CTRL+ARROW_LEFT" command="_WMq4W2CGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNEhAGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_WMwYDGCGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNEhAWCGEfC3wuS16sDcWA" keySequence="CTRL+C" command="_WMrfY2CGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNFvH2CGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.console" bindingContext="_WMwYAGCGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNFvIGCGEfC3wuS16sDcWA" keySequence="CTRL+Z" command="_WMt8L2CGEfC3wuS16sDcWA">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_WNG9QWCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" bindingContext="_WMwYE2CGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNG9QmCGEfC3wuS16sDcWA" keySequence="F1" command="_WMpqTGCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNG9Q2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" bindingContext="_WMwYG2CGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNG9RGCGEfC3wuS16sDcWA" keySequence="F2" command="_WMrfamCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNHkR2CGEfC3wuS16sDcWA" elementId="org.eclipse.buildship.ui.contexts.taskview" bindingContext="_WMwYFmCGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNHkSGCGEfC3wuS16sDcWA" keySequence="F5" command="_WMt7-WCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNHkUGCGEfC3wuS16sDcWA" elementId="org.eclipse.jst.pagedesigner.editorContext" bindingContext="_WMwX-2CGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNHkUWCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+F9" command="_WMtU22CGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNHkVGCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+F10" command="_WMt8MGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILUWCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+F11" command="_WMt8DmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILU2CGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+F12" command="_WMt8QmCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNILWWCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+F5" command="_WMtWEGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIyfGCGEfC3wuS16sDcWA" keySequence="CTRL+F5" command="_WMtVhmCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNILV2CGEfC3wuS16sDcWA" elementId="org.eclipse.jst.jsf.facesconfig.editorContext" bindingContext="_WMwYHGCGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNILWGCGEfC3wuS16sDcWA" keySequence="CTRL+SHIFT+F5" command="_WMtWEGCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNIye2CGEfC3wuS16sDcWA" keySequence="CTRL+F5" command="_WMtVhmCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNIydGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.console.ConsoleView" bindingContext="_WMwYBGCGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNIydWCGEfC3wuS16sDcWA" keySequence="CTRL+INSERT" command="_WMtVwmCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNJZfGCGEfC3wuS16sDcWA" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_WMwYDWCGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNJZfWCGEfC3wuS16sDcWA" keySequence="ALT+Y" command="_WMtVaWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZiGCGEfC3wuS16sDcWA" keySequence="ALT+A" command="_WMtVaWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZiWCGEfC3wuS16sDcWA" keySequence="ALT+B" command="_WMtVaWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZjGCGEfC3wuS16sDcWA" keySequence="ALT+C" command="_WMtVaWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZjWCGEfC3wuS16sDcWA" keySequence="ALT+D" command="_WMtVaWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZjmCGEfC3wuS16sDcWA" keySequence="ALT+E" command="_WMtVaWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNJZj2CGEfC3wuS16sDcWA" keySequence="ALT+F" command="_WMtVaWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKAgWCGEfC3wuS16sDcWA" keySequence="ALT+G" command="_WMtVaWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKAg2CGEfC3wuS16sDcWA" keySequence="ALT+P" command="_WMtVaWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKAhWCGEfC3wuS16sDcWA" keySequence="ALT+R" command="_WMtVaWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKAh2CGEfC3wuS16sDcWA" keySequence="ALT+S" command="_WMtVaWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKAiGCGEfC3wuS16sDcWA" keySequence="ALT+T" command="_WMtVaWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKAimCGEfC3wuS16sDcWA" keySequence="ALT+V" command="_WMtVaWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKAi2CGEfC3wuS16sDcWA" keySequence="ALT+W" command="_WMtVaWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKAjGCGEfC3wuS16sDcWA" keySequence="ALT+H" command="_WMtVaWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKAjWCGEfC3wuS16sDcWA" keySequence="ALT+L" command="_WMtVaWCGEfC3wuS16sDcWA"/>
    <bindings xmi:id="_WNKAjmCGEfC3wuS16sDcWA" keySequence="ALT+N" command="_WMtVaWCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WNKnm2CGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.BreakpointView" bindingContext="_WMwX4WCGEfC3wuS16sDcWA">
    <bindings xmi:id="_WNKnnGCGEfC3wuS16sDcWA" keySequence="ALT+CR" command="_WMtVSmCGEfC3wuS16sDcWA"/>
  </bindingTables>
  <bindingTables xmi:id="_WcrDMWCGEfC3wuS16sDcWA" bindingContext="_WcrDMGCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcrDM2CGEfC3wuS16sDcWA" bindingContext="_WcrDMmCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcrDNWCGEfC3wuS16sDcWA" bindingContext="_WcrDNGCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcrDN2CGEfC3wuS16sDcWA" bindingContext="_WcrDNmCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcrqQWCGEfC3wuS16sDcWA" bindingContext="_WcrqQGCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcrqQ2CGEfC3wuS16sDcWA" bindingContext="_WcrqQmCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcrqRWCGEfC3wuS16sDcWA" bindingContext="_WcrqRGCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcrqR2CGEfC3wuS16sDcWA" bindingContext="_WcrqRmCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcrqSWCGEfC3wuS16sDcWA" bindingContext="_WcrqSGCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcrqS2CGEfC3wuS16sDcWA" bindingContext="_WcrqSmCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcrqTWCGEfC3wuS16sDcWA" bindingContext="_WcrqTGCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcrqT2CGEfC3wuS16sDcWA" bindingContext="_WcrqTmCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcrqUWCGEfC3wuS16sDcWA" bindingContext="_WcrqUGCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcrqU2CGEfC3wuS16sDcWA" bindingContext="_WcrqUmCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcrqVWCGEfC3wuS16sDcWA" bindingContext="_WcrqVGCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcrqV2CGEfC3wuS16sDcWA" bindingContext="_WcrqVmCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcrqWWCGEfC3wuS16sDcWA" bindingContext="_WcrqWGCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcsRUGCGEfC3wuS16sDcWA" bindingContext="_WcrqWmCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcsRUmCGEfC3wuS16sDcWA" bindingContext="_WcsRUWCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcsRVGCGEfC3wuS16sDcWA" bindingContext="_WcsRU2CGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcsRVmCGEfC3wuS16sDcWA" bindingContext="_WcsRVWCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcsRWGCGEfC3wuS16sDcWA" bindingContext="_WcsRV2CGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcsRWmCGEfC3wuS16sDcWA" bindingContext="_WcsRWWCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcsRXGCGEfC3wuS16sDcWA" bindingContext="_WcsRW2CGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcsRXmCGEfC3wuS16sDcWA" bindingContext="_WcsRXWCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcsRYGCGEfC3wuS16sDcWA" bindingContext="_WcsRX2CGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcsRYmCGEfC3wuS16sDcWA" bindingContext="_WcsRYWCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcsRZGCGEfC3wuS16sDcWA" bindingContext="_WcsRY2CGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcsRZmCGEfC3wuS16sDcWA" bindingContext="_WcsRZWCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcsRaGCGEfC3wuS16sDcWA" bindingContext="_WcsRZ2CGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_WcsRamCGEfC3wuS16sDcWA" bindingContext="_WcsRaWCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_Wcs4YGCGEfC3wuS16sDcWA" bindingContext="_WcsRa2CGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_Wcs4YmCGEfC3wuS16sDcWA" bindingContext="_Wcs4YWCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_Wcs4ZGCGEfC3wuS16sDcWA" bindingContext="_Wcs4Y2CGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_Wcs4ZmCGEfC3wuS16sDcWA" bindingContext="_Wcs4ZWCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_Wcs4aGCGEfC3wuS16sDcWA" bindingContext="_Wcs4Z2CGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_Wcs4amCGEfC3wuS16sDcWA" bindingContext="_Wcs4aWCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_Wcs4bGCGEfC3wuS16sDcWA" bindingContext="_Wcs4a2CGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_Wcs4bmCGEfC3wuS16sDcWA" bindingContext="_Wcs4bWCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_Wcs4cGCGEfC3wuS16sDcWA" bindingContext="_Wcs4b2CGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_Wcs4cmCGEfC3wuS16sDcWA" bindingContext="_Wcs4cWCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_Wcs4dGCGEfC3wuS16sDcWA" bindingContext="_Wcs4c2CGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_Wcs4dmCGEfC3wuS16sDcWA" bindingContext="_Wcs4dWCGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_Wcs4eGCGEfC3wuS16sDcWA" bindingContext="_Wcs4d2CGEfC3wuS16sDcWA"/>
  <bindingTables xmi:id="_Wcs4emCGEfC3wuS16sDcWA" bindingContext="_Wcs4eWCGEfC3wuS16sDcWA"/>
  <rootContext xmi:id="_WMQoqmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_WMQoq2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.platform" name="In Windows" description="A window is open">
      <children xmi:id="_WMQorGCGEfC3wuS16sDcWA" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.platform" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_WMwX4GCGEfC3wuS16sDcWA" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_WMwX4WCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_WMwX4mCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_WMwX42CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.javaEditorScope" name="Editing JavaScript Source" description="Editing JavaScript Source Context">
          <children xmi:id="_WMwYFWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.javascriptViewScope" name="JavaScript View" description="JavaScript View Context"/>
        </children>
        <children xmi:id="_WMwX5GCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.DiffViewer" name="In Diff Viewer"/>
        <children xmi:id="_WMwX6GCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.sqltools.SQLEditorScope" name="Editing SQL" description="Editing SQL Context"/>
        <children xmi:id="_WMwX6WCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.classFileEditorScope" name="Browsing attached Java Source" description="Browsing attached Java Source Context"/>
        <children xmi:id="_WMwX62CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.javaEditorScope" name="Editing Java Source" description="Editing Java Source Context"/>
        <children xmi:id="_WMwX7WCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors">
          <children xmi:id="_WMwX7mCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xml.cleanup" name="XML Source Cleanup" description="XML Source Cleanup"/>
          <children xmi:id="_WMwX72CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.sse.comments" name="Source Comments in Structured Text Editors" description="Source Comments in Structured Text Editors"/>
          <children xmi:id="_WMwX8GCGEfC3wuS16sDcWA" elementId="org.eclipse.jst.jsp.core.jspsource" name="JSP Source" description="JSP Source"/>
          <children xmi:id="_WMwX8WCGEfC3wuS16sDcWA" elementId="org.eclipse.core.runtime.xml" name="Editing XML Source" description="Editing XML Source"/>
          <children xmi:id="_WMwX8mCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xml.occurrences" name="XML Source Occurrences" description="XML Source Occurrences"/>
          <children xmi:id="_WMwX82CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xml.grammar" name="XML Source Grammar" description="XML Source Grammar"/>
          <children xmi:id="_WMwX9GCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xml.comments" name="XML Source Comments" description="XML Source Comments"/>
          <children xmi:id="_WMwX9mCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xml.expand" name="XML Source Expand/Collapse" description="XML Source Expand/Collapse"/>
          <children xmi:id="_WMwX-WCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.sse.hideFormat" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors"/>
          <children xmi:id="_WMwX_GCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xml.selection" name="XML Source Selection" description="XML Source Selection"/>
          <children xmi:id="_WMwX_WCGEfC3wuS16sDcWA" elementId="org.eclipse.jst.jsp.ui.structured.text.editor.jsp.scope" name="Editing JSP Source" description="Editing JSP Source"/>
          <children xmi:id="_WMwYAmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xml.navigation" name="XML Source Navigation" description="XML Source Navigation"/>
          <children xmi:id="_WMwYDmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.css.core.csssource" name="Editing CSS Source" description="Editing CSS Source"/>
          <children xmi:id="_WMwYEGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.html.core.htmlsource" name="Editing HTML Source" description="Editing HTML Source"/>
          <children xmi:id="_WMwYGGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xml.dependencies" name="XML Source Dependencies" description="XML Source Dependencies"/>
          <children xmi:id="_WMwYHWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.html.occurrences" name="HTML Source Occurrences" description="HTML Source Occurrences"/>
        </children>
        <children xmi:id="_WMwX92CGEfC3wuS16sDcWA" elementId="org.eclipse.ant.ui.AntEditorScope" name="Editing Ant Buildfiles" description="Editing Ant Buildfiles Context"/>
        <children xmi:id="_WMwX-GCGEfC3wuS16sDcWA" elementId="org.eclipse.emf.codegen.ui.jetEditorScope" name="Editing JET Source" description="Editing JET Source Context"/>
        <children xmi:id="_WMwX-2CGEfC3wuS16sDcWA" elementId="org.eclipse.jst.pagedesigner.editorContext" name="Using Web Page Editor" description="Key binding context when using the web page editor"/>
        <children xmi:id="_WMwYA2CGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.pdeEditorContext" name="PDE editor" description="The context used by PDE editors"/>
        <children xmi:id="_WMwYBmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_WMwYD2CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xsd.ui.text.editor.context" name="Editing XSD context"/>
        <children xmi:id="_WMwYEmCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.editors.task" name="In Tasks Editor"/>
        <children xmi:id="_WMwYE2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context">
          <children xmi:id="_WMwYFGCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context"/>
          <children xmi:id="_WMwYF2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" name="Task Markup Editor Source Context"/>
        </children>
        <children xmi:id="_WMwYGWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.propertiesEditorScope" name="Editing Properties Files" description="Editing Properties Files Context"/>
        <children xmi:id="_WMwYHGCGEfC3wuS16sDcWA" elementId="org.eclipse.jst.jsf.facesconfig.editorContext" name="In Faces Config Editor" description="Key binding context when using the Faces Config Editor"/>
      </children>
      <children xmi:id="_WMwX5WCGEfC3wuS16sDcWA" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_WMwX52CGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_WMwX7GCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.sqltools.schemaobjecteditor.schemaediting" name="Schema Object Editor" description="Schema Object Editor"/>
      <children xmi:id="_WMwYAGCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_WMwYAWCGEfC3wuS16sDcWA" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" name="In Terminal View" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_WMwYBGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_WMwYBWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.serverViewScope" name="In Servers View" description="In Servers View"/>
      <children xmi:id="_WMwYB2CGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_WMwYCGCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_WMwYCWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xsl.debug.ui.context" name="XSLT Debugging" description="Context for debugging XSLT"/>
        <children xmi:id="_WMwYCmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.debug.ui.debugging" name="Debugging Java" description="Debugging Java programs"/>
      </children>
      <children xmi:id="_WMwYDGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_WMwYDWCGEfC3wuS16sDcWA" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_WMwYEWCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" name="In Tasks View"/>
      <children xmi:id="_WMwYFmCGEfC3wuS16sDcWA" elementId="org.eclipse.buildship.ui.contexts.taskview" name="In Gradle Tasks View" description="This context is activated when the Gradle Tasks view is in focus"/>
      <children xmi:id="_WMwYGmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View">
        <children xmi:id="_WMwYG2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" name="In Git Repositories View"/>
      </children>
    </children>
    <children xmi:id="_WMQorWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs" description="A dialog is open"/>
  </rootContext>
  <rootContext xmi:id="_WMwX5mCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_WMwX6mCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" name="Editor Breadcrumb Navigation" description="Editor Breadcrumb Navigation Context"/>
  <rootContext xmi:id="_WMwX9WCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xsd.ui.editor.sourceView" name="XSD Editor Source View" description="XSD Editor Source View"/>
  <rootContext xmi:id="_WMwX-mCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.wsdl.ui.editor.sourceView" name="WSDL Editor Source View" description="WSDL Editor Source View"/>
  <rootContext xmi:id="_WMwX_mCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xsd.ui.editor.designView" name="XSD Editor Design View" description="XSD Editor Design View"/>
  <rootContext xmi:id="_WMwX_2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_WMwYC2CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.wsdl.ui.editor.designView" name="WSDL Editor Design View" description="WSDL Editor Design View"/>
  <rootContext xmi:id="_WcrDMGCGEfC3wuS16sDcWA" elementId="org.eclipse.ant.ui.actionSet.presentation" name="Auto::org.eclipse.ant.ui.actionSet.presentation"/>
  <rootContext xmi:id="_WcrDMmCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.sqltools.sqlscrapbook.actionSet" name="Auto::org.eclipse.datatools.sqltools.sqlscrapbook.actionSet"/>
  <rootContext xmi:id="_WcrDNGCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_WcrDNmCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_WcrqQGCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_WcrqQmCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_WcrqRGCGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui.CoverageActionSet" name="Auto::org.eclipse.eclemma.ui.CoverageActionSet"/>
  <rootContext xmi:id="_WcrqRmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_WcrqSGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_WcrqSmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.SearchActionSet" name="Auto::org.eclipse.egit.ui.SearchActionSet"/>
  <rootContext xmi:id="_WcrqTGCGEfC3wuS16sDcWA" elementId="org.eclipse.emf.codegen.ui.jet.actionSet" name="Auto::org.eclipse.emf.codegen.ui.jet.actionSet"/>
  <rootContext xmi:id="_WcrqTmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.debug.ui.JDTDebugActionSet" name="Auto::org.eclipse.jdt.debug.ui.JDTDebugActionSet"/>
  <rootContext xmi:id="_WcrqUGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.junit.JUnitActionSet" name="Auto::org.eclipse.jdt.junit.JUnitActionSet"/>
  <rootContext xmi:id="_WcrqUmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.text.java.actionSet.presentation" name="Auto::org.eclipse.jdt.ui.text.java.actionSet.presentation"/>
  <rootContext xmi:id="_WcrqVGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet" name="Auto::org.eclipse.jdt.ui.JavaElementCreationActionSet"/>
  <rootContext xmi:id="_WcrqVmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.JavaActionSet" name="Auto::org.eclipse.jdt.ui.JavaActionSet"/>
  <rootContext xmi:id="_WcrqWGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.A_OpenActionSet" name="Auto::org.eclipse.jdt.ui.A_OpenActionSet"/>
  <rootContext xmi:id="_WcrqWmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.CodingActionSet" name="Auto::org.eclipse.jdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_WcsRUWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.SearchActionSet" name="Auto::org.eclipse.jdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_WcsRU2CGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jpa.ui.actionSet.jpaElementCreation" name="Auto::org.eclipse.jpt.jpa.ui.actionSet.jpaElementCreation"/>
  <rootContext xmi:id="_WcsRVWCGEfC3wuS16sDcWA" elementId="org.eclipse.jst.j2ee.J2eeMainActionSet" name="Auto::org.eclipse.jst.j2ee.J2eeMainActionSet"/>
  <rootContext xmi:id="_WcsRV2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.context.ui.actionSet" name="Auto::org.eclipse.mylyn.context.ui.actionSet"/>
  <rootContext xmi:id="_WcsRWWCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.java.actionSet" name="Auto::org.eclipse.mylyn.java.actionSet"/>
  <rootContext xmi:id="_WcsRW2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.java.actionSet.browsing" name="Auto::org.eclipse.mylyn.java.actionSet.browsing"/>
  <rootContext xmi:id="_WcsRXWCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.navigation" name="Auto::org.eclipse.mylyn.tasks.ui.navigation"/>
  <rootContext xmi:id="_WcsRX2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.navigation.additions" name="Auto::org.eclipse.mylyn.tasks.ui.navigation.additions"/>
  <rootContext xmi:id="_WcsRYWCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.SearchActionSet" name="Auto::org.eclipse.pde.ui.SearchActionSet"/>
  <rootContext xmi:id="_WcsRY2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_WcsRZWCGEfC3wuS16sDcWA" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_WcsRZ2CGEfC3wuS16sDcWA" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_WcsRaWCGEfC3wuS16sDcWA" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_WcsRa2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_Wcs4YWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_Wcs4Y2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_Wcs4ZWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_Wcs4Z2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_Wcs4aWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_Wcs4a2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_Wcs4bWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_Wcs4b2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_Wcs4cWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_Wcs4c2CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.server.ui.new.actionSet" name="Auto::org.eclipse.wst.server.ui.new.actionSet"/>
  <rootContext xmi:id="_Wcs4dWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.server.ui.internal.webbrowser.actionSet" name="Auto::org.eclipse.wst.server.ui.internal.webbrowser.actionSet"/>
  <rootContext xmi:id="_Wcs4d2CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.web.ui.wizardsActionSet" name="Auto::org.eclipse.wst.web.ui.wizardsActionSet"/>
  <rootContext xmi:id="_Wcs4eWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.ws.explorer.explorer" name="Auto::org.eclipse.wst.ws.explorer.explorer"/>
  <descriptors xmi:id="_WO_MgGCGEfC3wuS16sDcWA" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_WY1sEGCGEfC3wuS16sDcWA" elementId="org.eclipse.ant.ui.views.AntView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" category="Ant" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Ant</tags>
  </descriptors>
  <descriptors xmi:id="_WY2TIGCGEfC3wuS16sDcWA" elementId="org.eclipse.buildship.ui.views.taskview" label="Gradle Tasks" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/tasks_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.task.TaskView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_WY2TIWCGEfC3wuS16sDcWA" elementId="org.eclipse.buildship.ui.views.executionview" label="Gradle Executions" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/executions_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.execution.ExecutionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_WY26MGCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.connectivity.DataSourceExplorerNavigator" label="Data Source Explorer" iconURI="platform:/plugin/org.eclipse.datatools.connectivity.ui.dse/icons/full/cview16/enterprise_explorer.gif" tooltip="" category="Data Management" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.datatools.connectivity.ui.dse.views.DataSourceExplorerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.datatools.connectivity.ui.dse"/>
    <tags>View</tags>
    <tags>categoryTag:Data Management</tags>
  </descriptors>
  <descriptors xmi:id="_WY26MWCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.sqltools.plan.planView" label="Execution Plan" iconURI="platform:/plugin/org.eclipse.datatools.sqltools.plan/icons/sqlplan.gif" tooltip="" category="Data Management" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.datatools.sqltools.plan.internal.ui.view.PlanView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.datatools.sqltools.plan"/>
    <tags>View</tags>
    <tags>categoryTag:Data Management</tags>
  </descriptors>
  <descriptors xmi:id="_WY26MmCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.sqltools.result.resultView" label="SQL Results" iconURI="platform:/plugin/org.eclipse.datatools.sqltools.result.ui/icons/sqlresult.gif" tooltip="" category="Data Management" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.datatools.sqltools.result.internal.ui.view.ResultsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.datatools.sqltools.result.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Data Management</tags>
  </descriptors>
  <descriptors xmi:id="_WY3hQGCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WY3hQWCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WY3hQmCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WY3hQ2CGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WY4IUGCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WY4IUWCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WY4IUmCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WY4IU2CGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WY4IVGCGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui.CoverageView" label="Coverage" iconURI="platform:/plugin/org.eclipse.eclemma.ui/icons/full/eview16/coverage.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.eclemma.internal.ui.coverageview.CoverageView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.eclemma.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_WY4vYGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_WY4vYWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_WY4vYmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.rebase.RebaseInteractiveView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_WY4vY2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.dialogs.CompareTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_WY5WcGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.reflog.ReflogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_WY5WcWCGEfC3wuS16sDcWA" elementId="org.eclipse.gef.ui.palette_view" label="Palette" iconURI="platform:/plugin/org.eclipse.gef/icons/palette_view.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.gef.ui.views.palette.PaletteView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.gef"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WY5WcmCGEfC3wuS16sDcWA" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_WY5Wc2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" label="Bytecode" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/bytecodeview.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeOutlineView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_WY59gGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.bcoview.views.BytecodeReferenceView" label="Bytecode Reference" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/reference.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeReferenceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_WY59gWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.debug.ui.DisplayView" label="Debug Shell" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.debug.ui.display.DisplayView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WY59gmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.junit.ResultView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_WY59g2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.PackageExplorer" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_WY6kkGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.TypeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_WY6kkWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.ProjectsView" label="Projects" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/projects.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.ProjectsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_WY6kkmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.PackagesView" label="Packages" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/packages.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.PackagesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_WY6kk2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.TypesView" label="Types" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/types.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.TypesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_WY7LoGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.MembersView" label="Members" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/members.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.MembersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_WY7LoWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/call_hierarchy.png" tooltip="" allowMultiple="true" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.callhierarchy.CallHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_WY7LomCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WY7Lo2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_WY7LpGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.JavadocView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_WY7LpWCGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.ui.jpaStructureView" label="JPA Structure" iconURI="platform:/plugin/org.eclipse.jpt.jpa.ui/images/views/jpa-structure.gif" tooltip="" category="JPA" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jpt.jpa.ui.internal.views.JpaStructureView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jpt.jpa.ui"/>
    <tags>View</tags>
    <tags>categoryTag:JPA</tags>
  </descriptors>
  <descriptors xmi:id="_WY7ysGCGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.ui.jpaDetailsView" label="JPA Details" iconURI="platform:/plugin/org.eclipse.jpt.jpa.ui/images/views/jpa-details.gif" tooltip="" category="JPA" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jpt.jpa.ui.internal.views.JpaDetailsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jpt.jpa.ui"/>
    <tags>View</tags>
    <tags>categoryTag:JPA</tags>
  </descriptors>
  <descriptors xmi:id="_WY7ysWCGEfC3wuS16sDcWA" elementId="org.eclipse.jst.jsf.ui.component.ComponentTreeView" label="JSF Component Tree" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="JavaServer Faces" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jst.jsf.ui.internal.component.ComponentTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jst.jsf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:JavaServer Faces</tags>
  </descriptors>
  <descriptors xmi:id="_WY7ysmCGEfC3wuS16sDcWA" elementId="org.eclipse.jst.jsf.ui.tagregistry.TagRegistryView" label="Tag Registry" iconURI="platform:/plugin/org.eclipse.jst.jsf.ui/icons/obj16/library_obj.gif" tooltip="" category="JavaServer Faces" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jst.jsf.ui.internal.tagregistry.TagRegistryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jst.jsf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:JavaServer Faces</tags>
  </descriptors>
  <descriptors xmi:id="_WY8ZwGCGEfC3wuS16sDcWA" elementId="org.eclipse.jst.ws.jaxws.ui.views.AnnotationsView" label="Annotation Properties" iconURI="platform:/plugin/org.eclipse.jst.ws.jaxws.ui/icons/eview16/prop_ps.gif" tooltip="" category="Web Services" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jst.ws.internal.jaxws.ui.views.AnnotationsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jst.ws.jaxws.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Web Services</tags>
  </descriptors>
  <descriptors xmi:id="_WY8ZwWCGEfC3wuS16sDcWA" elementId="org.eclipse.lsp4e.callHierarchy.callHierarchyView" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/call_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.callhierarchy.CallHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_WY8ZwmCGEfC3wuS16sDcWA" elementId="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/type_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_WY8Zw2CGEfC3wuS16sDcWA" elementId="org.eclipse.lsp4e.ui.languageServersView" label="Language Servers" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.ui.LanguageServersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_WY9A0GCGEfC3wuS16sDcWA" elementId="org.eclipse.m2e.core.views.MavenRepositoryView" label="Maven Repositories" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/maven_indexes.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenRepositoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_WY9A0WCGEfC3wuS16sDcWA" elementId="org.eclipse.m2e.core.views.MavenLifecycleMappingsView" label="Maven Lifecycle Mappings" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/main_tab.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenLifecycleMappingsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_WY9A0mCGEfC3wuS16sDcWA" elementId="org.eclipse.m2e.core.views.MavenBuild" label="Maven Workspace Build" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.build.BuildDebugView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_WY9A02CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.builds.navigator.builds" label="Builds" iconURI="platform:/plugin/org.eclipse.mylyn.builds.ui/icons/eview16/build-view.png" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.builds.ui.view.BuildsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.builds.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_WY9A1GCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.commons.identity.ui.navigator.People" label="People" iconURI="platform:/plugin/org.eclipse.mylyn.commons.identity.ui/icons/obj16/people.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.identity.ui.PeopleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.identity.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_WY9A1WCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.commons.repositories.ui.navigator.Repositories" label="Team Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.commons.repositories.ui/icons/eview16/repositories.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.repositories.ui.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.repositories.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_WY9n4GCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.reviews.Explorer" label="Review" iconURI="platform:/plugin/org.eclipse.mylyn.reviews.ui/icons/obj16/review.png" tooltip="View artifacts and comments associated with reviews." category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.reviews.ui.views.ReviewExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.reviews.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_WY9n4WCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" allowMultiple="true" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_WY9n4mCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.views.repositories" label="Task Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/repositories.png" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskRepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_WY9n42CGEfC3wuS16sDcWA" elementId="org.eclipse.oomph.p2.ui.RepositoryExplorer" label="Repository Explorer" iconURI="platform:/plugin/org.eclipse.oomph.p2.ui/icons/obj16/repository.gif" tooltip="" category="Oomph" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.oomph.p2.internal.ui.RepositoryExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.oomph.p2.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Oomph</tags>
  </descriptors>
  <descriptors xmi:id="_WY9n5GCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.api.tools.ui.views.apitooling.views.apitoolingview" label="API Tools" iconURI="platform:/plugin/org.eclipse.pde.api.tools.ui/icons/full/obj16/api_tools.png" tooltip="" category="API Tools" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.api.tools.ui.internal.views.APIToolingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.api.tools.ui"/>
    <tags>View</tags>
    <tags>categoryTag:API Tools</tags>
  </descriptors>
  <descriptors xmi:id="_WY9n5WCGEfC3wuS16sDcWA" elementId="pde.bnd.ui.repositoriesView" label="Bundle Repositories" iconURI="platform:/plugin/org.eclipse.pde.bnd.ui/icons/database.png" tooltip="" category="OSGi" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.bnd.ui.views.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.bnd.ui"/>
    <tags>View</tags>
    <tags>categoryTag:OSGi</tags>
  </descriptors>
  <descriptors xmi:id="_WY-O8GCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.runtime.RegistryBrowser" label="Plug-in Registry" iconURI="platform:/plugin/org.eclipse.pde.runtime/icons/eview16/registry.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.runtime.registry.RegistryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.runtime"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_WY-O8WCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.PluginsView" label="Plug-ins" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/eview16/plugin_depend.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.plugins.PluginsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_WY-O8mCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.FeaturesView" label="Features" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/feature_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.features.FeaturesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_WY-2AGCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.DependenciesView" label="Plug-in Dependencies" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/req_plugins_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.dependencies.DependenciesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_WY-2AWCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.TargetPlatformState" label="Target Platform State" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/target_profile_xml_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.target.TargetStateView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_WY-2AmCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.ImageBrowserView" label="Plug-in Image Browser" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/psearch_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.imagebrowser.ImageBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_WY-2A2CGEfC3wuS16sDcWA" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WY_dEGCGEfC3wuS16sDcWA" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_WY_dEWCGEfC3wuS16sDcWA" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_WY_dEmCGEfC3wuS16sDcWA" elementId="org.eclipse.tips.ide.tipPart" label="Tip of the Day" iconURI="platform:/plugin/org.eclipse.tips.ui/icons/lightbulb.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.tips.ide/org.eclipse.tips.ide.internal.TipPart">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_WZAEIGCGEfC3wuS16sDcWA" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_WZAEIWCGEfC3wuS16sDcWA" elementId="org.eclipse.tcf.te.ui.terminals.TerminalsView" label="Terminals (Old)" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.OldTerminalsViewHandler"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_WZAEImCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZAEI2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZAEJGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_WZAEJWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZArMGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZArMWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZArMmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZBSQGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZBSQWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZBSQmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZBSQ2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZB5UGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZB5UWCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZB5UmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZB5U2CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.common.snippets.internal.ui.SnippetsView" label="Snippets" iconURI="platform:/plugin/org.eclipse.wst.common.snippets/icons/snippets_view.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.common.snippets.internal.ui.SnippetsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.common.snippets"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_WZCgYGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.internet.monitor.view" label="TCP/IP Monitor" iconURI="platform:/plugin/org.eclipse.wst.internet.monitor.ui/icons/cview16/monitorView.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.internet.monitor.ui.internal.view.MonitorView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.internet.monitor.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_WZCgYWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/call_hierarchy.gif" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.jsdt.internal.ui.callhierarchy.CallHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.jsdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_WZDHcGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/source.gif" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.jsdt.internal.ui.infoviews.SourceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.jsdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_WZDHcWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.JavadocView" label="Documentation" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/javadoc.gif" tooltip="JavaScript Documentation" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.jsdt.internal.ui.infoviews.JavadocView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.jsdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_WZDHcmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.server.ui.ServersView" label="Servers" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" tooltip="" category="Server" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.server.ui.internal.cnf.ServersView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.server.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Server</tags>
  </descriptors>
  <descriptors xmi:id="_WZDHc2CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xml.ui.views.annotations.XMLAnnotationsView" label="Documentation" iconURI="platform:/plugin/org.eclipse.wst.xml.ui/icons/full/obj16/comment_obj.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.xml.ui.internal.views.annotations.XMLAnnotationsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_WZDHdGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xml.ui.contentmodel.view" label="Content Model" iconURI="platform:/plugin/org.eclipse.wst.xml.ui/icons/full/view16/hierarchy.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.xml.ui.internal.views.contentmodel.ContentModelView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_WZDugGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xml.views.XPathView" label="XPath" iconURI="platform:/plugin/org.eclipse.wst.xml.xpath.ui/icons/full/xpath.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.xml.xpath.ui.internal.views.XPathView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.xml.xpath.ui"/>
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_WZDugWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xsl.jaxp.debug.ui.resultview" label="Result" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.xsl.jaxp.debug.ui.internal.views.ResultView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.xsl.jaxp.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_WZDugmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xsl.ui.view.outline" label="Stylesheet Model" iconURI="platform:/plugin/org.eclipse.wst.xsl.ui/icons/full/hierarchy.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.xsl.ui.internal.views.stylesheet.StylesheetModelView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.xsl.ui"/>
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <trimContributions xmi:id="_2r10UF9tEeO-yojH_y4TJA" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_76uUAF9tEeO-yojH_y4TJA" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_8tJPcF9tEeO-yojH_y4TJA" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_9LgmcF9tEeO-yojH_y4TJA" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_WMpqMGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMpqMWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMpqMmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMpqM2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_WMocKmCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMpqNGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_WMpqNWCGEfC3wuS16sDcWA" elementId="org.eclipse.oomph.p2.ui.SearchRequirements" commandName="Search Requirements" category="_WMocI2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMpqNmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to field" description="Invokes quick assist and selects 'Convert local variable to field'" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMpqN2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMpqOGCGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui.junitPluginShortcut.coverage" commandName="Coverage JUnit Plug-in Test" description="Coverage JUnit Plug-in Test" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMpqOWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.FetchGitLabMergeRequest" commandName="Fetch GitLab Merge Request" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMpqOmCGEfC3wuS16sDcWA" elementId="org.eclipse.lsp4e.openTypeHierarchy" commandName="Open Type Hierarchy" description="Open Type Hierarchy for the selected item" category="_WMpDJ2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMpqO2CGEfC3wuS16sDcWA" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_WMpDL2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMpqPGCGEfC3wuS16sDcWA" elementId="org.eclipse.oomph.setup.editor.openDiscoveredType" commandName="Open Discovered Type" category="_WMpDI2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMpqPWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMpqPmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMpqP2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.select.stopMultiSelection" commandName="End multi-selection" description="Unselects all multi-selections returning to a single cursor " category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMpqQGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_WMocHGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMpqQWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMpqQmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMpqQ2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMpqRGCGEfC3wuS16sDcWA" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_WMocKmCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMpqRWCGEfC3wuS16sDcWA" elementId="trigger" name="trigger"/>
  </commands>
  <commands xmi:id="_WMpqRmCGEfC3wuS16sDcWA" elementId="org.eclipse.emf.codegen.ui.jet.goto.matching.bracket" commandName="Goto Matching Bracket" description="Goto Matching Bracket" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMpqR2CGEfC3wuS16sDcWA" elementId="org.eclipse.ant.ui.toggleMarkOccurrences" commandName="Toggle Ant Mark Occurrences" description="Toggles mark occurrences in Ant editors" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMpqSGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMpqSWCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.connectivity.commands.export" commandName="Export Profiles Command" description="Command to export connection profiles" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMpqSmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.Revert" commandName="Revert Commit" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMpqS2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.junit.junitShortcut.debug" commandName="Debug JUnit Test" description="Debug JUnit Test" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMpqTGCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.wikitext.ui.editor.showCheatSheetCommand" commandName="Show Markup Cheat Sheet" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMpqTWCGEfC3wuS16sDcWA" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_WMpDM2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMpqTmCGEfC3wuS16sDcWA" elementId="org.eclipse.ant.ui.open.declaration.command" commandName="Open Declaration" description="Opens the Ant editor on the referenced element" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRQGCGEfC3wuS16sDcWA" elementId="org.eclipse.tm4e.languageconfiguration.toggleLineCommentCommand" commandName="Toggle Line Comment" category="_WMocLWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRQWCGEfC3wuS16sDcWA" elementId="org.eclipse.epp.mpc.ui.command.showInstalled" commandName="Manage installed plug-ins" description="Update or uninstall plug-ins installed from the Marketplace" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRQmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.create.delegate.methods" commandName="Generate Delegate Methods" description="Add delegate methods for a type's fields" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRQ2CGEfC3wuS16sDcWA" elementId="org.eclipse.gef.ui.palette_view" commandName="Palette" category="_WMocLGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRRGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xml.views.XPathView.prefixes" commandName="&amp;Edit Namespace Prefixes" category="_WMocJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRRWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.filediff.OpenWorkingTree" commandName="Open Working Tree Version" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRRmCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.sqltools.sqleditor.toggleCommentAction" commandName="Toggle Comment" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRR2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.context.ui.commands.task.clearContext" commandName="Clear Context" category="_WMocFWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRSGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRSWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRSmCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRS2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.searchForTask" commandName="Search Repository for Task" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRTGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRTWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRTmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRT2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.commit.UnifiedDiffCommand" commandName="Show Unified Diff" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRUGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_WMocHGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRUWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.refactor.migrate.jar" commandName="Migrate JAR File" description="Migrate a JAR File to a new version" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRUmCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.maximizePart" commandName="Maximize Part" description="Maximize Part" category="_WMpDIGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRU2CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRVGCGEfC3wuS16sDcWA" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_WMpDKWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRVWCGEfC3wuS16sDcWA" elementId="org.eclipse.oomph.setup.editor.importProjects" commandName="Import Projects" category="_WMpDI2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRVmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRV2CGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui.hideUnusedElements" commandName="Hide Unused Elements" category="_WMocG2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRWGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Fields" description="Choose fields to initialize and constructor from superclass to call " category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRWWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xml.ui.disable.grammar.constraints" commandName="Turn off Grammar Constraints" description="Turn off grammar Constraints" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRWmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.commands.showElementInTypeHierarchyView" commandName="Show Java Element Type Hierarchy" description="Show a Java element in the Type Hierarchy view" category="_WMpDJWCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMqRW2CGEfC3wuS16sDcWA" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_WMqRXGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRXWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRXmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRX2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.wikitext.ui.quickOutlineCommand" commandName="Quick Outline" description="Open a popup dialog with a quick outline of the current document" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRYGCGEfC3wuS16sDcWA" elementId="AnsiConsole.command.enable_disable" commandName="Enable / Disable ANSI Support" description="Enable / disable ANSI Support" category="_WMocGGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRYWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRYmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_WMpDLmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRY2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_WMpDLmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRZGCGEfC3wuS16sDcWA" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_WMpDKWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRZWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRZmCGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui.localJavaShortcut.coverage" commandName="Coverage Java Application" description="Coverage Java Application" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRZ2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRaGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRaWCGEfC3wuS16sDcWA" elementId="org.eclipse.oomph.setup.editor.refreshCache" commandName="Refresh Remote Cache" category="_WMpDI2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRamCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify var access" description="Invokes quick assist and selects 'Qualify var access'" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRa2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.run" commandName="Run Java Applet" description="Run Java Applet" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRbGCGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui.exportSession" commandName="Export Session..." category="_WMocG2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRbWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRbmCGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui.scalaShortcut.coverage" commandName="Coverage Scala Application" description="Coverage Scala Application" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRb2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRcGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_WMocHGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRcWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_WMpDLmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRcmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRc2CGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui.dumpExecutionData" commandName="Dump Execution Data" category="_WMocG2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRdGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.debug.ui.commands.ToggleLambdaEntryBreakpoint" commandName="Toggle Lambda Entry Breakpoint" description="Creates or removes a lambda entry breakpoint" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRdWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRdmCGEfC3wuS16sDcWA" elementId="org.eclipse.buildship.ui.commands.runtasks" commandName="Run Gradle Tasks" description="Runs all the selected Gradle tasks" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRd2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.toggleBreadcrumb" commandName="Toggle Java Editor Breadcrumb" description="Toggle the Java editor breadcrumb" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqReGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqReWCGEfC3wuS16sDcWA" elementId="org.eclipse.ant.ui.renameInFile" commandName="Rename In File" description="Renames all references within the same buildfile" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRemCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRe2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.interface" commandName="Extract Interface" description="Extract a set of members into a new interface and try to use the new interface" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRfGCGEfC3wuS16sDcWA" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_WMpDNGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRfWCGEfC3wuS16sDcWA" elementId="org.eclipse.m2e.actions.LifeCycleGenerateSources.run" commandName="Run Maven Generate Sources" description="Run Maven Generate Sources" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRfmCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRf2CGEfC3wuS16sDcWA" elementId="org.eclipse.jst.ws.jaxws.ui.configure.handlers" commandName="Configure Handlers" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRgGCGEfC3wuS16sDcWA" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRgWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.sse.ui.structure.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRgmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.debug.ui.commands.AddExceptionBreakpoint" commandName="Add Java Exception Breakpoint" description="Add a Java exception breakpoint" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRg2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRhGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRhWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.debug.ui.commands.ToggleTracepoint" commandName="Toggle Tracepoint" description="Creates or removes a tracepoint" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRhmCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.wikitext.ui.convertToMarkupCommand" commandName="Generate Markup" category="_WMocKmCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMqRh2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.wikitext.ui.targetLanguage" name="TargetLanguage" optional="false"/>
  </commands>
  <commands xmi:id="_WMqRiGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRiWCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.showToolTip" commandName="Show Tooltip Description" category="_WMocF2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRimCGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui.junitShortcut.coverage" commandName="Coverage JUnit Test" description="Coverage JUnit Test" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRi2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.context.ui.commands.task.copyContext" commandName="Copy Context" category="_WMocFWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRjGCGEfC3wuS16sDcWA" elementId="org.eclipse.tm.terminal.view.ui.command.launchToolbar" commandName="Open Local Terminal on Selection" category="_WMpDKGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRjWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRjmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRj2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.inline" commandName="Inline" description="Inline a constant, local variable or method" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRkGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRkWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRkmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRk2CGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui.workbenchShortcut.coverage" commandName="Coverage Eclipse Application" description="Coverage Eclipse Application" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRlGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_WMocHGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRlWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMqRlmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4UGCGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jpa.ui.newEntity" commandName="JPA Entity" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4UWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4UmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4U2CGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui.commands.OpenCoverageConfiguration" commandName="Coverage Configurations..." description="Coverage Configurations..." category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4VGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.search.return.continue.targets" commandName="Search break/continue Target Occurrences in File" description="Search for break/continue target occurrences of a selected target name" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4VWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4VmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.create.getter.setter" commandName="Generate Getters and Setters" description="Generate Getter and Setter methods for type's fields" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4V2CGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.edit.text.format" commandName="Format Source" description="Format a PDE Source Page" category="_WMocKGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4WGCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.sqltools.result.terminate" commandName="Terminate Result" category="_WMocH2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4WWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4WmCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.builds.ui.command.ShowBuildOutput" commandName="Show Build Output" category="_WMocE2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4W2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesViewCollapseWorkingTree" commandName="Collapse Working Tree" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4XGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4XWCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4XmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4X2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4YGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.open.implementation" commandName="Open Implementation" description="Opens the Implementations of a method or a type in its hierarchy" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4YWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.junit.gotoTest" commandName="Referring Tests" description="Referring Tests" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4YmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4Y2CGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui.openSessionExecutionData" commandName="Open Execution Data" category="_WMocG2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4ZGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.commands.showElementInPackageView" commandName="Show Java Element in Package Explorer" description="Select Java element in the Package Explorer view" category="_WMpDJWCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMq4ZWCGEfC3wuS16sDcWA" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_WMq4ZmCGEfC3wuS16sDcWA" elementId="org.eclipse.oomph.setup.editor.performDropdown" commandName="Perform Dropdown" category="_WMpDI2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4Z2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.debug.ui.commands.InstanceCount" commandName="Instance Count" description="View the instance count of the selected type loaded in the target VM" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4aGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4aWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4amCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.addAllPluginsToJavaSearch" commandName="Add All Plug-ins to Java Workspace Scope" description="Adds all plug-ins in the target platform to Java workspace scope" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4a2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_WMocKmCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMq4bGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_WMq4bWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.convert.anonymous.to.nested" commandName="Convert Anonymous Class to Nested" description="Convert an anonymous class to a nested class" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4bmCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.quickOutline" commandName="Quick Outline" description="Open a quick outline popup dialog for a given editor input" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4b2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMq4cGCGEfC3wuS16sDcWA" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfYGCGEfC3wuS16sDcWA" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_WMocK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfYWCGEfC3wuS16sDcWA" elementId="org.eclipse.m2e.profiles.ui.commands.selectMavenProfileCommand" commandName="Select Maven Profiles" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfYmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfY2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy Commit Id" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfZGCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.new.subtask" commandName="New Subtask" category="_WMocF2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfZWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_WMpDLWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfZmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfZ2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfaGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfaWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfamCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch..." category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfa2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfbGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfbWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfbmCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfb2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.openTask" commandName="Open Task" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfcGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfcWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfcmCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.java.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_WMpDJmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfc2CGEfC3wuS16sDcWA" elementId="org.eclipse.pde.runtime.spy.commands.spyCommand" commandName="Plug-in Selection Spy" description="Show the Plug-in Spy" category="_WMpDN2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfdGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfdWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfdmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfd2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfeGCGEfC3wuS16sDcWA" elementId="org.eclipse.lsp4e.symbolinworkspace" commandName="Go to Symbol in Workspace" category="_WMpDJ2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfeWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_WMocHGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfemCGEfC3wuS16sDcWA" elementId="org.eclipse.oomph.ui.ToggleOfflineMode" commandName="Toggle Offline Mode" category="_WMocKWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfe2CGEfC3wuS16sDcWA" elementId="org.eclipse.oomph.setup.editor.openLog" commandName="Open Setup Log" category="_WMpDI2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrffGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_WMpDLmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrffWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_WMocHGCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMrffmCGEfC3wuS16sDcWA" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_WMrff2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfgGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfgWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfgmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_WMpDLWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfg2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfhGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into Java comments" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfhWCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.activateTask" commandName="Activate Task" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfhmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfh2CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the JavaScript file" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfiGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfiWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfimCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfi2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfjGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_WMpDLmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfjWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.select.multiCaretDown" commandName="Multi caret down" description="Add a new caret/multi selection below the current line, or remove the first caret/multi selection " category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfjmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfj2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfkGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.JavaBrowsingPerspective" commandName="Java Browsing" description="Show the Java Browsing perspective" category="_WMpDM2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfkWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfkmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMrfk2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElement" commandName="Open Build Element" category="_WMocE2CGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMrflGCGEfC3wuS16sDcWA" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_WMsGcGCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.team.ui.commands.CopyCommitMessage" commandName="Copy Commit Message for Task" description="Copies a commit message for the currently selected task to the clipboard." category="_WMocF2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGcWCGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui.relaunchSession" commandName="Relaunch Coverage Session" category="_WMocG2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGcmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGc2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Tree" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGdGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" description="Check out, rename, create, or delete a branch in a git repository" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGdWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xml.ui.previousSibling" commandName="Previous Sibling" description="Go to Previous Sibling" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGdmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGd2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGeGCGEfC3wuS16sDcWA" elementId="org.eclipse.epp.mpc.ui.command.importFavoritesWizard" commandName="Import Marketplace Favorites" description="Import another user's Marketplace Favorites List" category="_WMocKmCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMsGeWCGEfC3wuS16sDcWA" elementId="favoritesUrl" name="favoritesUrl"/>
  </commands>
  <commands xmi:id="_WMsGemCGEfC3wuS16sDcWA" elementId="org.eclipse.ant.ui.antShortcut.run" commandName="Run Ant Build" description="Run Ant Build" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGe2CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGfGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGfWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.debug.ui.commands.Inspect" commandName="Inspect" description="Inspect result of evaluating selected text" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGfmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGf2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGgGCGEfC3wuS16sDcWA" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGgWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGgmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGg2CGEfC3wuS16sDcWA" elementId="org.eclipse.oomph.setup.donate" commandName="Sponsor" description="Sponsor to the development of the Eclipse IDE" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGhGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to var" description="Invokes quick assist and selects 'Convert local variable to var'" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGhWCGEfC3wuS16sDcWA" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGhmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGh2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGiGCGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui.junitRAPShortcut.coverage" commandName="Coverage RAP JUnit Test" description="Coverage RAP JUnit Test" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGiWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.ReplaceWithTheirs" commandName="Replace Conflicting Files with Their Revision" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGimCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.filediff.CheckoutNew" commandName="Check Out This Version" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGi2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGjGCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.SynchronizeAll" commandName="Synchronize Changed" category="_WMocF2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGjWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.debug.ui.commands.Watch" commandName="Watch" description="Create new watch expression" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGjmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGj2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.wikitext.context.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_WMocGmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGkGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGkWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.server.stop" commandName="Stop" description="Stop the server" category="_WMocGWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGkmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_WMpDLWCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMsGk2CGEfC3wuS16sDcWA" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_WMsGlGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGlWCGEfC3wuS16sDcWA" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script" description="Create a refactoring script from refactorings on the local workspace" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGlmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGl2CGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.sqltools.sqleditor.saveAsTemplateAction" commandName="Save as Template" category="_WMpDMmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGmGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGmWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGmmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.local.variable" commandName="Extract Local Variable" description="Extracts an expression into a new local variable and uses the new local variable" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGm2CGEfC3wuS16sDcWA" elementId="org.eclipse.oomph.p2.ui.ExploreRepository" commandName="Explore Repository" category="_WMocI2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGnGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.InstallLfsLocal" commandName="Enable LFS locally" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGnWCGEfC3wuS16sDcWA" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="Open Refactoring History " description="Opens the refactoring history" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGnmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGn2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGoGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.log.jdt.showinconsole" commandName="&amp;Show In Console" description="Show Stack Trace in Console View" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGoWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.push.down" commandName="Push Down" description="Move members to subclasses" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGomCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGo2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.UpdateRepositoryConfiguration" commandName="Update Repository Configuration" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGpGCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGpWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGpmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open this Version" category="_WMpDMWCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMsGp2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_WMsGqGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGqWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGqmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGq2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.add.import" commandName="Add Import" description="Create import statement on selection" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGrGCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.attachment.open" commandName="Open Attachment" category="_WMpDIGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsGrWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstgGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstgWCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstgmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstg2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsthGCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.sqltools.sqleditor.refreshFromDatabaseAction" commandName="Refresh from Database" category="_WMpDMmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsthWCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.sqltools.sqleditor.ExecuteAsOneStatementAction" commandName="Execute Selected Text As One Statement" category="_WMpDMmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsthmCGEfC3wuS16sDcWA" elementId="org.eclipse.lsp4e.formatfile" commandName="Format" category="_WMpDJ2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsth2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstiGCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.connectivity.commands.addrepository" commandName="New Repository Profile Command" description="Command to create a new repository profile" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstiWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstimCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.builds.ui.command.NewTaskFromTest" commandName="New Task From Test" category="_WMocE2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsti2CGEfC3wuS16sDcWA" elementId="org.eclipse.oomph.setup.editor.perform.startup" commandName="Perform Setup Tasks (Startup)" category="_WMpDI2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstjGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstjWCGEfC3wuS16sDcWA" elementId="org.eclipse.jst.jsp.ui.add.imports" commandName="Add Im&amp;port" description="Create import declaration for selection" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstjmCGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jpa.eclipselink.ui.upgradeToEclipseLinkMappingFile" commandName="Upgrade to EclipseLink Mapping File" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstj2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_WMocHmCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMstkGCGEfC3wuS16sDcWA" elementId="url" name="URL"/>
    <parameters xmi:id="_WMstkWCGEfC3wuS16sDcWA" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_WMstkmCGEfC3wuS16sDcWA" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_WMstk2CGEfC3wuS16sDcWA" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_WMstlGCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.sqltools.result.removeAllInstances" commandName="Remove All Visible Results" category="_WMocH2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstlWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstlmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstl2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstmGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_WMocHGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstmWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstmmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstm2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_WMocHmCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMstnGCGEfC3wuS16sDcWA" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_WMstnWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstnmCGEfC3wuS16sDcWA" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_WMpDKWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstn2CGEfC3wuS16sDcWA" elementId="org.eclipse.lsp4e.togglelinkwitheditor" commandName="Toggle Link with Editor" category="_WMpDJ2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstoGCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateSelectedTask" commandName="Deactivate Selected Task" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstoWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstomCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsto2CGEfC3wuS16sDcWA" elementId="org.eclipse.lsp4e.format" commandName="Format" category="_WMpDJ2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstpGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstpWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstpmCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.createAntBuildFile" commandName="Create Ant Build File" description="Creates an Ant build file for the current project" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstp2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.openSelectedTask" commandName="Open Selected Task" category="_WMocF2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstqGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstqWCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.enablement.sybase.asa.schemaobjecteditor.examples.tableschemaeditor.copycolumn" commandName="Copy" category="_WMpDKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstqmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Toggle &quot;Link with Editor and Selection&quot; (Git Repositories View)" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstq2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.context.ui.commands.toggle.focus.active.view" commandName="Focus on Active Task" description="Toggle the focus on active task for the active view" category="_WMocFWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstrGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstrWCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.goToNextUnread" commandName="Go To Next Unread Task" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstrmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.validation.ValidationCommand" commandName="Validate" description="Invoke registered Validators" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstr2CGEfC3wuS16sDcWA" elementId="org.eclipse.tm4e.languageconfiguration.addBlockCommentCommand" commandName="Add Block Comment" category="_WMocLWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstsGCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.sqltools.sqleditor.debugAction" commandName="Debug" category="_WMpDMmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstsWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.add.javadoc.comment" commandName="Add Javadoc Comment" description="Add a Javadoc comment stub to the member element" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstsmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="Interactive Rebase" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsts2CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsttGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsttWCGEfC3wuS16sDcWA" elementId="AnsiConsole.command.copy_with_escapes" commandName="Copy Text With ANSI Escapes" description="Copy the console content to clipboard, including the escape sequences" category="_WMocGGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsttmCGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jaxb.ui.generateJaxbClasses" commandName="JAXB Classes..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstt2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstuGCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.openDependencies" commandName="Open Plug-in Dependencies" description="Opens the plug-in dependencies view for the current plug-in" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstuWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in Java editors" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstumCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstu2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.replace.invocations" commandName="Replace Invocations" description="Replace invocations of the selected method" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstvGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstvWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstvmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstv2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_WMocHGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstwGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstwWCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstwmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_WMocHGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstw2CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstxGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_WMocHGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstxWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a Java editor" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstxmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstx2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.indirection" commandName="Introduce Indirection" description="Introduce an indirection to encapsulate invocations of a selected method" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstyGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstyWCGEfC3wuS16sDcWA" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script" description="Perform refactorings from a refactoring script on the local workspace" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstymCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMsty2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.submitTask" commandName="Submit Task" description="Submits the currently open task" category="_WMpDIGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstzGCGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui.testNgSuiteShortcut.coverage" commandName="Coverage TestNG Suite" description="Coverage TestNG Suite" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstzWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.superclass" commandName="Extract Superclass" description="Extract a set of members into a new superclass and try to use the new superclass" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstzmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xml.ui.reload.dependencies" commandName="Reload Dependencies" description="Reload Dependencies" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMstz2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMst0GCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.internal.merge.ToggleCurrentChangesCommand" commandName="Ignore Changes from Ancestor to Current Version" description="Toggle ignoring changes only between the ancestor and the current version in a three-way merge comparison" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMst0WCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMst0mCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.junit.junitShortcut.rerunFailedFirst" commandName="Rerun JUnit Test - Failures First" description="Rerun JUnit Test - Failures First" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUkGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_WMocHGCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMtUkWCGEfC3wuS16sDcWA" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_WMtUkmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xsd.ui.refactor.makeTypeGlobal" commandName="Make &amp;Anonymous Type Global" description="Promotes anonymous type to global level and replaces its references" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUk2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUlGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUlWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.JavaPerspective" commandName="Java" description="Show the Java perspective" category="_WMpDM2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUlmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.indent" commandName="Correct Indentation" description="Corrects the indentation of the selected lines" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUl2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUmGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUmWCGEfC3wuS16sDcWA" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUmmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_WMpDLmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUm2CGEfC3wuS16sDcWA" elementId="org.eclipse.gef.zoom_in" commandName="Zoom In" description="Zoom In" category="_WMpDNWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUnGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.with.resources" commandName="Surround with try-with-resources Block" description="Surround the selected text with a try-with-resources block" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUnWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUnmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_WMocHGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUn2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUoGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUoWCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.externalizeStrings" commandName="Externalize Strings in Plug-ins" description="Extract translatable strings from plug-in files" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUomCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_WMocHGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUo2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.ReplaceWithOurs" commandName="Replace Conflicting Files with Our Revision" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUpGCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.connectivity.commands.showcategory" commandName="Show Category" description="Show Category" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUpWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUpmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.correction.extractMethodInplace.assist" commandName="Quick Assist - Extract method" description="Invokes quick assist and selects 'Extract to method'" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUp2CGEfC3wuS16sDcWA" elementId="org.eclipse.oomph.p2.ui.SearchRepositories" commandName="Search Repositories" category="_WMocI2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUqGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUqWCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUqmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUq2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.disconnected" commandName="Disconnected" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUrGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUrWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUrmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in JavaScript editors" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUr2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUsGCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.github.ui.command.createGist" commandName="Create Gist" description="Create Gist based on selection" category="_WMocKmCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMtUsWCGEfC3wuS16sDcWA" elementId="publicGist" name="Public Gist"/>
  </commands>
  <commands xmi:id="_WMtUsmCGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jpa.ui.persistentAttributeMapAs" commandName="Map As" category="_WMocJmCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMtUs2CGEfC3wuS16sDcWA" elementId="specifiedPersistentAttributeMappingKey" name="specified mapping key" optional="false"/>
    <parameters xmi:id="_WMtUtGCGEfC3wuS16sDcWA" elementId="defaultPersistentAttributeMappingKey" name="default mapping key" optional="false"/>
  </commands>
  <commands xmi:id="_WMtUtWCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.index.ui.command.ResetIndex" commandName="Refresh Search Index" category="_WMocF2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUtmCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.github.ui.command.rebasePullRequest" commandName="Rebase pull request" description="Rebase onto destination branch" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUt2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUuGCGEfC3wuS16sDcWA" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_WMpDL2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUuWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUumCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.genericeditor.gotoMatchingBracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUu2CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to var" description="Invokes quick assist and selects 'Assign to var'" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUvGCGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jpa.ui.convertJavaGenerators" commandName="Move Java Generators to XML..." category="_WMocJGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUvWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_WMpDJWCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMtUvmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_WMtUv2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.new.local.task" commandName="New Local Task" category="_WMocF2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUwGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUwWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.sort.members" commandName="Sort Members" description="Sort all members using the member order preference" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUwmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUw2CGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.connectivity.commands.addprofile" commandName="New Connection Profile Command" description="Command to create a new connection profile" category="_WMocKmCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMtUxGCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.connectivity.ui.ignoreCategory" name="ignoreCategory"/>
    <parameters xmi:id="_WMtUxWCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.connectivity.ui.useSelection" name="useSelection"/>
  </commands>
  <commands xmi:id="_WMtUxmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUx2CGEfC3wuS16sDcWA" elementId="org.eclipse.m2e.discovery.ui" commandName="m2e Marketplace" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUyGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUyWCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskIncomplete" commandName="Mark Task Incomplete" category="_WMocF2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUymCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_WMocHGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUy2CGEfC3wuS16sDcWA" elementId="org.eclipse.codegen.ui.jet.rename" commandName="Rename" description="Rename" category="_WMpDImCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUzGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.correction.assignAllParamsToNewFields.assist" commandName="Quick Assist - Assign all parameters to new fields" description="Invokes quick assist and selects 'Assign all parameters to new fields'" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUzWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUzmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtUz2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.make.static" commandName="Make Static" description="Make Static" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU0GCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToNextUnread" commandName="Mark Task Read and Go To Next Unread Task" category="_WMocF2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU0WCGEfC3wuS16sDcWA" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_WMocK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU0mCGEfC3wuS16sDcWA" elementId="org.eclipse.buildship.ui.commands.rundefaulttasks" commandName="Run Gradle Default Tasks" description="Runs the default tasks of the selected Gradle project" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU02CGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jpa.ui.generateEntities" commandName="Generate Entities from Tables..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU1GCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU1WCGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui.removeActiveSession" commandName="Remove Active Session" category="_WMocG2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU1mCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskRead" commandName="Mark Task Read" category="_WMocF2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU12CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.move.element" commandName="Move - Refactoring " description="Move the selected element to a new location" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU2GCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU2WCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.indent" commandName="Indent Line" description="Indents the current line" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU2mCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU22CGEfC3wuS16sDcWA" elementId="org.eclipse.jst.pagedesigner.vertical" commandName="Vertical Layout" category="_WMocLmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU3GCGEfC3wuS16sDcWA" elementId="org.eclipse.codegen.ui.jet.source.quickmenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_WMpDJGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU3WCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU3mCGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jpa.ui.addToPersistenceUnit" commandName="Add to Persistence Unit" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU32CGEfC3wuS16sDcWA" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_WMpDKWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU4GCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_WMocHGCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMtU4WCGEfC3wuS16sDcWA" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_WMtU4mCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU42CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.CompareWithCommit" commandName="Compare with Commit..." category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU5GCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU5WCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateAllTasks" commandName="Deactivate Task" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU5mCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU52CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU6GCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.codemining" commandName="Toggle Code Mining" description="Toggle Code Mining Annotations" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU6WCGEfC3wuS16sDcWA" elementId="org.eclipse.m2e.core.ui.command.updateProject" commandName="Update Maven Project" description="Update Maven project configuration and dependencies" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU6mCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU62CGEfC3wuS16sDcWA" elementId="refresh.schema.editor.action.id" commandName="Refresh from Server" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU7GCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU7WCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.organizeManifest" commandName="Organize Manifests" description="Cleans up plug-in manifest files" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU7mCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU72CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.junit.junitShortcut.run" commandName="Run JUnit Test" description="Run JUnit Test" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU8GCGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jpa.ui.convertJavaQueries" commandName="Move Java Queries to XML..." category="_WMocJGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU8WCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU8mCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU82CGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui.swtBotJunitShortcut.coverage" commandName="Coverage SWTBot Test" description="Coverage SWTBot Test" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU9GCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU9WCGEfC3wuS16sDcWA" elementId="org.eclipse.jst.jsp.ui.refactor.move" commandName="Move" description="Move a Java Element to another package" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU9mCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the compilation unit" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU92CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_WMpDLmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU-GCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.enablement.sybase.asa.schemaobjecteditor.examples.tableschemaeditor.pastecolumn" commandName="Paste" category="_WMpDKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU-WCGEfC3wuS16sDcWA" elementId="org.eclipse.m2e.core.pomFileAction.run" commandName="Run Maven Build" description="Run Maven Build" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU-mCGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui.linkWithSelection" commandName="Link with Current Selection" category="_WMocG2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU-2CGEfC3wuS16sDcWA" elementId="org.eclipse.m2e.actions.LifeCycleInstall.run" commandName="Run Maven Install" description="Run Maven Install" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU_GCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.sqltools.sqleditor.ExecuteCurrentAction" commandName="Execute Current Text" category="_WMpDMmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU_WCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU_mCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionUp" commandName="Multi selection up relative to anchor selection" description="Search next matching region above and add it to the current selection, or remove last element from current multi-selection " category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtU_2CGEfC3wuS16sDcWA" elementId="org.eclipse.oomph.setup.editor.perform" commandName="Perform Setup Tasks" category="_WMpDI2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVAGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.raw.paste" commandName="Raw Paste" description="Paste and ignore smart insert setting" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVAWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_WMpDLWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVAmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVA2CGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVBGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVBWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVBmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVB2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.commons.ui.command.AddRepository" commandName="Add Repository" category="_WMocJ2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVCGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.commit.DiffEditorQuickOutlineCommand" commandName="Quick Outline" description="Show the quick outline for a unified diff" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVCWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVCmCGEfC3wuS16sDcWA" elementId="org.eclipse.ant.ui.antShortcut.debug" commandName="Debug Ant Build" description="Debug Ant Build" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVC2CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVDGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVDWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_WMocHmCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMtVDmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_WMtVD2CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVEGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xml.ui.nextSibling" commandName="Next Sibling" description="Go to Next Sibling" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVEWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a JavaScript editor" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVEmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_WMpDLWCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMtVE2CGEfC3wuS16sDcWA" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_WMtVFGCGEfC3wuS16sDcWA" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_WMtVFWCGEfC3wuS16sDcWA" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_WMtVFmCGEfC3wuS16sDcWA" elementId="revert.schema.editor.action.id" commandName="Revert Object" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVF2CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xsl.debug.ui.launchshortcut.debug" commandName="Debug XSLT Transformation" description="Create a configuration to debug an XSLT transformation" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVGGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVGWCGEfC3wuS16sDcWA" elementId="org.eclipse.m2e.core.ui.command.addDependency" commandName="Add Maven Dependency" description="Add Maven dependency" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVGmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.debug.ui.commands.Execute" commandName="Execute" description="Evaluate selected text" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVG2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVHGCGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jpa.core.synchronizeClasses" commandName="Synchronize Class List" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVHWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.navigate.gotopackage" commandName="Go to Folder" description="Go to Folder" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVHmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVH2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_WMocHGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVIGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.server.launchShortcut.run" commandName="Run on Server" description="Run the current selection on a server" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVIWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVImCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.server.launchShortcut.debug" commandName="Debug on Server" description="Debug the current selection on a server" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVI2CGEfC3wuS16sDcWA" elementId="org.eclipse.m2e.editor.RenameArtifactAction" commandName="Rename Maven Artifact..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVJGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVJWCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.sqltools.sqleditor.ExecuteSQLAction" commandName="Execute All" category="_WMpDMmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVJmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.rename.element" commandName="Rename - Refactoring " description="Rename the selected element" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVJ2CGEfC3wuS16sDcWA" elementId="org.eclipse.pde.api.tools.ui.remove.filters" commandName="Remove API Problem Filters..." description="Remove API problem filters for this project" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVKGCGEfC3wuS16sDcWA" elementId="org.eclipse.tm.terminal.view.ui.command.newview" commandName="New Terminal View" category="_WMpDKGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVKWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVKmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVK2CGEfC3wuS16sDcWA" elementId="org.eclipse.lsp4e.showkindinoutline" commandName="Show Kind in Outline" category="_WMpDJ2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVLGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_WMocHGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVLWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVLmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the compilation unit" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVL2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_WMocKmCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMtVMGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_WMtVMWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVMmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.generate.hashcode.equals" commandName="Generate hashCode() and equals()" description="Generates hashCode() and equals() methods for the type" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVM2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_WMpDJWCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMtVNGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_WMtVNWCGEfC3wuS16sDcWA" elementId="sed.tabletree.collapseAll" commandName="Collapse All" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVNmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVN2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_WMocImCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMtVOGCGEfC3wuS16sDcWA" elementId="title" name="Title"/>
    <parameters xmi:id="_WMtVOWCGEfC3wuS16sDcWA" elementId="message" name="Message"/>
    <parameters xmi:id="_WMtVOmCGEfC3wuS16sDcWA" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_WMtVO2CGEfC3wuS16sDcWA" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_WMtVPGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVPWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVPmCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskComplete" commandName="Mark Task Complete" category="_WMocF2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVP2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVQGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVQWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVQmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVQ2CGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui.commands.CoverageLast" commandName="Coverage" description="Coverage" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVRGCGEfC3wuS16sDcWA" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_WMpDKWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVRWCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.enablement.sybase.asa.schemaobjecteditor.examples.tableschemaeditor.cutcolumn" commandName="Cut" category="_WMpDKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVRmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVR2CGEfC3wuS16sDcWA" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVSGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVSWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository..." description="Adds an existing Git repository to the Git Repositories view" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVSmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.debug.ui.breakpoint.properties" commandName="Java Breakpoint Properties" description="View and edit the properties for a given Java breakpoint" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVS2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVTGCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearActiveTime" commandName="Clear Active Time" category="_WMocF2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVTWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVTmCGEfC3wuS16sDcWA" elementId="org.eclipse.buildship.ui.commands.refreshproject" commandName="Refresh Gradle Project" description="Synchronizes the Gradle builds of the selected projects with the workspace" category="_WMocEmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVT2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVUGCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.context.ui.commands.task.attachContext" commandName="Attach Context" category="_WMocFWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVUWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xml.views.XPathView.processor.xpathprocessor" commandName="XPath Processor" category="_WMocJWCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMtVUmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.commands.radioStateParameter" name="State" optional="false"/>
  </commands>
  <commands xmi:id="_WMtVU2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVVGCGEfC3wuS16sDcWA" elementId="org.eclipse.tm.terminal.connector.local.command.launch" commandName="Open Local Terminal on Selection" category="_WMpDKGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVVWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_WMocHmCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMtVVmCGEfC3wuS16sDcWA" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_WMtVV2CGEfC3wuS16sDcWA" elementId="org.eclipse.m2e.sourcelookup.ui.openSourceLookupInfoDialog" commandName="Source Lookup Info" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVWGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVWWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVWmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVW2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVXGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVXWCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVXmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVX2CGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.debug" commandName="Debug OSGi Framework" description="Debug OSGi Framework" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVYGCGEfC3wuS16sDcWA" elementId="org.eclipse.lsp4e.symbolinfile" commandName="Go to Symbol in File" category="_WMpDJ2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVYWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVYmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVY2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVZGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVZWCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVZmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVZ2CGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui.resetOnDump" commandName="Reset on Dump" category="_WMocG2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVaGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVaWCGEfC3wuS16sDcWA" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_WMpDL2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVamCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVa2CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.sse.ui.add.block.comment" commandName="Add Block Comment" description="Add Block Comment" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVbGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVbWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.debug.ui.commands.AllInstances" commandName="All Instances" description="View all instances of the selected type loaded in the target VM" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVbmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" description="Opens selected commit(s) in Commit Viewer(s)" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVb2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.correction.assignInTryWithResources.assist" commandName="Quick Assist - Assign to variable in new try-with-resources block" description="Invokes quick assist and selects 'Assign to variable in new try-with-resources block'" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVcGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVcWCGEfC3wuS16sDcWA" elementId="org.eclipse.m2e.actions.LifeCycleTest.run" commandName="Run Maven Test" description="Run Maven Test" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVcmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xsl.debug.ui.launchshortcut.run" commandName="Run XSLT Transformation" description="Create a configuration to debug an XSLT transformation" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVc2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVdGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to field" description="Invokes quick assist and selects 'Assign parameter to field'" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVdWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVdmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.move.inner.to.top.level" commandName="Move Type to New File" description="Move Type to New File" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVd2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVeGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.sse.ui.open.file.from.source" commandName="Open Selection" description="Open an editor on the selected link" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVeWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.open.hierarchy" commandName="Quick Hierarchy" description="Show the quick hierarchy of the selected element" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVemCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.builds.ui.command.ShowTestResults" commandName="Show Test Results" category="_WMocE2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVe2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVfGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.sse.ui.goto.matching.bracket" commandName="Matching Character" description="Go to Matching Character" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVfWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.FetchGiteaPullRequest" commandName="Fetch Gitea Pull Request" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVfmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.refactor.quickMenu" commandName="Show Refactor Quick Menu" description="Shows the refactor quick menu" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVf2CGEfC3wuS16sDcWA" elementId="org.eclipse.lsp4e.selectionRange.up" commandName="Enclosing Element" description="Expand Selection To Enclosing Element" category="_WMpDJ2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVgGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVgWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.override.methods" commandName="Override/Implement Methods" description="Override or implement methods from super types" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVgmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVg2CGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jaxb.eclipselink.ui.command.addEclipseLinkJaxbProperty" commandName="Add EclipseLink JAXB property" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVhGCGEfC3wuS16sDcWA" elementId="org.eclipse.lsp4e.selectionRange.down" commandName="Restore To Last Selection" description="Expand Selection To Restore To Last Selection" category="_WMpDJ2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVhWCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.wikitext.ui.convertToDocbookCommand" commandName="Generate Docbook" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVhmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVh2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.change.type" commandName="Generalize Declared Type" description="Change the declaration of a selected variable to a more general type consistent with usage" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtViGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtViWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository..." description="Clones a Git repository and adds the clone to the Git Repositories view" category="_WMpDMWCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMtVimCGEfC3wuS16sDcWA" elementId="repositoryUri" name="Repository URI"/>
  </commands>
  <commands xmi:id="_WMtVi2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVjGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.sse.ui.quick_outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVjWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.add.textblock" commandName="Add Text Block" description="Adds Text Block" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVjmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVj2CGEfC3wuS16sDcWA" elementId="org.eclipse.ant.ui.openExternalDoc" commandName="Open External Documentation" description="Open the External documentation for the current task in the Ant editor" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVkGCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.github.ui.command.mergePullRequest" commandName="Merge pull request" description="Merge into destination branch" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVkWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Git Repository..." description="Creates a new Git repository and adds it to the Git Repositories view" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVkmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVk2CGEfC3wuS16sDcWA" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_WMocHWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVlGCGEfC3wuS16sDcWA" elementId="org.eclipse.tm4e.languageconfiguration.removeBlockCommentCommand" commandName="Remove Block Comment" category="_WMocLWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVlWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVlmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVl2CGEfC3wuS16sDcWA" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_WMpDLWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVmGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVmWCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVmmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVm2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.context.ui.commands.open.context.dialog" commandName="Show Context Quick View" description="Show Context Quick View" category="_WMocFWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVnGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_WMocHGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVnWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVnmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.CompareWithRef" commandName="Compare with Branch, Tag or Reference..." category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVn2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.context.ui.commands.attachment.retrieveContext" commandName="Retrieve Context Attachment" category="_WMocFWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVoGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVoWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.pull.up" commandName="Pull Up" description="Move members to a superclass" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVomCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVo2CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVpGCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.bugs.commands.ReportBugAction" commandName="Report Bug or Enhancement..." description="Report Bug or Enhancement for predefined Products / Projects" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVpWCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.RefreshRepositoryTasks" commandName="Synchronize Changed" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVpmCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVp2CGEfC3wuS16sDcWA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_WMocKmCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMtVqGCGEfC3wuS16sDcWA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_WMtVqWCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVqmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVq2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVrGCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVrWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVrmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVr2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVsGCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.context.ui.commands.interest.increment" commandName="Make Landmark" description="Make Landmark" category="_WMocFWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVsWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.add.javadoc.comment" commandName="Add JSDoc Comment" description="Add a JSDoc comment stub to the member element" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVsmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVs2CGEfC3wuS16sDcWA" elementId="org.eclipse.pde.api.tools.ui.convert.javadocs" commandName="Convert API Tools Javadoc Tags..." description="Starts a wizard that will allow you to convert existing Javadoc tags to annotations" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVtGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesViewShowInSystemExplorer" commandName="Show In System Explorer" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVtWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.open.external.javadoc" commandName="Open Attached Javadoc" description="Open the attached Javadoc of the selected element in a browser" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVtmCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.run" commandName="Run OSGi Framework" description="Run OSGi Framework" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVt2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVuGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.sse.ui.structure.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVuWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVumCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.debug.ui.commands.AddClassPrepareBreakpoint" commandName="Add Class Load Breakpoint" description="Add a class load breakpoint" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVu2CGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jpa.ui.convertJavaProjectToJpa" commandName="Convert to JPA Project..." category="_WMocHGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVvGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesCreateGroup" commandName="Create a Repository Group" description="Create a repository group for structuring repositories in the Git Repositories view" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVvWCGEfC3wuS16sDcWA" elementId="org.eclipse.emf.codegen.ecore.ui.Generate" commandName="Generate Code" description="Generate code for the EMF models in the workspace" category="_WMpDLGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVvmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.generate.javadoc" commandName="Generate JSDoc" description="Generates JSDoc for a selectable set of JavaScript resources" category="_WMpDLmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVv2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVwGCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_WMpDM2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVwWCGEfC3wuS16sDcWA" elementId="org.eclipse.tips.ide.command.open" commandName="Tip of the Day" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVwmCGEfC3wuS16sDcWA" elementId="AnsiConsole.command.copy_without_escapes" commandName="Copy Text Without ANSI Escapes" description="Copy the console content to clipboard, removing the escape sequences" category="_WMocGGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVw2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.specific_content_assist.command" commandName="Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_WMocEGCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMtVxGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_WMtVxWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVxmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVx2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_WMocHGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVyGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.search.method.exits" commandName="Search Method Exit Occurrences in File" description="Search for method exit occurrences of a selected return type" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVyWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVymCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVy2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_WMocHGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVzGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVzWCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVzmCGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jpa.eclipselink.ui.generateDynamicEntities" commandName="Generate Dynamic Entities from Tables..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtVz2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV0GCGEfC3wuS16sDcWA" elementId="org.eclipse.m2e.sourcelookup.ui.importBinaryProject" commandName="Import Binary Project" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV0WCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV0mCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV02CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.promote.local.variable" commandName="Convert Local Variable to Field" description="Convert a local variable to a field" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV1GCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV1WCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV1mCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV12CGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV2GCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_WMpDLmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV2WCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.self.encapsulate.field" commandName="Encapsulate Fields" description="Create getting and setting methods for the field and use only those to access the field" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV2mCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV22CGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.run" commandName="Run Eclipse Application" description="Run Eclipse Application" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV3GCGEfC3wuS16sDcWA" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV3WCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.sse.ui.remove.block.comment" commandName="Remove Block Comment" description="Remove Block Comment" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV3mCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.runtime.spy.commands.menuSpyCommand" commandName="Plug-in Menu Spy" description="Show the Plug-in Spy" category="_WMpDN2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV32CGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jpa.ui.entityMappingsAddPersistentClass" commandName="Add Class..." category="_WMocJmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV4GCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Vars" description="Choose vars to initialize and constructor from superclass to call " category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV4WCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV4mCGEfC3wuS16sDcWA" elementId="org.eclipse.m2e.core.ui.command.openPom" commandName="Open Maven POM" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV42CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.index.rebuild" commandName="Rebuild Java Index" description="Rebuilds the Java index database" category="_WMpDLmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV5GCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV5WCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xsd.ui.refactor.rename.element" commandName="&amp;Rename XSD element" description="Rename XSD element" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV5mCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV52CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.goToPreviousUnread" commandName="Go To Previous Unread Task" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV6GCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV6WCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_WMocLGCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMtV6mCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_WMtV62CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_WMtV7GCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_WMtV7WCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV7mCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV72CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_WMpDJWCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMtV8GCGEfC3wuS16sDcWA" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_WMtV8WCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV8mCGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui.selectRootElements" commandName="Select Root Elements" category="_WMocG2CGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMtV82CGEfC3wuS16sDcWA" elementId="type" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_WMtV9GCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_WMocHGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV9WCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV9mCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV92CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV-GCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV-WCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.openPluginArtifact" commandName="Open Plug-in Artifact" description="Open a plug-in artifact in the manifest editor" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV-mCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.server.debug" commandName="Debug" description="Debug server" category="_WMocGWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV-2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV_GCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter" commandName="Introduce Parameter" description="Introduce a new method parameter based on the selected expression" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtV_WCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.addTaskRepository" commandName="Add Task Repository..." category="_WMocF2CGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMtV_mCGEfC3wuS16sDcWA" elementId="connectorKind" name="Repository Type"/>
  </commands>
  <commands xmi:id="_WMtV_2CGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui.removeAllSessions" commandName="Remove All Sessions" category="_WMocG2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWAGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.run" commandName="Run Java Application" description="Run Java Application" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWAWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWAmCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElementWithBrowser" commandName="Open Build with Browser" category="_WMocE2CGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMtWA2CGEfC3wuS16sDcWA" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_WMtWBGCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.updateUnitVersions" commandName="Update IU Versions from Repositories" description="Update to latest IU versions" category="_WMocKGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWBWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.sse.ui.structure.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWBmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.wsdl.ui.refactor.rename.element" commandName="Rename WSDL component" description="Renames WSDL component" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWB2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_WMpDLWCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMtWCGCGEfC3wuS16sDcWA" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_WMtWCWCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.viewSource.command" commandName="View Unformatted Text" category="_WMocF2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWCmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWC2CGEfC3wuS16sDcWA" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWDGCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.searchTargetRepositories" commandName="Add Artifact to Target Platform" description="Add an artifact to your target platform" category="_WMocKmCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMtWDWCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.searchTargetRepositories.term" name="The initial search pattern for the artifact search dialog"/>
  </commands>
  <commands xmi:id="_WMtWDmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWD2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWEGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWEWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_WMpDLmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWEmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.PullWithOptions" commandName="Pull..." category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWE2CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWFGCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.github.ui.command.checkoutPullRequest" commandName="Checkout Pull Request" description="Checkout pull request into topic branch" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWFWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.select.addAllMatchesToMultiSelection" commandName="Add all matches to multi-selection" description="Looks for all regions matching the current selection or identifier and adds them to a multi-selection " category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWFmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.server.publish" commandName="Publish" description="Publish to server" category="_WMocGWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWF2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.factory" commandName="Introduce Factory" description="Introduce a factory method to encapsulate invocation of the selected constructor" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWGGCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.updateClasspath" commandName="Update Classpath" description="Updates the plug-in classpath from latest settings" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWGWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.use.supertype" commandName="Use Supertype Where Possible" description="Change occurrences of a type to use a supertype instead" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWGmCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskUnread" commandName="Mark Task Unread" category="_WMocF2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWG2CGEfC3wuS16sDcWA" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWHGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.sse.ui.structure.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWHWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWHmCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.builds.ui.command.NewTaskFromBuild" commandName="New Task From Build" category="_WMocE2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWH2CGEfC3wuS16sDcWA" elementId="org.eclipse.tm.terminal.view.ui.command.launch" commandName="Open Terminal on Selection" category="_WMpDKGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWIGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWIWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xsd.ui.refactor.makeElementGlobal" commandName="Make Local Element &amp;Global" description="Promotes local element to global level and replaces its references" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWImCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.correction.encapsulateField.assist" commandName="Quick Assist - Create getter/setter for field" description="Invokes quick assist and selects 'Create getter/setter for field'" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWI2CGEfC3wuS16sDcWA" elementId="org.eclipse.oomph.setup.editor.openEditorDropdown" commandName="Open Setup Editor" category="_WMpDI2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWJGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_WMpDLWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWJWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.ReplaceWithPrevious" commandName="Replace with Previous Revision" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWJmCGEfC3wuS16sDcWA" elementId="org.eclipse.m2e.sourcelookup.ui.openPom" commandName="Open Pom" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWJ2CGEfC3wuS16sDcWA" elementId="org.eclipse.oomph.setup.ui.questionnaire" commandName="Configuration Questionnaire" description="Review the IDE's most fiercely contested preferences" category="_WMpDI2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWKGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWKWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWKmCGEfC3wuS16sDcWA" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_WMpDLWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWK2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWLGCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.sqltools.sqleditor.GotoMatchingTokenAction" commandName="Goto Matching Token" category="_WMpDMmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWLWCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.api.tools.ui.setup.projects" commandName="API Tools Setup..." description="Configure projects for API usage and compatibility checks" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWLmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.debug" commandName="Debug Java Application" description="Debug Java Application" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWL2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWMGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.CherryPick" commandName="Cherry Pick" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWMWCGEfC3wuS16sDcWA" elementId="org.eclipse.buildship.ui.commands.openbuildscript" commandName="Open Gradle Build Script" description="Opens the Gradle build script for the selected Gradle project" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWMmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.FetchGitHubPR" commandName="Fetch GitHub Pull Request" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWM2CGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jpa.eclipselink.ui.convertJavaConverters" commandName="Move Java Converters to XML..." category="_WMocJGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWNGCGEfC3wuS16sDcWA" elementId="org.eclipse.buildship.ui.commands.openrunconfiguration" commandName="Open Gradle Run Configuration" description="Opens the Run Configuration for the selected Gradle tasks" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWNWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWNmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWN2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWOGCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.github.ui.command.fetchPullRequest" commandName="Fetch Pull Request Commits" description="Fetch commits from pull request" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWOWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWOmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWO2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWPGCGEfC3wuS16sDcWA" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_WMpDL2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWPWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWPmCGEfC3wuS16sDcWA" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_WMpDKWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWP2CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xml.ui.generate.xml" commandName="XML File..." description="Generate a XML file from the selected DTD or Schema" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWQGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWQWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to var" description="Invokes quick assist and selects 'Assign parameter to var'" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWQmCGEfC3wuS16sDcWA" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_WMpDNGCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMtWQ2CGEfC3wuS16sDcWA" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_WMtWRGCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.importFromRepository" commandName="Import Plug-in from a Repository" description="Imports a plug-in from a source repository" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWRWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWRmCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.previousTask" commandName="Previous Task Command" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWR2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_WMocHGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWSGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWSWCGEfC3wuS16sDcWA" elementId="org.eclipse.emf.codegen.ui.jet.select.enclosing" commandName="Select Enclosing JET Element" description="Select Enclosing JET Element" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWSmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWS2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWTGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWTWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWTmCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToPreviousUnread" commandName="Mark Task Read and Go To Previous Unread Task" category="_WMocF2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWT2CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWUGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.constant" commandName="Extract Constant" description="Extracts a constant into a new static field and uses the new static field" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWUWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWUmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.sse.ui.cleanup.document" commandName="Cleanup Document..." description="Cleanup document" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWU2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.builds.ui.command.ShowBuildOutput.url" commandName="Show Build Output" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWVGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWVWCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWVmCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWV2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWWGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify field access" description="Invokes quick assist and selects 'Qualify field access'" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWWWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWWmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.sse.ui.format" commandName="Format" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWW2CGEfC3wuS16sDcWA" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWXGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWXWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWXmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWX2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWYGCGEfC3wuS16sDcWA" elementId="org.eclipse.m2e.core.ui.command.addPlugin" commandName="Add Maven Plugin" description="Add Maven plugin" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWYWCGEfC3wuS16sDcWA" elementId="sed.tabletree.expandAll" commandName="Expand All" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWYmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.modify.method.parameters" commandName="Change Method Signature" description="Change method signature includes parameter names and parameter order" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWY2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWZGCGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui.mergeSessions" commandName="Merge Sessions" category="_WMocG2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWZWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable (replace all occurrences)" description="Invokes quick assist and selects 'Extract local variable (replace all occurrences)'" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWZmCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWZ2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWaGCGEfC3wuS16sDcWA" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_WMpDLWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWaWCGEfC3wuS16sDcWA" elementId="org.eclipse.lsp4e.openCallHierarchy" commandName="Open Call Hierarchy" description="Open Call Hierarchy for the selected item" category="_WMpDJ2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWamCGEfC3wuS16sDcWA" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_WMpDKWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWa2CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.external.javadoc" commandName="Open External JSDoc" description="Open the JSDoc of the selected element in an external browser" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWbGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch..." category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWbWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWbmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWb2CGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jpa.eclipselink.ui.persistentTypeAddVirtualAttribute" commandName="Add Virtual Attribute..." category="_WMocJmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWcGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_WMpDM2CGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMtWcWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_WMtWcmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_WMtWc2CGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jpa.eclipselink.ui.newDynamicEntity" commandName="EclipseLink Dynamic Entity" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWdGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWdWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWdmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWd2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.debug" commandName="Debug Java Applet" description="Debug Java Applet" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWeGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xml.ui.gotoMatchingTag" commandName="Matching Tag" description="Go to Matching Tag" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWeWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWemCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.sqltools.sqleditor.saveToDatabaseAction" commandName="Save to Database" category="_WMpDMmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWe2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWfGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWfWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWfmCGEfC3wuS16sDcWA" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_WMpDKWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWf2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_WMpDLmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWgGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.filediff.OpenPrevious" commandName="Open Previous Version" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWgWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWgmCGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jpa.ui.persistentAttributeAddToXml" commandName="Add Attribute to XML" category="_WMocJmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWg2CGEfC3wuS16sDcWA" elementId="org.eclipse.tips.ide.command.trim.open" commandName="Tip of the Day" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMtWhGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7oGCGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui.selectCounters" commandName="Select Counters" category="_WMocG2CGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMt7oWCGEfC3wuS16sDcWA" elementId="type" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_WMt7omCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_WMpDLmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7o2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7pGCGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jpa.ui.persistentTypeMapAs" commandName="Map As" category="_WMocJmCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMt7pWCGEfC3wuS16sDcWA" elementId="persistentTypeMappingKey" name="mapping key" optional="false"/>
  </commands>
  <commands xmi:id="_WMt7pmCGEfC3wuS16sDcWA" elementId="org.eclipse.epp.package.common.contribute" commandName="Contribute" description="Contribute to the development and success of the Eclipse IDE!" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7p2CGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.sqltools.sqleditor.DMLDialogSelectionAction" commandName="Edit in SQL Query Builder..." category="_WMpDMmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7qGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7qWCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.debug" commandName="Debug JUnit Plug-in Test" description="Debug JUnit Plug-in Test" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7qmCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.github.ui.command.cloneGist" commandName="Clone Gist" description="Clone Gist into Git repository" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7q2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.correction.extractLocalNotReplaceOccurrences.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7rGCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.sqltools.sqleditor.attachProfileAction" commandName="Set Connection Information" category="_WMpDMmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7rWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7rmCGEfC3wuS16sDcWA" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_WMpDNGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7r2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_WMocHGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7sGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.debug.ui.commands.AllReferences" commandName="All References" description="Inspect all references to the selected object" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7sWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.junit.junitShortcut.rerunLast" commandName="Rerun JUnit Test" description="Rerun JUnit Test" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7smCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionDown" commandName="Multi selection down relative to anchor selection  " description="Search next matching region and add it to the current selection, or remove first element from current multi-selection " category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7s2CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.sse.ui.format.active.elements" commandName="Format Active Elements" description="Format active elements" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7tGCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.connectivity.commands.import" commandName="Import Profiles Command" description="Command to import connection profiles" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7tWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7tmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7t2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearOutgoing" commandName="Clear Outgoing Changes" category="_WMocF2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7uGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7uWCGEfC3wuS16sDcWA" elementId="org.eclipse.epp.mpc.ui.command.showFavorites" commandName="Eclipse Marketplace Favorites" description="Open Marketplace Favorites" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7umCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7u2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7vGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_WMpDLWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7vWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.infer.type.arguments" commandName="Infer Generic Type Arguments" description="Infer type arguments for references to generic classes and remove unnecessary casts" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7vmCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.builds.ui.command.ShowTestResults.url" commandName="Show Test Results" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7v2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7wGCGEfC3wuS16sDcWA" elementId="org.eclipse.m2e.actions.LifeCycleClean.run" commandName="Run Maven Clean" description="Run Maven Clean" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7wWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7wmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7w2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.wikitext.ui.convertToEclipseHelpCommand" commandName="Generate Eclipse Help (*.html and *-toc.xml)" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7xGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7xWCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7xmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.debug.ui.commands.ForceReturn" commandName="Force Return" description="Forces return from method with value of selected expression" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7x2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7yGCGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui.importSession" commandName="Import Session..." category="_WMocG2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7yWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7ymCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7y2CGEfC3wuS16sDcWA" elementId="org.eclipse.lsp4e.toggleSortOutline" commandName="Sort" category="_WMpDJ2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7zGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_WMocImCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMt7zWCGEfC3wuS16sDcWA" elementId="title" name="Title"/>
    <parameters xmi:id="_WMt7zmCGEfC3wuS16sDcWA" elementId="message" name="Message"/>
    <parameters xmi:id="_WMt7z2CGEfC3wuS16sDcWA" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_WMt70GCGEfC3wuS16sDcWA" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_WMt70WCGEfC3wuS16sDcWA" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_WMt70mCGEfC3wuS16sDcWA" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_WMt702CGEfC3wuS16sDcWA" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_WMt71GCGEfC3wuS16sDcWA" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_WMt71WCGEfC3wuS16sDcWA" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_WMt71mCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt712CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt72GCGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jpa.ui.generateDDL" commandName="Generate Tables from Entities..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt72WCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xml.ui.referencedFileErrors" commandName="Show Details..." description="Show Details..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt72mCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.sqltools.sqleditor.runAction" commandName="Run" category="_WMpDMmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt722CGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt73GCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt73WCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.debug.ui.command.OpenFromClipboard" commandName="Open from Clipboard" description="Opens a Java element or a Java stack trace from clipboard" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt73mCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.internationalize" commandName="Internationalize Plug-ins" description="Sets up internationalization for a plug-in" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt732CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt74GCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt74WCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt74mCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt742CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt75GCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt75WCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.JavaHierarchyPerspective" commandName="Java Type Hierarchy" description="Show the Java Type Hierarchy perspective" category="_WMpDM2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt75mCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt752CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.multicatch" commandName="Surround with try/multi-catch Block" description="Surround the selected text with a try/multi-catch block" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt76GCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.Tag" commandName="Create Tag..." category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt76WCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt76mCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt762CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_WMocKmCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMt77GCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_WMt77WCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_WMt77mCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt772CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt78GCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter.object" commandName="Introduce Parameter Object" description="Introduce a parameter object to a selected method" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt78WCGEfC3wuS16sDcWA" elementId="org.eclipse.gef.zoom_out" commandName="Zoom Out" description="Zoom Out" category="_WMpDNWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt78mCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.commands.openElementInEditor" commandName="Open Java Element" description="Open a Java element in its editor" category="_WMpDJWCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMt782CGEfC3wuS16sDcWA" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_WMt79GCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected JavaScript comment lines" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt79WCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.sqltools.result.removeInstance" commandName="Remove Result" category="_WMocH2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt79mCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt792CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7-GCGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jpa.ui.persistentAttributeAddToXmlAndMap" commandName="Add Attribute to XML and Map..." category="_WMocJmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7-WCGEfC3wuS16sDcWA" elementId="org.eclipse.buildship.ui.commands.refreshtaskview" commandName="Refresh View (Gradle Tasks)" description="Refreshes the Gradle Tasks view" category="_WMocLGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7-mCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.sse.ui.toggle.comment" commandName="Toggle Comment" description="Toggle Comment" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7-2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_WMpDLWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7_GCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7_WCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7_mCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt7_2CGEfC3wuS16sDcWA" elementId="org.eclipse.oomph.setup.editor.synchronizePreferences" commandName="Synchronize Preferences" category="_WMpDI2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8AGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xml.ui.cmnd.contentmodel.sych" commandName="Synch" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8AWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8AmCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8A2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8BGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xsd.ui.refactor.renameTargetNamespace" commandName="Rename Target Namespace" description="Changes the target namespace of the schema" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8BWCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.builds.ui.command.AbortBuild" commandName="Abort Build" category="_WMocE2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8BmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8B2CGEfC3wuS16sDcWA" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_WMocK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8CGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.generate.javadoc" commandName="Generate Javadoc" description="Generates Javadoc for a selectable set of Java resources" category="_WMpDLmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8CWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8CmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.debug.ui.commands.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8C2CGEfC3wuS16sDcWA" elementId="org.eclipse.buildship.ui.shortcut.test.run" commandName="Run Gradle Test" description="Run Gradle test based on the current selection" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8DGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8DWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8DmCGEfC3wuS16sDcWA" elementId="org.eclipse.jst.pagedesigner.design" commandName="Graphical Designer" category="_WMocLmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8D2CGEfC3wuS16sDcWA" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_WMpDL2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8EGCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8EWCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.wikitext.ui.convertToHtmlCommand" commandName="Generate HTML" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8EmCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.openManifest" commandName="Open Manifest" description="Open the plug-in manifest" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8E2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8FGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.generate.tostring" commandName="Generate toString()" description="Generates the toString() method for the type" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8FWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.organize.imports" commandName="Organize Imports" description="Evaluate all required imports and replace the current imports" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8FmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8F2CGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8GGCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.api.tools.ui.compare.to.baseline" commandName="API Baseline..." description="Allows to compare the selected resource with the current baseline" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8GWCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.imagebrowser.saveToWorkspace" commandName="Save Image" description="Save the selected image into a project in the workspace" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8GmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.debug.ui.commands.Display" commandName="Display" description="Display result of evaluating selected text" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8G2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8HGCGEfC3wuS16sDcWA" elementId="org.eclipse.tm.terminal.view.ui.command.disconnect" commandName="Disconnect Terminal" category="_WMpDKGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8HWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Revision Information" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8HmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to field" description="Invokes quick assist and selects 'Assign to field'" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8H2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8IGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.method" commandName="Extract Method" description="Extract a set of statements or an expression into a new method and use the new method" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8IWCGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jpa.eclipselink.ui.newEclipseLinkMappingFile" commandName="EclipseLink ORM Mapping File" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8ImCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.openRemoteTask" commandName="Open Remote Task" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8I2CGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8JGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.CompareWithEachOther" commandName="Compare with Each Other" description="Compare two files selected in the Compare Editor with each other." category="_WMpDKWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8JWCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.context.ui.commands.task.retrieveContext" commandName="Retrieve Context" category="_WMocFWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8JmCGEfC3wuS16sDcWA" elementId="org.eclipse.jst.jsp.ui.refactor.rename" commandName="Rename" description="Rename a Java Element" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8J2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8KGCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.run" commandName="Run JUnit Plug-in Test" description="Run JUnit Plug-in Test" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8KWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.navigate.gotopackage" commandName="Go to Package" description="Go to Package" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8KmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8K2CGEfC3wuS16sDcWA" elementId="org.eclipse.codegen.ui.jet.format" commandName="Format" description="Format" category="_WMpDJGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8LGCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.task.ui.editor.QuickOutline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_WMocF2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8LWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.select.multiCaretUp" commandName="Multi caret up" description="Add a new caret/multi selection above the current line, or remove the last caret/multi selection " category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8LmCGEfC3wuS16sDcWA" elementId="org.eclipse.buildship.ui.commands.addbuildshipnature" commandName="Add Gradle Nature" description="Adds the Gradle nature and synchronizes this project as if the Gradle Import wizard had been run on its location." category="_WMocEmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8L2CGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8MGCGEfC3wuS16sDcWA" elementId="org.eclipse.jst.pagedesigner.horizotal" commandName="Horizontal Layout" category="_WMocLmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8MWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8MmCGEfC3wuS16sDcWA" elementId="org.eclipse.lsp4e.typeHierarchy" commandName="Quick Type Hierarchy" description="Open Quick Call Hierarchy for the selected item" category="_WMpDJ2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8M2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8NGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8NWCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8NmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8N2CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8OGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8OWCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.bugs.commands.newTaskFromMarker" commandName="New Task from Marker..." description="Report as Bug from Marker" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8OmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.server.run" commandName="Run" description="Run server" category="_WMocGWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8O2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.context.ui.commands.focus.view" commandName="Focus View" category="_WMocKmCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMt8PGCGEfC3wuS16sDcWA" elementId="viewId" name="View ID to Focus" optional="false"/>
  </commands>
  <commands xmi:id="_WMt8PWCGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui.testNgShortcut.coverage" commandName="Coverage TestNG Test" description="Coverage TestNG Test" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8PmCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8P2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_WMocHmCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMt8QGCGEfC3wuS16sDcWA" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_WMt8QWCGEfC3wuS16sDcWA" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_WMt8QmCGEfC3wuS16sDcWA" elementId="org.eclipse.jst.pagedesigner.source" commandName="Source Code" category="_WMocLmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8Q2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_WMpDLWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8RGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.common.project.facet.ui.ConvertProjectToFacetedForm" commandName="Convert to Faceted Form..." category="_WMocHGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8RWCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.annotate.classFile" commandName="Annotate Class File" description="Externally add Annotations to a Class File." category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8RmCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8R2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.command.activateSelectedTask" commandName="Activate Selected Task" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8SGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8SWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.sse.ui.format.document" commandName="Format" description="Format selection" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8SmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8S2CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8TGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8TWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_WMocHGCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMt8TmCGEfC3wuS16sDcWA" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_WMt8T2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8UGCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected Java comment lines" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8UWCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.builds.ui.commands.CopyDetails" commandName="Copy Details" category="_WMocE2CGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMt8UmCGEfC3wuS16sDcWA" elementId="kind" name="Kind"/>
    <parameters xmi:id="_WMt8U2CGEfC3wuS16sDcWA" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_WMt8VGCGEfC3wuS16sDcWA" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8VWCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.builds.ui.command.RunBuild" commandName="Run Build" category="_WMocE2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8VmCGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui.selectActiveSession" commandName="Select Active Session..." category="_WMocG2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8V2CGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with Each Other" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8WGCGEfC3wuS16sDcWA" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8WWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8WmCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.context.ui.commands.interest.decrement" commandName="Make Less Interesting" description="Make Less Interesting" category="_WMocFWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8W2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8XGCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Check Out" category="_WMpDMWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8XWCGEfC3wuS16sDcWA" elementId="org.eclipse.codegen.ui.jet.refactor.quickmenu" commandName="Show Refactor Quick Menu" description="Shows the refactor quick menu" category="_WMpDImCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8XmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="_WMpDMGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8X2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8YGCGEfC3wuS16sDcWA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_WMocKmCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMt8YWCGEfC3wuS16sDcWA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_WMt8YmCGEfC3wuS16sDcWA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_WMt8Y2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.gotoBreadcrumb" commandName="Show In Breadcrumb" description="Shows the Java editor breadcrumb and sets the keyboard focus into it" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8ZGCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.debug" commandName="Debug Eclipse Application" description="Debug Eclipse Application" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8ZWCGEfC3wuS16sDcWA" elementId="org.eclipse.userstorage.ui.showPullDown" commandName="Show Pull Down Menu" category="_WMpDLWCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMt8ZmCGEfC3wuS16sDcWA" elementId="intoolbar" name="In Tool Bar" optional="false"/>
  </commands>
  <commands xmi:id="_WMt8Z2CGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jpa.ui.makePersistent" commandName="Make Persistent..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8aGCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.sqltools.sqleditor.ExecuteSelectionAction" commandName="Execute Selected Text" category="_WMpDMmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8aWCGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jaxb.ui.command.createPackageInfo" commandName="Create package-info.java" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8amCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.class" commandName="Extract Class..." description="Extracts fields into a new class" category="_WMpDK2CGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8a2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="_WMpDNmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8bGCGEfC3wuS16sDcWA" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_WMpDKWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8bWCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_WMocIWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8bmCGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jpa.ui.newMappingFile" commandName="JPA ORM Mapping File" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8b2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8cGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into JavaScript comments" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8cWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8cmCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElementWithBrowser.url" commandName="Open Build with Browser" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8c2CGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jpa.ui.xmlFileUpgradeToLatestVersion" commandName="Upgrade JPA Document Version" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8dGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_WMocFGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8dWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.sse.ui.search.find.occurrences" commandName="Occurrences in File" description="Find occurrences of the selection in the file" category="_WMocEGCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8dmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_WMocHmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8d2CGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8eGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.sse.ui.outline.customFilter" commandName="&amp;Filters" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8eWCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.sqltools.sqlscrapbook.commands.openscrapbook" commandName="Open SQL Scrapboo&amp;k" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8emCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_WMpDJWCGEfC3wuS16sDcWA">
    <parameters xmi:id="_WMt8e2CGEfC3wuS16sDcWA" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_WMt8fGCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="_WMocFmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WMt8fWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the JavaScript file" category="_WMpDJWCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WPwokGCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.convertAutomaticManifest" commandName="org.eclipse.pde.ui.convertAutomaticManifest"/>
  <commands xmi:id="_WYTgkGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.ant.ui.actionSet.presentation/org.eclipse.ant.ui.toggleAutoReconcile" commandName="Toggle Ant Editor Auto Reconcile" description="Toggle Ant Editor Auto Reconcile" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYUHoGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.datatools.sqltools.sqlscrapbook.actionSet/org.eclipse.datatools.sqltools.sqlscrapbook.actions.OpenScrapbookAction" commandName="Open SQL Scrapbook" description="Open scrapbook to edit SQL statements" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYUusGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYUusWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYUusmCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYUus2CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYVVwGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYVVwWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYVVwmCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYVVw2CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYVVxGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYVVxWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.eclemma.ui.CoverageActionSet/org.eclipse.eclemma.ui.actions.CoverageDropDownAction" commandName="Coverage" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYVVxmCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.eclemma.ui.CoverageActionSet/org.eclipse.eclemma.ui.actions.CoverageAsAction" commandName="Coverage As" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYVVx2CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.eclemma.ui.CoverageActionSet/org.eclipse.eclemma.ui.actions.CoverageHistoryAction" commandName="Coverage History" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYVVyGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.egit.ui.SearchActionSet/org.eclipse.egit.ui.actions.OpenCommitSearchPage" commandName="Git..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYXK8GCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New Java Class" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYXK8WCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenPackageWizard" commandName="Package..." description="New Java Package" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYXK8mCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenProjectWizard" commandName="Java Project..." description="New Java Project" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYXyAGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.ui.SearchActionSet/org.eclipse.jdt.ui.actions.OpenJavaSearchPage" commandName="Java..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYXyAWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jst.j2ee.J2eeMainActionSet/org.eclipse.jst.j2ee.internal.actions.NewJavaEEArtifact" commandName="Servlet" description="Create a new Java/Jakarta Servlet, optionally adding it to a project's deployment descriptor." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYXyAmCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jst.j2ee.J2eeMainActionSet/org.eclipse.jst.j2ee.internal.actions.NewJavaEEProject" commandName="Dynamic Web Project" description="Create a Dynamic Web project. A Dynamic Web project supports developing Java and Jakarta Servlets, and deploys to Java and Jakarta Servlet Containers and J2EE, Java EE, and Jakarta EE certified application servers." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYYZEGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.mylyn.java.actionSet.browsing/org.eclipse.mylyn.java.ui.actions.ApplyMylynToBrowsingPerspectiveAction" commandName="Focus Browsing Perspective" description="Focus Java Browsing Views on Active Task" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYYZEWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.navigation.additions/org.eclipse.mylyn.tasks.ui.navigate.task.history" commandName="Activate Previous Task" description="Activate Previous Task" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYYZEmCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.pde.ui.SearchActionSet/org.eclipse.pde.ui.actions.OpenPluginSearchPage" commandName="Plug-in..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYYZE2CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYYZFGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYYZFWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYYZFmCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYYZF2CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYYZGGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.wst.server.ui.new.actionSet/org.eclipse.wst.server.ui.action.new.server" commandName="Create Server" description="Create Server" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYYZGWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.open" commandName="Open Web Browser" description="Open Web Browser" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYYZGmCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.switch" commandName="Web Browser" description="Web Browser" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYZAIGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.wst.web.ui.wizardsActionSet/org.eclipse.wst.web.ui.actions.newCSSFile" commandName="CSS" description="Create a new Cascading Style Sheet" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYZAIWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.wst.web.ui.wizardsActionSet/org.eclipse.wst.web.ui.actions.newJSFile" commandName="JavaScript" description="Create a new JavaScript file" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYZAImCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.wst.web.ui.wizardsActionSet/org.eclipse.wst.web.ui.actions.newHTMLFile" commandName="HTML" description="Create a new HTML page" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYZAI2CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.wst.ws.explorer.explorer/org.eclipse.wst.ws.internal.explorer.action.LaunchWSEAction" commandName="Launch the Web Services Explorer" description="Launch the Web Services Explorer" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYZAJGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.ant.ui.BreakpointRulerActions/org.eclipse.ant.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYZAJWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.datatools.sqltools.rullerDoubleClick/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Add Breakpoint" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYZnMGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.emf.exporter.genModelEditorContribution/org.eclipse.emf.exporter.ui.GenModelExportActionDelegate.Editor" commandName="Export Model..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYZnMWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.emf.importer.genModelEditorContribution/org.eclipse.emf.importer.ui.GenModelReloadActionDelegate.Editor" commandName="Reload..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYZnMmCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.RemoveMappingActionID" commandName="Remove Mapping" description="Remove the mapping associated with the selected objects." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYZnM2CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.TypeMatchMappingActionID" commandName="Match Mapping by Type" description="Create child mappings automatically by type." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYZnNGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.NameMatchMappingActionID" commandName="Match Mapping by Name" description="Create child mappings automatically by name." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYZnNWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.CreateOneSidedMappingActionID" commandName="Create One-sided Mapping" description="Create a new mapping for the selected object." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYaOQGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.CreateMappingActionID" commandName="Create Mapping" description="Create a new mapping between the selected objects." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYaOQWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.ecore2ecore.action.AddOuputRootActionID" commandName="Add Output Root..." description="Add new output root." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYaOQmCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.ecore2ecore.action.AddInputRootActionID" commandName="Add Input Root..." description="Add new input root." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYaOQ2CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYaORGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYaORWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYaORmCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYaOR2CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetExecute" commandName="Execute" description="Execute the Selected Text" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYaOSGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetDisplay" commandName="Display" description="Display Result of Evaluating Selected Text" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYaOSWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetInspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYa1UGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYa1UWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYa1UmCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYa1U2CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYa1VGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYa1VWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jst.jsp.core.jspsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYa1VmCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jst.jsp.core.jspsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYa1V2CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.lsp4e.debug.textEditor.rulerActions/org.eclipse.lsp4e.debug.textEditor.doubleClickBreakpointAction" commandName="unusedlabel" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYa1WGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.lsp4e.debug.genericEditor.rulerActions/org.eclipse.lsp4e.debug.genericEditor.doubleClickBreakpointAction" commandName="unusedlabel" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYa1WWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution/org.eclipse.m2e.jdt.ui.downloadSourcesAction" commandName="label" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYa1WmCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution_38/org.eclipse.m2e.jdt.ui.downloadSourcesAction_38" commandName="label" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYa1W2CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Text Editor Bookmark Ruler Action" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYa1XGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYa1XWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.wst.css.core.csssource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYbcYGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.wst.css.core.csssource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYbcYWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.wst.dtd.core.dtdsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYbcYmCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.wst.dtd.core.dtdsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYbcY2CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.wst.html.core.htmlsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYcDcGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.wst.html.core.htmlsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYcDcWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="JavaScript Editor Bookmark Ruler Action" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYcDcmCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="JavaScript Editor Ruler Single-Click" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYcqgGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="JavaScript Editor Ruler Single-Click" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYcqgWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="JavaScript Editor Bookmark Ruler Action" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYcqgmCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="JavaScript Editor Ruler Single-Click" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYcqg2CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.ui.articles.action.contribution.editor/org.eclipse.wst.wsdl.ui.actions.ReloadDependenciesActionDelegate" commandName="Reload Dependencies" description="Reload Dependencies" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYcqhGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.wst.wsdl.wsdlsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYcqhWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.wst.wsdl.wsdlsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYcqhmCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.core.runtime.xml.source.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYcqh2CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.core.runtime.xml.source.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYdRkGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.wst.xsd.core.xsdsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYdRkWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.wst.xsd.core.xsdsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYdRkmCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYd4oGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYd4oWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYd4omCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYefsGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYefsWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYefsmCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYefs2CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYeftGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYeftWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYfGwGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYfGwWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYfGwmCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYfGw2CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYfGxGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYft0GCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYft0WCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYft0mCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYft02CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYft1GCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYft1WCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYft1mCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variablesViewActions.AllReferencesInView" commandName="Show References" description="Shows references to each object in the variables view as an array of objects." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYft12CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYft2GCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYft2WCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYft2mCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYft22CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYft3GCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.AllReferencesInView" commandName="Show References" description="Show &amp;References" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYft3WCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYgU4GCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYgU4WCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYgU4mCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYgU42CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.actions.AddException" commandName="Add Java Exception Breakpoint" description="Add Java Exception Breakpoint" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYgU5GCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.breakpointViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYgU5WCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowThreadGroups" commandName="Show Thread Groups" description="Show Thread Groups" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYgU5mCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYgU52CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowSystemThreads" commandName="Show System Threads" description="Show System Threads" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYgU6GCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowRunningThreads" commandName="Show Running Threads" description="Show Running Threads" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYgU6WCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowMonitorThreadInfo" commandName="Show Monitors" description="Show the Thread &amp; Monitor Information" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYgU6mCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Watch" commandName="Watch" description="Create a Watch Expression from the Selected Text" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYgU62CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Execute" commandName="Execute" description="Execute the Selected Text" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYg78GCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Display" commandName="Display" description="Display Result of Evaluating Selected Text" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYg78WCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Inspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYg78mCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.mylyn.context.ui.outline.contribution/org.eclipse.mylyn.context.ui.contentOutline.focus" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYg782CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.mylyn.java.ui.markers.breakpoints.contribution/org.eclipse.mylyn.java.ui.actions.focus.markers.breakpoints" commandName="Focus on Active Task" description="Focus on Active Task" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYg79GCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.mylyn.ui.debug.view.contribution/org.eclipse.mylyn.ui.actions.FilterResourceNavigatorAction" commandName="Focus on Active Task (Experimental)" description="Focus on Active Task (Experimental)" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYg79WCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.mylyn.ui.projectexplorer.filter/org.eclipse.mylyn.ide.ui.actions.focus.projectExplorer" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYg79mCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.mylyn.ui.search.contribution/org.eclipse.mylyn.ide.ui.actions.focus.search.results" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYg792CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.mylyn.ui.resource.navigator.filter/org.eclipse.mylyn.ide.ui.actions.focus.resourceNavigator" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYg7-GCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.mylyn.problems.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.problems" commandName="Focus on Active Task" description="Focus on Active Task" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYg7-WCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.mylyn.markers.all.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.all" commandName="Focus on Active Task" description="Focus on Active Task" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYg7-mCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.mylyn.markers.tasks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.tasks" commandName="Focus on Active Task" description="Focus on Active Task" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYg7-2CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.mylyn.markers.bookmarks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.bookmarks" commandName="Focus on Active Task" description="Focus on Active Task" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYg7_GCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.mylyn.java.explorer.contribution/org.eclipse.mylyn.java.actions.focus.packageExplorer" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYg7_WCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.search.open" commandName="Search Repository..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYg7_mCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.synchronize.changed" commandName="Synchronize Changed" description="Synchronize Changed" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYg7_2CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.tasks.restore" commandName="Restore Tasks from History..." category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYg8AGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.open.repositories.view" commandName="Show Task Repositories View" description="Show Task Repositories View" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYg8AWCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.doc.legend.show.action" commandName="Show UI Legend" description="Show Tasks UI Legend" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYg8AmCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.context.ui.actions.tasklist.focus" commandName="Focus on Workweek" description="Focus on Workweek" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYg8A2CGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.pde.ui.logViewActions/org.eclipse.jdt.debug.ui.LogViewActions.showStackTrace" commandName="Show Stack Trace in Console View" description="Show Stack Trace in Console View" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <commands xmi:id="_WYhjAGCGEfC3wuS16sDcWA" elementId="AUTOGEN:::org.eclipse.ui.articles.action.contribution.view/org.eclipse.wst.wsi.ui.internal.actions.actionDelegates.ValidateWSIProfileActionDelegate" commandName="WS-I Profile Validator" description="Validate WS-I Message Log File" category="_WMocKmCGEfC3wuS16sDcWA"/>
  <addons xmi:id="_WMQormCGEfC3wuS16sDcWA" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_WMQor2CGEfC3wuS16sDcWA" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_WMQosGCGEfC3wuS16sDcWA" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_WMQosWCGEfC3wuS16sDcWA" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_WMQosmCGEfC3wuS16sDcWA" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_WMQos2CGEfC3wuS16sDcWA" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_WMQotGCGEfC3wuS16sDcWA" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_WMQotWCGEfC3wuS16sDcWA" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_WMQotmCGEfC3wuS16sDcWA" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_WMQot2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_WMZykGCGEfC3wuS16sDcWA" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_tCNssGCGEfCG3J0YabKHdQ" elementId="org.eclipse.ui.ide.addon.0" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_dz0JgGOlEeSMMaPQU2nlzw" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <categories xmi:id="_WMocEGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_WMocEWCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.wikitext.ui.editor.category" name="WikiText Markup Editing Commands" description="commands for editing lightweight markup"/>
  <categories xmi:id="_WMocEmCGEfC3wuS16sDcWA" elementId="org.eclipse.buildship.ui.project" name="Buildship" description="Contains the Buildship specific commands"/>
  <categories xmi:id="_WMocE2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.builds.ui.category.Commands" name="Builds"/>
  <categories xmi:id="_WMocFGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_WMocFWCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.context.ui.commands" name="Focused UI" description="Task-Focused Interface"/>
  <categories xmi:id="_WMocFmCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.jsdt.ui.category.source" name="Source" description="JavaScript Source Actions"/>
  <categories xmi:id="_WMocF2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.commands" name="Task Repositories"/>
  <categories xmi:id="_WMocGGCGEfC3wuS16sDcWA" elementId="AnsiConsole.command.categoryid" name="ANSI Support Commands"/>
  <categories xmi:id="_WMocGWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.server.ui" name="Server" description="Server"/>
  <categories xmi:id="_WMocGmCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.wikitext.context.ui.commands" name="Mylyn WikiText" description="Commands used for Mylyn WikiText"/>
  <categories xmi:id="_WMocG2CGEfC3wuS16sDcWA" elementId="org.eclipse.eclemma.ui" name="EclEmma Code Coverage"/>
  <categories xmi:id="_WMocHGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_WMocHWCGEfC3wuS16sDcWA" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_WMocHmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_WMocH2CGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.sqltools.result.category" name="SQL Results View"/>
  <categories xmi:id="_WMocIGCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.genericeditor.extension.category.source" name="Target Definition Source" description="Target Definition Source Page actions"/>
  <categories xmi:id="_WMocIWCGEfC3wuS16sDcWA" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_WMocImCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_WMocI2CGEfC3wuS16sDcWA" elementId="org.eclipse.oomph" name="Oomph"/>
  <categories xmi:id="_WMocJGCGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jpa.ui.jpaMetadataConversionCommands" name="JPA Metadata Conversion"/>
  <categories xmi:id="_WMocJWCGEfC3wuS16sDcWA" elementId="org.eclipse.wst.xml.views.XPathView" name="XPath"/>
  <categories xmi:id="_WMocJmCGEfC3wuS16sDcWA" elementId="org.eclipse.jpt.jpa.ui.jpaStructureViewCommands" name="JPA Structure View"/>
  <categories xmi:id="_WMocJ2CGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.commons.repositories.ui.category.Team" name="Team"/>
  <categories xmi:id="_WMocKGCGEfC3wuS16sDcWA" elementId="org.eclipse.pde.ui.category.source" name="Manifest Editor Source" description="PDE Source Page actions"/>
  <categories xmi:id="_WMocKWCGEfC3wuS16sDcWA" elementId="org.eclipse.oomph.commands" name="Oomph"/>
  <categories xmi:id="_WMocKmCGEfC3wuS16sDcWA" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_WMocK2CGEfC3wuS16sDcWA" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_WMocLGCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_WMocLWCGEfC3wuS16sDcWA" elementId="org.eclipse.tm4e.languageconfiguration.category" name="TM4E Language Configuration"/>
  <categories xmi:id="_WMocLmCGEfC3wuS16sDcWA" elementId="org.eclipse.jst.pagedesigner.pagelayout" name="Web Page Editor Layout"/>
  <categories xmi:id="_WMpDIGCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.tasks.ui.category.editor" name="Task Editor"/>
  <categories xmi:id="_WMpDIWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_WMpDImCGEfC3wuS16sDcWA" elementId="org.eclipse.codegen.ui.jet.refactor" name="JET Refactor Actions"/>
  <categories xmi:id="_WMpDI2CGEfC3wuS16sDcWA" elementId="org.eclipse.oomph.setup.category" name="Oomph Setup"/>
  <categories xmi:id="_WMpDJGCGEfC3wuS16sDcWA" elementId="org.eclipse.codegen.ui.jet.source" name="JET Source Actions"/>
  <categories xmi:id="_WMpDJWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_WMpDJmCGEfC3wuS16sDcWA" elementId="org.eclipse.mylyn.java.ui.commands" name="Java Context" description="Java Task-Focused Interface Commands"/>
  <categories xmi:id="_WMpDJ2CGEfC3wuS16sDcWA" elementId="org.eclipse.lsp4e.category" name="Language Servers"/>
  <categories xmi:id="_WMpDKGCGEfC3wuS16sDcWA" elementId="org.eclipse.tm.terminal.view.ui.commands.category" name="Terminal Commands"/>
  <categories xmi:id="_WMpDKWCGEfC3wuS16sDcWA" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_WMpDKmCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.enablement.sybase.asa.schemaobjecteditor.examples.tableschemaedtor.10x" name="ASA 9.x table schema editor"/>
  <categories xmi:id="_WMpDK2CGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.category.refactoring" name="Refactor - Java" description="Java Refactoring Actions"/>
  <categories xmi:id="_WMpDLGCGEfC3wuS16sDcWA" elementId="org.eclipse.emf.codegen.ecore.ui.Commands" name="EMF Code Generation" description="Commands for the EMF code generation tools"/>
  <categories xmi:id="_WMpDLWCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_WMpDLmCGEfC3wuS16sDcWA" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_WMpDL2CGEfC3wuS16sDcWA" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_WMpDMGCGEfC3wuS16sDcWA" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_WMpDMWCGEfC3wuS16sDcWA" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_WMpDMmCGEfC3wuS16sDcWA" elementId="org.eclipse.datatools.sqltools.sqleditor.category" name="Database Tools" description="Database Development tools"/>
  <categories xmi:id="_WMpDM2CGEfC3wuS16sDcWA" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_WMpDNGCGEfC3wuS16sDcWA" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_WMpDNWCGEfC3wuS16sDcWA" elementId="org.eclipse.gef.category.view" name="View" description="View"/>
  <categories xmi:id="_WMpDNmCGEfC3wuS16sDcWA" elementId="org.eclipse.jdt.ui.category.source" name="Source" description="Java Source Actions"/>
  <categories xmi:id="_WMpDN2CGEfC3wuS16sDcWA" elementId="org.eclipse.pde.runtime.spy.commands.category" name="Spy"/>
</application:Application>
