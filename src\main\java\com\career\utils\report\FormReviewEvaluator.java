package com.career.utils.report;

import com.career.db.*;
import com.career.utils.*;
import com.career.utils.report.kit.FormReviewUtils;

import java.util.*;
import java.util.Random;

/**
 * 志愿表单审核评估器
 * 
 * 功能特性：
 * 1. 基础10个维度评估：分段定位、院校梯度、院校完整度、专业完整度、院校梯度顺序、专业梯度顺序、专业服从调剂、保底院校风险、区域优势、专业集中度
 * 2. 用户需求5个维度评估：个人适配性、经济适配性、地域偏好匹配、专业偏好匹配、身体条件适配
 * 3. 每个维度都会生成对应的等级（A/B/C/D）和具体建议
 * 4. 支持录取概率分析、志愿统计、专业组干净度分析等扩展功能
 * 
 * 使用示例：
 * <pre>
 * // 执行评估
 * FormReviewResult result = FormReviewEvaluator.reviewForm(params);
 * 
 * // 获取维度等级和建议
 * String grade1 = result.getGrade1();           // 分段定位等级
 * String suggestion1 = result.getSuggestion1(); // 分段定位建议
 * 
 * // 批量获取所有建议
 * String[] allSuggestions = result.getAllSuggestions(true); // 包含用户需求维度
 * 
 * // 根据维度编号获取建议
 * String suggestion = result.getSuggestionByDimension(1); // 获取第1个维度的建议
 * 
 * // 前台展示示例
 * String[] dimensionNames = FormReviewEvaluator.getDimensionNames(true);
 * String[] dimensionGrades = {result.getGrade1(), result.getGrade2(), ...};
 * String[] dimensionSuggestions = result.getAllSuggestions(true);
 * 
 * for (int i = 0; i < dimensionNames.length; i++) {
 *     System.out.println(dimensionNames[i] + ": " + dimensionGrades[i] + " - " + dimensionSuggestions[i]);
 * }
 * </pre>
 * 
 * <AUTHOR>
 * @version 2.0 - 支持动态建议生成
 */
public class FormReviewEvaluator {
    
    // ==================== 评级常量 ====================
    /** A级评价 - 优秀 */
    public static final String GRADE_A = "A";
    /** B级评价 - 良好 */
    public static final String GRADE_B = "B"; 
    /** C级评价 - 一般 */
    public static final String GRADE_C = "C";
    /** D级评价 - 较差 */
    public static final String GRADE_D = "D";
    
    // ==================== 维度数量常量 ====================
    /** 基础评估维度数量：前10个维度始终执行 */
    public static final int BASIC_DIMENSION_COUNT = 10;
    /** 用户需求评估维度数量：后5个维度仅在有用户需求数据时执行 */
    public static final int USER_NEEDS_DIMENSION_COUNT = 5;
    /** 总维度数量：最多15个维度 */
    public static final int TOTAL_DIMENSION_COUNT = BASIC_DIMENSION_COUNT + USER_NEEDS_DIMENSION_COUNT;
    
    // ==================== 冲稳保配置 ====================
    /** 冲击志愿数量：用于填报高于考生分数的院校，追求更好录取机会 */
    public static int DEFAULT_CHONG_COUNT = 16;
    /** 稳妥志愿数量：用于填报与考生分数相近的院校，确保录取概率 */
    public static int DEFAULT_WEN_COUNT = 14;
    /** 保底志愿数量：用于填报低于考生分数的院校，保证录取安全 */
    public static int DEFAULT_BAO_COUNT = 15;
    /** 总志愿数量：新高考平行志愿总数 */
    public static int TOTAL_VOLUNTEER_COUNT = 45;
    /** 每个志愿可填报的专业数量：6个（可配置参数） */
    public static int MAX_MAJORS_PER_VOLUNTEER = 6;
    
    
    // ==================== 分段定位配置 ====================
    /** 位次优秀线：前5000名认为位次优秀 */
    public static final int EXCELLENT_RANK_THRESHOLD = 5000;
    /** 位次良好线：前15000名认为位次良好 */
    public static final int GOOD_RANK_THRESHOLD = 15000;
    /** 位次中等线：前50000名认为位次中等 */
    public static final int MEDIUM_RANK_THRESHOLD = 50000;
    
    // ==================== 院校梯度配置 ====================
    /** 梯度分析最小分数差：院校间最小分数差距 */
    public static final double MIN_GRADIENT_SCORE = 2.0;
    /** 梯度分析最大分数差：院校间最大分数差距 */
    public static final double MAX_GRADIENT_SCORE = 10.0;
    /** 梯度合理率A级阈值：85%以上的梯度合理认为优秀 */
    public static final double GRADIENT_REASONABLE_RATE_A = 0.85;
    /** 梯度合理率B级阈值：65%以上的梯度合理认为良好 */
    public static final double GRADIENT_REASONABLE_RATE_B = 0.65;
    /** 梯度合理率C级阈值：45%以上的梯度合理认为一般 */
    public static final double GRADIENT_REASONABLE_RATE_C = 0.45;
    
    // ==================== 院校完整度评估配置 ====================
    /** 院校完整度A级比例：89%以上（45个志愿的40个及以上） */
    public static final double COLLEGE_COMPLETENESS_A_RATIO = 0.85;
    /** 院校完整度B级比例：78%以上（45个志愿的35个及以上） */
    public static final double COLLEGE_COMPLETENESS_B_RATIO = 0.70;
    /** 院校完整度C级比例：67%以上（45个志愿的30个及以上） */
    public static final double COLLEGE_COMPLETENESS_C_RATIO = 0.58;    
    
    // ==================== 专业完整度配置 ====================
    /** 专业填报率A级阈值：60%以上填报率认为优秀 */
    public static final double MAJOR_FILL_RATE_A = 0.6;
    /** 专业填报率最低标准：每个院校专业组至少填报4个专业 */
    public static final int MIN_MAJOR_COUNT_PER_GROUP = 4;
    /** 专业填报率基础标准：每个院校专业组至少填报3个专业 */
    public static final int BASIC_MAJOR_COUNT_PER_GROUP = 3;
    /** 专业覆盖率良好阈值：30%以上的专业覆盖率认为良好 */
    public static final double MAJOR_COVERAGE_RATE_GOOD = 0.3;
    /** 专业完整度A级阈值：85%以上的专业组完整度认为优秀 */
    public static final double MAJOR_COMPLETENESS_A = 0.85;
    /** 专业完整度B级阈值：65%以上的专业组完整度认为良好 */
    public static final double MAJOR_COMPLETENESS_B = 0.65;
    /** 专业完整度C级阈值：45%以上的专业组完整度认为一般 */
    public static final double MAJOR_COMPLETENESS_C = 0.45;
    
    // ==================== 冲稳保分类配置 ====================
    /** 冲击志愿分数差：高于用户分数8分以上认为冲击 */
    public static final int CHONG_SCORE_DIFF = 8;
    /** 保底志愿分数差：低于用户分数15分以上认为保底 */
    public static final int BAO_SCORE_DIFF = 15;
    /** 冲击志愿数量范围 - 最小值 */
    public static final int CHONG_COUNT_MIN = 10;
    /** 冲击志愿数量范围 - 最大值 */
    public static final int CHONG_COUNT_MAX = 20;
    /** 稳妥志愿数量范围 - 最小值 */
    public static final int WEN_COUNT_MIN = 10;
    /** 稳妥志愿数量范围 - 最大值 */
    public static final int WEN_COUNT_MAX = 20;
    /** 保底志愿数量范围 - 最小值 */
    public static final int BAO_COUNT_MIN = 10;
    /** 保底志愿数量范围 - 最大值 */
    public static final int BAO_COUNT_MAX = 20;
    
    // ==================== 专业梯度配置 ====================
    /** 专业顺序错误率阈值：30%以下的错误率认为合理 */
    public static final double MAJOR_ORDER_ERROR_RATE_THRESHOLD = 0.3;
    /** 专业梯度顺序A级阈值：85%以上的院校专业顺序合理 */
    public static final double MAJOR_GRADIENT_ORDER_A = 0.85;
    /** 专业梯度顺序B级阈值：65%以上的院校专业顺序合理 */
    public static final double MAJOR_GRADIENT_ORDER_B = 0.65;
    /** 专业梯度顺序C级阈值：45%以上的院校专业顺序合理 */
    public static final double MAJOR_GRADIENT_ORDER_C = 0.45;
    
    // ==================== 专业服从调剂配置 ====================
    /** 服从调剂标识：1表示服从调剂 */
    public static final int OBEDIENCE_FLAG = 1;
    /** 服从调剂A级阈值：90%以上的院校选择服从调剂 */
    public static final double OBEDIENCE_RATE_A = 0.99;
    /** 服从调剂B级阈值：70%以上的院校选择服从调剂 */
    public static final double OBEDIENCE_RATE_B = 0.9;
    /** 服从调剂C级阈值：50%以上的院校选择服从调剂 */
    public static final double OBEDIENCE_RATE_C = 0.7;
    
    // ==================== 保底院校风险配置 ====================
    /** 保底院校检查数量：检查最后10个志愿的保底安全性 */
    public static final int SAFETY_CHECK_COUNT = 10;
    /** 保底院校安全分数差：保底院校分数应低于考生分数15分以上 */
    public static final int SAFETY_SCORE_DIFF = 15;
    /** 保底院校安全分数差（位次较好时）：考生位次较好时可适当降低安全边际 */
    public static final int SAFETY_SCORE_DIFF_GOOD_RANK = 10;
    /** 位次较好阈值：前50000名认为位次较好 */
    public static final int GOOD_RANK_FOR_SAFETY = 50000;
    /** 保底院校安全率A级阈值：80%以上的保底院校安全 */
    public static final double SAFETY_RATE_A = 0.8;
    /** 保底院校安全率B级阈值：60%以上的保底院校安全 */
    public static final double SAFETY_RATE_B = 0.60;
    /** 保底院校安全率C级阈值：50%以上的保底院校安全 */
    public static final double SAFETY_RATE_C = 0.5;
    
    // ==================== 区域优势配置 ====================
    /** 地域多样性阈值：覆盖5个以上省份认为多样化 */
    public static final int REGION_DIVERSITY_THRESHOLD = 5;
    /** 发达地区比例适宜范围 - 最小值：30%以上发达地区院校 */
    public static final double DEVELOPED_REGION_RATE_MIN = 0.3;
    /** 发达地区比例适宜范围 - 最大值：70%以下发达地区院校 */
    public static final double DEVELOPED_REGION_RATE_MAX = 0.7;
    /** 本省院校比例适宜范围 - 最小值：20%以上本省院校 */
    public static final double LOCAL_REGION_RATE_MIN = 0.2;
    /** 本省院校比例适宜范围 - 最大值：60%以下本省院校 */
    public static final double LOCAL_REGION_RATE_MAX = 0.6;
    
    // ==================== 专业集中度配置 ====================
    /** 专业类别多样性A级阈值：8个以上专业类别 */
    public static final int MAJOR_CATEGORY_COUNT_A = 8;
    /** 专业类别多样性B级阈值：5个以上专业类别 */
    public static final int MAJOR_CATEGORY_COUNT_B = 5;
    /** 专业类别多样性C级阈值：3个以上专业类别 */
    public static final int MAJOR_CATEGORY_COUNT_C = 3;
    /** 专业过度集中阈值：超过50%集中在一个专业类别认为过度集中 */
    public static final double MAJOR_OVER_CONCENTRATION_THRESHOLD = 0.5;
    /** 专业集中度指数阈值：集中度指数低于0.4认为分布合理 */
    public static final double MAJOR_CONCENTRATION_INDEX_THRESHOLD = 0.4;
    
    // ==================== 权重配置 ====================
    /** 基础评估维度权重：前10个维度的权重 */
    public static final double BASIC_DIMENSION_WEIGHT = 1.0;
    /** 用户需求评估维度权重 - 个人适配性 */
    public static final double PERSONAL_FITNESS_WEIGHT = 0.8;
    /** 用户需求评估维度权重 - 经济适配性 */
    public static final double ECONOMIC_FITNESS_WEIGHT = 0.8;
    /** 用户需求评估维度权重 - 地域偏好匹配 */
    public static final double GEOGRAPHIC_PREFERENCE_WEIGHT = 0.6;
    /** 用户需求评估维度权重 - 专业偏好匹配 */
    public static final double MAJOR_PREFERENCE_WEIGHT = 0.6;
    /** 用户需求评估维度权重 - 身体条件适配 */
    public static final double PHYSICAL_FITNESS_WEIGHT = 0.7;
    
    // ==================== 综合评分配置 ====================
    /** A级分数：95分 */
    public static final double GRADE_A_SCORE = 95.0;
    /** B级分数：85分 */
    public static final double GRADE_B_SCORE = 85.0;
    /** C级分数：70分 */
    public static final double GRADE_C_SCORE = 70.0;
    /** D级分数：55分 */
    public static final double GRADE_D_SCORE = 55.0;
    
    // ==================== 通用等级判断方法 ====================
    
    /**
     * 通用等级判断方法，减少重复代码
     * @param value 待评估的值
     * @param thresholdA A级阈值
     * @param thresholdB B级阈值  
     * @param thresholdC C级阈值
     * @return 评估等级
     */
    public static String determineGrade(double value, double thresholdA, double thresholdB, double thresholdC) {
        if (value >= thresholdA) return GRADE_A;
        if (value >= thresholdB) return GRADE_B;
        if (value >= thresholdC) return GRADE_C;
        return GRADE_D;
    }
    
    /**
     * 通用等级判断方法（整数版本）
     */
    public static String determineGrade(int value, int thresholdA, int thresholdB, int thresholdC) {
        if (value >= thresholdA) return GRADE_A;
        if (value >= thresholdB) return GRADE_B;
        if (value >= thresholdC) return GRADE_C;
        return GRADE_D;
    }          
    
    // ==================== 梯度计算配置 ====================
    /** 梯度计算权重因子 - 最小范围权重：0.3 */
    public static final double GRADIENT_RANGE_MIN_WEIGHT = 0.3;
    /** 梯度计算权重因子 - 最大范围权重：1.5 */
    public static final double GRADIENT_RANGE_MAX_WEIGHT = 1.5;
    
    // ==================== 历年分数权重配置 ====================
    /** 三年分数权重 - 最近年权重：65% */
    public static final double RECENT_YEAR_WEIGHT = 0.8;
    /** 三年分数权重 - 前一年权重：25% */
    public static final double SECOND_YEAR_WEIGHT = 0.1;
    /** 三年分数权重 - 前两年权重：10% */
    public static final double THIRD_YEAR_WEIGHT = 0.1;
    /** 两年分数权重 - 主要年份权重：70% */
    public static final double TWO_YEAR_PRIMARY_WEIGHT = 0.75;
    /** 两年分数权重 - 次要年份权重：30% */
    public static final double TWO_YEAR_SECONDARY_WEIGHT = 0.25;
    
    // ==================== 院校数量比例配置 ====================
    /** 院校数量A级比例：85% */
    public static final double COLLEGE_COUNT_A_RATIO = 0.85;
    /** 院校数量B级比例：65% */
    public static final double COLLEGE_COUNT_B_RATIO = 0.65;
    /** 院校数量C级比例：50% */
    public static final double COLLEGE_COUNT_C_RATIO = 0.50;
    
    // ==================== 趋势变化阈值配置 ====================
    /** 分数趋势判断阈值：3分 */
    public static final double SCORE_TREND_THRESHOLD = 3.0;
    /** 计划变化率阈值：10% */
    public static final double PLAN_CHANGE_RATE_THRESHOLD = 0.1;
    
    // ==================== 专业顺序检验配置 ====================
    /** 专业顺序允许错误数：1个 */
    public static final int MAJOR_ORDER_ALLOWED_ERRORS = 1;
    
    // ==================== 分段定位评估配置 ====================
    /** 分段定位分数差值阈值 - A级：30分以上 */
    public static final int POSITION_SCORE_DIFF_A = 30;
    /** 分段定位分数差值阈值 - B级：20分以上 */
    public static final int POSITION_SCORE_DIFF_B = 20;
    /** 分段定位分数差值阈值 - C级：15分以上 */
    public static final int POSITION_SCORE_DIFF_C = 15;
   
    
    // ==================== 专业完整度评估配置 ====================
    /** 专业填报完整度B级阈值：80%以上 */
    public static final double MAJOR_FILL_COMPLETENESS_B = 0.8;
    /** 专业填报完整度C级阈值：60%以上 */
    public static final double MAJOR_FILL_COMPLETENESS_C = 0.6;
    
    /**
     * 表单审核参数类
     * 封装所有审核所需的数据和配置
     */
    public static class FormReviewParams {
        private List<SuperForm> superFormList;
        private SuperFormMain superFormMain;
        private ZyzdProvinceConfig provinceConfig;
        private ZDKSRank userRank;
        private ZDKSRank rankYearA;
        private ZDKSRank rankYearB;
        private ZDKSRank rankYearC;
        
        /** 用户需求数据 - 个人适配性等5个维度的评估依据 */
        private RtFillNeedsInfoBean userNeedsData;
        
        /**
         * 新的简化构造函数
         * @param superFormMain 表单主对象，包含用户基本信息和成绩
         * @param provinceConfig 省份配置，包含最新计划年度等信息
         * @param userNeedsData 用户需求数据，用于个人适配性等维度评估
         */
        public FormReviewParams(SuperFormMain superFormMain, ZyzdProvinceConfig provinceConfig, RtFillNeedsInfoBean userNeedsData) {
            this.superFormMain = superFormMain;
            this.superFormList = superFormMain.getSuperFormList();
            this.provinceConfig = provinceConfig;
            this.userNeedsData = userNeedsData;
            
            // 初始化rank数据
            initializeRankData(superFormMain, provinceConfig);
            
            // 初始化志愿配置
            initializeVolunteerConfig(superFormMain);
        }
        
        /**
         * 从数据库查询获取rank相关数据
         */
        private void initializeRankData(SuperFormMain superFormMain, ZyzdProvinceConfig provinceConfig) {
            try {
                ZyzdJDBC jdbc = new ZyzdJDBC();
                int LATEST_JH_YEAR = provinceConfig.getLatest_year_jh();
                String userXkCode = XKCombineUtils.getXKCodeByStudentSelection(superFormMain.getScore_xk());
                int userScore = superFormMain.getScore_cj();
                
                // 获取同位分数据
                HashMap<String, ZDKSRank> TONGWF_MAP = jdbc.getAllTongWF(provinceConfig.getP_name(), userXkCode, superFormMain.getScore_wc()); 
                this.rankYearA = TONGWF_MAP.get(String.valueOf(LATEST_JH_YEAR - 1));
                this.rankYearB = TONGWF_MAP.get(String.valueOf(LATEST_JH_YEAR - 2));
                this.rankYearC = TONGWF_MAP.get(String.valueOf(LATEST_JH_YEAR - 3));
                
                // 获取用户位次数据
                this.userRank = jdbc.getZDKSWCByScore(LATEST_JH_YEAR, provinceConfig.getP_name(), userXkCode, userScore);
                
            } catch (Exception e) {
                System.err.println("初始化rank数据失败: " + e.getMessage());
                e.printStackTrace();
            }
        }
        
        /**
         * 初始化志愿配置
         */
        private void initializeVolunteerConfig(SuperFormMain superFormMain) {
        	
        	String FORM_RULE = provinceConfig.getCal_FormRule(superFormMain.getPc_code(), null);
            if(Tools.isEmpty(FORM_RULE)) {
            	
            	TOTAL_VOLUNTEER_COUNT = provinceConfig.getCal_FormCnt(superFormMain.getPc_code(), null);
            	MAX_MAJORS_PER_VOLUNTEER = provinceConfig.getCal_MajorCnt(superFormMain.getPc_code(), null);
	            
            	HashSet<String> RULE_STAGE_SET = Tools.getSetByStrSplit(FORM_RULE);
	            Iterator<String> ruleIts = RULE_STAGE_SET.iterator();
	            
	            int index = 0;
	            while(ruleIts.hasNext()){
	            	int it = Tools.getInt(ruleIts.next());
	            	if(it > 0){
	            		if(index == 0){
	            			DEFAULT_CHONG_COUNT = it;
	            		}
	            		if(index == 1){
	            			DEFAULT_WEN_COUNT = it;
	            		}
	            		if(index == 2){
	            			DEFAULT_BAO_COUNT = it;
	            		}
	            	}
	            	index++;
	            }
            }
        }

        // Getters and Setters
        public List<SuperForm> getSuperFormList() { return superFormList; }
        public void setSuperFormList(List<SuperForm> superFormList) { this.superFormList = superFormList; }

        public SuperFormMain getSuperFormMain() { return superFormMain; }
        public void setSuperFormMain(SuperFormMain superFormMain) { this.superFormMain = superFormMain; }

        public ZyzdProvinceConfig getProvinceConfig() { return provinceConfig; }
        public void setProvinceConfig(ZyzdProvinceConfig provinceConfig) { this.provinceConfig = provinceConfig; }

        public ZDKSRank getUserRank() { return userRank; }
        public void setUserRank(ZDKSRank userRank) { this.userRank = userRank; }

        public ZDKSRank getRankYearA() { return rankYearA; }
        public void setRankYearA(ZDKSRank rankYearA) { this.rankYearA = rankYearA; }

        public ZDKSRank getRankYearB() { return rankYearB; }
        public void setRankYearB(ZDKSRank rankYearB) { this.rankYearB = rankYearB; }

        public ZDKSRank getRankYearC() { return rankYearC; }
        public void setRankYearC(ZDKSRank rankYearC) { this.rankYearC = rankYearC; }

        public RtFillNeedsInfoBean getUserNeedsData() { return userNeedsData; }
        public void setUserNeedsData(RtFillNeedsInfoBean userNeedsData) { this.userNeedsData = userNeedsData; }
    }

    
    // ==================== 公共接口方法 ====================
    
    /**
     * 综合审核志愿表单（新推荐方法）
     * 使用SuperFormMain，简化参数传递
     * @param superFormMain 统一表单主数据
     * @param provinceConfig 省份配置
     * @return 审核结果
     */
    public static FormReviewResult reviewForm(SuperFormMain superFormMain, ZyzdProvinceConfig provinceConfig) {
        FormReviewParams params = new FormReviewParams(superFormMain, provinceConfig, null);
        return reviewForm(params);
    }
    
    /**
     * 新的简化API接口（包含用户需求数据）
     * @param superFormMain 表单主对象
     * @param provinceConfig 省份配置
     * @param userNeedsData 用户需求数据，用于个人适配性等维度评估
     * @return 审核结果
     */
    public static FormReviewResult reviewForm(SuperFormMain superFormMain, ZyzdProvinceConfig provinceConfig, RtFillNeedsInfoBean userNeedsData) {
        FormReviewParams params = new FormReviewParams(superFormMain, provinceConfig, userNeedsData);
        return reviewForm(params);
    }
    
    /**
     * 综合审核志愿表单（推荐方法）
     * 使用FormReviewParams参数对象，支持更灵活的配置
     */
    public static FormReviewResult reviewForm(FormReviewParams params) {
        FormReviewResult result = new FormReviewResult();
        
        try {
            // 初始化评估上下文
            FormReviewContext context = new FormReviewContext(
                params.getSuperFormList(),
                params.getSuperFormMain(),
                params.getUserNeedsData(),
                params.getProvinceConfig(),
                params.getUserRank(),
                params.getRankYearA(),
                params.getRankYearB(),
                params.getRankYearC()
            );

            // 加载详细计划数据和用户选择的专业信息
            context.loadDetailedPlanData();
            context.buildSelectedMajorData();
            
            // 执行评估维度计算
            executeReviewDimensions(result, context);
            
            // 计算并设置专业组干净度统计信息
            calculateProfessionalGroupCleanliness(result, context); 

            // 执行录取概率分析
            executeAdmissionProbabilityAnalysis(result, context);
            
            // 计算并设置志愿填报统计信息
            calculateVolunteerStatistics(result, context);

            // 计算综合评分
            result.setOverallScore(calculateOverallScore(result));

        } catch (Exception e) {
            Tools.println("[FormReviewEvaluator] 评估过程中发生错误: " + e.getMessage());
            e.printStackTrace();
            // 在发生异常时，仍然返回一个结果对象，但包含错误信息
            result.setOverallScore(0.0);
            result.setSuggestions("系统在评估过程中发生错误，请联系技术支持。错误详情：" + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 执行各维度评估
     * 使用最优的评估算法
     */
    private static FormReviewResult executeReviewDimensions(FormReviewResult result, FormReviewContext context) {
        Tools.println("[executeReviewDimensions] 开始执行各维度评估");
        
        // 执行基础维度评估
        int actualDimensionCount = executeBasicDimensions(result, context);
        
        // 执行用户需求维度评估
        actualDimensionCount += executeUserNeedsDimensions(result, context);
        
        // 设置实际使用的维度数量
        result.setActualDimensionCount(actualDimensionCount);
        Tools.println("[executeReviewDimensions] 实际使用维度数量: " + actualDimensionCount);
        
        return result;
    }
    
    /**
     * 执行基础10个维度评估
     */
    private static int executeBasicDimensions(FormReviewResult result, FormReviewContext context) {
        Tools.println("[executeBasicDimensions] 执行基础10个维度评估");
        
        // 维度1：分段定位定级
        String grade1 = reviewPositionLevel(context);
        result.setGrade1(grade1);
        result.setSuggestion1(DimensionSuggestionGenerator.generateSuggestion(1, grade1));
        
        // 维度2：院校级差梯度
        String grade2 = reviewCollegeGradient(context);
        result.setGrade2(grade2);
        result.setSuggestion2(DimensionSuggestionGenerator.generateSuggestion(2, grade2));
        
        // 维度3：院校完整度
        String grade3 = reviewCollegeCompleteness(context);
        result.setGrade3(grade3);
        result.setSuggestion3(DimensionSuggestionGenerator.generateSuggestion(3, grade3));
        
        // 维度4：专业完整度
        String grade4 = reviewMajorCompleteness(context);
        result.setGrade4(grade4);
        result.setSuggestion4(DimensionSuggestionGenerator.generateSuggestion(4, grade4));
        
        // 维度5：院校梯度顺序
        String grade5 = reviewCollegeGradientOrder(context);
        result.setGrade5(grade5);
        result.setSuggestion5(DimensionSuggestionGenerator.generateSuggestion(5, grade5));
        
        // 维度6：专业梯度顺序
        String grade6 = reviewMajorGradientOrder(context);
        result.setGrade6(grade6);
        result.setSuggestion6(DimensionSuggestionGenerator.generateSuggestion(6, grade6));
        
        // 维度7：专业服从调剂
        String grade7 = reviewMajorObedience(context);
        result.setGrade7(grade7);
        result.setSuggestion7(DimensionSuggestionGenerator.generateSuggestion(7, grade7));
        
        // 维度8：保底院校风险
        String grade8 = reviewSafetyCollegeRisk(context);
        result.setGrade8(grade8);
        result.setSuggestion8(DimensionSuggestionGenerator.generateSuggestion(8, grade8));
        
        // 维度9：区域优势
        String grade9 = evaluateRegionalAdvantage(context);
        result.setGrade9(grade9);
        result.setSuggestion9(DimensionSuggestionGenerator.generateSuggestion(9, grade9));
        
        // 维度10：专业集中度
        String grade10 = evaluateMajorConcentration(context);
        result.setGrade10(grade10);
        result.setSuggestion10(DimensionSuggestionGenerator.generateSuggestion(10, grade10));
        
        Tools.println("[executeBasicDimensions] 基础维度评估结果: [" + 
                     grade1 + "," + grade2 + "," + grade3 + "," + 
                     grade4 + "," + grade5 + "," + grade6 + "," + 
                     grade7 + "," + grade8 + "," + grade9 + "," + 
                     grade10 + "]");
        
        return BASIC_DIMENSION_COUNT; // 基础维度始终计数
    }
    
    /**
     * 执行用户需求维度评估
     */
    private static int executeUserNeedsDimensions(FormReviewResult result, FormReviewContext context) {
        if (context.hasUserNeedsData()) {
            Tools.println("[executeUserNeedsDimensions] 存在用户需求数据，执行用户需求维度评估");
            result.setHasUserNeedsData(true);
            
            // 维度11：个人适配性
            String grade11 = UserNeedsEvaluator.evaluatePersonalFitness(context);
            result.setGrade11(grade11);
            result.setSuggestion11(DimensionSuggestionGenerator.generateSuggestion(11, grade11));
            
            // 维度12：经济适配性
            String grade12 = UserNeedsEvaluator.evaluateEconomicFitness(context);
            result.setGrade12(grade12);
            result.setSuggestion12(DimensionSuggestionGenerator.generateSuggestion(12, grade12));
            
            // 维度13：地域偏好匹配
            String grade13 = UserNeedsEvaluator.evaluateGeographicPreference(context);
            result.setGrade13(grade13);
            result.setSuggestion13(DimensionSuggestionGenerator.generateSuggestion(13, grade13));
            
            // 维度14：专业偏好匹配
            String grade14 = UserNeedsEvaluator.evaluateMajorPreference(context);
            result.setGrade14(grade14);
            result.setSuggestion14(DimensionSuggestionGenerator.generateSuggestion(14, grade14));
            
            // 维度15：身体条件适配
            String grade15 = UserNeedsEvaluator.evaluatePhysicalRequirements(context);
            result.setGrade15(grade15);
            result.setSuggestion15(DimensionSuggestionGenerator.generateSuggestion(15, grade15));
            
            Tools.println("[executeUserNeedsDimensions] 用户需求维度评估结果: [" + 
                         grade11 + "," + grade12 + "," + grade13 + "," + 
                         grade14 + "," + grade15 + "]");
            
            // 设置各个维度的数据标记
            setUserNeedsDataFlags(result, context);
            
            return USER_NEEDS_DIMENSION_COUNT; // 用户需求数据维度计数
        } else {
            Tools.println("[executeUserNeedsDimensions] 无用户需求数据，设置默认等级");
            setDefaultUserNeedsGrades(result);
            return 0; // 无用户需求数据时不计入维度数量
        }
    }
    
    /**
     * 设置用户需求数据标记
     */
    private static void setUserNeedsDataFlags(FormReviewResult result, FormReviewContext context) {
        RtFillNeedsInfoBean needsData = context.getUserNeedsData();
        if (needsData != null) {
            boolean hasPersonalData = !Tools.isEmpty(needsData.getRt_gender()) || !Tools.isEmpty(needsData.getRt_ethnicity());
            boolean hasEconomicData = !Tools.isEmpty(needsData.getRt_university_budget());
            boolean hasGeographicData = !Tools.isEmpty(needsData.getRt_preferred_cities()) || !Tools.isEmpty(needsData.getRt_preferred_provinces());
            boolean hasMajorData = !Tools.isEmpty(needsData.getRt_preferred_majors()) || !Tools.isEmpty(needsData.getRt_major_category());
            boolean hasPhysicalData = !Tools.isEmpty(needsData.getRt_physical_exam()) || !Tools.isEmpty(needsData.getRt_height()) || !Tools.isEmpty(needsData.getRt_drawing_skill());
            
            result.setHasPersonalFitnessData(hasPersonalData);
            result.setHasEconomicFitnessData(hasEconomicData);
            result.setHasGeographicData(hasGeographicData);
            result.setHasMajorPreferenceData(hasMajorData);
            result.setHasPhysicalData(hasPhysicalData);
            
            Tools.println("[setUserNeedsDataFlags] 用户需求数据可用性: 个人[" + hasPersonalData + "], 经济[" + hasEconomicData + "], 地域[" + hasGeographicData + "], 专业[" + hasMajorData + "], 身体[" + hasPhysicalData + "]");
        }
    }
    
    /**
     * 设置默认用户需求等级
     */
    private static void setDefaultUserNeedsGrades(FormReviewResult result) {
        result.setHasUserNeedsData(false);
        result.setHasPersonalFitnessData(false);
        result.setHasEconomicFitnessData(false);
        result.setHasGeographicData(false);
        result.setHasMajorPreferenceData(false);
        result.setHasPhysicalData(false);
        
        // 设置默认等级和建议
        result.setGrade11(GRADE_C);
        result.setSuggestion11(DimensionSuggestionGenerator.generateSuggestion(11, GRADE_C));
        
        result.setGrade12(GRADE_C);
        result.setSuggestion12(DimensionSuggestionGenerator.generateSuggestion(12, GRADE_C));
        
        result.setGrade13(GRADE_C);
        result.setSuggestion13(DimensionSuggestionGenerator.generateSuggestion(13, GRADE_C));
        
        result.setGrade14(GRADE_C);
        result.setSuggestion14(DimensionSuggestionGenerator.generateSuggestion(14, GRADE_C));
        
        result.setGrade15(GRADE_C);
        result.setSuggestion15(DimensionSuggestionGenerator.generateSuggestion(15, GRADE_C));
    }
    
    /**
     * 执行录取概率分析
     */
    private static void executeAdmissionProbabilityAnalysis(FormReviewResult result, FormReviewContext context) {
        try {
            Tools.println("[executeAdmissionProbabilityAnalysis] 开始执行录取概率分析");
            AdmissionProbabilityEvaluator.AdmissionProbabilityResult admissionResult = 
                AdmissionProbabilityEvaluator.evaluateAdmissionProbability(context);
            
            if (admissionResult != null) {
                result.setAdmissionProbabilityResult(admissionResult);
                result.setHasAdmissionProbabilityData(true);
                Tools.println("[executeAdmissionProbabilityAnalysis] 录取概率分析完成，总志愿数: " + admissionResult.getTotalVolunteers() + ", 可录取志愿数: " + admissionResult.getAdmittableVolunteers() + ", 平均录取概率: " + String.format("%.1f%%", admissionResult.getAverageProbability()));
            } else {
                Tools.println("[executeAdmissionProbabilityAnalysis] 录取概率分析结果为空");
                result.setHasAdmissionProbabilityData(false);
            }
        } catch (Exception e) {
            Tools.println("[executeAdmissionProbabilityAnalysis] 录取概率分析异常: " + e.getMessage());
            result.setHasAdmissionProbabilityData(false);
        }
    }

    /**
     * 🎯 计算志愿填报统计数据
     * 避免页面重复计算，统一在算法层面计算各种统计指标
     */
    private static void calculateVolunteerStatistics(FormReviewResult result, FormReviewContext context) {
        Tools.println("[calculateVolunteerStatistics] 开始计算志愿填报统计数据");
        
        List<SuperForm> superFormList = context.getSuperFormList();
        if (superFormList == null || superFormList.isEmpty()) {
            Tools.println("[calculateVolunteerStatistics] 志愿表单为空，跳过统计计算");
            return;
        }

        VolunteerStatistics statistics = new VolunteerStatistics();
        
        // 获取用户分数
        int userScore = context.getSuperFormMain().getScore_cj();
        Tools.println("[calculateVolunteerStatistics] 用户分数: " + userScore);
        
        // 统计变量 - 针对每个志愿（学校+专业组）进行统计
        int chongCount = 0, wenCount = 0, baoCount = 0;
        Set<String> uniqueColleges = new HashSet<>();
        Set<String> topUniversitySet = new HashSet<>();
        Set<String> provinces = new HashSet<>();
        double totalAdmissionProb = 0.0;
        int validVolunteers = 0;
        
        // 用于去重的志愿集合（学校代码+专业组代码）
        Set<String> processedVolunteers = new HashSet<>();
        
        Tools.println("[calculateVolunteerStatistics] 开始逐个分析志愿，每个志愿 = 学校+专业组");
        Tools.println("[calculateVolunteerStatistics] 总专业数量: " + superFormList.size());
        
        // 遍历志愿表单进行统计（需要按志愿去重：学校+专业组）
        for (int i = 0; i < superFormList.size(); i++) {
            SuperForm superForm = superFormList.get(i);
            
            // 构建志愿唯一标识（学校代码+专业组代码）
            String volunteerKey = superForm.getYxdm() + "_" + (superForm.getZyz() != null ? superForm.getZyz() : "");
            
            // 如果该志愿已经处理过，跳过（避免重复统计同一个志愿的多个专业）
            if (processedVolunteers.contains(volunteerKey)) {
                continue;
            }
            processedVolunteers.add(volunteerKey);
            
            int volunteerNumber = validVolunteers + 1;
            
            // 计算该志愿（学校+专业组）的平均分数
            double avgScore = calculateAverageScore(superForm, context);
            if (avgScore <= 0) {
                // Tools.println("[calculateVolunteerStatistics] 志愿" + volunteerNumber + " [" + superForm.getYxmc() + "-" + superForm.getZyz() + "] 无有效分数数据，跳过统计");
                continue; // 跳过无效数据
            }
            
            validVolunteers++;
            statistics.getVolunteerScoreMap().put(volunteerNumber, avgScore);
            
            // 判断该志愿的类型（冲稳保）- 基于学校+专业组的整体分数
            String volunteerType = determineVolunteerType(avgScore, userScore);
            statistics.getVolunteerTypeMap().put(volunteerNumber, volunteerType);
            
            // Tools.println("[calculateVolunteerStatistics] 志愿" + volunteerNumber + " [" + superForm.getYxmc() + "-" + superForm.getZyz() + "] " +  "平均分:" + String.format("%.1f", avgScore) + " 用户分:" + userScore + " 类型:" + volunteerType);
            
            // 统计冲稳保数量 - 每个志愿单独计算
            switch (volunteerType) {
                case "冲击":
                    chongCount++;
                    break;
                case "稳妥":
                    wenCount++;
                    break;
                case "保底":
                    baoCount++;
                    break;
                default:
                    // Tools.println("[calculateVolunteerStatistics] 警告：志愿" + volunteerNumber + " 类型未知: " + volunteerType);
                    break;
            }
            
            // 统计院校信息
            String yxmc = superForm.getYxmc();
            if (!Tools.isEmpty(yxmc)) {
                // 统计唯一院校数（去重）
                uniqueColleges.add(yxmc);
                
                // 判断是否为名校（按志愿计算，去重）
                if (FormReviewUtils.isTopUniversity(yxmc)) {
                	topUniversitySet.add(yxmc);
                }
                
                // 统计省份（去重）
                String province = getUniversityProvince(superForm);
                if (!Tools.isEmpty(province)) {
                    provinces.add(province);
                }
            }
            
            // 计算该志愿的录取概率
            if (!result.isHasAdmissionProbabilityData()) {
                double admissionProb = calculateSimpleAdmissionProbability(avgScore, userScore);
                totalAdmissionProb += admissionProb;
            }
        }
        
        // 设置统计结果
        statistics.setChongCount(chongCount);
        statistics.setWenCount(wenCount);
        statistics.setBaoCount(baoCount);
        statistics.setTotalVolunteers(validVolunteers);
        statistics.setTopUniversityCount(topUniversitySet.size());
        statistics.setTotalUniversityCount(uniqueColleges.size());
        statistics.setProvinceCount(provinces.size());
        
        // 设置平均录取概率
        if (result.isHasAdmissionProbabilityData() && result.getAdmissionProbabilityResult() != null) {
            // 使用精确的录取概率分析结果
            statistics.setAverageAdmissionProbability(result.getAdmissionProbabilityResult().getAverageProbability());
        } else if (validVolunteers > 0) {
            // 使用简化计算的平均录取概率
            statistics.setAverageAdmissionProbability(totalAdmissionProb / validVolunteers);
        }
        
        result.setVolunteerStatistics(statistics);
        
        // 验证统计结果的合理性
        int totalCounted = chongCount + wenCount + baoCount;
        if (totalCounted != validVolunteers) {
            Tools.println("[calculateVolunteerStatistics] 警告：统计数量不匹配 - 冲稳保总数:" + totalCounted + " 有效志愿数:" + validVolunteers);
        }
        
        Tools.println("[calculateVolunteerStatistics] 统计完成 - 冲击志愿:" + chongCount + 
                     ", 稳妥志愿:" + wenCount + ", 保底志愿:" + baoCount + 
                     ", 总志愿数:" + validVolunteers + ", 名校志愿数:" + topUniversitySet.size() + 
                     ", 涉及院校数:" + uniqueColleges.size() + ", 涉及省份数:" + provinces.size());
        Tools.println("[calculateVolunteerStatistics] 去重前专业总数: " + superFormList.size() + ", 去重后志愿数: " + validVolunteers);
    }

    /**
     * 计算志愿（学校+专业组）的平均分数
     * 基于历史三年的录取分数进行加权平均计算
     * 
     * @param superForm 志愿信息（学校+专业组）
     * @param context 评估上下文
     * @return 平均分数，如果无有效数据则返回0.0
     */
    private static double calculateAverageScore(SuperForm superForm, FormReviewContext context) {
        try {
            // 通过context获取历史数据
            List<JHBean> planData = context.getPlanData(superForm.getYxdm(), superForm.getZyz());
            if (planData == null || planData.isEmpty()) {
                return 0.0;
            }
            
            // 使用第一个匹配的JHBean数据
            JHBean jhBean = planData.get(0);
            
            int scoreA = Tools.getInt(jhBean.getZdf_a()); // 最近年份
            int scoreB = Tools.getInt(jhBean.getZdf_b()); // 第二年
            int scoreC = Tools.getInt(jhBean.getZdf_c()); // 第三年
            
            // 按照权重计算平均分数（最近年份权重更高）
            if (scoreA > 0 && scoreB > 0 && scoreC > 0) {
                // 三年数据都有效，使用加权平均
                return (scoreA * RECENT_YEAR_WEIGHT + scoreB * SECOND_YEAR_WEIGHT + scoreC * THIRD_YEAR_WEIGHT);
            } else if (scoreA > 0 && scoreB > 0) {
                // 只有两年数据，使用两年加权平均
                return (scoreA * TWO_YEAR_PRIMARY_WEIGHT + scoreB * TWO_YEAR_SECONDARY_WEIGHT);
            } else if (scoreA > 0) {
                // 只有最近一年数据
                return scoreA;
            } else if (scoreB > 0) {
                // 只有第二年数据
                return scoreB;
            } else if (scoreC > 0) {
                // 只有第三年数据
                return scoreC;
            }
            
        } catch (Exception e) {
            Tools.println("[calculateAverageScore] 计算志愿[" + superForm.getYxmc() + "-" + superForm.getZyz() + "]平均分数异常: " + e.getMessage());
        }
        return 0.0;
    }

    /**
     * 判断志愿类型（冲稳保）
     * 基于志愿（学校+专业组）的平均分数与用户分数的差值来判断
     * 
     * @param avgScore 志愿的平均分数
     * @param userScore 用户分数
     * @return 志愿类型：冲击/稳妥/保底
     */
    private static String determineVolunteerType(double avgScore, int userScore) {
        double scoreDiff = avgScore - userScore;
        
        // 志愿分数比用户分数高8分以上 -> 冲击志愿
        if (scoreDiff > CHONG_SCORE_DIFF) {
            return "冲击";
        } 
        // 志愿分数比用户分数低15分以内 -> 稳妥志愿
        else if (scoreDiff > -BAO_SCORE_DIFF) {
            return "稳妥";
        } 
        // 志愿分数比用户分数低15分以上 -> 保底志愿
        else {
            return "保底";
        }
    }

    /**
     * 获取院校所在省份
     */
    private static String getUniversityProvince(SuperForm superForm) {
        try {
            ZyzdUniversityBean bean = ZyzdCache.getUniversity(superForm.getYxmc_org());
            if (bean != null && !Tools.isEmpty(bean.getPosition_sf())) {
                return bean.getPosition_sf();
            }
        } catch (Exception e) {
            Tools.println("[getUniversityProvince] 获取院校省份异常: " + e.getMessage());
        }
        return "";
    }

    /**
     * 计算简化录取概率
     */
    private static double calculateSimpleAdmissionProbability(double avgScore, int userScore) {
        int scoreDiff = (int)(userScore - avgScore);
        
        if (scoreDiff >= 15) return 90.0;
        else if (scoreDiff >= 5) return 77.0;
        else if (scoreDiff >= -5) return 55.0;
        else if (scoreDiff >= -15) return 30.0;
        else return 12.0;
    }
    
    /**
     * 评估上下文类，封装所有评估所需的数据和参数
     * 充分利用JHBean的各个字段进行精准分析
     */
    /**
     * 志愿表单审核上下文
     * 包含所有审核所需的数据和缓存
     * 
     * 数据架构说明：
     * 1. 完整专业组数据：用于专业组干净度、历史趋势等整体分析
     * 2. 用户选择数据：用于个性化评估，如专业集中度、适配性等
     * 3. 混合使用：根据评估维度的需求智能选择数据源
     */
    public static class FormReviewContext {
        private List<SuperForm> superFormList;
        private SuperFormMain superFormMain;
        private ZyzdProvinceConfig provinceConfig;
        private ZDKSRank userRank;
        private ZDKSRank rankYearA;
        private ZDKSRank rankYearB;
        private ZDKSRank rankYearC;
        
        // === 完整专业组数据 (保留原有功能) ===
        private Map<String, List<JHBean>> detailedPlanData;
        private boolean planDataLoaded = false;
        
        // 专业组整体信息和趋势分析
        private Map<String, ProfessionalGroupInfo> groupInfoMap;
        private Map<String, HistoricalTrend> trendAnalysis;
        
        // === 新增：用户选择专业数据 ===
        private Map<String, List<JHBean>> selectedMajorPlanData;      // 用户实际选择的专业数据
        private Map<String, UserSelectedMajorInfo> selectedMajorInfoMap; // 用户选择的专业信息
        
        // === 数据关联映射 ===
        private Map<String, Set<String>> groupToSelectedMajorsMap;    // 专业组 -> 用户选择的专业名称
        private Map<String, String> selectedMajorToGroupMap;          // 用户选择专业 -> 所属专业组
        
        // 用户需求数据
        private RtFillNeedsInfoBean userNeedsData;
        
        public FormReviewContext(List<SuperForm> superFormList,
                               SuperFormMain superFormMain,
                               RtFillNeedsInfoBean userNeedsData,
                               ZyzdProvinceConfig provinceConfig,
                               ZDKSRank userRank,
                               ZDKSRank rankYearA,
                               ZDKSRank rankYearB,
                               ZDKSRank rankYearC) {
            this.superFormList = superFormList; 
            this.superFormMain = superFormMain;
            this.provinceConfig = provinceConfig;
            this.userRank = userRank;
            this.rankYearA = rankYearA;
            this.rankYearB = rankYearB;
            this.rankYearC = rankYearC;
            this.userNeedsData = userNeedsData;
            
            // 初始化数据容器
            this.detailedPlanData = new HashMap<>();
            this.groupInfoMap = new HashMap<>();
            this.trendAnalysis = new HashMap<>();
            this.selectedMajorPlanData = new HashMap<>();
            this.selectedMajorInfoMap = new HashMap<>();
            this.groupToSelectedMajorsMap = new HashMap<>();
            this.selectedMajorToGroupMap = new HashMap<>();
        }
        
        /**
         * 加载详细计划数据
         * 包括完整专业组数据和用户选择专业数据
         */
        public void loadDetailedPlanData() {
            if (planDataLoaded || superFormList == null || superFormList.isEmpty()) {
                return;
            }
            
            try {
                // 第一步：加载完整专业组数据（保持原有逻辑）
                loadFullGroupPlanData();
                
                // 第二步：构建用户选择专业数据
                buildSelectedMajorData();
                
                // 第三步：建立数据关联映射
                buildDataMappings();
                
                planDataLoaded = true;
                Tools.println("[loadDetailedPlanData] 数据加载完成 - 完整专业组: " + detailedPlanData.size() + ", 用户选择专业: " + selectedMajorPlanData.size());
            } catch (Exception e) {
                Tools.println("[loadDetailedPlanData] 数据加载失败: " + e.getMessage());
                planDataLoaded = false;
            }
        }

        /**
         * 加载完整专业组数据（保持原有逻辑）
         */
        private void loadFullGroupPlanData() {
            try {
                // 按院校专业组分组加载数据
                Map<String, HashSet<String>> collegeGroupMap = new HashMap<>();
                Map<String, HashSet<String>> majorGroupMap = new HashMap<>();
                
                for (SuperForm superForm : superFormList) {
                    String yxdm = superForm.getYxdm();
                    String zyz = superForm.getZyz();
                    
                    if (!Tools.isEmpty(yxdm)) {
                        String key = yxdm + "|" + Tools.trim(zyz);
                        
                        HashSet<String> yxdmSet = collegeGroupMap.computeIfAbsent(key, k -> new HashSet<>());
                        yxdmSet.add(yxdm);
                        
                        HashSet<String> zyzSet = majorGroupMap.computeIfAbsent(key, k -> new HashSet<>());
                        if (!Tools.isEmpty(zyz)) {
                            zyzSet.add(zyz);
                        }
                    }
                }
                
                // 优化：单次查询所有院校专业组数据
                HashSet<String> allYxdmSet = new HashSet<>();
                HashSet<String> allZyzSet = new HashSet<>();
                
                // 收集所有院校名称和专业组
                for (Map.Entry<String, HashSet<String>> entry : collegeGroupMap.entrySet()) {
                	allYxdmSet.addAll(entry.getValue());
                }
                for (Map.Entry<String, HashSet<String>> entry : majorGroupMap.entrySet()) {
                    allZyzSet.addAll(entry.getValue());
                }
                
                String xkCode = XKCombineUtils.getXKCodeByStudentSelection(superFormMain.getScore_xk());
                
                // 获取正确的年份
                int LATEST_JH_YEAR = provinceConfig.getLatest_year_jh();
                
                // 单次查询所有数据
                ZyzdFormJDBC zyzdFormJDBC = new ZyzdFormJDBC();
                List<JHBean> allPlanData = zyzdFormJDBC.getZyBySelectedForm_YxdmAndZyz(
                    LATEST_JH_YEAR,
                    provinceConfig.getP_table_code(),
                    xkCode,
                    superFormMain.getPc(),
                    superFormMain.getPc_code(),
                    allYxdmSet,
                    allZyzSet
                );
            	
                // 按院校专业组分组整理数据
                Map<String, List<JHBean>> groupedPlanData = new HashMap<>();
                for (JHBean bean : allPlanData) {
                    String yxdm = bean.getYxdm();
                    String zyz = bean.getZyz();
                    String key = Tools.trim(yxdm) + "|" + Tools.trim(zyz);
                    
                    groupedPlanData.computeIfAbsent(key, k -> new ArrayList<>()).add(bean);
                }
                
                // 将分组后的数据放入缓存并进行分析
                for (Map.Entry<String, List<JHBean>> entry : groupedPlanData.entrySet()) {
                    String key = entry.getKey();
                    List<JHBean> planData = entry.getValue();
                    
                    detailedPlanData.put(key, planData);
                    
                    // 分析专业组信息
                    if (!planData.isEmpty()) {
                        analyzeProfessionalGroupInfo(key, planData);
                        analyzeHistoricalTrend(key, planData);
                    }
                }
                
            } catch (Exception e) {
                // 加载失败时尝试重新加载 
                planDataLoaded = false;
            }
        }

        /**
         * 构建用户选择专业数据
         */
        private void buildSelectedMajorData() {
//            Tools.println("[buildSelectedMajorData] 开始构建用户选择专业数据");
            
            for (SuperForm superForm : superFormList) {
                String yxdm = superForm.getYxdm();
                String zyz = superForm.getZyz();
                String selectedMajorCode = superForm.getZydm(); // 用户选择的专业代码
                
                if (!Tools.isEmpty(yxdm)) {
                    String groupKey = Tools.trim(yxdm) + "|" + Tools.trim(zyz);
                    String selectedKey = groupKey + "|" + Tools.trim(selectedMajorCode);
                    
                    // 从完整数据中过滤出用户选择的专业
                    List<JHBean> fullPlanData = detailedPlanData.get(groupKey);
                    if (fullPlanData != null) {
                        List<JHBean> selectedData = new ArrayList<>();
                        
                        for (JHBean bean : fullPlanData) {
                            // 匹配专业名称或专业代码
                            if ((!Tools.isEmpty(selectedMajorCode) && selectedMajorCode.equals(bean.getZydm()))) {
                                selectedData.add(bean);
                            }
                        }
                        
                        if (!selectedData.isEmpty()) {
                            selectedMajorPlanData.put(selectedKey, selectedData);
                            analyzeSelectedMajorInfo(selectedKey, selectedData.get(0), superForm);
                            
//                            Tools.println("[buildSelectedMajorData] 添加用户选择专业: " + selectedMajor + " (" + groupKey + "), 数据条数: " + selectedData.size());
                        } else {
//                            Tools.println("[buildSelectedMajorData] 警告：未找到专业 " + selectedMajor + " 的计划数据");
                        }
                    }
                }
            }
            
//            Tools.println("[buildSelectedMajorData] 用户选择专业数据构建完成，总数: " + selectedMajorPlanData.size());
        }

        /**
         * 分析用户选择的专业信息
         */
        private void analyzeSelectedMajorInfo(String selectedKey, JHBean bean, SuperForm superForm) {
            UserSelectedMajorInfo info = new UserSelectedMajorInfo();
            
            info.setMajorName(bean.getZymc_org());
            info.setMajorCode(bean.getZydm());
            info.setMajorCategory(bean.getZyml());
            info.setYxdm(bean.getYxdm());
            info.setYxmc(bean.getYxmc_org());
            info.setZyz(bean.getZyz());
            
            // 计算该专业的平均分数
            double avgScore = 0;
            int scoreCount = 0;
            if (bean.getQsf_a() > 0) { avgScore += bean.getQsf_a(); scoreCount++; }
            if (bean.getQsf_b() > 0) { avgScore += bean.getQsf_b(); scoreCount++; }
            if (bean.getQsf_c() > 0) { avgScore += bean.getQsf_c(); scoreCount++; }
            info.setAvgScore(scoreCount > 0 ? avgScore / scoreCount : 0);
            
            // 设置招生计划
            try {
                if (!Tools.isEmpty(bean.getJhs())) {
                    info.setPlanCount(Integer.parseInt(bean.getJhs().trim()));
                }
            } catch (Exception e) {
                info.setPlanCount(0);
            }
            
            // 检查限制条件（基于用户需求数据）
            if (userNeedsData != null) {
                String gender = userNeedsData.getRt_gender();
                info.setHasGenderRestriction(FormReviewUtils.hasGenderRestriction(info.getMajorName(), gender));
                
                String physicalExam = userNeedsData.getRt_physical_exam();
                String height = userNeedsData.getRt_height();
                String bodyType = userNeedsData.getRt_body_type();
                
                boolean hasPhysicalReq = FormReviewUtils.hasHeightRequirement(info.getMajorName(), height) ||
                                       FormReviewUtils.hasColorVisionRequirement(info.getMajorName(), physicalExam) ||
                                       FormReviewUtils.hasPhysicalRequirement(info.getMajorName(), bodyType);
                info.setHasPhysicalRequirement(hasPhysicalReq);
            }
            
            // 计算对专业组干净度的贡献
            String groupKey = Tools.trim(info.getYxdm()) + "|" + Tools.trim(info.getZyz());
            ProfessionalGroupInfo groupInfo = groupInfoMap.get(groupKey);
            if (groupInfo != null && groupInfo.getMajorCategories() != null) {
                if (groupInfo.getMajorCategories().size() == 1) {
                    info.setCleanlinessContribution("干净");
                } else if (groupInfo.getMajorCategories().contains(info.getMajorCategory())) {
                    info.setCleanlinessContribution("一般");
                } else {
                    info.setCleanlinessContribution("不干净");
                }
            }
            
            selectedMajorInfoMap.put(selectedKey, info);
        }

        /**
         * 建立数据关联映射
         */
        private void buildDataMappings() {
            for (String selectedKey : selectedMajorPlanData.keySet()) {
                // 解析key: yxdm|zyz|majorName
                String[] parts = selectedKey.split("\\|");
                if (parts.length >= 3) {
                    String groupKey = parts[0] + "|" + parts[1];
                    String zydm = parts[2]; // 专业代码
                    
                    // 建立专业组到选择专业的映射
                    groupToSelectedMajorsMap.computeIfAbsent(groupKey, k -> new HashSet<>()).add(zydm);
                    
                    // 建立选择专业到专业组的映射
                    selectedMajorToGroupMap.put(selectedKey, groupKey);
                }
            }
        }
        
        /**
         * 分析专业组信息
         * 利用zyz(专业组)和znzy(组内专业)字段
         * 新增：计算专业组干净度（基于zyml专业门类的多样性）
         */
        private void analyzeProfessionalGroupInfo(String key, List<JHBean> planData) {
            ProfessionalGroupInfo groupInfo = new ProfessionalGroupInfo();
            
            Set<String> majorsInGroup = new HashSet<>();
            Set<String> majorCategories = new HashSet<>();  // 新增：专业门类集合
            int totalPlan = 0;
            double avgScore = 0;
            int scoreCount = 0;
            
            for (JHBean bean : planData) {
                // 统计专业组内的专业数量
                if (!Tools.isEmpty(bean.getZymc())) {
                    majorsInGroup.add(bean.getZymc());
                }
                
                // 新增：统计专业门类（利用zyml字段）
                if (!Tools.isEmpty(bean.getZyml())) {
                    majorCategories.add(bean.getZyml().trim());
                }
                
                // 统计招生计划
                try {
                    if (!Tools.isEmpty(bean.getJhs())) {
                        totalPlan += Integer.parseInt(bean.getJhs().trim());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                
                // 计算平均分数
                try {
                    if (bean.getQsf() > 0) {
                        avgScore += bean.getQsf();
                        scoreCount++;
                    }
                } catch (Exception e) {
                	e.printStackTrace();
                }
            }
            
            // 计算专业组干净度
            int categoryCount = majorCategories.size();
            String cleanlinessLevel;
            if (categoryCount == 1) {
                cleanlinessLevel = "干净";
            } else if (categoryCount <= 3) {
                cleanlinessLevel = "一般";
            } else {
                cleanlinessLevel = "不干净";
            }
            
            groupInfo.setTotalMajors(majorsInGroup.size());
            groupInfo.setTotalPlan(totalPlan);
            groupInfo.setAvgScore(scoreCount > 0 ? avgScore / scoreCount : 0);
            groupInfo.setMajorNames(majorsInGroup);
            
            // 新增：设置干净度相关信息
            groupInfo.setMajorCategories(majorCategories);
            groupInfo.setCategoryCount(categoryCount);
            groupInfo.setCleanlinessLevel(cleanlinessLevel);
            
            groupInfoMap.put(key, groupInfo);
        }
        
        /**
         * 分析历年趋势数据
         * 利用历年分数(zdf_a/b/c)和招生计划(jhs_a/b/c)数据
         */
        private void analyzeHistoricalTrend(String key, List<JHBean> planData) {
            HistoricalTrend trend = new HistoricalTrend();
            
            double scoreA = 0, scoreB = 0, scoreC = 0;
            int planA = 0, planB = 0, planC = 0;
            int countA = 0, countB = 0, countC = 0;
            
            // TODO
            for (JHBean bean : planData) {
                // 分析历年分数趋势
                try {
                    if (bean.getQsf_a() > 0) {
                        scoreA += bean.getQsf_a();
                        countA++;
                    }
                    if (bean.getQsf_b() > 0) {
                        scoreB += bean.getQsf_b();
                        countB++;
                    }
                    if (bean.getQsf_c() > 0) {
                        scoreC += bean.getQsf_c();
                        countC++;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                
                // 分析历年招生计划趋势
                try {
                    if (!Tools.isEmpty(bean.getJhs_a())) {
                        planA += Integer.parseInt(bean.getJhs_a().trim());
                    }
                    if (!Tools.isEmpty(bean.getJhs_b())) {
                        planB += Integer.parseInt(bean.getJhs_b().trim());
                    }
                    if (!Tools.isEmpty(bean.getJhs_c())) {
                        planC += Integer.parseInt(bean.getJhs_c().trim());
                    }
                } catch (Exception e) {
                    // 忽略解析错误
                }
            }
            
            // 设置平均分数
            trend.setAvgScoreA(countA > 0 ? scoreA / countA : 0);
            trend.setAvgScoreB(countB > 0 ? scoreB / countB : 0);
            trend.setAvgScoreC(countC > 0 ? scoreC / countC : 0);
            
            // 设置招生计划
            trend.setPlanA(planA);
            trend.setPlanB(planB);
            trend.setPlanC(planC);
            
            
            // 计算趋势
            trend.calculateTrends();
            
            trendAnalysis.put(key, trend);
        }
        
        
        /**
         * 检查是否有用户需求数据
         */
        public boolean hasUserNeedsData() {
            return userNeedsData != null;
        }
        
        /**
         * 获取用户需求数据
         */
        public RtFillNeedsInfoBean getUserNeedsData() {
            return userNeedsData;
        }
        
        /**
         * 获取指定院校专业组的详细计划数据
         */
        public List<JHBean> getPlanData(String yxdm, String zyz) {
            String key = Tools.trim(yxdm) + "|" + Tools.trim(zyz);
            return detailedPlanData.getOrDefault(key, new ArrayList<>());
        }
        
        /**
         * 获取所有详细计划数据
         */
        public List<JHBean> getAllPlanData() {
            List<JHBean> allData = new ArrayList<>();
            for (List<JHBean> data : detailedPlanData.values()) {
                allData.addAll(data);
            }
            return allData;
        }
        
        /**
         * 获取专业组信息
         */
        public ProfessionalGroupInfo getGroupInfo(String yxdm, String zyz) {
            String key = Tools.trim(yxdm) + "|" + Tools.trim(zyz);
            return groupInfoMap.get(key);
        }
        
        /**
         * 获取历年趋势数据
         */
        public HistoricalTrend getTrend(String yxdm, String zyz) {
            String key = Tools.trim(yxdm) + "|" + Tools.trim(zyz);
            return trendAnalysis.get(key);
        }
        
        // Getters
        public List<SuperForm> getSuperFormList() { return superFormList; }
        public SuperFormMain getSuperFormMain() { return superFormMain; }
        public ZyzdProvinceConfig getProvinceConfig() { return provinceConfig; }
        public ZDKSRank getUserRank() { return userRank; }
        public ZDKSRank getRankYearA() { return rankYearA; }
        public ZDKSRank getRankYearB() { return rankYearB; }
        public ZDKSRank getRankYearC() { return rankYearC; }
        public boolean isPlanDataLoaded() { return planDataLoaded; }

        // === 新增：用户选择专业数据获取方法 ===
        
        /**
         * 获取用户选择的专业数据
         */
        public List<JHBean> getSelectedMajorPlanData(String yxdm, String zyz, String zydm) {
            String selectedKey = Tools.trim(yxdm) + "|" + Tools.trim(zyz) + "|" + Tools.trim(zydm);
            return selectedMajorPlanData.getOrDefault(selectedKey, new ArrayList<>());
        }
        
        /**
         * 获取所有用户选择的专业数据
         */
        public List<JHBean> getAllSelectedMajorPlanData() {
            List<JHBean> allSelectedData = new ArrayList<>();
            for (List<JHBean> data : selectedMajorPlanData.values()) {
                allSelectedData.addAll(data);
            }
            return allSelectedData;
        }
        
        /**
         * 获取用户选择的专业信息
         */
        public UserSelectedMajorInfo getSelectedMajorInfo(String yxdm, String zyz, String majorName) {
            String selectedKey = Tools.trim(yxdm) + "|" + Tools.trim(zyz) + "|" + Tools.trim(majorName);
            return selectedMajorInfoMap.get(selectedKey);
        }
        
        /**
         * 获取专业组下用户选择的专业名称集合
         */
        public Set<String> getSelectedMajorsInGroup(String yxdm, String zyz) {
            String groupKey = Tools.trim(yxdm) + "|" + Tools.trim(zyz);
            return groupToSelectedMajorsMap.getOrDefault(groupKey, new HashSet<>());
        }
        
        /**
         * 检查是否有用户选择的专业数据
         */
        public boolean hasSelectedMajorData() {
            return !selectedMajorPlanData.isEmpty();
        }
        
        /**
         * 获取用户选择专业的数量
         */
        public int getSelectedMajorCount() {
            return selectedMajorPlanData.size();
        }
        
        /**
         * 智能数据获取：根据评估需求选择合适的数据源
         * @param evaluationType 评估类型：GROUP_ANALYSIS(专业组分析) 或 PERSONAL_ANALYSIS(个人分析)
         */
        public List<JHBean> getDataByEvaluationType(String yxdm, String zyz, EvaluationType evaluationType) {
            switch (evaluationType) {
                case GROUP_ANALYSIS:
                    // 专业组分析使用完整数据
                    return getPlanData(yxdm, zyz);
                case PERSONAL_ANALYSIS:
                    // 个人分析使用用户选择数据
                    return getSelectedMajorDataByGroup(yxdm, zyz);
                default:
                    return getPlanData(yxdm, zyz);
            }
        }
        
        /**
         * 获取专业组下所有用户选择的专业数据
         */
        private List<JHBean> getSelectedMajorDataByGroup(String yxdm, String zyz) {
            List<JHBean> groupSelectedData = new ArrayList<>();
            Set<String> selectedMajors = getSelectedMajorsInGroup(yxdm, zyz);
            
            for (String zydm : selectedMajors) {
                List<JHBean> majorData = getSelectedMajorPlanData(yxdm, zyz, zydm);
                groupSelectedData.addAll(majorData);
            }
            
            return groupSelectedData;
        }
    }

    /**
     * 评估类型枚举
     */
    public enum EvaluationType {
        GROUP_ANALYSIS,    // 专业组分析（使用完整数据）
        PERSONAL_ANALYSIS  // 个人分析（使用用户选择数据）
    }

    /**
     * 用户选择专业信息
     */
    public static class UserSelectedMajorInfo {
        private String majorName;                    // 专业名称
        private String majorCode;                    // 专业代码
        private String majorCategory;                // 专业门类
        private String yxdm;                        // 院校代码
        private String yxmc;                        // 院校名称
        private String zyz;                         // 专业组代码
        private double avgScore;                    // 平均分数
        private int planCount;                      // 招生计划
        private boolean hasGenderRestriction;       // 是否有性别限制
        private boolean hasPhysicalRequirement;     // 是否有身体条件要求
        private String cleanlinessContribution;     // 对专业组干净度的贡献

        // Getters and Setters
        public String getMajorName() { return majorName; }
        public void setMajorName(String majorName) { this.majorName = majorName; }

        public String getMajorCode() { return majorCode; }
        public void setMajorCode(String majorCode) { this.majorCode = majorCode; }

        public String getMajorCategory() { return majorCategory; }
        public void setMajorCategory(String majorCategory) { this.majorCategory = majorCategory; }

        public String getYxdm() { return yxdm; }
        public void setYxdm(String yxdm) { this.yxdm = yxdm; }

        public String getYxmc() { return yxmc; }
        public void setYxmc(String yxmc) { this.yxmc = yxmc; }

        public String getZyz() { return zyz; }
        public void setZyz(String zyz) { this.zyz = zyz; }

        public double getAvgScore() { return avgScore; }
        public void setAvgScore(double avgScore) { this.avgScore = avgScore; }

        public int getPlanCount() { return planCount; }
        public void setPlanCount(int planCount) { this.planCount = planCount; }

        public boolean isHasGenderRestriction() { return hasGenderRestriction; }
        public void setHasGenderRestriction(boolean hasGenderRestriction) { this.hasGenderRestriction = hasGenderRestriction; }

        public boolean isHasPhysicalRequirement() { return hasPhysicalRequirement; }
        public void setHasPhysicalRequirement(boolean hasPhysicalRequirement) { this.hasPhysicalRequirement = hasPhysicalRequirement; }

        public String getCleanlinessContribution() { return cleanlinessContribution; }
        public void setCleanlinessContribution(String cleanlinessContribution) { this.cleanlinessContribution = cleanlinessContribution; }
    }
    

    
    // ==================== 核心评估方法（15个维度） ====================
    
    /**
     * 1. 分段定位定级评估
     * 基于所有志愿的最高分和最低分的增强版差值进行评估
     * 大于30分为A级，大于20分为B级，大于15分为C级，其他为D级
     */
    public static String reviewPositionLevel(FormReviewContext context) {
        Tools.println("[reviewPositionLevel] 开始分段定位定级评估");
        
    	List<SuperForm> formList = context.getSuperFormList();
    	
        if (formList == null || formList.isEmpty()) {
            Tools.println("[reviewPositionLevel] 志愿表单为空，返回D级");
            return GRADE_D;
        }
        
        Tools.println("[reviewPositionLevel] 志愿表单数量: " + formList.size());
      
        List<Double> allScores = new ArrayList<>();
        
        Map<String, List<SuperForm>> collegeGroups = FormReviewUtils.groupByCollege(formList);
        Tools.println("[reviewPositionLevel] 院校专业组数量: " + collegeGroups.size());
        
        for (Map.Entry<String, List<SuperForm>> entry : collegeGroups.entrySet()) {
            List<SuperForm> group = entry.getValue();
            if (group.isEmpty()) continue;
            
            SuperForm firstForm = group.get(0);
            List<JHBean> planData = context.getPlanData(firstForm.getYxdm(), firstForm.getZyz());
            
            if (!planData.isEmpty()) {
                // 使用加权平均分作为该志愿的代表分数
                double avgScore = FormReviewUtils.calculateWeightedAvgScore(planData);
                if (avgScore > 0) {
                    allScores.add(avgScore);
//                    Tools.println("[reviewPositionLevel] 院校: " + firstForm.getYxdm() + ", 专业组: " + firstForm.getZyz() + ", 加权平均分: " + avgScore);
                }
            }
        }
        
        if (allScores.size() < 2) {
            Tools.println("[reviewPositionLevel] 有效分数数量不足(< 2)，返回D级");
            return GRADE_D;
        }
        
        // 计算最高分和最低分的差值
        double maxScore = Collections.max(allScores);
        double minScore = Collections.min(allScores);
        double scoreDiff = maxScore - minScore;
        
        Tools.println("[reviewPositionLevel] 最高分: " + maxScore + ", 最低分: " + minScore + ", 分数差值: " + scoreDiff);
        
        // 根据分数差值评估
        String grade;
        if (scoreDiff >= POSITION_SCORE_DIFF_A) {
            grade = GRADE_A;
        } else if (scoreDiff >= POSITION_SCORE_DIFF_B) {
            grade = GRADE_B;
        } else if (scoreDiff >= POSITION_SCORE_DIFF_C) {
            grade = GRADE_C;
        } else {
            grade = GRADE_D;
        }
        
        Tools.println("[reviewPositionLevel] 分段定位评估结果: " + grade + " (阈值A:" + POSITION_SCORE_DIFF_A + ", B:" + POSITION_SCORE_DIFF_B + ", C:" + POSITION_SCORE_DIFF_C + ")");
        return grade;
    }
    
    
    /**
     * 2. 院校级差梯度评估
     * 基于详细填报数据分析院校间分数梯度，充分利用历年数据
     */
    public static String reviewCollegeGradient(FormReviewContext context) {
        Tools.println("[reviewCollegeGradient] 开始院校级差梯度评估");
        
        List<SuperForm> formList = context.getSuperFormList();
        ZDKSRank userRank = context.getUserRank();
        
        if (formList == null || formList.isEmpty() || userRank == null) {
            Tools.println("[reviewCollegeGradient] 表单为空或用户位次为空，返回D级");
            return GRADE_D;
        }
        
        if (!context.isPlanDataLoaded()) {
            Tools.println("[reviewCollegeGradient] 计划数据未加载，返回D级");
            return GRADE_D;
        }
        
                // 基于详细填报数据分析梯度
        Map<String, List<SuperForm>> collegeGroups = FormReviewUtils.groupByCollege(formList);
        List<Double> avgScores = new ArrayList<>();
        List<Double> scoreRanges = new ArrayList<>();

//        Tools.println("[reviewCollegeGradient] 院校专业组数量: " + collegeGroups.size());
        
        for (Map.Entry<String, List<SuperForm>> entry : collegeGroups.entrySet()) {
            List<SuperForm> group = entry.getValue();
            if (group.isEmpty()) continue;
            
            SuperForm firstForm = group.get(0);
            List<JHBean> planData = context.getPlanData(firstForm.getYxdm(), firstForm.getZyz());
            HistoricalTrend trend = context.getTrend(firstForm.getYxdm(), firstForm.getZyz());
            
            if (!planData.isEmpty() && trend != null) {
                // 使用历年数据计算更精确的平均分和分数范围
                double weightedScore = FormReviewUtils.calculateAdvancedWeightedScore(planData, trend);
//                Tools.println("[reviewCollegeGradient] 院校: " + firstForm.getYxdm() + ", 专业组: " + firstForm.getZyz() + ", 高级加权分数: " + weightedScore);
                double scoreRange = FormReviewUtils.calculateScoreRange(planData); // TODO
                
                if (weightedScore > 0) {
                    avgScores.add(weightedScore);
                    scoreRanges.add(scoreRange);
//                    Tools.println("[reviewCollegeGradient] 分数范围: " + scoreRange + ", 累计有效分数数量: " + avgScores.size());
                }
            }
        }
        
        if (avgScores.size() < 2) {
            Tools.println("[reviewCollegeGradient] 有效分数数量不足(< 2)，返回C级");
            return GRADE_C;
        }
        
        // 分析梯度合理性（考虑分数波动范围）
        int reasonableGradients = 0;
        int totalGradients = avgScores.size() - 1;
        
//        Tools.println("[reviewCollegeGradient] 开始分析梯度合理性，总梯度数: " + totalGradients);
        
        for (int i = 0; i < avgScores.size() - 1; i++) {
            double diff = Math.abs(avgScores.get(i) - avgScores.get(i + 1));
            double avgRange = (scoreRanges.get(i) + scoreRanges.get(i + 1)) / 2;
            
            // 使用常量：动态梯度标准，考虑分数波动范围
            double minGradient = Math.max(MIN_GRADIENT_SCORE, avgRange * GRADIENT_RANGE_MIN_WEIGHT);
            double maxGradient = Math.min(MAX_GRADIENT_SCORE, avgRange * GRADIENT_RANGE_MAX_WEIGHT);
            
            boolean isReasonable = (diff >= minGradient && diff <= maxGradient);
            // Tools.println("[reviewCollegeGradient] 梯度" + (i+1) + ": 分数差=" + diff + ", 范围=" + avgRange + ", 最小梯度=" + minGradient + ", 最大梯度=" + maxGradient + ", 合理=" + isReasonable);
            
            if (isReasonable) {
                reasonableGradients++;
            }
        }
        
        double reasonableRate = (double) reasonableGradients / totalGradients;
//        Tools.println("[reviewCollegeGradient] 合理梯度数: " + reasonableGradients + ", 合理率: " + reasonableRate);
        
        String grade;
        if (reasonableRate >= GRADIENT_REASONABLE_RATE_A) {
            grade = GRADE_A;
        } else if (reasonableRate >= GRADIENT_REASONABLE_RATE_B) {
            grade = GRADE_B;
        } else if (reasonableRate >= GRADIENT_REASONABLE_RATE_C) {
            grade = GRADE_C;
        } else {
            grade = GRADE_D;
        }
        
        Tools.println("[reviewCollegeGradient] 院校梯度评估结果: " + grade + " (阈值A:" + GRADIENT_REASONABLE_RATE_A + ", B:" + GRADIENT_REASONABLE_RATE_B + ", C:" + GRADIENT_REASONABLE_RATE_C + ")");
        return grade;
    }
    

    
    /**
     * 3. 院校完整度评估
     * 基于TOTAL_VOLUNTEER_COUNT比例分析志愿填报数量的充分性
     * 按已填报志愿数量（院校代码+专业组组合）与总志愿数的比例进行评估
     */
    public static String reviewCollegeCompleteness(FormReviewContext context) {
        Tools.println("[reviewCollegeCompleteness] 开始院校完整度评估");
        
        List<SuperForm> formList = context.getSuperFormList();
        if (formList == null || formList.isEmpty()) {
            Tools.println("[reviewCollegeCompleteness] 志愿表单为空，返回D级");
            return GRADE_D;
        }
        
        Tools.println("[reviewCollegeCompleteness] 总志愿数配置: " + TOTAL_VOLUNTEER_COUNT + ", 表单数量: " + formList.size());
        
        // 统计已填报的志愿数量（院校代码+专业组组合）
        Set<String> uniqueVolunteers = new HashSet<>();
        
        for (SuperForm superForm : formList) {
            if (!Tools.isEmpty(superForm.getYxdm())) {
                // 使用院校代码+专业组作为唯一标识
                String volunteerKey = superForm.getYxdm() + "|" + Tools.trim(superForm.getZyz());
                uniqueVolunteers.add(volunteerKey);
            }
        }
        
        int filledVolunteerCount = uniqueVolunteers.size();
        Tools.println("[reviewCollegeCompleteness] 已填报志愿数: " + filledVolunteerCount);
        
        // 计算各级别的阈值
        int thresholdA = (int) Math.floor(TOTAL_VOLUNTEER_COUNT * COLLEGE_COMPLETENESS_A_RATIO);
        int thresholdB = (int) Math.floor(TOTAL_VOLUNTEER_COUNT * COLLEGE_COMPLETENESS_B_RATIO);
        int thresholdC = (int) Math.floor(TOTAL_VOLUNTEER_COUNT * COLLEGE_COMPLETENESS_C_RATIO);
        
        Tools.println("[reviewCollegeCompleteness] 阈值设定 - A级: " + thresholdA + ", B级: " + thresholdB + ", C级: " + thresholdC);
        
        String grade;
        if (filledVolunteerCount >= thresholdA) {
            grade = GRADE_A;
        } else if (filledVolunteerCount >= thresholdB) {
            grade = GRADE_B;
        } else if (filledVolunteerCount >= thresholdC) {
            grade = GRADE_C;
        } else {
            grade = GRADE_D;
        }
        
        Tools.println("[reviewCollegeCompleteness] 院校完整度评估结果: " + grade + " (志愿数: " + filledVolunteerCount + ")");
        return grade;
    }
    
    /**
     * 4. 专业完整度评估  
     * 基于新的专业填报完整度规则进行评估
     * 每个志愿（院校代码+专业组）可填报6个专业，按照完整度比例评估
     */
    public static String reviewMajorCompleteness(FormReviewContext context) {
        Tools.println("[reviewMajorCompleteness] 开始专业完整度评估");
        
        List<SuperForm> formList = context.getSuperFormList();
        if (formList == null || formList.isEmpty()) {
            Tools.println("[reviewMajorCompleteness] 志愿表单为空，返回D级");
            return GRADE_D;
        }
        
        Map<String, List<SuperForm>> collegeGroups = FormReviewUtils.groupByCollege(formList);
        int totalGroups = collegeGroups.size();
        int gradeAGroups = 0;
        int gradeBGroups = 0;
        int gradeCGroups = 0;
        
        Tools.println("[reviewMajorCompleteness] 专业组总数: " + totalGroups + ", 可填报专业数: " + MAX_MAJORS_PER_VOLUNTEER);
        
        for (List<SuperForm> group : collegeGroups.values()) {
            if (group.isEmpty()) continue;
            
            // 获取院校名称和专业组
            SuperForm firstForm = group.get(0);
            String yxdm = firstForm.getYxdm();
            String zyz = firstForm.getZyz();
            
            // 统计已填报的专业数量
            int filledMajors = 0;
            for (SuperForm superForm : group) {
                if (!Tools.isEmpty(superForm.getZydm())) {
                    filledMajors++;
                }
            }
            
            String groupGrade = evaluateSingleGroupMajorCompleteness(context, yxdm, zyz, filledMajors);
            // Tools.println("[reviewMajorCompleteness] 院校: " + yxdm + ", 专业组: " + zyz + ", 已填专业数: " + filledMajors + ", 单组评级: " + groupGrade);
            
            switch (groupGrade) {
                case GRADE_A:
                    gradeAGroups++;
                    break;
                case GRADE_B:
                    gradeBGroups++;
                    break;
                case GRADE_C:
                    gradeCGroups++;
                    break;
                // GRADE_D 不需要计数
            }
        }
        
        if (totalGroups == 0) {
            Tools.println("[reviewMajorCompleteness] 专业组数量为0，返回D级");
            return GRADE_D;
        }
        
        // 计算各等级的比例
        double gradeARate = (double) gradeAGroups / totalGroups;
        double gradeBRate = (double) gradeBGroups / totalGroups;
        double gradeCRate = (double) gradeCGroups / totalGroups;
        
        Tools.println("[reviewMajorCompleteness] 等级分布 - A级组: " + gradeAGroups + "(" + gradeARate + "), B级组: " + gradeBGroups + "(" + gradeBRate + "), C级组: " + gradeCGroups + "(" + gradeCRate + ")");
        
        // 根据A级专业组的比例进行整体评估
        String grade;
        if (gradeARate >= MAJOR_COMPLETENESS_A) {
            grade = GRADE_A;
        } else if (gradeARate >= MAJOR_COMPLETENESS_B || 
                   (gradeARate + gradeBRate) >= MAJOR_COMPLETENESS_A) {
            grade = GRADE_B;
        } else if (gradeARate >= MAJOR_COMPLETENESS_C || 
                   (gradeARate + gradeBRate + gradeCRate) >= MAJOR_COMPLETENESS_B) {
            grade = GRADE_C;
        } else {
            grade = GRADE_D;
        }
        
        Tools.println("[reviewMajorCompleteness] 专业完整度评估结果: " + grade + " (阈值A:" + MAJOR_COMPLETENESS_A + ", B:" + MAJOR_COMPLETENESS_B + ", C:" + MAJOR_COMPLETENESS_C + ")");
        return grade;
    }
    
    /**
     * 评估单个专业组的完整度
     * 按照新的规则：
     * 4.1：已填报专业数 == 计划专业数，为A
     * 4.2：已填报专业数 == 可填报专业数（6个），为A
     * 4.3：已填报专业数 < 可填报专业数且 < 计划专业数，按比例评估
     */
    private static String evaluateSingleGroupMajorCompleteness(
            FormReviewContext context, String yxdm, String zyz, int filledMajors) {
        
//        Tools.println("[evaluateSingleGroupMajorCompleteness] 评估专业组: " + yxdm + "|" + zyz + ", 已填专业数: " + filledMajors);
        
        if (filledMajors == 0) {
            Tools.println("[evaluateSingleGroupMajorCompleteness] 无专业填报，返回D级");
            return GRADE_D;
        }
        
        // 规则4.2：已填报专业数等于可填报专业数（6个），为A
        if (filledMajors >= MAX_MAJORS_PER_VOLUNTEER) {
            Tools.println("[evaluateSingleGroupMajorCompleteness] 规则4.2：已填满" + MAX_MAJORS_PER_VOLUNTEER + "个专业，返回A级");
            return GRADE_A;
        }
        
        // 获取计划数据以应用规则4.1和4.3
        if (context.isPlanDataLoaded()) {
            ProfessionalGroupInfo groupInfo = context.getGroupInfo(yxdm, zyz);
            if (groupInfo != null) {
                int availableMajors = groupInfo.getTotalMajors();
//                Tools.println("[evaluateSingleGroupMajorCompleteness] 计划专业数: " + availableMajors);
                
                // 规则4.1：已填报专业数等于这个专业组对应的计划专业数，为A
                if (availableMajors > 0 && filledMajors >= availableMajors) {
//                    Tools.println("[evaluateSingleGroupMajorCompleteness] 规则4.1：已填报专业数≥计划专业数，返回A级");
                    return GRADE_A;
                }
                
                // 规则4.3：已填报专业数 < 可填报专业数且 < 计划专业数，按比例评估
                if (filledMajors < MAX_MAJORS_PER_VOLUNTEER && filledMajors < availableMajors) {
                    double fillRatio = (double) filledMajors / MAX_MAJORS_PER_VOLUNTEER;
//                    Tools.println("[evaluateSingleGroupMajorCompleteness] 规则4.3：填报比例 " + fillRatio + " (阈值B:" + MAJOR_FILL_COMPLETENESS_B + ", C:" + MAJOR_FILL_COMPLETENESS_C + ")");
                    
                    if (fillRatio >= MAJOR_FILL_COMPLETENESS_B) {
                        return GRADE_B;
                    } else if (fillRatio >= MAJOR_FILL_COMPLETENESS_C) {
                        return GRADE_C;
                    } else {
                        return GRADE_D;
                    }
                }
            } else {
                Tools.println("[evaluateSingleGroupMajorCompleteness] 无专业组信息");
            }
        } else {
            Tools.println("[evaluateSingleGroupMajorCompleteness] 计划数据未加载");
        }
        
        // 没有详细数据时，按填报专业数与可填报专业数的比例评估
        double fillRatio = (double) filledMajors / MAX_MAJORS_PER_VOLUNTEER;
//        Tools.println("[evaluateSingleGroupMajorCompleteness] 默认规则：填报比例 " + fillRatio + " (阈值B:" + MAJOR_FILL_COMPLETENESS_B + ", C:" + MAJOR_FILL_COMPLETENESS_C + ")");
        
        if (fillRatio >= MAJOR_FILL_COMPLETENESS_B) {
            return GRADE_B;
        } else if (fillRatio >= MAJOR_FILL_COMPLETENESS_C) {
            return GRADE_C;
        } else {
            return GRADE_D;
        }
    }
    
    
    /**
     * 5. 院校梯度顺序评估
     * 基于详细计划数据分析冲稳保比例和排列合理性，使用高级算法
     */
    public static String reviewCollegeGradientOrder(FormReviewContext context) {
        Tools.println("[reviewCollegeGradientOrder] 开始院校梯度顺序评估");
        
        List<SuperForm> formList = context.getSuperFormList();
        ZDKSRank userRank = context.getUserRank();
        
        if (formList == null || formList.isEmpty() || userRank == null) {
            Tools.println("[reviewCollegeGradientOrder] 表单为空或用户位次为空，返回D级");
            return GRADE_D;
        }
        
        int userScore = userRank.getScore();
        int chongCount = 0, wenCount = 0, baoCount = 0;
        int orderErrors = 0;
        
        Tools.println("[reviewCollegeGradientOrder] 用户分数: " + userScore + ", 冲击阈值: +" + CHONG_SCORE_DIFF + ", 保底阈值: -" + BAO_SCORE_DIFF);
        
        Map<String, List<SuperForm>> collegeGroups = FormReviewUtils.groupByCollege(formList);
        List<String> groupKeys = new ArrayList<>(collegeGroups.keySet());
        Collections.sort(groupKeys);
        
        double lastAvgScore = Double.MAX_VALUE;
        
        Tools.println("[reviewCollegeGradientOrder] 院校专业组数量: " + collegeGroups.size() + ", 开始逐个分析");
        
        for (String groupKey : groupKeys) {
            List<SuperForm> group = collegeGroups.get(groupKey);
            if (group.isEmpty()) continue;
            
            double avgScore = FormReviewUtils.calculateAvgScore(group, context);
            
            if (avgScore > 0) {
                // 检查顺序是否递减
                boolean orderCorrect = avgScore <= lastAvgScore;
                if (!orderCorrect) {
                    orderErrors++;
                }
                
                // 使用常量：分类计数（使用更精确的分类标准）
                String category;
                if (avgScore > userScore) {
                    chongCount++;
                    category = "冲击";
                } else if (avgScore > userScore - BAO_SCORE_DIFF) {
                    wenCount++;
                    category = "稳妥";
                } else {
                    baoCount++;
                    category = "保底";
                }
                
                // Tools.println("[reviewCollegeGradientOrder] 分组: " + groupKey + ", 院校: " + group.get(0).getYxdm() + ", 平均分: " + avgScore + ", 类别: " + category + ", 顺序正确: " + orderCorrect);
                lastAvgScore = avgScore;
            }
        }
        
        // 使用常量：检查冲稳保比例是否合理
        int totalValid = chongCount + wenCount + baoCount;
        boolean ratioReasonable = (chongCount >= CHONG_COUNT_MIN && chongCount <= CHONG_COUNT_MAX) && 
                                 (wenCount >= WEN_COUNT_MIN && wenCount <= WEN_COUNT_MAX) && 
                                 (baoCount >= BAO_COUNT_MIN && baoCount <= BAO_COUNT_MAX);
        
        double orderAccuracy = 1.0 - (double) orderErrors / Math.max(totalValid - 1, 1);
        
        Tools.println("[reviewCollegeGradientOrder] 冲稳保统计 - 冲击: " + chongCount + ", 稳妥: " + wenCount + ", 保底: " + baoCount);
        Tools.println("[reviewCollegeGradientOrder] 比例合理性: " + ratioReasonable + ", 顺序错误数: " + orderErrors + ", 顺序准确率: " + orderAccuracy);
        
        String grade;
        if (ratioReasonable && orderAccuracy >= MAJOR_GRADIENT_ORDER_A) {
            grade = GRADE_A;
        } else if (ratioReasonable && orderAccuracy >= MAJOR_GRADIENT_ORDER_B) {
            grade = GRADE_B;
        } else if (orderAccuracy >= MAJOR_GRADIENT_ORDER_C) {
            grade = GRADE_C;
        } else {
            grade = GRADE_D;
        }
        
        Tools.println("[reviewCollegeGradientOrder] 院校梯度顺序评估结果: " + grade + " (阈值A:" + MAJOR_GRADIENT_ORDER_A + ", B:" + MAJOR_GRADIENT_ORDER_B + ", C:" + MAJOR_GRADIENT_ORDER_C + ")");
        return grade;
    }
    

    
    /**
     * 6. 专业梯度顺序评估
     * 基于详细计划数据分析同一院校内专业排列，使用高级算法
     */
    public static String reviewMajorGradientOrder(FormReviewContext context) {
        Tools.println("[reviewMajorGradientOrder] 开始专业梯度顺序评估");
        
        List<SuperForm> formList = context.getSuperFormList();
        
        if (formList == null || formList.isEmpty()) {
            Tools.println("[reviewMajorGradientOrder] 志愿表单为空，返回D级");
            return GRADE_D;
        }
        
        Map<String, List<SuperForm>> collegeGroups = FormReviewUtils.groupByCollege(formList);
        int totalGroups = 0;
        int goodOrderGroups = 0;
        
        Tools.println("[reviewMajorGradientOrder] 院校专业组总数: " + collegeGroups.size() + ", 计划数据已加载: " + context.isPlanDataLoaded());
        
        for (List<SuperForm> group : collegeGroups.values()) {
            if (group.size() <= 1) continue;
            
            totalGroups++;
            SuperForm firstForm = group.get(0);
            // Tools.println("[reviewMajorGradientOrder] 分析专业组: " + firstForm.getYxdm() + "|" + firstForm.getZyz() + ", 专业数: " + group.size());
            
            // Tools.println("[reviewMajorGradientOrder] 使用基础算法");
            int orderErrors = 0;
            for (int i = 0; i < group.size() - 1; i++) {
                int score1 = FormReviewUtils.getAvgThreeYearScore(group.get(i), context);
                int score2 = FormReviewUtils.getAvgThreeYearScore(group.get(i + 1), context);
                
                // 专业应该按分数递减排列
                if (score1 > 0 && score2 > 0 && score2 > score1) {
                    orderErrors++;
                }
            }
            
            boolean isGoodOrder = orderErrors <= MAJOR_ORDER_ALLOWED_ERRORS;
            // Tools.println("[reviewMajorGradientOrder] 基础算法 - 顺序错误数: " + orderErrors + ", 顺序合理: " + isGoodOrder);
            
            if (isGoodOrder) {
                goodOrderGroups++;
            }
        }
        
        if (totalGroups == 0) {
            Tools.println("[reviewMajorGradientOrder] 无有效专业组，返回C级");
            return GRADE_C;
        }
        
        double goodRate = (double) goodOrderGroups / totalGroups;
        Tools.println("[reviewMajorGradientOrder] 顺序合理的专业组: " + goodOrderGroups + "/" + totalGroups + ", 合理率: " + goodRate);
        
        String grade;
        if (goodRate >= MAJOR_GRADIENT_ORDER_A) {
            grade = GRADE_A;
        } else if (goodRate >= MAJOR_GRADIENT_ORDER_B) {
            grade = GRADE_B;
        } else if (goodRate >= MAJOR_GRADIENT_ORDER_C) {
            grade = GRADE_C;
        } else {
            grade = GRADE_D;
        }
        
        Tools.println("[reviewMajorGradientOrder] 专业梯度顺序评估结果: " + grade + " (阈值A:" + MAJOR_GRADIENT_ORDER_A + ", B:" + MAJOR_GRADIENT_ORDER_B + ", C:" + MAJOR_GRADIENT_ORDER_C + ")");
        return grade;
    }
    
    /**
     * 7. 专业服从调剂评估
     * 检查是否选择了专业服从调剂
     */
    public static String reviewMajorObedience(FormReviewContext context) {
        Tools.println("[reviewMajorObedience] 开始专业服从调剂评估");
        
        List<SuperForm> formList = context.getSuperFormList();
        if (formList == null || formList.isEmpty()) {
            Tools.println("[reviewMajorObedience] 志愿表单为空，返回D级");
            return GRADE_D;
        }
        
        Map<String, List<SuperForm>> collegeGroups = FormReviewUtils.groupByCollege(formList);
        int totalGroups = collegeGroups.size();
        int obedienceGroups = 0;
        
        Tools.println("[reviewMajorObedience] 专业组总数: " + totalGroups + ", 服从调剂标志: " + OBEDIENCE_FLAG);
        
        for (List<SuperForm> group : collegeGroups.values()) {
            // 检查该院校是否有服从调剂标记
            boolean hasObedience = false;
            for (SuperForm superForm : group) {
                if (Tools.getInt(superForm.getAdjust_zy()) == OBEDIENCE_FLAG) {  // 使用常量
                    hasObedience = true;
                    break;
                }
            }
            
            if (hasObedience) {
                obedienceGroups++;
                if (group.size() > 0) {
                    // Tools.println("[reviewMajorObedience] 院校 " + group.get(0).getYxdm() + " 选择了服从调剂");
                }
            }
        }
        
        if (totalGroups == 0) {
            Tools.println("[reviewMajorObedience] 专业组数量为0，返回D级");
            return GRADE_D;
        }
        
        double obedienceRate = (double) obedienceGroups / totalGroups;
        Tools.println("[reviewMajorObedience] 服从调剂的专业组: " + obedienceGroups + "/" + totalGroups + ", 服从率: " + obedienceRate);
        
        String grade;
        if (obedienceRate >= OBEDIENCE_RATE_A) {
            grade = GRADE_A;
        } else if (obedienceRate >= OBEDIENCE_RATE_B) {
            grade = GRADE_B;
        } else if (obedienceRate >= OBEDIENCE_RATE_C) {
            grade = GRADE_C;
        } else {
            grade = GRADE_D;
        }
        
        Tools.println("[reviewMajorObedience] 专业服从调剂评估结果: " + grade + " (阈值A:" + OBEDIENCE_RATE_A + ", B:" + OBEDIENCE_RATE_B + ", C:" + OBEDIENCE_RATE_C + ")");
        return grade;
    }
    
    /**
     * 8. 保底院校风险评估
     * 基于详细计划数据进行更精确的风险分析，使用高级算法
     */
    public static String reviewSafetyCollegeRisk(FormReviewContext context) {
        Tools.println("[reviewSafetyCollegeRisk] 开始保底院校风险评估");
        
        List<SuperForm> formList = context.getSuperFormList();
        ZDKSRank userRank = context.getUserRank();
        
        if (formList == null || formList.isEmpty() || userRank == null) {
            Tools.println("[reviewSafetyCollegeRisk] 表单为空或用户位次为空，返回D级");
            return GRADE_D;
        }
        
        int userScore = userRank.getScore();
        int userWc = userRank.getWc();
        
        Tools.println("[reviewSafetyCollegeRisk] 用户分数: " + userScore + ", 位次: " + userWc + ", 检查保底志愿数: " + SAFETY_CHECK_COUNT);
        
        // 找出后N个志愿作为保底志愿，使用常量
        Map<String, List<SuperForm>> collegeGroups = FormReviewUtils.groupByCollege(formList);
        List<String> groupKeys = new ArrayList<>(collegeGroups.keySet());
        Collections.sort(groupKeys, Collections.reverseOrder());
        
        int safetyCount = 0;
        int totalSafetyCheck = Math.min(SAFETY_CHECK_COUNT, groupKeys.size());
        
        Tools.println("[reviewSafetyCollegeRisk] 实际检查的保底志愿数: " + totalSafetyCheck);
        
        for (int i = 0; i < totalSafetyCheck; i++) {
            String groupKey = groupKeys.get(i);
            List<SuperForm> group = collegeGroups.get(groupKey);
            
            if (!group.isEmpty()) {
                double avgScore = FormReviewUtils.calculateAvgScore(group, context);
                
                if (avgScore > 0) {
                    // 计算安全分数，使用常量（考虑位次变化）
                    int safetyMargin = userScore - (int)avgScore;
                    boolean isSafe = false;
                    
                    // 使用常量：分数高出一定范围认为安全
                    if (safetyMargin >= SAFETY_SCORE_DIFF) {
                        isSafe = true;
                    } else if (safetyMargin >= SAFETY_SCORE_DIFF_GOOD_RANK && userWc <= GOOD_RANK_FOR_SAFETY) {
                        // 位次较好时可以适当降低安全边际
                        isSafe = true;
                    }
                    
                    if (isSafe) {
                        safetyCount++;
                    }
                    
                    Tools.println("[reviewSafetyCollegeRisk] 分组: " + groupKey + ", 院校: " + group.get(0).getYxdm() + ", 平均分: " + avgScore + ", 安全边际: " + safetyMargin + ", 安全: " + isSafe);
                }
            }
        }
        
        if (totalSafetyCheck == 0) {
            Tools.println("[reviewSafetyCollegeRisk] 无保底志愿可检查，返回D级");
            return GRADE_D;
        }
        
        double safetyRate = (double) safetyCount / totalSafetyCheck;
        Tools.println("[reviewSafetyCollegeRisk] 安全的保底志愿: " + safetyCount + "/" + totalSafetyCheck + ", 安全率: " + safetyRate);
        
        String grade;
        if (safetyRate >= SAFETY_RATE_A) {
            grade = GRADE_A;
        } else if (safetyRate >= SAFETY_RATE_B) {
            grade = GRADE_B;
        } else if (safetyRate >= SAFETY_RATE_C) {
            grade = GRADE_C;
        } else {
            grade = GRADE_D;
        }
        
        Tools.println("[reviewSafetyCollegeRisk] 保底风险评估结果: " + grade + " (阈值A:" + SAFETY_RATE_A + ", B:" + SAFETY_RATE_B + ", C:" + SAFETY_RATE_C + ")");
        return grade;
    }
    
    /**
     * 9. 区域优势评估
     * 基于详细计划数据进行地域分布分析，使用高级算法
     */
    public static String evaluateRegionalAdvantage(FormReviewContext context) {
        Tools.println("[evaluateRegionalAdvantage] 开始区域优势评估");
        
        List<SuperForm> formList = context.getSuperFormList();
        ZyzdProvinceConfig provinceConfig = context.getProvinceConfig();
        
        if (formList == null || formList.isEmpty()) {
            Tools.println("[evaluateRegionalAdvantage] 志愿表单为空，返回D级");
            return GRADE_D;
        }
        
        Map<String, Integer> provinceCount = new HashMap<>();
        Set<String> developedRegions = new HashSet<>();
        developedRegions.addAll(Arrays.asList("北京", "上海", "江苏", "浙江", "广东", "天津", "四川", "重庆", "福建", "陕西", "山东", "湖北", "湖南"));
        
        int developedCount = 0;
        String currentProvince = provinceConfig != null ? provinceConfig.getP_name() : "";
        int localCount = 0;
        int totalColleges = 0;
        
        Tools.println("[evaluateRegionalAdvantage] 当前省份: " + currentProvince + ", 使用详细数据: " + context.isPlanDataLoaded());

        HashMap<String, String> existYxdmMap = new HashMap<>(); // TODO
        for (SuperForm superForm : formList) {
        	String yxdmAndZyzKey = Tools.trim(superForm.getYxdm()) + "|" + Tools.trim(superForm.getZyz());
        	if(existYxdmMap.containsKey(yxdmAndZyzKey)){
        		continue;
        	}
        	existYxdmMap.put(yxdmAndZyzKey, currentProvince);
            String province = FormReviewUtils.getUniversityProvince(superForm);
            if (!Tools.isEmpty(province)) {
                provinceCount.put(province, provinceCount.getOrDefault(province, 0) + 1);
                totalColleges++;
                
                if (developedRegions.contains(province)) {
                    developedCount++;
                }
                
                if (province.equals(currentProvince)) {
                    localCount++;
                }
            }
        }
        
        if (totalColleges == 0) {
            Tools.println("[evaluateRegionalAdvantage] 无有效院校地域信息，返回D级");
            return GRADE_D;
        }
        
        double localRate = (double) localCount / totalColleges;
        double developedRate = (double) developedCount / totalColleges;
        int regionCount = provinceCount.size();
        
        Tools.println("[evaluateRegionalAdvantage] 地域统计 - 总院校: " + totalColleges + ", 省份数: " + regionCount + ", 本省院校: " + localCount + "(" + localRate + "), 发达地区: " + developedCount + "(" + developedRate + ")");
        Tools.println("[evaluateRegionalAdvantage] 省份分布: " + provinceCount);
        
        // 使用常量：综合评估：地域多样性 + 发达地区比例 + 本省比例
        boolean diversityGood = regionCount >= REGION_DIVERSITY_THRESHOLD;
        boolean developedGood = developedRate >= DEVELOPED_REGION_RATE_MIN && developedRate <= DEVELOPED_REGION_RATE_MAX;
        boolean localGood = localRate >= LOCAL_REGION_RATE_MIN && localRate <= LOCAL_REGION_RATE_MAX;
        
        Tools.println("[evaluateRegionalAdvantage] 评估标准 - 多样性好: " + diversityGood + "(阈值≥" + REGION_DIVERSITY_THRESHOLD + "), 发达地区比例好: " + developedGood + "(范围" + DEVELOPED_REGION_RATE_MIN + "-" + DEVELOPED_REGION_RATE_MAX + "), 本省比例好: " + localGood + "(范围" + LOCAL_REGION_RATE_MIN + "-" + LOCAL_REGION_RATE_MAX + ")");
        
        String grade;
        if (diversityGood && developedGood && localGood) {
            grade = GRADE_A;
        } else if ((diversityGood && developedGood) || (diversityGood && localGood)) {
            grade = GRADE_B;
        } else if (diversityGood || developedGood) {
            grade = GRADE_C;
        } else {
            grade = GRADE_D;
        }
        
        Tools.println("[evaluateRegionalAdvantage] 区域优势评估结果: " + grade);
        return grade;
    }
    
    /**
     * 10. 专业集中度评估
     * 基于详细计划数据进行专业分布分析，使用高级算法
     */
    /**
     * 10. 专业集中度评估 - 重构版本
     * 🎯 核心修复：基于用户实际选择的专业进行集中度分析，而非整个专业组
     */
    public static String evaluateMajorConcentration(FormReviewContext context) {
        Tools.println("[evaluateMajorConcentration] 🎯 开始专业集中度评估");
        
        List<SuperForm> formList = context.getSuperFormList();
        
        if (formList == null || formList.isEmpty()) {
            Tools.println("[evaluateMajorConcentration] 志愿表单为空，返回D级");
            return GRADE_D;
        }
        
        Map<String, Integer> majorCategoryCount = new HashMap<>();
        
        if (!formList.isEmpty()) {
            Tools.println("[evaluateMajorConcentration] 使用志愿表单数据进行分析");
            String pcCode = context.getSuperFormMain().getPc_code();
            
            for (SuperForm superForm : formList) {
                String category = FormReviewUtils.getMajorCategory(superForm, pcCode);
                if (!Tools.isEmpty(category)) {
                    majorCategoryCount.put(category, majorCategoryCount.getOrDefault(category, 0) + 1);
                }
            }
        } 
        
        if (majorCategoryCount.isEmpty()) {
            Tools.println("[evaluateMajorConcentration] 无专业类别信息，返回D级");
            return GRADE_D;
        }
        
        int categoryCount = majorCategoryCount.size();

        String grade;
        if (categoryCount > MAJOR_CATEGORY_COUNT_A) {
            grade = GRADE_D;
        } else if (categoryCount > MAJOR_CATEGORY_COUNT_B) {
            grade = GRADE_C;
        } else if (categoryCount > MAJOR_CATEGORY_COUNT_C) {
            grade = GRADE_B;
        } else {
            grade = GRADE_A;
        }
        
        Tools.println("[evaluateMajorConcentration] 专业集中度评估结果: " + grade + " (阈值A:" + MAJOR_CATEGORY_COUNT_A + ", B:" + MAJOR_CATEGORY_COUNT_B + ", C:" + MAJOR_CATEGORY_COUNT_C + ")");
        return grade;
    }
    
    /**
     * 计算综合评分
     * 根据实际使用的维度数量进行加权计算
     */
    public static double calculateOverallScore(FormReviewResult result) {
        Tools.println("[calculateOverallScore] 开始计算综合评分");
        
        // 检查是否滑档（未被录取）
        if (result.isHasAdmissionProbabilityData() && result.getAdmissionProbabilityResult() != null) {
            AdmissionProbabilityEvaluator.AdmissionProbabilityResult admissionResult = result.getAdmissionProbabilityResult();
//            Tools.println("[calculateOverallScore] 存在录取概率数据和结果对象。"); 
            
            // 如果录取预测列表为空或没有录取预测，说明滑档
            if (admissionResult.getAdmissionPredictions() == null || admissionResult.getAdmissionPredictions().isEmpty()) {
                java.util.Random random = new java.util.Random();
                double slidingScore = 28.0 + random.nextDouble() * 4.0; // 28-32分随机数
//                Tools.println("[calculateOverallScore] 检测到滑档情况（无录取预测），返回滑档评分: " + String.format("%.1f", slidingScore));
                return slidingScore;
            } else { // 添加的else块
            	Tools.println("[calculateOverallScore] 存在录取预测，不属于滑档情况。录取预测数量: " + admissionResult.getAdmissionPredictions().size());
                for (AdmissionProbabilityEvaluator.AdmissionPrediction prediction : admissionResult.getAdmissionPredictions()) {
//                    Tools.println("[calculateOverallScore] 录取预测详情 -> 院校名称: " + prediction.getYxmc() + ", 预测排名: " + prediction.getPredictionRank() + ", 录取概率: " + prediction.getAdmissionProbability());
                }
            }
        }

        
        String[] grades = {
            result.getGrade1(), result.getGrade2(), result.getGrade3(), result.getGrade4(),
            result.getGrade5(), result.getGrade6(), result.getGrade7(), result.getGrade8(),
            result.getGrade9(), result.getGrade10(), result.getGrade11(), result.getGrade12(),
            result.getGrade13(), result.getGrade14(), result.getGrade15()
        };
        
        // 基础评估维度权重
        double[] weights = {
            BASIC_DIMENSION_WEIGHT, BASIC_DIMENSION_WEIGHT, BASIC_DIMENSION_WEIGHT, BASIC_DIMENSION_WEIGHT, BASIC_DIMENSION_WEIGHT, 
            BASIC_DIMENSION_WEIGHT, BASIC_DIMENSION_WEIGHT, BASIC_DIMENSION_WEIGHT, BASIC_DIMENSION_WEIGHT, BASIC_DIMENSION_WEIGHT,  // 前10个维度
            PERSONAL_FITNESS_WEIGHT, ECONOMIC_FITNESS_WEIGHT, GEOGRAPHIC_PREFERENCE_WEIGHT, MAJOR_PREFERENCE_WEIGHT, PHYSICAL_FITNESS_WEIGHT  // 用户需求评估维度
        };
        
        double totalScore = 0;
        double totalWeight = 0;
        
        // 确定实际使用的维度范围
        int useDimensionCount = result.isHasUserNeedsData() ? TOTAL_DIMENSION_COUNT : BASIC_DIMENSION_COUNT;
        Tools.println("[calculateOverallScore] 使用维度数量: " + useDimensionCount + ", 有用户需求数据: " + result.isHasUserNeedsData());
        
        for(int i = 0; i < useDimensionCount && i < grades.length; i++) {
            String grade = grades[i];
            if(grade != null) {
                double gradeScore = 0;
                switch(grade) {
                    case GRADE_A: case "1": gradeScore = GRADE_A_SCORE; break;
                    case GRADE_B: case "2": gradeScore = GRADE_B_SCORE; break;
                    case GRADE_C: case "3": gradeScore = GRADE_C_SCORE; break;
                    case GRADE_D: case "4": gradeScore = GRADE_D_SCORE; break;
                }
                
                double weightedScore = gradeScore * weights[i];
                totalScore += weightedScore;
                totalWeight += weights[i];
                
                Tools.println("[calculateOverallScore] 维度" + (i+1) + ": 等级=" + grade + ", 分数=" + gradeScore + ", 权重=" + weights[i] + ", 加权分=" + weightedScore);
            }
        }
        
        double finalScore = totalWeight > 0 ? totalScore / totalWeight : 0;
        Tools.println("[calculateOverallScore] 总分: " + totalScore + ", 总权重: " + totalWeight + ", 最终评分: " + finalScore);
        
        return finalScore;
    }
    
    
    /**
     * 生成志愿统计信息（按专业组统计）
     */    
    /**
     * 获取维度名称列表
     * @param includeUserNeeds 是否包含用户需求维度
     * @return 维度名称数组
     */
    public static String[] getDimensionNames(boolean includeUserNeeds) {
        String[] basicDimensions = {
            "分段定位定级",     // grade1
            "院校级差梯度",     // grade2
            "院校完整度",       // grade3
            "专业完整度",       // grade4
            "院校梯度顺序",     // grade5
            "专业梯度顺序",     // grade6
            "专业服从调剂",     // grade7
            "保底院校风险",     // grade8
            "区域优势",         // grade9
            "专业集中度"        // grade10
        };
        
        if (!includeUserNeeds) {
            return basicDimensions;
        }
        
        String[] userNeedsDimensions = {
            "个人适配性",       // grade11
            "经济适配性",       // grade12
            "地域偏好匹配",     // grade13
            "专业偏好匹配",     // grade14
            "身体条件适配"      // grade15
        };
        
        String[] allDimensions = new String[TOTAL_DIMENSION_COUNT];
        System.arraycopy(basicDimensions, 0, allDimensions, 0, BASIC_DIMENSION_COUNT);
        System.arraycopy(userNeedsDimensions, 0, allDimensions, BASIC_DIMENSION_COUNT, USER_NEEDS_DIMENSION_COUNT);
        
        return allDimensions;
    }
    
    /**
     * 获取维度描述列表
     * @param includeUserNeeds 是否包含用户需求维度
     * @return 维度描述数组
     */
    public static String[] getDimensionDescriptions(boolean includeUserNeeds) {
        String[] basicDescriptions = {
            "基于志愿分数差值评估分段定位合理性",
            "分析院校间分数梯度设置的科学性",
            "评估志愿填报数量的充分性",
            "检查专业选择的完整性",
            "评估冲稳保比例和院校排列顺序",
            "分析同一院校内专业排列合理性",
            "检查专业服从调剂选择情况",
            "评估保底院校的安全性",
            "分析院校地域分布的优劣",
            "评估专业选择的多样性"
        };
        
        if (!includeUserNeeds) {
            return basicDescriptions;
        }
        
        String[] userNeedsDescriptions = {
            "评估志愿与个人特征的匹配度",
            "评估志愿与经济条件的适配性",
            "评估志愿与地域偏好的匹配度",
            "评估志愿与专业兴趣的匹配度",
            "评估志愿与身体条件的适配性"
        };
        
        String[] allDescriptions = new String[TOTAL_DIMENSION_COUNT];
        System.arraycopy(basicDescriptions, 0, allDescriptions, 0, BASIC_DIMENSION_COUNT);
        System.arraycopy(userNeedsDescriptions, 0, allDescriptions, BASIC_DIMENSION_COUNT, USER_NEEDS_DIMENSION_COUNT);
        
        return allDescriptions;
    }
    
    
    // ==================== 内部数据结构类 ====================
    
    /**
     * 审核结果封装类
     */
    public static class FormReviewResult {
        private String grade1;  // 分段定位定级
        private String grade2;  // 院校级差梯度
        private String grade3;  // 院校完整度
        private String grade4;  // 专业完整度
        private String grade5;  // 院校梯度顺序
        private String grade6;  // 专业梯度顺序
        private String grade7;  // 专业服从调剂
        private String grade8;  // 保底院校风险
        private String grade9;  // 区域优势
        private String grade10; // 专业集中度

        // 用户需求维度
        private String grade11; // 个人适配性
        private String grade12; // 经济适配性
        private String grade13; // 地域偏好匹配
        private String grade14; // 专业偏好匹配
        private String grade15; // 身体条件适配

        // 每个维度的具体建议
        private String suggestion1;  // 分段定位建议
        private String suggestion2;  // 院校级差梯度建议
        private String suggestion3;  // 院校完整度建议
        private String suggestion4;  // 专业完整度建议
        private String suggestion5;  // 院校梯度顺序建议
        private String suggestion6;  // 专业梯度顺序建议
        private String suggestion7;  // 专业服从调剂建议
        private String suggestion8;  // 保底院校风险建议
        private String suggestion9;  // 区域优势建议
        private String suggestion10; // 专业集中度建议
        
        // 用户需求维度建议
        private String suggestion11; // 个人适配性建议
        private String suggestion12; // 经济适配性建议
        private String suggestion13; // 地域偏好匹配建议
        private String suggestion14; // 专业偏好匹配建议
        private String suggestion15; // 身体条件适配建议

        private String suggestions;  // 审核建议
        private double overallScore; // 综合评分
        private int actualDimensionCount = 0; // 实际使用的维度数量

        // 数据可用性标记
        private boolean hasUserNeedsData = false;        // 是否有用户需求数据
        private boolean hasPersonalFitnessData = false;  // 是否有个人适配性数据
        private boolean hasEconomicFitnessData = false;  // 是否有经济适配性数据
        private boolean hasGeographicData = false;       // 是否有地域偏好数据
        private boolean hasMajorPreferenceData = false;  // 是否有专业偏好数据
        private boolean hasPhysicalData = false;         // 是否有身体条件数据

        // 其他分析结果
        private AdmissionProbabilityEvaluator.AdmissionProbabilityResult admissionProbabilityResult;
        private boolean hasAdmissionProbabilityData = false; // 是否有录取概率数据

        // 志愿统计数据
        private VolunteerStatistics volunteerStatistics;

        // 专业组干净度分析结果
        private ProfessionalGroupCleanlinessResult professionalGroupCleanlinessResult;

        // Getters and Setters
        public String getGrade1() { return grade1; }
        public void setGrade1(String grade1) { this.grade1 = grade1; }
        
        public String getGrade2() { return grade2; }
        public void setGrade2(String grade2) { this.grade2 = grade2; }
        
        public String getGrade3() { return grade3; }
        public void setGrade3(String grade3) { this.grade3 = grade3; }
        
        public String getGrade4() { return grade4; }
        public void setGrade4(String grade4) { this.grade4 = grade4; }
        
        public String getGrade5() { return grade5; }
        public void setGrade5(String grade5) { this.grade5 = grade5; }
        
        public String getGrade6() { return grade6; }
        public void setGrade6(String grade6) { this.grade6 = grade6; }
        
        public String getGrade7() { return grade7; }
        public void setGrade7(String grade7) { this.grade7 = grade7; }
        
        public String getGrade8() { return grade8; }
        public void setGrade8(String grade8) { this.grade8 = grade8; }
        
        public String getGrade9() { return grade9; }
        public void setGrade9(String grade9) { this.grade9 = grade9; }
        
        public String getGrade10() { return grade10; }
        public void setGrade10(String grade10) { this.grade10 = grade10; }
        
        public String getGrade11() { return grade11; }
        public void setGrade11(String grade11) { this.grade11 = grade11; }
        
        public String getGrade12() { return grade12; }
        public void setGrade12(String grade12) { this.grade12 = grade12; }
        
        public String getGrade13() { return grade13; }
        public void setGrade13(String grade13) { this.grade13 = grade13; }
        
        public String getGrade14() { return grade14; }
        public void setGrade14(String grade14) { this.grade14 = grade14; }
        
        public String getGrade15() { return grade15; }
        public void setGrade15(String grade15) { this.grade15 = grade15; }
        
        public String getSuggestions() { return suggestions; }
        public void setSuggestions(String suggestions) { this.suggestions = suggestions; }
        
        public double getOverallScore() { return overallScore; }
        public void setOverallScore(double overallScore) { this.overallScore = overallScore; }
        
        public int getActualDimensionCount() { return actualDimensionCount; }
        public void setActualDimensionCount(int actualDimensionCount) { this.actualDimensionCount = actualDimensionCount; }
        
        // 数据可用性标记字段的 getter/setter
        public boolean isHasUserNeedsData() { return hasUserNeedsData; }
        public void setHasUserNeedsData(boolean hasUserNeedsData) { this.hasUserNeedsData = hasUserNeedsData; }
        
        public boolean isHasPersonalFitnessData() { return hasPersonalFitnessData; }
        public void setHasPersonalFitnessData(boolean hasPersonalFitnessData) { this.hasPersonalFitnessData = hasPersonalFitnessData; }
        
        public boolean isHasEconomicFitnessData() { return hasEconomicFitnessData; }
        public void setHasEconomicFitnessData(boolean hasEconomicFitnessData) { this.hasEconomicFitnessData = hasEconomicFitnessData; }
        
        public boolean isHasGeographicData() { return hasGeographicData; }
        public void setHasGeographicData(boolean hasGeographicData) { this.hasGeographicData = hasGeographicData; }
        
        public boolean isHasMajorPreferenceData() { return hasMajorPreferenceData; }
        public void setHasMajorPreferenceData(boolean hasMajorPreferenceData) { this.hasMajorPreferenceData = hasMajorPreferenceData; }
        
        public boolean isHasPhysicalData() { return hasPhysicalData; }
        public void setHasPhysicalData(boolean hasPhysicalData) { this.hasPhysicalData = hasPhysicalData; }
        
        public AdmissionProbabilityEvaluator.AdmissionProbabilityResult getAdmissionProbabilityResult() { return admissionProbabilityResult; }
        public void setAdmissionProbabilityResult(AdmissionProbabilityEvaluator.AdmissionProbabilityResult admissionProbabilityResult) { this.admissionProbabilityResult = admissionProbabilityResult; }
        
        public boolean isHasAdmissionProbabilityData() { return hasAdmissionProbabilityData; }
        public void setHasAdmissionProbabilityData(boolean hasAdmissionProbabilityData) { this.hasAdmissionProbabilityData = hasAdmissionProbabilityData; }

        public VolunteerStatistics getVolunteerStatistics() { return volunteerStatistics; }
        public void setVolunteerStatistics(VolunteerStatistics volunteerStatistics) { this.volunteerStatistics = volunteerStatistics; }

        public ProfessionalGroupCleanlinessResult getProfessionalGroupCleanlinessResult() {
            return professionalGroupCleanlinessResult;
        }

        public void setProfessionalGroupCleanlinessResult(ProfessionalGroupCleanlinessResult professionalGroupCleanlinessResult) {
            this.professionalGroupCleanlinessResult = professionalGroupCleanlinessResult;
        }
        
        // 建议字段的 getter/setter 方法
        public String getSuggestion1() { return suggestion1; }
        public void setSuggestion1(String suggestion1) { this.suggestion1 = suggestion1; }
        
        public String getSuggestion2() { return suggestion2; }
        public void setSuggestion2(String suggestion2) { this.suggestion2 = suggestion2; }
        
        public String getSuggestion3() { return suggestion3; }
        public void setSuggestion3(String suggestion3) { this.suggestion3 = suggestion3; }
        
        public String getSuggestion4() { return suggestion4; }
        public void setSuggestion4(String suggestion4) { this.suggestion4 = suggestion4; }
        
        public String getSuggestion5() { return suggestion5; }
        public void setSuggestion5(String suggestion5) { this.suggestion5 = suggestion5; }
        
        public String getSuggestion6() { return suggestion6; }
        public void setSuggestion6(String suggestion6) { this.suggestion6 = suggestion6; }
        
        public String getSuggestion7() { return suggestion7; }
        public void setSuggestion7(String suggestion7) { this.suggestion7 = suggestion7; }
        
        public String getSuggestion8() { return suggestion8; }
        public void setSuggestion8(String suggestion8) { this.suggestion8 = suggestion8; }
        
        public String getSuggestion9() { return suggestion9; }
        public void setSuggestion9(String suggestion9) { this.suggestion9 = suggestion9; }
        
        public String getSuggestion10() { return suggestion10; }
        public void setSuggestion10(String suggestion10) { this.suggestion10 = suggestion10; }
        
        public String getSuggestion11() { return suggestion11; }
        public void setSuggestion11(String suggestion11) { this.suggestion11 = suggestion11; }
        
        public String getSuggestion12() { return suggestion12; }
        public void setSuggestion12(String suggestion12) { this.suggestion12 = suggestion12; }
        
        public String getSuggestion13() { return suggestion13; }
        public void setSuggestion13(String suggestion13) { this.suggestion13 = suggestion13; }
        
        public String getSuggestion14() { return suggestion14; }
        public void setSuggestion14(String suggestion14) { this.suggestion14 = suggestion14; }
        
                public String getSuggestion15() { return suggestion15; }
        public void setSuggestion15(String suggestion15) { this.suggestion15 = suggestion15; }
        
        /**
         * 获取所有维度的建议数组（便于前台遍历使用）
         * @param includeUserNeeds 是否包含用户需求维度
         * @return 建议数组
         */
        public String[] getAllSuggestions(boolean includeUserNeeds) {
            if (includeUserNeeds && hasUserNeedsData) {
                return new String[]{
                    suggestion1, suggestion2, suggestion3, suggestion4, suggestion5,
                    suggestion6, suggestion7, suggestion8, suggestion9, suggestion10,
                    suggestion11, suggestion12, suggestion13, suggestion14, suggestion15
                };
            } else {
                return new String[]{
                    suggestion1, suggestion2, suggestion3, suggestion4, suggestion5,
                    suggestion6, suggestion7, suggestion8, suggestion9, suggestion10
                };
            }
        }
        
        /**
         * 根据维度编号获取建议
         * @param dimensionIndex 维度编号（1-15）
         * @return 对应维度的建议
         */
        public String getSuggestionByDimension(int dimensionIndex) {
            switch (dimensionIndex) {
                case 1: return suggestion1;
                case 2: return suggestion2;
                case 3: return suggestion3;
                case 4: return suggestion4;
                case 5: return suggestion5;
                case 6: return suggestion6;
                case 7: return suggestion7;
                case 8: return suggestion8;
                case 9: return suggestion9;
                case 10: return suggestion10;
                case 11: return suggestion11;
                case 12: return suggestion12;
                case 13: return suggestion13;
                case 14: return suggestion14;
                case 15: return suggestion15;
                default: return "无效的维度编号";
            }
        }
      }

    /**
     * 🎯 志愿填报统计数据类
     * 避免页面重复计算，统一在算法层面计算各种统计指标
     */
    public static class VolunteerStatistics {
        // 志愿策略分布
        private int chongCount;        // 冲击志愿数
        private int wenCount;          // 稳妥志愿数  
        private int baoCount;          // 保底志愿数
        private int totalVolunteers;   // 总志愿数

        // 院校类型统计
        private int topUniversityCount;    // 名校数量（985/211/双一流等）
        private int totalUniversityCount;  // 总院校数
        private int provinceCount;         // 涉及省份数

        // 录取概率统计（简化版本，如果没有使用AdmissionProbabilityEvaluator）
        private double averageAdmissionProbability;  // 平均录取概率

        // 志愿类型映射
        private Map<Integer, String> volunteerTypeMap;  // 志愿序号 -> 志愿类型（冲击/稳妥/保底）
        private Map<Integer, Double> volunteerScoreMap; // 志愿序号 -> 平均分数

        public VolunteerStatistics() {
            this.volunteerTypeMap = new HashMap<>();
            this.volunteerScoreMap = new HashMap<>();
        }

        // Getters and Setters
        public int getChongCount() { return chongCount; }
        public void setChongCount(int chongCount) { this.chongCount = chongCount; }

        public int getWenCount() { return wenCount; }
        public void setWenCount(int wenCount) { this.wenCount = wenCount; }

        public int getBaoCount() { return baoCount; }
        public void setBaoCount(int baoCount) { this.baoCount = baoCount; }

        public int getTotalVolunteers() { return totalVolunteers; }
        public void setTotalVolunteers(int totalVolunteers) { this.totalVolunteers = totalVolunteers; }

        public int getTopUniversityCount() { return topUniversityCount; }
        public void setTopUniversityCount(int topUniversityCount) { this.topUniversityCount = topUniversityCount; }

        public int getTotalUniversityCount() { return totalUniversityCount; }
        public void setTotalUniversityCount(int totalUniversityCount) { this.totalUniversityCount = totalUniversityCount; }

        public int getProvinceCount() { return provinceCount; }
        public void setProvinceCount(int provinceCount) { this.provinceCount = provinceCount; }

        public double getAverageAdmissionProbability() { return averageAdmissionProbability; }
        public void setAverageAdmissionProbability(double averageAdmissionProbability) { this.averageAdmissionProbability = averageAdmissionProbability; }

        public Map<Integer, String> getVolunteerTypeMap() { return volunteerTypeMap; }
        public void setVolunteerTypeMap(Map<Integer, String> volunteerTypeMap) { this.volunteerTypeMap = volunteerTypeMap; }

        public Map<Integer, Double> getVolunteerScoreMap() { return volunteerScoreMap; }
        public void setVolunteerScoreMap(Map<Integer, Double> volunteerScoreMap) { this.volunteerScoreMap = volunteerScoreMap; }

        /**
         * 获取指定志愿序号的志愿类型
         */
        public String getVolunteerType(int volunteerNumber) {
            return volunteerTypeMap.getOrDefault(volunteerNumber, "未知");
        }

        /**
         * 获取指定志愿序号的平均分数
         */
        public Double getVolunteerScore(int volunteerNumber) {
            return volunteerScoreMap.get(volunteerNumber);
        }

        /**
         * 获取冲稳保分布比例
         */
        public double getChongRate() {
            return totalVolunteers > 0 ? (double) chongCount / totalVolunteers : 0.0;
        }

        public double getWenRate() {
            return totalVolunteers > 0 ? (double) wenCount / totalVolunteers : 0.0;
        }

        public double getBaoRate() {
            return totalVolunteers > 0 ? (double) baoCount / totalVolunteers : 0.0;
        }
    }
    
    
    /**
     * 专业组信息类
     */
    public static class ProfessionalGroupInfo {
        private int totalMajors;        // 专业组内总专业数
        private int totalPlan;          // 总招生计划
        private double avgScore;        // 平均分数
        private Set<String> majorNames; // 专业名称集合
        private Set<String> majorCategories; // 专业门类集合
        private int categoryCount; // 专业门类数量
        private String cleanlinessLevel; // 专业组干净度
        
        public int getTotalMajors() { return totalMajors; }
        public void setTotalMajors(int totalMajors) { this.totalMajors = totalMajors; }
        
        public int getTotalPlan() { return totalPlan; }
        public void setTotalPlan(int totalPlan) { this.totalPlan = totalPlan; }
        
        public double getAvgScore() { return avgScore; }
        public void setAvgScore(double avgScore) { this.avgScore = avgScore; }
        
        public Set<String> getMajorNames() { return majorNames; }
        public void setMajorNames(Set<String> majorNames) { this.majorNames = majorNames; }
        
        public Set<String> getMajorCategories() { return majorCategories; }
        public void setMajorCategories(Set<String> majorCategories) { this.majorCategories = majorCategories; }
        
        public int getCategoryCount() { return categoryCount; }
        public void setCategoryCount(int categoryCount) { this.categoryCount = categoryCount; }
        
        public String getCleanlinessLevel() { return cleanlinessLevel; }
        public void setCleanlinessLevel(String cleanlinessLevel) { this.cleanlinessLevel = cleanlinessLevel; }
    }
    
    /**
     * 历年趋势分析类
     */
    public static class HistoricalTrend {
        private double avgScoreA;    // A年平均分
        private double avgScoreB;    // B年平均分  
        private double avgScoreC;    // C年平均分
        private int planA;           // A年招生计划
        private int planB;           // B年招生计划
        private int planC;           // C年招生计划
        private String scoreTrend;   // 分数趋势：上升/下降/稳定
        private String planTrend;    // 计划趋势：增加/减少/稳定
        
        public void calculateTrends() {
            // 计算分数趋势
            if (avgScoreC > 0 && avgScoreB > 0) {
                double scoreDiff = avgScoreC - avgScoreB;
                if (scoreDiff > SCORE_TREND_THRESHOLD) {
                    scoreTrend = "上升";
                } else if (scoreDiff < -SCORE_TREND_THRESHOLD) {
                    scoreTrend = "下降";
                } else {
                    scoreTrend = "稳定";
                }
            } else {
                scoreTrend = "数据不足";
            }
            
            // 计算计划趋势
            if (planC > 0 && planB > 0) {
                double planDiff = (double)(planC - planB) / planB;
                if (planDiff > PLAN_CHANGE_RATE_THRESHOLD) {
                    planTrend = "增加";
                } else if (planDiff < -PLAN_CHANGE_RATE_THRESHOLD) {
                    planTrend = "减少";
                } else {
                    planTrend = "稳定";
                }
            } else {
                planTrend = "数据不足";
            }
        }
        
        // Getters and Setters
        public double getAvgScoreA() { return avgScoreA; }
        public void setAvgScoreA(double avgScoreA) { this.avgScoreA = avgScoreA; }
        
        public double getAvgScoreB() { return avgScoreB; }
        public void setAvgScoreB(double avgScoreB) { this.avgScoreB = avgScoreB; }
        
        public double getAvgScoreC() { return avgScoreC; }
        public void setAvgScoreC(double avgScoreC) { this.avgScoreC = avgScoreC; }
        
        public int getPlanA() { return planA; }
        public void setPlanA(int planA) { this.planA = planA; }
        
        public int getPlanB() { return planB; }
        public void setPlanB(int planB) { this.planB = planB; }
        
        public int getPlanC() { return planC; }
        public void setPlanC(int planC) { this.planC = planC; }
        
        public String getScoreTrend() { return scoreTrend; }
        public void setScoreTrend(String scoreTrend) { this.scoreTrend = scoreTrend; }
        
        public String getPlanTrend() { return planTrend; }
        public void setPlanTrend(String planTrend) { this.planTrend = planTrend; }
    }
    
    /**
     * 专业组干净度统计结果类
     * 提供类型安全API
     */
    public static class ProfessionalGroupCleanlinessResult {
        private Map<String, String> groupCleanlinessMap;
        private Map<String, Integer> groupCategoryCountMap;
        private Map<String, Set<String>> groupCategoriesMap;
        // 新增：志愿序号到专业组键的映射
        private Map<Integer, String> volunteerToGroupKeyMap;
        private int cleanCount;
        private int generalCount;
        private int dirtyCount;
        private int totalGroups;
        private double cleanRate;
        private double generalRate;
        private double dirtyRate;

        // 构造函数
        public ProfessionalGroupCleanlinessResult() {
            this.groupCleanlinessMap = new HashMap<>();
            this.groupCategoryCountMap = new HashMap<>();
            this.groupCategoriesMap = new HashMap<>();
            this.volunteerToGroupKeyMap = new HashMap<>();
        }

        public String getCleanlinessLevel(int seqNo) {
            String groupKey = volunteerToGroupKeyMap.get(seqNo);
            return groupKey != null ? groupCleanlinessMap.get(groupKey) : null;
        }
        
        public String getCleanlinessLevel(String groupKey) {
            return groupCleanlinessMap.get(groupKey);
        }
        
        // 便捷方法：获取指定专业组的门类数量
        public Integer getCategoryCount(int seqNo) {
            String groupKey = volunteerToGroupKeyMap.get(seqNo);
            return groupKey != null ? groupCategoryCountMap.get(groupKey) : null;
        }
        
        public Integer getCategoryCount(String groupKey) {
            return groupCategoryCountMap.get(groupKey);
        }
        
        // 便捷方法：获取指定专业组的门类集合
        public Set<String> getCategories(int seqNo) {
            String groupKey = volunteerToGroupKeyMap.get(seqNo);
            return groupKey != null ? groupCategoriesMap.get(groupKey) : null;
        }
        
        public Set<String> getCategories(String groupKey) {
            return groupCategoriesMap.get(groupKey);
        }
        
        // 便捷方法：判断专业组是否干净
        public boolean isClean(int seqNo) {
            return "干净".equals(getCleanlinessLevel(seqNo));
        }
        
        public boolean isClean(String groupKey) {
            return "干净".equals(getCleanlinessLevel(groupKey));
        }
        
        // 便捷方法：格式化门类信息显示
        public String formatCategoriesDisplay(int seqNo) {
            Set<String> categories = getCategories(seqNo);
            if (categories == null || categories.isEmpty()) {
                return "未知";
            }
            if (categories.size() == 1) {
                return categories.iterator().next();
            }
            return String.join("、", categories) + " 等" + categories.size() + "类";
        }
        
        public String formatCategoriesDisplay(String groupKey) {
            Set<String> categories = getCategories(groupKey);
            if (categories == null || categories.isEmpty()) {
                return "未知";
            }
            if (categories.size() == 1) {
                return categories.iterator().next();
            }
            return String.join("、", categories) + " 等" + categories.size() + "类";
        }

        // ... existing code ...

        public Map<Integer, String> getVolunteerToGroupKeyMap() { return volunteerToGroupKeyMap; }
        public void setVolunteerToGroupKeyMap(Map<Integer, String> volunteerToGroupKeyMap) { this.volunteerToGroupKeyMap = volunteerToGroupKeyMap; }

        // ... existing code ...

        // Getters and Setters
        public Map<String, String> getGroupCleanlinessMap() { return groupCleanlinessMap; }
        public void setGroupCleanlinessMap(Map<String, String> groupCleanlinessMap) { this.groupCleanlinessMap = groupCleanlinessMap; }
        
        public Map<String, Integer> getGroupCategoryCountMap() { return groupCategoryCountMap; }
        public void setGroupCategoryCountMap(Map<String, Integer> groupCategoryCountMap) { this.groupCategoryCountMap = groupCategoryCountMap; }
        
        public Map<String, Set<String>> getGroupCategoriesMap() { return groupCategoriesMap; }
        public void setGroupCategoriesMap(Map<String, Set<String>> groupCategoriesMap) { this.groupCategoriesMap = groupCategoriesMap; }
        
        public int getCleanCount() { return cleanCount; }
        public void setCleanCount(int cleanCount) { this.cleanCount = cleanCount; }
        
        public int getGeneralCount() { return generalCount; }
        public void setGeneralCount(int generalCount) { this.generalCount = generalCount; }
        
        public int getDirtyCount() { return dirtyCount; }
        public void setDirtyCount(int dirtyCount) { this.dirtyCount = dirtyCount; }
        
        public int getTotalGroups() { return totalGroups; }
        public void setTotalGroups(int totalGroups) { this.totalGroups = totalGroups; }
        
        public double getCleanRate() { return cleanRate; }
        public void setCleanRate(double cleanRate) { this.cleanRate = cleanRate; }
        
        public double getGeneralRate() { return generalRate; }
        public void setGeneralRate(double generalRate) { this.generalRate = generalRate; }
        
        public double getDirtyRate() { return dirtyRate; }
        public void setDirtyRate(double dirtyRate) { this.dirtyRate = dirtyRate; }
        
        // 便捷方法：获取总体干净度评价
        public String getOverallCleanlinessLevel() {
            if (cleanRate >= 0.8) return "优秀";
            if (cleanRate >= 0.6) return "良好";
            if (cleanRate >= 0.4) return "一般";
            return "需要优化";
        }
        
        // 便捷方法：获取总体干净度建议
        public String getCleanlinessAdvice() {
            if (cleanRate >= 0.8) {
                return "专业组干净度很高，专业选择集中度合理，有利于专业发展。";
            } else if (cleanRate >= 0.6) {
                return "专业组干净度较好，建议适当关注专业门类的一致性。";
            } else if (cleanRate >= 0.4) {
                return "部分专业组门类较为分散，建议优化专业选择的集中度。";
            } else {
                return "多数专业组门类分散，建议重新规划专业选择策略，提高专业集中度。";
            }
        }
    }
    
    /**
     * 计算专业组干净度统计信息并设置到 FormReviewResult
     * 提供类型安全和优雅的API访问
     * @param result FormReviewResult 对象，用于设置计算结果
     * @param context FormReviewContext 对象，包含评估所需的数据
     */
    private static void calculateProfessionalGroupCleanliness(FormReviewResult result, FormReviewContext context) {
        Tools.println("[calculateProfessionalGroupCleanliness] 开始计算专业组干净度统计");
        ProfessionalGroupCleanlinessResult cleanlinessResult = new ProfessionalGroupCleanlinessResult();
        
        try {
            // 按专业组分组
            List<SuperForm> formList = context.getSuperFormMain().getSuperFormList();
            if (formList == null || formList.isEmpty()) {
                Tools.println("[calculateProfessionalGroupCleanliness] 表单列表为空，跳过专业组干净度统计");
                result.setProfessionalGroupCleanlinessResult(cleanlinessResult);
                return;
            }
            
            Map<String, List<SuperForm>> collegeGroups = FormReviewUtils.groupByCollege(formList);
            
            // 专业组干净度映射
            Map<String, String> groupCleanlinessMap = new HashMap<>();
            Map<String, Integer> groupCategoryCountMap = new HashMap<>();
            Map<String, Set<String>> groupCategoriesMap = new HashMap<>();
            // 志愿序号到专业组键的映射
            Map<Integer, String> volunteerToGroupKeyMap = new HashMap<>();
            
            // 建立志愿序号到专业组键的映射（基于表单中志愿的顺序）
            for (int i = 0; i < formList.size(); i++) {
                SuperForm form = formList.get(i);
                String groupKey = Tools.trim(form.getYxdm()) + "|" + Tools.trim(form.getZyz());
                int volunteerNumber = i + 1; // 1-based 志愿序号
                volunteerToGroupKeyMap.put(volunteerNumber, groupKey);
            }
            
            // 干净度统计
            int cleanCount = 0;
            int generalCount = 0;
            int dirtyCount = 0;
            int totalGroups = 0;
            
            for (Map.Entry<String, List<SuperForm>> entry : collegeGroups.entrySet()) {
                List<SuperForm> group = entry.getValue();
                if (group.isEmpty()) continue;
                
                totalGroups++;
                SuperForm firstForm = group.get(0);
                String yxdm = firstForm.getYxdm();
                String zyz = firstForm.getZyz();
                String groupKey = entry.getKey();
                
                // 获取专业组信息
                ProfessionalGroupInfo groupInfo = context.getGroupInfo(yxdm, zyz);
                String cleanlinessLevel;
                int categoryCount;
                Set<String> categories;
                
                if (groupInfo != null) {
                    // 使用详细计算结果
                    cleanlinessLevel = groupInfo.getCleanlinessLevel();
                    categoryCount = groupInfo.getCategoryCount();
                    categories = groupInfo.getMajorCategories();
                } else {
                    // 使用简化计算
                    categories = new HashSet<>();
                    for (SuperForm form : group) {
                        ZyzdBaseMajor catgTwo = ZyzdCache.getZymlByZymc(context.getSuperFormMain().getPc_code(), form.getZymc_org());
                        if (catgTwo != null && !Tools.isEmpty(catgTwo.getM_catg_two())) {
                            categories.add(catgTwo.getM_catg_two());
                        }
                    }
                    categoryCount = categories.size();
                    cleanlinessLevel = categoryCount == 1 ? "干净" : (categoryCount <= 3 ? "一般" : "不干净");
                }
                
                // 存储结果
                groupCleanlinessMap.put(groupKey, cleanlinessLevel);
                groupCategoryCountMap.put(groupKey, categoryCount);
                groupCategoriesMap.put(groupKey, categories);
                
                // 统计计数
                switch (cleanlinessLevel) {
                    case "干净":
                        cleanCount++;
                        break;
                    case "一般":
                        generalCount++;
                        break;
                    case "不干净":
                        dirtyCount++;
                        break;
                }
            }
            
            // 计算比例
            double cleanRate = totalGroups > 0 ? (double) cleanCount / totalGroups : 0;
            double generalRate = totalGroups > 0 ? (double) generalCount / totalGroups : 0;
            double dirtyRate = totalGroups > 0 ? (double) dirtyCount / totalGroups : 0;
            
            // 设置结果
            // 这个映射表存储了每个专业组键对应的干净度级别（例如："干净", "一般", "不干净"）。
            cleanlinessResult.setGroupCleanlinessMap(groupCleanlinessMap);
            // 这个映射表存储了每个专业组键对应的专业类别数量。
            cleanlinessResult.setGroupCategoryCountMap(groupCategoryCountMap);
            // 这个映射表存储了每个专业组键对应的所有专业类别的名称集合。
            cleanlinessResult.setGroupCategoriesMap(groupCategoriesMap);
            // 这个映射表建立志愿序号到专业组键的对应关系，用于JSP页面根据志愿序号获取干净度信息。
            cleanlinessResult.setVolunteerToGroupKeyMap(volunteerToGroupKeyMap);
            // 表示志愿表中被评为"干净"的专业组总数。
            cleanlinessResult.setCleanCount(cleanCount);
            // 表示志愿表中被评为"一般"的专业组总数。
            cleanlinessResult.setGeneralCount(generalCount);
            // 表示志愿表中被评为"不干净"的专业组总数。
            cleanlinessResult.setDirtyCount(dirtyCount);
            // 表示志愿表中实际包含的专业组总数量。
            cleanlinessResult.setTotalGroups(totalGroups);
            // 将计算得到的"干净"专业组的比例（干净专业组数 / 总专业组数）设置到结果对象中，用百分比形式表示干净度。
            cleanlinessResult.setCleanRate(cleanRate);
            // 将计算得到的"一般"专业组的比例（一般专业组数 / 总专业组数）设置到结果对象中。
            cleanlinessResult.setGeneralRate(generalRate);
            // 将计算得到的"不干净"专业组的比例（不干净专业组数 / 总专业组数）设置到结果对象中。
            cleanlinessResult.setDirtyRate(dirtyRate);
            
            
            Tools.println("[calculateProfessionalGroupCleanliness] 调试信息:");
            Tools.println("  Cleanliness Map: " + cleanlinessResult.getGroupCleanlinessMap());
            Tools.println("  Category Count Map: " + cleanlinessResult.getGroupCategoryCountMap());
            Tools.println("  Categories Map: " + cleanlinessResult.getGroupCategoriesMap());
            Tools.println("  Volunteer To Group Key Map: " + cleanlinessResult.getVolunteerToGroupKeyMap());
            Tools.println("  Clean Count: " + cleanlinessResult.getCleanCount());
            Tools.println("  General Count: " + cleanlinessResult.getGeneralCount());
            Tools.println("  Dirty Count: " + cleanlinessResult.getDirtyCount());
            Tools.println("  Total Groups: " + cleanlinessResult.getTotalGroups());
            Tools.println("  Clean Rate: " + String.format("%.2f", cleanlinessResult.getCleanRate()));
            Tools.println("  General Rate: " + String.format("%.2f", cleanlinessResult.getGeneralRate()));
            Tools.println("  Dirty Rate: " + String.format("%.2f", cleanlinessResult.getDirtyRate()));
            
            result.setProfessionalGroupCleanlinessResult(cleanlinessResult);
            Tools.println("[calculateProfessionalGroupCleanliness] 专业组干净度统计完成.");
            
        } catch (Exception e) {
            System.err.println("计算专业组干净度统计失败: " + e.getMessage());
            e.printStackTrace();
            result.setProfessionalGroupCleanlinessResult(new ProfessionalGroupCleanlinessResult()); // 设置一个空结果以避免 NPE
        }
    }

    /**
     * 建议生成工具类
     */
    private static class DimensionSuggestionGenerator {
        private static final Random random = new Random();
        
        /**
         * 根据维度和等级生成建议
         */
        public static String generateSuggestion(int dimension, String grade) {
            String[][] suggestions = getSuggestionsForDimension(dimension);
            if (suggestions == null || suggestions.length == 0) {
                return "暂无建议";
            }
            
            int gradeIndex = getGradeIndex(grade);
            if (gradeIndex >= 0 && gradeIndex < suggestions.length && suggestions[gradeIndex].length > 0) {
                return suggestions[gradeIndex][random.nextInt(suggestions[gradeIndex].length)];
            }
            
            return "暂无建议";
        }
        
        /**
         * 获取等级对应的索引
         */
        private static int getGradeIndex(String grade) {
            switch (grade) {
                case GRADE_A: return 0;
                case GRADE_B: return 1;
                case GRADE_C: return 2;
                case GRADE_D: return 3;
                default: return -1;
            }
        }
        
        /**
         * 获取指定维度的建议模板
         */
        private static String[][] getSuggestionsForDimension(int dimension) {
            switch (dimension) {
                case 1: // 分段定位 - 基于志愿分数差值评估
                    return new String[][]{
                        {"志愿分数跨度优秀（≥30分），分段定位科学合理，冲稳保层次分明。", "志愿分数梯度设置得当，覆盖面广，录取机会充足。", "分数差值充分，志愿填报策略科学，风险控制良好。"},
                        {"志愿分数跨度良好（20-30分），分段定位基本合理，可适当优化。", "分数梯度设置不错，建议微调个别志愿的分数层次。", "分数差值较好，志愿策略可进一步完善。"},
                        {"志愿分数跨度一般（15-20分），建议扩大分数覆盖范围，增强梯度层次。", "分数梯度偏窄，需要增加不同分数段的志愿选择。", "分数差值不够充分，建议优化志愿分数分布。"},
                        {"志愿分数跨度过小（<15分），分段定位不合理，急需扩大分数覆盖面。", "分数梯度严重不足，存在较大录取风险。", "分数差值太小，志愿填报策略需要重新规划。"}
                    };
                case 2: // 院校梯度 - 基于院校间分数梯度合理性
                    return new String[][]{
                        {"院校间分数梯度科学合理，梯度递减明显，录取概率较高。", "院校搭配优秀，分数差距适中，志愿填报策略得当。", "院校梯度设置完美，层次分明，风险控制良好。"},
                        {"院校间分数梯度基本合理，大部分院校搭配恰当，可适当调整。", "院校梯度设置良好，建议微调个别院校的排列顺序。", "院校分数差距尚可，可进一步优化梯度分布。"},
                        {"院校间分数梯度存在问题，部分院校分数跨度过大或过小。", "院校梯度设置不够合理，需要调整院校间的分数差距。", "院校搭配欠佳，建议重新规划梯度结构。"},
                        {"院校间分数梯度混乱，梯度设置严重不合理，存在重大录取风险。", "院校搭配问题严重，分数差距不科学，急需全面调整。", "院校梯度完全不合理，志愿填报策略需要重新制定。"}
                    };
                case 3: // 院校完整度 - 基于填报志愿数量
                    return new String[][]{
                        {"志愿填报数量充分（≥38个），充分利用了志愿额度，选择面广。", "院校选择完整度优秀，志愿利用率高，录取机会充足。", "志愿数量配置理想，覆盖面广，选择余地大。"},
                        {"志愿填报数量较好（31-37个），基本充分利用志愿额度。", "院校选择较为完整，建议适当补充志愿数量。", "志愿数量良好，可考虑再增加几个备选院校。"},
                        {"志愿填报数量一般（26-30个），建议增加志愿数量，扩大选择范围。", "院校选择不够充分，需要补充更多志愿选择。", "志愿数量偏少，建议充分利用45个志愿额度。"},
                        {"志愿填报数量严重不足（<26个），存在志愿浪费，急需补充院校。", "院校选择过少，可能导致录取机会不足。", "志愿数量很少，强烈建议大幅增加志愿选择。"}
                    };
                case 4: // 专业完整度 - 基于专业填报完整度
                    return new String[][]{
                        {"各院校专业填报完整度优秀，专业选择充分，录取概率较高。", "专业配置科学，每个院校专业组都有充足的专业选择。", "专业填报策略完善，专业覆盖面广，录取保障充分。"},
                        {"大部分院校专业填报较完整，个别院校可增加专业选择。", "专业配置良好，建议部分院校补充专业选项。", "专业填报比较完整，可考虑优化专业结构。"},
                        {"部分院校专业填报不完整，建议每个院校专业组填报4-6个专业。", "专业选择不够充分，需要增加专业填报数量。", "专业完整度一般，建议补充专业选择。"},
                        {"多数院校专业填报严重不足，急需补充专业选择。", "专业配置很不完整，存在较大录取风险。", "专业填报问题严重，需要全面优化专业选择。"}
                    };
                case 5: // 院校梯度顺序 - 基于冲稳保比例和排列顺序
                    return new String[][]{
                        {"冲稳保比例配置科学（冲击10-20个，稳妥10-20个，保底10-20个），院校排列顺序合理。", "院校梯度顺序优秀，冲稳保搭配均衡，风险控制良好。", "志愿排列策略完美，层次分明，录取概率较高。"},
                        {"冲稳保比例基本合理，院校顺序大体正确，可适当调整优化。", "院校梯度顺序良好，建议微调个别院校位置。", "志愿排列较为合理，可考虑优化部分顺序。"},
                        {"冲稳保比例不够合理，或院校排列顺序存在问题。", "院校梯度顺序欠佳，需要重新规划冲稳保结构。", "志愿顺序安排不当，建议调整院校排列策略。"},
                        {"冲稳保比例严重失衡，院校顺序混乱，存在重大录取风险。", "院校梯度顺序很不合理，急需系统性调整。", "志愿排列策略有严重问题，需要全面重新规划。"}
                    };
                case 6: // 专业梯度顺序 - 基于同一院校内专业排列
                    return new String[][]{
                        {"同一院校内专业排列科学，专业梯度顺序合理，录取策略得当。", "专业梯度顺序优秀，院校内专业搭配均衡。", "各院校专业排列顺序完美，专业选择策略科学。"},
                        {"多数院校专业排列合理，个别院校可优化专业顺序。", "专业梯度顺序良好，建议微调部分专业位置。", "大部分院校专业顺序恰当，少数需要调整。"},
                        {"同一院校内专业排列不够合理，建议按专业录取分数由高到低排列。", "部分院校专业梯度存在问题，需要调整专业顺序。", "专业排列顺序欠佳，建议重新规划专业策略。"},
                        {"院校内专业排列混乱，专业梯度严重不合理，需要全面调整。", "专业顺序安排很不科学，存在较大录取风险。", "多数院校专业排列有问题，急需重新规划。"}
                    };
                case 7: // 专业服从调剂 - 基于服从调剂比例
                    return new String[][]{
                        {"专业服从调剂选择优秀（≥99%），大大降低了退档风险。", "调剂策略配置完美，为录取提供了充分保障。", "服从调剂比例很高，录取安全性得到充分保障。"},
                        {"多数院校选择了专业服从调剂（70-99%），录取保障良好。", "专业调剂配置较好，建议个别院校考虑调剂选择。", "服从调剂比例不错，可适当增加调剂院校。"},
                        {"部分院校选择了专业服从调剂（50-70%），建议增加调剂选择。", "专业调剂配置不够充分，需要增加调剂院校数量。", "服从调剂比例偏低，可能存在一定录取风险。"},
                        {"大多数院校未选择专业服从调剂（<50%），退档风险很高。", "专业调剂配置严重不足，存在重大录取风险。", "服从调剂选择很少，可能导致大量志愿无效。"}
                    };
                case 8: // 保底院校风险 - 基于保底院校安全性
                    return new String[][]{
                        {"保底院校选择科学（≥80%安全），录取安全性很高。", "保底策略配置优秀，为录取提供了可靠保障。", "保底院校安全性充分，录取风险很低。"},
                        {"保底院校选择较好（60-80%安全），录取安全性良好。", "保底策略基本合理，录取风险较低。", "保底院校安全性不错，可适当优化选择。"},
                        {"保底院校安全性一般（50-60%），建议选择录取把握更大的院校。", "保底策略存在风险，需要增加安全性较高的院校。", "保底院校选择不够安全，可能存在录取风险。"},
                        {"保底院校安全性严重不足（<50%），存在重大录取风险。", "保底策略有严重问题，可能导致无学可上。", "保底院校选择很不安全，录取风险很高。"}
                    };
                case 9: // 区域优势 - 基于地域分布多样性
                    return new String[][]{
                        {"院校地域分布优秀，覆盖省份多（≥5个），本省外省比例合理。", "地域配置科学，既有本地优势又有外地机会。", "区域分布均衡，为就业发展提供了多元化选择。"},
                        {"院校地域分布较好，省份覆盖面不错，可适当调整地区比例。", "地域配置良好，建议优化个别地区的院校选择。", "区域分布不错，可考虑增加热门地区院校。"},
                        {"院校地域分布不够均衡，建议增加不同地区的院校选择。", "地域配置存在偏向，需要优化地区分布结构。", "区域分布单一，建议扩大地域选择范围。"},
                        {"院校地域分布很不合理，过于集中某些地区，缺乏多样性。", "地域配置严重失衡，需要重新规划地区分布。", "区域分布问题严重，可能影响录取和发展机会。"}
                    };
                case 10: // 专业集中度 - 基于专业类别多样性（注意：类别越少越好）
                    return new String[][]{
                        {"专业选择高度集中（≤3个类别），专业方向明确，有利于深度发展。", "专业类别集中度优秀，专业定位清晰，发展方向明确。", "专业选择专一性强，有助于在特定领域深入发展。"},
                        {"专业选择较为集中（4-5个类别），专业方向相对明确。", "专业类别集中度良好，专业定位比较清晰。", "专业选择有一定集中度，发展方向较为明确。"},
                        {"专业选择集中度一般（6-8个类别），专业方向不够明确。", "专业类别分布较散，建议聚焦核心专业方向。", "专业选择缺乏集中度，可能影响专业发展深度。"},
                        {"专业选择过于分散（>8个类别），缺乏明确的专业方向。", "专业类别覆盖过广，专业定位不清晰。", "专业选择严重分散，建议聚焦特定专业领域。"}
                    };
                case 11: // 个人适配性 - 基于个人特征匹配
                    return new String[][]{
                        {"志愿填报与个人特征高度匹配，专业选择充分考虑了性别、民族等因素。", "个人适配性优秀，专业选择符合个人条件和特点。", "志愿与个人特征完美匹配，专业选择科学合理。"},
                        {"志愿填报与个人特征匹配度良好，大部分专业适合个人条件。", "个人适配性较好，建议关注个别专业的特殊要求。", "志愿与个人特征基本匹配，可进一步优化。"},
                        {"部分专业可能与个人特征不太匹配，建议重新检查相关限制条件。", "个人适配性一般，需要优化专业选择。", "志愿与个人特征匹配度有待提高。"},
                        {"志愿填报与个人特征存在明显冲突，请及时调整相关专业选择。", "个人适配性较差，急需重新规划专业志愿。", "志愿与个人特征严重不匹配，需要全面调整。"}
                    };
                case 12: // 经济适配性 - 基于家庭经济条件匹配
                    return new String[][]{
                        {"志愿填报与家庭经济条件高度匹配，学费负担合理可承受。", "经济适配性优秀，院校选择充分考虑了家庭经济状况。", "志愿与经济条件完美匹配，费用规划科学合理。"},
                        {"志愿填报与家庭经济条件匹配度良好，大部分院校学费可承受。", "经济适配性较好，建议关注个别高费用院校。", "志愿与经济条件基本匹配，可进一步优化。"},
                        {"部分院校学费可能超出预算，建议核实相关费用信息。", "经济适配性一般，需要平衡院校质量与费用。", "志愿与经济条件匹配度有待提高。"},
                        {"当前志愿表中多数院校学费超出预算范围，急需调整。", "经济适配性较差，急需重新规划院校选择。", "志愿与经济条件严重不匹配，需要全面调整。"}
                    };
                case 13: // 地域偏好匹配 - 基于地域偏好匹配度
                    return new String[][]{
                        {"志愿填报与地域偏好高度匹配，院校地理位置完全符合期望。", "地域偏好匹配度优秀，院校选择充分考虑了地域倾向。", "志愿与地域偏好完美匹配，地理位置选择科学合理。"},
                        {"志愿填报与地域偏好匹配度良好，大部分院校位于意向地区。", "地域偏好匹配较好，建议关注个别非意向地区院校。", "志愿与地域偏好基本匹配，可进一步优化。"},
                        {"当前志愿表与地域偏好匹配度一般，建议适当增加意向地区院校。", "地域偏好匹配一般，需要平衡地域与院校质量。", "志愿与地域偏好匹配度有待提高。"},
                        {"志愿填报与地域偏好差异较大，建议重新调整地域分布。", "地域偏好匹配较差，急需重新规划地域选择。", "志愿与地域偏好严重不匹配，需要全面调整。"}
                    };
                case 14: // 专业偏好匹配 - 基于专业兴趣匹配度
                    return new String[][]{
                        {"志愿填报与专业偏好高度匹配，专业选择完全符合兴趣方向。", "专业偏好匹配度优秀，专业选择充分考虑了兴趣爱好。", "志愿与专业偏好完美匹配，专业方向选择科学合理。"},
                        {"志愿填报与专业偏好匹配度良好，大部分专业符合兴趣方向。", "专业偏好匹配较好，建议关注专业发展前景。", "志愿与专业偏好基本匹配，可进一步优化。"},
                        {"当前专业选择与兴趣偏好匹配度一般，建议适当调整。", "专业偏好匹配一般，需要平衡兴趣与就业前景。", "志愿与专业偏好匹配度有待提高。"},
                        {"专业选择与兴趣偏好差异较大，强烈建议重新规划专业志愿。", "专业偏好匹配较差，急需重新考虑专业方向。", "志愿与专业偏好严重不匹配，需要全面调整。"}
                    };
                case 15: // 身体条件适配 - 基于身体条件匹配度
                    return new String[][]{
                        {"志愿填报与身体条件高度匹配，专业选择充分考虑了身体要求。", "身体条件适配性优秀，专业选择完全符合身体状况。", "志愿与身体条件完美匹配，专业选择科学合理。"},
                        {"志愿填报与身体条件匹配度良好，大部分专业适合身体状况。", "身体条件适配较好，建议关注个别专业的身体要求。", "志愿与身体条件基本匹配，可进一步优化。"},
                        {"部分专业可能对身体条件有特殊要求，建议仔细核实相关限制。", "身体条件适配一般，需要关注专业的身体要求。", "志愿与身体条件匹配度有待提高。"},
                        {"存在多个专业与身体条件不符，请及时调整专业选择。", "身体条件适配较差，急需重新规划专业选择。", "志愿与身体条件严重不匹配，需要全面调整。"}
                    };
                default:
                    return new String[][]{{"暂无建议"}};
            }
        }
    }

}
