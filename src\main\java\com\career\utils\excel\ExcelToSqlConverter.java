package com.career.utils.excel;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.career.utils.Tools;

import cn.hutool.core.lang.UUID;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

public class ExcelToSqlConverter {
	
	private static final Pattern SQL_SPECIAL_CHARS = Pattern.compile(
	        "[\u0000-\u001F\u0080-\u009F\u00A0-\u00BF\u2000-\u206F\u20A0-\u20CF\u2100-\u214F\u2190-\u21FF\u2200-\u22FF" +
	        "\u2300-\u23FF\u2500-\u257F\u25A0-\u25FF\u2600-\u26FF\u2700-\u27BF\u27C0-\u27EF\u27F0-\u27FF\u2800-\u28FF" +
	        "\u2900-\u297F\u2980-\u29FF\u2A00-\u2AFF\u2B00-\u2BFF\u2E00-\u2E7F\u3000-\u303F]");

    public static void generateInsertStatements(String path, String excelFile) {
    	StringBuffer SQL = new StringBuffer();
        try (FileInputStream fis = new FileInputStream(new File(path, excelFile));
             Workbook workbook = new XSSFWorkbook(fis)) {

            Sheet sheet = workbook.getSheetAt(0);
            
            // Get header rows
            Row chineseHeaderRow = sheet.getRow(1); // Chinese column names (row 2)
            Row fieldHeaderRow = sheet.getRow(2);   // Database field names (row 3)
            
            // Create mapping from column index to field name
            Map<Integer, String> columnToFieldMap = new HashMap<>();
            for (int i = 0; i < fieldHeaderRow.getLastCellNum(); i++) {
                Cell fieldCell = fieldHeaderRow.getCell(i);
                if (fieldCell != null) {
                    columnToFieldMap.put(i, fieldCell.getStringCellValue().trim());
                }
            }

            // Process data rows starting from row 4 (index 3)
            for (int rowNum = 3; rowNum <= sheet.getLastRowNum(); rowNum++) {
                Row dataRow = sheet.getRow(rowNum);
                if (dataRow == null) continue;

                StringBuilder sql = new StringBuilder("INSERT INTO original_data_jh_2025 (");
                StringBuilder values = new StringBuilder("VALUES (");

                boolean firstColumn = true;
                for (int colNum = 0; colNum < columnToFieldMap.size(); colNum++) {
                    String fieldName = columnToFieldMap.get(colNum);
                    if (fieldName == null || fieldName.isEmpty()) continue;

                    if (!firstColumn) {
                        sql.append(", ");
                        values.append(", ");
                    }
                    firstColumn = false;
                    
                    sql.append(fieldName);

                    Cell cell = dataRow.getCell(colNum);
                    String cellValue = sanitizeSqlValue(getCellValueAsString(cell));
                    
                    // Escape single quotes for SQL
                    cellValue = cellValue.replace("'", "''");
                    values.append("'").append(cellValue).append("'");
                }

                sql.append(") ").append(values).append(");");
                SQL.append(sql.toString() + "\r\n");
            }
            
            writeTempFile(new File("F:\\2025购买的数据\\原始文件\\生成的SQL\\", excelFile + "_" + Tools.getSecondDate(new Date()) + ".sql"), SQL);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private static String sanitizeSqlValue(String input) {
        if (input == null) {
            return "";
        }
        
        // 1. Escape single quotes
        String sanitized = input.replace("'", "''");
        
        // 2. Remove or replace problematic Unicode characters
        sanitized = SQL_SPECIAL_CHARS.matcher(sanitized).replaceAll("");
        
        // 3. Remove control characters except tab, newline, return
        sanitized = sanitized.replaceAll("[\\p{Cntrl}&&[^\r\n\t]]", "");
        
        // 4. Replace any remaining problematic ASCII characters
        sanitized = sanitized.replaceAll("[\"\\\\\u0000-\u001F]", "");
        
        // 5. Check for SQL injection patterns
        if (sanitized.matches("(?i).*\\b(SELECT|INSERT|UPDATE|DELETE|DROP|UNION|EXEC|ALTER|CREATE|TRUNCATE)\\b.*")) {
            sanitized = sanitized.replaceAll("(?i)\\b(SELECT|INSERT|UPDATE|DELETE|DROP|UNION|EXEC|ALTER|CREATE|TRUNCATE)\\b", "");
        }
        
        return sanitized;
    }
    
    public static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // Remove .0 from integer values
                    double num = cell.getNumericCellValue();
                    if (num == (long) num) {
                        return String.valueOf((long) num);
                    } else {
                        return String.valueOf(num);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    public static void main(String[] args) {
    	String path = "F:\\2025购买的数据\\原始文件\\费老师\\";
        String excelFile = "A2天津24计划合并23，22，21专业分(1).xlsx";
        generateInsertStatements(path, excelFile);
    }
}
