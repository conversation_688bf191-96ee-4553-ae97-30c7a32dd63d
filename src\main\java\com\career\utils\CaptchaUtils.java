package com.career.utils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Random;
 
public class CaptchaUtils {
    // 验证码字符集
    private static final String CHAR_SET = "23456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz";
    private static final int WIDTH = 120; // 图片宽度
    private static final int HEIGHT = 40; // 图片高度
    private static final int FONT_SIZE = 30; // 字体大小
    private static final int CODE_LENGTH = 4; // 验证码长度
    private static final int LINE_COUNT = 5; // 干扰线数量
    private static final int NOISE_COUNT = 30; // 噪点数量
    // 生成随机验证码
    public static String generateCaptchaCode() {
        Random random = new Random();
        StringBuilder captcha = new StringBuilder();
        for (int i = 0; i < CODE_LENGTH; i++) {
            captcha.append(CHAR_SET.charAt(random.nextInt(CHAR_SET.length())));
        }
        return captcha.toString();
    }
    // 生成验证码图片
    public static BufferedImage generateCaptchaImage(String captchaCode) {
        BufferedImage image = new BufferedImage(WIDTH, HEIGHT, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = image.createGraphics();
        // 设置背景色（随机浅色）
        g.setColor(getRandomLightColor());
        g.fillRect(0, 0, WIDTH, HEIGHT);
        // 设置字体（随机选择字体）
        g.setFont(getRandomFont());
        g.setColor(getRandomDarkColor());
        // 绘制验证码字符
        for (int i = 0; i < CODE_LENGTH; i++) {
            // 随机旋转字符角度
            double theta = Math.toRadians(new Random().nextInt(30) - 15);
            g.rotate(theta, i * (WIDTH / CODE_LENGTH) + 10, HEIGHT / 2 + 10);
            g.drawString(String.valueOf(captchaCode.charAt(i)), i * (WIDTH / CODE_LENGTH) + 10, HEIGHT / 2 + 10);
            g.rotate(-theta, i * (WIDTH / CODE_LENGTH) + 10, HEIGHT / 2 + 10);
        }
        // 绘制干扰线
        g.setColor(getRandomDarkColor());
        Random random = new Random();
        for (int i = 0; i < LINE_COUNT; i++) {
            int x1 = random.nextInt(WIDTH);
            int y1 = random.nextInt(HEIGHT);
            int x2 = random.nextInt(WIDTH);
            int y2 = random.nextInt(HEIGHT);
            g.drawLine(x1, y1, x2, y2);
        }
        // 绘制噪点
        for (int i = 0; i < NOISE_COUNT; i++) {
            int x = random.nextInt(WIDTH);
            int y = random.nextInt(HEIGHT);
            image.setRGB(x, y, getRandomDarkColor().getRGB());
        }
        g.dispose();
        return image;
    }
    // 将图片转换为字节数组
    public static byte[] imageToBytes(BufferedImage image) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "PNG", baos);
        return baos.toByteArray();
    }
    // 获取随机浅色
    private static Color getRandomLightColor() {
        Random random = new Random();
        return new Color(random.nextInt(100) + 155, random.nextInt(100) + 155, random.nextInt(100) + 155);
    }
    // 获取随机深色
    private static Color getRandomDarkColor() {
        Random random = new Random();
        return new Color(random.nextInt(100), random.nextInt(100), random.nextInt(100));
    }
    // 获取随机字体
    private static Font getRandomFont() {
        String[] fontNames = { "Arial", "Verdana", "Georgia", "Times New Roman", "Courier New" };
        Random random = new Random();
        return new Font(fontNames[random.nextInt(fontNames.length)], Font.BOLD, FONT_SIZE);
    }
}
