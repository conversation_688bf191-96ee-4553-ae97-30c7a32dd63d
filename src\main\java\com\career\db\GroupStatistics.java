package com.career.db;

import java.util.Date;
import java.util.List;
import com.career.utils.BaseBean;

public class GroupStatistics extends BaseBean {
    
    // 组长基本信息
    private String group_leader_id;
    private String group_leader_name;
    private String group_leader_phone;
    private Date group_leader_last_login;
    private int sub_account_count;
    
    // 专家列表
    private List<ExpertStatistics> expertList;
    
    // 组整体统计
    private int total_experts;              // 专家总数
    private int active_experts;             // 活跃专家数（最近30天有登录）
    private int total_customers;            // 客户总数
    private int total_paid_customers;       // 已付款客户总数
    private int total_active_customers;     // 有效客户总数
    private int total_deleted_customers;    // 已删除客户总数
    private int total_this_month;           // 本月新增客户总数
    private double group_conversion_rate;   // 组整体转化率
    
    public static void main(String args[]) {
        printBeanProperties(new GroupStatistics());
    }
    
    // Getters and Setters
    public String getGroup_leader_id() {
        return group_leader_id;
    }
    
    public void setGroup_leader_id(String group_leader_id) {
        this.group_leader_id = group_leader_id;
    }
    
    public String getGroup_leader_name() {
        return group_leader_name;
    }
    
    public void setGroup_leader_name(String group_leader_name) {
        this.group_leader_name = group_leader_name;
    }
    
    public String getGroup_leader_phone() {
        return group_leader_phone;
    }
    
    public void setGroup_leader_phone(String group_leader_phone) {
        this.group_leader_phone = group_leader_phone;
    }
    
    public Date getGroup_leader_last_login() {
        return group_leader_last_login;
    }
    
    public void setGroup_leader_last_login(Date group_leader_last_login) {
        this.group_leader_last_login = group_leader_last_login;
    }
    
    public int getSub_account_count() {
        return sub_account_count;
    }
    
    public void setSub_account_count(int sub_account_count) {
        this.sub_account_count = sub_account_count;
    }
    
    public List<ExpertStatistics> getExpertList() {
        return expertList;
    }
    
    public void setExpertList(List<ExpertStatistics> expertList) {
        this.expertList = expertList;
    }
    
    public int getTotal_experts() {
        return total_experts;
    }
    
    public void setTotal_experts(int total_experts) {
        this.total_experts = total_experts;
    }
    
    public int getActive_experts() {
        return active_experts;
    }
    
    public void setActive_experts(int active_experts) {
        this.active_experts = active_experts;
    }
    
    public int getTotal_customers() {
        return total_customers;
    }
    
    public void setTotal_customers(int total_customers) {
        this.total_customers = total_customers;
    }
    
    public int getTotal_paid_customers() {
        return total_paid_customers;
    }
    
    public void setTotal_paid_customers(int total_paid_customers) {
        this.total_paid_customers = total_paid_customers;
    }
    
    public int getTotal_active_customers() {
        return total_active_customers;
    }
    
    public void setTotal_active_customers(int total_active_customers) {
        this.total_active_customers = total_active_customers;
    }
    
    public int getTotal_deleted_customers() {
        return total_deleted_customers;
    }
    
    public void setTotal_deleted_customers(int total_deleted_customers) {
        this.total_deleted_customers = total_deleted_customers;
    }
    
    public int getTotal_this_month() {
        return total_this_month;
    }
    
    public void setTotal_this_month(int total_this_month) {
        this.total_this_month = total_this_month;
    }
    
    public double getGroup_conversion_rate() {
        return group_conversion_rate;
    }
    
    public void setGroup_conversion_rate(double group_conversion_rate) {
        this.group_conversion_rate = group_conversion_rate;
    }
} 