package com.career.utils;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

import com.career.db.JDBCConstants;
import com.career.db.ZyzdBaseMajor;

public class DealWith_Xuanke {

	static String URL = JDBCConstants.URL;
	static String USER = JDBCConstants.USER;
	static String PASSWD = JDBCConstants.PASSWD;
	public static int PAGE_ROW_CNT = 20;
	public static int PAGE_ROW_CNT_SPEC = 15;
	
	static {
		try {
			Class.forName("com.mysql.cj.jdbc.Driver");
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
	
	public static void main(String args[]) throws Exception {
		dealwith_xk();
	}
	
	public static void dealwith_xk() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		StringBuffer SQLSTR = new StringBuffer();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "select yxmc,zymc,xk_desc,zx from zyzd_temp_sc2025_xk x group by yxmc,zymc,xk_desc,zx";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			PreparedStatement ps2 = null;
			int i=0;
			while (rs.next()) {
				String yxmc_org = rs.getString("yxmc");
				String zymc_org = rs.getString("zymc");
				String xk_desc = rs.getString("xk_desc");
				String zx = rs.getString("zx");
				String SQLX = "update s5_sc_jh_2024_0116 x set x.xk_desc = '"+xk_desc+"', x.zx = '"+zx+"' "
						+ "where x.xk_desc is null and x.yxmc_org = '"+yxmc_org+"' and x.zymc_org = '"+zymc_org+"';\r\n";
				//ps2 = conn.prepareStatement(SQLX);
				//ps2.addBatch();
				
				SQLSTR.append(SQLX);
				
				Tools.println((i++) + "-yxmc_org->"+SQLX);
				
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		
		writeTempFile(new File("F://诊断考试换算//K9113XX.txt"), SQLSTR);
	}
	
	public static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	private static void closeAllConnection(Connection conn, PreparedStatement ps, ResultSet rs) {
		try {
			if (rs != null)
				rs.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (ps != null)
				ps.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (conn != null)
				conn.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		rs = null;
		ps = null;
		conn = null;
	}

}
