package com.payment;

import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderResult;
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryResult;
import com.github.binarywang.wxpay.constant.WxPayConstants;
import com.github.binarywang.wxpay.service.WxPayService;
import com.zsdwf.utils.WXUtils;
import com.zsdwf.utils.Tools;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.text.SimpleDateFormat;

/**
 * Native支付服务类
 * 
 * 功能说明：
 * 1. 创建Native支付订单和二维码
 * 2. 查询支付状态
 * 3. 管理订单信息（内存存储，后期需要迁移到数据库）
 * 4. 生成账号密码信息
 * 
 * 后期数据库优化点：
 * - 所有orderMap操作需要替换为数据库操作
 * - 需要创建payment_orders表存储订单信息
 * - 需要创建account_info表存储生成的账号密码
 * - 需要添加订单状态变更日志表
 */
public class NativePaymentService {
    
    // TODO: 后期需要替换为数据库存储
    // 临时使用内存Map存储订单信息
    private static final Map<String, PaymentOrder> orderMap = new ConcurrentHashMap<>();
    private static final Map<String, List<String>> openIdOrdersMap = new ConcurrentHashMap<>();
    
    /**
     * 产品配置信息
     * TODO: 后期需要从数据库product_config表读取
     */
    public static class ProductConfig {
        public static final String FAMILY_398 = "FAMILY_398";
        public static final String FAMILY_998 = "FAMILY_998"; 
        public static final String FAMILY_1980 = "FAMILY_1980";
        public static final String INSTITUTION_2980 = "INSTITUTION_2980";
        public static final String INSTITUTION_3980 = "INSTITUTION_3980";
        public static final String INSTITUTION_4980 = "INSTITUTION_4980";
        public static final String INSTITUTION_5980 = "INSTITUTION_5980";
        
        public static ProductInfo getProductInfo(String productType) {
            switch(productType) {
                case FAMILY_398:
                    return new ProductInfo("AI 志愿审核版", 39800, "家庭版-AI智能分析推荐和审核", 365);
                case FAMILY_998:
                    return new ProductInfo("AI+专家 志愿审核版", 99800, "家庭版-AI+专家双重审核", 365);
                case FAMILY_1980:
                    return new ProductInfo("专家一对一远程志愿", 198000, "家庭版-专家一对一远程服务", 365);
                case INSTITUTION_2980:
                    return new ProductInfo("单省旗舰版", 298000, "机构版-单省填报服务", 365);
                case INSTITUTION_3980:
                    return new ProductInfo("单省家长训练营版", 398000, "机构版-单省+家长功能", 365);
                case INSTITUTION_4980:
                    return new ProductInfo("全国旗舰版", 498000, "机构版-全国填报服务", 365);
                case INSTITUTION_5980:
                    return new ProductInfo("全国家长训练营版", 598000, "机构版-全国+家长功能", 365);
                default:
                    return null;
            }
        }
    }
    
    /**
     * 产品信息类
     */
    public static class ProductInfo {
        public String name;
        public int price; // 单位：分
        public String description;
        public int validDays;
        
        public ProductInfo(String name, int price, String description, int validDays) {
            this.name = name;
            this.price = price;
            this.description = description;
            this.validDays = validDays;
        }
    }
    
    /**
     * 订单信息类
     * TODO: 后期需要对应数据库表结构
     */
    public static class PaymentOrder {
        public String orderId;
        public String openId;
        public String productType;
        public String productName;
        public String productDescription;
        public int amount; // 单位：分
        public String payStatus; // PENDING, SUCCESS, FAILED
        public Date createTime;
        public Date payTime;
        public String wxTransactionId;
        public String codeUrl; // Native支付二维码URL
        
        // 生成的账号信息
        public String accountNo;
        public String password;
        public String serviceUrl;
        public int validDays;
        public Date expireTime;
        
        public PaymentOrder() {
            this.createTime = new Date();
            this.payStatus = "PENDING";
        }
    }
    
    /**
     * 创建Native支付订单
     * 
     * @param productType 产品类型
     * @param openId 微信openId（可选，主要用于后续订单查询）
     * @param clientIp 客户端IP
     * @return 包含二维码URL的订单信息
     */
    public PaymentOrder createNativePayment(String productType, String openId, String clientIp) throws Exception {
        // 1. 获取产品信息
        ProductInfo productInfo = ProductConfig.getProductInfo(productType);
        if (productInfo == null) {
            throw new IllegalArgumentException("不支持的产品类型: " + productType);
        }
        
        // 2. 生成订单号
        String orderId = generateOrderId();
        
        // 3. 创建订单对象
        PaymentOrder order = new PaymentOrder();
        order.orderId = orderId;
        order.openId = openId;
        order.productType = productType;
        order.productName = productInfo.name;
        order.productDescription = productInfo.description;
        order.amount = productInfo.price;
        order.validDays = productInfo.validDays;
        
        // 4. 调用微信支付API创建Native订单
        WxPayService wxPayService = WXUtils.payInit();
        WxPayUnifiedOrderRequest request = new WxPayUnifiedOrderRequest();
        request.setBody(productInfo.name);
        request.setOutTradeNo(orderId);
        request.setTotalFee(productInfo.price);
        request.setSpbillCreateIp(clientIp);
        request.setNotifyUrl("http://dwfcx.com/zsdwf/pay/native_callback.jsp"); // TODO: 配置回调URL
        request.setTradeType(WxPayConstants.TradeType.NATIVE);
        
        WxPayUnifiedOrderResult result = wxPayService.unifiedOrder(request);
        
        if ("SUCCESS".equals(result.getReturnCode()) && "SUCCESS".equals(result.getResultCode())) {
            order.codeUrl = result.getCodeURL();
            
            // 5. 保存订单信息 
            // TODO: 后期需要保存到数据库payment_orders表
            orderMap.put(orderId, order);
            
            // 6. 如果有openId，建立关联关系
            if (!Tools.isEmpty(openId)) {
                openIdOrdersMap.computeIfAbsent(openId, k -> new ArrayList<>()).add(orderId);
            }
            
            System.out.println("创建Native支付订单成功: " + orderId + ", 产品: " + productInfo.name + ", 金额: " + (productInfo.price/100.0) + "元");
            return order;
        } else {
            throw new Exception("微信支付订单创建失败: " + result.getReturnMsg());
        }
    }
    
    /**
     * 查询支付状态
     */
    public PaymentOrder queryPaymentStatus(String orderId) throws Exception {
        // 1. 从内存获取订单信息
        // TODO: 后期从数据库查询
        PaymentOrder order = orderMap.get(orderId);
        if (order == null) {
            return null;
        }
        
        // 2. 如果已经支付成功，直接返回
        if ("SUCCESS".equals(order.payStatus)) {
            return order;
        }
        
        // 3. 调用微信API查询最新状态
        WxPayService wxPayService = WXUtils.payInit();
        WxPayOrderQueryResult result = wxPayService.queryOrder(null, orderId);
        
        if ("SUCCESS".equals(result.getReturnCode()) && "SUCCESS".equals(result.getResultCode())) {
            if ("SUCCESS".equals(result.getTradeState())) {
                // 支付成功，更新订单状态并生成账号
                updateOrderToSuccess(order, result.getTransactionId());
            } else {
                order.payStatus = result.getTradeState(); // NOTPAY, CLOSED, REVOKED, USERPAYING, PAYERROR
            }
        }
        
        return order;
    }
    
    /**
     * 处理支付成功回调
     * 支持幂等操作，多次调用不会重复处理
     */
    public boolean handlePaymentCallback(String orderId, String wxTransactionId) {
        try {
            // TODO: 后期从数据库查询订单
            PaymentOrder order = orderMap.get(orderId);
            if (order == null) {
                System.err.println("回调处理失败: 订单不存在 " + orderId);
                return false;
            }
            
            // 幂等检查：如果已经处理过，直接返回成功
            if ("SUCCESS".equals(order.payStatus)) {
                System.out.println("订单已处理，跳过重复回调: " + orderId);
                return true;
            }
            
            // 更新订单为支付成功状态
            updateOrderToSuccess(order, wxTransactionId);
            
            System.out.println("支付回调处理成功: " + orderId);
            return true;
            
        } catch (Exception e) {
            System.err.println("处理支付回调失败: " + orderId + ", " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 根据openId查询用户的所有订单
     */
    public List<PaymentOrder> getOrdersByOpenId(String openId) {
        List<PaymentOrder> orders = new ArrayList<>();
        
        // TODO: 后期从数据库查询
        // SELECT * FROM payment_orders WHERE open_id = ? AND pay_status = 'SUCCESS' ORDER BY create_time DESC
        List<String> orderIds = openIdOrdersMap.get(openId);
        if (orderIds != null) {
            for (String orderId : orderIds) {
                PaymentOrder order = orderMap.get(orderId);
                if (order != null && "SUCCESS".equals(order.payStatus)) {
                    orders.add(order);
                }
            }
        }
        
        // 按时间倒序排列
        orders.sort((a, b) -> b.createTime.compareTo(a.createTime));
        
        return orders;
    }
    
    /**
     * 更新订单为支付成功状态并生成账号密码
     */
    private void updateOrderToSuccess(PaymentOrder order, String wxTransactionId) {
        order.payStatus = "SUCCESS";
        order.payTime = new Date();
        order.wxTransactionId = wxTransactionId;
        
        // 生成账号密码
        generateAccountInfo(order);
        
        // TODO: 后期需要更新数据库
        // UPDATE payment_orders SET pay_status='SUCCESS', pay_time=?, wx_transaction_id=?, 
        // account_no=?, password=?, expire_time=? WHERE order_id=?
        
        System.out.println("订单支付成功: " + order.orderId + ", 生成账号: " + order.accountNo);
    }
    
    /**
     * 生成账号密码信息
     * TODO: 后期可能需要根据不同产品类型生成不同的账号
     */
    private void generateAccountInfo(PaymentOrder order) {
        // 生成账号：产品前缀 + 日期 + 随机数
        String prefix = order.productType.startsWith("FAMILY") ? "JT" : "JG";
        String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        String randomStr = Tools.generateNonceStr().substring(0, 6).toUpperCase();
        order.accountNo = prefix + dateStr + randomStr;
        
        // 生成密码：8位随机字符
        order.password = generateRandomPassword();
        
        // 设置服务地址
        order.serviceUrl = "http://dwfcx.com/zsdwf/lhy/remote/rt_order.jsp";
        
        // 计算过期时间
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_YEAR, order.validDays);
        order.expireTime = cal.getTime();
        
        // TODO: 后期可能需要将账号信息保存到单独的account_info表
        // INSERT INTO account_info (order_id, account_no, password, service_url, expire_time) VALUES (...)
    }
    
    /**
     * 生成随机密码
     */
    private String generateRandomPassword() {
        String chars = "ABCDEFGHJKMNPQRSTUVWXYZ23456789"; // 去掉容易混淆的字符
        StringBuilder password = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 8; i++) {
            password.append(chars.charAt(random.nextInt(chars.length())));
        }
        return password.toString();
    }
    
    /**
     * 生成订单号
     */
    private String generateOrderId() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timeStr = sdf.format(new Date());
        String randomStr = Tools.generateNonceStr().substring(0, 6);
        return "NT" + timeStr + randomStr; // NT = Native
    }
    
    /**
     * 获取单例实例
     */
    private static NativePaymentService instance;
    public static synchronized NativePaymentService getInstance() {
        if (instance == null) {
            instance = new NativePaymentService();
        }
        return instance;
    }
} 