package com.career.utils.liuxue;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.career.utils.HttpSendUtils;
import com.career.utils.zsky.ResultInfoBean;

public class Spider_NCSS {
	
	// 重庆人事考试网抓取网页
		public static StringBuffer YOUZHIYUAN() {
			Map<String, String> headers = new HashMap<>();
			Map<String, String> params = new HashMap<>();

			
			headers.put("Referrer Policy", "strict-origin-when-cross-origin");
			headers.put("Accept", "*/*");
			headers.put("Accept-Encoding", "gzip, deflate, br, zstd");
			headers.put("Accept-Language", "zh-CN,zh;q=0.9");
			headers.put("Cache-Control", "no-cache");
			headers.put("Connection", "keep-alive");
			headers.put("Cookie", "SESSION=968d1fd3-59c0-4869-a46a-c6699ccbfed9; XSRF-CCKTOKEN=d85e5f8f7eb744596e4bdd0eb6333b8d; cna=1a5f26d2a4fdeb43d808c7282798a451; Hm_lvt_378ff17a1ac046691cf78376632d1ed4=**********; HMACCOUNT=F9E89F99DE4FFF7E; _gid=GA1.2.*********.**********; aliyungf_tc=b97d488cf91f2c028ee1c2db5913f9800cf0057dca48ffdac452a254e2363361; goaYXsyEWlxdO=60KaOk3H_YnSDdp9dw9R4qx7cxFnnd_7f3VCGXnJHDw0y4rM3pCgPpwAwEBdwDvXpyAIcNXPWWDKZGkcR6XQQDDG; CHSICC01=!a4+WMVQsuO6ce4rzYxYLahOzddj6Y9QVA8E2NRNPQkr0ynIhzbJI1/ARYEUABFBnfoGoDE52OlxNfg==; CHSICC_CLIENTFLAGSTUDENT=afe4efd20cab18c46368eec6df24c438; CHSICC_CLIENTFLAGMANAGER=c2f1cf805574a1a491517969c1ce7a8f; _abfpc=afc4613447229eece3388833b280d6ec73a4dbdd_2.0; acw_tc=ac110001**********0544254e37bff36aaafebdbef2fc5d22b7dc3e6e7e2a; Hm_lpvt_378ff17a1ac046691cf78376632d1ed4=**********; _ga=GA1.1.**********.**********; _ga_6CXWRD3K0D=GS1.1.**********.2.1.**********.0.0.0; goaYXsyEWlxdP=0OPgi3KZJL_WDMiIiqKRK0W6u.o_yooKi9fk4k5gfNNKKU8l9CvTsrKwEnduZozxtxYJG_fjFo5KXkojUo9Nf7FGf2PehMiqzv7XikL7j3m9bp_9n0Ud7no07P1wi83ekfQV6Y7.VY9G1J3C2SNlBJzINjaMUjveLaV4Tru24t_pZ1gFu.n4Gyp_penCqXNaggbAZuOj.7DleU4mWm9VSLh7qUUNU2NxSPz9RjBLT_8stOwwwKkfOkmYsaYYCAITSoU7im5r6mPNigrgRv3jdSQEj6ZgqfRywGEXkuV_ZohdScecy1vbvKccmjiZSS3mnyH_qZd4DyudkymYmPVXd3MdvqPpzEpm8KrCoSGFj1wgTuiSMp90h12SK0t8iqz0QH_lA5lvCFopH2Llhz6hyd9UEbXgUDxn6sjux6PiATQL");
			headers.put("Host", "job.ncss.cn");
			headers.put("Pragma", "no-cache");
			headers.put("Referer", "https://job.ncss.cn/student/jobs/index.html");
			headers.put("sec-ch-ua", "\"Chromium\";v=\"130\", \"Google Chrome\";v=\"130\", \"Not?A_Brand\";v=\"99\"");
			headers.put("sec-ch-ua-mobile", "?0");
			headers.put("sec-ch-ua-platform", "\"Windows\"");
			headers.put("Sec-Fetch-Dest", "empty");
			headers.put("Sec-Fetch-Mode", "cors");
			headers.put("Sec-Fetch-Site", "same-origin");
			headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36");
			headers.put("X-Requested-With", "XMLHttpRequest");
			
			
			StringBuffer SQL = new StringBuffer();


			String resultPageList = HttpSendUtils.get("https://job.ncss.cn/student/jobs/W9oWKePvntavbZL4JGZxLw/detail.html", headers);
			System.out.println(resultPageList);
			
			
			return SQL;
		}
		
		
		static String u(String tt) {
			String en_key = "eFPIKDkOCHio8sVfprqdxt0jEw9gMb";
			String result = tt.toLowerCase() + '&' + en_key.toLowerCase();
			return result;
		}
		
		static String get_u_sign(String data) {
			String sign_data = u(data);
			String encodedString = Base64.getEncoder().encodeToString(sign_data.getBytes());
		    return calculateMD5(encodedString);
		}
		
		private static String calculateMD5(String originalString) {
	        try {
	            // 创建MD5加密实例
	            MessageDigest md = MessageDigest.getInstance("MD5");
	 
	            // 执行加密操作
	            byte[] messageDigest = md.digest(originalString.getBytes());
	 
	            // 将得到的散列值转换为十六进制
	            StringBuilder sb = new StringBuilder();
	            for (byte b : messageDigest) {
	                sb.append(String.format("%02x", b));
	            }
	 
	            // 返回MD5散列值
	            return sb.toString();
	        } catch (NoSuchAlgorithmException e) {
	            throw new RuntimeException("MD5加密算法不可用", e);
	        }
	    }

		

		public static void main(String[] args) {
			try {
				StringBuffer SQL = new StringBuffer();
				for(int i=1;i<=20;i++) {
					File file = new File("F:\\就业报告\\jiuye\\" + i +".txt");
					BufferedReader br = new BufferedReader(new FileReader(file));
					StringBuffer sb = new StringBuffer();
					String line = null;
					while((line = br.readLine()) != null) {
						sb.append(line);
					}
					
					
					System.out.println(file.getAbsolutePath());
					JSONObject object = JSONObject.parseObject(sb.toString());
					JSONObject JSONObject = object.getJSONObject("data");
					JSONArray JSONArray = JSONObject.getJSONArray("list");
					for(int k=0;k<JSONArray.size();k++) {
						NCSSJOBBean bean = JSONArray.getObject(k, NCSSJOBBean.class);
						System.out.println(bean.getPublishDate());
						SQL.append(bean.generateSQL()+"\r\n");
					}
				}
				//writeTempFile(new File("F:\\就业报告\\jiuye\\RESULT.txt"), SQL);
				
				YOUZHIYUAN();
				
			}catch(Exception ex) {
				
			}
		}
		
		private static void writeTempFile(File file, StringBuffer sb) {
			try {
				BufferedWriter bw = new BufferedWriter(new FileWriter(file));
				bw.write(sb.toString());
				bw.flush();
				bw.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}

}
