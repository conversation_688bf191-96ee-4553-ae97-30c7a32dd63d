<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page import="com.career.utils.*,com.career.db.*,java.util.*" %>
<%@include file="/WEB-INF/include/_session_ajax_lhy.jsp"%>

<%
long start = System.currentTimeMillis();
LhyOrder lhyOrder = (LhyOrder)session.getAttribute(ZyzdCache.SES_KEY_ORDER_USE_CARD); 
if(lhyOrder == null){
	out.print("ERR:NO_USER_SELECTED"); 
	return;
}

ZyzdProvinceConfig provinceConfig = ZyzdCache.getProvinceConfig(lhyOrder.getSf());
String provinceTableName = provinceConfig.getP_table_code();
String provinceName = provinceConfig.getP_name();
int LATEST_ZY_YEAR = provinceConfig.getLatest_year_zy();
int LATEST_YX_YEAR = provinceConfig.getLatest_year_yx();
int LATEST_JH_YEAR = provinceConfig.getLatest_year_jh();

boolean is96 = provinceConfig.getForm_type() == 2;

String batch_id = Tools.trim(request.getParameter("batch_id"));

LhyJDBC lhyJDBC = new LhyJDBC();
ZyzdJDBC jdbc = new ZyzdJDBC();

LhyFormMain lhyFormMain = lhyJDBC.getMakerFormMainByBatchId(provinceTableName, batch_id);
List<LhyForm> formList = lhyJDBC.getMakerForm(provinceTableName, batch_id);

if(lhyFormMain == null || formList.size() == 0){
	out.print("ERR:EXCEED");
	return;
}

// 设置Session值，供专业排序调整时使用
session.setAttribute(ZyzdCache.SES_LHY_TB_ADJUST_CLICKED_FORM_MAIN_INFO, lhyFormMain);

String xkCode = XKCombineUtils.getXKCodeByStudentSelection(lhyFormMain.getScore_xk());

HashMap<String, ZDKSRank> TONGWF_MAP = jdbc.getAllTongWF(provinceName, xkCode, lhyFormMain.getScore_wc());
ZDKSRank rankYearA = TONGWF_MAP.get(String.valueOf(LATEST_JH_YEAR - 1)); 
ZDKSRank rankYearB = TONGWF_MAP.get(String.valueOf(LATEST_JH_YEAR - 2));
ZDKSRank rankYearC = TONGWF_MAP.get(String.valueOf(LATEST_JH_YEAR - 3)); 


int MAX_SEQ = formList.get(formList.size()-1).getSeq_no_yx();

Map<Integer, LhyForm> seqNoToMinWc = new HashMap<>();

Map<Integer, List<LhyForm>> MAP_LIST = new HashMap<>();
for(LhyForm bean : formList){ 
	int seqNo = bean.getSeq_no_yx();
	LhyForm teempBean = seqNoToMinWc.get(seqNo);
	 int zdfwc = bean.getZdfwc_a() <= 0 ? (bean.getZdfwc_b() <= 0 ? (bean.getZdfwc_c() <= 0 ? 0 : bean.getZdfwc_c()) : bean.getZdfwc_b()) : bean.getZdfwc_a(); 
	 if (!seqNoToMinWc.containsKey(seqNo) || zdfwc > (teempBean == null ? 0 : teempBean.getExt_zdfwc())) {
		 if(zdfwc > 0){
			 bean.setExt_zdfwc(zdfwc);
			 seqNoToMinWc.put(seqNo, bean);
		 }
	 }
	 
	 String key = Tools.getTBUniqueGroupOnlyKey(bean, is96);
	 
	 if(MAP_LIST.containsKey(seqNo)){
		 List<LhyForm> lhyFormList = MAP_LIST.get(seqNo);
		 lhyFormList.add(bean);
	 }else{
		 List<LhyForm> lhyFormList = new ArrayList<>();
		 lhyFormList.add(bean); 
		 MAP_LIST.put(seqNo, lhyFormList);
	 }
}

LinkedHashMap<String, List<LhyForm>> MAP_XX = new LinkedHashMap<>();

// 构建 MAP_XX - 按院校序号分组
for(LhyForm x : formList){
	String key = String.valueOf(x.getSeq_no_yx());
	if(MAP_XX.containsKey(key)){
		List<LhyForm> ls = MAP_XX.get(key);
		ls.add(x);
	}else{
		List<LhyForm> ls = new ArrayList<>();
		ls.add(x);
		MAP_XX.put(key, ls);
	}
}

Map<String, Integer> groupMajorTotalMap = new HashMap<>();
Set<String> userGroupSet = new HashSet<>();
Map<String, JHBean> groupMajorBeanMap = new HashMap<>();

HashSet<String> yxdmSet = new HashSet<>();
HashSet<String> zyzSet = new HashSet<>();

for(List<LhyForm> lhyFormList : MAP_XX.values()){
    if(lhyFormList != null && !lhyFormList.isEmpty()){
        LhyForm last = lhyFormList.get(lhyFormList.size() - 1);

        if(!Tools.isEmpty(last.getYxdm())){
        	yxdmSet.add(last.getYxdm());
        }

        if(!Tools.isEmpty(last.getZyz())){
        	zyzSet.add(last.getZyz());
        }

        userGroupSet.add(last.getYxdm().trim().toUpperCase() + "_" + Tools.trim(last.getZyz()));
    }
}

FromGenerateJDBC fromGenerateJDBC = new FromGenerateJDBC();
List<JHBean> allMajorList = fromGenerateJDBC.getJHByYxdmAndZyz(
    LATEST_JH_YEAR, provinceTableName, xkCode, lhyFormMain.getPc(), lhyFormMain.getPc_code(), yxdmSet, zyzSet, new HashSet<>()
);

for(JHBean bean : allMajorList){
    String key = bean.getYxdm().trim() + "_" + Tools.trim(bean.getZyz()); 
    if(userGroupSet.contains(key)) {
        groupMajorTotalMap.put(key, groupMajorTotalMap.getOrDefault(key, 0) + 1);
    }
    
    String key_major = Tools.getTBUniqueKey(bean, is96);
    groupMajorBeanMap.put(key_major, bean);
}

%>


<div class="container-fluid p-0">
    <div class="row g-0">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover table-sm mb-0 compact-table" id="adjust_drag_yx_exampleModalScrollable_content_sort_table">
                            <thead class="table-light sticky-top"> 
                                <tr class="border-bottom">
                                    <th scope="col" class="text-center fw-bold text-info" style="width: 10px;">
                                        <i class="bi bi-list-ol me-1"></i>序号
                                    </th>
                                    <th scope="col" class="fw-bold text-info" style="width: 18%;">
                                        <i class="bi bi-bank me-1"></i>院校名称
                                    </th>
                                    <th scope="col" class="fw-bold text-info" style="width: 9%;">
                                        <i class="bi bi-geo-alt me-1"></i>省份
                                    </th>
                                    <th scope="col" class="fw-bold text-info" style="width: 9%;">
                                        <i class="bi bi-layers me-1"></i>层次
                                    </th>
                                    <th scope="col" class="fw-bold text-info" style="width: 9%;">
                                        <i class="bi bi-star me-1"></i>类型
                                    </th>
                                    
                                    <%if(!is96){ %>
                                    <th scope="col" class="fw-bold text-info text-center" style="width: 15%;">
                                        <i class="bi bi-collection me-1"></i>专业组
                                    </th>
                                    <%} %>
                                    <th scope="col" class="fw-bold text-info" style="width: 25%;">
                                        <i class="bi bi-mortarboard me-1"></i><%if(!is96){ %>已填最低<%}else{ %>专业名称<%} %>
                                    </th>
                                    
                                    <th scope="col" class="fw-bold text-info text-center" style="width: 8%;">
                                        <i class="bi bi-graph-up me-1"></i>预测
                                    </th>
                                    <th scope="col" class="fw-bold text-info text-center" style="width: 8%;">
                                        <i class="bi bi-gear me-1"></i>操作
                                    </th>
                                </tr> 
                            </thead>
                            <tbody>
                            	<%
                            	int index = 0;
                            	HashMap<String, String> existCheck = new HashMap<>();
                            	for(LhyForm zyzdForm : formList){ 
                            		if(existCheck.containsKey(Tools.getTBUniqueGroupOnlyKey(zyzdForm, is96))){
                            			continue;
                            		}
                            		existCheck.put(Tools.getTBUniqueGroupOnlyKey(zyzdForm, is96), "IN");
                            		
                            		LhyForm zyzdFormLowest = seqNoToMinWc.get(zyzdForm.getSeq_no_yx());
                            		if(zyzdFormLowest == null){
                            			zyzdFormLowest = zyzdForm;
                            		}
                            		
                            		ZyzdUniversityBean zyzdUniversityBean = ZyzdCache.getUniversity(zyzdForm.getYxmc_org());
                            		zyzdUniversityBean = zyzdUniversityBean == null ? new ZyzdUniversityBean() : zyzdUniversityBean;
                            		
                            		String universityProvice = Tools.trim(zyzdUniversityBean.getPosition_sf() + (Tools.isEmpty(zyzdUniversityBean.getPosition_cs()) ? "" : Tools.viewForLimitLength("."+zyzdUniversityBean.getPosition_cs(), 5)));
                            		String universityIndNature = Tools.trim(zyzdUniversityBean.getInd_nature());
                            		String universityCatg = Tools.trim(zyzdUniversityBean.getInd_catg());;
                            		
                            		JHBean jHBean = groupMajorBeanMap.getOrDefault(Tools.getTBUniqueKey(zyzdFormLowest, is96), new JHBean());
                            		int qsf_a = jHBean.getQsf_a();
                            		int qsf_b = jHBean.getQsf_b();
                            		int qsf_c = jHBean.getQsf_c();
                            		
                            		String qsf_cz_a = qsf_a <=0 ? "-" : String.valueOf(lhyFormMain.getScore_cj() - qsf_a);
                            		String qsf_cz_b = qsf_b <=0 ? "-" : String.valueOf(lhyFormMain.getScore_cj() - qsf_b);
                            		String qsf_cz_c = qsf_c <=0 ? "-" : String.valueOf(lhyFormMain.getScore_cj() - qsf_c);
                            		
                            		int score_waste_cnt = (jHBean.getQsf() <= 0 ? 999999 : (lhyFormMain.getScore_cj() - jHBean.getQsf()));
                            		
                            		// 根据预测结果确定行的样式类
                            		String rowClass = "";
                            		if(score_waste_cnt == 999999) {
                            			rowClass = "table-warning-light";
                            		} else if(score_waste_cnt == 999998) {
                            			rowClass = "table-danger-light";
                            		} else if(score_waste_cnt >= 5) {
                            			rowClass = "table-success-light";
                            		}
                            		
                        			
                        			String yx_tags_all = zyzdUniversityBean.getYx_tags_all(); 
                        			String ranking_rk = zyzdUniversityBean.getRanking_rk();
                            		
                            	%>
                                <tr class="item_popup_drag_seq_no_yx align-middle <%=rowClass%>" 
                                    popup_drag_seq_no_yx="<%=zyzdForm.getSeq_no_yx() %>" 
                                    style="transition: all 0.15s ease;">
                                    
                                    <td class="text-center position-relative py-1">
                                        <div class="d-flex align-items-center justify-content-center">
                                            <span class="badge bg-danger rounded-pill me-2 fw-bold"
											      style="font-size: 11px; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center;">
											  <%=zyzdForm.getSeq_no_yx() %>
											</span>
                                            <div class="btn-group" role="group">
                                                <%if(index > 0){ %>
                                                <button type="button" 
                                                        class="btn btn-outline-info btn-sm p-1 move-btn" 
                                                        style="font-size: 10px; line-height: 1; width: 22px; height: 18px;"
                                                        onclick="moveWithAnimation('<%=zyzdForm.getId() %>', '<%=zyzdForm.getSeq_no_yx() %>', '<%=zyzdForm.getSeq_no_yx()-1 %>', this)"
                                                        title="向上移动">
                                                    ↑
                                                </button>
                                                <%} else { %>
                                                <button type="button" class="btn btn-outline-secondary btn-sm p-1" style="font-size: 10px; line-height: 1; width: 22px; height: 18px; opacity: 0.3;" disabled>
                                                    ↑
                                                </button>
                                                <%} %>
                                                
                                                <%if(index < MAX_SEQ - 1){ %>
                                                <button type="button" 
                                                        class="btn btn-outline-info btn-sm p-1 move-btn" 
                                                        style="font-size: 10px; line-height: 1; width: 22px; height: 18px;"
                                                        onclick="moveWithAnimation('<%=zyzdForm.getId() %>', '<%=zyzdForm.getSeq_no_yx() %>', '<%=zyzdForm.getSeq_no_yx() + 1 %>', this)"
                                                        title="向下移动">
                                                    ↓
                                                </button>
                                                <%} else { %>
                                                <button type="button" class="btn btn-outline-secondary btn-sm p-1" style="font-size: 10px; line-height: 1; width: 22px; height: 18px; opacity: 0.3;" disabled>
                                                    ↓
                                                </button>
                                                <%} %>
                                            </div>
                                        </div>
                                    </td>
                                    
                                    <td class="fw-medium py-1">
                                        <div class="d-flex align-items-center">
                                        	<span class="badge bg-light text-danger me-2 font-monospace" style="font-size: 11px;">[<%=zyzdForm.getYxdm() %>]</span>  
                                            <span class="text-truncate fw-bold text-info" title="<%=zyzdForm.getYxmc() %>"><%=zyzdForm.getYxmc_org() %> 
                                            <span style="margin-left:1px;font-size:9px;color:red;">
                                            	<%if(!Tools.isEmpty(yx_tags_all)){ 
													yx_tags_all = yx_tags_all.replaceAll(" ", ""); 
												%>
												<span style="margin-left:3px;color:blue;font-size:10px;" onclick="alert('<%=Tools.escapeForHtml(yx_tags_all) %>');">
													<%=Tools.viewForLimitLength(yx_tags_all, 30) %>  
												</span> 
												<%} %>
												
												<%if(!Tools.isEmpty(ranking_rk)){  
												%>   
												<span style="margin-left:3px;color:gray;font-size:10px;" onclick="MM_pick_yx('<%=zyzdFormLowest.getYxmc_org()%>');">
													[排名<b style="font-size:12px;color:red;"><%=ranking_rk %></b>]    
												</span> 
												<%} %> 
                                            </span></span>
                                        </div> 
                                    </td>
                                    
                                   <td class="fw-medium py-1">
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-light text-info me-2 font-monospace" style="font-size: 11px;"><%=universityProvice %></span>
                                        </div>
                                    </td>
                                    
                                    <td class="fw-medium py-1">
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-light text-info me-2 font-monospace" style="font-size: 11px;"><%=universityIndNature %></span>
                                        </div>
                                    </td>
                                    
                                    <td class="fw-medium py-1">
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-light text-info me-2 font-monospace" style="font-size: 11px;"><%=universityCatg %></span>
                                        </div>
                                    </td>
                                    
                                    
                                    <%if(!is96){ %>
                                    <td class="text-center py-1">
                                        <span class="badge bg-success-subtle text-secondary  rounded-pill" style="font-size: 10px;"><%=zyzdForm.getZyz() %></span>
                                        <span class="badge ti ti-activity-heartbeat text-secondary  rounded-pill" style="margin-left:2px;font-size: 10px;" onclick="javascript:alert('更新中，敬请期待');">干净度?</span>
                                    </td>
                                    <%} %>
                                    
                                    <td class="cursor-pointer py-1" data-seq="<%=zyzdForm.getSeq_no_yx() %>" title="">
                                        <div class="d-flex align-items-center">
                                            <%
                                            List<LhyForm> zyzList = MAP_LIST.get(zyzdForm.getSeq_no_yx());
                                            %>
                                            <i class="bi bi-info-circle-fill text-info me-2 major-detail-icon" 
                                               style="font-size: 12px; cursor: pointer;" 
                                               data-seq="<%=zyzdForm.getSeq_no_yx()%>"
                                               data-major-count="<%=zyzList != null ? zyzList.size() : 0%>"
                                               title="点击查看专业详情（共<%=zyzList != null ? zyzList.size() : 0%>个专业）"></i>
                                            
                                            <!-- 根据is96显示不同内容 -->
                                            <%if(is96){ %>
                                                <div class="d-flex flex-column">
                                                    <span class="fw-bold text-truncate mb-1 text-dark" style="font-size: 12px;"><%=zyzdForm.getZymc_org() %></span>
                                                    <div class="d-flex gap-1 small">
                                                        <span class="<%=Tools.getInt(qsf_cz_a) > 0 ? "text-success" : "text-danger"%>" style="font-size: 10px;"><%=Tools.viewScore(zyzdForm.getZdfwc_a()) %></span>
                                                        <span class="<%=Tools.getInt(qsf_cz_a) > 0 ? "text-success" : "text-danger"%>" style="font-size: 10px;font-weight: bold;">(<%=qsf_cz_a %>)</span> |
                                                        <span class="<%=Tools.getInt(qsf_cz_b) > 0 ? "text-success" : "text-danger"%>" style="font-size: 10px;"><%=Tools.viewScore(zyzdForm.getZdfwc_b())  %></span>
                                                        <span class="<%=Tools.getInt(qsf_cz_b) > 0 ? "text-success" : "text-danger"%>" style="font-size: 10px;font-weight: bold;">(<%=qsf_cz_b %>)</span> |
                                                        <span class="<%=Tools.getInt(qsf_cz_c) > 0 ? "text-success" : "text-danger"%>" style="font-size: 10px;"><%=Tools.viewScore(zyzdForm.getZdfwc_c())  %></span>
                                                        <span class="<%=Tools.getInt(qsf_cz_c) > 0 ? "text-success" : "text-danger"%>" style="font-size: 10px;font-weight: bold;">(<%=qsf_cz_c %>)</span> 
                                                    </div>
                                                </div>
                                            <%}else{ %>
                                                <%if(zyzdFormLowest != null){ %>
                                                    <%if(!is96){ %>
                                                    <span class="badge text-success text-warning me-2" style="font-size: 10px;">第 <%=zyzdFormLowest.getSeq_no_zy() %> 专业</span>
                                                    <%} %>
                                                    <div class="d-flex flex-column">
                                                    <span class="fw-bold text-truncate mb-1 text-dark" style="font-size: 12px;"><%=zyzdForm.getZymc_org() %></span>
                                                    <div class="d-flex gap-1 small">
                                                        <span class="<%=Tools.getInt(qsf_cz_a) > 0 ? "text-success" : "text-danger"%>" style="font-size: 10px;"><%=Tools.viewScore(zyzdFormLowest.getZdfwc_a()) %></span>
                                                        <span class="<%=Tools.getInt(qsf_cz_a) > 0 ? "text-success" : "text-danger"%>" style="font-size: 10px;font-weight: bold;">(<%=qsf_cz_a %>)</span> |
                                                        <span class="<%=Tools.getInt(qsf_cz_b) > 0 ? "text-success" : "text-danger"%>" style="font-size: 10px;"><%=Tools.viewScore(zyzdFormLowest.getZdfwc_b())  %></span>
                                                        <span class="<%=Tools.getInt(qsf_cz_b) > 0 ? "text-success" : "text-danger"%>" style="font-size: 10px;font-weight: bold;">(<%=qsf_cz_b %>)</span> |
                                                        <span class="<%=Tools.getInt(qsf_cz_c) > 0 ? "text-success" : "text-danger"%>" style="font-size: 10px;"><%=Tools.viewScore(zyzdFormLowest.getZdfwc_c())  %></span>
                                                        <span class="<%=Tools.getInt(qsf_cz_c) > 0 ? "text-success" : "text-danger"%>" style="font-size: 10px;font-weight: bold;">(<%=qsf_cz_c %>)</span> 
                                                    </div>
                                                </div> 
                                                <%}else{ %>
                                                    <span class="text-muted">-</span>
                                                <%} %>
                                            <%} %>
                                        </div>
                                    </td>
                                    
                                    <td class="text-center py-1">
                                        <%if(score_waste_cnt == 999999){ %>
                                            <span class="badge bg-warning-subtle text-warning-emphasis d-flex align-items-center justify-content-center" style="font-size: 10px;">
                                                <i class="bi bi-lightning me-1"></i>冲击(数据不全)
                                            </span>
                                        <%}else if(score_waste_cnt== 999998){ %>
                                            <span class="badge bg-danger-subtle text-warning-emphasis d-flex align-items-center justify-content-center" style="font-size: 10px;">
                                                <i class="bi bi-x-circle me-1"></i>冲击(大小年)
                                            </span>
                                        <%}else if(score_waste_cnt >= 5){ %>
                                            <span class="badge bg-success-subtle text-success-emphasis d-flex align-items-center justify-content-center" style="font-size: 10px;">
                                                <i class="bi bi-check-circle me-1"></i>超分
                                            </span>
                                        <%}else if(score_waste_cnt >= 0){ %>
                                            <span class="badge bg-success-subtle text-info-emphasis d-flex align-items-center justify-content-center" style="font-size: 10px;">
                                                <i class="bi bi-check-circle me-1"></i>分数接近
                                            </span>
                                        <%}else{ %>
                                            <span class="badge bg-info-subtle text-danger-emphasis d-flex align-items-center justify-content-center" style="font-size: 10px;">
                                                <i class="bi bi-dash-circle me-1"></i>分数不够
                                            </span>
                                        <%} %>
                                    </td>
                                    
                                    <td class="text-center py-1">
                                        <button type="button" 
                                                class="btn btn-outline-danger btn-sm delete-btn" 
                                                style="font-size: 10px; line-height: 1; padding: 2px 6px;"
                                                onclick="MM_TB_form_adjust_yx_delete_forDrag('<%=zyzdForm.getId()%>');"
                                                title="删除该志愿">
                                            <i class="bi bi-trash me-1"></i>删除
                                        </button>
                                    </td>
                                </tr>
                                
                                <tr id="SROT_SUB_SEQ_YX_NO_<%=zyzdForm.getSeq_no_yx()%>" style="display:none;">
                                	<td colspan="<%=is96?5:6%>" class="p-2 bg-light">
                                		<div class="text-center text-muted">
                                			<i class="bi bi-arrow-clockwise me-2"></i>加载中...
                                		</div>
                                	</td>
                                </tr>
                                <%
                                index++ ;} %>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 隐藏的专业数据 -->
<div id="majorDataContainer" style="display: none;">
<%
for(Map.Entry<Integer, List<LhyForm>> entry : MAP_LIST.entrySet()) {
    Integer seqNoYx = entry.getKey();
    List<LhyForm> majorList = entry.getValue();
%>
    <div class="major-data-group" data-seq="<%=seqNoYx%>">
    <%
    for(LhyForm major : majorList) {
        String majorName = major.getZymc_org() != null ? major.getZymc_org() : "";
    %>
        <div class="major-data-item"
             data-seq-zy="<%=major.getSeq_no_zy()%>"
             data-zymc="<%=majorName%>"
             data-zdf-a="<%=major.getZdf_a()%>"
             data-zdfwc-a="<%=major.getZdfwc_a()%>"
             data-pjf-a="<%=major.getPjf_a()%>"
             data-pjfwc-a="<%=major.getPjfwc_a()%>"
             data-jhs-a="<%=major.getJhs_a()%>"
             data-zdf-b="<%=major.getZdf_b()%>"
             data-zdfwc-b="<%=major.getZdfwc_b()%>"
             data-pjf-b="<%=major.getPjf_b()%>"
             data-pjfwc-b="<%=major.getPjfwc_b()%>"
             data-jhs-b="<%=major.getJhs_b()%>"
             data-zdf-c="<%=major.getZdf_c()%>"
             data-zdfwc-c="<%=major.getZdfwc_c()%>"
             data-pjf-c="<%=major.getPjf_c()%>"
             data-pjfwc-c="<%=major.getPjfwc_c()%>"
             data-jhs-c="<%=major.getJhs_c()%>">
        </div>
    <%
    }
    %>
    </div>
<%
}
%>
</div>

<!-- 存储is96值的隐藏字段 -->
<input type="hidden" id="is96Value" value="<%=is96%>">

<!-- 存储学生信息的隐藏字段 -->
<input type="hidden" id="stuName" value="<%=lhyOrder.getStu_info_name()%>">
<input type="hidden" id="stuProvince" value="<%=provinceConfig.getP_name()%>">
<input type="hidden" id="stuSubject" value="<%=Tools.viewXK(lhyOrder.getStu_score_xk())%>">
<input type="hidden" id="stuScore" value="<%=lhyOrder.getStu_score_cj()%>">
<input type="hidden" id="stuRank" value="<%=lhyOrder.getStu_score_wc()%>">
<input type="hidden" id="stuBatch" value="<%=lhyOrder.getPc()%>">

<style>

/* 基础表格样式 */
.compact-table { font-size: 11px; }
.compact-table td, .compact-table th { 
    padding: 0.25rem 0.4rem !important; 
    vertical-align: middle; 
    line-height: 1.2; 
}

/* 表格行下边框线 - 笔记本效果 */
.compact-table tbody tr {
    border-bottom: 1px solid #e8eaed !important;
    transition: all 0.15s ease;
}

/* 悬停时的边框效果 - 增强优先级 */
.compact-table tbody tr.item_popup_drag_seq_no_yx:hover {
    border-bottom: 1px solid #FF0000 !important;
    box-shadow: 0 1px 0 #FF0000 !important;
}

/* 特殊状态行的边框颜色 */
.table-success-light {
    border-bottom: 1px solid #d1e7dd !important;
}

.table-warning-light {
    border-bottom: 1px solid #fff3cd !important;
}

.table-danger-light {
    border-bottom: 1px solid #f8d7da !important;
}

/* 高亮行的特殊边框 */
.highlight-row {
    border-bottom: 2px solid #ffc107 !important;
}

/* 状态背景色 */
.table-success-light { background-color: rgba(25, 135, 84, 0.08) !important; }
.table-warning-light { background-color: rgba(255, 193, 7, 0.08) !important; }
.table-danger-light { background-color: rgba(220, 53, 69, 0.08) !important; }

/* 交互效果 */
.move-btn:hover { transform: scale(1.05); box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
.item_popup_drag_seq_no_yx:hover {
    background-color: rgba(13, 202, 240, 0.05) !important;
    transform: translateY(-0.5px);
}
.cursor-pointer:hover {
    background-color: rgba(13, 202, 240, 0.05) !important;
}
.cursor-pointer { cursor: pointer; }
.major-detail-icon:hover { transform: scale(1.1); transition: transform 0.2s ease; }

/* 动画效果 */
@keyframes moveAnimation { 0%, 100% { transform: translateX(0); } 50% { transform: translateX(2px); } }
.moving { animation: moveAnimation 0.2s ease-in-out; }

@keyframes pulseHighlight {
    0%, 100% { background-color: rgba(255, 193, 7, 0.3); box-shadow: 0 0 15px rgba(255, 193, 7, 0.5); transform: scale(1); }
    50% { background-color: rgba(255, 193, 7, 0.5); box-shadow: 0 0 25px rgba(255, 193, 7, 0.7); transform: scale(1.01); }
}
.highlight-row {
    background-color: rgba(255, 193, 7, 0.3) !important;
    border: 2px solid #ffc107 !important;
    box-shadow: 0 0 15px rgba(255, 193, 7, 0.5) !important;
    animation: pulseHighlight 1.5s ease-in-out infinite !important;
    transition: all 0.3s ease-in-out !important;
    position: relative !important;
    z-index: 10 !important;
}

.highlight-row::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid #ff9800;
    border-radius: 4px;
    animation: borderPulse 1.5s ease-in-out infinite;
    z-index: -1;
}

@keyframes borderPulse {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

/* 响应式 */
@media (max-width: 1200px) { .compact-table { font-size: 10px; } }

/* Toast容器 */
.toast-container { z-index: 9999; }

/* Sortable 拖动样式 */
.sortable-placeholder {
    background-color: rgba(0, 123, 255, 0.1) !important;
    border: 2px dashed #007bff !important;
    height: 60px !important;
    visibility: visible !important;
    position: relative !important;
}

.sortable-placeholder td {
    border: none !important;
    background: transparent !important;
}

.sortable-active {
    background-color: rgba(255, 193, 7, 0.2) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    transform: rotate(1deg) !important;
    z-index: 1000 !important;
    opacity: 0.9 !important;
    border: 1px solid #007bff !important;
}

.ui-sortable-helper {
    background-color: #fff !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 4px !important;
    z-index: 9999 !important;
    opacity: 0.95 !important;
}

/* Ensure cells within the helper table maintain their appearance */
.ui-sortable-helper td {
    background-color: inherit !important;
    color: inherit !important;
    border-right: 1px solid #dee2e6 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    padding: 0.25rem 0.4rem !important; 
    vertical-align: middle !important; 
}

.ui-sortable-helper td:last-child {
    border-right: none !important;
}

/* Styles for the actual row being dragged (sortable-active) if needed */
.sortable-active {
    opacity: 0.5 !important; 
}

/* 拖动时隐藏相邻的隐藏行，避免干扰 */
.sortable-active + tr[id^="SROT_SUB_SEQ_YX_NO_"] {
    display: none !important;
}

/* 增强表格行的过渡效果 */
.item_popup_drag_seq_no_yx {
    transition: all 0.15s ease !important;
    position: relative !important;
}

/* 拖动准备状态 */
.ui-sortable tbody tr.item_popup_drag_seq_no_yx {
    cursor: move !important;
}

.ui-sortable tbody tr.item_popup_drag_seq_no_yx:hover {
    background-color: rgba(13, 202, 240, 0.08) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* 专业详情排序相关样式 */
.cursor-move {
    cursor: move !important;
}

.major-sortable-row {
    transition: all 0.2s ease !important;
}

.major-sortable-row:hover {
    background-color: rgba(13, 202, 240, 0.08) !important;
}

.sortable-handle {
    opacity: 0.5;
    cursor: move;
    transition: opacity 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
}

.sortable-handle:hover {
    opacity: 1;
}

.sortable-major-placeholder {
    background-color: rgba(13, 202, 240, 0.1) !important;
    border: 2px dashed #0dcaf0 !important;
    visibility: visible !important;
}

.sorting-active {
    background-color: rgba(255, 193, 7, 0.2) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    border: 1px solid #ffc107 !important;
    z-index: 100;
}

#majorDetailSortableTable thead {
    z-index: 10;
    position: sticky;
    top: 0;
    background-color: #fff;
}

/* 删除按钮样式 */
.delete-btn {
    transition: all 0.2s ease !important;
    border-radius: 3px !important;
}

.delete-btn:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3) !important;
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
}

.delete-btn:active {
    transform: scale(0.98) !important;
}

.delete-btn:disabled {
    opacity: 0.6 !important;
    transform: none !important;
    box-shadow: none !important;
}

/* 学生信息框样式 */
.student-info-box {
    line-height: 1.4;
    max-width: 500px;
    border-top: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.student-info-box span {
    display: inline-block;
    vertical-align: middle;
}

#stuInfoName {
    font-weight: 600;
    margin-right: 6px;
}

#stuInfoProvince {
    margin-right: 2px;
    color: rgba(255, 255, 255, 0.85);
}

#stuInfoSubject {
    margin-left: 2px;
    margin-right: 6px;
    color: rgba(255, 255, 255, 0.85);
}

#stuInfoScore {
    font-weight: 600;
    color: #fff;
    margin-right: 2px;
}

#stuInfoRank {
    font-size: 10px !important;
    color: rgba(255, 255, 255, 0.7);
}

#stuInfoBatch {
    margin-left: 3px;
    color: rgba(255, 255, 255, 0.85);
}
</style>

<script>
// 全局变量
var pageMapList = {};
var isMoving = false;
var global_batch_id = "<%=batch_id%>";

// 业务函数
function MM_sort_popup_get_all_sub_major(seq_no_yx) {
    showMajorDetailModal(seq_no_yx);
}

// 带动画的移动函数
function moveWithAnimation(form_id, current_seq, target_seq, buttonElement) {
    if (isMoving) return;
    isMoving = true;
    
    var $button = $(buttonElement);
    var originalHtml = $button.html();
    var $row = $button.closest('tr');
    
    // 设置加载状态
    $button.html('<i class="bi bi-arrow-clockwise"></i>').prop('disabled', true);
    $row.addClass('moving');
    
    MM_form_adjust_move(form_id, current_seq, target_seq, true);
    
    setTimeout(function() {
        // 调用后端移动函数
        
        
        setTimeout(function() {
            $button.html(originalHtml).prop('disabled', false);
            $row.removeClass('moving');
            showToast('第 ' + current_seq + ' 志愿调整到第 ' + target_seq + ' 志愿成功！', 'success');
            
            // 延迟一点时间后高亮显示目标行
            setTimeout(function() {
                highlightTargetRow(target_seq);
            }, 300);
            
            isMoving = false;
        }, 500);
    }, 200);
}

// 高亮目标志愿行
function highlightTargetRow(target_seq) {
    var $targetRow = $('tr[popup_drag_seq_no_yx="' + target_seq + '"]');
    if ($targetRow.length === 0) return;
    
    // 移除所有行的高亮效果
    $('.item_popup_drag_seq_no_yx').removeClass('highlight-row');
    
    // 保存原始背景类
    var originalClasses = $targetRow.attr('class');
    var backgroundClass = '';
    
    if (originalClasses.indexOf('table-success-light') !== -1) {
        backgroundClass = 'table-success-light';
    } else if (originalClasses.indexOf('table-warning-light') !== -1) {
        backgroundClass = 'table-warning-light';
    } else if (originalClasses.indexOf('table-danger-light') !== -1) {
        backgroundClass = 'table-danger-light';
    }
    
    // 移除背景类并添加高亮类
    $targetRow.removeClass('table-success-light table-warning-light table-danger-light')
              .addClass('highlight-row');
    
    // 滚动到目标行
    $targetRow[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
    
    // 1秒后恢复原始背景
    setTimeout(function() {
        $targetRow.removeClass('highlight-row');
        if (backgroundClass) $targetRow.addClass(backgroundClass);
    }, 1000);
}

// Toast 提示函数
function showToast(message, type) {
    if (!type) type = 'info';
    // console.log('显示Toast:', message, type);
    
    // 移除现有的toast容器
    $('#toast-container').remove();
    
    // 创建新的toast容器
    var $toastContainer = $('<div id="toast-container" class="position-fixed top-0 end-0 p-2" style="z-index: 9999;"></div>');
    $('body').append($toastContainer);
    
    var bgClass = 'bg-primary';
    var iconClass = 'info-circle';
    var delay = 1500;
    
    if (type === 'success') {
        bgClass = 'bg-warning';
        iconClass = 'check-circle';
        delay = 1000;
    } else if (type === 'error') {
        bgClass = 'bg-danger';
        iconClass = 'x-circle';
    }
    
    var toastId = 'toast-' + Date.now();
    
    var $toast = $('<div id="' + toastId + '" class="toast align-items-center text-white ' + bgClass + ' border-0" role="alert" aria-live="assertive" aria-atomic="true">' +
        '<div class="d-flex">' +
            '<div class="toast-body">' +
                '<i class="bi bi-' + iconClass + ' me-2"></i>' + message +
            '</div>' +
            '<button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>' +
        '</div>' +
    '</div>');
    
    $toastContainer.append($toast);
    
    // 使用Bootstrap Toast API
    try {
        var toast = new bootstrap.Toast($toast[0], { 
            autohide: true, 
            delay: delay 
        });
        toast.show();
        
        $toast.on('hidden.bs.toast', function() { 
            $(this).remove(); 
        });
    } catch (e) {
        console.error('Bootstrap Toast错误:', e);
        // 降级处理：直接显示并自动隐藏
        $toast.show();
        setTimeout(function() {
            $toast.fadeOut(function() {
                $(this).remove();
            });
        }, delay);
    }
}

// 专业详情模态框
function showMajorDetailModal(seqNo) {
    var modalElement = document.getElementById('majorDetailModal');
    if (!modalElement) {
        console.error('未找到专业详情模态框元素');
        return;
    }
    
    // 存储当前显示的院校序号，供保存排序时使用
    $('#majorDetailModal').data('current-seq-no', seqNo);
    
    // 更新模态框标题，显示志愿序号
    $('#majorDetailModalLabel').html('<i class="bi bi-mortarboard me-2"></i>专业详情 <span class="badge bg-light text-primary ms-2">第' + seqNo + '志愿</span>');
    
    // 填充学生信息
    $('#stuInfoName').text($('#stuName').val());
    $('#stuInfoProvince').text($('#stuProvince').val());
    $('#stuInfoSubject').text($('#stuSubject').val());
    $('#stuInfoScore').text($('#stuScore').val());
    $('#stuInfoRank').text($('#stuRank').val());
    $('#stuInfoBatch').text('(' + $('#stuBatch').val() + ')');
    
    var modal = new bootstrap.Modal(modalElement);
    modal.show();
    
    $('#majorDetailContent').html(
        '<div class="text-center p-4">' +
            '<div class="spinner-border text-primary" role="status">' +
                '<span class="visually-hidden">加载中...</span>' +
            '</div>' +
            '<p class="mt-2 text-muted">正在加载专业详情...</p>' +
        '</div>'
    );
    
    setTimeout(function() {
        loadMajorDetailContent(seqNo);
    }, 200);
}

// 设置当前院校序号到Session
function setCurrentSeqNoYxSession(seqNo, callback) {
    // 简单的内联方式设置Session，使用一个很小的ajax请求
    $.ajax({
        type: "POST",
        url: "<%=request.getContextPath()%>/lhy/ajax_lhy_tb_form_search_by_yx_seq_no.jsp",
        data: {
            "batch_id": "<%=lhyFormMain.getBatch_id()%>",
            "seq_no_yx": seqNo,
            "rd": new Date().getTime()
        },
        dataType: "HTML",
        success: function(result) {
            // Session设置成功后执行回调
            if (callback && typeof callback === 'function') {
                callback();
            }
        },
        error: function(e) {
            console.error('设置Session出错:', e);
            // 即使出错也执行回调
            if (callback && typeof callback === 'function') {
                callback();
            }
        }
    });
}

// 从页面现有数据加载专业详情内容（支持动态更新的缓存）
function loadMajorDetailContent(seqNo) {
    // 从隐藏字段获取is96的值
    var is96 = $('#is96Value').val() === 'true';
    
    // 获取年份信息（从页面中获取）
    var latestJhYear = parseInt('<%= LATEST_JH_YEAR %>');
    var years = [
        { year: latestJhYear - 1, suffix: 'a', className: 'bg-primary-subtle' },
        { year: latestJhYear - 2, suffix: 'b', className: 'bg-info-subtle' },
        { year: latestJhYear - 3, suffix: 'c', className: 'bg-secondary-subtle' }
    ];
    
    var majorData = getMajorDataBySeq(seqNo, years);
    
    if (majorData && majorData.length > 0) {        
        // 动态生成专业详情表格
        generateMajorDetailTable(majorData, is96);
    } else {
        $('#majorDetailContent').html(
            '<div class="text-center p-4">' +
                '<i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>' +
                '<h5 class="mt-3">暂无专业信息</h5>' +
                '<p class="text-muted">该志愿暂时没有专业详情数据</p>' +
            '</div>'
        );
    }
}

// 初始化专业详情排序功能
function initializeMajorDetailSortable() {
    $("#majorDetailSortableTbody").sortable({
        items: "tr.major-sortable-row",
        cursor: "move",
        axis: "y",
        handle: ".sortable-handle",
        placeholder: "sortable-major-placeholder",
        forcePlaceholderSize: true,
        opacity: 0.7,
        tolerance: "pointer",
        helper: function(e, tr) {
            var $originals = tr.children();
            var $helper = tr.clone();
            $helper.children().each(function(index) {
                $(this).width($originals.eq(index).width());
            });
            return $helper;
        },
        start: function(event, ui) {
            ui.item.addClass("sorting-active");
            ui.placeholder.height(ui.item.height());
        },
        stop: function(event, ui) {
            ui.item.removeClass("sorting-active");
            
            // 更新序号显示
            updateMajorSequenceNumbers();
            
            // 添加保存提示
            $('#saveMajorSortOrderBtn').addClass('btn-warning').removeClass('btn-primary')
                .html('<i class="bi bi-exclamation-circle me-1"></i>点击保存排序');
        }
    }).disableSelection();
    
    // 保存按钮事件
    $(document).on('click', '#saveMajorSortOrderBtn', function() {
        // 只有当按钮处于警告状态（有未保存更改）时才能保存
        if ($(this).hasClass('btn-warning')) {
            saveMajorSortOrder();
        } else {
            // 如果是正常状态，提示用户
            showToast('当前没有需要保存的更改', 'info');
        }
    });
}

// 更新专业序号显示
function updateMajorSequenceNumbers() {
    $('#majorDetailSortableTbody tr.major-sortable-row').each(function(index) {
        // 更新显示的序号为当前位置+1
        $(this).find('td:first .badge').text(index + 1);
    });
}

// 更新专业排序顺序（更新页面静态数据缓存）
function updateMajorDataContainer(seqNo, sortedSeqZyArray) {
    var $majorGroup = $('#majorDataContainer .major-data-group[data-seq="' + seqNo + '"]');
    if ($majorGroup.length === 0) return;
    
    // 获取所有专业数据项
    var $majorItems = $majorGroup.find('.major-data-item');
    var majorItemsMap = {};
    
    // 建立seq-zy到DOM元素的映射
    $majorItems.each(function() {
        var seqZy = $(this).data('seq-zy');
        majorItemsMap[seqZy] = $(this).clone();
    });
    
    // 清空当前专业组
    $majorGroup.empty();
    
    // 按照新的顺序重新添加专业项
    for (var i = 0; i < sortedSeqZyArray.length; i++) {
        var seqZy = sortedSeqZyArray[i];
        if (majorItemsMap[seqZy]) {
            $majorGroup.append(majorItemsMap[seqZy]);
        }
    }
    
    console.log('已更新院校序号 ' + seqNo + ' 的专业数据容器，新顺序：', sortedSeqZyArray);
}

// 保存专业排序
function saveMajorSortOrder() {
    // 获取当前显示的院校序号
    var currentSeqNo = $('#majorDetailModal').data('current-seq-no');
    if (!currentSeqNo) {
        showToast('获取院校信息失败，请重新打开专业详情', 'error');
        return;
    }
    
    // 获取当前排序
    var sortedSeqZyArray = [];
    $('#majorDetailSortableTbody tr.major-sortable-row').each(function() {
        sortedSeqZyArray.push($(this).data('seq-zy'));
    });
    
    if (sortedSeqZyArray.length === 0) {
        showToast('没有找到专业数据', 'error');
        return;
    }
    
    // 设置保存中状态
    var $saveBtn = $('#saveMajorSortOrderBtn');
    $saveBtn.prop('disabled', true)
           .removeClass('btn-primary btn-warning')
           .addClass('btn-secondary')
           .html('<i class="bi bi-arrow-clockwise me-1"></i>保存中...');
    
    // 先设置当前院校序号到Session
    setCurrentSeqNoYxSession(currentSeqNo, function() {
        // 直接调用AJAX页面进行保存，确保能够获得真实的保存状态
        $.ajax({
            type: "POST",
            url: "<%=request.getContextPath()%>/lhy/ajax_lhy_tb_form_search_by_yx_seq_no_do_adjust_seq_zy.jsp",
            data: {
                "new_seq": sortedSeqZyArray.join(","),
                "rd": new Date().getTime()
            },
            dataType: "HTML",
            success: function(result) {
                var resultHTML = $.trim(result);
                
                // 检查登录状态 (使用字符串拼接避免父页面误判)
                if(resultHTML.indexOf("ERR:" + "LOGIN:REDIRECT-X_LOGIN") != -1){ 
                    location.href = "<%=request.getContextPath()%>/lhy/e_login.jsp";
                    return;
                }
                
                // 检查各种错误状态
                if(resultHTML.indexOf("ERR:" + "OPERATION_FAST_LIMIT") != -1){
                    showToast('操作过于频繁，请稍候再试', 'warning');
                    restoreButtonState($saveBtn, false);
                    return;
                }
                
                if(resultHTML.indexOf("ERR:" + "NOT_FOUND") != -1){
                    showToast('会话信息丢失，请重新操作', 'error');
                    restoreButtonState($saveBtn, false);
                    return;
                }
                
                if(resultHTML.indexOf("ERR:" + "EXCEED") != -1){
                    showToast('参数错误，保存失败', 'error');
                    restoreButtonState($saveBtn, false);
                    return;
                }
                
                // 检查是否返回了院校序号（表示保存成功）
                var savedSeqNo = parseInt(resultHTML);
                if(savedSeqNo > 0 && savedSeqNo == currentSeqNo) {
                    // 保存成功：更新页面数据并显示成功提示
                    updateMajorDataContainer(currentSeqNo, sortedSeqZyArray);
                    showToast('专业志愿排序已保存', 'success');
                    restoreButtonState($saveBtn, true);
                } else {
                    // 保存失败
                    showToast('保存失败，请重试', 'error');
                    restoreButtonState($saveBtn, false);
                }
            },
            error: function(xhr, status, error) {
                console.error('保存专业排序时发生错误:', error);
                showToast('网络错误，保存失败', 'error');
                restoreButtonState($saveBtn, false);
            }
        });
    });
}

// 恢复按钮状态的辅助函数
function restoreButtonState($saveBtn, isSuccess) {
    $saveBtn.prop('disabled', false)
           .removeClass('btn-secondary');
    
    if (isSuccess) {
        $saveBtn.removeClass('btn-warning')
               .addClass('btn-primary')
               .html('<i class="bi bi-check-circle me-1"></i>保存排序');
    } else {
        $saveBtn.addClass('btn-warning')
               .removeClass('btn-primary')
               .html('<i class="bi bi-exclamation-circle me-1"></i>点击保存排序');
    }
}

// 获取专业数据
function getMajorDataBySeq(seqNo, years) {
    var $majorGroup = $('#majorDataContainer .major-data-group[data-seq="' + seqNo + '"]');
    
    if ($majorGroup.length === 0) {
        return [];
    }
    
    var $majorItems = $majorGroup.find('.major-data-item');
    var result = [];
    
    $majorItems.each(function(index) {
        var $item = $(this);
        var seqZy = parseInt($item.data('seq-zy')) || 0;
        var name = $item.data('zymc') || '未知专业';
        
        // 获取数据的辅助函数
        var getScore = function(key) {
            var value = parseInt($item.data(key));
            return (value && value > 0) ? value : '-';
        };
        
        var getRank = function(key) {
            var value = parseInt($item.data(key));
            return (value && value > 0) ? '(' + value + ')' : '';
        };
        
        // 构建年份数据
        var yearDataArray = [];
        years.forEach(function(yearInfo) {
            var yearData = {
                year: yearInfo.year,
                plan: getScore('jhs-' + yearInfo.suffix),
                score: getScore('zdf-' + yearInfo.suffix),
                rank: getRank('zdfwc-' + yearInfo.suffix),
                avg: getScore('pjf-' + yearInfo.suffix),
                avgRank: getRank('pjfwc-' + yearInfo.suffix)
            };
            yearDataArray.push(yearData);
        });
        
        var majorData = {
            seqNo: seqZy,
            name: name,
            years: yearDataArray
        };
        
        result.push(majorData);
    });
    
    return result;
}

// 注：移除了parseMajorDataFromHTML函数，因为我们不再从AJAX HTML中解析数据
// 现在直接使用页面中的数据属性

// 生成专业详情表格
function generateMajorDetailTable(majorData, is96) {
    // 获取年份信息
    var latestJhYear = parseInt('<%= LATEST_JH_YEAR %>');
    var years = [
        { year: latestJhYear - 1, suffix: 'a', className: 'bg-primary-subtle' },
        { year: latestJhYear - 2, suffix: 'b', className: 'bg-info-subtle' },
        { year: latestJhYear - 3, suffix: 'c', className: 'bg-secondary-subtle' }
    ];
    
    // 生成表头
    var headerYears = '';
    years.forEach(function(yearInfo) {
        headerYears += '<th colspan="3" class="text-center border ' + yearInfo.className + ' fw-bold" style="border-width: 1px !important;">' + 
            yearInfo.year + '年</th>';
    });
    
    // 生成表格行
    var tableRows = '';
    for (var i = 0; i < majorData.length; i++) {
        var major = majorData[i];
        // 根据is96决定是否添加可排序的类
        var rowClass = i % 2 === 0 ? 
            'class="table-light ' + (!is96 ? 'major-sortable-row' : '') + '"' : 
            'class="' + (!is96 ? 'major-sortable-row' : '') + '"';
        
        // 使用当前索引+1作为显示序号，保留原始seq_zy用于数据操作
        var displaySeqNo = i + 1;
        
        var row = '<tr ' + rowClass + ' style="border-width: 1px !important;" data-seq-zy="' + major.seqNo + '">' +
            '<td class="text-center border" style="border-width: 1px !important;">' +
                '<div class="d-flex align-items-center justify-content-center">' +
                    '<span class="badge bg-primary rounded-pill me-2">' + displaySeqNo + '</span>' +
                    // 根据is96决定是否显示拖动手柄
                    (is96 ? '' : '<div class="sortable-handle cursor-move"><i class="bi bi-grip-vertical"></i></div>') +
                '</div>' +
            '</td>' +
            '<td class="fw-medium border" style="border-width: 1px !important;">' + major.name + '</td>';
        
        // 为每一年添加数据列
        major.years.forEach(function(yearData) {
            row += '<td class="text-center small border bg-light" style="border-width: 1px !important;">' +
                '<span class="text-success fw-bold">' + yearData.plan + '</span>' +
            '</td>' +
            '<td class="text-center small border" style="border-width: 1px !important;">' +
                '<span class="text-danger fw-bold">' + yearData.score + '</span>' +
                (yearData.rank ? '<br><span class="text-muted">' + yearData.rank + '</span>' : '') +
            '</td>' +
            '<td class="text-center small border" style="border-width: 1px !important;">' +
                '<span class="text-info fw-bold">' + yearData.avg + '</span>' +
                (yearData.avgRank ? '<br><span class="text-muted">' + yearData.avgRank + '</span>' : '') +
            '</td>';
        });
        
        row += '</tr>';
        tableRows += row;
    }
    
    // 生成最终HTML，根据is96决定是否显示保存排序按钮
    var finalHtml = '<div class="p-3 bg-light rounded shadow-sm">' +
        '<div class="alert alert-info d-flex align-items-center mb-3 shadow-sm">' +
            '<i class="bi bi-info-circle-fill me-2 fs-5"></i>' +
            '<div>' +
                '<strong>专业列表</strong> <span class="badge bg-primary ms-2">共 ' + majorData.length + ' 个专业</span>' +
                '<br><small class="text-muted">以下是该院校专业组的详细分数信息' + (is96 ? '' : '，可通过拖动调整专业志愿顺序') + '</small>' +
            '</div>' +
        '</div>' +
        '<div class="table-responsive shadow-sm rounded">' +
            '<table class="table table-bordered table-hover table-sm mb-0" id="majorDetailSortableTable">' +
                '<thead class="sticky-top">' +
                    '<tr class="bg-white">' +
                        '<th rowspan="2" class="text-center align-middle border" style="width: 8%; border-width: 1px !important;">志愿序号</th>' +
                        '<th rowspan="2" class="text-center align-middle border" style="width: 25%; border-width: 1px !important;">专业名称</th>' +
                        headerYears +
                    '</tr>' +
                    '<tr class="bg-white">' +
                        years.map(function() {
                            return '<th class="text-center small border py-2" style="border-width: 1px !important;">计划数</th>' +
                                   '<th class="text-center small border py-2" style="border-width: 1px !important;">最低分</th>' +
                                   '<th class="text-center small border py-2" style="border-width: 1px !important;">平均分</th>';
                        }).join('') +
                    '</tr>' +
                '</thead>' +
                '<tbody id="majorDetailSortableTbody" class="bg-white">' + tableRows + '</tbody>' +
            '</table>' +
        '</div>' +
        // 只有在非is96模式下才显示保存排序按钮
        (!is96 ? '<div class="mt-3 text-end">' +
            '<button type="button" class="btn btn-primary btn-sm" id="saveMajorSortOrderBtn">' +
                '<i class="bi bi-check-circle me-1"></i>保存排序' +
            '</button>' +
        '</div>' : '') +
    '</div>';
    
    $('#majorDetailContent').html(finalHtml);
    
    // 只有在非is96模式下才初始化排序功能
    if (!is96) {
        initializeMajorDetailSortable();
    }
}

// 事件绑定
$(document).ready(function() {
    // 专业详情点击事件
    $(document).on('click', '.cursor-pointer', function() {
        var seq = $(this).data('seq');
        if (seq) MM_sort_popup_get_all_sub_major(seq);
    });
    
    // 专业详情图标点击事件
    $(document).on('click', '.major-detail-icon', function(e) {
        e.stopPropagation();
        var seqNo = $(this).data('seq');
        if (seqNo) showMajorDetailModal(seqNo);
    });

    // 初始化表格行排序
    $("#adjust_drag_yx_exampleModalScrollable_content_sort_table tbody").sortable({  
        items: "tr.item_popup_drag_seq_no_yx",  // 明确指定只拖动主要数据行
        cursor: "move",  // 拖动时显示移动光标
        axis: "y",  // 限制只能在垂直方向拖动
        containment: "parent",  // 限制在父元素内拖动
        opacity: 0.7,  // 拖动时的透明度
        placeholder: "sortable-placeholder",  // 占位符样式
        forcePlaceholderSize: true,  // 强制占位符有尺寸
        tolerance: "pointer", // 使用指针位置来确定排序位置，对复杂布局更友好
        distance: 10, // 开始拖动前需要移动的距离（像素）
        delay: 10,  // 延迟时间（毫秒）
        refreshPositions: true,  // 每次移动时重新计算位置
        cursorAt: { top: -10 }, // 将光标定位在拖动助手顶部下方10像素的位置
        helper: function(e, tr) {
            // 创建一个克隆元素，保持原始宽度和结构
            var $originals = tr.children();
            var $helperRow = tr.clone(); 
            
            // 设置helper的宽度和样式
            $helperRow.children().each(function(index) {
                if (index < $originals.length) {
                    $(this).width($originals.eq(index).width());
                }
            });
            
            // 为helper添加table结构以保持正确显示
            var $table = $('<table class="table table-hover table-sm mb-0 compact-table" style="width: ' + tr.closest('table').width() + 'px; border-collapse: collapse;"></table>');
            var $tbody = $('<tbody></tbody>');
            $tbody.append($helperRow);
            $table.append($tbody);
            
            return $table;
        },
        start: function(event, ui) {
            // 拖动开始时添加样式 
            ui.item.addClass("sortable-active");
            ui.placeholder.height(ui.item.height()); // 设置占位符高度
            // 保存原始索引
            ui.item.data("start_pos", ui.item.index());
            
            // 添加全局样式以强制所有元素使用move光标
            $("<style id='popup-sort-cursor-style'>body, html { cursor: move !important; }</style>").appendTo("body");
        },	
        stop: function(event, ui) {
            // 拖动结束时移除样式
            ui.item.removeClass("sortable-active");  
            // 移除全局样式
            $("#popup-sort-cursor-style").remove();
            
            // 确保鼠标光标恢复正常
            setTimeout(function() {
                $("body").css("cursor", "");
                $("html").css("cursor", "");
                $(document).css("cursor", "");
            }, 100);
        },
        update: function(event, ui) {
            // 确保拖动结束后的位置是正确的
            var startPos = ui.item.data("start_pos");
            var endPos = ui.item.index();
            
            // 如果位置没有变化，不做处理
            if (startPos == endPos) return;
            
            // 确保这是由用户真实拖动触发的，而不是程序初始化
            if (!ui.item.hasClass("sortable-active")) return;
            
            var global_sort_array_for_popup_drag = []; // 用于存储排序后的数据
            $(".item_popup_drag_seq_no_yx").each(function() {
                global_sort_array_for_popup_drag.push($(this).attr('popup_drag_seq_no_yx')); 
            }); 
            
            // 延迟一点时间执行，确保拖动动画完成
            setTimeout(function() {
                MM_form_adjust_seq_yx_by_drag(global_batch_id, global_sort_array_for_popup_drag.join(","));
            }, 100);
        } 
    });
    
    // 防止拖动时选中文本
    $("#adjust_drag_yx_exampleModalScrollable_content_sort_table").disableSelection();
    
    $('#majorDetailModal').on('shown.bs.modal', function() {
        $(this).find('.modal-dialog').draggable({
            handle: '.modal-header',  
            cursor: "move"          
        });
    });
    
});

</script>

<!-- 专业详情模态框 -->
<div class="modal fade" id="majorDetailModal" tabindex="-1" aria-labelledby="majorDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <div class="d-flex flex-column">
                    <h5 class="modal-title mb-1" id="majorDetailModalLabel">
                        <i class="bi bi-mortarboard me-2"></i>专业详情
                    </h5>
                    <div class="student-info-box border-top border-light pt-2 mt-1" style="font-size: 14px; color: #e9f5ff;">
                        <span id="stuInfoName" style="font-size: 16px;"></span>
                        <span id="stuInfoProvince" style="font-size: 12px;"></span>/<span id="stuInfoSubject" style="font-size: 12px;"></span>
                        <span id="stuInfoScore" style="font-size: 14px;"></span>
                        <span id="stuInfoRank" class="text-white-50" style="margin-left: 1px; font-size: 10px;"></span>
                        <span id="stuInfoBatch" style="font-size: 12px;"></span>
                    </div>
                </div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                <div id="majorDetailContent">
                    <div class="text-center p-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2 text-muted">正在加载专业详情...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary btn-sm" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-1"></i>关闭
                </button>
            </div>
        </div>
    </div>
</div>
<%
long end = System.currentTimeMillis();
Tools.println((end - start) + ", drag page time used");%> 