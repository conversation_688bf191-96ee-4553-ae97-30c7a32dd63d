package com.career.utils.liuxue;

import java.util.Date;

public class NCSSJOBBean {
	
	private String jobName;
	private String highMonthPay;
	private String updateDate;
	private String lowMonthPay;
	private String headCount;
	private String memberLevel;
	private String recruitType;
	private String publishDate;
	private String degreeName;
	private String recName;
	private String recLogo;
	private String areaCodeName;
	private String jobId;
	private String recScale;
	private String sortPriority;
	private String sourcesNameCh;
	private String sourcesType;
	private String recTags;
	private String major;
	private String recProperty;
	private String userType;
	private String recId;
	private String keyUnits;
	private String sourcesName;
	private Date last_update_tm;
	public String getJobName() {
		return jobName;
	}
	public void setJobName(String jobName) {
		this.jobName = jobName;
	}
	public String getHighMonthPay() {
		return highMonthPay;
	}
	public void setHighMonthPay(String highMonthPay) {
		this.highMonthPay = highMonthPay;
	}
	public String getUpdateDate() {
		return updateDate;
	}
	public void setUpdateDate(String updateDate) {
		this.updateDate = updateDate;
	}
	public String getLowMonthPay() {
		return lowMonthPay;
	}
	public void setLowMonthPay(String lowMonthPay) {
		this.lowMonthPay = lowMonthPay;
	}
	public String getHeadCount() {
		return headCount;
	}
	public void setHeadCount(String headCount) {
		this.headCount = headCount;
	}
	public String getMemberLevel() {
		return memberLevel;
	}
	public void setMemberLevel(String memberLevel) {
		this.memberLevel = memberLevel;
	}
	public String getRecruitType() {
		return recruitType;
	}
	public void setRecruitType(String recruitType) {
		this.recruitType = recruitType;
	}
	public String getPublishDate() {
		return publishDate;
	}
	public void setPublishDate(String publishDate) {
		this.publishDate = publishDate;
	}
	public String getDegreeName() {
		return degreeName;
	}
	public void setDegreeName(String degreeName) {
		this.degreeName = degreeName;
	}
	public String getRecName() {
		return recName;
	}
	public void setRecName(String recName) {
		this.recName = recName;
	}
	public String getRecLogo() {
		return recLogo;
	}
	public void setRecLogo(String recLogo) {
		this.recLogo = recLogo;
	}
	public String getAreaCodeName() {
		return areaCodeName;
	}
	public void setAreaCodeName(String areaCodeName) {
		this.areaCodeName = areaCodeName;
	}
	public String getJobId() {
		return jobId;
	}
	public void setJobId(String jobId) {
		this.jobId = jobId;
	}
	public String getRecScale() {
		return recScale;
	}
	public void setRecScale(String recScale) {
		this.recScale = recScale;
	}
	public String getSortPriority() {
		return sortPriority;
	}
	public void setSortPriority(String sortPriority) {
		this.sortPriority = sortPriority;
	}
	public String getSourcesNameCh() {
		return sourcesNameCh;
	}
	public void setSourcesNameCh(String sourcesNameCh) {
		this.sourcesNameCh = sourcesNameCh;
	}
	public String getSourcesType() {
		return sourcesType;
	}
	public void setSourcesType(String sourcesType) {
		this.sourcesType = sourcesType;
	}
	public String getRecTags() {
		return recTags;
	}
	public void setRecTags(String recTags) {
		this.recTags = recTags;
	}
	public String getMajor() {
		return major;
	}
	public void setMajor(String major) {
		this.major = major;
	}
	public String getRecProperty() {
		return recProperty;
	}
	public void setRecProperty(String recProperty) {
		this.recProperty = recProperty;
	}
	public String getUserType() {
		return userType;
	}
	public void setUserType(String userType) {
		this.userType = userType;
	}
	public String getRecId() {
		return recId;
	}
	public void setRecId(String recId) {
		this.recId = recId;
	}
	public String getKeyUnits() {
		return keyUnits;
	}
	public void setKeyUnits(String keyUnits) {
		this.keyUnits = keyUnits;
	}
	public String getSourcesName() {
		return sourcesName;
	}
	public void setSourcesName(String sourcesName) {
		this.sourcesName = sourcesName;
	}
	public Date getLast_update_tm() {
		return last_update_tm;
	}
	public void setLast_update_tm(Date last_update_tm) {
		this.last_update_tm = last_update_tm;
	}
	
	public String generateSQL() {
		String SQL = "insert into career_ext_url_jobncss(jobName,highMonthPay,updateDate,lowMonthPay,headCount,memberLevel,recruitType,publishDate,degreeName,recName,recLogo,areaCodeName,jobId,recScale,sortPriority,sourcesNameCh,sourcesType,recTags,major,recProperty,userType,recId,keyUnits,sourcesName,last_update_tm) "
				+ "values('" + jobName + "', '" + highMonthPay + "', '" + updateDate + "','" + lowMonthPay + "', '" + headCount + "', '" + memberLevel + "', '" + recruitType + "','" + publishDate + "', '" + degreeName +"', '"+recName+"', '"+recLogo+"','"+areaCodeName+"','"
				+jobId+"','"+recScale+"','"+sortPriority+"','"+sourcesNameCh+"','"+sourcesType+"','"+recTags+"','"+major+"','"+recProperty+"','"+userType+"','"+recId+"','"+keyUnits+"','"+sourcesName+"',NOW());";
		return SQL;
	}
	

	
	
	
	
	
	

}
