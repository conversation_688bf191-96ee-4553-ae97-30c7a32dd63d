package com.career.utils;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class DealZDKS_CQ {

	public static void main(String args[]) throws Exception {
		
		runZHUANKEWK();
		run2BENWK();
		run1BENWK();
		
		runZHUANKE();
		run2BEN();
		run1BEN();
	}
	
	public static void runSC(String city, String zdpc, String KL, int erBen, int yiBen, int ZGF) {
		
	}
	
	
	public static void runZHUANKE() { //专科
		List<String> list = runConvertLatest("四川", "凉山","一诊", "理科", 180, 382, 180, 433 );
		StringBuffer sb = new StringBuffer();
		for(String str : list) {
			sb.append(str);
		}
		writeTempFile(new File("E://YYY_SC_LS_L3.txt"), sb);
	}
	
	public static void run2BEN() { //专科到2本
		List<String> list = runConvertLatest("四川", "凉山","一诊", "理科", 382, 470, 433, 520 );
		StringBuffer sb = new StringBuffer();
		for(String str : list) {
			sb.append(str);
		}
		writeTempFile(new File("E://YYY_SC_LS_L2.txt"), sb);
	}
	
	public static void run1BEN() { //1本-最高
		List<String> list = runConvertLatest("四川", "凉山","一诊", "理科", 470, 670, 520, 690 );
		StringBuffer sb = new StringBuffer();
		for(String str : list) {
			sb.append(str);
		}
		writeTempFile(new File("E://YYY_SC_LS_L1.txt"), sb);
	}
	
	public static void runZHUANKEWK() { //专科
		List<String> list = runConvertLatest("四川", "凉山","一诊", "文科", 180, 410, 180, 458 );
		StringBuffer sb = new StringBuffer();
		for(String str : list) {
			sb.append(str);
		}
		writeTempFile(new File("E://YYY_SC_PZH_W3.txt"), sb);
	}
	
	public static void run2BENWK() { //专科到2本
		List<String> list = runConvertLatest("四川", "凉山","一诊", "文科", 410, 472, 458, 527 );
		StringBuffer sb = new StringBuffer();
		for(String str : list) {
			sb.append(str);
		}
		writeTempFile(new File("E://YYY_SC_PZH_W2.txt"), sb);
	}
	
	public static void run1BENWK() { //1本-最高
		List<String> list = runConvertLatest("四川", "凉山","一诊", "文科", 472, 635, 527, 630 );
		StringBuffer sb = new StringBuffer();
		for(String str : list) {
			sb.append(str);
		}
		writeTempFile(new File("E://YYY_SC_PZH_W1.txt"), sb);
	}
	
	
	
	public static List<String> runConvertLatest(String sf, String city, String zdpc, String kl, int scoreZDFrom, int scoreZDTo, int gaokao23Start, int gaokao23To) {
		List<String> list = new ArrayList<>();

		double newStart = gaokao23Start;
		for(int i = scoreZDFrom;i < scoreZDTo;i++) {
			newStart = Arith.add(newStart, Arith.div(gaokao23To - gaokao23Start, scoreZDTo - scoreZDFrom)); 
			if(newStart > gaokao23To) {
				newStart = gaokao23To;
			}
			list.add("INSERT INTO zdks_score_convert(nf, sf, city, ZDPC, KL, SCORE_FROM, SCORE_TO) VALUES(2024, '"+sf+"', '"+city+"', '"+zdpc+"', '"+kl+"', "+i+","+Math.round(newStart)+" );\r\n");
			
		}
		
		return list;
		
	}

	
	public static List<String> runConvert(String sf, String city, String zdpc, String kl, int pc, int scoreFrom, int scoreTo) {
		List<String> list = new ArrayList<>();
		
		int highest2023 = 0;
		int YIBen2023 = 0;
		int ERBen2023 = 0;
		int ZHUANKe2023 = 0;

		
		if(kl.equals("文科")) {
			highest2023 = 636;
			YIBen2023 = 527;
			ERBen2023 = 458;
			ZHUANKe2023 = 150;
		}else if(kl.equals("理科")) {
			highest2023 = 698;
			YIBen2023 = 520;
			ERBen2023 = 433;
			ZHUANKe2023 = 150;
		}else {
			
		}
		
		if(pc == 1) {
			double score2023Start = YIBen2023;
			for(int i = scoreFrom;i < scoreTo;i++) {
				score2023Start = Arith.add(score2023Start, Arith.div(highest2023 - YIBen2023, scoreTo - scoreFrom)); 
				if(score2023Start > highest2023) {
					score2023Start = highest2023;
				}
				list.add("INSERT INTO zdks_score_convert(nf, sf, city, ZDPC, KL, SCORE_FROM, SCORE_TO) VALUES(2024, '"+city+"', '"+city+"', '"+zdpc+"', '"+kl+"', "+i+","+Math.round(score2023Start)+" );\r\n");
				
			}
		}else if(pc == 2) {
			double score2023Start = ERBen2023;
			for(int i = scoreFrom;i < scoreTo;i++) {
				score2023Start = Arith.add(score2023Start, Arith.div(YIBen2023 - ERBen2023, scoreTo - scoreFrom));
				if(score2023Start > YIBen2023) {
					score2023Start = YIBen2023;
				}
				list.add("INSERT INTO zdks_score_convert(nf, sf, city, ZDPC, KL, SCORE_FROM, SCORE_TO) VALUES(2024, '"+city+"', '"+city+"', '"+zdpc+"', '"+kl+"', "+i+","+Math.round(score2023Start)+" );\r\n");
			}
		}else if(pc == 3) { //专科
			double score2023Start = ZHUANKe2023;
			for(int i = scoreFrom;i < scoreTo;i++) {
				score2023Start = Arith.add(score2023Start, Arith.div(ERBen2023 - ZHUANKe2023, scoreTo - scoreFrom));
				if(score2023Start > ERBen2023) {
					score2023Start = ERBen2023;
				}
				list.add("INSERT INTO zdks_score_convert(nf, sf, city, ZDPC, KL, SCORE_FROM, SCORE_TO) VALUES(2024, '"+city+"', '"+city+"', '"+zdpc+"', '"+kl+"', "+i+","+Math.round(score2023Start)+" );\r\n");
			}
		}
		return list;
		
	}
	
	
	public static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
}
