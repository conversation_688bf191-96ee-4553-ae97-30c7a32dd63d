<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ page import="com.zsdwf.utils.*,com.zsdwf.db.*,com.career.utils.*,java.util.*,com.career.utils.Tools,com.career.db.DealZdksSQL,com.career.db.CityBean,com.zsdwf.db.JDBC"%>

<%@include file="/WEB-INF/include/_session_admin_ajax.jsp"%>
<%
int nf = Tools.getInt(request.getParameter("nf"));
String sf = Tools.trim(request.getParameter("prov"));
String city = Tools.trim(request.getParameter("city"));
String zdpc = Tools.trim(request.getParameter("zdpc"));
String hxb_id = Tools.trim(request.getParameter("hxb_id"));
int last_la = 0, last_l = 0, last_wa = 0, last_w = 0;
List<String> SQL_ALL = new ArrayList<>();
String xk_wl = "物理", xk_ls = "历史";

if("北京海南上海山东天津浙江".indexOf(sf) != -1){
	xk_wl = "综合";
	xk_ls = "综合";
}

String proviceTongkao = "北京天津上海重庆".indexOf(sf) != -1? "全市统考" : "全省统考";

List<CityBean> cityList = com.career.db.JDBC.HM_PROVINCE_CITY.get(sf);
if(city.indexOf("统考") == -1){
	cityList = new ArrayList<>();
	CityBean cityBean = new CityBean();
	cityBean.setCityNameExt(city);
	cityList.add(cityBean);
}

SQL_ALL.add(DealZdksSQL.generatDeleteSQL(nf, sf, city.indexOf("统考") != -1 ? null : city, zdpc));

String batch_id = UUID.randomUUID().toString();
for(CityBean bn : cityList){	
	for(int i=1;i<=8;i++){
		
		String pcx_name = com.career.utils.Tools.trim(request.getParameter("PCX_" + i));
		//理科/物理
		int cur_la = com.career.utils.Tools.getInt(request.getParameter("L_A_" + i)); //高考
		int cur_l = com.career.utils.Tools.getInt(request.getParameter("L_" + i));    //诊断
		
		//文科/历史
		int cur_wa = com.career.utils.Tools.getInt(request.getParameter("W_A_" + i));
		int cur_w = com.career.utils.Tools.getInt(request.getParameter("W_" + i));
		
		if(cur_la < 100 || cur_l < 100 || cur_wa < 100 || cur_w < 100){
			break;
		}
		
		com.career.db.ZyzdZdksConvertHistory bean = new com.career.db.ZyzdZdksConvertHistory();
		bean.setBatch_id(batch_id);
		bean.setCity(bn.getCityNameExt());
		bean.setNf(nf);
		bean.setSf(sf);
		bean.setZdks_name(zdpc);
		bean.setPcx_name(pcx_name);
		bean.setPcx_wl_zd(cur_l);
		bean.setPcx_wl_gk(cur_la);
		bean.setPcx_ls_zd(cur_w);
		bean.setPcx_ls_gk(cur_wa);
		bean.setHxb(hxb_id);
		new com.career.db.ZyzdJDBC().insertZdksConvertHistory(bean);
		
		if(last_la > 0){ 
			SQL_ALL.addAll(DealZdksSQL.runConvert(nf, sf, bn.getCityNameExt(), zdpc, xk_wl, last_l, cur_l, last_la, cur_la, hxb_id, batch_id));
		}
		last_la = cur_la;
		last_l = cur_l;
		
		
		
		if(last_wa > 0){
			if(!xk_wl.equals(xk_ls)){
				SQL_ALL.addAll(DealZdksSQL.runConvert(nf, sf, bn.getCityNameExt(), zdpc, xk_ls, last_w, cur_w, last_wa, cur_wa, hxb_id, batch_id));
			}
		}
		last_wa = cur_wa;
		last_w = cur_w;
	}
	SQL_ALL.addAll(DealZdksSQL.runConvert(nf, sf, bn.getCityNameExt(), zdpc, xk_wl, last_l, last_l + 30, last_la, last_la + 1, hxb_id, batch_id));
	if(!xk_wl.equals(xk_ls)){
		SQL_ALL.addAll(DealZdksSQL.runConvert(nf, sf, bn.getCityNameExt(), zdpc, xk_ls, last_w, last_w + 30, last_wa, last_wa + 1, hxb_id, batch_id));
	}
}

StringBuffer SQL_STR = new StringBuffer();
for(int i=0;i<SQL_ALL.size();i++){
	SQL_STR.append(SQL_ALL.get(i)+"\r\n");
}
session.setAttribute("SES_ADMIN_ZDKS_GENERATE_SQL", SQL_ALL);
%>

<textarea style="width:1200px;height:600px;font-size:12px;"> 
<%=SQL_STR %>
</textarea>



