<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
    <%@ page import="com.career.utils.*,com.career.db.*,java.util.*" %>
<div class="header-content-right">
           <!-- Start::header-element -->
           <div class="header-element country-selector">
               <!-- Start::header-link|dropdown-toggle -->
               <a href="javascript:void(0);" class="header-link dropdown-toggle" data-bs-auto-close="outside" data-bs-toggle="dropdown" onclick="MM_fast_change_province_page();">  
                   <span style="font-size:14px;font-weight:normal;color:#343a40"> 
                   <%
                   
                   CardBean cardHeaderTemp = (CardBean)session.getAttribute("SES_CARD_SC");
                   SAASConfig sAASConfigTemp = (SAASConfig)session.getAttribute(com.career.utils.ZyzdCache.SES_SAAS_CONFIG);
                   if(sAASConfigTemp == null){
                	   response.sendRedirect(request.getContextPath()+"/latest/t_login.jsp");
                	   return;
                   }
                   String provinceNameHeaderTemp = ZyzdCache.getUserCardProvince(cardHeaderTemp.getProv()).getName();
                   
                   String allowedProv = Tools.trim(cardHeaderTemp.getAllowedProv());
                   boolean onlyOneProv = false;
                   if(Tools.isEmpty(allowedProv) || "ALL".equalsIgnoreCase(allowedProv)){
                   %>
                   <span class="badge bg-danger-transparent ms-2" >全国切换</span>
                   <%}else if("ONLY".equalsIgnoreCase(allowedProv)){ onlyOneProv = true; }else{
                   	String[] allowedProvArray = allowedProv.split(","); 
                   %>
                   	<span class="badge bg-danger-transparent ms-2"><%=allowedProvArray.length %></span>
                   <%} %>
                   <%if(Tools.isCardFormal(cardHeaderTemp)){ %>
                   <b style="font-size:12px;color:red;font-weight:bold;">全年VIP</b>&nbsp;
                   <%} %>
                   <%if(Tools.trim(sAASConfigTemp.getServer_name()).indexOf("zk789") != -1 || Tools.trim(sAASConfigTemp.getServer_name()).indexOf("dwf360") != -1){ %> 
                   
                   <%}else{ %>
                   <%=provinceNameHeaderTemp %>.<%=cardHeaderTemp.getCity() %>
                   <%} %>
                   </span>
               </a>
               <script>
               function MM_fast_change_province_page(){
            	   $("#MM_popup_fast_change_id").html("");
            	   $.ajax({
            	        type : "POST",
            	        url : "<%=request.getContextPath()%>/latest/ajax_t_fast_change_province_page.jsp",
            			data: {
            				'rd' : new Date().getTime()
            			}, 
            			dataType: "HTML",
            	        //请求成功
            	        success : function(result) {
            	 			resultHTML = $.trim(result);
            	 			if(resultHTML.indexOf("ERR:EXCEED") != -1){
            	        		MM_show_alert_message("当前操作过于频繁，请休息一会重试。");
            	        		return;
            	        	}
            	        	
            	        	if(resultHTML.indexOf("ERR:LOGIN:REDIRECT-X_LOGIN") != -1){
            	        		location.href = "<%=request.getContextPath()%>/latest/t_login.jsp";
            	        		return;
            	        	}
            	        	$("#MM_popup_fast_change_id").html(resultHTML);
            	 			
            	        },
            	        //请求失败，包含具体的错误信息
            	        error : function(e){
            	        	console.log(e.status);
            	            console.log(e.responseText);
            	            //location.href = "<%=request.getContextPath()%>/latest/t_login.jsp";
            	        }
            	    });
               }
               
               
               function MM_fast_change_personal_page(){
            	   $("#MM_popup_fast_change_id").html("");
            	   $.ajax({
            	        type : "POST",
            	        url : "<%=request.getContextPath()%>/latest/ajax_t_fast_change_personal_page.jsp",
            			data: {
            				'rd' : new Date().getTime()
            			}, 
            			dataType: "HTML",
            	        //请求成功
            	        success : function(result) {
            	 			resultHTML = $.trim(result);
            	 			if(resultHTML.indexOf("ERR:EXCEED") != -1){
            	        		MM_show_alert_message("当前操作过于频繁，请休息一会重试。");
            	        		return;
            	        	}
            	        	
            	        	if(resultHTML.indexOf("ERR:LOGIN:REDIRECT-X_LOGIN") != -1){
            	        		location.href = "<%=request.getContextPath()%>/latest/t_login.jsp";
            	        		return;
            	        	}
            	        	
            	        	$("#MM_popup_fast_change_id").html(resultHTML);
            	 			
            	 			
            	        },
            	        //请求失败，包含具体的错误信息
            	        error : function(e){
            	        	console.log(e.status);
            	            console.log(e.responseText);
            	            //location.href = "<%=request.getContextPath()%>/latest/t_login.jsp";
            	        }
            	    });
               }
               
               function isNumber(value) {         //验证是否为数字
            	    var patrn = /^(-)?\d+(\.\d+)?$/;
            	    if (patrn.exec(value) == null || value == "") {
            	        return false
            	    } else {
            	        return true
            	    }
            	}

            	function MM_modify(){
            		
            		var selected_city = $("#selected_city").val();
            		var selected_nf = $("#selected_nf").val();
            		var selected_xk = $("#selected_xk").val();
            		var input_cj = $("#signin-cj").val(); 
            		var input_wc = $("#signin-wc").val(); 
            		
            		if(input_wc == "" || input_wc.length < 1 || input_wc.length > 7 || !isNumber(input_wc)){
            			alert("请输入1~7位整数位次");
            			return false;
            		}
            		
            		
            		
            		var MSG_FOR_CONFIRM = "您选择开通 [" + selected_nf + ", " + selected_city + "] 的规划卡，选科类型为 [" + selected_xk + "]，确定吗？";
            		
            		<%if(cardHeaderTemp.getYear() < 2000 && !Tools.isCardSCA(cardHeaderTemp)){ %>
            		if(confirm(MSG_FOR_CONFIRM)){
            		<%}else{%>
            		if(true){
            		<%}%>
            			$.ajax({
            		        type : "POST",
            		        url : "<%=request.getContextPath()%>/latest/ajax_personal_modify.jsp",
            				data: {"selected_nf" : selected_nf, "selected_city" : selected_city , "selected_xk" : selected_xk, "input_wc" : input_wc}, 
            				//dataType: "json",
            		        //请求成功
            		        success : function(result) {
            		        	var resultHTML = $.trim(result);
            		        	if(resultHTML.indexOf("ERR:LOGIN:REDIRECT-X_LOGIN") != -1){
            		        		location.href = "<%=request.getContextPath()%>/latest/ajax_t_login.jsp";
            		        		return;
            		        	}
            		        	
            		        	if(resultHTML.indexOf("SUCCSS:redirect") != -1){
            		        		location.href = "<%=request.getRequestURL()%>";
            		        	}else{
            		        		if(resultHTML.indexOf("ERR:EXCEED") != -1){
            		        			alert("本月修改次数超过30次，暂停修改功能！");
            		        		}else{
            		        			alert(result);
            		        		}
            		        	}
            		        },
            		        //请求失败，包含具体的错误信息
            		        error : function(e){
            		            console.log(e.status);
            		            console.log(e.responseText);
            		        }
            		    });
            		}
            	}


            	function MM_calWC(){
            		var selected_xk = $("#selected_xk").val();
            		var input_cj = $("#signin-cj").val(); 
            		
            		if(input_cj.length > 3){
            			$("#signin-cj").val(input_cj.substring(0,3));
            		}
            		
            		var isThreeDigitNumber = /^\d{3}$/.test(input_cj);
            		if(!isThreeDigitNumber){
            			return false;
            		}
            		
            		$.ajax({
            	        type : "POST",
            	        url : "<%=request.getContextPath()%>/latest/ajax_getwc_by_score.jsp",
            			data: {
            				"input_cj" : input_cj, 
            				"selected_xk" : selected_xk,
            				"rd" : new Date().getTime()
            			}, 
            			//dataType: "json",
            	        //请求成功
            	        success : function(result) {
            	        	if($.trim(result).indexOf("NO.FOUND") != -1){
            	        		return;
            	        	}
            	        	$("#signin-wc").val($.trim(result));
            	        	$("#signin-cj").focus();
            	        },
            	        //请求失败，包含具体的错误信息
            	        error : function(e){
            	            console.log(e.status);
            	            console.log(e.responseText);
            	        }
            	    });
            	}

            	function MM_xk_change(){
            		$("#signin-cj").val("");
            		$("#signin-wc").val("");
            	}
               </script>
               <%if(Tools.trim(sAASConfigTemp.getServer_name()).indexOf("zk789") != -1 || Tools.trim(sAASConfigTemp.getServer_name()).indexOf("dwf360") != -1){ %> 
               <a href="javascript:void(0);" style="margin-right:1px;" class="header-link dropdown-toggle" >    
               	<img alt="" src="<%=request.getContextPath()%>/sources/<%=sAASConfigTemp.getPage_logo_name() %>" style="width:120px;height:15px;">    
               </a> 
               <%} %>
               <a href="javascript:void(0);" style="margin-right:1px;" class="header-link dropdown-toggle" data-bs-auto-close="outside" data-bs-toggle="dropdown" onclick="MM_fast_change_personal_page();">    
               	<b style='font-size:12px;color:#6f42c1;margin-right:2px;' id="MM_HEADER_SCORE_VIEW"><%=Tools.viewXK(cardHeaderTemp.getXk()) %> <%=cardHeaderTemp.getScore_org() %><span class="text-muted fs-10" style="margin-left:1px;margin-top:2px;"><%=cardHeaderTemp.getScore() %></span> </b>
               	  
               </a> 
               
                                    
               <div class="modal fade" id="fast_change_score_popup" tabindex="-5" aria-labelledby="fast_change_score_popup" style="display: none;" data-backdrop="static" aria-hidden="false"> 
                      <div class="modal-dialog">
                          <div class="modal-content">
                              <div class="modal-header">
                                  <h6 class="modal-title">Modal title</h6>
                                  <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                              </div>
                              <div class="modal-body">
                                  ...
                              </div>
                              <div class="modal-footer">
                                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                  <button type="button" class="btn btn-primary">Save
                                      changes</button>
                              </div>
                          </div>
                      </div>
                  </div>
               
               
               <!-- End::header-link|dropdown-toggle -->
               <ul class="main-header-dropdown dropdown-menu dropdown-menu-end" data-popper-placement="none" style="padding:2px;background-color:gray;width:400px;" id="MM_popup_fast_change_id">
               	
                </ul>
                
             </div>
             <!-- End::header-element -->
       </div>