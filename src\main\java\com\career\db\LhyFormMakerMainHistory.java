package com.career.db;

import java.util.Date;

import com.career.utils.BaseBean;

public class LhyFormMakerMainHistory extends BaseBean {
	
	public static void main(String args[]) {
		LhyFormMakerMainHistory bean = new LhyFormMakerMainHistory();
		printBeanProperties(bean);
	}
	
	
    private String historyId;
    private Date storedTm;
    private int storedSeqNo;
    private String batchId;
    private int scoreCj;
    private int scoreWc;
    private String scoreXk;
    private String fNo;
    private String orderId;
    private String selectedZymc;
    private String pc;
    private String pc_code;
    private int nf;
    private Date createTm;
    private Date lastUpdateTm;
    private int joinedCheckerCnt;
    private String lastCheckerId;
    
    
    public String getPc_code() {
		return pc_code;
	}
	public void setPc_code(String pc_code) {
		this.pc_code = pc_code;
	}
	public String getHistoryId() {
		return historyId;
	}
	public void setHistoryId(String historyId) {
		this.historyId = historyId;
	}
	public Date getStoredTm() {
		return storedTm;
	}
	public void setStoredTm(Date storedTm) {
		this.storedTm = storedTm;
	}
	public int getStoredSeqNo() {
		return storedSeqNo;
	}
	public void setStoredSeqNo(int storedSeqNo) {
		this.storedSeqNo = storedSeqNo;
	}
	public String getBatchId() {
		return batchId;
	}
	public void setBatchId(String batchId) {
		this.batchId = batchId;
	}
	public int getScoreCj() {
		return scoreCj;
	}
	public void setScoreCj(int scoreCj) {
		this.scoreCj = scoreCj;
	}
	public int getScoreWc() {
		return scoreWc;
	}
	public void setScoreWc(int scoreWc) {
		this.scoreWc = scoreWc;
	}
	public String getScoreXk() {
		return scoreXk;
	}
	public void setScoreXk(String scoreXk) {
		this.scoreXk = scoreXk;
	}
	public String getfNo() {
		return fNo;
	}
	public void setfNo(String fNo) {
		this.fNo = fNo;
	}
	public String getOrderId() {
		return orderId;
	}
	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}
	public String getSelectedZymc() {
		return selectedZymc;
	}
	public void setSelectedZymc(String selectedZymc) {
		this.selectedZymc = selectedZymc;
	}
	public String getPc() {
		return pc;
	}
	public void setPc(String pc) {
		this.pc = pc;
	}
	public int getNf() {
		return nf;
	}
	public void setNf(int nf) {
		this.nf = nf;
	}
	public Date getCreateTm() {
		return createTm;
	}
	public void setCreateTm(Date createTm) {
		this.createTm = createTm;
	}
	public Date getLastUpdateTm() {
		return lastUpdateTm;
	}
	public void setLastUpdateTm(Date lastUpdateTm) {
		this.lastUpdateTm = lastUpdateTm;
	}
	public int getJoinedCheckerCnt() {
		return joinedCheckerCnt;
	}
	public void setJoinedCheckerCnt(int joinedCheckerCnt) {
		this.joinedCheckerCnt = joinedCheckerCnt;
	}
	public String getLastCheckerId() {
		return lastCheckerId;
	}
	public void setLastCheckerId(String lastCheckerId) {
		this.lastCheckerId = lastCheckerId;
	}
	public String getLastCheckerName() {
		return lastCheckerName;
	}
	public void setLastCheckerName(String lastCheckerName) {
		this.lastCheckerName = lastCheckerName;
	}
	public String getLastCheckerRemark() {
		return lastCheckerRemark;
	}
	public void setLastCheckerRemark(String lastCheckerRemark) {
		this.lastCheckerRemark = lastCheckerRemark;
	}
	public int getStatus() {
		return status;
	}
	public void setStatus(int status) {
		this.status = status;
	}
	public int getfType() {
		return fType;
	}
	public void setfType(int fType) {
		this.fType = fType;
	}
	public String getFormName() {
		return formName;
	}
	public void setFormName(String formName) {
		this.formName = formName;
	}


	private String lastCheckerName;
    private String lastCheckerRemark;
    private int status;
    private int fType;
    private String formName;
    
}
