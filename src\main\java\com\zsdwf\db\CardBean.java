package com.zsdwf.db;

import java.util.Date;

public class CardBean {
  private String id;
  
  private String passwd;
  
  private String prov;
  
  private String xk;
  
  private int score;
  
  private Date create;
  
  private Date active;
  
  private Date lastLogin;
  
  private int year;
  
  private int status;
  private String openID;
  
  public String getOpenID() {
	return openID;
}

public void setOpenID(String openID) {
	this.openID = openID;
}

public String getXk() {
    return this.xk;
  }
  
  public void setXk(String xk) {
    this.xk = xk;
  }
  
  public String getId() {
    return this.id;
  }
  
  public void setId(String id) {
    this.id = id;
  }
  
  public String getPasswd() {
    return this.passwd;
  }
  
  public void setPasswd(String passwd) {
    this.passwd = passwd;
  }
  
  public String getProv() {
    return this.prov;
  }
  
  public void setProv(String prov) {
    this.prov = prov;
  }
  
  public int getScore() {
    return this.score;
  }
  
  public void setScore(int score) {
    this.score = score;
  }
  
  public Date getCreate() {
    return this.create;
  }
  
  public void setCreate(Date create) {
    this.create = create;
  }
  
  public Date getActive() {
    return this.active;
  }
  
  public void setActive(Date active) {
    this.active = active;
  }
  
  public Date getLastLogin() {
    return this.lastLogin;
  }
  
  public void setLastLogin(Date lastLogin) {
    this.lastLogin = lastLogin;
  }
  
  public int getYear() {
    return this.year;
  }
  
  public void setYear(int year) {
    this.year = year;
  }
  
  public int getStatus() {
    return this.status;
  }
  
  public void setStatus(int status) {
    this.status = status;
  }
}
