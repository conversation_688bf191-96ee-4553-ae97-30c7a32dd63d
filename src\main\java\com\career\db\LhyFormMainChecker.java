package com.career.db;

import java.sql.Timestamp;

/**
 * 志愿表审核记录Bean
 * 对应数据表：lhy_a1_ah_form_maker_main_checker
 */
public class LhyFormMainChecker {
    private int mc_id;          		// 主键ID
    private int mc_result;      		// 审核结果：1-通过，0-未通过
    private String fm_id;       		// 表单ID
    private String c_id;        		// 审核人ID
    private String remark;      		// 审核评语
    private Timestamp create_tm;  		// 创建时间
    private String to_checker_remark; 	// 提交审核的备注
    
    public String getTo_checker_remark() {
		return to_checker_remark;
	}

	public void setTo_checker_remark(String to_checker_remark) {
		this.to_checker_remark = to_checker_remark;
	}

	public LhyFormMainChecker() {
    }
    
    public LhyFormMainChecker(int mc_result, String fm_id, String c_id, String to_checker_remark, String remark) {
        this.mc_result = mc_result;
        this.fm_id = fm_id;
        this.c_id = c_id;
        this.remark = remark;
        this.to_checker_remark = to_checker_remark;
    }
    
    public int getMc_id() {
        return mc_id;
    }
    
    public void setMc_id(int mc_id) {
        this.mc_id = mc_id;
    }
    
    public int getMc_result() {
        return mc_result;
    }
    
    public void setMc_result(int mc_result) {
        this.mc_result = mc_result;
    }
    
    public String getFm_id() {
        return fm_id;
    }
    
    public void setFm_id(String fm_id) {
        this.fm_id = fm_id;
    }
    
    public String getC_id() {
        return c_id;
    }
    
    public void setC_id(String c_id) {
        this.c_id = c_id;
    }
    
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
    
    public Timestamp getCreate_tm() {
        return create_tm;
    }
    
    public void setCreate_tm(Timestamp create_tm) {
        this.create_tm = create_tm;
    }
}