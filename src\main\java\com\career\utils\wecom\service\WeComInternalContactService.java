package com.career.utils.wecom.service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.career.utils.Tools;
import com.career.utils.wecom.WeComHttpClient;
import com.career.utils.wecom.WeComTokenManager;
import com.career.utils.wecom.config.WeComConfig;
import com.career.utils.wecom.exception.WeComApiException;
import com.career.utils.wecom.model.WeComInternalContact;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.HashSet;

/**
 * 企业微信内部成员管理服务类
 */
public class WeComInternalContactService {

    private static final String GET_USER_LIST_URL = "https://qyapi.weixin.qq.com/cgi-bin/user/list";
    private static final String GET_USER_DETAIL_URL = "https://qyapi.weixin.qq.com/cgi-bin/user/get";

    /**
     * 获取企业所有内部联系人的完整信息（支持增量同步优化）
     * 
     * @param corpId 企业ID
     * @param corpSecret 通讯录同步应用的Secret
     * @param departmentId 部门ID
     * @param isIncremental 是否增量同步
     * @param existingUserIds 已存在的用户ID集合（增量同步时使用）
     * @return 内部联系人完整信息列表
     */
    public static List<WeComInternalContact> getAllInternalContacts(String corpId, String corpSecret, 
            Long departmentId, boolean isIncremental, Set<String> existingUserIds) {
        
        if (WeComConfig.isEnableDetailedPhaseLogging()) {
            Tools.println("使用传统user/list接口获取内部联系人信息");
        }
        
        List<WeComInternalContact> contacts = new ArrayList<>();
        
        try {
            // 获取access_token
            String accessToken = WeComTokenManager.getAccessToken(corpId, corpSecret);
            
            // 使用传统接口获取完整的用户信息
            Long deptId = (departmentId != null) ? departmentId : 1L;
            String url = String.format("%s?access_token=%s&department_id=%d&fetch_child=1", 
                                     GET_USER_LIST_URL, accessToken, deptId);
            
            JSONObject response = WeComHttpClient.doGetWithRetry(url);
            
            // 根据配置输出API响应原始数据
            if (WeComConfig.isLogApiRawJsonResponse()) {
                Tools.println("=== 获取内部联系人列表API原始响应数据 ===");
                Tools.println("部门ID: " + deptId);
                Tools.println("请求URL: " + url);
                Tools.println("原始响应: " + response.toString());
                Tools.println("=========================================");
            }
            
            if (WeComConfig.isLogApiResponseDetails()) {
                Tools.println("传统user/list接口响应: " + response.toJSONString());
            }
            
            // 从响应中提取成员信息
            JSONArray userList = response.getJSONArray("userlist");
            if (userList != null) {
                for (int i = 0; i < userList.size(); i++) {
                    JSONObject userJson = userList.getJSONObject(i);
                    
                    // 如果是增量同步，检查用户是否已存在
                    if (isIncremental && existingUserIds != null) {
                        String userId = userJson.getString("userid");
                        if (existingUserIds.contains(userId)) {
                            continue; // 跳过已存在的用户
                        }
                    }
                    
                    // 转换为内部联系人对象
                    WeComInternalContact contact = convertJsonToInternalContact(userJson);
                    if (contact != null) {
                        contacts.add(contact);
                    }
                }
            }
            
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("获取内部联系人完成，数量: " + contacts.size());
            }
            
            return contacts;
            
        } catch (Exception e) {
            throw new WeComApiException("获取企业微信内部联系人列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取企业所有成员的UserID列表
     * @param corpId 企业ID
     * @param corpSecret 通讯录同步应用的Secret
     * @param departmentId 部门ID，从该部门开始递归获取 (通常为1，表示根部门)
     * @return 成员UserID列表
     */
    public static List<String> getAllUserIds(String corpId, String corpSecret, Long departmentId) {
        List<String> userIds = new ArrayList<>();

        try {
            // 获取access_token
            String accessToken = WeComTokenManager.getAccessToken(corpId, corpSecret);

            // 构造获取成员列表的URL
            String url = String.format("%s?access_token=%s&department_id=%d&fetch_child=1",
                                     GET_USER_LIST_URL, accessToken, departmentId);

            // 发送请求获取成员列表
            JSONObject response = WeComHttpClient.doGetWithRetry(url);

            // 根据配置输出API响应原始数据
            if (WeComConfig.isLogApiRawJsonResponse()) {
                Tools.println("=== 获取成员ID列表API原始响应数据 ===");
                Tools.println("部门ID: " + departmentId);
                Tools.println("请求URL: " + url);
                Tools.println("原始响应: " + response.toString());
                Tools.println("====================================");
            }

            // 从响应中提取成员ID
            JSONArray userList = response.getJSONArray("userlist");
            if (userList != null) {
                for (int i = 0; i < userList.size(); i++) {
                    userIds.add(userList.getJSONObject(i).getString("userid"));
                }
            }

            Tools.println("成功获取企业成员列表，成员数量: " + userIds.size());
            return userIds;

        } catch (Exception e) {
            throw new WeComApiException("获取企业微信成员列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 兼容性方法，支持旧版本调用
     * @param corpId 企业ID
     * @param corpSecret 通讯录同步应用的Secret
     * @param departmentId 部门ID
     * @return 内部联系人完整信息列表
     */
    public static List<WeComInternalContact> getAllInternalContacts(String corpId, String corpSecret, Long departmentId) {
        return getAllInternalContacts(corpId, corpSecret, departmentId, false, null);
    }

    /**
     * 根据用户ID获取单个内部联系人详细信息
     * @param corpId 企业ID
     * @param corpSecret 通讯录同步应用的Secret
     * @param userId 用户ID
     * @return 内部联系人信息
     */
    public static WeComInternalContact getInternalContactDetail(String corpId, String corpSecret, String userId) {
        try {
            // 获取access_token
            String accessToken = WeComTokenManager.getAccessToken(corpId, corpSecret);

            // 构造获取成员详情的URL
            String url = String.format("%s?access_token=%s&userid=%s",
                                     GET_USER_DETAIL_URL, accessToken, userId);

            // 发送请求获取成员详情
            JSONObject response = WeComHttpClient.doGetWithRetry(url);

            // 根据配置输出API响应原始数据
            if (WeComConfig.isLogApiRawJsonResponse()) {
                Tools.println("=== 获取内部联系人详情API原始响应数据 ===");
                Tools.println("用户ID: " + userId);
                Tools.println("请求URL: " + url);
                Tools.println("原始响应: " + response.toString());
                Tools.println("========================================");
            }

            if (WeComConfig.isLogApiResponseDetails()) {
                Tools.println("获取单个内部联系人响应数据 - 用户ID: " + userId);
                Tools.println("响应数据: " + response.toJSONString());
            }

            // 转换为内部联系人对象
            return convertJsonToInternalContact(response);

        } catch (Exception e) {
            throw new WeComApiException("获取企业微信内部联系人详情失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量获取内部联系人详情（带QPS控制）
     * 
     * @param corpId 企业ID
     * @param corpSecret 通讯录同步应用的Secret
     * @param userIds 需要获取详情的用户ID列表
     * @return 内部联系人详情列表
     */
    public static List<WeComInternalContact> batchGetInternalContactDetails(String corpId, String corpSecret, List<String> userIds) {
        List<WeComInternalContact> contacts = new ArrayList<>();
        String accessToken = WeComTokenManager.getAccessToken(corpId, corpSecret);
        
        // QPS控制计算
        long qpsInterval = 1000 / Math.max(1, WeComConfig.getApiQpsLimit());
        long lastApiCall = 0;
        
        int successCount = 0;
        int errorCount = 0;
        
        for (int i = 0; i < userIds.size(); i++) {
            String userId = userIds.get(i);
            
            try {
                // QPS限流控制
                long currentTime = System.currentTimeMillis();
                long timeSinceLastCall = currentTime - lastApiCall;
                if (timeSinceLastCall < qpsInterval) {
                    Thread.sleep(qpsInterval - timeSinceLastCall);
                }
                
                WeComInternalContact contact = getInternalContactDetail(corpId, corpSecret, userId);
                if (contact != null) {
                    contacts.add(contact);
                    successCount++;
                }
                
                lastApiCall = System.currentTimeMillis();
                
                // 进度日志
                if (WeComConfig.isEnableDetailedPhaseLogging() && (i + 1) % 10 == 0) {
                    Tools.println("批量获取进度: " + (i + 1) + "/" + userIds.size() + 
                        "，成功: " + successCount + "，失败: " + errorCount);
                }
                
            } catch (Exception e) {
                errorCount++;
                if (WeComConfig.isEnableVerboseLogging()) {
                    Tools.println("获取用户详情失败 [" + userId + "]: " + e.getMessage());
                }
                
                // 如果错误率过高，可以考虑中断
                if (errorCount > userIds.size() * 0.1) { // 超过10%的错误率
                    Tools.println("错误率过高(" + errorCount + "/" + (i + 1) + ")，建议检查网络或API状态");
                }
            }
        }
        
        if (WeComConfig.isEnableDetailedPhaseLogging()) {
            Tools.println("批量获取完成: 成功" + successCount + "个，失败" + errorCount + "个");
        }
        
        return contacts;
    }

    /**
     * 将JSON对象转换为WeComInternalContact对象
     * @param userJson 用户信息JSON对象
     * @return WeComInternalContact对象
     */
    private static WeComInternalContact convertJsonToInternalContact(JSONObject userJson) {
        if (userJson == null) {
            return null;
        }

        WeComInternalContact contact = new WeComInternalContact();
        
        // 基本信息
        contact.setUserId(userJson.getString("userid"));
        contact.setName(userJson.getString("name"));
        contact.setMobile(userJson.getString("mobile"));
        contact.setEmail(userJson.getString("email"));
        contact.setPosition(userJson.getString("position"));
        contact.setGender(userJson.getInteger("gender"));
        contact.setAvatar(userJson.getString("avatar"));
        contact.setThumbAvatar(userJson.getString("thumb_avatar"));
        contact.setTelephone(userJson.getString("telephone"));
        contact.setAlias(userJson.getString("alias"));
        contact.setAddress(userJson.getString("address"));
        contact.setOpenUserId(userJson.getString("open_userid"));
        contact.setMainDepartment(userJson.getInteger("main_department"));
        contact.setStatus(userJson.getInteger("status"));
        contact.setQrCode(userJson.getString("qr_code"));
        
        // 部门信息（数组）
        JSONArray departmentArray = userJson.getJSONArray("department");
        if (departmentArray != null) {
            contact.setDepartmentJson(departmentArray.toJSONString());
        }
        
        // 部门内的排序值（数组）
        JSONArray orderArray = userJson.getJSONArray("order");
        if (orderArray != null) {
            contact.setOrderJson(orderArray.toJSONString());
        }
        
        // 在所在的部门内是否为部门负责人（数组）
        JSONArray isLeaderInDeptArray = userJson.getJSONArray("is_leader_in_dept");
        if (isLeaderInDeptArray != null) {
            contact.setIsLeaderInDeptJson(isLeaderInDeptArray.toJSONString());
        }
        
        // 直属上级UserID（数组）
        JSONArray directLeaderArray = userJson.getJSONArray("direct_leader");
        if (directLeaderArray != null) {
            contact.setDirectLeaderJson(directLeaderArray.toJSONString());
        }
        
        // 扩展属性
        JSONObject extAttr = userJson.getJSONObject("extattr");
        if (extAttr != null) {
            contact.setExtAttrJson(extAttr.toJSONString());
        }
        
        // 对外职务
        contact.setExternalPosition(userJson.getString("external_position"));
        
        // 成员对外信息
        JSONObject externalProfile = userJson.getJSONObject("external_profile");
        if (externalProfile != null) {
            contact.setExternalProfileJson(externalProfile.toJSONString());
        }

        return contact;
    }
} 