package com.career.db;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

import com.career.utils.ResultVO;
import com.career.utils.SQLLogUtils;
import com.career.utils.Tools;


public class RtTbJDBC {
	
	static String URL = JDBCConstants.URL;
	static String USER = JDBCConstants.USER;
	static String PASSWD = JDBCConstants.PASSWD;
	
	public static int PAGE_ROW_CNT = 20;
	public static int PAGE_ROW_CNT_SPEC = 15;
	public static int PAGE_ROW_CNT_TEN = 10;
	
	static {
		try {
			Class.forName("com.mysql.cj.jdbc.Driver");
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
	
	/**
     * 插入RtCheckerApplyFormMakerMain对象到数据库
     *
     * @param connection 数据库连接
     * @param form       RtCheckerApplyFormMakerMain对象
     * @return 插入成功返回true，否则返回false
     */
    public boolean insertRtCheckerApplyFormMakerMain(RtCheckerApplyFormMakerMain form) {
    	Connection conn = null;
        PreparedStatement pstmt = null;

        String sql = "INSERT INTO rt_checker_apply_form_maker_main (" +
                "batch_id, score_cj, score_wc, score_xk, f_no, order_id, selected_zymc, pc, pc_code, nf, " +
                "create_tm, last_update_tm, joined_checker_cnt, last_checker_id, last_checker_name, last_checker_remark, " +
                "status, f_type, form_name, to_checker_remark, sf, rt_score_chinese, rt_score_math, rt_score_foreign_language, " +
                "rt_score_xk1, rt_score_xk2, rt_score_xk3, rt_score_ext, rt_nickname, rt_phone, rt_remark, rt_health) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"; 

        try {   
        	conn = DatabaseUtils.getConnection();
        	pstmt = conn.prepareStatement(sql);
        	int index = 1;
        	
        	pstmt.setString(index++, form.getBatch_id());
            pstmt.setInt(index++, form.getScore_cj());
            pstmt.setInt(index++, form.getScore_wc());
            pstmt.setString(index++, form.getScore_xk());
            pstmt.setString(index++, form.getF_no());
            pstmt.setString(index++, form.getOrder_id());
            pstmt.setString(index++, form.getSelected_zymc());
            pstmt.setString(index++, form.getPc());
            pstmt.setString(index++, form.getPc_code());
            pstmt.setInt(index++, form.getNf());
            pstmt.setTimestamp(index++, new java.sql.Timestamp(form.getCreate_tm().getTime()));
            pstmt.setTimestamp(index++, form.getLast_update_tm() != null ? new java.sql.Timestamp(form.getLast_update_tm().getTime()) : null);
            pstmt.setInt(index++, form.getJoined_checker_cnt());
            pstmt.setString(index++, form.getLast_checker_id());
            pstmt.setString(index++, form.getLast_checker_name());
            pstmt.setString(index++, form.getLast_checker_remark());
            pstmt.setInt(index++, form.getStatus());
            pstmt.setInt(index++, form.getF_type());
            pstmt.setString(index++, form.getForm_name());
            pstmt.setString(index++, form.getTo_checker_remark()); 
            pstmt.setString(index++, form.getSf());
            pstmt.setInt(index++, form.getRt_score_chinese());
            pstmt.setInt(index++, form.getRt_score_math());
            pstmt.setInt(index++, form.getRt_score_foreign_language());
            pstmt.setInt(index++, form.getRt_score_xk1() );
            pstmt.setInt(index++, form.getRt_score_xk2());
            pstmt.setInt(index++, form.getRt_score_xk3());
            pstmt.setInt(index++, form.getRt_score_ext());
            pstmt.setString(index++, form.getRt_nickname());
            pstmt.setString(index++, form.getRt_phone());
            pstmt.setString(index++, form.getRt_remark());
            pstmt.setString(index++, form.getRt_health());
            
            int rowsAffected = pstmt.executeUpdate();
            
            SQLLogUtils.printSQL(" ===insertRtCheckerApplyFormMakerMain : ", pstmt);
            
            return rowsAffected > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        } finally {
        	closeAllConnection(conn, pstmt, null);
        }
    }
	
    /**
     * 批量插入RtCheckerApplyFormMaker对象到数据库
     *
     * @param forms RtCheckerApplyFormMaker对象列表
     * @return 插入成功返回true，否则返回false
     */
    public boolean insertRtCheckerApplyFormMakerList(List<RtCheckerApplyFormMaker> forms) {
        String sql = "INSERT INTO rt_checker_apply_form_maker (" +
                "batch_id, batch_id_org, yxmc, yxmc_org, yxbz, yxdm, zyz, zymc, zymc_org, zybz, zydm, " +
                "zdf_a, zdf_b, zdf_c, zdfwc_a, zdfwc_b, zdfwc_c, pjf_a, pjf_b, pjf_c, pjfwc_a, pjfwc_b, pjfwc_c, " +
                "jhs_a, jhs_b, jhs_c, zgf_a, zgf_b, zgf_c, zgfwc_a, zgfwc_b, zgfwc_c, jhs, fee, seq_no_zy, seq_no_yx, adjust_zy, adjust_dx) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"; 

        Connection conn = null;
        PreparedStatement pstmt = null;

        try {
            conn = DatabaseUtils.getConnection();
            conn.setAutoCommit(false); // 开启事务
            pstmt = conn.prepareStatement(sql);

            for (RtCheckerApplyFormMaker form : forms) {
                int index = 1;
                pstmt.setString(index++, form.getBatch_id());
                pstmt.setString(index++, form.getBatch_id_org());
                pstmt.setString(index++, form.getYxmc());
                pstmt.setString(index++, form.getYxmc_org());
                pstmt.setString(index++, form.getYxbz());
                pstmt.setString(index++, form.getYxdm());
                pstmt.setString(index++, form.getZyz());
                pstmt.setString(index++, form.getZymc());
                pstmt.setString(index++, form.getZymc_org());
                pstmt.setString(index++, form.getZybz());
                pstmt.setString(index++, form.getZydm());
                pstmt.setInt(index++, form.getZdf_a() != null ? form.getZdf_a() : 0);
                pstmt.setInt(index++, form.getZdf_b() != null ? form.getZdf_b() : 0);
                pstmt.setInt(index++, form.getZdf_c() != null ? form.getZdf_c() : 0);
                pstmt.setInt(index++, form.getZdfwc_a() != null ? form.getZdfwc_a() : 0);
                pstmt.setInt(index++, form.getZdfwc_b() != null ? form.getZdfwc_b() : 0);
                pstmt.setInt(index++, form.getZdfwc_c() != null ? form.getZdfwc_c() : 0);
                pstmt.setInt(index++, form.getPjf_a() != null ? form.getPjf_a() : 0);
                pstmt.setInt(index++, form.getPjf_b() != null ? form.getPjf_b() : 0);
                pstmt.setInt(index++, form.getPjf_c() != null ? form.getPjf_c() : 0);
                pstmt.setInt(index++, form.getPjfwc_a() != null ? form.getPjfwc_a() : 0);
                pstmt.setInt(index++, form.getPjfwc_b() != null ? form.getPjfwc_b() : 0);
                pstmt.setInt(index++, form.getPjfwc_c() != null ? form.getPjfwc_c() : 0);
                pstmt.setInt(index++, form.getJhs_a() != null ? form.getJhs_a() : 0);
                pstmt.setInt(index++, form.getJhs_b() != null ? form.getJhs_b() : 0);
                pstmt.setInt(index++, form.getJhs_c() != null ? form.getJhs_c() : 0);
                pstmt.setInt(index++, form.getZgf_a() != null ? form.getZgf_a() : 0);
                pstmt.setInt(index++, form.getZgf_b() != null ? form.getZgf_b() : 0);
                pstmt.setInt(index++, form.getZgf_c() != null ? form.getZgf_c() : 0);
                pstmt.setInt(index++, form.getZgfwc_a() != null ? form.getZgfwc_a() : 0);
                pstmt.setInt(index++, form.getZgfwc_b() != null ? form.getZgfwc_b() : 0);
                pstmt.setInt(index++, form.getZgfwc_c() != null ? form.getZgfwc_c() : 0);
                pstmt.setInt(index++, form.getJhs() != null ? form.getJhs() : 0);
                pstmt.setString(index++, form.getFee());
                pstmt.setInt(index++, form.getSeq_no_zy());
                pstmt.setInt(index++, form.getSeq_no_yx());
                
                pstmt.setString(index++, form.getAdjust_zy() != null ? form.getAdjust_zy() : "1");
                pstmt.setString(index++, form.getAdjust_dx() != null ? form.getAdjust_dx() : "2");
                
                pstmt.addBatch();
                
            }

            int[] rowsAffected = pstmt.executeBatch();
            conn.commit(); // 提交事务
            
            SQLLogUtils.printSQL(" ===insertRtCheckerApplyFormMakerList : ", pstmt);
            
            return rowsAffected.length > 0;
        } catch (SQLException e) { 
            e.printStackTrace(); 
            return false;
        } finally {
        	closeAllConnection(conn, pstmt, null);
        }
    }
    
    /**
     * 根据批次ID查询rt_checker_apply_form_maker表数据
     *
     * @param batchId 批次ID
     * @return 匹配到的 RtCheckerApplyFormMaker 对象列表，如果没有找到则返回空列表
     */
    public List<RtCheckerApplyFormMaker> getRtCheckerApplyFormMakersByBatchId(String batchId) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        List<RtCheckerApplyFormMaker> formList = new ArrayList<>(); 

        String sql = "SELECT * FROM rt_checker_apply_form_maker WHERE batch_id_org = ? ";

        try {
            conn = DatabaseUtils.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, batchId);

            rs = pstmt.executeQuery();

            SQLLogUtils.printSQL(" ===getRtCheckerApplyFormMakersByBatchId 1 : ", pstmt);

            while (rs.next()) {
                formList.add(populateRtCheckerApplyFormMakerFromResultSet(rs));
            }

        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, rs);
        }

        return formList;
    }
    
    /**
     * 根据批次ID查询rt_checker_apply_form_maker表数据
     *
     * @param batchId 批次ID
     * @return 匹配到的 RtCheckerApplyFormMaker 对象列表，如果没有找到则返回空列表
     */
    public List<RtCheckerApplyFormMaker> getRtCheckerApplyFormMakersByBatchIdWithSort(String batchId, String ascOrDesc) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        List<RtCheckerApplyFormMaker> formList = new ArrayList<>(); 

        String sql = "SELECT * FROM rt_checker_apply_form_maker WHERE batch_id_org = ? ORDER BY zdfwc_a "+ascOrDesc+", zdfwc_b "+ascOrDesc+", zdfwc_c "+ascOrDesc+", pjfwc_a "+ascOrDesc+", pjfwc_b "+ascOrDesc+", pjfwc_c "+ascOrDesc;

        try {
            conn = DatabaseUtils.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, batchId);

            rs = pstmt.executeQuery();

            SQLLogUtils.printSQL(" ===getRtCheckerApplyFormMakersByBatchId : ", pstmt);

            while (rs.next()) {
                formList.add(populateRtCheckerApplyFormMakerFromResultSet(rs));
            }

        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, rs);
        }

        return formList;
    }
    
    /**
     * 从ResultSet中填充RtCheckerApplyFormMaker对象
     *
     * @param rs ResultSet对象
     * @return 填充好的RtCheckerApplyFormMaker对象
     * @throws SQLException 如果从ResultSet中读取数据出错
     */
    private RtCheckerApplyFormMaker populateRtCheckerApplyFormMakerFromResultSet(ResultSet rs) throws SQLException {
        RtCheckerApplyFormMaker form = new RtCheckerApplyFormMaker();
        form.setId(rs.getInt("id"));
        form.setBatch_id(rs.getString("batch_id"));
        form.setBatch_id_org(rs.getString("batch_id_org"));
        form.setYxmc(rs.getString("yxmc"));
        form.setYxmc_org(rs.getString("yxmc_org"));
        form.setYxbz(rs.getString("yxbz"));
        form.setYxdm(rs.getString("yxdm"));
        form.setZyz(rs.getString("zyz"));
        form.setZymc(rs.getString("zymc"));
        form.setZymc_org(rs.getString("zymc_org"));
        form.setZybz(rs.getString("zybz"));
        form.setZydm(rs.getString("zydm"));
        form.setZdf_a((Integer) rs.getObject("zdf_a"));
        form.setZdf_b((Integer) rs.getObject("zdf_b"));
        form.setZdf_c((Integer) rs.getObject("zdf_c"));
        form.setZdfwc_a((Integer) rs.getObject("zdfwc_a"));
        form.setZdfwc_b((Integer) rs.getObject("zdfwc_b"));
        form.setZdfwc_c((Integer) rs.getObject("zdfwc_c"));
        form.setPjf_a((Integer) rs.getObject("pjf_a"));
        form.setPjf_b((Integer) rs.getObject("pjf_b"));
        form.setPjf_c((Integer) rs.getObject("pjf_c"));
        form.setPjfwc_a((Integer) rs.getObject("pjfwc_a"));
        form.setPjfwc_b((Integer) rs.getObject("pjfwc_b"));
        form.setPjfwc_c((Integer) rs.getObject("pjfwc_c"));
        form.setJhs_a((Integer) rs.getObject("jhs_a"));
        form.setJhs_b((Integer) rs.getObject("jhs_b"));
        form.setJhs_c((Integer) rs.getObject("jhs_c"));
        form.setZgf_a((Integer) rs.getObject("zgf_a"));
        form.setZgf_b((Integer) rs.getObject("zgf_b"));
        form.setZgf_c((Integer) rs.getObject("zgf_c"));
        form.setZgfwc_a((Integer) rs.getObject("zgfwc_a"));
        form.setZgfwc_b((Integer) rs.getObject("zgfwc_b"));
        form.setZgfwc_c((Integer) rs.getObject("zgfwc_c"));
        form.setJhs((Integer) rs.getObject("jhs"));
        form.setFee(rs.getString("fee"));
        form.setSeq_no_zy(rs.getInt("seq_no_zy"));
        form.setSeq_no_yx(rs.getInt("seq_no_yx"));
        form.setAdjust_zy(rs.getString("adjust_zy"));
        form.setAdjust_dx(rs.getString("adjust_dx"));
        return form;
    }
    
    /**
     * 根据手机号查询rt_checker_apply_form_maker_main表数据 (返回列表)
     *
     * @param rtPhone 手机号码
     * @return 匹配到的 RtCheckerApplyFormMakerMain 对象列表，如果没有找到则返回空列表
     */
    public List<RtCheckerApplyFormMakerMain> getRtCheckerApplyFormMakerMainsByPhone(String rtPhone) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        List<RtCheckerApplyFormMakerMain> formList = new ArrayList<>(); 

        String sql = "SELECT * FROM rt_checker_apply_form_maker_main WHERE rt_phone = ? ORDER BY create_tm DESC";

        try {
            conn = DatabaseUtils.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, rtPhone);

            rs = pstmt.executeQuery();

            SQLLogUtils.printSQL(" ===getRtCheckerApplyFormMakerMainsByPhone : ", pstmt);

            while (rs.next()) {
                formList.add(populateFormFromResultSet(rs));
            }

        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, rs);
        }

        return formList;
    }
    
    
    /**
     * 
     * @param batchId
     * @return
     * 
     */
    public RtCheckerApplyFormMakerMain getRtCheckerApplyFormMakerMainByBatchId(String batchId) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        RtCheckerApplyFormMakerMain rtCheckerApplyFormMakerMain = new RtCheckerApplyFormMakerMain();
        
        String sql = "SELECT * FROM rt_checker_apply_form_maker_main WHERE batch_id = ?";

        try {
            conn = DatabaseUtils.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, batchId);

            rs = pstmt.executeQuery();

            SQLLogUtils.printSQL(" ===getRtCheckerApplyFormMakerMainByBatchId : ", pstmt);

            while (rs.next()) {
            	rtCheckerApplyFormMakerMain = populateFormFromResultSet(rs);
            }

        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, rs);
        }

        return rtCheckerApplyFormMakerMain;
    }
    
    
    /**
     * 获取rt_checker_apply_form_maker_main表数据的分页查询
     * 
     * @param sf 省份代码
     * @param status 状态
     * @param rt_nickname 用户昵称
     * @param rt_phone 用户电话
     * @param pageNumber 页码
     * @return ResultVO 包含查询结果及分页信息
     */
    public ResultVO getRtCheckerApplyFormMakerMain(String sf, String status, String rt_nickname, String rt_phone, int f_type, int pageNumber) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        ResultVO resultVO = new ResultVO();
        
        List<RtCheckerApplyFormMakerMain> formList = new ArrayList<>();
        
        try {
            conn = DatabaseUtils.getConnection();
            
            StringBuilder whereCondition = new StringBuilder();
            List<Object> params = new ArrayList<>();
            
            whereCondition.append(" AND f_type = ? ");
            params.add(f_type);
            
            // 构建动态查询条件
            if (!Tools.isEmpty(sf)) {
                whereCondition.append(" AND sf = ?");
                params.add(sf);
            }
            
            if (!Tools.isEmpty(status)) {
                whereCondition.append(" AND status = ?");
                params.add(Integer.parseInt(status));
            }
            
            if (!Tools.isEmpty(rt_nickname)) {
                whereCondition.append(" AND rt_nickname LIKE ?");
                params.add("%" + rt_nickname + "%");
            }
            
            if (!Tools.isEmpty(rt_phone)) {
                whereCondition.append(" AND rt_phone LIKE ?");
                params.add("%" + rt_phone + "%");
            }
            
            String SUM_CONDITION = "SELECT count(*) AS cnt ";
            String SELECT_CONDITION = "SELECT * ";
            String FROM_CONDITION = " FROM rt_checker_apply_form_maker_main WHERE 1=1 " + whereCondition.toString();
            String ORDER_CONDITION = " ORDER BY create_tm DESC LIMIT ?,? ";
            
            Tools.println(SELECT_CONDITION + FROM_CONDITION + ORDER_CONDITION);
            ps = conn.prepareStatement(SELECT_CONDITION + FROM_CONDITION + ORDER_CONDITION);
            
            // 设置查询参数
            int paramIndex = 1;
            for (Object param : params) {
                ps.setObject(paramIndex++, param);
            }
            ps.setInt(paramIndex++, (pageNumber - 1) * PAGE_ROW_CNT);
            ps.setInt(paramIndex, PAGE_ROW_CNT);

            SQLLogUtils.printSQL(" ===getRtCheckerApplyFormMakerMain : ", ps);
            
            rs = ps.executeQuery();
            while (rs.next()) {
            	 formList.add(populateFormFromResultSet(rs));
            }
            
            resultVO.setResult(formList);
            
            ps.close();
            ps = null;
            rs.close();
            rs = null;
            
            // 查询总记录数
            ps = conn.prepareStatement(SUM_CONDITION + FROM_CONDITION);
            paramIndex = 1;
            for (Object param : params) {
                ps.setObject(paramIndex++, param);
            }
            rs = ps.executeQuery();
            
            while (rs.next()) {
                resultVO.setRecordCnt(rs.getInt("cnt"));
            }
            
            resultVO.setCurrentPage(pageNumber);
            
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        return resultVO;
    }

    
    /**
     * 从ResultSet中填充RtCheckerApplyFormMakerMain对象
     *
     * @param rs ResultSet对象
     * @return 填充好的RtCheckerApplyFormMakerMain对象
     * @throws SQLException 如果从ResultSet中读取数据出错
     */
    private RtCheckerApplyFormMakerMain populateFormFromResultSet(ResultSet rs) throws SQLException {
        RtCheckerApplyFormMakerMain form = new RtCheckerApplyFormMakerMain();
        
        form.setId(rs.getInt("id"));
        form.setBatch_id(rs.getString("batch_id"));
        form.setScore_cj(rs.getInt("score_cj"));
        form.setScore_wc(rs.getInt("score_wc"));
        form.setScore_xk(rs.getString("score_xk"));
        form.setF_no(rs.getString("f_no"));
        form.setOrder_id(rs.getString("order_id"));
        form.setSelected_zymc(rs.getString("selected_zymc"));
        form.setPc(rs.getString("pc"));
        form.setPc_code(rs.getString("pc_code"));
        form.setNf(rs.getInt("nf"));
        form.setCreate_tm(rs.getTimestamp("create_tm"));
        form.setLast_update_tm(rs.getTimestamp("last_update_tm"));
        form.setTarget_send_tm(rs.getTimestamp("target_send_tm"));
        form.setUser_first_view_tm(rs.getTimestamp("user_first_view_tm"));
        form.setJoined_checker_cnt(rs.getInt("joined_checker_cnt"));
        form.setLast_checker_id(rs.getString("last_checker_id"));
        form.setLast_checker_name(rs.getString("last_checker_name"));
        form.setLast_checker_remark(rs.getString("last_checker_remark"));
        form.setStatus(rs.getInt("status"));
        form.setF_type(rs.getInt("f_type"));
        form.setForm_name(rs.getString("form_name"));
        form.setTo_checker_remark(rs.getString("to_checker_remark"));
        form.setSf(rs.getString("sf"));
        form.setRt_score_chinese(rs.getInt("rt_score_chinese"));
        form.setRt_score_math(rs.getInt("rt_score_math"));
        form.setRt_score_foreign_language(rs.getInt("rt_score_foreign_language"));
        form.setRt_score_xk1(rs.getInt("rt_score_xk1"));
        form.setRt_score_xk2(rs.getInt("rt_score_xk2"));
        form.setRt_score_xk3(rs.getInt("rt_score_xk3"));
        form.setRt_score_ext(rs.getInt("rt_score_ext"));
        form.setRt_nickname(rs.getString("rt_nickname"));
        form.setRt_phone(rs.getString("rt_phone"));
        form.setRt_remark(rs.getString("rt_remark"));
        form.setRt_health(rs.getString("rt_health"));
        return form;
    }
    
    
	private static void closeAllConnection(Connection conn, PreparedStatement ps, ResultSet rs) {
		/**
		try {
			if (rs != null)
				rs.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (ps != null)
				ps.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (conn != null)
				conn.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		rs = null;
		ps = null;
		conn = null;*/
		DatabaseUtils.closeAllResources(rs, ps, conn);
	}
	
	/**
     * 插入RtFillNeedsInfoBean对象到数据库
     *
     * @param form RtFillNeedsInfoBean对象
     * @return 插入成功返回true，否则返回false
     */
    public boolean insertRtFillNeedsInfo(RtFillNeedsInfoBean form) {
        Connection conn = null;
        PreparedStatement pstmt = null;

        String sql = "INSERT INTO rt_fill_needs_info (" +
                "rt_id, rt_user_id, rt_agent_id, rt_expert_id, rt_order_price_org, rt_order_price_discount, rt_order_price, " +
                "rt_province, rt_name, rt_phone, rt_gender, rt_graduate_status, rt_political_status, rt_ethnicity, " +
                "rt_height, rt_weight, rt_body_type, rt_drawing_skill, rt_hand_habit, rt_physical_exam, " +
                "rt_subject, rt_foreign_language, rt_score_chinese, rt_score_math, " +
                "rt_score_foreign_language, rt_score_a, rt_score_b, rt_score_c, rt_total_score, rt_score_ext, " +
                "rt_ranking, rt_interview, rt_is_eligible_for_special_plans, rt_accept_directed_plans, rt_university_budget, " +
                "rt_deep_planning, rt_accept_sino_foreign, rt_accept_private, rt_strategy, rt_preferred_cities, " +
                "rt_preferred_provinces, rt_major_level, rt_major_category, rt_major_subcategory, rt_preferred_majors, " +
                "rt_status, rt_create_time, rt_submit_time) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";

        try {
            conn = DatabaseUtils.getConnection();
            pstmt = conn.prepareStatement(sql);
            int index = 1;

            pstmt.setString(index++, form.getRt_id());
            pstmt.setString(index++, form.getRt_user_id());
            pstmt.setString(index++, form.getRt_agent_id());
            pstmt.setString(index++, form.getRt_expert_id());
            pstmt.setObject(index++, form.getRt_order_price_org());
            pstmt.setObject(index++, form.getRt_order_price_discount());
            pstmt.setObject(index++, form.getRt_order_price());
            pstmt.setString(index++, form.getRt_province());
            pstmt.setString(index++, form.getRt_name());
            pstmt.setString(index++, form.getRt_phone());
            pstmt.setString(index++, form.getRt_gender());
            pstmt.setString(index++, form.getRt_graduate_status());
            pstmt.setString(index++, form.getRt_political_status());
            pstmt.setString(index++, form.getRt_ethnicity());
            pstmt.setString(index++, form.getRt_height());
            pstmt.setString(index++, form.getRt_weight());
            pstmt.setString(index++, form.getRt_body_type());
            pstmt.setString(index++, form.getRt_drawing_skill());
            pstmt.setString(index++, form.getRt_hand_habit());
            pstmt.setString(index++, form.getRt_physical_exam());
            pstmt.setString(index++, form.getRt_subject());
            pstmt.setString(index++, form.getRt_foreign_language());
            pstmt.setObject(index++, form.getRt_score_chinese());
            pstmt.setObject(index++, form.getRt_score_math());
            pstmt.setObject(index++, form.getRt_score_foreign_language());
            pstmt.setObject(index++, form.getRt_score_a());
            pstmt.setObject(index++, form.getRt_score_b());
            pstmt.setObject(index++, form.getRt_score_c());
            pstmt.setObject(index++, form.getRt_total_score());
            pstmt.setObject(index++, form.getRt_score_ext());
            pstmt.setObject(index++, form.getRt_ranking());
            pstmt.setString(index++, form.getRt_interview());
            pstmt.setString(index++, form.getRt_is_eligible_for_special_plans());
            pstmt.setString(index++, form.getRt_accept_directed_plans());
            pstmt.setString(index++, form.getRt_university_budget());
            pstmt.setString(index++, form.getRt_deep_planning());
            pstmt.setString(index++, form.getRt_accept_sino_foreign());
            pstmt.setString(index++, form.getRt_accept_private());
            pstmt.setString(index++, form.getRt_strategy());
            pstmt.setString(index++, form.getRt_preferred_cities());
            pstmt.setString(index++, form.getRt_preferred_provinces());
            pstmt.setString(index++, form.getRt_major_level());
            pstmt.setString(index++, form.getRt_major_category());
            pstmt.setString(index++, form.getRt_major_subcategory());
            pstmt.setString(index++, form.getRt_preferred_majors());
            pstmt.setInt(index++, form.getRt_status() != null ? form.getRt_status() : 10); // 默认状态为10

            int rowsAffected = pstmt.executeUpdate();

            SQLLogUtils.printSQL(" ===insertRtFillNeedsInfo : ", pstmt);

            return rowsAffected > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        } finally {
            closeAllConnection(conn, pstmt, null);
        }
    }

    /**
     * 更新RtFillNeedsInfoBean对象到数据库
     *
     * @param form RtFillNeedsInfoBean对象
     * @return 更新成功返回true，否则返回false
     */
    public boolean updateRtFillNeedsInfo(RtFillNeedsInfoBean form) {
        Connection conn = null;
        PreparedStatement pstmt = null;

        String sql = "UPDATE rt_fill_needs_info SET " +
                "rt_user_id = ?, rt_agent_id = ?, rt_expert_id = ?, rt_order_price_org = ?, rt_order_price_discount = ?, " +
                "rt_order_price = ?, rt_province = ?, rt_name = ?, rt_phone = ?, rt_gender = ?, rt_graduate_status = ?, " +
                "rt_political_status = ?, rt_ethnicity = ?, rt_height = ?, rt_weight = ?, rt_body_type = ?, " +
                "rt_drawing_skill = ?, rt_hand_habit = ?, rt_physical_exam = ?, rt_subject = ?, " +
                "rt_foreign_language = ?, rt_score_chinese = ?, rt_score_math = ?, " +
                "rt_score_foreign_language = ?, rt_score_a = ?, rt_score_b = ?, rt_score_c = ?, " +
                "rt_total_score = ?, rt_score_ext = ?, rt_ranking = ?, rt_interview = ?, rt_is_eligible_for_special_plans = ?, " +
                "rt_accept_directed_plans = ?, rt_university_budget = ?, rt_deep_planning = ?, rt_accept_sino_foreign = ?, " +
                "rt_accept_private = ?, rt_strategy = ?, rt_preferred_cities = ?, rt_preferred_provinces = ?, " +
                "rt_major_level = ?, rt_major_category = ?, rt_major_subcategory = ?, rt_preferred_majors = ?, " +
                "rt_status = ?, rt_update_time = NOW(), rt_submit_time = ? " +
                "WHERE rt_id = ?";

        try {
            conn = DatabaseUtils.getConnection();
            pstmt = conn.prepareStatement(sql);
            int index = 1;

            pstmt.setString(index++, form.getRt_user_id());
            pstmt.setString(index++, form.getRt_agent_id());
            pstmt.setString(index++, form.getRt_expert_id());
            pstmt.setObject(index++, form.getRt_order_price_org());
            pstmt.setObject(index++, form.getRt_order_price_discount());
            pstmt.setObject(index++, form.getRt_order_price());
            pstmt.setString(index++, form.getRt_province());
            pstmt.setString(index++, form.getRt_name());
            pstmt.setString(index++, form.getRt_phone());
            pstmt.setString(index++, form.getRt_gender());
            pstmt.setString(index++, form.getRt_graduate_status());
            pstmt.setString(index++, form.getRt_political_status());
            pstmt.setString(index++, form.getRt_ethnicity());
            pstmt.setString(index++, form.getRt_height());
            pstmt.setString(index++, form.getRt_weight());
            pstmt.setString(index++, form.getRt_body_type());
            pstmt.setString(index++, form.getRt_drawing_skill());
            pstmt.setString(index++, form.getRt_hand_habit());
            pstmt.setString(index++, form.getRt_physical_exam());
            pstmt.setString(index++, form.getRt_subject());
            pstmt.setString(index++, form.getRt_foreign_language());
            pstmt.setObject(index++, form.getRt_score_chinese());
            pstmt.setObject(index++, form.getRt_score_math());
            pstmt.setObject(index++, form.getRt_score_foreign_language());
            pstmt.setObject(index++, form.getRt_score_a());
            pstmt.setObject(index++, form.getRt_score_b());
            pstmt.setObject(index++, form.getRt_score_c());
            pstmt.setObject(index++, form.getRt_total_score());
            pstmt.setObject(index++, form.getRt_score_ext());
            pstmt.setObject(index++, form.getRt_ranking());
            pstmt.setString(index++, form.getRt_interview());
            pstmt.setString(index++, form.getRt_is_eligible_for_special_plans());
            pstmt.setString(index++, form.getRt_accept_directed_plans());
            pstmt.setString(index++, form.getRt_university_budget());
            pstmt.setString(index++, form.getRt_deep_planning());
            pstmt.setString(index++, form.getRt_accept_sino_foreign());
            pstmt.setString(index++, form.getRt_accept_private());
            pstmt.setString(index++, form.getRt_strategy());
            pstmt.setString(index++, form.getRt_preferred_cities());
            pstmt.setString(index++, form.getRt_preferred_provinces());
            pstmt.setString(index++, form.getRt_major_level());
            pstmt.setString(index++, form.getRt_major_category());
            pstmt.setString(index++, form.getRt_major_subcategory());
            pstmt.setString(index++, form.getRt_preferred_majors());
            pstmt.setInt(index++, form.getRt_status() != null ? form.getRt_status() : 10);
            pstmt.setTimestamp(index++, form.getRt_submit_time());
            pstmt.setString(index++, form.getRt_id()); // WHERE 条件

            int rowsAffected = pstmt.executeUpdate();

            SQLLogUtils.printSQL(" ===updateRtFillNeedsInfo : ", pstmt);

            return rowsAffected > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        } finally {
            closeAllConnection(conn, pstmt, null);
        }
    }

    /**
     * 根据手机号查询rt_fill_needs_info表数据 (返回列表)
     *
     * @param rtPhone 手机号码
     * @return 匹配到的 RtFillNeedsInfoBean 对象列表，如果没有找到则返回空列表
     */
    public List<RtFillNeedsInfoBean> getRtFillNeedsInfosByPhone(String rtPhone) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        List<RtFillNeedsInfoBean> formList = new ArrayList<>();

        String sql = "SELECT * FROM rt_fill_needs_info WHERE rt_phone = ? ORDER BY rt_create_time DESC";

        try {
            conn = DatabaseUtils.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, rtPhone);

            rs = pstmt.executeQuery();

            SQLLogUtils.printSQL(" ===getRtFillNeedsInfosByPhone : ", pstmt);

            while (rs.next()) {
                formList.add(populateRtFillNeedsInfoFromResultSet(rs));
            }

        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, rs);
        }

        return formList;
    }
    
    public List<RtFillNeedsInfoBean> getRtFillNeedsInfosByCid(String c_id) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        List<RtFillNeedsInfoBean> formList = new ArrayList<>();

        String sql = "SELECT * FROM rt_fill_needs_info WHERE rt_phone = ? ORDER BY rt_create_time DESC";

        try {
            conn = DatabaseUtils.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, c_id);

            rs = pstmt.executeQuery();

            SQLLogUtils.printSQL(" ===getRtFillNeedsInfosByPhone : ", pstmt);

            while (rs.next()) {
                formList.add(populateRtFillNeedsInfoFromResultSet(rs));
            }

        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, rs);
        }

        return formList;
    }

    /**
     * 从ResultSet中填充RtFillNeedsInfoBean对象
     *
     * @param rs ResultSet对象
     * @return 填充好的RtFillNeedsInfoBean对象
     * @throws SQLException 如果从ResultSet中读取数据出错
     */
    private RtFillNeedsInfoBean populateRtFillNeedsInfoFromResultSet(ResultSet rs) throws SQLException {
        RtFillNeedsInfoBean form = new RtFillNeedsInfoBean();
        form.setRt_id(rs.getString("rt_id"));
        form.setRt_user_id(rs.getString("rt_user_id"));
        form.setRt_agent_id(rs.getString("rt_agent_id"));
        form.setRt_expert_id(rs.getString("rt_expert_id"));
        form.setRt_order_price_org((Integer) rs.getObject("rt_order_price_org"));
        form.setRt_order_price_discount((Integer) rs.getObject("rt_order_price_discount"));
        form.setRt_order_price((Integer) rs.getObject("rt_order_price"));
        form.setRt_province(rs.getString("rt_province"));
        form.setRt_name(rs.getString("rt_name"));
        form.setRt_phone(rs.getString("rt_phone"));
        form.setRt_gender(rs.getString("rt_gender"));
        form.setRt_graduate_status(rs.getString("rt_graduate_status"));
        form.setRt_political_status(rs.getString("rt_political_status"));
        form.setRt_ethnicity(rs.getString("rt_ethnicity"));
        form.setRt_height(rs.getString("rt_height"));
        form.setRt_weight(rs.getString("rt_weight"));
        form.setRt_body_type(rs.getString("rt_body_type"));
        form.setRt_drawing_skill(rs.getString("rt_drawing_skill"));
        form.setRt_hand_habit(rs.getString("rt_hand_habit"));
        form.setRt_physical_exam(rs.getString("rt_physical_exam"));
        form.setRt_subject(rs.getString("rt_subject"));
        form.setRt_foreign_language(rs.getString("rt_foreign_language"));
        form.setRt_score_chinese((Integer) rs.getObject("rt_score_chinese"));
        form.setRt_score_math((Integer) rs.getObject("rt_score_math"));
        form.setRt_score_foreign_language((Integer) rs.getObject("rt_score_foreign_language"));
        form.setRt_score_a((Integer) rs.getObject("rt_score_a"));
        form.setRt_score_b((Integer) rs.getObject("rt_score_b"));
        form.setRt_score_c((Integer) rs.getObject("rt_score_c"));
        form.setRt_total_score((Integer) rs.getObject("rt_total_score"));
        form.setRt_score_ext((Integer) rs.getObject("rt_score_ext"));
        form.setRt_ranking((Integer) rs.getObject("rt_ranking"));
        form.setRt_interview(rs.getString("rt_interview"));
        form.setRt_is_eligible_for_special_plans(rs.getString("rt_is_eligible_for_special_plans"));
        form.setRt_accept_directed_plans(rs.getString("rt_accept_directed_plans"));
        form.setRt_university_budget(rs.getString("rt_university_budget"));
        form.setRt_deep_planning(rs.getString("rt_deep_planning"));
        form.setRt_accept_sino_foreign(rs.getString("rt_accept_sino_foreign"));
        form.setRt_accept_private(rs.getString("rt_accept_private"));
        form.setRt_strategy(rs.getString("rt_strategy"));
        form.setRt_preferred_cities(rs.getString("rt_preferred_cities"));
        form.setRt_preferred_provinces(rs.getString("rt_preferred_provinces"));
        form.setRt_major_level(rs.getString("rt_major_level"));
        form.setRt_major_category(rs.getString("rt_major_category"));
        form.setRt_major_subcategory(rs.getString("rt_major_subcategory"));
        form.setRt_preferred_majors(rs.getString("rt_preferred_majors"));
        form.setRt_status((Integer) rs.getObject("rt_status"));
        form.setRt_create_time(rs.getTimestamp("rt_create_time"));
        form.setRt_update_time(rs.getTimestamp("rt_update_time"));
        form.setRt_submit_time(rs.getTimestamp("rt_submit_time"));
        return form;
    }

    /**
     * 根据ID查询rt_fill_needs_info表数据 (返回单个对象)
     *
     * @param rtId 记录ID
     * @return 匹配到的 RtFillNeedsInfoBean 对象，如果没有找到则返回null
     */
    public RtFillNeedsInfoBean getRtFillNeedsInfoById(String rtId) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        RtFillNeedsInfoBean form = null;

        String sql = "SELECT * FROM rt_fill_needs_info WHERE rt_id = ? ";

        try {
            conn = DatabaseUtils.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, rtId);

            rs = pstmt.executeQuery();

            SQLLogUtils.printSQL(" ===getRtFillNeedsInfoById : ", pstmt);

            if (rs.next()) {
                form = populateRtFillNeedsInfoFromResultSet(rs);
            }

        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, rs);
        }

        return form;
    }
    
    /**
     * 
     * @param jhYear
     * @param sfCode
     * @param xkCode
     * @param pc
     * @param pc_code
     * @param yxdmSets
     * @param zydmSets
     * @param zyzSets
     * @return
     * 
     */
    public List<JHBean> getJHByYxdmZydmZyz(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> yxdmSets, HashSet<String> zydmSets, HashSet<String> zyzSets) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		int dataYear = jhYear-1;
		
		try {
			String yxdmSetsStr = yxdmSets.size() > 0 ? " AND x.yxdm in ("+Tools.getSQLQueryin(yxdmSets)+")" : "";
			String zydmSetsStr = zydmSets.size() > 0 ? " AND x.zydm in ("+Tools.getSQLQueryin(zydmSets)+")" : "";
			String zyzSetsStr = zyzSets.size() > 0 ? " AND x.zyz in ("+Tools.getSQLQueryin(zyzSets)+")" : "";
			
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.xk_code like ? and x.pc = ? AND x.pc_code = ? " + yxdmSetsStr + zydmSetsStr + zyzSetsStr;
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, "%" + xkCode + "%");
			ps.setString(2, pc); 
			ps.setString(3, pc_code);

			SQLLogUtils.printSQL(" === getJHByYxmcOrg(): ", ps);
			
			rs = ps.executeQuery();
			
			while (rs.next()) {
				list.add(ZyzdFormJDBC.setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
    /**
     * 从ResultSet中填充RtCheckerApplyFormMakerMainChecker对象
     *
     * @param rs ResultSet对象
     * @return 填充好的RtCheckerApplyFormMakerMainChecker对象
     * @throws SQLException 如果从ResultSet中读取数据出错
     */
    private RtCheckerApplyFormMakerMainChecker populateRtCheckerApplyFormMakerMainCheckerFromResultSet(ResultSet rs) throws SQLException {
        RtCheckerApplyFormMakerMainChecker checker = new RtCheckerApplyFormMakerMainChecker();
        checker.setMcId(rs.getInt("mc_id"));
        checker.setMcResult(rs.getInt("mc_result"));
        checker.setFmId(rs.getString("fm_id"));
        checker.setCId(rs.getString("c_id"));
        checker.setRemark(rs.getString("remark"));
        checker.setToCheckerRemark(rs.getString("to_checker_remark"));
        checker.setCreateTm(rs.getTimestamp("create_tm"));
        return checker;
    }

    /**
     * 插入RtCheckerApplyFormMakerMainChecker对象到数据库
     *
     * @param checker RtCheckerApplyFormMakerMainChecker对象
     * @return 插入成功返回true，否则返回false
     */
    public boolean insertRtCheckerApplyFormMakerMainChecker(RtCheckerApplyFormMakerMainChecker checker) {
        Connection conn = null;
        PreparedStatement pstmt = null;

        String sql = "INSERT INTO rt_checker_apply_form_maker_main_checker (" +
                "mc_result, fm_id, c_id, remark, to_checker_remark, create_tm) " +
                "VALUES (?, ?, ?, ?, ?, NOW())";

        try {
            conn = DatabaseUtils.getConnection();
            pstmt = conn.prepareStatement(sql);
            int index = 1;

            pstmt.setInt(index++, checker.getMcResult());
            pstmt.setString(index++, checker.getFmId());
            pstmt.setString(index++, checker.getCId());
            pstmt.setString(index++, checker.getRemark());
            pstmt.setString(index++, checker.getToCheckerRemark());

            int rowsAffected = pstmt.executeUpdate();

            SQLLogUtils.printSQL(" ===insertRtCheckerApplyFormMakerMainChecker : ", pstmt);

            return rowsAffected > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        } finally {
            closeAllConnection(conn, pstmt, null);
        }
    }

    /**
     * 根据主键ID查询rt_checker_apply_form_maker_main_checker表数据
     *
     * @param mcId 主键ID
     * @return 匹配到的 RtCheckerApplyFormMakerMainChecker 对象，如果没有找到则返回null
     */
    public RtCheckerApplyFormMakerMainChecker getRtCheckerApplyFormMakerMainCheckerById(int mcId) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        RtCheckerApplyFormMakerMainChecker checker = null;

        String sql = "SELECT * FROM rt_checker_apply_form_maker_main_checker WHERE mc_id = ?";

        try {
            conn = DatabaseUtils.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, mcId);

            rs = pstmt.executeQuery();

            SQLLogUtils.printSQL(" ===getRtCheckerApplyFormMakerMainCheckerById : ", pstmt);

            if (rs.next()) {
                checker = populateRtCheckerApplyFormMakerMainCheckerFromResultSet(rs);
            }

        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, rs);
        }

        return checker;
    }

    /**
     * 根据fm_id查询rt_checker_apply_form_maker_main_checker表数据
     *
     * @param fmId form_maker_main的ID
     * @return 匹配到的 RtCheckerApplyFormMakerMainChecker 对象列表，如果没有找到则返回空列表
     */
    public List<RtCheckerApplyFormMakerMainChecker> getRtCheckerApplyFormMakerMainCheckersByFmId(String fmId) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        List<RtCheckerApplyFormMakerMainChecker> checkerList = new ArrayList<>();

        String sql = "SELECT * FROM rt_checker_apply_form_maker_main_checker WHERE fm_id = ? ORDER BY create_tm DESC";

        try {
            conn = DatabaseUtils.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, fmId);

            rs = pstmt.executeQuery();

            SQLLogUtils.printSQL(" ===getRtCheckerApplyFormMakerMainCheckersByFmId : ", pstmt);

            while (rs.next()) {
                checkerList.add(populateRtCheckerApplyFormMakerMainCheckerFromResultSet(rs));
            }

        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, rs);
        }

        return checkerList;
    }
    
    /**
     * 根据c_id查询rt_checker_apply_form_maker_main_checker表数据
     *
     * @param cId 审核用户ID
     * @return 匹配到的 RtCheckerApplyFormMakerMainChecker 对象列表，如果没有找到则返回空列表
     */
    public List<RtCheckerApplyFormMakerMainChecker> getRtCheckerApplyFormMakerMainCheckersByCId(String cId) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        List<RtCheckerApplyFormMakerMainChecker> checkerList = new ArrayList<>();

        String sql = "SELECT * FROM rt_checker_apply_form_maker_main_checker WHERE c_id = ? ORDER BY create_tm DESC";

        try {
            conn = DatabaseUtils.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, cId);

            rs = pstmt.executeQuery();

            SQLLogUtils.printSQL(" ===getRtCheckerApplyFormMakerMainCheckersByCId : ", pstmt);

            while (rs.next()) {
                checkerList.add(populateRtCheckerApplyFormMakerMainCheckerFromResultSet(rs));
            }

        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, rs);
        }

        return checkerList;
    }

    /**
     * 更新RtCheckerApplyFormMakerMainChecker对象到数据库
     *
     * @param checker RtCheckerApplyFormMakerMainChecker对象
     * @return 更新成功返回true，否则返回false
     */
    public boolean updateRtCheckerApplyFormMakerMainChecker(RtCheckerApplyFormMakerMainChecker checker) {
        Connection conn = null;
        PreparedStatement pstmt = null;

        String sql = "UPDATE rt_checker_apply_form_maker_main_checker SET " +
                "mc_result = ?, fm_id = ?, c_id = ?, remark = ?, to_checker_remark = ?, create_tm = ? " +
                "WHERE mc_id = ?";

        try {
            conn = DatabaseUtils.getConnection();
            pstmt = conn.prepareStatement(sql);
            int index = 1;

            pstmt.setInt(index++, checker.getMcResult());
            pstmt.setString(index++, checker.getFmId());
            pstmt.setString(index++, checker.getCId());
            pstmt.setString(index++, checker.getRemark());
            pstmt.setString(index++, checker.getToCheckerRemark());
            pstmt.setTimestamp(index++, new java.sql.Timestamp(checker.getCreateTm().getTime()));
            pstmt.setInt(index++, checker.getMcId()); // WHERE 条件

            int rowsAffected = pstmt.executeUpdate();

            SQLLogUtils.printSQL(" ===updateRtCheckerApplyFormMakerMainChecker : ", pstmt);

            return rowsAffected > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        } finally {
            closeAllConnection(conn, pstmt, null);
        }
    }

    /**
     * 根据主键ID删除rt_checker_apply_form_maker_main_checker表数据
     *
     * @param mcId 主键ID
     * @return 删除成功返回true，否则返回false
     */
    public boolean deleteRtCheckerApplyFormMakerMainChecker(int mcId) {
        Connection conn = null;
        PreparedStatement pstmt = null;

        String sql = "DELETE FROM rt_checker_apply_form_maker_main_checker WHERE mc_id = ?";

        try {
            conn = DatabaseUtils.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, mcId);

            int rowsAffected = pstmt.executeUpdate();

            SQLLogUtils.printSQL(" ===deleteRtCheckerApplyFormMakerMainChecker : ", pstmt);

            return rowsAffected > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        } finally {
            closeAllConnection(conn, pstmt, null);
        }
    }

    /**
     * 更新RtCheckerApplyFormMakerMain对象到数据库
     *
     * @param form RtCheckerApplyFormMakerMain对象
     * @return 更新成功返回true，否则返回false
     */
    public boolean updateRtCheckerApplyFormMakerMain(RtCheckerApplyFormMakerMain form) {
        Connection conn = null;
        PreparedStatement pstmt = null;

        String sql = "UPDATE rt_checker_apply_form_maker_main SET " +
                "score_cj = ?, score_wc = ?, score_xk = ?, f_no = ?, order_id = ?, selected_zymc = ?, pc = ?, pc_code = ?, nf = ?, " +
                "last_update_tm = NOW(), joined_checker_cnt = ?, last_checker_id = ?, last_checker_name = ?, last_checker_remark = ?, " +
                "status = ?, f_type = ?, form_name = ?, to_checker_remark = ?, sf = ?, rt_score_chinese = ?, rt_score_math = ?, rt_score_foreign_language = ?, " +
                "rt_score_xk1 = ?, rt_score_xk2 = ?, rt_score_xk3 = ?, rt_score_ext = ?, rt_nickname = ?, rt_phone = ?, rt_remark = ? " +
                "WHERE batch_id = ?";

        try {
            conn = DatabaseUtils.getConnection();
            pstmt = conn.prepareStatement(sql);
            int index = 1;

            pstmt.setInt(index++, form.getScore_cj());
            pstmt.setInt(index++, form.getScore_wc());
            pstmt.setString(index++, form.getScore_xk());
            pstmt.setString(index++, form.getF_no());
            pstmt.setString(index++, form.getOrder_id());
            pstmt.setString(index++, form.getSelected_zymc());
            pstmt.setString(index++, form.getPc());
            pstmt.setString(index++, form.getPc_code());
            pstmt.setInt(index++, form.getNf());
            pstmt.setInt(index++, form.getJoined_checker_cnt());
            pstmt.setString(index++, form.getLast_checker_id());
            pstmt.setString(index++, form.getLast_checker_name());
            pstmt.setString(index++, form.getLast_checker_remark());
            pstmt.setInt(index++, form.getStatus());
            pstmt.setInt(index++, form.getF_type());
            pstmt.setString(index++, form.getForm_name());
            pstmt.setString(index++, form.getTo_checker_remark());
            pstmt.setString(index++, form.getSf());
            pstmt.setInt(index++, form.getRt_score_chinese());
            pstmt.setInt(index++, form.getRt_score_math());
            pstmt.setInt(index++, form.getRt_score_foreign_language());
            pstmt.setInt(index++, form.getRt_score_xk1());
            pstmt.setInt(index++, form.getRt_score_xk2());
            pstmt.setInt(index++, form.getRt_score_xk3());
            pstmt.setInt(index++, form.getRt_score_ext());
            pstmt.setString(index++, form.getRt_nickname());
            pstmt.setString(index++, form.getRt_phone());
            pstmt.setString(index++, form.getRt_remark());

            pstmt.setString(index++, form.getBatch_id()); // WHERE 条件

            int rowsAffected = pstmt.executeUpdate();

            SQLLogUtils.printSQL(" ===updateRtCheckerApplyFormMakerMain : ", pstmt);

            return rowsAffected > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        } finally {
            closeAllConnection(conn, pstmt, null);
        }
    }
    
    public boolean updateRtCheckerApplyFormMakerMainForTargetTimeOnly(String batch_id, Date targetSendTm) {
        Connection conn = null;
        PreparedStatement pstmt = null;

        String sql = "UPDATE rt_checker_apply_form_maker_main set target_send_tm = ? WHERE batch_id = ?";

        try {
            conn = DatabaseUtils.getConnection(); 
            pstmt = conn.prepareStatement(sql);
            int index = 1;

            // 设置更新的字段值
            pstmt.setTimestamp(index++, new Timestamp(targetSendTm.getTime()));
            pstmt.setString(index++, batch_id);

            int rowsAffected = pstmt.executeUpdate();

            // 打印 SQL 日志
            SQLLogUtils.printSQL(" ===updateRtCheckerApplyFormMakerMainForTargetTimeOnly : ", pstmt);

            return rowsAffected > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        } finally {
            closeAllConnection(conn, pstmt, null);
        }
    }
    
    public boolean updateRtCheckerApplyFormMakerMainForStatusOnly(String batch_id, int status) {
        Connection conn = null;
        PreparedStatement pstmt = null;

        String sql = "UPDATE rt_checker_apply_form_maker_main set status = ? WHERE batch_id = ?";

        try {
            conn = DatabaseUtils.getConnection(); 
            pstmt = conn.prepareStatement(sql);
            int index = 1;

            // 设置更新的字段值
            pstmt.setInt(index++, status);
            pstmt.setString(index++, batch_id);

            int rowsAffected = pstmt.executeUpdate();

            // 打印 SQL 日志
            SQLLogUtils.printSQL(" ===updateRtCheckerApplyFormMakerMainForTargetTimeOnly : ", pstmt);

            return rowsAffected > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        } finally {
            closeAllConnection(conn, pstmt, null);
        }
    }

    /**
     * 
     * 
     * @param batchId
     * @param status
     * @return
     * 
     */
    public boolean updateRtCheckerApplyFormMakerMain(String batchId, int status) {
        Connection conn = null;
        PreparedStatement pstmt = null;

        String sql = "UPDATE rt_checker_apply_form_maker_main SET status = ?, create_tm = NOW() WHERE batch_id = ?";

        try {
            conn = DatabaseUtils.getConnection();
            pstmt = conn.prepareStatement(sql);
            int index = 1;

            pstmt.setInt(index++, status);
            pstmt.setString(index++, batchId); // WHERE 条件

            int rowsAffected = pstmt.executeUpdate();

            SQLLogUtils.printSQL(" ===updateRtCheckerApplyFormMakerMain : ", pstmt);

            return rowsAffected > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        } finally {
            closeAllConnection(conn, pstmt, null);
        }
    }
}
