<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page import="com.zsdwf.utils.*,com.zsdwf.db.*,java.util.*" %>
<%

String code = request.getParameter("code");
if(Tools.isEmpty(code)){
	out.print("参数有误，请重试");
	return;
}

String SES_VerifyCode = (String)session.getAttribute("SES_VerifyCode");
session.removeAttribute("SES_VerifyCode");
if(!code.equalsIgnoreCase(SES_VerifyCode)){
	out.print("验证码错误，请重新输入验证码");
	return;
}



String no = request.getParameter("no");
String passwd = request.getParameter("passwd");
if(Tools.isEmpty(no) || Tools.isEmpty(passwd)){
	return;
} 
YGJDBC jdbc = new YGJDBC();
YGCardBean card = jdbc.getYGAdminByIDandPasswd(no.trim().toUpperCase(), passwd.trim().toUpperCase());

if(card == null){
	System.out.println("ERR - no:"+no + ", passwd:"+passwd+" ");
	out.print("账号或密码错误，请联系管理员");
}else{
	if(card.getStatus() == 1){
		session.setAttribute("SES_ADMIN", card);
		if(!card.getId().startsWith("FX")){
			out.print("redirect:main");
		}else{
			out.print("redirect:freeze");
		}
	}else{
		out.print("该账号异常，暂无法登录，请联系管理员");
	}
}

%>