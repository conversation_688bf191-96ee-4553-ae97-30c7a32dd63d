<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page import="com.zsdwf.utils.*,com.zsdwf.db.*,java.util.*" %>
<%@include file="/WEB-INF/include/_session_admin_ajax.jsp" %>
<%
YGCardBean card = (YGCardBean)session.getAttribute("SES_ADMIN"); 

YGJDBC jdbc = new YGJDBC();
YGCardBean cardLatest = jdbc.getYGAdminByIDandPasswd(card.getId(), card.getPasswd());

String group_name = Tools.trim(request.getParameter("group_name"));
int nf = Tools.getInt(request.getParameter("nf"));
String lsy_name = Tools.trim(request.getParameter("lsy_name"));
if(group_name.length() < 2 || group_name.equals("%")){
	out.println("ERR:LOGIN:REDIRECT-X_LOGIN");
	return;
}

List<com.career.db.CareerJY> jyListYx = new com.career.db.ZyzdJDBC().pickJiuyeYxByGroupAndLsyForInternel(nf, group_name, lsy_name, 1);
List<com.career.db.CareerJY> jyListZy = new com.career.db.ZyzdJDBC().pickJiuyeZyByGroupAndLsyForInternel(nf, group_name, lsy_name, 1);

List<com.career.db.CareerJY> xlList = new com.career.db.ZyzdJDBC().pickJiuyeYxByGroupAndLsyForInternel_sum_xl(nf, group_name, lsy_name);
List<com.career.db.CareerJY> xbList = new com.career.db.ZyzdJDBC().pickJiuyeYxByGroupAndLsyForInternel_sum_xb(nf, group_name, lsy_name);
List<com.career.db.CareerJY> srcList = new com.career.db.ZyzdJDBC().pickJiuyeYxByGroupAndLsyForInternel_sum_src(nf, group_name, lsy_name);
List<com.career.db.CareerJY> lsyList = new com.career.db.ZyzdJDBC().pickJiuyeYxByGroupAndLsyForInternel_sum_groupname(nf, group_name);


HashSet<String> yxmcSet = new HashSet<String>();
for(com.career.db.CareerJY jy : jyListYx){
	yxmcSet.add(jy.getYxmc());
}


HashMap<String, List<com.career.db.SchoolBean>> ZDF_MAP_LK = new HashMap<>();
HashMap<String, List<com.career.db.SchoolBean>> ZDF_MAP_WK = new HashMap<>();
if(yxmcSet.size() > 0){
	ZDF_MAP_LK = new com.career.db.ZyzdJDBC().listJiuyeZDFByYXMC(yxmcSet, "1", "S5_SC", 2024, 1);  
	ZDF_MAP_WK = new com.career.db.ZyzdJDBC().listJiuyeZDFByYXMC(yxmcSet, "W", "S5_SC", 2024, 1); 
}

%>
<table class="pure-table pure-table-bordered">
    <thead>
    	<tr><th colspan='2'><%=group_name %>.<%=Tools.view(lsy_name) %></th></tr>
        <tr>
            <th>性别</th>
            <th>数量</th>
        </tr>
    </thead>

    <tbody>
    	<%
		for(int i=0;i<xbList.size();i++){ 
			com.career.db.CareerJY element = xbList.get(i);
		%>
    	<tr>
            <td><%=Tools.view(element.getXb()) %></td>
            <td><%=element.getCnt() %></td>
        </tr>
        <%} %>
    </tbody>
</table>
<br><br>

<table class="pure-table pure-table-bordered">
    <thead>
        <tr><th colspan='2'><%=group_name %>.<%=Tools.view(lsy_name) %></th></tr>
        <tr>
            <th>学历</th>
            <th>数量</th>
        </tr>
    </thead>

    <tbody>
    	<%
		for(int i=0;i<xlList.size();i++){ 
			com.career.db.CareerJY element = xlList.get(i);
		%>
    	<tr>
            <td><%=Tools.view(element.getXl()) %></td>
            <td><%=element.getCnt() %></td>
        </tr>
        <%} %>
    </tbody>
</table>
<br><br>
<table class="pure-table pure-table-bordered">
    <thead>
        <tr><th colspan='6'><%=group_name %>.<%=Tools.view(lsy_name) %></th></tr>
        <tr>
            <th>院校名称</th>
            <th>数量</th>
            <th>理科最低分</th>
            <th>理科位次</th>
            <th>文科最低分</th>
            <th>文科位次</th>
        </tr>
    </thead>

    <tbody>
    	<%
		for(int i=0;i<jyListYx.size();i++){ 
			com.career.db.CareerJY element = jyListYx.get(i);
			
			List<com.career.db.SchoolBean> schoolBeanList = ZDF_MAP_LK.get(element.getYxmc());
       		String zdf_lk = null;
       		String zdfwc_lk = null;
			if(schoolBeanList == null || schoolBeanList.size() == 0){
			}else{
				for(com.career.db.SchoolBean school : schoolBeanList){
					zdf_lk = school.getZdf();
					zdfwc_lk = school.getZdfwc();
					break;
				}
			}
			
			schoolBeanList = ZDF_MAP_WK.get(element.getYxmc());
       		String zdf_wk = null;
       		String zdfwc_wk = null;
			if(schoolBeanList == null || schoolBeanList.size() == 0){
			}else{
				for(com.career.db.SchoolBean school : schoolBeanList){
					zdf_wk = school.getZdf();
					zdfwc_wk = school.getZdfwc();
					break;
				}
			}
		%>
    	<tr>
            <td><%=element.getYxmc() %></td>
            <td><%=element.getCnt() %></td>
            <td><%=Tools.view(zdf_lk) %></td>
            <td><%=Tools.view(zdfwc_lk) %></td>
            <td><%=Tools.view(zdf_wk) %></td>
            <td><%=Tools.view(zdfwc_wk) %></td>
        </tr>
        <%} %>
    </tbody>
</table>

<br><br>
<table class="pure-table pure-table-bordered">
    <thead>
        <tr><th colspan='2'><%=group_name %>.<%=Tools.view(lsy_name) %></th></tr>
        <tr>
            <th>专业名称</th>
            <th>数量</th>
        </tr>
    </thead>

    <tbody>
    	<%
		for(int i=0;i<jyListZy.size();i++){ 
			com.career.db.CareerJY element = jyListZy.get(i);
		%>
    	<tr>
            <td><%=Tools.view(element.getZymc()) %></td>
            <td><%=element.getCnt() %></td>
        </tr>
        <%} %>
    </tbody>
</table>

<br><br>
<table class="pure-table pure-table-bordered">
    <thead>
        <tr><th colspan='2'><%=group_name %>.<%=Tools.view(lsy_name) %></th></tr>
        <tr>
            <th>来源</th>
            <th>数量</th>
        </tr>
    </thead>

    <tbody>
    	<%
		for(int i=0;i<srcList.size();i++){ 
			com.career.db.CareerJY element = srcList.get(i);
		%>
    	<tr>
            <td><%=Tools.view(element.getSrc()) %></td>
            <td><%=element.getCnt() %></td>
        </tr>
        <%} %>
    </tbody>
</table>

<br><br>
<table class="pure-table pure-table-bordered">
    <thead>
        <tr>
            <th><%=group_name %>下属单位</th>
            <th>数量</th>
        </tr>
    </thead>

    <tbody>
    	<%
		for(int i=0;i<lsyList.size();i++){ 
			com.career.db.CareerJY element = lsyList.get(i);
		%>
    	<tr>
            <td><%=element.getLsy() %></td>
            <td><%=element.getCnt() %></td>
        </tr>
        <%} %>
    </tbody>
</table>
