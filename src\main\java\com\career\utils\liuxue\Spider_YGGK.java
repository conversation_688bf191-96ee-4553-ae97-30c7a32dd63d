package com.career.utils.liuxue;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.career.db.JDBC;
import com.career.utils.HttpSendUtils;
import com.career.utils.Tools;

public class Spider_YGGK {
	
	// 靖志远抓取网页
	public static StringBuffer YOUZHIYUAN() {
		Map<String, String> headers = new HashMap<>();
		Map<String, String> params = new HashMap<>();
		
		params.put("ProvinceId", "6BD52781-2F8E-4114-8150-5DEB3AC228D3");
		params.put("Year", "2023");
		params.put("SelectSubject", "物理");
		params.put("PageIndex", "1");
		params.put("PageSize", "10");
		
		params.put("BatchIdList", "[]");
		params.put("SchoolIdList", "[]");
		params.put("SchoolProvinceIdList", "[]");
		params.put("SchoolCategoryIdList", "[]");
		params.put("SchoolTagList", "[]");
		params.put("SchoolNature", "[]");
		
	
		headers.put("Accept", "application/json, text/plain, */*");
		headers.put("Accept-Encoding", "gzip, deflate, br, zstd");
		headers.put("Accept-Language", "zh-CN,zh;q=0.9");
		headers.put("Cache-Control", "no-cache");
		headers.put("Connection", "keep-alive");
		headers.put("Content-Type", "application/json; charset=UTF-8");
		headers.put("Host", "tapi.cnjzy.net");
		headers.put("Origin", "https://t.cnjzy.net");
		headers.put("Pragma", "no-cache");
		headers.put("Referer", "https://t.cnjzy.net/");
		headers.put("Sec-Fetch-Dest", "empty");
		headers.put("Sec-Fetch-Mode", "cors");
		headers.put("Sec-Fetch-Site", "same-site");
		headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36");
		headers.put("sec-ch-ua", "\"Chromium\";v=\"130\", \"Google Chrome\";v=\"130\", \"Not?A_Brand\";v=\"99\"");
		headers.put("sec-ch-ua-mobile", "?0");
		headers.put("sec-ch-ua-platform", "\"Windows\"");
		headers.put("token", "6a4eda6d044e45ee862766b332d6e9b5");
		
		StringBuffer SQL = new StringBuffer();

		String resultPageList = HttpSendUtils.post("https://tapi.cnjzy.net/api/Public/LineSchooScoreList", params, headers);
		System.out.println(resultPageList);
		
		
		return SQL;
	}
		
		public static StringBuffer shanghairanking_cn_XUEKE() {
			Map<String, String> headers = new HashMap<>();
			StringBuffer SQL = new StringBuffer();


			String resultPageList = HttpSendUtils2.get("https://www.shanghairanking.cn/_nuxt/static/1728726515/rankings/bcsr/2024/0802/payload.js", headers);
			// System.out.println(resultPageList);
			JSONObject object = JSONObject.parseObject(resultPageList);
			JSONObject JSONObject = object.getJSONObject("data");
			JSONArray JSONArray = JSONObject.getJSONArray("majors");
			for(int i=0;i<JSONArray.size();i++) {
				JSONObject element = JSONArray.getJSONObject(i);
				
				String id = element.getString("id");
				String pid = element.getString("pid");
				String name = element.getString("name");
				String code = element.getString("code");
				int ordNo = element.getIntValue("ordNo");
				String remark = element.getString("remark");
				
				SQL.append(
						"insert into base_major_ruanke(id,pid,name,code,ordno,remark,univPublished,level) values('"+id+"','"
								+ pid + "','" + name + "','" + code + "',"+ordNo+",'"+remark+"',NULL,'1');\r\n");
				
				
				JSONArray newArray = element.getJSONArray("children");
				for(int ix=0;ix<newArray.size();ix++) {
					JSONObject newElement = newArray.getJSONObject(ix);
					
					String id1 = newElement.getString("id");
					String pid1 = newElement.getString("pid");
					String name1 = newElement.getString("name");
					String code1 = newElement.getString("code");
					int ordNo1 = newElement.getIntValue("ordNo");
					String remark1 = newElement.getString("remark");
					
					SQL.append(
							"insert into base_major_ruanke(id,pid,name,code,ordno,remark,univPublished,level) values('"+id1+"','"
									+ pid1 + "','" + name1 + "','" + code1 + "',"+ordNo1+",'"+remark1+"',NULL,'2');\r\n");
					
					JSONArray newArray2 = newElement.getJSONArray("children");
					for(int ixx=0;ixx<newArray2.size();ixx++) {
						JSONObject newElement2 = newArray2.getJSONObject(ixx);
						
						String id2 = newElement2.getString("id");
						String pid2 = newElement2.getString("pid");
						String name2 = newElement2.getString("name");
						String code2 = newElement2.getString("code");
						int ordNo2 = newElement2.getIntValue("ordNo");
						String remark2 = newElement2.getString("remark");
						String univPublished2 = newElement2.getString("univPublished");
						
						SQL.append(
								"insert into base_major_ruanke(id,pid,name,code,ordno,remark,univPublished,level) values('"+id2+"','"
										+ pid2 + "','" + name2 + "','" + code2 + "',"+ordNo2+",'"+remark2+"','"+univPublished2+"','3');\r\n");
					}
				}
				
			}
			
			
			//writeTempFile(new File("F://就业报告//RUANKE/PAGE_1015.txt"), SQL);
			
			return SQL;
		}
		
		
		public static StringBuffer shanghairanking_cn_rankget() {
			Map<String, String> headers = new HashMap<>();
			StringBuffer SQL = new StringBuffer();
			
			
			headers.put("Accept","text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
			headers.put("Accept-Encoding","gzip, deflate, br, zstd");
			headers.put("Accept-Language","zh-CN,zh;q=0.9");
			headers.put("Cache-Control","max-age=0");
			headers.put("Connection","keep-alive");
			headers.put("Cookie","_ga_VEMJM9VZ7D=GS1.1.1726187206.2.0.1726187206.0.0.0; CHSIDSTID=18dda521c6b13c7-0c1864bf74f0a-26001851-510000-18dda521c6c29c2; _ga_RNH4PZV76K=GS1.1.**********.1.1.**********.0.0.0; goaYXsyEWlxdO=60nwMf77.Ahq8KLeygMMvNPsOzRmBZ7GTj1RtOkuhRN6K9BUNKeD_ivroMITafiTyyhivYFkf1b23FttMRqaGbsG; aliyungf_tc=4630d5cc604154a553ea7fba14c80446cd4056e7e79f4560032c50c7fe2ecfe3; CHSICC01=!UMsVfvKHlUrF2fInVPBkiJOoJxwY2hiwc/dWQJLlQeu8WtGOitWGEY0x8SHHco7uwXwDTlIt/cuXKw==; 35067579362dd9cd78=59fbf0c8a101db3bd053546b0c8f0479; XSRF-CCKTOKEN=ab7698ab15c2f02c9a5f72e5dfdcd3af; Hm_lvt_19141110b831c2c573190bb7a3b0ef3f=**********,**********; HMACCOUNT=130147FE24221DE4; _gid=GA1.3.*********.**********; CHSICC_CLIENTFLAGGAOKAO=bd7a8e108dde627b4554b1d1d812ec2f; _abfpc=fd98585bfc13adcd5f192c769ac8046388fd4f80_2.0; cna=1a5f26d2a4fdeb43d808c7282798a451; CHSICC_CLIENTFLAGGAOKAOZZBM=36d74a212067b06b0065dbeca11e53f5; CHSICC_CLIENTFLAGCHSI=b21876ba6439379177792112c53c5886; CHSICC_CLIENTFLAGSPECIALITY=37a442206b447168512f53bea63324ac; CHSICC_CLIENTFLAGZYCK=df76b86fe78c5a3c9c2a35be83579e47; _ga_YKHKPH396P=GS1.1.**********.3.1.**********.0.0.0; _ga_TT7MCH8RRF=GS1.1.**********.4.1.**********.0.0.0; _ga_5FHT145N0D=deleted; _ga_5FHT145N0D=deleted; JSESSIONID=E5DE9D7D31E0340AA4A944070DD25459; CHSICC_CLIENTFLAGGAOKAOZYBK=e00c466ab38a257e107a91b518ead6ee; Hm_lpvt_19141110b831c2c573190bb7a3b0ef3f=**********; _ga=GA1.1.*********.**********; _ga_5FHT145N0D=GS1.1.**********.1.1.**********.0.0.0; goaYXsyEWlxdP=05PT0QfkK9lCOSNjplXfRAgXVw94vcr_dxg2o0GBwY7EejMmlGhLZvqOT1tN6lrshafDn7M10arufNKE9VnOPrHm_bfC2N3dl8M6_A2lylrcsjicnTP1aaUEW6fiz.eXWtfpphOQLdq1izrs0BZoNtxCKE_KIvwFUdXWWE.vDixO43Ns99dhq.ZIm3N.bgDvuS8OiZeGSC61hQ8EAsGgKVW0o97CcpCDS4fwP4x2_OHjOpBoxTCgl7e8lzXJilPIC0BIkMar8nFH80uMVmYtfEkc.Rc7LVPdsLpmnnHIs6h4jC5syVmDArf3Y2_2dwqrwgmLn1uQwjtpWfweEMqtkzLEjKWWH_o0R5Vl6K._7sJRDVGdhLeORAAH7p0gkKdC.LejAywg.TFrumF8ADGtvejE.EMTEuU86SULxdB9MWFBOkRsVUzMk3O.iznheBg2UAcjUyQg38VX0AcJRWnguoA");
			headers.put("Host","gaokao.chsi.com.cn");
			headers.put("Sec-Fetch-Dest","document");
			headers.put("Sec-Fetch-Mode","navigate");
			headers.put("Sec-Fetch-Site","none");
			headers.put("Sec-Fetch-User","?1");
			headers.put("Upgrade-Insecure-Requests","1");
			headers.put("User-Agent","Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
			headers.put("sec-ch-ua","\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"");
			headers.put("sec-ch-ua-mobile","?0");
			headers.put("sec-ch-ua-platform","\"Windows\"");


			
			int cnt = 50;
			String cookie = null;
			for(int k =0;k<29;k++) {
				
				cookie = new JDBC().getCookie();
				
				headers.put("Cookie", cookie);

				System.out.println("execute--> "+k);
				int cur_record_start = k * 20;
				//String url = "https://gaokao.chsi.com.cn/zsgs/zhangcheng/listVerifedZszc--method-index,ssdm-,yxls-,xlcc-,zgsx-,yxjbz-,start-"+cur_record_start+".dhtml";
				
				String url = "https://gaokao.chsi.com.cn/sch/search--searchType-1,ssdm-51,start-"+cur_record_start+".dhtml";
				String listPage = HttpSendUtils2.get(url, headers);
				Document listPageDoc = Jsoup.parse(listPage);
				Elements listSchtitle = listPageDoc.getElementsByClass("sch-title");
				for(int kk =0;kk<listSchtitle.size();kk++) {
					
					if(cnt % 20 == 0) {
						try {
							System.out.println("WAIT COOKIE--> "+k);
							Thread.sleep(1000);
						} catch (InterruptedException e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
						cookie = new JDBC().getCookie();
					}
					
					
					Elements aTag = listSchtitle.get(kk).getElementsByTag("a");
					Element aTagItem = aTag.get(0);
					String yxmc = aTagItem.text();
					String urlLink = aTagItem.attr("href");
					
					String uid = urlLink.substring(urlLink.indexOf("schId-")+6 , urlLink.indexOf(".dhtml"));
					if(Tools.isEmpty(uid)) {
						continue;
					}
					
					if(cnt > 0) {
						//break;
					}
					cnt++;
					
					
					String mainUrl = "https://gaokao.chsi.com.cn/sch/schoolInfoMain--schId-"+uid+".dhtml";
					String resultMainPageList = HttpSendUtils2.get(mainUrl,headers);
					Document documentMainList = Jsoup.parse(resultMainPageList);
					Elements mainList = documentMainList.getElementsByClass("nav-wrapper");
					if(mainList.size() == 0) {
						System.out.println("EXCEPTION:"+k+"/"+uid);
						continue;
					}
					Elements subAList = mainList.get(0).getElementsByTag("a");

					
					System.out.println("mainUrl--> "+k + "/" + uid+"-"+mainUrl);
					
					try {
						String detail_jj = HttpSendUtils2.get("https://gaokao.chsi.com.cn"+subAList.get(1).attr("href"), headers); //学校简介
						Document detail_jj_page = Jsoup.parse(detail_jj);
						Elements detail_jj_page_html = detail_jj_page.getElementsByClass("yxk-nav-box");
						String detail_jj_content = detail_jj_page_html.get(0).html();
						SQL.append("insert into zyzd_yx_detail_content(yxmc, desp, ct_type, uid) values('"+yxmc+"','"+detail_jj_content.replaceAll("'Times New Roman'", "Times New Roman")+"','学校简介','"+uid+"');\r\n");
						
						String detail_yxsz = HttpSendUtils2.get("https://gaokao.chsi.com.cn"+subAList.get(2).attr("href"), headers); //院系设置
						Document detail_yxsz_page = Jsoup.parse(detail_yxsz);
						Elements detail_yxsz_page_html = detail_yxsz_page.getElementsByClass("yxk-nav-box");
						String detail_yxsz_content = detail_yxsz_page_html.get(0).html();
						SQL.append("insert into zyzd_yx_detail_content(yxmc, desp, ct_type, uid) values('"+yxmc+"','"+detail_yxsz_content.replaceAll("'Times New Roman'", "Times New Roman")+"','院系设置','"+uid+"');\r\n");
						
						
						String detail_zyjs = HttpSendUtils2.get("https://gaokao.chsi.com.cn"+subAList.get(3).attr("href"), headers); //专业介绍
						Document detail_zyjs_page = Jsoup.parse(detail_zyjs);
						Elements detail_zyjs_page_html = detail_zyjs_page.getElementsByClass("yxk-nav-box");
						String detail_zyjs_content = detail_zyjs_page_html.get(0).html();
						SQL.append("insert into zyzd_yx_detail_content(yxmc, desp, ct_type, uid) values('"+yxmc+"','"+detail_zyjs_content.replaceAll("'Times New Roman'", "Times New Roman")+"','专业介绍','"+uid+"');\r\n");
						
						String detail_lqgz = HttpSendUtils2.get("https://gaokao.chsi.com.cn"+subAList.get(4).attr("href"), headers); //录取规则
						Document detail_lqgz_page = Jsoup.parse(detail_lqgz);
						Elements detail_lqgz_page_html = detail_lqgz_page.getElementsByClass("yxk-nav-box");
						String detail_lqgz_content = detail_lqgz_page_html.get(0).html();
						SQL.append("insert into zyzd_yx_detail_content(yxmc, desp, ct_type, uid) values('"+yxmc+"','"+detail_lqgz_content.replaceAll("'Times New Roman'", "Times New Roman")+"','录取规则','"+uid+"');\r\n");
						
						String detail_jxjsz = HttpSendUtils2.get("https://gaokao.chsi.com.cn"+subAList.get(5).attr("href"), headers); //奖学金设置
						Document detail_jxjsz_page = Jsoup.parse(detail_jxjsz);
						Elements detail_jxjsz_page_html = detail_jxjsz_page.getElementsByClass("yxk-nav-box");
						String detail_jxjsz_content = detail_jxjsz_page_html.get(0).html();
						SQL.append("insert into zyzd_yx_detail_content(yxmc, desp, ct_type, uid) values('"+yxmc+"','"+detail_jxjsz_content.replaceAll("'Times New Roman'", "Times New Roman")+"','奖学金设置','"+uid+"');\r\n");
						
						
						String detail_sstj = HttpSendUtils2.get("https://gaokao.chsi.com.cn"+subAList.get(6).attr("href"), headers); //食宿条件
						Document detail_sstj_page = Jsoup.parse(detail_sstj);
						Elements detail_sstj_page_html = detail_sstj_page.getElementsByClass("yxk-nav-box");
						String detail_sstj_content = detail_sstj_page_html.get(0).html();
						SQL.append("insert into zyzd_yx_detail_content(yxmc, desp, ct_type, uid) values('"+yxmc+"','"+detail_sstj_content.replaceAll("'Times New Roman'", "Times New Roman")+"','食宿条件','"+uid+"');\r\n");
						
						String detail_bysjy = HttpSendUtils2.get("https://gaokao.chsi.com.cn"+subAList.get(10).attr("href"), headers); //毕业生就业
						Document detail_bysjy_page = Jsoup.parse(detail_bysjy);
						Elements detail_bysjy_page_html = detail_bysjy_page.getElementsByClass("yxk-nav-box");
						String detail_bysjy_content = detail_bysjy_page_html.get(0).html();
						SQL.append("insert into zyzd_yx_detail_content(yxmc, desp, ct_type, uid) values('"+yxmc+"','"+detail_bysjy_content.replaceAll("'Times New Roman'", "Times New Roman")+"','毕业生就业','"+uid+"');\r\n");
						
						
						String detail_tjyq = HttpSendUtils2.get("https://gaokao.chsi.com.cn"+subAList.get(13).attr("href"), headers); //体检要求
						Document detail_tjyq_page = Jsoup.parse(detail_tjyq);
						Elements detail_tjyq_page_html = detail_tjyq_page.getElementsByClass("yxk-nav-box");
						String detail_tjyq_content = detail_tjyq_page_html.get(0).html();
						SQL.append("insert into zyzd_yx_detail_content(yxmc, desp, ct_type, uid) values('"+yxmc+"','"+detail_tjyq_content.replaceAll("'Times New Roman'", "Times New Roman")+"','体检要求','"+uid+"');\r\n");
						
					}catch(Exception ex) {
						System.out.println("ERR:mainUrl--> "+k + "/" + uid);
						//ex.printStackTrace();
					}
					
					
					
					try {
						SQL.append("insert into zyzd_yx_url_temp(uid, url, bz) values('"+uid+"','"+"https://gaokao.chsi.com.cn"+subAList.get(1).attr("href")+"','学校简介');\r\n");
						SQL.append("insert into zyzd_yx_url_temp(uid, url, bz) values('"+uid+"','"+"https://gaokao.chsi.com.cn"+subAList.get(2).attr("href")+"','院系设置');\r\n");
						SQL.append("insert into zyzd_yx_url_temp(uid, url, bz) values('"+uid+"','"+"https://gaokao.chsi.com.cn"+subAList.get(3).attr("href")+"','专业介绍');\r\n");
						SQL.append("insert into zyzd_yx_url_temp(uid, url, bz) values('"+uid+"','"+"https://gaokao.chsi.com.cn"+subAList.get(4).attr("href")+"','录取规则');\r\n");
						SQL.append("insert into zyzd_yx_url_temp(uid, url, bz) values('"+uid+"','"+"https://gaokao.chsi.com.cn"+subAList.get(5).attr("href")+"','奖学金设置');\r\n");
						SQL.append("insert into zyzd_yx_url_temp(uid, url, bz) values('"+uid+"','"+"https://gaokao.chsi.com.cn"+subAList.get(6).attr("href")+"','食宿条件');\r\n");
						SQL.append("insert into zyzd_yx_url_temp(uid, url, bz) values('"+uid+"','"+"https://gaokao.chsi.com.cn"+subAList.get(10).attr("href")+"','毕业生就业');\r\n");
						SQL.append("insert into zyzd_yx_url_temp(uid, url, bz) values('"+uid+"','"+"https://gaokao.chsi.com.cn"+subAList.get(13).attr("href")+"','体检要求');\r\n");
						
						
						String jzMainUrl = "https://gaokao.chsi.com.cn/zsgs/zhangcheng/listZszc--schId-"+uid+".dhtml";
						String resultJZPageList = HttpSendUtils2.get(jzMainUrl,headers);
						Document documentList = Jsoup.parse(resultJZPageList);
						Elements aiticleList = documentList.getElementsByClass("zszc-zc-title");
						String jz_href = aiticleList.get(0).attr("href");
						String jz_title = aiticleList.get(0).text();
						
						String resultJZDetail = HttpSendUtils2.get("https://gaokao.chsi.com.cn"+jz_href,headers);
						Document jz_detail = Jsoup.parse(resultJZDetail);
						
						System.out.println("jz_href--> "+k + "/" + uid+"-"+jz_href);
						
						Elements jz_detail_html = jz_detail.getElementsByClass("zszc-2022-containter");
						String jz_content = jz_detail_html.get(0).html();
						
						SQL.append("insert into zyzd_yx_url_temp(uid, url, bz) values('"+uid+"','"+"https://gaokao.chsi.com.cn"+jz_href+"','简章');\r\n");
						SQL.append("insert into zyzd_yx_detail_jz(nf, yxmc, jz_title, jz_content, uid) values(2024, '"+yxmc+"','"+jz_title+"','"+jz_content.replaceAll("\'", "&apos;")+"','"+uid+"');\r\n");
					}catch(Exception ex) {
						System.out.println("ERR:jz_href--> "+k + "/" + uid);
						//ex.printStackTrace();
					}
				}
				
				
				writeTempFile(new File("F://就业报告//RUANKE/jiuye/"+k+".txt"), SQL);
			}
			
			
			
			
			return SQL;
		}

		public static void main(String[] args) {
			shanghairanking_cn_rankget();

		}
		
		private static void writeTempFile(File file, StringBuffer sb) {
			try {
				BufferedWriter bw = new BufferedWriter(new FileWriter(file));
				bw.write(sb.toString());
				bw.flush();
				bw.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}

}
