package com.career.db;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SpiderJDBC {

	static String URL = JDBCConstants.URL;
	static String USER = JDBCConstants.USER;
	static String PASSWD = JDBCConstants.PASSWD;

	
	static {
		try {
			Class.forName("com.mysql.cj.jdbc.Driver");
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
	
	public int getMaxOffcnid() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT max(offcn_id) FROM career_ext_url";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt(1);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public int getMaxJOBNCSSCNid() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT max(offcn_id) FROM career_ext_url";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt(1);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public SpiderInfoBean getSpiderInfo(String spider_name) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		SpiderInfoBean bean = null;
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT * FROM career_ext_url_spider WHERE spider_name = ?";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, spider_name);
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = new SpiderInfoBean();
				bean.setSpider_last_id(rs.getString("spider_last_id"));
				bean.setSpider_last_time(rs.getString("spider_last_time"));
				bean.setSpider_name(rs.getString("spider_name"));
				bean.setSpider_status(rs.getInt("spider_status"));
				bean.setSpider_per_cnt(rs.getInt("spider_per_cnt"));
				bean.setSpider_per_interval(rs.getInt("spider_per_interval"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}
	
	public void updateSpiderInfo(SpiderInfoBean spiderInfoBean) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "UPDATE career_ext_url_spider SET spider_last_id = ? , spider_last_time = ?, spider_status = ? WHERE spider_name = ?";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, spiderInfoBean.getSpider_last_id());
			ps.setString(2, spiderInfoBean.getSpider_last_time());
			ps.setInt(3, spiderInfoBean.getSpider_status());
			ps.setString(4, spiderInfoBean.getSpider_name());
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public int getExtUrlCount(String url) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT count(*) FROM career_ext_url where view_status = 1 and url = ?";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, url);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt(1);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public List<OffcnExtUrlMapBean> getExtUrlMapProvince(String url_domain) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<OffcnExtUrlMapBean> list = new ArrayList<>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT * FROM career_ext_url_map where url_domain = ?";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, url_domain);
			rs = ps.executeQuery();
			OffcnExtUrlMapBean bean = null;
			while (rs.next()) {
				bean = new OffcnExtUrlMapBean();
				bean.setUrl_cs(rs.getString("url_cs"));
				bean.setUrl_domain(rs.getString("url_domain"));
				bean.setUrl_from(rs.getString("url_from"));
				bean.setUrl_qy(rs.getString("url_qy"));
				bean.setUrl_sf(rs.getString("url_sf"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<OffcnExtUrlBean> getLatestOffcnExtUrl(int status, int pageSize) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<OffcnExtUrlBean> list = new ArrayList<>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT * FROM career_ext_url where view_status = "+status+" order by offcn_id desc limit 0," + pageSize;
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			OffcnExtUrlBean bean = null;
			while (rs.next()) {
				bean = new OffcnExtUrlBean();
				bean.setCreate_time(new Date(rs.getTimestamp("create_time").getTime()));
				bean.setOffcn_id(rs.getInt("offcn_id"));
				bean.setPublish_time(rs.getString("publish_time"));
				bean.setTitle(rs.getString("title"));
				bean.setTitle_org(rs.getString("title_org"));
				bean.setUrl(rs.getString("url"));
				bean.setUrl_domain(rs.getString("url_domain"));
				bean.setUrl_from(rs.getString("url_from"));
				bean.setSf(rs.getString("url_sf"));
				bean.setCs(rs.getString("url_cs"));
				bean.setView_status(status);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<OffcnExtUrlBean> getLatestOffcnExtUrl(String keywords, int status, int pageSize) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<OffcnExtUrlBean> list = new ArrayList<>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT * FROM career_ext_url where view_status = "+status+" and title like ? order by offcn_id desc limit 0," + pageSize;
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + keywords + "%");
			rs = ps.executeQuery();
			OffcnExtUrlBean bean = null;
			while (rs.next()) {
				bean = new OffcnExtUrlBean();
				bean.setCreate_time(new Date(rs.getTimestamp("create_time").getTime()));
				bean.setOffcn_id(rs.getInt("offcn_id"));
				bean.setPublish_time(rs.getString("publish_time"));
				bean.setTitle(rs.getString("title"));
				bean.setTitle_org(rs.getString("title_org"));
				bean.setUrl(rs.getString("url"));
				bean.setUrl_domain(rs.getString("url_domain"));
				bean.setUrl_from(rs.getString("url_from"));
				bean.setSf(rs.getString("url_sf"));
				bean.setCs(rs.getString("url_cs"));
				bean.setView_status(status);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public boolean insertOffcnExtUrl(OffcnExtUrlBean bean) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "insert into career_ext_url(offcn_id,url_from, title, title_org, url, url_domain, publish_time, create_time, view_status, url_sf, url_cs) values(?,?,?,?,?,?,?,now(),?,?,?)";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, bean.getOffcn_id());
			ps.setString(2, bean.getUrl_from());
			ps.setString(3, bean.getTitle());
			ps.setString(4, bean.getTitle_org());
			ps.setString(5, bean.getUrl());
			ps.setString(6, bean.getUrl_domain());
			ps.setString(7, bean.getPublish_time());
			ps.setInt(8, bean.getView_status());
			ps.setString(9, bean.getSf());
			ps.setString(10, bean.getCs());
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}

	private static void closeAllConnection(Connection conn, PreparedStatement ps, ResultSet rs) {
		try {
			if (rs != null)
				rs.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (ps != null)
				ps.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (conn != null)
				conn.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		rs = null;
		ps = null;
		conn = null;
	}
}
