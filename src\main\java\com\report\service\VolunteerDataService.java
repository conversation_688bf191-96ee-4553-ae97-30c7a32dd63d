package com.report.service;

import com.career.db.LhyFormMain;
import com.career.db.LhyForm;
import com.career.db.LhyJDBC;
import com.career.db.LhyOrder;
import com.career.db.ZyzdBaseMajor;
import com.career.db.ZyzdProvinceConfig;
import com.career.db.ZyzdUniversityBean;
import com.career.utils.AdmitBean;
import com.career.utils.CalculateAdmit;
import com.career.utils.Tools;
import com.career.utils.ZyzdCache;
import com.report.entity.VolunteerData;
import com.report.entity.JobStatistics;
import com.report.db.ReportJDBC;
import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;

/**
 * 
 * 志愿数据服务类
 * 用于获取学生的志愿填报信息
 * 
 */
public class VolunteerDataService {
    
    
    private static HashMap<String, ZyzdUniversityBean> zyzdUniversityBeanMap = new LinkedHashMap<>();
    
    private static HashMap<String, ZyzdBaseMajor> zyzdBaseMajorMap = new LinkedHashMap<>();
    
    /**
     * 判断是否为96志愿模式
     * @param sfCode 省份代码
     * @return true表示96志愿模式，false表示传统模式
     */
    public static boolean is96Mode(String sfCode) {
        if (sfCode == null || sfCode.trim().isEmpty()) {
            return false;
        }
        
        try {
            ZyzdProvinceConfig provinceConfig = ZyzdCache.getProvinceConfig(sfCode);
            if (provinceConfig == null) {
                System.err.println("省份配置未找到，无法判断志愿模式: " + sfCode);
                return false;
            }
            
            boolean is96 = (provinceConfig.getForm_type() == 2);
            Tools.println("省份 " + sfCode + " 志愿模式: " + (is96 ? "96志愿模式" : "传统模式"));
            return is96;
            
        } catch (Exception e) {
            System.err.println("判断志愿模式时发生错误: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }


    /**
     * 根据batch_id和sf_code获取志愿数据
     * @param batchId 批次ID
     * @param sfCode 省份代码
     * @return 志愿数据对象，如果没有数据则返回null
     */
    public static VolunteerData getVolunteerData(String batchId, String sfCode) {
        
    	if (batchId == null || batchId.trim().isEmpty() || 
            sfCode == null || sfCode.trim().isEmpty()) {
            return null;
        }
        
        try {
            LhyJDBC lhyJdbc = new LhyJDBC();
            ZyzdProvinceConfig provinceConfig = ZyzdCache.getProvinceConfig(sfCode);
            
            if (provinceConfig == null) {
                System.err.println("省份配置未找到: " + sfCode);
                return null;
            }
            
            String tableCode = provinceConfig.getP_table_code();
            
            // 获取申请表单主信息
            LhyFormMain formMain = lhyJdbc.getMakerFormMainByBatchId(tableCode, batchId);
            
            if (formMain == null) {
                Tools.println("未找到志愿表单主信息 - batchId: " + batchId + ", tableCode: " + tableCode);
                return null;
            }
            
            
            // 查询学生姓名
            LhyOrder lhyOrder = lhyJdbc.getLhyPaidOrder(formMain.getOrder_id());
            formMain.setExt_stu_name(lhyOrder.getStu_info_name());
            
            
            // 获取志愿表单列表
            List<LhyForm> formList = lhyJdbc.getMakerForm(tableCode, formMain.getBatch_id());
            
            for(LhyForm lhyForm : formList) {
            	ZyzdUniversityBean zyzdUniversityBean = ZyzdCache.getUniversity(lhyForm.getYxmc());
            	ZyzdBaseMajor zyzdBaseMajor = ZyzdCache.getZymlByZymc(formMain.getPc_code(), lhyForm.getZymc_org());
            	if(zyzdUniversityBean != null) {
            		zyzdUniversityBeanMap.put(zyzdUniversityBean.getYxmc(), zyzdUniversityBean);
            	}
            	if(zyzdBaseMajor != null) {
            		zyzdBaseMajorMap.put(zyzdBaseMajor.getM_zymc(), zyzdBaseMajor);
            	}
            }
            
            if (formList == null || formList.isEmpty()) {
                Tools.println("未找到志愿表单列表 - batchId: " + formMain.getBatch_id() + ", tableCode: " + tableCode);
            }
            
            VolunteerData volunteerData = new VolunteerData(formMain, formList);
            
            // 设置院校和专业详细信息Map到VolunteerData中
            volunteerData.setUniversityMap(zyzdUniversityBeanMap);
            volunteerData.setMajorMap(zyzdBaseMajorMap);
            
            Tools.println("成功获取志愿数据 - 主信息: " + (formMain != null ? "有" : "无") +  ", 志愿数量: " + (formList != null ? formList.size() : 0) 
                + ", 院校信息: " + zyzdUniversityBeanMap.size() + "个, 专业信息: " + zyzdBaseMajorMap.size() + "个");
            
            // 获取预测录取数据
            List<AdmitBean> admitBeanList = CalculateAdmit.getLhySchoolStatisticsForm(provinceConfig, formMain);
            
            // 只取前3条数据设置到VolunteerData中
            if (admitBeanList != null && !admitBeanList.isEmpty()) {
                List<AdmitBean> top3AdmitBeans = admitBeanList.size() <= 3 ? admitBeanList : admitBeanList.subList(0, 3);
                volunteerData.setAdmitBeanList(top3AdmitBeans);
                Tools.println("设置预测录取数据 - 总数: " + admitBeanList.size() + ", 设置数量: " + top3AdmitBeans.size());
                
                // 查询就业统计数据
                try {
                    ReportJDBC reportJdbc = new ReportJDBC();
                    
                    // 1. 原有的院校+专业组合查询
                    List<List<JobStatistics>> jobStatisticsList = new ArrayList<>();
                    
                    // 2. 按院校名称查询
                    List<List<JobStatistics>> schoolJobStatisticsList = new ArrayList<>();
                    List<String> schoolNames = new ArrayList<>();
                    
                    // 3. 按专业名称查询
                    List<List<JobStatistics>> majorJobStatisticsList = new ArrayList<>();
                    List<String> majorNames = new ArrayList<>();
                    
                    for (AdmitBean admitBean : top3AdmitBeans) {
                        String yxmc = admitBean.getYxmc();
                        String zymc = admitBean.getZymc();
                        
                        // 原有的院校+专业组合查询
                        List<JobStatistics> jobStats = reportJdbc.getJobStatisticsBySchoolAndMajor(yxmc, zymc);
                        jobStatisticsList.add(jobStats);
                        
                        // 收集院校和专业名称用于批量查询
                        schoolNames.add(yxmc);
                        majorNames.add(zymc);
                        
                        Tools.println("查询就业数据 - 院校: " + yxmc + ", 专业: " + zymc + ", 数据条数: " + jobStats.size());
                    }
                    
                    // 批量查询院校就业数据
                    schoolJobStatisticsList = reportJdbc.batchGetJobStatisticsBySchools(schoolNames);
                    Tools.println("批量查询院校就业数据完成，共 " + schoolJobStatisticsList.size() + " 组数据");
                    
                    // 批量查询专业就业数据
                    majorJobStatisticsList = reportJdbc.batchGetJobStatisticsByMajors(majorNames);
                    Tools.println("批量查询专业就业数据完成，共 " + majorJobStatisticsList.size() + " 组数据");
                    
                    // 设置到VolunteerData中
                    volunteerData.setJobStatisticsList(jobStatisticsList);
                    volunteerData.setSchoolJobStatisticsList(schoolJobStatisticsList);
                    volunteerData.setMajorJobStatisticsList(majorJobStatisticsList);
                    
                    Tools.println("成功设置就业统计数据：");
                    Tools.println("- 院校+专业组合数据: " + jobStatisticsList.size() + " 组");
                    Tools.println("- 院校数据: " + schoolJobStatisticsList.size() + " 组");
                    Tools.println("- 专业数据: " + majorJobStatisticsList.size() + " 组");
                    
                } catch (Exception e) {
                    System.err.println("查询就业统计数据时发生错误: " + e.getMessage());
                    e.printStackTrace();
                }
                
            } else {
                Tools.println("预测录取数据为空");
            }
            
            return volunteerData;
            
        } catch (Exception e) {
            System.err.println("获取志愿数据时发生错误: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
} 