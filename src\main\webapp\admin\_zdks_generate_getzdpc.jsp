<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page import="com.zsdwf.utils.*,com.zsdwf.db.*,java.util.*" %>
<%@include file="/WEB-INF/include/_session_admin_ajax.jsp"%>
<%
String prov = Tools.trim(request.getParameter("prov"));

HashMap<String, List<com.career.db.CityBean>> mapProvCity = com.career.db.JDBC.HM_PROVINCE_CITY;
HashMap<String, List<com.career.db.ZDPCBean>> mapProvZdpc = com.career.db.JDBC.getAllCitiesZDPCAsMap();

List<com.career.db.CityBean> cityListOption = new ArrayList<>();
Iterator<String> its = mapProvCity.keySet().iterator();
while(its.hasNext()){
	String val = its.next();
	if(prov.equals(val)){
		cityListOption = mapProvCity.get(val);
	}
}

List<com.career.db.ZDPCBean> zdpcListOption = new ArrayList<>();
its = mapProvZdpc.keySet().iterator();
while(its.hasNext()){
	String val = its.next();
	if(prov.equals(val)){
		zdpcListOption = mapProvZdpc.get(val);
	}
}

int YEAR = 2025;
List<com.career.db.ZyzdProvPcx> pcxList = new com.career.db.ZyzdJDBC().getPcx(prov, YEAR);

List<com.career.db.ZyzdScoreConvert> existList = new com.career.db.ZyzdJDBC().getExistRecord(prov);


String provinceCode = JDBC.HM_PROVINCE_CODE.get(prov);
List<com.career.db.ZdksHxb> hxbList = new com.career.db.ZyzdJDBC().getZdksHxb(provinceCode);

String proviceTongkao = "北京天津上海重庆".indexOf(prov) != -1? "全市统考" : "全省统考";
%>

<div style="margin:5px;">
	<div class="query_rec_label">诊断批次：</div>
	<div style="float:left;margin-right:10px;">
		<select style="width:250px;height:35px;" id="zdpc">
			<%
			for(int i=0;i<zdpcListOption.size();i++){
				com.career.db.ZDPCBean bean = zdpcListOption.get(i);
			%>
			<option value="<%=bean.getZdpc()%>"><%=bean.getZdpc()%></option>
			<%} %>
		</select>
	</div>
	
	<div style="clear:both;"></div>
</div>

<div style="margin:5px;">
	<div class="query_rec_label">诊断城市：</div>
	<div style="float:left;margin-right:10px;">
		<select style="width:200px;height:35px;" id="city">
			<option value="<%=proviceTongkao %>"><%=proviceTongkao %></option>
			<%
			for(int i=0;i<cityListOption.size();i++){
				com.career.db.CityBean bean = cityListOption.get(i);
			%>
			<option value="<%=bean.getCityNameExt()%>"><%=bean.getCityNameExt()%></option>
			<%} %>
		</select>
	</div>
	
	<div style="clear:both;"></div>
</div>

<div style="margin:5px;">
	<div class="query_rec_label">对应划线：</div>
	<div style="float:left;margin-right:10px;">
		<select style="width:250px;height:35px;" id="hxb_id">
			<%
			for(int i=0;i<hxbList.size();i++){
				com.career.db.ZdksHxb bean = hxbList.get(i);
			%>
			<option value="<%=bean.getId()%>">[<%=bean.getZd_rq()%>]<%=bean.getZd_name()%><%=bean.getZd_desc()==null?"":bean.getZd_desc()%></option>
			<%} %>
		</select>
	</div>
	
	<div style="clear:both;"></div>
</div>

<%
for(int i=0;i<pcxList.size();i++){ 
	com.career.db.ZyzdProvPcx pcxElement = pcxList.get(i);
%>
<input type="hidden" value="<%=pcxElement.getPcx_name() %>" id="PCX_<%=i+1%>"/>
<div style="margin:3px;width:400px;">
	<div class="query_rec_label"><%=pcxElement.getPcx_name() %>：</div>
	<div style="float:left;margin-right:10px;">
		理科：
		 <input type="text" style="width:40px;height:30px;font-size:16px;color:blue;font-weight:bold;" value="<%=pcxElement.getPcx_wl() %>" id="L_A_<%=i+1%>"/> ->
		 <input type="text" style="width:40px;height:30px;font-size:16px;color:red;font-weight:bold;" value="" id="L_<%=i+1%>"/>
		 
	</div>
	<div style="float:left;">
		文科：
		 <input type="text" style="width:40px;height:30px;font-size:16px;color:blue;font-weight:bold;" value="<%=pcxElement.getPcx_ls() %>" id="W_A_<%=i+1%>"/> ->
		 <input type="text" style="width:40px;height:30px;font-size:16px;color:red;font-weight:bold;" value="" id="W_<%=i+1%>"/>
	</div>
	<div style="clear:both;"></div>
</div>
<%} %>

<div style="margin:5px;">
	<div class="query_rec_label"></div>
	<div style="float:left;margin:10px 2px;">
		<input type="button" style="width:145px;height:40px;font-size:14px;" value="开始换算" onclick="MM_addTel();"/>
	</div>
	<div style="clear:both;"></div>
</div>

<div style="margin-top:15px;width:400px; height:400px;over-flow:scroll;">
<table class="pure-table pure-table-bordered">
    <thead>
        <tr>
            <th>省份</th>
            <th>年份</th>
            <th>城市</th>
            <th>诊断批次</th>
            <th>记录数量</th>
            <th>划线ID</th>
            <th>换算ID</th>
        </tr>
    </thead>

    <tbody>
    	<%
		for(int i=0;i<existList.size();i++){ 
			com.career.db.ZyzdScoreConvert pcxElement = existList.get(i);
		%>
    	<tr>
            <td><%=pcxElement.getSf() %></td>
            <td><%=pcxElement.getNf() %></td>
            <td><%=pcxElement.getCity() %></td>
            <td><%=pcxElement.getZdpc() %></td>
            <td><%=pcxElement.getExt_count() %></td>
            <td><%=Tools.view(pcxElement.getHxb_id()) %></td>
            <td><%=Tools.view(pcxElement.getHistory_id()) %></td>
        </tr>
        <%} %>
    </tbody>
</table>
</div>

