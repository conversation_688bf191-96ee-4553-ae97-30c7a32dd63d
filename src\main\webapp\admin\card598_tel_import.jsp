<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page import="com.zsdwf.utils.*,com.zsdwf.db.*,java.util.*" %>
<%@include file="/WEB-INF/include/_session_admin.jsp" %>
<%
YGCardBean card = (YGCardBean)session.getAttribute("SES_ADMIN"); 

YGJDBC jdbc = new YGJDBC();
YGCardBean cardLatest = jdbc.getYGAdminByIDandPasswd(card.getId(), card.getPasswd());

int cardACnt = cardLatest.getCardACnt();
int cardBCnt = cardLatest.getCardBCnt();
int cardCCnt = cardLatest.getCardCCnt();
int cardDCnt = cardLatest.getCardDCnt();
int cardECnt = cardLatest.getCardECnt();
int cardFCnt = cardLatest.getCardFCnt();
int cardGCnt = cardLatest.getCardGCnt();

%>
<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"> 
<meta name="format-dection" content="telephone=no"/>
<title><%=CCache.SYS_TITLE_NAME %></title>
<script src="<%=request.getContextPath()%>/sources/jquery-3.6.4.min.js"></script>
<style type="text/css">
html {
    font-family: sans-serif;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
}
 
body {
    margin: 10px;
    font-size:12px;
}
table {
    border-collapse: collapse;
    border-spacing: 0;
}
 
td,th {
    padding: 0;
}
 
.pure-table {
    border-collapse: collapse;
    border-spacing: 0;
    empty-cells: show;
    border: 1px solid #cbcbcb;
}
 
.pure-table caption {
    color: #000;
    font: italic 85%/1 arial,sans-serif;
    padding: 1em 0;
    text-align: center;
}
 
.pure-table td,.pure-table th {
    border-left: 1px solid #cbcbcb;
    border-width: 0 0 0 1px;
    font-size: inherit;
    margin: 0;
    overflow: visible;
    padding: .5em 1em;
}
 
.pure-table thead {
    background-color: #e0e0e0;
    color: #000;
    text-align: left;
    vertical-align: bottom;
}
 
.pure-table td {
    background-color: transparent;
}
 
.pure-table-bordered td {
    border-bottom: 1px solid #cbcbcb;
}
 
.pure-table-bordered tbody>tr:last-child>td {
    border-bottom-width: 0;
}

.query_rec_label{
	float:left;width:140px;height:25px;line-height:25px;font-weight:bold;margin:2px 1px;
}
.query_rec_label2{
	float:left;width:90px;height:25px;line-height:25px;font-weight:bold;margin:2px 1px;
}
.query_rec_aa{
	float:left;width:130px;font-size:10px;height:25px;line-height:25px;margin:2px;border:1px solid #7766cc;text-align:center;cursor:pointer;
}
.query_rec_a{
	float:left;width:100px;font-size:10px;height:25px;line-height:25px;margin:2px;border:1px solid #7766cc;text-align:center;cursor:pointer;
}
.query_rec_b{
	float:left;width:60px;font-size:10px;height:25px;line-height:25px;margin:2px;border:1px solid #7766cc;text-align:center;cursor:pointer;
}
.query_rec_c{
	float:left;width:40px;font-size:10px;height:25px;line-height:25px;margin:2px;border:1px solid #7766cc;text-align:center;cursor:pointer;
}
.query_rec_d{
	float:left;width:80px;font-size:10px;height:25px;line-height:25px;margin:2px;border:1px solid #7766cc;text-align:center;cursor:pointer;
}
.query_bind_event{
	;
}
.query_select_bind_event{
	;
}


.school_pick{
	float:left;width:200px;border:1px solid red;height:20px;text-align:left;line-height:20px;margin:1px 10px;font-size:12px;;
}

.major_one{
	cursor:pointer;float:left;width:80px;height:400px;text-align:center;margin:1px;
}
.major_two{
	cursor:pointer;float:left;width:140px;height:400px;text-align:center;margin:1px;
}
.major_three{
	cursor:pointer;float:left;width:140px;height:400px;text-align:center;margin:1px;
}

.school_list{
	cursor:pointer;height:18px;text-align:left;line-height:18px;margin:1px 2px;font-size:12px;;
}
.major_info{
	cursor:pointer;float:left;width:210px;text-align:left;margin:2px;border:1px solid #cc0000;padding:1px;
}
.major_info3{
	cursor:pointer;float:left;width:300px;text-align:left;margin:2px;border:1px solid #cc0000;padding:1px;
}
.major_info2{
	cursor:pointer;float:left;width:128px;text-align:left;margin:2px;border:1px solid #cc0000;padding:1px;
}
.major_info22{
	cursor:pointer;float:left;width:155px;text-align:left;margin:2px;border:1px solid #cc0000;padding:1px;
}
.major_info_title{
	cursor:pointer;float:left;width:200px;text-align:left;margin:2px;font-weight:bold;fon-size:16px;color:blue;;
}

.fixedLayer1 { 
	position:fixed; 
	left: 0px; 
	top: 0px; 
	width:100px; 
	line-height:40px; 
	height:40px;
	background: #FC6; 
	font-size:26px;
	text-align:center;
	font-weight:bold;
	color:#000;
	border:1px solid #F90; 
	filter: alpha(opacity = 90);
	-moz-opacity: 0.9;
	-khtml-opacity: 0.9;
	opacity: 0.9;
} 

.fixedLayer2 { 
	position:fixed; 
	left: 0px; 
	top: 0px; 
	width:100px; 
	line-height:30px; 
	background: #FC6; 
	border:1px solid #F90; 
} 

.fixedLayerMenu { 
	line-height:25px; 
	height:25px;
	margin:5px;
} 
</style>
</head>
<body>
<div>

	
	<div style="margin:5px;">
		<%if(card.getId().indexOf("A") != -1){ %>
			<div style="font-size:16px;font-weight:bold;text-align:left;'">志愿卡自动发货系统 V2.0 (<a href="<%=request.getContextPath()%>/admin/tel_import.jsp">切换防落榜系统</a>)</div>
		<%}else if(card.getId().indexOf("B") != -1){ %>
			<div style="font-size:16px;font-weight:bold;text-align:left;'">志愿卡自动发货系统 V2.0</div>
		<%}else if(card.getId().indexOf("C") != -1){ %>
		<div style="font-size:16px;font-weight:bold;text-align:left;'">志愿卡自动发货系统 V2.0</div>
		<%}else if(card.getId().indexOf("EP") != -1){ %>
		<div style="font-size:16px;font-weight:bold;text-align:left;'">志愿卡自动提卡系统 V2.0</div>
		<%}else{
			return;
		} %>
	</div>
	
		<div style="margin:3px;">
			<div class="query_rec_label">手机号（接收短信）：</div>
			<div style="float:left;">
				<input type="text" style="width:140px;height:30px;font-size:16px;color:blue;font-weight:bold;" id="tel"/>
				<input type="button" style="width:60px;font-size:14px;height:30px;" value="查询" onclick="MM_searchTelA();"/>
			</div>
			<div style="clear:both;"></div>
		</div>
		
		<div style="margin:3px;">
			<div class="query_rec_label">唯一识别（订单号等）：</div>
			<div style="float:left;">
				<input type="text" style="width:200px;height:30px;font-size:12px;color:blue;font-weight:bold;" id="orderID"/>
				<input type="button" style="width:60px;font-size:14px;height:30px;" value="查询" onclick="MM_searchTelB();"/>
			</div>
			<div style="clear:both;"></div>
		</div>
		
		<div style="margin:3px;">
			<div class="query_rec_label">开通模块：</div>
			
			<%if(card.getId().indexOf("C") != -1){%>
				<div style="float:left;font-size:14px;">
				<div style="float:left;width:180px;height:40px;line-height:40px;border:1px solid red;margin:5px;<%=cardCCnt<=0?"color:gray;":"color:blue;"%>"><input type="radio" value="C" name="module" <%=cardCCnt <= 0 ? "disabled" : "" %>/>考鱼等位分(<%=cardCCnt %>)</div>
				<div style="float:left;width:220px;height:40px;line-height:40px;border:1px solid red;margin:5px;<%=cardFCnt<=0?"color:gray;":"color:blue;"%>"><input checked type="radio" value="F" name="module" <%=cardFCnt <= 0 ? "disabled" : "" %>/>全程学业规划(<%=cardFCnt %>)</div>
				<div style="float:left;width:240px;height:40px;line-height:40px;border:1px solid red;margin:5px;<%=cardGCnt<=0?"color:gray;":"color:blue;"%>"><input type="radio" value="G" name="module" <%=cardGCnt <= 0 ? "disabled" : "" %>/>等位分+全程学业规划(<%=cardGCnt %>)</div>
				<div style="clear:both;"></div>
			</div>
			
				
			<%}else{%>
			
			<div style="float:left;font-size:14px;">
				<div style="float:left;width:160px;height:40px;line-height:40px;border:1px solid red;margin:5px;<%=cardACnt<=0?"color:gray;":"color:blue;"%>"><input type="radio" value="A" name="module" <%=cardACnt <= 0 ? "disabled" : "" %>/>优志愿(<%=cardACnt %>)</div>
				<div style="float:left;width:160px;height:40px;line-height:40px;border:1px solid red;margin:5px;<%=cardBCnt<=0?"color:gray;":"color:blue;"%>"><input type="radio" value="B" name="module" <%=cardBCnt <= 0 ? "disabled" : "" %>/>规划师(<%=cardBCnt %>) </div>
				<div style="float:left;width:180px;height:40px;line-height:40px;border:1px solid red;margin:5px;<%=cardCCnt<=0?"color:gray;":"color:blue;"%>"><input type="radio" value="C" name="module" <%=cardCCnt <= 0 ? "disabled" : "" %>/>考鱼等位分(<%=cardCCnt %>)</div>
				<div style="clear:both;"></div>
				<div style="float:left;width:220px;height:40px;line-height:40px;border:1px solid red;margin:5px;<%=cardDCnt<=0?"color:gray;":"color:blue;"%>"><input type="radio" value="D" name="module" <%=cardDCnt <= 0 ? "disabled" : "" %>/>优志愿+规划师(<%=cardDCnt %>)</div>
				<div style="float:left;width:240px;height:40px;line-height:40px;border:1px solid red;margin:5px;<%=cardECnt<=0?"color:gray;":"color:blue;"%>"><input type="radio" value="E" name="module" <%=cardECnt <= 0 ? "disabled" : "" %>/>等位分+规划师(<%=cardECnt %>)</div>
				<div style="clear:both;"></div>
				<div style="float:left;width:220px;height:40px;line-height:40px;border:1px solid red;margin:5px;<%=cardFCnt<=0?"color:gray;":"color:blue;"%>"><input type="radio" value="F" name="module" <%=cardFCnt <= 0 ? "disabled" : "" %>/>全程学业规划(<%=cardFCnt %>)</div>
				<div style="float:left;width:240px;height:40px;line-height:40px;border:1px solid red;margin:5px;<%=cardGCnt<=0?"color:gray;":"color:blue;"%>"><input type="radio" value="G" name="module" <%=cardGCnt <= 0 ? "disabled" : "" %>/>等位分+全程学业规划(<%=cardGCnt %>)</div>
				
				<div style="clear:both;"></div>
			</div>
			
			<%} %>
			<div style="clear:both;"></div>
		</div>
		
		<div style="margin:3px;">
			<div class="query_rec_label"></div>
			<div style="float:left;">
				<input type="button" style="width:145px;height:40px;font-size:14px;" value="生成志愿卡" onclick="MM_addTel();"/>
			</div>
			<div style="clear:both;"></div>
		</div>
</div>


<div style="margin:5px 5px;">

	<table class="pure-table pure-table-bordered">
        <thead>
            <tr>
                <th>卡号</th>
                <th>密码</th>
                <th>客户电话</th>
                <th>唯一识别</th>
                <th>开通模块</th>
                <th>开通时间</th>
                <th>开通人</th>
            </tr>
        </thead>
    
        <tbody id="resultHTML">
        </tbody>
    </table>
    
</div>

</body>
</html>
<script>

function MM_searchTelA(){
	var tel = $("#tel").val();
	
	$.ajax({
        type : "POST",
        url : "<%=request.getContextPath()%>/admin/_card598_tel_query2.jsp",
		data: {"val" : tel, "f" : "A"}, 
		//dataType: "json",
        //请求成功
        success : function(result) {
        	if(result.indexOf("ERR:LOGIN:REDIRECT-X_LOGIN") != -1){
				location.href = "<%=request.getContextPath()%>/admin/login.jsp";
        	}else{
        		$("#resultHTML").html(result);
        	}
        },
        //请求失败，包含具体的错误信息
        error : function(e){
            console.log(e.status);
            console.log(e.responseText);
        }
    });
}

function MM_searchTelB(){
	var orderID = $("#orderID").val();
	
	$.ajax({
        type : "POST",
        url : "<%=request.getContextPath()%>/admin/_card598_tel_query2.jsp",
		data: {"val" : orderID, "f" : "B"}, 
		//dataType: "json",
        //请求成功
        success : function(result) {
        	if(result.indexOf("ERR:LOGIN:REDIRECT-X_LOGIN") != -1){
				location.href = "<%=request.getContextPath()%>/admin/login.jsp";
        	}else{
        		$("#resultHTML").html(result);
        	}
        },
        //请求失败，包含具体的错误信息
        error : function(e){
            console.log(e.status);
            console.log(e.responseText);
        }
    });
}


function MM_searchTel(){
	var tel = $("#tel").val();
	var orderID = $("#orderID").val();
	
	$.ajax({
        type : "POST",
        url : "<%=request.getContextPath()%>/admin/_card598_tel_query.jsp",
		data: {"tel" : tel,"orderID" : orderID}, 
		//dataType: "json",
        //请求成功
        success : function(result) {
        	if(result.indexOf("ERR:LOGIN:REDIRECT-X_LOGIN") != -1){
				location.href = "<%=request.getContextPath()%>/admin/login.jsp";
        	}else{
        		$("#resultHTML").html(result);
        	}
        },
        //请求失败，包含具体的错误信息
        error : function(e){
            console.log(e.status);
            console.log(e.responseText);
        }
    });
	
}

function MM_addTel(){
	var tel = $("#tel").val();
	if(tel == "" || tel.length != 11){
		alert("请输入正确手机号");
		$("#tel").focus();
		return false;
	}
	
	var orderID = $("#orderID").val();
	if(orderID == "" || orderID.length < 5){
		alert("请输入正确唯一识别号");
		$("#orderID").focus();
		return false;
	}
	
	var module = $("input[name='module']:checked").val();
	if(module == "" || module == undefined || module == "undefined"){
		alert("请选择开通系统套餐");
		return false;
	}
	
	$.ajax({
        type : "POST",
        url : "<%=request.getContextPath()%>/admin/_card598_tel_import.jsp",
		data: {"tel" : tel, "orderID" : orderID, "module" : module}, 
		//dataType: "json",
        //请求成功
        success : function(result) {
        	if(result.indexOf("reload:refresh-list") != -1){
        		MM_searchTel();
        	}else if(result.indexOf("ERR:LOGIN:REDIRECT-X_LOGIN") != -1){
				location.href = "<%=request.getContextPath()%>/admin/login.jsp";
        	}else{
        		alert($.trim(result));
        	}
        },
        //请求失败，包含具体的错误信息
        error : function(e){
            console.log(e.status);
            console.log(e.responseText);
        }
    });
}

$(function(){
	MM_searchTel();
});
</script>