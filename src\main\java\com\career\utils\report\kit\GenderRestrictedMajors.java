package com.career.utils.report.kit;

import java.util.*;

/**
 * 性别限制专业枚举类
 * 管理各类专业的性别限制规则
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public enum GenderRestrictedMajors {
    
    // ==================== 男性限制专业（女性不能报考） ====================
    
    /** 消防工程类专业 */
    FIREFIGHTING("消防", MajorGenderType.MALE_ONLY, "消防工程、消防指挥等专业通常要求较强的体能"),
    
    /** 航海技术类专业 */
    NAVIGATION("航海", MajorGenderType.MALE_ONLY, "航海技术、海事管理等专业工作环境特殊"),
    
    /** 轮机工程类专业 */
    MARINE_ENGINEERING("轮机", MajorGenderType.MALE_ONLY, "轮机工程、船舶与海洋工程等专业体力要求高"),
    
    /** 海洋工程类专业 */
    OCEAN_ENGINEERING("海洋工程", MajorGenderType.MALE_ONLY, "海洋工程技术、海洋资源开发等专业"),
    
    /** 采矿工程类专业 */
    MINING_ENGINEERING("采矿", MajorGenderType.MALE_ONLY, "采矿工程、矿物加工工程等专业工作环境恶劣"),
    
    /** 石油工程类专业 */
    PETROLEUM_ENGINEERING("石油工程", MajorGenderType.MALE_ONLY, "石油工程、油气储运工程等专业野外作业多"),
    
    /** 地质勘探类专业 */
    GEOLOGICAL_EXPLORATION("地质勘探", MajorGenderType.MALE_ONLY, "地质勘探、勘查技术与工程等专业"),
    
    /** 核工程类专业 */
    NUCLEAR_ENGINEERING("核工程", MajorGenderType.MALE_ONLY, "核工程与核技术、辐射防护等专业"),
    
    /** 军事指挥类专业 */
    MILITARY_COMMAND("军事指挥", MajorGenderType.MALE_ONLY, "军事指挥、作战指挥等专业"),
    
    /** 特种作战类专业 */
    SPECIAL_OPERATIONS("特种作战", MajorGenderType.MALE_ONLY, "特种作战、特种兵等专业"),
    
    /** 潜水作业类专业 */
    DIVING_OPERATIONS("潜水", MajorGenderType.MALE_ONLY, "潜水作业、水下工程等专业"),
    
    /** 高空作业类专业 */
    HIGH_ALTITUDE_WORK("高空作业", MajorGenderType.MALE_ONLY, "高空作业、架线工程等专业"),
    
    /** 重型机械类专业 */
    HEAVY_MACHINERY("重型机械", MajorGenderType.MALE_ONLY, "重型机械操作、工程机械等专业"),
    
    /** 钢铁冶金类专业 */
    METALLURGY("冶金", MajorGenderType.MALE_ONLY, "钢铁冶金、有色金属冶金等专业高温作业"),
    
    /** 煤炭工程类专业 */
    COAL_ENGINEERING("煤炭", MajorGenderType.MALE_ONLY, "煤炭工程、煤化工等专业"),
    
    // ==================== 女性限制专业（男性不能报考） ====================
    
    /** 助产学专业 */
    MIDWIFERY("助产学", MajorGenderType.FEMALE_ONLY, "助产学专业基于职业特殊性通常只招收女生"),
    
    /** 妇幼保健类专业 */
    MATERNAL_CHILD_HEALTH("妇幼保健", MajorGenderType.FEMALE_ONLY, "妇幼保健医学、妇产科学等专业"),
    
    /** 护理学专业（部分院校） */
    NURSING("护理学", MajorGenderType.FEMALE_PREFERRED, "部分院校护理学专业倾向招收女生"),
    
    /** 学前教育专业（部分院校） */
    PRESCHOOL_EDUCATION("学前教育", MajorGenderType.FEMALE_PREFERRED, "部分院校学前教育专业倾向招收女生"),
    
    /** 空中乘务专业（部分要求） */
    FLIGHT_ATTENDANT("空中乘务", MajorGenderType.GENDER_SPECIFIC, "空中乘务专业可能有特定的性别比例要求");
    
    // ==================== 枚举属性 ====================
    
    /** 专业关键词 */
    private final String keyword;
    
    /** 性别限制类型 */
    private final MajorGenderType genderType;
    
    /** 限制说明 */
    private final String description;
    
    // ==================== 构造函数 ====================
    
    GenderRestrictedMajors(String keyword, MajorGenderType genderType, String description) {
        this.keyword = keyword;
        this.genderType = genderType;
        this.description = description;
    }
    
    // ==================== Getter方法 ====================
    
    public String getKeyword() {
        return keyword;
    }
    
    public MajorGenderType getGenderType() {
        return genderType;
    }
    
    public String getDescription() {
        return description;
    }
    
    // ==================== 静态工具方法 ====================
    
    /**
     * 获取所有男性限制专业关键词
     * @return 男性限制专业关键词集合
     */
    public static Set<String> getMaleOnlyKeywords() {
        Set<String> keywords = new HashSet<>();
        for (GenderRestrictedMajors major : values()) {
            if (major.genderType == MajorGenderType.MALE_ONLY) {
                keywords.add(major.keyword);
            }
        }
        return keywords;
    }
    
    /**
     * 获取所有女性限制专业关键词
     * @return 女性限制专业关键词集合
     */
    public static Set<String> getFemaleOnlyKeywords() {
        Set<String> keywords = new HashSet<>();
        for (GenderRestrictedMajors major : values()) {
            if (major.genderType == MajorGenderType.FEMALE_ONLY) {
                keywords.add(major.keyword);
            }
        }
        return keywords;
    }
    
    /**
     * 获取所有女性倾向专业关键词
     * @return 女性倾向专业关键词集合
     */
    public static Set<String> getFemalePreferredKeywords() {
        Set<String> keywords = new HashSet<>();
        for (GenderRestrictedMajors major : values()) {
            if (major.genderType == MajorGenderType.FEMALE_PREFERRED) {
                keywords.add(major.keyword);
            }
        }
        return keywords;
    }
    
    /**
     * 检查专业是否对指定性别有限制
     * @param majorName 专业名称
     * @param gender 用户性别（"男"/"女"）
     * @return 是否存在性别限制
     */
    public static boolean hasGenderRestriction(String majorName, String gender) {
        if (majorName == null || gender == null) {
            return false;
        }
        
        for (GenderRestrictedMajors major : values()) {
            if (majorName.contains(major.keyword)) {
                switch (major.genderType) {
                    case MALE_ONLY:
                        return "女".equals(gender);
                    case FEMALE_ONLY:
                        return "男".equals(gender);
                    case FEMALE_PREFERRED:
                        // 女性倾向专业对男性有一定限制，但不是绝对限制
                        return "男".equals(gender);
                    case GENDER_SPECIFIC:
                        // 特定性别要求的专业需要具体分析
                        return false; // 暂时不做限制，需要具体业务规则
                    default:
                        return false;
                }
            }
        }
        
        return false;
    }
    
    /**
     * 获取专业的性别限制信息
     * @param majorName 专业名称
     * @return 性别限制信息，如果没有限制返回null
     */
    public static GenderRestrictedMajors getGenderRestriction(String majorName) {
        if (majorName == null) {
            return null;
        }
        
        for (GenderRestrictedMajors major : values()) {
            if (majorName.contains(major.keyword)) {
                return major;
            }
        }
        
        return null;
    }
    
    // ==================== 性别限制类型枚举 ====================
    
    /**
     * 专业性别限制类型
     */
    public enum MajorGenderType {
        /** 仅限男性 */
        MALE_ONLY,
        /** 仅限女性 */
        FEMALE_ONLY,
        /** 女性倾向（男性有一定限制但不绝对） */
        FEMALE_PREFERRED,
        /** 特定性别要求（需要具体分析） */
        GENDER_SPECIFIC
    }
} 