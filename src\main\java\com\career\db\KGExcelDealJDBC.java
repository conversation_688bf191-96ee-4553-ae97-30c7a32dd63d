package com.career.db;

import com.career.utils.Tools;
import com.career.utils.XKCombineUtils;
import com.career.utils.ZyzdCache;
import com.career.utils.ZyzdProvince;
import com.zsdwf.db.BaseCardBean;
import com.zsdwf.db.PredictBean;
import com.zsdwf.db.YGDataPatchDBTools;
import com.zsdwf.pdf.PDFGaoZhongStudentInfo;
import com.career.db.SchoolBean;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.UUID;

public class KGExcelDealJDBC {
	static String URL = JDBCConstants.URL;
	static String USER = JDBCConstants.USER;
	static String PASSWD = JDBCConstants.PASSWD;

	static {
		try {
			Class.forName("com.mysql.cj.jdbc.Driver");
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		
	}

	

	public void executeBatchForcareer_job_gwy_req_org(List<CareerJobGwyReqOrg> list) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into career_job_gwy_req_org(nf, group_id, sf, job_lx, job_type, job_title, job_descp) values(?,?,?,?,?,?,?);";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			for(CareerJobGwyReqOrg form : list) {
				ps.setInt(1, form.getNf());
				ps.setString(2, form.getGroup_id());
				ps.setString(3, form.getSf());
				ps.setString(4, form.getJob_lx());
				ps.setString(5, form.getJob_type());
				ps.setString(6, form.getJob_title());
				ps.setString(7, form.getJob_descp());
				ps.addBatch();
			}
			ps.executeBatch();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	private static void closeAllConnection(Connection conn, PreparedStatement ps, ResultSet rs) {
		/**
		try {
			if (rs != null)
				rs.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (ps != null)
				ps.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (conn != null)
				conn.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		rs = null;
		ps = null;
		conn = null;
		*/
		DatabaseUtils.closeAllResources(rs, ps, conn);
	}
  	
  	
}
