<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page import="com.zsdwf.utils.*,com.zsdwf.db.*,java.util.*" %>
<%@include file="/WEB-INF/include/_session_admin.jsp" %>
<%
YGCardBean card = (YGCardBean)session.getAttribute("SES_ADMIN");
%>
<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"> 
<meta name="format-dection" content="telephone=no"/>
<title><%=CCache.SYS_TITLE_NAME %></title>
<script src="<%=request.getContextPath()%>/sources/jquery-3.6.4.min.js"></script>
<style type="text/css">
html {
    font-family: sans-serif;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
}
 
body {
    margin: 10px;
    font-size:12px;
}
table {
    border-collapse: collapse;
    border-spacing: 0;
}
 
td,th {
    padding: 0;
}
 
.pure-table {
    border-collapse: collapse;
    border-spacing: 0;
    empty-cells: show;
    border: 1px solid #cbcbcb;
}
 
.pure-table caption {
    color: #000;
    font: italic 85%/1 arial,sans-serif;
    padding: 1em 0;
    text-align: center;
}
 
.pure-table td,.pure-table th {
    border-left: 1px solid #cbcbcb;
    border-width: 0 0 0 1px;
    font-size: inherit;
    margin: 0;
    overflow: visible;
    padding: .5em 1em;
}
 
.pure-table thead {
    background-color: #e0e0e0;
    color: #000;
    text-align: left;
    vertical-align: bottom;
}
 
.pure-table td {
    background-color: transparent;
}
 
.pure-table-bordered td {
    border-bottom: 1px solid #cbcbcb;
}
 
.pure-table-bordered tbody>tr:last-child>td {
    border-bottom-width: 0;
}

.query_rec_label{
	float:left;width:90px;height:25px;line-height:25px;font-weight:bold;margin:2px 1px;
}
.query_rec_label2{
	float:left;width:90px;height:25px;line-height:25px;font-weight:bold;margin:2px 1px;
}
.query_rec_aa{
	float:left;width:130px;font-size:10px;height:25px;line-height:25px;margin:2px;border:1px solid #7766cc;text-align:center;cursor:pointer;
}
.query_rec_a{
	float:left;width:100px;font-size:10px;height:25px;line-height:25px;margin:2px;border:1px solid #7766cc;text-align:center;cursor:pointer;
}
.query_rec_b{
	float:left;width:60px;font-size:10px;height:25px;line-height:25px;margin:2px;border:1px solid #7766cc;text-align:center;cursor:pointer;
}
.query_rec_c{
	float:left;width:40px;font-size:10px;height:25px;line-height:25px;margin:2px;border:1px solid #7766cc;text-align:center;cursor:pointer;
}
.query_rec_d{
	float:left;width:80px;font-size:10px;height:25px;line-height:25px;margin:2px;border:1px solid #7766cc;text-align:center;cursor:pointer;
}
.query_bind_event{
	;
}
.query_select_bind_event{
	;
}


.school_pick{
	float:left;width:200px;border:1px solid red;height:20px;text-align:left;line-height:20px;margin:1px 10px;font-size:12px;;
}

.major_one{
	cursor:pointer;float:left;width:80px;height:400px;text-align:center;margin:1px;
}
.major_two{
	cursor:pointer;float:left;width:140px;height:400px;text-align:center;margin:1px;
}
.major_three{
	cursor:pointer;float:left;width:140px;height:400px;text-align:center;margin:1px;
}

.school_list{
	cursor:pointer;height:18px;text-align:left;line-height:18px;margin:1px 2px;font-size:12px;;
}
.major_info{
	cursor:pointer;float:left;width:210px;text-align:left;margin:2px;border:1px solid #cc0000;padding:1px;
}
.major_info3{
	cursor:pointer;float:left;width:300px;text-align:left;margin:2px;border:1px solid #cc0000;padding:1px;
}
.major_info2{
	cursor:pointer;float:left;width:128px;text-align:left;margin:2px;border:1px solid #cc0000;padding:1px;
}
.major_info22{
	cursor:pointer;float:left;width:155px;text-align:left;margin:2px;border:1px solid #cc0000;padding:1px;
}
.major_info_title{
	cursor:pointer;float:left;width:200px;text-align:left;margin:2px;font-weight:bold;fon-size:16px;color:blue;;
}

.fixedLayer1 { 
	position:fixed; 
	left: 0px; 
	top: 0px; 
	width:100px; 
	line-height:40px; 
	height:40px;
	background: #FC6; 
	font-size:26px;
	text-align:center;
	font-weight:bold;
	color:#000;
	border:1px solid #F90; 
	filter: alpha(opacity = 90);
	-moz-opacity: 0.9;
	-khtml-opacity: 0.9;
	opacity: 0.9;
} 

.fixedLayer2 { 
	position:fixed; 
	left: 0px; 
	top: 0px; 
	width:100px; 
	line-height:30px; 
	background: #FC6; 
	border:1px solid #F90; 
} 

.fixedLayerMenu { 
	line-height:25px; 
	height:25px;
	margin:5px;
} 
</style>
</head>
<body>
<div>

	
	<div style="margin:5px;">
		<%if(card.getId().indexOf("A") != -1){ %>
			<div style="font-size:16px;font-weight:bold;text-align:left;'">全国预测/防落榜系统自动发货 (<a href="<%=request.getContextPath()%>/admin/card598_tel_import.jsp">切换598发卡系统</a>)</div>
		<%}else if(card.getId().indexOf("B") != -1){ 
			
			response.sendRedirect(request.getContextPath() + "/admin/card598_tel_import.jsp");
			return;
		}else{ %>
			<div style="font-size:16px;font-weight:bold;text-align:left;'">全国预测/防落榜系统自动发货 </div>
		<%} %>
	</div>
	
		<div style="margin:3px;">
			<div class="query_rec_label">手机号：</div>
			<div style="float:left;">
				<input type="text" style="width:140px;height:23px;font-size:16px;color:blue;font-weight:bold;" id="tel"/>
				<input type="button" style="width:40px;font-size:14px;" value="查询" onclick="MM_searchTel();"/> &nbsp;
				<input type="button" style="width:80px;font-size:14px;" value="查验证码" onclick="MM_searchSMSCode();"/>
			</div>
			<div style="clear:both;"></div>
		</div>
		
		<div style="margin:3px;">
			<div class="query_rec_label">充次数：</div>
			<div style="float:left;">
				<input type="radio" value="80" name="cnt" checked/>328元/80次
				<%if(card.getId().indexOf("A") != -1){ %>
				<input type="radio" value="30" name="cnt"/>198元/30次
				<input type="radio" value="10" name="cnt"/>赠送10次
				<%} %>
			</div>
			<div style="clear:both;"></div>
		</div>
		
		<div style="margin:3px;">
			<div class="query_rec_label"></div>
			<div style="float:left;">
				<input type="button" style="width:100px;font-size:14px;" value="充值" onclick="MM_addTel();"/>
			</div>
			<div style="clear:both;"></div>
		</div>
</div>


<div style="margin:5px 5px;">

	<table class="pure-table pure-table-bordered">
        <thead>
            <tr>
                <th>卡号</th>
                <th>密码</th>
                <th>手机号</th>
                <th>总次数</th>
                <th>已使用</th>
                <th>最后使用</th>
                <th>创建时间</th>
                <th>更新时间</th>
                <th>充值情况</th>
            </tr>
        </thead>
    
        <tbody id="resultHTML">
        </tbody>
    </table>
    
</div>


<div style="margin:40px 5px;">
<textarea style="width:320px;height:160px;" id="xxxxx"></textarea>
<textarea style="width:320px;height:160px;" id="xxxxx2"></textarea>
<input type="button" style="width:40px;font-size:14px;" value="生成SQL" onclick="MM_getSQL();"/>
</div>
</body>
</html>
<script>

function MM_getSQL(){
	var xxxxx = $("#xxxxx").val();
	var el = xxxxx.split("，");
	var outEL = [];
	if(el.length <= 2){
		var el2 = xxxxx.split(" ");
		if(el2.length != 0){
			outEL = el2;
		}
	}else{
		outEL = el;
	}
	
	var kkk = "";

	if(outEL.length != 0){
		for(var i=0;i<outEL.length;i++){
			var str = "INSERT INTO `yg_card` (`C_ID`, `C_PASSWD`, `C_PHONE`, `C_PROV`, `C_WC`, `C_XK`, `C_CNT`, `C_USED`, `C_STATUS`, `C_LAST_QUERY`, `C_CREATE`, `C_UPDATE`, `C_REMARK`, `C_ADMIN`, `C_OPEN_ID`) ";
			str += "VALUES ('" + outEL[i] + "', '444', '"+ outEL[i] +"', NULL, 0, NULL, 80, 0, 1, NULL, NOW(), NOW(), 'A01(80)', NULL, NULL);";
			kkk += str +"\r\n";
		}
		$("#xxxxx2").val(kkk);
	}
}

function MM_searchSMSCode(){
	var tel = $("#tel").val();
	$.ajax({
        type : "POST",
        url : "<%=request.getContextPath()%>/admin/_tel_query_smscode.jsp",
		data: {"tel" : tel}, 
		//dataType: "json",
        //请求成功
        success : function(result) {
        	var result = $.trim(result);
        	if(result.indexOf("ERR:LOGIN:REDIRECT-X_LOGIN") != -1){
				location.href = "<%=request.getContextPath()%>/admin/login.jsp";
        	}else{
        		alert(result);
        	}
        },
        //请求失败，包含具体的错误信息
        error : function(e){
            console.log(e.status);
            console.log(e.responseText);
        }
    });
}

function MM_searchTel(){
	var tel = $("#tel").val();
	$.ajax({
        type : "POST",
        url : "<%=request.getContextPath()%>/admin/_tel_query.jsp",
		data: {"tel" : tel}, 
		//dataType: "json",
        //请求成功
        success : function(result) {
        	if(result.indexOf("ERR:LOGIN:REDIRECT-X_LOGIN") != -1){
				location.href = "<%=request.getContextPath()%>/admin/login.jsp";
        	}else{
        		$("#resultHTML").html(result);
        	}
        },
        //请求失败，包含具体的错误信息
        error : function(e){
            console.log(e.status);
            console.log(e.responseText);
        }
    });
	
}

function MM_addTel(){
	var tel = $("#tel").val();
	
	if(tel == "" || tel.length != 11){
		alert("请输入正确手机号");
		$("#tel").focus();
		return false;
	}
	
	var cnt = $("input[name='cnt']:checked").val();
	if(cnt == "" || cnt == undefined || cnt == "undefined"){
		alert("请选择充值套餐");
		return false;
	}
	
	var res = confirm("您确定要为手机号【" + tel + "】充值吗？");
	
	if(!res){
		return false;
	}
	
	$.ajax({
        type : "POST",
        url : "<%=request.getContextPath()%>/admin/_tel_import.jsp",
		data: {"tel" : tel, "cnt" : cnt}, 
        //请求成功
        success : function(result) {
        	if(result.indexOf("reload:refresh-list") != -1){
        		location.href = "<%=request.getContextPath()%>/admin/tel_import.jsp";
        	}else if(result.indexOf("ERR:LOGIN:REDIRECT-X_LOGIN") != -1){
				location.href = "<%=request.getContextPath()%>/admin/login.jsp";
        	}else{
        		alert($.trim(result));
        	}
        },
        //请求失败，包含具体的错误信息
        error : function(e){
            console.log(e.status);
            console.log(e.responseText);
        }
    });
}

var loginType = "card";

$(function(){
	MM_searchTel();
});
</script>