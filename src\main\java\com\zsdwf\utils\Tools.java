package com.zsdwf.utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;
import java.util.UUID;

public class Tools {

  public static String trim(String str) {
    return (str == null) ? "" : str.trim();
  }
  
  public static boolean isEmpty(String str) {
    return (trim(str).length() == 0);
  }
  
  public static int getInt(String str) {
    try {
      return Integer.parseInt(str.trim());
    } catch (Exception ex) {
      return -1;
    } 
  }
  
  public static String getDate(Date dt) {
    try {
      DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
      return df.format(dt);
    } catch (Exception ex) {
      return null;
    } 
  }
  
  public static String getDate2(Date dt) {
	    try {
	      DateFormat df = new SimpleDateFormat("MM-dd HH:mm");
	      return df.format(dt);
	    } catch (Exception ex) {
	      return null;
	    } 
	  }
  
  public static String getDateTm(Date dt) {
	    try {
	      DateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
	      return df.format(dt);
	    } catch (Exception ex) {
	      return null;
	    } 
	  }
  
  public static String getPercent(String str) {
    try {
      NumberFormat numberFormat = NumberFormat.getInstance();
      numberFormat.setMaximumFractionDigits(2);
      String result = numberFormat.format(Double.parseDouble(str) * 100.0D);
      return String.valueOf(result) + "%";
    } catch (Exception ex) {
      return "--";
    } 
  }
  
  public static String view(String str) {
    return isEmpty(str) ? "-" : str.trim();
  }
  
  public static int getWCPercent(String wc, int max) {
    int wcInt = getInt(wc);
    if (wcInt <= 0)
      return wcInt; 
    int val = wcInt * 100 / max;
    if (val <= 0)
      val = 1; 
    if (val > 100)
      val = 100; 
    return val;
  }
  //时间间隔，分钟
  public static long timeSpan(Date from, Date to) {
    return (to.getTime() - from.getTime()) / 60000L;
  }
  
  public static String viewForLimitLength(String str) {
    return viewForLimitLength(str, 7);
  }
  
  
  public static String hideMiddle(String str) {
	    if (isEmpty(str))
	      return "--"; 
	    str = str.trim();
	    if (str.length() <= 5) {
	    	return "**" + str.substring(2); 
	    }
	      
	    return String.valueOf(str.substring(0, 1)) + "***" + str.substring(4);
	  }
  
  public static String hidePre(String str) {
	    if (isEmpty(str))
	      return "后台查看";  
	    str = str.trim();
	    if (str.length() <= 9) {
	    	return "后台查看"; 
	    }
	      
	    return String.valueOf("****" + str.substring(5));
	  }
  
  public static String hidePre2(String str) {
	    if (isEmpty(str))
	      return "*";  
	    str = str.trim();
	    if (str.length() <= 9) {
	    	return "*"; 
	    }
	      
	    return String.valueOf("*" + str.substring(str.length() - 9));
	  }
  
  public static String viewForLimitLength(String str, int length) {
    if (isEmpty(str))
      return "--"; 
    str = str.trim();
    if (str.length() <= length)
      return str; 
    return String.valueOf(str.substring(0, length));
  }
  
  public static String viewSubfixForLimitLength(String str, int length) {
	    if (isEmpty(str))
	      return "--"; 
	    str = str.trim();
	    if (str.length() <= length)
	      return str; 
	    return  "*" + String.valueOf(str.substring(str.length() - length));
	  }
  
  public static boolean contain985211syl(String is985, String is211, String issyl) {
    if (isEmpty(is985) && isEmpty(is211) && isEmpty(issyl))
      return false; 
    return true;
  }
  
  public static String viewFor985211syl(String is985, String is211, String issyl) {
    if (!contain985211syl(is985, is211, issyl))
      return "--"; 
    if (isEmpty(is985)) {
      if (isEmpty(is211))
        return issyl; 
      return String.valueOf(is211) + "/" + issyl;
    } 
    if (isEmpty(is211)) {
      if (isEmpty(issyl))
        return is985; 
      return String.valueOf(is985) + "/" + issyl;
    } 
    if (isEmpty(issyl))
      return String.valueOf(is985) + "/" + is211; 
    return String.valueOf(is985) + "/" + is211 + "/" + issyl;
  }
  
  public static String containsTag(String str) {
    return isEmpty(str) ? "--" : str.trim();
  }
  
  public static HashSet<String> getSetByStrSplit(String str) {
    return getSetByStrSplit(str, ",");
  }
  
  public static HashSet<String> getSetByStrSplit(String str, String split) {
    HashSet<String> hs = new HashSet<>();
    String[] st = trim(str).split(split);
    for (int i = 0; i < st.length; i++)
      hs.add(st[i]); 
    return hs;
  }
  
  public static HashSet<String> convertSetToHashSet(Set sets) {
    HashSet<String> hs = new HashSet<>();
    Iterator<String> it = sets.iterator();
    while (it.hasNext())
      hs.add(it.next()); 
    return hs;
  }
  
  
  public static String getZyOffName(String str) {
    String s = trim(str);
    if (s.indexOf("(") == -1) {
      if (s.indexOf("（") == -1) {
        return s; 
      }else {
    	  return s.substring(0, s.indexOf("（"));
      }
    } 
    return s.substring(0, s.indexOf("("));
  }
  
  public static String getSQLQueryin(HashSet<String> sets) {
    if (sets == null || sets.size() == 0)
      return ""; 
    String str = "";
    Iterator<String> it = sets.iterator();
    int index = 1;
    while (it.hasNext()) {
      String temp = it.next();
      if (!isEmpty(temp)) {
        str = String.valueOf(str) + "'" + temp + "'";
        if (index != sets.size())
          str = String.valueOf(str) + ","; 
      } 
      index++;
    } 
    return str;
  }
  
  public static String getSQLQueryin2(HashSet<String> sets) {
	    if (sets == null || sets.size() == 0)
	      return ""; 
	    String str = "";
	    Iterator<String> it = sets.iterator();
	    int index = 1;
	    while (it.hasNext()) {
	      String temp = it.next();
	      if (!isEmpty(temp)) {
	        str = String.valueOf(str) + "" + temp + "";
	        if (index != sets.size())
	          str = String.valueOf(str) + ","; 
	      } 
	      index++;
	    } 
	    return str;
	  }
  
  public static HashSet<String> convertXK(String xk) {
    HashSet<String> sets = new HashSet<>();
    HashSet<String> xkList = getSetByStrSplit(xk, "\\+");
    Iterator<String> it = xkList.iterator();
    while (it.hasNext()) {
      String temp = it.next();
      if (temp.equals("物理")) {
        sets.add("物");
        continue;
      } 
      if (temp.equals("历史")) {
        sets.add("历");
        sets.add("史");
        continue;
      } 
      if (temp.equals("理科")) {
        sets.add("理");
        continue;
      } 
      if (temp.equals("文科")) {
        sets.add("文");
        continue;
      } 
      if (temp.equals("地理")) {
        sets.add("地");
        continue;
      } 
      if (temp.equals("生物")) {
        sets.add("生");
        continue;
      } 
      if (temp.equals("政治")) {
        sets.add("政");
        continue;
      } 
      if (temp.equals("化学")) {
        sets.add("化");
        continue;
      } 
      if (temp.equals("技术"))
        sets.add("技"); 
    } 
    return sets;
  }
  
  
  public static String sha1(String data) throws NoSuchAlgorithmException {
      MessageDigest md = MessageDigest.getInstance("SHA1");
      //把字符串转为字节数组
      byte[] b = data.getBytes();
      //使用指定的字节来更新我们的摘要
      md.update(b);
      //获取密文  （完成摘要计算）
      byte[] b2 = md.digest();
      //获取计算的长度
      int len = b2.length;
      //16进制字符串
      String str = "0123456789abcdef";
      //把字符串转为字符串数组
      char[] ch = str.toCharArray();
      //创建一个40位长度的字节数组
      char[] chs = new char[len*2];
      //循环20次
      for(int i=0,k=0;i<len;i++) {
          //获取摘要计算后的字节数组中的每个字节
          byte b3 = b2[i];
          // >>>:无符号右移
          // &:按位与
          //0xf:0-15的数字
          chs[k++] = ch[b3 >>> 4 & 0xf];
          chs[k++] = ch[b3 & 0xf];
      }
      //字符数组转为字符串
      return new String(chs);
  }

  
  public static String getWXPaymentID(String openID){
	  String uuid = generateNonceStr();
	  try {
		  String tm = getDateTm(new Date());
		  return tm + uuid.substring(0,10) + openID.substring(0, 6);
	  }catch(Exception ex) {
		  return uuid.substring(0,30);
	  }
  }
  
  public static String generateNonceStr() {
      return UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32);
  }
  
  public static String MD5_PRI_KEY_FOR_URI = "^%%##($()!!++_::l><<jk!&&^#()~!@34FKF,55";
  public static String MD5_PRI_KEY_FOR_URI_INTERFACE = "^%%##(*@@@mmDJFDSFDFE309983+_($()!!++_::l><<jk!&&^#()~!@34FKF,55";
  public static String md5(String message) {
	  try {
          // 创建MD5消息摘要对象
          MessageDigest md = MessageDigest.getInstance("MD5");
          
          // 计算消息的摘要
          byte[] digest = md.digest(message.getBytes());
          
          // 将摘要转换为十六进制字符串
          return bytesToHex(digest);
      } catch (Exception e) {
          e.printStackTrace();
       }
	  return message;
  }
  
  public static String bytesToHex(byte[] bytes) {
      StringBuilder hexString = new StringBuilder();
      for (byte b : bytes) {
          String hex = Integer.toHexString(0xff & b);
          if (hex.length() == 1) {
              hexString.append('0');
          }
          hexString.append(hex);
      }
      return hexString.toString();
  }
  
}
