package com.career.utils.kaogong;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.alibaba.fastjson2.JSONObject;

import com.career.utils.zsky.*;

public class DealWithKG {

	public static void main(String args[]) throws Exception {
		dealWithProvinceGangWei();
	}
	
	public static void dealWithProvinceGangWei() throws Exception {
		StringBuffer SQL = new StringBuffer();
		String fileName = null;
		for (int i = 0; i <= 9; i++) {
			try {
				
				File file = new File("E://chongqing//" + i + "//");
				File[] fileList = file.listFiles();
				
				for(File xx : fileList) {
					fileName = xx.getName();
					
					BufferedReader bw = new BufferedReader(new FileReader(xx));
					StringBuffer sb = new StringBuffer();
					String str = null;
					int k = 0;
					
					String tempZLJG = null, tempNSJG = null, tempWorkPlace = null, tempXT = null, tempCode = null, tempName = null, tempCategory = null, tempJJ = null, tempZW = null;
					String tempRS = null, tempFW = null, tempDX = null, tempXB = null, tempXL = null, tempXW = null, tempZY = null, tempQT = null, tempZZMM = null;
					String tempBSBL = null, tempMSBL = null, tempBZ = null ,tempJS = null;;
					
					while ((str = bw.readLine()) != null) {
						if(xx.getName().equals("ZGJY_zw710.txt")) {
							//System.out.println(str+" line : "+k++);
							
						}
						
						if(str.indexOf("<th>招录机关</th><td>") != -1) {
							//System.out.println(xx.getName());
							
							if(str.indexOf("</td><th>") == -1) {
								tempZLJG = str.substring(str.indexOf("<th>招录机关</th><td>") + "<th>招录机关</th><td>".length()).trim();
								str = bw.readLine();
								tempZLJG += str.substring(0, str.indexOf("</td><th>")).trim();
							}else {
								tempZLJG = str.substring(str.indexOf("<th>招录机关</th><td>") + "<th>招录机关</th><td>".length(), str.indexOf("</td><th>"));
							}
							
							
							
							if(str.indexOf("</td>          </tr>") == -1) {
								tempNSJG = str.substring(str.indexOf("<th>内设机构</th><td>") + "<th>内设机构</th><td>".length()).trim();
								str = bw.readLine();
								if(str.indexOf("</td>          </tr>") == -1) {
									tempNSJG += str.trim();
									str = bw.readLine();
									tempNSJG += str.substring(0, str.indexOf("</td>          </tr>")).trim();
								}else {
									tempNSJG += str.substring(0, str.indexOf("</td>          </tr>")).trim();
								}
							}else {
								tempNSJG = str.substring(str.indexOf("<th>内设机构</th><td>") + "<th>内设机构</th><td>".length(), str.indexOf("</td>          </tr>"));
							}
							
							
							bw.readLine();
							str = bw.readLine();
							tempWorkPlace = str.substring(str.indexOf("<th>地区</th><td>") + "<th>地区</th><td>".length(), str.indexOf("</td><th>"));
							tempXT = str.substring(str.indexOf("<th>系统</th><td>") + "<th>系统</th><td>".length(), str.indexOf("</td>          </tr>"));
							//System.out.println(fileName + ": " + tempZLJG + "," + tempNSJG + "," + tempWorkPlace + "," + tempXT);
							
						}
						
						if(str.indexOf("<th>职位编码</th><td>") != -1) {
							tempCode = str.substring(str.indexOf("<th>职位编码</th><td>") + "<th>职位编码</th><td>".length(), str.indexOf("</td><th>"));
							
							if(str.indexOf("</td>            </tr>") == -1) {
								tempName = str.substring(str.indexOf("<th>职位名称</th><td>") + "<th>职位名称</th><td>".length()).trim();
								str = bw.readLine();
								tempName = str.substring(0, str.indexOf("</td>            </tr>")).trim();
							}else {
								tempName = str.substring(str.indexOf("<th>职位名称</th><td>") + "<th>职位名称</th><td>".length(), str.indexOf("</td>            </tr>"));
							}
						}
						
						
						if(str.indexOf("<th>职位类别</th><td>") != -1) {
							tempCategory = str.substring(str.indexOf("<th>职位类别</th><td>") + "<th>职位类别</th><td>".length(), str.indexOf("</td><th>"));
							
							if(str.indexOf("</td>            </tr>") == -1) {
								tempJJ = str.substring(str.indexOf("<th>职位简介</th><td>") + "<th>职位简介</th><td>".length()).trim();
								str = bw.readLine();
								tempJJ = str.substring(0, str.indexOf("</td>            </tr>")).trim();
							}else {
								tempJJ = str.substring(str.indexOf("<th>职位简介</th><td>") + "<th>职位简介</th><td>".length(), str.indexOf("</td>            </tr>"));
							}
						}
						
						if(str.indexOf("<th>拟任职务</th><td>") != -1) {
							tempZW = str.substring(str.indexOf("<th>拟任职务</th><td>") + "<th>拟任职务</th><td>".length(), str.indexOf("</td><th>"));
							tempRS = str.substring(str.indexOf("<th>招考人数</th><td>") + "<th>招考人数</th><td>".length(), str.indexOf("</td>            </tr>"));
						}
						
						if(str.indexOf("<th>招录范围</th><td>") != -1) {
							tempFW = str.substring(str.indexOf("<th>招录范围</th><td>") + "<th>招录范围</th><td>".length(), str.indexOf("</td><th>"));
							tempDX = str.substring(str.indexOf("<th>招录对象</th><td>") + "<th>招录对象</th><td>".length(), str.indexOf("</td>            </tr>"));
						}
						
						if(str.indexOf("<th>性别</th><td>") != -1) {
							tempXB = str.substring(str.indexOf("<th>性别</th><td>") + "<th>性别</th><td>".length(), str.indexOf("</td><th>"));
						}
						
						if(str.indexOf("<th>学历</th><td>") != -1) {
							tempXL = str.substring(str.indexOf("<th>学历</th><td>") + "<th>学历</th><td>".length(), str.indexOf("</td><th>"));
							tempXW = str.substring(str.indexOf("<th>学位</th><td>") + "<th>学位</th><td>".length(), str.indexOf("</td>            </tr>"));
						}
						
						if(str.indexOf("<th>专业</th><td>") != -1) {
							
							if(str.indexOf("</td><th>") == -1) {
								tempZY = str.substring(str.indexOf("<th>专业</th><td>") + "<th>专业</th><td>".length()).trim();
								str = bw.readLine();
								while(str.indexOf("</td><th>") == -1) {
									tempZY += str.trim();
									str = bw.readLine();
								}
								tempZY += str.substring(0, str.indexOf("</td><th>")).trim();
							}else {
								tempZY = str.substring(str.indexOf("<th>专业</th><td>") + "<th>专业</th><td>".length(), str.indexOf("</td><th>"));
							}
							
							if(str.indexOf("</td>            </tr>") == -1) {
								tempQT = str.substring(str.indexOf("<th>其他条件</th><td>") + "<th>其他条件</th><td>".length()).trim();
								str = bw.readLine();
								tempQT = str.substring(0, str.indexOf("</td>            </tr>"));
							}else {
								tempQT = str.substring(str.indexOf("<th>其他条件</th><td>") + "<th>其他条件</th><td>".length(), str.indexOf("</td>            </tr>"));
							}
						}
						
						if(str.indexOf("<th>政治面貌</th><td>") != -1) {
							tempZZMM = str.substring(str.indexOf("<th>政治面貌</th><td>") + "<th>政治面貌</th><td>".length(), str.indexOf("</td><th>"));
						}
						
						
						if(str.indexOf("<th>面试比例</th><td>") != -1) {
							tempMSBL = str.substring(str.indexOf("<th>面试比例</th><td>") + "<th>面试比例</th><td>".length(), str.indexOf("</td><th>"));
							tempBSBL = str.substring(str.indexOf("<th>笔试比例</th><td>") + "<th>笔试比例</th><td>".length(), str.indexOf("</td>            </tr>"));
						}
						
						if(str.indexOf("<th>备注</th><td>") != -1) {
							if(str.indexOf("</td><th>") == -1) {
								tempBZ = str.substring(str.indexOf("<th>备注</th><td>") + "<th>备注</th><td>".length()).trim();
								str = bw.readLine();
								while(str.indexOf("</td><th>") == -1) {
									tempBZ += str.trim();
									str = bw.readLine();
								}
								tempBZ += str.substring(0, str.indexOf("</td><th>")).trim();
							}else {
								tempBZ = str.substring(str.indexOf("<th>备注</th><td>") + "<th>备注</th><td>".length(), str.indexOf("</td><th>"));
							}
							
							tempJS = str.substring(str.indexOf("<th>是否需要加试</th><td>") + "<th>是否需要加试</th><td>".length(), str.indexOf("</td>            </tr>")).trim();
						}
						
					}
					
					/**
					ResultMajorHistoryBean bean = (ResultMajorHistoryBean) JSONObject.parseObject(sb.toString(), ResultMajorHistoryBean.class);
					MajorHistory info = bean.getData();
					if (info == null) {
						continue;
					}
					
					MajorHistoryItem[] elements = info.getItem();
					if(elements != null && elements.length > 0) {
						for(MajorHistoryItem item : elements) {
							SQL.append(item.generateSQL(i)+"\r\n");
						}
					}
					*/
					
					SQL.append("INSERT INTO career_kg_skao values('重庆','2023','" + fileName + "','" + tempZLJG + "','" + tempNSJG + "','" + tempWorkPlace + "','" + tempXT + "','"
							+ tempCode + "','" + tempName + "','" + tempCategory + "','" + tempJJ + "','" + tempZW + "','" + tempRS + "','" + tempFW
							+ "','" + tempDX + "','" + tempXB + "','" + tempXL + "','" + tempXW + "','" + tempZY + "','" + tempQT + "','" + tempZZMM + "','" + tempBSBL + "','" + tempMSBL +"','" + tempBZ + "','" + tempJS +"');\r\n");

					
				}
			} catch (Exception ex) {
				ex.printStackTrace();
				System.out.println("ERROR:"+fileName);
			}

		}

		writeTempFile(new File("E://kaogong//CQdealWithProvinceGangWei.txt"), SQL);
	}
	
	public static void dealWithMajorAdjust() throws Exception {
		StringBuffer SQL = new StringBuffer();
		
		for (int i = 2; i <= 1600; i++) {
			try {
				BufferedReader bw = new BufferedReader(
						new FileReader(new File("E://kaoyan//" + i + "//runn2_4_"+i+"adjust_data.txt")));
				StringBuffer sb = new StringBuffer();
				String str = null;
				while ((str = bw.readLine()) != null) {
					sb.append(str);
				}
				ResultAdjustBean bean = (ResultAdjustBean) JSONObject.parseObject(sb.toString(), ResultAdjustBean.class);
				MajorAdjust info = bean.getData();
				if (info == null) {
					continue;
				}
				
				MajorAdjustItem[] elements = info.getData();
				if(elements != null && elements.length > 0) {
					for(MajorAdjustItem item : elements) {
						SQL.append(item.generateSQL()+"\r\n");
					}
				}
				
				
			} catch (Exception ex) {
			}

		}

		writeTempFile(new File("E://kaoyan//KYSchoolMajorAdjustSQL.txt"), SQL);
	}

	private static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	private static String removeTag(String html) {

		// 定义要匹配的正则表达式模式
		Pattern pattern = Pattern.compile("<[^>]+>", Pattern.CASE_INSENSITIVE);

		// 创建 Matcher 对象并进行匹配操作
		Matcher matcher = pattern.matcher(html);

		// 将匹配到的 HTML 标签替换为空字符串
		String result = matcher.replaceAll("").replaceAll("&nbsp;", "");

		return result.trim();
	}

}
