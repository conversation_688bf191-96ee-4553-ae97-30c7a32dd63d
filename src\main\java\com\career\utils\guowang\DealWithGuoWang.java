package com.career.utils.guowang;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import com.alibaba.fastjson2.JSONObject;
import com.career.utils.HttpSendUtils;
import com.career.utils.Tools;
import com.career.utils.zsky.MajorAdjust;
import com.career.utils.zsky.MajorAdjustItem;
import com.career.utils.zsky.ResultAdjustBean;

public class DealWithGuoWang {

	public static void main(String args[]) throws Exception {
		deal();
	}
	
	public static void getURL() {
		
		File file = new File("E:\\dianwang2025");
		File[] files = file.listFiles();
		int index = 0;
		for(int i = 0; i < files.length; i++) {
			
			try {
				StringBuffer sb = new StringBuffer();
				BufferedReader br = new BufferedReader(new FileReader(files[i]));
				String line = null;
				while((line = br.readLine()) != null) {
					sb.append(line);
				}
				
				if(files[i].getName().indexOf("1") != -1) {
					continue;
				}
				Document detailPage = Jsoup.parse(sb.toString());
				Elements ue_table = detailPage.getElementsByTag("a");
				for(int xi=0;xi<ue_table.size();xi++) {
					Element el = ue_table.get(xi);
					String gw = el.text();
					String link = el.attr("href");
					if(link.indexOf("javascript")!=-1) {
						continue;
					}
					String id = link.substring(link.indexOf("=")+1, link.indexOf("&particulars="));
					index++;
					

					Map<String, String> headers = new HashMap<>();
					headers.put("referer", "https://zhaopin.sgcc.com.cn/sgcchr/static/unitPart.html?bullet_id="+id+"&particulars=flase");
					headers.put("Accept", "application/json, text/javascript, */*; q=0.01");
					headers.put("Accept-Encoding", "gzip, deflate, br, zstd");
					headers.put("Accept-Language", "zh-CN,zh;q=0.9");
					headers.put("Host", "zhaopin.sgcc.com.cn");
					headers.put("cookie", "PW9ydXnjjO8XS=60abeMZbhNbBWRNi2hyfp32UB3dRoh5PEK12eiwEWmjK4bVv2bCaL9YrMVuFyaqrwLzlwapiBS5qwfCGXSi1WSRG; PW9ydXnjjO8XT=0T9B8buMxvvT5A0QeAYMAlZPuOuuOVHbFmcdczF_R7YhoE.f74DQabb.ZIqSmC_atRhm_o2Q9lsffHqHIIZn5RqdqRcpIYyzHXZ_ZNzySpYsoe4bX548hfcm8q1rzB105Z7QX3LFYKwMW2_appqjaFkd.YYKuMcLvt9Ft7.Cd8IxYQjbnI9U6UCx88ByPiBQ73cDJd3e4Y9mcjISVgVCTkiTfq8K4yJngjRFQ.11kiu3YYd5NV92AYTBgmmcYihj0.sYNBTx1Ai73_CHQ.dpcJlMO4pLU3m7X0HBTjgShKq_twcuz_jK1FTqefbwrV9hVd2C6zzqTffTaAMXnnO83XEHlJUtgr7l1Y9hHUnsYOfpeJOApc7wsG9XBPzFysLvmvA7IO3MApckc1Rnr2MZDrq");
					headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
					headers.put("Content-Type", "application/json;charset=utf-8");
					headers.put("Sec-Fetch-Dest", "empty");
					headers.put("Sec-Fetch-Mode", "cors");
					headers.put("Sec-Fetch-Site", "same-origin");
					headers.put("sec-ch-ua", "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"");
					headers.put("sec-ch-ua-mobile", "?0");
					headers.put("sec-ch-ua-platform", "\"Windows\"");


					
					
					String URL = "https://zhaopin.sgcc.com.cn/sgcchr/static/unitPart.html?bullet_id="+id+"&particulars=flase";
					//String resultPageList = HttpSendUtils.get(URL, headers);
					System.out.println(index+"   <a href='"+URL+"'>"+id+"</a><br><br>");
					
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}
	
	public static List<String> deal() throws Exception {
		List<String> list = new ArrayList<>();
		String fileName = null;
		StringBuffer SQL = new StringBuffer();
		int index = 0;
		for (int i = 1; i <= 1; i++) {
			try {
				
				File file = new File("E://dianwang2025//ext");
				File[] fileList = file.listFiles();
				
				for(File xx : fileList) {
					fileName = xx.getName();
					
					if(fileName.indexOf("6.txt") != -1) {
						continue;
					}
					
					BufferedReader bw = new BufferedReader(new FileReader(xx));
					
					String str = null;
					StringBuffer tempSTR = new StringBuffer();
					while ((str = bw.readLine()) != null) {
						tempSTR.append(str);
					}
					
					str = tempSTR.toString();
					try {
					GW_Result bean = (GW_Result) JSONObject.parseObject(str, GW_Result.class);
					GW_Pagedata info = bean.getRetdata().getPage_data();
					if (info == null) {
						continue;
					}
					
					
					Document documentList = Jsoup.parse(info.getContent());
					//String bz = "常规招聘计划";
					Elements trs = documentList.getElementsByTag("tr");
					for(int k=1;k<trs.size();k++) {
						Element tr = trs.get(k);
						Elements tds = tr.getElementsByTag("td");
						String xm = null,xb=null,yxmc=null,dh=null;
						index++;
						try {
						xm = tds.get(0).text();
						xb = tds.get(1).text();
						yxmc = tds.get(2).text();
						dh = tds.get(3).text();
						}catch(Exception ex) {System.out.println(index+"->"+xm);}
						String lsy = info.getBullet_title().substring(0, info.getBullet_title().indexOf("关于2025"));
						
						SQL.append("insert into career_jy_all_2025(src, group_name,lsy,dw,nf,sj,xm,xb,yxmc) values('"+info.getBullet_title()+"','国家电网','"+lsy+"','"+lsy+"',2025,'"+20250121+"','"+xm+"','"+xb+"','"+yxmc+"');\r\n");
					}
					
					}catch(Exception ex) {
						ex.printStackTrace();
						System.out.println("ERROR:"+fileName);}

					
				}
				System.out.print(index);
				writeTempFile(new File("E://dianwang2025//Result5551.txt"), SQL);
			} catch (Exception ex) {
				ex.printStackTrace();
				System.out.println("ERROR:"+fileName);
			}

		}
		
		return list;
		
	}
	
	
	public static void dealResultJson() throws Exception {
		StringBuffer SQL = new StringBuffer();
		HashSet<String> hs = new HashSet<>();
		String fileName = null;
		StringBuffer temp = new StringBuffer();
		temp.append("gw_90a3d270a9224968a08a62d1bd52114e.txt");
		temp.append("gw_f65560d10f024fb292fe1d978d1a68c8.txt");
		temp.append("gw_76122aa1988748c4a486f6463c4ef2b3.txt");
		temp.append("gw_a4c70fbed13e419ab97bf2b70ded5b65.txt");
		temp.append("gw_70410f5d97d543f683b2a5546138f32c.txt");
		temp.append("gw_1a98c3bdd567406a8135936b0ff73426.txt");
		temp.append("gw_b3e6bcf41f91416f9219a73d150cafc2.txt");
		temp.append("gw_4a33734754854ef9b4bc15cce1fbf52d.txt");
		temp.append("gw_30af081b75594a1ea0d8297eba4cf045.txt");
		temp.append("gw_b49cff7792174aaab1864c81354666c2.txt");
		temp.append("gw_bf450a709e094855969997684a8f0672.txt");
		temp.append("gw_bd71d91f710d4e3392d0ac640d7b0e67.txt");
		temp.append("gw_dc3e754932434c5585836d70aa1de26c.txt");
		temp.append("gw_2c152ef653b141119b7b980c94bd4e30.txt");
		temp.append("gw_bc90aceffadb4ba4a7c395464ea61184.txt");
		temp.append("gw_428376557d0b4860a868da488e52aa4c.txt");
		temp.append("gw_f47a305972aa4dfeaf37da02eeda3182.txt");
		temp.append("gw_09649edd2d8c4feabccb7907b677dbc2.txt");
		temp.append("gw_fd6a1f5820db46ad841ce2218f5fcf83.txt");
		temp = null;
		temp = new StringBuffer();
		
		int index = 0;
		for (int i = 1; i <= 1; i++) {
			try {
				
				File file = new File("G:\\就业数据汇总\\dw2");
				File[] fileList = file.listFiles();
				
				for(File xx : fileList) {
					fileName = xx.getName();
					
					if(fileName.indexOf(".txt") == -1) {
						continue;
					}

					if(temp.toString().indexOf(fileName) == -1) {
						continue;
					}
						
					System.out.println(fileName);
					
					BufferedReader bw = new BufferedReader(new FileReader(xx));

					String str = null;
					StringBuffer tempSTR = new StringBuffer();
					while ((str = bw.readLine()) != null) {
						tempSTR.append(str);
					}
					
					str = tempSTR.toString();
					
					
					if(tempSTR.indexOf("2023-12") != -1) {
						System.out.println(fileName);
						continue;
					}
					
					String companyName = tempSTR.substring(tempSTR.indexOf("unit_name") + ("unit_name".length()) + 3, tempSTR.indexOf("\"}},\"retmsg\":\"成功\"}"));

					
					int st = -1;
					int innerSpanIndex = 0;
					StringBuffer innerSpanIndexStr = new StringBuffer();
					while((st = str.indexOf("宋体; font-size: 10.0000pt")) != -1) {
						str = str.substring(st);
						String tempURL = str.substring(("宋体; font-size: 10.0000pt".length()) + 4, str.indexOf("</span></p>"));
						//System.out.println(tempURL);
						
						str = str.substring(str.indexOf("宋体; font-size: 10.0000pt") + 20);
						innerSpanIndex++;
						
						if(innerSpanIndex % 4 > 0) {
							innerSpanIndexStr.append("'"+tempURL+"',");
						}
						
						
						if(innerSpanIndex % 4 == 0) {
							if(innerSpanIndexStr.toString().indexOf("<span style") != -1) {
								hs.add(fileName);
								innerSpanIndexStr = new StringBuffer();
								continue;
							}
							SQL.append("insert into career_jiuye(dw, xm, xb, yxmc,ssgs,sshy) values('"+companyName+"',"+innerSpanIndexStr.toString()+"'国家电网','电气');\r\n");
							innerSpanIndexStr = new StringBuffer();
						}
					}
					
					/**
					ResultMajorHistoryBean bean = (ResultMajorHistoryBean) JSONObject.parseObject(sb.toString(), ResultMajorHistoryBean.class);
					MajorHistory info = bean.getData();
					if (info == null) {
						continue;
					}
					
					MajorHistoryItem[] elements = info.getItem();
					if(elements != null && elements.length > 0) {
						for(MajorHistoryItem item : elements) {
							SQL.append(item.generateSQL(i)+"\r\n");
						}
					}
					*/
					
				}
			} catch (Exception ex) {
				ex.printStackTrace();
				System.out.println("ERROR:"+fileName);
			}

		}
		
		Iterator<String> it = hs.iterator();
		while(it.hasNext()) {
			System.out.println("redo - " + it.next());
		}
		
		
		writeTempFile(new File("E://dianwang2024//result/Result555.txt"), SQL);
		
	}
	
	//不同格式的内容
	public static void dealResultJson2() throws Exception {
		StringBuffer SQL = new StringBuffer();
		HashSet<String> hs = new HashSet<>();
		String fileName = null;
		StringBuffer temp = new StringBuffer();
		temp.append("gw_4a0b8fca3a8a4f1da36238cef7bb0c0a.txt");//
		
		int index = 0;
		for (int i = 1; i <= 1; i++) {
			try {
				
				File file = new File("E://dianwang2024//");
				File[] fileList = file.listFiles();
				
				for(File xx : fileList) {
					fileName = xx.getName();
					
					if(fileName.indexOf(".txt") == -1) {
						continue;
					}

					if(temp.toString().indexOf(fileName) == -1) {
						continue;
					}

					
					BufferedReader bw = new BufferedReader(new FileReader(xx));

					String str = null;
					StringBuffer tempSTR = new StringBuffer();
					while ((str = bw.readLine()) != null) {
						tempSTR.append(str);
					}
					
					str = tempSTR.toString();
					
					
					if(tempSTR.indexOf("2023-12") != -1) {
						System.out.println(fileName);
						continue;
					}
					
					String companyName = tempSTR.substring(tempSTR.indexOf("unit_name") + ("unit_name".length()) + 3, tempSTR.indexOf("\"}},\"retmsg\":\"成功\"}"));
					System.out.println(i+","+companyName + "," + fileName);
					//companyName = "南瑞集团有限公司";
					int st = -1;
					int innerSpanIndex = 0;
					StringBuffer innerSpanIndexStr = new StringBuffer();
					while((st = str.indexOf("font-size: 10.0pt; font-family: 宋体;")) != -1) {
						str = str.substring(st);
						String tempURL = str.substring(("font-size: 10.0pt; font-family: 宋体;".length()) + 3, str.indexOf("</span>"));
						System.out.println(tempURL);
						
						str = str.substring(str.indexOf("font-size: 10.0pt; font-family: 宋体;") + 20);
						innerSpanIndex++;
						
						innerSpanIndexStr.append("'"+tempURL+"',");
						
						
						if(innerSpanIndex % 4 == 0) {
							if(innerSpanIndexStr.toString().indexOf("<span style") != -1) {
								hs.add(fileName);
								//innerSpanIndexStr = new StringBuffer();
								//continue;
							}
							SQL.append("insert into career_jiuye(dw, xm, xb, yxmc,ttt,ssgs,sshy, dt, nf) values('"+companyName+"',"+innerSpanIndexStr.toString()+"'国家电网','电气',now(),2024);\r\n");
							innerSpanIndexStr = new StringBuffer();
						}
					}
					
					/**
					ResultMajorHistoryBean bean = (ResultMajorHistoryBean) JSONObject.parseObject(sb.toString(), ResultMajorHistoryBean.class);
					MajorHistory info = bean.getData();
					if (info == null) {
						continue;
					}
					
					MajorHistoryItem[] elements = info.getItem();
					if(elements != null && elements.length > 0) {
						for(MajorHistoryItem item : elements) {
							SQL.append(item.generateSQL(i)+"\r\n");
						}
					}
					*/
					
				}
			} catch (Exception ex) {
				ex.printStackTrace();
				System.out.println("ERROR:"+fileName);
			}

		}
		
		Iterator<String> it = hs.iterator();
		while(it.hasNext()) {
			System.out.println("redo - " + it.next());
		}
		
		
		writeTempFile(new File("E://dianwang2024//result/Result2.txt"), SQL);
		
	}
	

	private static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
}
