<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ page import="com.career.utils.*,com.career.db.*,java.util.*"%>

<%@include file="/WEB-INF/include/_session_ajax_ai.jsp"%>  
<%
AiCard aiCard = (AiCard)session.getAttribute(ZyzdCache.SES_KEY_AI_BASE_CARD); 
AiJDBC aiJDBC = new AiJDBC();
ZyzdJDBC zyzdJDBC = new ZyzdJDBC();

ZyzdProvinceConfig provinceConfig = ZyzdCache.getProvinceConfig(aiCard.getC_prov());
String provinceTableName = provinceConfig.getP_table_code();
String provinceName = provinceConfig.getP_name();
int jh_version = provinceConfig.getJh_version();
String xkCode = XKCombineUtils.getXKCodeByStudentSelection(aiCard.getC_xk());

int LATEST_JH_YEAR = provinceConfig.getLatest_year_jh();

int jh_id = Tools.getInt(request.getParameter("jh_id"));
JHBean bean = aiJDBC.getOriginalJhData(LATEST_JH_YEAR, provinceTableName, jh_id);
if(bean == null){
	out.print("ERR:NO.FOUND");
	return;
}

com.zsdwf.db.YGDataPatchDBTools dp = new com.zsdwf.db.YGDataPatchDBTools();

HashMap<String, ZDKSRank> TONGWF_MAP = zyzdJDBC.getAllTongWF(provinceName, xkCode, aiCard.getC_score_wc());
ZDKSRank rankYearA = TONGWF_MAP.get(String.valueOf(LATEST_JH_YEAR - 1)); 
ZDKSRank rankYearB = TONGWF_MAP.get(String.valueOf(LATEST_JH_YEAR - 2));
ZDKSRank rankYearC = TONGWF_MAP.get(String.valueOf(LATEST_JH_YEAR - 3));

boolean is96 = provinceConfig.getForm_type() == 2;

List<ZDKSRank> list = zyzdJDBC.getAllZdksRankByKL(LATEST_JH_YEAR, provinceName, xkCode);
Map<Integer, Integer> rankScoreData = new TreeMap<Integer, Integer>();
for(ZDKSRank rank : list) {
	rankScoreData.put(rank.getWc(), rank.getScore());
}
TrendScoreCalculator calculator = new TrendScoreCalculator(rankScoreData);

String key = Tools.trim(bean.getYxmc()) + Tools.trim(bean.getZymc()) + Tools.trim(bean.getYxdm()) + Tools.trim(bean.getZydm());

int zdfwc_a = Tools.getInt(bean.getZdfwc_a());
int zdfwc_b = Tools.getInt(bean.getZdfwc_b());
int zdfwc_c = Tools.getInt(bean.getZdfwc_c());

int[] historicalRanks = {zdfwc_a, zdfwc_b, zdfwc_c};
int[] currentRanks = calculator.convertToCurrentYearRanks(historicalRanks);


int dxfwc_a = zdfwc_a > 0 ? currentRanks[0] : 0;
int dxf_a = zdfwc_a > 0 ? calculator.getScoreByRank(currentRanks[0]) : 0;
int dxf_a_cz = dxf_a > 0 ? (aiCard.getC_score() - dxf_a) : 0;

int dxfwc_b = zdfwc_b > 0 ? currentRanks[1] : 0;
int dxf_b = zdfwc_b > 0 ? calculator.getScoreByRank(currentRanks[1]) : 0;
int dxf_b_cz = dxf_b > 0 ? (aiCard.getC_score() - dxf_b) : 0;


int dxfwc_c = zdfwc_c > 0 ? currentRanks[2] : 0;
int dxf_c = zdfwc_c > 0 ? calculator.getScoreByRank(currentRanks[2]) : 0;
int dxf_c_cz = dxf_c > 0 ? (aiCard.getC_score() - dxf_c) : 0;


int jhs_a = Tools.getInt(Tools.getInt(bean.getJhs_a()) > 0 ? bean.getJhs_a() : Tools.getInt(bean.getLqrs_a()) > 0 ? bean.getLqrs_a() : "");
int jhs_b = Tools.getInt(Tools.getInt(bean.getJhs_b()) > 0 ? bean.getJhs_b() : Tools.getInt(bean.getLqrs_b()) > 0 ? bean.getLqrs_b() : "");
int jhs_c = Tools.getInt(Tools.getInt(bean.getJhs_c()) > 0 ? bean.getJhs_c() : Tools.getInt(bean.getLqrs_c()) > 0 ? bean.getLqrs_c() : "");
int jhs = Tools.getInt(bean.getJhs());

int qs_yc = (int)Math.ceil(Tools.qs_yc(dxf_a, dxf_b, dxf_c)); 

com.zsdwf.db.PredictBean predictBean = dp.yc2023WC721(zdfwc_c, zdfwc_b, zdfwc_a, "", 1);
%>

                                <div class="table-responsive">
                                    <table class="table text-nowrap table-bordered">
								    <thead>
								        <tr class="table-secondary">
								            <th style="width:15%;">院校名称</th>
								            <th style="width:15%;">专业名称</th> 
								             
								            <th style="width:6%;">年份</th>  
								            <th style="width:6%;">计划数</th> 
								            <th style="width:8%;">最低分</th> 
								            <th style="width:8%;">平均分</th>  
								            <th style="width:8%;">最高分</th>   
								            
								        </tr>
								    </thead>
								    <tbody> 
                                    <tr>
							            <td rowspan=4> 
							            	<div>
							                    <p class="mb-0 fw-semibold">
							            			<span class="mb-0 fw-semibold"><%=Tools.view(bean.getYxmc()) %></span>
							            		</p>
							                    <p class="fs-10 text-danger fw-semibold"><span class="form-check-label fs-10">[<%=Tools.view(bean.getYxdm()) %>]</span>  <%=Tools.view(bean.getYxsf()) %>.<%=Tools.view(bean.getYxcs()) %></p>
							                    
							                </div>
							            </td> 
							            <td rowspan=4> 
							            	<div>
							                    <p class="mb-0 fw-semibold"> 
							            			<span class="mb-0 fw-semibold"><%=Tools.view(bean.getZymc_org()) %></span>
							            		</p>
							                    <p class="mb-0 fs-10 text-primary fw-semibold">
							                    	<span class="form-check-label fs-10">[<%=Tools.view(bean.getZydm()) %>]</span> <%=Tools.view(bean.getZyml()) %></p> 
							                    	
							                    	<%if(!is96){ %>
							                    <p class="mb-0 fs-10 text-primary fw-semibold">第<span style="font-size:16px;color:#000;"><%=Tools.view(bean.getZyz()) %></span>组</p>
							                    <%} %>
							                    
							                    <%if(jhs > 0){ %>
							                    	<p class="mb-0 fs-10 text-primary fw-semibold"><span style="color:#000;"><%=LATEST_JH_YEAR %>年计划数：</span> 
									            		<%if(jhs > jhs_a){ %>
									            		<span class="text-danger"><%=jhs %><i class="ti ti-arrow-big-up-lines ms-1"></i></span>
									            		<%}else if(jhs == jhs_a){ %>
									            		<span class="text-success"><%=jhs %><i class="ti ti-arrow-narrow-left ms-1"></i></span> 
									            		<%}else{ %> 
									            		<span class="text-success"><%=jhs %><i class="ti ti-arrow-big-down-lines ms-1"></i></span>
									            		<%} %>
									            	</p>
									            	<%} %> 
							                    <%if(!Tools.isEmpty(bean.getZnzy())){ %>
							                    <p class="mb-0 fs-10 text-primary fw-semibold"><%=Tools.viewForLimitLength(bean.getZnzy(), 20) %></p>
							                    <%} %>
							                </div>
							            </td> 
							        </tr>
							        
							        <tr>   
							            <td class="table-active"><%=LATEST_JH_YEAR - 1 %></td>  
							            <td>
							            <span class="text-success">
							                	<%if(jhs_a > 0){ %>
							                		<%if(jhs_a > jhs_b){ %>
							                		<span class="text-danger"><%=jhs_a %><i class="ti ti-arrow-big-up-lines ms-1"></i></span>
							                		<%}else if(jhs_a == jhs_b){ %>
							                		<span class="text-success"><%=jhs_a %><i class="ti ti-arrow-narrow-left ms-1"></i></span>
							                		<%}else{ %>
							                		<span class="text-success"><%=jhs_a %><i class="ti ti-arrow-big-down-lines ms-1"></i></span>
							                		<%} %>
							                	<%}else{ %>  
							                		-
							                	<%} %>
							                </span>
							            </td>
							            
							            <td>  
							            	<div>
							                    <span class="mb-0 fw-semibold"> 
							            			<span class="mb-0 fw-semibold"><%=Tools.viewScore(bean.getZdf_a()) %></span>
							            		</span> 
							                    <span class="fs-10 text-primary fw-semibold"><%=Tools.viewScore(bean.getZdfwc_a()) %></span>
							                </div>
							            </td>
							            <td>
							            	<div>
							                    <span class="mb-0 fw-semibold"> 
							            			<span class="mb-0 fw-semibold"><%=Tools.viewScore(bean.getPjf_a()) %></span>
							            		</span> 
							                    <span class="fs-10 text-primary fw-semibold"><%=Tools.viewScore(bean.getPjfwc_a()) %></span>
							                </div>
							            </td>
							            <td>
							            	<div>
							                    <span class="mb-0 fw-semibold"> 
							            			<span class="mb-0 fw-semibold"><%=Tools.viewScore(bean.getZgf_a()) %></span>
							            		</span>  
							                    <span class="fs-10 text-primary fw-semibold"><%=Tools.viewScore(bean.getZgfwc_a()) %></span>
							                </div>
							            </td>
							            
							            
							         </tr>    
							         
							         <tr>   
							            <td class="table-active"><%=LATEST_JH_YEAR - 2%></td> 
							            <td>
							            <%if(jhs_b > 0){ %>
							            		<%if(jhs_b > jhs_c){ %>
							            		<span class="text-danger"><%=jhs_b %><i class="ti ti-arrow-big-up-lines ms-1"></i></span>
							            		<%}else if(jhs_b == jhs_c){ %>
							               		<span class="text-success"><%=jhs_b %><i class="ti ti-arrow-narrow-left ms-1"></i></span>
							               		<%}else{ %>
							            		<span class="text-success"><%=jhs_b %><i class="ti ti-arrow-big-down-lines ms-1"></i></span>
							            		<%} %>
							            	<%}else{ %>  
							            		-
							            	<%} %>
							            </td>
							            
							            <td> 
							            	<div>
							                    <span class="mb-0 fw-semibold"> 
							            			<span class="mb-0 fw-semibold"><%=Tools.viewScore(bean.getZdf_b()) %></span>
							            		</span> 
							                    <span class="fs-10 text-primary fw-semibold"><%=Tools.viewScore(bean.getZdfwc_b()) %></span>
							                </div>
							            </td>
							            <td>
							            	<div>
							                    <span class="mb-0 fw-semibold"> 
							            			<span class="mb-0 fw-semibold"><%=Tools.viewScore(bean.getPjf_b()) %></span>
							            		</span> 
							                    <span class="fs-10 text-primary fw-semibold"><%=Tools.viewScore(bean.getPjfwc_b()) %></span>
							                </div>
							            </td>
							            <td>
							            	<div>
							                    <span class="mb-0 fw-semibold"> 
							            			<span class="mb-0 fw-semibold"><%=Tools.viewScore(bean.getZgf_b()) %></span> 
							            		</span>  
							                    <span class="fs-10 text-primary fw-semibold"><%=Tools.viewScore(bean.getZgfwc_b()) %></span>
							                </div>
							            </td>
							         </tr>   
							           
							         <tr>     
							            <td class="table-active"><%=LATEST_JH_YEAR - 3%></td>  
							            <td><%=Tools.view(bean.getJhs_c()) %></td> 
							            
							            <td> 
							            	<div>
							                    <span class="mb-0 fw-semibold"> 
							            			<span class="mb-0 fw-semibold"><%=Tools.viewScore(bean.getZdf_c()) %></span>
							            		</span> 
							                    <span class="fs-10 text-primary fw-semibold"><%=Tools.viewScore(bean.getZdfwc_c()) %></span>
							                </div>
							            </td> 
							            <td>
							            	<div>
							                    <span class="mb-0 fw-semibold">   
							            			<span class="mb-0 fw-semibold"><%=Tools.viewScore(bean.getPjf_c()) %></span> 
							            		</span> 
							                    <span class="fs-10 text-primary fw-semibold"><%=Tools.viewScore(bean.getPjfwc_c()) %></span>
							                </div>
							            </td>
							            <td> 
							            	<div>
							                    <span class="mb-0 fw-semibold"> 
							            			<span class="mb-0 fw-semibold"><%=Tools.viewScore(bean.getZgf_c()) %></span> 
							            		</span>  
							                    <span class="fs-10 text-primary fw-semibold"><%=Tools.viewScore(bean.getZgfwc_c()) %></span>
							                </div>
							            </td> 
							         </tr>  
							         
							         
							         <tr><td colspan="10" class="mt-2" style="height:1px;padding:0px;">&nbsp;</td></tr>  
							          
							    </tbody>
							</table>
							</div>
							
							 <div class="table-responsive">
								<table class="table text-nowrap table-striped table-bordered">
	                                        <tbody>
	                                        	<tr class="table-info"> 
	                                                <td colspan="4"><b style="font-size:16px;color:blue;">趋势预测算法</b>（假设<%=LATEST_JH_YEAR %>年考生成绩<%=aiCard.getC_score() %>，位次<%=aiCard.getC_score_wc() %>）</td>
	                                            </tr>
	                                            <tr> 
	                                            	<td>年份</td> 
	                                                <td scope="row">2024年</td>
	                                                <td>2023年</td>
	                                                <td>2022年</td>
	                                            </tr>
	                                            <tr>
	                                            	<td><%=bean.getYxmc_org() %>/<%=bean.getZymc_org() %></td>
	                                                <td scope="row"><b><%=Tools.viewScore(bean.getZdf_a()) %></b> <span style="font-size:10px;"><%=Tools.viewScore(bean.getZdfwc_a()) %></span></td>
	                                                <td scope="row"><b><%=Tools.viewScore(bean.getZdf_b()) %></b> <span style="font-size:10px;"><%=Tools.viewScore(bean.getZdfwc_b()) %></span></td>
	                                                <td scope="row"><b><%=Tools.viewScore(bean.getZdf_c()) %></b> <span style="font-size:10px;"><%=Tools.viewScore(bean.getZdfwc_c()) %></span></td>
	                                            </tr>
	                                            <tr class="table-warning" style="text-align:center;"> 
	                                            	<td colspan="4"><span style="font-size:14px;color:red;">根据历年招生计划变化及上线人数变化</span></td>
	                                            </tr>
	                                            <tr class="table-danger"> 
	                                            	<td><b style="color:red;">A</b>换算为<%=LATEST_JH_YEAR %>年分数（<b style="color:red;">B</b>）</td>  
	                                                <td scope="row"><b><%=dxf_a %></b>(趋势等位分) </td>
	                                                <td scope="row"><b><%=dxf_b %></b>(趋势等位分) </td> 
	                                                <td scope="row"><b><%=dxf_c %></b>(趋势等位分) </td> 
	                                            </tr> 
	                                            <tr> 
	                                                <td><b>考生<%=LATEST_JH_YEAR %>年高考成绩（<b style="color:red;">C</b>）</b></td> 
	                                                <td colspan="3" align="center"><b style="color:blue;"><%=aiCard.getC_score() %></b></td> 
	                                            </tr>
	                                            <tr>
	                                                <td colspan="4"><b>用三年趋势分数（<b style="color:red;">B</b>），与<%=LATEST_JH_YEAR %>年学生高考成绩（<b style="color:red;">C</b>）比较，即可作为能否上线的标准</b></td>
	                                            </tr>
	                                
	                                        </tbody>
	                                    </table>
                                </div>
                                
                                <%if("1".equals("2")){ %>
                                <div class="table-responsive" style="margin-top:20px;">
									<table class="table text-nowrap table-striped table-bordered">
	                                        <tbody> 
	                                        	<tr class="table-info"> 
	                                                <td colspan="4"><b style="font-size:16px;color:blue;">等效分算法</b>（假设<%=LATEST_JH_YEAR %>年考生成绩<%=aiCard.getC_score() %>，位次<%=aiCard.getC_score_wc() %>）</td>
	                                            </tr>
	                                            <tr>
	                                                <td><b>考生<%=LATEST_JH_YEAR %>年高考成绩</b></td> 
	                                                <td colspan="3" align="center"><b>成绩<%=aiCard.getC_score() %></b>， 位次<b style="color:blue;"><%=aiCard.getC_score_wc() %></b></td> 
	                                            </tr>
	                                            <tr> 
	                                            	<td>年份</td>
	                                                <td scope="row"><%=LATEST_JH_YEAR-1 %>年</td>
	                                                <td><%=LATEST_JH_YEAR-2 %>年</td>
	                                                <td><%=LATEST_JH_YEAR-3 %>年</td>
	                                            </tr>
	                                            <tr> 
	                                            	<td>与考生<%=LATEST_JH_YEAR %>年高考位次近似的位次（<b style="color:red;">A</b>）</td>
	                                                <td scope="row"><%=rankYearA.getWc() %></td>
	                                                <td scope="row"><%=rankYearB.getWc() %></td>
	                                                <td scope="row"><%=rankYearC.getWc() %></td>
	                                            </tr>
	                                            <tr class="table-danger"> 
	                                            	<td>把结果<b style="color:red;">A</b>换算为各年的分数（<b style="color:red;">B</b>）</td>  
	                                                <td scope="row"><b><%=rankYearA.getScore() %></b> </td>
	                                                <td scope="row"><b><%=rankYearB.getScore() %></b> </td>
	                                                <td scope="row"><b><%=rankYearC.getScore() %></b> </td>
	                                            </tr> 
	                                            <tr>
	                                            	<td><b><%=bean.getYxmc_org() %>/<%=bean.getZymc_org() %></b> 各年录取最低分（<b style="color:red;">C</b>）</td>
	                                                <td scope="row"><b><%=Tools.view(bean.getZdf_a()) %></b> </td>
	                                                <td scope="row"><b><%=Tools.view(bean.getZdf_b()) %></b> </td>
	                                                <td scope="row"><b><%=Tools.view(bean.getZdf_c()) %></b> </td>
	                                            </tr>
	                                            
	                                            <tr>
	                                                <td colspan="4"><b>用换算后的各年分数（<b style="color:red;">B</b>），与院校/专业历年的分数（<b style="color:red;">C</b>）对比，即可作为能否上线的标准</b></td>
	                                            </tr>
	                                
	                                        </tbody>
	                                    </table>
                                </div>
                                
                                <div class="table-responsive" style="margin-top:20px;">
									<table class="table text-nowrap table-striped table-bordered">
	                                        <tbody>
	                                        	<tr class="table-info"> 
	                                                <td colspan="4"><b style="font-size:16px;color:blue;">权重位次算法</b>（假设<%=LATEST_JH_YEAR %>年考生成绩<%=aiCard.getC_score() %>，位次<%=aiCard.getC_score_wc() %>）</td>
	                                            </tr>
	                                            <tr> 
	                                            	<td>年份</td> 
	                                                <td scope="row">2024年</td>
	                                                <td>2023年</td>
	                                                <td>2022年</td>
	                                            </tr>
	                                            <tr>
	                                            	<td><%=bean.getYxmc_org() %>/<%=bean.getZymc_org() %></td>
	                                                <td scope="row"><b><%=Tools.view(bean.getZdf_a()) %></b> <span style="font-size:10px;"><%=Tools.view(bean.getZdfwc_a()) %></span></td>
	                                                <td scope="row"><b><%=Tools.view(bean.getZdf_b()) %></b> <span style="font-size:10px;"><%=Tools.view(bean.getZdfwc_a()) %></span></td>
	                                                <td scope="row"><b><%=Tools.view(bean.getZdf_c()) %></b> <span style="font-size:10px;"><%=Tools.view(bean.getZdfwc_a()) %></span></td>
	                                            </tr>
	                                            <tr>
	                                            	<td>年份权重占比(可按需调整)</td>
	                                                <td scope="row">70%</td>
	                                                <td scope="row">20%</td>
	                                                <td scope="row">10%</td>
	                                            </tr>
	                                            <tr class="table-warning"> 
	                                            	<td>得到占比后的3年位次和（<b style="color:red;">A</b>）</td>  
	                                                <td colspan="3" scope="row">
	                                                	<%if(predictBean.getResultMin() > 1){ 
	                                                	%>
									                	<span style="font-size:12px;"> 
									                   	<%=Tools.view(String.valueOf(predictBean.getResultMin()))%>
									                   	</span>
									                   	<%}else{ %>
									                   	<span class="text-muted fs-10">-</span>
									                   	<%} %>
	                                                </td>
	                                            </tr>
	                                            <tr class="table-danger"> 
	                                            	<td>把结果<b style="color:red;">A</b>换算为<%=LATEST_JH_YEAR %>年分数（<b style="color:red;">B</b>）</td>  
	                                                <td colspan="3" scope="row"><% 
	                                                		HashMap<String, ZDKSRank> TONGWF_MAPX = zyzdJDBC.getAllTongWF(provinceName, xkCode, predictBean.getResultMin());
                                            				ZDKSRank rankYear0 = TONGWF_MAPX.get(String.valueOf(LATEST_JH_YEAR)); 
                                            				if(rankYear0 != null){
                                            					out.print(rankYear0.getScore());
                                            				}else{
                                            					out.print("-");
                                            				}
	                                                	
	                                                %></td>
	                                            </tr>  
	                                            <tr>
	                                                <td><b>考生<%=LATEST_JH_YEAR %>年高考成绩（<b style="color:red;">C</b>）</b></td> 
	                                                <td colspan="3" align="center"><b style="color:blue;"><%=aiCard.getC_score() %></b></td> 
	                                            </tr>
	                                            <tr>
	                                                <td colspan="4"><b>用换算后的<%=LATEST_JH_YEAR %>年分数（<b style="color:red;">B</b>），直接与<%=LATEST_JH_YEAR %>年学生高考成绩（<b style="color:red;">C</b>）</b>比较，即可作为能否上线的标准</b></td>
	                                            </tr>
	                                
	                                        </tbody>
	                                    </table>
                                </div>
                                <%}%>
