package com.career.db;

import com.career.utils.BaseBean;

public class ArtJhBean extends BaseBean {
    
    // 对应数据库表字段
    private int yxdm;                    // 学校代号
    private String yxmc;                 // 学校名称
    private String pc;                   // 批次
    private String xk;                   // 文理
    private String ysfl;                 // 统考类
    private String zymc;                 // 招生专业
    private String zydm;                 // 专业代码
    private String zkfx;                 // 招考方向
    private String zkfx_ext;             // 包含招考方向
    private String bhzy;                 // 包含专业
    private int xz;                      // 学制
    private String xf;                   // 学费
    private int jhs;                     // 计划数
    private String zybz;                 // 备注
    private double zhcj;                 // 综合成绩
    private double zyfs;                 // 专业分数
    private int wc;                      // 位次
    private String zgbm;                 // 主管部门
    private String yxcs;                 // 所在地
    private String csbq;                 // 城市标签
    private String pc_code;              // 办学层次
    private String ind_catg;             // 院校类型
    private String ind_nature;           // 备注.1
    private String gxcc;                 // 高校层次
    private String bc;                   // 别称
    private String bjzyjh;               // 拔尖卓越计划
    private String xygm;                 // 学校共建及更名合并
    private String gx_zzy;               // 高校信息.转专业情况
    private int gx_ssd;                  // 高校信息.全校硕士专业数
    private String gx_sszy;              // 高校信息.全校硕士专业
    private int gx_bsd;                  // 全校博士专业数
    private String gx_bszy;              // 全校博士专业
    private double gx_byl2023;           // 2023届保研率
    private String gx_pm;                // 高校排名

    // 构造函数
    public ArtJhBean() {}

    // Getter和Setter方法
    public int getYxdm() {
        return yxdm;
    }

    public void setYxdm(int yxdm) {
        this.yxdm = yxdm;
    }

    public String getYxmc() {
        return yxmc;
    }

    public void setYxmc(String yxmc) {
        this.yxmc = yxmc;
    }

    public String getPc() {
        return pc;
    }

    public void setPc(String pc) {
        this.pc = pc;
    }

    public String getXk() {
        return xk;
    }

    public void setXk(String xk) {
        this.xk = xk;
    }

    public String getYsfl() {
        return ysfl;
    }

    public void setYsfl(String ysfl) {
        this.ysfl = ysfl;
    }

    public String getZymc() {
        return zymc;
    }

    public void setZymc(String zymc) {
        this.zymc = zymc;
    }

    public String getZydm() {
        return zydm;
    }

    public void setZydm(String zydm) {
        this.zydm = zydm;
    }

    public String getZkfx() {
        return zkfx;
    }

    public void setZkfx(String zkfx) {
        this.zkfx = zkfx;
    }

    public String getZkfx_ext() {
        return zkfx_ext;
    }

    public void setZkfx_ext(String zkfx_ext) {
        this.zkfx_ext = zkfx_ext;
    }

    public String getBhzy() {
        return bhzy;
    }

    public void setBhzy(String bhzy) {
        this.bhzy = bhzy;
    }

    public int getXz() {
        return xz;
    }

    public void setXz(int xz) {
        this.xz = xz;
    }

    public String getXf() {
        return xf;
    }

    public void setXf(String xf) {
        this.xf = xf;
    }

    public int getJhs() {
        return jhs;
    }

    public void setJhs(int jhs) {
        this.jhs = jhs;
    }

    public String getZybz() {
        return zybz;
    }

    public void setZybz(String zybz) {
        this.zybz = zybz;
    }

    public double getZhcj() {
        return zhcj;
    }

    public void setZhcj(double zhcj) {
        this.zhcj = zhcj;
    }

    public double getZyfs() {
        return zyfs;
    }

    public void setZyfs(double zyfs) {
        this.zyfs = zyfs;
    }

    public int getWc() {
        return wc;
    }

    public void setWc(int wc) {
        this.wc = wc;
    }

    public String getZgbm() {
        return zgbm;
    }

    public void setZgbm(String zgbm) {
        this.zgbm = zgbm;
    }

    public String getYxcs() {
        return yxcs;
    }

    public void setYxcs(String yxcs) {
        this.yxcs = yxcs;
    }

    public String getCsbq() {
        return csbq;
    }

    public void setCsbq(String csbq) {
        this.csbq = csbq;
    }

    public String getPc_code() {
        return pc_code;
    }

    public void setPc_code(String pc_code) {
        this.pc_code = pc_code;
    }

    public String getInd_catg() {
        return ind_catg;
    }

    public void setInd_catg(String ind_catg) {
        this.ind_catg = ind_catg;
    }

    public String getInd_nature() {
        return ind_nature;
    }

    public void setInd_nature(String ind_nature) {
        this.ind_nature = ind_nature;
    }

    public String getGxcc() {
        return gxcc;
    }

    public void setGxcc(String gxcc) {
        this.gxcc = gxcc;
    }

    public String getBc() {
        return bc;
    }

    public void setBc(String bc) {
        this.bc = bc;
    }

    public String getBjzyjh() {
        return bjzyjh;
    }

    public void setBjzyjh(String bjzyjh) {
        this.bjzyjh = bjzyjh;
    }

    public String getXygm() {
        return xygm;
    }

    public void setXygm(String xygm) {
        this.xygm = xygm;
    }

    public String getGx_zzy() {
        return gx_zzy;
    }

    public void setGx_zzy(String gx_zzy) {
        this.gx_zzy = gx_zzy;
    }

    public int getGx_ssd() {
        return gx_ssd;
    }

    public void setGx_ssd(int gx_ssd) {
        this.gx_ssd = gx_ssd;
    }

    public String getGx_sszy() {
        return gx_sszy;
    }

    public void setGx_sszy(String gx_sszy) {
        this.gx_sszy = gx_sszy;
    }

    public int getGx_bsd() {
        return gx_bsd;
    }

    public void setGx_bsd(int gx_bsd) {
        this.gx_bsd = gx_bsd;
    }

    public String getGx_bszy() {
        return gx_bszy;
    }

    public void setGx_bszy(String gx_bszy) {
        this.gx_bszy = gx_bszy;
    }

    public double getGx_byl2023() {
        return gx_byl2023;
    }

    public void setGx_byl2023(double gx_byl2023) {
        this.gx_byl2023 = gx_byl2023;
    }

    public String getGx_pm() {
        return gx_pm;
    }

    public void setGx_pm(String gx_pm) {
        this.gx_pm = gx_pm;
    }
} 