package com.career.utils;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.career.db.JHBean;
import com.career.db.ZDKSRank;

import org.apache.poi.ss.util.CellRangeAddress;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;

public class ExcelJHBeanExporter {
	
	static int LATEST_JH_YEAR = 2025;
	
	public static void exportToExcel(HashMap<String, ZDKSRank> TONGWF_MAP, List<JHBean> jhDataList, String filePath) throws IOException {
        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        // 创建工作表
        Sheet sheet = workbook.createSheet("查詢結果"); 

        // 创建表头样式
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setBorderTop(BorderStyle.THIN); 
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = {"院校名称", "专业名称", "年份", "计划数", "等效差", "最低分", "平均分", "最高分", "批次", "学费"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 设置列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.setColumnWidth(i, 256 * 15); // 15个字符宽
        }

        // 填充数据
        int rowNum = 1;
        for (JHBean bean : jhDataList) {
            // 每个 Bean 占用 4 行
            for (int i = 0; i < 3; i++) {
                Row row = sheet.createRow(rowNum + i);

                // 院校名称和专业名称跨行
                if (i == 0) {
                    Cell cell = row.createCell(0);
                    cell.setCellValue(bean.getYxmc());
                    sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum + 3, 0, 0)); // 合并院校名称

                    cell = row.createCell(1);
                    cell.setCellValue(bean.getZymc_org());
                    sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum + 3, 1, 1)); // 合并专业名称
                }

                // 年份
                Cell cell = row.createCell(2);
                cell.setCellValue(LATEST_JH_YEAR - i);

                // 计划数
                cell = row.createCell(3);
                switch (i) {
                    case 0:
                        cell.setCellValue(Tools.view(bean.getJhs_a()));
                        break;
                    case 1:
                        cell.setCellValue(Tools.view(bean.getJhs_b()));
                        break;
                    case 2:
                        cell.setCellValue(Tools.view(bean.getJhs_c()));
                        break;
                    default:
                        cell.setCellValue("-");
                }

                ZDKSRank rankYearA = TONGWF_MAP.get(String.valueOf(LATEST_JH_YEAR - 1)); 
                ZDKSRank rankYearB = TONGWF_MAP.get(String.valueOf(LATEST_JH_YEAR - 2));
                ZDKSRank rankYearC = TONGWF_MAP.get(String.valueOf(LATEST_JH_YEAR - 3));
                
                // 等效差
                cell = row.createCell(4);
                
                switch (i) {
                    case 0:
                        cell.setCellValue((rankYearA == null ? "-" : Tools.view(String.valueOf(Math.abs(rankYearA.getScore() - Tools.getInt(bean.getZdf_a()))))) + "(" + (rankYearA == null ? "-" : Tools.view(String.valueOf(Math.abs(Tools.getInt(bean.getZdfwc_a()) - rankYearA.getWc())))) + ")");
                        break;
                    case 1:
                    	cell.setCellValue((rankYearB == null ? "-" : Tools.view(String.valueOf(Math.abs(rankYearB.getScore() - Tools.getInt(bean.getZdf_b()))))) + "(" + (rankYearB == null ? "-" : Tools.view(String.valueOf(Math.abs(Tools.getInt(bean.getZdfwc_b()) - rankYearB.getWc())))) + ")");
                        break;
                    case 2:
                    	cell.setCellValue((rankYearC == null ? "-" : Tools.view(String.valueOf(Math.abs(rankYearC.getScore() - Tools.getInt(bean.getZdf_c()))))) + "(" + (rankYearC == null ? "-" : Tools.view(String.valueOf(Math.abs(Tools.getInt(bean.getZdfwc_c()) - rankYearC.getWc())))) + ")");
                        break;
                    default:
                        cell.setCellValue("-");
                }

                // 最低分
                cell = row.createCell(5);
                switch (i) {
                    case 0:
                        cell.setCellValue(Tools.view(bean.getZdf_a()) + "("+ Tools.view(bean.getZdfwc_a()) +")");
                        break;
                    case 1:
                    	cell.setCellValue(Tools.view(bean.getZdf_b()) + "("+ Tools.view(bean.getZdfwc_b()) +")");
                        break;
                    case 2:
                    	cell.setCellValue(Tools.view(bean.getZdf_c()) + "("+ Tools.view(bean.getZdfwc_c()) +")");
                        break;
                    default:
                        cell.setCellValue("-");
                }

                // 平均分
                cell = row.createCell(6);
                switch (i) {
                    case 0:
                        cell.setCellValue(Tools.view(bean.getPjf_a()) + "("+ Tools.view(bean.getPjfwc_a()) +")");
                        break;
                    case 1:
                        cell.setCellValue(Tools.view(bean.getPjf_b()) + "("+ Tools.view(bean.getPjfwc_b()) +")");
                        break;
                    case 2:
                        cell.setCellValue(Tools.view(bean.getPjf_c()) + "("+ Tools.view(bean.getPjfwc_c()) +")");
                        break;
                    default:
                        cell.setCellValue("-");
                }

                // 最高分
                cell = row.createCell(7);
                switch (i) {
                    case 0:
                        cell.setCellValue(Tools.view(bean.getZgf_a())+ "("+ Tools.view(bean.getZgfwc_a()) +")");
                        break;
                    case 1:
                        cell.setCellValue(Tools.view(bean.getZgf_b())+ "("+ Tools.view(bean.getZgfwc_b()) +")");
                        break;
                    case 2:
                        cell.setCellValue(Tools.view(bean.getZgf_c())+ "("+ Tools.view(bean.getZgfwc_c()) +")");
                        break;
                    default:
                        cell.setCellValue("-");
                }

                // 批次
                if (i == 0) {
                    cell = row.createCell(8);
                    cell.setCellValue(bean.getPc());
                    sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum + 3, 8, 8)); // 合并批次
                }

                // 学费
                if (i == 0) {
                    cell = row.createCell(9);
                    cell.setCellValue(bean.getFee());
                    sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum + 3, 9, 9)); // 合并学费
                }
            }

            // 跳过 4 行
            rowNum += 4;
        }

        // 写入文件
        try (FileOutputStream fileOut = new FileOutputStream(filePath)) {
            workbook.write(fileOut);
        }

        // 关闭工作簿
        workbook.close();
    }
}