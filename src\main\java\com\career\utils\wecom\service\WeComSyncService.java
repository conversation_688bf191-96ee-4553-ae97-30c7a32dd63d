package com.career.utils.wecom.service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.career.utils.Tools;
import com.career.utils.wecom.WeComTokenManager;
import com.career.utils.wecom.config.WeComConfig;
import com.career.utils.wecom.db.WeComJDBC;
import com.career.utils.wecom.model.*;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.text.SimpleDateFormat;
import java.util.Objects;

/**
 * 企业微信数据同步服务
 * 
 * 采用标准化的同步顺序：
 * 1. 内部联系人 (基础数据)
 * 2. 外部联系人 (基础数据) 
 * 3. 客户群 (容器数据)
 * 4. 群成员关系 (关系数据)
 * 5. 跟进人关系 (关系数据)
 * 
 * 特性：
 * - 支持增量同步，基于数据库时间戳字段
 * - 完整的异常处理和容错机制
 * - 详细的进度监控和状态统计
 * - 事务分阶段提交，支持断点续传
 * 
 * 使用示例：
 * WeComSyncService.SyncResult result = WeComSyncService.performFullSync(corpId, corpSecret, saasId);
 * WeComSyncService.SyncResult result = WeComSyncService.performIncrementalSync(corpId, corpSecret, saasId);
 */
public class WeComSyncService {
    
    private static final WeComJDBC weComJDBC = new WeComJDBC();
    
    // 同步进度监控
    private static final Map<String, SyncProgress> activeSyncSessions = new ConcurrentHashMap<>();
    
    /**
     * 执行完整同步
     * 
     * @param corpId 企业ID
     * @param corpSecret 应用密钥
     * @param saasId SaaS ID
     * @return 同步结果
     */
    public static SyncResult performFullSync(String corpId, String corpSecret, String saasId) {
        return performSync(corpId, corpSecret, saasId, false);
    }
    
    /**
     * 执行增量同步
     * 
     * @param corpId 企业ID  
     * @param corpSecret 应用密钥
     * @param saasId SaaS ID
     * @return 同步结果
     */
    public static SyncResult performIncrementalSync(String corpId, String corpSecret, String saasId) {
        return performSync(corpId, corpSecret, saasId, true);
    }
    
    /**
     * 核心同步方法
     * 
     * @param corpId 企业ID
     * @param corpSecret 应用密钥  
     * @param saasId SaaS ID
     * @param isIncremental 是否增量同步
     * @return 同步结果
     */
    private static SyncResult performSync(String corpId, String corpSecret, String saasId, boolean isIncremental) {
        // 验证配置
        String configValidation = WeComConfig.validateConfig();
        if (configValidation != null) {
            SyncResult errorResult = new SyncResult();
            errorResult.setSuccess(false);
            errorResult.setErrorMessage("配置验证失败: " + configValidation);
            return errorResult;
        }
        
        String sessionId = generateSessionId(corpId, saasId);
        SyncResult result = new SyncResult();
        SyncProgress progress = new SyncProgress();
        int totalErrorCount = 0; // 错误计数器
        
        try {
            // 检查并发会话数限制
            if (activeSyncSessions.size() >= WeComConfig.getMaxConcurrentSyncSessions()) {
                SyncResult errorResult = new SyncResult();
                errorResult.setSuccess(false);
                errorResult.setErrorMessage("超过最大并发同步会话数限制: " + WeComConfig.getMaxConcurrentSyncSessions());
                return errorResult;
            }
            
            // 注册同步会话
            activeSyncSessions.put(sessionId, progress);
            result.setSessionId(sessionId);
            
            // 智能同步模式：根据配置自动选择同步模式
            if (WeComConfig.isEnableSmartSyncMode() && !isIncremental) {
                // 这里可以实现智能判断逻辑，比如检查上次同步时间、数据变化量等
                // 简化实现：如果配置了智能模式且默认是增量同步，则优先使用增量
                if (WeComConfig.isDefaultSyncModeIncremental()) {
                    isIncremental = true;
                    if (WeComConfig.isEnableDetailedPhaseLogging()) {
                        Tools.println("智能同步模式：自动选择增量同步");
                    }
                }
            }
            
            result.setSyncMode(isIncremental ? "INCREMENTAL" : "FULL");
            result.setSyncStartTime(new Date());
            
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("=== 开始企业微信数据同步 [" + result.getSyncMode() + "] ===");
                Tools.println("企业ID: " + corpId + ", SaaS ID: " + saasId + ", 会话ID: " + sessionId);
                Tools.println("配置信息: API间隔=" + WeComConfig.getApiCallIntervalMs() + "ms, " +
                    "批处理大小=" + WeComConfig.getBatchSize() + ", " + 
                    "进度监控=" + (WeComConfig.isEnableProgressMonitoring() ? "启用" : "禁用") + ", " +
                    "最大容忍错误=" + WeComConfig.getMaxToleratedErrors());
            }
            
            // 阶段1: 同步内部联系人 (基础数据)
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("\n开始阶段1: 同步内部联系人");
            }
            progress.setCurrentPhase("INTERNAL_CONTACTS");
            progress.setPhaseProgress(0);
            try {
                syncInternalContacts(corpId, corpSecret, saasId, isIncremental, result, progress);
            } catch (Exception e) {
                totalErrorCount++;
                result.addError("INTERNAL_CONTACTS", e.getMessage());
                if (!WeComConfig.isContinueOnError() || totalErrorCount > WeComConfig.getMaxToleratedErrors()) {
                    throw e;
                }
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("阶段1出现错误但继续执行: " + e.getMessage());
                }
            }
            
            // API调用间隔
            if (WeComConfig.getApiCallIntervalMs() > 0) {
                try {
                    Thread.sleep(WeComConfig.getApiCallIntervalMs());
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
            
            // 阶段2: 同步外部联系人 (基础数据)
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("\n开始阶段2: 同步外部联系人");
            }
            progress.setCurrentPhase("EXTERNAL_CONTACTS"); 
            progress.setPhaseProgress(0);
            try {
                syncExternalContacts(corpId, corpSecret, saasId, isIncremental, result, progress);
            } catch (Exception e) {
                totalErrorCount++;
                result.addError("EXTERNAL_CONTACTS", e.getMessage());
                if (!WeComConfig.isContinueOnError() || totalErrorCount > WeComConfig.getMaxToleratedErrors()) {
                    throw e;
                }
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("阶段2出现错误但继续执行: " + e.getMessage());
                }
            }
            
            // API调用间隔
            if (WeComConfig.getApiCallIntervalMs() > 0) {
                try {
                    Thread.sleep(WeComConfig.getApiCallIntervalMs());
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
            
            // 阶段3: 同步客户群 (容器数据)
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("\n开始阶段3: 同步客户群");
            }
            progress.setCurrentPhase("GROUPS");
            progress.setPhaseProgress(0);
            try {
                syncGroups(corpId, corpSecret, saasId, isIncremental, result, progress);
            } catch (Exception e) {
                totalErrorCount++;
                result.addError("GROUPS", e.getMessage());
                if (!WeComConfig.isContinueOnError() || totalErrorCount > WeComConfig.getMaxToleratedErrors()) {
                    throw e;
                }
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("阶段3出现错误但继续执行: " + e.getMessage());
                }
            }
            
            // API调用间隔
            if (WeComConfig.getApiCallIntervalMs() > 0) {
                try {
                    Thread.sleep(WeComConfig.getApiCallIntervalMs());
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
            
            // 阶段4: 同步群成员关系 (关系数据)
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("\n开始阶段4: 同步群成员关系");
            }
            progress.setCurrentPhase("GROUP_MEMBERS");
            progress.setPhaseProgress(0);
            try {
                syncGroupMembers(corpId, corpSecret, saasId, isIncremental, result, progress);
            } catch (Exception e) {
                totalErrorCount++;
                result.addError("GROUP_MEMBERS", e.getMessage());
                if (!WeComConfig.isContinueOnError() || totalErrorCount > WeComConfig.getMaxToleratedErrors()) {
                    throw e;
                }
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("阶段4出现错误但继续执行: " + e.getMessage());
                }
            }
            
            // API调用间隔
            if (WeComConfig.getApiCallIntervalMs() > 0) {
                try {
                    Thread.sleep(WeComConfig.getApiCallIntervalMs());
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
            
            // 阶段5: 同步跟进人关系 (关系数据)
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("\n开始阶段5: 同步跟进人关系");
            }
            progress.setCurrentPhase("FOLLOWERS");
            progress.setPhaseProgress(0);
            try {
                syncFollowers(corpId, corpSecret, saasId, isIncremental, result, progress);
            } catch (Exception e) {
                totalErrorCount++;
                result.addError("FOLLOWERS", e.getMessage());
                if (!WeComConfig.isContinueOnError() || totalErrorCount > WeComConfig.getMaxToleratedErrors()) {
                    throw e;
                }
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("阶段5出现错误但继续执行: " + e.getMessage());
                }
            }
            
            // 同步后验证（如果启用）
            if (WeComConfig.isEnablePostSyncValidation()) {
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("\n开始同步后数据验证");
                }
                progress.setCurrentPhase("VALIDATION");
                progress.setPhaseProgress(0);
                try {
                    performPostSyncValidation(corpId, saasId, result, progress);
                } catch (Exception e) {
                    totalErrorCount++;
                    result.addError("VALIDATION", e.getMessage());
                    if (WeComConfig.isEnableDetailedPhaseLogging()) {
                        Tools.println("数据验证出现错误: " + e.getMessage());
                    }
                }
            }
            
            // 完成同步
            progress.setCurrentPhase("COMPLETED");
            progress.setPhaseProgress(100);
            result.setSyncEndTime(new Date());
            result.setSuccess(totalErrorCount <= WeComConfig.getMaxToleratedErrors());
            
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("=== 同步完成 ===");
                Tools.println("总错误数: " + totalErrorCount + "/" + WeComConfig.getMaxToleratedErrors());
            }
            
            if (WeComConfig.isGenerateDetailedReport()) {
                Tools.println(result.getSummary());
            }
            
        } catch (Exception e) {
            result.setSyncEndTime(new Date());
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            progress.setCurrentPhase("ERROR");
            Tools.println("同步过程发生异常: " + e.getMessage());
            if (WeComConfig.isEnableVerboseLogging()) {
                e.printStackTrace();
            }
        } finally {
            // 清理同步会话
            if (WeComConfig.isAutoCleanupExpiredSessions()) {
                activeSyncSessions.remove(sessionId);
            }
        }
        
        return result;
    }
    
    /**
     * 阶段1: 同步内部联系人
     */
    private static void syncInternalContacts(String corpId, String corpSecret, String saasId, 
            boolean isIncremental, SyncResult result, SyncProgress progress) {
        try {
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("\n--- 阶段1: 同步内部联系人 ---");
            }
            
            if (WeComConfig.isEnableProgressMonitoring()) {
                progress.setPhaseProgress(10);
            }
            
            // 优化：增量同步时先获取已有用户ID集合
            Set<String> existingUserIds = null;
            if (isIncremental) {
                List<WeComInternalContact> existingContacts = weComJDBC.getAllWeComInternalContacts(saasId);
                existingUserIds = new HashSet<>();
                for (WeComInternalContact contact : existingContacts) {
                    existingUserIds.add(contact.getUserId());
                }
                
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("增量同步模式：当前数据库中有 " + existingUserIds.size() + " 个内部联系人");
                }
                
                // 增量同步智能跳过：检查时间阈值
                if (WeComConfig.getIncrementalSyncSkipDays() > 0) {
                    if (WeComConfig.isEnableDetailedPhaseLogging()) {
                        Tools.println("增量同步模式：开始内部联系人智能跳过分析，时间阈值=" + WeComConfig.getIncrementalSyncSkipDays() + "天");
                    }
                    
                    // 计算时间阈值
                    long skipThresholdMs = WeComConfig.getIncrementalSyncSkipDays() * 24 * 60 * 60 * 1000L;
                    long currentTime = System.currentTimeMillis();
                    
                    // 检查是否所有联系人都在时间阈值内
                    boolean allWithinThreshold = true;
                    int outdatedCount = 0;
                    
                    for (WeComInternalContact contact : existingContacts) {
                        if (contact.getSyncTime() == null || 
                            (currentTime - contact.getSyncTime().getTime()) > skipThresholdMs) {
                            allWithinThreshold = false;
                            outdatedCount++;
                        }
                    }
                    
                    if (allWithinThreshold && !existingContacts.isEmpty()) {
                        if (WeComConfig.isEnableDetailedPhaseLogging()) {
                            Tools.println("增量同步：所有内部联系人都在时间阈值内，跳过API调用");
                            Tools.println("  - 智能跳过: " + existingContacts.size() + " 个");
                            Tools.println("  - API调用节约: 1 次");
                        }
                        return;
                    } else if (WeComConfig.isEnableDetailedPhaseLogging()) {
                        Tools.println("增量同步分析结果:");
                        Tools.println("  - 需要检查更新: " + outdatedCount + " 个超过阈值");
                        Tools.println("  - 继续执行API调用以获取最新数据");
                    }
                }
            }
            
            if (WeComConfig.isEnableProgressMonitoring()) {
                progress.setPhaseProgress(20);
            }
            
            // 使用优化版方法获取API数据
            List<WeComInternalContact> apiContacts = WeComInternalContactService.getAllInternalContacts(
                corpId, corpSecret, 1L, isIncremental, existingUserIds);
            
            if (apiContacts == null || apiContacts.isEmpty()) {
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    if (isIncremental) {
                        Tools.println("增量同步：未发现新增的内部联系人");
                    } else {
                        Tools.println("未获取到内部联系人数据");
                    }
                }
                return;
            }
            
            if (WeComConfig.isEnableProgressMonitoring()) {
                progress.setPhaseProgress(50);
            }
            
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                if (isIncremental) {
                    Tools.println("增量同步：从API获取到 " + apiContacts.size() + " 个新增内部联系人");
                } else {
                    Tools.println("全量同步：从API获取到 " + apiContacts.size() + " 个内部联系人");
                }
            }
            
            // 设置saasId和同步时间
            Date syncTime = new Date();
            for (WeComInternalContact contact : apiContacts) {
                contact.setWecom_saas_id(saasId);
                contact.setSyncTime(syncTime);
            }
            
            if (WeComConfig.isEnableProgressMonitoring()) {
                progress.setPhaseProgress(70);
            }
            
            if (isIncremental) {
                performIncrementalInternalContactSync(apiContacts, saasId, result);
            } else {
                performFullInternalContactSync(apiContacts, result);
            }
            
            if (WeComConfig.isEnableProgressMonitoring()) {
                progress.setPhaseProgress(100);
            }
            
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("内部联系人同步完成: 新增=" + result.getNewInternalContactCount() + 
                        ", 更新=" + result.getUpdatedInternalContactCount() + 
                        ", 删除=" + result.getDeletedInternalContactCount());
            }
                    
        } catch (Exception e) {
            result.addError("INTERNAL_CONTACTS", e.getMessage());
            Tools.println("内部联系人同步失败: " + e.getMessage());
            if (WeComConfig.isLog40096Details()) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 阶段2: 同步外部联系人
     */
    private static void syncExternalContacts(String corpId, String corpSecret, String saasId,
            boolean isIncremental, SyncResult result, SyncProgress progress) {
        try {
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("\n--- 阶段2: 同步外部联系人 ---");
            }
            
            if (WeComConfig.isEnableProgressMonitoring()) {
                progress.setPhaseProgress(10);
            }
            
            // 获取外部联系人ID列表
            List<String> externalUserIds = WeComExternalContactService.getExternalContactList(corpId, corpSecret);
            
            if (externalUserIds == null || externalUserIds.isEmpty()) {
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("未获取到外部联系人ID列表");
                }
                return;
            }

            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("从API获取到 " + externalUserIds.size() + " 个外部联系人ID");
            }
            
            // 设置外部联系人总数
            if (!isIncremental) {
                // 全量同步模式下，设置总数
                result.totalExternalContactCount = externalUserIds.size();
            }
            
            // 增量同步优化：先检查数据库中已有的数据
            if (isIncremental && WeComConfig.getIncrementalSyncSkipDays() > 0) {
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("增量同步模式：开始智能跳过分析，时间阈值=" + WeComConfig.getIncrementalSyncSkipDays() + "天");
                }
                
                // 获取数据库中的外部联系人映射
                Map<String, WeComCustomer> dbContactMap = weComJDBC.getWeComExternalContactsByIds(externalUserIds);
                
                // 计算时间阈值
                long skipThresholdMs = WeComConfig.getIncrementalSyncSkipDays() * 24 * 60 * 60 * 1000L;
                long currentTime = System.currentTimeMillis();
                
                List<String> needUpdateIds = new ArrayList<>();
                List<String> skippedIds = new ArrayList<>();
                
                for (String externalUserId : externalUserIds) {
                    WeComCustomer dbContact = dbContactMap.get(externalUserId);
                    
                    if (dbContact == null) {
                        // 数据库中不存在，需要新增
                        needUpdateIds.add(externalUserId);
                    } else if (dbContact.getExternalSyncTime() == null) {
                        // 没有同步时间记录，需要更新
                        needUpdateIds.add(externalUserId);
                    } else {
                        // 检查同步时间是否在阈值内
                        long timeSinceLastSync = currentTime - dbContact.getExternalSyncTime().getTime();
                        if (timeSinceLastSync > skipThresholdMs) {
                            // 超过阈值，需要重新同步
                            needUpdateIds.add(externalUserId);
                        } else {
                            // 在阈值内，跳过
                            skippedIds.add(externalUserId);
                        }
                    }
                }
                
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("增量同步分析结果:");
                    Tools.println("  - 需要更新: " + needUpdateIds.size() + " 个");
                    Tools.println("  - 智能跳过: " + skippedIds.size() + " 个");
                    Tools.println("  - API调用节约: " + skippedIds.size() + " 次");
                }
                
                // 只处理需要更新的ID
                if (!needUpdateIds.isEmpty()) {
                    processExternalContactChunk(corpId, corpSecret, saasId, needUpdateIds, isIncremental, result, progress);
                } else {
                    if (WeComConfig.isEnableDetailedPhaseLogging()) {
                        Tools.println("增量同步：所有外部联系人都在时间阈值内，无需API调用");
                    }
                }
                
            } else {
                // 传统处理方式：处理所有数据
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    if (!isIncremental) {
                        Tools.println("全量同步模式：处理所有外部联系人");
                    } else {
                        Tools.println("增量同步模式：时间阈值优化已禁用，处理所有外部联系人");
                    }
                }
                
                // 检查数据量是否超过限制
                if (isDataVolumeExceedsLimit(externalUserIds.size())) {
                    if (WeComConfig.isEnableDetailedPhaseLogging()) {
                        Tools.println("外部联系人数量(" + externalUserIds.size() + ")超过单批限制(" + 
                            WeComConfig.getMaxSyncItemsPerBatch() + ")，将分批处理");
                    }
                    
                    // 分批处理
                    List<List<String>> chunks = chunkData(externalUserIds, WeComConfig.getMaxSyncItemsPerBatch());
                    for (int i = 0; i < chunks.size(); i++) {
                        List<String> chunk = chunks.get(i);
                        if (WeComConfig.isEnableDetailedPhaseLogging()) {
                            Tools.println("处理第" + (i + 1) + "/" + chunks.size() + "批，数量: " + chunk.size());
                        }
                        processExternalContactChunk(corpId, corpSecret, saasId, chunk, isIncremental, result, progress);
                        
                        // 批次间隔
                        if (i < chunks.size() - 1 && WeComConfig.getBatchIntervalMs() > 0) {
                            try {
                                Thread.sleep(WeComConfig.getBatchIntervalMs());
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                            }
                        }
                    }
                } else {
                    // 正常处理
                    processExternalContactChunk(corpId, corpSecret, saasId, externalUserIds, isIncremental, result, progress);
                }
            }
            
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("外部联系人同步完成: 新增=" + result.getNewExternalContactCount() + 
                        ", 更新=" + result.getUpdatedExternalContactCount() + 
                        ", 删除=" + result.getDeletedExternalContactCount());
            }
                    
        } catch (Exception e) {
            result.addError("EXTERNAL_CONTACTS", e.getMessage());
            Tools.println("外部联系人同步失败: " + e.getMessage());
            if (WeComConfig.isLog40096Details()) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 处理外部联系人数据块
     */
    private static void processExternalContactChunk(String corpId, String corpSecret, String saasId,
            List<String> externalUserIds, boolean isIncremental, SyncResult result, SyncProgress progress) {
        
        if (WeComConfig.isEnableProgressMonitoring()) {
            progress.setPhaseProgress(30);
        }
        
        if (WeComConfig.isEnableDetailedPhaseLogging()) {
            Tools.println("从API获取到 " + externalUserIds.size() + " 个外部联系人ID");
        }
        
        // 批量获取详细信息
        List<WeComCustomer> apiContacts = new ArrayList<>();
        Date syncTime = new Date();
        int processedCount = 0;
        int successCount = 0;
        int error40096Count = 0;
        int otherErrorCount = 0;
        int batchSize = Math.min(200, WeComConfig.getBatchSize()); // 小批次处理
        
        String accessToken = WeComTokenManager.getAccessToken(corpId, corpSecret);
        
        // QPS控制计算
        long qpsInterval = 1000 / WeComConfig.getApiQpsLimit(); // 每次API调用的最小间隔
        long lastApiCall = 0;
        
        for (int i = 0; i < externalUserIds.size(); i++) {
            String externalUserId = externalUserIds.get(i);
            try {
                // QPS限流控制
                long currentTime = System.currentTimeMillis();
                long timeSinceLastCall = currentTime - lastApiCall;
                if (timeSinceLastCall < qpsInterval) {
                    Thread.sleep(qpsInterval - timeSinceLastCall);
                }
                
                // 每处理100个用户检查一次token
                if (i > 0 && i % 100 == 0) {
                    // 检查token是否即将过期，提前刷新
                    if (WeComTokenManager.isTokenNearExpiry(corpId)) {
                        accessToken = WeComTokenManager.refreshAccessToken(corpId, corpSecret);
                        if (WeComConfig.isEnableDetailedPhaseLogging()) {
                            Tools.println("已刷新access_token");
                        }
                    }
                }
                
                WeComCustomer customer = WeComExternalContactService.getCustomerDetailWithEnhancedErrorHandling(
                        accessToken, externalUserId, saasId);
                if (customer != null) {
                    customer.setExternalSyncTime(syncTime);
                    apiContacts.add(customer);
                    successCount++;
                    
                    // 达到小批次阈值时，立即提交一批数据
                    if (apiContacts.size() >= batchSize) {
                        if (isIncremental) {
                            performIncrementalExternalContactSync(apiContacts, saasId, result);
                        } else {
                            // 对于全量同步，使用增量方式添加，而不是先删除所有再添加
                            performIncrementalExternalContactSync(apiContacts, saasId, result);
                        }
                        // 清空已处理列表，准备处理下一批
                        apiContacts.clear();
                        
                        // 记录日志
                        if (WeComConfig.isEnableDetailedPhaseLogging()) {
                            Tools.println("已处理并保存一批外部联系人数据: " + batchSize + " 条");
                        }
                    }
                }
                
                lastApiCall = System.currentTimeMillis();
                
            } catch (Exception e) {
                // 处理token过期错误
                if (e.getMessage() != null && e.getMessage().contains("42001")) {
                    // 遇到token过期错误，立即刷新token
                    Tools.println("检测到access_token已过期，正在刷新...");
                    accessToken = WeComTokenManager.refreshAccessToken(corpId, corpSecret);
                    // 重试当前用户，而不是跳过
                    i--; // 退回一个索引，以便重试当前用户
                    continue;
                }
            
                if (e.getMessage() != null && e.getMessage().contains("40096")) {
                    error40096Count++;
                    if (WeComConfig.isLog40096Details()) {
                        Tools.println("40096错误 - 外部联系人ID: " + externalUserId);
                    }
                } else {
                    otherErrorCount++;
                    if (WeComConfig.isEnableVerboseLogging()) {
                        Tools.println("获取外部联系人详情失败: " + externalUserId + " - " + e.getMessage());
                    }
                }
            }
            
            processedCount++;
            if (WeComConfig.isEnableProgressMonitoring() && 
                processedCount % WeComConfig.getProgressUpdateFrequency() == 0) {
                int currentProgress = 30 + (processedCount * 40 / externalUserIds.size());
                progress.setPhaseProgress(currentProgress);
                
                if (WeComConfig.isEnableConsoleProgress()) {
                    Tools.println("外部联系人处理进度: " + processedCount + "/" + externalUserIds.size() + 
                        " (成功:" + successCount + ", 40096错误:" + error40096Count + ", 其他错误:" + otherErrorCount + ")");
                }
            }
        }
        
        // 处理剩余的联系人数据
        if (!apiContacts.isEmpty()) {
            if (isIncremental) {
                performIncrementalExternalContactSync(apiContacts, saasId, result);
            } else {
                performIncrementalExternalContactSync(apiContacts, saasId, result);
            }
            
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("已处理并保存最后一批外部联系人数据: " + apiContacts.size() + " 条");
            }
        }
        
        if (WeComConfig.isEnableProgressMonitoring()) {
            progress.setPhaseProgress(70);
        }
        
        if (WeComConfig.isEnableDetailedPhaseLogging()) {
            Tools.println("外部联系人详情获取完成 - 成功: " + successCount + 
                    ", 40096错误: " + error40096Count + ", 其他错误: " + otherErrorCount);
        }
        
        if (WeComConfig.isEnableProgressMonitoring()) {
            progress.setPhaseProgress(100);
        }
    }
    
    /**
     * 阶段3: 同步客户群
     */
    private static void syncGroups(String corpId, String corpSecret, String saasId,
            boolean isIncremental, SyncResult result, SyncProgress progress) {
        try {
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("\n--- 阶段3: 同步客户群 ---");
            }
            
            if (WeComConfig.isEnableProgressMonitoring()) {
                progress.setPhaseProgress(10);
            }
            
            // 获取群列表
            List<JSONObject> groupChatIdList = WeComGroupChatService.getGroupChatList(corpId, corpSecret, null, 0);
            
            if (groupChatIdList == null || groupChatIdList.isEmpty()) {
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("未获取到客户群列表");
                }
                return;
            }
            
            if (WeComConfig.isEnableProgressMonitoring()) {
                progress.setPhaseProgress(30);
            }
            
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("从API获取到 " + groupChatIdList.size() + " 个客户群");
            }
            
            // 增量同步优化：先检查数据库中已有的群数据
            if (isIncremental && WeComConfig.getIncrementalSyncSkipDays() > 0) {
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("增量同步模式：开始客户群智能跳过分析，时间阈值=" + WeComConfig.getIncrementalSyncSkipDays() + "天");
                }
                
                // 提取群ID列表
                List<String> chatIds = new ArrayList<>();
                for (JSONObject groupInfo : groupChatIdList) {
                    chatIds.add(groupInfo.getString("chat_id"));
                }
                
                // 获取数据库中的群数据
                Map<String, GroupChat> dbGroupMap = weComJDBC.getWeComGroupsByChatIds(chatIds);
                
                // 计算时间阈值
                long skipThresholdMs = WeComConfig.getIncrementalSyncSkipDays() * 24 * 60 * 60 * 1000L;
                long currentTime = System.currentTimeMillis();
                
                List<JSONObject> needUpdateGroups = new ArrayList<>();
                List<String> skippedChatIds = new ArrayList<>();
                
                for (JSONObject groupInfo : groupChatIdList) {
                    String chatId = groupInfo.getString("chat_id");
                    GroupChat dbGroup = dbGroupMap.get(chatId);
                    
                    if (dbGroup == null) {
                        // 数据库中不存在，需要新增
                        needUpdateGroups.add(groupInfo);
                    } else {
                        // 检查memberVersion是否相同（优先使用版本号对比）
                        if (groupInfo.containsKey("member_version") && dbGroup.getMemberVersion() != null) {
                            Long apiMemberVersion = groupInfo.getLong("member_version");
                            if (!Objects.equals(apiMemberVersion, dbGroup.getMemberVersion())) {
                                // 版本号不同，需要更新
                                needUpdateGroups.add(groupInfo);
                                if (WeComConfig.isEnableVerboseLogging()) {
                                    Tools.println("客户群 " + chatId + " 版本号变化: " + dbGroup.getMemberVersion() + " -> " + apiMemberVersion);
                                }
                            } else {
                                // 版本号相同，跳过
                                skippedChatIds.add(chatId);
                                if (WeComConfig.isEnableVerboseLogging()) {
                                    Tools.println("客户群 " + chatId + " 版本号未变化，跳过同步");
                                }
                            }
                        } else if (dbGroup.getGroupSyncTime() == null) {
                            // 没有同步时间记录，需要更新
                            needUpdateGroups.add(groupInfo);
                        } else {
                            // 检查同步时间是否在阈值内
                            long timeSinceLastSync = currentTime - dbGroup.getGroupSyncTime().getTime();
                            if (timeSinceLastSync > skipThresholdMs) {
                                // 超过阈值，需要重新同步
                                needUpdateGroups.add(groupInfo);
                            } else {
                                // 在阈值内，跳过
                                skippedChatIds.add(chatId);
                            }
                        }
                    }
                }
                
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("客户群增量同步分析结果:");
                    Tools.println("  - 需要更新: " + needUpdateGroups.size() + " 个");
                    Tools.println("  - 智能跳过: " + skippedChatIds.size() + " 个 (基于版本号或时间)");
                    Tools.println("  - API调用节约: " + skippedChatIds.size() + " 次");
                }
                
                // 只处理需要更新的群
                if (!needUpdateGroups.isEmpty()) {
                    processGroupChunk(corpId, corpSecret, saasId, needUpdateGroups, isIncremental, result, progress);
                } else {
                    if (WeComConfig.isEnableDetailedPhaseLogging()) {
                        Tools.println("增量同步：所有客户群都无需更新，无需API调用");
                    }
                }
                
            } else {
                // 传统处理方式：处理所有群
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    if (!isIncremental) {
                        Tools.println("全量同步模式：处理所有客户群");
                    } else {
                        Tools.println("增量同步模式：时间阈值优化已禁用，处理所有客户群");
                    }
                }
                
                processGroupChunk(corpId, corpSecret, saasId, groupChatIdList, isIncremental, result, progress);
            }
            
            if (WeComConfig.isEnableProgressMonitoring()) {
                progress.setPhaseProgress(100);
            }
            
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("客户群同步完成: 新增=" + result.getNewGroupCount() + 
                        ", 更新=" + result.getUpdatedGroupCount() + 
                        ", 删除=" + result.getDeletedGroupCount());
            }
                    
        } catch (Exception e) {
            result.addError("GROUPS", e.getMessage());
            Tools.println("客户群同步失败: " + e.getMessage());
            if (WeComConfig.isLog40096Details()) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 处理群组数据块，支持分批处理
     */
    private static void processGroupChunk(String corpId, String corpSecret, String saasId,
            List<JSONObject> groupChatIdList, boolean isIncremental, SyncResult result, SyncProgress progress) {
        
        if (groupChatIdList == null || groupChatIdList.isEmpty()) {
            return;
        }
        
        if (WeComConfig.isEnableDetailedPhaseLogging()) {
            Tools.println("处理群组数据，群组数量: " + groupChatIdList.size());
        }
        
        // 检查是否需要分批处理
        if (isDataVolumeExceedsLimit(groupChatIdList.size())) {
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("群组数量(" + groupChatIdList.size() + ")超过单批限制(" + 
                    WeComConfig.getMaxSyncItemsPerBatch() + ")，将分批处理");
            }
            
            // 分批处理
            List<List<JSONObject>> chunks = chunkData(groupChatIdList, WeComConfig.getMaxSyncItemsPerBatch());
            for (int i = 0; i < chunks.size(); i++) {
                List<JSONObject> chunk = chunks.get(i);
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("处理群组第" + (i + 1) + "/" + chunks.size() + "批，数量: " + chunk.size());
                }
                processGroupBatch(corpId, corpSecret, saasId, chunk, isIncremental, result, progress);
                
                // 批次间隔
                if (i < chunks.size() - 1 && WeComConfig.getBatchIntervalMs() > 0) {
                    try {
                        Thread.sleep(WeComConfig.getBatchIntervalMs());
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        } else {
            // 正常处理
            processGroupBatch(corpId, corpSecret, saasId, groupChatIdList, isIncremental, result, progress);
        }
    }
    
    /**
     * 处理一批群组数据
     */
    private static void processGroupBatch(String corpId, String corpSecret, String saasId,
            List<JSONObject> groupChatIdList, boolean isIncremental, SyncResult result, SyncProgress progress) {
        
        // 获取群详情
        List<GroupChat> apiGroups = new ArrayList<>();
        int processedCount = 0;
        
        for (JSONObject groupInfo : groupChatIdList) {
            String chatId = groupInfo.getString("chat_id");
            try {
                GroupChat groupDetail = WeComGroupChatService.getGroupChatDetail(corpId, corpSecret, chatId);
                if (groupDetail != null) {
                    groupDetail.setSaasId(saasId);
                    groupDetail.setGroupSyncTime(new Date()); // 设置同步时间
                    apiGroups.add(groupDetail);
                }
            } catch (Exception e) {
                result.addError("GROUP_" + chatId, "获取群详情失败: " + e.getMessage());
                if (WeComConfig.isEnableVerboseLogging()) {
                    Tools.println("获取群详情失败: " + chatId + " - " + e.getMessage());
                }
            }
            
            processedCount++;
            if (WeComConfig.isEnableProgressMonitoring() && 
                processedCount % WeComConfig.getProgressUpdateFrequency() == 0) {
                int currentProgress = 30 + (processedCount * 40 / groupChatIdList.size());
                progress.setPhaseProgress(currentProgress);
                
                if (WeComConfig.isEnableConsoleProgress()) {
                    Tools.println("客户群处理进度: " + processedCount + "/" + groupChatIdList.size());
                }
            }
        }
        
        if (!apiGroups.isEmpty()) {
            // 使用小批次插入数据库
            int batchSize = Math.min(50, WeComConfig.getBatchSize());
            int totalGroups = apiGroups.size();
            
            for (int i = 0; i < totalGroups; i += batchSize) {
                int endIndex = Math.min(i + batchSize, totalGroups);
                List<GroupChat> batchGroups = apiGroups.subList(i, endIndex);
                
                if (WeComConfig.isEnableDetailedPhaseLogging() && totalGroups > batchSize) {
                    Tools.println("处理群组数据库写入批次 " + (i/batchSize + 1) + "/" + 
                                 (totalGroups/batchSize + (totalGroups%batchSize > 0 ? 1 : 0)) + 
                                 "，数量: " + batchGroups.size());
                }
                
                if (isIncremental) {
                    performIncrementalGroupSync(batchGroups, saasId, result);
                } else {
                    // 即使是全量同步，也使用增量方式执行，避免数据丢失风险
                    performIncrementalGroupSync(batchGroups, saasId, result);
                }
                
                // 批次间隔
                if (endIndex < totalGroups && WeComConfig.getBatchIntervalMs() > 0) {
                    try {
                        Thread.sleep(WeComConfig.getBatchIntervalMs());
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        }
    }
    
    /**
     * 阶段4: 同步群成员关系
     */
    private static void syncGroupMembers(String corpId, String corpSecret, String saasId,
            boolean isIncremental, SyncResult result, SyncProgress progress) {
        try {
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("\n--- 阶段4: 同步群成员关系 ---");
            }
            
            if (WeComConfig.isEnableProgressMonitoring()) {
                progress.setPhaseProgress(10);
            }
            
            // 获取数据库中的群列表
            List<GroupChat> allGroups = weComJDBC.getAllWeComGroups(saasId);
            if (allGroups == null || allGroups.isEmpty()) {
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("数据库中无客户群数据，跳过群成员同步");
                }
                return;
            }
            
            if (WeComConfig.isEnableProgressMonitoring()) {
                progress.setPhaseProgress(20);
            }
            
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("开始同步 " + allGroups.size() + " 个群的成员关系");
            }
            
            List<GroupChat> groupsToProcess = allGroups;
            
            // 增量同步优化：基于时间阈值和版本号的智能跳过
            if (isIncremental && WeComConfig.getIncrementalSyncSkipDays() > 0) {
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("增量同步模式：开始群成员关系智能跳过分析，时间阈值=" + WeComConfig.getIncrementalSyncSkipDays() + "天");
                }
                
                // 计算时间阈值
                long skipThresholdMs = WeComConfig.getIncrementalSyncSkipDays() * 24 * 60 * 60 * 1000L;
                long currentTime = System.currentTimeMillis();
                
                List<GroupChat> needUpdateGroups = new ArrayList<>();
                List<String> skippedChatIds = new ArrayList<>();
                
                for (GroupChat group : allGroups) {
                    String chatId = group.getChatId();
                    boolean needUpdate = false;
                    String skipReason = "";
                    
                    // 检查群的基本信息是否最近更新过
                    if (group.getGroupSyncTime() == null) {
                        // 没有同步时间记录，需要同步
                        needUpdate = true;
                        skipReason = "无同步时间记录";
                    } else {
                        long timeSinceLastSync = currentTime - group.getGroupSyncTime().getTime();
                        if (timeSinceLastSync > skipThresholdMs) {
                            // 群基本信息超过阈值，需要同步成员
                            needUpdate = true;
                            skipReason = "群信息超过时间阈值";
                        } else {
                            // 检查是否有memberVersion变化（如果群信息是最近同步的且有版本号）
                            // 这里可以通过API重新获取群版本号，但为了节约API调用，我们依赖之前的群同步结果
                            skipReason = "在时间阈值内且无版本变化";
                        }
                    }
                    
                    if (needUpdate) {
                        needUpdateGroups.add(group);
                    } else {
                        skippedChatIds.add(chatId);
                        if (WeComConfig.isEnableVerboseLogging()) {
                            Tools.println("跳过群 " + chatId + " 成员同步: " + skipReason);
                        }
                    }
                }
                
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("群成员关系增量同步分析结果:");
                    Tools.println("  - 需要同步成员: " + needUpdateGroups.size() + " 个群");
                    Tools.println("  - 智能跳过: " + skippedChatIds.size() + " 个群");
                    Tools.println("  - API调用节约: " + skippedChatIds.size() + " 次");
                }
                
                groupsToProcess = needUpdateGroups;
            } else {
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    if (!isIncremental) {
                        Tools.println("全量同步模式：处理所有群的成员关系");
                    } else {
                        Tools.println("增量同步模式：时间阈值优化已禁用，处理所有群的成员关系");
                    }
                }
            }
            
            // 处理需要同步的群
            int processedGroupCount = 0;
            
            for (GroupChat group : groupsToProcess) {
                String chatId = group.getChatId();
                try {
                    // 获取群成员列表
                    List<GroupChatMember> members = WeComGroupChatService.getGroupChatMembers(corpId, corpSecret, chatId);
                    
                    if (members != null && !members.isEmpty()) {
                        // 设置基础信息
                        Date memberSyncTime = new Date();
                        for (GroupChatMember member : members) {
                            member.setChatId(chatId);
                            member.setWecom_saas_id(saasId);
                            member.setMemberSyncTime(memberSyncTime);
                        }
                        
                        // 使用分批处理方法
                        processGroupMemberChunk(chatId, members, saasId, result, progress);
                    }
                    
                } catch (Exception e) {
                    result.addError("GROUP_MEMBERS_" + chatId, "同步群成员失败: " + e.getMessage());
                    if (WeComConfig.isEnableVerboseLogging()) {
                        Tools.println("同步群 " + chatId + " 成员失败: " + e.getMessage());
                    }
                }
                
                processedGroupCount++;
                if (WeComConfig.isEnableProgressMonitoring() && groupsToProcess.size() > 0) {
                    int currentProgress = 20 + (processedGroupCount * 70 / groupsToProcess.size());
                    progress.setPhaseProgress(currentProgress);
                    
                    if (WeComConfig.isEnableConsoleProgress() && 
                        processedGroupCount % WeComConfig.getProgressUpdateFrequency() == 0) {
                        Tools.println("群成员关系同步进度: " + processedGroupCount + "/" + groupsToProcess.size() + 
                            " (总群数: " + allGroups.size() + ")");
                    }
                }
            }
            
            if (WeComConfig.isEnableProgressMonitoring()) {
                progress.setPhaseProgress(100);
            }
            
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("群成员关系同步完成: 新增=" + result.getNewMemberCount() + 
                        ", 删除=" + result.getDeletedMemberCount());
                if (isIncremental && groupsToProcess.size() < allGroups.size()) {
                    Tools.println("增量同步优化: 处理了 " + groupsToProcess.size() + "/" + allGroups.size() + 
                            " 个群，减少了 " + (allGroups.size() - groupsToProcess.size()) + " 次API调用");
                }
            }
                
        } catch (Exception e) {
            result.addError("GROUP_MEMBERS", e.getMessage());
            Tools.println("群成员关系同步失败: " + e.getMessage());
            if (WeComConfig.isLog40096Details()) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 处理群成员关系，支持分批处理
     */
    private static void processGroupMemberChunk(String chatId, List<GroupChatMember> members, String saasId, SyncResult result, SyncProgress progress) {
        if (members == null || members.isEmpty()) {
            return;
        }
        
        if (WeComConfig.isEnableDetailedPhaseLogging()) {
            Tools.println("处理群 " + chatId + " 的成员关系，成员数量: " + members.size());
        }
        
        // 检查是否需要分批处理
        if (isDataVolumeExceedsLimit(members.size())) {
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("群成员数量(" + members.size() + ")超过单批限制(" + 
                    WeComConfig.getMaxSyncItemsPerBatch() + ")，将分批处理");
            }
            
            // 分批处理
            List<List<GroupChatMember>> chunks = chunkData(members, WeComConfig.getMaxSyncItemsPerBatch());
            for (int i = 0; i < chunks.size(); i++) {
                List<GroupChatMember> chunk = chunks.get(i);
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("处理群成员第" + (i + 1) + "/" + chunks.size() + "批，数量: " + chunk.size());
                }
                processGroupMemberBatch(chatId, chunk, saasId, result);
                
                // 批次间隔
                if (i < chunks.size() - 1 && WeComConfig.getBatchIntervalMs() > 0) {
                    try {
                        Thread.sleep(WeComConfig.getBatchIntervalMs());
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        } else {
            // 正常处理
            processGroupMemberBatch(chatId, members, saasId, result);
        }
    }
    
    /**
     * 处理一批群成员数据
     */
    private static void processGroupMemberBatch(String chatId, List<GroupChatMember> members, String saasId, SyncResult result) {
        // 执行增量或全量同步
        if (WeComConfig.isDefaultSyncModeIncremental()) {
            performIncrementalGroupMemberSync(chatId, members, saasId, result);
        } else {
            performFullGroupMemberSync(chatId, members, saasId, result);
        }
    }
    
    /**
     * 阶段5: 同步跟进人关系
     */
    private static void syncFollowers(String corpId, String corpSecret, String saasId,
            boolean isIncremental, SyncResult result, SyncProgress progress) {
        try {
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("\n--- 阶段5: 同步跟进人关系 ---");
            }
            
            if (WeComConfig.isEnableProgressMonitoring()) {
                progress.setPhaseProgress(10);
            }
            
            // 获取外部联系人列表
            List<WeComCustomer> externalContacts = weComJDBC.getAllWeComExternalContacts(saasId);
            if (externalContacts == null || externalContacts.isEmpty()) {
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("数据库中无外部联系人数据，跳过跟进人关系同步");
                }
                return;
            }
            
            if (WeComConfig.isEnableProgressMonitoring()) {
                progress.setPhaseProgress(20);
            }
            
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("开始同步 " + externalContacts.size() + " 个外部联系人的跟进人关系");
            }
            
            List<WeComCustomer> contactsToProcess = externalContacts;
            
            // 增量同步优化：基于时间阈值的智能跳过
            if (isIncremental && WeComConfig.getIncrementalSyncSkipDays() > 0) {
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("增量同步模式：开始跟进人关系智能跳过分析，时间阈值=" + WeComConfig.getIncrementalSyncSkipDays() + "天");
                }
                
                // 计算时间阈值
                long skipThresholdMs = WeComConfig.getIncrementalSyncSkipDays() * 24 * 60 * 60 * 1000L;
                long currentTime = System.currentTimeMillis();
                
                List<WeComCustomer> needProcessContacts = new ArrayList<>();
                List<String> skippedContactIds = new ArrayList<>();
                
                for (WeComCustomer contact : externalContacts) {
                    String externalUserId = contact.getExternalUserId();
                    boolean needProcess = false;
                    String skipReason = "";
                    
                    // 检查外部联系人的同步时间
                    if (contact.getExternalSyncTime() == null) {
                        // 没有同步时间记录，需要处理
                        needProcess = true;
                        skipReason = "无外部联系人同步时间记录";
                    } else {
                        long timeSinceLastSync = currentTime - contact.getExternalSyncTime().getTime();
                        if (timeSinceLastSync > skipThresholdMs) {
                            // 外部联系人超过阈值，可能跟进人关系也有变化
                            needProcess = true;
                            skipReason = "外部联系人超过时间阈值";
                        } else {
                            // 检查是否有跟进人JSON内容（如果外部联系人最近同步过）
                            String followUsersJson = contact.getFollowUsersJson();
                            if (followUsersJson == null || followUsersJson.trim().isEmpty()) {
                                // 没有跟进人数据，也需要跳过
                                skipReason = "在时间阈值内且无跟进人数据";
                            } else {
                                // 有跟进人数据但在时间阈值内，可以跳过
                                skipReason = "在时间阈值内且跟进人数据已存在";
                            }
                        }
                    }
                    
                    if (needProcess) {
                        needProcessContacts.add(contact);
                    } else {
                        skippedContactIds.add(externalUserId);
                        if (WeComConfig.isEnableVerboseLogging()) {
                            Tools.println("跳过外部联系人 " + externalUserId + " 跟进人同步: " + skipReason);
                        }
                    }
                }
                
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("跟进人关系增量同步分析结果:");
                    Tools.println("  - 需要处理: " + needProcessContacts.size() + " 个外部联系人");
                    Tools.println("  - 智能跳过: " + skippedContactIds.size() + " 个外部联系人");
                    Tools.println("  - 处理量减少: " + skippedContactIds.size() + " 次");
                }
                
                contactsToProcess = needProcessContacts;
            } else {
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    if (!isIncremental) {
                        Tools.println("全量同步模式：处理所有外部联系人的跟进人关系");
                    } else {
                        Tools.println("增量同步模式：时间阈值优化已禁用，处理所有外部联系人的跟进人关系");
                    }
                }
            }
            
            // 处理需要同步的外部联系人
            String accessToken = WeComTokenManager.getAccessToken(corpId, corpSecret);
            
            // ==================== 优化部分开始 ====================
            
            // 1. 收集所有需要处理的外部联系人ID
            List<String> allExternalUserIds = new ArrayList<>();
            for (WeComCustomer contact : contactsToProcess) {
                allExternalUserIds.add(contact.getExternalUserId());
            }
            
            // 2. 一次性批量查询所有已存在的跟进人关系
            Map<String, List<FollowerInfo>> existingFollowersMap = new HashMap<>();
            if (!allExternalUserIds.isEmpty()) {
                List<FollowerInfo> allExistingFollowers = weComJDBC.batchGetWeComExternalContactFollowers(allExternalUserIds, saasId);
                
                // 按外部联系人ID分组
                for (FollowerInfo follower : allExistingFollowers) {
                    String externalUserId = follower.getExternalUserId();
                    if (!existingFollowersMap.containsKey(externalUserId)) {
                        existingFollowersMap.put(externalUserId, new ArrayList<>());
                    }
                    existingFollowersMap.get(externalUserId).add(follower);
                }
                
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("批量获取到 " + allExistingFollowers.size() + " 条现有跟进人关系记录，涉及 " + 
                            existingFollowersMap.size() + " 个外部联系人");
                }
            }
            
            // 3. 准备批量处理的数据
            List<FollowerInfo> allFollowersToAdd = new ArrayList<>();
            List<FollowerInfo> allFollowersToUpdate = new ArrayList<>();
            Map<String, Set<String>> followersToDeleteMap = new HashMap<>();
            
            int processedCount = 0;
            int totalFollowersProcessed = 0;
            
            // 4. 处理逻辑
            for (WeComCustomer contact : contactsToProcess) {
                String externalUserId = contact.getExternalUserId();
                try {
                    // 获取跟进人信息（从数据库获取的JSON字符串）
                    String followUsersJson = contact.getFollowUsersJson();
                    
                    if (WeComConfig.isLogApiRawJsonResponse()) {
                        Tools.println("=== 外部联系人跟进人关系原始数据 ===");
                        Tools.println("外部联系人ID: " + externalUserId);
                        Tools.println("跟进人JSON原始数据: " + (followUsersJson != null ? followUsersJson : "null"));
                        Tools.println("==============================");
                    }
                    
                    if (followUsersJson != null && !followUsersJson.trim().isEmpty()) {
                        // 解析JSON字符串为JSONArray
                        JSONArray followUsers = com.alibaba.fastjson2.JSON.parseArray(followUsersJson);

                        if (followUsers != null && !followUsers.isEmpty()) {
                            List<FollowerInfo> followers = parseFollowerInfo(externalUserId, followUsers, saasId, contact);
                            
                            if (!followers.isEmpty()) {
                                // 获取该外部联系人现有的跟进人
                                List<FollowerInfo> existingFollowers = existingFollowersMap.getOrDefault(externalUserId, new ArrayList<>());
                                Map<String, FollowerInfo> existingFollowerMap = buildFollowerMap(existingFollowers);
                                
                                // 记录API中存在的跟进人ID
                                Set<String> apiFollowerIds = new HashSet<>();
                                
                                // 分类处理
                                for (FollowerInfo apiFollower : followers) {
                                    String followerUserId = apiFollower.getFollowerUserId();
                                    apiFollowerIds.add(followerUserId);
                                    
                                    FollowerInfo dbFollower = existingFollowerMap.get(followerUserId);
                                    if (dbFollower == null) {
                                        // 数据库中不存在，加入新增列表
                                        allFollowersToAdd.add(apiFollower);
                                    } else {
                                        // 数据库中存在，检查是否需要更新
                                        if (needsFollowerUpdate(apiFollower, dbFollower)) {
                                            allFollowersToUpdate.add(apiFollower);
                                        }
                                    }
                                }
                                
                                // 找出需要删除的跟进人
                                if (!existingFollowers.isEmpty()) {
                                    Set<String> followersToDelete = new HashSet<>();
                                    for (FollowerInfo existingFollower : existingFollowers) {
                                        if (!apiFollowerIds.contains(existingFollower.getFollowerUserId())) {
                                            followersToDelete.add(existingFollower.getFollowerUserId());
                                        }
                                    }
                                    
                                    if (!followersToDelete.isEmpty()) {
                                        followersToDeleteMap.put(externalUserId, followersToDelete);
                                    }
                                }
                                
                                totalFollowersProcessed += followers.size();
                            }
                        }
                    }
                    
                } catch (Exception e) {
                    result.addError("FOLLOWERS_" + externalUserId, "同步跟进人关系失败: " + e.getMessage());
                    if (WeComConfig.isEnableVerboseLogging()) {
                        Tools.println("同步外部联系人 " + externalUserId + " 跟进人关系失败: " + e.getMessage());
                        e.printStackTrace();
                    }
                }
                
                processedCount++;
                // 只在每处理100个联系人或最后一个联系人时输出进度
                if ((processedCount % 100 == 0 || processedCount == contactsToProcess.size()) && 
                    WeComConfig.isEnableProgressMonitoring()) {
                    int currentProgress = 20 + (processedCount * 70 / contactsToProcess.size());
                    progress.setPhaseProgress(currentProgress);
                    
                    if (WeComConfig.isEnableConsoleProgress()) {
                        Tools.println("跟进人关系同步进度: " + processedCount + "/" + contactsToProcess.size() + 
                            " (总联系人数: " + externalContacts.size() + ", 已处理跟进人:" + totalFollowersProcessed + ")");
                    }
                }
            }
            
            // 5. 批量执行数据库操作
            // 批量新增跟进人
            if (!allFollowersToAdd.isEmpty()) {
                int addedCount = weComJDBC.batchAddWeComExternalContactFollowers(allFollowersToAdd);
                result.newFollowerCount += addedCount;
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("批量新增跟进人关系: " + addedCount + " 条");
                }
            }
            
            // 批量更新跟进人
            if (!allFollowersToUpdate.isEmpty()) {
                int updatedCount = weComJDBC.batchUpdateWeComExternalContactFollowers(allFollowersToUpdate);
                result.updatedFollowerCount += updatedCount;
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("批量更新跟进人关系: " + updatedCount + " 条");
                }
            }
            
            // 批量删除跟进人
            if (!followersToDeleteMap.isEmpty()) {
                int deletedCount = weComJDBC.batchDeleteWeComExternalContactFollowers(followersToDeleteMap);
                result.deletedFollowerCount += deletedCount;
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("批量删除跟进人关系: " + deletedCount + " 条");
                }
            }
            
            // ==================== 优化部分结束 ====================
            
            if (WeComConfig.isEnableProgressMonitoring()) {
                progress.setPhaseProgress(100);
            }
            
        } catch (Exception e) {
            Tools.println("同步跟进人关系时发生异常: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
    
    // ==================== 增量同步实现方法 ====================
    
    private static void performIncrementalInternalContactSync(List<WeComInternalContact> apiContacts, 
            String saasId, SyncResult result) {
        // 获取数据库现有数据
        List<WeComInternalContact> dbContacts = weComJDBC.getAllWeComInternalContacts(saasId);
        Map<String, WeComInternalContact> dbContactMap = buildInternalContactMap(dbContacts);
        
        List<WeComInternalContact> toAdd = new ArrayList<>();
        List<WeComInternalContact> toUpdate = new ArrayList<>();
        Set<String> apiUserIds = new HashSet<>();
        
        // 分类处理
        for (WeComInternalContact apiContact : apiContacts) {
            String userId = apiContact.getUserId();
            apiUserIds.add(userId);
            
            WeComInternalContact dbContact = dbContactMap.get(userId);
            if (dbContact == null) {
                toAdd.add(apiContact);
            } else if (needsInternalContactUpdate(apiContact, dbContact)) {
                toUpdate.add(apiContact);
            }
        }
        
        // 找出需要删除的
        List<String> toDelete = new ArrayList<>();
        for (String dbUserId : dbContactMap.keySet()) {
            if (!apiUserIds.contains(dbUserId)) {
                toDelete.add(dbUserId);
            }
        }
        
        // 执行操作
        if (!toAdd.isEmpty()) {
            int addedCount = weComJDBC.batchAddWeComInternalContacts(toAdd);
            result.newInternalContactCount = addedCount;
        }
        
        if (!toUpdate.isEmpty()) {
            for (WeComInternalContact contact : toUpdate) {
                if (weComJDBC.updateWeComInternalContact(contact)) {
                    result.updatedInternalContactCount++;
                }
            }
        }
        
        if (!toDelete.isEmpty()) {
            for (String userId : toDelete) {
                if (weComJDBC.deleteWeComInternalContact(userId)) {
                    result.deletedInternalContactCount++;
                }
            }
        }
    }
    
    private static void performFullInternalContactSync(List<WeComInternalContact> apiContacts, SyncResult result) {
        if (apiContacts.isEmpty()) {
            return;
        }
        
        // 先删除现有的内部联系人数据
        List<WeComInternalContact> existingContacts = weComJDBC.getAllWeComInternalContacts(apiContacts.get(0).getWecom_saas_id());
        for (WeComInternalContact existing : existingContacts) {
            if (weComJDBC.deleteWeComInternalContact(existing.getUserId())) {
                result.deletedInternalContactCount++;
            }
        }
        
        // 批处理间隔
        if (WeComConfig.getBatchIntervalMs() > 0 && !existingContacts.isEmpty()) {
            try {
                Thread.sleep(WeComConfig.getBatchIntervalMs());
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        // 批量添加新的内部联系人数据
        int savedCount = weComJDBC.batchAddWeComInternalContacts(apiContacts);
        result.newInternalContactCount = savedCount;
        result.totalInternalContactCount = apiContacts.size();
    }
    
    private static void performIncrementalExternalContactSync(List<WeComCustomer> apiContacts, 
            String saasId, SyncResult result) {
        List<String> externalUserIds = new ArrayList<>();
        for (WeComCustomer contact : apiContacts) {
            externalUserIds.add(contact.getExternalUserId());
        }
        
        Map<String, WeComCustomer> dbContactMap = weComJDBC.getWeComExternalContactsByIds(externalUserIds);
        
        List<WeComCustomer> toAdd = new ArrayList<>();
        List<WeComCustomer> toUpdate = new ArrayList<>();
        
        for (WeComCustomer apiContact : apiContacts) {
            String externalUserId = apiContact.getExternalUserId();
            WeComCustomer dbContact = dbContactMap.get(externalUserId);
            
            if (dbContact == null) {
                toAdd.add(apiContact);
            } else if (needsExternalContactUpdate(apiContact, dbContact)) {
                toUpdate.add(apiContact);
            }
        }
        
        if (!toAdd.isEmpty()) {
            int addedCount = weComJDBC.batchAddWeComExternalContacts(toAdd);
            // 修改为累加而不是覆盖
            result.newExternalContactCount += addedCount;
        }
        
        if (!toUpdate.isEmpty()) {
            int updatedCount = weComJDBC.batchUpdateWeComExternalContacts(toUpdate);
            // 修改为累加而不是覆盖
            result.updatedExternalContactCount += updatedCount;
        }
    }
    
    private static void performFullExternalContactSync(List<WeComCustomer> apiContacts, SyncResult result) {
        if (apiContacts.isEmpty()) {
            return;
        }
        
        String saasId = apiContacts.get(0).getWecom_saas_id();
        
        // 通过增量同步模式处理，而不是先删除后添加
        if (WeComConfig.isEnableDetailedPhaseLogging()) {
            Tools.println("全量同步改用增量方式执行，避免删除再添加的风险");
        }
        performIncrementalExternalContactSync(apiContacts, saasId, result);
        
        // 注意：totalExternalContactCount 已经在 processExternalContactChunk 中累加过了
    }
    
    private static void performIncrementalGroupSync(List<GroupChat> apiGroups, String saasId, SyncResult result) {
        List<String> chatIds = new ArrayList<>();
        for (GroupChat group : apiGroups) {
            chatIds.add(group.getChatId());
        }
        
        Map<String, GroupChat> dbGroupMap = weComJDBC.getWeComGroupsByChatIds(chatIds);
        
        for (GroupChat apiGroup : apiGroups) {
            String chatId = apiGroup.getChatId();
            GroupChat dbGroup = dbGroupMap.get(chatId);
            
            if (dbGroup == null) {
                if (weComJDBC.addWeComGroup(apiGroup)) {
                    result.newGroupCount++;
                }
            } else if (needsGroupUpdate(apiGroup, dbGroup)) {
                if (weComJDBC.updateWeComGroup(apiGroup)) {
                    result.updatedGroupCount++;
                }
            }
        }
    }
    
    private static void performFullGroupSync(List<GroupChat> apiGroups, SyncResult result) {
        if (apiGroups.isEmpty()) {
            return;
        }
        
        String saasId = apiGroups.get(0).getSaasId();
        
        if (WeComConfig.isEnableDetailedPhaseLogging()) {
            Tools.println("全量群组同步改用增量方式执行，避免删除再添加的风险");
        }
        
        // 使用增量同步代替删除后添加
        performIncrementalGroupSync(apiGroups, saasId, result);
        
        // 记录总数
        result.totalGroupCount = apiGroups.size();
    }
    
    private static void performIncrementalGroupMemberSync(String chatId, List<GroupChatMember> newMembers, 
            String saasId, SyncResult result) {
        // 获取数据库中现有的群成员关系
        List<GroupChatMember> existingMembers = weComJDBC.getWeComGroupMemberRelationsByGroup(chatId, saasId);
        Map<String, GroupChatMember> existingMemberMap = buildGroupMemberMap(existingMembers);
        
        // 用于跟踪API中的成员ID，最后删除不在API中的成员
        Set<String> apiMemberIds = new HashSet<>();
        
        // 收集需要新增的成员
        List<GroupChatMember> membersToAdd = new ArrayList<>();
        // 收集需要更新的成员
        List<GroupChatMember> membersToUpdate = new ArrayList<>();
        
        // 处理API中的每个成员
        for (GroupChatMember apiMember : newMembers) {
            String userId = apiMember.getUserId();
            apiMemberIds.add(userId);
            
            GroupChatMember dbMember = existingMemberMap.get(userId);
            if (dbMember == null) {
                // 数据库中不存在，加入新增列表
                membersToAdd.add(apiMember);
            } else {
                // 数据库中存在，检查是否需要更新
                if (needsGroupMemberUpdate(apiMember, dbMember)) {
                    membersToUpdate.add(apiMember);
                }
            }
        }
        
        // 批量新增成员
        if (!membersToAdd.isEmpty()) {
            int addedCount = weComJDBC.batchAddWeComGroupMemberRelations(membersToAdd);
            result.newMemberCount += addedCount;
            if (WeComConfig.isEnableDetailedPhaseLogging() && addedCount > 0) {
                Tools.println("批量新增群成员关系: " + addedCount + " 条");
            }
        }
        
        // 逐个更新成员（更新操作相对较少，暂时保持单个处理）
        for (GroupChatMember memberToUpdate : membersToUpdate) {
            if (weComJDBC.updateWeComGroupMemberRelation(memberToUpdate)) {
                result.updatedMemberCount++;
            }
        }
        
        // 删除不在API中的成员（这些成员已经退群）
        for (GroupChatMember existingMember : existingMembers) {
            if (!apiMemberIds.contains(existingMember.getUserId())) {
                if (weComJDBC.deleteWeComGroupMemberRelation(chatId, existingMember.getUserId())) {
                    result.deletedMemberCount++;
                }
            }
        }
    }
    
    private static void performFullGroupMemberSync(String chatId, List<GroupChatMember> members, 
            String saasId, SyncResult result) {
        // 获取数据库中现有的群成员关系
        List<GroupChatMember> existingMembers = weComJDBC.getWeComGroupMemberRelationsByGroup(chatId, saasId);
        Map<String, GroupChatMember> existingMemberMap = buildGroupMemberMap(existingMembers);
        
        // 用于跟踪API中的成员ID，最后删除不在API中的成员
        Set<String> apiMemberIds = new HashSet<>();
        
        // 收集需要新增的成员
        List<GroupChatMember> membersToAdd = new ArrayList<>();
        // 收集需要更新的成员
        List<GroupChatMember> membersToUpdate = new ArrayList<>();
        
        // 处理API中的每个成员
        for (GroupChatMember apiMember : members) {
            String userId = apiMember.getUserId();
            apiMemberIds.add(userId);
            
            GroupChatMember dbMember = existingMemberMap.get(userId);
            if (dbMember == null) {
                // 数据库中不存在，加入新增列表
                membersToAdd.add(apiMember);
            } else {
                // 数据库中存在，检查是否需要更新
                if (needsGroupMemberUpdate(apiMember, dbMember)) {
                    membersToUpdate.add(apiMember);
                }
            }
        }
        
        // 批量新增成员
        if (!membersToAdd.isEmpty()) {
            int addedCount = weComJDBC.batchAddWeComGroupMemberRelations(membersToAdd);
            result.newMemberCount += addedCount;
            if (WeComConfig.isEnableDetailedPhaseLogging() && addedCount > 0) {
                Tools.println("批量新增群成员关系: " + addedCount + " 条");
            }
        }
        
        // 逐个更新成员（更新操作相对较少，暂时保持单个处理）
        for (GroupChatMember memberToUpdate : membersToUpdate) {
            if (weComJDBC.updateWeComGroupMemberRelation(memberToUpdate)) {
                result.updatedMemberCount++;
            }
        }
        
        // 删除不在API中的成员（这些成员已经退群）
        for (GroupChatMember existingMember : existingMembers) {
            if (!apiMemberIds.contains(existingMember.getUserId())) {
                if (weComJDBC.deleteWeComGroupMemberRelation(chatId, existingMember.getUserId())) {
                    result.deletedMemberCount++;
                }
            }
        }
    }
    
    private static void performIncrementalFollowerSync(String externalUserId, List<FollowerInfo> followers, 
            String saasId, SyncResult result) {
        // 获取数据库中现有的跟进人关系
        List<FollowerInfo> existingFollowers = weComJDBC.getWeComExternalContactFollowersByExternalUser(externalUserId, saasId);
        Map<String, FollowerInfo> existingFollowerMap = buildFollowerMap(existingFollowers);
        
        // 用于跟踪API中的跟进人ID，最后删除不在API中的跟进人
        Set<String> apiFollowerIds = new HashSet<>();
        
        // 收集需要新增的跟进人
        List<FollowerInfo> followersToAdd = new ArrayList<>();
        // 收集需要更新的跟进人
        List<FollowerInfo> followersToUpdate = new ArrayList<>();
        
        // 处理API中的每个跟进人
        for (FollowerInfo apiFollower : followers) {
            String followerUserId = apiFollower.getFollowerUserId();
            apiFollowerIds.add(followerUserId);
            
            FollowerInfo dbFollower = existingFollowerMap.get(followerUserId);
            if (dbFollower == null) {
                // 数据库中不存在，加入新增列表
                followersToAdd.add(apiFollower);
            } else {
                // 数据库中存在，检查是否需要更新
                if (needsFollowerUpdate(apiFollower, dbFollower)) {
                    followersToUpdate.add(apiFollower);
                }
            }
        }
        
        // 批量新增跟进人
        if (!followersToAdd.isEmpty()) {
            int addedCount = weComJDBC.batchAddWeComExternalContactFollowers(followersToAdd);
            result.newFollowerCount += addedCount;
            if (WeComConfig.isEnableDetailedPhaseLogging() && addedCount > 0) {
                Tools.println("批量新增跟进人关系: " + addedCount + " 条");
            }
        }
        
        // 逐个更新跟进人（更新操作相对较少，暂时保持单个处理）
        for (FollowerInfo followerToUpdate : followersToUpdate) {
            if (weComJDBC.updateWeComExternalContactFollower(followerToUpdate)) {
                result.updatedFollowerCount++;
            }
        }
        
        // 删除不在API中的跟进人（这些跟进人关系已经解除）
        for (FollowerInfo existingFollower : existingFollowers) {
            if (!apiFollowerIds.contains(existingFollower.getFollowerUserId())) {
                if (weComJDBC.deleteWeComExternalContactFollower(externalUserId, existingFollower.getFollowerUserId())) {
                    result.deletedFollowerCount++;
                }
            }
        }
    }
    
    private static void performFullFollowerSync(String externalUserId, List<FollowerInfo> followers, SyncResult result) {
        // 获取数据库中现有的跟进人关系
        List<FollowerInfo> existingFollowers = weComJDBC.getWeComExternalContactFollowersByExternalUser(externalUserId, ""); // 全量时不指定saasId
        Map<String, FollowerInfo> existingFollowerMap = buildFollowerMap(existingFollowers);
        
        // 用于跟踪API中的跟进人ID，最后删除不在API中的跟进人
        Set<String> apiFollowerIds = new HashSet<>();
        
        // 收集需要新增的跟进人
        List<FollowerInfo> followersToAdd = new ArrayList<>();
        // 收集需要更新的跟进人
        List<FollowerInfo> followersToUpdate = new ArrayList<>();
        
        // 处理API中的每个跟进人
        for (FollowerInfo apiFollower : followers) {
            String followerUserId = apiFollower.getFollowerUserId();
            apiFollowerIds.add(followerUserId);
            
            FollowerInfo dbFollower = existingFollowerMap.get(followerUserId);
            if (dbFollower == null) {
                // 数据库中不存在，加入新增列表
                followersToAdd.add(apiFollower);
            } else {
                // 数据库中存在，检查是否需要更新
                if (needsFollowerUpdate(apiFollower, dbFollower)) {
                    followersToUpdate.add(apiFollower);
                }
            }
        }
        
        // 批量新增跟进人
        if (!followersToAdd.isEmpty()) {
            int addedCount = weComJDBC.batchAddWeComExternalContactFollowers(followersToAdd);
            result.newFollowerCount += addedCount;
            if (WeComConfig.isEnableDetailedPhaseLogging() && addedCount > 0) {
                Tools.println("批量新增跟进人关系: " + addedCount + " 条");
            }
        }
        
        // 逐个更新跟进人（更新操作相对较少，暂时保持单个处理）
        for (FollowerInfo followerToUpdate : followersToUpdate) {
            if (weComJDBC.updateWeComExternalContactFollower(followerToUpdate)) {
                result.updatedFollowerCount++;
            }
        }
        
        // 删除不在API中的跟进人（这些跟进人关系已经解除）
        for (FollowerInfo existingFollower : existingFollowers) {
            if (!apiFollowerIds.contains(existingFollower.getFollowerUserId())) {
                if (weComJDBC.deleteWeComExternalContactFollower(externalUserId, existingFollower.getFollowerUserId())) {
                    result.deletedFollowerCount++;
                }
            }
        }
    }
    
    // ==================== 辅助方法 ====================
    
    /**
     * 同步后数据验证
     */
    private static void performPostSyncValidation(String corpId, String saasId, SyncResult result, SyncProgress progress) {
        try {
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("开始数据完整性验证");
            }
            
            progress.setPhaseProgress(20);
            
            // 验证内部联系人数据
            List<WeComInternalContact> internalContacts = weComJDBC.getAllWeComInternalContacts(saasId);
            if (WeComConfig.isEnableInternalContactDedup()) {
                int beforeCount = internalContacts.size();
                // 这里可以添加去重逻辑
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("内部联系人验证完成，记录数: " + beforeCount);
                }
            }
            
            progress.setPhaseProgress(40);
            
            // 验证外部联系人数据
            List<WeComCustomer> externalContacts = weComJDBC.getAllWeComExternalContacts(saasId);
            if (WeComConfig.isEnableExternalContactDedup()) {
                int beforeCount = externalContacts.size();
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("外部联系人验证完成，记录数: " + beforeCount);
                }
            }
            
            progress.setPhaseProgress(60);
            
            // 验证客户群数据
            List<GroupChat> groups = weComJDBC.getAllWeComGroups(saasId);
            if (WeComConfig.isEnableGroupDedup()) {
                int beforeCount = groups.size();
                if (WeComConfig.isEnableDetailedPhaseLogging()) {
                    Tools.println("客户群验证完成，记录数: " + beforeCount);
                }
            }
            
            progress.setPhaseProgress(80);
            
            // 验证数据一致性
            if (WeComConfig.isEnableDataIntegrityCheck()) {
                validateDataIntegrity(saasId, result);
            }
            
            progress.setPhaseProgress(100);
            
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("数据验证完成");
            }
            
        } catch (Exception e) {
            Tools.println("数据验证失败: " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * 数据完整性检查
     */
    private static void validateDataIntegrity(String saasId, SyncResult result) {
        try {
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("开始数据完整性检查");
            }
            
            // 检查孤立的群成员关系（群不存在但有成员关系）
            // 检查孤立的跟进人关系（外部联系人不存在但有跟进人关系）
            // 这里可以添加具体的完整性检查逻辑
            
            if (WeComConfig.isEnableDetailedPhaseLogging()) {
                Tools.println("数据完整性检查完成");
            }
            
        } catch (Exception e) {
            Tools.println("数据完整性检查失败: " + e.getMessage());
            if (WeComConfig.isAutoFixInconsistency()) {
                Tools.println("尝试自动修复数据不一致...");
                // 这里可以添加自动修复逻辑
            }
            throw e;
        }
    }
    
    /**
     * 检查数据量是否超过限制
     */
    private static boolean isDataVolumeExceedsLimit(int dataCount) {
        return dataCount > WeComConfig.getMaxSyncItemsPerBatch();
    }
    
    /**
     * 分批处理大数据量
     */
    private static <T> List<List<T>> chunkData(List<T> data, int chunkSize) {
        List<List<T>> chunks = new ArrayList<>();
        for (int i = 0; i < data.size(); i += chunkSize) {
            chunks.add(data.subList(i, Math.min(i + chunkSize, data.size())));
        }
        return chunks;
    }
    
    private static String generateSessionId(String corpId, String saasId) {
        return corpId + "_" + saasId + "_" + System.currentTimeMillis();
    }
    
    private static Map<String, WeComInternalContact> buildInternalContactMap(List<WeComInternalContact> contacts) {
        Map<String, WeComInternalContact> map = new HashMap<>();
        if (contacts != null) {
            for (WeComInternalContact contact : contacts) {
                map.put(contact.getUserId(), contact);
            }
        }
        return map;
    }
    
    private static Map<String, WeComCustomer> buildExternalContactMap(List<WeComCustomer> contacts) {
        Map<String, WeComCustomer> map = new HashMap<>();
        if (contacts != null) {
            for (WeComCustomer contact : contacts) {
                map.put(contact.getExternalUserId(), contact);
            }
        }
        return map;
    }
    
    private static Map<String, GroupChatMember> buildGroupMemberMap(List<GroupChatMember> members) {
        Map<String, GroupChatMember> map = new HashMap<>();
        if (members != null) {
            for (GroupChatMember member : members) {
                map.put(member.getUserId(), member);
            }
        }
        return map;
    }
    
    private static Map<String, FollowerInfo> buildFollowerMap(List<FollowerInfo> followers) {
        Map<String, FollowerInfo> map = new HashMap<>();
        if (followers != null) {
            for (FollowerInfo follower : followers) {
                map.put(follower.getFollowerUserId(), follower);
            }
        }
        return map;
    }
    
    private static boolean needsInternalContactUpdate(WeComInternalContact apiContact, WeComInternalContact dbContact) {
        if (dbContact.getSyncTime() == null) return true;
        
        return !Objects.equals(apiContact.getName(), dbContact.getName()) ||
               !Objects.equals(apiContact.getMobile(), dbContact.getMobile()) ||
               !Objects.equals(apiContact.getStatus(), dbContact.getStatus()) ||
               !Objects.equals(apiContact.getPosition(), dbContact.getPosition());
    }
    
    private static boolean needsExternalContactUpdate(WeComCustomer apiContact, WeComCustomer dbContact) {
        if (dbContact.getExternalSyncTime() == null) return true;
        
        return !Objects.equals(apiContact.getName(), dbContact.getName()) ||
               !Objects.equals(apiContact.getFollowUsersJson(), dbContact.getFollowUsersJson());
    }
    
    private static boolean needsGroupUpdate(GroupChat apiGroup, GroupChat dbGroup) {
        return !Objects.equals(apiGroup.getName(), dbGroup.getName()) ||
               !Objects.equals(apiGroup.getMemberVersion(), dbGroup.getMemberVersion()) ||
               !Objects.equals(apiGroup.getMemberCount(), dbGroup.getMemberCount());
    }
    
    private static boolean needsGroupMemberUpdate(GroupChatMember apiMember, GroupChatMember dbMember) {
        return !Objects.equals(apiMember.getGroupNickname(), dbMember.getGroupNickname()) ||
               !Objects.equals(apiMember.getGroupVersion(), dbMember.getGroupVersion()) ||
               !Objects.equals(apiMember.getJoinTime(), dbMember.getJoinTime()) ||
               !Objects.equals(apiMember.getJoinScene(), dbMember.getJoinScene()) ||
               !Objects.equals(apiMember.getInvitorUserId(), dbMember.getInvitorUserId());
    }
    
    private static boolean needsFollowerUpdate(FollowerInfo apiFollower, FollowerInfo dbFollower) {
        return !Objects.equals(apiFollower.getRemark(), dbFollower.getRemark()) ||
               !Objects.equals(apiFollower.getDescription(), dbFollower.getDescription()) ||
               !Objects.equals(apiFollower.getTags(), dbFollower.getTags()) ||
               !Objects.equals(apiFollower.getRemarkMobiles(), dbFollower.getRemarkMobiles()) ||
               !Objects.equals(apiFollower.getRemarkCorpName(), dbFollower.getRemarkCorpName()) ||
               !Objects.equals(apiFollower.getAddWay(), dbFollower.getAddWay()) ||
               !Objects.equals(apiFollower.getOperUserId(), dbFollower.getOperUserId()) ||
               !Objects.equals(apiFollower.getFollowersVersion(), dbFollower.getFollowersVersion());
    }
    
    private static List<FollowerInfo> parseFollowerInfo(String externalUserId, JSONArray followUsers, String saasId, WeComCustomer contact) {
        List<FollowerInfo> followers = new ArrayList<>();
        
        // 生成一个基于外部联系人和时间戳的版本号
        String baseFollowersVersion = externalUserId + "_" + System.currentTimeMillis();
        
        for (int i = 0; i < followUsers.size(); i++) {
            JSONObject followUser = followUsers.getJSONObject(i);
            FollowerInfo follower = new FollowerInfo();
            
            // 设置基本信息
            follower.setExternalUserId(externalUserId);
            follower.setFollowerUserId(followUser.getString("userid"));
            follower.setRemark(followUser.getString("remark"));
            follower.setDescription(followUser.getString("description"));
            follower.setCreateTime(followUser.getString("createtime"));
            follower.setAddWay(followUser.getIntValue("add_way"));
            follower.setWecom_saas_id(saasId);
            
            // 处理标签信息
            if (followUser.containsKey("tags") && followUser.get("tags") != null) {
                Object tagsObj = followUser.get("tags");
                if (tagsObj instanceof JSONArray) {
                    JSONArray tagsArray = (JSONArray) tagsObj;
                    follower.setTags(tagsArray.toJSONString());
                }
            }
            
            // 处理备注手机号
            if (followUser.containsKey("remark_mobiles") && followUser.get("remark_mobiles") != null) {
                Object remarkMobilesObj = followUser.get("remark_mobiles");
                if (remarkMobilesObj instanceof JSONArray) {
                    JSONArray remarkMobilesArray = (JSONArray) remarkMobilesObj;
                    follower.setRemarkMobiles(remarkMobilesArray.toJSONString());
                }
            }
            
            // 处理操作用户ID
            if (followUser.containsKey("oper_userid")) {
                follower.setOperUserId(followUser.getString("oper_userid"));
            }
            
            // 处理备注公司名称
            if (followUser.containsKey("remark_corp_name")) {
                follower.setRemarkCorpName(followUser.getString("remark_corp_name"));
            }
            
            // 设置跟进人版本号 - 解决wecom_followers_version为null的问题
            // 企业微信API的跟进人信息中本身没有版本字段，我们需要生成一个
            String followersVersion = null;
            
            // 方案1: 使用外部联系人的同步时间戳作为版本
            if (contact != null && contact.getExternalSyncTime() != null) {
                followersVersion = externalUserId + "_" + contact.getExternalSyncTime().getTime();
            }
            // 方案2: 使用当前时间戳作为版本
            else {
                followersVersion = baseFollowersVersion + "_" + i;
            }
            
            follower.setFollowersVersion(followersVersion);
            
            // 设置同步时间
            follower.setFollowerSyncTime(new Date());
            
            // 详细日志输出
            if (WeComConfig.isLogApiRawJsonResponse() || WeComConfig.isEnableVerboseLogging()) {
                Tools.println("==============================");
            }
            
            followers.add(follower);
        }
        
        return followers;
    }
    
    /**
     * 获取同步进度
     * 
     * @param sessionId 会话ID
     * @return 同步进度，如果会话不存在返回null
     */
    public static SyncProgress getSyncProgress(String sessionId) {
        return activeSyncSessions.get(sessionId);
    }
    
    /**
     * 获取所有活跃的同步会话
     * 
     * @return 活跃会话列表
     */
    public static Map<String, SyncProgress> getActiveSyncSessions() {
        return new HashMap<>(activeSyncSessions);
    }
    
    // ==================== 内部类：同步结果 ====================
    
    /**
     * 同步结果统计类
     */
    public static class SyncResult {
        private String sessionId;
        private String syncMode; // FULL 或 INCREMENTAL
        private Date syncStartTime;
        private Date syncEndTime;
        private boolean success = false;
        private String errorMessage;
        
        // 内部联系人统计
        private int newInternalContactCount = 0;
        private int updatedInternalContactCount = 0;
        private int deletedInternalContactCount = 0;
        private int totalInternalContactCount = 0;
        
        // 外部联系人统计
        private int newExternalContactCount = 0;
        private int updatedExternalContactCount = 0;
        private int deletedExternalContactCount = 0;
        private int totalExternalContactCount = 0;
        
        // 客户群统计
        private int newGroupCount = 0;
        private int updatedGroupCount = 0;
        private int deletedGroupCount = 0;
        private int totalGroupCount = 0;
        
        // 群成员关系统计
        private int newMemberCount = 0;
        private int updatedMemberCount = 0;
        private int deletedMemberCount = 0;
        private int totalMemberCount = 0;
        
        // 跟进人关系统计
        private int newFollowerCount = 0;
        private int updatedFollowerCount = 0;
        private int deletedFollowerCount = 0;
        private int totalFollowerCount = 0;
        
        // 错误信息收集
        private Map<String, String> errors = new HashMap<>();
        
        // Getter和Setter方法
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
        
        public String getSyncMode() { return syncMode; }
        public void setSyncMode(String syncMode) { this.syncMode = syncMode; }
        
        public Date getSyncStartTime() { return syncStartTime; }
        public void setSyncStartTime(Date syncStartTime) { this.syncStartTime = syncStartTime; }
        
        public Date getSyncEndTime() { return syncEndTime; }
        public void setSyncEndTime(Date syncEndTime) { this.syncEndTime = syncEndTime; }
        
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        // 内部联系人统计
        public int getNewInternalContactCount() { return newInternalContactCount; }
        public int getUpdatedInternalContactCount() { return updatedInternalContactCount; }
        public int getDeletedInternalContactCount() { return deletedInternalContactCount; }
        public int getTotalInternalContactCount() { return totalInternalContactCount; }
        
        // 外部联系人统计
        public int getNewExternalContactCount() { return newExternalContactCount; }
        public int getUpdatedExternalContactCount() { return updatedExternalContactCount; }
        public int getDeletedExternalContactCount() { return deletedExternalContactCount; }
        public int getTotalExternalContactCount() { return totalExternalContactCount; }
        
        // 客户群统计
        public int getNewGroupCount() { return newGroupCount; }
        public int getUpdatedGroupCount() { return updatedGroupCount; }
        public int getDeletedGroupCount() { return deletedGroupCount; }
        public int getTotalGroupCount() { return totalGroupCount; }
        
        // 群成员关系统计
        public int getNewMemberCount() { return newMemberCount; }
        public int getUpdatedMemberCount() { return updatedMemberCount; }
        public int getDeletedMemberCount() { return deletedMemberCount; }
        public int getTotalMemberCount() { return totalMemberCount; }
        
        // 跟进人关系统计
        public int getNewFollowerCount() { return newFollowerCount; }
        public int getUpdatedFollowerCount() { return updatedFollowerCount; }
        public int getDeletedFollowerCount() { return deletedFollowerCount; }
        public int getTotalFollowerCount() { return totalFollowerCount; }
        
        public void addError(String key, String message) {
            errors.put(key, message);
        }
        
        public Map<String, String> getErrors() {
            return new HashMap<>(errors);
        }
        
        public long getSyncDurationMs() {
            if (syncStartTime != null && syncEndTime != null) {
                return syncEndTime.getTime() - syncStartTime.getTime();
            }
            return 0;
        }
        
        public String getSummary() {
            StringBuilder sb = new StringBuilder();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            
            sb.append("=== 同步结果汇总 ===\n");
            sb.append("会话ID: ").append(sessionId).append("\n");
            sb.append("同步模式: ").append(syncMode).append("\n");
            sb.append("开始时间: ").append(syncStartTime != null ? sdf.format(syncStartTime) : "N/A").append("\n");
            sb.append("结束时间: ").append(syncEndTime != null ? sdf.format(syncEndTime) : "N/A").append("\n");
            sb.append("耗时: ").append(getSyncDurationMs()).append("ms\n");
            sb.append("状态: ").append(success ? "成功" : "失败").append("\n");
            
            if (!success && errorMessage != null) {
                sb.append("错误信息: ").append(errorMessage).append("\n");
            }
            
            sb.append("\n--- 数据统计 ---\n");
            sb.append("内部联系人: 新增=").append(newInternalContactCount)
              .append(", 更新=").append(updatedInternalContactCount)
              .append(", 删除=").append(deletedInternalContactCount).append("\n");
            
            sb.append("外部联系人: 新增=").append(newExternalContactCount)
              .append(", 更新=").append(updatedExternalContactCount)
              .append(", 删除=").append(deletedExternalContactCount).append("\n");
            
            sb.append("客户群: 新增=").append(newGroupCount)
              .append(", 更新=").append(updatedGroupCount)
              .append(", 删除=").append(deletedGroupCount).append("\n");
            
            sb.append("群成员关系: 新增=").append(newMemberCount)
              .append(", 更新=").append(updatedMemberCount)
              .append(", 删除=").append(deletedMemberCount).append("\n");
            
            sb.append("跟进人关系: 新增=").append(newFollowerCount)
              .append(", 更新=").append(updatedFollowerCount)
              .append(", 删除=").append(deletedFollowerCount).append("\n");
            
            if (!errors.isEmpty()) {
                sb.append("\n--- 错误详情 ---\n");
                for (Map.Entry<String, String> error : errors.entrySet()) {
                    sb.append(error.getKey()).append(": ").append(error.getValue()).append("\n");
                }
            }
            
            return sb.toString();
        }
    }
    
    // ==================== 内部类：同步进度 ====================
    
    /**
     * 同步进度监控类
     */
    public static class SyncProgress {
        private String currentPhase = "INIT"; // 当前阶段
        private int phaseProgress = 0; // 当前阶段进度 (0-100)
        private Date lastUpdateTime = new Date();
        private final AtomicInteger totalProcessedItems = new AtomicInteger(0);
        
        public String getCurrentPhase() { return currentPhase; }
        public void setCurrentPhase(String currentPhase) { 
            this.currentPhase = currentPhase; 
            this.lastUpdateTime = new Date();
        }
        
        public int getPhaseProgress() { return phaseProgress; }
        public void setPhaseProgress(int phaseProgress) { 
            this.phaseProgress = phaseProgress; 
            this.lastUpdateTime = new Date();
        }
        
        public Date getLastUpdateTime() { return lastUpdateTime; }
        
        public int getTotalProcessedItems() { return totalProcessedItems.get(); }
        public void incrementProcessedItems() { totalProcessedItems.incrementAndGet(); }
        
        public String getProgressDescription() {
            String phaseDesc;
            switch (currentPhase) {
                case "INTERNAL_CONTACTS": phaseDesc = "同步内部联系人"; break;
                case "EXTERNAL_CONTACTS": phaseDesc = "同步外部联系人"; break;
                case "GROUPS": phaseDesc = "同步客户群"; break;
                case "GROUP_MEMBERS": phaseDesc = "同步群成员关系"; break;
                case "FOLLOWERS": phaseDesc = "同步跟进人关系"; break;
                case "COMPLETED": phaseDesc = "同步完成"; break;
                case "ERROR": phaseDesc = "同步失败"; break;
                default: phaseDesc = "初始化"; break;
            }
            return phaseDesc + " (" + phaseProgress + "%)";
        }
    }
}
