package com.career.db;

import java.io.*;
import java.util.HashMap;

public class AdvInfoBean {
	
	public static void main(String args[]){
		try {
			BufferedReader br = new BufferedReader(
					new FileReader(new File("F:\\DDD\\career\\WEB-INF\\classes\\com\\career\\db\\a.txt")));
			String read = null;
			StringBuffer sb = new StringBuffer();
			while((read = br.readLine()) != null){
				if(read.indexOf(".HM_PROVINCE_CODE_NAME :") != -1) {
					String read1  = br.readLine();
					String read2  = br.readLine();
					if(read1.indexOf("g \"") == -1 ) {
						continue;
					}
					
					String temp1 = read1.substring(read1.indexOf("g \"")+3, read1.indexOf("\"> ["));
					String temp2 = read2.substring(read2.indexOf("g \"")+3, read2.indexOf("\"> ["));
					//String temp4 = read4.substring(read4.indexOf("g \"")+3, read4.indexOf("\"> ["));
					//String temp5 = read5.substring(read5.indexOf("g \"")+3, read5.indexOf("\"> ["));
					//String temp6 = read6.substring(read6.indexOf("iconst")+7);
					//String temp7 = read7.substring(read7.indexOf("g \"")+3, read7.indexOf("\"> ["));
					//String temp8 = read8.substring(read8.indexOf("g \"")+3, read8.indexOf("\"> ["));
					
					String s = "HM_PROVINCE_CODE_NAME.put(\""+temp1+"\",\"" + temp2 + "\");\r\n";
					sb.append(s);
					
					
					continue;
				}
				
			}
			writeTempFile(new File("E://rcv1.txt"), sb);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	private static void writeTempFile(File file, StringBuffer sb)
    {
        try
        {
            BufferedWriter bw = new BufferedWriter(new FileWriter(file));
            bw.write(sb.toString());
            bw.flush();
            bw.close();
        }
        catch(IOException e)
        {
            e.printStackTrace();
        }
    }
	
  private String no;
  
  private String name;
  
  private String page;
  
  private String position;
  
  private String price;
  
  private String signPrice;
  
  private String signAgent;
  
  private String signAgentContact;
  
  private String signUni;
  
  private String signDt;
  
  private String contractExpDt;
  
  private String firstResvDT;
  
  private String fristResvAgent;
  
  private int status;
  
  private String remark;
  
  public String getPage() {
    return this.page;
  }
  
  public void setPage(String page) {
    this.page = page;
  }
  
  public String getRemark() {
    return this.remark;
  }
  
  public void setRemark(String remark) {
    this.remark = remark;
  }
  
  public String getSignAgentContact() {
    return this.signAgentContact;
  }
  
  public void setSignAgentContact(String signAgentContact) {
    this.signAgentContact = signAgentContact;
  }
  
  public String getPosition() {
    return this.position;
  }
  
  public void setPosition(String position) {
    this.position = position;
  }
  
  public String getNo() {
    return this.no;
  }
  
  public void setNo(String no) {
    this.no = no;
  }
  
  public String getName() {
    return this.name;
  }
  
  public void setName(String name) {
    this.name = name;
  }
  
  public String getPrice() {
    return this.price;
  }
  
  public void setPrice(String price) {
    this.price = price;
  }
  
  public String getSignPrice() {
    return this.signPrice;
  }
  
  public void setSignPrice(String signPrice) {
    this.signPrice = signPrice;
  }
  
  public String getSignAgent() {
    return this.signAgent;
  }
  
  public void setSignAgent(String signAgent) {
    this.signAgent = signAgent;
  }
  
  public String getSignUni() {
    return this.signUni;
  }
  
  public void setSignUni(String signUni) {
    this.signUni = signUni;
  }
  
  public String getSignDt() {
    return this.signDt;
  }
  
  public void setSignDt(String signDt) {
    this.signDt = signDt;
  }
  
  public String getContractExpDt() {
    return this.contractExpDt;
  }
  
  public void setContractExpDt(String contractExpDt) {
    this.contractExpDt = contractExpDt;
  }
  
  public String getFirstResvDT() {
    return this.firstResvDT;
  }
  
  public void setFirstResvDT(String firstResvDT) {
    this.firstResvDT = firstResvDT;
  }
  
  public String getFristResvAgent() {
    return this.fristResvAgent;
  }
  
  public void setFristResvAgent(String fristResvAgent) {
    this.fristResvAgent = fristResvAgent;
  }
  
  public int getStatus() {
    return this.status;
  }
  
  public void setStatus(int status) {
    this.status = status;
  }
  
  public static HashMap<String, String> ADV_SORT = new HashMap<>();
  
  static {
    ADV_SORT.put("1", "SORT");
    ADV_SORT.put("2", "AD_NO");
    ADV_SORT.put("3", "AD_SIGN_AGENT");
    ADV_SORT.put("4", "AD_SIGN_UNI");
    ADV_SORT.put("5", "AD_COTR_EXP_DT");
    ADV_SORT.put("6", "AD_PAGE");
  }
}
