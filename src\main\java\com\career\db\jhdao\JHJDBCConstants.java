package com.career.db.jhdao;

public class JHJDBCConstants {

	public static String URL_JH = "*********************************************************************************?" +
			"useUnicode=true&characterEncoding=utf-8" +
			"&autoReconnect=false" +                    // 自动重连
			"&connectTimeout=20000" +                   // 连接超时时间(毫秒)
			"&socketTimeout=60000" +                   // Socket超时时间(毫秒)
			"&useSSL=false" +                          // 禁用SSL(如果不需要)
			"&allowPublicKeyRetrieval=true" +          // 允许获取公钥
			"&serverTimezone=Asia/Shanghai" +          // 设置时区 
			"&rewriteBatchedStatements=true" +         // 批处理优化
			"&cachePrepStmts=true" +                   // 缓存预编译语句
			"&prepStmtCacheSize=250" +                 // 预编译语句缓存大小
			"&prepStmtCacheSqlLimit=2048" +            // 预编译语句SQL长度限制
			"&useServerPrepStmts=true" +               // 使用服务器端预编译
			"&maintainTimeStats=false";                // 禁用时间统计(性能优化)

	public static String USER_JH = "yfdata_2025"; 
	public static String PASSWD_JH = "yfdata_2025_new**A";
}
