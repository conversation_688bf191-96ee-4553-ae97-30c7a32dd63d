package com.career.utils;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DealWithZJW {
	
	public static void main(String args[]) throws Exception{
		
		dealWithOrg();
		dealWith2();
	}
	
	public static void dealWithOrg() throws Exception{
		StringBuffer sb = new StringBuffer();
		StringBuffer sb22 = new StringBuffer();
		for(int i=1;i<=582;i++) {//1574
			BufferedReader bw = new BufferedReader(new FileReader(new File("E://danzhao2024//testZHIJIAOPage_"+i+".txt")));
			String str = null;
			
			String schoolName = "",schoolTag = "", schoolAddress = "",schoolDesc = "", schoolMajor = "" ;
			
			while((str = bw.readLine()) != null) {
				
				if(str.indexOf("四川职教_学校查询</title>") > 0) {
					//System.out.println(i + " - NO");
					continue;
				}
				if(str.indexOf("<title>四川职教_") != -1) {
					schoolName = str.substring(str.indexOf("<title>四川职教_")+12, str.indexOf("_学校详情</title>"));
				}
				
				if(str.indexOf("<dl class=\"address\">") != -1) {
					
					String line = null;
					StringBuffer tempSb = new StringBuffer();
					tempSb.append(str.substring(str.indexOf("<dl class=\"address\">")));
					while((line = bw.readLine()) != null) {
						if(line.indexOf("<!-- 学校介绍 -->") != -1) {
							break;
						}
						
						if(!Tools.isEmpty(Tools.trim(line))) {
							tempSb.append(line);
						}
					}
					schoolAddress = tempSb.toString();
				}
				
				if(str.indexOf("<div class=\"big show6\" style=\"display: none; margin-bottom: 300px\">") != -1) {
					String line = null;
					StringBuffer tempSb = new StringBuffer();
					while((line = bw.readLine()) != null) {
						if(line.indexOf("<!-- 版心 -->") != -1) {
							break;
						}

						
						if(!Tools.isEmpty(Tools.trim(line))) {
							tempSb.append(line);
						}
					}
					schoolDesc = tempSb.toString();
				}
				
				if(str.indexOf("<div class=\"tags clearfix\">") != -1) {
					String line = null;
					StringBuffer tempSb = new StringBuffer();
					while((line = bw.readLine()) != null) {
						if(line.indexOf("</div>") != -1) {
							break;
						}
						
						if(line.indexOf("<span></span>") != -1) {
							continue;
						}
						
						if(!Tools.isEmpty(Tools.trim(line))) {
							tempSb.append(line);
						}
					}
					schoolTag = tempSb.toString();
				}
				
				
				if(str.indexOf("<div class=\"school-item-bar clearfix\"><i></i><span class=\"name\">开设专业</span></div>") != -1) {
					String line = null;
					StringBuffer tempSb = new StringBuffer();
					while((line = bw.readLine()) != null) {
						if(line.indexOf("<!-- 开设专业 -->") != -1) {
							break;
						}
						
						if(!Tools.isEmpty(Tools.trim(line))) {
							tempSb.append(line);
						}
					}
					schoolMajor = tempSb.toString();
				}
				
				
				if(str.indexOf("<!-- 版权归属 -->") != -1) {
					
					if(Tools.isEmpty(schoolName)) {
						continue;
					}
					
					sb.append("INSERT INTO s5_sc_dz_school(SID, YXMC, YX_TAGS, YX_DESC, YX_ADDR, YX_MAJOR) values("+i+",'"+schoolName+"','"+schoolTag+"','"+schoolDesc+"','"+schoolAddress+"','"+schoolMajor+"'); \r\n");
					
					sb22.append("<schoolID>"+i+"</schoolID>\r\n<schoolName>"+schoolName+"</schoolName>\r\n<schoolTag>\r\n"+schoolTag+"\r\n</schoolTag>\r\n<schoolDesc>\r\n"+schoolDesc+"\r\n</schoolDesc>\r\n<schoolAddress>\r\n"+schoolAddress+"\r\n</schoolAddress>\r\n<schoolMajor>\r\n"+schoolMajor+"\r\n</schoolMajor>\r\n<HHHHRRR>\r\n");
					
					schoolName = "";
					schoolTag = "";
					schoolAddress = "";
					schoolDesc = "" ;
					schoolMajor = "";
				}
				
				
				
			}
		}
		
		sb22.append("<ENDENDENDENDEND>");
		writeTempFile(new File("E://danzhao2024//DZschoolinfoHTML.txt"), sb22);
	}
	
		
		public static void dealWith2() throws Exception{
			StringBuffer sb = new StringBuffer();
			StringBuffer sbMajor = new StringBuffer();
			StringBuffer sbTag = new StringBuffer();
			StringBuffer sbDuikou = new StringBuffer();
			
			BufferedReader bw = new BufferedReader(new FileReader(new File("E://danzhao2024//DZschoolinfoHTML.txt")));
			String schoolID = "", schoolName = "", schoolTag = "", schoolAddress = "", schoolWebsite = "", schoolPhone = "", schoolDuikou = "", schoolDesc = "", schoolMajor = "" ;
			String str = null;
			while((str = bw.readLine()) != null) {
				if(str.indexOf("<schoolID>") != -1) {
					schoolID = str.substring(str.indexOf("<schoolID>") + 10, str.indexOf("</schoolID>"));
				}
				
				if(str.indexOf("<schoolName>") != -1) {
					schoolName = str.substring(str.indexOf("<schoolName>") + 12, str.indexOf("</schoolName>"));
				}
				
				if(str.indexOf("<schoolDesc>") != -1) {
					String line = null;
					StringBuffer tempSb = new StringBuffer();
					while((line = bw.readLine()) != null) {
						if(line.indexOf("</schoolDesc>") != -1) {
							break;
						}
						
						if(!Tools.isEmpty(Tools.trim(line))) {
							tempSb.append(line);
						}
					}
					schoolDesc = tempSb.toString();
				}
				
				if(str.indexOf("<schoolTag>") != -1) {
					str = bw.readLine();
					str = str.replace("<span>", ",");
					str = str.replace("</span>", ",");
					
					String[] xx = str.split(",");
					if(xx.length > 0) {
						for(int k=0;k<xx.length;k++) {
							if(!Tools.isEmpty(xx[k])) {
								schoolTag += xx[k].trim() + (k == (xx.length - 1) ? "":",");
							}
							
						}
					}
					
					if(!Tools.isEmpty(schoolTag)) {
						String[] schoolTagXX = schoolTag.split(",");
						for(String xxx : schoolTagXX) {
							sbTag.append("INSERT INTO s5_sc_school_tag(YXMC, TAG) VALUES('"+schoolName+"','"+xxx+"');\r\n");
						}
					}
				}
				
				if(str.indexOf("<schoolAddress>") != -1) {
					str = bw.readLine();
					try {
						int newIndex = str.indexOf("<dd>学校地址");
						String temp = str.substring(newIndex);
						schoolAddress = temp.substring(9, temp.indexOf("</dd>"));
					}catch(Exception ex) {}
					try {
						int newIndex = str.indexOf("<dd>学校官网");
						String temp = str.substring(newIndex);
						schoolWebsite = temp.substring(9, temp.indexOf("</dd>"));
					}catch(Exception ex) {}
					try {
						int newIndex = str.indexOf("<dd>官方电话");
						String temp = str.substring(newIndex);
						schoolPhone = temp.substring(9, temp.indexOf("</dd>"));
					}catch(Exception ex) {}
					try {
						int newIndex = str.indexOf("专升本对口院校");
						String temp = str.substring(newIndex);
						schoolDuikou = temp.substring(8, temp.indexOf("</dd>"));
						
						
						if(!Tools.isEmpty(schoolDuikou)) {
							String[] schoolTagXX = schoolDuikou.split(" ");
							for(String xxx : schoolTagXX) {
								sbDuikou.append("INSERT INTO s5_sc_school_duikou(YXMC, R_YXMC, D_TYPE) VALUES('"+schoolName+"','"+xxx+"',1);\r\n");
							}
						}
						
					}catch(Exception ex) {}
				}
				
				if(str.indexOf("<schoolMajor>") != -1) {
					String line = null;
					StringBuffer tempSb = new StringBuffer();
					while((line = bw.readLine()) != null) {
						if(line.indexOf("</schoolMajor>") != -1) {
							break;
						}
						
						if(!Tools.isEmpty(Tools.trim(line))) {
							tempSb.append(line.substring(620, line.length() - 90).trim());
						}
					}
					
					
					String tempSbxx = Tools.trim(tempSb.toString());
					schoolMajor = tempSbxx;
					
					if(!Tools.isEmpty(tempSbxx)) {
						while(true) {
							if(tempSbxx.indexOf("<td>") != -1) {
								tempSbxx = tempSbxx.substring(tempSbxx.indexOf("<td>"));
								int DALEI_END = tempSbxx.indexOf("</td>");
								String DALEI_str = tempSbxx.substring(4, DALEI_END);
								tempSbxx = tempSbxx.substring(DALEI_END);
								
								int ZHUANYE_START = tempSbxx.indexOf("<td>");
								tempSbxx = tempSbxx.substring(ZHUANYE_START);
								
								int ZHUANYE_END = tempSbxx.indexOf("</td>");
								String ZHUANYE_str = tempSbxx.substring(4, ZHUANYE_END);
								
								String[] ZHUANYE_strChar = ZHUANYE_str.split("、");
								for(String xxx : ZHUANYE_strChar) {
									sbMajor.append("INSERT INTO s5_sc_dz_school_major(YXMC, ZYL, ZYMC,ZYMCS) VALUES('"+schoolName+"','"+DALEI_str+"','"+xxx+"','"+ZHUANYE_str+"');\r\n");
								}
								
								tempSbxx = tempSbxx.substring(ZHUANYE_END);
								
							}else {
								break;
							}
						}
					}
					
					
				}
				
				
				if(str.indexOf("<HHHHRRR>") != -1) {
					
					if(Tools.isEmpty(schoolName)) {
						continue;
					}
					
					sb.append("INSERT INTO s5_sc_dz_school(SID, YXMC, YX_TAGS, YX_DESC, YX_ADDR, YX_WEBSITE, YX_PHONE, YX_DUIKOU) values("+schoolID+",'"+schoolName+"','"+schoolTag+"','"+removeTag(schoolDesc)+"','"+schoolAddress+"', '"+schoolWebsite+"', '"+schoolPhone+"', '"+schoolDuikou+"'); \r\n");
					
					if(Integer.parseInt(schoolID) < 1000) {
						System.out.println(schoolID + "," + schoolName+" - " );
					}
					schoolID = "";
					schoolName = "";
					schoolTag = "";
					schoolAddress = "";
					schoolDesc = "" ;
					schoolMajor = "";
					schoolWebsite = "";
					schoolPhone = "";
					schoolDuikou = "";
				}
				
			}
			
			//writeTempFile(new File("E://danzhao2024//DZschoolinfoSQL.txt"), sb);
			//writeTempFile(new File("E://danzhao2024//DZschoolinfoMajorSQL.txt"), sbMajor);
			writeTempFile(new File("E://danzhao2024//DZschoolinfoTagSQL.txt"), sbTag);
			writeTempFile(new File("E://danzhao2024//DZschoolinfoDuikouSQL.txt"), sbDuikou);
		}
	
	private static void writeTempFile(File file, StringBuffer sb) {
	    try {
	      BufferedWriter bw = new BufferedWriter(new FileWriter(file));
	      bw.write(sb.toString());
	      bw.flush();
	      bw.close();
	    } catch (IOException e) {
	      e.printStackTrace();
	    } 
	  }
	
	private static String removeTag(String html) {
        
        // 定义要匹配的正则表达式模式
        Pattern pattern = Pattern.compile("<[^>]+>", Pattern.CASE_INSENSITIVE);
        
        // 创建 Matcher 对象并进行匹配操作
        Matcher matcher = pattern.matcher(html);
        
        // 将匹配到的 HTML 标签替换为空字符串
        String result = matcher.replaceAll("").replaceAll("&nbsp;", "");
        
        return result.trim();
	}

}
