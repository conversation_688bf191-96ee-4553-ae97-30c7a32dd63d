package com.career.db;

import java.math.BigDecimal;
import java.util.Date;

public class WecomProductInfo {
	private String product_id; // 产品ID
    private String product_name; // 产品名称
    private String product_code; // 产品代码
    private String product_category; // 产品类别
    private BigDecimal product_price; // 产品价格
    private Integer product_stock; // 库存数量
    private String product_description; // 产品描述
    private Integer status; // 产品状态（如：1在售、2下架等）
    private Date create_tm; // 产品创建日期
    private Date update_tm; // 产品更新日期
	public String getProduct_id() {
		return product_id;
	}
	public void setProduct_id(String product_id) {
		this.product_id = product_id;
	}
	public String getProduct_name() {
		return product_name;
	}
	public void setProduct_name(String product_name) {
		this.product_name = product_name;
	}
	public String getProduct_code() {
		return product_code;
	}
	public void setProduct_code(String product_code) {
		this.product_code = product_code;
	}
	public String getProduct_category() {
		return product_category;
	}
	public void setProduct_category(String product_category) {
		this.product_category = product_category;
	}
	public BigDecimal getProduct_price() {
		return product_price;
	}
	public void setProduct_price(BigDecimal product_price) {
		this.product_price = product_price;
	}
	public Integer getProduct_stock() {
		return product_stock;
	}
	public void setProduct_stock(Integer product_stock) {
		this.product_stock = product_stock;
	}
	public String getProduct_description() {
		return product_description;
	}
	public void setProduct_description(String product_description) {
		this.product_description = product_description;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public Date getCreate_tm() {
		return create_tm;
	}
	public void setCreate_tm(Date create_tm) {
		this.create_tm = create_tm;
	}
	public Date getUpdate_tm() {
		return update_tm;
	}
	public void setUpdate_tm(Date update_tm) {
		this.update_tm = update_tm;
	}
    
    
}
