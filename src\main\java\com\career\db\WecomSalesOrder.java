package com.career.db;

import java.util.Date;

public class WecomSalesOrder {

	private String order_id;
    private Date create_tm;
    private String staff_id;
    private String customer_id;
    private String operate_staff_id;
    private String product_id;
    private double order_price;
    private String order_remark;
    
    private WecomProductInfo ext_WecomProductInfo;
    private WecomSalesCustomer ext_WecomSalesCustomer;
    
	public WecomSalesCustomer getExt_WecomSalesCustomer() {
		return ext_WecomSalesCustomer;
	}
	public void setExt_WecomSalesCustomer(WecomSalesCustomer ext_WecomSalesCustomer) {
		this.ext_WecomSalesCustomer = ext_WecomSalesCustomer;
	}
	public WecomProductInfo getExt_WecomProductInfo() {
		return ext_WecomProductInfo;
	}
	public void setExt_WecomProductInfo(WecomProductInfo ext_WecomProductInfo) {
		this.ext_WecomProductInfo = ext_WecomProductInfo;
	}
	public String getOrder_id() {
		return order_id;
	}
	public void setOrder_id(String order_id) {
		this.order_id = order_id;
	}
	public Date getCreate_tm() {
		return create_tm;
	}
	public void setCreate_tm(Date create_tm) {
		this.create_tm = create_tm;
	}
	public String getStaff_id() {
		return staff_id;
	}
	public void setStaff_id(String staff_id) {
		this.staff_id = staff_id;
	}
	public String getCustomer_id() {
		return customer_id;
	}
	public void setCustomer_id(String customer_id) {
		this.customer_id = customer_id;
	}
	public String getOperate_staff_id() {
		return operate_staff_id;
	}
	public void setOperate_staff_id(String operate_staff_id) {
		this.operate_staff_id = operate_staff_id;
	}
	public String getProduct_id() {
		return product_id;
	}
	public void setProduct_id(String product_id) {
		this.product_id = product_id;
	}
	public double getOrder_price() {
		return order_price;
	}
	public void setOrder_price(double order_price) {
		this.order_price = order_price;
	}
	public String getOrder_remark() {
		return order_remark;
	}
	public void setOrder_remark(String order_remark) {
		this.order_remark = order_remark;
	}
    
    
}
