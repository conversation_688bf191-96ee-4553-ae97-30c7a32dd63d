<%@page import="com.zsdwf.servlet.RunOnceThread"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page import="com.career.utils.*,com.career.db.*,java.util.*" %>
<%@include file="/WEB-INF/include/_session_admin.jsp" %>
<script src="<%=request.getContextPath()%>/sources/jquery-3.6.4.min.js"></script>

<div style="margin:20px;">
<a href="<%=request.getContextPath()%>/admin/refresh_autorun_data_SERVER.jsp" style="margin:10px;">恢复无人直播CASE状态</a>
<a href="<%=request.getContextPath()%>/admin/reload_offcn_data_SERVER.jsp" style="margin:10px;">重启OFFCN进程</a>
<a href="<%=request.getContextPath()%>/admin/clear_cache_SERVER.jsp" style="margin:10px;">更新诊断批次缓存</a>
</div>

<div style="margin:20px;">
你的账号ID（大写）:<input type="text" id="cid" >
省份：<input type="text" id="sf">
城市：<input type="text" id="city">
诊断批次：<input type="text" id="zdpc">
选科：<input type="text" id="xk">
成绩：<input type="text" id="score">

<input type="button" value="插队" onclick="MM_join();">
</div>

<script>
function MM_join(){
	var cid = $("#cid").val();
	if(cid == ""){
		alert("请输入");
		$("#cid").focus();
		return false;
	}
	
	var sf = $("#sf").val();
	if(sf == ""){
		alert("请输入");
		$("#sf").focus();
		return false;
	}
	
	var city = $("#city").val();
	if(city == ""){
		alert("请输入");
		$("#city").focus();
		return false;
	}
	
	var zdpc = $("#zdpc").val();
	if(zdpc == ""){
		alert("请输入");
		$("#zdpc").focus();
		return false;
	}
	
	var xk = $("#xk").val();
	if(xk == ""){
		alert("请输入");
		$("#xk").focus();
		return false;
	}
	
	var score = $("#score").val();
	if(score == ""){
		alert("请输入");
		$("#score").focus();
		return false;
	}
	
	$.ajax({
        type : "POST",
        url : "<%=request.getContextPath()%>/admin/join_autorun_data_SERVER.jsp",
		data: {"cid" : cid, "sf" : sf, "city" : city, "zdpc" : zdpc, "xk" : xk, "score" : score}, 
		//dataType: "json",
        //请求成功
        success : function(result) {
        	if(result.indexOf("ERR:LOGIN:REDIRECT-X_LOGIN") != -1){
				location.href = "<%=request.getContextPath()%>/admin/login.jsp";
        	}else{
        		alert("OK");
        	}
        },
        //请求失败，包含具体的错误信息
        error : function(e){
            console.log(e.status);
            console.log(e.responseText);
        }
    });
}
</script>