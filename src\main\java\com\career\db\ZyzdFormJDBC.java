package com.career.db;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;

import com.career.db.career.MajorPlan;
import com.career.utils.ResultVO;
import com.career.utils.SQLLogUtils;
import com.career.utils.Tools;
import com.zsdwf.db.YGPaymentBean;


public class ZyzdFormJDBC {
	
	static String URL = JDBCConstants.URL;
	static String USER = JDBCConstants.USER;
	static String PASSWD = JDBCConstants.PASSWD;
	public static int PAGE_ROW_CNT = 20;
	public static int PAGE_ROW_CNT_SPEC = 15;
	
	static {
		try {
			Class.forName("com.mysql.cj.jdbc.Driver");
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
	
	public static JHxBean setJHxBeanAll(int dataYear, ResultSet rs) throws Exception {
	    JHxBean bean = new JHxBean();
	    bean.setId(rs.getInt("id"));
	    bean.setSf(rs.getString("sf"));
	    bean.setNf(rs.getInt("nf"));
	    bean.setXk(rs.getString("xk"));
	    bean.setZx(rs.getString("zx"));
	    bean.setPc(rs.getString("pc"));
	    bean.setPc_code(rs.getString("pc_code"));
	    bean.setPc_type(rs.getString("pc_type"));
	    bean.setPc_desc(rs.getString("pc_desc"));
	    bean.setYxdm(rs.getString("yxdm"));
	    bean.setYxmc(rs.getString("yxmc"));
	    bean.setYxmc_org(rs.getString("yxmc_org"));
		bean.setYxbz(rs.getString("yxbz"));
	    bean.setZyz(rs.getString("zyz"));
	    bean.setZys(rs.getInt("zys"));
	    bean.setZnzyls(rs.getInt("znzyls"));
	    bean.setZnzy(rs.getString("znzy"));
	    bean.setZyzjhs(rs.getString("zyzjhs"));
	    bean.setZyzzdf(rs.getString("zyzzdf"));
	    bean.setZyzzdfwc(rs.getString("zyzzdfwc"));
	    bean.setZdf_a(rs.getString("zdf_" + dataYear));
	    bean.setZdf_b(rs.getString("zdf_" + (dataYear - 1)));
	    bean.setZdf_c(rs.getString("zdf_" + (dataYear - 2)));
	    bean.setZdfwc_a(rs.getString("zdfwc_" + dataYear));
	    bean.setZdfwc_b(rs.getString("zdfwc_" + (dataYear - 1)));
	    bean.setZdfwc_c(rs.getString("zdfwc_" + (dataYear - 2)));
	    bean.setPjf_a(rs.getString("pjf_" + dataYear));
	    bean.setPjf_b(rs.getString("pjf_" + (dataYear - 1)));
	    bean.setPjf_c(rs.getString("pjf_" + (dataYear - 2)));
	    bean.setPjfwc_a(rs.getString("pjfwc_" + dataYear));
	    bean.setPjfwc_b(rs.getString("pjfwc_" + (dataYear - 1)));
	    bean.setPjfwc_c(rs.getString("pjfwc_" + (dataYear - 2)));
	    bean.setJhs_a(rs.getString("jhs_" + dataYear));
	    bean.setJhs_b(rs.getString("jhs_" + (dataYear - 1)));
	    bean.setJhs_c(rs.getString("jhs_" + (dataYear - 2)));
	    bean.setLqrs_a(rs.getString("lqrs_" + dataYear));
	    bean.setLqrs_b(rs.getString("lqrs_" + (dataYear - 1)));
	    bean.setLqrs_c(rs.getString("lqrs_" + (dataYear - 2)));
	    bean.setYxsf(rs.getString("yxsf"));
	    bean.setYxcs(rs.getString("yxcs"));
	    bean.setInd_catg(rs.getString("ind_catg"));
	    bean.setInd_nature(rs.getString("ind_nature"));
	    bean.setYx_tags(rs.getString("yx_tags"));
	    bean.setCnt_company(rs.getInt("cnt_company"));
	    bean.setCnt_employ(rs.getInt("cnt_employ"));
	    bean.setCnt_grad(rs.getFloat("cnt_grad"));
	    bean.setIs_hz(rs.getInt("is_hz"));
	    bean.setLqpc(rs.getString("lqpc"));
	    bean.setYx_tags_all(rs.getString("yx_tags_all"));
	    return bean;
	}
	
	public static JHBean setJHBeanAll(int dataYear, ResultSet rs) throws Exception {
		JHBean bean = new JHBean();
		bean.setId(rs.getInt("id"));
		bean.setSf(rs.getString("sf"));
		bean.setNf(rs.getInt("nf"));
		bean.setXk(rs.getString("xk"));
		bean.setZx(rs.getString("zx"));
		bean.setPc(rs.getString("pc"));
		bean.setPc_code(rs.getString("pc_code"));
		bean.setPc_type(rs.getString("pc_type"));
	    bean.setPc_desc(rs.getString("pc_desc"));
		bean.setYxdm(rs.getString("yxdm"));
		bean.setYxmc(rs.getString("yxmc"));
		bean.setYxmc_org(rs.getString("yxmc_org"));
		bean.setYxbz(rs.getString("yxbz"));
		bean.setZydm(rs.getString("zydm"));
		bean.setZymc(rs.getString("zymc"));
		bean.setZymc_org(rs.getString("zymc_org"));
		bean.setZyz(rs.getString("zyz"));
		bean.setZybz(rs.getString("zybz"));
		bean.setJhs(rs.getString("jhs"));
		bean.setXz(rs.getString("xz"));
		bean.setFee(rs.getString("fee"));
		bean.setJhs_a(rs.getString("jhs_"+dataYear));
		bean.setJhs_b(rs.getString("jhs_"+(dataYear-1)));
		bean.setJhs_c(rs.getString("jhs_"+(dataYear-2)));
		bean.setLqrs_a(rs.getString("lqrs_"+dataYear));
		bean.setLqrs_b(rs.getString("lqrs_"+(dataYear-1)));
		bean.setLqrs_c(rs.getString("lqrs_"+(dataYear-2)));
		bean.setXz_a(rs.getString("xz_"+dataYear));
		bean.setFee_a(rs.getString("fee_"+dataYear));
		bean.setZdf_a(rs.getString("zdf_"+dataYear));
		bean.setZdf_b(rs.getString("zdf_"+(dataYear-1)));
		bean.setZdf_c(rs.getString("zdf_"+(dataYear-2)));
		bean.setZdfwc_a(rs.getString("zdfwc_"+dataYear));
		bean.setZdfwc_b(rs.getString("zdfwc_"+(dataYear-1)));
		bean.setZdfwc_c(rs.getString("zdfwc_"+(dataYear-2)));
		bean.setZgf_a(rs.getString("zgf_"+dataYear));
		bean.setZgf_b(rs.getString("zgf_"+(dataYear-1)));
		bean.setZgf_c(rs.getString("zgf_"+(dataYear-2)));
		bean.setZgfwc_a(rs.getString("zgfwc_"+dataYear));
		bean.setZgfwc_b(rs.getString("zgfwc_"+(dataYear-1)));
		bean.setZgfwc_c(rs.getString("zgfwc_"+(dataYear-2)));
		bean.setPjf_a(rs.getString("pjf_"+dataYear));
		bean.setPjf_b(rs.getString("pjf_"+(dataYear-1)));
		bean.setPjf_c(rs.getString("pjf_"+(dataYear-2)));
		bean.setPjfwc_a(rs.getString("pjfwc_"+dataYear));
		bean.setPjfwc_b(rs.getString("pjfwc_"+(dataYear-1)));
		bean.setPjfwc_c(rs.getString("pjfwc_"+(dataYear-2)));
		
		bean.setYxsf(rs.getString("yxsf"));
		bean.setYxcs(rs.getString("yxcs"));
		bean.setZyml(rs.getString("zyml"));
		bean.setInd_catg(rs.getString("ind_catg"));
		bean.setInd_nature(rs.getString("ind_nature"));
		bean.setYx_tags(rs.getString("yx_tags"));
		bean.setCnt_company(rs.getInt("cnt_company"));
		bean.setCnt_employ(rs.getInt("cnt_employ"));
		bean.setCnt_grad(rs.getFloat("cnt_grad"));
		bean.setLqpc(rs.getString("lqpc"));
		bean.setZdf(rs.getInt("zdf"));
		bean.setZdfwc(rs.getInt("zdfwc"));
		bean.setIs_hz(rs.getInt("is_hz"));
		bean.setIs_first(rs.getInt("is_first"));
		bean.setZnzyls(rs.getInt("znzyls"));
		bean.setZnzys(rs.getInt("znzys"));
		bean.setZnjhs(rs.getInt("znjhs")); 
		bean.setYcwc(rs.getInt("ycwc"));
		bean.setYx_tags_all(rs.getString("yx_tags_all"));

		bean.setQsf_a(rs.getInt("qsf_a"));
		bean.setQsf_b(rs.getInt("qsf_b"));
		bean.setQsf_c(rs.getInt("qsf_c"));
		bean.setQsf(rs.getInt("qsf"));
		return bean;
	}
	
	public ResultVO searchZyResultForForm(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> zyml, HashSet<String> univSf, String univNature,String key_words, String tag, int wcFrom, int wcTo, int orderPriority, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = jhYear - 1;
		ResultVO resultVO = new ResultVO();
		List<JHBean> list = new ArrayList<>();
		int recordCnt = 0;
		try {
			String zymlInSQL = zyml.size() == 0 ? "" : "and x.zyml in ("+Tools.getSQLQueryin(zyml)+")";
			String yxsfInSQl = univSf.size() == 0 ? "" : "and x.yxsf in ("+Tools.getSQLQueryin(univSf)+")";
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.pc = ? and x.pc_code = ? and x.ind_nature like ? "+yxsfInSQl+" and x.xk_code like ? "+ zymlInSQL + " and x.yx_tags like ? and (x.yxmc like ? or x.zymc like ?) and x.zdfwc between ? and ? ";
			String SELECT_CNT = "SELECT COUNT(*) as cnt FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.pc = ? and x.pc_code = ? and x.ind_nature like ? "+yxsfInSQl+" and x.xk_code like ? "+ zymlInSQL + " and x.yx_tags like ? and (x.yxmc like ? or x.zymc like ?) and x.zdfwc between ? and ? ";
			String ORDER_CONDITION = " ORDER BY x.zdfwc ASC";
			if(orderPriority == 2) {
				ORDER_CONDITION = " ORDER BY x.cnt_employ DESC";
			}else if(orderPriority == 3) {
				ORDER_CONDITION = " ORDER BY x.cnt_grad DESC";
			}else {
				
			}
			String LIMIT_CONDITION = " limit ?,?";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION + LIMIT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION + LIMIT_CONDITION);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%" + univNature + "%");
			ps.setString(4, "%" + xkCode + "%");
			ps.setString(5, "%" + tag + "%");
			ps.setString(6, "%" + key_words + "%");
			ps.setString(7, "%" + key_words + "%");
			ps.setInt(8, wcFrom);
			ps.setInt(9, wcTo);
			ps.setInt(10, (pageNumber - 1) * PAGE_ROW_CNT); // 1开始
			ps.setInt(11, PAGE_ROW_CNT);

			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setJHBeanAll(dataYear, rs));
			}

			ps.close();
			ps = null;
			rs.close();
			rs = null;
			ps = conn.prepareStatement(SELECT_CNT);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%" + univNature + "%");
			ps.setString(4, "%" + xkCode + "%");
			ps.setString(5, "%" + tag + "%");
			ps.setString(6, "%" + key_words + "%");
			ps.setString(7, "%" + key_words + "%");
			ps.setInt(8, wcFrom);
			ps.setInt(9, wcTo);

			rs = ps.executeQuery();
			while (rs.next()) {
				recordCnt = rs.getInt("cnt");
			}
			
			resultVO.setCurrentPage(pageNumber);
			resultVO.setRecordCnt(recordCnt);
			resultVO.setResult(list);
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return resultVO;
	}
	
	public ResultVO searchHyResultForForm(String sfTable, int jhYear,String xk_code, String pc, String pc_code, HashSet<String> vocOneSets, HashSet<String> sfSets, String key_words, String tag, int wcfrom, int wcto, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = jhYear - 1;
		ResultVO resultVO = new ResultVO();
		int recordCnt = 0;
		List<SchoolMarjorCommonBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String yxsfInSQl = sfSets.size() == 0 ? "" : "and y.yxsf in ("+Tools.getSQLQueryin(sfSets)+")";
			String SQL_LIST = "SELECT x.yxmc, MIN(y.zdf) as zdf, MAX(y.zdfwc) as zdfwc, COUNT(*) as cnt from career_jy_all_2024 x,"+sfTable+"_jh_" + jhYear + " y,zyzd_career_vocation z WHERE y.pc = ? and y.pc_code = ? and (y.zdfwc between ? and ?) and y.xk_code like ? and (y.yxmc like ?) "+yxsfInSQl+" and y.yx_tags like ? and x.yxmc = y.yxmc_org and x.group_name = z.v_name and z.p_name IN ("+Tools.getSQLQueryin(vocOneSets)+") GROUP BY x.yxmc ";
			String SQL_CNT = "SELECT COUNT(*) as cnt from (SELECT count(distinct x.yxmc) as cnt from career_jy_all_2024 x,"+sfTable+"_jh_" + jhYear + " y,zyzd_career_vocation z WHERE y.pc = ? and y.pc_code = ? and (y.zdfwc between ? and ?) and y.xk_code like ? and (y.yxmc like ?) "+yxsfInSQl+" and y.yx_tags like ? and x.yxmc = y.yxmc_org and x.group_name = z.v_name and z.p_name IN ("+Tools.getSQLQueryin(vocOneSets)+") GROUP BY x.yxmc ) AS tmp ";
			String SQL_ORDER = " ORDER BY COUNT(*) DESC LIMIT ?,?";
			Tools.println(SQL_LIST + SQL_ORDER);
			ps = conn.prepareStatement(SQL_LIST + SQL_ORDER);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setInt(3, wcfrom);
			ps.setInt(4, wcto);
			ps.setString(5, "%" + xk_code + "%");
			ps.setString(6, "%" + key_words + "%");
			ps.setString(7, "%" + tag + "%");
			ps.setInt(8, (pageNumber - 1) * PAGE_ROW_CNT);
			ps.setInt(9, PAGE_ROW_CNT);
			rs = ps.executeQuery();
			SchoolMarjorCommonBean bean = null;
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc"));
				bean.setZdf(rs.getString("zdf"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setCnt_employ(rs.getInt("cnt"));
				list.add(bean);
			}
			
			ps.close();
			ps = null;
			rs.close();
			rs = null;
			ps = conn.prepareStatement(SQL_CNT);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setInt(3, wcfrom);
			ps.setInt(4, wcfrom);
			ps.setString(5, "%" + xk_code + "%");
			ps.setString(6, "%" + key_words + "%");
			ps.setString(7, "%" + tag + "%");

			rs = ps.executeQuery();
			while (rs.next()) {
				recordCnt = rs.getInt("cnt");
			}
			
			resultVO.setCurrentPage(pageNumber);
			resultVO.setRecordCnt(recordCnt);
			resultVO.setResult(list);
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return resultVO;
	}
	
	public ResultVO searchYxResultForForm(String sfTable,int jhYear,String xk_code, String pc, String pc_code, String univName, HashSet<String> univNameSet, HashSet<String> univCatg, HashSet<String> univSf, String univNature, String tag, int orderPriority, int wcFrom, int wcTo, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = jhYear - 1;
		ResultVO resultVO = new ResultVO();
		int recordCnt = 0;
		List<ZyzdUniversityDataBean> list = new ArrayList<ZyzdUniversityDataBean>();
		try {
			
			conn = DatabaseUtils.getConnection();
			String andSQL = univNameSet.size() == 0 ? "" : "and yxmc_org in (" + Tools.getSQLQueryin(univNameSet) + ")";
			String andYXLXSQL = univCatg.size() == 0 ? "" : "and ind_catg in (" + Tools.getSQLQueryin(univCatg) + ")";
			String andYXSFSQL = univSf.size() == 0 ? "" : "and yxsf in (" + Tools.getSQLQueryin(univSf) + ")";
			String SELECT_CONDITION = "SELECT x.yxmc_org, min(x.zdf) as zdf, max(x.zdfwc) as zdfwc FROM "+sfTable+"_jh_" + jhYear + " x WHERE x.pc = ? and x.pc_code = ? and x.ind_nature like ? "+andYXLXSQL+" "+andYXSFSQL+" and x.xk_code like ? and (x.yxmc like ? " + andSQL + ") and x.yx_tags like ? and x.zdfwc between ? and ? group by x.yxmc_org ";
			String SELECT_CNT = "SELECT COUNT(*) as cnt FROM (SELECT yxmc_org FROM "+sfTable+"_jh_" + jhYear + " x WHERE x.pc = ? and x.pc_code = ? and x.ind_nature like ? "+andYXLXSQL+" "+andYXSFSQL+" and x.xk_code like ? and (x.yxmc like ? " + andSQL + ") and x.yx_tags like ? and x.zdfwc between ? and ? group by x.yxmc_org) as TMP ";
			String ORDER_CONDITION = " ORDER BY min(x.zdfwc) ASC";
			String LIMIT_CONDITION = " limit ?,?";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION + LIMIT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION + LIMIT_CONDITION);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%" + univNature + "%");
			ps.setString(4, "%" + xk_code + "%");
			ps.setString(5, "%" + univName + "%");
			ps.setString(6, "%" + tag + "%");
			ps.setInt(7, wcFrom);
			ps.setInt(8, wcTo);

			ps.setInt(9, (pageNumber - 1) * PAGE_ROW_CNT); // 1开始
			ps.setInt(10, PAGE_ROW_CNT);

			rs = ps.executeQuery();
			ZyzdUniversityDataBean sb = null; 
			while (rs.next()) {
				sb = new ZyzdUniversityDataBean();
				sb.setYxmc_org(rs.getString("yxmc_org"));
				sb.setZdf(rs.getString("zdf"));
				sb.setZdfwc(rs.getString("zdfwc"));
				list.add(sb);
			}
			
			ps.close();
			ps = null;
			rs.close();
			rs = null;
			ps = conn.prepareStatement(SELECT_CNT);
			Tools.println(SELECT_CNT);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%" + univNature + "%");
			ps.setString(4, "%" + xk_code + "%");
			ps.setString(5, "%" + univName + "%");
			ps.setString(6, "%" + tag + "%");
			ps.setInt(7, wcFrom);
			ps.setInt(8, wcTo);

			rs = ps.executeQuery();
			while (rs.next()) {
				recordCnt = rs.getInt("cnt");
			}
			
			resultVO.setCurrentPage(pageNumber);
			resultVO.setRecordCnt(recordCnt);
			resultVO.setResult(list);
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return resultVO;
	}
	
	public ResultVO searchYxResultForFormInLhy(String sfTable,int jhYear,String xk_code, String pc, String pc_code, String univName, HashSet<String> univNameSet, HashSet<String> univCatg, HashSet<String> univSf, String univNature, String tag, String yxzymc_keywords, int orderPriority, int scoreFromWC, int scoreToWC, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = jhYear - 1;
		ResultVO resultVO = new ResultVO();
		int recordCnt = 0;
		List<JHxBean> list = new ArrayList<JHxBean>();
		resultVO.setResult(list);
		try {
			
			conn = DatabaseUtils.getConnection();
			String andSQL = univNameSet.size() == 0 ? "" : "and yxmc_org in (" + Tools.getSQLQueryin(univNameSet) + ")";
			String andYXLXSQL = univCatg.size() == 0 ? "" : "and ind_catg in (" + Tools.getSQLQueryin(univCatg) + ")";
			String andYXSFSQL = univSf.size() == 0 ? "" : "and yxsf in (" + Tools.getSQLQueryin(univSf) + ")";
			String SELECT_CONDITION = "SELECT * FROM "+sfTable+"_jh_" + jhYear + "x x WHERE x.pc = ? and x.pc_code = ? and x.ind_nature like ? "+andYXLXSQL+" "+andYXSFSQL+" and x.xk_code like ? and (1 = 1 " + andSQL + ") and x.yx_tags like ? and (x.zyzzdfwc is NULL OR x.zyzzdfwc between ? and ? ) and (x.yxmc like ? OR x.znzy like ? OR yx_tag_all_for_search like ?) ";
			String SELECT_CNT = "SELECT count(*) as cnt FROM "+sfTable+"_jh_" + jhYear + "x x WHERE x.pc = ? and x.pc_code = ? and x.ind_nature like ? "+andYXLXSQL+" "+andYXSFSQL+" and x.xk_code like ? and (1 = 1 " + andSQL + ") and x.yx_tags like ? and (x.zyzzdfwc is NULL OR x.zyzzdfwc between ? and ?) and (x.yxmc like ? OR x.znzy like ? OR yx_tag_all_for_search like ?) ";
			String ORDER_CONDITION = " ORDER BY x.zyzzdfwc is NULL, cast(COALESCE(zyzzdfwc,99999999) as signed) ASC";
			String LIMIT_CONDITION = " limit ?,?";
			
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION + LIMIT_CONDITION); 
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%" + univNature + "%");
			ps.setString(4, "%" + xk_code + "%");
			ps.setString(5, "%" + tag + "%");
			ps.setInt(6, scoreFromWC);
			ps.setInt(7, scoreToWC);
			ps.setString(8, "%" + yxzymc_keywords + "%");
			ps.setString(9, "%" + yxzymc_keywords + "%");
			ps.setString(10, "%" + yxzymc_keywords + "%");

			ps.setInt(11, (pageNumber - 1) * PAGE_ROW_CNT); // 1开始
			ps.setInt(12, PAGE_ROW_CNT);

			SQLLogUtils.printSQL("searchYxResultForFormInLhy", ps);
			
			rs = ps.executeQuery();
			JHxBean bean = null; 
			while (rs.next()) {
				bean = new JHxBean();
				bean.setId(rs.getInt("id"));
				bean.setSf(rs.getString("sf"));
				bean.setNf(rs.getInt("nf"));
				bean.setXk(rs.getString("xk"));
				bean.setZx(rs.getString("zx"));
				bean.setPc(rs.getString("pc"));
				bean.setPc_code(rs.getString("pc_code"));
				bean.setYxdm(rs.getString("yxdm"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setZyz(rs.getString("zyz"));
				bean.setZys(rs.getInt("zys"));
				bean.setZnzy(rs.getString("znzy"));
				bean.setZyzjhs(rs.getString("zyzjhs"));
				bean.setZyzzdf(rs.getString("zyzzdf"));
				bean.setZyzzdfwc(rs.getString("zyzzdfwc"));
				bean.setZdf_a(rs.getString("zdf_"+dataYear));
				bean.setZdf_b(rs.getString("zdf_"+(dataYear-1)));
				bean.setZdf_c(rs.getString("zdf_"+(dataYear-2)));
				bean.setZdfwc_a(rs.getString("zdfwc_"+dataYear));
				bean.setZdfwc_b(rs.getString("zdfwc_"+(dataYear-1)));
				bean.setZdfwc_c(rs.getString("zdfwc_"+(dataYear-2)));
				bean.setPjf_a(rs.getString("pjf_"+dataYear));
				bean.setPjf_b(rs.getString("pjf_"+(dataYear-1)));
				bean.setPjf_c(rs.getString("pjf_"+(dataYear-2)));
				bean.setPjfwc_a(rs.getString("pjfwc_"+dataYear));
				bean.setPjfwc_b(rs.getString("pjfwc_"+(dataYear-1)));
				bean.setPjfwc_c(rs.getString("pjfwc_"+(dataYear-2)));
				bean.setYxsf(rs.getString("yxsf"));
				bean.setYxcs(rs.getString("yxcs"));
				bean.setInd_catg(rs.getString("ind_catg"));
				bean.setInd_nature(rs.getString("ind_nature"));
				bean.setYx_tags(rs.getString("yx_tags"));
				bean.setCnt_company(rs.getInt("cnt_company"));
				bean.setCnt_employ(rs.getInt("cnt_employ"));
				bean.setCnt_grad(rs.getFloat("cnt_grad"));
				bean.setLqpc(rs.getString("lqpc"));

				list.add(bean);
			}
			
			ps.close();
			ps = null;
			rs.close();
			rs = null;
			ps = conn.prepareStatement(SELECT_CNT);
			Tools.println(SELECT_CNT);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%" + univNature + "%");
			ps.setString(4, "%" + xk_code + "%");
			ps.setString(5, "%" + tag + "%");
			ps.setInt(6, scoreFromWC); 
			ps.setInt(7, scoreToWC);
			ps.setString(8, "%" + yxzymc_keywords + "%");
			ps.setString(9, "%" + yxzymc_keywords + "%");
			ps.setString(10, "%" + yxzymc_keywords + "%");

			rs = ps.executeQuery();
			while (rs.next()) {
				recordCnt = rs.getInt("cnt");
			}
			
			resultVO.setCurrentPage(pageNumber);
			resultVO.setRecordCnt(recordCnt);
			
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return resultVO;
	}
	
	
	
	
	public ResultVO searchFirstYxResultForFormInLhy(String sfTable,int jhYear,String xk_code, String pc, String pc_code, String univName, HashSet<String> univNameSet, HashSet<String> univCatg, HashSet<String> univSf, String univNature, String tag, String yxzymc_keywords, int orderPriority, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = jhYear - 1;
		ResultVO resultVO = new ResultVO();
		int recordCnt = 0;
		List<JHxBean> list = new ArrayList<JHxBean>();
		try {
			
			conn = DatabaseUtils.getConnection();
			String andSQL = univNameSet.size() == 0 ? "" : "and yxmc_org in (" + Tools.getSQLQueryin(univNameSet) + ")";
			String andYXLXSQL = univCatg.size() == 0 ? "" : "and ind_catg in (" + Tools.getSQLQueryin(univCatg) + ")";
			String andYXSFSQL = univSf.size() == 0 ? "" : "and yxsf in (" + Tools.getSQLQueryin(univSf) + ")";
			String SELECT_CONDITION = "SELECT * FROM "+sfTable+"_jh_" + jhYear + "x x WHERE x.pc = ? and x.pc_code = ? and x.ind_nature like ? "+andYXLXSQL+" "+andYXSFSQL+" and x.xk_code like ? and (1 = 1 " + andSQL + ") and x.yx_tags like ? and (x.zyzzdfwc is NULL) and (x.yxmc like ? OR x.znzy like ?)  ";
			String SELECT_CNT = "SELECT count(*) as cnt FROM "+sfTable+"_jh_" + jhYear + "x x WHERE x.pc = ? and x.pc_code = ? and x.ind_nature like ? "+andYXLXSQL+" "+andYXSFSQL+" and x.xk_code like ? and (1 = 1 " + andSQL + ") and x.yx_tags like ? and (x.zyzzdfwc is NULL) and (x.yxmc like ? OR x.znzy like ?)  ";
			String ORDER_CONDITION = " ORDER BY cast(zyzzdfwc as signed) ASC";
			String LIMIT_CONDITION = " limit ?,?";
			
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION + LIMIT_CONDITION); 
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%" + univNature + "%");
			ps.setString(4, "%" + xk_code + "%");
			ps.setString(5, "%" + tag + "%");
			ps.setString(6, "%" + yxzymc_keywords + "%");
			ps.setString(7, "%" + yxzymc_keywords + "%");
			ps.setInt(8, (pageNumber - 1) * PAGE_ROW_CNT); // 1开始
			ps.setInt(9, PAGE_ROW_CNT);

			SQLLogUtils.printSQL("searchFirstYxResultForFormInLhy", ps);
			
			rs = ps.executeQuery();
			JHxBean bean = null; 
			while (rs.next()) {
				bean = new JHxBean();
				bean.setId(rs.getInt("id"));
				bean.setSf(rs.getString("sf"));
				bean.setNf(rs.getInt("nf"));
				bean.setXk(rs.getString("xk"));
				bean.setZx(rs.getString("zx"));
				bean.setPc(rs.getString("pc"));
				bean.setPc_code(rs.getString("pc_code"));
				bean.setYxdm(rs.getString("yxdm"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setZyz(rs.getString("zyz"));
				bean.setZys(rs.getInt("zys"));
				bean.setZnzy(rs.getString("znzy"));
				bean.setZyzjhs(rs.getString("zyzjhs"));
				bean.setZyzzdf(rs.getString("zyzzdf"));
				bean.setZyzzdfwc(rs.getString("zyzzdfwc"));
				bean.setZdf_a(rs.getString("zdf_"+dataYear));
				bean.setZdf_b(rs.getString("zdf_"+(dataYear-1)));
				bean.setZdf_c(rs.getString("zdf_"+(dataYear-2)));
				bean.setZdfwc_a(rs.getString("zdfwc_"+dataYear));
				bean.setZdfwc_b(rs.getString("zdfwc_"+(dataYear-1)));
				bean.setZdfwc_c(rs.getString("zdfwc_"+(dataYear-2)));
				bean.setPjf_a(rs.getString("pjf_"+dataYear));
				bean.setPjf_b(rs.getString("pjf_"+(dataYear-1)));
				bean.setPjf_c(rs.getString("pjf_"+(dataYear-2)));
				bean.setPjfwc_a(rs.getString("pjfwc_"+dataYear));
				bean.setPjfwc_b(rs.getString("pjfwc_"+(dataYear-1)));
				bean.setPjfwc_c(rs.getString("pjfwc_"+(dataYear-2)));
				bean.setYxsf(rs.getString("yxsf"));
				bean.setYxcs(rs.getString("yxcs"));
				bean.setInd_catg(rs.getString("ind_catg"));
				bean.setInd_nature(rs.getString("ind_nature"));
				bean.setYx_tags(rs.getString("yx_tags"));
				bean.setCnt_company(rs.getInt("cnt_company"));
				bean.setCnt_employ(rs.getInt("cnt_employ"));
				bean.setCnt_grad(rs.getFloat("cnt_grad"));
				bean.setLqpc(rs.getString("lqpc"));

				list.add(bean);
			}
			
			ps.close();
			ps = null;
			rs.close();
			rs = null;
			ps = conn.prepareStatement(SELECT_CNT);
			Tools.println(SELECT_CNT);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%" + univNature + "%");
			ps.setString(4, "%" + xk_code + "%");
			ps.setString(5, "%" + tag + "%");
			ps.setString(6, "%" + yxzymc_keywords + "%");
			ps.setString(7, "%" + yxzymc_keywords + "%");

			rs = ps.executeQuery();
			while (rs.next()) {
				recordCnt = rs.getInt("cnt");
			}
			
			resultVO.setCurrentPage(pageNumber);
			resultVO.setRecordCnt(recordCnt);
			resultVO.setResult(list);
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return resultVO;
	}
	
	public List<ZyzdMajorRanking> getRuanKeRankingByYxmc(String yxmc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdMajorRanking> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_base_rank_rk_major WHERE yxmc = ? ORDER BY sort, ranking";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yxmc);
			rs = ps.executeQuery();
			ZyzdMajorRanking bean = null;
			while (rs.next()) {
				bean = new ZyzdMajorRanking();
				bean.setRanking(rs.getInt("ranking"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZyml(rs.getString("zyml"));
				bean.setLevel(rs.getString("ranking_level"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdMajorRanking> getRuanKeRankingByYxmcSets(HashSet<String> yxmcSets) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdMajorRanking> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			if(yxmcSets.size() == 0) {
				return list;
			}
			String SQL = "SELECT * FROM zyzd_base_rank_rk_major WHERE yxmc in ("+Tools.getSQLQueryin(yxmcSets)+") ORDER BY yxmc, sort, ranking";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			ZyzdMajorRanking bean = null;
			while (rs.next()) {
				bean = new ZyzdMajorRanking();
				bean.setRanking(rs.getInt("ranking"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZyml(rs.getString("zyml"));
				bean.setLevel(rs.getString("ranking_level"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JHBean> getJHByYxmcOrg(int jhYear, String sfCode, String xkCode, String pc, String pc_code, String yxmc_org) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		int dataYear = jhYear-1;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.xk_code like ? AND x.pc_code = ? AND x.yxmc_org = ? and x.pc = ? ORDER BY x.zdfwc ASC";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, "%" + xkCode + "%");
			ps.setString(2, pc_code); 
			ps.setString(3, yxmc_org);
			ps.setString(4, pc);

			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === getJHByYxmcOrg(): ", ps);
			
			JHBean bean = null;
			while (rs.next()) {
				list.add(setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JHBean> getJHByYxmcAndZyz(int jhYear, String sfCode, String xkCode, String pc, String pc_code, String yxmc, String zyz) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		int dataYear = jhYear-1;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.xk_code like ? AND x.pc_code = ? AND x.yxmc = ? and x.zyz = ? and x.pc = ? ORDER BY x.zdfwc ASC";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, "%" + xkCode + "%");
			ps.setString(2, pc_code); 
			ps.setString(3, yxmc);
			ps.setString(4, zyz);
			ps.setString(5, pc);

			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === getJHByYxmcOrg(): ", ps);
			
			while (rs.next()) {
				list.add(setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JHBean> getJHByYxmcOrgWithoutPC(int jhYear, String sfCode, String xkCode, String lqpc, String yxmc_org) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		int dataYear = jhYear-1;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.xk_code like ? AND x.yxmc_org = ? and x.lqpc = ? ORDER BY x.yxmc, x.zyz, x.zdf DESC";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, "%" + xkCode + "%");
			ps.setString(2, yxmc_org);
			ps.setString(3, lqpc);

			rs = ps.executeQuery();
			JHBean bean = null;
			while (rs.next()) {
				list.add(setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JHBean> getJHByYxmcOrgSets(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> yxmcOrgSets) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		int dataYear = jhYear-1;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.xk_code like ? AND x.pc_code = ? AND x.yxmc_org in ("+Tools.getSQLQueryin(yxmcOrgSets)+") and x.pc = ? ORDER BY x.yxmc, x.zyz, x.zdf DESC";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, "%" + xkCode + "%");
			ps.setString(2, pc_code);
			ps.setString(3, pc);

			rs = ps.executeQuery();
			JHBean bean = null;
			while (rs.next()) {
				list.add(setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JHBean> getJHByYxmcOrgWithoutPcFor96(int jhYear, String sfCode, String xkCode, String lqpc, String yxmc_org) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		int dataYear = jhYear-1;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.xk_code like ? AND x.yxmc_org = ? and x.lqpc = ? ORDER BY x.yxmc, x.zx, x.zdf DESC";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, "%" + xkCode + "%");
			ps.setString(2, yxmc_org);
			ps.setString(3, lqpc);

			rs = ps.executeQuery();
			JHBean bean = null;
			while (rs.next()) {
				list.add(setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JHBean> getJHByYxmcOrgFor96(int jhYear, String sfCode, String xkCode, String pc, String pc_code, String yxmc_org) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		int dataYear = jhYear-1;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.xk_code like ? AND x.pc_code = ? AND x.yxmc_org = ? and x.pc = ? ORDER BY x.zdfwc asc";
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, "%" + xkCode + "%");
			ps.setString(2, pc_code);
			ps.setString(3, yxmc_org);
			ps.setString(4, pc);

			rs = ps.executeQuery();
			SQLLogUtils.printSQL(">>>>>", ps);
			while (rs.next()) {
				list.add(setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JHBean> getJHBean(int jhYear, String sfCode, String xkCode, String pc, String pc_code, String yxdm, String zyz) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		int dataYear = jhYear-1;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.xk_code like ? AND x.pc_code = ? AND x.yxdm = ? and x.pc = ? and zyz = ? ";
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, "%" + xkCode + "%");
			ps.setString(2, pc_code);
			ps.setString(3, yxdm); 
			ps.setString(4, pc);
			ps.setString(5, zyz);
			SQLLogUtils.printSQL(ps);
			rs = ps.executeQuery();
			JHBean bean = null;
			while (rs.next()) {
				list.add(setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JHBean> getJHByIds(int jhYear, String sfCode, HashSet<String> ids) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		int dataYear = jhYear-1;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.id in ("+Tools.getSQLQueryin(ids)+") ORDER BY x.yxmc, x.zyz, x.zdf DESC";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);

			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public JHBean getJHById(int jhYear, String sfCode, int id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		int dataYear = jhYear-1;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.id = ?";
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setInt(1, id);
			JHBean bean = null;
			SQLLogUtils.printSQL(ps);
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = setJHBeanAll(dataYear, rs);
			}
			return bean;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public List<ZyzdForm> getMakerFormForYxAndZyz(String sfCode, String batch_id_org, String yxmc, String zyz) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdForm> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String zyzSQL = Tools.isEmpty(zyz) ? "and zyz is null" : "and zyz = ?";
			String SQL = "SELECT * FROM "+sfCode+"_form_maker x WHERE batch_id_org = ? and yxmc = ? "+zyzSQL+" ORDER BY seq_no_yx, seq_no_zy";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			ps.setString(2, yxmc);
			if(!Tools.isEmpty(zyz)) {
				ps.setString(3, zyz);
			}
			rs = ps.executeQuery();
			ZyzdForm bean = null;
			while (rs.next()) {
				list.add(setZyzdForm(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdBaseUniversityAndMajorTag> getZyzdBaseUniversityAndMajorTagAll() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdBaseUniversityAndMajorTag> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_base_university_and_major_tag";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			ZyzdBaseUniversityAndMajorTag bean = null;
			while (rs.next()) {
				bean = new ZyzdBaseUniversityAndMajorTag();
	            bean.setNf(rs.getInt("nf"));
	            bean.setYxmc_org(rs.getString("yxmc_org"));
	            bean.setZymc_org(rs.getString("zymc_org"));
	            bean.setYxbq(rs.getString("yxbq"));
	            bean.setYxsp(rs.getString("yxsp"));
	            bean.setGmhbzs(rs.getString("gmhbzs"));
	            bean.setLsdw(rs.getString("lsdw"));
	            bean.setYxbyl(rs.getString("yxbyl"));
	            bean.setZzyqk(rs.getString("zzyqk"));
	            bean.setQxsszys(rs.getString("qxsszys"));
	            bean.setQxsszy(rs.getString("qxsszy"));
	            bean.setQxbszys(rs.getString("qxbszys"));
	            bean.setQxbszy(rs.getString("qxbszy"));
	            bean.setZysp(rs.getString("zysp"));
	            bean.setRkpm(rs.getString("rkpm"));
	            bean.setRkpj(rs.getString("rkpj"));
	            bean.setXkpg(rs.getString("xkpg"));
	            bean.setBzyssd(rs.getString("bzyssd"));
	            bean.setBzybsd(rs.getString("bzybsd"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	private ZyzdForm setZyzdForm(ResultSet rs) throws SQLException {
		return LhyJDBC.setZyzdForm(rs);
	}
	
	public List<ZyzdForm> getMakerFormExistFor96(String sfCode, String batch_id_org, String yxdm, String yxmc, String zydm, String zymc, String zybz) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdForm> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+sfCode+"_form_maker x WHERE batch_id_org = ? and yxdm = ? and yxmc = ? and zydm = ? and zymc = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			ps.setString(2, yxdm);
			ps.setString(3, yxmc);
			ps.setString(4, zydm);
			ps.setString(5, zymc);

			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				list.add(setZyzdForm(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdForm> getLhyFormByBatchIdAndYxSeq(String sfCode, String batch_id_org, int yx_seq) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdForm> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+sfCode+"_form_maker x WHERE batch_id_org = ? and seq_no_yx = ? ORDER BY seq_no_yx, seq_no_zy";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			ps.setInt(2, yx_seq);

			rs = ps.executeQuery();
			ZyzdForm bean = null;
			while (rs.next()) {
				list.add(setZyzdForm(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public void adjustLhyFormZyNoWithFormId(String sfCode, List<ZyzdForm> updateList) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "update "+sfCode+"_form_maker set seq_no_zy = ? where id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			for(ZyzdForm form : updateList) {
				ps.setInt(1, form.getSeq_no_zy());
				ps.setInt(2, form.getId());
				ps.addBatch();
			}
			ps.executeBatch();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public ZyzdForm getMakerFormByBatchOrgAndYxSeqnoAndZySeqno(String sfCode, String batch_id_org, int yx_seq_no, int zy_seq_no) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+sfCode+"_form_maker x WHERE batch_id_org = ? and seq_no_yx = ? and seq_no_zy = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			ps.setInt(2, yx_seq_no);
			ps.setInt(3, zy_seq_no);

			rs = ps.executeQuery();
			ZyzdForm bean = null;
			while (rs.next()) {
				bean = new ZyzdForm();
				bean.setBatch_id(rs.getString("batch_id"));
				bean.setId(rs.getInt("id"));
				bean.setBatch_id_org(rs.getString("batch_id_org"));
				bean.setSeq_no_yx(rs.getInt("seq_no_yx"));
				bean.setSeq_no_zy(rs.getInt("seq_no_zy"));
				bean.setYxbz(rs.getString("yxbz"));
				bean.setYxdm(rs.getString("yxdm"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZybz(rs.getString("zybz"));
				bean.setZydm(rs.getString("zydm"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZyz(rs.getString("zyz"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setZymc_org(rs.getString("zymc_org"));
				
				bean.setZdf_a(rs.getInt("zdf_a"));
				bean.setZdf_b(rs.getInt("zdf_b"));
				bean.setZdf_c(rs.getInt("zdf_c"));
				bean.setZdfwc_a(rs.getInt("zdfwc_a"));
				bean.setZdfwc_b(rs.getInt("zdfwc_b"));
				bean.setZdfwc_c(rs.getInt("zdfwc_c"));

				bean.setPjf_a(rs.getInt("pjf_a"));
				bean.setPjf_b(rs.getInt("pjf_b"));
				bean.setPjf_c(rs.getInt("pjf_c"));
				bean.setPjfwc_a(rs.getInt("pjfwc_a"));
				bean.setPjfwc_b(rs.getInt("pjfwc_b"));
				bean.setPjfwc_c(rs.getInt("pjfwc_c"));
				return bean;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public ZyzdForm getMakerFormById(String sfCode, int id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+sfCode+"_form_maker x WHERE id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, id);

			rs = ps.executeQuery();
			ZyzdForm bean = null;
			while (rs.next()) {
				bean = new ZyzdForm();
				bean.setBatch_id(rs.getString("batch_id"));
				bean.setId(rs.getInt("id"));
				bean.setBatch_id_org(rs.getString("batch_id_org"));
				bean.setSeq_no_yx(rs.getInt("seq_no_yx"));
				bean.setSeq_no_zy(rs.getInt("seq_no_zy"));
				bean.setYxbz(rs.getString("yxbz"));
				bean.setYxdm(rs.getString("yxdm"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZybz(rs.getString("zybz"));
				bean.setZydm(rs.getString("zydm"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZyz(rs.getString("zyz"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setZymc_org(rs.getString("zymc_org"));
				
				bean.setZdf_a(rs.getInt("zdf_a"));
				bean.setZdf_b(rs.getInt("zdf_b"));
				bean.setZdf_c(rs.getInt("zdf_c"));
				bean.setZdfwc_a(rs.getInt("zdfwc_a"));
				bean.setZdfwc_b(rs.getInt("zdfwc_b"));
				bean.setZdfwc_c(rs.getInt("zdfwc_c"));

				bean.setPjf_a(rs.getInt("pjf_a"));
				bean.setPjf_b(rs.getInt("pjf_b"));
				bean.setPjf_c(rs.getInt("pjf_c"));
				bean.setPjfwc_a(rs.getInt("pjfwc_a"));
				bean.setPjfwc_b(rs.getInt("pjfwc_b"));
				bean.setPjfwc_c(rs.getInt("pjfwc_c"));
				return bean;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public List<ZyzdForm> getMakerFormForYx(String sfCode, String batch_id_org, String yxmc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdForm> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+sfCode+"_form_maker x WHERE batch_id_org = ? and yxmc like ? ORDER BY seq_no_yx, seq_no_zy";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			ps.setString(2, "%"+yxmc+"%");

			rs = ps.executeQuery();
			ZyzdForm bean = null;
			while (rs.next()) {
				bean = new ZyzdForm();
				bean.setBatch_id(rs.getString("batch_id"));
				bean.setId(rs.getInt("id"));
				bean.setBatch_id_org(rs.getString("batch_id_org"));
				bean.setSeq_no_yx(rs.getInt("seq_no_yx"));
				bean.setSeq_no_zy(rs.getInt("seq_no_zy"));
				bean.setYxbz(rs.getString("yxbz"));
				bean.setYxdm(rs.getString("yxdm"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZybz(rs.getString("zybz"));
				bean.setZydm(rs.getString("zydm"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZyz(rs.getString("zyz"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setZymc_org(rs.getString("zymc_org"));
				
				bean.setZdf_a(rs.getInt("zdf_a"));
				bean.setZdf_b(rs.getInt("zdf_b"));
				bean.setZdf_c(rs.getInt("zdf_c"));
				bean.setZdfwc_a(rs.getInt("zdfwc_a"));
				bean.setZdfwc_b(rs.getInt("zdfwc_b"));
				bean.setZdfwc_c(rs.getInt("zdfwc_c"));

				bean.setPjf_a(rs.getInt("pjf_a"));
				bean.setPjf_b(rs.getInt("pjf_b"));
				bean.setPjf_c(rs.getInt("pjf_c"));
				bean.setPjfwc_a(rs.getInt("pjfwc_a"));
				bean.setPjfwc_b(rs.getInt("pjfwc_b"));
				bean.setPjfwc_c(rs.getInt("pjfwc_c"));
				
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public ResultVO pickCandoByZymc(int jhYear, String sfCode, String xkCode, String pc, String pc_code, String zymcOrg, int scoreFrom, int scoreTo, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = jhYear-1;
		ResultVO resultVO = new ResultVO();
		int recordCnt = 0;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE pc = ? and pc_code = ? and zymc_org = ? and xk_code like ? and zdf_"+(jhYear-1)+" between ? and ? order by zdf_"+(jhYear-1)+" desc limit ?,?";
			String SQL_CNT = "SELECT count(*) as cnt FROM "+sfCode+"_jh_" + jhYear + " x WHERE pc = ? and pc_code = ? and zymc_org = ? and xk_code like ? and zdf_"+(jhYear-1)+" between ? and ? order by zdf_"+(jhYear-1)+" desc";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, zymcOrg);
			ps.setString(4, "%" + xkCode + "%");
			ps.setInt(5, scoreFrom);
			ps.setInt(6, scoreTo);
			ps.setInt(7, (pageNumber - 1) * PAGE_ROW_CNT); // 1开始
			ps.setInt(8, PAGE_ROW_CNT);

			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setJHBeanAll(dataYear, rs));
			}

			ps.close();
			ps = null;
			rs.close();
			rs = null;
			ps = conn.prepareStatement(SQL_CNT);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, zymcOrg);
			ps.setString(4, "%" + xkCode + "%");
			ps.setInt(5, scoreFrom);
			ps.setInt(6, scoreTo);

			rs = ps.executeQuery();
			while (rs.next()) {
				recordCnt = rs.getInt("cnt");
			}
			
			resultVO.setCurrentPage(pageNumber);
			resultVO.setRecordCnt(recordCnt);
			resultVO.setResult(list);
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return resultVO;
	}
	
	public ResultVO pickCandoByZymcWithoutPage(int jhYear, String sfCode, String xkCode, String pc, String pc_code, String zymcOrg, int wcFrom, int wcTo) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = jhYear-1;
		ResultVO resultVO = new ResultVO();
		int recordCnt = 0;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE pc = ? and pc_code = ? and zymc_org = ? and xk_code like ? and zdfwc between ? and ? order by zdfwc ASC";
			String SQL_CNT = "SELECT count(*) as cnt FROM "+sfCode+"_jh_" + jhYear + " x WHERE pc = ? and pc_code = ? and zymc_org = ? and xk_code like ? and zdfwc between ? and ? order by zdfwc ASC";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, pc);
			ps.setString(2, pc_code); 
			ps.setString(3, zymcOrg); 
			ps.setString(4, "%" + xkCode + "%");
			ps.setInt(5, wcFrom);
			ps.setInt(6, wcTo);

			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				list.add(setJHBeanAll(dataYear, rs));
			}

			ps.close();
			ps = null;
			rs.close();
			rs = null;
			ps = conn.prepareStatement(SQL_CNT);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, zymcOrg);
			ps.setString(4, "%" + xkCode + "%");
			ps.setInt(5, wcFrom);
			ps.setInt(6, wcTo);

			rs = ps.executeQuery();
			while (rs.next()) {
				recordCnt = rs.getInt("cnt");
			}
			
			resultVO.setCurrentPage(1);
			resultVO.setRecordCnt(recordCnt);
			resultVO.setResult(list);
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return resultVO;
	}
	
	//含大类
	public ResultVO pickCandoByZymcWithZyml(int jhYear, String sfCode, String xkCode, String pc, String pc_code, String zymcWithMlOrg, int scoreFrom, int scoreTo, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = jhYear-1;
		ResultVO resultVO = new ResultVO();
		int recordCnt = 0;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE pc = ? and pc_code = ? and (zymc_org = ?) and xk_code like ? and zdf between ? and ? order by zdf desc limit ?,?";
			String SQL_CNT = "SELECT count(*) as cnt FROM "+sfCode+"_jh_" + jhYear + " x WHERE pc = ? and pc_code = ? and (zymc_org = ?) and xk_code like ? and zdf between ? and ? order by zdf desc";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, zymcWithMlOrg);
			ps.setString(4, "%" + xkCode + "%");
			ps.setInt(5, scoreFrom);
			ps.setInt(6, scoreTo);
			ps.setInt(7, (pageNumber - 1) * PAGE_ROW_CNT); // 1开始
			ps.setInt(8, PAGE_ROW_CNT);

			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setJHBeanAll(dataYear, rs));
			}

			ps.close();
			ps = null;
			rs.close();
			rs = null;
			ps = conn.prepareStatement(SQL_CNT);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, zymcWithMlOrg);
			ps.setString(4, "%" + xkCode + "%");
			ps.setInt(5, scoreFrom);
			ps.setInt(6, scoreTo);

			rs = ps.executeQuery();
			while (rs.next()) {
				recordCnt = rs.getInt("cnt");
			}
			
			resultVO.setCurrentPage(pageNumber);
			resultVO.setRecordCnt(recordCnt);
			resultVO.setResult(list);
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return resultVO;
	}
	
	public ResultVO pickCandoByZyml(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> zymlSets, int scoreFrom, int scoreTo, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = jhYear-1;
		ResultVO resultVO = new ResultVO();
		int recordCnt = 0;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE pc = ? and pc_code = ? and zyml in ("+Tools.getSQLQueryin(zymlSets)+") and xk_code like ? and zdf between ? and ? order by zdf desc limit ?, ?";
			String SQL_CNT = "SELECT count(*) as cnt FROM "+sfCode+"_jh_" + jhYear + " x WHERE pc = ? and pc_code = ? and zyml in ("+Tools.getSQLQueryin(zymlSets)+") and xk_code like ? and zdf between ? and ? order by zdf desc";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%" + xkCode + "%");
			ps.setInt(4, scoreFrom);
			ps.setInt(5, scoreTo);
			ps.setInt(6, (pageNumber - 1) * PAGE_ROW_CNT); // 1开始
			ps.setInt(7, PAGE_ROW_CNT);

			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setJHBeanAll(dataYear, rs));
			}

			ps.close();
			ps = null;
			rs.close();
			rs = null;
			ps = conn.prepareStatement(SQL_CNT);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%" + xkCode + "%");
			ps.setInt(4, scoreFrom);
			ps.setInt(5, scoreTo);

			rs = ps.executeQuery();
			while (rs.next()) {
				recordCnt = rs.getInt("cnt");
			}
			
			resultVO.setCurrentPage(pageNumber);
			resultVO.setRecordCnt(recordCnt);
			resultVO.setResult(list);
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return resultVO;
	}
	
	public List<JHBean> getZyByZymc(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> zymcOrgSets, HashSet<String> yxsfSets, int zdf_from, int zdf_to) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String zymcSQL = zymcOrgSets.size() == 0 ? "" : "and x.zymc_org in ("+Tools.getSQLQueryin(zymcOrgSets)+")";
			String yxsfSQL = yxsfSets.size() == 0 ? "" : "and x.yxsf in ("+Tools.getSQLQueryin(yxsfSets)+")";
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.pc = ? and x.pc_code = ? and x.xk_code like ? "+yxsfSQL+" and (x.zdf between ? and ?) " + zymcSQL;
			String ORDER_CONDITION = " ORDER BY x.zdf DESC";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			ps.setInt(4, zdf_from);
			ps.setInt(5, zdf_to);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JHBean> getZyByZymcWithOrderFollow(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> zymcOrgSets, HashSet<String> yxsfSets, String order_id, int zdf_from, int zdf_to) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String zymcSQL = zymcOrgSets.size() == 0 ? "" : "and x.zymc_org in ("+Tools.getSQLQueryin(zymcOrgSets)+")";
			String yxsfSQL = yxsfSets.size() == 0 ? "" : "and x.yxsf in ("+Tools.getSQLQueryin(yxsfSets)+")";
			String SELECT_CONDITION = "SELECT x.* FROM lhy_s5_sc_form_user_follow y, "+sfCode+"_jh_" + jhYear + " x WHERE x.yxmc_org = y.yxmc_org and y.order_id = ? and x.pc = ? and x.pc_code = ? and x.xk_code like ? "+yxsfSQL+" and (x.zdf between ? and ?) " + zymcSQL;
			String ORDER_CONDITION = " ORDER BY x.zdf DESC";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, order_id);
			ps.setString(2, pc);
			ps.setString(3, pc_code);
			ps.setString(4, "%"+xkCode+"%");
			ps.setInt(5, zdf_from);
			ps.setInt(6, zdf_to);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JHBean> getZyByZymc_gb(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> zymcOrgSets, HashSet<String> yxsfSets, int zdf_from, int zdf_to) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String zymcSQL = zymcOrgSets.size() == 0 ? "" : "and x.zymc_org in ("+Tools.getSQLQueryin(zymcOrgSets)+")";
			String yxsfSQL = yxsfSets.size() == 0 ? "" : "and x.yxsf in ("+Tools.getSQLQueryin(yxsfSets)+")";
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.pc = ? and x.pc_code = ? and x.xk_code like ? "+yxsfSQL+" and (x.zdf between ? and ?) and x.ind_nature = '公办' " + zymcSQL;
			String ORDER_CONDITION = " ORDER BY x.zdf DESC";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			ps.setInt(4, zdf_from);
			ps.setInt(5, zdf_to);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JHBean> getZyByZyml(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> zymlSets, HashSet<String> yxsfSets, int zdfwc_from, int zdfwc_to) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String zymlSQL = zymlSets.size() == 0 ? "" : "and x.zyml in ("+Tools.getSQLQueryin(zymlSets)+")";
			String yxsfSQL = yxsfSets.size() == 0 ? "" : "and x.yxsf in ("+Tools.getSQLQueryin(yxsfSets)+")";
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.pc = ? and x.pc_code = ? and x.xk_code like ? "+yxsfSQL+" and (x.zdfwc between ? and ?) " + zymlSQL;
			String ORDER_CONDITION = " ORDER BY x.zdfwc ASC";
			
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			ps.setInt(4, zdfwc_from);
			ps.setInt(5, zdfwc_to);

			SQLLogUtils.printSQL("getZyByZyml-->", ps);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JHBean> getZyByZyml_gb(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> zymlSets, HashSet<String> yxsfSets, int zdf_from, int zdf_to) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String zymlSQL = zymlSets.size() == 0 ? "" : "and x.zyml in ("+Tools.getSQLQueryin(zymlSets)+")";
			String yxsfSQL = yxsfSets.size() == 0 ? "" : "and x.yxsf in ("+Tools.getSQLQueryin(yxsfSets)+")";
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.pc = ? and x.pc_code = ? and x.xk_code like ? "+yxsfSQL+" and (x.zdf between ? and ?) and x.ind_nature = '公办' " + zymlSQL;
			String ORDER_CONDITION = " ORDER BY x.zdf DESC";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			ps.setInt(4, zdf_from);
			ps.setInt(5, zdf_to);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public List<JHBean> getZyByZyml_notin(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> zymlSets, HashSet<String> yxsfSets, int zdf_from, int zdf_to) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String zymlSQL = zymlSets.size() == 0 ? "" : "and x.zyml not in ("+Tools.getSQLQueryin(zymlSets)+")";
			String yxsfSQL = yxsfSets.size() == 0 ? "" : "and x.yxsf in ("+Tools.getSQLQueryin(yxsfSets)+")";
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.pc = ? and pc_code = ? and xk_code like ? "+yxsfSQL+" and (zdf between ? and ?) " + zymlSQL;
			String ORDER_CONDITION = " ORDER BY x.zdf DESC";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			ps.setInt(4, zdf_from);
			ps.setInt(5, zdf_to);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	
	public List<JHBean> getZyBySelectedForm_YxmcAndZyz(int jhYear, String sfCode, String xkCode, String pc, String pc_code, String yxdm, String zyz) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = jhYear-1;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String zyzSQL = Tools.isEmpty(zyz)? "" : "and zyz = ?";
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.pc = ? and pc_code = ? and xk_code like ? and x.yxdm = ? " + zyzSQL;
			String ORDER_CONDITION = " ORDER BY x.zdf DESC";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			ps.setString(4, yxdm);
			if(!Tools.isEmpty(zyzSQL)){
				ps.setString(5, zyz);
			}
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JHBean> getZyBySelectedForm_YxdmAndZyz(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> yxdmSets, HashSet<String> zyzSets) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = jhYear-1;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String yxmcSQL = yxdmSets.size() <= 0 ? " and yxdm = 'yxdm' " : "and yxdm in ("+Tools.getSQLQueryin(yxdmSets)+") ";
			String zyzSQL = zyzSets.size() <= 0 ? " and zyz = 'zyz' " : "and zyz in ("+Tools.getSQLQueryin(zyzSets)+") ";
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.pc = ? and pc_code = ? and xk_code like ? " + yxmcSQL + zyzSQL ;
			String ORDER_CONDITION = " ORDER BY x.zdf DESC";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");

			SQLLogUtils.printSQL(" === getZyBySelectedForm_YxdmAndZyz: ", ps);
			
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JHBean> getZyBySelectedForm_Yxdm(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> yxdmSets) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = jhYear-1;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String yxmcSQL = yxdmSets.size() <= 0 ? " and yxdm = 'yxdm' " : "and yxdm in ("+Tools.getSQLQueryin(yxdmSets)+") ";
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.pc = ? and pc_code = ? and xk_code like ? " + yxmcSQL ;
			String ORDER_CONDITION = " ORDER BY x.zdf DESC";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");

			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<String> getZyzByYxmc(int jhYear, String sfCode, String xkCode, String pc, String pc_code, String yxmc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = jhYear-1;
		List<String> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT distinct zyz FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.pc = ? and pc_code = ? and xk_code like ? and x.yxmc = ? ";
			
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION );
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			ps.setString(4, yxmc);
			
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(rs.getString("zyz"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public List<ZyzdUniversityDataBean> getMyFollowed(int jhYear, String sfCode, String xkCode, String pc, String pc_code, String c_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdUniversityDataBean> list = new ArrayList<ZyzdUniversityDataBean>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT x.yxmc_org, min(y.zdf) as zdf, max(y.zdfwc) as zdfwc FROM "+sfCode+"_form_user_follow x left join "+sfCode+"_jh_" + jhYear + " y on x.yxmc_org = y.yxmc_org and y.pc = ? and y.pc_code = ? and y.xk_code like ? WHERE x.c_id = ? GROUP BY x.yxmc_org ";
			String ORDER_CONDITION = " order by min(y.zdf) desc ";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			ps.setString(4, c_id);
			
			rs = ps.executeQuery();
			ZyzdUniversityDataBean sb = null; 
			while (rs.next()) {
				sb = new ZyzdUniversityDataBean();
				sb.setYxmc_org(rs.getString("yxmc_org"));
				sb.setZdf(rs.getString("zdf"));
				sb.setZdfwc(rs.getString("zdfwc"));
				list.add(sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<CareerJY> pickJiuyeYxByDW(int jhYear, String sfCode, String xkCode, String pc, String pc_code, String group_name, String lsy, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = jhYear-1;
		List<CareerJY> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.yxmc, COUNT(*) as ct, min(y.zdf_"+dataYear+") as zdf, max(y.zdfwc_"+dataYear+") as zdfwc FROM career_jy_all_2024 x LEFT JOIN "+sfCode+"_jh_" + jhYear + " y ON x.yxmc = y.yxmc_org and y.pc = ? and y.pc_code = ? and y.xk_code like ? WHERE x.group_name = ? AND x.lsy = ? GROUP BY x.yxmc ORDER BY COUNT(*) desc limit ?,?;";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			ps.setString(4, group_name);
			ps.setString(5, lsy);

			ps.setInt(6, (pageNumber - 1) * 10);
			ps.setInt(7, 10);
			rs = ps.executeQuery();
			CareerJY bean = null;
			while (rs.next()) {
				bean = new CareerJY();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setCnt(rs.getInt("ct"));
				bean.setExt_zdf(rs.getString("zdf"));
				bean.setExt_zdfwc(rs.getString("zdfwc"));
				list.add(bean);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdMajorRanking> getRankingUnivRuanKeByZymc_WithYxScore(int jhYear, String sfCode, String xkCode, String pc, String pc_code , String zymc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = jhYear-1;
		List<ZyzdMajorRanking> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.zymc, x.yxmc, x.ranking, min(y.zdf_"+dataYear+") as zdf, max(y.zdfwc_"+dataYear+") as zdfwc FROM zyzd_base_rank_rk_major x LEFT JOIN "+sfCode+"_jh_" + jhYear + " y ON x.yxmc = y.yxmc_org and y.pc = ? and y.pc_code = ? and y.xk_code like ? WHERE x.zymc = ? GROUP BY x.zymc, x.yxmc, x.ranking ORDER BY x.ranking";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			ps.setString(4, zymc);
			rs = ps.executeQuery();
			ZyzdMajorRanking bean = null;
			while (rs.next()) {
				bean = new ZyzdMajorRanking();
				bean.setRanking(rs.getInt("ranking"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZymc(rs.getString("zymc"));
				bean.setExt_zdf(rs.getString("zdf"));
				bean.setExt_zdfwc(rs.getString("zdfwc"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdMajorRanking> getRankingUnivRuanKeByZymc_WithZyScore(int jhYear, String sfCode, String xkCode, String pc, String pc_code , String zymc) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<ZyzdMajorRanking> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.zymc, x.yxmc, x.ranking, min(y.zdf_"+dataYear+") as zdf, max(y.zdfwc_"+dataYear+") as zdfwc FROM zyzd_base_rank_rk_major x LEFT JOIN "+sfCode+"_jh_" + jhYear + " y ON x.yxmc = y.yxmc_org and x.zymc = y.zymc_org and y.pc = ? and y.pc_code = ? and y.xk_code like ? WHERE x.zymc = ? GROUP BY x.zymc, x.yxmc, x.ranking ORDER BY x.ranking";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			ps.setString(4, zymc);
			rs = ps.executeQuery();
			ZyzdMajorRanking bean = null;
			while (rs.next()) {
				bean = new ZyzdMajorRanking();
				bean.setRanking(rs.getInt("ranking"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZymc(rs.getString("zymc"));
				bean.setExt_zdf(rs.getString("zdf"));
				bean.setExt_zdfwc(rs.getString("zdfwc"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public List<ZyzdMajorRanking> getRelatedRankingUnivRuanKeByZymc_WithYxScore(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> zymlSets) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear - 1;
		ResultSet rs = null;
		List<ZyzdMajorRanking> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.zymc, x.yxmc, x.ranking, min(y.zdf_"+dataYear+") as zdf, max(y.zdfwc_"+dataYear+") as zdfwc FROM zyzd_base_rank_rk_major x LEFT JOIN "+sfCode+"_jh_" + jhYear + " y ON x.yxmc = y.yxmc_org and y.pc = ? and y.pc_code = ? and y.xk_code like ? WHERE x.zyml in ("+Tools.getSQLQueryin(zymlSets)+") GROUP BY x.zymc, x.yxmc, x.ranking ORDER BY x.ranking";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			rs = ps.executeQuery();
			ZyzdMajorRanking bean = null;
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				bean = new ZyzdMajorRanking();
				bean.setRanking(rs.getInt("ranking"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZymc(rs.getString("zymc"));
				bean.setExt_zdf(rs.getString("zdf"));
				bean.setExt_zdfwc(rs.getString("zdfwc"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdMajorRanking> getRelatedRankingUnivRuanKeByZymc_WithZyScore(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> zymlSets) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = jhYear - 1;
		List<ZyzdMajorRanking> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.zymc, x.yxmc, x.ranking, min(y.zdf_"+dataYear+") as zdf, max(y.zdfwc_"+dataYear+") as zdfwc FROM zyzd_base_rank_rk_major x LEFT JOIN "+sfCode+"_jh_" + jhYear + " y ON x.yxmc = y.yxmc_org and x.zymc = y.zymc_org and x.zymc = y.zymc_org and y.pc = ? and y.pc_code = ? and y.xk_code like ? WHERE x.zyml in ("+Tools.getSQLQueryin(zymlSets)+") GROUP BY x.zymc, x.yxmc, x.ranking ORDER BY x.ranking";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			rs = ps.executeQuery();
			ZyzdMajorRanking bean = null;
			while (rs.next()) {
				bean = new ZyzdMajorRanking();
				bean.setRanking(rs.getInt("ranking"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZymc(rs.getString("zymc"));
				bean.setExt_zdf(rs.getString("zdf"));
				bean.setExt_zdfwc(rs.getString("zdfwc"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdMajorRanking> getRankingUnivRuanKeByZyml_WithYxScore(int jhYear, String sfCode, String xkCode, String pc, String pc_code, String zyml) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = jhYear - 1;
		List<ZyzdMajorRanking> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.zymc, x.yxmc, x.ranking, min(y.zdf_"+dataYear+") as zdf, max(y.zdfwc_"+dataYear+") as zdfwc FROM zyzd_base_rank_rk_major x LEFT JOIN "+sfCode+"_jh_" + jhYear + " y ON x.yxmc = y.yxmc_org and y.pc = ? and y.pc_code = ? and y.xk_code like ? WHERE x.zyml = ? GROUP BY x.zymc, x.yxmc, x.ranking ORDER BY x.ranking";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			ps.setString(4, zyml);
			rs = ps.executeQuery();
			ZyzdMajorRanking bean = null;
			while (rs.next()) {
				bean = new ZyzdMajorRanking();
				bean.setRanking(rs.getInt("ranking"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZymc(rs.getString("zymc"));
				bean.setExt_zdf(rs.getString("zdf"));
				bean.setExt_zdfwc(rs.getString("zdfwc"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdMajorRanking> getRankingUnivRuanKeByZyml_WithZyScore(int jhYear, String sfCode, String xkCode, String pc, String pc_code, String zyml) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = jhYear - 1;
		List<ZyzdMajorRanking> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.zymc, x.yxmc, x.ranking, min(y.zdf_"+dataYear+") as zdf, max(y.zdfwc_"+dataYear+") as zdfwc FROM zyzd_base_rank_rk_major x LEFT JOIN "+sfCode+"_jh_" + jhYear + " y ON x.yxmc = y.yxmc_org and x.zymc = y.zymc_org and y.pc = ? and y.pc_code = ? and y.xk_code like ? WHERE x.zyml = ? GROUP BY x.zymc, x.yxmc, x.ranking ORDER BY x.ranking";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			ps.setString(4, zyml);
			rs = ps.executeQuery();
			ZyzdMajorRanking bean = null;
			while (rs.next()) {
				bean = new ZyzdMajorRanking();
				bean.setRanking(rs.getInt("ranking"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZymc(rs.getString("zymc"));
				bean.setExt_zdf(rs.getString("zdf"));
				bean.setExt_zdfwc(rs.getString("zdfwc"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public int getDownloadCnt(String c_id, int d_type) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT count(*) as cnt FROM zyzd_download x WHERE x.ref_c_id = ? AND x.d_type = ? ";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, c_id);
			ps.setInt(2, d_type);

			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("cnt");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	private static void closeAllConnection(Connection conn, PreparedStatement ps, ResultSet rs) {
		/**
		try {
			if (rs != null)
				rs.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (ps != null)
				ps.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (conn != null)
				conn.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		rs = null;
		ps = null;
		conn = null; */
		DatabaseUtils.closeAllResources(rs, ps, conn);
	}

}
