package com.report.service;

import com.report.entity.*;
import com.career.utils.Tools;
import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import freemarker.template.TemplateExceptionHandler;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

/**
 * 人生规划书生成服务（单例模式）
 * 专门负责生成志愿填报相关的人生规划书PDF
 */
public class LifePlanningReportService {
    
    // 单例实例
    private static volatile LifePlanningReportService instance;
    
    private Configuration freemarkerConfig;
    private String templateBasePath;
    private String outputBasePath;
    private String webappRootPath;
    
    private Map<String, String> reportGenerationLog = new HashMap<>();
    
    private LifePlanningReportService(String webappRootPath) {
        this.webappRootPath = webappRootPath;
        this.templateBasePath = webappRootPath + "report/templates/";
        this.outputBasePath = webappRootPath + "report/output/life_planning/";
        
        Tools.println("LifePlanningReportService 初始化:");
        Tools.println("- webappRootPath: " + webappRootPath);
        Tools.println("- templateBasePath: " + templateBasePath);
        Tools.println("- outputBasePath: " + outputBasePath);
        
        // 创建输出目录
        createDirectoryIfNotExists(outputBasePath);
        
        // 初始化FreeMarker引擎
        initializeFreeMarkerEngine();
        
        Tools.println("人生规划书生成服务初始化完成");
    }
    
    /**
     * 获取单例实例
     */
    public static LifePlanningReportService getInstance(String webappRootPath) {
        if (instance == null) {
            synchronized (LifePlanningReportService.class) {
                if (instance == null) {
                    instance = new LifePlanningReportService(webappRootPath);
                }
            }
        }
        return instance;
    }
    
    /**
     * 获取单例实例（如果已存在）
     */
    public static LifePlanningReportService getInstance() {
        return instance;
    }
    
    /**
     * 重置单例实例（用于Web应用重新部署）
     */
    public static void resetInstance() {
        synchronized (LifePlanningReportService.class) {
            instance = null;
        }
    }
    
    /**
     * 初始化FreeMarker模板引擎
     */
    private void initializeFreeMarkerEngine() {
        try {
            freemarkerConfig = new Configuration(Configuration.VERSION_2_3_32);
            
            // 设置模板文件目录
            File templateDir = new File(templateBasePath);
            if (!templateDir.exists()) {
                templateDir.mkdirs();
            }
            freemarkerConfig.setDirectoryForTemplateLoading(templateDir);
            
            // 设置编码
            freemarkerConfig.setDefaultEncoding("UTF-8");
            
            // 设置数字格式（避免科学计数法）
            freemarkerConfig.setNumberFormat("0.######");
            
            // 设置日期格式
            freemarkerConfig.setDateFormat("yyyy-MM-dd");
            freemarkerConfig.setTimeFormat("HH:mm:ss");
            
            // 设置模板异常处理器
            freemarkerConfig.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
            
            Tools.println("FreeMarker引擎初始化成功，模板路径: " + templateBasePath);
            
        } catch (Exception e) {
            System.err.println("FreeMarker引擎初始化失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 创建目录（如果不存在）
     */
    private void createDirectoryIfNotExists(String dirPath) {
        try {
            Files.createDirectories(Paths.get(dirPath));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 生成人生规划书PDF报告
     * @param batchId 批次ID
     * @param sfCode 省份代码
     * @return 报告ID，失败返回null
     */
    public String generateLifePlanningReport(String batchId, String sfCode) {
        try {
            // 生成报告ID
            String reportId = "LIFE_PLAN_" + batchId + "_" + sfCode + "_" + System.currentTimeMillis();
            
            // 生成模拟的四年规划数据
            UniversityFourYearPlan fourYearPlan = MockDataService.generateMockUniversityFourYearPlan();
            
            // 获取志愿数据
            VolunteerData volunteerData = null;
            if (batchId != null && sfCode != null) {
                volunteerData = VolunteerDataService.getVolunteerData(batchId, sfCode);
            }
            
            // 准备FreeMarker数据模型
            Map<String, Object> dataModel = new HashMap<>();
            dataModel.put("fourYearPlan", fourYearPlan);
            dataModel.put("volunteerData", volunteerData);
            dataModel.put("reportId", reportId);
            dataModel.put("generateTime", new Date());
            
            // 添加志愿表单类型判断
            boolean is96 = (sfCode != null) ? VolunteerDataService.is96Mode(sfCode) : false;
            dataModel.put("is96", is96);
            
            // 生成HTML内容
            String htmlContent = processTemplate("life_planning_report.ftl", dataModel);
            
            // 转换为PDF
            String pdfFilePath = outputBasePath + reportId + ".pdf";
            convertHtmlToPdf(htmlContent, pdfFilePath);
            
            // 记录到内存缓存
            saveReportGenerationToCache(reportId, "LIFE_PLANNING", pdfFilePath);
            
            Tools.println("人生规划书生成成功: " + reportId);
            return reportId;
            
        } catch (Exception e) {
            System.err.println("生成人生规划书失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 生成人生规划书HTML预览
     * @param batchId 批次ID
     * @param sfCode 省份代码
     * @return HTML内容，失败返回null
     */
    public String generateLifePlanningPreview(String batchId, String sfCode) {
        Tools.println("=== LifePlanningReportService.generateLifePlanningPreview 开始 ===");
        Tools.println("输入参数 - batchId: " + batchId + ", sfCode: " + sfCode);
        
        try {
            // 生成预览ID
            String previewId = "LIFE_PLAN_PREVIEW_" + batchId + "_" + sfCode + "_" + System.currentTimeMillis();
            Tools.println("生成的预览ID: " + previewId);
            
            // 生成模拟的四年规划数据
            Tools.println("开始生成模拟数据...");
            UniversityFourYearPlan fourYearPlan = MockDataService.generateMockUniversityFourYearPlan();
            Tools.println("四年规划数据生成完成，学生姓名: " + (fourYearPlan != null ? fourYearPlan.getStudentName() : "null"));
            
            // 获取志愿数据
            VolunteerData volunteerData = null;
            if (batchId != null && sfCode != null) {
                Tools.println("尝试获取真实志愿数据...");
                try {
                    volunteerData = VolunteerDataService.getVolunteerData(batchId, sfCode);
                    Tools.println("志愿数据获取结果: " + (volunteerData != null ? "成功" : "为空"));
                    if (volunteerData != null && volunteerData.getAdmitBeanList() != null) {
                        Tools.println("预测录取数据数量: " + volunteerData.getAdmitBeanList().size());
                        if (volunteerData.getJobStatisticsList() != null) {
                            Tools.println("就业统计数据组数: " + volunteerData.getJobStatisticsList().size());
                        }
                    }
                } catch (Exception e) {
                    Tools.println("志愿数据获取失败: " + e.getMessage());
                    e.printStackTrace();
                }
            }
            
            // 准备FreeMarker数据模型
            Tools.println("准备FreeMarker数据模型...");
            Map<String, Object> dataModel = new HashMap<>();
            dataModel.put("fourYearPlan", fourYearPlan);
            dataModel.put("volunteerData", volunteerData);
            dataModel.put("reportId", previewId);
            dataModel.put("generateTime", new Date());
            dataModel.put("isPreview", true);
            
            // 添加志愿表单类型判断
            boolean is96 = (sfCode != null) ? VolunteerDataService.is96Mode(sfCode) : false;
            dataModel.put("is96", is96);
            Tools.println("数据模型准备完成，is96模式: " + is96);
            
            // 生成HTML内容
            Tools.println("开始处理FreeMarker模板...");
            String htmlContent = processTemplate("life_planning_report.ftl", dataModel);
            Tools.println("HTML内容生成完成，长度: " + (htmlContent != null ? htmlContent.length() : 0));
            
            if (htmlContent == null || htmlContent.trim().isEmpty()) {
                System.err.println("错误: 生成的HTML内容为空");
                return null;
            }
            
            // 保存HTML预览文件
            String htmlFilePath = outputBasePath + previewId + ".html";
            Tools.println("保存HTML文件到: " + htmlFilePath);
            
            File outputDir = new File(outputBasePath);
            if (!outputDir.exists()) {
                Tools.println("创建输出目录: " + outputBasePath);
                outputDir.mkdirs();
            }
            
            try (FileWriter fileWriter = new FileWriter(htmlFilePath, java.nio.charset.StandardCharsets.UTF_8)) {
                fileWriter.write(htmlContent);
                fileWriter.flush();
            }
            
            // 验证文件是否成功创建
            File htmlFile = new File(htmlFilePath);
            if (htmlFile.exists()) {
                Tools.println("HTML文件创建成功，文件大小: " + htmlFile.length() + " bytes");
            } else {
                System.err.println("错误: HTML文件创建失败");
                return null;
            }
            
            // 记录到内存缓存
            saveReportGenerationToCache(previewId, "LIFE_PLANNING_PREVIEW", htmlFilePath);
            Tools.println("报告记录已保存到缓存");
            
            Tools.println("=== 人生规划书HTML预览生成成功: " + previewId + " ===");
            
            // 直接返回HTML内容
            return htmlContent;
            
        } catch (Exception e) {
            System.err.println("=== 生成人生规划书HTML预览失败 ===");
            System.err.println("错误信息: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 处理FreeMarker模板
     */
    private String processTemplate(String templateName, Map<String, Object> dataModel) throws IOException, TemplateException {
        Template template = freemarkerConfig.getTemplate(templateName);
        StringWriter writer = new StringWriter();
        template.process(dataModel, writer);
        return writer.toString();
    }
    
    /**
     * 保存报告生成记录到内存缓存
     */
    private void saveReportGenerationToCache(String reportId, String reportType, String reportPath) {
        String reportInfo = reportType + "|" + reportPath + "|" + new Date().toString();
        reportGenerationLog.put(reportId, reportInfo);
    }
    
    /**
     * 将HTML内容转换为PDF并直接输出到流
     * @param htmlContent HTML内容
     * @param outputStream 输出流
     * @throws Exception 转换异常
     */
    public void convertHtmlToPdfStream(String htmlContent, OutputStream outputStream) throws Exception {
        Tools.println("开始HTML转PDF流处理，HTML长度: " + (htmlContent != null ? htmlContent.length() : 0));
        
        // 确保HTML内容格式正确
        if (!htmlContent.toLowerCase().contains("<!doctype")) {
            htmlContent = "<!DOCTYPE html>\n" + htmlContent;
        }
        
        // 修正HTML内容，确保meta标签以XML兼容的方式闭合
        htmlContent = htmlContent.replaceAll("<meta([^>]*?)>", "<meta$1/>");
        
        // 使用OpenHTMLToPDF转换
        PdfRendererBuilder builder = new PdfRendererBuilder();

        // 注册字体以支持中文
        String fontPath = this.webappRootPath + "WEB-INF/fonts/msyh.ttc";
        File fontFile = new File(fontPath);
        if (fontFile.exists()) {
            try {
                builder.useFont(fontFile, "Microsoft YaHei");
                builder.useFont(fontFile, "微软雅黑");
                builder.useFont(fontFile, "serif");
                builder.useFont(fontFile, "sans-serif");
                
                Tools.println("字体注册成功: " + fontPath);
            } catch (Exception fontException) {
                System.err.println("字体注册失败: " + fontException.getMessage());
                fontException.printStackTrace();
            }
        } else {
            System.err.println("字体文件不存在: " + fontPath);
        }
        
        // 设置PDF生成选项以保持样式
        builder.useFastMode();
        builder.usePdfVersion(1.7f);
        
        // 设置基础URI（重要：用于正确解析CSS和图片）
        String baseUri = "file:///" + this.webappRootPath.replace("\\", "/");
        if (!baseUri.endsWith("/")) {
            baseUri += "/";
        }
        
        builder.withHtmlContent(htmlContent, baseUri);
        builder.toStream(outputStream);
        builder.run();
        
        Tools.println("PDF流生成成功");
        Tools.println("使用基础URI: " + baseUri);
    }
    
    /**
     * 将HTML内容转换为PDF
     */
    private void convertHtmlToPdf(String htmlContent, String outputPath) throws Exception {
        try (OutputStream os = new FileOutputStream(outputPath)) {
            // 确保HTML内容格式正确
            if (!htmlContent.toLowerCase().contains("<!doctype")) {
                htmlContent = "<!DOCTYPE html>\n" + htmlContent;
            }
            
            // 修正HTML内容，确保meta标签以XML兼容的方式闭合
            htmlContent = htmlContent.replaceAll("<meta([^>]*?)>", "<meta$1/>");
            
            // 使用OpenHTMLToPDF转换
            PdfRendererBuilder builder = new PdfRendererBuilder();

            // 注册字体以支持中文
            String fontPath = this.webappRootPath + "WEB-INF/fonts/msyh.ttc";
            File fontFile = new File(fontPath);
            if (fontFile.exists()) {
                try {
                    builder.useFont(fontFile, "Microsoft YaHei");
                    builder.useFont(fontFile, "微软雅黑");
                    builder.useFont(fontFile, "serif");
                    builder.useFont(fontFile, "sans-serif");
                    
                    Tools.println("字体注册成功: " + fontPath);
                } catch (Exception fontException) {
                    System.err.println("字体注册失败: " + fontException.getMessage());
                    fontException.printStackTrace();
                }
            } else {
                System.err.println("字体文件不存在: " + fontPath);
            }
            
            // 设置PDF生成选项以保持样式
            builder.useFastMode();
//            builder.usePdfAConformance(com.openhtmltopdf.extend.FSSupplier.PdfAConformance.NONE);
            builder.usePdfVersion(1.7f);
            
            // 设置基础URI（重要：用于正确解析CSS和图片）
            String baseUri = "file:///" + this.webappRootPath.replace("\\", "/");
            if (!baseUri.endsWith("/")) {
                baseUri += "/";
            }
            
            builder.withHtmlContent(htmlContent, baseUri);
            builder.toStream(os);
            builder.run();
            
            Tools.println("PDF生成成功: " + outputPath);
            Tools.println("使用基础URI: " + baseUri);
        }
    }
    
    /**
     * 获取报告文件路径
     */
    public String getReportFilePath(String reportId) {
        String reportInfo = reportGenerationLog.get(reportId);
        if (reportInfo != null) {
            String[] parts = reportInfo.split("\\|");
            if (parts.length >= 2) {
                return parts[1]; // 返回文件路径
            }
        }
        return null;
    }
    
    /**
     * 检查报告是否存在
     */
    public boolean reportExists(String reportId) {
        String filePath = getReportFilePath(reportId);
        if (filePath != null) {
            return new File(filePath).exists();
        }
        return false;
    }
    
    /**
     * 删除报告
     */
    public boolean deleteReport(String reportId) {
        try {
            String filePath = getReportFilePath(reportId);
            if (filePath != null) {
                File file = new File(filePath);
                boolean deleted = file.delete();
                
                if (deleted) {
                    reportGenerationLog.remove(reportId);
                    Tools.println("报告删除成功: " + reportId);
                    return true;
                } else {
                    System.err.println("文件删除失败: " + filePath);
                    return false;
                }
            } else {
                System.err.println("报告不存在: " + reportId);
                return false;
            }
        } catch (Exception e) {
            System.err.println("删除报告时发生错误: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 获取报告文件大小
     */
    public long getReportFileSize(String reportId) {
        String filePath = getReportFilePath(reportId);
        if (filePath != null) {
            File file = new File(filePath);
            if (file.exists()) {
                return file.length();
            }
        }
        return 0;
    }
} 