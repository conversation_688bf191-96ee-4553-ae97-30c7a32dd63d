package com.career.utils.wecom.service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.career.utils.Tools;
import com.career.utils.wecom.WeComHttpClient;
import com.career.utils.wecom.WeComTokenManager;
import com.career.utils.wecom.exception.WeComApiException;
import com.career.utils.wecom.model.GroupChat;
import com.career.utils.wecom.model.GroupChatMember;
import com.career.utils.wecom.model.WeComCustomer;
import com.career.utils.wecom.config.WeComConfig;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.Map;
import java.util.HashMap;

/**
 * 企业微信客户群管理服务类
 */
public class WeComGroupChatService {

    private static final String GET_GROUP_CHAT_LIST_URL = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/list";
    private static final String GET_GROUP_CHAT_DETAIL_URL = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/get";

    /**
     * 分页获取客户群列表（返回chat_id、owner、部分成员信息等）
     * 此方法用于获取群的基本列表信息，不返回完整的群详情或member_version
     * @param corpId 企业ID
     * @param corpSecret 应用的Secret
     * @param userId 成员ID，如果为null则获取全部群
     * @param statusFilter 群状态过滤，0-所有群, 1-客户群, 2-已离职成员的群 (注：只支持全部群或已离职成员的群)
     * @return 客户群列表项，包含chat_id、owner、部分成员信息等，可能返回null表示获取失败
     */
    public static List<JSONObject> getGroupChatList(String corpId, String corpSecret, String userId, Integer statusFilter) {
        List<JSONObject> groupChatListItems = new ArrayList<>();

        try {
            // 获取access_token
            String accessToken = WeComTokenManager.getAccessToken(corpId, corpSecret);

            // 构建请求参数
            JSONObject params = new JSONObject();
            // status_filter 0:所有群, 1:客户群(在职成员的群), 2:已离职成员的群
            // 注意：API文档说明status_filter只支持0和2过滤，1实际上是不支持的，这里保持原代码逻辑但需注意
            // 如果需要只获取在职成员的客户群，可能需要在获取所有群(0)后自行过滤owner
            if (statusFilter != null) {
                 params.put("status_filter", statusFilter);
            } else {
                 params.put("status_filter", 0); // 默认为所有群
            }

            params.put("limit", 100); // 每次拉取的群数量，最大100

            // 根据userId过滤群主
            if (userId != null && !userId.isEmpty()) {
                JSONArray ownerList = new JSONArray();
                ownerList.add(userId);
                params.put("owner_filter", new JSONObject().fluentPut("userid_list", ownerList));
            }

            // 分页获取群列表
            String cursor = null;
            boolean hasMore = true;
            int pageCount = 0; // 添加页数计数器

            while(hasMore) {
                pageCount++;
                if (cursor != null) {
                    params.put("cursor", cursor);
                } else {
                    // 确保第一次请求时移除cursor参数
                    params.remove("cursor");
                }

                String url = String.format("%s?access_token=%s", GET_GROUP_CHAT_LIST_URL, accessToken);
                
                // 添加详细的分页调试日志
                Tools.println("=== 获取客户群列表 - 第" + pageCount + "页 ===");
                Tools.println("请求URL: " + url);
                Tools.println("请求参数: " + params.toJSONString());
                Tools.println("当前cursor: " + (cursor != null ? cursor : "null"));
                Tools.println("已获取群数量: " + groupChatListItems.size());
                
                JSONObject response = WeComHttpClient.doPostWithRetry(url, params.toJSONString());

                // 根据配置输出API响应原始数据
                if (WeComConfig.isLogApiRawJsonResponse()) {
                    Tools.println("=== 获取客户群列表API原始响应数据 ===");
                    Tools.println("请求URL: " + url);
                    Tools.println("请求参数: " + params.toJSONString());
                    Tools.println("原始响应: " + response.toString());
                    Tools.println("=====================================");
                }

                // 检查API调用是否成功
                int errcode = response.getIntValue("errcode");
                if (errcode != 0) {
                    String errmsg = response.getString("errmsg");
                    // 细化错误日志，包含API和参数信息
                    Tools.println(String.format("获取客户群列表API返回错误. URL: %s, 参数: %s, 错误码: %d, 错误信息: %s", url, params.toJSONString(), errcode, errmsg));
                    return null; // API调用失败，返回null
                }

                JSONArray chatList = response.getJSONArray("group_chat_list");
                int currentPageCount = 0;
                if (chatList != null) {
                    currentPageCount = chatList.size();
                    for (int i = 0; i < chatList.size(); i++) {
                        JSONObject chatItem = chatList.getJSONObject(i);
                        // 只提取 list 接口实际返回的关键信息，不包含 member_version
                        JSONObject listItem = new JSONObject();
                        listItem.put("chat_id", chatItem.getString("chat_id"));
                        listItem.put("owner", chatItem.getString("owner")); 
                        groupChatListItems.add(listItem);
                    }
                }

                // 获取分页信息
                String nextCursor = response.getString("next_cursor");
                int hasMoreValue = response.getIntValue("has_more");
                hasMore = hasMoreValue == 1;
                
                // 详细的分页调试信息
                Tools.println("=== 第" + pageCount + "页响应分析 ===");
                Tools.println("本页返回群数量: " + currentPageCount);
                Tools.println("累计群数量: " + groupChatListItems.size());
                Tools.println("has_more值: " + hasMoreValue + " (1=有更多, 0=无更多)");
                Tools.println("next_cursor: " + (nextCursor != null ? nextCursor : "null"));
                Tools.println("是否继续分页: " + hasMore);
                Tools.println("===============================");
                
                cursor = nextCursor;

                // 防止无限循环的安全检查
                if (pageCount > 200) { // 假设最多200页，可以根据实际情况调整
                    Tools.println("警告: 分页次数超过50次，可能存在无限循环，强制退出");
                    break;
                }
                
                // 如果本页没有返回任何数据，也退出循环
                if (currentPageCount == 0) {
                    Tools.println("本页未返回任何群数据，退出分页循环");
                    break;
                }

            } // while (hasMore)

            Tools.println("=== 分页获取完成 ===");
            Tools.println("总页数: " + pageCount);
            Tools.println("成功获取客户群列表，总数: " + groupChatListItems.size());
            Tools.println("====================");
            return groupChatListItems;

        } catch (Exception e) {
            throw new WeComApiException("获取客户群列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取群聊详情
     *
     * @param corpId 企业ID
     * @param corpSecret 应用密钥
     * @param chatId 群聊ID
     * @return 群聊对象
     */
    public static GroupChat getGroupChatDetail(String corpId, String corpSecret, String chatId) {
        try {
            String accessToken = WeComTokenManager.getAccessToken(corpId, corpSecret);

            Map<String, Object> params = new HashMap<>();
            params.put("chat_id", chatId);

            JSONObject response = WeComHttpClient.doPostWithRetry(GET_GROUP_CHAT_DETAIL_URL + "?access_token=" + accessToken, 
                    JSONObject.toJSONString(params));

            int errcode = response.getIntValue("errcode");
            if (errcode != 0) {
                Tools.println("获取客户群详情失败: " + response.getString("errmsg") + " (errcode=" + errcode + ")");
                return null;
            }

            // 根据配置决定是否输出API响应原始数据，用于调试
            if (WeComConfig.isLogApiRawJsonResponse()) {
                Tools.println("=== 获取客户群详情API原始响应数据 ===");
                Tools.println("群ID: " + chatId);
                Tools.println("原始响应: " + response.toString());
                Tools.println("===============================");
            }

            JSONObject groupInfo = response.getJSONObject("group_chat");
            if (groupInfo == null) {
                Tools.println("返回的客户群详情数据为空");
                return null;
            }

            // 如果开启API响应详情日志，也输出group_chat部分的详细信息
            if (WeComConfig.isLogApiRawJsonResponse()) {
                Tools.println("=== 群聊详情解析 ===");
//                Tools.println("chat_id: " + groupInfo.getString("chat_id"));
//                Tools.println("name: " + groupInfo.getString("name"));
//                Tools.println("owner: " + groupInfo.getString("owner"));
//                Tools.println("create_time: " + groupInfo.getLong("create_time"));
//                Tools.println("member_count: " + groupInfo.getInteger("member_count"));
//                Tools.println("member_version: " + groupInfo.getString("member_version"));
                Tools.println("==================");
            }

            GroupChat group = new GroupChat();
            group.setChatId(groupInfo.getString("chat_id"));
            group.setName(groupInfo.getString("name"));
            group.setOwner(groupInfo.getString("owner"));
            
            Long createTime = groupInfo.getLong("create_time");
            if (createTime != null) {
                group.setCreateTime(new Date(createTime * 1000)); // 将秒转为毫秒
            }
            
            // 增强member_count字段的处理和调试
            Integer memberCount = groupInfo.getInteger("member_count");
            
            if (WeComConfig.isLogApiRawJsonResponse()) {
                Tools.println("member_count原始值: " + memberCount + " (类型: " + (memberCount != null ? memberCount.getClass().getSimpleName() : "null") + ")");
            }
            
            // 如果API没有返回member_count，尝试从member_list计算
            if (memberCount == null) {
                com.alibaba.fastjson2.JSONArray memberList = groupInfo.getJSONArray("member_list");
                if (memberList != null) {
                    memberCount = memberList.size();
                    if (WeComConfig.isLogApiRawJsonResponse()) {
                        Tools.println("API未返回member_count，从member_list计算得到: " + memberCount);
                    }
                } else {
                    if (WeComConfig.isLogApiRawJsonResponse()) {
                        Tools.println("API既未返回member_count，也未返回member_list，无法计算成员数量");
                    }
                }
            }
            
            if (WeComConfig.isLogApiRawJsonResponse()) {
                Tools.println("member_count最终值: " + memberCount + " (类型: " + (memberCount != null ? memberCount.getClass().getSimpleName() : "null") + ")");
            }
            
            group.setMemberCount(memberCount);
            group.setMemberVersion(groupInfo.getString("member_version"));

            // 输出最终设置的群组信息（调试用）
            if (WeComConfig.isLogApiRawJsonResponse()) {
                Tools.println("=== 最终设置的GroupChat对象信息 ===");
//                Tools.println("setChatId: " + group.getChatId());
//                Tools.println("setName: " + group.getName());
//                Tools.println("setOwner: " + group.getOwner());
//                Tools.println("setCreateTime: " + group.getCreateTime());
//                Tools.println("setMemberCount: " + group.getMemberCount());
//                Tools.println("setMemberVersion: " + group.getMemberVersion());
                Tools.println("=================================");
            }

            return group;
        } catch (Exception e) {
            Tools.println("获取客户群详情时发生异常: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取客户群的成员列表（单独获取成员信息，不放在GroupChat对象中）
     * @param corpId 企业ID
     * @param corpSecret 应用的Secret
     * @param chatId 群聊ID
     * @return 客户群成员列表，如果获取失败则返回null
     */
    public static List<GroupChatMember> getGroupChatMembers(String corpId, String corpSecret, String chatId) {
         try {
            // 获取access_token
            String accessToken = WeComTokenManager.getAccessToken(corpId, corpSecret);

            JSONObject params = new JSONObject();
            params.put("chat_id", chatId);
            params.put("need_name", 1); // 是否需要返回群成员的名字

            String url = String.format("%s?access_token=%s", GET_GROUP_CHAT_DETAIL_URL, accessToken);
            JSONObject response = WeComHttpClient.doPostWithRetry(url, params.toJSONString());

            // 根据配置输出API响应原始数据
            if (WeComConfig.isLogApiRawJsonResponse()) {
                Tools.println("=== 获取客户群成员列表API原始响应数据 ===");
                Tools.println("群ID: " + chatId);
                Tools.println("请求URL: " + url);
                Tools.println("请求参数: " + params.toJSONString());
                Tools.println("原始响应: " + response.toString());
                Tools.println("=========================================");
            }

             // 检查API调用是否成功
            int errcode = response.getIntValue("errcode");
            if (errcode != 0) {
                String errmsg = response.getString("errmsg");
                Tools.println(String.format("获取客户群成员列表API返回错误. 群ID: %s, 错误码: %d, 错误信息: %s", chatId, errcode, errmsg));
                return null; // API调用失败，返回null
            }

            JSONObject groupInfo = response.getJSONObject("group_chat");
            if (groupInfo != null) {
                // 从 API 响应中获取 member_version
                String memberVersion = groupInfo.getString("member_version");
                
                // 添加调试日志，检查member_version是否为null
                if (memberVersion == null) {
                    Tools.println("警告: 群 " + chatId + " 的API响应中member_version为null");
                    Tools.println("群信息响应: " + groupInfo.toString());
                } else {
                    Tools.println("群 " + chatId + " 的member_version: " + memberVersion);
                }
                
                // 处理群成员
                JSONArray memberList = groupInfo.getJSONArray("member_list");
                List<GroupChatMember> members = new ArrayList<>();

                if (memberList != null) {
                    for (int i = 0; i < memberList.size(); i++) {
                        JSONObject memberJson = memberList.getJSONObject(i);

                        GroupChatMember member = new GroupChatMember();
                        member.setChatId(chatId); // 设置所属群ID
                        member.setUserId(memberJson.getString("userid"));
                        member.setType(memberJson.getIntValue("type"));
                        Long joinTimeMillis = memberJson.getLong("join_time");
                        if (joinTimeMillis != null) {
                            member.setJoinTime(new Date(joinTimeMillis * 1000));
                        }
                        member.setJoinScene(memberJson.getInteger("join_scene"));
                        member.setGroupNickname(memberJson.getString("name")); // 注意：现在使用groupNickname代替name
                        
                        // 设置群版本号 - 添加null检查
                        if (memberVersion != null) {
                            member.setGroupVersion(memberVersion);
                        } else {
                            Tools.println("警告: 为群成员 " + member.getUserId() + " 设置的群版本号为null");
                        }
                        
                        // 添加邀请人ID（如果有）
                        if (memberJson.containsKey("invitor") && memberJson.getJSONObject("invitor") != null) {
                            JSONObject invitor = memberJson.getJSONObject("invitor");
                            if (invitor.containsKey("userid")) {
                                member.setInvitorUserId(invitor.getString("userid"));
                            }
                        }

                        members.add(member);
                    }
                }

                return members;
            }

        } catch (Exception e) {
            Tools.println("获取客户群成员列表失败，群ID: " + chatId + ", 错误信息: " + e.getMessage());
        }

        return null;
    }

    /**
     * 获取客户详细信息的特殊版本，直接处理84061错误而不抛出异常
     * 此方法是对WeComExternalContactService.getCustomerDetail的包装，
     * 专门用于处理客户群同步时的外部联系人获取
     * 
     * @param accessToken 访问令牌
     * @param externalUserId 客户ID
     * @param notExternalContactCounter 统计不是外部联系人的计数器，可以为null
     * @param memberName 成员名称，用于日志记录
     * @return 客户信息，如果获取失败或信息不完整或不是外部联系人则返回null
     */
    public static WeComCustomer getCustomerDetailSuppressing84061(String accessToken, String externalUserId,
            AtomicInteger notExternalContactCounter, String memberName) {
        String nameInfo = (memberName != null && !memberName.isEmpty()) ? 
            memberName + " (" + externalUserId + ")" : externalUserId;
            
        try {
            return WeComExternalContactService.getCustomerDetail(accessToken, externalUserId);
        } catch (WeComApiException e) {
            // 如果是84061错误(not external contact)，只输出简单信息，不打印异常堆栈
            if (e.getMessage() != null && (e.getMessage().contains("84061") || e.getMessage().contains("not external contact"))) {
                if (notExternalContactCounter != null) {
                    notExternalContactCounter.incrementAndGet();
                }
                Tools.logWithTimestamp("  - 用户 " + nameInfo + " 不是外部联系人，跳过");
                return null;
            }
            
            // 尝试从错误信息中解析出错误码和错误消息
            String errorCodeStr = null;
            String errorMsg = null;
            try {
                if (e.getMessage().contains("errcode")) {
                    // 尝试解析JSON错误信息
                    int errcodeIndex = e.getMessage().indexOf("\"errcode\"");
                    int errmsgIndex = e.getMessage().indexOf("\"errmsg\"");
                    if (errcodeIndex >= 0 && errmsgIndex >= 0) {
                        int codeStartIndex = e.getMessage().indexOf(":", errcodeIndex) + 1;
                        int codeEndIndex = e.getMessage().indexOf(",", codeStartIndex);
                        errorCodeStr = e.getMessage().substring(codeStartIndex, codeEndIndex).trim();
                        
                        int msgStartIndex = e.getMessage().indexOf(":", errmsgIndex) + 1;
                        int msgEndIndex = e.getMessage().indexOf("\"", msgStartIndex + 1);
                        if (msgEndIndex < 0) {
                            msgEndIndex = e.getMessage().indexOf("}", msgStartIndex);
                        }
                        errorMsg = e.getMessage().substring(msgStartIndex, msgEndIndex).trim().replace("\"", "");
                    }
                }
            } catch (Exception ex) {
                // 解析错误码出错，忽略并使用原始异常信息
            }
            
            if (errorCodeStr != null && errorMsg != null) {
                Tools.logWithTimestamp("  - 获取外部联系人 " + nameInfo + " 详情失败，API错误码: " + errorCodeStr + ", 错误信息: " + errorMsg);
            } else {
                // 其他API错误仍然记录，但不打印堆栈
                Tools.logWithTimestamp("  - 获取外部联系人 " + nameInfo + " 详情失败，API错误: " + e.getMessage());
            }
            
            // 针对特定错误码判断是否需要重试
            // 42001: access_token过期
            // 40001: 不合法的access_token
            // 45009: 接口调用超过限频
            // 对这些错误，外层应考虑重试
            if (errorCodeStr != null && (errorCodeStr.equals("42001") || errorCodeStr.equals("40001") || errorCodeStr.equals("45009"))) {
                throw e; // 对这些可恢复错误，重新抛出异常让外层处理重试
            }
            
            return null;
        } catch (Exception e) {
            // 其他未知异常
            Tools.logWithTimestamp("  - 获取外部联系人 " + nameInfo + " 详情时发生异常: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 获取客户详细信息的特殊版本，直接处理84061错误而不抛出异常
     * 此方法是对WeComExternalContactService.getCustomerDetail的包装，
     * 专门用于处理客户群同步时的外部联系人获取
     * 
     * @param accessToken 访问令牌
     * @param externalUserId 客户ID
     * @return 客户信息，如果获取失败或信息不完整或不是外部联系人则返回null
     */
    public static WeComCustomer getCustomerDetailSuppressing84061(String accessToken, String externalUserId) {
        return getCustomerDetailSuppressing84061(accessToken, externalUserId, null, null);
    }
    
    /**
     * 获取客户详细信息的特殊版本，直接处理84061错误而不抛出异常
     * 此方法是对WeComExternalContactService.getCustomerDetail的包装，
     * 专门用于处理客户群同步时的外部联系人获取
     * 
     * @param accessToken 访问令牌
     * @param externalUserId 客户ID
     * @param notExternalContactCounter 统计不是外部联系人的计数器，可以为null
     * @return 客户信息，如果获取失败或信息不完整或不是外部联系人则返回null
     */
    public static WeComCustomer getCustomerDetailSuppressing84061(String accessToken, String externalUserId, AtomicInteger notExternalContactCounter) {
        return getCustomerDetailSuppressing84061(accessToken, externalUserId, notExternalContactCounter, null);
    }
    
    /**
     * 获取群聊成员外部联系人详情
     *
     * @param corpId 企业ID
     * @param corpSecret 应用密钥
     * @param chatId 群聊ID
     * @return 外部联系人列表
     */
    public static List<WeComCustomer> fetchGroupChatExternalUserDetail(String corpId, String corpSecret, String chatId) {
        try {
            // 1. 首先获取群聊详情，包括成员列表
            String accessToken = WeComTokenManager.getAccessToken(corpId, corpSecret);
            
            Map<String, Object> params = new HashMap<>();
            params.put("chat_id", chatId);
            params.put("need_name", 1); // 需要群成员名称

            JSONObject response = WeComHttpClient.doPostWithRetry(GET_GROUP_CHAT_DETAIL_URL + "?access_token=" + accessToken, 
                    JSONObject.toJSONString(params));

            int errcode = response.getIntValue("errcode");
            if (errcode != 0) {
                Tools.println("获取客户群详情失败: " + response.getString("errmsg") + " (errcode=" + errcode + ")");
                return null;
            }
            
            JSONObject groupChatInfo = response.getJSONObject("group_chat");
            if (groupChatInfo == null) {
                Tools.println("返回的客户群详情数据为空");
                return null;
            }
            
            JSONArray memberList = groupChatInfo.getJSONArray("member_list");
            if (memberList == null || memberList.isEmpty()) {
                Tools.println("群聊 " + chatId + " 没有成员");
                return new ArrayList<>();
            }
            
            // 2. 筛选出类型为2的成员（外部联系人）
            List<JSONObject> externalMembers = new ArrayList<>();
            for (int i = 0; i < memberList.size(); i++) {
                JSONObject member = memberList.getJSONObject(i);
                if (member.getIntValue("type") == 2) { // 类型2表示外部联系人
                    externalMembers.add(member);
                }
            }
            
            if (externalMembers.isEmpty()) {
                Tools.println("群聊 " + chatId + " 中没有外部联系人");
                return new ArrayList<>();
            }
            
            Tools.println("群聊 " + chatId + " 中有 " + externalMembers.size() + " 个外部联系人需要获取详情");
            
            // 3. 逐个获取外部联系人详情
            List<WeComCustomer> customerList = new ArrayList<>();
            AtomicInteger notExternalContactCounter = new AtomicInteger(0);
            
            // 请求限制，避免频率过高
            final int MAX_REQUESTS_PER_SECOND = 15; // 每秒最多处理的请求数
            final long REQUEST_INTERVAL = 1000 / MAX_REQUESTS_PER_SECOND; // 毫秒
            long lastRequestTime = 0;
            
            // 进度计数器
            int processedCount = 0;
            int totalCount = externalMembers.size();
            
            for (JSONObject member : externalMembers) {
                processedCount++;
                
                // 控制请求频率
                long currentTime = System.currentTimeMillis();
                long elapsed = currentTime - lastRequestTime;
                if (elapsed < REQUEST_INTERVAL) {
                    try {
                        Thread.sleep(REQUEST_INTERVAL - elapsed);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
                lastRequestTime = System.currentTimeMillis();
                
                String userId = member.getString("userid");
                String memberName = member.getString("name");
                if (memberName == null || memberName.isEmpty()) {
                    memberName = userId;
                }
                
                // 只在每处理10个成员或最后一个成员时输出进度
                if (processedCount % 10 == 0 || processedCount == totalCount) {
                    Tools.println(String.format("处理外部联系人进度: %d/%d (%.1f%%)", processedCount, totalCount, 
                            (processedCount * 100.0 / totalCount)));
                }
                
                // 获取外部联系人详情
                WeComCustomer customer = getCustomerDetailSuppressing84061(accessToken, userId, 
                        notExternalContactCounter, memberName);
                
                if (customer != null) {
                    customerList.add(customer);
                }
            }
            
            // 统计错误数
            int errorCount = notExternalContactCounter.get();
            if (errorCount > 0) {
                Tools.println("获取外部联系人详情时出现 " + errorCount + " 个 84061 错误 (该成员不是外部联系人)");
            }
            
            Tools.println("群聊 " + chatId + " 中共获取到 " + customerList.size() + "/" + externalMembers.size() + 
                    " 个外部联系人详情，成功率: " + String.format("%.1f%%", customerList.size() * 100.0 / externalMembers.size()));
            
            return customerList;
        } catch (Exception e) {
            Tools.println("获取群成员外部联系人详情时发生异常: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }
    
    /**
     * 从客户群中获取所有外部联系人(客户)的详细信息
     * 注意：此方法会遍历群成员并逐个调用获取客户详情接口，效率较低，慎用。
     * 推荐在检测到群成员变化时调用，而不是频繁全量获取。
     * // TODO: 考虑在高并发或大量成员场景下，此方法可能成为性能瓶颈。上层业务逻辑应设计更高效的同步策略。
     * @param corpId 企业ID
     * @param corpSecret 应用的Secret
     * @param groupChat 客户群对象
     * @return 客户详细信息列表
     */
    public static List<WeComCustomer> fetchGroupChatExternalUserDetail(String corpId, String corpSecret, GroupChat groupChat) {
        if (groupChat == null) {
            return new ArrayList<>();
        }
        
        return fetchGroupChatExternalUserDetail(corpId, corpSecret, groupChat.getChatId());
    }

    /**
     * 获取客户群列表，并包含member_version字段（通过获取每个群的详情）
     * 注意：此方法会对每个群聊调用详情接口，可能会有性能问题，请谨慎使用
     * @param corpId 企业ID
     * @param corpSecret 应用的Secret
     * @param userId 成员ID，如果为null则获取全部群
     * @param statusFilter 群状态过滤，0-所有群, 1-客户群, 2-已离职成员的群
     * @return 包含member_version的客户群列表
     */
    public static List<JSONObject> getGroupChatListWithVersion(String corpId, String corpSecret, String userId, Integer statusFilter) {
        // 先获取基本的群列表
        List<JSONObject> basicGroupList = getGroupChatList(corpId, corpSecret, userId, statusFilter);
        if (basicGroupList == null || basicGroupList.isEmpty()) {
            return basicGroupList;
        }
        
        List<JSONObject> groupListWithVersion = new ArrayList<>();
        Tools.println("正在获取" + basicGroupList.size() + "个群的详细信息以包含member_version...");
        
        // 对每个群聊获取详情以获取member_version
        for (JSONObject basicGroup : basicGroupList) {
            String chatId = basicGroup.getString("chat_id");
            if (chatId == null || chatId.isEmpty()) {
                continue;
            }
            
            GroupChat groupDetail = getGroupChatDetail(corpId, corpSecret, chatId);
            if (groupDetail != null) {
                JSONObject groupWithVersion = new JSONObject();
                groupWithVersion.put("chat_id", chatId);
                groupWithVersion.put("owner", basicGroup.getString("owner"));
                groupWithVersion.put("name", groupDetail.getName());
                groupWithVersion.put("member_version", groupDetail.getMemberVersion());
                // 可以根据需要添加更多从详情中获取的字段
                
                groupListWithVersion.add(groupWithVersion);
            }
        }
        
        Tools.println("成功获取" + groupListWithVersion.size() + "个群的详细信息（含member_version）");
        return groupListWithVersion;
    }

    /**
     * 测试获取客户群列表的API权限和分页功能
     * 这个方法用于调试和验证API是否正常工作
     * @param corpId 企业ID
     * @param corpSecret 应用的Secret
     * @return 测试结果信息
     */
    public static String testGroupChatListApi(String corpId, String corpSecret) {
        StringBuilder testResult = new StringBuilder();
        testResult.append("=== 客户群列表API测试 ===\n");
        
        try {
            // 获取access_token
            String accessToken = WeComTokenManager.getAccessToken(corpId, corpSecret);
            testResult.append("✓ Access Token获取成功\n");
            
            // 测试基本API调用
            JSONObject params = new JSONObject();
            params.put("status_filter", 0);
            params.put("limit", 10); // 先测试小批量
            
            String url = String.format("%s?access_token=%s", GET_GROUP_CHAT_LIST_URL, accessToken);
            testResult.append("请求URL: ").append(url).append("\n");
            testResult.append("请求参数: ").append(params.toJSONString()).append("\n");
            
            JSONObject response = WeComHttpClient.doPostWithRetry(url, params.toJSONString());
            
            int errcode = response.getIntValue("errcode");
            if (errcode != 0) {
                testResult.append("✗ API调用失败: ").append(errcode).append(" - ").append(response.getString("errmsg")).append("\n");
                return testResult.toString();
            }
            
            testResult.append("✓ API调用成功\n");
            
            // 分析响应数据
            JSONArray chatList = response.getJSONArray("group_chat_list");
            int returnedCount = chatList != null ? chatList.size() : 0;
            String nextCursor = response.getString("next_cursor");
            int hasMore = response.getIntValue("has_more");
            
            testResult.append("返回群数量: ").append(returnedCount).append("\n");
            testResult.append("has_more: ").append(hasMore).append(" (1=有更多, 0=无更多)\n");
            testResult.append("next_cursor: ").append(nextCursor != null ? nextCursor : "null").append("\n");
            
            // 如果有更多数据，测试第二页
            if (hasMore == 1 && nextCursor != null) {
                testResult.append("\n--- 测试第二页 ---\n");
                params.put("cursor", nextCursor);
                
                JSONObject response2 = WeComHttpClient.doPostWithRetry(url, params.toJSONString());
                int errcode2 = response2.getIntValue("errcode");
                
                if (errcode2 == 0) {
                    JSONArray chatList2 = response2.getJSONArray("group_chat_list");
                    int returnedCount2 = chatList2 != null ? chatList2.size() : 0;
                    String nextCursor2 = response2.getString("next_cursor");
                    int hasMore2 = response2.getIntValue("has_more");
                    
                    testResult.append("✓ 第二页调用成功\n");
                    testResult.append("第二页返回群数量: ").append(returnedCount2).append("\n");
                    testResult.append("第二页has_more: ").append(hasMore2).append("\n");
                    testResult.append("第二页next_cursor: ").append(nextCursor2 != null ? nextCursor2 : "null").append("\n");
                } else {
                    testResult.append("✗ 第二页调用失败: ").append(errcode2).append(" - ").append(response2.getString("errmsg")).append("\n");
                }
            } else {
                testResult.append("没有更多数据或next_cursor为空，无法测试分页\n");
            }
            
            // 测试不同的limit值
            testResult.append("\n--- 测试不同limit值 ---\n");
            for (int limit : new int[]{50, 100, 200}) {
                JSONObject testParams = new JSONObject();
                testParams.put("status_filter", 0);
                testParams.put("limit", limit);
                
                JSONObject testResponse = WeComHttpClient.doPostWithRetry(url, testParams.toJSONString());
                int testErrcode = testResponse.getIntValue("errcode");
                
                if (testErrcode == 0) {
                    JSONArray testChatList = testResponse.getJSONArray("group_chat_list");
                    int testReturnedCount = testChatList != null ? testChatList.size() : 0;
                    testResult.append("limit=").append(limit).append(": 返回").append(testReturnedCount).append("个群\n");
                } else {
                    testResult.append("limit=").append(limit).append(": 失败 - ").append(testResponse.getString("errmsg")).append("\n");
                }
            }
            
        } catch (Exception e) {
            testResult.append("✗ 测试过程中发生异常: ").append(e.getMessage()).append("\n");
            e.printStackTrace();
        }
        
        testResult.append("=== 测试完成 ===");
        return testResult.toString();
    }
} 