package com.career.utils.wecom.model;

import java.util.Date;

// import java.util.List;

/**
 * 企业微信客户信息模型
 */
public class WeComCustomer {
    // 客户ID (对应 wecom_external_userid)
    private String externalUserId;
    // 客户名称 (对应 wecom_name)
    private String name;
    // 职位信息 (对应 wecom_position)
    private String position;
    // 头像 (对应 wecom_avatar)
    private String avatar;
    // 公司名称 (对应 wecom_corp_name)
    private String corpName;
    // 公司全称 (对应 wecom_corp_full_name)
    private String corpFullName;
    // 客户类型 (对应 wecom_type)
    private Integer type;
    // 性别 (对应 wecom_gender)
    private Integer gender;
    // 微信unionid (对应 wecom_unionid)
    private String unionId;
    // 外部档案 (对应 wecom_external_profile)
    private String externalProfile;
    // 跟进人信息列表的JSON字符串 (对应 wecom_follow_users_json)
    private String followUsersJson;
    // 加入群的时间 (对应 wecom_join_tm)
    private Date joinTime;
    // 系统创建时间 (对应 sys_create_tm)
    private Date createdAt;
    // 系统更新时间 (对应 sys_update_tm)
    private Date updatedAt;
    
    private String wecom_saas_id;
    
    // 外部联系人同步时间戳
    private Date externalSyncTime;

    // 构造函数
    public WeComCustomer() {
    }
    
    // Getter和Setter方法
    public String getExternalUserId() {
        return externalUserId;
    }

    public void setExternalUserId(String externalUserId) {
        this.externalUserId = externalUserId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getCorpName() {
        return corpName;
    }

    public void setCorpName(String corpName) {
        this.corpName = corpName;
    }

    public String getCorpFullName() {
        return corpFullName;
    }

    public void setCorpFullName(String corpFullName) {
        this.corpFullName = corpFullName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public String getExternalProfile() {
        return externalProfile;
    }

    public void setExternalProfile(String externalProfile) {
        this.externalProfile = externalProfile;
    }

    public String getWecom_saas_id() {
		return wecom_saas_id;
	}

	public void setWecom_saas_id(String wecom_saas_id) {
		this.wecom_saas_id = wecom_saas_id;
	}

	public Date getJoinTime() {
		return joinTime;
	}

	public void setJoinTime(Date joinTime) {
		this.joinTime = joinTime;
	}

	public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

	public Date getExternalSyncTime() {
        return externalSyncTime;
    }

    public void setExternalSyncTime(Date externalSyncTime) {
        this.externalSyncTime = externalSyncTime;
    }

    /**
     * 获取跟进人信息的JSON字符串
     * @return 跟进人信息JSON字符串
     */
    public String getFollowUsersJson() {
        return followUsersJson;
    }

    /**
     * 设置跟进人信息的JSON字符串
     * @param followUsersJson 跟进人信息JSON字符串
     */
    public void setFollowUsersJson(String followUsersJson) {
        this.followUsersJson = followUsersJson;
    }
    
    /**
     * 兼容方法：获取跟进人信息（与getFollowUsersJson等价）
     * @return 跟进人信息JSON字符串
     */
    public String getFollowersJson() {
        return followUsersJson;
    }

    /**
     * 兼容方法：设置跟进人信息（与setFollowUsersJson等价）
     * @param followersJson 跟进人信息JSON字符串
     */
    public void setFollowersJson(String followersJson) {
        this.followUsersJson = followersJson;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("客户信息:\n");
        sb.append("  客户ID: ").append(externalUserId).append("\n");
        sb.append("  客户名称: ").append(name).append("\n");
        sb.append("  职位: ").append(position).append("\n");
        sb.append("  头像: ").append(avatar).append("\n");
        sb.append("  公司名称: ").append(corpName).append("\n");
        sb.append("  公司全称: ").append(corpFullName).append("\n");
        sb.append("  类型: ").append(type).append("\n");
        sb.append("  性别: ").append(gender).append("\n");
        sb.append("  unionId: ").append(unionId).append("\n");
        sb.append("  外部档案: ").append(externalProfile).append("\n");
        sb.append("  跟进人信息JSON: ").append(followUsersJson != null ? followUsersJson : "无").append("\n");

        return sb.toString();
    }
} 