package com.zsdwf.servlet;

import jakarta.servlet.ServletContextEvent;
import jakarta.servlet.ServletContextListener;

public class ServerStartHandleListener implements ServletContextListener {
 
    public void contextInitialized(ServletContextEvent sce) {
    	System.out.println("load Thread....");
    	new Thread(new Server()).start();
        System.out.println("Server start....");
    }
 
    public void contextDestroyed(ServletContextEvent sce) {
        System.out.println("Server down....");
    }
    
    
    
    class Server implements Runnable {
        
        public void run() {
        	com.career.utils.kaogong.KGHttpSendUtils.runCheck();
        }
    }


}
