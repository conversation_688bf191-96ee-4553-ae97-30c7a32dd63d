package com.career.utils;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.career.db.CardBean;
import com.career.db.GzdzCard;
import com.career.db.ZyzdForm;
import com.career.db.ZyzdInvitationCode;
import com.career.db.ZyzdMajorRanking;
import com.career.db.ZyzdUniversityBean;
import com.career.db.ZyzdUniversityDataBean;

import cn.hutool.core.lang.UUID;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;


public class ExcelUtils {
	
	public static void main(String args[]) {
		
		try {
			InputStream is = new FileInputStream(new File("c:\\2024软科院校排名总榜cX.xlsx"));
			readExcel(is, "软科大学排名");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
	}
	
	public static List<Map<String, String>> readExcel(InputStream inputStream, String sheetName) {
		StringBuffer SQL = new StringBuffer();
        //定义工作簿
        XSSFWorkbook xssfWorkbook = null;
        try {
            xssfWorkbook = new XSSFWorkbook(inputStream);
        } catch (Exception e) {
            System.out.println("Excel data file cannot be found!");
        }

        //定义工作表
        XSSFSheet xssfSheet;
        if (sheetName.equals("")) {
            // 默认取第一个子表
            xssfSheet = xssfWorkbook.getSheetAt(0);
        } else {
            xssfSheet = xssfWorkbook.getSheet(sheetName);
        }

        List<Map<String, String>> list = new ArrayList<Map<String, String>>();

        //定义行
        //默认第一行为标题行，index = 0
        XSSFRow titleRow = xssfSheet.getRow(0);

        //循环取每行的数据
        for (int rowIndex = 1; rowIndex < xssfSheet.getPhysicalNumberOfRows(); rowIndex++) {
            XSSFRow xssfRow = xssfSheet.getRow(rowIndex);
            if (xssfRow == null) {
                continue;
            }
            XSSFCell xssfCell0 = xssfRow.getCell(0);
            XSSFCell xssfCell1 = xssfRow.getCell(1);
            XSSFCell xssfCell2 = xssfRow.getCell(2);
            XSSFCell xssfCell3 = xssfRow.getCell(3);
            XSSFCell xssfCell4 = xssfRow.getCell(4);
            XSSFCell xssfCell5 = xssfRow.getCell(5);
            XSSFCell xssfCell6 = xssfRow.getCell(6);
            XSSFCell xssfCell7 = xssfRow.getCell(7); 
            
            String str6 = getString(xssfCell6);
            if(str6.contains(".0")) {
            	str6 = str6.substring(0, str6.length() - 2);
            }
            
            String str7 = getString(xssfCell7);
            if(str7.contains(".0")) {
            	str7 = str7.substring(0, str7.length() - 2);
            }
            
            String str0 = getString(xssfCell0);
            if(str0.contains(".0")) {
            	str0 = str0.substring(0, str0.length() - 2);
            }

            SQL.append("insert into zyzd_base_rank_rk_univ(xh, yxmc, tags, lx, sf, score, seq, rank) "
            		+ "values('"+str0+"','"+getString(xssfCell1)+"','"+getString(xssfCell2)+"','"+getString(xssfCell3)+"','"+getString(xssfCell4)+"','"+getString(xssfCell5)+"','"+str6+"','"+str7+"');\r\n");
        }
        
        
        writeTempFile(new File("c://zyzd_rk.sql"), SQL);
        return list;
    }
	
	public static List<Map<String, String>> readTSZH_ZXjhExcel(InputStream inputStream, String sheetName) {
		StringBuffer SQL = new StringBuffer();
        //定义工作簿
        XSSFWorkbook xssfWorkbook = null;
        try {
            xssfWorkbook = new XSSFWorkbook(inputStream);
        } catch (Exception e) {
            System.out.println("Excel data file cannot be found!");
        }

        //定义工作表
        XSSFSheet xssfSheet;
        if (sheetName.equals("")) {
            // 默认取第一个子表
            xssfSheet = xssfWorkbook.getSheetAt(0);
        } else {
            xssfSheet = xssfWorkbook.getSheet(sheetName);
        }

        List<Map<String, String>> list = new ArrayList<Map<String, String>>();

        //定义行
        //默认第一行为标题行，index = 0
        XSSFRow titleRow = xssfSheet.getRow(0);

        //循环取每行的数据
        for (int rowIndex = 1; rowIndex < xssfSheet.getPhysicalNumberOfRows(); rowIndex++) {
            XSSFRow xssfRow = xssfSheet.getRow(rowIndex);
            if (xssfRow == null) {
                continue;
            }
            XSSFCell xssfCell0 = xssfRow.getCell(0);
            XSSFCell xssfCell1 = xssfRow.getCell(1);
            XSSFCell xssfCell2 = xssfRow.getCell(2);
            XSSFCell xssfCell3 = xssfRow.getCell(3);
            XSSFCell xssfCell4 = xssfRow.getCell(4);
            XSSFCell xssfCell5 = xssfRow.getCell(5);
            XSSFCell xssfCell6 = xssfRow.getCell(6);
            XSSFCell xssfCell7 = xssfRow.getCell(7);
            XSSFCell xssfCell8 = xssfRow.getCell(8);
            XSSFCell xssfCell9 = xssfRow.getCell(9);
            XSSFCell xssfCell10 = xssfRow.getCell(10);
            XSSFCell xssfCell11 = xssfRow.getCell(11);
            XSSFCell xssfCell12 = xssfRow.getCell(12);
            XSSFCell xssfCell13 = xssfRow.getCell(13);
            XSSFCell xssfCell14 = xssfRow.getCell(14);
            System.out.println(getString(xssfCell0) +","+getString(xssfCell1)+","+getString(xssfCell8));

            SQL.append("insert into zyzd_zyjh(nf, yxmc, sf, pc, lx, xk, zx, zyz, zymc, zdf, zgf, pjf, jhs, lqrs, bz) "
            		+ "values('"+getString(xssfCell0)+"','"+getString(xssfCell1)+"','"+getString(xssfCell2)+"','"+getString(xssfCell3)+"','"+getString(xssfCell4)+"','"+getString(xssfCell5)+"','"+getString(xssfCell6)+"','"+getString(xssfCell7)+"','"+getString(xssfCell8)+"','"+getString(xssfCell9)+"','"+getString(xssfCell10)+"','"+getString(xssfCell11)+"','"+getString(xssfCell12)+"','"+getString(xssfCell13)+"','"+getString(xssfCell14)+"');\r\n");
        }
        
        
        writeTempFile(new File("c://zyzd_zyjh.sql"), SQL);
        return list;
    }
	
	public static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	public static String getString(XSSFCell xssfCell) {
        if (xssfCell == null) {
            return "";
        }
        if (xssfCell.getCellType() == CellType.NUMERIC) {
            return String.valueOf(xssfCell.getNumericCellValue());
        } else if (xssfCell.getCellType() == CellType.BOOLEAN) {
            return String.valueOf(xssfCell.getBooleanCellValue());
        } else {
            return xssfCell.getStringCellValue();
        }
    }
	


	public static void createGzdzCardExcel(String filePathAndName,  List<GzdzCard> dataList) {
		// 创建一个新的工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        Tools.println(filePathAndName);
        // 创建一个工作表
        Sheet sheet = workbook.createSheet("卡密");
 
        // 创建一个行，并在其中创建一个单元格
        Row rowOne = sheet.createRow(0);
		Cell rowOneCell1 = rowOne.createCell(0);
		rowOneCell1.setCellValue("卡号");
        Cell rowOneCell2 = rowOne.createCell(1);
        rowOneCell2.setCellValue("密码");
        
        for(int i= 0; i < dataList.size(); i++) {
        	GzdzCard cardBean = dataList.get(i);
	        Row row = sheet.createRow(i+1);
	        Cell cell1 = row.createCell(0);
	        cell1.setCellValue(cardBean.getC_id());
	        Cell cell2 = row.createCell(1);
	        cell2.setCellValue(cardBean.getC_passwd());
        }
 
        // 写出到文件
        try{
        	FileOutputStream fileOut = new FileOutputStream(filePathAndName);
            workbook.write(fileOut);
            workbook.close();
        } catch (Exception e){
        	e.printStackTrace();
        }finally {
        	try {
            	workbook.close();
            } catch (IOException e){
                e.printStackTrace();
            } 
        }
    }
	
	public static void createCardExcel(String filePathAndName,  List<CardBean> dataList) {
		// 创建一个新的工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        Tools.println(filePathAndName);
        // 创建一个工作表
        Sheet sheet = workbook.createSheet("卡密");
 
        // 创建一个行，并在其中创建一个单元格
        Row rowOne = sheet.createRow(0);
		Cell rowOneCell1 = rowOne.createCell(0);
		rowOneCell1.setCellValue("卡号");
        Cell rowOneCell2 = rowOne.createCell(1);
        rowOneCell2.setCellValue("密码");
        
        for(int i= 0; i < dataList.size(); i++) {
        	CardBean cardBean = dataList.get(i);
	        Row row = sheet.createRow(i+1);
	        Cell cell1 = row.createCell(0);
	        cell1.setCellValue(cardBean.getId());
	        Cell cell2 = row.createCell(1);
	        cell2.setCellValue(cardBean.getPasswd());
        }
 
        // 写出到文件
        try{
        	FileOutputStream fileOut = new FileOutputStream(filePathAndName);
            workbook.write(fileOut);
            workbook.close();
        } catch (Exception e){
        	e.printStackTrace();
        }finally {
        	try {
            	workbook.close();
            } catch (IOException e){
                e.printStackTrace();
            } 
        }
    }
	
	public static void createTrailCodeActivedExcel(String filePathAndName,  List<ZyzdInvitationCode> dataList) {
		// 创建一个新的工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        Tools.println(filePathAndName);
        // 创建一个工作表
        Sheet sheet = workbook.createSheet("卡密");
 
        // 创建一个行，并在其中创建一个单元格
        Row rowOne = sheet.createRow(0);
		Cell rowOneCell1 = rowOne.createCell(0);
		rowOneCell1.setCellValue("体验码");
		Cell rowOneCell2 = rowOne.createCell(1);
		rowOneCell2.setCellValue("账号ID");
        Cell rowOneCell3 = rowOne.createCell(2);
        rowOneCell3.setCellValue("激活时间");
        Cell rowOneCell4 = rowOne.createCell(3);
        rowOneCell4.setCellValue("绑定成绩");
        Cell rowOneCell5 = rowOne.createCell(4);
        rowOneCell5.setCellValue("电话");
        Cell rowOneCell6 = rowOne.createCell(5);
        rowOneCell6.setCellValue("升级VIP");
        
        for(int i= 0; i < dataList.size(); i++) {
        	ZyzdInvitationCode bean = dataList.get(i);
	        Row row = sheet.createRow(i+1);
	        Cell cell1 = row.createCell(0);
	        cell1.setCellValue(bean.getIvt_id());
	        Cell cell2 = row.createCell(1);
	        cell2.setCellValue(bean.getUser_c_id());
	        Cell cell3 = row.createCell(2);
	        cell3.setCellValue(Tools.view(Tools.getDate4(bean.getActive_tm())));
	        Cell cell4 = row.createCell(3);
	        cell4.setCellValue(Tools.view(ZyzdCache.getUserCardProvince(bean.getExt_prov()).getName()) + Tools.viewXK(bean.getExt_xk()) + bean.getExt_score_org());
	        Cell cell5 = row.createCell(4);
	        cell5.setCellValue(Tools.view(bean.getUser_phone()));
	        Cell cell6 = row.createCell(5);
	        cell6.setCellValue("FU".equals(bean.getExt_sys_ind())?"已升级":"未升级");
        }
 
        // 写出到文件
        try{
        	FileOutputStream fileOut = new FileOutputStream(filePathAndName);
            workbook.write(fileOut);
            workbook.close();
        } catch (Exception e){
        	e.printStackTrace();
        }finally {
        	try {
            	workbook.close();
            } catch (IOException e){
                e.printStackTrace();
            } 
        }
    }
	
	//导出诊断
	public static void createZdResultExcel(String filePathAndName, String userProvinceName, String firstRowTitle, List<ZyzdUniversityDataBean> dataList, List<ZyzdUniversityDataBean> dataList10More, List<ZyzdUniversityDataBean> dataList20More, List<ZyzdUniversityDataBean> dataList30More) {
		// 创建一个新的工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        
        CellStyle cellStyleFontColumnHeader = workbook.createCellStyle();
        cellStyleFontColumnHeader.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontColumnHeader = workbook.createFont();
        fontColumnHeader.setFontHeightInPoints((short) 12);
        fontColumnHeader.setBold(true);
        fontColumnHeader.setColor(IndexedColors.BLACK.getIndex());
        fontColumnHeader.setFontName("微软雅黑");
        cellStyleFontColumnHeader.setFont(fontColumnHeader);
        cellStyleFontColumnHeader.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        cellStyleFontColumnHeader.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyleFontColumnHeader.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontColumnHeader.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontColumnHeader.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontColumnHeader.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontNormal1 = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontNormal1.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontNormal = workbook.createFont();
        fontNormal.setFontHeightInPoints((short) 10);
        fontNormal.setBold(false);
        fontNormal.setColor(IndexedColors.BLACK.getIndex());
        fontNormal.setFontName("微软雅黑");
        cellStyleFontNormal1.setFont(fontNormal);
        cellStyleFontNormal1.setFillForegroundColor(IndexedColors.LEMON_CHIFFON.getIndex());
        cellStyleFontNormal1.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontNormal1.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontNormal1.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontNormal1.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontNormal1Small = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontNormal1Small.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontNormalSmall = workbook.createFont();
        fontNormalSmall.setFontHeightInPoints((short) 6);
        fontNormalSmall.setBold(false);
        fontNormalSmall.setColor(IndexedColors.BLACK.getIndex());
        fontNormalSmall.setFontName("微软雅黑");
        cellStyleFontNormal1Small.setFont(fontNormalSmall);
        cellStyleFontNormal1Small.setFillForegroundColor(IndexedColors.LEMON_CHIFFON.getIndex());
        cellStyleFontNormal1Small.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontNormal1Small.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontNormal1Small.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontNormal1Small.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontNormal2 = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontNormal2.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontNorma2 = workbook.createFont();
        fontNorma2.setFontHeightInPoints((short) 10);
        fontNorma2.setBold(true);
        fontNorma2.setColor(IndexedColors.BLUE.getIndex());
        fontNorma2.setFontName("微软雅黑");
        cellStyleFontNormal2.setFont(fontNorma2);
        cellStyleFontNormal2.setFillForegroundColor(IndexedColors.LEMON_CHIFFON.getIndex());
        cellStyleFontNormal2.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontNormal2.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontNormal2.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontNormal2.setBorderRight(BorderStyle.THIN);//右边框
        

        // 创建一个工作表(当前可上)
        createZDSheet(workbook, cellStyleFontColumnHeader, cellStyleFontNormal1, cellStyleFontNormal2, "当前可上", userProvinceName, firstRowTitle, dataList);
        // 创建一个工作表(多考10分)
        if(dataList10More.size() > 0) {
        	createZDSheet(workbook, cellStyleFontColumnHeader, cellStyleFontNormal1, cellStyleFontNormal2, "多考10分", userProvinceName, firstRowTitle, dataList10More);
        }
        // 创建一个工作表(多考20分)
        if(dataList20More.size() > 0) {
        	createZDSheet(workbook, cellStyleFontColumnHeader, cellStyleFontNormal1, cellStyleFontNormal2, "多考20分", userProvinceName, firstRowTitle, dataList20More);
        }
        // 创建一个工作表(多考30分)
        if(dataList30More.size() > 0) {
        	createZDSheet(workbook, cellStyleFontColumnHeader, cellStyleFontNormal1, cellStyleFontNormal2, "多考30分", userProvinceName, firstRowTitle, dataList30More);
        }
        
        
        
 
        // 写出到文件
        try{
        	FileOutputStream fileOut = new FileOutputStream(filePathAndName);
            workbook.write(fileOut);
            workbook.close();
        } catch (Exception e){
        	e.printStackTrace();
        }finally {
        	try {
            	workbook.close();
            } catch (IOException e){
                e.printStackTrace();
            } 
        }
    }
	
	public static void createTrialCodeExcel(String filePathAndName,  List<ZyzdInvitationCode> dataList) {
		// 创建一个新的工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        Tools.println(filePathAndName);
        // 创建一个工作表
        Sheet sheet = workbook.createSheet("卡密");
 
        // 创建一个行，并在其中创建一个单元格
        Row rowOne = sheet.createRow(0);
		Cell rowOneCell1 = rowOne.createCell(0);
		rowOneCell1.setCellValue("体验码");
        
        for(int i= 0; i < dataList.size(); i++) {
        	ZyzdInvitationCode cardBean = dataList.get(i);
	        Row row = sheet.createRow(i+1);
	        Cell cell1 = row.createCell(0);
	        cell1.setCellValue(cardBean.getIvt_id());
        }
 
        // 写出到文件
        try{
        	FileOutputStream fileOut = new FileOutputStream(filePathAndName);
            workbook.write(fileOut);
            workbook.close();
        } catch (Exception e){
        	e.printStackTrace();
        }finally {
        	try {
            	workbook.close();
            } catch (IOException e){
                e.printStackTrace();
            } 
        }
    }
	
	public static void createYxZyRankingExcel(String filePathAndName,  HashMap<String, List<ZyzdMajorRanking>> dataListMap) {
		// 创建一个新的工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        Tools.println(filePathAndName);
        createYxZyRankingExcel(workbook, dataListMap);
 
        // 写出到文件
        try{
        	FileOutputStream fileOut = new FileOutputStream(filePathAndName);
            workbook.write(fileOut);
            workbook.close();
        } catch (Exception e){
        	e.printStackTrace();
        }finally {
        	try {
            	workbook.close();
            } catch (IOException e){
                e.printStackTrace();
            } 
        }
    }
	
	private static void createYxZyRankingExcel(XSSFWorkbook workbook, HashMap<String, List<ZyzdMajorRanking>> dataListMap) {
		Iterator<String> it = dataListMap.keySet().iterator();
        while(it.hasNext()) {
        	String tabName = it.next();
        	List<ZyzdMajorRanking> dataList = dataListMap.get(tabName);
        	// 创建一个工作表
            Sheet sheet = workbook.createSheet(tabName);
            sheet.setColumnWidth(0, 8*256);
            sheet.setColumnWidth(1, 8*256);
            sheet.setColumnWidth(2, 20*256);
            sheet.setColumnWidth(3, 10*256);
            sheet.setColumnWidth(4, 16*256);
            sheet.setColumnWidth(5, 25*256);
            sheet.setColumnWidth(6, 15*256);
            
            CellStyle cellStyleFontGreen = workbook.createCellStyle();
            //必须设置 否则背景色不生效
            cellStyleFontGreen.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            Font fontGreen = workbook.createFont();
            fontGreen.setFontHeightInPoints((short) 12);
            fontGreen.setBold(true);
            fontGreen.setColor(IndexedColors.GREEN.getIndex());
            cellStyleFontGreen.setFont(fontGreen);
            
            CellStyle cellStyleFontRed = workbook.createCellStyle();
            //必须设置 否则背景色不生效
            cellStyleFontRed.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            Font fontRed = workbook.createFont();
            fontRed.setFontHeightInPoints((short) 12);
            fontRed.setColor(IndexedColors.RED.getIndex());
            cellStyleFontRed.setFont(fontRed);
            
            // 创建一个行，并在其中创建一个单元格
            Row rowOne = sheet.createRow(0);
    		Cell rowOneCell1 = rowOne.createCell(0);
    		rowOneCell1.setCellValue("排名");
            Cell rowOneCell2 = rowOne.createCell(1);
            rowOneCell2.setCellValue("院校名称");
            Cell rowOneCell3 = rowOne.createCell(2);
            rowOneCell3.setCellValue("院校属性");
            Cell rowOneCell4 = rowOne.createCell(3);
            rowOneCell4.setCellValue("院校最低");
            Cell rowOneCell5 = rowOne.createCell(4);
            rowOneCell5.setCellValue("专业名称");
            Cell rowOneCell6 = rowOne.createCell(5);
            rowOneCell6.setCellValue("专业最低");
            
            for(int i= 0; i < dataList.size(); i++) {
            	ZyzdMajorRanking bean = dataList.get(i);
    	        Row row = sheet.createRow(i+1);
    	        Cell cell1 = row.createCell(0);
    	        cell1.setCellValue(bean.getRanking());
    	        Cell cell2 = row.createCell(1);
    	        cell2.setCellValue(bean.getYxmc());
    	        Cell cell3 = row.createCell(2);
    	        cell3.setCellValue(bean.getExt_tags());
    	        Cell cell4 = row.createCell(3);
    	        if(Tools.isEmpty(bean.getExt_zdf())) {
    	        	cell4.setCellValue("--");
    	        }else {
    	        	cell4.setCellStyle(bean.isExt_is_overload() ? cellStyleFontRed:cellStyleFontGreen);
    	        	cell4.setCellValue(bean.getExt_zdf()+"("+bean.getExt_zdfwc()+")");
    	        }
    	        Cell cell5 = row.createCell(4);
    	        cell5.setCellValue(bean.getZymc());
    	        Cell cell6 = row.createCell(5);
    	        if(Tools.isEmpty(bean.getExt_zy_zdf())) {
    	        	cell6.setCellValue("--");
    	        }else {
    	        	cell6.setCellValue(bean.getExt_zy_zdf()+"("+bean.getExt_zy_zdfwc()+")");
    	        }
            }
        }
    }
	
	public static void createZDSheet(XSSFWorkbook workbook, CellStyle cellStyleFontColumnHeader, CellStyle cellStyleFontNormal1, CellStyle cellStyleFontNormal2, String tabName, String userProvinceName, String firstRowTitle, List<ZyzdUniversityDataBean> dataList) {
		Sheet sheet = workbook.createSheet("换算方案("+tabName+")");
        sheet.setColumnWidth(0, 30*256);
        sheet.setColumnWidth(1, 10*256);
        sheet.setColumnWidth(2, 10*256);
        sheet.setColumnWidth(3, 10*256);
        sheet.setColumnWidth(4, 15*256);
        sheet.setColumnWidth(5, 15*256); 
        sheet.setColumnWidth(6, 15*256);
 
        if(dataList.size() == 0) {
        	return;
        }
        
        
        Row rowTitle = sheet.createRow(0);
        Cell rowTitleCell1 = rowTitle.createCell(0);
        rowTitleCell1.setCellValue(firstRowTitle+" 的换算方案("+tabName+")");
        CellStyle cellStyle = workbook.createCellStyle();
        CreationHelper creationHelper = workbook.getCreationHelper();
        cellStyle.setDataFormat(creationHelper.createDataFormat().getFormat("text")); // 对于文本，使用"text"格式以保持文本格式不变
        cellStyle.setAlignment(HorizontalAlignment.CENTER); // 设置水平居中对齐
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直居中对齐（如果需要）
        Font fontBlueBig = workbook.createFont();
        fontBlueBig.setFontHeightInPoints((short) 14);
        fontBlueBig.setBold(true);
        fontBlueBig.setColor(IndexedColors.ORANGE.getIndex());
        fontBlueBig.setFontName("微软雅黑");
        cellStyle.setFont(fontBlueBig);
        rowTitleCell1.setCellStyle(cellStyle); // 应用样式到单元格
        CellRangeAddress deviceCellRangeTitle = new CellRangeAddress(0, 0, 0, 6);
		sheet.addMergedRegion(deviceCellRangeTitle);
        
        ZyzdUniversityDataBean first = dataList.get(0);
        
        // 创建一个行，并在其中创建一个单元格
        Row rowOne = sheet.createRow(1);
		Cell rowOneCell1 = rowOne.createCell(0);
		rowOneCell1.setCellValue("院校名称"); 
		rowOneCell1.setCellStyle(cellStyleFontColumnHeader);
		
		Cell rowOneCell2 = rowOne.createCell(1);
		rowOneCell2.setCellValue("院校代码");
		rowOneCell2.setCellStyle(cellStyleFontColumnHeader);
		
        Cell rowOneCell3 = rowOne.createCell(2);
        rowOneCell3.setCellValue("院校性质");
		rowOneCell3.setCellStyle(cellStyleFontColumnHeader);
		
        Cell rowOneCell4 = rowOne.createCell(3);
        rowOneCell4.setCellValue("院校层次");
		rowOneCell4.setCellStyle(cellStyleFontColumnHeader);
		
        Cell rowOneCell5 = rowOne.createCell(4);
        rowOneCell5.setCellValue("所属地区");
		rowOneCell5.setCellStyle(cellStyleFontColumnHeader);
		
        Cell rowOneCell6 = rowOne.createCell(5);
        rowOneCell6.setCellValue(first.getNf() +"最低分");
		rowOneCell6.setCellStyle(cellStyleFontColumnHeader);
		
        Cell rowOneCell7 = rowOne.createCell(6);
        rowOneCell7.setCellValue(first.getNf() +"最低位");
		rowOneCell7.setCellStyle(cellStyleFontColumnHeader);
        
        for(int i= 0; i < dataList.size(); i++) {
        	ZyzdUniversityDataBean bean = dataList.get(i);
        	
        	ZyzdUniversityBean CACHE_UNIV = ZyzdCache.getUniversity(bean.getYxmc_org());
        	if(CACHE_UNIV == null){
        		CACHE_UNIV = new ZyzdUniversityBean();
        	}
        	
        	String yxdm = bean.getYxdm();
        	String zyz = bean.getZyz();
        	String displayYxInfoA = "", displayYxInfoB = "";;
        	if(Tools.isEmpty(yxdm) && Tools.isEmpty(zyz)){
        		
        	}else{
        		if(!Tools.isEmpty(yxdm)){
        			displayYxInfoA = yxdm;
        		}else{
        			displayYxInfoA = zyz;
        		}
        	}
        	
        	
        	String is985 = CACHE_UNIV == null ? null : CACHE_UNIV.getIs985();
        	String is211 = CACHE_UNIV == null ? null : CACHE_UNIV.getIs211();
        	String issyl = CACHE_UNIV == null ? null : CACHE_UNIV.getIssyl();
        	if(Tools.isEmpty(is985) && Tools.isEmpty(is211) && Tools.isEmpty(issyl)){
        		
        	}else{
        		if(!Tools.isEmpty(is985)){
        			displayYxInfoB = "985";
        		}else if(!Tools.isEmpty(is211)){
        			displayYxInfoB = "211";
        		}else{
        			displayYxInfoB = "双一流";
        		}
        	}
        	
        	String yxsf = CACHE_UNIV.getPosition_sf();
        	String yxcs = CACHE_UNIV.getPosition_cs();
        	
	        Row row = sheet.createRow(i + 2);
	        Cell cell1 = row.createCell(0);
	        cell1.setCellStyle(cellStyleFontNormal1);
	        cell1.setCellValue(bean.getYxmc());
	        
	        Cell cell2 = row.createCell(1);
	        cell2.setCellStyle(cellStyleFontNormal1);
	        cell2.setCellValue(bean.getYxdm());
	        
	        Cell cell3 = row.createCell(2);
	        cell3.setCellStyle(cellStyleFontNormal1);
	        cell3.setCellValue(bean.getInd_nature());
	        
	        Cell cell4 = row.createCell(3);
	        cell4.setCellStyle(cellStyleFontNormal1);
	        cell4.setCellValue(displayYxInfoB);
	        
	        Cell cell5 = row.createCell(4);
	        if(userProvinceName.equals(yxsf)) {
	        	cell5.setCellStyle(cellStyleFontNormal2);
	        }else {
	        	cell5.setCellStyle(cellStyleFontNormal1);
	        }
	        cell5.setCellValue(Tools.view(yxsf) + (Tools.isEmpty(yxcs)?"":("."+yxcs)));
	        Cell cell6 = row.createCell(5);
	        cell6.setCellStyle(cellStyleFontNormal1);
	        cell6.setCellValue(bean.getZdf());
	        Cell cell7 = row.createCell(6);
	        cell7.setCellStyle(cellStyleFontNormal1);
	        cell7.setCellValue(bean.getZdfwc());
        }
	}
	
	public static void createFormReportExcel(int jhYear, String filePathAndName, String score, List<ZyzdForm> dataList, HashMap<String, List<ZyzdMajorRanking>> dataListMap) {
		// 创建一个新的工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        Tools.println(filePathAndName);
        // 创建一个工作表
        Sheet sheet = workbook.createSheet("志愿表_"+score+"");
        sheet.setColumnWidth(0, 8*256);
        sheet.setColumnWidth(1, 20*256);
        sheet.setColumnWidth(2, 8*256);
        sheet.setColumnWidth(3, 25*256);
        sheet.setColumnWidth(4, 14*256);
        sheet.setColumnWidth(5, 14*256);
        sheet.setColumnWidth(6, 14*256);
        sheet.setColumnWidth(6, 25*256);
 
        // 创建一个行，并在其中创建一个单元格
        Row rowOne = sheet.createRow(0);
		Cell rowOneCell1 = rowOne.createCell(0);
		rowOneCell1.setCellValue("志愿顺序");
        Cell rowOneCell2 = rowOne.createCell(1);
        rowOneCell2.setCellValue("院校名称");
        Cell rowOneCell3 = rowOne.createCell(2);
        rowOneCell3.setCellValue("专业顺序");
        Cell rowOneCell4 = rowOne.createCell(3);
        rowOneCell4.setCellValue("专业名称");
        Cell rowOneCell5 = rowOne.createCell(4);
        rowOneCell5.setCellValue(String.valueOf(jhYear-1));
        Cell rowOneCell6 = rowOne.createCell(5);
        rowOneCell6.setCellValue(String.valueOf(jhYear-2));
        Cell rowOneCell7 = rowOne.createCell(6);
        rowOneCell7.setCellValue(String.valueOf(jhYear-3));
        Cell rowOneCell8 = rowOne.createCell(7);
        rowOneCell8.setCellValue("排名");
        
        for(int i= 0; i < dataList.size(); i++) {
        	ZyzdForm bean = dataList.get(i);
	        Row row = sheet.createRow(i+1);
	        Cell cell1 = row.createCell(0);
	        cell1.setCellValue(bean.getSeq_no_yx());
	        Cell cell2 = row.createCell(1);
	        cell2.setCellValue(bean.getExt_yxmc());
	        Cell cell3 = row.createCell(2);
	        cell3.setCellValue(bean.getSeq_no_zy());
	        Cell cell4 = row.createCell(3);
	        cell4.setCellValue(bean.getZymc());
	        Cell cell5 = row.createCell(4);
	        if(bean.getZdf_a() < 10) {
	        	cell5.setCellValue("--");
	        }else {
	        	cell5.setCellValue(bean.getZdf_a()+"("+bean.getZdfwc_a()+")");
	        }
	        Cell cell6 = row.createCell(5);
	        if(bean.getZdf_b() < 10) {
	        	cell6.setCellValue("--");
	        }else {
	        	cell6.setCellValue(bean.getZdf_b()+"("+bean.getZdfwc_b()+")");
	        }
	        Cell cell7 = row.createCell(6);
	        if(bean.getZdf_c() < 10) {
	        	cell7.setCellValue("--");
	        }else {
	        	cell7.setCellValue(bean.getZdf_c()+"("+bean.getZdfwc_c()+")");
	        }
	        Cell cell8 = row.createCell(7);
	        if(Tools.isEmpty(bean.getExt_ranking())) {
	        	cell8.setCellValue("--");
	        }else {
	        	cell8.setCellValue(bean.getExt_ranking()+"("+bean.getExt_level()+")");
	        }
        }
        
        //createYxZyRankingExcel(workbook, dataListMap);
 
        // 写出到文件
        try{
        	FileOutputStream fileOut = new FileOutputStream(filePathAndName);
            workbook.write(fileOut);
            workbook.close();
        } catch (Exception e){
        	e.printStackTrace();
        }finally {
        	try {
            	workbook.close();
            } catch (IOException e){
                e.printStackTrace();
            } 
        }
    }
	
	public static void createForm96ReportExcel(int jhYear, String filePathAndName,  List<ZyzdForm> dataList) {
		// 创建一个新的工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        Tools.println(filePathAndName);
        // 创建一个工作表
        Sheet sheet = workbook.createSheet("志愿表");
 
        // 创建一个行，并在其中创建一个单元格
        Row rowOne = sheet.createRow(0);
		Cell rowOneCell1 = rowOne.createCell(0);
		rowOneCell1.setCellValue("志愿顺序");
        Cell rowOneCell2 = rowOne.createCell(1);
        rowOneCell2.setCellValue("院校名称");
        Cell rowOneCell3 = rowOne.createCell(2);
        rowOneCell3.setCellValue("排名");
        Cell rowOneCell4 = rowOne.createCell(3);
        rowOneCell4.setCellValue("专业名称");
        Cell rowOneCell5 = rowOne.createCell(4);
        rowOneCell5.setCellValue(String.valueOf(jhYear-1));
        Cell rowOneCell6 = rowOne.createCell(5);
        rowOneCell6.setCellValue(String.valueOf(jhYear-2));
        Cell rowOneCell7 = rowOne.createCell(6);
        rowOneCell7.setCellValue(String.valueOf(jhYear-3));
        
        for(int i= 0; i < dataList.size(); i++) {
        	ZyzdForm bean = dataList.get(i);
	        Row row = sheet.createRow(i+1);
	        Cell cell1 = row.createCell(0);
	        cell1.setCellValue(bean.getSeq_no_yx());
	        Cell cell2 = row.createCell(1);
	        cell2.setCellValue(bean.getYxmc());
	        Cell cell3 = row.createCell(2);
	        cell3.setCellValue(bean.getExt_ranking()+"("+bean.getExt_level()+")");
	        Cell cell4 = row.createCell(3);
	        cell4.setCellValue(bean.getZymc());
	        Cell cell5 = row.createCell(4);
	        cell5.setCellValue(bean.getZdf_a()+"("+bean.getZdfwc_a()+")");
	        Cell cell6 = row.createCell(5);
	        cell6.setCellValue(bean.getZdf_b()+"("+bean.getZdfwc_b()+")");
	        Cell cell7 = row.createCell(6);
	        cell7.setCellValue(bean.getZdf_c()+"("+bean.getZdfwc_c()+")");
        }
 
        // 写出到文件
        try{
        	FileOutputStream fileOut = new FileOutputStream(filePathAndName);
            workbook.write(fileOut);
            workbook.close();
        } catch (Exception e){
        	e.printStackTrace();
        }finally {
        	try {
            	workbook.close();
            } catch (IOException e){
                e.printStackTrace();
            } 
        }
    }

}
