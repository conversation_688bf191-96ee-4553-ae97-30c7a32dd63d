package com.career.utils;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;

public class DealWithData {
	
	public static void main(String args[]) throws Exception{
		StringBuffer sb = new StringBuffer();
		
		for(int i=2;i<=1574;i++) {//1574
			BufferedReader bw = new BufferedReader(new FileReader(new File("E://kaoyan//" + i +"//info_" + i + "_data.txt")));
			String str = null;
			System.out.println(i);
			while((str = bw.readLine()) != null) {
				String schoolName = "",num_master = "", num_master_2nd = "", num_doctor = "", num_doctor_2nd = "", num_subject = "", num_lab = "", intro = "" ;
				if(str.indexOf("school_name") != -1) {
					schoolName = str.substring(str.indexOf("school_name")+14, str.indexOf("province")-3);
				}
				
				if(str.indexOf("num_master") != -1) {
					if(str.indexOf("num_master_2nd") == -1) {
						break;
					}
					String x2 = str.substring(str.indexOf("num_master"), str.indexOf("create_date"));
					num_master = x2.substring(12, x2.indexOf("num_master_2nd") -2);
					num_master_2nd = x2.substring(x2.indexOf("num_master_2nd") + 16, x2.indexOf("num_doctor") -2);
					num_doctor = x2.substring(x2.indexOf("num_doctor") + 12, x2.indexOf("num_doctor_2nd") -2);
					num_doctor_2nd = x2.substring(x2.indexOf("num_doctor_2nd") + 16, x2.indexOf("num_subject") -2);
					num_subject = x2.substring(x2.indexOf("num_subject") + 13, x2.indexOf("num_lab") -2);
					num_lab = x2.substring(x2.indexOf("num_lab") + 9, x2.length() - 2);
				}
				
				if(str.indexOf("intro") != -1) {
					intro = str.substring(str.indexOf("intro")+8, str.indexOf("content_id") - 3);
				}
				
				int rankIndex = str.indexOf("rank\":[{");
				if(rankIndex != -1) {
					//System.out.println("4:"+str.substring(rankIndex+6, str.indexOf("}]") + 2));
					
				}
				
				if(Tools.isEmpty(schoolName)) {
					continue;
				}
				sb.append("INSERT INTO XXX_YYY(school_code, school_name, num_master, master_2nd, num_doctor, num_doctor_2nd, num_subject, num_lab, intro) values('"+i+"', '"+schoolName+"','"+num_master+"','"+num_master_2nd+"','"+num_doctor+"','"+num_doctor_2nd+"','"+num_subject+"','"+num_lab+"','"+intro+"'); \r\n");
			}
			
			writeTempFile(new File("E://schoolinfoSQL.sql"), sb);
		}
		
		//System.out.println(sb);
	}
	
	
	
	
	private static void writeTempFile(File file, StringBuffer sb) {
	    try {
	      BufferedWriter bw = new BufferedWriter(new FileWriter(file));
	      bw.write(sb.toString());
	      bw.flush();
	      bw.close();
	    } catch (IOException e) {
	      e.printStackTrace();
	    } 
	  }

}
