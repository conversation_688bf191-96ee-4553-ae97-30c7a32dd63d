package com.career.utils.liuxue;

public class School2 {

	private String college_school_id;
	private String college_school_name;
	private String count;
	private String degree;
	private String description;
	private String fall_deadline;
	private String general_rank;
	private String gpa;
	private String gre;
	private String has_reason;
	private String rating;
	private String rating_count;
	private String school_name;
	private String short_name;
	private String toefl;
	private String stat_rank;
	
	private String program_full_name;
	private String program_name;
	private String program_link;
	private String major;
	
	public String getProgram_full_name() {
		return program_full_name;
	}

	public void setProgram_full_name(String program_full_name) {
		this.program_full_name = program_full_name;
	}

	public String getProgram_name() {
		return program_name;
	}

	public void setProgram_name(String program_name) {
		this.program_name = program_name;
	}

	public String getProgram_link() {
		return program_link;
	}

	public void setProgram_link(String program_link) {
		this.program_link = program_link;
	}

	public String getMajor() {
		return major;
	}

	public void setMajor(String major) {
		this.major = major;
	}


	private School college_info;

	public String getCollege_school_id() {
		return college_school_id;
	}

	public void setCollege_school_id(String college_school_id) {
		this.college_school_id = college_school_id;
	}

	public String getCollege_school_name() {
		return college_school_name;
	}

	public void setCollege_school_name(String college_school_name) {
		this.college_school_name = college_school_name;
	}

	public String getCount() {
		return count;
	}

	public void setCount(String count) {
		this.count = count;
	}

	public String getDegree() {
		return degree;
	}

	public void setDegree(String degree) {
		this.degree = degree;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getFall_deadline() {
		return fall_deadline;
	}

	public void setFall_deadline(String fall_deadline) {
		this.fall_deadline = fall_deadline;
	}

	public String getGeneral_rank() {
		return general_rank;
	}

	public void setGeneral_rank(String general_rank) {
		this.general_rank = general_rank;
	}

	public String getGpa() {
		return gpa;
	}

	public void setGpa(String gpa) {
		this.gpa = gpa;
	}

	public String getGre() {
		return gre;
	}

	public void setGre(String gre) {
		this.gre = gre;
	}

	public String getHas_reason() {
		return has_reason;
	}

	public void setHas_reason(String has_reason) {
		this.has_reason = has_reason;
	}

	public String getRating() {
		return rating;
	}

	public void setRating(String rating) {
		this.rating = rating;
	}

	public String getRating_count() {
		return rating_count;
	}

	public void setRating_count(String rating_count) {
		this.rating_count = rating_count;
	}

	public String getSchool_name() {
		return school_name;
	}

	public void setSchool_name(String school_name) {
		this.school_name = school_name;
	}

	public String getShort_name() {
		return short_name;
	}

	public void setShort_name(String short_name) {
		this.short_name = short_name;
	}

	public String getToefl() {
		return toefl;
	}

	public void setToefl(String toefl) {
		this.toefl = toefl;
	}

	public String getStat_rank() {
		return stat_rank;
	}

	public void setStat_rank(String stat_rank) {
		this.stat_rank = stat_rank;
	}

	public School getCollege_info() {
		return college_info;
	}

	public void setCollege_info(School college_info) {
		this.college_info = college_info;
	}
	
	
	public String generateSQL() {
		description = description == null ? "":description.replaceAll("’", " ");
		String SQL = "insert into career_lx_school_item(college_school_id,college_school_name, cnt, degree, descp, fall_deadline, general_rank, gpa, gre, program_full_name, program_link, program_name, major, rating, rating_count, school_name, short_name, stat_rank, toefl) " + 
	"values('"+college_school_id + "','" + college_school_name + "','" + count + "','" + degree + "','" + description + "','" + fall_deadline + "','" + general_rank + "','" + gpa + "','" + gre + "','"+program_full_name +"','"+program_link+"','" + program_name + "','" + major + "','" + rating + "','" + rating_count + "','" + school_name + "','" + short_name + "','" + stat_rank + "','" + toefl + "');";
		return SQL;
	}
	
}
