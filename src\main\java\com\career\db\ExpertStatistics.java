package com.career.db;

import java.util.Date;
import com.career.utils.BaseBean;

public class ExpertStatistics extends BaseBean {
    
    // 专家基本信息
    private String c_id;
    private String c_name;
    private String c_phone;
    private Date last_login_tm;
    private Date create_tm;
    private int c_status;
    
    // 客户数量统计
    private int total_orders;           // 总客户数
    private int active_orders;          // 有效客户数 (status 10-19)
    private int paid_orders;            // 已付款客户数 (status 20-29)
    private int deleted_orders;         // 已删除客户数 (status 30)
    private int deposit_orders;         // 定金客户数 (status 21)
    private int full_payment_orders;    // 全款客户数 (status 22)
    
    // 时间统计
    private Date last_order_time;       // 最近创建客户时间
    private int orders_this_week;       // 本周新增客户
    private int orders_this_month;      // 本月新增客户
    
    // 业绩统计
    private double conversion_rate;     // 转化率 (付款客户/总客户)
    
    public static void main(String args[]) {
        printBeanProperties(new ExpertStatistics());
    }
    
    // Getters and Setters
    public String getC_id() {
        return c_id;
    }
    
    public void setC_id(String c_id) {
        this.c_id = c_id;
    }
    
    public String getC_name() {
        return c_name;
    }
    
    public void setC_name(String c_name) {
        this.c_name = c_name;
    }
    
    public String getC_phone() {
        return c_phone;
    }
    
    public void setC_phone(String c_phone) {
        this.c_phone = c_phone;
    }
    
    public Date getLast_login_tm() {
        return last_login_tm;
    }
    
    public void setLast_login_tm(Date last_login_tm) {
        this.last_login_tm = last_login_tm;
    }
    
    public Date getCreate_tm() {
        return create_tm;
    }
    
    public void setCreate_tm(Date create_tm) {
        this.create_tm = create_tm;
    }
    
    public int getC_status() {
        return c_status;
    }
    
    public void setC_status(int c_status) {
        this.c_status = c_status;
    }
    
    public int getTotal_orders() {
        return total_orders;
    }
    
    public void setTotal_orders(int total_orders) {
        this.total_orders = total_orders;
    }
    
    public int getActive_orders() {
        return active_orders;
    }
    
    public void setActive_orders(int active_orders) {
        this.active_orders = active_orders;
    }
    
    public int getPaid_orders() {
        return paid_orders;
    }
    
    public void setPaid_orders(int paid_orders) {
        this.paid_orders = paid_orders;
    }
    
    public int getDeleted_orders() {
        return deleted_orders;
    }
    
    public void setDeleted_orders(int deleted_orders) {
        this.deleted_orders = deleted_orders;
    }
    
    public int getDeposit_orders() {
        return deposit_orders;
    }
    
    public void setDeposit_orders(int deposit_orders) {
        this.deposit_orders = deposit_orders;
    }
    
    public int getFull_payment_orders() {
        return full_payment_orders;
    }
    
    public void setFull_payment_orders(int full_payment_orders) {
        this.full_payment_orders = full_payment_orders;
    }
    
    public Date getLast_order_time() {
        return last_order_time;
    }
    
    public void setLast_order_time(Date last_order_time) {
        this.last_order_time = last_order_time;
    }
    
    public int getOrders_this_week() {
        return orders_this_week;
    }
    
    public void setOrders_this_week(int orders_this_week) {
        this.orders_this_week = orders_this_week;
    }
    
    public int getOrders_this_month() {
        return orders_this_month;
    }
    
    public void setOrders_this_month(int orders_this_month) {
        this.orders_this_month = orders_this_month;
    }
    
    public double getConversion_rate() {
        return conversion_rate;
    }
    
    public void setConversion_rate(double conversion_rate) {
        this.conversion_rate = conversion_rate;
    }
} 