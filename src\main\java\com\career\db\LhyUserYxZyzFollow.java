package com.career.db;

import java.util.Date;

import com.career.utils.BaseBean;

public class LhyUserYxZyzFollow extends BaseBean{
	
	private int id;
	private String order_id;
	private String yxmc_org;
	private String yxmc;
	private String zyz;
	private Date create_tm;
	
	public static void main(String args[]) {
		LhyUserYxZyzFollow bean = new LhyUserYxZyzFollow();
		printBeanProperties(bean);
	}

	public String getYxmc() {
		return yxmc;
	}

	public void setYxmc(String yxmc) {
		this.yxmc = yxmc;
	}

	public String getZyz() {
		return zyz;
	}

	public void setZyz(String zyz) {
		this.zyz = zyz;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getOrder_id() {
		return order_id;
	}

	public void setOrder_id(String order_id) {
		this.order_id = order_id;
	}

	public String getYxmc_org() {
		return yxmc_org;
	}

	public void setYxmc_org(String yxmc_org) {
		this.yxmc_org = yxmc_org;
	}

	public Date getCreate_tm() {
		return create_tm;
	}

	public void setCreate_tm(Date create_tm) {
		this.create_tm = create_tm;
	}
	
}
