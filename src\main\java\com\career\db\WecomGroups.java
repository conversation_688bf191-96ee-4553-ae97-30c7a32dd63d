package com.career.db;

import java.util.Date;

public class WecomGroups {

	private String wecom_chat_id; // 客户群ID
    private String wecom_name; // 群名
    private String wecom_owner_userid; // 群主成员ID
    private Date wecom_create_time; // 群创建时间戳
    private Integer wecom_member_count; // 群成员数量
    private String wecom_member_version; // 群成员版本信息
    private Date wecom_created_at; // 记录创建时间
    private Date wecom_updated_at; // 记录更新时间
    private String wecom_saas_id; // saas_id
	public String getWecom_chat_id() {
		return wecom_chat_id;
	}
	public void setWecom_chat_id(String wecom_chat_id) {
		this.wecom_chat_id = wecom_chat_id;
	}
	public String getWecom_name() {
		return wecom_name;
	}
	public void setWecom_name(String wecom_name) {
		this.wecom_name = wecom_name;
	}
	public String getWecom_owner_userid() {
		return wecom_owner_userid;
	}
	public void setWecom_owner_userid(String wecom_owner_userid) {
		this.wecom_owner_userid = wecom_owner_userid;
	}
	public Date getWecom_create_time() {
		return wecom_create_time;
	}
	public void setWecom_create_time(Date wecom_create_time) {
		this.wecom_create_time = wecom_create_time;
	}
	public Integer getWecom_member_count() {
		return wecom_member_count;
	}
	public void setWecom_member_count(Integer wecom_member_count) {
		this.wecom_member_count = wecom_member_count;
	}
	public String getWecom_member_version() {
		return wecom_member_version;
	}
	public void setWecom_member_version(String wecom_member_version) {
		this.wecom_member_version = wecom_member_version;
	}
	public Date getWecom_created_at() {
		return wecom_created_at;
	}
	public void setWecom_created_at(Date wecom_created_at) {
		this.wecom_created_at = wecom_created_at;
	}
	public Date getWecom_updated_at() {
		return wecom_updated_at;
	}
	public void setWecom_updated_at(Date wecom_updated_at) {
		this.wecom_updated_at = wecom_updated_at;
	}
	public String getWecom_saas_id() {
		return wecom_saas_id;
	}
	public void setWecom_saas_id(String wecom_saas_id) {
		this.wecom_saas_id = wecom_saas_id;
	}
    
    
}
