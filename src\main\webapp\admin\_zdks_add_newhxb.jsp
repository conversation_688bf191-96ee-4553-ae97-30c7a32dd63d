<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ page import="com.zsdwf.utils.*,com.career.db.*,com.zsdwf.db.*,com.career.utils.*,java.util.*,com.career.utils.Tools,com.career.db.DealZdksSQL,com.career.db.CityBean,com.zsdwf.db.JDBC"%>

<%@include file="/WEB-INF/include/_session_admin_ajax.jsp"%>
<%
String new_title = Tools.trim(request.getParameter("new_title"));
String new_rq = Tools.trim(request.getParameter("new_rq"));
String new_desc = Tools.trim(request.getParameter("new_desc"));
String selectedHxbValues = Tools.trim(request.getParameter("selectedHxbValues"));
String prov = Tools.trim(request.getParameter("prov"));

if(Tools.isEmpty(new_title) || Tools.isEmpty(prov) || Tools.isEmpty(selectedHxbValues)){
	out.print("ERR:LOGIN:REDIRECT-X_LOGIN");
	return;
}

com.career.db.ZyzdJDBC jdbc = new com.career.db.ZyzdJDBC();
ZyzdProvince zyzdProvince = ZyzdCache.getUserCardProvinceName(prov);
if(zyzdProvince == null){
	out.print("ERR:LOGIN:REDIRECT-X_LOGIN");
	return;
}


//{'list': [{‘name’: '本二批', 'score':432}.....]}
String id = UUID.randomUUID().toString();
StringBuffer str = new StringBuffer();
str.append("{'list':[");
String[] selectedHxbValuesArray = selectedHxbValues.split(",");
for(int ix = 0;ix < selectedHxbValuesArray.length; ix++){
	String ss = selectedHxbValuesArray[ix];
	String[] each = ss.split("->");
	String name = each[0];
	String score = each[1];
	str.append("{'name': '"+name+"', 'score':'"+score+"'}" + ( (ix < selectedHxbValuesArray.length - 1) ? ",":"" ));
}
str.append("]}");

ZdksHxb zdksHxb = new ZdksHxb();
zdksHxb.setId(id);
zdksHxb.setZd_name(new_title);
zdksHxb.setZd_rq(new_rq);
zdksHxb.setZd_desc(new_desc);
zdksHxb.setZd_hx(str.toString()); 

jdbc.insertZdksHxb(zyzdProvince.getTableName(), zdksHxb);

%> 




