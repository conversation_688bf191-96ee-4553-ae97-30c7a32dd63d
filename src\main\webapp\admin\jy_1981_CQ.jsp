<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page import="com.career.utils.*,com.career.db.*,java.util.*"%>
<%@include file="/WEB-INF/include/_session_admin.jsp" %>
<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<script src="<%=request.getContextPath()%>/sources/jquery-3.6.4.min.js"></script>
<title></title>
</head>
<body>
<%
ZyzdJDBC jdbc = new ZyzdJDBC();
List<JiuyeBean> jyList2025 = jdbc.getAllGroupNameAndLsy(2025);
List<JiuyeBean> jyList2024 = jdbc.getAllGroupNameAndLsy(2024);
%>
<div style="float:left;width:300px;font-size:12px;">  
<%for(JiuyeBean bean : jyList2025){ %>
<div onclick="MM_pickto_input(this);" onmouseover="MM_change_color(true, this);" onmouseout="MM_change_color(false, this);"><%="2025_"+bean.getSshy()+"_"+bean.getSsgs()+"_"+bean.getCnt() %></div>
<%} %>
<%for(JiuyeBean bean : jyList2024){ %>
<div onclick="MM_pickto_input(this);" onmouseover="MM_change_color(true, this);" onmouseout="MM_change_color(false, this);"><%="2024_"+bean.getSshy()+"_"+bean.getSsgs()+"_"+bean.getCnt() %></div>
<%} %>
</div>
 
<div style="float:left;margin-left:10px;font-size:10px;"> 
	年份：<input type="text" name="nf" id="nf"/><br> 
	所属行业：<input type="text" name="group_name" id="group_name"/><br>
	 
	单位名称：<input type="text" name="lsy_name" id="lsy_name"/><br>
	
	<input type="button" value="查询" onclick="MM_search();">
	
	<div id="result_html"></div>
</div>
</body>
</html>

<script>
function MM_change_color(v,o){
	if(v){
		$(o).css("color","blue");
	}else{
		$(o).css("color","#000");
	}
}

function MM_pickto_input(thisObj){
	var val = thisObj.innerHTML;
	var valArray = val.split("_");
	$("#nf").val(valArray[0]);
	$("#group_name").val(valArray[1]);
	$("#lsy_name").val(valArray[2]);
}

function MM_search(){
	if($("#group_name").val() == ""){
		alert("请输入行业");
		return false;
	}
	$.ajax({
        type : "POST",
        url : "<%=request.getContextPath()%>/admin/_jy_search.jsp",
		data: {
			"nf" : $("#nf").val(), 
			"group_name" : $("#group_name").val(), 
			"lsy_name" : $("#lsy_name").val(), 
			'rd' : new Date().getTime()
		}, 
		//dataType: "json",
        //请求成功
        success : function(result) {
        	if(result.indexOf("reload:refresh-list") != -1){

        	}else if(result.indexOf("ERR:LOGIN:REDIRECT-X_LOGIN") != -1){
				location.href = "<%=request.getContextPath()%>/admin/login.jsp";
        	}else{
        		var resultHTML = $.trim(result);
        		$("#result_html").html(resultHTML);
        	}
        },
        //请求失败，包含具体的错误信息
        error : function(e){
            console.log(e.status);
            console.log(e.responseText);
        }
    });
	
}
</script>

