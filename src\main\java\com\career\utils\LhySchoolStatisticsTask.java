package com.career.utils;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.career.db.JDBCConstants;
import com.career.db.LhyCardSchoolStudent;
import com.career.db.ZyzdFormMain;

/**
 * 学校统计任务定时器
 * 用于获取学校学生的志愿填报数据统计
 */
public class LhySchoolStatisticsTask {

    static String URL = JDBCConstants.URL;
    static String USER = JDBCConstants.USER;
    static String PASSWD = JDBCConstants.PASSWD;
    
    static {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    
    /**
     * 
     * 主方法，用于测试
     * 
     */
    public static void main(String[] args) {
    	
        Tools.println("===== 开始测试 LhySchoolStatisticsTask =====");
        
        LhySchoolStatisticsTask task = new LhySchoolStatisticsTask();
        
        try {
            List<LhyCardSchoolStudent> studentList = task.getAllStudentVolunteerForms();
            
            printLhyCardSchoolStudentList(studentList);
            
        } catch (Exception e) {
        	Tools.println("测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
        
        Tools.println("===== 测试完成 =====");
    }
    
    /**
     * 获取所有学生的志愿记录
     * 通过关联 lhy_card_school_student 和 s5_sc_form_maker_main 表
     * 
     * @return 按学生分组的志愿记录列表，每个学生包含其所有志愿表
     */
    public List<LhyCardSchoolStudent> getAllStudentVolunteerForms() {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        Map<String, LhyCardSchoolStudent> studentMap = new HashMap<>();
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            String sql = "SELECT " +
                        "s.st_id, s.school_name, s.class_name, s.student_name, s.base_c_id, s.lhy_c_id, " +
                        "f.id, f.batch_id, f.score_cj, f.score_wc, f.score_xk, f.f_no, f.c_id, " +
                        "f.selected_zymc, f.pc, f.pc_code, f.nf, f.create_tm, f.last_update_tm, " +
                        "f.joined_checker_cnt, f.last_checker_id, f.last_checker_name, f.last_checker_remark, " +
                        "f.status, f.f_type " +
                        "FROM lhy_card_school_student s " +
                        "INNER JOIN s5_sc_form_maker_main f ON s.base_c_id = f.c_id " +
                        "WHERE f.status = 1 " +  
                        "ORDER BY s.school_name, s.class_name, s.student_name, f.create_tm DESC";
            
            ps = conn.prepareStatement(sql);
            
            SQLLogUtils.printSQL(" ===getAllStudentVolunteerForms : ", ps);
            
            rs = ps.executeQuery();
            
            while (rs.next()) {
                String stId = rs.getString("st_id");
                
                // 如果学生不存在于Map中，创建新的学生对象
                LhyCardSchoolStudent student = studentMap.get(stId);
                if (student == null) {
                    student = populateLhyCardSchoolStudentFromResultSet(rs);
                    student.setZyzdFormMainList(new ArrayList<>());
                    studentMap.put(stId, student);
                }
                
                // 创建志愿表对象并添加到学生的志愿表列表中
                ZyzdFormMain form = populateZyzdFormMainFromResultSet(rs);
                student.getZyzdFormMainList().add(form);
            }
            
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return new ArrayList<>(studentMap.values());
    }
    
    /**
     * 从ResultSet中填充LhyCardSchoolStudent对象
     * 
     * @param rs ResultSet对象
     * @return 填充好的LhyCardSchoolStudent对象
     * @throws SQLException 如果从ResultSet中读取数据出错
     */
    private LhyCardSchoolStudent populateLhyCardSchoolStudentFromResultSet(ResultSet rs) throws SQLException {
        LhyCardSchoolStudent student = new LhyCardSchoolStudent();
        
        student.setSt_id(rs.getString("st_id"));
        student.setSchool_name(rs.getString("school_name"));
        student.setClass_name(rs.getString("class_name"));
        student.setStudent_name(rs.getString("student_name"));
        student.setBase_c_id(rs.getString("base_c_id"));
        student.setLhy_c_id(rs.getString("lhy_c_id"));
        
        return student;
    }
    
    /**
     * 从ResultSet中填充ZyzdFormMain对象
     * 
     * @param rs ResultSet对象
     * @return 填充好的ZyzdFormMain对象
     * @throws SQLException 如果从ResultSet中读取数据出错
     */
    private ZyzdFormMain populateZyzdFormMainFromResultSet(ResultSet rs) throws SQLException {
        ZyzdFormMain form = new ZyzdFormMain();
        
        form.setId(rs.getInt("id"));
        form.setBatch_id(rs.getString("batch_id"));
        form.setScore_cj(rs.getInt("score_cj"));
        form.setScore_wc(rs.getInt("score_wc"));
        form.setScore_xk(rs.getString("score_xk"));
        form.setF_no(rs.getString("f_no"));
        form.setC_id(rs.getString("c_id"));
        form.setSelected_zymc(rs.getString("selected_zymc"));
        form.setPc(rs.getString("pc"));
        form.setPc_code(rs.getString("pc_code"));
        form.setNf(rs.getInt("nf"));
        form.setCreate_tm(rs.getTimestamp("create_tm"));
        form.setLast_update_tm(rs.getTimestamp("last_update_tm"));
        form.setJoined_checker_cnt(rs.getInt("joined_checker_cnt"));
        form.setChecker_id(rs.getString("last_checker_id"));
        form.setChecker_name(rs.getString("last_checker_name"));
        form.setChecker_remark(rs.getString("last_checker_remark"));
        form.setStatus(rs.getInt("status"));
        form.setF_type(rs.getInt("f_type"));
        
        return form;
    }
    
    /**
     * 关闭数据库连接
     */
    private static void closeAllConnection(Connection conn, PreparedStatement ps, ResultSet rs) {
        try {
            if (rs != null)
                rs.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        try {
            if (ps != null)
                ps.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        try {
            if (conn != null)
                conn.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        rs = null;
        ps = null;
        conn = null;
    }
    
    
	private static void printLhyCardSchoolStudentList(List<LhyCardSchoolStudent> studentList) {
    	
        Tools.println("查询结果学生总数: " + studentList.size());
        
        int totalForms = 0;
        int aiFormCount = 0;
        int manualFormCount = 0;
        
        // 统计总志愿表数量
        for (LhyCardSchoolStudent student : studentList) {
            if (student.getZyzdFormMainList() != null) {
                totalForms += student.getZyzdFormMainList().size();
                for (ZyzdFormMain form : student.getZyzdFormMainList()) {
                    if (form.getF_type() == 1) {
                        aiFormCount++;
                    } else {
                        manualFormCount++;
                    }
                }
            }
        }
        
        Tools.println("志愿表总数: " + totalForms);
        Tools.println("AI生成志愿表数: " + aiFormCount);
        Tools.println("人工填报志愿表数: " + manualFormCount);
        Tools.println("===== 前5名学生详情 =====");
        
        for (int i = 0; i < Math.min(5, studentList.size()); i++) {
            LhyCardSchoolStudent student = studentList.get(i);
            Tools.println("学生 " + (i + 1) + ":");
            Tools.println("  学生ID: " + student.getSt_id());
            Tools.println("  学生姓名: " + student.getStudent_name());
            Tools.println("  学校名称: " + student.getSchool_name());
            Tools.println("  班级名称: " + student.getClass_name());
            Tools.println("  基础卡ID: " + student.getBase_c_id());
            Tools.println("  一对一ID: " + student.getLhy_c_id());
            
            if (student.getZyzdFormMainList() != null) {
                Tools.println("  志愿表数量: " + student.getZyzdFormMainList().size());
                
                // 显示前2个志愿表详情
                for (int j = 0; j < Math.min(2, student.getZyzdFormMainList().size()); j++) {
                    ZyzdFormMain form = student.getZyzdFormMainList().get(j);
                    Tools.println("    志愿表 " + (j + 1) + ":");
                    Tools.println("      ID: " + form.getId());
                    Tools.println("      批次: " + form.getPc());
                    Tools.println("      成绩: " + form.getScore_cj());
                    Tools.println("      位次: " + form.getScore_wc());
                    Tools.println("      类型: " + (form.getF_type() == 1 ? "AI" : "人工"));
                    Tools.println("      创建时间: " + form.getCreate_tm());
                }
            } else {
                Tools.println("  志愿表数量: 0");
            }
            Tools.println("  -------------------------");
        }
    }
    

}
