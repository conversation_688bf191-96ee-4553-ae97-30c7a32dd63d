package com.career.db;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;

import com.career.db.career.MajorPlan;
import com.career.utils.Tools;

public class SYjdbc {
	
	static String URL = "*******************************************************************************************************************";
	static String USER = "kimi_mysql_connector_zyzd";
	static String PASSWD = "88uejjsf02__+&%@@@!!@JGD+jlmjKKJ";

	public static StringBuffer SQL_all = new StringBuffer();
	
	public static void main(String args[]) {
		SYjdbc jdbc = new SYjdbc();
		jdbc.pickJiuyeYxByGroupAndLsy();
	}
	
	
	public void search(String table) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT * FROM `"+table+"` ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
		
			rs = ps.executeQuery();
			int k = 0;
			String newGW = null;
			boolean is_bx = false;
			while (rs.next()) {
				String f1 = rs.getString(1);
				String f2 = rs.getString(2);
				String f3 = rs.getString(3);
				String f4 = rs.getString(4);
				if(Tools.isEmpty(f1)) {
					String xx = f1+f2+f3+f4;
					if(!xx.equals("nullnullnullnull")) {
						Tools.println(f1+f2+f3+f4);
					}
					continue;
				}
				if(f1.indexOf("招聘岗位") != -1) {
					String temp = f1.substring(5).trim();
					
					if(newGW == null) {
						newGW = temp;
						is_bx = false;
					}else {
						if(newGW.equals(temp)) {
							//还是之前的
						}else {
							//新的
							newGW = temp;
							is_bx = false;
							
						}
					}
					continue;
				}
				
				if(f1.indexOf("递补") != -1) {
					is_bx = true;
					continue;
				}
				
				String dw = table.substring(0, table.indexOf("2024"));
				String xm = Tools.trim(f1);
				String yxmc = Tools.trim(f2);
				String zymc = Tools.trim(f3);
				String xl = Tools.trim(f4);
				String lsy = dw;
				String group_name = "中国石油";
				String src = table;
				
				String is_bx_view = is_bx ? "递补":"拟录用";
				
				//Tools.println();
				SQL_all.append("insert into career_jy_all_2025(gw,dw,lsy,group_name,src,xm,xl,yxmc,zymc,bz,is_bx,nf,sj,emp_cnt,create_tm)values('"+newGW+"','"+dw+"','"+lsy+"','"+group_name+"','"+src+"','"+xm+"','"+xl+"','"+yxmc+"','"+zymc+"','"+is_bx_view+"',"+(is_bx?"'递补'":null)+",2025,'20250102',1,now());\r\n");
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	private static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	public void pickJiuyeYxByGroupAndLsy() {
		
		List<String> list = new ArrayList<>();
		
		list.add("昆仑能源有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("国际勘探开发有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("中国石油国际事业有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("大庆油田有限责任公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("辽河油田分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("长庆油田分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("塔里木油田分公司2024年秋季高校毕业生招聘拟录用人选公示");
		list.add("新疆油田分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("西南油气田分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("吉林油田分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("大港油田分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("青海油田分公司2024年秋季高校毕业生招聘拟录用人选公示");
		list.add("华北油田分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("吐哈油田分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("冀东油田分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("玉门油田分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("浙江油田分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("中石油煤层气有限责任公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("南方石油勘探开发有限责任公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("中油国际管道公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("勘探开发研究院2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("中石油深圳新能源研究院有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("大庆石化分公司2024年秋季高校毕业生招聘拟录用人选公示");
		list.add("吉林石化分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("抚顺石化分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("辽阳石化分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("兰州石化分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("独山子石化分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("乌鲁木齐石化分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("宁夏石化分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("大连石化分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("大连西太平洋石油化工有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("锦州石化分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("锦西石化分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("大庆炼化分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("哈尔滨石化分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("广西石化分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("四川石化公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("广东石化有限责任公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("中石油云南石化有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("大港石化分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("华北石化分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("呼和浩特石化分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("辽河石化分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("长庆石化分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("中石油克拉玛依石化有限责任公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("庆阳石化分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("中石油燃料油有限责任公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("润滑油分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("东北化工销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("西北化工销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("华东化工销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("华南化工销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("西南化工销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("东北销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("西北销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("中石油昆仑好客有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("北京销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("上海销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("湖北销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("广东销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("云南销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("辽宁销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("吉林销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("黑龙江销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("天津销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("河北销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("山西销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("内蒙古销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("陕西销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("甘肃销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("青海销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("宁夏销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("中石油新疆销售有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("重庆销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("四川销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("贵州销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("西藏销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("江苏销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("浙江销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("安徽销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("福建销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("江西销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("山东销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("河南销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("湖南销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("广西销售分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("中石油海南销售有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("石油化工研究院2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("中石油（上海）新材料研究院有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("蓝海新材料（通州湾）有限责任公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("西部钻探工程有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("长城钻探工程有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("渤海钻探工程有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("川庆钻探工程有限公司2024年秋季高校毕业生招聘拟录用人选公示");
		list.add("东方地球物理勘探有限责任公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("中油测井公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("海洋工程有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("管道局工程有限公司2024年秋季高校毕业生招聘拟录用人选公示");
		list.add("大庆钻探工程有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("中国石油工程建设有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("中国寰球工程有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("中国昆仑工程有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("中国石油集团工程有限公司北京项目管理分公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("中国石油技术开发有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("宝鸡石油机械有限责任公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("宝鸡石油钢管有限责任公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("济柴动力有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("渤海石油装备制造有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("中国石油数智研究院2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("规划总院2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("经济技术研究院2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("工程技术研究院有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("安全环保技术研究院有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("工程材料研究院有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("昆仑数智科技有限责任公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("北京石油管理干部学院2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("石油工业出版社有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("《中国石油报》社有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("中国石油审计服务中心有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("中国石油物资采购中心2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("共享运营有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("昆仑物流有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("中国华油集团有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("装备制造创新中心（北京石油机械有限公司）2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("中油财务有限责任公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("昆仑银行股份有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("昆仑信托有限责任公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("昆仑金融租赁有限责任公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("中石油专属财产保险股份有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		list.add("昆仑保险经纪股份有限公司2024年秋季高校毕业生招聘拟录用和递补人选公示");
		
		
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<String> listMy = new ArrayList<>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SHOW TABLES";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			
			rs = ps.executeQuery();
			while (rs.next()) {
				
				String str = rs.getString(1);
				listMy.add(str);
				
				//search(str);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		
		for(String all : list) {
			boolean isIn = false;
			for(String my : listMy) {
				if(all.indexOf(my) != -1) {
					isIn = true;
				}
			}
			if(!isIn) {
				Tools.println(all);
			}
		}
		
		//writeTempFile(new File("F://就业报告//CQ/PAGE_20250102.txt"), SQL_all);
	}
	
	

	
	private static void closeAllConnection(Connection conn, PreparedStatement ps, ResultSet rs) {
		try {
			if (rs != null)
				rs.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (ps != null)
				ps.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (conn != null)
				conn.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		rs = null;
		ps = null;
		conn = null;
	}

}
