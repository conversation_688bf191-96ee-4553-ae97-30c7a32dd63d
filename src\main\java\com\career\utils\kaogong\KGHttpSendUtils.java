package com.career.utils.kaogong;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.cookie.Cookie;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import com.career.db.OffcnExtUrlBean;
import com.career.db.OffcnExtUrlMapBean;
import com.career.db.SpiderInfoBean;
import com.career.db.SpiderJDBC;
import com.career.utils.HttpSendUtils;
import com.career.utils.Tools;

public class KGHttpSendUtils {

	public static String post(String url, Map<String, String> params, Map<String, String> headers) {
		CloseableHttpClient client = HttpClients.createDefault();
		HttpPost httpPost = new HttpPost(url);
		UrlEncodedFormEntity entity;
		List<NameValuePair> paramPairs = new ArrayList<NameValuePair>();
		if (params != null) {
			for (Map.Entry<String, String> en : params.entrySet()) {
				paramPairs.add(new BasicNameValuePair(en.getKey(), en.getValue()));
			}
		}
		if (headers != null) {
			for (Map.Entry<String, String> en : headers.entrySet()) {
				httpPost.setHeader(en.getKey(), en.getValue());
			}
		}
		try {
			entity = new UrlEncodedFormEntity(paramPairs, "UTF-8");
			httpPost.setEntity(entity);
			HttpResponse resp = client.execute(httpPost);
			HttpEntity respEntity = resp.getEntity();
			if (null != respEntity) {
				return EntityUtils.toString(respEntity, "UTF-8");
			}

		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		} catch (ClientProtocolException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			try {
				client.close();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return null;
	}

	public static String get(String url, Map<String, String> headers) {
		CloseableHttpClient client = HttpClients.createDefault();
		HttpGet httpGet = new HttpGet(url);
		if (headers != null) {
			for (Map.Entry<String, String> en : headers.entrySet()) {
				httpGet.setHeader(en.getKey(), en.getValue());
			}
		}
		try {
			HttpResponse resp = client.execute(httpGet);
			HttpEntity respEntity = resp.getEntity();
			if (null != respEntity) {
				return EntityUtils.toString(respEntity, "UTF-8");
			}
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		} catch (ClientProtocolException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			try {
				client.close();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return null;
	}
	
	public Cookie Cookie(){
        String url = "http://L.xueya63.com";
        BasicCookieStore cookieStore = new BasicCookieStore();
        Cookie cookie = null;  
        try {
	        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
	        HttpPost post = new HttpPost(url);
	        List<NameValuePair> list = new ArrayList<>();
	        list.add(new BasicNameValuePair("",""));
	 
	        post.addHeader("content-Ttpe","application/json; charset=UTF-8");
	        post.setEntity(new UrlEncodedFormEntity(list,"UTF-8"));
	        CloseableHttpResponse httpResponse = httpClient.execute(post);
	        List<Cookie> cookies = cookieStore.getCookies();
	        cookie =cookies.get(0);
	        httpClient.close();
        }catch(Exception ex) {
        	ex.printStackTrace();
        }
        return  cookie;
    }
	
	public static void runOFFCNPager(int pageNumber) {
		//https://tapi.cnjzy.net/api/Public/LineRankList
		Map<String, String> headers = new HashMap<>();
		Map<String, String> params = new HashMap<>();
		params.put("category_ids[]", "2");
		params.put("category_ids[]", "9");
		params.put("category_ids[]", "10");
		params.put("category_ids[]", "11");
		params.put("category_ids[]", "12");
		params.put("category_ids[]", "13");
		params.put("category_ids[]", "14");
		params.put("category_ids[]", "15");
		params.put("category_ids[]", "16");
		params.put("category_ids[]", "17");
		params.put("category_ids[]", "18");
		params.put("category_ids[]", "19");
		params.put("page_size", "50");
		params.put("page", String.valueOf(pageNumber));
	

		headers.put(":Request URL", "https://ajax.eoffcn.com/api/article/getArticle");
		headers.put(":Request Method", "POST");
		
		headers.put("Remote Address", "**************:443");
		headers.put("Referrer Policy", "no-referrer-when-downgrade");
		headers.put(":method", "POST");
		headers.put(":authority", "ajax.eoffcn.com");
		headers.put(":path", "/api/article/getArticle");
		headers.put("Accept", "application/json, text/javascript, */*; q=0.01");
		headers.put("Accept-Encoding", "gzip, deflate, br");
		headers.put("Accept-Language", "zh-CN,zh;q=0.9");
		headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
		
		headers.put("Origin", "https://www.eoffcn.com");
		headers.put("Referer","https://www.eoffcn.com/kszx/");
		headers.put("Host","tapi.cnjzy.net");
		headers.put("Sec-Ch-Ua","\"Not A(Brand\";v=\"99\", \"Google Chrome\";v=\"121\", \"Chromium\";v=\"121\"");
		headers.put("Sec-Ch-Ua-Mobile","?0");
		headers.put("Sec-Ch-Ua-Platform","\"Windows\"");
		
		headers.put("Sec-Fetch-Dest","empty");
		headers.put("Sec-Fetch-Mode","cors");
		headers.put("Sec-Fetch-Site","same-site");
		

		headers.put("acw_tc","0b328f3a17067081288306758ed6daa613894dae1ef459974a20cf26fa6938");
		
		headers.put("User-Agent","Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
		
		String res = HttpSendUtils.post("https://ajax.eoffcn.com/api/article/getArticle", params, headers);
		
		File file = new File("E://offcn//");
		if(!file.exists()) {
			try {
				file.mkdirs();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
	}
	
	
	public static String LAST_PROCESS_HOUR = null; 
	public static synchronized void runCheck() {
		while(true) {
			String hourDate = Tools.getHourDate(new Date());
			if(!hourDate.equals(LAST_PROCESS_HOUR)) {
				System.out.println("start runCheck"); 
				LAST_PROCESS_HOUR = hourDate;
				runOFFCNArticle();
				try {
					int sleepIterval = (int)(Math.random() * 3600000);
					if(sleepIterval < 1200000) {
						sleepIterval = 1200000;
					}
					//晚上2小时执行一次
					if(Integer.parseInt(hourDate.substring(hourDate.length() - 2)) <= 6) {
						sleepIterval = sleepIterval * 2;
					}
					System.out.println(sleepIterval); 
					Thread.sleep(sleepIterval);
				} catch (Exception e) {}
			}
		}
	}
	
	//中公文章解析
	public static void runOFFCNArticle() {
		SpiderJDBC jdbc = new SpiderJDBC();
		int maxOffcnid = jdbc.getMaxOffcnid();
		SpiderInfoBean lastSpiderInfoBean = jdbc.getSpiderInfo("OFFCN.COM");
		if(lastSpiderInfoBean.getSpider_status() == 1) {
			System.out.println("runOFFCNArticle has thread working, abort this try."); 
			return;
		}
		int lastSpiderOffcnid = Tools.getInt(lastSpiderInfoBean.getSpider_last_id());
		if(lastSpiderOffcnid < maxOffcnid) {
			//防止被重写
			System.out.println("lastSpiderOffcnid error, pls check"); 
		}else {
			maxOffcnid = lastSpiderOffcnid;
		}

		lastSpiderInfoBean.setSpider_status(1);
		jdbc.updateSpiderInfo(lastSpiderInfoBean);
		
		int toOffcnid = maxOffcnid + lastSpiderInfoBean.getSpider_per_cnt();
		Map<String, String> headers = new HashMap<>();
		//headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
		//headers.put("Cookie", "Hm_lvt_89058dc85076e76bc971f6b58d30b5e8=1700832699,1701134686,1701307779,1702214910; JSESSIONID=EC53AEC17F5D79D0816FB194B3D921D0; Hm_lpvt_89058dc85076e76bc971f6b58d30b5e8=1703386280");
		headers.put("Set-Cookie", "mantis8961=c028200804cf46c48e267401cee4ce2e@8961; _ga=GA1.2.2068939227.1703398827; _qddaz=QD.vdlbsf.5wvw5v.lqj3oqbg; Hm_lvt_a6adf98bf5f7dd3d72872cf8b3535543=1706529707,1706704699; Hm_lvt_caaba5ffed655930fa7f43efc22fbd1e=1706529707,1706704699; Hm_lvt_3b55befee519827f19523fc1585b132c=1706529707,1706704699; Hm_lvt_a50bb98b162401cba9c89f1aa21a0b95=1706529707,1706704699; mantis_lp8961=https://www.eoffcn.com/zti/2023/sk/gsgwy53/?wt.mc_id=gwy_mbd_gbks_232623_pc&bd_vid=11432718196815647411; mantis_lp_id8961=lp:f180d57a50494bea87d7fe90e93ee4f8@8961; mantisrf8961=%257B%2522ad%2522%253A%2522baidu%2522%252C%2522source%2522%253A%2522referer%2522%252C%2522type%2522%253A%2522sem%2522%257D; _gid=GA1.2.68598622.1706704699; acw_tc=6eb974a317067078311377903e67fc8eed583d1cec2f9375a49e0b152f; www_session=eyJpdiI6IkFheHU3aEhLVmNCOFwvSlc3SU44ZGhBPT0iLCJ2YWx1ZSI6IkFscEdlNFc4M0ltVnlBczhFTkY3VGtERTJGNUI4R3NUVHpuOEhEOWxxWXg2dWtRQlwvZXVBRjVSeTJrTlE5TEJzIiwibWFjIjoiYjk1YmIxOGZlNmNkNTIzZDVhZjc4NjllNmUxZTM5M2YwNWUyODJiMjU3NDIxZTk2MjY5YzQwYzdkMDY2MDk4NyJ9; _qdda=3-1.1myr47; _qddab=3-gesyjj.ls1tsavo; XSRF-TOKEN=eyJpdiI6Ik1WVm5rQ3ZEalBuWitlWjB6SFhBYVE9PSIsInZhbHVlIjoiZmY1OUlDZk5lVEVScDRnQWhTZmpWTkNRZ2o3VnNEYjRpV2hiRjBiZzZjQW5veHprT09MXC9pVnFwQjcrNUtuK2YiLCJtYWMiOiIxOTZiZGUyOWI1ZTZmN2JlNGE3YzIwOGYwMzJiYTk0NzlmMzlhZDRhYTUzMDBkMjM0OTgyMDJjZDcwYjQ5NzdkIn0%3D; www_news_session=eyJpdiI6InFwamN4dzRqR0ZjQ1hMeFRzN0U2Znc9PSIsInZhbHVlIjoiZVBJMkN3NXM5QURqOGc5eVVVVEg1d3h1T1FKaDdJWk1kXC9teFpzYkgwNzMrSDJkS3NFWUkwSVh2cDdGd3JMVDIiLCJtYWMiOiI0MzY4YmJjNjFjODkxOTM0NzRjNTI1ZGE3YWZiMzhjOTM5N2MyM2U0MjcxNDk4ZWJhODM1YTUzODk1NzY5MjI4In0%3D; Hm_lpvt_a6adf98bf5f7dd3d72872cf8b3535543=1706709520; Hm_lpvt_3b55befee519827f19523fc1585b132c=1706709520; Hm_lpvt_a50bb98b162401cba9c89f1aa21a0b95=1706709520; _ga_HVCRMGQJM7=GS1.2.1706707831.5.1.1706709519.0.0.0; Hm_lpvt_caaba5ffed655930fa7f43efc22fbd1e=1706709520");
		
		for(int i = (maxOffcnid + 1);i <= toOffcnid;i++) {
			String curURL = "https://www.eoffcn.com/kszx/detail/"+i+".html";
			
			try {
				Thread.sleep((int)(Math.random() * lastSpiderInfoBean.getSpider_per_interval()));
				//Thread.sleep((int)(Math.random() * 5000));
			} catch (Exception e) {}
			String resultPage = HttpSendUtils.get(curURL, headers);
			if(resultPage == null || resultPage.indexOf("<title>404错误页面_中公网校</title>") != -1) {
				System.out.println("404:"+curURL); 
				continue;
			}
			System.out.println("GET:"+curURL); 
			deal(i,resultPage);
		}
		
		//再次查询数据库里的数据,获得真实的最大值
		int endMaxOffcnid = jdbc.getMaxOffcnid();
		
		lastSpiderInfoBean.setSpider_last_id(String.valueOf(endMaxOffcnid));
		lastSpiderInfoBean.setSpider_last_time(Tools.getDate(new Date()));
		lastSpiderInfoBean.setSpider_status(2);
		jdbc.updateSpiderInfo(lastSpiderInfoBean);
	}
	
	
	public static boolean deal(int offcnid, String resultPage){
		String title = null, from = null, titleORG_current = null, titleORG = null, url = null, time = null, urldomain = null;
		Document document = Jsoup.parse(resultPage);
		Elements elements = document.getElementsByClass("tit");
		if(elements != null) {
			title = elements.get(0).text(); 
		}
		
		boolean ignore = false;
		elements = document.getElementsByClass("titBot");
		if(elements != null && elements.size() > 0) {
			String innerValue = elements.get(0).text();
			from = innerValue.substring(innerValue.indexOf("来源：")+3, innerValue.indexOf("发布时间")).trim();
			if(from.indexOf("中公") != -1) {
				System.out.println("ERROR,中公:"+offcnid);
				ignore = true;
			}
			time = innerValue.substring(innerValue.indexOf("发布时间：") + 5).trim();
		}
		
		if(!ignore) { //不忽略
			elements = document.select("p");
	        for (Element element : elements) {
	            String innerValue = element.text();
	            if(innerValue.indexOf("文章来源:") != -1) {
	            	url = innerValue.substring(5).trim();
	            	
	            	try {
	            		String tempURL = url.substring(8);
	                	urldomain = url.substring(0, tempURL.indexOf("/") + 8);
	                	
						//Document docOrgPage = Jsoup.connect(url).get();
						//titleORG = docOrgPage.title();
					} catch (Exception e) {
					}
	            	
	            }
	            
	            if(innerValue.indexOf("原标题:") != -1) {
	    			titleORG_current = innerValue.substring(4).trim();
	            }
	        }
		}else { //忽略
			url = "https://www.eoffcn.com/kszx/detail/"+offcnid+".html";
		}
        
        
        
        if(Tools.isEmpty(titleORG)) {
        	titleORG = titleORG_current;
    	}
        
        OffcnExtUrlBean bean = new OffcnExtUrlBean();
		bean.setCreate_time(new Date());
        bean.setOffcn_id(offcnid);
        bean.setPublish_time(time);
        bean.setTitle(title);
        bean.setTitle_org(titleORG);
        bean.setUrl(url);
        bean.setUrl_domain(urldomain);
        bean.setUrl_from(from);
        bean.setView_status(1);
        SpiderJDBC jdbc = new SpiderJDBC();
        
        if(!Tools.isEmpty(urldomain)) {
        	List<OffcnExtUrlMapBean> listDomain = jdbc.getExtUrlMapProvince(urldomain);
        	if(listDomain.size() == 1) {
        		OffcnExtUrlMapBean offcnExtUrlMapBean = listDomain.get(0);
        		if(!Tools.isEmpty(offcnExtUrlMapBean.getUrl_sf())) {
        			bean.setSf(offcnExtUrlMapBean.getUrl_sf());
        		}
        		
        		if(!Tools.isEmpty(offcnExtUrlMapBean.getUrl_cs())) {
        			bean.setCs(offcnExtUrlMapBean.getUrl_cs());
        		}
        	}
        }
        
        if(ignore || Tools.isEmpty(titleORG) || Tools.isEmpty(urldomain) || !urldomain.toLowerCase().startsWith("http")) {
        	bean.setView_status(2);
        }else {
	        if(jdbc.getExtUrlCount(url) > 0) {
	        	bean.setView_status(2); //去重复
	        }
	        
        }
        jdbc.insertOffcnExtUrl(bean);
        return true;
	}
	
	public static void main(String[] args) {
		runOFFCNArticle();
	
	}
	
}
