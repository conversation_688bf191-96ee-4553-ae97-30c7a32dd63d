package com.career.db;

public class ZyzdMajorRanking {

	private int ranking;
	private int sort;
	private String yxmc;
	private String zymc;
	private String zyml;
	private String level;
	private String ext_zdf;
	private String ext_tags;
	private String ext_zdfwc;
	private String catg;
	

	private String ext_zdf_a;
	private String ext_zdf_b;
	private String ext_zdf_c;
	private String ext_zdfwc_a;
	private String ext_zdfwc_b;
	private String ext_zdfwc_c;

	private String ext_zy_zdf;
	private String ext_zy_zdfwc;

	private boolean ext_is_overload;
	
	public String getExt_zdf_a() {
		return ext_zdf_a;
	}
	public void setExt_zdf_a(String ext_zdf_a) {
		this.ext_zdf_a = ext_zdf_a;
	}
	public String getExt_zdf_b() {
		return ext_zdf_b;
	}
	public void setExt_zdf_b(String ext_zdf_b) {
		this.ext_zdf_b = ext_zdf_b;
	}
	public String getExt_zdf_c() {
		return ext_zdf_c;
	}
	public void setExt_zdf_c(String ext_zdf_c) {
		this.ext_zdf_c = ext_zdf_c;
	}
	public String getExt_zdfwc_a() {
		return ext_zdfwc_a;
	}
	public void setExt_zdfwc_a(String ext_zdfwc_a) {
		this.ext_zdfwc_a = ext_zdfwc_a;
	}
	public String getExt_zdfwc_b() {
		return ext_zdfwc_b;
	}
	public void setExt_zdfwc_b(String ext_zdfwc_b) {
		this.ext_zdfwc_b = ext_zdfwc_b;
	}
	public String getExt_zdfwc_c() {
		return ext_zdfwc_c;
	}
	public void setExt_zdfwc_c(String ext_zdfwc_c) {
		this.ext_zdfwc_c = ext_zdfwc_c;
	}
	public boolean isExt_is_overload() {
		return ext_is_overload;
	}
	public void setExt_is_overload(boolean ext_is_overload) {
		this.ext_is_overload = ext_is_overload;
	}
	public String getExt_zy_zdf() {
		return ext_zy_zdf;
	}
	public void setExt_zy_zdf(String ext_zy_zdf) {
		this.ext_zy_zdf = ext_zy_zdf;
	}
	public String getExt_zy_zdfwc() {
		return ext_zy_zdfwc;
	}
	public void setExt_zy_zdfwc(String ext_zy_zdfwc) {
		this.ext_zy_zdfwc = ext_zy_zdfwc;
	}
	public String getExt_tags() {
		return ext_tags;
	}
	public void setExt_tags(String ext_tags) {
		this.ext_tags = ext_tags;
	}
	public String getZyml() {
		return zyml;
	}
	public void setZyml(String zyml) {
		this.zyml = zyml;
	}
	public String getCatg() {
		return catg;
	}
	public void setCatg(String catg) {
		this.catg = catg;
	}
	public String getExt_zdf() {
		return ext_zdf;
	}
	public void setExt_zdf(String ext_zdf) {
		this.ext_zdf = ext_zdf;
	}
	public String getExt_zdfwc() {
		return ext_zdfwc;
	}
	public void setExt_zdfwc(String ext_zdfwc) {
		this.ext_zdfwc = ext_zdfwc;
	}
	public int getRanking() {
		return ranking;
	}
	public void setRanking(int ranking) {
		this.ranking = ranking;
	}
	public int getSort() {
		return sort;
	}
	public void setSort(int sort) {
		this.sort = sort;
	}
	public String getYxmc() {
		return yxmc;
	}
	public void setYxmc(String yxmc) {
		this.yxmc = yxmc;
	}
	public String getZymc() {
		return zymc;
	}
	public void setZymc(String zymc) {
		this.zymc = zymc;
	}
	public String getLevel() {
		return level;
	}
	public void setLevel(String level) {
		this.level = level;
	}
	
	
}
