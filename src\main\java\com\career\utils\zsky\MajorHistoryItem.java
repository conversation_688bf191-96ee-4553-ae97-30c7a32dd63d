package com.career.utils.zsky;

import java.util.Arrays;

public class MajorHistoryItem {

	private int year;
	private int degree_type;
	private String degree_type_name;
	private String special_code;
	private String special_name;
	private int total;
	private int politics;
	private int english;
	private int special_one;
	private int special_two;
	private String note;
	private int diff_total;
	private int diff_politics;
	private int diff_english;
	private int diff_special_one;
	private int diff_special_two;
	public int getYear() {
		return year;
	}
	public void setYear(int year) {
		this.year = year;
	}
	public int getDegree_type() {
		return degree_type;
	}
	public void setDegree_type(int degree_type) {
		this.degree_type = degree_type;
	}
	public String getDegree_type_name() {
		return degree_type_name;
	}
	public void setDegree_type_name(String degree_type_name) {
		this.degree_type_name = degree_type_name;
	}
	public String getSpecial_code() {
		return special_code;
	}
	public void setSpecial_code(String special_code) {
		this.special_code = special_code;
	}
	public String getSpecial_name() {
		return special_name;
	}
	public void setSpecial_name(String special_name) {
		this.special_name = special_name;
	}
	public int getTotal() {
		return total;
	}
	public void setTotal(int total) {
		this.total = total;
	}
	public int getPolitics() {
		return politics;
	}
	public void setPolitics(int politics) {
		this.politics = politics;
	}
	public int getEnglish() {
		return english;
	}
	public void setEnglish(int english) {
		this.english = english;
	}
	public int getSpecial_one() {
		return special_one;
	}
	public void setSpecial_one(int special_one) {
		this.special_one = special_one;
	}
	public int getSpecial_two() {
		return special_two;
	}
	public void setSpecial_two(int special_two) {
		this.special_two = special_two;
	}
	public String getNote() {
		return note;
	}
	public void setNote(String note) {
		this.note = note;
	}
	public int getDiff_total() {
		return diff_total;
	}
	public void setDiff_total(int diff_total) {
		this.diff_total = diff_total;
	}
	public int getDiff_politics() {
		return diff_politics;
	}
	public void setDiff_politics(int diff_politics) {
		this.diff_politics = diff_politics;
	}
	public int getDiff_english() {
		return diff_english;
	}
	public void setDiff_english(int diff_english) {
		this.diff_english = diff_english;
	}
	public int getDiff_special_one() {
		return diff_special_one;
	}
	public void setDiff_special_one(int diff_special_one) {
		this.diff_special_one = diff_special_one;
	}
	public int getDiff_special_two() {
		return diff_special_two;
	}
	public void setDiff_special_two(int diff_special_two) {
		this.diff_special_two = diff_special_two;
	}
	
	public String generateSQL(int school_id) {
		String SQL = "insert into career_university_major_data(school_id, data_year, degree_type, degree_type_name, special_code, special_name, total, politics, english, special_one, special_two, note, diff_total, diff_politics, diff_english, diff_special_one, diff_special_two) values(" + school_id + ", " + year
				+ ", " + degree_type + ",'" + degree_type_name + "', '" + special_code + "', '" + special_name + "', " + total + "," + politics+ "," + english+ "," + special_one+ "," + special_two+ ",'" + note+ "'," + diff_total+ "," + diff_politics+ "," + diff_english+ "," + diff_special_one+ "," + diff_special_two+");";
		return SQL;
	}
	
}
