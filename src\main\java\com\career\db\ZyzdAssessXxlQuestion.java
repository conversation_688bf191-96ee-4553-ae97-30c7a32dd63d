package com.career.db;

import com.career.utils.BaseBean;

public class ZyzdAssessXxlQuestion extends BaseBean{
	
	public static void main(String args[]) {
		printBeanProperties(new ZyzdAssessXxlQuestion());
	}
	
	private String assess_name;
	private int q_no;
	private String view_no;
	private int sort;
	private String descp;
	
	private int seg_no;
	private String set_title;
	private int item_cnt;
	private int item_type;
	private String item_q1;
	private String item_q2;
	private String item_q3;
	private String item_q4;
	private String item_q5;
	private int item_r1;
	private int item_r2;
	private int item_r3;
	private int item_r4;
	private int item_r5;
	public String getAssess_name() {
		return assess_name;
	}
	public void setAssess_name(String assess_name) {
		this.assess_name = assess_name;
	}
	public int getQ_no() {
		return q_no;
	}
	public void setQ_no(int q_no) {
		this.q_no = q_no;
	}
	public String getView_no() {
		return view_no;
	}
	public void setView_no(String view_no) {
		this.view_no = view_no;
	}
	public int getSort() {
		return sort;
	}
	public void setSort(int sort) {
		this.sort = sort;
	}
	public String getDescp() {
		return descp;
	}
	public void setDescp(String descp) {
		this.descp = descp;
	}
	public int getSeg_no() {
		return seg_no;
	}
	public void setSeg_no(int seg_no) {
		this.seg_no = seg_no;
	}
	public String getSet_title() {
		return set_title;
	}
	public void setSet_title(String set_title) {
		this.set_title = set_title;
	}
	public int getItem_cnt() {
		return item_cnt;
	}
	public void setItem_cnt(int item_cnt) {
		this.item_cnt = item_cnt;
	}
	public int getItem_type() {
		return item_type;
	}
	public void setItem_type(int item_type) {
		this.item_type = item_type;
	}
	public String getItem_q1() {
		return item_q1;
	}
	public void setItem_q1(String item_q1) {
		this.item_q1 = item_q1;
	}
	public String getItem_q2() {
		return item_q2;
	}
	public void setItem_q2(String item_q2) {
		this.item_q2 = item_q2;
	}
	public String getItem_q3() {
		return item_q3;
	}
	public void setItem_q3(String item_q3) {
		this.item_q3 = item_q3;
	}
	public String getItem_q4() {
		return item_q4;
	}
	public void setItem_q4(String item_q4) {
		this.item_q4 = item_q4;
	}
	public String getItem_q5() {
		return item_q5;
	}
	public void setItem_q5(String item_q5) {
		this.item_q5 = item_q5;
	}
	public int getItem_r1() {
		return item_r1;
	}
	public void setItem_r1(int item_r1) {
		this.item_r1 = item_r1;
	}
	public int getItem_r2() {
		return item_r2;
	}
	public void setItem_r2(int item_r2) {
		this.item_r2 = item_r2;
	}
	public int getItem_r3() {
		return item_r3;
	}
	public void setItem_r3(int item_r3) {
		this.item_r3 = item_r3;
	}
	public int getItem_r4() {
		return item_r4;
	}
	public void setItem_r4(int item_r4) {
		this.item_r4 = item_r4;
	}
	public int getItem_r5() {
		return item_r5;
	}
	public void setItem_r5(int item_r5) {
		this.item_r5 = item_r5;
	}
	

}
