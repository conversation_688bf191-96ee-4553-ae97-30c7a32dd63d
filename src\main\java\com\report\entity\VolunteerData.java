package com.report.entity;

import com.career.db.LhyFormMain;
import com.career.utils.AdmitBean;
import com.career.db.LhyForm;
import com.career.db.ZyzdUniversityBean;
import com.career.db.ZyzdBaseMajor;
import java.util.List;
import java.util.Map;

/**
 * 志愿数据实体类
 * 用于在报告中展示学生的志愿填报信息
 */
public class VolunteerData {
    
    private LhyFormMain formMain;        // 志愿表单主信息
    private List<LhyForm> formList;      // 志愿表单列表
    private List<AdmitBean> admitBeanList;
    
    private List<List<JobStatistics>> jobStatisticsList; // 就业统计数据列表，对应每个预测录取的院校专业
    private List<List<JobStatistics>> schoolJobStatisticsList; // 按院校查询的就业统计数据列表
    private List<List<JobStatistics>> majorJobStatisticsList; // 按专业查询的就业统计数据列表
    
    // 新增：院校和专业详细信息Map
    private Map<String, ZyzdUniversityBean> universityMap;  // 院校名称 -> 院校详细信息
    private Map<String, ZyzdBaseMajor> majorMap;            // 专业名称 -> 专业详细信息
    
    public List<AdmitBean> getAdmitBeanList() {
		return admitBeanList;
	}

	public void setAdmitBeanList(List<AdmitBean> admitBeanList) {
		this.admitBeanList = admitBeanList;
	}

	public VolunteerData() {
    }
    
    public VolunteerData(LhyFormMain formMain, List<LhyForm> formList) {
        this.formMain = formMain;
        this.formList = formList;
    }
    
    // Getters and Setters
    public LhyFormMain getFormMain() {
        return formMain;
    }

	public void setFormMain(LhyFormMain formMain) {
        this.formMain = formMain;
    }
    
    public List<LhyForm> getFormList() {
        return formList;
    }
    
    public void setFormList(List<LhyForm> formList) {
        this.formList = formList;
    }
    
    public List<List<JobStatistics>> getJobStatisticsList() {
        return jobStatisticsList;
    }
    
    public void setJobStatisticsList(List<List<JobStatistics>> jobStatisticsList) {
        this.jobStatisticsList = jobStatisticsList;
    }
    
    public List<List<JobStatistics>> getSchoolJobStatisticsList() {
        return schoolJobStatisticsList;
    }
    
    public void setSchoolJobStatisticsList(List<List<JobStatistics>> schoolJobStatisticsList) {
        this.schoolJobStatisticsList = schoolJobStatisticsList;
    }
    
    public List<List<JobStatistics>> getMajorJobStatisticsList() {
        return majorJobStatisticsList;
    }
    
    public void setMajorJobStatisticsList(List<List<JobStatistics>> majorJobStatisticsList) {
        this.majorJobStatisticsList = majorJobStatisticsList;
    }
    
    // 新增的getter和setter方法
    public Map<String, ZyzdUniversityBean> getUniversityMap() {
        return universityMap;
    }
    
    public void setUniversityMap(Map<String, ZyzdUniversityBean> universityMap) {
        this.universityMap = universityMap;
    }
    
    public Map<String, ZyzdBaseMajor> getMajorMap() {
        return majorMap;
    }
    
    public void setMajorMap(Map<String, ZyzdBaseMajor> majorMap) {
        this.majorMap = majorMap;
    }
    
    /**
     * 检查是否有有效的志愿数据
     */
    public boolean hasData() {
        return formMain != null || (formList != null && !formList.isEmpty());
    }
    
    /**
     * 获取志愿数量
     */
    public int getVolunteerCount() {
        return formList != null ? formList.size() : 0;
    }
    
    /**
     * 检查是否有院校详细信息
     */
    public boolean hasUniversityData() {
        return universityMap != null && !universityMap.isEmpty();
    }
    
    /**
     * 检查是否有专业详细信息
     */
    public boolean hasMajorData() {
        return majorMap != null && !majorMap.isEmpty();
    }
} 