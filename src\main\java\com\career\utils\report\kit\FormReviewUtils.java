package com.career.utils.report.kit;

import com.career.db.*;
import com.career.utils.*;
import com.career.utils.report.FormReviewEvaluator;

import java.util.*;

/**
 * 志愿表单审核工具类
 * 提供所有审核相关的辅助方法和工具函数
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class FormReviewUtils {
    
    // ==================== 数据分组和统计方法 ====================
    
    /**
     * 按院校（专业组）分组
     * 使用院校代码和专业组代码的组合作为分组键，确保不同专业组被正确分组
     */
    public static Map<String, List<SuperForm>> groupByCollege(List<SuperForm> formList) {
        Map<String, List<SuperForm>> groups = new HashMap<>();
        
        for (SuperForm superForm : formList) {
            // 使用院校代码和专业组代码作为组合键
            String groupKey = Tools.trim(superForm.getYxdm()) + "|" + Tools.trim(superForm.getZyz());
            groups.computeIfAbsent(groupKey, k -> new ArrayList<>()).add(superForm);
        }
        
        return groups;
    }
    
    /**
     * 计算两个集合的交集
     */
    public static <T> Set<T> intersection(Set<T> set1, Set<T> set2) {
        Set<T> result = new HashSet<>(set1);
        result.retainAll(set2);
        return result;
    }
    
    // ==================== 院校信息获取方法 ====================
    
    /**
     * 获取院校所在省份（安全版本）
     */
    public static String getUniversityProvince(SuperForm superForm) {
        if (superForm == null || Tools.isEmpty(superForm.getYxmc_org())) {
            return "未知";
        }
        
        try {
            ZyzdUniversityBean zyzdUniversityBean = ZyzdCache.getUniversity(superForm.getYxmc_org());
            if (zyzdUniversityBean != null && !Tools.isEmpty(zyzdUniversityBean.getPosition_sf())) {
                return Tools.trim(zyzdUniversityBean.getPosition_sf());
            }
        } catch (Exception e) {
            Tools.println("[getUniversityProvince] 获取院校省份失败: " + e.getMessage());
        }
        
        return "未知";
    }
    
    /**
     * 获取院校所在城市（安全版本）
     */
    public static String getUniversityCity(SuperForm superForm) {
        if (superForm == null || Tools.isEmpty(superForm.getYxmc_org())) {
            return "未知";
        }
        
        try {
            ZyzdUniversityBean zyzdUniversityBean = ZyzdCache.getUniversity(superForm.getYxmc_org());
            if (zyzdUniversityBean != null && !Tools.isEmpty(zyzdUniversityBean.getPosition_cs())) {
                return Tools.trim(zyzdUniversityBean.getPosition_cs());
            }
        } catch (Exception e) {
            Tools.println("[getUniversityCity] 获取院校城市失败: " + e.getMessage());
        }
        
        return "未知";
    }
    
    /**
     * 获取院校类型
     */
    public static String getCollegeType(JHBean bean) {
        if (bean == null) {
            return "其他";
        }
        
        // 根据JHBean的相关字段判断院校类型
        String yxTags = bean.getYx_tags();
        List<InputTag> yxtagList = ZyzdCache.yxtagList;
        for(InputTag tag : yxtagList) {
        	if(yxTags.contains(tag.getCode())) {
        		return tag.getCode();
        	}
        }
        
        String indNature = bean.getInd_nature();
        
        List<InputTag> bxxzList = ZyzdCache.bxxzList;
        for(InputTag tag : bxxzList) {
        	if(indNature.contains(tag.getCode())) {
        		return tag.getCode();
        	}
        }

        return "其他";
    }
    
    // ==================== 专业信息获取方法 ====================
    
    /**
     * 获取专业类别
     */
    public static String getMajorCategory(SuperForm superForm, String pcCode) {
        if (superForm == null || Tools.isEmpty(superForm.getZymc_org())) {
            return null;
        }
        
        ZyzdBaseMajor catgTwo = ZyzdCache.getZymlByZymc(pcCode, superForm.getZymc_org());
        if (catgTwo != null) {
            return catgTwo.getM_catg_two();
        }
        
        return null;
    }
    
    // ==================== 分数计算方法 ====================
    
    /**
     * 计算院校平均分数
     */
    public static int calculateAvgScore(List<SuperForm> group, FormReviewEvaluator.FormReviewContext context) {
        if (group == null || group.isEmpty()) return 0;
        
        int total = 0;
        int count = 0;
        
        for (SuperForm superForm : group) {
            int avgScore = getAvgThreeYearScore(superForm, context);
            if (avgScore > 0) {
                total += avgScore;
                count++;
            }
        }
        
        return count > 0 ? total / count : 0;
    }
    
    /**
     * 获取三年平均分数
     * 🎯 智能选择数据源：优先使用用户选择专业数据，降级使用完整专业组数据
     */
    public static int getAvgThreeYearScore(SuperForm superForm, FormReviewEvaluator.FormReviewContext context) {
        if (superForm == null || context == null) {
            return 0;
        }
        
        // 🎯 优先使用用户选择的专业数据（如果该专业被用户选择）
        List<JHBean> planData = null;
        
        if (context.hasSelectedMajorData()) {
            // 检查该专业是否被用户选择
            List<JHBean> selectedData = context.getSelectedMajorPlanData(
                superForm.getYxdm(), superForm.getZyz(), superForm.getZydm());
            
            if (!selectedData.isEmpty()) {
                planData = selectedData;
            }
        }
        
        // 降级使用完整专业组数据
        if (planData == null || planData.isEmpty()) {
            planData = context.getPlanData(superForm.getYxdm(), superForm.getZyz());
        }
        
        if (planData.isEmpty()) {
            return 0;
        }
        
        int total = 0;
        int count = 0;
        
        // 遍历计划数据，获取三年分数
        for (JHBean bean : planData) {
            try {
                // A年分数
            	int scoreA = bean.getQsf_a();
                if (scoreA > 0) {
                    total += scoreA;
                    count++;
                }
                
                // B年分数
                int scoreB = bean.getQsf_b();
                if (scoreB > 0) {
                    total += scoreB;
                    count++;
                }
                
                // C年分数
                int scoreC = bean.getQsf_c();
                if (scoreC > 0) {
                    total += scoreC;
                    count++;
                }
            } catch (NumberFormatException e) {
                e.printStackTrace();
            }
        }
        
        return count > 0 ? total / count : 0;
    }
    
    /**
     * 计算基于招生计划的加权平均分
     */
    public static double calculateWeightedAvgScore(List<JHBean> planData) {
        if (planData == null || planData.isEmpty()) {
            return 0;
        }
        
        double totalWeightedScore = 0;
        int totalPlan = 0;
        
        for (JHBean bean : planData) {
            try {
                int qsf = bean.getQsf(); // 今年的预测趋势分
                int jhs = Tools.getInt(bean.getJhs()); // 今年的招生计划 
                
                if (qsf > 0 && jhs > 0) {
                	totalWeightedScore += qsf * jhs;
                    totalPlan += jhs;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        
        return totalPlan > 0 ? totalWeightedScore / totalPlan : 0;
    }
    
    /**
     * 计算高级加权分数，考虑历年趋势
     */
    public static double calculateAdvancedWeightedScore(List<JHBean> planData, FormReviewEvaluator.HistoricalTrend trend) {
        if (planData == null || planData.isEmpty() || trend == null) {
            return 0;
        }
        
        // 根据趋势调整权重：近年权重更高
        double scoreA = trend.getAvgScoreA();
        double scoreB = trend.getAvgScoreB(); 
        double scoreC = trend.getAvgScoreC();
        
        // 权重分配：最近年份权重65%，前一年25%，前两年10%
        if (scoreC > 0 && scoreB > 0 && scoreA > 0) { // ABC
            return scoreC * FormReviewEvaluator.RECENT_YEAR_WEIGHT + scoreB * FormReviewEvaluator.SECOND_YEAR_WEIGHT + scoreA * FormReviewEvaluator.THIRD_YEAR_WEIGHT;
        } else if (scoreA > 0 && scoreB > 0) { // AB
            return scoreA * FormReviewEvaluator.TWO_YEAR_PRIMARY_WEIGHT + scoreB * FormReviewEvaluator.TWO_YEAR_SECONDARY_WEIGHT;
        } else if (scoreA > 0 && scoreC > 0) { // AC
            return scoreA * FormReviewEvaluator.TWO_YEAR_PRIMARY_WEIGHT + scoreC * FormReviewEvaluator.TWO_YEAR_SECONDARY_WEIGHT;
        } else if (scoreC > 0 && scoreB > 0) { // BC
            return scoreB * FormReviewEvaluator.TWO_YEAR_PRIMARY_WEIGHT + scoreC * FormReviewEvaluator.TWO_YEAR_SECONDARY_WEIGHT;
        } else if (scoreA > 0) {  // A
            return scoreA;
        } else if (scoreB > 0) {  // B
            return scoreB;
        } else if (scoreC > 0) {  // C
            return scoreC;
        }
        return 0;
    }
    
    /**
     * 计算分数波动范围
     */
    public static double calculateScoreRange(List<JHBean> planData) {
        if (planData == null || planData.isEmpty()) {
            return 10; // 默认范围
        }
        
        double minScore = Double.MAX_VALUE;
        double maxScore = Double.MIN_VALUE;
        
        for (JHBean bean : planData) {
            try {
                // 检查最低分
            	minScore = Math.min(Math.min(minScore, bean.getQsf_a()), Math.min(bean.getQsf_b(), bean.getQsf_c()));
            	maxScore = Math.max(Math.max(minScore, bean.getQsf_a()), Math.max(bean.getQsf_b(), bean.getQsf_c()));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        
        if (minScore != Double.MAX_VALUE && maxScore != Double.MIN_VALUE) {
            return maxScore - minScore;
        }
        
        return 10; // 默认范围
    }
    
    // ==================== 数据解析和验证方法 ====================
    
    /**
     * 解析JSON字符串为Set集合
     */
    public static Set<String> parseJsonStringToSet(String jsonString) {
        Set<String> result = new HashSet<>();
        if (Tools.isEmpty(jsonString)) {
            return result;
        }
        
        try {
            // 简单的JSON解析，假设格式为["item1","item2"]
            if (jsonString.startsWith("[") && jsonString.endsWith("]")) {
                String content = jsonString.substring(1, jsonString.length() - 1);
                String[] items = content.split(",");
                for (String item : items) {
                    String cleanItem = item.trim().replaceAll("^\"|\"$", "");
                    if (!cleanItem.isEmpty()) {
                        result.add(cleanItem);
                    }
                }
            } else {
                // 如果不是JSON格式，按逗号分割
                String[] items = jsonString.split(",");
                for (String item : items) {
                    String cleanItem = item.trim();
                    if (!cleanItem.isEmpty()) {
                        result.add(cleanItem);
                    }
                }
            }
        } catch (Exception e) {
            // 解析失败时，直接将整个字符串作为一个项目
            result.add(jsonString);
        }
        
        return result;
    }
    
    /**
     * 解析学费金额
     */
    public static int parseFeeAmount(String fee) {
        if (Tools.isEmpty(fee)) return 0;
        
        String numericFee = fee.replaceAll("[^0-9]", "");
        if (numericFee.isEmpty()) return 0;
        
        try {
            return Integer.parseInt(numericFee);
        } catch (NumberFormatException e) {
            return 0;
        }
    }
    
    /**
     * 解析预算金额
     * 根据系统预设的预算选项进行解析：
     * - "不限制": 返回Integer.MAX_VALUE表示无预算限制
     * - "5万以下": 返回50000
     * - "5-10万": 返回100000（取上限）
     * - "10-15万": 返回150000（取上限）
     * - "15-20万": 返回200000（取上限）
     * - "20万以上": 返回Integer.MAX_VALUE表示高预算
     */
    public static int parseBudgetAmount(String budget) {
        if (Tools.isEmpty(budget)) return 0;
        
        // 去除空格并转换为小写进行匹配
        String trimmedBudget = budget.trim();
        
        // 处理"不限制"情况
        if ("不限制".equals(trimmedBudget)) {
            return Integer.MAX_VALUE;
        }
        
        // 处理"X万以下"格式
        if (trimmedBudget.contains("万以下")) {
            String num = trimmedBudget.replace("万以下", "").trim();
            try {
                return Integer.parseInt(num) * 10000;
            } catch (NumberFormatException e) {
                return 0;
            }
        }
        
        // 处理"X万以上"格式
        if (trimmedBudget.contains("万以上")) {
            // 对于"X万以上"，返回一个很大的值表示高预算
            return Integer.MAX_VALUE;
        }
        
        // 处理"X-Y万"格式的范围
        if (trimmedBudget.contains("-") && trimmedBudget.contains("万")) {
            String[] parts = trimmedBudget.replace("万", "").split("-");
            if (parts.length >= 2) {
                try {
                    // 取范围的上限作为预算限制
                    int maxBudget = Integer.parseInt(parts[1].trim());
                    return maxBudget * 10000;
                } catch (NumberFormatException e) {
                    return 0;
                }
            }
        }
        
        // 兜底处理：尝试提取数字并按万为单位处理
        String numericBudget = trimmedBudget.replaceAll("[^0-9]", "");
        if (!numericBudget.isEmpty()) {
            try {
                int amount = Integer.parseInt(numericBudget);
                // 如果原字符串包含"万"，则乘以10000
                if (trimmedBudget.contains("万")) {
                    return amount * 10000;
                }
                return amount;
            } catch (NumberFormatException e) {
                return 0;
            }
        }
        
        return 0;
    }
    
    /**
     * 解析身高
     */
    public static int parseHeight(String height) {
        if (Tools.isEmpty(height)) return 0;
        
        String numericHeight = height.replaceAll("[^0-9]", "");
        if (numericHeight.isEmpty()) return 0;
        
        try {
            return Integer.parseInt(numericHeight);
        } catch (NumberFormatException e) {
            return 0;
        }
    }
    
    // ==================== 专业和身体条件验证方法 ====================
    
    /**
     * 检查专业是否有性别限制
     */
    public static boolean hasGenderRestriction(String majorName, String gender) {
        return GenderRestrictedMajors.hasGenderRestriction(majorName, gender);
    }
    
    /**
     * 检查专业是否需要绘画技能
     */
    public static boolean requiresDrawingSkill(String majorName) {
        if (Tools.isEmpty(majorName)) return false;
        
        String[] drawingMajors = {"美术", "绘画", "设计", "艺术", "动画", "工业设计", "建筑学", "城乡规划", "风景园林"};
        
        for (String keyword : drawingMajors) {
            if (majorName.contains(keyword)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查专业是否有身高要求
     */
    public static boolean hasHeightRequirement(String majorName, String height) {
        if (Tools.isEmpty(majorName) || Tools.isEmpty(height)) return false;
        
        int heightValue = parseHeight(height);
        if (heightValue <= 0) return false;
        
        // 有身高要求的专业
        if ((majorName.contains("空乘") || majorName.contains("航空") || majorName.contains("乘务")) && heightValue < 160) {
            return true;
        }
        
        if (majorName.contains("模特") && heightValue < 170) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 检查专业是否有色觉要求 TODO
     */
    public static boolean hasColorVisionRequirement(String majorName, String physicalExam) {
        if (Tools.isEmpty(majorName) || Tools.isEmpty(physicalExam)) return false;
        
        // 如果有色盲色弱问题
        if (physicalExam.contains("色盲") || physicalExam.contains("色弱")) {
        	String[] colorRequiredMajors = {
                    // 医学及相关领域
                    "临床医学", "口腔医学", "医学影像学", "药物制剂", "生物制药", "生物技术",
                    // 工学与技术领域
                    "应用化学", "材料化学", "化学工程与工艺", "制药工程", "交通运输", "航海技术",
                    "轮机工程", "飞行技术", "高分子材料工程", "冶金工程", "环境工程",
                    // 理学与农学领域
                    "化学", "生物科学", "地质学", "心理学", "应用心理学", "生态学", "海洋科学",
                    "海洋技术", "动物医学", "动物科学", "野生动物与自然保护区管理", "农学",
                    "园艺", "植物保护", "茶学", "林学", "园林", "蚕学", "农业资源与环境",
                    "水产养殖学", "海洋渔业科学与技术",
                    // 法学
                    "侦查学",
                    // 教育学
                    "学前教育", "特殊教育", "体育教育", "运动训练", "运动人体科学", "武术与民族传统体育",
                    // 历史学
                    "考古学", "文物与博物馆学",
                    // 艺术学
                    "美术学", "绘画", "艺术设计", "摄影", "动画",
                    // 其他受限领域
                    "应用物理学", "天文学", "地理科学", "应用气象学", "材料物理", "矿物加工工程",
                    "资源勘查工程", "无机非金属材料工程", "油气储运工程"
                };
            
            for (String keyword : colorRequiredMajors) {
                if (majorName.contains(keyword)) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * 检查专业是否有其他身体要求
     */
    public static boolean hasPhysicalRequirement(String majorName, String bodyType) {
        // 暂时简化处理
        return false;
    }
    
    // ==================== 经济适配性验证方法 ====================
    
    /**
     * 判断是否可承受学费
     */
    public static boolean isAffordable(String fee, String budget, String collegeType, 
                               String acceptSinoForeign, String acceptPrivate) {
        if (Tools.isEmpty(fee) || Tools.isEmpty(budget)) {
            return true; // 没有明确信息时认为可承受
        }
        
        // 解析学费（去除非数字字符）
        int feeAmount = parseFeeAmount(fee);
        int budgetAmount = parseBudgetAmount(budget);
        
        // 如果预算为Integer.MAX_VALUE（不限制或20万以上），则认为可承受
        if (budgetAmount == Integer.MAX_VALUE || budgetAmount <= 0) {
            // 仍需检查办学类型偏好
            if ("中外合作".equals(collegeType) && !"接受".equals(acceptSinoForeign)) {
                return false;
            }
            
            if ("民办".equals(collegeType) && !"接受".equals(acceptPrivate)) {
                return false;
            }
            
            return true;
        }
        
        // 检查办学类型偏好
        if ("中外合作".equals(collegeType) && !"接受".equals(acceptSinoForeign)) {
            return false;
        }
        
        if ("民办".equals(collegeType) && !"接受".equals(acceptPrivate)) {
            return false;
        }
        
        return feeAmount <= budgetAmount;
    }
    
    // ==================== 建议生成方法 ====================
    
    /**
     * 根据等级随机获取建议
     */
    public static String getRandomSuggestion(Random random, String grade, String[][] suggestions) {
        if (grade == null) return null;
        
        int gradeIndex = -1;
        switch(grade) {
            case FormReviewEvaluator.GRADE_A: case "1": gradeIndex = 0; break;
            case FormReviewEvaluator.GRADE_B: case "2": gradeIndex = 1; break;
            case FormReviewEvaluator.GRADE_C: case "3": gradeIndex = 2; break;
            case FormReviewEvaluator.GRADE_D: case "4": gradeIndex = 3; break;
        }
        
        if (gradeIndex >= 0 && gradeIndex < suggestions.length) {
            String[] gradeOptions = suggestions[gradeIndex];
            if (gradeOptions.length > 0) {
                return gradeOptions[random.nextInt(gradeOptions.length)];
            }
        }
        
        return null;
    }
    
    // ==================== 录取概率相关工具方法 ====================
    
    /**
     * 根据录取概率获取概率等级描述
     */
    public static String getProbabilityLevelDescription(double probability) {
        if (probability >= 80) return "录取概率很高";
        if (probability >= 60) return "录取概率较高";
        if (probability >= 40) return "录取概率中等";
        if (probability >= 20) return "录取概率较低";
        return "录取概率很低";
    }
    
    /**
     * 根据录取概率获取建议
     */
    public static String getProbabilityAdvice(double probability) {
        if (probability >= 80) return "建议重点关注，录取把握很大";
        if (probability >= 60) return "建议作为稳妥志愿，录取机会较大";
        if (probability >= 40) return "建议谨慎考虑，录取存在一定风险";
        if (probability >= 20) return "建议作为冲击志愿，录取风险较高";
        return "不建议填报，录取可能性极小";
    }
    
    // ==================== 专业组干净度计算方法 ====================
    
    /**
     * 计算专业组的干净度等级
     * @param categoryCount 专业门类数量
     * @return 干净度等级：干净、一般、不干净
     */
    public static String calculateCleanlinessLevel(int categoryCount) {
        if (categoryCount <= 1) {
            return "干净";
        } else if (categoryCount <= 2) {
            return "一般";
        } else {
            return "不干净";
        }
    }
    
    
    /**
     * 格式化专业门类显示
     */
    public static String formatCategoriesDisplay(Set<String> categories) {
        if (categories == null || categories.isEmpty()) {
            return "无";
        }
        
        if (categories.size() <= 3) {
            return String.join("、", categories);
        } else {
            List<String> categoryList = new ArrayList<>(categories);
            return String.join("、", categoryList.subList(0, 3)) + "等" + categories.size() + "类";
        }
    }
    
    /**
     * 获取整体干净度等级
     */
    public static String getOverallCleanlinessLevel(int cleanCount, int generalCount, int dirtyCount) {
        int totalGroups = cleanCount + generalCount + dirtyCount;
        if (totalGroups == 0) return "待计算";
        
        double cleanRate = (double) cleanCount / totalGroups;
        
        if (cleanRate >= 0.8) {
            return "优秀";
        } else if (cleanRate >= 0.6) {
            return "良好";
        } else if (cleanRate >= 0.4) {
            return "一般";
        } else {
            return "需要优化";
        }
    }
    
    /**
     * 获取干净度建议
     */
    public static String getCleanlinessAdvice(int cleanCount, int generalCount, int dirtyCount) {
        int totalGroups = cleanCount + generalCount + dirtyCount;
        if (totalGroups == 0) return "暂无数据";
        
        double cleanRate = (double) cleanCount / totalGroups;
        double dirtyRate = (double) dirtyCount / totalGroups;
        
        if (cleanRate >= 0.8) {
            return "专业组干净度很高，志愿填报结构合理";
        } else if (dirtyRate >= 0.5) {
            return "建议优化专业组选择，减少跨门类填报";
        } else {
            return "专业组干净度适中，可适当优化";
        }
    }
    
    /**
     * 定义985院校关键词列表
     */
    private static final String[] TOP_985_KEYWORDS = {
        "北京大学", "清华大学", "复旦大学", "上海交通大学", "浙江大学", "南京大学", "中国科学技术大学", "西安交通大学", "哈尔滨工业大学", "武汉大学", "华中科技大学", "四川大学", "吉林大学", "山东大学", "中山大学", "厦门大学", "天津大学", "南开大学", "东南大学", "同济大学", "大连理工大学", "重庆大学", "中南大学", "湖南大学", "华南理工大学", "北京航空航天大学", "北京理工大学", "中国农业大学", "国防科技大学", "东北大学", "西北工业大学", "华东师范大学", "北京师范大学", "中国人民大学", "中央民族大学", "兰州大学", "电子科技大学", "北京邮电大学", "北京交通大学", "北京科技大学", "北京化工大学", "北京林业大学", "北京中医药大学", "北京外国语大学", "中国传媒大学", "中国政法大学", "中国地质大学", "中国矿业大学", "中国石油大学", "中国海洋大学", "中国药科大学"
    };

    /**
     * 定义211院校关键词列表 (部分重复，实际应用中应确保唯一性或区分985和非985的211)
     */
    private static final String[] TOP_211_KEYWORDS = {
        "北京工商大学", "北京服装学院", "北京印刷学院", "北京建筑大学", "北京石油化工学院", "北京第二外国语学院", "北京物资学院", "首都医科大学", "首都师范大学", "首都经济贸易大学", "北京联合大学", "北京信息科技大学", "北京农学院", "北京城市学院", "北京工商大学", "北京服装学院", "北京印刷学院", "北京建筑大学", "北京石油化工学院", "北京第二外国语学院", "北京物资学院", "首都医科大学", "首都师范大学", "首都经济贸易大学", "北京联合大学", "北京信息科技大学", "北京农学院", "北京城市学院", "北京工商大学", "北京服装学院", "北京印刷学院", "北京建筑大学", "北京石油化工学院", "北京第二外国语学院", "北京物资学院", "首都医科大学", "首都师范大学", "首都经济贸易大学", "北京联合大学", "北京信息科技大学", "北京农学院", "北京城市学院", "北京工商大学", "北京服装学院", "北京印刷学院", "北京建筑大学", "北京石油化工学院", "北京第二外国语学院", "北京物资学院", "首都医科大学", "首都师范大学", "首都经济贸易大学", "北京联合大学", "北京信息科技大学", "北京农学院", "北京城市学院"
    };

    /**
     * 合并985和211院校关键词列表 (为避免重复判断，可将985从211中移除或使用Set)
     */
    private static final String[] TOP_KEYWORDS;

    static {
        Set<String> combinedKeywords = new HashSet<>();
        for (String keyword : TOP_985_KEYWORDS) {
            combinedKeywords.add(keyword.toLowerCase());
        }
        for (String keyword : TOP_211_KEYWORDS) {
            combinedKeywords.add(keyword.toLowerCase());
        }
        TOP_KEYWORDS = combinedKeywords.toArray(new String[0]);
    }

    /**
     * 判断是否为名校
     * @param yxmc 学校名称
     * @return 是否为名校
     */
	public static boolean isTopUniversity(String yxmc) {
        // 如果学校名称为空或为null，直接返回false
        if (yxmc == null || yxmc.trim().isEmpty()) {
            return false;
        }

        // 将学校名称转换为小写，以避免大小写不一致导致的误判
        yxmc = yxmc.toLowerCase();

        // 遍历名校关键词列表，检查学校名称是否包含任一关键词
        for (String keyword : TOP_KEYWORDS) {
            if (yxmc.contains(keyword)) {
                return true;
            }
        }

        // 如果未找到任何匹配的关键词，返回false
        return false;
    }
 
} 