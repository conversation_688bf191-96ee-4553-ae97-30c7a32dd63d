package com.zsdwf.utils;

import com.github.binarywang.wxpay.bean.order.WxPayMwebOrderResult;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.constant.WxPayConstants;
import com.github.binarywang.wxpay.service.WxPayService;

public class H5Payment {
	
	private static WxPayService wxPayService;
	 
    public String createH5Pay(String tradeNo, String openId, String body, String userIp, double amount) throws Exception {
        WxPayUnifiedOrderRequest orderRequest = new WxPayUnifiedOrderRequest();
        orderRequest.setOutTradeNo(tradeNo);
        orderRequest.setOpenid(openId);
        orderRequest.setBody(body);
        orderRequest.setTotalFee((int) (amount * 100));
        orderRequest.setTradeType(WxPayConstants.TradeType.MWEB);
        orderRequest.setNotifyUrl("http://www.lx2b.com/pay/h5_paid_call_back.jsp"); 
        orderRequest.setSpbillCreateIp(userIp);
        // 在这里设置H5支付的参数，如调用方式，必要时可以添加自定义参数
        // ...
 
        WxPayMwebOrderResult wxxPayMwebOrderResult = wxPayService.createOrder(orderRequest); // 调用微信支付API创建订单
        //String mwebUrl = wxPayService.createOrder(orderRequest); // 调用微信支付API创建订单
        System.out.println(wxxPayMwebOrderResult.getMwebUrl());
        return wxxPayMwebOrderResult.getMwebUrl();
        // 跳转到mwebUrl，用户在手机网站上完成支付
    }
    
    public static com.github.binarywang.wxpay.service.WxPayService payInit() {
        wxPayService = new com.github.binarywang.wxpay.service.impl.WxPayServiceImpl();
        wxPayService.setConfig(payConfig);
        return wxPayService;
	}
    
    static com.github.binarywang.wxpay.config.WxPayConfig  payConfig = new com.github.binarywang.wxpay.config.WxPayConfig ();
	static {
		payConfig.setAppId("wx29fd5eeca8b5f498");
        payConfig.setMchId("1646287519");
        payConfig.setMchKey("aaa112233A1qazxsw23edcvfr45tgbnh");
        //payConfig.setSubAppId("");
        //payConfig.setSubMchId("");
        payConfig.setKeyPath("C:\\Program Files\\etc\\apiclient_cert.p12");

        // 可以指定是否使用沙箱环境
        payConfig.setUseSandboxEnv(false);
        
        payInit();
	}
	
	
	


}
