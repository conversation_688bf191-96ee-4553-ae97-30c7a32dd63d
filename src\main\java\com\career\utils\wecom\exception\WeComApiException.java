package com.career.utils.wecom.exception;

/**
 * 企业微信API异常基类
 */
public class WeComApiException extends RuntimeException {
    
    // 错误码
    private int errorCode;
    
    public WeComApiException(String message) {
        super(message);
        this.errorCode = -1;
    }
    
    public WeComApiException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = -1;
    }
    
    public WeComApiException(int errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public WeComApiException(int errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public int getErrorCode() {
        return errorCode;
    }
    
    /**
     * 根据错误码创建特定类型的异常
     */
    public static WeComApiException create(int errorCode, String message) {
        switch (errorCode) {
            case -1:
                return new SystemBusyException(message);
            case 40001:
            case 40014:
            case 42001:
                return new AuthenticationException(errorCode, message);
            case 45009:
            case 45028:
            case 45033:
                return new ApiLimitException(errorCode, message);
            case 40003:
            case 40004:
            case 40005:
                return new InvalidParameterException(errorCode, message);
            case 60011:
            case 60012:
                return new UserException(errorCode, message);
            default:
                return new WeComApiException(errorCode, message);
        }
    }
    
    /**
     * 系统繁忙异常
     */
    public static class SystemBusyException extends WeComApiException {
        public SystemBusyException(String message) {
            super(-1, message);
        }
    }
    
    /**
     * 认证/授权相关异常
     */
    public static class AuthenticationException extends WeComApiException {
        public AuthenticationException(int errorCode, String message) {
            super(errorCode, message);
        }
    }
    
    /**
     * API调用频率限制异常
     */
    public static class ApiLimitException extends WeComApiException {
        public ApiLimitException(int errorCode, String message) {
            super(errorCode, message);
        }
    }
    
    /**
     * 参数无效异常
     */
    public static class InvalidParameterException extends WeComApiException {
        public InvalidParameterException(int errorCode, String message) {
            super(errorCode, message);
        }
    }
    
    /**
     * 用户相关异常
     */
    public static class UserException extends WeComApiException {
        public UserException(int errorCode, String message) {
            super(errorCode, message);
        }
    }
    
    /**
     * 网络相关异常
     */
    public static class NetworkException extends WeComApiException {
        public NetworkException(String message, Throwable cause) {
            super(message, cause);
        }
    }
    
    /**
     * 成员没有外部联系人权限异常 (错误码84061)
     */
    public static class NoExternalContactPermissionException extends WeComApiException {
        private String userId;
        
        public NoExternalContactPermissionException(String userId, int errorCode, String message) {
            super(errorCode, message);
            this.userId = userId;
        }
        
        public String getUserId() {
            return userId;
        }
        
        @Override
        public String getMessage() {
            return "成员 " + userId + " 没有外部联系人权限: " + super.getMessage();
        }
    }
} 