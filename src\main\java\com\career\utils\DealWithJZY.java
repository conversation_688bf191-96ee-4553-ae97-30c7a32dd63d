package com.career.utils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.util.HashMap;

import com.alibaba.fastjson2.*;
import com.career.db.JDBC;

public class DealWithJZY {
	
	static HashMap<String, String> hss = new HashMap<>();
	static {
		hss.put("文科", "WLRKQG");
		hss.put("历史", "WLRKQG");
		hss.put("理科", "14387D");
		hss.put("物理", "14387D");
		hss.put("综合", "123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");
	}
	
	public static String getx(String key){
		return hss.get(key);
	}
	
	
	public static void xx() {
		StringBuffer sb = new StringBuffer();
		File file = new File("E:\\yifenyiduan\\org3");
		File[] files = file.listFiles();
		for(int i = 0; i < files.length; i++) {
			System.out.println(files[i].getName());
			File element = files[i]; //其中一个文件
			String fileName = element.getName();
			int underSPostion = fileName.indexOf("_");
			String sf = fileName.substring(0, underSPostion);
			String kl = fileName.substring(underSPostion + 1, underSPostion + 3);
			String year = fileName.substring(underSPostion + 3, fileName.length() - 4);
			
			try {
				BufferedReader bw = new BufferedReader(new FileReader(element));
				String line = null;
				while((line = bw.readLine()) != null) {
					if(line.indexOf("Score") != -1) {
						String score = line.substring(line.indexOf(":") + 1, line.indexOf(","));
						
						line = bw.readLine();
						String persons = line.substring(line.indexOf(":") + 1, line.indexOf(","));
						
						line = bw.readLine();
						String totalPerson = line.substring(line.indexOf(":") + 1, line.indexOf(","));
						
						line = bw.readLine();
						String rankHigh = line.substring(line.indexOf(":") + 1, line.indexOf(","));
						
						line = bw.readLine();
						String rankLow = line.substring(line.indexOf(":") + 1).trim();
						
						sb.append("insert into zdks_rank(sf,nf,score,cnt,wc,kl,fd,KL_CODE) values('"+sf+"',"+year+",'" + score + "','"+persons + "','" + totalPerson+"','"+kl+"','F','"+getx(kl)+"');\r\n");
					}
				}
				
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			
			DealZDKS_GZ.writeTempFile(new File("E://yifenyiduan/quanguo/result30304.txt"), sb);
			
		}
	}
	
	
	public static void xx2() {
		StringBuffer sb = new StringBuffer();
		File file = new File("E:\\yifenyiduan\\org3");
		File[] files = file.listFiles();
		for(int i = 0; i < files.length; i++) {
			System.out.println(files[i].getName());
			File element = files[i]; //其中一个文件
			String fileName = element.getName();
			int underSPostion = fileName.indexOf("_");
			String sf = fileName.substring(0, underSPostion);
			String kl = fileName.substring(underSPostion + 1, underSPostion + 3);
			String year = fileName.substring(underSPostion + 3, fileName.length() - 4);
			
			try {
				BufferedReader bw = new BufferedReader(new FileReader(element));
				String line = null;
				StringBuffer sbb = new StringBuffer();
				while((line = bw.readLine()) != null) {
					sbb.append(line);
				}
				
				HashMap<String,String> MMM = new HashMap<>();
				JSONObject jsobject = JSONObject.parseObject(sbb.toString());
				JSONArray jsonarray = jsobject.getJSONArray("data");
				for(int ix=0;ix<jsonarray.size();ix++) {
					JSONObject obj = jsonarray.getJSONObject(ix);
					String score = obj.getString("Score");
					String Persons = obj.getString("Persons");
					String TotalPersons = obj.getString("TotalPersons");
					if(MMM.containsKey(sf+year)) {
						continue;
					}else {
						MMM.put(sf+year, sf+year);
					}
					//sb.append("insert into zdks_rank(sf,nf,score,cnt,wc,kl,fd,KL_CODE) values('"+sf+"',"+year+",'" + score + "','"+Persons + "','" + TotalPersons+"','"+kl+"','F','"+getx(kl)+"');\r\n");
					sb.append("delete from zdks_rank where sf = '"+sf+"' and nf = " + year +";\r\n");
					String tableCode = JDBC.HM_PROVINCE_CODE.get(sf);
					sb.append("delete from "+tableCode+"_zdks_rank where sf = '"+sf+"' and nf = " + year+";\r\n");
					//sb.append("insert into "+tableCode+"_zdks_rank(sf,nf,score,cnt,wc,kl,fd,KL_CODE) values('"+sf+"',"+year+",'" + score + "','"+Persons + "','" + TotalPersons+"','"+kl+"','F','"+getx(kl)+"');\r\n");
				}
				
				
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			
			DealZDKS_GZ.writeTempFile(new File("E://yifenyiduan/quanguo/result0305.txt"), sb);
			
		}
	}
	
	public static void main(String args[]) {
		
		
		xx2();
		
		
		/**
		
		StringBuffer sb = new StringBuffer();
		for(int i = 2020; i <= 2023; i++) {
			try {
				BufferedReader bw = new BufferedReader(new FileReader(new File("E://JZY/HB_"+i+"_W.txt")));
				String st = null;
				while((st = bw.readLine()) != null) {
					if(st.indexOf("Score") != -1) {
						String score = st.substring(st.indexOf("Score") + 7, st.indexOf(","));
						st = bw.readLine();
						String personSUM = st.substring(st.indexOf("Persons") + 9, st.indexOf(","));
						st = bw.readLine();
						String RankLow = st.substring(st.indexOf("TotalPersons") + 14, st.indexOf(","));
						sb.append("insert into zdks_rank(sf,nf,score,cnt,wc,kl,fd,KL_CODE) values('湖北',"+i+",'" + score + "','"+personSUM + "','" + RankLow+"','历史','F','WLRKQG');\r\n");
					}
				}
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		for(int i = 2020; i <= 2023; i++) {
			try {
				BufferedReader bw = new BufferedReader(new FileReader(new File("E://JZY/HB_"+i+"_L.txt")));
				String st = null;
				while((st = bw.readLine()) != null) {
					if(st.indexOf("Score") != -1) {
						String score = st.substring(st.indexOf("Score") + 7, st.indexOf(","));
						st = bw.readLine();
						String personSUM = st.substring(st.indexOf("Persons") + 9, st.indexOf(","));
						st = bw.readLine();
						String RankLow = st.substring(st.indexOf("TotalPersons") + 14, st.indexOf(","));
						sb.append("insert into zdks_rank(sf,nf,score,cnt,wc,kl,fd,KL_CODE) values('湖北',"+i+",'" + score + "','"+personSUM + "','" + RankLow+"','物理','F','14387D');\r\n");
					}
				}
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		DealZDKS_GZ.writeTempFile(new File("E://yifenyiduan/hb/result.txt"), sb);
		
		*/
	}

}
