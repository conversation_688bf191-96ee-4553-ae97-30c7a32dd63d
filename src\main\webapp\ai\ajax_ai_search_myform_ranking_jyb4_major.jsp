<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ page import="com.career.utils.*,com.career.db.*,java.util.*"%>

<%
JDBC jdbc = new JDBC();
ZyzdJDBC zyzdJDBC = new ZyzdJDBC();
LhyJDBC lhyJDBC = new LhyJDBC();
ZyzdFormJDBC zyzdFormJDBC = new ZyzdFormJDBC();


int selected_sort = Tools.getInt(request.getParameter("selected_sort"));
String selected_ranking_yxmc = Tools.trim(request.getParameter("selected_ranking_yxmc"));
String selected_ranking_zymc = Tools.trim(request.getParameter("selected_ranking_zymc"));


List<ZyzdBaseRankJYB4MajorBean> rankjyb4List = zyzdJDBC.getZyzdBaseRankJYB4Major(selected_ranking_zymc, selected_sort); 


ZyzdUniversityBean univ = ZyzdCache.getUniversity(selected_ranking_yxmc);

%>

<div class="card-body" style="padding:0px;">
  <div class="table-responsive">
      <table class="table text-nowrap table-bordered border-primary">
          <thead>
              <tr style="background-color:#cfe2ff"> 
                  <th scope="col">院校名称</th>
	              <th scope="col">等级</th>
              </tr>
          </thead>
          <tbody>
          		<%
				for(int i=0;i<rankjyb4List.size();i++){ 
					ZyzdBaseRankJYB4MajorBean jyb4ranking = rankjyb4List.get(i);
				%>
              <tr <%=jyb4ranking.getYxmc().equals(selected_ranking_yxmc) ? "style='background-color:#B0C4DE'" : "" %>>
	                <th scope="row">
					    <div class="d-flex align-items-center gap-2">
					        <%=Tools.view(jyb4ranking.getYxmc()) %>
					    </div>
					</th>
                  <td>
                      <%=Tools.view(jyb4ranking.getRankingLevel())%>
                  </td>
              </tr>
              <%} %>
            </tbody>
        </table>
    </div>
</div>

