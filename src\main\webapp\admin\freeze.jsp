<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page import="com.zsdwf.utils.*,com.zsdwf.db.*,java.util.*" %>
<%@include file="/WEB-INF/include/_session_admin.jsp" %>
<%
YGCardBean card = (YGCardBean)session.getAttribute("SES_ADMIN"); 
if(!card.getId().startsWith("FX")){
	return;
}

%>
<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"> 
<meta name="format-dection" content="telephone=no"/>
<title><%=CCache.SYS_TITLE_NAME %></title>
<script src="<%=request.getContextPath()%>/sources/jquery-3.6.4.min.js"></script>
<style type="text/css">
html {
    font-family: sans-serif;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
}
 
body { 
    margin: 10px;
    font-size:12px;
}
table {
    width: 100%;
    border-collapse: collapse;
}
table, th, td {
    border: 1px solid black;
}
th, td {
    padding: 8px;
}
th {
    background-color: #f2f2f2;
}
 
.pure-table {
    border-collapse: collapse;
    border-spacing: 0;
    empty-cells: show;
    border: 1px solid #cbcbcb;
}
 
.pure-table caption {
    color: #000;
    font: italic 85%/1 arial,sans-serif;
    padding: 1em 0;
    text-align: center;
}
 
.pure-table td,.pure-table th {
    border-left: 1px solid #cbcbcb;
    border-width: 0 0 0 1px;
    font-size: inherit;
    margin: 0;
    overflow: visible;
    padding: .5em 1em;
}
 
.pure-table thead {
    background-color: #e0e0e0;
    color: #000;
    text-align: left;
    vertical-align: bottom;
}
 
.pure-table td {
    background-color: transparent;
}
 
.pure-table-bordered td {
    border-bottom: 1px solid #cbcbcb;
}
 
.pure-table-bordered tbody>tr:last-child>td {
    border-bottom-width: 0;
}

.query_rec_label{
	float:left;width:70px;height:25px;line-height:25px;font-weight:bold;margin:2px 1px;
}
.query_rec_label2{ 
	float:left;width:90px;height:25px;line-height:25px;font-weight:bold;margin:2px 1px;
}
.query_rec_aa{
	float:left;width:130px;font-size:10px;height:25px;line-height:25px;margin:2px;border:1px solid #7766cc;text-align:center;cursor:pointer;
}
.query_rec_a{
	float:left;width:100px;font-size:10px;height:25px;line-height:25px;margin:2px;border:1px solid #7766cc;text-align:center;cursor:pointer;
}
.query_rec_b{
	float:left;width:60px;font-size:10px;height:25px;line-height:25px;margin:2px;border:1px solid #7766cc;text-align:center;cursor:pointer;
}
.query_rec_c{
	float:left;width:40px;font-size:10px;height:25px;line-height:25px;margin:2px;border:1px solid #7766cc;text-align:center;cursor:pointer;
}
.query_rec_d{
	float:left;width:80px;font-size:10px;height:25px;line-height:25px;margin:2px;border:1px solid #7766cc;text-align:center;cursor:pointer;
}
.query_bind_event{
	;
}
.query_select_bind_event{
	;
}


.school_pick{
	float:left;width:200px;border:1px solid red;height:20px;text-align:left;line-height:20px;margin:1px 10px;font-size:12px;;
}

.major_one{
	cursor:pointer;float:left;width:80px;height:400px;text-align:center;margin:1px;
}
.major_two{
	cursor:pointer;float:left;width:140px;height:400px;text-align:center;margin:1px;
}
.major_three{
	cursor:pointer;float:left;width:140px;height:400px;text-align:center;margin:1px;
}

.school_list{
	cursor:pointer;height:18px;text-align:left;line-height:18px;margin:1px 2px;font-size:12px;;
}
.major_info{
	cursor:pointer;float:left;width:210px;text-align:left;margin:2px;border:1px solid #cc0000;padding:1px;
}
.major_info3{
	cursor:pointer;float:left;width:300px;text-align:left;margin:2px;border:1px solid #cc0000;padding:1px;
}
.major_info2{
	cursor:pointer;float:left;width:128px;text-align:left;margin:2px;border:1px solid #cc0000;padding:1px;
}
.major_info22{
	cursor:pointer;float:left;width:155px;text-align:left;margin:2px;border:1px solid #cc0000;padding:1px;
}
.major_info_title{
	cursor:pointer;float:left;width:200px;text-align:left;margin:2px;font-weight:bold;fon-size:16px;color:blue;;
}

.fixedLayer1 { 
	position:fixed; 
	left: 0px; 
	top: 0px; 
	width:100px; 
	line-height:40px; 
	height:40px;
	background: #FC6; 
	font-size:26px;
	text-align:center;
	font-weight:bold;
	color:#000;
	border:1px solid #F90; 
	filter: alpha(opacity = 90);
	-moz-opacity: 0.9;
	-khtml-opacity: 0.9;
	opacity: 0.9;
} 

.fixedLayer2 { 
	position:fixed; 
	left: 0px; 
	top: 0px; 
	width:100px; 
	line-height:30px; 
	background: #FC6; 
	border:1px solid #F90; 
} 

.fixedLayerMenu { 
	line-height:25px; 
	height:25px;
	margin:5px;
} 
</style>

</head>
<body>
<div>

	
	<div style="margin:5px;">
		<div style="font-size:16px;font-weight:bold;text-align:left;'">卡密冻结系统 V1.0</div>
	</div>
	
		<div style="margin:3px;">
			<div class="query_rec_label">卡号：</div>
			<div style="float:left;">
				<input type="text" style="width:140px;height:30px;font-size:16px;color:blue;font-weight:bold;" id="cardno"/>
				<input type="button" style="width:60px;font-size:14px;height:30px;" value="查询" onclick="MM_searchCard();"/>
			</div>
			<div style="clear:both;"></div>
		</div>
		
		<div style="margin:3px;">
			<div class="query_rec_label">密码：</div>
			<div style="float:left;">
				<input type="text" style="width:200px;height:30px;font-size:12px;color:blue;font-weight:bold;" id="passwd"/>
			</div>
			<div style="clear:both;"></div>
		</div>
		 
		
		<div style="margin:3px;">
			<div class="query_rec_label"></div>
			<div style="float:left;">
				<input type="button" style="width:70px;height:30px;font-size:12px;" value="立即冻结" onclick="MM_freeze();"/>
			</div> 
			<div style="float:left;margin-left:5px;">  
				<input type="button" style="width:70px;height:30px;font-size:12px;" value="诊断解锁" onclick="MM_unlock();"/>
			</div>
			<div style="float:left;margin-left:5px;">  
				<input type="button" style="width:40px;height:30px;font-size:12px;" value="重置" onclick="MM_reset();"/>
			</div>
			<div style="clear:both;"></div>
		</div>
		
		<div style="margin:3px;margin-top:20px;"> 
		<b>立即冻结：</b>冻结后用户不能登录，等于此卡作废。<br>
		<b>诊断解锁：</b>诊断次数达3次，如还需要再查询，可点此按钮多加3次。<br> 
		<b>重置：</b>重置用户省份选择/城市选择/选科选科，恢复初始状态，用户需要重新登录后重新选择。此功能不会删除用户的志愿表/测评等数据。
		</div>
</div>


<div class="row" style="margin-top:20px;">
<table>
<tr>
	<th>用户操作</th>
	<th>管理操作</th>
</tr> 
<tr>
	<td>
		<a href="<%=request.getContextPath()%>/admin/freeze.jsp" style="margin:10px;">冻结/解锁/重置</a><br><br>
	</td>
	<td> 
		<a href="<%=request.getContextPath()%>/admin/clear_cache_PROVINCE.jsp" style="margin:10px;">更新省份缓存</a><br><br>
		<a href="<%=request.getContextPath()%>/admin/clear_cache_SERVER.jsp" style="margin:10px;">更新诊断批次缓存</a> <br><br>
		<a href="<%=request.getContextPath()%>/admin/reload_offcn_data_SERVER.jsp" style="margin:10px;">重启OFFCN进程</a><br><br>
		<a href="<%=request.getContextPath()%>/admin/zdks_generate.jsp" style="margin:10px;">诊断换算</a>
	</td>
</tr>
</table>
</div>



</body>
</html>
<script>

function MM_freeze(){
	var cardno = $("#cardno").val();
	var passwd = $("#passwd").val();
	
	$.ajax({
        type : "POST",
        url : "<%=request.getContextPath()%>/admin/_freeze_card.jsp",
		data: {"cardno" : cardno,"passwd" : passwd}, 
		//dataType: "json",
        //请求成功
        success : function(result) {
        	if(result.indexOf("ERR:LOGIN:REDIRECT-X_LOGIN") != -1){
				location.href = "<%=request.getContextPath()%>/admin/login.jsp";
        	}else{
        		alert($.trim(result));
        	}
        },
        //请求失败，包含具体的错误信息
        error : function(e){
            console.log(e.status);
            console.log(e.responseText);
        }
    });
}

function MM_unlock(){
	var cardno = $("#cardno").val();
	var passwd = $("#passwd").val();
	
	$.ajax({
        type : "POST",
        url : "<%=request.getContextPath()%>/admin/_unlock_card.jsp",
		data: {"cardno" : cardno,"passwd" : passwd}, 
		//dataType: "json",
        //请求成功
        success : function(result) {
        	if(result.indexOf("ERR:LOGIN:REDIRECT-X_LOGIN") != -1){
				location.href = "<%=request.getContextPath()%>/admin/login.jsp";
        	}else{
        		alert($.trim(result));
        	}
        },
        //请求失败，包含具体的错误信息
        error : function(e){
            console.log(e.status);
            console.log(e.responseText);
        }
    });
}

function MM_reset(){
	var cardno = $("#cardno").val();
	var passwd = $("#passwd").val();
	$.ajax({
        type : "POST",
        url : "<%=request.getContextPath()%>/admin/_unlock_card.jsp",
		data: {"cardno" : cardno,"passwd" : passwd}, 
		//dataType: "json",
        //请求成功
        success : function(result) {
        	if(result.indexOf("ERR:LOGIN:REDIRECT-X_LOGIN") != -1){
				location.href = "<%=request.getContextPath()%>/admin/login.jsp";
        	}else{
        		alert($.trim(result));
        	}
        },
        //请求失败，包含具体的错误信息
        error : function(e){
            console.log(e.status);
            console.log(e.responseText);
        }
    });
}

function MM_searchCard(){
	var cardno = $("#cardno").val();
	
	$.ajax({
        type : "POST",
        url : "<%=request.getContextPath()%>/admin/_freeze_card_search.jsp",
		data: {"cardno" : cardno}, 
		//dataType: "json",
        //请求成功
        success : function(result) {
        	if(result.indexOf("ERR:LOGIN:REDIRECT-X_LOGIN") != -1){
				location.href = "<%=request.getContextPath()%>/admin/login.jsp";
        	}else{
        		alert($.trim(result));
        	}
        },
        //请求失败，包含具体的错误信息
        error : function(e){
            console.log(e.status);
            console.log(e.responseText);
        }
    });
}

</script>