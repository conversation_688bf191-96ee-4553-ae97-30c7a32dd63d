package com.career.utils.liuxue;

import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

public class ImageDownloader {

	public static void main(String[] args) {
        String url = "https://mp.weixin.qq.com/s/jlqshJhg_8VY4g7HGSuBxg";
        List<String> imageUrls = getImageUrls(url);
        downloadImages(imageUrls);
    }

    public static List<String> getImageUrls(String url) {
        List<String> imageUrls = new ArrayList<>();
        try {
            HttpClient httpClient = createHttpClientWithProxy();
            HttpGet httpGet = new HttpGet(url);
            HttpResponse response = httpClient.execute(httpGet);
            HttpEntity entity = response.getEntity();
            String html = EntityUtils.toString(entity);
            Document doc = Jsoup.parse(html);
            Elements imgElements = doc.getElementsByTag("img");
            for (Element imgElement : imgElements) {
                String imgUrl = imgElement.absUrl("src");
                if (!imgUrl.isEmpty()) {
                    imageUrls.add(imgUrl);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return imageUrls;
    }

    public static void downloadImages(List<String> imageUrls) {
        for (String imageUrl : imageUrls) {
            try {
                HttpClient httpClient = createHttpClientWithProxy();
                HttpGet httpGet = new HttpGet(imageUrl);
                HttpResponse response = httpClient.execute(httpGet);
                HttpEntity entity = response.getEntity();
                InputStream inputStream = entity.getContent();
                String fileName = imageUrl.substring(imageUrl.lastIndexOf("/") + 1);
                OutputStream outputStream = new FileOutputStream("F:\\就业报告\\CQimages/" + fileName);
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                inputStream.close();
                outputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static HttpClient createHttpClientWithProxy() {
        CredentialsProvider credsProvider = new BasicCredentialsProvider();
        credsProvider.setCredentials(
                new AuthScope("www.16yun.cn", 5445),
                new UsernamePasswordCredentials("16QMSOML", "280651"));

        HttpHost proxy = new HttpHost("www.16yun.cn", 5445);
        RequestConfig requestConfig = RequestConfig.custom()
                .setProxy(proxy)
                .build();

        return HttpClients.custom()
                .setDefaultCredentialsProvider(credsProvider)
                .setDefaultRequestConfig(requestConfig)
                .build();
    }
    
}
