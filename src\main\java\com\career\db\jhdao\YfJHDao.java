package com.career.db.jhdao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;

import com.career.db.DatabaseUtils;
import com.career.db.JHBean;
import com.career.db.JHxBean;
import com.career.db.SchoolBean;
import com.career.db.ZyzdFormJDBC;
import com.career.utils.ResultVO;
import com.career.utils.SQLLogUtils;
import com.career.utils.Tools;

public class YfJHDao {
	

	public static int PAGE_ROW_CNT = 20;
	
	static {
		try {
			Class.forName("com.mysql.cj.jdbc.Driver");
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
	
	public YfJHBean getJHById(int jhYear, String sfCode, int id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = JHDatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM yf_"+sfCode+"_jh_" + jhYear + " x WHERE x.id = ?";
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setInt(1, id);
			YfJHBean bean = null;
			SQLLogUtils.printSQL(ps);
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = setJHBeanAll(rs);
			}
			return bean;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}

	
	public YfJHBean getJHByYxdmAndZydmAndZyz(int jhYear, String sfCode, String xkCode, String pc, String pc_code, String yxdm, String zydm, String zyz) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = jhYear-1;
		try {
			conn = JHDatabaseUtils.getConnection();
			String zyzSQL = Tools.isEmpty(zyz) ? "" : "and x.zyz  = ? ";
			String SELECT_CONDITION = "SELECT * FROM yf_"+sfCode+"_jh_" + jhYear + " x WHERE x.pc = ? and pc_code = ? and xk_code like ? and x.yxdm = ? and x.zydm = ? " + zyzSQL;
			String ORDER_CONDITION = " ORDER BY x.zdfwc is NULL, x.zdfwc ASC";
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			ps.setString(4, yxdm);
			ps.setString(5, zydm);
			if(!Tools.isEmpty(zyz)) {
				ps.setString(6, zyz);
			}
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				return setJHBeanAll(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public List<YfJHBean> getJHByYxdmAndZyz(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> yxdmSets, HashSet<String> zyzSets) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = jhYear-1;
		List<YfJHBean> list = new ArrayList<>();
		try {
			conn = JHDatabaseUtils.getConnection();
			String yxdmSQL = yxdmSets.size() == 0 ? "and x.yxdm = '这种情况是不能查询的'" : "and x.yxdm in ("+Tools.getSQLQueryin(yxdmSets)+")";
			String zyzSQL = zyzSets.size() == 0 ? "" : " and x.zyz in ("+Tools.getSQLQueryin(zyzSets)+")";
			String SELECT_CONDITION = "SELECT * FROM yf_"+sfCode+"_jh_" + jhYear + " x WHERE x.pc = ? and pc_code = ? and xk_code like ? " + yxdmSQL + zyzSQL;
			String ORDER_CONDITION = " ORDER BY x.zdfwc is NULL, x.zdfwc ASC";
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				list.add(setJHBeanAll(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<YfJHBean> getJHByYxdmAndZyz(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> yxdmSets, HashSet<String> zyzSets, HashSet<String> zymlSets) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = jhYear-1;
		List<YfJHBean> list = new ArrayList<>();
		try {
			conn = JHDatabaseUtils.getConnection();
			String yxdmSQL = yxdmSets.size() == 0 ? "and x.yxdm = '这种情况是不能查询的'" : "and x.yxdm in ("+Tools.getSQLQueryin(yxdmSets)+")";
			String zyzSQL = zyzSets.size() == 0 ? "" : " and x.zyz in ("+Tools.getSQLQueryin(zyzSets)+")";
			String zymlSQL = zymlSets.size() == 0 ? "" : " and x.zyml in ("+Tools.getSQLQueryin(zymlSets)+")";
			String SELECT_CONDITION = "SELECT * FROM yf_"+sfCode+"_jh_" + jhYear + " x WHERE x.pc = ? and pc_code = ? and xk_code like ? " + yxdmSQL + zyzSQL + zymlSQL;
			String ORDER_CONDITION = " ORDER BY x.zdfwc is NULL, x.zdfwc ASC";
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				list.add(setJHBeanAll(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public List<YfJHBean> getJHByYxdmAndZyz(int jhYear, String sfCode, String xkCode, String pc, String pc_code, String yxdm, String zyz) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = jhYear-1;
		List<YfJHBean> list = new ArrayList<>();
		try {
			conn = JHDatabaseUtils.getConnection();
			String zyzSQL = Tools.isEmpty(zyz) ? "" : " and x.zyz = ? ";
			String SELECT_CONDITION = "SELECT * FROM yf_"+sfCode+"_jh_" + jhYear + " x WHERE x.pc = ? and pc_code = ? and xk_code like ? and yxdm = ? " + zyzSQL;
			String ORDER_CONDITION = " ORDER BY x.zdfwc is NULL, x.zdfwc ASC";
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			ps.setString(4, yxdm);
			if(!Tools.isEmpty(zyz)) {
				ps.setString(5, zyz);
			}
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				list.add(setJHBeanAll(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<YfJHxBean> getJHxByYxdmAndZyz(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> yxdmSets, HashSet<String> zyzSets) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<YfJHxBean> list = new ArrayList<>();
		try {
			conn = JHDatabaseUtils.getConnection();
	
			String yxmcSetsSQL = yxdmSets.size() == 0 ? "and x.yxdm = '这种情况是不能查询的'" : " AND x.yxdm in ("+Tools.getSQLQueryin(yxdmSets)+")";
			String zyzSetsSQL = zyzSets.size() == 0 ? "" : " AND x.zyz in ("+Tools.getSQLQueryin(zyzSets)+")";
			String SEARCH_CONDITION = "SELECT * FROM yf_" + sfCode + "_jh_" + jhYear + "x x WHERE x.xk_code like ? and x.pc = ? and x.pc_code = ? " + yxmcSetsSQL + zyzSetsSQL;
			String ORDER_CONDITION = " ORDER BY x.znzdfwc is NULL, x.znzdfwc ASC";
			
			ps = conn.prepareStatement(SEARCH_CONDITION + ORDER_CONDITION); 
			int index = 1;
			ps.setString(index++, "%"+xkCode+"%");
			ps.setString(index++, pc);
			ps.setString(index++, pc_code);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				list.add(setJHxBeanAll(rs));
			}
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<YfJHBean> getJHByYxmcOrg(int jhYear, String sfCode, String xkCode, String pc, String pc_code, String yxmc_org) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<YfJHBean> list = new ArrayList<>();
		int dataYear = jhYear-1;
		try {
			conn = JHDatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM yf_"+sfCode+"_jh_" + jhYear + " x WHERE x.xk_code like ? AND x.pc_code = ? AND x.yxmc_org = ? and x.pc = ? ORDER BY x.zdfwc ASC";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, "%" + xkCode + "%");
			ps.setString(2, pc_code); 
			ps.setString(3, yxmc_org);
			ps.setString(4, pc);

			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === getJHByYxmcOrg(): ", ps);
			
			while (rs.next()) {
				list.add(setJHBeanAll(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<YfJHBean> getZyByZyml(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> zymlSets, HashSet<String> yxsfSets, int zdfwc_from, int zdfwc_to) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<YfJHBean> list = new ArrayList<>();
		try {
			conn = JHDatabaseUtils.getConnection();
			String zymlSQL = zymlSets.size() == 0 ? "" : "and x.zyml in ("+Tools.getSQLQueryin(zymlSets)+")";
			String yxsfSQL = yxsfSets.size() == 0 ? "" : "and x.yxsf in ("+Tools.getSQLQueryin(yxsfSets)+")";
			String SELECT_CONDITION = "SELECT * FROM yf_"+sfCode+"_jh_" + jhYear + " x WHERE x.pc = ? and x.pc_code = ? and x.xk_code like ? "+yxsfSQL+" and (x.zdfwc between ? and ?) " + zymlSQL;
			String ORDER_CONDITION = " ORDER BY x.zdfwc ASC";
			
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			ps.setInt(4, zdfwc_from);
			ps.setInt(5, zdfwc_to);

			SQLLogUtils.printSQL("getZyByZyml-->", ps);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setJHBeanAll(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<YfJHBean> getJHByYxmcOrgSets(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> yxmcOrgSets) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<YfJHBean> list = new ArrayList<>();
		try {
			conn = JHDatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM yf_"+sfCode+"_jh_" + jhYear + " x WHERE x.xk_code like ? AND x.pc_code = ? AND x.yxmc_org in ("+Tools.getSQLQueryin(yxmcOrgSets)+") and x.pc = ? ORDER BY x.yxmc, x.zyz, x.zdf DESC";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, "%" + xkCode + "%");
			ps.setString(2, pc_code);
			ps.setString(3, pc);

			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setJHBeanAll(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public HashMap<String, List<SchoolBean>> getZdfByYxmcOrg(String sfCode, int jhYear, HashSet<String> yxmcOrgSets, String xk_code) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, List<SchoolBean>> MAP = new HashMap<>();
		try {
			conn = JHDatabaseUtils.getConnection();
			String SQL = "SELECT distinct yxmc_org, zdf_a, zdfwc_a, pc FROM yf_"+sfCode+"_jh_" + jhYear + " x WHERE x.yxmc_org IN ("+Tools.getSQLQueryin(yxmcOrgSets)+") AND xk_code LIKE ? GROUP BY yxmc_org, pc, zdf_a, zdfwc_a order by yxmc_org, zdf_a desc ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%"+xk_code+"%");
			rs = ps.executeQuery();
			
			while (rs.next()) {
				String yxmc = rs.getString("yxmc_org");
				List<SchoolBean> list = null;
				
				if(MAP.containsKey(yxmc)){
					list = MAP.get(yxmc);
				}else {
					list = new ArrayList<>();
				}
				
				SchoolBean schoolBean = new SchoolBean();
				schoolBean.setZdf(rs.getString("zdf_a"));
				schoolBean.setZdfwc(rs.getString("zdfwc_a"));
				schoolBean.setPc(rs.getString("pc"));
				
				list.add(schoolBean);
				
				MAP.put(yxmc, list);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return MAP;
	}
	
	/**
	 * 专业优先
	 * @param jhYear
	 * @param sfCode
	 * @param xkCode
	 * @param pc
	 * @param pc_code
	 * @param selectedSfValuesSets
	 * @param zymlSets
	 * @param zymcSets
	 * @param selectedYXTAGValuesSets
	 * @param selectedYXLXValuesSets
	 * @param selectedBXXZValuesSet
	 * @param keywords
	 * @param wc_start
	 * @param wc_to
	 * @param pageNumber
	 * @return
	 */
	public ResultVO searchJhDataForZyyx(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> selectedSfValuesSets, HashSet<String> zymlSets, HashSet<String> zymcSets, 
				HashSet<String> selectedYXTAGValuesSets, HashSet<String> selectedYXLXValuesSets, HashSet<String> selectedBXXZValuesSet, HashSet<String> selectedTspcExceptValuesSet, boolean is_hz, String keywords, String keywords_ext, int selected_znzyls, int selected_znzys, int wc_start, int wc_to, int pageNumber) {
		
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<YfJHBean> list = new ArrayList<>();
		ResultVO resultVO = new ResultVO();
		try {
			conn = JHDatabaseUtils.getConnection();
			String selectedSfValuesSetsSQL = selectedSfValuesSets.size() == 0 ? "" : " AND x.yxsf in ("+Tools.getSQLQueryin(selectedSfValuesSets)+")";
			String zymlSetsSQL = zymlSets.size() == 0 ? "" : " AND x.zyml in ("+Tools.getSQLQueryin(zymlSets)+")";
			String zymcSetsSQL = zymcSets.size() == 0 ? "" : " AND x.zymc_org in ("+Tools.getSQLQueryin(zymcSets)+")";
			String tspcExceptSetsSQL = selectedTspcExceptValuesSet.size() == 0 ? "" : " AND (x.pc_type is NULL OR x.pc_type not in ("+Tools.getSQLQueryin(selectedTspcExceptValuesSet)+"))";
			String selectedYXLXValuesSetsSQL = selectedYXLXValuesSets.size() == 0 ? "" : " AND x.ind_catg in ("+Tools.getSQLQueryin(selectedYXLXValuesSets)+")";
			
			HashSet<String> new_selectedBXXZValuesSet = new HashSet<>();
			boolean contained_hz = false;
			if(selectedBXXZValuesSet.size() > 0) {
				Iterator<String> it = selectedBXXZValuesSet.iterator();
				while(it.hasNext()) {
					String itStr = it.next();
					if(itStr.indexOf("合作") != -1) {
						contained_hz = true;
					}else {
						new_selectedBXXZValuesSet.add(itStr);
					}
				}
			}
			
			//公办/中外合作办学
			String selectedBXXZValuesSetSQL = selectedBXXZValuesSet.size() == 0 ? "" : " AND x.ind_nature in ("+Tools.getSQLQueryin(selectedBXXZValuesSet)+")";
			String selectedHzbxValuesSetSQL =  is_hz ? " AND x.is_hz = 1 " : " AND (x.is_hz = 0 OR x.is_hz is null) ";
			
			
			String yxTagsSQL = "";
			if(selectedYXTAGValuesSets.size() > 0) {
				Iterator<String> it = selectedYXTAGValuesSets.iterator();
				StringBuffer temp_sql = new StringBuffer();
				while(it.hasNext()) {
					String itStr = it.next();
					temp_sql.append(" OR x.yx_tags_all like '%"+itStr+"%' ");
				}
				
				if(!Tools.isEmpty(temp_sql.toString())) {
					yxTagsSQL = " AND (" + temp_sql.substring(4) + ")";
				}
			}
			
			
			
			String SEARCH_CONDITION = " FROM yf_" + sfCode + "_jh_" + jhYear + " x WHERE x.xk_code like ? and x.pc = ? and x.pc_code = ? and ((x.yxmc like ? or x.zymc like ? "+(Tools.isEmpty(keywords + keywords_ext) ? "OR x.zybz IS NULL" : "")+" OR x.zybz like ?) AND (x.yxmc like ? or x.zymc like ? "+(Tools.isEmpty(keywords + keywords_ext) ? "OR x.zybz IS NULL" : "")+" OR x.zybz like ?)) and (znzyls IS NULL OR znzyls <= ?) and (znzys IS NULL OR znzys <= ?) and (x.zdfwc is null OR x.zdfwc <= 0 OR CAST(COALESCE(x.zdfwc,99999999) AS SIGNED) between ? and ?) " 
					+ selectedSfValuesSetsSQL + zymlSetsSQL + zymcSetsSQL + selectedYXLXValuesSetsSQL + selectedBXXZValuesSetSQL + selectedHzbxValuesSetSQL + tspcExceptSetsSQL + yxTagsSQL ;
			String ORDER = " ORDER BY x.zdfwc IS NULL, x.zdfwc <= 0,  CAST(COALESCE(x.zdfwc,99999999) AS SIGNED) ASC LIMIT ?,?"; 
			String SELECT_RECORD = "SELECT * " + SEARCH_CONDITION + ORDER;
			String SELECT_CNT = "SELECT count(*) as cnt " + SEARCH_CONDITION ;
			  
			ps = conn.prepareStatement(SELECT_RECORD); 
			int index = 1;
			ps.setString(index++, "%"+xkCode+"%");
			ps.setString(index++, pc);
			ps.setString(index++, pc_code);
			ps.setString(index++, "%"+keywords+"%"); 
			ps.setString(index++, "%"+keywords+"%");
			ps.setString(index++, "%"+keywords+"%");
			ps.setString(index++, "%"+keywords_ext+"%");
			ps.setString(index++, "%"+keywords_ext+"%");
			ps.setString(index++, "%"+keywords_ext+"%");
			ps.setInt(index++, selected_znzyls);
			ps.setInt(index++, selected_znzys);
			ps.setInt(index++, wc_start);
			ps.setInt(index++, wc_to);
			ps.setInt(index++, (pageNumber - 1) * PAGE_ROW_CNT);
			ps.setInt(index++, PAGE_ROW_CNT);
			SQLLogUtils.printSQL(ps);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setJHBeanAll(rs)); 
			}
			
			
			index = 1;
			ps = conn.prepareStatement(SELECT_CNT);
			ps.setString(index++, "%"+xkCode+"%");
			ps.setString(index++, pc);
			ps.setString(index++, pc_code);
			ps.setString(index++, "%"+keywords+"%");
			ps.setString(index++, "%"+keywords+"%");
			ps.setString(index++, "%"+keywords+"%");
			ps.setString(index++, "%"+keywords_ext+"%");
			ps.setString(index++, "%"+keywords_ext+"%");
			ps.setString(index++, "%"+keywords_ext+"%");
			ps.setInt(index++, selected_znzyls);
			ps.setInt(index++, selected_znzys);
			ps.setInt(index++, wc_start);
			ps.setInt(index++, wc_to);
			rs = ps.executeQuery();
			while (rs.next()) {
				resultVO.setRecordCnt(rs.getInt("cnt"));
			}
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		resultVO.setResult(list);
		resultVO.setCurrentPage(pageNumber);
		
		return resultVO;
	}
	
	public ResultVO searchJhxDataInFormMgrForReplace(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> selectedSfValuesSets, HashSet<String> zymcSets, 
			HashSet<String> selectedYXTAGValuesSets, HashSet<String> selectedYXLXValuesSets, HashSet<String> selectedBXXZValuesSet, HashSet<String> selectedTspcExceptValuesSet, boolean is_hz, String keywords, String keywords_ext, int selected_znzyls, int selected_znzys, int wc_start, int wc_to, int pageNumber) {
	
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<YfJHxBean> list = new ArrayList<>();
		ResultVO resultVO = new ResultVO();
		try {
			conn = JHDatabaseUtils.getConnection();
			String selectedSfValuesSetsSQL = selectedSfValuesSets.size() == 0 ? "" : " AND x.yxsf in ("+Tools.getSQLQueryin(selectedSfValuesSets)+")";
			String selectedYXLXValuesSetsSQL = selectedYXLXValuesSets.size() == 0 ? "" : " AND x.ind_catg in ("+Tools.getSQLQueryin(selectedYXLXValuesSets)+")";
			String tspcExceptSetsSQL = selectedTspcExceptValuesSet.size() == 0 ? "" : " AND (x.pc_type is NULL OR x.pc_type not in ("+Tools.getSQLQueryin(selectedTspcExceptValuesSet)+"))";
			
			String zymcSetsSQL = "";
			if(zymcSets.size() > 0) {
				Iterator<String> it = zymcSets.iterator();
				StringBuffer temp_sql = new StringBuffer();
				while(it.hasNext()) {
					String itStr = it.next();
					temp_sql.append(" OR x.znzy like '%"+itStr+"%' ");
				}
				
				if(!Tools.isEmpty(temp_sql.toString())) {
					zymcSetsSQL = " AND (" + temp_sql.substring(4) + ")";
				}
			}
			
			//公办/中外合作办学
			String selectedBXXZValuesSetSQL = selectedBXXZValuesSet.size() == 0 ? "" : " AND x.ind_nature in ("+Tools.getSQLQueryin(selectedBXXZValuesSet)+")";
			String selectedHzbxValuesSetSQL =  is_hz ? " AND x.is_hz = 1 " : " AND (x.is_hz = 0 OR x.is_hz is null) ";
			
			String yxTagsSQL = ""; 
			if(selectedYXTAGValuesSets.size() > 0) {
				Iterator<String> it = selectedYXTAGValuesSets.iterator();
				StringBuffer temp_sql = new StringBuffer();
				while(it.hasNext()) {
					String itStr = it.next();
					temp_sql.append(" OR x.yx_tags_all like '%"+itStr+"%' ");
				}
				
				if(!Tools.isEmpty(temp_sql.toString())) {
					yxTagsSQL = " AND (" + temp_sql.substring(4) + ")";
				}
			}
			
			
			String SEARCH_CONDITION = " FROM yf_" + sfCode + "_jh_" + jhYear + "x x WHERE x.xk_code like ? AND x.pc = ? AND x.pc_code = ? AND ((x.yxmc like ? "+(Tools.isEmpty(keywords + keywords_ext) ? "OR x.znzy IS NULL" : "")+" OR x.znzy like ?) "
					+ "AND (x.yxmc like ? "+(Tools.isEmpty(keywords + keywords_ext) ? "OR x.znzy IS NULL" : "")+" OR x.znzy like ?)) AND (znzyls is null OR znzyls <= ?) AND (x.znzdfwc is null OR x.znzdfwc <= 0 OR CAST(COALESCE(x.znzdfwc,99999999) AS SIGNED) between ? AND ?) " 
					+ selectedSfValuesSetsSQL + zymcSetsSQL + selectedYXLXValuesSetsSQL + selectedBXXZValuesSetSQL + selectedHzbxValuesSetSQL + tspcExceptSetsSQL + yxTagsSQL ;
			String ORDER = " ORDER BY x.znzdfwc IS NULL, x.znzdfwc <= 0, CAST(COALESCE(x.znzdfwc,99999999) AS SIGNED) ASC LIMIT ?,?";  
			String SELECT_RECORD = "SELECT * " + SEARCH_CONDITION + ORDER;
			String SELECT_CNT = "SELECT count(*) as cnt " + SEARCH_CONDITION ; 
			System.out.println(SELECT_RECORD);
			ps = conn.prepareStatement(SELECT_RECORD);
			int index = 1; 
			ps.setString(index++, "%"+xkCode+"%");
			ps.setString(index++, pc);
			ps.setString(index++, pc_code);
			ps.setString(index++, "%"+keywords+"%");
			ps.setString(index++, "%"+keywords+"%");
			ps.setString(index++, "%"+keywords_ext+"%");
			ps.setString(index++, "%"+keywords_ext+"%");
			ps.setInt(index++, selected_znzyls);
			ps.setInt(index++, wc_start);
			ps.setInt(index++, wc_to);
			ps.setInt(index++, (pageNumber - 1) * PAGE_ROW_CNT);
			ps.setInt(index++, PAGE_ROW_CNT);
			SQLLogUtils.printSQL(ps);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setJHxBeanAll(rs));
			}
			
			
			index = 1;
			ps = conn.prepareStatement(SELECT_CNT);
			ps.setString(index++, "%"+xkCode+"%");
			ps.setString(index++, pc);
			ps.setString(index++, pc_code);
			ps.setString(index++, "%"+keywords+"%");
			ps.setString(index++, "%"+keywords+"%");
			ps.setString(index++, "%"+keywords_ext+"%");
			ps.setString(index++, "%"+keywords_ext+"%");
			ps.setInt(index++, selected_znzyls);
			ps.setInt(index++, wc_start);
			ps.setInt(index++, wc_to);
			rs = ps.executeQuery();
			while (rs.next()) {
				resultVO.setRecordCnt(rs.getInt("cnt"));
			}
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		resultVO.setResult(list);
		resultVO.setCurrentPage(pageNumber);
		return resultVO;
	}
	
	public static YfJHxBean setJHxBeanAll(ResultSet rs) throws Exception {
	    YfJHxBean bean = new YfJHxBean();
	    bean.setId(rs.getInt("id"));
	    bean.setSf(rs.getString("sf"));
	    bean.setNf(rs.getInt("nf"));
	    bean.setYxdm(rs.getString("yxdm"));
	    bean.setYxmc(rs.getString("yxmc"));
	    bean.setYxbz(rs.getString("yxbz"));
	    bean.setYxmc_org(rs.getString("yxmc_org"));
	    bean.setYcwc(rs.getInt("ycwc"));
	    bean.setZnzys(rs.getInt("znzys"));
	    bean.setZnzy(rs.getString("znzy"));
	    bean.setZnjhs(rs.getInt("znjhs"));
	    bean.setZnzdfwc(rs.getInt("znzdfwc"));
	    bean.setZnzyls(rs.getInt("znzyls"));
	    bean.setZyz(rs.getString("zyz"));
	    bean.setPc(rs.getString("pc"));
	    bean.setPc_code(rs.getString("pc_code"));
	    bean.setPc_type(rs.getString("pc_type"));
	    bean.setPc_desc(rs.getString("pc_desc"));
	    bean.setPc_remark(rs.getString("pc_remark"));
	    bean.setXk(rs.getString("xk"));
	    bean.setXk_code(rs.getString("xk_code"));
	    bean.setXk_code_org(rs.getString("xk_code_org"));
	    bean.setZx(rs.getString("zx"));
	    bean.setLqpc(rs.getString("lqpc"));
	    bean.setInd_nature(rs.getString("ind_nature"));
	    bean.setInd_catg(rs.getString("ind_catg"));
	    bean.setCnt_grad(rs.getFloat("cnt_grad"));
	    bean.setYxsf(rs.getString("yxsf"));
	    bean.setYxcs(rs.getString("yxcs"));
	    bean.setYx_tags(rs.getString("yx_tags"));
	    bean.setYx_tags_all(rs.getString("yx_tags_all"));
	    bean.setYx_tag_all_for_search(rs.getString("yx_tag_all_for_search"));
	    bean.setIs_hz(rs.getInt("is_hz"));
	    bean.setIs_first_school(rs.getInt("is_first_school"));
	    bean.setZdf_a(rs.getInt("zdf_a"));
	    bean.setZdf_b(rs.getInt("zdf_b"));
	    bean.setZdf_c(rs.getInt("zdf_c"));
	    bean.setZdfwc_a(rs.getInt("zdfwc_a"));
	    bean.setZdfwc_b(rs.getInt("zdfwc_b"));
	    bean.setZdfwc_c(rs.getInt("zdfwc_c"));
	    bean.setPjf_a(rs.getInt("pjf_a"));
	    bean.setPjf_b(rs.getInt("pjf_b"));
	    bean.setPjf_c(rs.getInt("pjf_c"));
	    bean.setPjfwc_a(rs.getInt("pjfwc_a"));
	    bean.setPjfwc_b(rs.getInt("pjfwc_b"));
	    bean.setPjfwc_c(rs.getInt("pjfwc_c"));
	    bean.setJhs_a(rs.getInt("jhs_a"));
	    bean.setJhs_b(rs.getInt("jhs_b"));
	    bean.setJhs_c(rs.getInt("jhs_c"));
	    bean.setLqrs_a(rs.getInt("lqrs_a"));
	    bean.setLqrs_b(rs.getInt("lqrs_b"));
	    bean.setLqrs_c(rs.getInt("lqrs_c"));
	    return bean;
	}
	
	public static YfJHBean setJHBeanAll(ResultSet rs) throws Exception {
	    YfJHBean bean = new YfJHBean();
	    bean.setId(rs.getInt("id"));
	    bean.setSf(rs.getString("sf"));
	    bean.setNf(rs.getInt("nf"));
	    bean.setPc(rs.getString("pc"));
	    bean.setPc_code(rs.getString("pc_code"));
	    bean.setPc_desc(rs.getString("pc_desc"));
	    bean.setPc_type(rs.getString("pc_type"));
	    bean.setPc_remark(rs.getString("pc_remark"));
	    bean.setZyz(rs.getString("zyz"));
	    bean.setZyz_desc(rs.getString("zyz_desc"));
	    bean.setYxdm(rs.getString("yxdm"));
	    bean.setYxmc(rs.getString("yxmc"));
	    bean.setYxbz(rs.getString("yxbz"));
	    bean.setYxmc_org(rs.getString("yxmc_org"));
	    bean.setZydm(rs.getString("zydm"));
	    bean.setZymc(rs.getString("zymc"));
	    bean.setZybz(rs.getString("zybz"));
	    bean.setZymc_org(rs.getString("zymc_org"));
	    bean.setZyml(rs.getString("zyml"));
	    bean.setJhs(rs.getInt("jhs"));
	    bean.setFee(rs.getString("fee"));
	    bean.setXz(rs.getString("xz"));
	    bean.setXk(rs.getString("xk"));
	    bean.setZx(rs.getString("zx"));
	    bean.setXk_code(rs.getString("xk_code"));
	    bean.setXk_code_org(rs.getString("xk_code_org"));
	    bean.setXk_desc(rs.getString("xk_desc"));
	    bean.setZnzy(rs.getString("znzy"));
	    bean.setZdfwc(rs.getInt("zdfwc"));
	    bean.setZnzdfwc(rs.getInt("znzdfwc"));
	    bean.setLqrs_a(rs.getInt("lqrs_a"));
	    bean.setLqrs_b(rs.getInt("lqrs_b"));
	    bean.setLqrs_c(rs.getInt("lqrs_c"));
	    bean.setJhs_a(rs.getInt("jhs_a"));
	    bean.setJhs_b(rs.getInt("jhs_b"));
	    bean.setJhs_c(rs.getInt("jhs_c"));
	    bean.setZdf_a(rs.getInt("zdf_a"));
	    bean.setZdf_b(rs.getInt("zdf_b"));
	    bean.setZdf_c(rs.getInt("zdf_c"));
	    bean.setZdfwc_a(rs.getInt("zdfwc_a"));
	    bean.setZdfwc_b(rs.getInt("zdfwc_b"));
	    bean.setZdfwc_c(rs.getInt("zdfwc_c"));
	    bean.setPjf_a(rs.getInt("pjf_a"));
	    bean.setPjf_b(rs.getInt("pjf_b"));
	    bean.setPjf_c(rs.getInt("pjf_c"));
	    bean.setPjfwc_a(rs.getInt("pjfwc_a"));
	    bean.setPjfwc_b(rs.getInt("pjfwc_b"));
	    bean.setPjfwc_c(rs.getInt("pjfwc_c"));
	    bean.setQsf_a(rs.getInt("qsf_a"));
	    bean.setQsf_b(rs.getInt("qsf_b"));
	    bean.setQsf_c(rs.getInt("qsf_c"));
	    bean.setQsf(rs.getInt("qsf"));
	    bean.setYcwc(rs.getInt("ycwc"));
	    bean.setLqpc(rs.getString("lqpc"));
	    bean.setInd_nature(rs.getString("ind_nature"));
	    bean.setInd_catg(rs.getString("ind_catg"));
	    bean.setYxsf(rs.getString("yxsf"));
	    bean.setYxcs(rs.getString("yxcs"));
	    bean.setYx_tags(rs.getString("yx_tags"));
	    bean.setYx_tags_all(rs.getString("yx_tags_all"));
	    bean.setCnt_grad(rs.getFloat("cnt_grad"));
	    bean.setIs_hz(rs.getInt("is_hz"));
	    bean.setIs_first(rs.getInt("is_first"));
	    bean.setZnzys(rs.getInt("znzys"));
	    bean.setZnzyls(rs.getInt("znzyls"));
	    bean.setZnjhs(rs.getInt("znjhs"));
	    bean.setIs_bsyx(rs.getInt("is_bsyx"));
	    bean.setIs_ybeb(rs.getInt("is_ybeb"));
	    return bean;
	}
	
	private static void closeAllConnection(Connection conn, PreparedStatement ps, ResultSet rs) {
		JHDatabaseUtils.closeAllResources(rs, ps, conn);
	}
	
}
