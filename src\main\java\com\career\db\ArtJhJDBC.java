package com.career.db;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import com.career.utils.ResultVO;
import com.career.utils.SQLLogUtils;
import com.career.utils.Tools;

public class ArtJhJDBC {
    
    static String URL = JDBCConstants.URL;
    static String USER = JDBCConstants.USER;
    static String PASSWD = JDBCConstants.PASSWD;
    
    public static int PAGE_ROW_CNT = 20;
    
    static {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
    
    /**
     * 查询艺术类招考数据
     * 
     * @param jhYear 招考年份
     * @param sfCode 省份代码
     * @param yxmc 学校名称 (模糊查询)
     * @param pc 批次
     * @param xk 文理
     * @param ysfl 统考类
     * @param zymc 招生专业
     * @param zhcjMin 综合成绩最小值
     * @param zhcjMax 综合成绩最大值
     * @param zyfsMin 专业分数最小值
     * @param zyfsMax 专业分数最大值
     * @param wcMin 位次最小值
     * @param wcMax 位次最大值
     * @param pageNumber 页码
     * @return ResultVO 包含查询结果及分页信息
     */
    public ResultVO searchArtJhData(int jhYear, String sfCode, String yxmc, String pc, String xk, 
            String ysfl, String zymc, String zhcjMin, String zhcjMax, String zyfsMin, 
            String zyfsMax, String wcMin, String wcMax, int pageNumber) {
        
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        ResultVO resultVO = new ResultVO();
        
        List<ArtJhBean> artJhList = new ArrayList<>();
        
        try {
            conn = DatabaseUtils.getConnection();
            
            StringBuilder whereCondition = new StringBuilder();
            List<Object> params = new ArrayList<>();
            
            // 构建动态查询条件
            if (!Tools.isEmpty(yxmc)) {
                whereCondition.append(" AND yxmc LIKE ?");
                params.add("%" + yxmc + "%");
            }
            
            if (!Tools.isEmpty(pc)) {
                whereCondition.append(" AND pc = ?");
                params.add(pc);
            }
            
            if (!Tools.isEmpty(xk)) {
                whereCondition.append(" AND xk = ?");
                params.add(xk);
            }
            
            if (!Tools.isEmpty(ysfl)) {
                whereCondition.append(" AND ysfl = ?");
                params.add(ysfl);
            }
            
            if (!Tools.isEmpty(zymc)) {
                whereCondition.append(" AND zymc = ?");
                params.add(zymc);
            }
            
            // 综合成绩区间
            if (!Tools.isEmpty(zhcjMin)) {
                whereCondition.append(" AND zhcj >= ?");
                params.add(Double.parseDouble(zhcjMin));
            }
            
            if (!Tools.isEmpty(zhcjMax)) {
                whereCondition.append(" AND zhcj <= ?");
                params.add(Double.parseDouble(zhcjMax));
            }
            
            // 专业分数区间
            if (!Tools.isEmpty(zyfsMin)) {
                whereCondition.append(" AND zyfs >= ?");
                params.add(Double.parseDouble(zyfsMin));
            }
            
            if (!Tools.isEmpty(zyfsMax)) {
                whereCondition.append(" AND zyfs <= ?");
                params.add(Double.parseDouble(zyfsMax));
            }
            
            // 位次区间
            if (!Tools.isEmpty(wcMin)) {
                whereCondition.append(" AND wc >= ?");
                params.add(Integer.parseInt(wcMin));
            }
            
            if (!Tools.isEmpty(wcMax)) {
                whereCondition.append(" AND wc <= ?");
                params.add(Integer.parseInt(wcMax));
            }
            
            String tableName = sfCode + "_art_jh_" + jhYear;
            
            String SUM_CONDITION = "SELECT count(*) AS cnt ";
            String SELECT_CONDITION = "SELECT * ";
            String FROM_CONDITION = " FROM " + tableName + " WHERE 1=1" + whereCondition.toString();
            String ORDER_CONDITION = " ORDER BY zhcj DESC, zyfs DESC, wc ASC LIMIT ?,? ";
            
            Tools.println(SELECT_CONDITION + FROM_CONDITION + ORDER_CONDITION);
            ps = conn.prepareStatement(SELECT_CONDITION + FROM_CONDITION + ORDER_CONDITION);
            
            // 设置查询参数
            int paramIndex = 1;
            for (Object param : params) {
                ps.setObject(paramIndex++, param);
            }
            ps.setInt(paramIndex++, (pageNumber - 1) * PAGE_ROW_CNT);
            ps.setInt(paramIndex, PAGE_ROW_CNT);

            SQLLogUtils.printSQL(" ===searchArtJhData : ", ps);
            
            rs = ps.executeQuery();
            
            while (rs.next()) {
                artJhList.add(populateArtJhBeanFromResultSet(rs));
            }
            
            resultVO.setResult(artJhList);
            
            ps.close();
            ps = null;
            rs.close();
            rs = null;
            
            // 查询总记录数
            ps = conn.prepareStatement(SUM_CONDITION + FROM_CONDITION);
            paramIndex = 1;
            for (Object param : params) {
                ps.setObject(paramIndex++, param);
            }
            rs = ps.executeQuery();
            
            SQLLogUtils.printSQL(" ===searchArtJhData: ", ps);
            
            while (rs.next()) {
                resultVO.setRecordCnt(rs.getInt("cnt"));
            }
            
            resultVO.setCurrentPage(pageNumber);
            
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        return resultVO;
    }
    
    /**
     * 获取指定字段的去重值列表 (用于下拉框数据源)
     * 
     * @param jhYear 招考年份
     * @param sfCode 省份代码
     * @param fieldName 字段名
     * @return 去重后的值列表
     */
    public List<String> getDistinctValues(int jhYear, String sfCode, String fieldName) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<String> valueList = new ArrayList<>();
        
        try {
            conn = DatabaseUtils.getConnection();
            
            String tableName = sfCode + "_art_jh_" + jhYear;
            String sql = "SELECT DISTINCT " + fieldName + " FROM " + tableName + 
                        " WHERE " + fieldName + " IS NOT NULL AND " + fieldName + " != '' " +
                        " ORDER BY " + fieldName;
            
            Tools.println(sql);
            ps = conn.prepareStatement(sql);
            
            SQLLogUtils.printSQL(" ===getDistinctValues : ", ps);
            
            rs = ps.executeQuery();
            
            while (rs.next()) {
                String value = rs.getString(fieldName);
                if (!Tools.isEmpty(value)) {
                    valueList.add(value);
                }
            }
            
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        return valueList;
    }
    
    /**
     * 从ResultSet中填充ArtJhBean对象
     *
     * @param rs ResultSet对象
     * @return 填充好的ArtJhBean对象
     * @throws SQLException 如果从ResultSet中读取数据出错
     */
    private ArtJhBean populateArtJhBeanFromResultSet(ResultSet rs) throws SQLException {
        ArtJhBean bean = new ArtJhBean();
        
        bean.setYxdm(rs.getInt("yxdm"));
        bean.setYxmc(rs.getString("yxmc"));
        bean.setPc(rs.getString("pc"));
        bean.setXk(rs.getString("xk"));
        bean.setYsfl(rs.getString("ysfl"));
        bean.setZymc(rs.getString("zymc"));
        bean.setZydm(rs.getString("zydm"));
        bean.setZkfx(rs.getString("zkfx"));
        bean.setZkfx_ext(rs.getString("zkfx_ext"));
        bean.setBhzy(rs.getString("bhzy"));
        bean.setXz(rs.getInt("xz"));
        bean.setXf(rs.getString("xf"));
        bean.setJhs(rs.getInt("jhs"));
        bean.setZybz(rs.getString("zybz"));
        bean.setZhcj(rs.getDouble("zhcj"));
        bean.setZyfs(rs.getDouble("zyfs"));
        bean.setWc(rs.getInt("wc"));
        bean.setZgbm(rs.getString("zgbm"));
        bean.setYxcs(rs.getString("yxcs"));
        bean.setCsbq(rs.getString("csbq"));
        bean.setPc_code(rs.getString("pc_code"));
        bean.setInd_catg(rs.getString("ind_catg"));
        bean.setInd_nature(rs.getString("ind_nature"));
        bean.setGxcc(rs.getString("gxcc"));
        bean.setBc(rs.getString("bc"));
        bean.setBjzyjh(rs.getString("bjzyjh"));
        bean.setXygm(rs.getString("xygm"));
        bean.setGx_zzy(rs.getString("gx_zzy"));
        bean.setGx_ssd(rs.getInt("gx_ssd"));
        bean.setGx_sszy(rs.getString("gx_sszy"));
        bean.setGx_bsd(rs.getInt("gx_bsd"));
        bean.setGx_bszy(rs.getString("gx_bszy"));
        bean.setGx_byl2023(rs.getDouble("gx_byl2023"));
        bean.setGx_pm(rs.getString("gx_pm"));
        
        return bean;
    }
    
    /**
     * 查询艺术类招考数据总数（用于导出前的数量检查）
     */
    public ResultVO searchArtJhDataCount(int jhYear, String sfCode, String yxmc, String pc, String xk, 
            String ysfl, String zymc, String zhcjMin, String zhcjMax, String zyfsMin, 
            String zyfsMax, String wcMin, String wcMax) {
        
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        ResultVO resultVO = new ResultVO();
        
        try {
            conn = DatabaseUtils.getConnection();
            
            StringBuilder whereCondition = new StringBuilder();
            List<Object> params = new ArrayList<>();
            
            // 构建动态查询条件
            if (!Tools.isEmpty(yxmc)) {
                whereCondition.append(" AND yxmc LIKE ?");
                params.add("%" + yxmc + "%");
            }
            
            if (!Tools.isEmpty(pc)) {
                whereCondition.append(" AND pc = ?");
                params.add(pc);
            }
            
            if (!Tools.isEmpty(xk)) {
                whereCondition.append(" AND xk = ?");
                params.add(xk);
            }
            
            if (!Tools.isEmpty(ysfl)) {
                whereCondition.append(" AND ysfl = ?");
                params.add(ysfl);
            }
            
            if (!Tools.isEmpty(zymc)) {
                whereCondition.append(" AND zymc = ?");
                params.add(zymc);
            }
            
            // 综合成绩区间
            if (!Tools.isEmpty(zhcjMin)) {
                whereCondition.append(" AND zhcj >= ?");
                params.add(Double.parseDouble(zhcjMin));
            }
            
            if (!Tools.isEmpty(zhcjMax)) {
                whereCondition.append(" AND zhcj <= ?");
                params.add(Double.parseDouble(zhcjMax));
            }
            
            // 专业分数区间
            if (!Tools.isEmpty(zyfsMin)) {
                whereCondition.append(" AND zyfs >= ?");
                params.add(Double.parseDouble(zyfsMin));
            }
            
            if (!Tools.isEmpty(zyfsMax)) {
                whereCondition.append(" AND zyfs <= ?");
                params.add(Double.parseDouble(zyfsMax));
            }
            
            // 位次区间
            if (!Tools.isEmpty(wcMin)) {
                whereCondition.append(" AND wc >= ?");
                params.add(Integer.parseInt(wcMin));
            }
            
            if (!Tools.isEmpty(wcMax)) {
                whereCondition.append(" AND wc <= ?");
                params.add(Integer.parseInt(wcMax));
            }
            
            String tableName = sfCode + "_art_jh_" + jhYear;
            String countSql = "SELECT COUNT(*) FROM " + tableName + " WHERE 1=1" + whereCondition.toString();
            
            ps = conn.prepareStatement(countSql);
            
            // 设置查询参数
            int paramIndex = 1;
            for (Object param : params) {
                ps.setObject(paramIndex++, param);
            }

            rs = ps.executeQuery();
            
            if (rs.next()) {
                resultVO.setRecordCnt(rs.getInt(1));
            }
            
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        return resultVO;
    }
    
    /**
     * 查询艺术类招考数据用于导出（不分页）
     */
    public ResultVO searchArtJhDataForExport(int jhYear, String sfCode, String yxmc, String pc, String xk, 
            String ysfl, String zymc, String zhcjMin, String zhcjMax, String zyfsMin, 
            String zyfsMax, String wcMin, String wcMax, int maxRecords) {
        
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        ResultVO resultVO = new ResultVO();
        
        List<ArtJhBean> artJhList = new ArrayList<>();
        
        try {
            conn = DatabaseUtils.getConnection();
            
            StringBuilder whereCondition = new StringBuilder();
            List<Object> params = new ArrayList<>();
            
            // 构建动态查询条件
            if (!Tools.isEmpty(yxmc)) {
                whereCondition.append(" AND yxmc LIKE ?");
                params.add("%" + yxmc + "%");
            }
            
            if (!Tools.isEmpty(pc)) {
                whereCondition.append(" AND pc = ?");
                params.add(pc);
            }
            
            if (!Tools.isEmpty(xk)) {
                whereCondition.append(" AND xk = ?");
                params.add(xk);
            }
            
            if (!Tools.isEmpty(ysfl)) {
                whereCondition.append(" AND ysfl = ?");
                params.add(ysfl);
            }
            
            if (!Tools.isEmpty(zymc)) {
                whereCondition.append(" AND zymc = ?");
                params.add(zymc);
            }
            
            // 综合成绩区间
            if (!Tools.isEmpty(zhcjMin)) {
                whereCondition.append(" AND zhcj >= ?");
                params.add(Double.parseDouble(zhcjMin));
            }
            
            if (!Tools.isEmpty(zhcjMax)) {
                whereCondition.append(" AND zhcj <= ?");
                params.add(Double.parseDouble(zhcjMax));
            }
            
            // 专业分数区间
            if (!Tools.isEmpty(zyfsMin)) {
                whereCondition.append(" AND zyfs >= ?");
                params.add(Double.parseDouble(zyfsMin));
            }
            
            if (!Tools.isEmpty(zyfsMax)) {
                whereCondition.append(" AND zyfs <= ?");
                params.add(Double.parseDouble(zyfsMax));
            }
            
            // 位次区间
            if (!Tools.isEmpty(wcMin)) {
                whereCondition.append(" AND wc >= ?");
                params.add(Integer.parseInt(wcMin));
            }
            
            if (!Tools.isEmpty(wcMax)) {
                whereCondition.append(" AND wc <= ?");
                params.add(Integer.parseInt(wcMax));
            }
            
            String tableName = sfCode + "_art_jh_" + jhYear;
            
            String SELECT_CONDITION = "SELECT * ";
            String FROM_CONDITION = " FROM " + tableName + " WHERE 1=1" + whereCondition.toString();
            String ORDER_CONDITION = " ORDER BY zhcj DESC, zyfs DESC, wc ASC LIMIT " + maxRecords;
            
            Tools.println(SELECT_CONDITION + FROM_CONDITION + ORDER_CONDITION);
            ps = conn.prepareStatement(SELECT_CONDITION + FROM_CONDITION + ORDER_CONDITION);
            
            // 设置查询参数
            int paramIndex = 1;
            for (Object param : params) {
                ps.setObject(paramIndex++, param);
            }

            SQLLogUtils.printSQL(" ===searchArtJhDataForExport : ", ps);
            
            rs = ps.executeQuery();
            
            while (rs.next()) {
                artJhList.add(populateArtJhBeanFromResultSet(rs));
            }
            
            resultVO.setResult(artJhList);
            resultVO.setRecordCnt(artJhList.size());
            
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        return resultVO;
    }

    /**
     * 关闭数据库连接
     */
    private static void closeAllConnection(Connection conn, PreparedStatement ps, ResultSet rs) {
        /**
    	try {
            if (rs != null)
                rs.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        try {
            if (ps != null)
                ps.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        try {
            if (conn != null)
                conn.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        rs = null;
        ps = null;
        conn = null;
        */
    	DatabaseUtils.closeAllResources(rs, ps, conn);
    }
} 