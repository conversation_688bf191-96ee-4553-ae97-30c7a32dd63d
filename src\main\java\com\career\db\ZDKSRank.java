package com.career.db;

public class ZDKSRank {
	
	private String sf;
	private String kl;
	private String fd;
	private int nf;
	private int score;
	private int cnt;
	private int wc;
	
	private int ext_max_rank;
	private int ext_min_rank;
	
	public int getExt_max_rank() {
		return ext_max_rank;
	}
	public void setExt_max_rank(int ext_max_rank) {
		this.ext_max_rank = ext_max_rank;
	}
	public int getExt_min_rank() {
		return ext_min_rank;
	}
	public void setExt_min_rank(int ext_min_rank) {
		this.ext_min_rank = ext_min_rank;
	}
	public String getSf() {
		return sf;
	}
	public void setSf(String sf) {
		this.sf = sf;
	}
	public String getKl() {
		return kl;
	}
	public void setKl(String kl) {
		this.kl = kl;
	}
	public String getFd() {
		return fd;
	}
	public void setFd(String fd) {
		this.fd = fd;
	}
	public int getNf() {
		return nf;
	}
	public void setNf(int nf) {
		this.nf = nf;
	}
	public int getScore() {
		return score;
	}
	public void setScore(int score) {
		this.score = score;
	}
	public int getCnt() {
		return cnt;
	}
	public void setCnt(int cnt) {
		this.cnt = cnt;
	}
	public int getWc() {
		return wc;
	}
	public void setWc(int wc) {
		this.wc = wc;
	}

}