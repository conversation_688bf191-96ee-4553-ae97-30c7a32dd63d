package com.career.utils;

import java.util.HashMap;

public class SalesCache {
	
	// 性别
    public static final int SALES_DB_CUSTOMER_INDICATOR_GENDER_UNKNOWN = 0;
    public static final int SALES_DB_CUSTOMER_INDICATOR_GENDER_MAN = 1;
    public static final int SALES_DB_CUSTOMER_INDICATOR_GENDER_WOMAN = 2;

    // 外部联系人类型
    public static final int SALES_DB_CUSTOMER_INDICATOR_WECOM_TYPE_WECHAT = 1;
    public static final int SALES_DB_CUSTOMER_INDICATOR_WECOM_TYPE_WECOM = 2;

    // 客户角色
    public static final int SALES_DB_CUSTOMER_INDICATOR_ROLE_MOTHER = 1;
    public static final int SALES_DB_CUSTOMER_INDICATOR_ROLE_FATHER = 2;
    public static final int SALES_DB_CUSTOMER_INDICATOR_ROLE_CHILD = 3;
    public static final int SALES_DB_CUSTOMER_INDICATOR_ROLE_OTHER = 4;

    // 客户是否是老板
    public static final int SALES_DB_CUSTOMER_INDICATOR_BOSS_IND_YES = 1;
    public static final int SALES_DB_CUSTOMER_INDICATOR_BOSS_IND_NO = 2;

    // 客户最高学历
    public static final int SALES_DB_CUSTOMER_INDICATOR_EDUCATION_ASSOCIATE_OR_BELOW = 1;
    public static final int SALES_DB_CUSTOMER_INDICATOR_EDUCATION_BACHELOR = 2;
    public static final int SALES_DB_CUSTOMER_INDICATOR_EDUCATION_MASTER = 3;
    public static final int SALES_DB_CUSTOMER_INDICATOR_EDUCATION_PHD_OR_ABOVE = 4;

    // 客户工作年限
    public static final int SALES_DB_CUSTOMER_INDICATOR_WORK_YEAR_NOT_WORK = 1;
    public static final int SALES_DB_CUSTOMER_INDICATOR_WORK_YEAR_1_TO_2 = 2;
    public static final int SALES_DB_CUSTOMER_INDICATOR_WORK_YEAR_3_TO_5 = 3;
    public static final int SALES_DB_CUSTOMER_INDICATOR_WORK_YEAR_5_ABOVE = 4;

    // 客户类型
    public static final int SALES_DB_CUSTOMER_INDICATOR_TYPE_JUNIOR_HIGH = 1;
    public static final int SALES_DB_CUSTOMER_INDICATOR_TYPE_SENIOR_HIGH = 2;
    public static final int SALES_DB_CUSTOMER_INDICATOR_TYPE_COLLEGE = 3;
    public static final int SALES_DB_CUSTOMER_INDICATOR_TYPE_GRADUATE = 4;
    public static final int SALES_DB_CUSTOMER_INDICATOR_TYPE_WORKING = 5;

    // 状态
    public static final int SALES_DB_CUSTOMER_INDICATOR_STATUS_ACTIVE = 1;
    public static final int SALES_DB_CUSTOMER_INDICATOR_STATUS_DELETED = 2;

    // 购买意向
    public static final int SALES_DB_CUSTOMER_INDICATOR_PURCHASE_INTENTION = 1;

    // 标注状态
    public static final int SALES_DB_CUSTOMER_INDICATOR_NEW_INDICATOR_PENDING = 1;
    public static final int SALES_DB_CUSTOMER_INDICATOR_NEW_INDICATOR_MARKED = 2;
    
    
    public static final HashMap<Integer, String> CUSTOMER_ROLES = new HashMap<>();
    static {
        CUSTOMER_ROLES.put(1, "母亲");
        CUSTOMER_ROLES.put(2, "父亲");
        CUSTOMER_ROLES.put(3, "孩子");
        CUSTOMER_ROLES.put(4, "其他");
    }

    // 客户性别
    public static final HashMap<Integer, String> CUSTOMER_GENDER = new HashMap<>();
    static {
        CUSTOMER_GENDER.put(0, "未知");
        CUSTOMER_GENDER.put(1, "男性");
        CUSTOMER_GENDER.put(2, "女性");
    }

    // 客户是否是老板
    public static final HashMap<Integer, String> CUSTOMER_BOSS_IND = new HashMap<>();
    static {
        CUSTOMER_BOSS_IND.put(1, "是");
        CUSTOMER_BOSS_IND.put(2, "不是");
    }

    // 客户最高学历
    public static final HashMap<Integer, String> CUSTOMER_HIGHEST_EDUCATION = new HashMap<>();
    static {
        CUSTOMER_HIGHEST_EDUCATION.put(1, "专科及以下");
        CUSTOMER_HIGHEST_EDUCATION.put(2, "本科");
        CUSTOMER_HIGHEST_EDUCATION.put(3, "硕士");
        CUSTOMER_HIGHEST_EDUCATION.put(4, "博士及以上");
    }

    // 客户工作年限
    public static final HashMap<Integer, String> CUSTOMER_WORK_YEAR = new HashMap<>();
    static {
        CUSTOMER_WORK_YEAR.put(1, "工作");
        CUSTOMER_WORK_YEAR.put(2, "1-2年");
        CUSTOMER_WORK_YEAR.put(3, "3-5年");
        CUSTOMER_WORK_YEAR.put(4, "8年以上");
    }

    // 客户类型
    public static final HashMap<Integer, String> CUSTOMER_TYPE = new HashMap<>();
    static {
        CUSTOMER_TYPE.put(1, "初中生");
        CUSTOMER_TYPE.put(2, "高中生");
        CUSTOMER_TYPE.put(3, "大学生");
        CUSTOMER_TYPE.put(4, "研究生");
        CUSTOMER_TYPE.put(5, "在职人士");
    }

    // 状态
    public static final HashMap<Integer, String> STATUS = new HashMap<>();
    static {
        STATUS.put(1, "有效");
        STATUS.put(2, "删除");
    }

    // 购买意向
    public static final HashMap<Integer, String> PURCHASE_INTENTION = new HashMap<>();
    static {
        PURCHASE_INTENTION.put(1, "低");
        PURCHASE_INTENTION.put(2, "中");
        PURCHASE_INTENTION.put(3, "高");
    }

    // 新标注
    public static final HashMap<Integer, String> NEW_INDICATOR = new HashMap<>();
    static {
        NEW_INDICATOR.put(1, "未标注");
        NEW_INDICATOR.put(2, "已标注");
    }

}
