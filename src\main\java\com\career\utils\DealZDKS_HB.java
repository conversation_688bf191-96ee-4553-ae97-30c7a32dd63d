package com.career.utils;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class DealZDKS_HB {

	public static void main(String args[]) throws Exception {
		
		run2BENWK();
		
		run2BEN();
	}
	
	public static void runSC(String city, String zdpc, String KL, int erBen, int yiBen, int ZGF) {
		
	}
	
	
	static HashMap<String, String> xk_code_map = new HashMap<>();
	static {
		xk_code_map.put("物理", "14387D");
		xk_code_map.put("理科", "14387D");
		xk_code_map.put("历史", "WLRKQG");
		xk_code_map.put("文科", "WLRKQG");
		xk_code_map.put("综合", "123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");
		
	}

	public static void run2BEN() { //专科到2本
		
		
		List<String> list = runConvertLatest("湖北", "武汉","十一校联考第一次", "物理", 180, 480, 180, 536, "14387D");
		StringBuffer sb = new StringBuffer();
		for(String str : list) {
			sb.append(str);
		}
		
		list = runConvertLatest("湖北", "武汉","十一校联考第一次", "物理", 480, 544, 536, 598, "14387D");
		for(String str : list) {
			sb.append(str);
		}
		
		
		list = runConvertLatest("湖北", "武汉","十一校联考第一次", "物理", 544, 604, 598, 645, "14387D");
		for(String str : list) {
			sb.append(str);
		}
		
		
		list = runConvertLatest("湖北", "武汉","十一校联考第一次", "物理", 604, 670, 645, 691, "14387D");
		for(String str : list) {
			sb.append(str);
		}
		
		list = runConvertLatest("湖北", "武汉","十一校联考第一次", "物理", 670, 690, 691, 695, "14387D");
		for(String str : list) {
			sb.append(str);
		}
		
		writeTempFile(new File("E://HUBEU_WULI_1.txt"), sb);
	}
	
	
	

	
	public static void run2BENWK() { //专科到2本
		List<String> list = runConvertLatest("湖北", "武汉","十一校联考第一次", "历史", 180, 442, 180, 532, "WLRKQG");
		StringBuffer sb = new StringBuffer();
		for(String str : list) {
			sb.append(str);
		}
		
		list = runConvertLatest("湖北", "武汉","十一校联考第一次", "历史", 442, 536, 532, 581, "WLRKQG");
		for(String str : list) {
			sb.append(str);
		}
		
		
		list = runConvertLatest("湖北", "武汉","十一校联考第一次", "历史", 536, 580, 581, 611, "WLRKQG");
		for(String str : list) {
			sb.append(str);
		}
		
		
		list = runConvertLatest("湖北", "武汉","十一校联考第一次", "历史", 580, 651, 611, 661, "WLRKQG");
		for(String str : list) {
			sb.append(str);
		}
		
		list = runConvertLatest("湖北", "武汉","十一校联考第一次", "历史", 651, 660, 661, 666, "WLRKQG");
		for(String str : list) {
			sb.append(str);
		}
		
		writeTempFile(new File("E://HUBEU_lishi_1.txt"), sb);
	}
	
	
	
	public static List<String> runConvertLatest(String sf, String city, String zdpc, String kl, int scoreZDFrom, int scoreZDTo, int gaokao23Start, int gaokao23To, String klCode) {
		List<String> list = new ArrayList<>();

		double newStart = gaokao23Start;
		for(int i = scoreZDFrom;i < scoreZDTo;i++) {
			newStart = Arith.add(newStart, Arith.div(gaokao23To - gaokao23Start, scoreZDTo - scoreZDFrom)); 
			if(newStart > gaokao23To) {
				newStart = gaokao23To;
			}
			list.add("INSERT INTO h5_hb_zdks_score_convert(nf, sf, city, ZDPC, KL, SCORE_FROM, SCORE_TO, KL_CODE) VALUES(2024, '"+sf+"', '"+city+"', '"+zdpc+"', '"+kl+"', "+i+","+Math.round(newStart)+", '"+klCode+"' );\r\n");
			
		}
		
		return list;
		
	}

	
	public static List<String> runConvert(String sf, String city, String zdpc, String kl, int pc, int scoreFrom, int scoreTo) {
		List<String> list = new ArrayList<>();
		
		int highest2023 = 0;
		int YIBen2023 = 0;
		int ERBen2023 = 0;
		int ZHUANKe2023 = 0;

		
		if(kl.equals("文科")) {
			highest2023 = 636;
			YIBen2023 = 527;
			ERBen2023 = 458;
			ZHUANKe2023 = 150;
		}else if(kl.equals("理科")) {
			highest2023 = 698;
			YIBen2023 = 520;
			ERBen2023 = 433;
			ZHUANKe2023 = 150;
		}else {
			
		}
		
		if(pc == 1) {
			double score2023Start = YIBen2023;
			for(int i = scoreFrom;i < scoreTo;i++) {
				score2023Start = Arith.add(score2023Start, Arith.div(highest2023 - YIBen2023, scoreTo - scoreFrom)); 
				if(score2023Start > highest2023) {
					score2023Start = highest2023;
				}
				list.add("INSERT INTO zdks_score_convert(nf, sf, city, ZDPC, KL, SCORE_FROM, SCORE_TO) VALUES(2024, '"+city+"', '"+city+"', '"+zdpc+"', '"+kl+"', "+i+","+Math.round(score2023Start)+" );\r\n");
				
			}
		}else if(pc == 2) {
			double score2023Start = ERBen2023;
			for(int i = scoreFrom;i < scoreTo;i++) {
				score2023Start = Arith.add(score2023Start, Arith.div(YIBen2023 - ERBen2023, scoreTo - scoreFrom));
				if(score2023Start > YIBen2023) {
					score2023Start = YIBen2023;
				}
				list.add("INSERT INTO zdks_score_convert(nf, sf, city, ZDPC, KL, SCORE_FROM, SCORE_TO) VALUES(2024, '"+city+"', '"+city+"', '"+zdpc+"', '"+kl+"', "+i+","+Math.round(score2023Start)+" );\r\n");
			}
		}else if(pc == 3) { //专科
			double score2023Start = ZHUANKe2023;
			for(int i = scoreFrom;i < scoreTo;i++) {
				score2023Start = Arith.add(score2023Start, Arith.div(ERBen2023 - ZHUANKe2023, scoreTo - scoreFrom));
				if(score2023Start > ERBen2023) {
					score2023Start = ERBen2023;
				}
				list.add("INSERT INTO zdks_score_convert(nf, sf, city, ZDPC, KL, SCORE_FROM, SCORE_TO) VALUES(2024, '"+city+"', '"+city+"', '"+zdpc+"', '"+kl+"', "+i+","+Math.round(score2023Start)+" );\r\n");
			}
		}
		return list;
		
	}
	
	
	public static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
}
