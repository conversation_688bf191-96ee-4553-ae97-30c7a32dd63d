package com.career.utils;

public class AliCloundVideoBean {
	
	private String vid;
	private String PlayAuth;
	private String VideoMeta_Title;
	private float VideoMeta_Duration;
	private String VideoMeta_CoverURL;
	
	
	public String getVid() {
		return vid;
	}
	public void setVid(String vid) {
		this.vid = vid;
	}
	public String getPlayAuth() {
		return PlayAuth;
	}
	public void setPlayAuth(String playAuth) {
		PlayAuth = playAuth;
	}
	public String getVideoMeta_Title() {
		return VideoMeta_Title;
	}
	public void setVideoMeta_Title(String videoMeta_Title) {
		VideoMeta_Title = videoMeta_Title;
	}
	public float getVideoMeta_Duration() {
		return VideoMeta_Duration;
	}
	public void setVideoMeta_Duration(float videoMeta_Duration) {
		VideoMeta_Duration = videoMeta_Duration;
	}
	public String getVideoMeta_CoverURL() {
		return VideoMeta_CoverURL;
	}
	public void setVideoMeta_CoverURL(String videoMeta_CoverURL) {
		VideoMeta_CoverURL = videoMeta_CoverURL;
	}

}
