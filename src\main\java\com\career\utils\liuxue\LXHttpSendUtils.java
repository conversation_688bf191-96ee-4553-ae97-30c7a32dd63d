package com.career.utils.liuxue;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.cookie.Cookie;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import com.career.db.OffcnExtUrlBean;
import com.career.db.SpiderInfoBean;
import com.career.db.SpiderJDBC;
import com.career.utils.HttpSendUtils;
import com.career.utils.Tools;

public class LXHttpSendUtils {

	public static String post(String url, Map<String, String> params, Map<String, String> headers) {
		CloseableHttpClient client = HttpClients.createDefault();
		HttpPost httpPost = new HttpPost(url);
		UrlEncodedFormEntity entity;
		List<NameValuePair> paramPairs = new ArrayList<NameValuePair>();
		if (params != null) {
			for (Map.Entry<String, String> en : params.entrySet()) {
				paramPairs.add(new BasicNameValuePair(en.getKey(), en.getValue()));
			}
		}
		if (headers != null) {
			for (Map.Entry<String, String> en : headers.entrySet()) {
				httpPost.setHeader(en.getKey(), en.getValue());
			}
		}
		try {
			entity = new UrlEncodedFormEntity(paramPairs, "UTF-8");
			httpPost.setEntity(entity);
			HttpResponse resp = client.execute(httpPost);
			HttpEntity respEntity = resp.getEntity();
			if (null != respEntity) {
				return EntityUtils.toString(respEntity, "UTF-8");
			}

		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		} catch (ClientProtocolException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			try {
				client.close();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return null;
	}

	public static String get(String url, Map<String, String> headers) {
		CloseableHttpClient client = HttpClients.createDefault();
		HttpGet httpGet = new HttpGet(url);
		if (headers != null) {
			for (Map.Entry<String, String> en : headers.entrySet()) {
				httpGet.setHeader(en.getKey(), en.getValue());
			}
		}
		try {
			HttpResponse resp = client.execute(httpGet);
			HttpEntity respEntity = resp.getEntity();
			if (null != respEntity) {
				return EntityUtils.toString(respEntity, "UTF-8");
			}
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		} catch (ClientProtocolException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			try {
				client.close();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return null;
	}
	
	public Cookie Cookie(){
        String url = "http://L.xueya63.com";
        BasicCookieStore cookieStore = new BasicCookieStore();
        Cookie cookie = null;  
        try {
	        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
	        HttpPost post = new HttpPost(url);
	        List<NameValuePair> list = new ArrayList<>();
	        list.add(new BasicNameValuePair("",""));
	 
	        post.addHeader("content-Ttpe","application/json; charset=UTF-8");
	        post.setEntity(new UrlEncodedFormEntity(list,"UTF-8"));
	        CloseableHttpResponse httpResponse = httpClient.execute(post);
	        List<Cookie> cookies = cookieStore.getCookies();
	        cookie =cookies.get(0);
	        httpClient.close();
        }catch(Exception ex) {
        	ex.printStackTrace();
        }
        return  cookie;
    }
	
	public static byte[] readInputStream(InputStream inStream) throws Exception{   
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();   
        byte[] buffer = new byte[1024];   
        int len = 0;   
        while( (len=inStream.read(buffer)) != -1 ){   
            outStream.write(buffer, 0, len);   
        }   
        inStream.close();   
        return outStream.toByteArray();   
    }   
	
	public static byte[] getImageFromNetByUrl(String strUrl){   
        try {   
            URL url = new URL(strUrl);   
            HttpURLConnection conn = (HttpURLConnection)url.openConnection();   
            conn.setRequestMethod("GET");   
            conn.setConnectTimeout(5 * 1000);   
            InputStream inStream = conn.getInputStream();//通过输入流获取图片数据   
            byte[] btImg = readInputStream(inStream);//得到图片的二进制数据   
            return btImg;   
        } catch (Exception e) {   
            e.printStackTrace();   
        }   
        return null;   
    }   
	
	public static void writeImageToDisk(byte[] img, String fileName){
		try {
			File file = new File("C:\\yx_logo\\" + fileName);
			FileOutputStream fops = new FileOutputStream(file);
			fops.write(img);
			fops.flush();
			fops.close();
			System.out.println("图片已经写入到C盘");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	
	public static void new_country_03(String cname) {
		String url = "http://qt.liuxueqiao.net/images/country_all/"+cname+"/new_country_03.jpg";
		byte[] btImg = getImageFromNetByUrl(url);
		if(null != btImg && btImg.length > 0){
			System.out.println("读取到：" + btImg.length + " 字节");
			String fileName = "new_country_03.jpg";
			writeImageToDisk(btImg, fileName);
		}else{
			System.out.println("没有从该连接获得内容");
		}

	}
	
	public static void new_country_42(String cname) {
		String url = "http://qt.liuxueqiao.net/images/country_all/"+cname+"/new_country_42.jpg";
		byte[] btImg = getImageFromNetByUrl(url);
		if(null != btImg && btImg.length > 0){
			System.out.println("读取到：" + btImg.length + " 字节");
			String fileName = "new_country_42.jpg";
			writeImageToDisk(btImg, fileName);
		}else{
			System.out.println("没有从该连接获得内容");
		}

	}
	
	public static void new_country_89(String cname) {
		String url = "http://qt.liuxueqiao.net/images/country_all/"+cname+"/new_country_89.jpg";
		byte[] btImg = getImageFromNetByUrl(url);
		if(null != btImg && btImg.length > 0){
			System.out.println("读取到：" + btImg.length + " 字节");
			String fileName = "new_country_89.jpg";
			writeImageToDisk(btImg, fileName);
		}else{
			System.out.println("没有从该连接获得内容");
		}

	}
	
	public static void main(String[] args) {
		new_country_03("xinxilan");
		new_country_42("xinxilan");
		new_country_89("xinxilan");
	}
	
	public static void runPathWayCom(String URL) {
		//https://tapi.cnjzy.net/api/Public/LineRankList
		Map<String, String> headers = new HashMap<>();
		Map<String, String> params = new HashMap<>();
		
		String[] ua_list = new String[] {
		           "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
		};
		
		String schoolID = "x";
		if(URL.indexOf("colleges?ID=") != -1) {
			schoolID = URL.substring(URL.indexOf("colleges?ID=") + "colleges?ID=".length());
			headers.put("Cookie", "cf_clearance=n5hhVaQKYj7Ws64tyHnEOcXdxI04pqdiyfE97yMijbE-1708245914-1.0-AQeiXTVGP/OnBQPzjsiy/wUr6ruDBi/GeaJiAQmgtyB1PwT83W0G9M5m1Rqlh4PFctduod2G7ctQKGk55z5xtZA=");
			headers.put("Upgrade-Insecure-Requests", "1");
			headers.put("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 7_1_2 like Mac OS X) App leWebKit/537.51.2 (KHTML, like Gecko) Version/7.0 Mobile/11D257 Safari/9537.53");
			headers.put("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
			headers.put(":authority", "www.forwardpathway.us");
			headers.put(":scheme", "https");
			headers.put(":scheme", "https");
			headers.put("Referrer Policy", "strict-origin-when-cross-origin");
			headers.put("Referrer", "https://www.forwardpathway.com/");
			
			headers.put("Accept-Encoding", "gzip, deflate, br");
			headers.put("Accept-Language", "zh-CN,zh;q=0.9");
			headers.put("Cache-Control", "max-age=0");
			headers.put("Sec-Ch-Ua", "\"Not A(Brand\";v=\"99\", \"Google Chrome\";v=\"121\", \"Chromium\";v=\"121\"");
			headers.put("Sec-Ch-Ua-Mobile", "?0");
			headers.put("Sec-Ch-Ua-Platform", "\"Windows\"");
			headers.put("Sec-Fetch-Dest", "document");
			headers.put("Sec-Ch-Ua-Mode", "navigate");
			headers.put("Sec-Ch-Ua-Site", "none");
			headers.put("Remote Address", "127.0.0.1:7890");
			return;
			
		}else {
			
			schoolID = URL.substring(URL.indexOf("forwardpathway.com/") + "forwardpathway.com/".length());
			headers.put("Cookie", "_ga=GA1.1.536031483.1707529578; cf_clearance=rQjhoffLZzWmCZQ9JNCGJysvXDbEm4WlLTVpxgUGeDQ-1707529798-1-AQTFvoOqCFe1cWmbt6eOmETTgrTfCqkicxX4LM+qtzWOOw52LP6uAwWRAOi6p+jUr15Yf+9EKy7ZXnZAJKztnvE=; _ga_XXKYVTBWNE=GS1.1.1707529577.1.1.1707529894.60.0.0");
			headers.put("Upgrade-Insecure-Requests", "1");
			headers.put("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 7_1_2 like Mac OS X) App leWebKit/537.51.2 (KHTML, like Gecko) Version/7.0 Mobile/11D257 Safari/9537.53");
			headers.put("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
			headers.put(":authority", "www.forwardpathway.com");
			headers.put(":scheme", "https");
			
		}
		
		
		
		StringBuffer SQL = new StringBuffer();
		System.out.println(URL);
		String res = HttpSendUtils.get(URL, headers);
		SQL.append(res);
		
		
		File file = new File("E://xuhang//detail//"+(Tools.getInt(schoolID)%9) + "//");
		if(!file.exists()) {
			try {
				file.mkdirs();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		
		
		writeTempFile(new File(file, schoolID + ".txt"), SQL);
		
	}
	
	
	public static void runPathWayComGetRank(String URL) {
		//https://tapi.cnjzy.net/api/Public/LineRankList
		Map<String, String> headers = new HashMap<>();
		Map<String, String> params = new HashMap<>();
		
		String[] ua_list = new String[] {
		           "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
		};
		
		String schoolID = "x";
		if(URL.indexOf("colleges?ID=") != -1) {
			schoolID = URL.substring(URL.indexOf("colleges?ID=") + "colleges?ID=".length());
			headers.put("Host", "www.forwardpathway.com");
			headers.put("User-Agent", "Mozilla/5.0 (X11; Linux x86_64)AppleWebKit/537.36(KHTML, like Gecko)Chrome/91.0.4472.120 Safari/537.36");
			headers.put("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8");
			headers.put("Accept-Language", "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2");
			headers.put("Accept-Encoding", "gzip, deflate, br");
			headers.put("Connection", "keep-alive");
			headers.put("Upgrade-Insecure-Requests", "1");
			headers.put("Sec-Fetch-Dest", "document");
			headers.put("Sec-Fetch-Mode", "navigate");
			headers.put("Sec-Fetch-Site", "none");
			headers.put("Sec-Fetch-User", "?1");
			headers.put("Referer", "https://www.forwardpathway.com/us-college-database");
			
		}else {
			
			schoolID = URL.substring(URL.indexOf("forwardpathway.com/") + "forwardpathway.com/".length());
			headers.put("Cookie", "_ga=GA1.1.536031483.1707529578; cf_clearance=rQjhoffLZzWmCZQ9JNCGJysvXDbEm4WlLTVpxgUGeDQ-1707529798-1-AQTFvoOqCFe1cWmbt6eOmETTgrTfCqkicxX4LM+qtzWOOw52LP6uAwWRAOi6p+jUr15Yf+9EKy7ZXnZAJKztnvE=; _ga_XXKYVTBWNE=GS1.1.1707529577.1.1.1707529894.60.0.0");
			headers.put("Upgrade-Insecure-Requests", "1");
			headers.put("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 7_1_2 like Mac OS X) App leWebKit/537.51.2 (KHTML, like Gecko) Version/7.0 Mobile/11D257 Safari/9537.53");
			headers.put("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
			headers.put(":authority", "www.forwardpathway.com");
			headers.put(":scheme", "https");
			return;
		}
		
		
		
		StringBuffer SQL = new StringBuffer();
		System.out.println(URL);
		if(URL.indexOf("colleges?ID=") != -1) {
			String res = HttpSendUtils.get("https://www.forwardpathway.com/d3v7/dataphp/school_database/score10_20231213.php?name=colleges&ID=" + schoolID, headers);
			SQL.append(res);
			System.out.println(res);
		}else {
			String res = HttpSendUtils.get("https://www.forwardpathway.com/d3v7/dataphp/school_database/score10_20231213.php?name=" + schoolID, headers);
			SQL.append(res);
		}
		
		
		File file = new File("E://xuhang//rank//"+(Tools.getInt(schoolID)%9) + "//");
		if(!file.exists()) {
			try {
				file.mkdirs();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		
		
		writeTempFile(new File(file, schoolID + ".txt"), SQL);
		
	}
	
	
	public static void runXinDongFang(int schoolID) {
		//https://tapi.cnjzy.net/api/Public/LineRankList
		Map<String, String> headers = new HashMap<>();
		Map<String, String> params = new HashMap<>();


		headers.put("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
		headers.put("Accept-Encoding", "gzip, deflate, br");
		headers.put("Accept-Language", "zh-CN,zh;q=0.9");
		headers.put("Cache-Control", "no-cache");
		headers.put("Connection", "keep-alive");
		headers.put("Cookie", "visitor_type=old; visitor_type=old; qt_guid=18dbd225453-WsmpoFfjkB; qt_referer_land=https%3A%2F%2Fliuxue.xdf.cn%2F; AGL_USER_ID=7b9b9ffd-4b97-4486-a46c-20e9117fd342; Hm_lvt_7454b3dde334f8d8876851bc894bea29=1708275162; _gid=GA1.2.785512098.1708275162; 53gid2=13867195376002; 53gid0=13867195376002; 53gid1=13867195376002; 53revisit=1708275162665; 53kf_72214454_from_host=liuxue.xdf.cn; 53kf_72214454_keyword=https%3A%2F%2Fwww.baidu.com%2Flink%3Furl%3D5f6INEfRGVccFHllyeEiWB7YCzVPjsSQQlLBvX-ROZ7%26wd%3D%26eqid%3De91a943800cafc2e0000000665d235cf; uuid_53kf_72214454=fe48fda754afaefbdcd9561a67c79c32; 53kf_72214454_land_page=https%253A%252F%252Fliuxue.xdf.cn%252F; kf_72214454_land_page_ok=1; 53uvid=1; onliner_zdfq72214454=0; qt_city=chengdu%2C%E6%88%90%E9%83%BD; visitor_type=old; SECKEY_ABVK=GAmve1rtI3xoFz3DrkX+cTd+p88+MIQVGybH27PQaB0%3D; BMAP_SECKEY=2c_u53jC8dNhOwURL9EOmDgWnOQhXULWetrh3y36KCdjti67QPa9ZeY6UA39kufUTgB1iqw2AfJ5myr9NhzH0-d9tX7eT7Oj-8NxY2WGdK6redt1vuusLlXapZy_QHrq9snL188Bd9VFhb872mc7DnAKSF00vMy74GSeP_I6zo-y07b_uVQJw_nxzG8tdOyG; sajssdk_2015_cross_new_user=1; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2218dbd225453-WsmpoFfjkB%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMThkYmQyMjY3NzA1ODEtMDA5NWJjYjc3MmY4YzQxOC0yNjAwMTg1MS01MzA4NDE2LTE4ZGJkMjI2NzcxMjI1NSJ9%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%2218dbd226770581-0095bcb772f8c418-26001851-5308416-18dbd2267712255%22%7D; invite_53kf_totalnum_1=3; out_detain_num72214454_106521771=1; _ga_T5KN1FM7JQ=GS1.1.1708275162.1.1.1708277537.60.0.0; qt_search_text_content=%E6%B3%A2%E8%8E%AB%E7%BA%B3%E5%AD%A6%E9%99%A2%2C%E4%B9%8C%E6%8B%89%E5%B0%94%E8%81%94%E9%82%A6%E5%A4%A7%E5%AD%A6%2C%E7%8F%AD%E5%B0%BC%E8%8B%8F%E7%BB%B4%E5%A4%AB%E5%A4%A7%E5%AD%A6; qt_channel_source=%E5%85%8D%E8%B4%B9%E6%B8%A0%E9%81%93%2C%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F; qt_zt_xxb=22; _gat=1; Hm_lpvt_7454b3dde334f8d8876851bc894bea29=1708305470; _ga_T5KN1FM7JQ=GS1.1.1708305244.2.1.1708305469.50.0.0; _ga=GA1.1.2034928454.1708275162; _ga_829K0HT0GQ=GS1.2.1708305244.2.1.1708305470.43.0.0");
		headers.put("Host", "liuxue.xdf.cn");
		headers.put("Pragma", "no-cache");
		headers.put("Referer", "https://liuxue.xdf.cn/university/");
		headers.put("Sec-Fetch-Dest", "document");
		headers.put("Sec-Fetch-Mode", "navigate");
		headers.put("Sec-Fetch-Site", "same-origin");
		headers.put("Sec-Fetch-User", "?1");
		headers.put("Upgrade-Insecure-Requests", "1");
		headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
		headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Google Chrome\";v=\"121\", \"Chromium\";v=\"121\"");
		headers.put("sec-ch-ua-mobile", "?0");
		headers.put("sec-ch-ua-platform", "\"Windows\"");
		
		System.out.println("https://liuxue.xdf.cn/university/"+schoolID+"/");
		
		StringBuffer SQL = new StringBuffer();
		String res = HttpSendUtils.get("https://liuxue.xdf.cn/university/"+schoolID+"/", headers);
		SQL.append(res);
		
		
		File file = new File("E://xindongfang//"+(schoolID%9) + "//");
		if(!file.exists()) {
			try {
				file.mkdirs();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		
		
		writeTempFile(new File(file, schoolID + ".txt"), SQL);
		
	}
	
	
	public static void runXinDongFangArticle(String URL) {
		//https://tapi.cnjzy.net/api/Public/LineRankList
		Map<String, String> headers = new HashMap<>();
		Map<String, String> params = new HashMap<>();


		headers.put("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
		headers.put("Accept-Encoding", "gzip, deflate, br");
		headers.put("Accept-Language", "zh-CN,zh;q=0.9");
		headers.put("Cache-Control", "no-cache");
		headers.put("Connection", "keep-alive");
		headers.put("Cookie", "visitor_type=old; visitor_type=old; qt_guid=18dbd225453-WsmpoFfjkB; qt_referer_land=https%3A%2F%2Fliuxue.xdf.cn%2F; AGL_USER_ID=7b9b9ffd-4b97-4486-a46c-20e9117fd342; Hm_lvt_7454b3dde334f8d8876851bc894bea29=1708275162; _gid=GA1.2.785512098.1708275162; 53gid2=13867195376002; 53gid0=13867195376002; 53gid1=13867195376002; 53revisit=1708275162665; 53kf_72214454_from_host=liuxue.xdf.cn; 53kf_72214454_keyword=https%3A%2F%2Fwww.baidu.com%2Flink%3Furl%3D5f6INEfRGVccFHllyeEiWB7YCzVPjsSQQlLBvX-ROZ7%26wd%3D%26eqid%3De91a943800cafc2e0000000665d235cf; uuid_53kf_72214454=fe48fda754afaefbdcd9561a67c79c32; 53kf_72214454_land_page=https%253A%252F%252Fliuxue.xdf.cn%252F; kf_72214454_land_page_ok=1; 53uvid=1; onliner_zdfq72214454=0; qt_city=chengdu%2C%E6%88%90%E9%83%BD; visitor_type=old; SECKEY_ABVK=GAmve1rtI3xoFz3DrkX+cTd+p88+MIQVGybH27PQaB0%3D; BMAP_SECKEY=2c_u53jC8dNhOwURL9EOmDgWnOQhXULWetrh3y36KCdjti67QPa9ZeY6UA39kufUTgB1iqw2AfJ5myr9NhzH0-d9tX7eT7Oj-8NxY2WGdK6redt1vuusLlXapZy_QHrq9snL188Bd9VFhb872mc7DnAKSF00vMy74GSeP_I6zo-y07b_uVQJw_nxzG8tdOyG; sajssdk_2015_cross_new_user=1; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2218dbd225453-WsmpoFfjkB%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMThkYmQyMjY3NzA1ODEtMDA5NWJjYjc3MmY4YzQxOC0yNjAwMTg1MS01MzA4NDE2LTE4ZGJkMjI2NzcxMjI1NSJ9%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%2218dbd226770581-0095bcb772f8c418-26001851-5308416-18dbd2267712255%22%7D; invite_53kf_totalnum_1=3; out_detain_num72214454_106521771=1; _ga_T5KN1FM7JQ=GS1.1.1708275162.1.1.1708277537.60.0.0; qt_search_text_content=%E6%B3%A2%E8%8E%AB%E7%BA%B3%E5%AD%A6%E9%99%A2%2C%E4%B9%8C%E6%8B%89%E5%B0%94%E8%81%94%E9%82%A6%E5%A4%A7%E5%AD%A6%2C%E7%8F%AD%E5%B0%BC%E8%8B%8F%E7%BB%B4%E5%A4%AB%E5%A4%A7%E5%AD%A6; qt_channel_source=%E5%85%8D%E8%B4%B9%E6%B8%A0%E9%81%93%2C%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F; qt_zt_xxb=22; _gat=1; Hm_lpvt_7454b3dde334f8d8876851bc894bea29=1708305470; _ga_T5KN1FM7JQ=GS1.1.1708305244.2.1.1708305469.50.0.0; _ga=GA1.1.2034928454.1708275162; _ga_829K0HT0GQ=GS1.2.1708305244.2.1.1708305470.43.0.0");
		headers.put("Host", "liuxue.xdf.cn");
		headers.put("Pragma", "no-cache");
		headers.put("Referer", "https://liuxue.xdf.cn/university/");
		headers.put("Sec-Fetch-Dest", "document");
		headers.put("Sec-Fetch-Mode", "navigate");
		headers.put("Sec-Fetch-Site", "same-origin");
		headers.put("Sec-Fetch-User", "?1");
		headers.put("Upgrade-Insecure-Requests", "1");
		headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
		headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Google Chrome\";v=\"121\", \"Chromium\";v=\"121\"");
		headers.put("sec-ch-ua-mobile", "?0");
		headers.put("sec-ch-ua-platform", "\"Windows\"");
		
		StringBuffer SQL = new StringBuffer();
		String res = HttpSendUtils.get(URL, headers);
		SQL.append(res);
		
		int articleID = Integer.parseInt(URL.substring(URL.lastIndexOf("/")+1, URL.indexOf(".shtml")));
		
		System.out.println(URL + ", " +articleID);
		
		File file = new File("E://xindongfang//article//"+(articleID%50) + "//");
		if(!file.exists()) {
			try {
				file.mkdirs();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		writeTempFile(new File(file, articleID + ".txt"), SQL);
		
	}
	
	
	
	public static void runQSMajor(String URL, String majorName) {
		//https://tapi.cnjzy.net/api/Public/LineRankList
		Map<String, String> headers = new HashMap<>();
		Map<String, String> params = new HashMap<>();

		headers.put("Cookie", "cookie-agreed-version=1.0.0; cookie-agreed-categories=[%22necessary%22%2C%22analytics%22%2C%22marketing%22]; cookie-agreed=2; _gcl_au=1.1.1171111986.1708561582; _parsely_session={%22sid%22:1%2C%22surl%22:%22https://www.qschina.cn/university-rankings/world-university-rankings/2024%22%2C%22sref%22:%22%22%2C%22sts%22:1708561582181%2C%22slts%22:0}; _parsely_visitor={%22id%22:%22pid=a2dfb37b-5c29-4181-a6db-e5925c594221%22%2C%22session_count%22:1%2C%22last_session_ts%22:1708561582181}; _hjSession_173635=eyJpZCI6Ijg3MWQ0NWNiLTdmNGQtNDg3YS1iM2EwLTRjZDNlMmZiZWQ4ZCIsImMiOjE3MDg1NjE1ODIyODAsInMiOjAsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjoxLCJzcCI6MX0=; __hstc=242697949.45fb5a8b7dbbbfe6eddf75a42d6dc394.1708561583851.1708561583851.1708561583851.1; hubspotutk=45fb5a8b7dbbbfe6eddf75a42d6dc394; __hssrc=1; _hjSessionUser_173635=eyJpZCI6ImE3ZWZlZDJmLTM4N2QtNTY5ZS04MjJkLTcwOTAyODAwMzcxMSIsImNyZWF0ZWQiOjE3MDg1NjE1ODIyNzgsImV4aXN0aW5nIjp0cnVlfQ==; _fbp=fb.1.1708561665214.1044904699; _ga_RXD808V3JK=GS1.1.1708562245.1.1.1708563741.0.0.0; _ga_29KNGNK9HC=GS1.1.1708566577.1.1.1708566672.60.0.0; __hssc=242697949.33.1708561583851; _ga_HLKFSN9Q00=GS1.1.1708561581.1.1.1708567540.60.0.0; _ga_YEJ4CZ5SBZ=GS1.1.1708561581.1.1.1708567540.0.0.0");
		
		headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
		
		
		StringBuffer SQL = new StringBuffer();
		String res = HttpSendUtils.get(URL, headers);
		
		if(res.indexOf("\"currentPath\":\"node\\/") != -1) {
			System.out.println(res.substring(res.indexOf("\"currentPath\":\"node\\/") + ",\"currentPath\":\"node\\/".length(), res.indexOf("\",\"currentPathIsAdmin\":")));
		}
		
		SQL.append(res);
		
		File file = new File("F://留学//");
		if(!file.exists()) {
			try {
				file.mkdirs();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		writeTempFile(new File(file, majorName + ".txt"), SQL);
		
	}
	
	public static String convertUnicode(String ori){
        char aChar;
        int len = ori.length();
        StringBuffer outBuffer = new StringBuffer(len);
        for (int x = 0; x < len;) {
            aChar = ori.charAt(x++);
            if (aChar == '\\') {
                aChar = ori.charAt(x++);
                if (aChar == 'u') {
                    // Read the xxxx
                    int value = 0;
                    for (int i = 0; i < 4; i++) {
                        aChar = ori.charAt(x++);
                        switch (aChar) {
                        case '0':
                        case '1':
                        case '2':
                        case '3':
                        case '4':
                        case '5':
                        case '6':
                        case '7':
                        case '8':
                        case '9':
                            value = (value << 4) + aChar - '0';
                            break;
                        case 'a':
                        case 'b':
                        case 'c':
                        case 'd':
                        case 'e':
                        case 'f':
                            value = (value << 4) + 10 + aChar - 'a';
                            break;
                        case 'A':
                        case 'B':
                        case 'C':
                        case 'D':
                        case 'E':
                        case 'F':
                            value = (value << 4) + 10 + aChar - 'A';
                            break;
                        default:
                            throw new IllegalArgumentException(
                                    "Malformed \\uxxxx encoding.");
                        }
                    }
                    outBuffer.append((char) value);
                } else {
                    if (aChar == 't')
                        aChar = '\t';
                    else if (aChar == 'r')
                        aChar = '\r';
                    else if (aChar == 'n')
                        aChar = '\n';
                    else if (aChar == 'f')
                        aChar = '\f';
                    outBuffer.append(aChar);
                }
            } else
                outBuffer.append(aChar);
        }
        return outBuffer.toString();
    }

	
	public static String unicodeDecode(String string) {
        Pattern pattern = Pattern.compile("(\\\\u(\\p{XDigit}{4}))");
        Matcher matcher = pattern.matcher(string);
        char ch;
        while (matcher.find()) {
            ch = (char) Integer.parseInt(matcher.group(2), 16);
            string = string.replace(matcher.group(1), ch + "");
        }
        return string;
    }
	
	private static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	
	
}
