package com.career.db;

import java.util.Date;

import com.career.utils.Tools;

public class AiTbForm implements Comparable<AiTbForm> {

	private String key;
	private int id;
	private String batch_id;
	private String batch_id_org;
	private String order_id;
	private String checker_id;
	private String checker_name;

	private String checker_remark;
	private String yxdm;
	private String yxmc;
	private String yxmc_org;
	private String zymc_org;
	private String yxbz;
	private String zyz;
	private String zydm;
	private String zymc;
	private String zybz;
	private Date create_tm;
	private Date last_update_tm;
	private int seq_no_zy;
	private int seq_no_yx;
	private String f_no;

	private int score_cj;
	private int score_wc;
	private String score_xk;

	private int zdf_a;
	private int zdf_b;
	private int zdf_c;
	private int zdfwc_a;
	private int zdfwc_b;
	private int zdfwc_c;

	private int pjf_a;
	private int pjf_b;
	private int pjf_c;
	private int pjfwc_a;
	private int pjfwc_b;
	private int pjfwc_c;
	
	private int zgf_a;
	private int zgf_b;
	private int zgf_c;
	private int zgfwc_a;
	private int zgfwc_b;
	private int zgfwc_c;
	private int jhs;
	private String fee;

	private int jhs_a;
	private int jhs_b;
	private int jhs_c;
	private int ext_zdfwc;

	public int getExt_zdfwc() {
		return ext_zdfwc;
	}

	public void setExt_zdfwc(int ext_zdfwc) {
		this.ext_zdfwc = ext_zdfwc;
	}

	public int getZgf_a() {
		return zgf_a;
	}

	public void setZgf_a(int zgf_a) {
		this.zgf_a = zgf_a;
	}

	public int getZgf_b() {
		return zgf_b;
	}

	public void setZgf_b(int zgf_b) {
		this.zgf_b = zgf_b;
	}

	public int getZgf_c() {
		return zgf_c;
	}

	public void setZgf_c(int zgf_c) {
		this.zgf_c = zgf_c;
	}

	public int getZgfwc_a() {
		return zgfwc_a;
	}

	public void setZgfwc_a(int zgfwc_a) {
		this.zgfwc_a = zgfwc_a;
	}

	public int getZgfwc_b() {
		return zgfwc_b;
	}

	public void setZgfwc_b(int zgfwc_b) {
		this.zgfwc_b = zgfwc_b;
	}

	public int getZgfwc_c() {
		return zgfwc_c;
	}

	public void setZgfwc_c(int zgfwc_c) {
		this.zgfwc_c = zgfwc_c;
	}

	public int getJhs() {
		return jhs;
	}

	public void setJhs(int jhs) {
		this.jhs = jhs;
	}

	public String getFee() {
		return fee;
	}

	public void setFee(String fee) {
		this.fee = fee;
	}

	public int getJhs_a() {
		return jhs_a;
	}

	public void setJhs_a(int jhs_a) {
		this.jhs_a = jhs_a;
	}

	public int getJhs_b() {
		return jhs_b;
	}

	public void setJhs_b(int jhs_b) {
		this.jhs_b = jhs_b;
	}

	public int getJhs_c() {
		return jhs_c;
	}

	public void setJhs_c(int jhs_c) {
		this.jhs_c = jhs_c;
	}

	private String ext_ranking;
	private String ext_level;

	private int ext_org_seq_no_zy;
	private int ext_org_seq_no_yx;

	private String ext_yxmc;

	private boolean ext_no_adjust;
	private int ext_waste_score;

	public String getExt_yxmc() {
		return ext_yxmc;
	}

	public int getExt_org_seq_no_zy() {
		return ext_org_seq_no_zy;
	}

	public void setExt_org_seq_no_zy(int ext_org_seq_no_zy) {
		this.ext_org_seq_no_zy = ext_org_seq_no_zy;
	}

	public int getExt_org_seq_no_yx() {
		return ext_org_seq_no_yx;
	}

	public void setExt_org_seq_no_yx(int ext_org_seq_no_yx) {
		this.ext_org_seq_no_yx = ext_org_seq_no_yx;
	}

	public int getExt_waste_score() {
		return ext_waste_score;
	}

	public void setExt_waste_score(int ext_waste_score) {
		this.ext_waste_score = ext_waste_score;
	}

	public boolean isExt_no_adjust() {
		return ext_no_adjust;
	}

	public void setExt_no_adjust(boolean ext_no_adjust) {
		this.ext_no_adjust = ext_no_adjust;
	}

	public void setExt_yxmc(String ext_yxmc) {
		this.ext_yxmc = ext_yxmc;
	}

	public String getExt_ranking() {
		return ext_ranking;
	}

	public void setExt_ranking(String ext_ranking) {
		this.ext_ranking = ext_ranking;
	}

	public String getExt_level() {
		return ext_level;
	}

	public void setExt_level(String ext_level) {
		this.ext_level = ext_level;
	}

	public String getYxmc_org() {
		return yxmc_org;
	}

	public void setYxmc_org(String yxmc_org) {
		this.yxmc_org = yxmc_org;
	}

	public String getZymc_org() {
		return zymc_org;
	}

	public void setZymc_org(String zymc_org) {
		this.zymc_org = zymc_org;
	}

	public int compareTo(AiTbForm p) {
		return p.getZdf_a() - this.getZdf_a();
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getZdf_a() {
		return zdf_a;
	}

	public void setZdf_a(int zdf_a) {
		this.zdf_a = zdf_a;
	}

	public int getZdf_b() {
		return zdf_b;
	}

	public void setZdf_b(int zdf_b) {
		this.zdf_b = zdf_b;
	}

	public int getZdf_c() {
		return zdf_c;
	}

	public void setZdf_c(int zdf_c) {
		this.zdf_c = zdf_c;
	}

	public int getZdfwc_a() {
		return zdfwc_a;
	}

	public void setZdfwc_a(int zdfwc_a) {
		this.zdfwc_a = zdfwc_a;
	}

	public int getZdfwc_b() {
		return zdfwc_b;
	}

	public void setZdfwc_b(int zdfwc_b) {
		this.zdfwc_b = zdfwc_b;
	}

	public int getZdfwc_c() {
		return zdfwc_c;
	}

	public void setZdfwc_c(int zdfwc_c) {
		this.zdfwc_c = zdfwc_c;
	}

	public int getPjf_a() {
		return pjf_a;
	}

	public void setPjf_a(int pjf_a) {
		this.pjf_a = pjf_a;
	}

	public int getPjf_b() {
		return pjf_b;
	}

	public void setPjf_b(int pjf_b) {
		this.pjf_b = pjf_b;
	}

	public int getPjf_c() {
		return pjf_c;
	}

	public void setPjf_c(int pjf_c) {
		this.pjf_c = pjf_c;
	}

	public int getPjfwc_a() {
		return pjfwc_a;
	}

	public void setPjfwc_a(int pjfwc_a) {
		this.pjfwc_a = pjfwc_a;
	}

	public int getPjfwc_b() {
		return pjfwc_b;
	}

	public void setPjfwc_b(int pjfwc_b) {
		this.pjfwc_b = pjfwc_b;
	}

	public int getPjfwc_c() {
		return pjfwc_c;
	}

	public void setPjfwc_c(int pjfwc_c) {
		this.pjfwc_c = pjfwc_c;
	}

	public String getChecker_name() {
		return checker_name;
	}

	public void setChecker_name(String checker_name) {
		this.checker_name = checker_name;
	}

	public int getScore_cj() {
		return score_cj;
	}

	public void setScore_cj(int score_cj) {
		this.score_cj = score_cj;
	}

	public int getScore_wc() {
		return score_wc;
	}

	public void setScore_wc(int score_wc) {
		this.score_wc = score_wc;
	}

	public String getScore_xk() {
		return score_xk;
	}

	public void setScore_xk(String score_xk) {
		this.score_xk = score_xk;
	}

	public String getBatch_id() {
		return batch_id;
	}

	public void setBatch_id(String batch_id) {
		this.batch_id = batch_id;
	}

	public String getBatch_id_org() {
		return batch_id_org;
	}

	public void setBatch_id_org(String batch_id_org) {
		this.batch_id_org = batch_id_org;
	}

	public String getOrder_id() {
		return order_id;
	}

	public void setOrder_id(String order_id) {
		this.order_id = order_id;
	}

	public String getChecker_id() {
		return checker_id;
	}

	public void setChecker_id(String checker_id) {
		this.checker_id = checker_id;
	}

	public String getChecker_remark() {
		return checker_remark;
	}

	public void setChecker_remark(String checker_remark) {
		this.checker_remark = checker_remark;
	}

	public String getYxdm() {
		return yxdm;
	}

	public void setYxdm(String yxdm) {
		this.yxdm = yxdm;
	}

	public String getYxmc() {
		return yxmc;
	}

	public void setYxmc(String yxmc) {
		this.yxmc = yxmc;
	}

	public String getYxbz() {
		return yxbz;
	}

	public void setYxbz(String yxbz) {
		this.yxbz = yxbz;
	}

	public String getZyz() {
		return zyz;
	}

	public void setZyz(String zyz) {
		this.zyz = zyz;
	}

	public String getZydm() {
		return zydm;
	}

	public void setZydm(String zydm) {
		this.zydm = zydm;
	}

	public String getZymc() {
		return zymc;
	}

	public void setZymc(String zymc) {
		this.zymc = zymc;
	}

	public String getZybz() {
		return zybz;
	}

	public void setZybz(String zybz) {
		this.zybz = zybz;
	}

	public Date getCreate_tm() {
		return create_tm;
	}

	public void setCreate_tm(Date create_tm) {
		this.create_tm = create_tm;
	}

	public Date getLast_update_tm() {
		return last_update_tm;
	}

	public void setLast_update_tm(Date last_update_tm) {
		this.last_update_tm = last_update_tm;
	}

	public int getSeq_no_zy() {
		return seq_no_zy;
	}

	public void setSeq_no_zy(int seq_no_zy) {
		this.seq_no_zy = seq_no_zy;
	}

	public int getSeq_no_yx() {
		return seq_no_yx;
	}

	public void setSeq_no_yx(int seq_no_yx) {
		this.seq_no_yx = seq_no_yx;
	}

	public String getF_no() {
		return f_no;
	}

	public void setF_no(String f_no) {
		this.f_no = f_no;
	}

}
