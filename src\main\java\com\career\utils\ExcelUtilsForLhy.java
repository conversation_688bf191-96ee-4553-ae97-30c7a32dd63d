package com.career.utils;

import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.career.db.CardBean;
import com.career.db.JHBean;
import com.career.db.LhyForm;
import com.career.db.ZyzdForm;
import com.career.db.ZyzdMajorRanking;
import com.career.db.ZyzdUniversityBean;
import com.career.db.ZyzdUniversityDataBean;
import com.career.db.jhdao.YfJHBean;

import cn.hutool.core.lang.UUID;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;


public class ExcelUtilsForLhy {
	
	public static void main(String args[]) {
	}

	public static void createYxZyRankingExcel(String filePathAndName,  HashMap<String, List<ZyzdMajorRanking>> dataListMap) {
		// 创建一个新的工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        Tools.println(filePathAndName);
        createYxZyRankingExcel(workbook, dataListMap);
 
        // 写出到文件
        try{
        	FileOutputStream fileOut = new FileOutputStream(filePathAndName);
            workbook.write(fileOut);
            workbook.close();
        } catch (Exception e){
        	e.printStackTrace();
        }finally {
        	try {
            	workbook.close();
            } catch (IOException e){
                e.printStackTrace();
            } 
        }
    }
	
	private static void createYxZyRankingExcel(XSSFWorkbook workbook, HashMap<String, List<ZyzdMajorRanking>> dataListMap) {
		Iterator<String> it = dataListMap.keySet().iterator();
        while(it.hasNext()) {
        	String tabName = it.next();
        	List<ZyzdMajorRanking> dataList = dataListMap.get(tabName);
        	// 创建一个工作表
            Sheet sheet = workbook.createSheet(tabName);
            sheet.setColumnWidth(0, 8*256);
            sheet.setColumnWidth(1, 8*256);
            sheet.setColumnWidth(2, 20*256);
            sheet.setColumnWidth(3, 10*256);
            sheet.setColumnWidth(4, 16*256);
            sheet.setColumnWidth(5, 25*256);
            sheet.setColumnWidth(6, 15*256);
            
            CellStyle cellStyleFontBold = workbook.createCellStyle();
            //必须设置 否则背景色不生效
            cellStyleFontBold.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            Font fontBold = workbook.createFont();
            fontBold.setFontHeightInPoints((short) 10);
            fontBold.setBold(true);
            fontBold.setColor(IndexedColors.BLACK.getIndex());
            
            CellStyle cellStyleFontNormal = workbook.createCellStyle();
            //必须设置 否则背景色不生效
            cellStyleFontNormal.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            Font fontNormal = workbook.createFont();
            fontNormal.setFontHeightInPoints((short) 10);
            fontNormal.setBold(false);
            fontNormal.setColor(IndexedColors.BLACK.getIndex());
            
            CellStyle cellStyleFontGreen = workbook.createCellStyle();
            //必须设置 否则背景色不生效
            cellStyleFontGreen.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            Font fontGreen = workbook.createFont();
            fontGreen.setFontHeightInPoints((short) 10);
            fontGreen.setBold(true);
            fontGreen.setColor(IndexedColors.GREEN.getIndex());
            cellStyleFontGreen.setFont(fontGreen);
            
            CellStyle cellStyleFontRed = workbook.createCellStyle();
            //必须设置 否则背景色不生效
            cellStyleFontRed.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            Font fontRed = workbook.createFont();
            fontRed.setFontHeightInPoints((short) 10);
            fontRed.setColor(IndexedColors.RED.getIndex());
            cellStyleFontRed.setFont(fontRed);
            
            // 创建一个行，并在其中创建一个单元格
            Row rowOne = sheet.createRow(0);
    		Cell rowOneCell1 = rowOne.createCell(0);
    		rowOneCell1.setCellValue("排名");
    		rowOneCell1.setCellStyle(cellStyleFontBold);
    		
            Cell rowOneCell2 = rowOne.createCell(1);
            rowOneCell2.setCellValue("院校名称");
            rowOneCell2.setCellStyle(cellStyleFontBold);
    		
            Cell rowOneCell3 = rowOne.createCell(2);
            rowOneCell3.setCellValue("院校属性");
            rowOneCell3.setCellStyle(cellStyleFontBold);
    		
            
            Cell rowOneCell4 = rowOne.createCell(3);
            rowOneCell4.setCellValue("院校最低");
            rowOneCell4.setCellStyle(cellStyleFontBold);
    		
            Cell rowOneCell5 = rowOne.createCell(4);
            rowOneCell5.setCellValue("专业名称");
            rowOneCell5.setCellStyle(cellStyleFontBold);
    		
            Cell rowOneCell6 = rowOne.createCell(5);
            rowOneCell6.setCellValue("专业最低");
            rowOneCell6.setCellStyle(cellStyleFontBold);
    		
            
            for(int i= 0; i < dataList.size(); i++) {
            	ZyzdMajorRanking bean = dataList.get(i);
    	        Row row = sheet.createRow(i+1);
    	        Cell cell1 = row.createCell(0);
    	        cell1.setCellValue(bean.getRanking());
    	        cell1.setCellStyle(cellStyleFontNormal);
    	        
    	        Cell cell2 = row.createCell(1);
    	        cell2.setCellValue(bean.getYxmc());
    	        cell2.setCellStyle(cellStyleFontNormal);
    	        
    	        Cell cell3 = row.createCell(2);
    	        cell3.setCellValue(bean.getExt_tags());
    	        cell3.setCellStyle(cellStyleFontNormal);
    	        
    	        Cell cell4 = row.createCell(3);
    	        if(Tools.isEmpty(bean.getExt_zdf())) {
    	        	cell4.setCellValue("-");
    	        	cell4.setCellStyle(cellStyleFontNormal);
    	        }else {
    	        	cell4.setCellStyle(bean.isExt_is_overload() ? cellStyleFontRed:cellStyleFontGreen);
    	        	cell4.setCellValue(bean.getExt_zdf()+"("+bean.getExt_zdfwc()+")");
    	        }
    	        Cell cell5 = row.createCell(4);
    	        cell5.setCellValue(bean.getZymc());
    	        cell5.setCellStyle(cellStyleFontNormal);
	        	
    	        Cell cell6 = row.createCell(5);
    	        cell6.setCellStyle(cellStyleFontNormal);
    	        if(Tools.isEmpty(bean.getExt_zy_zdf())) {
    	        	cell6.setCellValue("-");
    	        }else {
    	        	cell6.setCellValue(bean.getExt_zy_zdf()+"("+bean.getExt_zy_zdfwc()+")");
    	        }
            }
        }
    }
	
	public static void createFormReportExcel(int jhYear, String filePathAndName, String sf, String pc, String xk, String score,String wc, String xm, List<LhyForm> dataList, HashMap<String, List<ZyzdMajorRanking>> dataRankingListMap, List<JHBean> existYxAllDataList) {
		// 创建一个新的工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        Tools.println(filePathAndName);
        // 创建一个工作表
        Sheet sheet = workbook.createSheet("志愿表_"+Tools.viewXK(xk)+score+"("+wc+")");
        
        sheet.setColumnWidth(0, 10*256);
        sheet.setColumnWidth(1, 10*256);
        sheet.setColumnWidth(2, 30*256);
        sheet.setColumnWidth(3, 13*256);
        sheet.setColumnWidth(4, 10*256);
        sheet.setColumnWidth(5, 10*256);
        sheet.setColumnWidth(6, 10*256);
        sheet.setColumnWidth(7, 30*256);
        sheet.setColumnWidth(8, 25*256);
        sheet.setColumnWidth(9, 25*256);
        sheet.setColumnWidth(10, 25*256);
        sheet.setColumnWidth(11, 25*256);
        sheet.setColumnWidth(12, 10*256);
        
        XSSFCellStyle style_content = workbook.createCellStyle();
        style_content.setBorderBottom(BorderStyle.THIN);//下边框
        style_content.setBorderTop(BorderStyle.THIN);//上边框
        style_content.setBorderLeft(BorderStyle.THIN);//左边框
        style_content.setBorderRight(BorderStyle.THIN);//右边框
        style_content.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        style_content.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        CellStyle cellStyleFontBold = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontBold.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontBold = workbook.createFont();
        fontBold.setFontHeightInPoints((short) 10);
        fontBold.setBold(true);
        fontBold.setColor(IndexedColors.BLACK.getIndex());
        fontBold.setFontName("微软雅黑");
        cellStyleFontBold.setFont(fontBold);
        cellStyleFontBold.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontBold.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontBold.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontBold.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontNormal0 = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontNormal0.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontNormal0 = workbook.createFont();
        fontNormal0.setFontHeightInPoints((short) 10);
        fontNormal0.setBold(false);
        fontNormal0.setColor(IndexedColors.BLACK.getIndex());
        fontNormal0.setFontName("微软雅黑");
        cellStyleFontNormal0.setFont(fontNormal0);
        cellStyleFontNormal0.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        cellStyleFontNormal0.setAlignment(HorizontalAlignment.CENTER);
        cellStyleFontNormal0.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyleFontNormal0.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontNormal0.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontNormal0.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontNormal0.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontNormal2 = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontNormal2.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontNormal2 = workbook.createFont();
        fontNormal2.setFontHeightInPoints((short) 10);
        fontNormal2.setBold(false);
        fontNormal2.setColor(IndexedColors.BLACK.getIndex());
        fontNormal2.setFontName("微软雅黑");
        cellStyleFontNormal2.setFont(fontNormal2);
        cellStyleFontNormal2.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        cellStyleFontNormal2.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontNormal2.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontNormal2.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontNormal2.setBorderRight(BorderStyle.THIN);//右边框
        
        
        
        CellStyle cellStyleFontNormal1 = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontNormal1.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontNormal = workbook.createFont();
        fontNormal.setFontHeightInPoints((short) 10);
        fontNormal.setBold(false);
        fontNormal.setColor(IndexedColors.BLACK.getIndex());
        fontNormal.setFontName("微软雅黑");
        cellStyleFontNormal1.setFont(fontNormal);
        cellStyleFontNormal1.setFillForegroundColor(IndexedColors.LEMON_CHIFFON.getIndex());
        cellStyleFontNormal1.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontNormal1.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontNormal1.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontNormal1.setBorderRight(BorderStyle.THIN);//右边框
        

        CellStyle cellStyleFontHighLight = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontHighLight.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontHighLight = workbook.createFont();
        fontHighLight.setFontHeightInPoints((short) 10);
        fontHighLight.setBold(true);
        fontHighLight.setColor(IndexedColors.BLACK.getIndex());
        fontHighLight.setFontName("微软雅黑");
        cellStyleFontHighLight.setFont(fontHighLight);
        cellStyleFontHighLight.setAlignment(HorizontalAlignment.CENTER);
        cellStyleFontHighLight.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyleFontHighLight.setFillForegroundColor(IndexedColors.TAN.getIndex());
        cellStyleFontHighLight.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontHighLight.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontHighLight.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontHighLight.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontGreen = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontGreen.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyleFontGreen.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        Font fontGreen = workbook.createFont();
        fontGreen.setFontHeightInPoints((short) 10);
        fontGreen.setBold(true);
        fontGreen.setColor(IndexedColors.GREEN.getIndex());
        fontGreen.setFontName("微软雅黑");
        cellStyleFontGreen.setFont(fontGreen);
        
        CellStyle cellStyleFontRed = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontRed.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontRed = workbook.createFont();
        fontRed.setFontHeightInPoints((short) 10);
        fontRed.setColor(IndexedColors.RED.getIndex());
        fontRed.setFontName("微软雅黑");
        cellStyleFontRed.setFont(fontRed);
        
        CellStyle cellStyleFontBlueBig = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontBlueBig.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontBlueBig = workbook.createFont();
        fontBlueBig.setFontHeightInPoints((short) 18);
        fontBlueBig.setBold(true);
        fontBlueBig.setColor(IndexedColors.ORANGE.getIndex());
        fontBlueBig.setFontName("微软雅黑");
        cellStyleFontBlueBig.setFont(fontBlueBig);
        cellStyleFontBlueBig.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        cellStyleFontBlueBig.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontBlueBig.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontBlueBig.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontBlueBig.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontBlack14 = workbook.createCellStyle();
        cellStyleFontBlueBig.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontBlackBig14 = workbook.createFont();
        fontBlackBig14.setFontHeightInPoints((short) 14);
        fontBlackBig14.setBold(true);
        fontBlackBig14.setColor(IndexedColors.BLACK.getIndex());
        fontBlackBig14.setFontName("微软雅黑");
        cellStyleFontBlack14.setFont(fontBlackBig14);
        cellStyleFontBlack14.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        cellStyleFontBlack14.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyleFontBlack14.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontBlack14.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontBlack14.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontBlack14.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontThinBlack14 = workbook.createCellStyle();
        cellStyleFontThinBlack14.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontBlackThin14 = workbook.createFont();
        fontBlackThin14.setFontHeightInPoints((short) 14);
        fontBlackThin14.setBold(false);
        fontBlackThin14.setColor(IndexedColors.BLACK.getIndex());
        fontBlackThin14.setFontName("微软雅黑");
        cellStyleFontThinBlack14.setFont(fontBlackThin14);
        cellStyleFontThinBlack14.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        cellStyleFontThinBlack14.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyleFontThinBlack14.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontThinBlack14.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontThinBlack14.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontThinBlack14.setBorderRight(BorderStyle.THIN);//右边框
        
        
        CellStyle cellStyleFontColumnHeader = workbook.createCellStyle();
        cellStyleFontColumnHeader.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontColumnHeader = workbook.createFont();
        fontColumnHeader.setFontHeightInPoints((short) 11); 
        fontColumnHeader.setBold(true);
        fontColumnHeader.setColor(IndexedColors.BLACK.getIndex());
        fontColumnHeader.setFontName("微软雅黑");
        cellStyleFontColumnHeader.setFont(fontColumnHeader);
        cellStyleFontColumnHeader.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        cellStyleFontColumnHeader.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyleFontColumnHeader.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontColumnHeader.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontColumnHeader.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontColumnHeader.setBorderRight(BorderStyle.THIN);//右边框
        

        Row rowTitle = sheet.createRow(0);
        Cell rowTitleCell1 = rowTitle.createCell(0);
        rowTitleCell1.setCellValue(jhYear + "年志愿方案-"+sf+"-"+xm+"-"+pc);
        
        CellStyle cellStyle = workbook.createCellStyle();
        CreationHelper creationHelper = workbook.getCreationHelper();
        cellStyle.setDataFormat(creationHelper.createDataFormat().getFormat("text")); // 对于文本，使用"text"格式以保持文本格式不变
        cellStyle.setAlignment(HorizontalAlignment.CENTER); // 设置水平居中对齐
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直居中对齐（如果需要）
        fontBlueBig.setFontName("微软雅黑");
        cellStyle.setFont(fontBlueBig);
        rowTitleCell1.setCellStyle(cellStyle); // 应用样式到单元格
        
        CellRangeAddress deviceCellRangeTitle = new CellRangeAddress(0, 0, 0, 12);
		sheet.addMergedRegion(deviceCellRangeTitle);
		
		
        //必须设置 否则背景色不生效
        cellStyleFontBlueBig.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        
        
        
        Row rowSubTitle = sheet.createRow(1);
        
        for(int i=0;i<2;i++) {
	        Cell rowSubTitleCell1 = rowSubTitle.createCell(i);
	        rowSubTitleCell1.setCellValue("报考批次");
	        rowSubTitleCell1.setCellStyle(cellStyleFontBlack14);
        }
        for(int i=2;i<3;i++) {
	        Cell rowSubTitleCell1 = rowSubTitle.createCell(i);
	        rowSubTitleCell1.setCellValue(pc);
	        rowSubTitleCell1.setCellStyle(cellStyleFontThinBlack14);
        }
        
        for(int i=3;i<5;i++) {
	        Cell rowSubTitleCell1 = rowSubTitle.createCell(i);
	        rowSubTitleCell1.setCellValue("选科组合");
	        rowSubTitleCell1.setCellStyle(cellStyleFontBlack14);
        }
        for(int i=5;i<6;i++) {
	        Cell rowSubTitleCell1 = rowSubTitle.createCell(i);
	        rowSubTitleCell1.setCellValue(Tools.viewXK(xk));
	        rowSubTitleCell1.setCellStyle(cellStyleFontThinBlack14);
        }
        for(int i=6;i<8;i++) {
        	Cell rowSubTitleCell1 = rowSubTitle.createCell(i);
	        rowSubTitleCell1.setCellValue("成绩");
	        rowSubTitleCell1.setCellStyle(cellStyleFontBlack14);
        }
        for(int i=8;i<10;i++) {
        	Cell rowSubTitleCell1 = rowSubTitle.createCell(i);
	        rowSubTitleCell1.setCellValue(score+"/"+wc);
	        rowSubTitleCell1.setCellStyle(cellStyleFontThinBlack14);
        }

        CellRangeAddress deviceCellRangeSubTitle1 = new CellRangeAddress(1, 1, 0, 1);
		sheet.addMergedRegion(deviceCellRangeSubTitle1);
		
		CellRangeAddress deviceCellRangeSubTitle2 = new CellRangeAddress(1, 1, 3, 4);
		sheet.addMergedRegion(deviceCellRangeSubTitle2);
		
		CellRangeAddress deviceCellRangeSubTitle3 = new CellRangeAddress(1, 1, 6, 7);
		sheet.addMergedRegion(deviceCellRangeSubTitle3);
		
		CellRangeAddress deviceCellRangeSubTitle4 = new CellRangeAddress(1, 1, 8, 12);
		sheet.addMergedRegion(deviceCellRangeSubTitle4);
 
		// 创建一个行，并在其中创建一个单元格
		Row rowOne = sheet.createRow(2);
		Cell rowOneCell1 = rowOne.createCell(0);
		rowOneCell1.setCellValue("志愿顺序");
		rowOneCell1.setCellStyle(cellStyleFontColumnHeader);

		Cell rowOneCell2 = rowOne.createCell(1);
		rowOneCell2.setCellValue("院校代码");
		rowOneCell2.setCellStyle(cellStyleFontColumnHeader);

		Cell rowOneCell3 = rowOne.createCell(2);
		rowOneCell3.setCellValue("院校名称");
		rowOneCell3.setCellStyle(cellStyleFontColumnHeader);

		Cell rowOneCell4 = rowOne.createCell(3);
		rowOneCell4.setCellValue("院校层次");
		rowOneCell4.setCellStyle(cellStyleFontColumnHeader);

		Cell rowOneCell5 = rowOne.createCell(4);
		rowOneCell5.setCellValue("专业顺序");
		rowOneCell5.setCellStyle(cellStyleFontColumnHeader);

		Cell rowOneCell6 = rowOne.createCell(5);
		rowOneCell6.setCellValue("专业代码");
		rowOneCell6.setCellStyle(cellStyleFontColumnHeader);

		// 修正重复的 rowOneCell7，改为 rowOneCell7 和 rowOneCell8
		Cell rowOneCell7 = rowOne.createCell(6);
		rowOneCell7.setCellValue("计划人数");
		rowOneCell7.setCellStyle(cellStyleFontColumnHeader);

		Cell rowOneCell8 = rowOne.createCell(7);
		rowOneCell8.setCellValue("专业名称");
		rowOneCell8.setCellStyle(cellStyleFontColumnHeader);

		Cell rowOneCell9 = rowOne.createCell(8);
		rowOneCell9.setCellValue(String.valueOf(jhYear - 1) + "年最低分(位次)/录取人数");
		rowOneCell9.setCellStyle(cellStyleFontColumnHeader);

		Cell rowOneCell10 = rowOne.createCell(9);
		rowOneCell10.setCellValue(String.valueOf(jhYear - 2) + "年最低分(位次)/录取人数");
		rowOneCell10.setCellStyle(cellStyleFontColumnHeader);

		Cell rowOneCell11 = rowOne.createCell(10);
		rowOneCell11.setCellValue(String.valueOf(jhYear - 3) + "年最低分(位次)/录取人数");
		rowOneCell11.setCellStyle(cellStyleFontColumnHeader);

		Cell rowOneCell12 = rowOne.createCell(11);
		rowOneCell12.setCellValue("专业排名(等级或近似专业)");
		rowOneCell12.setCellStyle(cellStyleFontColumnHeader);
		
		Cell rowOneCell13 = rowOne.createCell(12);
		rowOneCell13.setCellValue("学费");
		rowOneCell13.setCellStyle(cellStyleFontColumnHeader);
        
        HashMap<Integer, Integer> MAP_YX_ZYZ_SPAN = new HashMap<>(); 
        
        for(int i= 0; i < dataList.size(); i++) {
        	LhyForm bean = dataList.get(i);
        	if("重庆贵州河北山东浙江辽宁青海C1G4H2L1S1Z1Q1".indexOf(sf) == -1) {
        		MAP_YX_ZYZ_SPAN.put(bean.getSeq_no_yx(), bean.getSeq_no_zy());
        	}
	        Row row = sheet.createRow(i+3);
	        Cell cell1 = row.createCell(0);
	        cell1.setCellValue(bean.getSeq_no_yx());
	        cell1.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        
	        Cell cell2 = row.createCell(1);
	        cell2.setCellValue(bean.getYxdm());
	        cell2.setCellStyle(cellStyleFontHighLight);
	        
	        Cell cell3 = row.createCell(2);
	        cell3.setCellValue(bean.getExt_yxmc());
	        cell3.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        
	        ZyzdUniversityBean CACHE_UNIV = ZyzdCache.getUniversity(bean.getYxmc_org());
	        CACHE_UNIV = CACHE_UNIV == null ? new ZyzdUniversityBean(): CACHE_UNIV;
	        
	        String displayYxInfoB = "";
	        String is985 = CACHE_UNIV == null ? null : CACHE_UNIV.getIs985();
			String is211 = CACHE_UNIV == null ? null : CACHE_UNIV.getIs211();
			String issyl = CACHE_UNIV == null ? null : CACHE_UNIV.getIssyl();
			if(Tools.isEmpty(is985) && Tools.isEmpty(is211) && Tools.isEmpty(issyl)){
				
			}else{
				if(!Tools.isEmpty(is985)){
					displayYxInfoB = "985";
				}else if(!Tools.isEmpty(is211)){
					displayYxInfoB = "211";
				}else{
					displayYxInfoB = "双一流";
				}
			}
	         
	        Cell cell4 = row.createCell(3);
	        if(Tools.isEmpty(displayYxInfoB)) {
	        	cell4.setCellValue(Tools.view(CACHE_UNIV.getInd_nature()));
	        }else {
	        	cell4.setCellValue(Tools.view(CACHE_UNIV.getInd_nature()) + ", " + Tools.view(displayYxInfoB));
	        }
	        cell4.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);

	        // 修正重复的 cell4，改为 cell5
	        Cell cell5 = row.createCell(4);
	        cell5.setCellValue(bean.getSeq_no_zy());
	        cell5.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);

	        Cell cell6 = row.createCell(5);
	        cell6.setCellValue(bean.getZydm());
	        cell6.setCellStyle(cellStyleFontHighLight);

	        Cell cell7 = row.createCell(6);
	        cell7.setCellValue(bean.getJhs());
	        cell7.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);

	        Cell cell8 = row.createCell(7);
	        cell8.setCellValue(bean.getZymc());
	        cell8.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);

	        Cell cell9 = row.createCell(8);
	        cell9.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        if (bean.getZdf_a() < 10) {
	            cell9.setCellValue("-");
	        } else {
	            cell9.setCellValue(bean.getZdf_a() + "(" + bean.getZdfwc_a() + ")" + " / " + bean.getLqrs_a());
	        }

	        Cell cell10 = row.createCell(9);
	        cell10.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        if (bean.getZdf_b() < 10) {
	            cell10.setCellValue("-");
	        } else {
	            cell10.setCellValue(bean.getZdf_b() + "(" + bean.getZdfwc_b() + ")" + " / " + bean.getLqrs_b());
	        }

	        Cell cell11 = row.createCell(10);
	        cell11.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        if (bean.getZdf_c() < 10) {
	            cell11.setCellValue("-");
	        } else {
	            cell11.setCellValue(bean.getZdf_c() + "(" + bean.getZdfwc_c() + ")" + " / " + bean.getLqrs_c());
	        }

	        Cell cell12 = row.createCell(11);
	        cell12.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        if (Tools.isEmpty(bean.getExt_ranking())) {
	            cell12.setCellValue("-");
	        } else {
	            cell12.setCellValue(bean.getExt_ranking() + "(" + bean.getExt_level() + ")");
	        }
	        
	        Cell cell13 = row.createCell(12);
	        cell13.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        if (Tools.isEmpty(bean.getFee())) {
	        	cell13.setCellValue("-");
	        } else {
	        	cell13.setCellValue(bean.getFee()); 
	        }
        }
        
        int startIndex = 2;
        Iterator<Integer> its = MAP_YX_ZYZ_SPAN.keySet().iterator();
        while(its.hasNext()) {
        	int form_no = its.next();
        	int zy_count = MAP_YX_ZYZ_SPAN.get(form_no);
        	if(zy_count > 1) {
        		CellRangeAddress deviceCellRange0 = new CellRangeAddress(startIndex + 1, startIndex + zy_count, 0, 0);
        		sheet.addMergedRegion(deviceCellRange0);
        		CellRangeAddress deviceCellRange1 = new CellRangeAddress(startIndex + 1, startIndex + zy_count, 1, 1);
        		sheet.addMergedRegion(deviceCellRange1);
        		CellRangeAddress deviceCellRange2 = new CellRangeAddress(startIndex + 1, startIndex + zy_count, 2, 2);
        		sheet.addMergedRegion(deviceCellRange2);
        	}
            startIndex += zy_count;
        }
        

        if(existYxAllDataList.size() > 0) {
        	createExistYxDataAll(workbook, jhYear, existYxAllDataList);
        }
 
        // 写出到文件
        try{
        	FileOutputStream fileOut = new FileOutputStream(filePathAndName);
            workbook.write(fileOut);
            workbook.close();
        } catch (Exception e){
        	e.printStackTrace();
        }finally {
        	try {
            	workbook.close();
            } catch (IOException e){
                e.printStackTrace();
            } 
        }
    }
	
	public static void createFormReportExcel2026(int jhYear, String filePathAndName, String sf, String pc, String xk, String score,String wc, String xm, List<LhyForm> dataList, HashMap<String, List<ZyzdMajorRanking>> dataRankingListMap, List<YfJHBean> existYxAllDataList) {
		// 创建一个新的工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        Tools.println(filePathAndName);
        // 创建一个工作表
        Sheet sheet = workbook.createSheet("志愿表_"+Tools.viewXK(xk)+score+"("+wc+")");
        
        sheet.setColumnWidth(0, 10*256);
        sheet.setColumnWidth(1, 10*256);
        sheet.setColumnWidth(2, 30*256);
        sheet.setColumnWidth(3, 13*256);
        sheet.setColumnWidth(4, 10*256);
        sheet.setColumnWidth(5, 10*256);
        sheet.setColumnWidth(6, 10*256);
        sheet.setColumnWidth(7, 30*256);
        sheet.setColumnWidth(8, 25*256);
        sheet.setColumnWidth(9, 25*256);
        sheet.setColumnWidth(10, 25*256);
        sheet.setColumnWidth(11, 25*256);
        sheet.setColumnWidth(12, 10*256);
        
        XSSFCellStyle style_content = workbook.createCellStyle();
        style_content.setBorderBottom(BorderStyle.THIN);//下边框
        style_content.setBorderTop(BorderStyle.THIN);//上边框
        style_content.setBorderLeft(BorderStyle.THIN);//左边框
        style_content.setBorderRight(BorderStyle.THIN);//右边框
        style_content.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        style_content.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        CellStyle cellStyleFontBold = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontBold.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontBold = workbook.createFont();
        fontBold.setFontHeightInPoints((short) 10);
        fontBold.setBold(true);
        fontBold.setColor(IndexedColors.BLACK.getIndex());
        fontBold.setFontName("微软雅黑");
        cellStyleFontBold.setFont(fontBold);
        cellStyleFontBold.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontBold.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontBold.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontBold.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontNormal0 = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontNormal0.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontNormal0 = workbook.createFont();
        fontNormal0.setFontHeightInPoints((short) 10);
        fontNormal0.setBold(false);
        fontNormal0.setColor(IndexedColors.BLACK.getIndex());
        fontNormal0.setFontName("微软雅黑");
        cellStyleFontNormal0.setFont(fontNormal0);
        cellStyleFontNormal0.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        cellStyleFontNormal0.setAlignment(HorizontalAlignment.CENTER);
        cellStyleFontNormal0.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyleFontNormal0.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontNormal0.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontNormal0.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontNormal0.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontNormal2 = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontNormal2.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontNormal2 = workbook.createFont();
        fontNormal2.setFontHeightInPoints((short) 10);
        fontNormal2.setBold(false);
        fontNormal2.setColor(IndexedColors.BLACK.getIndex());
        fontNormal2.setFontName("微软雅黑");
        cellStyleFontNormal2.setFont(fontNormal2);
        cellStyleFontNormal2.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        cellStyleFontNormal2.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontNormal2.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontNormal2.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontNormal2.setBorderRight(BorderStyle.THIN);//右边框
        
        
        
        CellStyle cellStyleFontNormal1 = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontNormal1.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontNormal = workbook.createFont();
        fontNormal.setFontHeightInPoints((short) 10);
        fontNormal.setBold(false);
        fontNormal.setColor(IndexedColors.BLACK.getIndex());
        fontNormal.setFontName("微软雅黑");
        cellStyleFontNormal1.setFont(fontNormal);
        cellStyleFontNormal1.setFillForegroundColor(IndexedColors.LEMON_CHIFFON.getIndex());
        cellStyleFontNormal1.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontNormal1.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontNormal1.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontNormal1.setBorderRight(BorderStyle.THIN);//右边框
        

        CellStyle cellStyleFontHighLight = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontHighLight.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontHighLight = workbook.createFont();
        fontHighLight.setFontHeightInPoints((short) 10);
        fontHighLight.setBold(true);
        fontHighLight.setColor(IndexedColors.BLACK.getIndex());
        fontHighLight.setFontName("微软雅黑");
        cellStyleFontHighLight.setFont(fontHighLight);
        cellStyleFontHighLight.setAlignment(HorizontalAlignment.CENTER);
        cellStyleFontHighLight.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyleFontHighLight.setFillForegroundColor(IndexedColors.TAN.getIndex());
        cellStyleFontHighLight.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontHighLight.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontHighLight.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontHighLight.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontGreen = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontGreen.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyleFontGreen.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        Font fontGreen = workbook.createFont();
        fontGreen.setFontHeightInPoints((short) 10);
        fontGreen.setBold(true);
        fontGreen.setColor(IndexedColors.GREEN.getIndex());
        fontGreen.setFontName("微软雅黑");
        cellStyleFontGreen.setFont(fontGreen);
        
        CellStyle cellStyleFontRed = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontRed.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontRed = workbook.createFont();
        fontRed.setFontHeightInPoints((short) 10);
        fontRed.setColor(IndexedColors.RED.getIndex());
        fontRed.setFontName("微软雅黑");
        cellStyleFontRed.setFont(fontRed);
        
        CellStyle cellStyleFontBlueBig = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontBlueBig.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontBlueBig = workbook.createFont();
        fontBlueBig.setFontHeightInPoints((short) 18);
        fontBlueBig.setBold(true);
        fontBlueBig.setColor(IndexedColors.ORANGE.getIndex());
        fontBlueBig.setFontName("微软雅黑");
        cellStyleFontBlueBig.setFont(fontBlueBig);
        cellStyleFontBlueBig.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        cellStyleFontBlueBig.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontBlueBig.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontBlueBig.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontBlueBig.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontBlack14 = workbook.createCellStyle();
        cellStyleFontBlueBig.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontBlackBig14 = workbook.createFont();
        fontBlackBig14.setFontHeightInPoints((short) 14);
        fontBlackBig14.setBold(true);
        fontBlackBig14.setColor(IndexedColors.BLACK.getIndex());
        fontBlackBig14.setFontName("微软雅黑");
        cellStyleFontBlack14.setFont(fontBlackBig14);
        cellStyleFontBlack14.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        cellStyleFontBlack14.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyleFontBlack14.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontBlack14.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontBlack14.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontBlack14.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontThinBlack14 = workbook.createCellStyle();
        cellStyleFontThinBlack14.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontBlackThin14 = workbook.createFont();
        fontBlackThin14.setFontHeightInPoints((short) 14);
        fontBlackThin14.setBold(false);
        fontBlackThin14.setColor(IndexedColors.BLACK.getIndex());
        fontBlackThin14.setFontName("微软雅黑");
        cellStyleFontThinBlack14.setFont(fontBlackThin14);
        cellStyleFontThinBlack14.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        cellStyleFontThinBlack14.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyleFontThinBlack14.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontThinBlack14.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontThinBlack14.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontThinBlack14.setBorderRight(BorderStyle.THIN);//右边框
        
        
        CellStyle cellStyleFontColumnHeader = workbook.createCellStyle();
        cellStyleFontColumnHeader.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontColumnHeader = workbook.createFont();
        fontColumnHeader.setFontHeightInPoints((short) 11); 
        fontColumnHeader.setBold(true);
        fontColumnHeader.setColor(IndexedColors.BLACK.getIndex());
        fontColumnHeader.setFontName("微软雅黑");
        cellStyleFontColumnHeader.setFont(fontColumnHeader);
        cellStyleFontColumnHeader.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        cellStyleFontColumnHeader.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyleFontColumnHeader.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontColumnHeader.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontColumnHeader.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontColumnHeader.setBorderRight(BorderStyle.THIN);//右边框
        

        Row rowTitle = sheet.createRow(0);
        Cell rowTitleCell1 = rowTitle.createCell(0);
        rowTitleCell1.setCellValue(jhYear + "年志愿方案-"+sf+"-"+xm+"-"+pc);
        
        CellStyle cellStyle = workbook.createCellStyle();
        CreationHelper creationHelper = workbook.getCreationHelper();
        cellStyle.setDataFormat(creationHelper.createDataFormat().getFormat("text")); // 对于文本，使用"text"格式以保持文本格式不变
        cellStyle.setAlignment(HorizontalAlignment.CENTER); // 设置水平居中对齐
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直居中对齐（如果需要）
        fontBlueBig.setFontName("微软雅黑");
        cellStyle.setFont(fontBlueBig);
        rowTitleCell1.setCellStyle(cellStyle); // 应用样式到单元格
        
        CellRangeAddress deviceCellRangeTitle = new CellRangeAddress(0, 0, 0, 12);
		sheet.addMergedRegion(deviceCellRangeTitle);
		
		
        //必须设置 否则背景色不生效
        cellStyleFontBlueBig.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        Row rowSubTitle = sheet.createRow(1);
        
        for(int i=0;i<2;i++) {
	        Cell rowSubTitleCell1 = rowSubTitle.createCell(i);
	        rowSubTitleCell1.setCellValue("报考批次");
	        rowSubTitleCell1.setCellStyle(cellStyleFontBlack14);
        }
        for(int i=2;i<3;i++) {
	        Cell rowSubTitleCell1 = rowSubTitle.createCell(i);
	        rowSubTitleCell1.setCellValue(pc);
	        rowSubTitleCell1.setCellStyle(cellStyleFontThinBlack14);
        }
        
        for(int i=3;i<5;i++) {
	        Cell rowSubTitleCell1 = rowSubTitle.createCell(i);
	        rowSubTitleCell1.setCellValue("选科组合");
	        rowSubTitleCell1.setCellStyle(cellStyleFontBlack14);
        }
        for(int i=5;i<6;i++) {
	        Cell rowSubTitleCell1 = rowSubTitle.createCell(i);
	        rowSubTitleCell1.setCellValue(Tools.viewXK(xk));
	        rowSubTitleCell1.setCellStyle(cellStyleFontThinBlack14);
        }
        for(int i=6;i<8;i++) {
        	Cell rowSubTitleCell1 = rowSubTitle.createCell(i);
	        rowSubTitleCell1.setCellValue("成绩");
	        rowSubTitleCell1.setCellStyle(cellStyleFontBlack14);
        }
        for(int i=8;i<10;i++) {
        	Cell rowSubTitleCell1 = rowSubTitle.createCell(i);
	        rowSubTitleCell1.setCellValue(score+"/"+wc);
	        rowSubTitleCell1.setCellStyle(cellStyleFontThinBlack14);
        }

        CellRangeAddress deviceCellRangeSubTitle1 = new CellRangeAddress(1, 1, 0, 1);
		sheet.addMergedRegion(deviceCellRangeSubTitle1);
		
		CellRangeAddress deviceCellRangeSubTitle2 = new CellRangeAddress(1, 1, 3, 4);
		sheet.addMergedRegion(deviceCellRangeSubTitle2);
		
		CellRangeAddress deviceCellRangeSubTitle3 = new CellRangeAddress(1, 1, 6, 7);
		sheet.addMergedRegion(deviceCellRangeSubTitle3);
		
		CellRangeAddress deviceCellRangeSubTitle4 = new CellRangeAddress(1, 1, 8, 12);
		sheet.addMergedRegion(deviceCellRangeSubTitle4);
 
		// 创建一个行，并在其中创建一个单元格
		Row rowOne = sheet.createRow(2);
		Cell rowOneCell1 = rowOne.createCell(0);
		rowOneCell1.setCellValue("志愿顺序");
		rowOneCell1.setCellStyle(cellStyleFontColumnHeader);

		Cell rowOneCell2 = rowOne.createCell(1);
		rowOneCell2.setCellValue("院校代码");
		rowOneCell2.setCellStyle(cellStyleFontColumnHeader);

		Cell rowOneCell3 = rowOne.createCell(2);
		rowOneCell3.setCellValue("院校名称");
		rowOneCell3.setCellStyle(cellStyleFontColumnHeader);

		Cell rowOneCell4 = rowOne.createCell(3);
		rowOneCell4.setCellValue("院校层次");
		rowOneCell4.setCellStyle(cellStyleFontColumnHeader);

		Cell rowOneCell5 = rowOne.createCell(4);
		rowOneCell5.setCellValue("专业顺序");
		rowOneCell5.setCellStyle(cellStyleFontColumnHeader);

		Cell rowOneCell6 = rowOne.createCell(5);
		rowOneCell6.setCellValue("专业代码");
		rowOneCell6.setCellStyle(cellStyleFontColumnHeader);

		// 修正重复的 rowOneCell7，改为 rowOneCell7 和 rowOneCell8
		Cell rowOneCell7 = rowOne.createCell(6);
		rowOneCell7.setCellValue("计划人数");
		rowOneCell7.setCellStyle(cellStyleFontColumnHeader);

		Cell rowOneCell8 = rowOne.createCell(7);
		rowOneCell8.setCellValue("专业名称");
		rowOneCell8.setCellStyle(cellStyleFontColumnHeader);

		Cell rowOneCell9 = rowOne.createCell(8);
		rowOneCell9.setCellValue(String.valueOf(jhYear - 1) + "年最低分(位次)/录取人数");
		rowOneCell9.setCellStyle(cellStyleFontColumnHeader);

		Cell rowOneCell10 = rowOne.createCell(9);
		rowOneCell10.setCellValue(String.valueOf(jhYear - 2) + "年最低分(位次)/录取人数");
		rowOneCell10.setCellStyle(cellStyleFontColumnHeader);

		Cell rowOneCell11 = rowOne.createCell(10);
		rowOneCell11.setCellValue(String.valueOf(jhYear - 3) + "年最低分(位次)/录取人数");
		rowOneCell11.setCellStyle(cellStyleFontColumnHeader);

		Cell rowOneCell12 = rowOne.createCell(11);
		rowOneCell12.setCellValue("专业排名(等级或近似专业)");
		rowOneCell12.setCellStyle(cellStyleFontColumnHeader);
		
		Cell rowOneCell13 = rowOne.createCell(12);
		rowOneCell13.setCellValue("学费");
		rowOneCell13.setCellStyle(cellStyleFontColumnHeader);
        
        HashMap<Integer, Integer> MAP_YX_ZYZ_SPAN = new HashMap<>(); 
        
        for(int i= 0; i < dataList.size(); i++) {
        	LhyForm bean = dataList.get(i);
        	if("重庆贵州河北山东浙江辽宁青海C1G4H2L1S1Z1Q1".indexOf(sf) == -1) {
        		MAP_YX_ZYZ_SPAN.put(bean.getSeq_no_yx(), bean.getSeq_no_zy());
        	}
        	
        	YfJHBean yfJHBean = bean.getYfJHBean();
        	yfJHBean = yfJHBean == null ? new YfJHBean() : yfJHBean;
        	
	        Row row = sheet.createRow(i+3);
	        Cell cell1 = row.createCell(0);
	        cell1.setCellValue(bean.getSeq_no_yx());
	        cell1.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        
	        Cell cell2 = row.createCell(1);
	        cell2.setCellValue(bean.getYxdm());
	        cell2.setCellStyle(cellStyleFontHighLight);
	        
	        Cell cell3 = row.createCell(2);
	        cell3.setCellValue(bean.getExt_yxmc());
	        cell3.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        
	        ZyzdUniversityBean CACHE_UNIV = ZyzdCache.getUniversity(yfJHBean.getYxmc_org());
	        CACHE_UNIV = CACHE_UNIV == null ? new ZyzdUniversityBean(): CACHE_UNIV;
	        
	        String displayYxInfoB = "";
	        String is985 = CACHE_UNIV == null ? null : CACHE_UNIV.getIs985();
			String is211 = CACHE_UNIV == null ? null : CACHE_UNIV.getIs211();
			String issyl = CACHE_UNIV == null ? null : CACHE_UNIV.getIssyl();
			if(Tools.isEmpty(is985) && Tools.isEmpty(is211) && Tools.isEmpty(issyl)){
				
			}else{
				if(!Tools.isEmpty(is985)){
					displayYxInfoB = "985";
				}else if(!Tools.isEmpty(is211)){
					displayYxInfoB = "211";
				}else{
					displayYxInfoB = "双一流";
				}
			}
	         
	        Cell cell4 = row.createCell(3);
	        if(Tools.isEmpty(displayYxInfoB)) {
	        	cell4.setCellValue(Tools.view(CACHE_UNIV.getInd_nature()));
	        }else {
	        	cell4.setCellValue(Tools.view(CACHE_UNIV.getInd_nature()) + ", " + Tools.view(displayYxInfoB));
	        }
	        cell4.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);

	        // 修正重复的 cell4，改为 cell5
	        Cell cell5 = row.createCell(4);
	        cell5.setCellValue(bean.getSeq_no_zy());
	        cell5.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);

	        Cell cell6 = row.createCell(5);
	        cell6.setCellValue(yfJHBean.getZydm());
	        cell6.setCellStyle(cellStyleFontHighLight);

	        Cell cell7 = row.createCell(6);
	        cell7.setCellValue(yfJHBean.getJhs());
	        cell7.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);

	        Cell cell8 = row.createCell(7);
	        cell8.setCellValue(yfJHBean.getZymc());
	        cell8.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);

	        Cell cell9 = row.createCell(8);
	        cell9.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        if (yfJHBean.getZdf_a() < 10) {
	            cell9.setCellValue("-");
	        } else {
	            cell9.setCellValue(yfJHBean.getZdf_a() + "(" + yfJHBean.getZdfwc_a() + ")" + " / " + Tools.getLqrs(yfJHBean.getJhs_a(), yfJHBean.getLqrs_a()));
	        }

	        Cell cell10 = row.createCell(9);
	        cell10.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        if (yfJHBean.getZdf_b() < 10) {
	            cell10.setCellValue("-");
	        } else {
	            cell10.setCellValue(yfJHBean.getZdf_b() + "(" + yfJHBean.getZdfwc_b() + ")" + " / " + Tools.getLqrs(yfJHBean.getJhs_b(), yfJHBean.getLqrs_b()));
	        }

	        Cell cell11 = row.createCell(10);
	        cell11.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        if (yfJHBean.getZdf_c() < 10) {
	            cell11.setCellValue("-");
	        } else {
	            cell11.setCellValue(yfJHBean.getZdf_c() + "(" + yfJHBean.getZdfwc_c() + ")" + " / " + Tools.getLqrs(yfJHBean.getJhs_c(), yfJHBean.getLqrs_c()));
	        }

	        Cell cell12 = row.createCell(11);
	        cell12.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        if (Tools.isEmpty(bean.getExt_ranking())) {
	            cell12.setCellValue("-");
	        } else {
	            cell12.setCellValue(bean.getExt_ranking() + "(" + bean.getExt_level() + ")");
	        }
	        
	        Cell cell13 = row.createCell(12);
	        cell13.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        if (Tools.isEmpty(yfJHBean.getFee())) {
	        	cell13.setCellValue("-");
	        } else {
	        	cell13.setCellValue(yfJHBean.getFee()); 
	        }
        }
        
        int startIndex = 2;
        Iterator<Integer> its = MAP_YX_ZYZ_SPAN.keySet().iterator();
        while(its.hasNext()) {
        	int form_no = its.next();
        	int zy_count = MAP_YX_ZYZ_SPAN.get(form_no);
        	if(zy_count > 1) {
        		CellRangeAddress deviceCellRange0 = new CellRangeAddress(startIndex + 1, startIndex + zy_count, 0, 0);
        		sheet.addMergedRegion(deviceCellRange0);
        		CellRangeAddress deviceCellRange1 = new CellRangeAddress(startIndex + 1, startIndex + zy_count, 1, 1);
        		sheet.addMergedRegion(deviceCellRange1);
        		CellRangeAddress deviceCellRange2 = new CellRangeAddress(startIndex + 1, startIndex + zy_count, 2, 2);
        		sheet.addMergedRegion(deviceCellRange2);
        	}
            startIndex += zy_count;
        }
        

        if(existYxAllDataList.size() > 0) {
        	createExistYxDataAll2026(workbook, jhYear, existYxAllDataList);
        }
 
        // 写出到文件
        try{
        	FileOutputStream fileOut = new FileOutputStream(filePathAndName);
            workbook.write(fileOut);
            workbook.close();
        } catch (Exception e){
        	e.printStackTrace();
        }finally {
        	try {
            	workbook.close();
            } catch (IOException e){
                e.printStackTrace();
            } 
        }
    }
	
	//导出诊断
	public static void createZdResultExcel(String filePathAndName, String userProvinceName, String firstRowTitle, List<ZyzdUniversityDataBean> dataList, List<ZyzdUniversityDataBean> dataList10More, List<ZyzdUniversityDataBean> dataList20More, List<ZyzdUniversityDataBean> dataList30More) {
		// 创建一个新的工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        
        CellStyle cellStyleFontColumnHeader = workbook.createCellStyle();
        cellStyleFontColumnHeader.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontColumnHeader = workbook.createFont();
        fontColumnHeader.setFontHeightInPoints((short) 12);
        fontColumnHeader.setBold(true);
        fontColumnHeader.setColor(IndexedColors.BLACK.getIndex());
        fontColumnHeader.setFontName("微软雅黑");
        cellStyleFontColumnHeader.setFont(fontColumnHeader);
        cellStyleFontColumnHeader.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        cellStyleFontColumnHeader.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyleFontColumnHeader.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontColumnHeader.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontColumnHeader.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontColumnHeader.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontNormal1 = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontNormal1.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontNormal = workbook.createFont();
        fontNormal.setFontHeightInPoints((short) 10);
        fontNormal.setBold(false);
        fontNormal.setColor(IndexedColors.BLACK.getIndex());
        fontNormal.setFontName("微软雅黑");
        cellStyleFontNormal1.setFont(fontNormal);
        cellStyleFontNormal1.setFillForegroundColor(IndexedColors.LEMON_CHIFFON.getIndex());
        cellStyleFontNormal1.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontNormal1.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontNormal1.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontNormal1.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontNormal2 = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontNormal2.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontNorma2 = workbook.createFont();
        fontNorma2.setFontHeightInPoints((short) 10);
        fontNorma2.setBold(true);
        fontNorma2.setColor(IndexedColors.BLUE.getIndex());
        fontNorma2.setFontName("微软雅黑");
        cellStyleFontNormal2.setFont(fontNorma2);
        cellStyleFontNormal2.setFillForegroundColor(IndexedColors.LEMON_CHIFFON.getIndex());
        cellStyleFontNormal2.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontNormal2.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontNormal2.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontNormal2.setBorderRight(BorderStyle.THIN);//右边框
        
        // 创建一个工作表(当前可上)
        createZDSheet(workbook, cellStyleFontColumnHeader, cellStyleFontNormal1, cellStyleFontNormal2, "当前可上", userProvinceName, firstRowTitle, dataList);
        // 创建一个工作表(多考10分)
        if(dataList10More.size() > 0) {
        	createZDSheet(workbook, cellStyleFontColumnHeader, cellStyleFontNormal1, cellStyleFontNormal2, "多考10分", userProvinceName, firstRowTitle, dataList10More);
        }
        // 创建一个工作表(多考20分)
        if(dataList20More.size() > 0) {
        	createZDSheet(workbook, cellStyleFontColumnHeader, cellStyleFontNormal1, cellStyleFontNormal2, "多考20分", userProvinceName, firstRowTitle, dataList20More);
        }
        // 创建一个工作表(多考30分)
        if(dataList30More.size() > 0) {
        	createZDSheet(workbook, cellStyleFontColumnHeader, cellStyleFontNormal1, cellStyleFontNormal2, "多考30分", userProvinceName, firstRowTitle, dataList30More);
        }
        
        
 
        // 写出到文件
        try{
        	FileOutputStream fileOut = new FileOutputStream(filePathAndName);
            workbook.write(fileOut);
            workbook.close();
        } catch (Exception e){
        	e.printStackTrace();
        }finally {
        	try {
            	workbook.close();
            } catch (IOException e){
                e.printStackTrace();
            } 
        }
    }
	
	private static void createZDSheet(XSSFWorkbook workbook, CellStyle cellStyleFontColumnHeader, CellStyle cellStyleFontNormal1, CellStyle cellStyleFontNormal2, String tabName, String userProvinceName, String firstRowTitle, List<ZyzdUniversityDataBean> dataList) {
		// 创建一个工作表
        Sheet sheet = workbook.createSheet("换算方案("+tabName+")");
        sheet.setColumnWidth(0, 30*256);
        sheet.setColumnWidth(1, 10*256);
        sheet.setColumnWidth(2, 10*256);
        sheet.setColumnWidth(3, 10*256);
        sheet.setColumnWidth(4, 15*256);
        sheet.setColumnWidth(5, 15*256); 
        sheet.setColumnWidth(6, 15*256);
 
        if(dataList.size() == 0) {
        	return;
        }
        
        
        Row rowTitle = sheet.createRow(0);
        Cell rowTitleCell1 = rowTitle.createCell(0);
        rowTitleCell1.setCellValue(firstRowTitle+" 的换算方案("+tabName+")");
        CellStyle cellStyle = workbook.createCellStyle();
        CreationHelper creationHelper = workbook.getCreationHelper();
        cellStyle.setDataFormat(creationHelper.createDataFormat().getFormat("text")); // 对于文本，使用"text"格式以保持文本格式不变
        cellStyle.setAlignment(HorizontalAlignment.CENTER); // 设置水平居中对齐
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直居中对齐（如果需要）
        Font fontBlueBig = workbook.createFont();
        fontBlueBig.setFontHeightInPoints((short) 14);
        fontBlueBig.setBold(true);
        fontBlueBig.setColor(IndexedColors.ORANGE.getIndex());
        fontBlueBig.setFontName("微软雅黑");
        cellStyle.setFont(fontBlueBig);
        rowTitleCell1.setCellStyle(cellStyle); // 应用样式到单元格
        CellRangeAddress deviceCellRangeTitle = new CellRangeAddress(0, 0, 0, 6);
		sheet.addMergedRegion(deviceCellRangeTitle);
        
        ZyzdUniversityDataBean first = dataList.get(0);
        
        // 创建一个行，并在其中创建一个单元格
        Row rowOne = sheet.createRow(1);
		Cell rowOneCell1 = rowOne.createCell(0);
		rowOneCell1.setCellValue("院校名称"); 
		rowOneCell1.setCellStyle(cellStyleFontColumnHeader);
		
		Cell rowOneCell2 = rowOne.createCell(1);
		rowOneCell2.setCellValue("院校代码");
		rowOneCell2.setCellStyle(cellStyleFontColumnHeader);
		
        Cell rowOneCell3 = rowOne.createCell(2);
        rowOneCell3.setCellValue("院校性质");
		rowOneCell3.setCellStyle(cellStyleFontColumnHeader);
		
        Cell rowOneCell4 = rowOne.createCell(3);
        rowOneCell4.setCellValue("院校层次");
		rowOneCell4.setCellStyle(cellStyleFontColumnHeader);
		
        Cell rowOneCell5 = rowOne.createCell(4);
        rowOneCell5.setCellValue("所属地区");
		rowOneCell5.setCellStyle(cellStyleFontColumnHeader);
		
        Cell rowOneCell6 = rowOne.createCell(5);
        rowOneCell6.setCellValue(first.getNf() +"最低分");
		rowOneCell6.setCellStyle(cellStyleFontColumnHeader);
		
        Cell rowOneCell7 = rowOne.createCell(6);
        rowOneCell7.setCellValue(first.getNf() +"最低位");
		rowOneCell7.setCellStyle(cellStyleFontColumnHeader);
        
        for(int i= 0; i < dataList.size(); i++) {
        	ZyzdUniversityDataBean bean = dataList.get(i);
        	
        	ZyzdUniversityBean CACHE_UNIV = ZyzdCache.getUniversity(bean.getYxmc_org());
        	if(CACHE_UNIV == null){
        		CACHE_UNIV = new ZyzdUniversityBean();
        	}
        	
        	String yxdm = bean.getYxdm();
        	String zyz = bean.getZyz();
        	String displayYxInfoA = "", displayYxInfoB = "";;
        	if(Tools.isEmpty(yxdm) && Tools.isEmpty(zyz)){
        		
        	}else{
        		if(!Tools.isEmpty(yxdm)){
        			displayYxInfoA = yxdm;
        		}else{
        			displayYxInfoA = zyz;
        		}
        	}
        	
        	
        	String is985 = CACHE_UNIV == null ? null : CACHE_UNIV.getIs985();
        	String is211 = CACHE_UNIV == null ? null : CACHE_UNIV.getIs211();
        	String issyl = CACHE_UNIV == null ? null : CACHE_UNIV.getIssyl();
        	if(Tools.isEmpty(is985) && Tools.isEmpty(is211) && Tools.isEmpty(issyl)){
        		
        	}else{
        		if(!Tools.isEmpty(is985)){
        			displayYxInfoB = "985";
        		}else if(!Tools.isEmpty(is211)){
        			displayYxInfoB = "211";
        		}else{
        			displayYxInfoB = "双一流";
        		}
        	}
        	
        	String yxsf = CACHE_UNIV.getPosition_sf();
        	String yxcs = CACHE_UNIV.getPosition_cs();
        	
	        Row row = sheet.createRow(i + 2);
	        Cell cell1 = row.createCell(0);
	        cell1.setCellStyle(cellStyleFontNormal1);
	        cell1.setCellValue(bean.getYxmc());
	        
	        Cell cell2 = row.createCell(1);
	        cell2.setCellStyle(cellStyleFontNormal1);
	        cell2.setCellValue(bean.getYxdm());
	        
	        Cell cell3 = row.createCell(2);
	        cell3.setCellStyle(cellStyleFontNormal1);
	        cell3.setCellValue(bean.getInd_nature());
	        
	        Cell cell4 = row.createCell(3);
	        cell4.setCellStyle(cellStyleFontNormal1);
	        cell4.setCellValue(displayYxInfoB);
	        
	        Cell cell5 = row.createCell(4);
	        if(userProvinceName.equals(yxsf)) {
	        	cell5.setCellStyle(cellStyleFontNormal2);
	        }else {
	        	cell5.setCellStyle(cellStyleFontNormal1);
	        }
	        cell5.setCellValue(Tools.view(yxsf) + (Tools.isEmpty(yxcs)?"":("."+yxcs)));
	        Cell cell6 = row.createCell(5);
	        cell6.setCellStyle(cellStyleFontNormal1);
	        cell6.setCellValue(bean.getZdf());
	        Cell cell7 = row.createCell(6);
	        cell7.setCellStyle(cellStyleFontNormal1);
	        cell7.setCellValue(bean.getZdfwc());
        }
	}
	
	private static void createExistYxDataAll(XSSFWorkbook workbook, int jhYear, List<JHBean> existYxAllDataList) {
		Sheet sheet = workbook.createSheet("志愿表涉及院校完整数据");
        sheet.setColumnWidth(0, 10*256);
        sheet.setColumnWidth(1, 25*256);
        sheet.setColumnWidth(2, 15*256);
        sheet.setColumnWidth(3, 10*256);
        sheet.setColumnWidth(4, 10*256);
        sheet.setColumnWidth(5, 10*256);
        sheet.setColumnWidth(6, 30*256);
        sheet.setColumnWidth(7, 25*256);
        sheet.setColumnWidth(8, 25*256);
        sheet.setColumnWidth(9, 25*256);
        
        CellStyle cellStyleFontNormal2 = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontNormal2.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontNormal2 = workbook.createFont();
        fontNormal2.setFontHeightInPoints((short) 10);
        fontNormal2.setBold(false);
        fontNormal2.setColor(IndexedColors.BLACK.getIndex());
        fontNormal2.setFontName("微软雅黑");
        cellStyleFontNormal2.setFont(fontNormal2);
        cellStyleFontNormal2.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontNormal2.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontNormal2.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontNormal2.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontNormal1 = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontNormal1.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontNormal = workbook.createFont();
        fontNormal.setFontHeightInPoints((short) 10);
        fontNormal.setBold(false);
        fontNormal.setColor(IndexedColors.BLACK.getIndex());
        fontNormal.setFontName("微软雅黑");
        cellStyleFontNormal1.setFont(fontNormal);
        cellStyleFontNormal1.setFillForegroundColor(IndexedColors.LEMON_CHIFFON.getIndex());
        cellStyleFontNormal1.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontNormal1.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontNormal1.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontNormal1.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontColumnHeader = workbook.createCellStyle();
        cellStyleFontColumnHeader.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontColumnHeader = workbook.createFont();
        fontColumnHeader.setFontHeightInPoints((short) 12);
        fontColumnHeader.setBold(true);
        fontColumnHeader.setColor(IndexedColors.BLACK.getIndex());
        fontColumnHeader.setFontName("微软雅黑");
        cellStyleFontColumnHeader.setFont(fontColumnHeader);
        cellStyleFontColumnHeader.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        cellStyleFontColumnHeader.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyleFontColumnHeader.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontColumnHeader.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontColumnHeader.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontColumnHeader.setBorderRight(BorderStyle.THIN);//右边框
        
        
        // 创建一个行，并在其中创建一个单元格
        Row rowOne = sheet.createRow(0);
        Cell rowOneCell1 = rowOne.createCell(0);
		rowOneCell1.setCellValue("院校代码");
		rowOneCell1.setCellStyle(cellStyleFontColumnHeader);
		
        Cell rowOneCell2 = rowOne.createCell(1);
        rowOneCell2.setCellValue("院校名称");
        rowOneCell2.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell3 = rowOne.createCell(2);
        rowOneCell3.setCellValue("所属区域");
        rowOneCell3.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell4 = rowOne.createCell(3);
        rowOneCell4.setCellValue("专业组");
        rowOneCell4.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell5 = rowOne.createCell(4);
        rowOneCell5.setCellValue("选科");
        rowOneCell5.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell6 = rowOne.createCell(5);
        rowOneCell6.setCellValue("专业代码");
        rowOneCell6.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell7 = rowOne.createCell(6);
        rowOneCell7.setCellValue("专业名称");
        rowOneCell7.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell8 = rowOne.createCell(7);
        rowOneCell8.setCellValue(String.valueOf(jhYear-1)+"年最低分");
        rowOneCell8.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell9 = rowOne.createCell(8);
        rowOneCell9.setCellValue(String.valueOf(jhYear-2)+"年最低分");
        rowOneCell9.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell10 = rowOne.createCell(9);
        rowOneCell10.setCellValue(String.valueOf(jhYear-3)+"年最低分");
        rowOneCell10.setCellStyle(cellStyleFontColumnHeader);
        
        int index = 0;
        HashMap<String, Integer> MAPX = new HashMap<>();
        for(int i= 0; i < existYxAllDataList.size(); i++) {
        	JHBean bean = existYxAllDataList.get(i);
        	if(!MAPX.containsKey(bean.getYxmc())) {
        		MAPX.put(bean.getYxmc(), index++);
        	}
        }
        
        for(int i= 0; i < existYxAllDataList.size(); i++) {
        	JHBean bean = existYxAllDataList.get(i);
        	CellStyle tempCell = cellStyleFontNormal1;
        	
        	int kk = MAPX.get(bean.getYxmc());
        	
        	if(kk % 2 == 0) {
        		tempCell = cellStyleFontNormal2;
        	}else {
        		tempCell = cellStyleFontNormal1;
        	}
        	
	        Row row = sheet.createRow(i+1);
	        Cell cell1 = row.createCell(0);
	        cell1.setCellValue(bean.getYxdm());
	        cell1.setCellStyle(tempCell);
	        
	        Cell cell2 = row.createCell(1);
	        cell2.setCellValue(bean.getYxmc());
	        cell2.setCellStyle(tempCell);
	        
	        Cell cell3 = row.createCell(2);
	        if(Tools.isEmpty(bean.getYxcs())) {
	        	cell3.setCellValue(bean.getYxsf());
	        }else {
	        	cell3.setCellValue(bean.getYxsf()+"."+bean.getYxcs());
	        }
	        cell3.setCellStyle(tempCell);
	        
	        Cell cell4 = row.createCell(3);
	        cell4.setCellValue(bean.getZyz());
	        cell4.setCellStyle(tempCell);
	        
	        Cell cell5 = row.createCell(4);
	        cell5.setCellValue(bean.getZx());
	        cell5.setCellStyle(tempCell);

	        Cell cell6 = row.createCell(5);
	        cell6.setCellValue(bean.getZydm());
	        cell6.setCellStyle(tempCell);
	        
	        Cell cell7 = row.createCell(6);
	        cell7.setCellValue(bean.getZymc());
	        cell7.setCellStyle(tempCell);
	        
	        Cell cell8 = row.createCell(7);
	        cell8.setCellStyle(tempCell);
	        if(Tools.getInt(bean.getZdf_a()) < 10) {
	        	cell8.setCellValue("-");
	        }else {
	        	cell8.setCellValue(bean.getZdf_a()+"("+bean.getZdfwc_a()+")");
	        }
	        Cell cell9 = row.createCell(8);
	        cell9.setCellStyle(tempCell);
	        
	        if(Tools.getInt(bean.getZdf_b()) < 10) {
	        	cell9.setCellValue("-");
	        }else {
	        	cell9.setCellValue(bean.getZdf_b()+"("+bean.getZdfwc_b()+")");
	        }
	        
	        Cell cell10 = row.createCell(9);
	        cell10.setCellStyle(tempCell);
	        
	        if(Tools.getInt(bean.getZdf_c()) < 10) {
	        	cell10.setCellValue("-");
	        }else {
	        	cell10.setCellValue(bean.getZdf_c()+"("+bean.getZdfwc_c()+")");
	        }
        }
    }
	
	private static void createExistYxDataAll2026(XSSFWorkbook workbook, int jhYear, List<YfJHBean> existYxAllDataList) {
		Sheet sheet = workbook.createSheet("志愿表涉及院校完整数据");
        sheet.setColumnWidth(0, 10*256);
        sheet.setColumnWidth(1, 25*256);
        sheet.setColumnWidth(2, 15*256);
        sheet.setColumnWidth(3, 10*256);
        sheet.setColumnWidth(4, 10*256);
        sheet.setColumnWidth(5, 10*256);
        sheet.setColumnWidth(6, 30*256);
        sheet.setColumnWidth(7, 25*256);
        sheet.setColumnWidth(8, 25*256);
        sheet.setColumnWidth(9, 25*256);
        
        CellStyle cellStyleFontNormal2 = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontNormal2.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontNormal2 = workbook.createFont();
        fontNormal2.setFontHeightInPoints((short) 10);
        fontNormal2.setBold(false);
        fontNormal2.setColor(IndexedColors.BLACK.getIndex());
        fontNormal2.setFontName("微软雅黑");
        cellStyleFontNormal2.setFont(fontNormal2);
        cellStyleFontNormal2.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontNormal2.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontNormal2.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontNormal2.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontNormal1 = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontNormal1.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontNormal = workbook.createFont();
        fontNormal.setFontHeightInPoints((short) 10);
        fontNormal.setBold(false);
        fontNormal.setColor(IndexedColors.BLACK.getIndex());
        fontNormal.setFontName("微软雅黑");
        cellStyleFontNormal1.setFont(fontNormal);
        cellStyleFontNormal1.setFillForegroundColor(IndexedColors.LEMON_CHIFFON.getIndex());
        cellStyleFontNormal1.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontNormal1.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontNormal1.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontNormal1.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontColumnHeader = workbook.createCellStyle();
        cellStyleFontColumnHeader.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontColumnHeader = workbook.createFont();
        fontColumnHeader.setFontHeightInPoints((short) 12);
        fontColumnHeader.setBold(true);
        fontColumnHeader.setColor(IndexedColors.BLACK.getIndex());
        fontColumnHeader.setFontName("微软雅黑");
        cellStyleFontColumnHeader.setFont(fontColumnHeader);
        cellStyleFontColumnHeader.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        cellStyleFontColumnHeader.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyleFontColumnHeader.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontColumnHeader.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontColumnHeader.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontColumnHeader.setBorderRight(BorderStyle.THIN);//右边框
        
        
        // 创建一个行，并在其中创建一个单元格
        Row rowOne = sheet.createRow(0);
        Cell rowOneCell1 = rowOne.createCell(0);
		rowOneCell1.setCellValue("院校代码");
		rowOneCell1.setCellStyle(cellStyleFontColumnHeader);
		
        Cell rowOneCell2 = rowOne.createCell(1);
        rowOneCell2.setCellValue("院校名称");
        rowOneCell2.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell3 = rowOne.createCell(2);
        rowOneCell3.setCellValue("所属区域");
        rowOneCell3.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell4 = rowOne.createCell(3);
        rowOneCell4.setCellValue("专业组");
        rowOneCell4.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell5 = rowOne.createCell(4);
        rowOneCell5.setCellValue("选科");
        rowOneCell5.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell6 = rowOne.createCell(5);
        rowOneCell6.setCellValue("专业代码");
        rowOneCell6.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell7 = rowOne.createCell(6);
        rowOneCell7.setCellValue("专业名称");
        rowOneCell7.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell8 = rowOne.createCell(7);
        rowOneCell8.setCellValue(String.valueOf(jhYear-1)+"年最低分");
        rowOneCell8.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell9 = rowOne.createCell(8);
        rowOneCell9.setCellValue(String.valueOf(jhYear-2)+"年最低分");
        rowOneCell9.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell10 = rowOne.createCell(9);
        rowOneCell10.setCellValue(String.valueOf(jhYear-3)+"年最低分");
        rowOneCell10.setCellStyle(cellStyleFontColumnHeader);
        
        int index = 0;
        HashMap<String, Integer> MAPX = new HashMap<>();
        for(int i= 0; i < existYxAllDataList.size(); i++) {
        	YfJHBean bean = existYxAllDataList.get(i);
        	if(!MAPX.containsKey(bean.getYxmc())) {
        		MAPX.put(bean.getYxmc(), index++);
        	}
        }
        
        for(int i= 0; i < existYxAllDataList.size(); i++) {
        	YfJHBean bean = existYxAllDataList.get(i);
        	CellStyle tempCell = cellStyleFontNormal1;
        	
        	int kk = MAPX.get(bean.getYxmc());
        	
        	if(kk % 2 == 0) {
        		tempCell = cellStyleFontNormal2;
        	}else {
        		tempCell = cellStyleFontNormal1;
        	}
        	
	        Row row = sheet.createRow(i+1);
	        Cell cell1 = row.createCell(0);
	        cell1.setCellValue(bean.getYxdm());
	        cell1.setCellStyle(tempCell);
	        
	        Cell cell2 = row.createCell(1);
	        cell2.setCellValue(bean.getYxmc());
	        cell2.setCellStyle(tempCell);
	        
	        Cell cell3 = row.createCell(2);
	        if(Tools.isEmpty(bean.getYxcs())) {
	        	cell3.setCellValue(bean.getYxsf());
	        }else {
	        	cell3.setCellValue(bean.getYxsf()+"."+bean.getYxcs());
	        }
	        cell3.setCellStyle(tempCell);
	        
	        Cell cell4 = row.createCell(3);
	        cell4.setCellValue(bean.getZyz());
	        cell4.setCellStyle(tempCell);
	        
	        Cell cell5 = row.createCell(4);
	        cell5.setCellValue(bean.getZx());
	        cell5.setCellStyle(tempCell);

	        Cell cell6 = row.createCell(5);
	        cell6.setCellValue(bean.getZydm());
	        cell6.setCellStyle(tempCell);
	        
	        Cell cell7 = row.createCell(6);
	        cell7.setCellValue(bean.getZymc());
	        cell7.setCellStyle(tempCell);
	        
	        Cell cell8 = row.createCell(7);
	        cell8.setCellStyle(tempCell);
	        if(bean.getZdf_a() < 10) {
	        	cell8.setCellValue("-");
	        }else {
	        	cell8.setCellValue(bean.getZdf_a()+"("+bean.getZdfwc_a()+")");
	        }
	        Cell cell9 = row.createCell(8);
	        cell9.setCellStyle(tempCell);
	        
	        if(bean.getZdf_b() < 10) {
	        	cell9.setCellValue("-");
	        }else {
	        	cell9.setCellValue(bean.getZdf_b()+"("+bean.getZdfwc_b()+")");
	        }
	        
	        Cell cell10 = row.createCell(9);
	        cell10.setCellStyle(tempCell);
	        
	        if(bean.getZdf_c() < 10) {
	        	cell10.setCellValue("-");
	        }else {
	        	cell10.setCellValue(bean.getZdf_c()+"("+bean.getZdfwc_c()+")");
	        }
        }
    }
	
	
	public static void createForm96ReportExcel(int jhYear, String filePathAndName,  List<LhyForm> dataList) {
		// 创建一个新的工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        Tools.println(filePathAndName);
        // 创建一个工作表
        Sheet sheet = workbook.createSheet("志愿表");
 
        // 创建一个行，并在其中创建一个单元格
        Row rowOne = sheet.createRow(0);
		Cell rowOneCell1 = rowOne.createCell(0);
		rowOneCell1.setCellValue("志愿顺序");
        Cell rowOneCell2 = rowOne.createCell(1);
        rowOneCell2.setCellValue("院校名称");
        Cell rowOneCell3 = rowOne.createCell(2);
        rowOneCell3.setCellValue("排名");
        Cell rowOneCell4 = rowOne.createCell(3);
        rowOneCell4.setCellValue("专业名称");
        Cell rowOneCell5 = rowOne.createCell(4);
        rowOneCell5.setCellValue(String.valueOf(jhYear-1));
        Cell rowOneCell6 = rowOne.createCell(5);
        rowOneCell6.setCellValue(String.valueOf(jhYear-2));
        Cell rowOneCell7 = rowOne.createCell(6);
        rowOneCell7.setCellValue(String.valueOf(jhYear-3));
        
        for(int i= 0; i < dataList.size(); i++) {
        	LhyForm bean = dataList.get(i);
	        Row row = sheet.createRow(i+1);
	        Cell cell1 = row.createCell(0);
	        cell1.setCellValue(bean.getSeq_no_yx());
	        Cell cell2 = row.createCell(1);
	        cell2.setCellValue(bean.getYxmc());
	        Cell cell3 = row.createCell(2);
	        cell3.setCellValue(bean.getExt_ranking()+"("+bean.getExt_level()+")");
	        Cell cell4 = row.createCell(3);
	        cell4.setCellValue(bean.getZymc());
	        Cell cell5 = row.createCell(4);
	        cell5.setCellValue(bean.getZdf_a()+"("+bean.getZdfwc_a()+")");
	        Cell cell6 = row.createCell(5);
	        cell6.setCellValue(bean.getZdf_b()+"("+bean.getZdfwc_b()+")");
	        Cell cell7 = row.createCell(6);
	        cell7.setCellValue(bean.getZdf_c()+"("+bean.getZdfwc_c()+")");
        }
 
        // 写出到文件
        try{
        	FileOutputStream fileOut = new FileOutputStream(filePathAndName);
            workbook.write(fileOut);
            workbook.close();
        } catch (Exception e){
        	e.printStackTrace();
        }finally {
        	try {
            	workbook.close();
            } catch (IOException e){
                e.printStackTrace();
            } 
        }
    }

}
