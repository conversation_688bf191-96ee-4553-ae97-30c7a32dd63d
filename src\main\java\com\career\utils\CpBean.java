package com.career.utils;

import java.util.Comparator;

public class CpBean {
	
	private String CONST;
	private int cnt;

	public CpBean(String _CONST, int _cnt) {
		CONST = _CONST;
		cnt = _cnt;
	}

	public String getCONST() {
		return CONST;
	}


	public void setCONST(String cONST) {
		CONST = cONST;
	}


	public int getCnt() {
		return cnt;
	}


	public void setCnt(int cnt) {
		this.cnt = cnt;
	}


	class cmp implements Comparator<CpBean>{
	    public int compare(CpBean o1, CpBean o2){
	        return o2.cnt - o1.cnt;
	    }
	}
	
	
}
