package com.career.db.jhdao;

public class YfJHBean {
	public int id;
    public String sf;
    public int nf;
    public String pc;
    public String pc_code;
    public String pc_desc;
    public String pc_type;
    public String pc_remark;
    public String zyz;
    public String zyz_desc;
    public String yxdm;
    public String yxmc;
    public String yxbz;
    public String yxmc_org;
    public String zydm;
    public String zymc;
    public String zybz;
    public String zymc_org;
    public String zyml;
    public int jhs;
    public String fee;
    public String xz;
    public String xk;
    public String zx;
    public String xk_code;
    public String xk_code_org;
    public String xk_desc;
    public String znzy;
    public int zdfwc;
    public int znzdfwc;
    public int lqrs_a;
    public int lqrs_b;
    public int lqrs_c;
    public int jhs_a;
    public int jhs_b;
    public int jhs_c;
    public int zdf_a;
    public int zdf_b;
    public int zdf_c;
    public int zdfwc_a;
    public int zdfwc_b;
    public int zdfwc_c;
    public int pjf_a;
    public int pjf_b;
    public int pjf_c;
    public int pjfwc_a;
    public int pjfwc_b;
    public int pjfwc_c;
    public int qsf_a;
    public int qsf_b;
    public int qsf_c;
    public int qsf;
    public int ycwc;
    public String lqpc;
    public String ind_nature;
    public String ind_catg;
    public String yxsf;
    public String yxcs;
    public String yx_tags;
    public String yx_tags_all;
    public float cnt_grad;
    public int is_hz;
    public int is_first;
    public int znzys;
    public int znzyls;
    public int znjhs;
    public int is_bsyx;
    public int is_ybeb;
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getSf() {
		return sf;
	}
	public void setSf(String sf) {
		this.sf = sf;
	}
	public int getNf() {
		return nf;
	}
	public void setNf(int nf) {
		this.nf = nf;
	}
	public String getPc() {
		return pc;
	}
	public void setPc(String pc) {
		this.pc = pc;
	}
	public String getPc_code() {
		return pc_code;
	}
	public void setPc_code(String pc_code) {
		this.pc_code = pc_code;
	}
	public String getPc_desc() {
		return pc_desc;
	}
	public void setPc_desc(String pc_desc) {
		this.pc_desc = pc_desc;
	}
	public String getPc_type() {
		return pc_type;
	}
	public void setPc_type(String pc_type) {
		this.pc_type = pc_type;
	}
	public String getPc_remark() {
		return pc_remark;
	}
	public void setPc_remark(String pc_remark) {
		this.pc_remark = pc_remark;
	}
	public String getZyz() {
		return zyz;
	}
	public void setZyz(String zyz) {
		this.zyz = zyz;
	}
	public String getZyz_desc() {
		return zyz_desc;
	}
	public void setZyz_desc(String zyz_desc) {
		this.zyz_desc = zyz_desc;
	}
	public String getYxdm() {
		return yxdm;
	}
	public void setYxdm(String yxdm) {
		this.yxdm = yxdm;
	}
	public String getYxmc() {
		return yxmc;
	}
	public void setYxmc(String yxmc) {
		this.yxmc = yxmc;
	}
	public String getYxbz() {
		return yxbz;
	}
	public void setYxbz(String yxbz) {
		this.yxbz = yxbz;
	}
	public String getYxmc_org() {
		return yxmc_org;
	}
	public void setYxmc_org(String yxmc_org) {
		this.yxmc_org = yxmc_org;
	}
	public String getZydm() {
		return zydm;
	}
	public void setZydm(String zydm) {
		this.zydm = zydm;
	}
	public String getZymc() {
		return zymc;
	}
	public void setZymc(String zymc) {
		this.zymc = zymc;
	}
	public String getZybz() {
		return zybz;
	}
	public void setZybz(String zybz) {
		this.zybz = zybz;
	}
	public String getZymc_org() {
		return zymc_org;
	}
	public void setZymc_org(String zymc_org) {
		this.zymc_org = zymc_org;
	}
	public String getZyml() {
		return zyml;
	}
	public void setZyml(String zyml) {
		this.zyml = zyml;
	}
	public int getJhs() {
		return jhs;
	}
	public void setJhs(int jhs) {
		this.jhs = jhs;
	}
	public String getFee() {
		return fee;
	}
	public void setFee(String fee) {
		this.fee = fee;
	}
	public String getXz() {
		return xz;
	}
	public void setXz(String xz) {
		this.xz = xz;
	}
	public String getXk() {
		return xk;
	}
	public void setXk(String xk) {
		this.xk = xk;
	}
	public String getZx() {
		return zx;
	}
	public void setZx(String zx) {
		this.zx = zx;
	}
	public String getXk_code() {
		return xk_code;
	}
	public void setXk_code(String xk_code) {
		this.xk_code = xk_code;
	}
	public String getXk_code_org() {
		return xk_code_org;
	}
	public void setXk_code_org(String xk_code_org) {
		this.xk_code_org = xk_code_org;
	}
	public String getXk_desc() {
		return xk_desc;
	}
	public void setXk_desc(String xk_desc) {
		this.xk_desc = xk_desc;
	}
	public String getZnzy() {
		return znzy;
	}
	public void setZnzy(String znzy) {
		this.znzy = znzy;
	}
	public int getZdfwc() {
		return zdfwc;
	}
	public void setZdfwc(int zdfwc) {
		this.zdfwc = zdfwc;
	}
	public int getZnzdfwc() {
		return znzdfwc;
	}
	public void setZnzdfwc(int znzdfwc) {
		this.znzdfwc = znzdfwc;
	}
	public int getLqrs_a() {
		return lqrs_a;
	}
	public void setLqrs_a(int lqrs_a) {
		this.lqrs_a = lqrs_a;
	}
	public int getLqrs_b() {
		return lqrs_b;
	}
	public void setLqrs_b(int lqrs_b) {
		this.lqrs_b = lqrs_b;
	}
	public int getLqrs_c() {
		return lqrs_c;
	}
	public void setLqrs_c(int lqrs_c) {
		this.lqrs_c = lqrs_c;
	}
	public int getJhs_a() {
		return jhs_a;
	}
	public void setJhs_a(int jhs_a) {
		this.jhs_a = jhs_a;
	}
	public int getJhs_b() {
		return jhs_b;
	}
	public void setJhs_b(int jhs_b) {
		this.jhs_b = jhs_b;
	}
	public int getJhs_c() {
		return jhs_c;
	}
	public void setJhs_c(int jhs_c) {
		this.jhs_c = jhs_c;
	}
	public int getZdf_a() {
		return zdf_a;
	}
	public void setZdf_a(int zdf_a) {
		this.zdf_a = zdf_a;
	}
	public int getZdf_b() {
		return zdf_b;
	}
	public void setZdf_b(int zdf_b) {
		this.zdf_b = zdf_b;
	}
	public int getZdf_c() {
		return zdf_c;
	}
	public void setZdf_c(int zdf_c) {
		this.zdf_c = zdf_c;
	}
	public int getZdfwc_a() {
		return zdfwc_a;
	}
	public void setZdfwc_a(int zdfwc_a) {
		this.zdfwc_a = zdfwc_a;
	}
	public int getZdfwc_b() {
		return zdfwc_b;
	}
	public void setZdfwc_b(int zdfwc_b) {
		this.zdfwc_b = zdfwc_b;
	}
	public int getZdfwc_c() {
		return zdfwc_c;
	}
	public void setZdfwc_c(int zdfwc_c) {
		this.zdfwc_c = zdfwc_c;
	}
	public int getPjf_a() {
		return pjf_a;
	}
	public void setPjf_a(int pjf_a) {
		this.pjf_a = pjf_a;
	}
	public int getPjf_b() {
		return pjf_b;
	}
	public void setPjf_b(int pjf_b) {
		this.pjf_b = pjf_b;
	}
	public int getPjf_c() {
		return pjf_c;
	}
	public void setPjf_c(int pjf_c) {
		this.pjf_c = pjf_c;
	}
	public int getPjfwc_a() {
		return pjfwc_a;
	}
	public void setPjfwc_a(int pjfwc_a) {
		this.pjfwc_a = pjfwc_a;
	}
	public int getPjfwc_b() {
		return pjfwc_b;
	}
	public void setPjfwc_b(int pjfwc_b) {
		this.pjfwc_b = pjfwc_b;
	}
	public int getPjfwc_c() {
		return pjfwc_c;
	}
	public void setPjfwc_c(int pjfwc_c) {
		this.pjfwc_c = pjfwc_c;
	}
	public int getQsf_a() {
		return qsf_a;
	}
	public void setQsf_a(int qsf_a) {
		this.qsf_a = qsf_a;
	}
	public int getQsf_b() {
		return qsf_b;
	}
	public void setQsf_b(int qsf_b) {
		this.qsf_b = qsf_b;
	}
	public int getQsf_c() {
		return qsf_c;
	}
	public void setQsf_c(int qsf_c) {
		this.qsf_c = qsf_c;
	}
	public int getQsf() {
		return qsf;
	}
	public void setQsf(int qsf) {
		this.qsf = qsf;
	}
	public int getYcwc() {
		return ycwc;
	}
	public void setYcwc(int ycwc) {
		this.ycwc = ycwc;
	}
	public String getLqpc() {
		return lqpc;
	}
	public void setLqpc(String lqpc) {
		this.lqpc = lqpc;
	}
	public String getInd_nature() {
		return ind_nature;
	}
	public void setInd_nature(String ind_nature) {
		this.ind_nature = ind_nature;
	}
	public String getInd_catg() {
		return ind_catg;
	}
	public void setInd_catg(String ind_catg) {
		this.ind_catg = ind_catg;
	}
	public String getYxsf() {
		return yxsf;
	}
	public void setYxsf(String yxsf) {
		this.yxsf = yxsf;
	}
	public String getYxcs() {
		return yxcs;
	}
	public void setYxcs(String yxcs) {
		this.yxcs = yxcs;
	}
	public String getYx_tags() {
		return yx_tags;
	}
	public void setYx_tags(String yx_tags) {
		this.yx_tags = yx_tags;
	}
	public String getYx_tags_all() {
		return yx_tags_all;
	}
	public void setYx_tags_all(String yx_tags_all) {
		this.yx_tags_all = yx_tags_all;
	}
	public float getCnt_grad() {
		return cnt_grad;
	}
	public void setCnt_grad(float cnt_grad) {
		this.cnt_grad = cnt_grad;
	}
	public int getIs_hz() {
		return is_hz;
	}
	public void setIs_hz(int is_hz) {
		this.is_hz = is_hz;
	}
	public int getIs_first() {
		return is_first;
	}
	public void setIs_first(int is_first) {
		this.is_first = is_first;
	}
	public int getZnzys() {
		return znzys;
	}
	public void setZnzys(int znzys) {
		this.znzys = znzys;
	}
	public int getZnzyls() {
		return znzyls;
	}
	public void setZnzyls(int znzyls) {
		this.znzyls = znzyls;
	}
	public int getZnjhs() {
		return znjhs;
	}
	public void setZnjhs(int znjhs) {
		this.znjhs = znjhs;
	}
	public int getIs_bsyx() {
		return is_bsyx;
	}
	public void setIs_bsyx(int is_bsyx) {
		this.is_bsyx = is_bsyx;
	}
	public int getIs_ybeb() {
		return is_ybeb;
	}
	public void setIs_ybeb(int is_ybeb) {
		this.is_ybeb = is_ybeb;
	}
    
}
