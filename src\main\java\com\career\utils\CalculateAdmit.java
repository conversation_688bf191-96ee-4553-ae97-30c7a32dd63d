package com.career.utils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.career.db.JHBean;
import com.career.db.LhyCardSchoolStudent;
import com.career.db.LhyForm;
import com.career.db.LhyFormMain;
import com.career.db.LhyJDBC;
import com.career.db.LhySchoolStatisticsForm;
import com.career.db.ZyzdForm;
import com.career.db.ZyzdFormJDBC;
import com.career.db.ZyzdFormMain;
import com.career.db.ZyzdJDBC;
import com.career.db.ZyzdProvinceConfig;
import com.career.db.ZyzdProvincePcConfig;

public class CalculateAdmit {
	
	
	public static void main(String args[]) {
		ZyzdJDBC zyzdJDBC = new ZyzdJDBC(); 
		
		List<ZyzdFormMain> list = zyzdJDBC.getAllSQLLinkMakerFormMain(2025, "S5_SC");
		ZyzdProvinceConfig provinceConfig = ZyzdCache.getProvinceConfig("S5");
		for(ZyzdFormMain main : list) {
			LhyCardSchoolStudent student = zyzdJDBC.getLhyCardSchoolStudent(main.getC_id());
			if(student == null) {
				System.out.println("NOT FOUND: "+ main.getC_id());
				continue;
			}
			System.out.println("getLhy_c_id: "+ student.getLhy_c_id());
			getZyzdSchoolStatisticsForm(provinceConfig, main, student);
		}
	}
	
	
	/**
	 * 志愿卡录取结果，返回的是一个集合， 第一个是最优先录取的，依次类推
	 * @param provinceConfig
	 * @param zyzdFormMain
	 */
	public static void getZyzdSchoolStatisticsForm(ZyzdProvinceConfig provinceConfig, ZyzdFormMain zyzdFormMain, LhyCardSchoolStudent student) {
		ZyzdJDBC zyzdJDBC = new ZyzdJDBC(); 
		LhyJDBC lhyJDBC = new LhyJDBC(); 
		ZyzdFormJDBC formJDBC = new ZyzdFormJDBC(); 
		
		List<LhySchoolStatisticsForm> forms = new ArrayList<>();
		
		ZyzdProvincePcConfig zyzdProvincePcConfig = new ZyzdJDBC().getProvincePcConfigByParams(provinceConfig.getP_table_code(), zyzdFormMain.getPc_code(), zyzdFormMain.getPc());
		int FORM_CNT = provinceConfig.getCal_FormCnt(zyzdFormMain.getPc_code(), zyzdProvincePcConfig);
		int MAJOR_CNT = provinceConfig.getCal_MajorCnt(zyzdFormMain.getPc_code(), zyzdProvincePcConfig);
		String FORM_RULE = provinceConfig.getCal_FormRule(zyzdFormMain.getPc_code(), zyzdProvincePcConfig);
		int form_type = zyzdProvincePcConfig == null ? 1 : zyzdProvincePcConfig.getForm_type();  // 1-平行志愿，非1-顺序志愿 
		boolean is96 = provinceConfig.getForm_type() == 2;

		String[] RULE_STAGE = FORM_RULE.split(",");

		int STAGE_CNT_CHONG = 15;
		int STAGE_CNT_WEN = 20;
		int STAGE_CNT_BAO = 10;
		
		String xkCode = XKCombineUtils.getXKCodeByStudentSelection(zyzdFormMain.getScore_xk());
		List<ZyzdForm> formList = zyzdJDBC.getMakerForm(provinceConfig.getP_table_code(), zyzdFormMain.getBatch_id());
		
		
		LinkedHashMap<String, List<ZyzdForm>> groupLhyFormMap = new LinkedHashMap<>();

		HashSet<String> yxdmSets = new HashSet<>();
		HashSet<String> zyzSets = new HashSet<>();

		for(ZyzdForm bean : formList){
			groupLhyFormMap.computeIfAbsent(Tools.getTBUniqueGroupOnlyKey(bean, is96), k -> new ArrayList<>()).add(bean);
			if(!Tools.isEmpty(bean.getYxdm())){
				yxdmSets.add(bean.getYxdm());
			}
			
			if(!Tools.isEmpty(bean.getZyz())){
				zyzSets.add(bean.getZyz());
			}
		}


		LinkedHashMap<String, List<JHBean>> groupJhBeanFormMap = new LinkedHashMap<>(); //为了查询
		LinkedHashMap<String, JHBean> earchJhBeanFormMap = new LinkedHashMap<>(); //为了查询
		//查出来志愿表对应的院校+专业组完整信息
		List<JHBean> listMajorJh_all_matched = formJDBC.getZyBySelectedForm_YxdmAndZyz(provinceConfig.getLatest_year_jh(), provinceConfig.getP_table_code(), xkCode, zyzdFormMain.getPc(), zyzdFormMain.getPc_code(), yxdmSets, zyzSets);
		for(JHBean bean : listMajorJh_all_matched){
			earchJhBeanFormMap.put(Tools.getTBUniqueKey(bean, is96), bean);
			groupJhBeanFormMap.computeIfAbsent(Tools.getTBUniqueGroupOnlyKey(bean, is96), k -> new ArrayList<>()).add(bean);
		}
		
		
		List<AdmitBean> admitList = new ArrayList<>();
		//算录取，被谁录取
		for(Map.Entry<String, List<ZyzdForm>> entry : groupLhyFormMap.entrySet()) {
			List<ZyzdForm> tbFormList = entry.getValue();
			List<JHBean> jhBeanList = groupJhBeanFormMap.getOrDefault(entry.getKey(), new ArrayList<>());
			boolean isAdjustAdmit = true;
			AdmitBean admitBean = null;
			for(ZyzdForm zyzdForm : tbFormList){
				JHBean jHbean = earchJhBeanFormMap.getOrDefault(Tools.getTBUniqueKey(zyzdForm, is96), new JHBean());
				
				if(jHbean.getQsf() > 0 && (zyzdFormMain.getScore_cj() - jHbean.getQsf()) > 0){
					isAdjustAdmit = false;
					admitBean = new AdmitBean();
					admitBean.setZyzdForm(zyzdForm);
					admitBean.setAdjustAdmit(isAdjustAdmit);
					admitBean.setAdmitSeqNoYx(zyzdForm.getSeq_no_yx());
					admitBean.setAdmitSeqNoZy(zyzdForm.getSeq_no_zy());
					admitBean.setYxmc(zyzdForm.getYxmc());
					admitBean.setZymc(zyzdForm.getZymc_org());
					admitBean.setZyz(zyzdForm.getZyz());
					admitBean.setYxdm(zyzdForm.getYxdm());
					admitBean.setZydm(zyzdForm.getZydm());
					admitBean.setPc(jHbean.getPc());
					admitBean.setPc_code(jHbean.getPc_code());
					admitList.add(admitBean);
					break;
				}
			}
			ZyzdForm lastForm = tbFormList.get(tbFormList.size() - 1);
			if(isAdjustAdmit){
				for(JHBean jhBean : jhBeanList){
					//Tools.println(jhBeanList.size() + " - > ["+ entry.getKey()+"]" + jhBean.getYxmc() + " ["+jhBean.getZyz() +"]" +" . " + jhBean.getQsf());
					if(jhBean.getQsf() > 0 && (zyzdFormMain.getScore_cj() - jhBean.getQsf()) > 0){ // ADJUST
						admitBean = new AdmitBean();
						admitBean.setJhBean(jhBean);
						admitBean.setZyzdForm(lastForm);
						admitBean.setAdjustAdmit(isAdjustAdmit);
						admitBean.setAdmitSeqNoYx(lastForm.getSeq_no_yx());
						admitBean.setYxmc(jhBean.getYxmc());
						admitBean.setZymc(jhBean.getZymc_org());
						admitBean.setZyz(jhBean.getZyz());
						admitBean.setYxdm(jhBean.getYxdm());
						admitBean.setZydm(jhBean.getZydm());
						admitBean.setPc(jhBean.getPc());
						admitBean.setPc_code(jhBean.getPc_code());
						admitList.add(admitBean);
						break;
					}
				}
			}
			
			if(admitList.size() >= 1){
				LhySchoolStatisticsForm form1 = new LhySchoolStatisticsForm();
				AdmitBean admitBeanOK = admitList.get(0);
				
				
		        form1.setSchoolLhyCId(student.getLhy_c_parent_id());
		        form1.setClassLhyCId(student.getLhy_c_id());
		        form1.setClassName(student.getClass_name());
		        form1.setCId(student.getBase_c_id());
		        form1.setCStudentName(student.getStudent_name());
		        form1.setFormScoreCj(zyzdFormMain.getScore_cj());
		        form1.setFormScoreWc(zyzdFormMain.getScore_wc());
		        form1.setFormScoreXk(zyzdFormMain.getScore_xk());
		        form1.setPcCode(zyzdFormMain.getPc_code());
		        form1.setPc(zyzdFormMain.getPc());
		        form1.setCreateTm(new Timestamp(System.currentTimeMillis()));
		        form1.setRecordCreateTm(new Timestamp(System.currentTimeMillis()));
		        form1.setLqYxsf(null);
		        form1.setLqYxcs(null);
		        form1.setLqYxdm(admitBeanOK.getYxdm());
		        form1.setLqYxmc(admitBeanOK.getYxmc());
		        form1.setLqYxmcOrg(null);
		        form1.setLqZyz(admitBeanOK.getZyz());
		        form1.setLqZydm(admitBeanOK.getZydm());
		        form1.setLqZymc(admitBeanOK.getZymc());
		        form1.setLqZymcOrg(null);
		        form1.setIsAdjust(1);
		        form1.setWasteScoreCnt(5);

		        forms.add(form1);
				
				break;
			}
		}
		
		System.out.println("----------admitList---->" + admitList.size());
		for(LhySchoolStatisticsForm form1 : forms) {
			System.out.println(form1.getClassName()+","+form1.getFormScoreCj()+","+form1.getLqYxmc());
		}
		
		if(forms.size() > 0) {
			//填充LhySchoolStatisticsForm
			lhyJDBC.batchInsertLhySchoolStatisticsForm(forms);
		}else {
			LhySchoolStatisticsForm form1 = new LhySchoolStatisticsForm();
	        form1.setSchoolLhyCId(student.getLhy_c_id());
	        form1.setClassLhyCId(student.getLhy_c_id());
	        form1.setClassName(student.getClass_name());
	        form1.setCId(student.getBase_c_id());
	        form1.setCStudentName(student.getStudent_name());
	        form1.setFormScoreCj(zyzdFormMain.getScore_cj());
	        form1.setFormScoreWc(zyzdFormMain.getScore_wc());
	        form1.setFormScoreXk(zyzdFormMain.getScore_xk());
	        form1.setPcCode(zyzdFormMain.getPc_code());
	        form1.setPc(zyzdFormMain.getPc());
	        form1.setCreateTm(new Timestamp(System.currentTimeMillis()));
	        form1.setRecordCreateTm(new Timestamp(System.currentTimeMillis()));

	        form1.setIsAdjust(999);
	        form1.setWasteScoreCnt(999);
	        
	        forms.add(form1);

		}
	}
	
	/**
	 * 领航员录取结果，返回的是一个集合， 第一个是最优先录取的，依次类推
	 * @param provinceConfig
	 * @param lhyFormMain
	 * @return
	 */
	public static List<AdmitBean> getLhySchoolStatisticsForm(ZyzdProvinceConfig provinceConfig, LhyFormMain lhyFormMain) {
		LhyJDBC lhyJDBC = new LhyJDBC(); 
		ZyzdFormJDBC formJDBC = new ZyzdFormJDBC(); 
		
		List<LhySchoolStatisticsForm> forms = new ArrayList<>();
		
		ZyzdProvincePcConfig zyzdProvincePcConfig = new ZyzdJDBC().getProvincePcConfigByParams(provinceConfig.getP_table_code(), lhyFormMain.getPc_code(), lhyFormMain.getPc());
		int FORM_CNT = provinceConfig.getCal_FormCnt(lhyFormMain.getPc_code(), zyzdProvincePcConfig);
		int MAJOR_CNT = provinceConfig.getCal_MajorCnt(lhyFormMain.getPc_code(), zyzdProvincePcConfig);
		String FORM_RULE = provinceConfig.getCal_FormRule(lhyFormMain.getPc_code(), zyzdProvincePcConfig);
		int form_type = zyzdProvincePcConfig == null ? 1 : zyzdProvincePcConfig.getForm_type();  // 1-平行志愿，非1-顺序志愿 
		
		boolean is96 = provinceConfig.getForm_type() == 2;

		String[] RULE_STAGE = FORM_RULE.split(",");

		int STAGE_CNT_CHONG = 15;
		int STAGE_CNT_WEN = 20;
		int STAGE_CNT_BAO = 10;
		
		String xkCode = XKCombineUtils.getXKCodeByStudentSelection(lhyFormMain.getScore_xk());
		List<LhyForm> formList = lhyJDBC.getMakerForm(provinceConfig.getP_table_code(), lhyFormMain.getBatch_id());
		
		
		LinkedHashMap<String, List<LhyForm>> groupLhyFormMap = new LinkedHashMap<>();

		HashSet<String> yxdmSets = new HashSet<>();
		HashSet<String> zyzSets = new HashSet<>();

		for(LhyForm bean : formList){
			groupLhyFormMap.computeIfAbsent(Tools.getTBUniqueGroupOnlyKey(bean, is96), k -> new ArrayList<>()).add(bean);
			if(!Tools.isEmpty(bean.getYxdm())){
				yxdmSets.add(bean.getYxdm());
			}
			
			if(!Tools.isEmpty(bean.getZyz())){
				zyzSets.add(bean.getZyz());
			}
		}


		LinkedHashMap<String, List<JHBean>> groupJhBeanFormMap = new LinkedHashMap<>(); //为了查询
		LinkedHashMap<String, JHBean> earchJhBeanFormMap = new LinkedHashMap<>(); //为了查询
		//查出来志愿表对应的院校+专业组完整信息
		List<JHBean> listMajorJh_all_matched = formJDBC.getZyBySelectedForm_YxdmAndZyz(provinceConfig.getLatest_year_jh(), provinceConfig.getP_table_code(), xkCode, lhyFormMain.getPc(), lhyFormMain.getPc_code(), yxdmSets, zyzSets);
		for(JHBean bean : listMajorJh_all_matched){
			earchJhBeanFormMap.put(Tools.getTBUniqueKey(bean, is96), bean);
			groupJhBeanFormMap.computeIfAbsent(Tools.getTBUniqueGroupOnlyKey(bean, is96), k -> new ArrayList<>()).add(bean);
		}
		
		
		List<AdmitBean> admitList = new ArrayList<>();
		//算录取，被谁录取
		for(Map.Entry<String, List<LhyForm>> entry : groupLhyFormMap.entrySet()) {
			List<LhyForm> tbFormList = entry.getValue();
			List<JHBean> jhBeanList = groupJhBeanFormMap.getOrDefault(entry.getKey(), new ArrayList<>());
			boolean isAdjustAdmit = true;
			AdmitBean admitBean = null;
			for(LhyForm zyzdForm : tbFormList){
				JHBean jHbean = earchJhBeanFormMap.getOrDefault(Tools.getTBUniqueKey(zyzdForm, is96), new JHBean());
				
				if(jHbean.getQsf() > 0 && (lhyFormMain.getScore_cj() - jHbean.getQsf()) > 0){
					isAdjustAdmit = false;
					admitBean = new AdmitBean();
					admitBean.setLhyForm(zyzdForm);
					admitBean.setAdjustAdmit(isAdjustAdmit);
					admitBean.setAdmitSeqNoYx(zyzdForm.getSeq_no_yx());
					admitBean.setAdmitSeqNoZy(zyzdForm.getSeq_no_zy());
					admitBean.setYxmc(zyzdForm.getYxmc());
					admitBean.setZymc(zyzdForm.getZymc_org());
					admitList.add(admitBean);
					break;
				}
			}
			LhyForm lastForm = tbFormList.get(tbFormList.size() - 1);
			if(isAdjustAdmit){
				for(JHBean jhBean : jhBeanList){
					Tools.println(jhBeanList.size() + " - > ["+ entry.getKey()+"]" + jhBean.getYxmc() + " ["+jhBean.getZyz() +"]" +" . " + jhBean.getQsf());
					if(jhBean.getQsf() > 0 && (lhyFormMain.getScore_cj() - jhBean.getQsf()) > 0){ // ADJUST
						admitBean = new AdmitBean();
						admitBean.setJhBean(jhBean);
						admitBean.setLhyForm(lastForm);
						admitBean.setAdjustAdmit(isAdjustAdmit);
						admitBean.setAdmitSeqNoYx(lastForm.getSeq_no_yx());
						admitBean.setYxmc(jhBean.getYxmc());
						admitBean.setZymc(jhBean.getZymc_org());
						admitList.add(admitBean);
						break;
					}
				}
			}
			
		}
		
		return admitList;
	}

}
