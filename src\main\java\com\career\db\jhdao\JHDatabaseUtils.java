package com.career.db.jhdao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * 数据库操作工具类
 * 提供安全的数据库连接管理和资源释放
 * 
 * 主要功能：
 * 1. 统一的连接获取方式
 * 2. 自动资源释放
 * 3. 异常处理
 * 4. 事务管理
 * 
 * <AUTHOR> Admin
 * @version 1.0
 */
public class JHDatabaseUtils {
    
    /**
     * 获取数据库连接
     * 推荐使用此方法替代直接调用DriverManager.getConnection()
     * 
     * @return 数据库连接
     * @throws SQLException 连接获取失败时抛出
     */
    public static Connection getConnection() throws SQLException {
        return JHConnectionPoolManager.getConnection();
    }
    
    /**
     * 安全关闭数据库资源
     * 按照 ResultSet -> PreparedStatement -> Connection 的顺序关闭
     * 
     * @param rs 结果集
     * @param ps 预编译语句
     * @param conn 数据库连接
     */
    public static void closeAllResources(ResultSet rs, PreparedStatement ps, Connection conn) {
        closeResultSet(rs);
        closePreparedStatement(ps);
        closeConnection(conn);
    }
    
    /**
     * 安全关闭数据库资源（不包含ResultSet）
     * 
     * @param ps 预编译语句
     * @param conn 数据库连接
     */
    public static void closeResources(PreparedStatement ps, Connection conn) {
        closePreparedStatement(ps);
        closeConnection(conn);
    }
    
    /**
     * 安全关闭数据库资源（Statement版本）
     * 
     * @param rs 结果集
     * @param stmt 语句对象
     * @param conn 数据库连接
     */
    public static void closeAllResources(ResultSet rs, Statement stmt, Connection conn) {
        closeResultSet(rs);
        closeStatement(stmt);
        closeConnection(conn);
    }
    
    /**
     * 关闭ResultSet
     * 
     * @param rs 结果集
     */
    public static void closeResultSet(ResultSet rs) {
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                System.err.println("关闭ResultSet时出错: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 关闭PreparedStatement
     * 
     * @param ps 预编译语句
     */
    public static void closePreparedStatement(PreparedStatement ps) {
        if (ps != null) {
            try {
                ps.close();
            } catch (SQLException e) {
                System.err.println("关闭PreparedStatement时出错: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 关闭Statement
     * 
     * @param stmt 语句对象
     */
    public static void closeStatement(Statement stmt) {
        if (stmt != null) {
            try {
                stmt.close();
            } catch (SQLException e) {
                System.err.println("关闭Statement时出错: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 关闭Connection
     * 
     * @param conn 数据库连接
     */
    public static void closeConnection(Connection conn) {
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
                System.err.println("关闭Connection时出错: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 开始事务
     * 
     * @param conn 数据库连接
     * @throws SQLException 事务开始失败时抛出
     */
    public static void beginTransaction(Connection conn) throws SQLException {
        if (conn != null) {
            conn.setAutoCommit(false);
        }
    }
    
    /**
     * 提交事务
     * 
     * @param conn 数据库连接
     */
    public static void commitTransaction(Connection conn) {
        if (conn != null) {
            try {
                conn.commit();
            } catch (SQLException e) {
                System.err.println("提交事务时出错: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 回滚事务
     * 
     * @param conn 数据库连接
     */
    public static void rollbackTransaction(Connection conn) {
        if (conn != null) {
            try {
                conn.rollback();
            } catch (SQLException e) {
                System.err.println("回滚事务时出错: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 恢复自动提交模式
     * 
     * @param conn 数据库连接
     */
    public static void restoreAutoCommit(Connection conn) {
        if (conn != null) {
            try {
                conn.setAutoCommit(true);
            } catch (SQLException e) {
                System.err.println("恢复自动提交模式时出错: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 执行带事务的数据库操作
     * 使用示例：
     * <pre>
     * DatabaseUtils.executeInTransaction(conn -> {
     *     // 你的数据库操作代码
     *     PreparedStatement ps = conn.prepareStatement("INSERT INTO ...");
     *     ps.executeUpdate();
     *     return true; // 返回操作结果
     * });
     * </pre>
     * 
     * @param operation 数据库操作
     * @return 操作结果
     * @throws SQLException 操作失败时抛出
     */
    public static <T> T executeInTransaction(DatabaseOperation<T> operation) throws SQLException {
        Connection conn = null;
        try {
            conn = getConnection();
            beginTransaction(conn);
            
            T result = operation.execute(conn);
            
            commitTransaction(conn);
            return result;
            
        } catch (Exception e) {
            rollbackTransaction(conn);
            if (e instanceof SQLException) {
                throw (SQLException) e;
            } else {
                throw new SQLException("事务执行失败", e);
            }
        } finally {
            restoreAutoCommit(conn);
            closeConnection(conn);
        }
    }
    
    /**
     * 数据库操作接口
     * 
     * @param <T> 返回值类型
     */
    @FunctionalInterface
    public interface DatabaseOperation<T> {
        T execute(Connection conn) throws SQLException;
    }
    
    /**
     * 获取连接池状态信息
     * 
     * @return 连接池状态
     */
    public static String getConnectionPoolStatus() {
        return JHConnectionPoolManager.getPoolStatus();
    }
    
    /**
     * 检查数据库连接是否健康
     * 
     * @return true表示健康，false表示有问题
     */
    public static boolean isDatabaseHealthy() {
        return JHConnectionPoolManager.isHealthy();
    }
}
