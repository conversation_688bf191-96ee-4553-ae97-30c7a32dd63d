<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page import="com.career.utils.*,com.career.db.*,java.util.*" %>

<%@include file="/WEB-INF/include/_session_admin_ajax.jsp" %>
<%
String cardno = Tools.trim(request.getParameter("cardno"));
String passwd = Tools.trim(request.getParameter("passwd"));


if(Tools.isEmpty(cardno) || Tools.isEmpty(passwd)){
	out.println("卡密都不输，你提交搞哪样");
	return;
}


ZyzdJDBC jdbc = new ZyzdJDBC();
CardBean cardBean = jdbc.getCardByIDandPasswd(cardno, passwd);
if(cardBean == null){
	out.println("卡密不存在，请确认输入是否正确");
	return;
}
jdbc.unlockCardForZdksTimes(cardBean.getId());
out.println("该卡号诊断次数解锁成功，次数重新恢复");
%>



		