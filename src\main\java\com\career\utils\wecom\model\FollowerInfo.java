package com.career.utils.wecom.model;

import java.util.Date;
import java.text.SimpleDateFormat;

/**
 * 企业微信客户的跟进人信息
 */
public class FollowerInfo {
    // 外部联系人ID (对应 wecom_external_userid)
    private String externalUserId;
    // 跟进人（企业成员）ID (对应 wecom_follower_userid)
    private String followerUserId;
    // 备注 (对应 wecom_remark)
    private String remark;
    // 描述 (对应 wecom_description)
    private String description;
    // 跟进人添加客户的时间戳 (对应 wecom_createtime)
    private Date createTime;
    // 跟进人添加客户的时间字符串格式
    private String createTimeStr;
    // 跟进人给客户添加的标签列表，字符串形式存储 (对应 wecom_tags)
    private String tags;
    // 跟进人给客户添加的备注手机号列表，字符串形式存储 (对应 wecom_remark_mobiles)
    private String remarkMobiles;
    // 跟进人备注公司名称 (对应 wecom_remark_corp_name)
    private String remarkCorpName;
    // 跟进客户方式 (对应 wecom_add_way)
    private int addWay;
    // 进行操作（如添加客户）的成员userid (对应 wecom_oper_userid)
    private String operUserId;
    
    private String wecom_saas_id;
    
    // 跟进人版本号
    private String followersVersion;
    
    // 跟进人关系同步时间戳 (用于增量同步)
    private Date followerSyncTime;
    
	public String getWecom_saas_id() {
		return wecom_saas_id;
	}

	public void setWecom_saas_id(String wecom_saas_id) {
		this.wecom_saas_id = wecom_saas_id;
	}

	public String getExternalUserId() {
        return externalUserId;
    }

    public void setExternalUserId(String externalUserId) {
        this.externalUserId = externalUserId;
    }

    public String getFollowerUserId() {
        return followerUserId;
    }

    public void setFollowerUserId(String followerUserId) {
        this.followerUserId = followerUserId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 设置创建时间的字符串格式
     * @param createTimeStr 创建时间字符串，可以是时间戳字符串
     */
    public void setCreateTime(String createTimeStr) {
        this.createTimeStr = createTimeStr;
        try {
            if (createTimeStr != null && !createTimeStr.isEmpty()) {
                // 尝试解析为时间戳(秒)
                long timestamp = Long.parseLong(createTimeStr);
                this.createTime = new Date(timestamp * 1000);
            }
        } catch (NumberFormatException e) {
            // 如果不是时间戳格式，尝试作为日期字符串处理
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                this.createTime = sdf.parse(createTimeStr);
            } catch (Exception ex) {
                // 忽略解析错误
            }
        }
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getRemarkMobiles() {
        return remarkMobiles;
    }

    public void setRemarkMobiles(String remarkMobiles) {
        this.remarkMobiles = remarkMobiles;
    }

    public String getRemarkCorpName() {
        return remarkCorpName;
    }

    public void setRemarkCorpName(String remarkCorpName) {
        this.remarkCorpName = remarkCorpName;
    }

    public int getAddWay() {
        return addWay;
    }

    public void setAddWay(int addWay) {
        this.addWay = addWay;
    }

    public String getOperUserId() {
        return operUserId;
    }

    public void setOperUserId(String operUserId) {
        this.operUserId = operUserId;
    }
    
    public String getFollowersVersion() {
        return followersVersion;
    }

    public void setFollowersVersion(String followersVersion) {
        this.followersVersion = followersVersion;
    }
    
    public Date getFollowerSyncTime() {
        return followerSyncTime;
    }
    
    public void setFollowerSyncTime(Date followerSyncTime) {
        this.followerSyncTime = followerSyncTime;
    }

    @Override
    public String toString() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedTime = createTime != null ? sdf.format(createTime) : "N/A";
        String formattedSyncTime = followerSyncTime != null ? sdf.format(followerSyncTime) : "N/A";
        String remarkMobilesStr = remarkMobiles != null ? remarkMobiles : "无";
        String tagsStr = tags != null ? tags : "无标签";

        return "外部客户ID: " + externalUserId +
               ", 跟进人ID: " + followerUserId +
               ", 备注: " + remark +
               ", 描述: " + description +
               ", 添加时间: " + formattedTime +
               ", 备注手机号: " + remarkMobilesStr +
               ", 备注公司: " + remarkCorpName +
               ", 添加方式: " + addWay +
               ", 操作成员ID: " + operUserId +
               ", 标签: " + tagsStr +
               ", 同步时间: " + formattedSyncTime;
    }
} 