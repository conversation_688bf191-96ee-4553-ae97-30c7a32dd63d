package com.career.db;

import java.sql.Timestamp; 

public class RtFillNeedsInfoBean {

    // 主键和基础字段
    private String rt_id; // VARCHAR(100)
    private String rt_user_id; // VARCHAR(50)
    
    // 新增字段
    private String rt_agent_id; // VARCHAR(100)
    private String rt_expert_id; // VARCHAR(100)
    private Integer rt_order_price_org; // INT
    private Integer rt_order_price_discount; // INT
    private Integer rt_order_price; // INT
    

    // -- 基本信息 (Step 1 - 必填)
    private String rt_province; // VARCHAR(50)
    private String rt_name; // VARCHAR(100)
    private String rt_phone; // VARCHAR(20)
    
    // -- 个人特征 (Step 1)
    private String rt_gender; // VARCHAR(10)
    private String rt_graduate_status; // VARCHAR(10)
    private String rt_political_status; // VARCHAR(20)
    private String rt_ethnicity; // VARCHAR(20)
    
    // -- 身体特征 (Step 1)
    private String rt_height; // VARCHAR(20)
    private String rt_weight; // VARCHAR(20)
    private String rt_body_type; // VARCHAR(20)
    
    // -- 专业技能与健康状况 (Step 1)
    private String rt_drawing_skill; // VARCHAR(20)
    private String rt_hand_habit; // VARCHAR(20)
    private String rt_physical_exam; // JSON (存储为String)
    
    // -- 选科信息 (Step 2)
    private String rt_subject; // VARCHAR(50) - 替代原来的rt_subject_primary和rt_subject_secondary
    private String rt_foreign_language; // VARCHAR(20)
    
    // -- 考试成绩 (Step 2)
    private Integer rt_score_chinese; // INT
    private Integer rt_score_math; // INT
    private Integer rt_score_foreign_language; // INT
    private Integer rt_score_a; // INT
    private Integer rt_score_b; // INT
    private Integer rt_score_c; // INT
    private Integer rt_total_score; // INT
    private Integer rt_score_ext; // INT
    private Integer rt_ranking; // INT
    private String rt_interview; // VARCHAR(20)
    
    // -- 专项计划 (Step 3)
    private String rt_is_eligible_for_special_plans; // VARCHAR(10)
    private String rt_accept_directed_plans; // VARCHAR(10)
    
    // -- 教育预算与规划 (Step 3)
    private String rt_university_budget; // VARCHAR(20)
    private String rt_deep_planning; // JSON (存储为String)
    
    // -- 办学类型偏好 (Step 3)
    private String rt_accept_sino_foreign; // VARCHAR(10)
    private String rt_accept_private; // VARCHAR(10)
    
    // -- 填报策略 (Step 4 - 必填)
    private String rt_strategy; // VARCHAR(50)
    
    // -- 意向城市 (Step 5)
    private String rt_preferred_cities; // JSON (存储为String)
    private String rt_preferred_provinces; // JSON (存储为String)
    
    // -- 意向专业 (Step 6)
    private String rt_major_level; // VARCHAR(20)
    private String rt_major_category; // VARCHAR(50)
    private String rt_major_subcategory; // VARCHAR(50)
    private String rt_preferred_majors; // JSON (存储为String)
    
    // -- 系统字段
    private Integer rt_status; // INT
    private Timestamp rt_create_time; // DATETIME
    private Timestamp rt_update_time; // DATETIME
    private Timestamp rt_submit_time; // DATETIME

    // 无参构造函数
    public RtFillNeedsInfoBean() {
    }

    // Getter 和 Setter 方法

    public String getRt_id() {
        return rt_id;
    }

    public void setRt_id(String rt_id) {
        this.rt_id = rt_id;
    }

    public String getRt_user_id() {
        return rt_user_id;
    }

    public void setRt_user_id(String rt_user_id) {
        this.rt_user_id = rt_user_id;
    }

    public String getRt_agent_id() {
        return rt_agent_id;
    }

    public void setRt_agent_id(String rt_agent_id) {
        this.rt_agent_id = rt_agent_id;
    }
    
    public String getRt_expert_id() {
		return rt_expert_id;
	}

	public void setRt_expert_id(String rt_expert_id) {
		this.rt_expert_id = rt_expert_id;
	}

	public Integer getRt_order_price_org() {
        return rt_order_price_org;
    }

    public void setRt_order_price_org(Integer rt_order_price_org) {
        this.rt_order_price_org = rt_order_price_org;
    }

    public Integer getRt_order_price_discount() {
        return rt_order_price_discount;
    }

    public void setRt_order_price_discount(Integer rt_order_price_discount) {
        this.rt_order_price_discount = rt_order_price_discount;
    }

    public Integer getRt_order_price() {
        return rt_order_price;
    }

    public void setRt_order_price(Integer rt_order_price) {
        this.rt_order_price = rt_order_price;
    }

    public String getRt_province() {
        return rt_province;
    }

    public void setRt_province(String rt_province) {
        this.rt_province = rt_province;
    }

    public String getRt_name() {
        return rt_name;
    }

    public void setRt_name(String rt_name) {
        this.rt_name = rt_name;
    }

    public String getRt_phone() {
        return rt_phone;
    }

    public void setRt_phone(String rt_phone) {
        this.rt_phone = rt_phone;
    }

    public String getRt_gender() {
        return rt_gender;
    }

    public void setRt_gender(String rt_gender) {
        this.rt_gender = rt_gender;
    }

    public String getRt_graduate_status() {
        return rt_graduate_status;
    }

    public void setRt_graduate_status(String rt_graduate_status) {
        this.rt_graduate_status = rt_graduate_status;
    }

    public String getRt_political_status() {
        return rt_political_status;
    }

    public void setRt_political_status(String rt_political_status) {
        this.rt_political_status = rt_political_status;
    }

    public String getRt_ethnicity() {
        return rt_ethnicity;
    }

    public void setRt_ethnicity(String rt_ethnicity) {
        this.rt_ethnicity = rt_ethnicity;
    }

    public String getRt_height() {
        return rt_height;
    }

    public void setRt_height(String rt_height) {
        this.rt_height = rt_height;
    }

    public String getRt_weight() {
        return rt_weight;
    }

    public void setRt_weight(String rt_weight) {
        this.rt_weight = rt_weight;
    }

    public String getRt_body_type() {
        return rt_body_type;
    }

    public void setRt_body_type(String rt_body_type) {
        this.rt_body_type = rt_body_type;
    }

    public String getRt_drawing_skill() {
        return rt_drawing_skill;
    }

    public void setRt_drawing_skill(String rt_drawing_skill) {
        this.rt_drawing_skill = rt_drawing_skill;
    }

    public String getRt_hand_habit() {
        return rt_hand_habit;
    }

    public void setRt_hand_habit(String rt_hand_habit) {
        this.rt_hand_habit = rt_hand_habit;
    }

    public String getRt_physical_exam() {
        return rt_physical_exam;
    }

    public void setRt_physical_exam(String rt_physical_exam) {
        this.rt_physical_exam = rt_physical_exam;
    }

    public String getRt_subject() {
        return rt_subject;
    }

    public void setRt_subject(String rt_subject) {
        this.rt_subject = rt_subject;
    }

    public String getRt_foreign_language() {
        return rt_foreign_language;
    }

    public void setRt_foreign_language(String rt_foreign_language) {
        this.rt_foreign_language = rt_foreign_language;
    }

    public Integer getRt_score_chinese() {
        return rt_score_chinese;
    }

    public void setRt_score_chinese(Integer rt_score_chinese) {
        this.rt_score_chinese = rt_score_chinese;
    }

    public Integer getRt_score_math() {
        return rt_score_math;
    }

    public void setRt_score_math(Integer rt_score_math) {
        this.rt_score_math = rt_score_math;
    }

    public Integer getRt_score_foreign_language() {
        return rt_score_foreign_language;
    }

    public void setRt_score_foreign_language(Integer rt_score_foreign_language) {
        this.rt_score_foreign_language = rt_score_foreign_language;
    }

    public Integer getRt_score_a() {
        return rt_score_a;
    }

    public void setRt_score_a(Integer rt_score_a) {
        this.rt_score_a = rt_score_a;
    }

    public Integer getRt_score_b() {
        return rt_score_b;
    }

    public void setRt_score_b(Integer rt_score_b) {
        this.rt_score_b = rt_score_b;
    }

    public Integer getRt_score_c() {
        return rt_score_c;
    }

    public void setRt_score_c(Integer rt_score_c) {
        this.rt_score_c = rt_score_c;
    }

    public Integer getRt_total_score() {
        return rt_total_score;
    }

    public void setRt_total_score(Integer rt_total_score) {
        this.rt_total_score = rt_total_score;
    }
    
    public Integer getRt_score_ext() {
		return rt_score_ext;
	}

	public void setRt_score_ext(Integer rt_score_ext) {
		this.rt_score_ext = rt_score_ext;
	}

	public Integer getRt_ranking() {
        return rt_ranking;
    }

    public void setRt_ranking(Integer rt_ranking) {
        this.rt_ranking = rt_ranking;
    }

    public String getRt_interview() {
        return rt_interview;
    }

    public void setRt_interview(String rt_interview) {
        this.rt_interview = rt_interview;
    }

    public String getRt_is_eligible_for_special_plans() {
        return rt_is_eligible_for_special_plans;
    }

    public void setRt_is_eligible_for_special_plans(String rt_is_eligible_for_special_plans) {
        this.rt_is_eligible_for_special_plans = rt_is_eligible_for_special_plans;
    }

    public String getRt_accept_directed_plans() {
        return rt_accept_directed_plans;
    }

    public void setRt_accept_directed_plans(String rt_accept_directed_plans) {
        this.rt_accept_directed_plans = rt_accept_directed_plans;
    }

    public String getRt_university_budget() {
        return rt_university_budget;
    }

    public void setRt_university_budget(String rt_university_budget) {
        this.rt_university_budget = rt_university_budget;
    }

    public String getRt_deep_planning() {
        return rt_deep_planning;
    }

    public void setRt_deep_planning(String rt_deep_planning) {
        this.rt_deep_planning = rt_deep_planning;
    }

    public String getRt_accept_sino_foreign() {
        return rt_accept_sino_foreign;
    }

    public void setRt_accept_sino_foreign(String rt_accept_sino_foreign) {
        this.rt_accept_sino_foreign = rt_accept_sino_foreign;
    }

    public String getRt_accept_private() {
        return rt_accept_private;
    }

    public void setRt_accept_private(String rt_accept_private) {
        this.rt_accept_private = rt_accept_private;
    }

    public String getRt_strategy() {
        return rt_strategy;
    }

    public void setRt_strategy(String rt_strategy) {
        this.rt_strategy = rt_strategy;
    }

    public String getRt_preferred_cities() {
        return rt_preferred_cities;
    }

    public void setRt_preferred_cities(String rt_preferred_cities) {
        this.rt_preferred_cities = rt_preferred_cities;
    }

    public String getRt_preferred_provinces() {
        return rt_preferred_provinces;
    }

    public void setRt_preferred_provinces(String rt_preferred_provinces) {
        this.rt_preferred_provinces = rt_preferred_provinces;
    }

    public String getRt_major_level() {
        return rt_major_level;
    }

    public void setRt_major_level(String rt_major_level) {
        this.rt_major_level = rt_major_level;
    }

    public String getRt_major_category() {
        return rt_major_category;
    }

    public void setRt_major_category(String rt_major_category) {
        this.rt_major_category = rt_major_category;
    }

    public String getRt_major_subcategory() {
        return rt_major_subcategory;
    }

    public void setRt_major_subcategory(String rt_major_subcategory) {
        this.rt_major_subcategory = rt_major_subcategory;
    }

    public String getRt_preferred_majors() {
        return rt_preferred_majors;
    }

    public void setRt_preferred_majors(String rt_preferred_majors) {
        this.rt_preferred_majors = rt_preferred_majors;
    }

    public Integer getRt_status() {
        return rt_status;
    }

    public void setRt_status(Integer rt_status) {
        this.rt_status = rt_status;
    }

    public Timestamp getRt_create_time() {
        return rt_create_time;
    }

    public void setRt_create_time(Timestamp rt_create_time) {
        this.rt_create_time = rt_create_time;
    }

    public Timestamp getRt_update_time() {
        return rt_update_time;
    }

    public void setRt_update_time(Timestamp rt_update_time) {
        this.rt_update_time = rt_update_time;
    }

    public Timestamp getRt_submit_time() {
        return rt_submit_time;
    }

    public void setRt_submit_time(Timestamp rt_submit_time) {
        this.rt_submit_time = rt_submit_time;
    }

    @Override
    public String toString() {
        return "RtRtFillNeedsInfoBean{" +
               "rt_id='" + rt_id + '\'' +
               ", rt_user_id='" + rt_user_id + '\'' +
               ", rt_agent_id='" + rt_agent_id + '\'' +
               ", rt_order_price_org=" + rt_order_price_org +
               ", rt_order_price_discount=" + rt_order_price_discount +
               ", rt_order_price=" + rt_order_price +
               ", rt_province='" + rt_province + '\'' +
               ", rt_name='" + rt_name + '\'' +
               ", rt_phone='" + rt_phone + '\'' +
               ", rt_gender='" + rt_gender + '\'' +
               ", rt_graduate_status='" + rt_graduate_status + '\'' +
               ", rt_political_status='" + rt_political_status + '\'' +
               ", rt_ethnicity='" + rt_ethnicity + '\'' +
               ", rt_height='" + rt_height + '\'' +
               ", rt_weight='" + rt_weight + '\'' +
               ", rt_body_type='" + rt_body_type + '\'' +
               ", rt_drawing_skill='" + rt_drawing_skill + '\'' +
               ", rt_hand_habit='" + rt_hand_habit + '\'' +
               ", rt_physical_exam='" + rt_physical_exam + '\'' + // 注意: JSON以字符串形式输出
               ", rt_subject='" + rt_subject + '\'' +
               ", rt_foreign_language='" + rt_foreign_language + '\'' +
               ", rt_score_chinese=" + rt_score_chinese +
               ", rt_score_math=" + rt_score_math +
               ", rt_score_foreign_language=" + rt_score_foreign_language +
               ", rt_score_a=" + rt_score_a +
               ", rt_score_b=" + rt_score_b +
               ", rt_score_c=" + rt_score_c +
               ", rt_total_score=" + rt_total_score +
               ", rt_ranking=" + rt_ranking +
               ", rt_interview='" + rt_interview + '\'' +
               ", rt_is_eligible_for_special_plans='" + rt_is_eligible_for_special_plans + '\'' +
               ", rt_accept_directed_plans='" + rt_accept_directed_plans + '\'' +
               ", rt_university_budget='" + rt_university_budget + '\'' +
               ", rt_deep_planning='" + rt_deep_planning + '\'' + // 注意: JSON以字符串形式输出
               ", rt_accept_sino_foreign='" + rt_accept_sino_foreign + '\'' +
               ", rt_accept_private='" + rt_accept_private + '\'' +
               ", rt_strategy='" + rt_strategy + '\'' +
               ", rt_preferred_cities='" + rt_preferred_cities + '\'' + // 注意: JSON以字符串形式输出
               ", rt_preferred_provinces='" + rt_preferred_provinces + '\'' + // 注意: JSON以字符串形式输出
               ", rt_major_level='" + rt_major_level + '\'' +
               ", rt_major_category='" + rt_major_category + '\'' +
               ", rt_major_subcategory='" + rt_major_subcategory + '\'' +
               ", rt_preferred_majors='" + rt_preferred_majors + '\'' + // 注意: JSON以字符串形式输出
               ", rt_status=" + rt_status +
               ", rt_create_time=" + rt_create_time +
               ", rt_update_time=" + rt_update_time +
               ", rt_submit_time=" + rt_submit_time +
               '}';
    }
}