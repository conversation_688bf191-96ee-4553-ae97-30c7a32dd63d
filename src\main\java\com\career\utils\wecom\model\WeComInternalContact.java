package com.career.utils.wecom.model;

import java.util.Date;

/**
 * 企业微信内部联系人信息模型
 */
public class WeComInternalContact {
    
    // 数据库主键ID
    private Long id;
    
    // 成员UserID，对应企业微信的userid
    private String userId;
    
    // 成员名称
    private String name;
    
    // 手机号码
    private String mobile;
    
    // 邮箱
    private String email;
    
    // 职务信息
    private String position;
    
    // 性别。0表示未定义，1表示男性，2表示女性
    private Integer gender;
    
    // 头像url
    private String avatar;
    
    // 头像缩略图url
    private String thumbAvatar;
    
    // 座机
    private String telephone;
    
    // 别名
    private String alias;
    
    // 地址
    private String address;
    
    // 全局唯一。对于同一个服务商，不同应用获取到企业内同一个成员的open_userid是相同的
    private String openUserId;
    
    // 主部门
    private Integer mainDepartment;
    
    // 激活状态: 1=已激活，2=已禁用，4=未激活，5=退出企业
    private Integer status;
    
    // 员工个人二维码
    private String qrCode;
    
    // 成员所属部门id列表（JSON数组字符串）
    private String departmentJson;
    
    // 部门内的排序值，默认为0（JSON数组字符串）
    private String orderJson;
    
    // 在所在的部门内是否为部门负责人（JSON数组字符串）
    private String isLeaderInDeptJson;
    
    // 直属上级UserID（JSON数组字符串）
    private String directLeaderJson;
    
    // 扩展属性（JSON字符串）
    private String extAttrJson;
    
    // 对外职务，如果设置了该值，则以此作为对外展示的职务
    private String externalPosition;
    
    // 成员对外信息（JSON字符串）
    private String externalProfileJson;
    
    // 创建时间
    private Date createdAt;
    
    // 更新时间
    private Date updatedAt;
    
    private String wecom_saas_id;
    
    // 企业微信同步时间戳
    private Date syncTime;
    
    public String getWecom_saas_id() {
		return wecom_saas_id;
	}

	public void setWecom_saas_id(String wecom_saas_id) {
		this.wecom_saas_id = wecom_saas_id;
	}

	public Date getSyncTime() {
        return syncTime;
    }

    public void setSyncTime(Date syncTime) {
        this.syncTime = syncTime;
    }

	// 构造函数
    public WeComInternalContact() {
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getThumbAvatar() {
        return thumbAvatar;
    }

    public void setThumbAvatar(String thumbAvatar) {
        this.thumbAvatar = thumbAvatar;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getOpenUserId() {
        return openUserId;
    }

    public void setOpenUserId(String openUserId) {
        this.openUserId = openUserId;
    }

    public Integer getMainDepartment() {
        return mainDepartment;
    }

    public void setMainDepartment(Integer mainDepartment) {
        this.mainDepartment = mainDepartment;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getQrCode() {
        return qrCode;
    }

    public void setQrCode(String qrCode) {
        this.qrCode = qrCode;
    }

    public String getDepartmentJson() {
        return departmentJson;
    }

    public void setDepartmentJson(String departmentJson) {
        this.departmentJson = departmentJson;
    }

    public String getOrderJson() {
        return orderJson;
    }

    public void setOrderJson(String orderJson) {
        this.orderJson = orderJson;
    }

    public String getIsLeaderInDeptJson() {
        return isLeaderInDeptJson;
    }

    public void setIsLeaderInDeptJson(String isLeaderInDeptJson) {
        this.isLeaderInDeptJson = isLeaderInDeptJson;
    }

    public String getDirectLeaderJson() {
        return directLeaderJson;
    }

    public void setDirectLeaderJson(String directLeaderJson) {
        this.directLeaderJson = directLeaderJson;
    }

    public String getExtAttrJson() {
        return extAttrJson;
    }

    public void setExtAttrJson(String extAttrJson) {
        this.extAttrJson = extAttrJson;
    }

    public String getExternalPosition() {
        return externalPosition;
    }

    public void setExternalPosition(String externalPosition) {
        this.externalPosition = externalPosition;
    }

    public String getExternalProfileJson() {
        return externalProfileJson;
    }

    public void setExternalProfileJson(String externalProfileJson) {
        this.externalProfileJson = externalProfileJson;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("内部联系人信息:\n");
        sb.append("  用户ID: ").append(userId).append("\n");
        sb.append("  姓名: ").append(name).append("\n");
        sb.append("  手机号: ").append(mobile).append("\n");
        sb.append("  邮箱: ").append(email).append("\n");
        sb.append("  职务: ").append(position).append("\n");
        sb.append("  性别: ").append(getGenderText()).append("\n");
        sb.append("  头像: ").append(avatar).append("\n");
        sb.append("  座机: ").append(telephone).append("\n");
        sb.append("  别名: ").append(alias).append("\n");
        sb.append("  地址: ").append(address).append("\n");
        sb.append("  OpenUserID: ").append(openUserId).append("\n");
        sb.append("  主部门: ").append(mainDepartment).append("\n");
        sb.append("  状态: ").append(getStatusText()).append("\n");
        sb.append("  部门列表: ").append(departmentJson).append("\n");
        sb.append("  部门排序: ").append(orderJson).append("\n");
        sb.append("  是否部门负责人: ").append(isLeaderInDeptJson).append("\n");
        sb.append("  直属上级: ").append(directLeaderJson).append("\n");
        sb.append("  扩展属性: ").append(extAttrJson).append("\n");
        sb.append("  对外职务: ").append(externalPosition).append("\n");
        sb.append("  对外信息: ").append(externalProfileJson).append("\n");
        return sb.toString();
    }

    /**
     * 获取性别文本描述
     */
    private String getGenderText() {
        if (gender == null) return "未知";
        switch (gender) {
            case 0: return "未定义";
            case 1: return "男性";
            case 2: return "女性";
            default: return "未知";
        }
    }

    /**
     * 获取状态文本描述
     */
    private String getStatusText() {
        if (status == null) return "未知";
        switch (status) {
            case 1: return "已激活";
            case 2: return "已禁用";
            case 4: return "未激活";
            case 5: return "退出企业";
            default: return "未知";
        }
    }
} 