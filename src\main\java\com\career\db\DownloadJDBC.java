package com.career.db;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;

import com.career.utils.ResultVO;
import com.career.utils.SQLLogUtils;
import com.career.utils.Tools;

public class DownloadJDBC {

	static String URL = JDBCConstants.URL;
	static String USER = JDBCConstants.USER;
	static String PASSWD = JDBCConstants.PASSWD;
	public static int PAGE_ROW_CNT = 10;

	static {
		try {
			Class.forName("com.mysql.cj.jdbc.Driver");
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
	
	
	public void insertDownload(String c_id, int d_type) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert zyzd_download(ref_c_id, d_type, d_tm) values(?,?,now())";
			//Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, c_id);
			ps.setInt(2, d_type);
			SQLLogUtils.printSQL(ps); 
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public int getDownloadCntByToday(String c_id, int d_type) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_RECORD_ALL = "SELECT count(*) as cnt FROM zyzd_download x WHERE ref_c_id = ? and d_type = ? and TO_DAYS(d_tm)  = TO_DAYS(NOW())" ;
			
			ps = conn.prepareStatement(SELECT_RECORD_ALL);
			int index = 1;
			ps.setString(index++, c_id);
			ps.setInt(index++, d_type);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				return rs.getInt("cnt");
			}
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	//最近幾分鐘
	public int getDownloadCntByLatestMins(String c_id, int d_type, int latest_mins_CNT) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_RECORD_ALL = "SELECT count(*) as cnt FROM zyzd_download x WHERE ref_c_id = ? and d_type = ? and d_tm > NOW() - INTERVAL "+latest_mins_CNT+" MINUTE" ;
			
			ps = conn.prepareStatement(SELECT_RECORD_ALL);
			int index = 1;
			ps.setString(index++, c_id);
			ps.setInt(index++, d_type);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				return rs.getInt("cnt");
			}
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	
	private static void closeAllConnection(Connection conn, PreparedStatement ps, ResultSet rs) {
		/**
		try {
			if (rs != null)
				rs.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (ps != null)
				ps.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try { 
			if (conn != null)
				conn.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		rs = null;
		ps = null;
		conn = null;
		*/
		DatabaseUtils.closeAllResources(rs, ps, conn);
	}
}
