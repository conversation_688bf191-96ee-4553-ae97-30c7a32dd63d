// Decompiled by Jad v1.5.8g. Copyright 2001 <PERSON>.
// Jad home page: http://www.kpdus.com/jad.html
// Decompiler options: packimports(3) 
// Source File Name:   MarjorBean.java

package com.career.db;

import java.io.*;

public class MarjorBeanZK{
	private String zydl;
	private String zyl;
	private String zydm;
	private String zymc;
	private String zyfx;
	private String dyzy;
	private String zzzy;
	private String xbzy;
	public String getZydl() {
		return zydl;
	}
	public void setZydl(String zydl) {
		this.zydl = zydl;
	}
	public String getZyl() {
		return zyl;
	}
	public void setZyl(String zyl) {
		this.zyl = zyl;
	}
	public String getZydm() {
		return zydm;
	}
	public void setZydm(String zydm) {
		this.zydm = zydm;
	}
	public String getZymc() {
		return zymc;
	}
	public void setZymc(String zymc) {
		this.zymc = zymc;
	}
	public String getZyfx() {
		return zyfx;
	}
	public void setZyfx(String zyfx) {
		this.zyfx = zyfx;
	}
	public String getDyzy() {
		return dyzy;
	}
	public void setDyzy(String dyzy) {
		this.dyzy = dyzy;
	}
	public String getZzzy() {
		return zzzy;
	}
	public void setZzzy(String zzzy) {
		this.zzzy = zzzy;
	}
	public String getXbzy() {
		return xbzy;
	}
	public void setXbzy(String xbzy) {
		this.xbzy = xbzy;
	}
	
}
