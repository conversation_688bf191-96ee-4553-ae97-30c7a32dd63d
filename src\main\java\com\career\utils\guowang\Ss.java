package com.career.utils.guowang;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import com.career.utils.liuxue.HttpSendUtils2;

public class Ss {
	
	public static StringBuffer rlsbj_cq_gov(String x) {
		Map<String, String> headers = new HashMap<>();
		StringBuffer SQL = new StringBuffer();
		//String href = "https://zhaopin.chinatowercom.cn/recruit/#/model_view_anonymous?model=_HB5_UmVjcnVpdEFubm91bmNl&id=_HB5_MTz1&meta_state=_HB5_cG9ydGFsX3BsdWdpbg%253D%253D";
		
		String href = "<a href='https://zhaopin.chinatowercom.cn/recruit/#/model_view_anonymous?model=_HB5_UmVjcnVpdEFubm91bmNl&id=_HB5_MT"+x+"&meta_state=_HB5_cG9ydGFsX3BsdWdpbg%253D%253D'>"+x+"</a>";
		//String rs = HttpSendUtils2.get(href, headers);
		System.out.println(href);
		return null;
	}
	
	
	public static void main(String[] args) {
		String[] xx1 =  {"0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z"};
		String[] xx2 =  {"0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z"};
		String[] xx11 =  {"a"};
		String[] xx22 =  {"0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z"};
		System.out.println("<html><head></head><body>");
		for(String xx : xx22) {
			for(String x : xx11) {
				rlsbj_cq_gov(xx + x);
			}
		}
		System.out.println("</body></html>");
	}
	
	private static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

}
