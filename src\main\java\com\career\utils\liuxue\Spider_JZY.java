package com.career.utils.liuxue;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.career.utils.HttpSendUtils;

public class Spider_JZY {
	
	// 靖志远抓取网页
	public static StringBuffer YOUZHIYUAN() {
		Map<String, String> headers = new HashMap<>();
		Map<String, String> params = new HashMap<>();
		
		params.put("ProvinceId", "6BD52781-2F8E-4114-8150-5DEB3AC228D3");
		params.put("Year", "2023");
		params.put("SelectSubject", "物理");
		params.put("PageIndex", "1");
		params.put("PageSize", "10");
		
		params.put("BatchIdList", "[]");
		params.put("SchoolIdList", "[]");
		params.put("SchoolProvinceIdList", "[]");
		params.put("SchoolCategoryIdList", "[]");
		params.put("SchoolTagList", "[]");
		params.put("SchoolNature", "[]");
		
	
		headers.put("Accept", "application/json, text/plain, */*");
		headers.put("Accept-Encoding", "gzip, deflate, br, zstd");
		headers.put("Accept-Language", "zh-CN,zh;q=0.9");
		headers.put("Cache-Control", "no-cache");
		headers.put("Connection", "keep-alive");
		headers.put("Content-Type", "application/json; charset=UTF-8");
		headers.put("Host", "tapi.cnjzy.net");
		headers.put("Origin", "https://t.cnjzy.net");
		headers.put("Pragma", "no-cache");
		headers.put("Referer", "https://t.cnjzy.net/");
		headers.put("Sec-Fetch-Dest", "empty");
		headers.put("Sec-Fetch-Mode", "cors");
		headers.put("Sec-Fetch-Site", "same-site");
		headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36");
		headers.put("sec-ch-ua", "\"Chromium\";v=\"130\", \"Google Chrome\";v=\"130\", \"Not?A_Brand\";v=\"99\"");
		headers.put("sec-ch-ua-mobile", "?0");
		headers.put("sec-ch-ua-platform", "\"Windows\"");
		headers.put("token", "6a4eda6d044e45ee862766b332d6e9b5");
		
		StringBuffer SQL = new StringBuffer();

		String resultPageList = HttpSendUtils.post("https://tapi.cnjzy.net/api/Public/LineSchooScoreList", params, headers);
		System.out.println(resultPageList);
		
		
		return SQL;
	}
		
		public static StringBuffer shanghairanking_cn_XUEKE() {
			Map<String, String> headers = new HashMap<>();
			StringBuffer SQL = new StringBuffer();


			String resultPageList = HttpSendUtils2.get("https://www.shanghairanking.cn/_nuxt/static/1728726515/rankings/bcsr/2024/0802/payload.js", headers);
			// System.out.println(resultPageList);
			JSONObject object = JSONObject.parseObject(resultPageList);
			JSONObject JSONObject = object.getJSONObject("data");
			JSONArray JSONArray = JSONObject.getJSONArray("majors");
			for(int i=0;i<JSONArray.size();i++) {
				JSONObject element = JSONArray.getJSONObject(i);
				
				String id = element.getString("id");
				String pid = element.getString("pid");
				String name = element.getString("name");
				String code = element.getString("code");
				int ordNo = element.getIntValue("ordNo");
				String remark = element.getString("remark");
				
				SQL.append(
						"insert into base_major_ruanke(id,pid,name,code,ordno,remark,univPublished,level) values('"+id+"','"
								+ pid + "','" + name + "','" + code + "',"+ordNo+",'"+remark+"',NULL,'1');\r\n");
				
				
				JSONArray newArray = element.getJSONArray("children");
				for(int ix=0;ix<newArray.size();ix++) {
					JSONObject newElement = newArray.getJSONObject(ix);
					
					String id1 = newElement.getString("id");
					String pid1 = newElement.getString("pid");
					String name1 = newElement.getString("name");
					String code1 = newElement.getString("code");
					int ordNo1 = newElement.getIntValue("ordNo");
					String remark1 = newElement.getString("remark");
					
					SQL.append(
							"insert into base_major_ruanke(id,pid,name,code,ordno,remark,univPublished,level) values('"+id1+"','"
									+ pid1 + "','" + name1 + "','" + code1 + "',"+ordNo1+",'"+remark1+"',NULL,'2');\r\n");
					
					JSONArray newArray2 = newElement.getJSONArray("children");
					for(int ixx=0;ixx<newArray2.size();ixx++) {
						JSONObject newElement2 = newArray2.getJSONObject(ixx);
						
						String id2 = newElement2.getString("id");
						String pid2 = newElement2.getString("pid");
						String name2 = newElement2.getString("name");
						String code2 = newElement2.getString("code");
						int ordNo2 = newElement2.getIntValue("ordNo");
						String remark2 = newElement2.getString("remark");
						String univPublished2 = newElement2.getString("univPublished");
						
						SQL.append(
								"insert into base_major_ruanke(id,pid,name,code,ordno,remark,univPublished,level) values('"+id2+"','"
										+ pid2 + "','" + name2 + "','" + code2 + "',"+ordNo2+",'"+remark2+"','"+univPublished2+"','3');\r\n");
					}
				}
				
			}
			
			
			writeTempFile(new File("F://就业报告//RUANKE/PAGE_1015.txt"), SQL);
			
			return SQL;
		}
		
		
		public static StringBuffer shanghairanking_cn_rankget() {
			Map<String, String> headers = new HashMap<>();
			StringBuffer SQL = new StringBuffer();


			String resultPageList = HttpSendUtils2.get("https://www.shanghairanking.cn/api/pub/v1/bcmr/rank?year=2024&majorCode=080214T", headers);
			// System.out.println(resultPageList);
			JSONObject object = JSONObject.parseObject(resultPageList);
			JSONObject JSONObject = object.getJSONObject("data");
			JSONArray JSONArray = JSONObject.getJSONArray("majors");
			for(int i=0;i<JSONArray.size();i++) {
				JSONObject element = JSONArray.getJSONObject(i);
				
				String id = element.getString("id");
				String pid = element.getString("pid");
				String name = element.getString("name");
				String code = element.getString("code");
				int ordNo = element.getIntValue("ordNo");
				String remark = element.getString("remark");
				
				
				JSONArray newArray = element.getJSONArray("children");
				for(int ix=0;ix<newArray.size();ix++) {
					JSONObject newElement = newArray.getJSONObject(ix);
					
					String id1 = newElement.getString("id");
					String pid1 = newElement.getString("pid");
					String name1 = newElement.getString("name");
					String code1 = newElement.getString("code");
					int ordNo1 = newElement.getIntValue("ordNo");
					String remark1 = newElement.getString("remark");
					
					
					JSONArray newArray2 = newElement.getJSONArray("children");
					for(int ixx=0;ixx<newArray2.size();ixx++) {
						JSONObject newElement2 = newArray2.getJSONObject(ixx);
						
						String id2 = newElement2.getString("id");
						String pid2 = newElement2.getString("pid");
						String name2 = newElement2.getString("name");
						String code2 = newElement2.getString("code");
						int ordNo2 = newElement2.getIntValue("ordNo");
						String remark2 = newElement2.getString("remark");
						String univPublished2 = newElement2.getString("univPublished");
						
						String URL = "https://www.shanghairanking.cn/api/pub/v1/bcmr/rank?year=2024&majorCode=" + code2;
						System.out.println(URL);
						String result = HttpSendUtils2.get(URL, headers);
						// System.out.println(resultPageList);
						JSONObject resultObject = JSONObject.parseObject(result);
						JSONObject data = resultObject.getJSONObject("data");
						JSONArray rankings = data.getJSONArray("rankings");
						
						for(int ixxx=0;ixxx<rankings.size();ixxx++) {
							JSONObject rank = rankings.getJSONObject(ixxx);
							String univCode = rank.getString("univCode");
							String univNameCn = rank.getString("univNameCn");
							String univUp = rank.getString("univUp");
							String univLogo = rank.getString("univLogo");
							String univLikeCount = rank.getString("univLikeCount");
							String province = rank.getString("province");
							String city = rank.getString("city");
							String ranking = rank.getString("ranking");
							String grade = rank.getString("grade");
							String score = rank.getString("score");
							String liked = rank.getString("liked");
							String inbound = rank.getString("inbound");
							
							SQL.append(
									"insert into ruanke_major_rank(univCode,univNameCn,univUp,univLogo,univLikeCount,province,city,ranking,grade,score,liked,inbound,major_code,rank_year) values('"+univCode+"','"
											+ univNameCn + "','" + univUp + "','" + univLogo + "','"+univLikeCount+"','"+province+"','"+city+"','"+ranking+"','"+grade+"','"+score+"','"+liked+"','"+inbound+"','"+code2+"',2024);\r\n");
							
					}
					}
				}
				
			}
			
			
			writeTempFile(new File("F://就业报告//RUANKE/PAGE_1016.txt"), SQL);
			
			return SQL;
		}

		public static void main(String[] args) {
			YOUZHIYUAN();

		}
		
		private static void writeTempFile(File file, StringBuffer sb) {
			try {
				BufferedWriter bw = new BufferedWriter(new FileWriter(file));
				bw.write(sb.toString());
				bw.flush();
				bw.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}

}
