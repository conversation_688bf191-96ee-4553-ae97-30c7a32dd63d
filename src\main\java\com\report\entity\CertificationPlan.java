package com.report.entity;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.HashMap;

/**
 * 考证规划实体类
 * 基于《大学四年考证规划》设计
 */
public class CertificationPlan {
    
    // 按年级分类的考证规划
    private Map<String, List<CertificationItem>> certificationsByYear;
    
    // 推荐考证时间线
    private List<CertificationTimeline> timeline;
    
    // 基于专业的推荐证书
    private List<String> majorSpecificCertifications;
    
    // 通用必考证书
    private List<String> universalCertifications;
    
    public CertificationPlan() {
        this.certificationsByYear = new HashMap<>();
        this.timeline = new ArrayList<>();
        this.majorSpecificCertifications = new ArrayList<>();
        this.universalCertifications = new ArrayList<>();
        
        initializeBasicCertifications();
    }
    
    /**
     * 初始化基础考证信息
     */
    private void initializeBasicCertifications() {
        // 大一大二阶段
        List<CertificationItem> freshmanSophomoreCerts = new ArrayList<>();
        freshmanSophomoreCerts.add(new CertificationItem("英语四六级(CET)", "必考", "大一大二", 
            "企业衡量毕业生英语水平的重要标准。2025年上半年笔试在6月14日，口试在5月24-25日。", "通常3月和9月报名"));
        freshmanSophomoreCerts.add(new CertificationItem("普通话证书", "推荐", "大一大二", 
            "师范、播音、主持等职业必需，部分省市对公务员也有要求。", "全年可考，各地时间不同，请关注当地语言文字网"));
        freshmanSophomoreCerts.add(new CertificationItem("全国计算机等级考试(NCRE)", "推荐", "大一大二", 
            "计算机应用知识与技能的全国性考试。2025年春季考试在3月29-31日。", "通常在考前2-3个月报名"));
        freshmanSophomoreCerts.add(new CertificationItem("驾驶证", "建议", "大一大二", 
            "大学阶段时间充足，是最适合考驾照的时期", "自行通过驾校预约"));
        freshmanSophomoreCerts.add(new CertificationItem("初级会计", "专业相关", "大一大二", 
            "会计专业学生首选，其他专业可选", "通常在11月份报名，次年5月考试"));
        
        certificationsByYear.put("大一大二", freshmanSophomoreCerts);
        
        // 大二大三阶段
        List<CertificationItem> sophomorJuniorCerts = new ArrayList<>();
        sophomorJuniorCerts.add(new CertificationItem("英语专业八级", "英语专业", "大二大三", 
            "英语专业学生必考，非英语专业无法参加", "每年3月上旬"));
        sophomorJuniorCerts.add(new CertificationItem("翻译证", "语言相关", "大二大三", 
            "全国翻译专业资格水平认证", "每年5月和10月"));
        sophomorJuniorCerts.add(new CertificationItem("导游证", "旅游管理", "大二大三", 
            "旅游管理专业必考，其他专业可选", "6-8月报名，11月笔试"));
        
        certificationsByYear.put("大二大三", sophomorJuniorCerts);
        
        // 大三大四阶段
        List<CertificationItem> juniorSeniorCerts = new ArrayList<>();
        juniorSeniorCerts.add(new CertificationItem("教师资格证", "教育相关", "大三大四", 
            "教育行业从业必备，含金量高", "上半年1月报名，3月笔试；下半年9月报名，11月笔试"));
        juniorSeniorCerts.add(new CertificationItem("注册会计师(CPA)", "会计专业", "大三大四", 
            "财会领域黄金职业证书。2025年专业阶段考试在8月23-24日。", "通常在4月报名"));
        juniorSeniorCerts.add(new CertificationItem("特许公认会计师(ACCA)", "会计专业", "大三大四", 
            "国际注册会计师，适用国际会计准则", "每年有四次考季，分别在3月、6月、9月、12月"));
        juniorSeniorCerts.add(new CertificationItem("法律职业资格证", "法学专业", "大三大四", 
            "法官、检察官、律师等职业必需", "6-7月报名，9月考试"));
        juniorSeniorCerts.add(new CertificationItem("护士资格证", "医学专业", "大三大四", 
            "护理专业必考", "12月报名，次年考试"));
        
        certificationsByYear.put("大三大四", juniorSeniorCerts);
    }
    
    /**
     * 考证项目实体
     */
    public static class CertificationItem {
        private String name;                // 证书名称
        private String category;            // 类别：必考/推荐/专业相关
        private String recommendedPeriod;   // 推荐考试时期
        private String description;         // 描述
        private String examSchedule;        // 考试安排
        private String difficulty;          // 难度等级
        private String importance;          // 重要程度
        private List<String> benefits;      // 获得益处
        
        public CertificationItem(String name, String category, String recommendedPeriod, 
                               String description, String examSchedule) {
            this.name = name;
            this.category = category;
            this.recommendedPeriod = recommendedPeriod;
            this.description = description;
            this.examSchedule = examSchedule;
            this.benefits = new ArrayList<>();
        }
        
        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }
        public String getRecommendedPeriod() { return recommendedPeriod; }
        public void setRecommendedPeriod(String recommendedPeriod) { this.recommendedPeriod = recommendedPeriod; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public String getExamSchedule() { return examSchedule; }
        public void setExamSchedule(String examSchedule) { this.examSchedule = examSchedule; }
        public String getDifficulty() { return difficulty; }
        public void setDifficulty(String difficulty) { this.difficulty = difficulty; }
        public String getImportance() { return importance; }
        public void setImportance(String importance) { this.importance = importance; }
        public List<String> getBenefits() { return benefits; }
        public void setBenefits(List<String> benefits) { this.benefits = benefits; }
    }
    
    /**
     * 考证时间线
     */
    public static class CertificationTimeline {
        private String year;            // 学年
        private String semester;        // 学期
        private String month;           // 月份
        private List<String> certifications; // 该时期推荐考证
        private String note;            // 备注
        
        public CertificationTimeline() {
            this.certifications = new ArrayList<>();
        }
        
        // Getters and Setters
        public String getYear() { return year; }
        public void setYear(String year) { this.year = year; }
        public String getSemester() { return semester; }
        public void setSemester(String semester) { this.semester = semester; }
        public String getMonth() { return month; }
        public void setMonth(String month) { this.month = month; }
        public List<String> getCertifications() { return certifications; }
        public void setCertifications(List<String> certifications) { this.certifications = certifications; }
        public String getNote() { return note; }
        public void setNote(String note) { this.note = note; }
    }
    
    // Getters and Setters
    public Map<String, List<CertificationItem>> getCertificationsByYear() { return certificationsByYear; }
    public void setCertificationsByYear(Map<String, List<CertificationItem>> certificationsByYear) { 
        this.certificationsByYear = certificationsByYear; 
    }
    
    public List<CertificationTimeline> getTimeline() { return timeline; }
    public void setTimeline(List<CertificationTimeline> timeline) { this.timeline = timeline; }
    
    public List<String> getMajorSpecificCertifications() { return majorSpecificCertifications; }
    public void setMajorSpecificCertifications(List<String> majorSpecificCertifications) { 
        this.majorSpecificCertifications = majorSpecificCertifications; 
    }
    
    public List<String> getUniversalCertifications() { return universalCertifications; }
    public void setUniversalCertifications(List<String> universalCertifications) { 
        this.universalCertifications = universalCertifications; 
    }
}