package com.career.utils.liuxue;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.career.utils.HttpSendUtils;
import com.career.utils.Tools;
import com.career.utils.zsky.ResultInfoBean;
import com.career.utils.zsky.SchoolInfo;
import com.career.utils.zsky.SchoolTagItem;

public class DealWithLX2 {

	public static void main(String args[]) throws Exception {
		//dealWithHEZUOBANXUE();
		for(int i=10;i<=10;i++) {
			//gogo(i);
		}
		liufusousuo_com();
	}
	
	public static void hebin() throws IOException {
		StringBuffer universityContent = new StringBuffer();
		int cnt = 0;
		for(int i=601;i<=683;i++) {
			File file = new File("G://留学案例库//SQL_"+i+".txt");
			BufferedReader bw = new BufferedReader(new FileReader(file));
			
			String str = null;
			while ((str = bw.readLine()) != null) {
				universityContent.append(str+"\r\n");
				cnt++;
			}
		}
		System.out.println(cnt);
		writeTempFile(new File("G://留学_RESULT_6.txt"), universityContent);
	}
	
	//抓取指南者留学
	public static void gogo(int page) {
		StringBuffer sql1 = new StringBuffer();
		Map<String, String> headers = new HashMap<>();
		Map<String, String> params = new HashMap<>();
		params.put("ctry", "0");
		params.put("pa", "p" + page);
		
		headers.put(":authority", "pc.compassedu.hk");
		headers.put(":path", "/v/offer/gainList");
		headers.put("Accept", "*/*");
		headers.put("Accept-Encoding", "gzip, deflate, br, zstd");
		headers.put("Accept-Language", "zh-CN,zh;q=0.9");
		headers.put("Cache-Control", "no-cache");
		headers.put("Connection", "keep-alive");
		headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
		//headers.put("Cookie", "PW9ydXnjjO8XS=60abeMZbhNbBWRNi2hyfp32UB3dRoh5PEK12eiwEWmjK4bVv2bCaL9YrMVuFyaqrwLzlwapiBS5qwfCGXSi1WSRG; PW9ydXnjjO8XT=0J5FTTMAKarsz4pOMoe13Ox4JuuFiqvPupWWYrD5IUhCUXGuvsrpAGLX.ZBkJxcBn_Fy8Jt_syIQOkZUBlAtaGgJ5zrgYXAB_.Yy6jQX1WAthkI2Gr6R3DMiuamBYa4ro5uIiTPQ.8q56klzt7RqomLosrSIkeLLbSDFRFbPzGTR14LX1apGcXEGJpS6geR5vOSc3kfQBlnrSVJmNRuiPJvyC_Us9KgCda0djNBD.6Zra8tjMPhKv522M6TwfHZ9ppVyhWfk1x81PwdRZkLiJEvfyxU3oZCUXlU1ZKT3CNsEmKstyzzZwKDTNlGn1s8TCCiTiMPkVel7m6431R94Bmkuf2etz4ysnSEUW0OUxKnaVArvY6JX2vqeHcsSAMlkWGwV4uLkBkq273kh51UhVLa");

		String url = "https://pc.compassedu.hk/v/offer/gainList";
		String res22 = HttpSendUtils.post(url, params, headers);
		JSONObject object = JSONObject.parseObject(res22);
		JSONArray JSONArray = object.getJSONArray("result");
		for(int m=0;m<JSONArray.size();m++) {
			JSONObject ele = JSONArray.getJSONObject(m);
			//System.out.println(ele.toString());
			String id_news = (String)ele.getString("id_news");
			
			
			if(Integer.parseInt(id_news) <= 35621) {
				continue;
			}
			
			String id_univ = (String)ele.getString("id_univ");
			String title_news = (String)ele.getString("title_news");
			String cname_news = (String)ele.getString("cname_news");
			String cuniv_news = (String)ele.getString("cuniv_news");
			String cmajr_news = (String)ele.getString("cmajr_news");
			String detail_news = (String)ele.getString("detail_news");
			String time_news = (String)ele.getString("time_news");
			String appdate_new = (String)ele.getString("appdate_new");
			String univrank_news = (String)ele.getString("univrank_news");
			String time_list = (String)ele.getString("time_list");
			
			sql1.append("insert into career_lx_cases(id_news,id_univ,title_news,cname_news,cuniv_news,cmajr_news,detail_news,time_news,appdate_new,univrank_news,time_list) values('"+id_news+"','"+id_univ+"','"+title_news+"','"+cname_news+"','"+cuniv_news+"','"+cmajr_news+"','"+detail_news+"','"+time_news+"','"+appdate_new+"','"+univrank_news+"','"+time_list+"');\r\n");
			
			
			
			
			
			
			System.out.println(page + "->" + id_news);
			
			String result = HttpSendUtils.get("https://www.compassedu.hk/newst_" + id_news, headers);
			
			Document document = Jsoup.parse(result);
			

			try {
	        Elements detail__module__studetail = document.getElementsByClass("detail__module__studetail");
	        if(detail__module__studetail != null && detail__module__studetail.size() > 0) {
	        	Elements xxx = detail__module__studetail.get(0).getElementsByClass("info-item");
	        	String school = xxx.get(2).getElementsByClass("value center").get(0).text();
	        	String major = xxx.get(3).getElementsByClass("value text-href").get(0).text();
	        	sql1.append("insert into career_lx_cases_ext values('"+id_news+"','院校专业','"+school+"','"+major+"');\r\n");
	        }
			}catch(Exception ex) {}
	        
	        Elements valueExp = document.getElementsByClass("value-exp");
	        if(valueExp != null && valueExp.size() > 0) {
		        Elements valueItems = valueExp.get(0).getElementsByClass("value-item");
		        for(int i=0;i<valueItems.size();i++) {
		        	String val = valueItems.get(i).text();
		        	sql1.append("insert into career_lx_cases_ext values('"+id_news+"','主要经历','"+(i+1)+"','"+val+"');\r\n");
		        }
	        }
	        
	        Elements detail__module__caseproject = document.getElementsByClass("detail__module__caseproject");
	        if(detail__module__caseproject != null && detail__module__caseproject.size() > 0) {
		        Elements projectList = detail__module__caseproject.get(0).getElementsByClass("project-list");
		        Elements labels = projectList.get(0).getElementsByClass("label");
		        Elements values = projectList.get(0).getElementsByClass("value");
		        for(int i=0;i<labels.size();i++) {
		        	String label = labels.get(i).text();
		        	String value = values.get(i).text();
		        	sql1.append("insert into career_lx_cases_ext values('"+id_news+"','项目简介','"+label+"','"+value+"');\r\n");
	
		        }
	        }
	        
	        Elements detail__module__limits = document.getElementsByClass("detail__module__limits");
	        if(detail__module__limits != null && detail__module__limits.size() > 0) {
		        Elements richtexts = detail__module__limits.get(0).getElementsByClass("richtext");
		        for(int i=0;i<richtexts.size();i++) {
		        	String richtext = richtexts.get(i).text();
		        	sql1.append("insert into career_lx_cases_ext values('"+id_news+"','申请要求','"+(i+1)+"','"+richtext+"');\r\n");
		        	
		        }
	        }
	        
	        Elements detail__module__cultivate = document.getElementsByClass("detail__module__cultivate");
	        if(detail__module__cultivate != null && detail__module__cultivate.size() > 0) {
	        	Elements richtexts = detail__module__cultivate.get(0).getElementsByClass("richtext");
		        for(int i=0;i<richtexts.size();i++) {
		        	String richtext = richtexts.get(i).text();
		        	sql1.append("insert into career_lx_cases_ext values('"+id_news+"','培养目标','"+(i+1)+"','"+richtext+"');\r\n");
		        }
	        }
	        
	        Elements detail__module__language = document.getElementsByClass("detail__module__language");
	        if(detail__module__language != null && detail__module__language.size() > 0) {
		        Elements rowItems = detail__module__language.get(0).getElementsByClass("row-item");
		        for(int i=0;i<rowItems.size();i++) {
		        	Elements text = rowItems.get(i).getElementsByClass("text");
		        	String mc = text.get(0).text();
		        	
		        	String zf = text.get(1).text();
		        	
		        	String xf = text.get(2).text();
		        	
		        	sql1.append("insert into career_lx_cases_ext values('"+id_news+"','"+mc+"','"+zf+"','"+xf+"');\r\n");
		        }
	        }
			
		}
		
		
		writeTempFile(new File("G://留学案例库//SQL0929_"+page+".txt"), sql1);
		//https://www.compassedu.hk/newst_35068
	}

	
	public static void tototo(int page) {
		Map<String, String> headers = new HashMap<>();
		Map<String, String> params = new HashMap<>();
		StringBuffer sql1 = new StringBuffer();
		params.put("ctry", "0");
		params.put("pa", "p" + page);
		
		headers.put("Referer", "https://zhaopin.csg.cn/");
		headers.put("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
		headers.put("Accept-Encoding", "gzip, deflate, br, zstd");
		headers.put("Accept-Language", "zh-CN,zh;q=0.9");
		headers.put("Cache-Control", "no-cache");
		headers.put("Connection", "keep-alive");
		headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
		//headers.put("Cookie", "PW9ydXnjjO8XS=60abeMZbhNbBWRNi2hyfp32UB3dRoh5PEK12eiwEWmjK4bVv2bCaL9YrMVuFyaqrwLzlwapiBS5qwfCGXSi1WSRG; PW9ydXnjjO8XT=0J5FTTMAKarsz4pOMoe13Ox4JuuFiqvPupWWYrD5IUhCUXGuvsrpAGLX.ZBkJxcBn_Fy8Jt_syIQOkZUBlAtaGgJ5zrgYXAB_.Yy6jQX1WAthkI2Gr6R3DMiuamBYa4ro5uIiTPQ.8q56klzt7RqomLosrSIkeLLbSDFRFbPzGTR14LX1apGcXEGJpS6geR5vOSc3kfQBlnrSVJmNRuiPJvyC_Us9KgCda0djNBD.6Zra8tjMPhKv522M6TwfHZ9ppVyhWfk1x81PwdRZkLiJEvfyxU3oZCUXlU1ZKT3CNsEmKstyzzZwKDTNlGn1s8TCCiTiMPkVel7m6431R94Bmkuf2etz4ysnSEUW0OUxKnaVArvY6JX2vqeHcsSAMlkWGwV4uLkBkq273kh51UhVLa");

		String url = "https://pc.compassedu.hk/v/offer/gainList";
		String res22 = HttpSendUtils.post(url, params, headers);
		JSONObject object = JSONObject.parseObject(res22);
		JSONArray JSONArray = object.getJSONArray("result");
		for(int m=0;m<JSONArray.size();m++) {
			JSONObject ele = JSONArray.getJSONObject(m);
			//System.out.println(ele.toString());
			String id_news = (String)ele.getString("id_news");
			String id_univ = (String)ele.getString("id_univ");
			String title_news = (String)ele.getString("title_news");
			String cname_news = (String)ele.getString("cname_news");
			String cuniv_news = (String)ele.getString("cuniv_news");
			String cmajr_news = (String)ele.getString("cmajr_news");
			String detail_news = (String)ele.getString("detail_news");
			String time_news = (String)ele.getString("time_news");
			String appdate_new = (String)ele.getString("appdate_new");
			String univrank_news = (String)ele.getString("univrank_news");
			String time_list = (String)ele.getString("time_list");
			
			sql1.append("insert into career_lx_cases() values('"+id_news+"','"+id_univ+"','"+title_news+"','"+cname_news+"','"+cuniv_news+"','"+cmajr_news+"','"+detail_news+"','"+time_news+"','"+appdate_new+"','"+univrank_news+"','"+time_list+"');\r\n");
			
			
			
			
			
			
			System.out.println(page + "->" + id_news);
			String result = HttpSendUtils.get("https://www.compassedu.hk/newst_" + id_news, headers);
			
			Document document = Jsoup.parse(result);
			

			try {
	        Elements detail__module__studetail = document.getElementsByClass("detail__module__studetail");
	        if(detail__module__studetail != null && detail__module__studetail.size() > 0) {
	        	Elements xxx = detail__module__studetail.get(0).getElementsByClass("info-item");
	        	String school = xxx.get(2).getElementsByClass("value center").get(0).text();
	        	String major = xxx.get(3).getElementsByClass("value text-href").get(0).text();
	        	sql1.append("insert into career_lx_cases_ext values('"+id_news+"','院校专业','"+school+"','"+major+"');\r\n");
	        }
			}catch(Exception ex) {}
	        
	        Elements valueExp = document.getElementsByClass("value-exp");
	        if(valueExp != null && valueExp.size() > 0) {
		        Elements valueItems = valueExp.get(0).getElementsByClass("value-item");
		        for(int i=0;i<valueItems.size();i++) {
		        	String val = valueItems.get(i).text();
		        	sql1.append("insert into career_lx_cases_ext values('"+id_news+"','主要经历','"+(i+1)+"','"+val+"');\r\n");
		        }
	        }
	        
	        Elements detail__module__caseproject = document.getElementsByClass("detail__module__caseproject");
	        if(detail__module__caseproject != null && detail__module__caseproject.size() > 0) {
		        Elements projectList = detail__module__caseproject.get(0).getElementsByClass("project-list");
		        Elements labels = projectList.get(0).getElementsByClass("label");
		        Elements values = projectList.get(0).getElementsByClass("value");
		        for(int i=0;i<labels.size();i++) {
		        	String label = labels.get(i).text();
		        	String value = values.get(i).text();
		        	sql1.append("insert into career_lx_cases_ext values('"+id_news+"','项目简介','"+label+"','"+value+"');\r\n");
	
		        }
	        }
	        
	        Elements detail__module__limits = document.getElementsByClass("detail__module__limits");
	        if(detail__module__limits != null && detail__module__limits.size() > 0) {
		        Elements richtexts = detail__module__limits.get(0).getElementsByClass("richtext");
		        for(int i=0;i<richtexts.size();i++) {
		        	String richtext = richtexts.get(i).text();
		        	sql1.append("insert into career_lx_cases_ext values('"+id_news+"','申请要求','"+(i+1)+"','"+richtext+"');\r\n");
		        	
		        }
	        }
	        
	        Elements detail__module__cultivate = document.getElementsByClass("detail__module__cultivate");
	        if(detail__module__cultivate != null && detail__module__cultivate.size() > 0) {
	        	Elements richtexts = detail__module__cultivate.get(0).getElementsByClass("richtext");
		        for(int i=0;i<richtexts.size();i++) {
		        	String richtext = richtexts.get(i).text();
		        	sql1.append("insert into career_lx_cases_ext values('"+id_news+"','培养目标','"+(i+1)+"','"+richtext+"');\r\n");
		        }
	        }
	        
	        Elements detail__module__language = document.getElementsByClass("detail__module__language");
	        if(detail__module__language != null && detail__module__language.size() > 0) {
		        Elements rowItems = detail__module__language.get(0).getElementsByClass("row-item");
		        for(int i=0;i<rowItems.size();i++) {
		        	Elements text = rowItems.get(i).getElementsByClass("text");
		        	String mc = text.get(0).text();
		        	
		        	String zf = text.get(1).text();
		        	
		        	String xf = text.get(2).text();
		        	
		        	sql1.append("insert into career_lx_cases_ext values('"+id_news+"','"+mc+"','"+zf+"','"+xf+"');\r\n");
		        }
	        }
			
		}
		
		
		writeTempFile(new File("G://留学案例库//SQL_"+page+".txt"), sql1);
		//https://www.compassedu.hk/newst_35068
	}

	
	
	public static void dealWithHEZUOBANXUE() throws Exception {
		StringBuffer SQL = new StringBuffer();
		
		Map<String, String> headers = new HashMap<>();
		Map<String, String> params = new HashMap<>();


		headers.put("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
		headers.put("Accept-Encoding", "gzip, deflate, br, zstd");
		headers.put("Accept-Language", "zh-CN,zh;q=0.9");
		headers.put("Cache-Control", "no-cache");
		headers.put("Connection", "keep-alive");
		headers.put("Cookie", "cookiesession1=678B286BSTUVWXYZBCDEFGHIJKLMDCA0; Hm_lvt_05c4b54fab7a9b720b7006e9b3bfb401=1715676738,1715833714; token=e646e7b0a05e94a6f9ca7851534887b4; ci_session=d31691da2968e0348623f346dab81245be3bc24b; Hm_lpvt_05c4b54fab7a9b720b7006e9b3bfb401=1715849193");
		headers.put("Referer", "https://www.crs.jsj.edu.cn/aproval/orglists/2");
		headers.put("Pragma", "no-cache");
		headers.put("Sec-Fetch-Dest", "document");
		headers.put("Sec-Fetch-Mode", "navigate");
		headers.put("Sec-Fetch-Site", "same-origin");
		headers.put("Sec-Fetch-User", "?1");
		headers.put("Upgrade-Insecure-Requests", "1");
		headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
		headers.put("sec-ch-ua", "\"Not A(Brand\";v=\"99\", \"Google Chrome\";v=\"121\", \"Chromium\";v=\"121\"");
		headers.put("sec-ch-ua-mobile", "?0");
		headers.put("sec-ch-ua-platform", "\"Windows\"");
		

		
for(int w=1;w<=30;w++) {		
	String res22 = HttpSendUtils.get("https://www.crs.jsj.edu.cn/aproval/localbyarea/" + w, headers);
	
	
		
		Document document = Jsoup.parse(res22);
		
        Elements list = document.getElementsByTag("li");
        for(int i=0;i<list.size();i++) {
        	Elements elements = list.get(i).getElementsByAttribute("href");
        	String url = elements.get(0).attr("href").trim();
        	
        	
        	
        	
        	String res = HttpSendUtils.get(url, headers);
        	if(res.indexOf("数据库中不存在该信息") != -1) {
        		continue;
        	}
        	System.out.println(url);
        	Document result = Jsoup.parse(res.toString());
        	Elements maincontent = result.getElementsByClass("maincontent");
        	Elements listEle = maincontent.get(0).getElementsByTag("tbody").get(0).getElementsByTag("td");
        	
        	List<String> listValue = new ArrayList<>();
        	boolean hasQZYF = false;
        	for(int ix=0;ix<listEle.size();ix++) {
        		String elements2 = listEle.get(ix).text().trim();
        		if(elements2.equals("机构名称") || elements2.equals("项目名称")) {
        			ix++;
        			listValue.add(listEle.get(ix).text().trim()); //1-名称
        			ix++;
        			ix++;
        			ix++;
        			ix++;
        			listValue.add(listEle.get(ix).text().trim()); // 2-地址
        			continue;
        		}
        		if(elements2.equals("中外合作办学者")) {
        			ix++;
        			listValue.add(listEle.get(ix).text().trim()); // 3国内大学
        			ix++;
        			for(int k =ix;k<ix+5;k++) {
        				String tp = listEle.get(k).text();
        				if(tp.indexOf("外方") != -1) {
        					listValue.add(tp); // 4国外大学
        					break;
        				}
        			}
        			continue;
        		}
        		if(elements2.equals("办学层次和类别")) {
        			ix++;
        			listValue.add(listEle.get(ix).text().trim());
        			continue;
        		}
        		if(elements2.equals("学制")) {
        			ix++;
        			listValue.add(listEle.get(ix).text().trim());
        			continue;
        		}
        		if(elements2.equals("每期招生人数") || elements2.equals("办学规模")) {
        			ix++;
        			listValue.add(listEle.get(ix).text().trim());
        			continue;
        		}
        		if(elements2.equals("招生起止年份")) {
        			ix++;
        			listValue.add(listEle.get(ix).text().trim());
        			hasQZYF = true;
        			continue;
        		}
        		if(elements2.equals("招生方式")) {
        			ix++;
        			listValue.add(listEle.get(ix).text().trim());
        			continue;
        		}
        		if(elements2.equals("开设专业或课程")) {
        			ix++;
        			listValue.add(listEle.get(ix).text().trim());
        			continue;
        		}
        		if(elements2.equals("颁发证书")) {
        			ix++;
        			listValue.add(listEle.get(ix).text().trim());//中方证书
        			ix++;
        			listValue.add(listEle.get(ix).text().trim());//外放证书
        			continue;
        		}
        		if(elements2.equals("审批机关")) {
        			ix++;
        			listValue.add(listEle.get(ix).text().trim());
        			continue;
        		}
        		if(elements2.indexOf("编号") != -1) {
        			ix++;
        			listValue.add(listEle.get(ix).text().trim());
        			continue;
        		}
        		if(elements2.indexOf("有效期") != -1) {
        			ix++;
        			listValue.add(listEle.get(ix).text().trim());
        			continue;
        		}
        		if(elements2.indexOf("备注") != -1) {
        			ix++;
        			listValue.add(listEle.get(ix).html().trim());
        			continue;
        		}
        		
        	}
        	
        	
        	Elements tableElements = maincontent.get(0).getElementsByTag("table");
        	if(tableElements.size() == 3) {
        		listValue.add(tableElements.get(2).html());
        	}else {
        		listValue.add("--");
        	}
        	
        	listValue.add("--");
        	listValue.add("--");
        	listValue.add("--");
        	
        	if(hasQZYF) {
    			SQL.append("insert into career_lx_hezuo values('"+url+"','"+listValue.get(0)+"','"+listValue.get(1)+"','"+listValue.get(2)+"','"+listValue.get(3)+"','"+listValue.get(4)+"','"+listValue.get(5)+"','"+listValue.get(6)+"','"+listValue.get(7)+"','"+listValue.get(8)+"','"+listValue.get(9)+"','"+listValue.get(10)+"','"+listValue.get(11)+"','"+listValue.get(12)+"','"+listValue.get(13)+"','"+listValue.get(14)+"','"+listValue.get(15)+"');\r\n");
    		}else {
    			SQL.append("insert into career_lx_hezuo values('"+url+"','"+listValue.get(0)+"','"+listValue.get(1)+"','"+listValue.get(2)+"','"+listValue.get(3)+"','"+listValue.get(4)+"','"+listValue.get(5)+"','"+listValue.get(6)+"','--','"+listValue.get(7)+"','"+listValue.get(8)+"','"+listValue.get(9)+"','"+listValue.get(10)+"','"+listValue.get(11)+"','"+listValue.get(12)+"','"+listValue.get(13)+"','"+listValue.get(14)+"');\r\n");
    		}
        	
        	
        	
        }
}

        
        writeTempFile(new File("G://XDF_ATTR_ALL_1_SQL.txt"), SQL);
	}
	
	
	
	
	public static void dealWithXinDFUniversityDetail(int folderNumber) throws Exception {
		StringBuffer rankSQL = new StringBuffer();
		File files = new File("E:\\xindongfang\\"+folderNumber+"\\");
		File[] fileList = files.listFiles();
		int index = 0;
		for(File file : fileList) {
			if(file.isDirectory()) {
				continue;
			}
			BufferedReader bw = new BufferedReader(new FileReader(file));
			StringBuffer universityContent = new StringBuffer();
			String str = null;
			while ((str = bw.readLine()) != null) {
				universityContent.append(str);
			}
			
			String id = file.getName().substring(0,file.getName().indexOf(".txt"));
			String contentShort = null;
			
			Document document = Jsoup.parse(universityContent.toString());
			Element element = document.head().select("meta[name=description]").first();
	        if (element != null){
	        	contentShort = element.attr("content");
	        	rankSQL.append("insert into career_lx_university_xdf_attr_temp(id,attr_type,attr_ind,attr_name,attr_value) values('"+id+"','院校介绍','介绍缩写','介绍缩写','"+Tools.replaceX27(element.attr("content"))+"');\r\n");
	        }
	        
	        Elements yxjj_pageElements = document.getElementsByClass("page yxjj_page");
	        if(yxjj_pageElements != null && yxjj_pageElements.size() > 0) {
		        Elements elements = yxjj_pageElements.get(0).getElementsByClass("pm_list pm_1 clearfix");
		        if(elements != null && elements.size() > 0) {
		        	Elements elementItemNames = elements.get(0).getElementsByTag("span");
		        	Elements elementItemValues = elements.get(0).getElementsByTag("b");
		        	if(elementItemNames != null && elementItemNames.size() > 0) {
		        		for(int k = 0; k < elementItemNames.size(); k++) {
		        			//System.out.println(id+","+elementItemNames.get(k).text()+"," +elementItemValues.get(k).text());
		        			rankSQL.append("insert into career_lx_university_xdf_attr_temp(id,attr_type,attr_ind,attr_name,attr_value) values('"+id+"','排名','世界排名','"+Tools.replaceX27(elementItemNames.get(k).text())+"','"+Tools.replaceX27(elementItemValues.get(k).text())+"');\r\n");
		        		}
		        	}
		        }
		        
		        
		        elements = yxjj_pageElements.get(0).getElementsByClass("pm_list pm_2 clearfix");
		        if(elements != null && elements.size() > 0) {
		        	Elements elementItemNames = elements.get(0).getElementsByTag("span");
		        	Elements elementItemValues = elements.get(0).getElementsByTag("b");
		        	if(elementItemNames != null && elementItemNames.size() > 0) {
		        		for(int k = 0; k < elementItemNames.size(); k++) {
		        			//System.out.println(file.getName().substring(0,file.getName().indexOf(".txt"))+","+elementItemNames.get(k).text()+"," +elementItemValues.get(k).text());
		        			rankSQL.append("insert into career_lx_university_xdf_attr_temp(id,attr_type,attr_ind,attr_name,attr_value) values('"+id+"','排名','区域排名','"+Tools.replaceX27(elementItemNames.get(k).text())+"','"+Tools.replaceX27(elementItemValues.get(k).text())+"');\r\n");
		        		}
		        	}
		        }
		        
		        
		        elements = yxjj_pageElements.get(0).getElementsByClass("clearfix");
		        if(elements != null && elements.size() > 0) {
		        	Elements elementItemNames = elements.get(0).getElementsByTag("dt");
		        	Elements elementItemValues = elements.get(0).getElementsByTag("dd");
		        	if(elementItemNames != null && elementItemNames.size() > 0) {
		        		for(int k = 0; k < elementItemNames.size(); k++) {
		        			//System.out.println(file.getName().substring(0,file.getName().indexOf(".txt"))+","+elementItemNames.get(k).text()+"," +elementItemValues.get(k).text());
		        			rankSQL.append("insert into career_lx_university_xdf_attr_temp(id,attr_type,attr_ind,attr_name,attr_value) values('"+id+"','院校介绍','基本信息','"+Tools.replaceX27(elementItemNames.get(k).text())+"','"+Tools.replaceX27(elementItemValues.get(k).text())+"');\r\n");
		        		}
		        	}
		        }
		        
		        elements = yxjj_pageElements.get(0).getElementsByClass("item style_text");
		        if(elements != null && elements.size() > 0) {
		        	for(int xx = 0; xx < elements.size(); xx++) {
			        	Elements elementItemNames = elements.get(xx).getElementsByClass("tit");
			        	Elements elementItemValues = elements.get(xx).getElementsByClass("con");
			        	if(elementItemNames != null && elementItemNames.size() > 0) {
			        		for(int k = 0; k < elementItemNames.size(); k++) {
			        			//System.out.println(file.getName().substring(0,file.getName().indexOf(".txt"))+","+elementItemNames.get(k).text()+"," +elementItemValues.get(k).text());
			        			rankSQL.append("insert into career_lx_university_xdf_attr_temp(id,attr_type,attr_ind,attr_name,attr_value) values('"+id+"','院校介绍','院校介绍','"+Tools.replaceX27(elementItemNames.get(k).text())+"','"+Tools.replaceX27(elementItemValues.get(k).text())+"');\r\n");
			        		}
			        	}
		        	}
		        }
	        }
	        
	        
	        
	        Elements sqgl_page_listElements = document.getElementsByClass("sqgl_page_list bk");
	        if(sqgl_page_listElements != null && sqgl_page_listElements.size() > 0) {
		        Elements elements = sqgl_page_listElements.get(0).getElementsByClass("item style_1");
		        if(elements != null && elements.size() > 0) {
		        	for(int xx = 0; xx < elements.size(); xx++) {
		        		
		        		Elements elementItemNameAndValues = elements.get(xx).getElementsByTag("li");
			        	if(elementItemNameAndValues != null && elementItemNameAndValues.size() > 1) {
			        		for(int xxx =0;xxx<elementItemNameAndValues.size();xxx++) {
			        			Elements elementItemNameAndValuesItems = elementItemNameAndValues.get(xxx).getElementsByTag("span");
			        			rankSQL.append("insert into career_lx_university_xdf_attr_temp(id,attr_type,attr_ind,attr_name,attr_value) values('"+id+"','院校介绍','申请与费用','"+Tools.replaceX27(elementItemNameAndValuesItems.get(0).text())+"','"+Tools.replaceX27(elementItemNameAndValuesItems.get(1).text())+"');\r\n");
			        		}
			        	}
		        	}
		        }
		        
		        elements = sqgl_page_listElements.get(0).getElementsByClass("item ksyq style_1");
		        if(elements != null && elements.size() > 0) {
		        	for(int xx = 0; xx < elements.size(); xx++) {
		        		Elements elementItemNameAndValues = elements.get(xx).getElementsByTag("li");
			        	if(elementItemNameAndValues != null && elementItemNameAndValues.size() > 1) {
			        		for(int xxx =0;xxx<elementItemNameAndValues.size();xxx++) {
			        			Elements elementItemNameAndValuesItems = elementItemNameAndValues.get(xxx).getElementsByTag("span");
			        			rankSQL.append("insert into career_lx_university_xdf_attr_temp(id,attr_type,attr_ind,attr_name,attr_value) values('"+id+"','院校介绍','考试要求','"+Tools.replaceX27(elementItemNameAndValuesItems.get(0).text())+"','"+Tools.replaceX27(elementItemNameAndValuesItems.get(1).text())+"');\r\n");
			        		}
			        	}
		        	}
		        }
		        
		        elements = sqgl_page_listElements.get(0).getElementsByClass("item style_text");
		        if(elements != null && elements.size() > 0) {
		        	for(int xx = 0; xx < elements.size(); xx++) {
		        		Elements elementItemNames = elements.get(xx).getElementsByClass("tit");
			        	Elements elementItemValues = elements.get(xx).getElementsByClass("con");
			        	if(elementItemNames != null && elementItemNames.size() > 0) {
			        		for(int k = 0; k < elementItemNames.size(); k++) {
			        			rankSQL.append("insert into career_lx_university_xdf_attr_temp(id,attr_type,attr_ind,attr_name,attr_value) values('"+id+"','院校介绍','院校介绍','"+Tools.replaceX27(elementItemNames.get(k).text())+"','"+Tools.replaceX27(elementItemValues.get(k).text())+"');\r\n");
			        		}
			        	}
		        	}
		        }
	        }
	        
	        
	        
	        
	        Elements xyzy_pageElements = document.getElementsByClass("page xyzy_page qtSensorsdata");
	        if(xyzy_pageElements != null && xyzy_pageElements.size() > 0) {
		        Elements elements = xyzy_pageElements.get(0).getElementsByClass("item style_label");
		        if(elements != null && elements.size() > 0) {
		        	for(int xx = 0; xx < elements.size(); xx++) {
		        		Elements elementItemNames = elements.get(xx).getElementsByClass("tit");
			        	Elements elementItemValues = elements.get(xx).getElementsByClass("con");
			        	if(elementItemNames != null && elementItemNames.size() > 0) {
			        		for(int k = 0; k < elementItemNames.size(); k++) {
			        			rankSQL.append("insert into career_lx_university_xdf_attr_temp(id,attr_type,attr_ind,attr_name,attr_value) values('"+id+"','院校介绍','优势专业','"+Tools.replaceX27(elementItemNames.get(k).text())+"','"+Tools.replaceX27(elementItemValues.get(k).text())+"');\r\n");
			        		}
			        	}
		        	}
		        }
		        
		        elements = xyzy_pageElements.get(0).getElementsByClass("table");
		        if(elements != null && elements.size() > 0) {
		        	for(int xx = 0; xx < elements.size(); xx++) {
		        		Elements elementItemNames = elements.get(xx).getElementsByTag("li");
			        	if(elementItemNames != null && elementItemNames.size() > 0) {
			        		for(int k = 0; k < elementItemNames.size(); k++) {
			        			Elements elementItemNameValueP1s = elementItemNames.get(k).getElementsByClass("p1");
			        			Elements elementItemNameValueP2s = elementItemNames.get(k).getElementsByClass("p2");
			        			Elements elementItemNameValueP3s = elementItemNames.get(k).getElementsByClass("p3");
			        			String tempP2 = elementItemNameValueP2s.get(0).text();
			        			if(!Tools.isEmpty(tempP2)) {
			        				tempP2 = "[" + tempP2 + "]";
			        			}
			        			if(elementItemNameValueP1s != null && elementItemNameValueP1s.size() > 0) {
			        				rankSQL.append("insert into career_lx_university_xdf_attr_temp(id,attr_type,attr_ind,attr_name,attr_value) values('"+id+"','院校介绍','院系专业','"+Tools.replaceX27(elementItemNameValueP1s.get(0).text())+tempP2+"','"+Tools.replaceX27(elementItemNameValueP3s.get(0).text())+"');\r\n");
			        			}
			        		}
			        	}
		        	}
		        }
	        }
	        
	        Elements yx_decElements = document.getElementsByClass("yx_dec");
	        if(yx_decElements != null && yx_decElements.size() > 0) {
		        Elements elements = yx_decElements.get(0).getElementsByTag("i");
		        if(elements != null && elements.size() > 0) {
		        	rankSQL.append("insert into career_lx_university_xdf_attr_temp(id,attr_type,attr_ind,attr_name,attr_value) values('"+id+"','院校介绍','基本信息','官方网站','"+Tools.replaceX27(elements.get(0).text())+"');\r\n");
		        	if(elements.size() > 1) {
		        		rankSQL.append("insert into career_lx_university_xdf_attr_temp(id,attr_type,attr_ind,attr_name,attr_value) values('"+id+"','院校介绍','基本信息','官方电话','"+Tools.replaceX27(elements.get(1).text())+"');\r\n");
		        	}
		        	if(elements.size() > 2) {
		        		rankSQL.append("insert into career_lx_university_xdf_attr_temp(id,attr_type,attr_ind,attr_name,attr_value) values('"+id+"','院校介绍','基本信息','Email','"+Tools.replaceX27(elements.get(2).text())+"');\r\n");
		        	}
		        }
	        }
		}
		
		
		writeTempFile(new File("E://xindongfang//result//XDF_ATTR_ALL_"+folderNumber+"_SQL.txt"), rankSQL);
	}
	
	
	public static void dealMajor() throws Exception {
		StringBuffer SQL = new StringBuffer();
		
		File files = new File("E:\\xuhang\\");
		File[] fileList = files.listFiles();
		int index = 0;
		for(File file : fileList) {
			if(file.isDirectory()) {
				continue;
			}
			BufferedReader bw = new BufferedReader(new FileReader(file));
			StringBuffer sb = new StringBuffer();
			String str = null;
			while ((str = bw.readLine()) != null) {
				sb.append(str);
			}
			JSONArray array = JSONArray.parseArray(sb.toString());
			
			for(int x = 0; x < array.size(); x++) {
				JSONArray each = array.getJSONArray(x);
				String qs24 = each.get(0).toString();
				String qs23 = each.get(5).toString();
				String name_cn = each.get(1).toString();
				String school_url = name_cn.substring(name_cn.indexOf("https://www"), name_cn.indexOf("' rel='' target="));
				
				LXHttpSendUtils.runPathWayComGetRank(school_url);
				
				
				
				name_cn = name_cn.substring(name_cn.indexOf(" target='_blank'>") + " target='_blank'>".length(), name_cn.indexOf("</a>"));
				//System.out.println(file.getName() + " -- " +school_url);
				String name_en = each.get(2).toString();
				name_en = name_en.substring(name_en.indexOf(" target='_blank'>") + " target='_blank'>".length(), name_en.indexOf("</a>"));
				
				name_cn = name_cn.replaceAll("'", "*");
				name_en = name_en.replaceAll("'", "*");
				
				String logo_url = each.get(3).toString();
				if(!Tools.isEmpty(logo_url)) {
					logo_url = logo_url.substring(logo_url.indexOf("https://www"), logo_url.indexOf("' alt='"));
				}
				String province = each.get(4).toString();
				String province_url = province.substring(province.indexOf("https://www"), province.indexOf("' rel='' target="));
				province = province.substring(province.indexOf(" target='_blank'>") + " target='_blank'>".length(), province.indexOf("</a>"));
				
				SQL.append("INSERT INTO career_lx_university_us_temp(qs24,qs23,name_cn,name_en,logo_url,province,school_url,province_url) VALUES('"+qs24+"','"+qs23+"','"+name_cn+"','"+name_en+"','"+logo_url+"','"+province+"','"+school_url+"','"+province_url+"');\r\n");
			} 
		}
		

		//writeTempFile(new File("E://xuhang//result//US_SQL.txt"), SQL);
	}
	
	
	public static void dealXinDongFang() throws Exception {
		StringBuffer SQL = new StringBuffer();
		
		File files = new File("E:\\xindongfang\\");
		File[] fileList = files.listFiles();

		for(File file : fileList) {
			if(file.isDirectory()) {
				continue;
			}
			
			if(file.getName().indexOf("result.txt") == -1) {
				continue;
			}
			
			BufferedReader bw = new BufferedReader(new FileReader(file));
			StringBuffer sb = new StringBuffer();
			String str = null;
			while ((str = bw.readLine()) != null) {
				sb.append(str);
			}
			JSONObject object = JSONObject.parseObject(sb.toString());
			JSONArray array = object.getJSONArray("data");
			/**
			 * {
			 * "application":",02,03",
			 * "localRankValue":"3",
			 * "worldRankName":"USNEWS",
			 * "worldRankValue":"1",
			 * "city":"美国-马萨诸塞-波士顿",
			 * "nature":"私立",
			 * "LOGO":"https://liuxue.xdf.cn/university/pic/P020230426343796291180.jpg",
			 * "Url":"https://liuxue.xdf.cn/university/4406302/",
			 * "times":"5244",
			 * "ename":"HarvardUniversity",
			 * "Id":"4406302",
			 * "localRankName":"USNEWS",
			 * "cname":"哈佛大学"}
			 */
			for(int x = 0; x < array.size(); x++) {
				JSONObject each = array.getJSONObject(x);
				String application = each.getString("application");
				String localRankValue = Tools.trim(each.getString("localRankValue"));
				String worldRankName = Tools.trim(each.getString("worldRankName"));
				String worldRankValue = Tools.trim(each.getString("worldRankValue"));
				String city = each.getString("city");
				String nature = each.getString("nature");
				String LOGO = each.getString("LOGO");
				String Url = each.getString("Url");
				String times = each.getString("times");
				String ename = each.getString("ename");
				String Id = each.getString("Id");
				String localRankName = Tools.trim(each.getString("localRankName"));
				String cname = each.getString("cname");
				
				//https://liuxue.xdf.cn/university/4415695/
				//LXHttpSendUtils.runXinDongFang(Integer.parseInt(Id));
				if(Tools.isEmpty(localRankName) && Tools.isEmpty(localRankValue) && Tools.isEmpty(worldRankName) && Tools.isEmpty(worldRankValue)) {
					continue;
				}
				SQL.append("UPDATE career_lx_university_xdf_temp x set x.localRankName = '"+localRankName+"', x.localRankValue = '"+localRankValue+"', x.worldRankName = '"+worldRankName+"', x.worldRankValue = '"+worldRankValue+"' WHERE x.id = '"+Id+"';\r\n");
				//SQL.append("INSERT INTO career_lx_university_xdf_temp(application,localRankValue,worldRankName,city,nature,LOGO,Url,times,ename,Id,localRankName,cname) VALUES('"+application+"','"+localRankValue+"','"+worldRankName+"','"+city+"','"+nature+"','"+LOGO+"','"+Url+"','"+times+"','"+ename+"','"+Id+"','"+localRankName+"','"+cname+"');\r\n");
			} 
		}
		

		writeTempFile(new File("E://xindongfang//result//XDF_UPDATE_SQL.txt"), SQL);
	}
	
	public static void dealXinDongFangArticle() throws Exception {
		StringBuffer SQL = new StringBuffer();
		
		File files = new File("E:\\xindongfang\\");
		File[] fileList = files.listFiles();

		for(File file : fileList) {
			
			if(file.isDirectory()) {
				continue;
			}
			if(!file.getName().equals("article.txt")) {
				continue;
			}
			
			System.out.println(file.getName());
			
			BufferedReader bw = new BufferedReader(new FileReader(file));
			StringBuffer sb = new StringBuffer();
			String str = null;
			while ((str = bw.readLine()) != null) {
				sb.append(str);
			}
			JSONObject object = JSONObject.parseObject(sb.toString());
			JSONArray array = object.getJSONArray("data");
			/**
			 {"content":"加拿大，作为一个教育水平高、生活环境宜居的国家，吸引了众多国际学生。本文将为您介绍几所在加拿大相对容易申请的大学，帮助您重新规划学术道路。",
			 "time":"2024.02.18",
			 "title":"考研失败后留学加拿大有哪些大学可以申请？",
			 "level":"研究生","hitscount":"101",
			 "tag":"留学资讯",
			 "consult":{"title":"加拿大留学咨询顾问","consultName":"邱燕","level":"01,02,03","userName":"qiuyan8","pic":"https://liuxue.xdf.cn/blog/qiuyan8/P020211206556692643375.jpg","url":"https://liuxue.xdf.cn/blog/qiuyan8/","country":"CA"},
			 "url":"https://liuxue.xdf.cn/canada/postgraduate/5039570.shtml",
			 "country":"加拿大"}
			 */
			for(int x = 0; x < array.size(); x++) {
				JSONObject each = array.getJSONObject(x);
				String content = each.getString("content");
				content = content.replaceAll("'", "&#x27;");
				String time = each.getString("time");
				String title = each.getString("title");
				title = title.replaceAll("'", "&#x27;");
				String level = each.getString("level");
				String tag = each.getString("tag");
				String consult = each.getString("consult");
				String url = each.getString("url");
				String country = each.getString("country");
				
				//https://liuxue.xdf.cn/canada/undergraduate/5016092.shtml
				LXHttpSendUtils.runXinDongFangArticle(url);
				
				SQL.append("INSERT INTO career_lx_university_xdf_article_temp(content,time,title,level,tag,url,country) VALUES('"+content+"','"+time+"','"+title+"','"+level+"','"+tag+"','"+url+"','"+country+"');\r\n");
			} 
		}
		

		writeTempFile(new File("E://xindongfang//result//XDF_ARTICLE_SQL.txt"), SQL);
	}
	
	
	public static void dealQS() throws Exception {
		StringBuffer SQL = new StringBuffer();
		
		File files = new File("F:\\留学\\");
		File[] fileList = files.listFiles();

		for(File file : fileList) {
			
			if(file.isDirectory()) {
				continue;
			}
			if(!file.getName().equals("QS2024_EN.txt")) {
				continue;
			}
			
			System.out.println(file.getName());
			
			BufferedReader bw = new BufferedReader(new FileReader(file));
			StringBuffer sb = new StringBuffer();
			String str = null;
			while ((str = bw.readLine()) != null) {
				sb.append(str);
			}
			JSONArray array = JSONArray.parseArray(sb.toString());
			/**
			 {
    "core_id": "410",
    "country": "United States",
    "guide": "",
    "nid": "294850",
    "title": "<div class=\"td-wrap\"><a href=\"/universities/massachusetts-institute-technology-mit\" class=\"uni-link\">麻省理工学院</a></div>",
    "logo": "/sites/default/files/massachusetts-institute-of-technology-mit_410_small.jpg",
    "score": "100",
    "rank_display": "1",
    "asia_overall_ranking": "",
    "region": "North America",
    "stars": "",
    "recm": "0--",
    "dagger": false,
    "program_type": "",
    "ind_0": "<div class=\"td-wrap\">100</div>",
    "rank_d_0": "<div class=\"td-wrap\">4</div>",
    "rank_0": "4",
    "ind_1": "<div class=\"td-wrap\">100</div>",
    "rank_d_1": "<div class=\"td-wrap\">2</div>",
    "rank_1": "2",
    "ind_2": "<div class=\"td-wrap\">100</div>",
    "rank_d_2": "<div class=\"td-wrap\">16</div>",
    "rank_2": "16" //buda University
  },
			 */
			for(int x = 0; x < array.size(); x++) {
				JSONObject each = array.getJSONObject(x);
				String core_id = each.getString("core_id");
				String country = each.getString("country");
				String region = each.getString("region");
				String rank_display = each.getString("rank_display");
				rank_display = rank_display.replaceAll("=", "");
				int rank = 0;
				if(rank_display.indexOf("-") != -1) {
					rank = Integer.parseInt(rank_display.split("-")[0]);
				}else if(rank_display.indexOf("+") != -1) {
					rank = Integer.parseInt(rank_display.substring(0,4));
				}else {
					rank = Integer.parseInt(rank_display);
				}
				//
				String title = each.getString("title");
				title = title.replaceAll("'", "&#x27;");
				title = title.substring(0, title.indexOf("</a></div>"));
				String yxmc = title.substring(title.lastIndexOf(">")+1);
				System.out.println(yxmc);
				
				//https://liuxue.xdf.cn/canada/undergraduate/5016092.shtml
				//LXHttpSendUtils.runXinDongFangArticle(url);
				
				SQL.append("update career_university_rank_temp set yxmc_en_org = '"+yxmc+"' where core_id = '"+core_id+"';\r\n");
			} 
		}
		

		writeTempFile(new File("F:\\留学\\RANK_EN_SQL.txt"), SQL);
	}
	
	
	public static void dealQSMajor() throws Exception {
		StringBuffer SQL = new StringBuffer();
		
		File file = new File("F:\\留学\\m.txt");
		BufferedReader bw = new BufferedReader(new FileReader(file));
		StringBuffer sb = new StringBuffer();
		String str = null;
		while ((str = bw.readLine()) != null) {
			sb.append(str);
		}
		JSONArray array = JSONArray.parseArray(sb.toString());
		/**
		  "core_id": "253",
    "country": "United States",
    "guide": "",
    "nid": "294270",
    "title": "<div class=\"td-wrap\"><a href=\"/universities/harvard-university\" class=\"uni-link\">哈佛大学</a></div>",
    "logo": "/sites/default/files/harvard-university_253_small.jpg",
    "score": "98.6",
    "rank_display": "1",
    "asia_overall_ranking": "",
    "region": "North America",
    "stars": "",
    "recm": "0--",
    "dagger": false,
    "program_type": "",
    "ind_0": "<div class=\"td-wrap\">100</div>",
    "rank_d_0": "<div class=\"td-wrap\">1</div>",
    "rank_0": "1",
    "ind_1": "<div class=\"td-wrap\">100</div>",
    "rank_d_1": "<div class=\"td-wrap\">1</div>",
    "rank_1": "1",
    "ind_2": "<div class=\"td-wrap\">93.2</div>",
    "rank_d_2": "<div class=\"td-wrap\">5</div>",
    "rank_2": "5"
  },
		 */
		for(int x = 0; x < array.size(); x++) {
			JSONObject each = array.getJSONObject(x);
			String core_id = each.getString("core_id");
			String rank_display = each.getString("rank_display");
			rank_display = rank_display.replaceAll("=", "");
			int rank = 0;
			if(rank_display.indexOf("-") != -1) {
				rank = Integer.parseInt(rank_display.split("-")[0]);
			}else if(rank_display.indexOf("+") != -1) {
				rank = Integer.parseInt(rank_display.substring(0,4));
			}else {
				rank = Integer.parseInt(rank_display);
			}
			
			//https://liuxue.xdf.cn/canada/undergraduate/5016092.shtml
			//LXHttpSendUtils.runXinDongFangArticle(url);
			
			SQL.append("insert into career_lx_major_qs_rank(core_id,major_cn,rank_desc,rank_num) values('"+core_id+"','酒店管理','"+rank_display+"',"+rank+");\r\n");
		} 
		

		writeTempFile(new File("F:\\留学\\RANK_MAJOR_RANK酒店管理_SQL.txt"), SQL);
	}
	
	
	public static void dealQSMajorAll() throws Exception {
		StringBuffer SQL = new StringBuffer();
		
		File files = new File("F:\\留学\\");
		File[] fileList = files.listFiles();

		for(File file : fileList) {
			
			if(file.isDirectory()) {
				continue;
			}
			if(!file.getName().startsWith("RANK_MAJOR_RANK")) {
				continue;
			}
			
			System.out.println(file.getName());
			
			BufferedReader bw = new BufferedReader(new FileReader(file));
			String str = null;
			while ((str = bw.readLine()) != null) {
				SQL.append(str+"\r\n");
			}
		}
		

		writeTempFile(new File("F:\\留学\\RANK_MAJOR_ALL_FOR_MAJOR_RANK_SQL.txt"), SQL);
	}
	
	
	public static void dealSchool() throws Exception {
		StringBuffer SQL = new StringBuffer();
		
		File files = new File("E:\\BaiduNetdiskDownload\\MAC_AIR_2023\\");
		File[] fileList = files.listFiles();
		for(File file : fileList) {
			if(file.getName().indexOf("offerjson-2") == -1) {
				continue;
			}
			BufferedReader bw = new BufferedReader(new FileReader(file));
			StringBuffer sb = new StringBuffer();
			
			StringBuffer eachColleges = new StringBuffer();
			String str = null;
			while ((str = bw.readLine()) != null) {
				if(str.indexOf("\"colleges\":") != -1) {
					eachColleges = new StringBuffer();;
					eachColleges.append("{"+str);
				}else {
					eachColleges.append(str);
					if(str.indexOf("\"total\": 644") != -1) {
						eachColleges.append("}");
						
						System.out.println(eachColleges);
						JSONObject bean = JSONObject.parseObject(eachColleges.toString());
						JSONArray array = (JSONArray)bean.getJSONArray("colleges");
						for(int x =0;x<array.size();x++) {
							JSONObject xxxx = array.getJSONObject(x);
							School item = JSONObject.parseObject(xxxx.toString(), School.class);
							SQL.append(item.generateSQL()+"\r\n");
						} 
						
					}
				}
			}
			
		}
		

		writeTempFile(new File("E://liuxue//LX_School_SQL.txt"), SQL);
	}
	
	
	public static void dealSchool2(int index) throws Exception {
		StringBuffer SQL = new StringBuffer();
		
		File files = new File("E:\\BaiduNetdiskDownload\\MAC_AIR_2023\\m"+index+"\\");
		File[] fileList = files.listFiles();
		for(File file : fileList) {
			BufferedReader bw = new BufferedReader(new FileReader(file));
			StringBuffer sb = new StringBuffer();
			
			StringBuffer eachColleges = new StringBuffer();
			String str = null;
			boolean start = false;
			while ((str = bw.readLine()) != null) {
				if(str.indexOf("\"programs\":") != -1) {
					eachColleges = new StringBuffer();;
					eachColleges.append("{"+str.trim());
					start = true;
				}else {
					if(start) {
						eachColleges.append(str.trim());
					}
					if(str.indexOf("\"total\":") != -1) {
						start = false;
						eachColleges.append("}");
						System.out.println(index + " - " + file.getName());
						System.out.println(eachColleges);
						JSONObject bean = JSONObject.parseObject(eachColleges.toString().trim());
						JSONArray array = (JSONArray)bean.getJSONArray("programs");
						for(int x =0;x<array.size();x++) {
							JSONObject xxxx = array.getJSONObject(x);
							School2 item = JSONObject.parseObject(xxxx.toString(), School2.class);
							SQL.append(item.generateSQL()+"\r\n");
							SQL.append(item.getCollege_info().generateSQL()+"\r\n");
						} 
						
					}
				}
			}
			
		}
		

		writeTempFile(new File("E://liuxue//LX_School_ext_SQL.txt"), SQL);
	}
	
	public static void liufusousuo_com() throws Exception {
		
		Map<String, String> headers = new HashMap<>();
		Map<String, String> params = new HashMap<>();
		StringBuffer SQL = new StringBuffer();
		StringBuffer SQL2 = new StringBuffer();
		StringBuffer SQL3 = new StringBuffer();
		StringBuffer SQL4 = new StringBuffer();
		//params.put("id", "1");
		//params.put("type", "学校简介");
		
		headers.put("cookie", "admin-sid=04BDF4D893F2E1BFC05B7913132F01ED; sid=B49E32C5B0D5C98B35E547244B45C3B2; Hm_lvt_999c6414e876993b7517c5b3df933bf8=**********; HMACCOUNT=F9E89F99DE4FFF7E; Hm_lvt_814b862c3d6178aa79da93176c4f98ae=**********; __bid_n=1929d21aeee4032dd18ccd; sajssdk_2015_cross_new_user=1; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%221929d21aefa99e-0485cb5339f1404-********-5308416-1929d21aefb2714%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTkyOWQyMWFlZmE5OWUtMDQ4NWNiNTMzOWYxNDA0LTI2MDAxMDUxLTUzMDg0MTYtMTkyOWQyMWFlZmIyNzE0In0%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%221929d21aefa99e-0485cb5339f1404-********-5308416-1929d21aefb2714%22%7D; BMAP_SECKEY=UCtVaG76KZsuMH94isTfPMtqAnrQrArTRK7QP-Es_cMIQoM9mqbcDAw3x-BFl9Nmf4-vhx26J2aXq5IjSjGDf-L6yKTff3kSfqCXKyCmR9e9gLdYlMz9pAjGn_fVzI3UFAdm22OGxdT_o7NZqe40GMaSCsJNChZiGsT-O4mu1qcMLZ5EKNca6FaIE3ongQGMWQAXghuvcOMv26S8docOWQ; SECKEY_ABVK=Dsdj0eeRa0VD1bSrNa7d3Yf0983x6wAhBv85yzxK0WE%3D; Hm_lpvt_999c6414e876993b7517c5b3df933bf8=1729213367; Hm_lpvt_814b862c3d6178aa79da93176c4f98ae=1729213367");
		headers.put("Referrer Policy", "strict-origin-when-cross-origin");
		headers.put("Accept", "*/*");
		headers.put("Accept-Encoding", "gzip, deflate, br, zstd");
		headers.put("Accept-Language", "zh-CN,zh;q=0.9");
		headers.put("Cache-Control", "no-cache");
		headers.put("Connection", "keep-alive");
		headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
		//headers.put("Cookie", "PW9ydXnjjO8XS=60abeMZbhNbBWRNi2hyfp32UB3dRoh5PEK12eiwEWmjK4bVv2bCaL9YrMVuFyaqrwLzlwapiBS5qwfCGXSi1WSRG; PW9ydXnjjO8XT=0J5FTTMAKarsz4pOMoe13Ox4JuuFiqvPupWWYrD5IUhCUXGuvsrpAGLX.ZBkJxcBn_Fy8Jt_syIQOkZUBlAtaGgJ5zrgYXAB_.Yy6jQX1WAthkI2Gr6R3DMiuamBYa4ro5uIiTPQ.8q56klzt7RqomLosrSIkeLLbSDFRFbPzGTR14LX1apGcXEGJpS6geR5vOSc3kfQBlnrSVJmNRuiPJvyC_Us9KgCda0djNBD.6Zra8tjMPhKv522M6TwfHZ9ppVyhWfk1x81PwdRZkLiJEvfyxU3oZCUXlU1ZKT3CNsEmKstyzzZwKDTNlGn1s8TCCiTiMPkVel7m6431R94Bmkuf2etz4ysnSEUW0OUxKnaVArvY6JX2vqeHcsSAMlkWGwV4uLkBkq273kh51UhVLa");

		String listURL = "https://www.liufusousuo.com/liufu/school/collegeLibrary?page=1&size=10000&rankingName=QS&country=";
		String resLIST = HttpSendUtils.get(listURL, params);
		JSONObject listOBJECT = JSONObject.parseObject(resLIST);
		JSONArray listData = listOBJECT.getJSONArray("data");
		
		
		for(int i=0;i<listData.size();i++) {
			JSONObject el = listData.getJSONObject(i);
			
			String schoolID = el.getString("id");
			String schoolName = el.getString("schoolName");
			String englishName = el.getString("englishName");
			String logo = el.getString("logo");
			String address = el.getString("address");
			
			/**
			SQL2.append("insert into lx_temp_school(id,schoolName,englishName,logo,address) values('" + schoolID + "','" +schoolName + "','" +englishName + "','" +logo + "','" +address + "');\r\n");
			
			String url = "https://www.liufusousuo.com/liufu/school/collegeRelated?id="+schoolID+"&type=%E5%AD%A6%E6%A0%A1%E7%AE%80%E4%BB%8B";
			System.out.println(url);
			String res22 = HttpSendUtils.get(url, params);
			JSONObject object = JSONObject.parseObject(res22);
			JSONObject details = object.getJSONObject("details");
			String schoolIntroduction = details.get("schoolIntroduction").toString();
			schoolIntroduction = schoolIntroduction.replaceAll("'", "&#39;");
			String collegeAccommodation = details.get("collegeAccommodation") == null ? null : details.get("collegeAccommodation").toString();
			collegeAccommodation = collegeAccommodation == null ? null : collegeAccommodation.replaceAll("'", "&#39;");
			String geographicalPosition = details.get("geographicalPosition") == null ? null : details.get("geographicalPosition").toString();
			geographicalPosition = geographicalPosition == null ? null : geographicalPosition.replaceAll("'", "&#39;");
			String characteristicsOfTheInstitution = details.get("characteristicsOfTheInstitution") == null ? null : details.get("characteristicsOfTheInstitution").toString();
			characteristicsOfTheInstitution = characteristicsOfTheInstitution == null ? null :characteristicsOfTheInstitution.replaceAll("'", "&#39;");
			String sid = schoolID;
			
			JSONObject overviewOfTheInstitution = details.getJSONObject("overviewOfTheInstitution");
			String id = null,establishmentTime = null,numberOfSchools = null,facultyStudentRatio = null,genderRatio = null,graduationSalary = null,tuitionReference = null;
			if(overviewOfTheInstitution != null) {
				id = overviewOfTheInstitution.get("id").toString();
				establishmentTime = overviewOfTheInstitution.get("establishmentTime") == null ? null : overviewOfTheInstitution.get("establishmentTime").toString();
				numberOfSchools = overviewOfTheInstitution.get("numberOfSchools") == null ? null : overviewOfTheInstitution.get("numberOfSchools").toString();
				facultyStudentRatio = overviewOfTheInstitution.get("facultyStudentRatio") == null ? null : overviewOfTheInstitution.get("facultyStudentRatio").toString();
				genderRatio = overviewOfTheInstitution.get("genderRatio") == null ? null : overviewOfTheInstitution.get("genderRatio").toString();
				graduationSalary = overviewOfTheInstitution.get("graduationSalary") == null ? null : overviewOfTheInstitution.get("graduationSalary").toString();
				tuitionReference = overviewOfTheInstitution.get("tuitionReference") == null ? null : overviewOfTheInstitution.get("tuitionReference").toString();
			}
			
			SQL.append("insert into lx_temp_school_info(sid,id,schoolIntroduction,collegeAccommodation,establishmentTime,numberOfSchools,facultyStudentRatio,genderRatio,graduationSalary,tuitionReference,geographicalPosition,characteristicsOfTheInstitution) values('"+sid+"','"+id+"','"+schoolIntroduction+"','"+collegeAccommodation+"','"+establishmentTime+"','"+numberOfSchools+"','"+facultyStudentRatio+"','"+genderRatio+"','"+graduationSalary+"','"+tuitionReference+"','"+geographicalPosition+"','"+characteristicsOfTheInstitution + "');\r\n");
			
			*/
			
			
			String url2 = "https://www.liufusousuo.com/liufu/school/collegeRelated?id="+schoolID+"&type=%E7%83%AD%E9%97%A8%E4%B8%93%E4%B8%9A&page=1&size=300";
			System.out.println(url2);
			String temp2 = HttpSendUtils.get(url2, params);
			System.out.println(temp2);
			JSONObject temp2object = JSONObject.parseObject(temp2);
			JSONArray temp2dataArray = temp2object.getJSONArray("data");
			for(int k=0;k<temp2dataArray.size();k++) {
				JSONObject temp2data = temp2dataArray.getJSONObject(k);
				String temp2id = temp2data.getString("id");
				String professionalName = temp2data.getString("professionalName");
				String majorEnglishName = temp2data.getString("majorEnglishName");
				String professionalClassification = temp2data.getString("professionalClassification");
				String displayName = temp2data.getString("displayName");
				String displayEnglishName = temp2data.getString("displayEnglishName");
				
				SQL3.append("insert into lx_temp_school_major values('" + temp2id + "','" +professionalName + "','" +majorEnglishName + "','" +professionalClassification + "','" +displayName + "','"+displayEnglishName+"','"+schoolID+"');\r\n");
				
			}
			
			
			
			String url4 = "https://www.liufusousuo.com/liufu/school/collegeRelated?id="+schoolID+"&type=%E7%94%B3%E8%AF%B7%E9%A1%BB%E7%9F%A5";
			System.out.println(url4);
			String temp4 = HttpSendUtils.get(url4, params);
			JSONObject temp4object = JSONObject.parseObject(temp4);
			JSONObject temp4data = temp4object.getJSONObject("details");
			JSONObject undergraduateApplication = temp4data.getJSONObject("undergraduateApplication");
			if(undergraduateApplication != null) {
				String temp4id = undergraduateApplication.getString("id");
				String averageAdmissionRate = undergraduateApplication.getString("averageAdmissionRate");
				String languageAchievement = undergraduateApplication.getString("languageAchievement");
				String candidateScore = undergraduateApplication.getString("candidateScore");
				String schoolStartDate = undergraduateApplication.getString("schoolStartDate");
				String tuition = undergraduateApplication.getString("tuition");
				String foodAndAccommodation = undergraduateApplication.getString("foodAndAccommodation");
				String applicationMaterials = undergraduateApplication.getString("applicationMaterials");
				String applicationType = undergraduateApplication.getString("applicationType");
				String toefl = undergraduateApplication.getString("toefl");
				String ielts = undergraduateApplication.getString("ielts");
				SQL4.append("insert into lx_temp_school_application values('" + temp4id + "','" +averageAdmissionRate + "','" +languageAchievement + "','" +candidateScore + "','" +schoolStartDate + "','"+tuition+"','"+foodAndAccommodation+"','"+applicationMaterials+"','"+applicationType+"','"+toefl+"','"+ielts+"','"+schoolID+"');\r\n");
				
			}
			JSONObject masterSApplication = temp4data.getJSONObject("masterSApplication");
			if(masterSApplication != null) {
				String temp4id = masterSApplication.getString("id");
				String averageAdmissionRate = masterSApplication.getString("averageAdmissionRate");
				String languageAchievement = masterSApplication.getString("languageAchievement");
				String candidateScore = masterSApplication.getString("candidateScore");
				String schoolStartDate = masterSApplication.getString("schoolStartDate");
				String tuition = masterSApplication.getString("tuition");
				String foodAndAccommodation = masterSApplication.getString("foodAndAccommodation");
				String applicationMaterials = masterSApplication.getString("applicationMaterials");
				String applicationType = masterSApplication.getString("applicationType");
				String toefl = masterSApplication.getString("toefl");
				String ielts = masterSApplication.getString("ielts");
				SQL4.append("insert into lx_temp_school_application values('" + temp4id + "','" +averageAdmissionRate + "','" +languageAchievement + "','" +candidateScore + "','" +schoolStartDate + "','"+tuition+"','"+foodAndAccommodation+"','"+applicationMaterials+"','"+applicationType+"','"+toefl+"','"+ielts+"','"+schoolID+"');\r\n");
			}
			
			
			
			
			
		}
			
		writeTempFile(new File("E://xindongfang//liufusousuo_com//XDF_UPDATE_SQL1.txt"), SQL);
		writeTempFile(new File("E://xindongfang//liufusousuo_com//XDF_UPDATE_SQL2.txt"), SQL2);
		writeTempFile(new File("E://xindongfang//liufusousuo_com//XDF_UPDATE_SQL3.txt"), SQL3);
		writeTempFile(new File("E://xindongfang//liufusousuo_com//XDF_UPDATE_SQL4.txt"), SQL4);
	}
	
	
	private static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
}
