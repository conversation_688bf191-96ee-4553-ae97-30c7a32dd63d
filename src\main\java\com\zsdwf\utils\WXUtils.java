package com.zsdwf.utils;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.PrivateKey;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.IOUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.util.EntityUtils;

import com.alibaba.fastjson2.JSONObject;
import com.career.utils.HttpSendUtils;
import com.career.utils.liuxue.HttpSendUtils2;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyResponse;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.constant.WxPayConstants;
import com.tencentcloudapi.mps.v20190612.models.ActivityResult;
import com.wechat.pay.contrib.apache.httpclient.WechatPayHttpClientBuilder;
import com.wechat.pay.contrib.apache.httpclient.auth.PrivateKeySigner;
import com.wechat.pay.contrib.apache.httpclient.auth.WechatPay2Credentials;
import com.wechat.pay.contrib.apache.httpclient.auth.WechatPay2Validator;
import com.wechat.pay.contrib.apache.httpclient.util.PemUtil;
import com.wechat.pay.java.core.http.HttpClient;

import cn.binarywang.wx.miniapp.bean.live.WxMaAssistantResult.Assistant;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.bean.subscribemsg.CategoryData;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;

public class WXUtils {
	
	
	public static void main(String args[]) {
		List<String> sca = new ArrayList<>();
		
		sca.add("SCA6987");
		sca.add("SCA6988");
		sca.add("SCA6989");
		
		StringBuffer SQL = new StringBuffer();
		for(String x : sca) {
			String key = x.substring(3);
			String qr = generateShareQRUrl(key);
			SQL.append("insert into wx_share_user(s_id,s_passwd,qrscene, qr_url,create_tm,base_card_id,used_status) values('F"+key+"','8888','"+key+"','"+qr+"',now(),'"+x+"',1);\r\n");
		}
		
		writeTempFile(new File("C://eeeex.txt"), SQL);
	}
	
	private static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	static String accessToken = null;
	public static String generateShareQRUrl(String qrscence_code){
		if(accessToken == null) {
			accessToken = getAccessToken();
		}
		System.out.println(accessToken);
		if(!Tools.isEmpty(accessToken)) {
			System.out.println(accessToken);
			String result = sendGenerateShareQRUrlMessage(accessToken, qrscence_code);
			JSONObject object = JSONObject.parseObject(result);
			return object.getString("url");
		}
		return null;
	}
	
	public static String sendGenerateShareQRUrlMessage(String accessToken, String qrscence_code){
        HttpURLConnection conn = null;
        JSONObject jsonObject = null;
        String result = "";

        InputStreamReader isr = null;
        BufferedReader bf = null;

        OutputStreamWriter osw = null;
        PrintWriter printw = null;
        try {
            String send_message = "https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token="+accessToken;
            URL u = new URL(send_message);
            conn = (HttpURLConnection) u.openConnection();
            conn.setRequestMethod("POST");
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            conn.setRequestProperty("Content-Type",  "application/x-www-form-urlencoded; charset=utf-8");
            conn.setRequestProperty("Accept-Charset", "UTF-8");
            conn.setRequestProperty("contentType", "UTF-8");

            conn.setDoOutput(true);
            conn.setDoInput(true);

            conn.connect();

            osw = new OutputStreamWriter(conn.getOutputStream(),"UTF-8");
            printw = new PrintWriter(osw);
            
            String sendMSGContent = "{\r\n"
            		+ "    \"action_name\": \"QR_LIMIT_SCENE\", \r\n"
            		+ "    \"action_info\": {\r\n"
            		+ "        \"scene\": {\r\n"
            		+ "            \"scene_id\": "+qrscence_code+"\r\n"
            		+ "        }\r\n"
            		+ "    }\r\n"
            		+ "}";
            printw.print(sendMSGContent); //输入参数
            printw.flush();

            //获取返回值
            isr = new InputStreamReader(conn.getInputStream(),"UTF-8");
            bf = new BufferedReader(isr);
            String line;
            while((line = bf.readLine()) != null){
                result += line;
            }
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }finally{
            if(bf != null){
                try {
                    bf.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if(isr != null){
                try {
                    isr.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if(printw != null){
                printw.close();
            }
            if(osw != null){
                try {
                    osw.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return result;
    }
	
	public static String getAccessToken(){
		try {
			WxMpService wxMpService = new WxMpServiceImpl();
			  WxMpDefaultConfigImpl config = new WxMpDefaultConfigImpl();
			  config.setAppId("wx29fd5eeca8b5f498"); // 设置微信公众号的appid
			  config.setSecret("658a284e779cb3a217901854e0f3e26a"); // 设置微信公众号的app corpSecret //658a284e779cb3a217901854e0f3e26a
			  config.setToken("1qazxsw2"); // 设置微信公众号的token
			  config.setAesKey("DUrmurpTj5qK99N1zHufHHp8TSosYLzB6RTBuPUbsPh"); // 设置微信公众号的EncodingAESKey
			  wxMpService.setWxMpConfigStorage(config);
			  return wxMpService.getAccessToken();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return null;
	}
	
	public static String getYGOpenIDURL(String stateStr) {
		WxMpService wxMpService = new WxMpServiceImpl();
		  WxMpDefaultConfigImpl config = new WxMpDefaultConfigImpl();
		  config.setAppId("wx29fd5eeca8b5f498"); // 设置微信公众号的appid
		  config.setSecret("658a284e779cb3a217901854e0f3e26a"); // 设置微信公众号的app corpSecret //658a284e779cb3a217901854e0f3e26a
		  config.setToken("1qazxsw2"); // 设置微信公众号的token
		  config.setAesKey("DUrmurpTj5qK99N1zHufHHp8TSosYLzB6RTBuPUbsPh"); // 设置微信公众号的EncodingAESKey
		  wxMpService.setWxMpConfigStorage(config);
		  String url = "http://dwfcx.com/zsdwf/wx/auth_callback_handle.jsp";
		  String url2 = wxMpService.getOAuth2Service().buildAuthorizationUrl(url, WxConsts.OAuth2Scope.SNSAPI_USERINFO, stateStr);
		  System.out.println(url2);
		  return url2;
	}
	
	
	public static String jsapi_ticket = null;
	public static Date jsapi_ticket_issues_tm = null;
	public static String getJsApiTicket(){
		if(jsapi_ticket_issues_tm != null && Tools.timeSpan(jsapi_ticket_issues_tm, new Date()) < 115) {
			return jsapi_ticket;
		}else {
			try {
				Map<String, String> headers = new HashMap<>();
				String accessToken = getAccessToken();
				String resultPageList = HttpSendUtils.get("https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token="+accessToken+"&type=jsapi", headers);
				JSONObject object = JSONObject.parseObject(resultPageList);
				com.career.utils.Tools.println(resultPageList);
				jsapi_ticket = object.getString("ticket");
				jsapi_ticket_issues_tm = new Date();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			return "...";
		}
	}
	
	
	public static String callback(String code) {
		WxMpService wxMpService = new WxMpServiceImpl();
		  WxMpDefaultConfigImpl config = new WxMpDefaultConfigImpl();
		  config.setAppId("wx29fd5eeca8b5f498"); // 设置微信公众号的appid
		  config.setSecret("658a284e779cb3a217901854e0f3e26a"); // 设置微信公众号的app corpSecret //658a284e779cb3a217901854e0f3e26a
		  config.setToken("1qazxsw2"); // 设置微信公众号的token
		  config.setAesKey("DUrmurpTj5qK99N1zHufHHp8TSosYLzB6RTBuPUbsPh"); // 设置微信公众号的EncodingAESKey
		  wxMpService.setWxMpConfigStorage(config);
		
		  
		try {
			WxOAuth2AccessToken accessToken = wxMpService.getOAuth2Service().getAccessToken(code);
			return accessToken.getOpenId();
			//WxMpUser wxMpUser = wxMpService.getUserService().userInfo(accessToken.getOpenId(), null);
			//System.out.println(wxMpUser.getOpenId());
			//return wxMpUser.getOpenId() + "/" + wxMpUser.getNickname();
		} catch (WxErrorException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return "--";

	}
	
	static com.github.binarywang.wxpay.config.WxPayConfig  payConfig = new com.github.binarywang.wxpay.config.WxPayConfig ();
	static {
		payConfig.setAppId("wx29fd5eeca8b5f498");
        payConfig.setMchId("1646287519");
        payConfig.setMchKey("aaa112233A1qazxsw23edcvfr45tgbnh");
        //payConfig.setSubAppId("");
        //payConfig.setSubMchId("");
        payConfig.setKeyPath("/usr/local/etc/apiclient_cert.p12");

        // 可以指定是否使用沙箱环境
        payConfig.setUseSandboxEnv(false);
	}
	
	
	public static com.github.binarywang.wxpay.service.WxPayService payInit() {
        com.github.binarywang.wxpay.service.WxPayService wxPayService = new com.github.binarywang.wxpay.service.impl.WxPayServiceImpl();
        wxPayService.setConfig(payConfig);
        return wxPayService;
	}
	
	
	public static String getIp(HttpServletRequest request) {
        if (request == null) {
            return "127.0.0.1";
        }
        String ip = request.getHeader("X-Requested-For");
        if (Tools.isEmpty(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Forwarded-For");
        }
        if (Tools.isEmpty(ip) || "unknown".equalsIgnoreCase(ip)) { 
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (Tools.isEmpty(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (Tools.isEmpty(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (Tools.isEmpty(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (Tools.isEmpty(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
	
	//下单
	public static String unifiedOrder(String outTradeNo, String openID, String ip, int payCnt, String body) throws Exception
    {
		com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest wxPayUnifiedOrderRequest = new com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest();
        wxPayUnifiedOrderRequest.setBody(body);
        wxPayUnifiedOrderRequest.setOutTradeNo(outTradeNo);
        wxPayUnifiedOrderRequest.setTotalFee(payCnt);
        wxPayUnifiedOrderRequest.setSpbillCreateIp(ip);
        wxPayUnifiedOrderRequest.setNotifyUrl("http://dwfcx.com/zsdwf/pay/zsdwf_callback_payment.jsp");
        wxPayUnifiedOrderRequest.setTradeType("JSAPI");
        wxPayUnifiedOrderRequest.setOpenid(openID);
        //wxPayUnifiedOrderRequest.setSign("38906C82EBF8AD5CDB278E9A735A3A0F");
        
        //System.out.println(wxPayUnifiedOrderRequest.toXML());
        
        com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderResult result = payInit().unifiedOrder(wxPayUnifiedOrderRequest);
        if("SUCCESS".equals(result.getReturnCode()) && "SUCCESS".equals(result.getResultCode())) {
        	return result.getPrepayId();
        }else {
        	return null;
        }
    }
	
	//查询订单
	public static String queryOrder(String outTradeNo) throws Exception
    {
		com.github.binarywang.wxpay.bean.result.WxPayOrderQueryResult wxPayOrderQueryResult = payInit().queryOrder(null, outTradeNo);
		if("SUCCESS".equals(wxPayOrderQueryResult.getReturnCode()) && "SUCCESS".equals(wxPayOrderQueryResult.getTradeState()) && "SUCCESS".equals(wxPayOrderQueryResult.getResultCode())) {
        	return "OK";
        }else {
        	return wxPayOrderQueryResult.getTradeState();
        }
    }
	
 
}