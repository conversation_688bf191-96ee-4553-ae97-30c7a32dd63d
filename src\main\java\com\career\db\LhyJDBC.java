package com.career.db;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Vector;
import java.util.stream.Collectors;

import com.career.db.career.MajorPlan;
import com.career.utils.ResultVO;
import com.career.utils.SQLLogUtils;
import com.career.utils.Tools;
import com.career.utils.ZyzdCache;
import com.zsdwf.db.YGPaymentBean;


public class LhyJDBC {
	
	static String URL = JDBCConstants.URL;
	static String USER = JDBCConstants.USER;
	static String PASSWD = JDBCConstants.PASSWD;
	public static int PAGE_ROW_CNT = 20;
	public static int PAGE_ROW_CNT_SPEC = 15;
	public static int PAGE_ROW_CNT_TEN = 10;
	
	static {
		try {
			Class.forName("com.mysql.cj.jdbc.Driver");
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
	
	public ResultVO searchLhyCardByParentAndName(String p_c_id, String name, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		ResultVO resultVO = new ResultVO();
		List<LhyCard> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL_CONDITION = "FROM lhy_card x WHERE x.p_c_id = ? and (c_name like ? or c_phone like ? or c_id like ?) and c_status = 1";
			String SELECT_SQL = "SELECT * "+SQL_CONDITION+" order by create_tm desc, last_login_tm desc, c_id desc LIMIT ?,? ";
			String SELECT_CNT = "SELECT count(*) AS cnt "+SQL_CONDITION;
			ps = conn.prepareStatement(SELECT_SQL);
			ps.setString(1, p_c_id);
			ps.setString(2, "%"+name+"%");
			ps.setString(3, "%"+name+"%");
			ps.setString(4, "%"+name+"%");
			ps.setInt(5, (pageNumber - 1) * PAGE_ROW_CNT);
			ps.setInt(6, PAGE_ROW_CNT);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setLhyBaseCard(rs));
			}
			
			ps = conn.prepareStatement(SELECT_CNT);
			ps.setString(1, p_c_id);
			ps.setString(2, "%"+name+"%");
			ps.setString(3, "%"+name+"%");
			ps.setString(4, "%"+name+"%");
			rs = ps.executeQuery();
			while (rs.next()) {
				resultVO.setRecordCnt(rs.getInt("cnt"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		
		resultVO.setResult(list);
		resultVO.setCurrentPage(pageNumber);
		return resultVO;
	}
	
	
	public ResultVO getAllLhyCard(String name, int sales_status, int auto_ind_status, String saas_id, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		ResultVO resultVO = new ResultVO();
		List<LhyCard> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL_CONDITION = "FROM lhy_card x WHERE x.saled_status = ? and c_auto_ind = ? and (COALESCE(c_id ,'') like ? or COALESCE(c_name,'') like ? or COALESCE(c_remark,'') like ? or COALESCE(c_phone,'') like ?) and c_status = 1 and saas_id = ?";
			String SELECT_SQL = "SELECT * "+SQL_CONDITION+" order by create_tm desc, last_login_tm desc, c_id desc LIMIT ?,? "; 
			String SELECT_CNT = "SELECT count(*) AS cnt "+SQL_CONDITION;
			ps = conn.prepareStatement(SELECT_SQL);
			ps.setInt(1, sales_status);
			ps.setInt(2, auto_ind_status);
			ps.setString(3, "%"+name+"%");
			ps.setString(4, "%"+name+"%");
			ps.setString(5, "%"+name+"%");
			ps.setString(6, "%"+name+"%");
			ps.setString(7, saas_id);
			ps.setInt(8, (pageNumber - 1) * PAGE_ROW_CNT);
			ps.setInt(9, PAGE_ROW_CNT); 
			SQLLogUtils.printSQL(ps);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setLhyBaseCard(rs));
			}
			
			ps = conn.prepareStatement(SELECT_CNT);
			ps.setInt(1, sales_status);
			ps.setInt(2, auto_ind_status);
			ps.setString(3, "%"+name+"%");
			ps.setString(4, "%"+name+"%");
			ps.setString(5, "%"+name+"%");
			ps.setString(6, "%"+name+"%");
			ps.setString(7, saas_id);
			rs = ps.executeQuery();
			while (rs.next()) {
				resultVO.setRecordCnt(rs.getInt("cnt"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		
		resultVO.setResult(list);
		resultVO.setCurrentPage(pageNumber);
		return resultVO;
	}
	
	public boolean updateLhyOrder(LhyOrder order) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        try {
            conn = DatabaseUtils.getConnection();
            String sql = "UPDATE lhy_order SET stu_info_name = ?, stu_info_xb = ?, stu_info_bj = ?, stu_info_xx = ?, stu_info_dh = ?, stu_info_mz = ?, stu_info_sl = ?, stu_info_sg = ?, stu_info_tz = ?, stu_score_xk = ?, stu_score_cj = ?, stu_score_wc = ?, ext_father_name = ?, ext_mother_name = ?, ext_other_name = ?, ext_contact = ?, ext_economics = ?, ext_remark = ? WHERE order_id = ?";
            pstmt = conn.prepareStatement(sql);

            pstmt.setString(1, order.getStu_info_name());
            pstmt.setString(2, order.getStu_info_xb());
            pstmt.setString(3, order.getStu_info_bj());
            pstmt.setString(4, order.getStu_info_xx());
            pstmt.setString(5, order.getStu_info_dh());
            pstmt.setString(6, order.getStu_info_mz());
            pstmt.setString(7, order.getStu_info_sl());
            pstmt.setString(8, order.getStu_info_sg());
            pstmt.setString(9, order.getStu_info_tz());
            pstmt.setString(10, order.getStu_score_xk());
            pstmt.setInt(11, order.getStu_score_cj());
            pstmt.setInt(12, order.getStu_score_wc());
            pstmt.setString(13, order.getExt_father_name());
            pstmt.setString(14, order.getExt_mother_name());
            pstmt.setString(15, order.getExt_other_name());
            pstmt.setString(16, order.getExt_contact());
            pstmt.setString(17, order.getExt_economics());
            pstmt.setString(18, order.getExt_remark()); 
            pstmt.setString(19, order.getOrder_id());

            int affectedRows = pstmt.executeUpdate();
            
            SQLLogUtils.printSQL(" === updateLhyOrder : ", pstmt);
            
            return affectedRows > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, null);
        }
        return false;
    }
	
	public boolean updateLhyOrderForSearchSfOnly(String sfStr, String order_id) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        try {
            conn = DatabaseUtils.getConnection();
            String sql = "UPDATE lhy_order SET latest_search_sf = ? WHERE order_id = ?";
            pstmt = conn.prepareStatement(sql);

            pstmt.setString(1, sfStr);
            pstmt.setString(2, order_id);

            int affectedRows = pstmt.executeUpdate();
            
            SQLLogUtils.printSQL(" === updateLhyOrder : ", pstmt);
            
            return affectedRows > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, null);
        }
        return false;
    }
	
	public ResultVO searchOriginalJhData(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> yxsfSets, HashSet<String> zymlSets, HashSet<String> catgSets, HashSet<String> natureSets, 
			boolean is985, boolean is211, boolean issyl, boolean isgzd, boolean isszd, boolean isssyx, boolean isbyzg, boolean hasbsd, boolean hasssd, boolean isgjts, String keywords, int zdfwc_from, int zdfwc_to, int pageNumber) {
		
		
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		ResultVO resultVO = new ResultVO();
		try {
			conn = DatabaseUtils.getConnection();
			String sfSQL = yxsfSets.size() == 0 ? "" : " and x.yxsf in ("+Tools.getSQLQueryin(yxsfSets)+")";
			String natureSQL = natureSets.size() == 0 ? "" : " and x.ind_nature in ("+Tools.getSQLQueryin(natureSets)+")";
			String zymlSQL = zymlSets.size() == 0 ? "" : " and x.zyml in ("+Tools.getSQLQueryin(zymlSets)+")";
			String catgSQL = catgSets.size() == 0 ? "" : " and x.ind_catg in ("+Tools.getSQLQueryin(catgSets)+")";
			String is985SQL = is985 ? " and yx_tags like '%985%" : "";
			String is211SQL = is211 ? " and yx_tags like '%211%" : "";
			String issylSQL = issyl ? " and yx_tags like '%双一流%" : "";
			String isgzdSQL = isgzd ? " and yx_tags like '%%'" : "";
			String isszdSQL = isszd ? " and yx_tags like '%%'" : "";
			String isssyxSQL = isssyx ? " and yx_tags like '%%'" : "";
			String isbyzgSQL = isbyzg ? " and yx_tags like '%%'" : "";
			String isgjtsSQL = isgjts ? " and yx_tags like '%%'" : "";
			hasbsd = false;
			hasssd = false;
			String hasbsdSQL = hasbsd ? " and CAST(yx_bsd AS UNSIGNED) > 0" : "";
			String hasssdSQL = hasssd ? " and CAST(yx_ssd AS UNSIGNED) > 0" : "";
			
			String SEARCH_CONDITION = " FROM " + sfCode + "_jh_" + jhYear + " x WHERE xk_code like ? and pc = ? and pc_code = ? and (yxmc like ? or zymc like ? ) and zdfwc between ? and ? " 
					+ sfSQL + zymlSQL + natureSQL + catgSQL + is985SQL + is211SQL + issylSQL + isgzdSQL  + isszdSQL  + isssyxSQL  + isbyzgSQL  + isgjtsSQL  + hasbsdSQL  + hasssdSQL ;
			String ORDER = " ORDER BY zdfwc ASC LIMIT ?,?";
			String SELECT_RECORD = "SELECT * " + SEARCH_CONDITION + ORDER;
			String SELECT_CNT = "SELECT count(*) as cnt " + SEARCH_CONDITION;
			
			ps = conn.prepareStatement(SELECT_RECORD);
			int index = 1;
			ps.setString(index++, "%"+xkCode+"%");
			ps.setString(index++, pc);
			ps.setString(index++, pc_code);
			ps.setString(index++, "%"+keywords+"%");
			ps.setString(index++, "%"+keywords+"%");

			ps.setInt(index++, zdfwc_from);
			ps.setInt(index++, zdfwc_to);
			
			ps.setInt(index++, (pageNumber - 1) * PAGE_ROW_CNT);
			ps.setInt(index++, PAGE_ROW_CNT);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				list.add(ZyzdFormJDBC.setJHBeanAll(dataYear, rs));
			}
			
			resultVO.setResult(list);
			
			index = 1;
			ps = conn.prepareStatement(SELECT_CNT);
			ps.setString(index++, "%"+xkCode+"%");
			ps.setString(index++, pc);
			ps.setString(index++, pc_code);
			ps.setString(index++, "%"+keywords+"%");
			ps.setString(index++, "%"+keywords+"%");

			ps.setInt(index++, zdfwc_from);
			ps.setInt(index++, zdfwc_to);
			
			rs = ps.executeQuery();
			while (rs.next()) {
				resultVO.setRecordCnt(rs.getInt("cnt"));
			}
			
			resultVO.setCurrentPage(pageNumber);
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return resultVO;
	}
	
	/**
	 * 专业优先
	 * @param jhYear
	 * @param sfCode
	 * @param xkCode
	 * @param pc
	 * @param pc_code
	 * @param selectedSfValuesSets
	 * @param zymlSets
	 * @param zymcSets
	 * @param selectedYXTAGValuesSets
	 * @param selectedYXLXValuesSets
	 * @param selectedBXXZValuesSet
	 * @param keywords
	 * @param wc_start
	 * @param wc_to
	 * @param pageNumber
	 * @return
	 */
	public ResultVO searchJhDataForZyyx(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> selectedSfValuesSets, HashSet<String> zymlSets, HashSet<String> zymcSets, 
				HashSet<String> selectedYXTAGValuesSets, HashSet<String> selectedYXLXValuesSets, HashSet<String> selectedBXXZValuesSet, HashSet<String> selectedTspcExceptValuesSet, boolean is_hz, String keywords, String keywords_ext, int selected_znzyls, int selected_znzys, int wc_start, int wc_to, int pageNumber) {
		
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		ResultVO resultVO = new ResultVO();
		try {
			conn = DatabaseUtils.getConnection();
			String selectedSfValuesSetsSQL = selectedSfValuesSets.size() == 0 ? "" : " AND x.yxsf in ("+Tools.getSQLQueryin(selectedSfValuesSets)+")";
			String zymlSetsSQL = zymlSets.size() == 0 ? "" : " AND x.zyml in ("+Tools.getSQLQueryin(zymlSets)+")";
			String zymcSetsSQL = zymcSets.size() == 0 ? "" : " AND x.zymc_org in ("+Tools.getSQLQueryin(zymcSets)+")";
			String tspcExceptSetsSQL = selectedTspcExceptValuesSet.size() == 0 ? "" : " AND (x.pc_type is NULL OR x.pc_type not in ("+Tools.getSQLQueryin(selectedTspcExceptValuesSet)+"))";
			String selectedYXLXValuesSetsSQL = selectedYXLXValuesSets.size() == 0 ? "" : " AND x.ind_catg in ("+Tools.getSQLQueryin(selectedYXLXValuesSets)+")";
			
			HashSet<String> new_selectedBXXZValuesSet = new HashSet<>();
			boolean contained_hz = false;
			if(selectedBXXZValuesSet.size() > 0) {
				Iterator<String> it = selectedBXXZValuesSet.iterator();
				while(it.hasNext()) {
					String itStr = it.next();
					if(itStr.indexOf("合作") != -1) {
						contained_hz = true;
					}else {
						new_selectedBXXZValuesSet.add(itStr);
					}
				}
			}
			
			//公办/中外合作办学
			String selectedBXXZValuesSetSQL = selectedBXXZValuesSet.size() == 0 ? "" : " AND x.ind_nature in ("+Tools.getSQLQueryin(selectedBXXZValuesSet)+")";
			String selectedHzbxValuesSetSQL =  is_hz ? " AND x.is_hz = 1 " : " AND (x.is_hz = 0 OR x.is_hz is null) ";
			
			
			String yxTagsSQL = "";
			if(selectedYXTAGValuesSets.size() > 0) {
				Iterator<String> it = selectedYXTAGValuesSets.iterator();
				StringBuffer temp_sql = new StringBuffer();
				while(it.hasNext()) {
					String itStr = it.next();
					temp_sql.append(" OR x.yx_tags_all like '%"+itStr+"%' ");
				}
				
				if(!Tools.isEmpty(temp_sql.toString())) {
					yxTagsSQL = " AND (" + temp_sql.substring(4) + ")";
				}
			}
			
			
			
			String SEARCH_CONDITION = " FROM " + sfCode + "_jh_" + jhYear + " x WHERE x.xk_code like ? and x.pc = ? and x.pc_code = ? and ((x.yxmc like ? or x.zymc like ? "+(Tools.isEmpty(keywords + keywords_ext) ? "OR x.zybz IS NULL" : "")+" OR x.zybz like ?) AND (x.yxmc like ? or x.zymc like ? "+(Tools.isEmpty(keywords + keywords_ext) ? "OR x.zybz IS NULL" : "")+" OR x.zybz like ?)) and (znzyls IS NULL OR znzyls <= ?) and (znzys IS NULL OR znzys <= ?) and (x.zdfwc is null OR x.zdfwc <= 0 OR CAST(COALESCE(x.zdfwc,99999999) AS SIGNED) between ? and ?) " 
					+ selectedSfValuesSetsSQL + zymlSetsSQL + zymcSetsSQL + selectedYXLXValuesSetsSQL + selectedBXXZValuesSetSQL + selectedHzbxValuesSetSQL + tspcExceptSetsSQL + yxTagsSQL ;
			String ORDER = " ORDER BY x.zdfwc IS NULL, x.zdfwc <= 0,  CAST(COALESCE(x.zdfwc,99999999) AS SIGNED) ASC LIMIT ?,?"; 
			String SELECT_RECORD = "SELECT * " + SEARCH_CONDITION + ORDER;
			String SELECT_CNT = "SELECT count(*) as cnt " + SEARCH_CONDITION ;
			  
			ps = conn.prepareStatement(SELECT_RECORD); 
			int index = 1;
			ps.setString(index++, "%"+xkCode+"%");
			ps.setString(index++, pc);
			ps.setString(index++, pc_code);
			ps.setString(index++, "%"+keywords+"%"); 
			ps.setString(index++, "%"+keywords+"%");
			ps.setString(index++, "%"+keywords+"%");
			ps.setString(index++, "%"+keywords_ext+"%");
			ps.setString(index++, "%"+keywords_ext+"%");
			ps.setString(index++, "%"+keywords_ext+"%");
			ps.setInt(index++, selected_znzyls);
			ps.setInt(index++, selected_znzys);
			ps.setInt(index++, wc_start);
			ps.setInt(index++, wc_to);
			ps.setInt(index++, (pageNumber - 1) * PAGE_ROW_CNT);
			ps.setInt(index++, PAGE_ROW_CNT);
			SQLLogUtils.printSQL(ps);
			rs = ps.executeQuery();
			while (rs.next()) {
				JHBean bean =ZyzdFormJDBC.setJHBeanAll(dataYear, rs);
				list.add(bean); 
			}
			
			
			index = 1;
			ps = conn.prepareStatement(SELECT_CNT);
			ps.setString(index++, "%"+xkCode+"%");
			ps.setString(index++, pc);
			ps.setString(index++, pc_code);
			ps.setString(index++, "%"+keywords+"%");
			ps.setString(index++, "%"+keywords+"%");
			ps.setString(index++, "%"+keywords+"%");
			ps.setString(index++, "%"+keywords_ext+"%");
			ps.setString(index++, "%"+keywords_ext+"%");
			ps.setString(index++, "%"+keywords_ext+"%");
			ps.setInt(index++, selected_znzyls);
			ps.setInt(index++, selected_znzys);
			ps.setInt(index++, wc_start);
			ps.setInt(index++, wc_to);
			rs = ps.executeQuery();
			while (rs.next()) {
				resultVO.setRecordCnt(rs.getInt("cnt"));
			}
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		resultVO.setResult(list);
		resultVO.setCurrentPage(pageNumber);
		
		return resultVO;
	}
	
	public ResultVO searchJhDataForZyyxAndExtScoreAndBssw(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> selectedSfValuesSets, HashSet<String> zymlSets, HashSet<String> zymcSets, 
			HashSet<String> selectedYXTAGValuesSets, HashSet<String> selectedYXLXValuesSets, HashSet<String> selectedBXXZValuesSet, String keywords, int selected_znzyls, int selected_znzys, String except_yxsf, int qsf_start, int qsf_to, int pageNumber) {
	
	Connection conn = null;
	PreparedStatement ps = null;
	int dataYear = jhYear-1;
	ResultSet rs = null;
	List<JHBean> list = new ArrayList<>();
	ResultVO resultVO = new ResultVO();
	try {
		conn = DatabaseUtils.getConnection();
		String selectedSfValuesSetsSQL = selectedSfValuesSets.size() == 0 ? "" : " AND x.yxsf in ("+Tools.getSQLQueryin(selectedSfValuesSets)+")";
		String zymlSetsSQL = zymlSets.size() == 0 ? "" : " AND x.zyml in ("+Tools.getSQLQueryin(zymlSets)+")";
		String zymcSetsSQL = zymcSets.size() == 0 ? "" : " AND x.zymc_org in ("+Tools.getSQLQueryin(zymcSets)+")";
		String selectedYXLXValuesSetsSQL = selectedYXLXValuesSets.size() == 0 ? "" : " AND x.ind_catg in ("+Tools.getSQLQueryin(selectedYXLXValuesSets)+")";
		
		HashSet<String> new_selectedBXXZValuesSet = new HashSet<>();
		boolean contained_hz = false;
		if(selectedBXXZValuesSet.size() > 0) {
			Iterator<String> it = selectedBXXZValuesSet.iterator();
			while(it.hasNext()) {
				String itStr = it.next();
				if(itStr.indexOf("合作") != -1) {
					contained_hz = true;
				}else {
					new_selectedBXXZValuesSet.add(itStr);
				}
			}
		}
		//是否需要中外合作办学
		String hzbxSQL = contained_hz ? " AND (x.is_hz = 1 OR x.ind_nature like '%中外%') " : " AND (x.is_hz <> 1 AND x.ind_nature not like '%中外%') ";
		
		
		String selectedBXXZValuesSetSQL = new_selectedBXXZValuesSet.size() == 0 ? "" : " AND x.ind_nature in ("+Tools.getSQLQueryin(new_selectedBXXZValuesSet)+")";
		
		
		String yxTagsSQL = "";
		if(selectedYXTAGValuesSets.size() > 0) {
			Iterator<String> it = selectedYXTAGValuesSets.iterator();
			StringBuffer temp_sql = new StringBuffer();
			while(it.hasNext()) {
				String itStr = it.next();
				temp_sql.append(" OR x.yx_tags_all like '%"+itStr+"%' ");
			}
			
			if(!Tools.isEmpty(temp_sql.toString())) {
				yxTagsSQL = " AND (" + temp_sql.substring(4) + ")";
			}
		}
		
		
		
		String SEARCH_CONDITION = " FROM " + sfCode + "_jh_" + jhYear + " x WHERE x.xk_code like ? and x.pc = ? and x.pc_code = ? and (x.yxmc like ? or x.zymc like ?) AND (x.zybz IS NULL OR x.zybz like ?) and (x.znzyls IS NULL OR x.znzyls <= ?) and (x.znzys IS NULL OR x.znzys <= ?) and (x.qsf is null OR x.qsf = 0 OR x.qsf between ? and ?) AND (x.is_bsyx = 1 OR x.yxsf <> ?) " 
				+ selectedSfValuesSetsSQL + zymlSetsSQL + zymcSetsSQL + selectedYXLXValuesSetsSQL + selectedBXXZValuesSetSQL + hzbxSQL + yxTagsSQL ;
		String ORDER = " ORDER BY x.qsf is NULL,  x.qsf DESC LIMIT ?,?"; 
		String SELECT_RECORD = "SELECT * " + SEARCH_CONDITION + ORDER;
		String SELECT_CNT = "SELECT count(*) as cnt " + SEARCH_CONDITION ;
		
		ps = conn.prepareStatement(SELECT_RECORD);
		int index = 1;
		ps.setString(index++, "%"+xkCode+"%");
		ps.setString(index++, pc);
		ps.setString(index++, pc_code);
		ps.setString(index++, "%"+keywords+"%");
		ps.setString(index++, "%"+keywords+"%");
		ps.setString(index++, "%"+keywords+"%");
		ps.setInt(index++, selected_znzyls);
		ps.setInt(index++, selected_znzys);
		ps.setInt(index++, qsf_start);
		ps.setInt(index++, qsf_to);
		ps.setString(index++, except_yxsf);
		ps.setInt(index++, (pageNumber - 1) * PAGE_ROW_CNT);
		ps.setInt(index++, PAGE_ROW_CNT);
		SQLLogUtils.printSQL(ps);
		rs = ps.executeQuery();
		JHBean bean = null;
		while (rs.next()) {
			list.add(ZyzdFormJDBC.setJHBeanAll(dataYear, rs));
		}
		
		
		index = 1;
		ps = conn.prepareStatement(SELECT_CNT);
		ps.setString(index++, "%"+xkCode+"%");
		ps.setString(index++, pc);
		ps.setString(index++, pc_code);
		ps.setString(index++, "%"+keywords+"%");
		ps.setString(index++, "%"+keywords+"%");
		ps.setString(index++, "%"+keywords+"%");
		ps.setInt(index++, selected_znzyls);
		ps.setInt(index++, selected_znzys);
		ps.setInt(index++, qsf_start);
		ps.setInt(index++, qsf_to);
		ps.setString(index++, except_yxsf);
		rs = ps.executeQuery();
		while (rs.next()) {
			resultVO.setRecordCnt(rs.getInt("cnt"));
		}
		
	} catch (Exception ex) {
		ex.printStackTrace();
	} finally {
		closeAllConnection(conn, ps, rs);
	}
	resultVO.setResult(list);
	resultVO.setCurrentPage(pageNumber);
	return resultVO;
}
	
	
	public List<SchoolMarjorCommonBean> pickMajorBySchoolNameAndZyml(String provinceTableName, String xkCode, String yxmcOrg, String zyml, int jhYear) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolMarjorCommonBean> list = new ArrayList<>();

		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT yxmc, yxmc_org , zymc, zymc_org, pc, xk, zx, zybz, zyml ,nf , zdf, zdfwc FROM " + provinceTableName + "_" + (jhYear - 1) + " x WHERE yxmc_org = ? AND x.zyml = ? AND xk_code_org like ? "
					+ "UNION ALL " 
					+ "SELECT yxmc, yxmc_org , zymc, zymc_org, pc, xk, zx, zybz, zyml ,nf , zdf, zdfwc FROM " + provinceTableName + "_" + (jhYear - 2) + " x WHERE yxmc_org = ? AND x.zyml = ? AND xk_code_org like ? "
					+ "UNION ALL " 
					+ "SELECT yxmc, yxmc_org , zymc, zymc_org, pc, xk, zx, zybz, zyml ,nf , zdf, zdfwc FROM " + provinceTableName + "_" + (jhYear - 3) + " x WHERE yxmc_org = ? AND x.zyml = ? AND xk_code_org like ?  "
					+ "order by zymc, zdf is null, zdf desc";
			
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yxmcOrg);
			ps.setString(2, zyml);
			ps.setString(3, "%" + xkCode + "%");
			ps.setString(4, yxmcOrg);
			ps.setString(5, zyml);
			ps.setString(6, "%" + xkCode + "%");
			ps.setString(7, yxmcOrg);
			ps.setString(8, zyml);
			ps.setString(9, "%" + xkCode + "%");
			rs = ps.executeQuery();
			SchoolMarjorCommonBean bean = null;
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setZdf(rs.getString("zdf"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZymc_org(rs.getString("zymc_org"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setPc(rs.getString("pc"));
				bean.setXk(rs.getString("xk"));
				bean.setZx(rs.getString("zx"));
				bean.setZybz(rs.getString("zybz")); 
				bean.setZyml(rs.getString("zyml"));
				bean.setNf(Tools.getInt(rs.getString("nf")));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<SchoolMarjorCommonBean> pickMajorBySchoolNameAndZymlIsNull(String provinceTableName, String xkCode, String yxmcOrg, int jhYear) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolMarjorCommonBean> list = new ArrayList<>();

		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT yxmc, yxmc_org , zymc, zymc_org, pc, xk, zx, zybz, zyml ,nf , zdf, zdfwc FROM " + provinceTableName + "_" + (jhYear - 1) + " x WHERE yxmc_org = ? AND x.zyml IS NULL AND xk_code_org like ? "
					+ "UNION ALL " 
					+ "SELECT yxmc, yxmc_org , zymc, zymc_org, pc, xk, zx, zybz, zyml ,nf , zdf, zdfwc FROM " + provinceTableName + "_" + (jhYear - 2) + " x WHERE yxmc_org = ? AND x.zyml IS NULL AND xk_code_org like ? "
					+ "UNION ALL " 
					+ "SELECT yxmc, yxmc_org , zymc, zymc_org, pc, xk, zx, zybz, zyml ,nf , zdf, zdfwc FROM " + provinceTableName + "_" + (jhYear - 3) + " x WHERE yxmc_org = ? AND x.zyml IS NULL AND xk_code_org like ? "
					+ "order by zymc, zdf is null, zdf desc ";
			
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yxmcOrg);
			ps.setString(2, "%" + xkCode + "%");
			ps.setString(3, yxmcOrg);
			ps.setString(4, "%" + xkCode + "%");
			ps.setString(5, yxmcOrg); 
			ps.setString(6, "%" + xkCode + "%");
			rs = ps.executeQuery();
			SchoolMarjorCommonBean bean = null;
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setZdf(rs.getString("zdf"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZymc_org(rs.getString("zymc_org"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setPc(rs.getString("pc"));
				bean.setXk(rs.getString("xk"));
				bean.setZx(rs.getString("zx"));
				bean.setZybz(rs.getString("zybz")); 
				bean.setZyml(rs.getString("zyml"));
				bean.setNf(Tools.getInt(rs.getString("nf")));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<SchoolMarjorCommonBean> pickMajorBySchoolNameAndZymcOrg(String provinceTableName, String xkCode, String yxmcOrg, String zymcOrg, int jhYear) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolMarjorCommonBean> list = new ArrayList<>();

		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT yxmc, yxmc_org , zymc, zymc_org, pc, xk, zx, zybz, zyml ,nf , zdf, zdfwc FROM " + provinceTableName + "_" + (jhYear - 1) + " x WHERE yxmc_org = ? AND x.zymc_org = ? AND xk_code_org like ? "
					+ "UNION ALL " 
					+ "SELECT yxmc, yxmc_org , zymc, zymc_org, pc, xk, zx, zybz, zyml ,nf , zdf, zdfwc FROM " + provinceTableName + "_" + (jhYear - 2) + " x WHERE yxmc_org = ? AND x.zymc_org = ? AND xk_code_org like ? "
					+ "UNION ALL " 
					+ "SELECT yxmc, yxmc_org , zymc, zymc_org, pc, xk, zx, zybz, zyml ,nf , zdf, zdfwc FROM " + provinceTableName + "_" + (jhYear - 3) + " x WHERE yxmc_org = ? AND x.zymc_org = ? AND xk_code_org like ? "
							+ "order by zymc, zdf is null, zdf desc ";
			
			ps = conn.prepareStatement(SQL); 
			ps.setString(1, yxmcOrg);
			ps.setString(2, zymcOrg);
			ps.setString(3, "%" + xkCode + "%");
			ps.setString(4, yxmcOrg);
			ps.setString(5, zymcOrg);
			ps.setString(6, "%" + xkCode + "%");
			ps.setString(7, yxmcOrg);
			ps.setString(8, zymcOrg);
			ps.setString(9, "%" + xkCode + "%");
			rs = ps.executeQuery();
			SchoolMarjorCommonBean bean = null;
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setZdf(rs.getString("zdf"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZymc_org(rs.getString("zymc_org"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setPc(rs.getString("pc"));
				bean.setXk(rs.getString("xk"));
				bean.setZx(rs.getString("zx"));
				bean.setZybz(rs.getString("zybz")); 
				bean.setZyml(rs.getString("zyml"));
				bean.setNf(Tools.getInt(rs.getString("nf")));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public ResultVO searchJhDataForZyyxAndExtScoreAndSnybeb(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> selectedSfValuesSets, HashSet<String> zymlSets, HashSet<String> zymcSets, 
			HashSet<String> selectedYXTAGValuesSets, HashSet<String> selectedYXLXValuesSets, HashSet<String> selectedBXXZValuesSet, String keywords, int selected_znzyls, int selected_znzys, String snYx_sfCName, int snybeb, int qsf_start, int qsf_to, int pageNumber) {
	
	Connection conn = null;
	PreparedStatement ps = null;
	int dataYear = jhYear-1;
	ResultSet rs = null;
	List<JHBean> list = new ArrayList<>();
	ResultVO resultVO = new ResultVO();
	try {
		conn = DatabaseUtils.getConnection();
		String selectedSfValuesSetsSQL = selectedSfValuesSets.size() == 0 ? "" : " AND x.yxsf in ("+Tools.getSQLQueryin(selectedSfValuesSets)+")";
		String zymlSetsSQL = zymlSets.size() == 0 ? "" : " AND x.zyml in ("+Tools.getSQLQueryin(zymlSets)+")";
		String zymcSetsSQL = zymcSets.size() == 0 ? "" : " AND x.zymc_org in ("+Tools.getSQLQueryin(zymcSets)+")";
		String selectedYXLXValuesSetsSQL = selectedYXLXValuesSets.size() == 0 ? "" : " AND x.ind_catg in ("+Tools.getSQLQueryin(selectedYXLXValuesSets)+")";
		
		HashSet<String> new_selectedBXXZValuesSet = new HashSet<>();
		boolean contained_hz = false;
		if(selectedBXXZValuesSet.size() > 0) {
			Iterator<String> it = selectedBXXZValuesSet.iterator();
			while(it.hasNext()) {
				String itStr = it.next();
				if(itStr.indexOf("合作") != -1) {
					contained_hz = true;
				}else {
					new_selectedBXXZValuesSet.add(itStr);
				}
			}
		}
		//是否需要中外合作办学
		String hzbxSQL = contained_hz ? " AND (x.is_hz = 1 OR x.ind_nature like '%中外%') " : " AND (x.is_hz <> 1 AND x.ind_nature not like '%中外%') ";
		
		
		String selectedBXXZValuesSetSQL = new_selectedBXXZValuesSet.size() == 0 ? "" : " AND x.ind_nature in ("+Tools.getSQLQueryin(new_selectedBXXZValuesSet)+")";
		
		
		String yxTagsSQL = "";
		if(selectedYXTAGValuesSets.size() > 0) {
			Iterator<String> it = selectedYXTAGValuesSets.iterator();
			StringBuffer temp_sql = new StringBuffer();
			while(it.hasNext()) {
				String itStr = it.next();
				temp_sql.append(" OR x.yx_tags_all like '%"+itStr+"%' ");
			}
			
			if(!Tools.isEmpty(temp_sql.toString())) {
				yxTagsSQL = " AND (" + temp_sql.substring(4) + ")";
			}
		}
		
		
		
		String SEARCH_CONDITION = " FROM " + sfCode + "_jh_" + jhYear + " x WHERE x.xk_code like ? and x.pc = ? and x.pc_code = ? and (x.yxmc like ? or x.zymc like ?) AND (x.zybz is null OR x.zybz like ?) and (znzyls is null OR znzyls <= ?) and (znzys is null OR znzys <= ?) and (x.qsf is null OR x.qsf = 0 OR x.qsf between ? and ?) AND x.is_ybeb = ? AND x.yxsf = ? " 
				+ selectedSfValuesSetsSQL + zymlSetsSQL + zymcSetsSQL + selectedYXLXValuesSetsSQL + selectedBXXZValuesSetSQL + hzbxSQL + yxTagsSQL ;
		String ORDER = " ORDER BY x.qsf is NULL,  x.qsf DESC LIMIT ?,?"; 
		String SELECT_RECORD = "SELECT * " + SEARCH_CONDITION + ORDER;
		String SELECT_CNT = "SELECT count(*) as cnt " + SEARCH_CONDITION ;
		
		ps = conn.prepareStatement(SELECT_RECORD);
		int index = 1;
		ps.setString(index++, "%"+xkCode+"%");
		ps.setString(index++, pc);
		ps.setString(index++, pc_code);
		ps.setString(index++, "%"+keywords+"%");
		ps.setString(index++, "%"+keywords+"%");
		ps.setString(index++, "%"+keywords+"%");
		ps.setInt(index++, selected_znzyls);
		ps.setInt(index++, selected_znzys);
		ps.setInt(index++, qsf_start);
		ps.setInt(index++, qsf_to);
		ps.setInt(index++, snybeb);
		ps.setString(index++, snYx_sfCName);
		ps.setInt(index++, (pageNumber - 1) * PAGE_ROW_CNT);
		ps.setInt(index++, PAGE_ROW_CNT);
		SQLLogUtils.printSQL(ps);
		rs = ps.executeQuery();
		JHBean bean = null;
		while (rs.next()) {
			list.add(ZyzdFormJDBC.setJHBeanAll(dataYear, rs));
		}
		
		
		index = 1;
		ps = conn.prepareStatement(SELECT_CNT);
		ps.setString(index++, "%"+xkCode+"%");
		ps.setString(index++, pc);
		ps.setString(index++, pc_code);
		ps.setString(index++, "%"+keywords+"%");
		ps.setString(index++, "%"+keywords+"%");
		ps.setString(index++, "%"+keywords+"%");
		ps.setInt(index++, selected_znzyls);
		ps.setInt(index++, selected_znzys);
		ps.setInt(index++, qsf_start);
		ps.setInt(index++, qsf_to);
		ps.setInt(index++, snybeb);
		ps.setString(index++, snYx_sfCName);
		rs = ps.executeQuery();
		while (rs.next()) {
			resultVO.setRecordCnt(rs.getInt("cnt"));
		}
		
	} catch (Exception ex) {
		ex.printStackTrace();
	} finally {
		closeAllConnection(conn, ps, rs);
	}
	resultVO.setResult(list);
	resultVO.setCurrentPage(pageNumber);
	return resultVO;
}
	
	public ResultVO searchJhDataForZyzyx(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> selectedSfValuesSets, HashSet<String> zymlSets, HashSet<String> zymcSets, 
			HashSet<String> selectedYXTAGValuesSets, HashSet<String> selectedYXLXValuesSets, HashSet<String> selectedBXXZValuesSet, String keywords, int selected_znzyls, int selected_znzys, int wc_start, int wc_to, int pageNumber) {
	
			Connection conn = null;
			PreparedStatement ps = null;
			int dataYear = jhYear-1;
			ResultSet rs = null;
			List<JHBean> list = new ArrayList<>();
			ResultVO resultVO = new ResultVO();
			try {
				conn = DatabaseUtils.getConnection();
				String selectedSfValuesSetsSQL = selectedSfValuesSets.size() == 0 ? "" : " AND x.yxsf in ("+Tools.getSQLQueryin(selectedSfValuesSets)+")";
				String zymlSetsSQL = zymlSets.size() == 0 ? "" : " AND x.zyml in ("+Tools.getSQLQueryin(zymlSets)+")";
				String zymcSetsSQL = zymcSets.size() == 0 ? "" : " AND x.zymc_org in ("+Tools.getSQLQueryin(zymcSets)+")";
				String selectedYXLXValuesSetsSQL = selectedYXLXValuesSets.size() == 0 ? "" : " AND x.ind_catg in ("+Tools.getSQLQueryin(selectedYXLXValuesSets)+")";
				
				HashSet<String> new_selectedBXXZValuesSet = new HashSet<>();
				boolean contained_hz = false;
				if(selectedBXXZValuesSet.size() > 0) {
					Iterator<String> it = selectedBXXZValuesSet.iterator();
					while(it.hasNext()) {
						String itStr = it.next();
						if(itStr.indexOf("合作") != -1) {
							contained_hz = true;
						}else {
							new_selectedBXXZValuesSet.add(itStr);
						}
					}
				}
				//是否需要中外合作办学
				String hzbxSQL = contained_hz ? " AND (x.is_hz = 1 OR x.ind_nature like '%中外%') " : " AND (x.is_hz <> 1 AND x.ind_nature not like '%中外%') ";
				
				
				String selectedBXXZValuesSetSQL = new_selectedBXXZValuesSet.size() == 0 ? "" : " AND x.ind_nature in ("+Tools.getSQLQueryin(new_selectedBXXZValuesSet)+")";
				
				
				String yxTagsSQL = "";
				if(selectedYXTAGValuesSets.size() > 0) {
					Iterator<String> it = selectedYXTAGValuesSets.iterator();
					StringBuffer temp_sql = new StringBuffer();
					while(it.hasNext()) {
						String itStr = it.next();
						temp_sql.append(" OR x.yx_tags_all like '%"+itStr+"%' ");
					}
					
					if(!Tools.isEmpty(temp_sql.toString())) {
						yxTagsSQL = " AND (" + temp_sql.substring(4) + ")";
					}
				}
				
				
				
				String SEARCH_CONDITION = " FROM " + sfCode + "_jh_" + jhYear + " x WHERE x.xk_code like ? and x.pc = ? and x.pc_code = ? and (x.yxmc like ? or x.zymc like ?) and (x.zybz is NULL or x.zybz like ?) and (znzyls is null OR znzyls <= ?) and (znzys is null OR znzys <= ?) and (x.zdfwc is null OR x.zdfwc between ? and ?) " 
						+ selectedSfValuesSetsSQL + zymlSetsSQL + zymcSetsSQL + selectedYXLXValuesSetsSQL + selectedBXXZValuesSetSQL + hzbxSQL + yxTagsSQL ;
				String GROUP_BY = " GROUP BY x.yxdm, x.zyz";
				String ORDER = " ORDER BY MAX(x.zdfwc) is NULL,MAX(x.zdfwc) ASC LIMIT ?,?";
				String SELECT_RECORD = "SELECT x.yxdm, x.yxmc, x.zyz " + SEARCH_CONDITION + GROUP_BY + ORDER;
				String SELECT_CNT = "SELECT count(*) as cnt " + SEARCH_CONDITION + GROUP_BY;
				
				ps = conn.prepareStatement(SELECT_RECORD);
				int index = 1;
				ps.setString(index++, "%"+xkCode+"%");
				ps.setString(index++, pc);
				ps.setString(index++, pc_code);
				ps.setString(index++, "%"+keywords+"%"); 
				ps.setString(index++, "%"+keywords+"%");
				ps.setString(index++, "%"+keywords+"%");
				ps.setInt(index++, selected_znzyls);
				ps.setInt(index++, selected_znzys);
				ps.setInt(index++, wc_start);
				ps.setInt(index++, wc_to);
				ps.setInt(index++, (pageNumber - 1) * PAGE_ROW_CNT);
				ps.setInt(index++, PAGE_ROW_CNT);
				rs = ps.executeQuery();
				SQLLogUtils.printSQL("searchJhDataForZyzyx>>>>>>>>", ps);
				JHBean bean = null;
				while (rs.next()) {
					bean = new JHBean();
					bean.setYxdm(rs.getString("yxdm")); 
					bean.setYxmc(rs.getString("yxmc")); 
				    bean.setZyz(rs.getString("zyz"));
					list.add(bean);
				}
				
				
				index = 1;
				ps = conn.prepareStatement(SELECT_CNT);
				ps.setString(index++, "%"+xkCode+"%");
				ps.setString(index++, pc);
				ps.setString(index++, pc_code);
				ps.setString(index++, "%"+keywords+"%");
				ps.setString(index++, "%"+keywords+"%");
				ps.setString(index++, "%"+keywords+"%");
				ps.setInt(index++, selected_znzyls);
				ps.setInt(index++, selected_znzys);
				ps.setInt(index++, wc_start);
				ps.setInt(index++, wc_to);
				rs = ps.executeQuery();
				while (rs.next()) {
					resultVO.setRecordCnt(rs.getInt("cnt"));
				}
				
			} catch (Exception ex) {
				ex.printStackTrace();
			} finally {
				closeAllConnection(conn, ps, rs);
			}
			resultVO.setResult(list);
			resultVO.setCurrentPage(pageNumber);
			return resultVO;
		}
	
	
	
	
	public ResultVO searchJhDataForYxyx(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> selectedSfValuesSets, HashSet<String> zymlSets, HashSet<String> zymcSets, 
			HashSet<String> selectedYXTAGValuesSets, HashSet<String> selectedYXLXValuesSets, HashSet<String> selectedBXXZValuesSet, String keywords, int selected_znzyls, int selected_znzys, int wc_start, int wc_to, int pageNumber) {
	
			Connection conn = null;
			PreparedStatement ps = null;
			int dataYear = jhYear-1;
			ResultSet rs = null;
			List<JHBean> list = new ArrayList<>();
			ResultVO resultVO = new ResultVO();
			try {
				conn = DatabaseUtils.getConnection();
				String selectedSfValuesSetsSQL = selectedSfValuesSets.size() == 0 ? "" : " AND x.yxsf in ("+Tools.getSQLQueryin(selectedSfValuesSets)+")";
				String zymlSetsSQL = zymlSets.size() == 0 ? "" : " AND x.zyml in ("+Tools.getSQLQueryin(zymlSets)+")";
				String zymcSetsSQL = zymcSets.size() == 0 ? "" : " AND x.zymc_org in ("+Tools.getSQLQueryin(zymcSets)+")";
				String selectedYXLXValuesSetsSQL = selectedYXLXValuesSets.size() == 0 ? "" : " AND x.ind_catg in ("+Tools.getSQLQueryin(selectedYXLXValuesSets)+")";
				
				HashSet<String> new_selectedBXXZValuesSet = new HashSet<>();
				boolean contained_hz = false;
				if(selectedBXXZValuesSet.size() > 0) {
					Iterator<String> it = selectedBXXZValuesSet.iterator();
					while(it.hasNext()) {
						String itStr = it.next();
						if(itStr.indexOf("合作") != -1) {
							contained_hz = true;
						}else {
							new_selectedBXXZValuesSet.add(itStr);
						}
					}
				}
				//是否需要中外合作办学
				String hzbxSQL = contained_hz ? " AND (x.is_hz = 1 OR x.ind_nature like '%中外%') " : " AND (x.is_hz <> 1 AND x.ind_nature not like '%中外%') ";
				
				
				String selectedBXXZValuesSetSQL = new_selectedBXXZValuesSet.size() == 0 ? "" : " AND x.ind_nature in ("+Tools.getSQLQueryin(new_selectedBXXZValuesSet)+")";
				
				
				String yxTagsSQL = "";
				if(selectedYXTAGValuesSets.size() > 0) {
					Iterator<String> it = selectedYXTAGValuesSets.iterator();
					StringBuffer temp_sql = new StringBuffer();
					while(it.hasNext()) {
						String itStr = it.next();
						temp_sql.append(" OR x.yx_tags_all like '%"+itStr+"%' ");
					}
					
					if(!Tools.isEmpty(temp_sql.toString())) {
						yxTagsSQL = " AND (" + temp_sql.substring(4) + ")";
					}
				}
				
				
				
				String SEARCH_CONDITION = " FROM " + sfCode + "_jh_" + jhYear + " x WHERE x.xk_code like ? and x.pc = ? and x.pc_code = ? and (x.yxmc like ? or x.zymc like ? or x.zybz like ?) and znzyls <= ? and znzys <= ? and (x.zdfwc is null OR x.zdfwc between ? and ?) " 
						+ selectedSfValuesSetsSQL + zymlSetsSQL + zymcSetsSQL + selectedYXLXValuesSetsSQL + selectedBXXZValuesSetSQL + hzbxSQL + yxTagsSQL ;
				String GROUP_BY = " GROUP BY x.yxdm,x.yxmc, x.yxmc_org ";
				String ORDER = " ORDER BY MAX(x.zdfwc) is NULL,MAX(x.zdfwc) ASC LIMIT ?,?";
				String SELECT_RECORD = "SELECT x.yxdm, x.yxmc, x.yxmc_org, "
						+ "sum(COALESCE(jhs_2024, 0)) as jhs_2024, sum(COALESCE(jhs_2023, 0)) as jhs_2023, sum(COALESCE(jhs_2022, 0)) as jhs_2022, "
						+ "min(COALESCE(zdf_2024, 99999999)) as zdf_2024, min(COALESCE(zdf_2023, 99999999)) as zdf_2023, min(COALESCE(zdf_2022, 99999999)) as zdf_2022, "
						+ "max(COALESCE(zdfwc_2024, 0)) as zdfwc_2024, max(COALESCE(zdfwc_2023, 0)) as zdfwc_2023, max(COALESCE(zdfwc_2022, 0)) as zdfwc_2022, "
						+ "min(COALESCE(pjf_2024, 99999999)) as pjf_2024, min(COALESCE(pjf_2023, 99999999)) as pjf_2023, min(COALESCE(pjf_2022, 99999999)) as pjf_2022, "
						+ "max(COALESCE(pjfwc_2024, 0)) as pjfwc_2024, max(COALESCE(pjfwc_2023, 0)) as pjfwc_2023, max(COALESCE(pjfwc_2022, 0)) as pjfwc_2022, "
						+ "min(COALESCE(zgf_2024, 99999999)) as zgf_2024, min(COALESCE(zgf_2023, 99999999)) as zgf_2023, min(COALESCE(zgf_2022, 99999999)) as zgf_2022, "
						+ "max(COALESCE(zgfwc_2024, 0)) as zgfwc_2024, max(COALESCE(zgfwc_2023, 0)) as zgfwc_2023, max(COALESCE(zgfwc_2022, 0)) as zgfwc_2022, "
						+ "min(COALESCE(qsf_a, 99999999)) as qsf_a, min(COALESCE(qsf_b, 99999999)) as qsf_b, min(COALESCE(qsf_c, 99999999)) as qsf_c, min(COALESCE(qsf, 99999999)) as qsf,"
						+ "sum(COALESCE(znzys,0)) AS znzys, sum(COALESCE(znzyls,0)) AS znzyls, sum(COALESCE(jhs,0)) AS znjhs" + SEARCH_CONDITION + GROUP_BY + ORDER;
				String SELECT_CNT = "SELECT count(*) as cnt " + SEARCH_CONDITION + GROUP_BY;
				
				ps = conn.prepareStatement(SELECT_RECORD);
				int index = 1;
				ps.setString(index++, "%"+xkCode+"%");
				ps.setString(index++, pc);
				ps.setString(index++, pc_code); 
				ps.setString(index++, "%"+keywords+"%");
				ps.setString(index++, "%"+keywords+"%");
				ps.setString(index++, "%"+keywords+"%");
				ps.setInt(index++, selected_znzyls);
				ps.setInt(index++, selected_znzys);
				ps.setInt(index++, wc_start);
				ps.setInt(index++, wc_to);
				ps.setInt(index++, (pageNumber - 1) * PAGE_ROW_CNT);
				ps.setInt(index++, PAGE_ROW_CNT);
				rs = ps.executeQuery();
				SQLLogUtils.printSQL("searchJhDataForYxyx>>>>>>>>", ps);
				JHBean bean = null;
				while (rs.next()) {
					bean = new JHBean();
					bean.setYxdm(rs.getString("yxdm"));  
				    bean.setYxmc(rs.getString("yxmc"));  
				    bean.setYxmc_org(rs.getString("yxmc_org"));  
				    bean.setZdf_a(rs.getString("zdf_2024"));
				    bean.setZdf_b(rs.getString("zdf_"+(dataYear-1)+""));
				    bean.setZdf_c(rs.getString("zdf_2022"));
				    bean.setZdfwc_a(rs.getString("zdfwc_2024"));
				    bean.setZdfwc_b(rs.getString("zdfwc_2023"));
				    bean.setZdfwc_c(rs.getString("zdfwc_2022"));
				    bean.setPjf_a(rs.getString("pjf_2024"));
				    bean.setPjf_b(rs.getString("pjf_2023"));
				    bean.setPjf_c(rs.getString("pjf_2022"));
				    bean.setPjfwc_a(rs.getString("pjfwc_2024"));
				    bean.setPjfwc_b(rs.getString("pjfwc_2023"));
				    bean.setPjfwc_c(rs.getString("pjfwc_2022"));
				    bean.setZgf_a(rs.getString("zgf_2024"));
				    bean.setZgf_b(rs.getString("zgf_2023"));
				    bean.setZgf_c(rs.getString("zgf_2022"));
				    bean.setZgfwc_a(rs.getString("zgfwc_2024"));
				    bean.setZgfwc_b(rs.getString("zgfwc_2023"));
				    bean.setZgfwc_c(rs.getString("zgfwc_2022"));
				    bean.setJhs_a(rs.getString("jhs_2024"));
				    bean.setJhs_b(rs.getString("jhs_2023"));
				    bean.setJhs_c(rs.getString("jhs_2022"));
				    bean.setQsf_a(rs.getInt("qsf_a"));
				    bean.setQsf_b(rs.getInt("qsf_b"));
				    bean.setQsf_c(rs.getInt("qsf_c"));
				    bean.setQsf(rs.getInt("qsf"));
				    bean.setZnzys(rs.getInt("znzys"));
				    bean.setZnzyls(rs.getInt("znzyls"));
				    bean.setZnjhs(rs.getInt("znjhs"));
					list.add(bean);
				}
				
				
				index = 1;
				ps = conn.prepareStatement(SELECT_CNT);
				ps.setString(index++, "%"+xkCode+"%");
				ps.setString(index++, pc);
				ps.setString(index++, pc_code);
				ps.setString(index++, "%"+keywords+"%");
				ps.setString(index++, "%"+keywords+"%");
				ps.setString(index++, "%"+keywords+"%");
				ps.setInt(index++, selected_znzyls);
				ps.setInt(index++, selected_znzys);
				ps.setInt(index++, wc_start);
				ps.setInt(index++, wc_to);
				rs = ps.executeQuery();
				while (rs.next()) {
					resultVO.setRecordCnt(rs.getInt("cnt"));
				}
				
			} catch (Exception ex) {
				ex.printStackTrace();
			} finally {
				closeAllConnection(conn, ps, rs);
			}
			resultVO.setResult(list);
			resultVO.setCurrentPage(pageNumber);
			return resultVO;
		}
	
	public ResultVO searchJhxDataInFormMgrForReplace(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> selectedSfValuesSets, HashSet<String> zymcSets, 
			HashSet<String> selectedYXTAGValuesSets, HashSet<String> selectedYXLXValuesSets, HashSet<String> selectedBXXZValuesSet, HashSet<String> selectedTspcExceptValuesSet, boolean is_hz, String keywords, String keywords_ext, int selected_znzyls, int selected_znzys, int wc_start, int wc_to, int pageNumber) {
	
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHxBean> list = new ArrayList<>();
		ResultVO resultVO = new ResultVO();
		try {
			conn = DatabaseUtils.getConnection();
			String selectedSfValuesSetsSQL = selectedSfValuesSets.size() == 0 ? "" : " AND x.yxsf in ("+Tools.getSQLQueryin(selectedSfValuesSets)+")";
			String selectedYXLXValuesSetsSQL = selectedYXLXValuesSets.size() == 0 ? "" : " AND x.ind_catg in ("+Tools.getSQLQueryin(selectedYXLXValuesSets)+")";
			String tspcExceptSetsSQL = selectedTspcExceptValuesSet.size() == 0 ? "" : " AND (x.pc_type is NULL OR x.pc_type not in ("+Tools.getSQLQueryin(selectedTspcExceptValuesSet)+"))";
			
			String zymcSetsSQL = "";
			if(zymcSets.size() > 0) {
				Iterator<String> it = zymcSets.iterator();
				StringBuffer temp_sql = new StringBuffer();
				while(it.hasNext()) {
					String itStr = it.next();
					temp_sql.append(" OR x.znzy like '%"+itStr+"%' ");
				}
				
				if(!Tools.isEmpty(temp_sql.toString())) {
					zymcSetsSQL = " AND (" + temp_sql.substring(4) + ")";
				}
			}
			
			//公办/中外合作办学
			String selectedBXXZValuesSetSQL = selectedBXXZValuesSet.size() == 0 ? "" : " AND x.ind_nature in ("+Tools.getSQLQueryin(selectedBXXZValuesSet)+")";
			String selectedHzbxValuesSetSQL =  is_hz ? " AND x.is_hz = 1 " : " AND (x.is_hz = 0 OR x.is_hz is null) ";
			
			String yxTagsSQL = ""; 
			if(selectedYXTAGValuesSets.size() > 0) {
				Iterator<String> it = selectedYXTAGValuesSets.iterator();
				StringBuffer temp_sql = new StringBuffer();
				while(it.hasNext()) {
					String itStr = it.next();
					temp_sql.append(" OR x.yx_tags_all like '%"+itStr+"%' ");
				}
				
				if(!Tools.isEmpty(temp_sql.toString())) {
					yxTagsSQL = " AND (" + temp_sql.substring(4) + ")";
				}
			}
			
			
			String SEARCH_CONDITION = " FROM " + sfCode + "_jh_" + jhYear + "x x WHERE x.xk_code like ? AND x.pc = ? AND x.pc_code = ? AND ((x.yxmc like ? "+(Tools.isEmpty(keywords + keywords_ext) ? "OR x.znzy IS NULL" : "")+" OR x.znzy like ?) AND (x.yxmc like ? "+(Tools.isEmpty(keywords + keywords_ext) ? "OR x.znzy IS NULL" : "")+" OR x.znzy like ?)) AND (znzyls is null OR znzyls <= ?) AND (x.zyzzdfwc is null OR x.zyzzdfwc <= 0 OR CAST(COALESCE(x.zyzzdfwc,99999999) AS SIGNED) between ? AND ?) " 
					+ selectedSfValuesSetsSQL + zymcSetsSQL + selectedYXLXValuesSetsSQL + selectedBXXZValuesSetSQL + selectedHzbxValuesSetSQL + tspcExceptSetsSQL + yxTagsSQL ;
			String ORDER = " ORDER BY x.zyzzdfwc IS NULL, x.zyzzdfwc <= 0, CAST(COALESCE(x.zyzzdfwc,99999999) AS SIGNED) ASC LIMIT ?,?";  
			String SELECT_RECORD = "SELECT * " + SEARCH_CONDITION + ORDER;
			String SELECT_CNT = "SELECT count(*) as cnt " + SEARCH_CONDITION ; 
			System.out.println(SELECT_RECORD);
			ps = conn.prepareStatement(SELECT_RECORD);
			int index = 1; 
			ps.setString(index++, "%"+xkCode+"%");
			ps.setString(index++, pc);
			ps.setString(index++, pc_code);
			ps.setString(index++, "%"+keywords+"%");
			ps.setString(index++, "%"+keywords+"%");
			ps.setString(index++, "%"+keywords_ext+"%");
			ps.setString(index++, "%"+keywords_ext+"%");
			ps.setInt(index++, selected_znzyls);
			ps.setInt(index++, wc_start);
			ps.setInt(index++, wc_to);
			ps.setInt(index++, (pageNumber - 1) * PAGE_ROW_CNT);
			ps.setInt(index++, PAGE_ROW_CNT);
			SQLLogUtils.printSQL(ps);
			rs = ps.executeQuery();
			JHBean bean = null;
			while (rs.next()) {
				list.add(ZyzdFormJDBC.setJHxBeanAll(dataYear, rs));
			}
			
			
			index = 1;
			ps = conn.prepareStatement(SELECT_CNT);
			ps.setString(index++, "%"+xkCode+"%");
			ps.setString(index++, pc);
			ps.setString(index++, pc_code);
			ps.setString(index++, "%"+keywords+"%");
			ps.setString(index++, "%"+keywords+"%");
			ps.setString(index++, "%"+keywords_ext+"%");
			ps.setString(index++, "%"+keywords_ext+"%");
			ps.setInt(index++, selected_znzyls);
			ps.setInt(index++, wc_start);
			ps.setInt(index++, wc_to);
			rs = ps.executeQuery();
			while (rs.next()) {
				resultVO.setRecordCnt(rs.getInt("cnt"));
			}
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		resultVO.setResult(list);
		resultVO.setCurrentPage(pageNumber);
		return resultVO;
	}
	
	public List<JHxBean> searchJhxData(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> yxmcSets, HashSet<String> zyzSets) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHxBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
	
			String yxmcSetsSQL = yxmcSets.size() == 0 ? "" : " AND x.yxmc in ("+Tools.getSQLQueryin(yxmcSets)+")";
			String zyzSetsSQL = zyzSets.size() == 0 ? "" : " AND x.zyz in ("+Tools.getSQLQueryin(zyzSets)+")";
			String SEARCH_CONDITION = "SELECT * FROM " + sfCode + "_jh_" + jhYear + "x x WHERE x.xk_code like ? and x.pc = ? and x.pc_code = ? " + yxmcSetsSQL + zyzSetsSQL;
			
			ps = conn.prepareStatement(SEARCH_CONDITION); 
			int index = 1;
			ps.setString(index++, "%"+xkCode+"%");
			ps.setString(index++, pc);
			ps.setString(index++, pc_code);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL("searchJhxData>>>>>>>>", ps);
			while (rs.next()) {
				list.add(ZyzdFormJDBC.setJHxBeanAll(dataYear, rs));
			}
			
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JHBean> searchJhData(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> yxdmSets) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
	
			String yxdmSetsSQL = yxdmSets.size() == 0 ? "" : " AND x.yxdm in ("+Tools.getSQLQueryin(yxdmSets)+")";
			String SEARCH_CONDITION = "SELECT * FROM " + sfCode + "_jh_" + jhYear + " x WHERE x.xk_code like ? and x.pc = ? and x.pc_code = ? " + yxdmSetsSQL ;
			
			ps = conn.prepareStatement(SEARCH_CONDITION); 
			int index = 1;
			ps.setString(index++, "%"+xkCode+"%");
			ps.setString(index++, pc);
			ps.setString(index++, pc_code);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL("searchJhData>>>>>>>>", ps);
			JHBean bean = null;
			while (rs.next()) {
				list.add(ZyzdFormJDBC.setJHBeanAll(dataYear, rs));
			}
			
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JHBean> searchJhDataByYxmc(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> yxmcSets) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
	
			String yxmcSetsSQL = yxmcSets.size() == 0 ? "" : " AND x.yxmc in ("+Tools.getSQLQueryin(yxmcSets)+")";
			String SEARCH_CONDITION = "SELECT * FROM " + sfCode + "_jh_" + jhYear + " x WHERE x.xk_code like ? and x.pc = ? and x.pc_code = ? " + yxmcSetsSQL ;
			
			ps = conn.prepareStatement(SEARCH_CONDITION); 
			int index = 1;
			ps.setString(index++, "%"+xkCode+"%");
			ps.setString(index++, pc);
			ps.setString(index++, pc_code);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL("searchJhDataByYxmc>>>>>>>>", ps);
			JHBean bean = null;
			while (rs.next()) {
				list.add(ZyzdFormJDBC.setJHBeanAll(dataYear, rs));
			}
			
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public List<JHBean> searchJhWithNoData(int jhYear, String sfCode, String xkCode, String pc, String pc_code) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
	
			String SEARCH_CONDITION = "SELECT x.yxmc, x.yxmc_org, x.yxsf  FROM " + sfCode + "_jh_" + jhYear + " x WHERE x.xk_code like ? and x.pc = ? and x.pc_code = ? "
					+ "and (x.zdf_"+dataYear+" is null or x.zdf_"+dataYear+" = '') "
					+ "and (x.zdf_"+(dataYear - 1)+" is null or x.zdf_"+(dataYear - 1)+" = '')"
					+ "and (x.zdf_"+(dataYear - 2)+" is null or x.zdf_"+(dataYear - 2)+" = '')";
			
			ps = conn.prepareStatement(SEARCH_CONDITION + " GROUP by x.yxmc, x.yxmc_org, x.yxsf"); 
			int index = 1;
			ps.setString(index++, "%"+xkCode+"%");
			ps.setString(index++, pc);
			ps.setString(index++, pc_code);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL("searchJhData>>>>>>>>", ps); 
			JHBean bean = null;
			while (rs.next()) {
				bean = new JHBean();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setYxsf(rs.getString("yxsf"));
				list.add(bean);
			}
			
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public List<JHBean> searchJhWithNearestPcx(int jhYear, String sfCode, String xkCode, String pc, String pc_code, int pcx_a, int pcx_b, int pcx_c) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SEARCH_CONDITION = "SELECT x.yxmc, x.yxmc_org, x.yxsf  FROM " + sfCode + "_jh_" + jhYear + " x WHERE x.xk_code like ? and x.pc = ? and x.pc_code = ? "
					+ "and ("
					+ "CAST(COALESCE(x.zdf_"+dataYear+", '99999') AS SIGNED) < ? "
							+ "OR CAST(COALESCE(x.zdf_"+(dataYear - 1)+",'99999') AS SIGNED) < ? "
									+ "OR CAST(COALESCE(x.zdf_"+(dataYear - 2)+",'99999') AS SIGNED) < ?)";
			
			ps = conn.prepareStatement(SEARCH_CONDITION + " GROUP by x.yxmc, x.yxmc_org, x.yxsf"); 
			int index = 1;
			ps.setString(index++, "%"+xkCode+"%");
			ps.setString(index++, pc);
			ps.setString(index++, pc_code); 
			ps.setInt(index++, pcx_a + 15); 
			ps.setInt(index++, pcx_b + 15);
			ps.setInt(index++, pcx_c + 15);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL("searchJhData>>>>>>>>", ps); 
			JHBean bean = null;
			while (rs.next()) {
				bean = new JHBean();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setYxsf(rs.getString("yxsf"));
				list.add(bean);
			}
			
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	// 根据动态条件查询计划院校信息
		public ResultVO getMajorJhByDynamicCondition(int jhYear, String sfCode, String xkCode, 
			    String pc, String pc_code, String zymc, int wcStart, int wcEnd, 
			    String majorScope, String indNature, int pageNumber) {
			        
			    Connection conn = null;
			    PreparedStatement ps = null;
			    ResultSet rs = null;
			    ResultVO resultVO = new ResultVO();
			    int recordCnt = 0;
			    List<JHBean> list = new ArrayList<>();
			    
			    try {
			    
			        conn = DatabaseUtils.getConnection();
			        
			        // 构建查询SQL
			        StringBuilder sql = new StringBuilder();
			        sql.append("SELECT * FROM ").append(sfCode).append("_jh_").append(jhYear)
			           .append(" WHERE pc = ? AND pc_code = ? AND xk_code LIKE ? ")
			           .append(" AND zdfwc BETWEEN ? AND ? ")
			           .append(" AND ").append(majorScope).append(" = ? ")
			           .append(" AND ind_nature LIKE ? ")
			           .append(" ORDER BY zdfwc ASC LIMIT ?,? ");
			        
			        // 执行查询
			        ps = conn.prepareStatement(sql.toString());
			        int paramIndex = 1;
			        ps.setString(paramIndex++, pc);
			        ps.setString(paramIndex++, pc_code);
			        ps.setString(paramIndex++, "%" + xkCode + "%");
			        ps.setInt(paramIndex++, wcEnd);
			        ps.setInt(paramIndex++, wcStart);
			        ps.setString(paramIndex++, zymc);
			        ps.setString(paramIndex++, indNature);
			        ps.setInt(paramIndex++, (pageNumber - 1) * PAGE_ROW_CNT);
			        ps.setInt(paramIndex++, PAGE_ROW_CNT);
			        
			        rs = ps.executeQuery();
			        
			        SQLLogUtils.printSQL(" === getMajorJhByDynamicCondition: ", ps);
			        
			        while(rs.next()) {
			            list.add(setJHBeanAll(jhYear-1, rs));
			        }
			        
			        // 获取总记录数
			        StringBuilder countSql = new StringBuilder();
			        countSql.append("SELECT COUNT(*) as cnt FROM ").append(sfCode).append("_jh_").append(jhYear)
			                .append(" WHERE pc = ? AND pc_code = ? AND xk_code LIKE ? ")
			                .append(" AND zdfwc BETWEEN ? AND ? ")
			                .append(" AND ").append(majorScope).append(" = ? ")
			                .append(" AND ind_nature LIKE ? ");
			        
			        ps = conn.prepareStatement(countSql.toString());
			        paramIndex = 1;
			        ps.setString(paramIndex++, pc);
			        ps.setString(paramIndex++, pc_code);
			        ps.setString(paramIndex++, "%" + xkCode + "%");
			        ps.setInt(paramIndex++, wcEnd);
			        ps.setInt(paramIndex++, wcStart);
			        ps.setString(paramIndex++, zymc);
			        ps.setString(paramIndex++, indNature);
			        
			        rs = ps.executeQuery();
			        if(rs.next()) {
			            recordCnt = rs.getInt("cnt");
			        }
			        
			        // 设置返回结果
			        resultVO.setCurrentPage(pageNumber);
			        resultVO.setRecordCnt(recordCnt);
			        resultVO.setResult(list);
			        
			    } catch(Exception ex) {
			        ex.printStackTrace();
			    } finally {
			        closeAllConnection(conn, ps, rs);
			    }
			    
			    return resultVO;
			}
	
	public boolean addLhyOrder(LhyOrder lhyOrder) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		LhyOrder order = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "insert into lhy_order(order_id,ref_c_id,lhy_c_id,lhy_c_id_org,sf,stu_info_name,stu_info_xb,stu_info_bj,stu_info_xx,stu_info_dh,stu_info_mz,stu_info_sl,stu_info_sg,stu_info_tz,stu_score_xk,stu_score_cj,stu_score_wc,ext_father_name,ext_mother_name,ext_other_name,ext_contact,ext_economics,ext_remark,status,create_tm,next_resp_tm) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,NOW(),NULL)";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			int index = 1;
			ps.setString(index++, lhyOrder.getOrder_id());
			ps.setString(index++, lhyOrder.getRef_c_id());
			ps.setString(index++, lhyOrder.getLhy_c_id());
			ps.setString(index++, lhyOrder.getLhy_c_id_org());
			ps.setString(index++, lhyOrder.getSf());
			ps.setString(index++, lhyOrder.getStu_info_name());
			ps.setString(index++, lhyOrder.getStu_info_xb());
			ps.setString(index++, lhyOrder.getStu_info_bj());
			ps.setString(index++, lhyOrder.getStu_info_xx());
			ps.setString(index++, lhyOrder.getStu_info_dh());
			ps.setString(index++, lhyOrder.getStu_info_mz());
			ps.setString(index++, lhyOrder.getStu_info_sl());
			ps.setString(index++, lhyOrder.getStu_info_sg());
			ps.setString(index++, lhyOrder.getStu_info_tz());
			ps.setString(index++, lhyOrder.getStu_score_xk());
			ps.setInt(index++, lhyOrder.getStu_score_cj());
			ps.setInt(index++, lhyOrder.getStu_score_wc());
			ps.setString(index++, lhyOrder.getExt_father_name());
			ps.setString(index++, lhyOrder.getExt_mother_name());
			ps.setString(index++, lhyOrder.getExt_other_name());
			ps.setString(index++, lhyOrder.getExt_contact());
			ps.setString(index++, lhyOrder.getExt_economics());
			ps.setString(index++, lhyOrder.getExt_remark());
			ps.setInt(index++, lhyOrder.getStatus());
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean deleteLhyOrder(String order_id, String lhy_c_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		LhyOrder order = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "update lhy_order x set x.status = 30 where x.order_id = ? and x.lhy_c_id = ?";
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, order_id);
			ps.setString(2, lhy_c_id); 
			SQLLogUtils.printSQL(ps);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}

	private static ZyzdExpPackageMajor setZyzdExpPackageMajor(ResultSet rs) throws Exception {
		ZyzdExpPackageMajor bean = new ZyzdExpPackageMajor();
		bean.setId(rs.getInt("id"));
		bean.setZymc(rs.getString("zymc"));
		bean.setCc(rs.getString("cc"));
		bean.setPack_name(rs.getString("pack_name"));
		bean.setPack_desc(rs.getString("pack_desc"));
		return bean;
	}
	
	private static ZyzdExpPackageHY setZyzdExpPackageHY(ResultSet rs) throws Exception {
		ZyzdExpPackageHY bean = new ZyzdExpPackageHY();
		bean.setId(rs.getInt("id"));
		bean.setZymc(rs.getString("zymc"));
		bean.setCc(rs.getString("cc"));
		bean.setPack_name(rs.getString("pack_name"));
		bean.setPack_desc(rs.getString("pack_desc"));
		return bean;
	}
	
	
	public List<ZyzdExpPackageMajor> getExpPackageNames(String cc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdExpPackageMajor> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT pack_name,pack_desc FROM zyzd_exp_package_major x WHERE x.cc = ? group by pack_name,pack_desc order by pack_name";
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, cc);
			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === getExpPackageNames(): ", ps);
			
			ZyzdExpPackageMajor bean = null;
			while (rs.next()) {
				bean = new ZyzdExpPackageMajor();
				bean.setPack_name(rs.getString("pack_name"));
				bean.setPack_desc(rs.getString("pack_desc"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public List<ZyzdExpPackageMajor> getExpPackageListByName(String pack_name, String cc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdExpPackageMajor> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM zyzd_exp_package_major x WHERE x.pack_name = ? and x.cc = ? order by sort desc";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			
			ps.setString(1, pack_name);
			ps.setString(2, cc);
			
			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === getExpPackageListByName(): ", ps);
			
			while (rs.next()) {
				list.add(setZyzdExpPackageMajor(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public List<ZyzdExpPackageHY> getExpPackageHYNames(String cc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdExpPackageHY> list = new ArrayList<>();
		
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT pack_name,pack_desc,sort_pack FROM zyzd_exp_package_hy x WHERE x.cc = ? group by pack_name,pack_desc,sort_pack order by sort_pack desc, pack_name";
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, cc);
			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === getExpPackageHYNames(): ", ps);
			
			ZyzdExpPackageHY bean = null;
			while (rs.next()) {
				bean = new ZyzdExpPackageHY();
				bean.setPack_name(rs.getString("pack_name"));
				bean.setPack_desc(rs.getString("pack_desc"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public List<ZyzdExpPackageHY> getExpPackageHYListByName(String pack_name, String cc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdExpPackageHY> list = new ArrayList<>();
		
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM zyzd_exp_package_hy x WHERE x.pack_name = ? and x.cc = ? order by sort desc";
			ps = conn.prepareStatement(SELECT_CONDITION);
			
			ps.setString(1, pack_name);
			ps.setString(2, cc);
			
			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === getExpPackageHYListByName : ", ps);
			
			while (rs.next()) {
				list.add(setZyzdExpPackageHY(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<LhyUserMajorFollow> getLhyOrderFollow(String sfCode, String order_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyUserMajorFollow> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM lhy_"+sfCode+"_form_user_major x WHERE x.order_id = ? order by create_tm desc";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, order_id);
			rs = ps.executeQuery();
			LhyUserMajorFollow bean = null;
			while (rs.next()) {
				bean = new LhyUserMajorFollow();
				bean.setId(rs.getInt("id"));
				bean.setOrder_id(rs.getString("order_id"));
				bean.setZymc_org(rs.getString("zymc_org"));
				bean.setZyml(rs.getString("zyml"));
				bean.setZyl(rs.getString("zyl"));
				bean.setCc(rs.getString("cc"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<LhyUserYxFollow> getLhyOrderFollowYx(String sfCode, String order_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyUserYxFollow> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM lhy_"+sfCode+"_form_user_follow x WHERE x.order_id = ? order by create_tm desc";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, order_id);
			rs = ps.executeQuery();
			LhyUserYxFollow bean = null;
			while (rs.next()) {
				bean = new LhyUserYxFollow();
				bean.setId(rs.getInt("id"));
				bean.setOrder_id(rs.getString("order_id"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<LhyUserYxZyzFollow> getLhyOrderFollowYxZyz(String sfCode, String order_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyUserYxZyzFollow> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM lhy_"+sfCode+"_form_zyz_follow x WHERE x.order_id = ? order by create_tm desc";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, order_id);
			rs = ps.executeQuery();
			LhyUserYxZyzFollow bean = null;
			while (rs.next()) {
				bean = new LhyUserYxZyzFollow();
				bean.setId(rs.getInt("id"));
				bean.setOrder_id(rs.getString("order_id"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setZyz(rs.getString("zyz"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public LhyUserYxFollow getLhyOrderYxFollowById(String sfCode, int id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM lhy_"+sfCode+"_form_user_follow x WHERE x.id = ? ";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setInt(1, id);
			rs = ps.executeQuery();
			LhyUserYxFollow bean = null;
			while (rs.next()) {
				bean = new LhyUserYxFollow();
				bean.setId(rs.getInt("id"));
				bean.setOrder_id(rs.getString("order_id"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				return bean;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public LhyUserMajorFollow getLhyOrderFollowById(String sfCode, int id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM lhy_"+sfCode+"_form_user_major x WHERE x.id = ? ";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setInt(1, id);
			rs = ps.executeQuery();
			LhyUserMajorFollow bean = null;
			while (rs.next()) {
				bean = new LhyUserMajorFollow();
				bean.setId(rs.getInt("id"));
				bean.setOrder_id(rs.getString("order_id"));
				bean.setZymc_org(rs.getString("zymc_org"));
				bean.setZyml(rs.getString("zyml"));
				bean.setZyl(rs.getString("zyl"));
				bean.setCc(rs.getString("cc"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				return bean;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public List<LhyUserMajorFollow> getLhyOrderFollowByName(String sfCode, String name) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyUserMajorFollow> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM lhy_"+sfCode+"_form_user_major x WHERE x.zymc_org = ? ";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, name);
			rs = ps.executeQuery();
			LhyUserMajorFollow bean = null;
			while (rs.next()) {
				bean = new LhyUserMajorFollow();
				bean.setId(rs.getInt("id"));
				bean.setOrder_id(rs.getString("order_id"));
				bean.setZymc_org(rs.getString("zymc_org"));
				bean.setZyml(rs.getString("zyml"));
				bean.setZyl(rs.getString("zyl"));
				bean.setCc(rs.getString("cc"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public boolean addFollow(String sfCode, String order_id, String zymc, String cc, String zyml, String zyl) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into lhy_"+sfCode+"_form_user_major(order_id, zymc_org, cc, zyml, zyl, create_tm) values(?,?,?,?,?,NOW())";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, order_id);
			ps.setString(2, zymc);
			ps.setString(3, cc);
			ps.setString(4, zyml);
			ps.setString(5, zyl);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			//ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		} 
		return false;
	}
	
	public boolean addFollowYx(String sfCode, String order_id, String yxmc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into lhy_"+sfCode+"_form_user_follow(order_id, yxmc_org, create_tm) values(?,?,NOW())";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, order_id);
			ps.setString(2, yxmc);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			//ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		} 
		return false;
	}
	
	public boolean addFollowYxzyz(String sfCode, String order_id, String yxmc, String yxmc_org, String zyz) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into lhy_"+sfCode+"_form_zyz_follow(order_id, yxmc, yxmc_org, zyz, create_tm) values(?,?,?,?,NOW())";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, order_id);
			ps.setString(2, yxmc);
			ps.setString(3, yxmc_org);
			ps.setString(4, zyz);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			//ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		} 
		return false;
	}
	
	public boolean deleteZyzFollow(String sfCode, String order_id, int id) {
		Connection conn = null; 
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from lhy_"+sfCode+"_form_zyz_follow where order_id = ? and id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, id);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean deleteZyzFollow(String sfCode, String order_id, String yxmc, String zyz) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from lhy_"+sfCode+"_form_zyz_follow where order_id = ? and yxmc = ? and zyz = ?";
			Tools.println(SQL); 
			ps = conn.prepareStatement(SQL);
			ps.setString(1, order_id);
			ps.setString(2, yxmc);
			ps.setString(3, zyz);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	
	public boolean deleteFollow(String sfCode, int id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from lhy_"+sfCode+"_form_user_major where id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, id);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean deleteFollow(String sfCode, String zymc, String cc, String order_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from lhy_"+sfCode+"_form_user_major where zymc_org = ? and cc = ? and order_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, zymc);
			ps.setString(2, cc);
			ps.setString(3, order_id);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	
	public boolean deleteYxFollow(String sfCode, int id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from lhy_"+sfCode+"_form_user_follow where id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, id);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean insertBlockCount(String cid, String action, String spec) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into zdks_block(FROM_ACTION, SPEC_VALUE, C_ID, CREATE_DT) values(?,?,?,NOW())";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, action);
			ps.setString(2, spec);
			ps.setString(3, cid);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public int checkBlockCount(String cid, String action) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		YGCardBean bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT count(*) as ct FROM zdks_block WHERE C_ID = ? and FROM_ACTION = ? and DATE_FORMAT(CREATE_DT, '%Y%m%d%H%i') = DATE_FORMAT(now(), '%Y%m%d%H%i')";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.setString(2, action);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("ct");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return -1;
	}
	
	public boolean deleteSysVisitLog(String cid) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "DELETE FROM sys_trans_visit WHERE c_id = ? AND VISIT_TM < (unix_timestamp(NOW()) - 60 * 60)";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}

	public int getLatest30SecsVisitLogCount(String cid) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT COUNT(1) as ct FROM sys_trans_visit WHERE c_id = ? AND VISIT_TM > (unix_timestamp(NOW()) - 10)";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			rs = ps.executeQuery();
			if (rs.next())
				return rs.getInt(1);
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	private static LhyCard setLhyBaseCard(ResultSet rs) throws SQLException {
        LhyCard bean = new LhyCard();
        bean.setC_id(rs.getString("c_id"));
        bean.setC_sub_prefix(rs.getString("c_sub_prefix"));
        bean.setP_c_id(rs.getString("p_c_id"));
        bean.setC_passwd(rs.getString("c_passwd"));
        bean.setC_name(rs.getString("c_name"));
        bean.setC_remark(rs.getString("c_remark"));
        bean.setC_phone(rs.getString("c_phone"));
        bean.setC_prov(rs.getString("c_prov"));
        bean.setC_allow_prov(rs.getString("c_allow_prov"));
        bean.setC_function(rs.getString("c_function"));
        bean.setC_status(rs.getInt("c_status"));
        bean.setC_level(rs.getInt("c_level"));
        bean.setC_type(rs.getInt("c_type"));
        bean.setOrder_cnt(rs.getInt("order_cnt"));
        bean.setCard_cnt(rs.getInt("card_cnt"));
        bean.setSub_acct_cnt(rs.getInt("sub_acct_cnt"));
        bean.setZd_query_cnt(rs.getInt("zd_query_cnt"));
        bean.setSys_ind(rs.getInt("sys_ind"));
        bean.setCreate_tm(rs.getTimestamp("create_tm"));
        bean.setActive_tm(rs.getTimestamp("active_tm"));
        bean.setLast_login_tm(rs.getTimestamp("last_login_tm"));
        bean.setExpire_tm(rs.getTimestamp("expire_tm"));
        bean.setSaas_id(rs.getString("saas_id"));
        bean.setC_batch_id(rs.getString("c_batch_id"));
        bean.setC_auto_ind(rs.getString("c_auto_ind"));
        return bean;
    }
	
	public List<LhyCard> getLhyCardByParent(String p_c_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyCard> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM lhy_card x WHERE x.p_c_id = ? and c_status = 1 order by create_tm desc, last_login_tm desc, c_id desc";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, p_c_id);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setLhyBaseCard(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<LhyCard> getParentLhyCardByBatchId(String saas_id, String batch_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyCard> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM lhy_card x WHERE x.saas_id = ? and c_batch_id = ? and c_level = 2";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, saas_id);
			ps.setString(2, batch_id);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setLhyBaseCard(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public int getLhySubCardCntByParent(String p_c_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyCard> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT COUNT(*) as cnt FROM lhy_card x WHERE x.p_c_id = ? ";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, p_c_id);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("cnt");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public ResultVO searchCompanyLhyCardBySaasId(String saas_id, String name, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		ResultVO resultVO = new ResultVO();
		List<LhyCard> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL_CONDITION = "FROM lhy_card x WHERE x.saas_id = ? and c_status = 1";
			String SELECT_SQL = "SELECT * "+SQL_CONDITION+" order by create_tm desc, last_login_tm desc, c_id desc LIMIT ?,? ";
			String SELECT_CNT = "SELECT count(*) AS cnt "+SQL_CONDITION;
			ps = conn.prepareStatement(SELECT_SQL);
			ps.setString(1, saas_id);
			ps.setInt(2, (pageNumber - 1) * PAGE_ROW_CNT);
			ps.setInt(3, PAGE_ROW_CNT);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setLhyBaseCard(rs));
			}
			
			ps = conn.prepareStatement(SELECT_CNT);
			ps.setString(1, saas_id);
			rs = ps.executeQuery();
			while (rs.next()) {
				resultVO.setRecordCnt(rs.getInt("cnt"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		
		resultVO.setResult(list);
		resultVO.setCurrentPage(pageNumber);
		return resultVO;
	}
	
	public int getCardCntByIdForStatistics(String c_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyCard> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT sum(card_cnt) as cnt FROM lhy_card x WHERE x.c_id = ?";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, c_id);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("cnt");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public int getActiveCntByIdForStatistics(String c_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyCard> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT sum(1) as cnt FROM lhy_related_card x, base_card by WHERE x.base_c_id = y.c_id and y.c_active is not null and x.lhy_c_id = ?";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, c_id);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("cnt");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public int getActiveCntByPIdForStatistics(String p_c_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyCard> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT sum(1) as cnt FROM lhy_related_card x, base_card by WHERE x.base_c_id = y.c_id and y.c_active is not null and x.lhy_c_id in (SElect c_id FROM lhy_card where p_c_id = ?)";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, p_c_id);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("cnt");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public int getInputScoreCntByIdForStatistics(String c_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyCard> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT sum(1) as cnt FROM lhy_related_card x, base_card by WHERE x.base_c_id = y.c_id and y.c_score > 0 and x.lhy_c_id = ?";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, c_id);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("cnt");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public int getInputScoreCntByPIdForStatistics(String p_c_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyCard> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT sum(1) as cnt FROM lhy_related_card x, base_card by WHERE x.base_c_id = y.c_id and y.c_score > 0 and x.lhy_c_id in (SElect c_id FROM lhy_card where p_c_id = ?)";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, p_c_id);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("cnt");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	
	public int getCardCntByPIdForStatistics(String p_c_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyCard> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT sum(card_cnt) as cnt FROM lhy_card x WHERE x.c_id in (SElect c_id FROM lhy_card where p_c_id = ?)";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, p_c_id);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("cnt");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public int getMaxLhyCardSubAccountByParent(String p_c_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyCard> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT max(CAST(right(c_id, 3) AS SIGNED)) as cnt FROM lhy_card x WHERE x.p_c_id = ?";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, p_c_id);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("cnt");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public LhyCard getLhyBaseCard(String c_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM lhy_card x WHERE x.c_id = ? and c_status = 1";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, c_id);
			rs = ps.executeQuery();
			while (rs.next()) {
				return setLhyBaseCard(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public LhyCard autoGenerateCard(String c_auto_ind, String c_batch_id, String saas_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String UPDATE_CONDITION = "UPDATE lhy_card x SET x.c_batch_id = ?, x.saas_id = ? WHERE x.c_auto_ind = ? AND x.c_batch_id is null AND x.saas_id IS NULL ORDER BY rand() LIMIT 1 ";
			ps = conn.prepareStatement(UPDATE_CONDITION);
			ps.setString(1, c_batch_id);
			ps.setString(2, saas_id);
			ps.setString(3, c_auto_ind);
			ps.executeUpdate();
			SQLLogUtils.printSQL(ps);
			String SELECT_CONDITION = "select * from lhy_card x where x.c_batch_id = ?"; 
			
			ps = null;
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, c_batch_id);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			while(rs.next()) {
				return setLhyBaseCard(rs);
			}
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public List<LhyCard> getLhyBaseSysCardByPhone(String c_phone) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyCard> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM lhy_card x WHERE c_phone = ? and sys_ind = 2 and c_status = 1";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, c_phone);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setLhyBaseCard(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public boolean arrayLhyCardForSysRegist(String c_phone) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE lhy_card x SET x.c_phone = ? WHERE c_phone is null and sys_ind = 2 and c_status = 1 ORDER BY rand () LIMIT 1" ;
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, c_phone);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public LhyOrder getLhyPaidOrder(String order_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		LhyOrder order = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM lhy_order x WHERE x.order_id = ? and x.status > 20";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, order_id);
			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === getLhyPaidOrder: ", ps);
			
			while (rs.next()) {
				return setLhyOrderAll(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public LhyOrder getLhyPaidOrderById(int id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		LhyOrder order = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM lhy_order x WHERE x.id = ? and x.status > 20";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setInt(1, id);
			rs = ps.executeQuery();
			while (rs.next()) {
				return setLhyOrderAll(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public List<LhyOrder> getLhyPaidOrderByFamilyAcct(String acct_id, String acct_pass) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyOrder> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM lhy_order x WHERE x.student_acc_id = ? and student_acc_pas = ? and x.status > 20";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, acct_id);
			ps.setString(2, acct_pass);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setLhyOrderAll(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public boolean updateLhyOrderScore(String order_id, int cj, int wc, String xk) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		LhyOrder order = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "update lhy_order x set x.stu_score_cj = ? ,x.stu_score_wc = ? , x.stu_score_xk = ? WHERE x.order_id = ?";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setInt(1, cj);
			ps.setInt(2, wc);
			ps.setString(3, xk);
			ps.setString(4, order_id);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean updateLhyOrderName(String order_id, String new_name) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		LhyOrder order = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "update lhy_order x set x.stu_info_name = ? WHERE x.order_id = ?";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, new_name);
			ps.setString(2, order_id);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean updateLhyOrderAcct(String order_id, String new_id, String new_pass) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		LhyOrder order = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "update lhy_order x set x.student_acc_id = ?, x.student_acc_pas = ?  WHERE x.order_id = ?";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, new_id);
			ps.setString(2, new_pass);
			ps.setString(3, order_id);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean updateLhyFormMakerMainName(String sfTable, String batch_id, String new_name) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		LhyOrder order = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "update lhy_"+sfTable+"_form_maker_main x set x.form_name = ? WHERE x.batch_id = ?";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, new_name);
			ps.setString(2, batch_id);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean updateLhyOrderDh(String order_id, String new_dh) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		LhyOrder order = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "update lhy_order x set x.stu_info_dh = ? WHERE x.order_id = ?";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, new_dh);
			ps.setString(2, order_id);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}

	
	public LhyFormMain getFormMainByBatchId(String sfCode, String batchId) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker_main x WHERE batch_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batchId);
			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === getFormMainByBatchId(): ", ps);
			
			while (rs.next()) {
				return setLhyFormMain(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public ZyzdBaseMajor getZymcById(int id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "select * from zyzd_base_major x WHERE x.id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, id);
			rs = ps.executeQuery();
			ZyzdBaseMajor bean = null;
			while (rs.next()) {
				bean = new ZyzdBaseMajor();
				bean.setM_cando(rs.getString("m_cando"));
				bean.setId(rs.getInt("id"));
				bean.setM_catg_one(rs.getString("m_catg_one"));
				bean.setM_catg_two(rs.getString("m_catg_two"));
				bean.setM_cc(rs.getString("m_cc"));
				bean.setM_jy_area(rs.getString("m_jy_area"));
				bean.setM_jy_gw(rs.getString("m_jy_gw"));
				bean.setM_jy_hy(rs.getString("m_jy_hy"));
				bean.setM_jy_to(rs.getString("m_jy_to"));
				bean.setM_learn(rs.getString("m_learn"));
				bean.setM_ratio_jy(rs.getString("m_ratio_jy"));
				bean.setM_ratio_xb(rs.getString("m_ratio_xb"));
				bean.setM_sentense(rs.getString("m_sentense"));
				bean.setM_what(rs.getString("m_what"));
				bean.setM_xk(rs.getString("m_xk"));
				bean.setM_year_cnt(rs.getString("m_year_cnt"));
				bean.setM_zydm(rs.getString("m_zydm"));
				bean.setM_zymc(rs.getString("m_zymc"));
				return bean;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	private LhyFormMain setLhyFormMain(ResultSet rs) throws SQLException{
		LhyFormMain bean = new LhyFormMain(); 
		bean.setId(rs.getInt("id"));
		bean.setBatch_id(rs.getString("batch_id"));
		bean.setOrder_id(rs.getString("order_id"));
		bean.setF_no(rs.getString("f_no"));
		bean.setScore_cj(rs.getInt("score_cj"));
		bean.setScore_wc(rs.getInt("score_wc"));
		bean.setScore_xk(rs.getString("score_xk"));
		bean.setChecker_id(rs.getString("last_checker_id"));
		bean.setChecker_name(rs.getString("last_checker_name"));
		bean.setChecker_remark(rs.getString("last_checker_remark"));
		bean.setSelected_zymc(rs.getString("selected_zymc"));
		bean.setCreate_tm(rs.getTimestamp("create_tm"));
		bean.setLast_update_tm(rs.getTimestamp("last_update_tm"));
		bean.setJoined_checker_cnt(rs.getInt("joined_checker_cnt"));
		bean.setNf(rs.getInt("nf"));
		bean.setStatus(rs.getInt("status"));
		bean.setF_type(rs.getInt("f_type"));
		bean.setPc(rs.getString("pc"));
		bean.setPc_code(rs.getString("pc_code"));
		bean.setForm_name(rs.getString("form_name"));
		bean.setToCheckerRemark(rs.getString("to_checker_remark"));
		return bean;
	} 
	
	public List<LhyForm> getLhyForm(String sfCode, String batch_id_org) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyForm> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker x WHERE batch_id_org = ? ORDER BY seq_no_yx, seq_no_zy";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setLhyFormAll(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	private static LhyForm setLhyFormAll(ResultSet rs) throws Exception {
		LhyForm bean = new LhyForm();
		bean.setBatch_id(rs.getString("batch_id"));
		bean.setId(rs.getInt("id"));
		bean.setBatch_id_org(rs.getString("batch_id_org"));
		bean.setSeq_no_yx(rs.getInt("seq_no_yx"));
		bean.setSeq_no_zy(rs.getInt("seq_no_zy"));
		bean.setYxbz(rs.getString("yxbz"));
		bean.setYxdm(rs.getString("yxdm"));
		bean.setYxmc(rs.getString("yxmc"));
		bean.setZybz(rs.getString("zybz"));
		bean.setZydm(rs.getString("zydm"));
		bean.setZymc(rs.getString("zymc"));
		bean.setZymc_org(rs.getString("zymc_org"));
		bean.setYxmc_org(rs.getString("yxmc_org"));
		bean.setZyz(rs.getString("zyz"));
		
		bean.setZdf_a(rs.getInt("zdf_a"));
		bean.setZdf_b(rs.getInt("zdf_b"));
		bean.setZdf_c(rs.getInt("zdf_c"));
		bean.setZdfwc_a(rs.getInt("zdfwc_a"));
		bean.setZdfwc_b(rs.getInt("zdfwc_b"));
		bean.setZdfwc_c(rs.getInt("zdfwc_c"));

		bean.setPjf_a(rs.getInt("pjf_a"));
		bean.setPjf_b(rs.getInt("pjf_b"));
		bean.setPjf_c(rs.getInt("pjf_c"));
		bean.setPjfwc_a(rs.getInt("pjfwc_a"));
		bean.setPjfwc_b(rs.getInt("pjfwc_b"));
		bean.setPjfwc_c(rs.getInt("pjfwc_c"));
		return bean;
	}
	
	public List<LhyFormMain> getMakerFormMain(int nf, String sf, String order_id, int fType) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyFormMain> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM lhy_"+sf+"_form_maker_main x WHERE order_id = ? and nf = ? and f_type = ? and status > 0 ORDER BY create_tm desc";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, order_id);
			ps.setInt(2, nf);
			ps.setInt(3, fType);
			SQLLogUtils.printSQL(ps);
			rs = ps.executeQuery();
			LhyFormMain bean = null;
			while (rs.next()) {
				bean = setLhyFormMain(rs);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<LhyFormMain> getAllLhyFormMaker(int nf, String sf) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyFormMain> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM lhy_"+sf+"_form_maker_main x WHERE nf = ? ORDER BY create_tm desc";
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, nf);
			SQLLogUtils.printSQL(ps);
			rs = ps.executeQuery();
			LhyFormMain bean = null;
			while (rs.next()) {
				bean = setLhyFormMain(rs);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<LhyFormMain> getMakerFormMainWithPc(int nf, String sf, String order_id, int fType, String pc, String pc_code) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyFormMain> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM lhy_"+sf+"_form_maker_main x WHERE order_id = ? and nf = ? and f_type = ? and pc = ? and pc_code = ? and status > 0 ORDER BY create_tm desc";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, order_id);
			ps.setInt(2, nf);
			ps.setInt(3, fType);
			ps.setString(4, pc);
			ps.setString(5, pc_code);
			SQLLogUtils.printSQL(ps);
			rs = ps.executeQuery();
			LhyFormMain bean = null;
			while (rs.next()) {
				bean = setLhyFormMain(rs);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public List<LhyForm> getMakerForm(String sfCode, String batch_id_org) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyForm> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker x WHERE batch_id_org = ? ORDER BY seq_no_yx, seq_no_zy";

			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			rs = ps.executeQuery();
			ZyzdForm bean = null;
			
			SQLLogUtils.printSQL(" === getMakerFormMainByBatchId() : ", ps);
			
			while (rs.next()) {
				list.add(setMakerFormAll(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<LhyForm> getMakerForm(String sfCode, String batch_id_org, String yxdm, String yxmc, String zyz) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyForm> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker x WHERE batch_id_org = ? AND yxdm = ? and yxmc = ? and zyz = ? ORDER BY seq_no_yx, seq_no_zy";

			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			ps.setString(2, yxdm);
			ps.setString(3, yxmc);
			ps.setString(4, zyz);
			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === getMakerFormMainByBatchId() : ", ps);
			
			while (rs.next()) {
				list.add(setMakerFormAll(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<LhyForm> getMakerFormWithJHInfo(int nf, String sfCode, String batch_id_org, String pc, String pc_code, String xk_code) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = nf - 1;
		List<LhyForm> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.batch_id, x.id, x.batch_id_org, x.seq_no_yx, x.seq_no_zy, x.yxdm, jh.yxmc, x.zyz, x.zydm, jh.zymc, "
					+ "jh.zybz, jh.zymc_org, jh.yxmc_org, jh.jhs, jh.fee, jh.xz, jh.znzys,  "
					+ "jh.qsf_a, jh.qsf_b, jh.qsf_c, jh.qsf, "
					+ "jh.zdf_"+(dataYear)+" AS zdf_a, jh.zdf_"+(dataYear - 1)+" AS zdf_b, jh.zdf_"+(dataYear-2)+" AS zdf_c, "
					+ "jh.zdfwc_"+(dataYear)+" AS zdfwc_a, jh.zdfwc_"+(dataYear - 1)+" AS zdfwc_b, jh.zdfwc_"+(dataYear-2)+" AS zdfwc_c, "
					+ "jh.zgf_"+(dataYear)+" AS zgf_a, jh.zgf_"+(dataYear - 1)+" AS zgf_b, jh.zgf_"+(dataYear-2)+" AS zgf_c, "
					+ "jh.zgfwc_"+(dataYear)+" AS zgfwc_a, jh.zgfwc_"+(dataYear - 1)+" AS zgfwc_b, jh.zgfwc_"+(dataYear-2)+" AS zgfwc_c, "
					+ "jh.pjf_"+(dataYear)+" AS pjf_a, jh.pjf_"+(dataYear-1)+" AS pjf_b, jh.pjf_"+(dataYear-2)+" AS pjf_c, "
					+ "jh.pjfwc_"+(dataYear)+" AS pjfwc_a, jh.pjfwc_"+(dataYear-1)+" AS pjfwc_b, jh.pjfwc_"+(dataYear-2)+" AS pjfwc_c, "
					+ "jh.lqrs_"+(dataYear)+" AS lqrs_a, jh.lqrs_"+(dataYear-1)+" AS lqrs_b, jh.lqrs_"+(dataYear-2)+" AS lqrs_c, "
					+ "jh.jhs_"+(dataYear)+" AS jhs_a, jh.jhs_"+(dataYear-1)+" AS jhs_b, jh.jhs_"+(dataYear-2)+" AS jhs_c "
					+ "FROM lhy_"+sfCode+"_form_maker x LEFT JOIN "+sfCode+"_jh_"+nf+" jh "
					+ "ON x.yxdm = jh.yxdm AND x.zydm = jh.zydm AND x.zyz = jh.zyz AND jh.pc = ? AND jh.pc_code = ? AND jh.xk_code like ? "
					+ "WHERE x.batch_id_org = ? ORDER BY x.seq_no_yx, x.seq_no_zy";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%" + xk_code + "%");
			ps.setString(4, batch_id_org); 
			rs = ps.executeQuery();
			LhyForm bean = null;
			SQLLogUtils.printSQL(" === getMakerFormMainByBatchId() : ", ps);
			while (rs.next()) {
				bean = setMakerFormAll(rs);
				bean.setExt_jh_qsf_a(rs.getInt("qsf_a"));
				bean.setExt_jh_qsf_b(rs.getInt("qsf_b"));
				bean.setExt_jh_qsf_c(rs.getInt("qsf_c"));
				bean.setExt_jh_qsf(rs.getInt("qsf"));
				bean.setLqrs_a(rs.getInt("lqrs_a"));
				bean.setLqrs_b(rs.getInt("lqrs_b"));
				bean.setLqrs_c(rs.getInt("lqrs_c"));
				bean.setExt_jh_znzys(rs.getInt("znzys"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<LhyForm> getMakerFormWithJHInfo96(int nf, String sfCode, String batch_id_org, String pc, String pc_code, String xk_code) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = nf - 1;
		List<LhyForm> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.batch_id, x.id, x.batch_id_org, x.seq_no_yx, x.seq_no_zy, x.yxdm, jh.yxmc, x.zyz, x.zydm, jh.zymc, "
					+ "jh.zybz, jh.zymc_org, jh.yxmc_org, jh.jhs, jh.fee, jh.xz, jh.znzys,  "
					+ "jh.qsf_a, jh.qsf_b, jh.qsf_c, jh.qsf, "
					+ "jh.zdf_"+(dataYear)+" AS zdf_a, jh.zdf_"+(dataYear - 1)+" AS zdf_b, jh.zdf_"+(dataYear-2)+" AS zdf_c, "
					+ "jh.zdfwc_"+(dataYear)+" AS zdfwc_a, jh.zdfwc_"+(dataYear - 1)+" AS zdfwc_b, jh.zdfwc_"+(dataYear-2)+" AS zdfwc_c, "
					+ "jh.zgf_"+(dataYear)+" AS zgf_a, jh.zgf_"+(dataYear - 1)+" AS zgf_b, jh.zgf_"+(dataYear-2)+" AS zgf_c, "
					+ "jh.zgfwc_"+(dataYear)+" AS zgfwc_a, jh.zgfwc_"+(dataYear - 1)+" AS zgfwc_b, jh.zgfwc_"+(dataYear-2)+" AS zgfwc_c, "
					+ "jh.pjf_"+(dataYear)+" AS pjf_a, jh.pjf_"+(dataYear-1)+" AS pjf_b, jh.pjf_"+(dataYear-2)+" AS pjf_c, "
					+ "jh.pjfwc_"+(dataYear)+" AS pjfwc_a, jh.pjfwc_"+(dataYear-1)+" AS pjfwc_b, jh.pjfwc_"+(dataYear-2)+" AS pjfwc_c, "
					+ "jh.lqrs_"+(dataYear)+" AS lqrs_a, jh.lqrs_"+(dataYear-1)+" AS lqrs_b, jh.lqrs_"+(dataYear-2)+" AS lqrs_c, "
					+ "jh.jhs_"+(dataYear)+" AS jhs_a, jh.jhs_"+(dataYear-1)+" AS jhs_b, jh.jhs_"+(dataYear-2)+" AS jhs_c "
					+ "FROM lhy_"+sfCode+"_form_maker x LEFT JOIN "+sfCode+"_jh_"+nf+" jh "
					+ "ON x.yxdm = jh.yxdm AND x.zydm = jh.zydm AND jh.pc = ? AND jh.pc_code = ? AND jh.xk_code like ? "
					+ "WHERE x.batch_id_org = ? ORDER BY x.seq_no_yx, x.seq_no_zy";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%" + xk_code + "%");
			ps.setString(4, batch_id_org); 
			rs = ps.executeQuery();
			LhyForm bean = null;
			SQLLogUtils.printSQL(" === getMakerFormMainByBatchId() : ", ps);
			while (rs.next()) {
				bean = setMakerFormAll(rs);
				bean.setExt_jh_qsf_a(rs.getInt("qsf_a"));
				bean.setExt_jh_qsf_b(rs.getInt("qsf_b"));
				bean.setExt_jh_qsf_c(rs.getInt("qsf_c"));
				bean.setExt_jh_qsf(rs.getInt("qsf"));
				bean.setLqrs_a(rs.getInt("lqrs_a"));
				bean.setLqrs_b(rs.getInt("lqrs_b"));
				bean.setLqrs_c(rs.getInt("lqrs_c"));
				bean.setExt_jh_znzys(rs.getInt("znzys"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<LhyForm> getMakerForm(String sfCode, String batch_id_org, int seq_yx_no) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyForm> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker x WHERE batch_id_org = ? and seq_no_yx = ? ORDER BY seq_no_yx, seq_no_zy";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			ps.setInt(2, seq_yx_no);
			rs = ps.executeQuery();
			ZyzdForm bean = null;
			while (rs.next()) {
				list.add(setMakerFormAll(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	private static LhyForm setMakerFormAll(ResultSet rs) throws Exception {
		LhyForm bean = new LhyForm();
		bean.setBatch_id(rs.getString("batch_id"));
		bean.setId(rs.getInt("id"));
		bean.setBatch_id_org(rs.getString("batch_id_org"));
		bean.setSeq_no_yx(rs.getInt("seq_no_yx"));
		bean.setSeq_no_zy(rs.getInt("seq_no_zy"));
		bean.setYxdm(rs.getString("yxdm"));
		bean.setYxmc(rs.getString("yxmc"));
		bean.setZybz(rs.getString("zybz"));
		bean.setZydm(rs.getString("zydm"));
		bean.setZymc(rs.getString("zymc"));
		bean.setZymc_org(rs.getString("zymc_org"));
		bean.setYxmc_org(rs.getString("yxmc_org"));
		bean.setZyz(rs.getString("zyz"));
		
		bean.setZdf_a(rs.getInt("zdf_a"));
		bean.setZdf_b(rs.getInt("zdf_b"));
		bean.setZdf_c(rs.getInt("zdf_c"));
		bean.setZdfwc_a(rs.getInt("zdfwc_a"));
		bean.setZdfwc_b(rs.getInt("zdfwc_b"));
		bean.setZdfwc_c(rs.getInt("zdfwc_c"));

		bean.setPjf_a(rs.getInt("pjf_a"));
		bean.setPjf_b(rs.getInt("pjf_b"));
		bean.setPjf_c(rs.getInt("pjf_c"));
		bean.setPjfwc_a(rs.getInt("pjfwc_a"));
		bean.setPjfwc_b(rs.getInt("pjfwc_b"));
		bean.setPjfwc_c(rs.getInt("pjfwc_c"));
		bean.setJhs(rs.getInt("jhs"));
		bean.setJhs_a(rs.getInt("jhs_a"));
		bean.setJhs_b(rs.getInt("jhs_b"));
		bean.setJhs_c(rs.getInt("jhs_c"));
		bean.setFee(rs.getString("fee"));
		
		return bean;
	}
	
	public List<LhyFormMain> getMakerFormMain(int nf, String sf, String order_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyFormMain> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM lhy_"+sf+"_form_maker_main x WHERE order_id = ? and nf = ? and status > 0 ORDER BY create_tm desc";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, order_id);
			ps.setInt(2, nf);
			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === getMakerFormMain() : ", ps);
			
			LhyFormMain bean = null;
			while (rs.next()) {
				bean = setLhyFormMain(rs);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<String> getPcTypeByPc(int nf, String sf, String pc, String pc_code) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<String> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT distinct pc_type FROM "+sf+"_jh_"+nf+" x WHERE pc = ? and pc_code = ? and pc_type is not null ";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === getPcTypeByPc() : ", ps);
			
			while (rs.next()) {
				list.add(rs.getString("pc_type")); 
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public LhyFormMain getLatestMakerFormMain(int nf, String sfCode, String order_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker_main x WHERE order_id = ? and nf = ? ORDER BY create_tm desc limit 1";
			ps = conn.prepareStatement(SQL);
			ps.setString(1,  order_id);
			ps.setInt(2,  nf);
			SQLLogUtils.printSQL(ps);
			rs = ps.executeQuery();
			while (rs.next()) {
				return setLhyFormMain(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public LhyFormMain getLatestMakerFormMain(int nf, String sfCode, String order_id, int fType) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker_main x WHERE order_id = ? and nf = ? and f_type = ? ORDER BY create_tm desc limit 1";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1,  order_id);
			ps.setInt(2,  nf);
			ps.setInt(3,  fType);
			rs = ps.executeQuery();
			while (rs.next()) {
				return setLhyFormMain(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public LhyFormMain getMakerFormMainByFno(String sfCode, String fno) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker_main x WHERE f_no = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, fno);
			rs = ps.executeQuery();
			LhyFormMain bean = null;
			while (rs.next()) {
				return setLhyFormMain(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public LhyFormMain getLhyFormMainByFno(String sfCode, String fno) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker_main x WHERE f_no = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, fno);
			rs = ps.executeQuery();
			LhyFormMain bean = null;
			while (rs.next()) {
				return setLhyFormMain(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public void adjustLhyFormYxNoWithBatchId(String sfCode, List<LhyForm> updateList) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "update lhy_"+sfCode+"_form_maker set seq_no_yx = ? where id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			for(LhyForm form : updateList) {
				ps.setInt(1, form.getSeq_no_yx());
				ps.setInt(2, form.getId());
				ps.addBatch();
			}
			ps.executeBatch();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void refreshNewJHForForm(int nf, String sfCode, String batch_id_org, String pc, String pc_code, String xk_code) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE lhy_"+sfCode+"_form_maker x "
					+ "LEFT JOIN "+sfCode+"_jh_"+nf+" y "
					+ "ON x.yxmc = y.yxmc AND x.zymc = y.zymc "
					+ "SET "
					+ "    x.zydm = y.zydm, "
					+ "    x.yxdm = y.yxdm, "
					+ "    x.zdf_a = y.zdf_2024,"
					+ "    x.zdf_b = y.zdf_2023,"
					+ "    x.zdf_c = y.zdf_2022,"
					+ "    x.zdfwc_a = y.zdfwc_2024,"
					+ "    x.zdfwc_b = y.zdfwc_2023,"
					+ "    x.zdfwc_c = y.zdfwc_2022,"
					+ "    x.pjf_a = y.pjf_2024,"
					+ "    x.pjf_b = y.pjf_2023,"
					+ "    x.pjf_c = y.pjf_2022,"
					+ "    x.pjfwc_a = y.pjfwc_2024,"
					+ "    x.pjfwc_b = y.pjfwc_2023,"
					+ "    x.pjfwc_c = y.pjfwc_2022,"
					+ "    x.jhs_a = y.jhs_2024,"
					+ "    x.jhs_b = y.jhs_2023,"
					+ "    x.jhs_c = y.jhs_2022,"
					+ "    x.zgf_a = y.zgf_2024,"
					+ "    x.zgf_b = y.zgf_2023,"
					+ "    x.zgf_c = y.zgf_2022,"
					+ "    x.zgfwc_a = y.zgfwc_2024,"
					+ "    x.zgfwc_b = y.zgfwc_2023,"
					+ "    x.zgfwc_c = y.zgfwc_2022,"
					+ "    x.jhs = y.jhs,"
					+ "    x.fee = y.fee,"
					+ "    x.yxmc_org = y.yxmc_org, "
					+ "    x.yxbz = y.yxbz, "
					+ "    x.zyz = y.zyz, "
					+ "    x.zybz = y.zybz, "
					+ "    x.zymc_org = y.zymc_org "
					+ " WHERE "
					+ "    y.pc = ? "
					+ "    AND y.pc_code = ? "
					+ "    AND y.xk_code LIKE ? "
					+ "    AND x.batch_id_org = ?";
			
			ps = conn.prepareStatement(SQL);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xk_code+"%");
			ps.setString(4, batch_id_org);
			SQLLogUtils.printSQL(ps);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void adjustLhyFormYxAndZyNoWithBatchId(String sfCode, List<LhyForm> updateList) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE lhy_"+sfCode+"_form_maker SET seq_no_yx = ?, seq_no_zy = ? WHERE id = ?";
			ps = conn.prepareStatement(SQL);
			for(LhyForm form : updateList) {
				ps.setInt(1, form.getSeq_no_yx());
				ps.setInt(2, form.getSeq_no_zy());
				ps.setInt(3, form.getId()); 
				ps.addBatch();
			}
			SQLLogUtils.printSQL(ps);
			ps.executeBatch();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void batchInsertLhySchoolStatisticsForm(List<LhySchoolStatisticsForm> forms) {
		Connection conn = null;
		PreparedStatement pstmt = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "INSERT INTO lhy_school_statistics_form (school_lhy_c_id, class_lhy_c_id, class_name, c_id, c_student_name, form_score_cj, form_score_wc, form_score_xk, pc_code, pc, lq_is_985, lq_is_211, lq_is_syl, lq_is_hzbx, create_tm, record_create_tm, lq_yxsf, lq_yxcs, lq_yxdm, lq_yxmc, lq_yxmc_org, lq_zyz, lq_zydm, lq_zymc, lq_zymc_org, is_adjust, waste_score_cnt) " +
			        "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
			pstmt = conn.prepareStatement(SQL);
			for(LhySchoolStatisticsForm form : forms) {
				int index = 1;
				System.out.println("++++++++++++++++++++++++++++++++++++++++++++++++"+form.getClassName());
				System.out.println("++++++++++++++++++++++++++++++++++++++++++++++++"+form.getCStudentName());
				pstmt.setString(index++, form.getSchoolLhyCId());
                pstmt.setString(index++, form.getClassLhyCId());
                pstmt.setString(index++, form.getClassName());
                pstmt.setString(index++, form.getCId());
                pstmt.setString(index++, form.getCStudentName());
                pstmt.setInt(index++, form.getFormScoreCj());
                pstmt.setInt(index++, form.getFormScoreWc());
                pstmt.setString(index++, form.getFormScoreXk());
                pstmt.setString(index++, form.getPcCode());
                pstmt.setString(index++, form.getPc());
                pstmt.setString(index++, form.getLqIs985());
                pstmt.setString(index++, form.getLqIs211());
                pstmt.setString(index++, form.getLqIsSyl());
                pstmt.setString(index++, form.getLqIsHzbx());
                pstmt.setTimestamp(index++, form.getCreateTm());
                pstmt.setTimestamp(index++, form.getRecordCreateTm());
                pstmt.setString(index++, form.getLqYxsf());
                pstmt.setString(index++, form.getLqYxcs());
                pstmt.setString(index++, form.getLqYxdm());
                pstmt.setString(index++, form.getLqYxmc());
                pstmt.setString(index++, form.getLqYxmcOrg());
                pstmt.setString(index++, form.getLqZyz());
                pstmt.setString(index++, form.getLqZydm());
                pstmt.setString(index++, form.getLqZymc());
                pstmt.setString(index++, form.getLqZymcOrg());
                pstmt.setInt(index++, form.getIsAdjust());
                pstmt.setInt(index++, form.getWasteScoreCnt());

                pstmt.addBatch();
			}
			SQLLogUtils.printSQL(pstmt);
			pstmt.executeLargeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, pstmt, rs);
		}
	}
	
	public void adjustLhyFormZyNoWithFormId(String sfCode, List<LhyForm> updateList) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "update lhy_"+sfCode+"_form_maker set seq_no_zy = ? where id = ?";
			ps = conn.prepareStatement(SQL);
			for(LhyForm form : updateList) {
				ps.setInt(1, form.getSeq_no_zy());
				ps.setInt(2, form.getId());
				ps.addBatch();
			}
			SQLLogUtils.printSQL(ps); 
			ps.executeBatch();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void adjustLhyFormYxNoWithFormId(String sfCode, List<LhyForm> updateList) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			conn.setAutoCommit(false);
			String SQL = "update lhy_"+sfCode+"_form_maker set seq_no_yx = ? where id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			for(LhyForm form : updateList) {
				ps.setInt(1, form.getSeq_no_yx());
				ps.setInt(2, form.getId());
				ps.addBatch();
			}
			ps.executeBatch();
			conn.commit();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	/**
	 * 拖动事务控制
	 * @param sfCode
	 * @param updateList
	 */
	public static void adjustLhyFormYxNoWithFormIdWithTransaction(String sfCode, String new_seq, String xkCode, String batch_id_org, String time) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			conn.setAutoCommit(false);
			String[] new_seq_array = new_seq.split(",");
			if(Tools.isEmpty(new_seq) || new_seq_array.length == 0){ 
				return;
			}

			Hashtable<Integer, Integer> TEMP_MAP = new Hashtable<>();
			for(int i = 0 ; i < new_seq_array.length; i++){
				int no = Tools.getInt(new_seq_array[i]);
				if(no < 1){
					return;
				}
				TEMP_MAP.put(no, i + 1);
			}
			
			Vector<LhyForm> formList = new Vector<>();
			String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker x WHERE batch_id_org = ? ORDER BY seq_no_yx, seq_no_zy";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);

			rs = ps.executeQuery();
			while (rs.next()) {
				formList.add(setLhyForm(rs));
			}
			
			for(LhyForm bean : formList){
				bean.setExt_org_seq_no_yx(bean.getSeq_no_yx());
				if(TEMP_MAP.containsKey(bean.getExt_org_seq_no_yx())){
					bean.setSeq_no_yx(TEMP_MAP.get(bean.getExt_org_seq_no_yx()));
				}
			}

			Vector<LhyForm> updateList = new Vector<>(); 
			for(LhyForm bean : formList){
				if(bean.getSeq_no_yx() != bean.getExt_org_seq_no_yx()){
					updateList.add(bean);
				}
			}
			
			if(updateList.size() > 0) {
				String SQL_UPDATE = "update lhy_"+sfCode+"_form_maker set seq_no_yx = ? where id = ?";
				ps = conn.prepareStatement(SQL_UPDATE);
				for(LhyForm form : updateList) {
					ps.setInt(1, form.getSeq_no_yx());
					ps.setInt(2, form.getId());
					ps.addBatch();
				}
				ps.executeBatch();
			}
			conn.commit();
		} catch (Exception ex) {
			ex.printStackTrace();
			try {
				conn.rollback();
			}catch(Exception exx) {}
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	/**
	 * 
	 * @param sfCode
	 * @param new_seq
	 * @param xkCode
	 * @param batch_id_org
	 * @param time
	 */
	public synchronized static void correctLhyForm(int nf, String sfCode, String batch_id, List<LhyForm> new_list, List<LhyForm> remove_list) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			conn.setAutoCommit(false);
			
			String SQL_UPDATE = "UPDATE lhy_"+sfCode+"_form_maker x SET seq_no_yx = ?, seq_no_zy = ? WHERE id = ? AND batch_id_org = ?";
			ps = conn.prepareStatement(SQL_UPDATE);
			for(LhyForm form : new_list) {
				ps.setInt(1, form.getSeq_no_yx());
				ps.setInt(2, form.getSeq_no_zy());
				ps.setInt(3, form.getId());
				ps.setString(4, batch_id); // 加强验证
				ps.addBatch();
			}
			ps.executeBatch();
			
			if(remove_list.size() > 0) {
				String SQL_DELETE = "DELETE FROM lhy_"+sfCode+"_form_maker where id = ? AND batch_id_org = ?";
				ps = conn.prepareStatement(SQL_DELETE);
				for(LhyForm form : remove_list) {
					ps.setInt(1, form.getId());
					ps.setString(2, batch_id);
					ps.addBatch();
				}
				ps.executeBatch();
			}
			
			conn.commit();
		} catch (Exception ex) {
			ex.printStackTrace();
			try {
				conn.rollback();
			}catch(Exception exx) {}
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void adjustMakerFormYxNoWithBatchIddForDrag(String sfCode, List<LhyForm> updateList) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "update lhy_"+sfCode+"_form_maker set seq_no_yx = ? where id = ?";
			ps = conn.prepareStatement(SQL);
			for(LhyForm form : updateList) {
				ps.setInt(1, form.getSeq_no_yx());
				ps.setInt(2, form.getId());
				ps.addBatch();
			}
			SQLLogUtils.printSQL("adjustMakerFormYxNoWithBatchIddForDrag>>>", ps); 
			ps.executeBatch();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public LhyFormMain getMakerFormMainByBatchId(String sfCode, String batchId) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker_main x WHERE batch_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batchId);
			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === getMakerFormMainByBatchId() : ", ps);
			
			LhyFormMain bean = null;
			while (rs.next()) {
				return setLhyFormMain(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public LhyFormMain getLhyFormMainByBatchId(String sfCode, String batchId) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker_main x WHERE batch_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batchId);
			rs = ps.executeQuery();
			while (rs.next()) {
				return setLhyFormMain(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public void deleteMakerFormMainAllWithOrderId(int nf, String sfCode, String order_id, int fType) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from lhy_"+sfCode+"_form_maker_main where order_id = ? and nf = ? and f_type = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, order_id);
			ps.setInt(2, nf);
			ps.setInt(3, fType);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void deleteMakerFormMainAllWithOrderId(String sfCode, String order_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from lhy_"+sfCode+"_form_maker_main where order_id = ?";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, order_id);
			SQLLogUtils.printSQL(ps);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void deleteMakerFormAllWithOrderId(String sfCode, String order_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from lhy_"+sfCode+"_form_maker where batch_id_org in (select batch_id from lhy_"+sfCode+"_form_maker_main where order_id = ?)";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, order_id);
			SQLLogUtils.printSQL(ps);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public boolean updateLhyOrderWC(String c_id, int wc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "update lhy_order set stu_score_wc = ? where order_id = ?" ;
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, wc);
			ps.setString(2, c_id);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	
	public static void main(String args[]) {
		new LhyJDBC().deleteMakerFormAllWithOrderId("", PASSWD);
	}
	
	
	public void deleteMakerFormMainWithBatchid(int nf, String sfCode, String batch_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "update lhy_"+sfCode+"_form_maker_main set status = 0 where batch_id = ? and nf = ?";
			Tools.println(SQL+" -> " + batch_id );
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			ps.setInt(2, nf);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void finishMakerFormMainWithBatchid(int nf, String sfCode, String batch_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "update lhy_"+sfCode+"_form_maker_main set status = 2 where batch_id = ? and nf = ?";
			Tools.println(SQL+" -> " + batch_id );
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			ps.setInt(2, nf);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void deleteMakerFormMain(int nf, String sfCode, String order_id, String pc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from lhy_"+sfCode+"_form_maker_main where order_id = ? and pc = ? and nf = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, order_id);
			ps.setString(2, pc);
			ps.setInt(3, nf);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void deleteMakerFormMainMaual(int nf, String sfCode, String order_id, String pc, String pc_code, int f_type) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from lhy_"+sfCode+"_form_maker_main where order_id = ? and pc = ? and pc_code = ? and f_type = ? and nf = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, order_id);
			ps.setString(2, pc);
			ps.setString(3, pc_code);
			ps.setInt(4, f_type);
			ps.setInt(5, nf);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void updateMakerFormMainFno(int nf, String sfCode, String batch_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "update lhy_"+sfCode+"_form_maker_main set f_no = id where batch_id = ? and nf = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			ps.setInt(2, nf);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void deleteZdksBlock(String c_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from zdks_block where c_id = ? ";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, c_id);
			ps.executeUpdate();
			SQLLogUtils.printSQL(ps);
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void insertMakerFormMain(String sfCode, LhyFormMain lhyFormMain) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into lhy_"+sfCode+"_form_maker_main(batch_id, score_cj, score_wc, score_xk, f_no, order_id, selected_zymc, pc, pc_code, nf, f_type, form_name, status, create_tm) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,1,now())";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, lhyFormMain.getBatch_id());
			ps.setInt(2, lhyFormMain.getScore_cj());
			ps.setInt(3, lhyFormMain.getScore_wc());
			ps.setString(4, lhyFormMain.getScore_xk());
			ps.setString(5, lhyFormMain.getF_no());
			ps.setString(6, lhyFormMain.getOrder_id());
			ps.setString(7, lhyFormMain.getSelected_zymc());
			ps.setString(8, lhyFormMain.getPc());
			ps.setString(9, lhyFormMain.getPc_code());
			ps.setInt(10, lhyFormMain.getNf());
			ps.setInt(11, lhyFormMain.getF_type());
			ps.setString(12, lhyFormMain.getForm_name());
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void deleteMakerForm(String batch_id, int seq_no_yx, String sfCode) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from lhy_"+sfCode+"_form_maker where batch_id_org = ? and seq_no_yx = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			ps.setInt(2, seq_no_yx);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	/**
	 * 删除给定志愿院校编号之后的（如编号是8，则删除9,10....）
	 * @param batch_id
	 * @param seq_no_yx
	 * @param sfCode
	 */
	public void deleteMakerFormAfter(String batch_id, int giving_seq_no_yx, String sfCode) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from lhy_"+sfCode+"_form_maker where batch_id_org = ? and seq_no_yx > ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			ps.setInt(2, giving_seq_no_yx);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void deleteMakerFormWithId(int form_id, String sfCode) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from lhy_"+sfCode+"_form_maker where id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, form_id);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void deleteMakerFormWithBatchId(String batch_id, String sfCode) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from lhy_"+sfCode+"_form_maker where batch_id_org = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void increaseMakerFormSeqYxNoWithBatchId(String sfCode, String batch_id, int fromSeqYxNo) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "update lhy_"+sfCode+"_form_maker set seq_no_yx = seq_no_yx + 1 where batch_id_org = ? and seq_no_yx >= ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			ps.setInt(2, fromSeqYxNo);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void adjustMakerFormZyNoWithId(String sfCode, List<LhyForm> updateList) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "update lhy_"+sfCode+"_form_maker set seq_no_zy = ? where id = ?";
			ps = conn.prepareStatement(SQL);
			for(LhyForm form : updateList) {
				ps.setInt(1, form.getSeq_no_zy());
				ps.setInt(2, form.getId());
				ps.addBatch();
			}
			SQLLogUtils.printSQL(ps);
			ps.executeBatch();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void increaseMakerFormYxNoWithBatchId(String sfCode, String batch_id, int fromSeqYxNo) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "update lhy_"+sfCode+"_form_maker set seq_no_yx = seq_no_yx - 1 where batch_id_org = ? and seq_no_yx >= ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			ps.setInt(2, fromSeqYxNo);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void deleteMakerFormMainByBatchId(String batch_id, String sfCode) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from lhy_"+sfCode+"_form_maker_main where batch_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void decreaseMakerFormSeqYxNoWithBatchId(String sfCode, String batch_id, int fromSeqYxNo) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "update lhy_"+sfCode+"_form_maker set seq_no_yx = seq_no_yx - 1 where batch_id_org = ? and seq_no_yx >= ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			ps.setInt(2, fromSeqYxNo);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void decreaseMakerFormSeqZyNoWithBatchIdAndSeqYxNo(String sfCode, String batch_id, int seq_no_yx, int fromSeqZyNo) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "update lhy_"+sfCode+"_form_maker set seq_no_zy = seq_no_zy - 1 where batch_id_org = ? and seq_no_yx = ? and seq_no_zy >= ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			ps.setInt(2, seq_no_yx);
			ps.setInt(3, fromSeqZyNo);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public LhyForm insertMakerForm(String sfCode, LhyForm form) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into lhy_"+sfCode+"_form_maker(batch_id, batch_id_org, seq_no_yx, yxmc, yxbz, yxdm, zyz, seq_no_zy, zymc, zybz, zydm, zdf_a, zdf_b, zdf_c, zdfwc_a, zdfwc_b, zdfwc_c, pjf_a, pjf_b, pjf_c, pjfwc_a, pjfwc_b, pjfwc_c, yxmc_org, zymc_org, jhs_a, jhs_b, jhs_c, zgf_a, zgf_b, zgf_c, zgfwc_a, zgfwc_b, zgfwc_c, jhs, fee) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
			//Tools.println(SQL);
			ps = conn.prepareStatement(SQL, Statement.RETURN_GENERATED_KEYS); 
			
			int index = 1;
			
			ps.setString(index++, form.getBatch_id());
			ps.setString(index++, form.getBatch_id_org());
			ps.setInt(index++, form.getSeq_no_yx());
			ps.setString(index++, form.getYxmc());
			ps.setString(index++, form.getYxbz());
			ps.setString(index++, form.getYxdm());
			ps.setString(index++, form.getZyz());
			ps.setInt(index++, form.getSeq_no_zy());
			ps.setString(index++, form.getZymc());
			ps.setString(index++, form.getZybz());
			ps.setString(index++, form.getZydm());

			ps.setInt(index++, form.getZdf_a());
			ps.setInt(index++, form.getZdf_b());
			ps.setInt(index++, form.getZdf_c());
			
			ps.setInt(index++, form.getZdfwc_a());
			ps.setInt(index++, form.getZdfwc_b());
			ps.setInt(index++, form.getZdfwc_c());
			
			ps.setInt(index++, form.getPjf_a());
			ps.setInt(index++, form.getPjf_b());
			ps.setInt(index++, form.getPjf_c());
			
			ps.setInt(index++, form.getPjfwc_a());
			ps.setInt(index++, form.getPjfwc_b());
			ps.setInt(index++, form.getPjfwc_c());
			
			ps.setString(index++, form.getYxmc_org());
			ps.setString(index++, form.getZymc_org());
			
			ps.setInt(index++, form.getJhs_a());
			ps.setInt(index++, form.getJhs_b());
			ps.setInt(index++, form.getJhs_c());
			
			ps.setInt(index++, form.getZgf_a());
			ps.setInt(index++, form.getZgf_b());
			ps.setInt(index++, form.getZgf_c());
			
			ps.setInt(index++, form.getZgfwc_a());
			ps.setInt(index++, form.getZgfwc_b());
			ps.setInt(index++, form.getZgfwc_c());
			ps.setInt(index++, form.getJhs());
			ps.setString(index++, form.getFee());
			
			ps.executeUpdate();
			
			rs = ps.getGeneratedKeys(); // 获取生成的键值
	        if (rs.next()) {
	            int generatedId = rs.getInt(1); 
	            form.setId(generatedId);
	        }
	        
			SQLLogUtils.printSQL("Check: ", ps);
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return form;
	}
	
	public void insertMakerFormByBatch(String sfCode, List<LhyForm> formList) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into lhy_"+sfCode+"_form_maker(batch_id, batch_id_org, seq_no_yx, yxmc, yxbz, yxdm, zyz, seq_no_zy, zymc, zybz, zydm, zdf_a, zdf_b, zdf_c, zdfwc_a, zdfwc_b, zdfwc_c, pjf_a, pjf_b, pjf_c, pjfwc_a, pjfwc_b, pjfwc_c, yxmc_org, zymc_org, jhs_a, jhs_b, jhs_c, zgf_a, zgf_b, zgf_c, zgfwc_a, zgfwc_b, zgfwc_c, jhs, fee) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			
			for(LhyForm form : formList) {
				int index = 1;
				ps.setString(index++, form.getBatch_id());
				ps.setString(index++, form.getBatch_id_org());
				ps.setInt(index++, form.getSeq_no_yx());
				ps.setString(index++, form.getYxmc());
				ps.setString(index++, form.getYxbz());
				ps.setString(index++, form.getYxdm());
				ps.setString(index++, form.getZyz());
				ps.setInt(index++, form.getSeq_no_zy());
				ps.setString(index++, form.getZymc());
				ps.setString(index++, form.getZybz());
				ps.setString(index++, form.getZydm());
	
				ps.setInt(index++, form.getZdf_a());
				ps.setInt(index++, form.getZdf_b());
				ps.setInt(index++, form.getZdf_c());
				
				ps.setInt(index++, form.getZdfwc_a());
				ps.setInt(index++, form.getZdfwc_b());
				ps.setInt(index++, form.getZdfwc_c());
				
				ps.setInt(index++, form.getPjf_a());
				ps.setInt(index++, form.getPjf_b());
				ps.setInt(index++, form.getPjf_c());
				
				ps.setInt(index++, form.getPjfwc_a());
				ps.setInt(index++, form.getPjfwc_b());
				ps.setInt(index++, form.getPjfwc_c());
				
				ps.setString(index++, form.getYxmc_org());
				ps.setString(index++, form.getZymc_org());
				
				ps.setInt(index++, form.getJhs_a());
				ps.setInt(index++, form.getJhs_b());
				ps.setInt(index++, form.getJhs_c());
				
				ps.setInt(index++, form.getZgf_a());
				ps.setInt(index++, form.getZgf_b());
				ps.setInt(index++, form.getZgf_c());
				
				ps.setInt(index++, form.getZgfwc_a());
				ps.setInt(index++, form.getZgfwc_b());
				ps.setInt(index++, form.getZgfwc_c());
				ps.setInt(index++, form.getJhs());
				ps.setString(index++, form.getFee());
				
				ps.addBatch();
			}
			ps.executeBatch();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void insertMakerFormByBatchForQC(String sfCode, List<LhyForm> formList) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into "+sfCode+"_form_maker(batch_id, batch_id_org, seq_no_yx, yxmc, yxbz, yxdm, zyz, seq_no_zy, zymc, zybz, zydm, zdf_a, zdf_b, zdf_c, zdfwc_a, zdfwc_b, zdfwc_c, pjf_a, pjf_b, pjf_c, pjfwc_a, pjfwc_b, pjfwc_c, yxmc_org, zymc_org, jhs_a, jhs_b, jhs_c) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
			//Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			
			for(LhyForm form : formList) {
				int index = 1;
				ps.setString(index++, form.getBatch_id());
				ps.setString(index++, form.getBatch_id_org());
				ps.setInt(index++, form.getSeq_no_yx());
				ps.setString(index++, form.getYxmc());
				ps.setString(index++, form.getYxbz());
				ps.setString(index++, form.getYxdm());
				ps.setString(index++, form.getZyz());
				ps.setInt(index++, form.getSeq_no_zy());
				ps.setString(index++, form.getZymc());
				ps.setString(index++, form.getZybz());
				ps.setString(index++, form.getZydm());
	
				ps.setInt(index++, form.getZdf_a());
				ps.setInt(index++, form.getZdf_b());
				ps.setInt(index++, form.getZdf_c());
				
				ps.setInt(index++, form.getZdfwc_a());
				ps.setInt(index++, form.getZdfwc_b());
				ps.setInt(index++, form.getZdfwc_c());
				
				ps.setInt(index++, form.getPjf_a());
				ps.setInt(index++, form.getPjf_b());
				ps.setInt(index++, form.getPjf_c());
				
				ps.setInt(index++, form.getPjfwc_a());
				ps.setInt(index++, form.getPjfwc_b());
				ps.setInt(index++, form.getPjfwc_c());
				
				ps.setString(index++, form.getYxmc_org());
				ps.setString(index++, form.getZymc_org());
				
				ps.setInt(index++, form.getJhs_a());
				ps.setInt(index++, form.getJhs_b());
				ps.setInt(index++, form.getJhs_c());
				ps.addBatch();
			}
			ps.executeBatch();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public boolean updateLhyBaseCard(LhyCard bean) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		boolean result = false;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "update lhy_card set c_passwd = ?, c_name = ?, c_phone = ?, c_prov = ?, c_allow_prov = ?, c_status = ?, c_level = ?, c_type= ?, order_cnt = ?, card_cnt = ?, sub_acct_cnt = ?, c_function = ?, active_tm = ? ,last_login_tm = ?, expire_tm = ?, p_c_id=? WHERE c_id = ? ";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, bean.getC_passwd());
			ps.setString(2, bean.getC_name());
			ps.setString(3, bean.getC_phone());
			ps.setString(4, bean.getC_prov());
			ps.setString(5, bean.getC_allow_prov());
			ps.setInt(6, bean.getC_status());
			ps.setInt(7, bean.getC_level());
			ps.setInt(8, bean.getC_type());
			ps.setInt(9, bean.getOrder_cnt());
			ps.setInt(10, bean.getCard_cnt());
			ps.setInt(11, bean.getSub_acct_cnt());
			ps.setString(12, bean.getC_function());
			ps.setTimestamp(13, bean.getActive_tm() == null ? null : new Timestamp(bean.getActive_tm().getTime()));
			ps.setTimestamp(14, bean.getLast_login_tm() == null ? null : new Timestamp(bean.getLast_login_tm().getTime()));
			ps.setTimestamp(15, bean.getExpire_tm() == null ? null : new Timestamp(bean.getExpire_tm().getTime()));
			ps.setString(16, bean.getP_c_id());
			ps.setString(17, bean.getC_id());
			ps.executeUpdate();
			result = true; 
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return result;
	}
	public boolean updateLhyBaseCardAll(LhyCard bean) {
        Connection conn = null;
        PreparedStatement ps = null;
        boolean result = false;

        try {
            conn = DatabaseUtils.getConnection();
            String UPDATE_CONDITION = "UPDATE lhy_card SET " +
                    "c_sub_prefix = ?, " +
                    "p_c_id = ?, " +
                    "c_passwd = ?, " +
                    "c_name = ?, " +
                    "c_remark = ?, " +
                    "c_phone = ?, " +
                    "c_prov = ?, " +
                    "c_allow_prov = ?, " +
                    "c_function = ?, " +
                    "c_status = ?, " +
                    "c_level = ?, " +
                    "c_type = ?, " +
                    "order_cnt = ?, " +
                    "card_cnt = ?, " +
                    "sub_acct_cnt = ?, " +
                    "zd_query_cnt = ?, " +
                    "sys_ind = ?, " +
                    "create_tm = ?, " +
                    "active_tm = ?, " +
                    "last_login_tm = ?, " +
                    "expire_tm = ?, " +
                    "saas_id = ?, " +
                    "c_batch_id = ?, " +
                    "c_auto_ind = ? " +
                    "WHERE c_id = ?";
            Tools.println(UPDATE_CONDITION);

            ps = conn.prepareStatement(UPDATE_CONDITION);
            ps.setString(1, bean.getC_sub_prefix());
            ps.setString(2, bean.getP_c_id());
            ps.setString(3, bean.getC_passwd());
            ps.setString(4, bean.getC_name());
            ps.setString(5, bean.getC_remark());
            ps.setString(6, bean.getC_phone());
            ps.setString(7, bean.getC_prov());
            ps.setString(8, bean.getC_allow_prov());
            ps.setString(9, bean.getC_function());
            ps.setInt(10, bean.getC_status());
            ps.setInt(11, bean.getC_level());
            ps.setInt(12, bean.getC_type());
            ps.setInt(13, bean.getOrder_cnt());
            ps.setInt(14, bean.getCard_cnt());
            ps.setInt(15, bean.getSub_acct_cnt());
            ps.setInt(16, bean.getZd_query_cnt());
            ps.setInt(17, bean.getSys_ind());
            ps.setTimestamp(18, bean.getCreate_tm() == null ? null : new Timestamp(bean.getCreate_tm().getTime()));
            ps.setTimestamp(19, bean.getActive_tm() == null ? null : new Timestamp(bean.getActive_tm().getTime()));
            ps.setTimestamp(20, bean.getLast_login_tm() == null ? null : new Timestamp(bean.getLast_login_tm().getTime()));
            ps.setTimestamp(21, bean.getExpire_tm() == null ? null : new Timestamp(bean.getExpire_tm().getTime()));
            ps.setString(22, bean.getSaas_id());
            ps.setString(23, bean.getC_batch_id());
            ps.setString(24, bean.getC_auto_ind());
            ps.setString(25, bean.getC_id());

            int rowsAffected = ps.executeUpdate();
            result = rowsAffected > 0;
        } catch (SQLException ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, null);
        }

        return result;
    }
	
	public boolean deleteLhyCard(String c_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		boolean result = false;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "update lhy_card set c_status = 0 where c_id = ?";
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, c_id);
			ps.executeUpdate();
			SQLLogUtils.printSQL(ps);
			result = true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return result;
	}
	
	public boolean updateChildLhyCardSaasIdByParent(String p_c_id, String saas_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		boolean result = false;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "update lhy_card set saas_id = ? where p_c_id = ?"; 
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, saas_id);
			ps.setString(2, p_c_id);
			ps.executeUpdate();
			SQLLogUtils.printSQL(ps);
			result = true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return result;
	}
	
	public boolean insertLhyCard(LhyCard bean) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		boolean result = false;
		try {
			conn = DatabaseUtils.getConnection();
			String INSERT_CONDITION = "INSERT INTO lhy_card (c_id, p_c_id, c_passwd, c_name, c_phone, c_prov, c_allow_prov, c_function, c_status, c_level, c_type, order_cnt, card_cnt, sub_acct_cnt, zd_query_cnt, create_tm, expire_tm, c_sub_prefix, saas_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?,?)";
            Tools.println(INSERT_CONDITION);
            ps = conn.prepareStatement(INSERT_CONDITION);
            ps.setString(1, bean.getC_id());
            ps.setString(2, bean.getP_c_id());
            ps.setString(3, bean.getC_passwd());
            ps.setString(4, bean.getC_name());
            ps.setString(5, bean.getC_phone());
            ps.setString(6, bean.getC_prov());
            ps.setString(7, bean.getC_allow_prov());
            ps.setString(8, bean.getC_function()); // 添加 c_function 字段
            ps.setInt(9, bean.getC_status());
            ps.setInt(10, bean.getC_level());
            ps.setInt(11, bean.getC_type());
            ps.setInt(12, bean.getOrder_cnt());
            ps.setInt(13, bean.getCard_cnt());
            ps.setInt(14, bean.getSub_acct_cnt());
            ps.setInt(15, bean.getZd_query_cnt());
            ps.setTimestamp(16, bean.getExpire_tm() == null ? null : new Timestamp(bean.getExpire_tm().getTime()));
            ps.setString(17, bean.getC_sub_prefix());
            ps.setString(18, bean.getSaas_id());
            ps.executeUpdate();
			result = true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return result;
	}
	
	public boolean arrayLhyCardForAgent(LhyCard bean, int cardCnt) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            conn = DatabaseUtils.getConnection();
            String SQL = "UPDATE lhy_card x SET x.c_name = ?, x.c_phone = ?, x.order_cnt = ?, x.sub_acct_cnt = ?, x.c_allow_prov = ?, x.c_function = ?, x.c_batch_id = ?, x.c_prov = ?, x.saled_status = 2, x.c_remark = ?, x.saas_id = ? "
            		+ " WHERE x.saled_status = 1 AND x.c_auto_ind = 11 AND x.c_batch_id IS NULL AND x.c_prov IS NULL ORDER BY RAND() LIMIT ?";
            ps = conn.prepareStatement(SQL);
            ps.setString(1, bean.getC_name()); // c_name
            ps.setString(2, bean.getC_phone()); // c_phone
            ps.setInt(3, bean.getOrder_cnt()); // order_cnt
            ps.setInt(4, bean.getSub_acct_cnt()); // sub_acct_cnt
            ps.setString(5, bean.getC_allow_prov()); // c_allow_prov
            ps.setString(6, bean.getC_function()); // c_batch_id
            ps.setString(7, bean.getC_batch_id()); // c_auto_ind
            ps.setString(8, bean.getC_prov()); // c_prov
            ps.setString(9, bean.getC_remark()); // c_level
            ps.setString(10, bean.getSaas_id()); // c_remark
            ps.setInt(11, cardCnt);
            
            SQLLogUtils.printSQL(ps);
            ps.executeUpdate();
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        return false;
    }
	
	public boolean insertLhyCardWithBatch(List<LhyCard> list) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		boolean result = false;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "insert into lhy_card(c_id,p_c_id,c_passwd,c_name,c_phone,c_prov,c_allow_prov,c_status,c_level,c_type,order_cnt,card_cnt,sub_acct_cnt,zd_query_cnt,create_tm,expire_tm,c_sub_prefix) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),?,?)";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			for(LhyCard bean : list) {
				ps.setString(1, bean.getC_id());
				ps.setString(2, bean.getP_c_id());
				ps.setString(3, bean.getC_passwd());
				ps.setString(4, bean.getC_name());
				ps.setString(5, bean.getC_phone());
				ps.setString(6, bean.getC_prov());
				ps.setString(7, bean.getC_allow_prov());
				ps.setInt(8, bean.getC_status());
				ps.setInt(9, bean.getC_level());
				ps.setInt(10, bean.getC_type());
				ps.setInt(11, bean.getOrder_cnt());
				ps.setInt(12, bean.getCard_cnt());
				ps.setInt(13, bean.getSub_acct_cnt());
				ps.setInt(14, bean.getZd_query_cnt());
				ps.setTimestamp(15, new Timestamp(bean.getExpire_tm().getTime()));
				ps.setString(16, bean.getC_sub_prefix());
				ps.addBatch();
			}
			ps.executeBatch();
			result = true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return result;
	}
	
	public List<ZyzdForm> getMakerFormByBatchId(String sfCode, String batch_id_org) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdForm> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+sfCode+"_form_maker x WHERE batch_id_org = ? ORDER BY seq_no_yx, seq_no_zy";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);

			rs = ps.executeQuery();
			ZyzdForm bean = null;
			while (rs.next()) {
				list.add(setZyzdForm(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<LhyForm> getLhyFormByBatchId(String sfCode, String batch_id_org) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyForm> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker x WHERE batch_id_org = ? ORDER BY seq_no_yx, seq_no_zy";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setLhyForm(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdForm> getMakerFormByBatchIdAndYxSeq(String sfCode, String batch_id_org, int yx_seq) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdForm> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+sfCode+"_form_maker x WHERE batch_id_org = ? and seq_no_yx = ? ORDER BY seq_no_yx, seq_no_zy";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			ps.setInt(2, yx_seq);

			rs = ps.executeQuery();
			ZyzdForm bean = null;
			while (rs.next()) {
				list.add(setZyzdForm(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<LhyForm> getLhyFormForYx(String sfCode, String batch_id_org, String yxmc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyForm> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker x WHERE batch_id_org = ? and yxmc like ? ORDER BY seq_no_yx, seq_no_zy";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			ps.setString(2, "%"+yxmc+"%");

			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === getLhyFormForYx: ", ps);
			
			LhyForm bean = null;
			while (rs.next()) {
				list.add(setLhyForm(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<LhyForm> getLhyFormByBatchIdAndYxSeq(String sfCode, String batch_id_org, int yx_seq) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyForm> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker x WHERE batch_id_org = ? and seq_no_yx = ? ORDER BY seq_no_yx, seq_no_zy";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			ps.setInt(2, yx_seq);

			rs = ps.executeQuery();
			LhyForm bean = null;
			while (rs.next()) {
				list.add(setLhyForm(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<LhyForm> getLhyFormByBatchIdAndYxSeqWithJHData(int jh_nf, String xkCode, String sfCode, String batch_id_org, int yx_seq) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = jh_nf - 1;
		List<LhyForm> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.batch_id, x.id, x.batch_id_org, x.seq_no_yx, x.seq_no_zy, x.yxdm, x.yxmc, x.zyz, x.zydm, x.zymc, NULL AS yxbz, "
					+ "jh.zybz, jh.zymc_org, jh.yxmc_org, jh.jhs, jh.fee, jh.xz,  "
					+ "jh.qsf_a, jh.qsf_b, jh.qsf_c, jh.qsf, "
					+ "jh.znzyls, "
					+ "jh.zdf_"+(dataYear)+" AS zdf_a, jh.zdf_"+(dataYear - 1)+" AS zdf_b, jh.zdf_"+(dataYear-2)+" AS zdf_c, "
					+ "jh.zdfwc_"+(dataYear)+" AS zdfwc_a, jh.zdfwc_"+(dataYear - 1)+" AS zdfwc_b, jh.zdfwc_"+(dataYear-2)+" AS zdfwc_c, "
					+ "jh.zgf_"+(dataYear)+" AS zgf_a, jh.zgf_"+(dataYear - 1)+" AS zgf_b, jh.zgf_"+(dataYear-2)+" AS zgf_c, "
					+ "jh.zgfwc_"+(dataYear)+" AS zgfwc_a, jh.zgfwc_"+(dataYear - 1)+" AS zgfwc_b, jh.zgfwc_"+(dataYear-2)+" AS zgfwc_c, "
					+ "jh.pjf_"+(dataYear)+" AS pjf_a, jh.pjf_"+(dataYear-1)+" AS pjf_b, jh.pjf_"+(dataYear-2)+" AS pjf_c, "
					+ "jh.pjfwc_"+(dataYear)+" AS pjfwc_a, jh.pjfwc_"+(dataYear-1)+" AS pjfwc_b, jh.pjfwc_"+(dataYear-2)+" AS pjfwc_c, "
					+ "jh.lqrs_"+(dataYear)+" AS lqrs_a, jh.lqrs_"+(dataYear-1)+" AS lqrs_b, jh.lqrs_"+(dataYear-2)+" AS lqrs_c, "
					+ "jh.jhs_"+(dataYear)+" AS jhs_a, jh.jhs_"+(dataYear-1)+" AS jhs_b, jh.jhs_"+(dataYear-2)+" AS jhs_c "
					+ "FROM lhy_"+sfCode+"_form_maker x LEFT JOIN lhy_"+sfCode+"_form_maker_main ma ON x.batch_id_org = ma.batch_id LEFT JOIN "+sfCode+"_jh_"+jh_nf+" jh "
					+ "ON ma.pc = jh.pc AND ma.pc_code = jh.pc_code AND x.yxdm = jh.yxdm AND x.zydm = jh.zydm AND x.zyz = jh.zyz AND jh.xk_code like ? "
					+ "WHERE x.batch_id_org = ? AND x.seq_no_yx = ? ORDER BY x.seq_no_yx, x.seq_no_zy";
			
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + xkCode + "%");
			ps.setString(2, batch_id_org);
			ps.setInt(3, yx_seq);

			rs = ps.executeQuery();
			LhyForm bean = null;
			while (rs.next()) {
				bean = setLhyForm(rs);
				
				bean.setExt_jh_qsf_a(rs.getInt("qsf_a"));
				bean.setExt_jh_qsf_b(rs.getInt("qsf_b"));
				bean.setExt_jh_qsf_c(rs.getInt("qsf_c"));
				bean.setExt_jh_qsf(rs.getInt("qsf"));
				bean.setLqrs_a(rs.getInt("lqrs_a"));
				bean.setLqrs_b(rs.getInt("lqrs_b"));
				bean.setLqrs_c(rs.getInt("lqrs_c"));
				bean.setExt_jh_znzyls(rs.getInt("znzyls"));
				list.add(bean);
				
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public LhyForm getLhyFormSeqNoByBatchIdAndYxForSeqUp(String sfCode, String batch_id_org, int yx_seq) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker x WHERE batch_id_org = ? and seq_no_yx < ? ORDER BY seq_no_yx DESC limit 1";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			ps.setInt(2, yx_seq);

			rs = ps.executeQuery();
			LhyForm bean = null;
			while (rs.next()) {
				return setLhyForm(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public LhyForm getLhyFormByBatchIdAndYxForSeqDown(String sfCode, String batch_id_org, int yx_seq) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker x WHERE batch_id_org = ? and seq_no_yx > ? ORDER BY seq_no_yx limit 1";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			ps.setInt(2, yx_seq);

			rs = ps.executeQuery();
			while (rs.next()) {
				return setLhyForm(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public static ZyzdForm setZyzdForm(ResultSet rs) throws SQLException {
		ZyzdForm bean = new ZyzdForm();
		bean.setBatch_id(rs.getString("batch_id"));
		bean.setId(rs.getInt("id"));
		bean.setBatch_id_org(rs.getString("batch_id_org"));
		bean.setSeq_no_yx(rs.getInt("seq_no_yx"));
		bean.setSeq_no_zy(rs.getInt("seq_no_zy"));
		bean.setYxbz(rs.getString("yxbz"));
		bean.setYxdm(rs.getString("yxdm"));
		bean.setYxmc(rs.getString("yxmc"));
		bean.setZybz(rs.getString("zybz"));
		bean.setZydm(rs.getString("zydm"));
		bean.setZymc(rs.getString("zymc"));
		bean.setZyz(rs.getString("zyz"));
		bean.setYxmc_org(rs.getString("yxmc_org"));
		bean.setZymc_org(rs.getString("zymc_org"));
		
		bean.setZdf_a(rs.getInt("zdf_a"));
		bean.setZdf_b(rs.getInt("zdf_b"));
		bean.setZdf_c(rs.getInt("zdf_c"));
		bean.setZdfwc_a(rs.getInt("zdfwc_a"));
		bean.setZdfwc_b(rs.getInt("zdfwc_b"));
		bean.setZdfwc_c(rs.getInt("zdfwc_c"));

		bean.setPjf_a(rs.getInt("pjf_a"));
		bean.setPjf_b(rs.getInt("pjf_b"));
		bean.setPjf_c(rs.getInt("pjf_c"));
		bean.setPjfwc_a(rs.getInt("pjfwc_a"));
		bean.setPjfwc_b(rs.getInt("pjfwc_b"));
		bean.setPjfwc_c(rs.getInt("pjfwc_c"));
		bean.setJhs(rs.getInt("jhs"));
		bean.setFee(rs.getString("fee"));
		
		bean.setZgf_a(rs.getInt("zgf_a"));
		bean.setZgf_b(rs.getInt("zgf_b"));
		bean.setZgf_c(rs.getInt("zgf_c"));
		bean.setZgfwc_a(rs.getInt("zgfwc_a"));
		bean.setZgfwc_b(rs.getInt("zgfwc_b"));
		bean.setZgfwc_c(rs.getInt("zgfwc_c"));
		
		return bean;
	}
	
	public ZyzdForm getMakerFormById(String sfCode, int id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+sfCode+"_form_maker x WHERE id = ? ORDER BY seq_no_yx, seq_no_zy";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, id);

			rs = ps.executeQuery();
			ZyzdForm bean = null;
			while (rs.next()) {
				return setZyzdForm(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public LhyForm getLhyFormById(String sfCode, int id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker x WHERE id = ? ORDER BY seq_no_yx, seq_no_zy";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, id);

			rs = ps.executeQuery();
			LhyForm bean = null;
			while (rs.next()) {
				return setLhyForm(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	private static LhyForm setLhyForm(ResultSet rs) throws SQLException {
		LhyForm bean = new LhyForm();
		bean.setBatch_id(rs.getString("batch_id"));
		bean.setId(rs.getInt("id"));
		bean.setBatch_id_org(rs.getString("batch_id_org"));
		bean.setSeq_no_yx(rs.getInt("seq_no_yx"));
		bean.setSeq_no_zy(rs.getInt("seq_no_zy"));
		bean.setYxdm(rs.getString("yxdm"));
		bean.setZydm(rs.getString("zydm"));
		bean.setZyz(rs.getString("zyz"));
		return bean;
	}
	
	public LhyForm getLhyFormByBatchOrgAndYxSeqnoAndZySeqno(String sfCode, String batch_id_org, int yx_seq_no, int zy_seq_no) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker x WHERE batch_id_org = ? and seq_no_yx = ? and seq_no_zy = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			ps.setInt(2, yx_seq_no);
			ps.setInt(3, zy_seq_no);

			rs = ps.executeQuery();
			LhyForm bean = null;
			while (rs.next()) {
				return setLhyForm(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public List<JHBean> getZyByZymlForLhy(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> zymlSets, String exceptYx, int zdf_from, int zdf_to) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String zymlSQL = zymlSets.size() == 0 ? "" : "and x.zyml in ("+Tools.getSQLQueryin(zymlSets)+")";
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.pc = ? and x.pc_code = ? and x.xk_code like ? and x.yxmc_org <> ? and (x.zdf between ? and ?) " + zymlSQL;
			String ORDER_CONDITION = " ORDER BY x.zdf DESC";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			ps.setString(4, exceptYx);
			ps.setInt(5, zdf_from);
			ps.setInt(6, zdf_to);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JHBean> getYxSearchForLhy(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> catgSets, HashSet<String> sfSets, String ind_nature, String yx_tag, String tsxm, int zdfwc_from, int zdfwc_to) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String sfSQL = sfSets.size() == 0 ? "" : "and x.yxsf in ("+Tools.getSQLQueryin(sfSets)+")";
			String catgSQL = catgSets.size() == 0 ? "" : "and x.ind_catg in ("+Tools.getSQLQueryin(catgSets)+")";
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.pc = ? and x.pc_code = ? and x.xk_code like ? and x.ind_nature like ? and yx_tags like ? and (x.zdfwc between ? and ?) and (yxmc like ? or zymc like ? or zybz like ?) " + sfSQL + " " + catgSQL;
			String ORDER_CONDITION = " ORDER BY x.zdf DESC";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			ps.setString(4, "%"+ind_nature+"%");
			ps.setString(5, "%"+yx_tag+"%");
			ps.setInt(6, zdfwc_from);
			ps.setInt(7, zdfwc_to);
			ps.setString(8, "%"+tsxm+"%");
			ps.setString(9, "%"+tsxm+"%");
			ps.setString(10, "%"+tsxm+"%");
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public ResultVO getYxZyzSearchForLhy(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> catgSets, HashSet<String> sfSets, String ind_nature, boolean hzbx, String yx_tag, String tsxm, String yxmc, int zdfwc_from, int zdfwc_to, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHxBean> list = new ArrayList<>();
		ResultVO resultVO = new ResultVO();
		try {
			String ind_nature_new = hzbx ? " and (yxmc like '%中外%' or znzy like '%中外%') " : "";

			conn = DatabaseUtils.getConnection();
			String sfSQL = sfSets.size() == 0 ? "" : "and x.yxsf in ("+Tools.getSQLQueryin(sfSets)+")";
			String catgSQL = catgSets.size() == 0 ? "" : "and x.ind_catg in ("+Tools.getSQLQueryin(catgSets)+")";
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + "x x WHERE x.pc = ? and x.pc_code = ? and x.xk_code like ? and x.ind_nature like ? and yx_tags like ? and (CAST(COALESCE(x.zyzzdfwc,99999999) AS SIGNED) between ? and ?) and (yxmc like ?) " + ind_nature_new + sfSQL + " " + catgSQL;
			String SELECT_CONDITION_SUM = "SELECT COUNT(*) as cnt FROM "+sfCode+"_jh_" + jhYear + "x x WHERE x.pc = ? and x.pc_code = ? and x.xk_code like ? and x.ind_nature like ? and yx_tags like ? and (CAST(COALESCE(x.zyzzdfwc,99999999) AS SIGNED) between ? and ?) and (yxmc like ?) " + ind_nature_new + sfSQL + " " + catgSQL;;
			String ORDER_CONDITION = " ORDER BY x.zyzzdf DESC LIMIT ?,?";
			
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			ps.setString(4, "%"+ind_nature+"%");
			ps.setString(5, "%"+yx_tag+"%");
			ps.setInt(6, zdfwc_from);
			ps.setInt(7, zdfwc_to);
			ps.setString(8, "%"+yxmc+"%");
			ps.setInt(9, (pageNumber - 1) * PAGE_ROW_CNT);
			ps.setInt(10, PAGE_ROW_CNT);
			rs = ps.executeQuery(); 
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				list.add(setJHxBeanAll(dataYear, rs)); 
			}
			
			resultVO.setResult(list);
			
			rs.close();
			ps.close();
			rs = null; ps = null;
			
			ps = conn.prepareStatement(SELECT_CONDITION_SUM);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			ps.setString(4, "%"+ind_nature+"%");
			ps.setString(5, "%"+yx_tag+"%");
			ps.setInt(6, zdfwc_from); 
			ps.setInt(7, zdfwc_to);
			ps.setString(8, "%"+yxmc+"%");  
			rs = ps.executeQuery();
			while (rs.next()) {
				resultVO.setRecordCnt(rs.getInt("cnt"));
			}
			
			resultVO.setCurrentPage(pageNumber);
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return resultVO;
	}
	
	public ResultVO getYxZyzSearchForLhy(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> catgSets, HashSet<String> sfSets, String ind_nature, String yx_tag, String tsxm, String yxmc, int zdfwc_from, int zdfwc_to, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHxBean> list = new ArrayList<>();
		ResultVO resultVO = new ResultVO();
		try {
			conn = DatabaseUtils.getConnection();
			String sfSQL = sfSets.size() == 0 ? "" : "and x.yxsf in ("+Tools.getSQLQueryin(sfSets)+")";
			String catgSQL = catgSets.size() == 0 ? "" : "and x.ind_catg in ("+Tools.getSQLQueryin(catgSets)+")";
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + "x x WHERE x.pc = ? and x.pc_code = ? and x.xk_code like ? and x.ind_nature like ? and yx_tags like ? and (CAST(COALESCE(x.zyzzdfwc,99999999) AS SIGNED) between ? and ?) and (yxmc like ?) "  + sfSQL + " " + catgSQL;
			String SELECT_CONDITION_SUM = "SELECT COUNT(*) as cnt FROM "+sfCode+"_jh_" + jhYear + "x x WHERE x.pc = ? and x.pc_code = ? and x.xk_code like ? and x.ind_nature like ? and yx_tags like ? and (CAST(COALESCE(x.zyzzdfwc,99999999) AS SIGNED) between ? and ?) and (yxmc like ?) "  + sfSQL + " " + catgSQL;;
			String ORDER_CONDITION = " ORDER BY x.zyzzdf DESC LIMIT ?,?";
			
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			ps.setString(4, "%"+ind_nature+"%");
			ps.setString(5, "%"+yx_tag+"%");
			ps.setInt(6, zdfwc_from);
			ps.setInt(7, zdfwc_to);
			ps.setString(8, "%"+yxmc+"%");
			ps.setInt(9, (pageNumber - 1) * PAGE_ROW_CNT);
			ps.setInt(10, PAGE_ROW_CNT);
			rs = ps.executeQuery(); 
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				list.add(setJHxBeanAll(dataYear, rs)); 
			}
			
			resultVO.setResult(list);
			
			rs.close();
			ps.close();
			rs = null; ps = null;
			
			ps = conn.prepareStatement(SELECT_CONDITION_SUM);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			ps.setString(4, "%"+ind_nature+"%");
			ps.setString(5, "%"+yx_tag+"%");
			ps.setInt(6, zdfwc_from); 
			ps.setInt(7, zdfwc_to);
			ps.setString(8, "%"+yxmc+"%");  
			rs = ps.executeQuery();
			while (rs.next()) {
				resultVO.setRecordCnt(rs.getInt("cnt"));
			}
			
			resultVO.setCurrentPage(pageNumber);
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return resultVO;
	}
	
	
	public ResultVO getYxSearchForLhy(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> yxmcSets, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		ResultVO resultVO = new ResultVO();
		List<JHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String yxmcSQL = yxmcSets.size() == 0 ? "" : "and x.yxmc in ("+Tools.getSQLQueryin(yxmcSets)+")";
			String SELECT_CONDITION_SUM = "SELECT COUNT(*) as cnt FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.pc = ? and x.pc_code = ? and x.xk_code like ? " + yxmcSQL;
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.pc = ? and x.pc_code = ? and x.xk_code like ? " + yxmcSQL;
			String ORDER_CONDITION = " ORDER BY x.zdf DESC limit ?, ?";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			ps.setInt(4, (pageNumber - 1)*PAGE_ROW_CNT );
			ps.setInt(5, PAGE_ROW_CNT );
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setJHBeanAll(dataYear, rs));
			}
			resultVO.setResult(list);
			
			rs.close();
			ps.close();
			rs = null; ps = null;
			
			ps = conn.prepareStatement(SELECT_CONDITION_SUM);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			rs = ps.executeQuery();
			while (rs.next()) {
				resultVO.setRecordCnt(rs.getInt("cnt"));
			}
			
			resultVO.setCurrentPage(pageNumber);
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return resultVO;
	}
	
	public List<String> getYxmcOnlySearchForLhy(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> catgSets, HashSet<String> sfSets, String ind_nature, String yx_tag, String tsxm, int zdfwc_from, int zdfwc_to) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<String> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String sfSQL = sfSets.size() == 0 ? "" : "and x.yxsf in ("+Tools.getSQLQueryin(sfSets)+")";
			String catgSQL = catgSets.size() == 0 ? "" : "and x.ind_catg in ("+Tools.getSQLQueryin(catgSets)+")";
			String SELECT_CONDITION = "SELECT distinct yxmc FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.pc = ? and x.pc_code = ? and x.xk_code like ? and x.ind_nature like ? and yx_tags like ? and (x.zdfwc between ? and ?) and (yxmc like ? or zymc like ? or zybz like ?) " + sfSQL + " " + catgSQL;
			String ORDER_CONDITION = " ORDER BY x.zdf DESC";
			
			Tools.println(SELECT_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			ps.setString(4, "%"+ind_nature+"%");
			ps.setString(5, "%"+yx_tag+"%");
			ps.setInt(6, zdfwc_from);
			ps.setInt(7, zdfwc_to);
			ps.setString(8, "%"+tsxm+"%");
			ps.setString(9, "%"+tsxm+"%");
			ps.setString(10, "%"+tsxm+"%");
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(rs.getString("yxmc"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public ResultVO getLhyPaidFinaledOrder(String sfCode, String lhy_c_id, String stu_name, String status, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		ResultVO resultVO = new ResultVO();
		List<LhyFormMain> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SUM_CONDITION = "SELECT count(*) AS cnt ";
			String SELECT_CONDITION = "SELECT x.status, x.form_name, x.order_id,y.lhy_c_id, y.stu_info_name, y.stu_info_dh, z.c_id, z.c_name, x.batch_id, x.f_no, x.selected_zymc, x.pc, x.pc_code, x.score_xk, x.score_cj, x.score_wc,x.create_tm ";
			String FROM_CONDITION = " FROM lhy_"+sfCode+"_form_maker_main x, lhy_order y, lhy_card z WHERE x.status = ? and y.lhy_c_id = z.c_id and x.order_id = y.order_id and (y.lhy_c_id = ? or z.p_c_id = ?) and y.stu_info_name like ?";
			String ORDER_CONDITION = " ORDER BY x.create_tm DESC LIMIT ?,? ";
			
			Tools.println(SELECT_CONDITION + FROM_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + FROM_CONDITION + ORDER_CONDITION);
			ps.setString(1, status);
			ps.setString(2, lhy_c_id);
			ps.setString(3, lhy_c_id);
			ps.setString(4, "%" + stu_name + "%");
			ps.setInt(5, (pageNumber - 1) * PAGE_ROW_CNT);
			ps.setInt(6, PAGE_ROW_CNT);
			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === getLhyPaidFinaledOrder() : ", ps);
			
			LhyFormMain lhyFormMain = null;
			while (rs.next()) {
				lhyFormMain = new LhyFormMain();
				lhyFormMain.setExt_c_id(rs.getString("c_id"));
				lhyFormMain.setExt_stu_name(rs.getString("stu_info_name"));
				lhyFormMain.setExt_stu_dh(rs.getString("stu_info_dh"));
				lhyFormMain.setBatch_id(rs.getString("batch_id"));
				lhyFormMain.setF_no(rs.getString("f_no"));
				lhyFormMain.setSelected_zymc(rs.getString("selected_zymc"));
				lhyFormMain.setPc(rs.getString("pc"));
				lhyFormMain.setPc_code(rs.getString("pc_code"));
				lhyFormMain.setScore_xk(rs.getString("score_xk"));
				lhyFormMain.setScore_cj(rs.getInt("score_cj"));
				lhyFormMain.setScore_wc(rs.getInt("score_wc"));
				lhyFormMain.setOrder_id(rs.getString("order_id"));
				lhyFormMain.setExt_c_id(rs.getString("lhy_c_id"));
				lhyFormMain.setCreate_tm(rs.getTimestamp("create_tm"));
				lhyFormMain.setForm_name(rs.getString("form_name"));
				lhyFormMain.setStatus(rs.getInt("status"));
				lhyFormMain.setExt_c_name(rs.getString("c_name"));
				list.add(lhyFormMain);
			}
			resultVO.setResult(list);
			
			ps.close();
			ps = null;
			rs.close();
			rs = null;
			
			ps = conn.prepareStatement(SUM_CONDITION + FROM_CONDITION);
			ps.setString(1, status);
			ps.setString(2, lhy_c_id);
			ps.setString(3, lhy_c_id);
			ps.setString(4, "%" + stu_name + "%");
			rs = ps.executeQuery();
			
			while (rs.next()) {
				resultVO.setRecordCnt(rs.getInt("cnt"));
			}
			
			resultVO.setCurrentPage(pageNumber);
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return resultVO;
	}
	
	public ResultVO getLhyPaidFinaledOrderByMyself(String sfCode, String lhy_c_id, String stu_name, String status, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		ResultVO resultVO = new ResultVO();
		List<LhyFormMain> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SUM_CONDITION = "SELECT count(*) AS cnt ";
			String SELECT_CONDITION = "SELECT x.status, x.form_name, x.order_id,y.lhy_c_id, y.stu_info_name, y.stu_info_dh, z.c_id, z.c_name, x.batch_id, x.f_no, x.selected_zymc, x.pc, x.pc_code, x.score_xk, x.score_cj, x.score_wc,x.create_tm ";
			String FROM_CONDITION = " FROM lhy_"+sfCode+"_form_maker_main x, lhy_order y, lhy_card z WHERE x.status = ? and y.lhy_c_id = z.c_id and x.order_id = y.order_id and (y.lhy_c_id = ?) and y.stu_info_name like ?";
			String ORDER_CONDITION = " ORDER BY x.create_tm DESC LIMIT ?,? ";
			
			Tools.println(SELECT_CONDITION + FROM_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + FROM_CONDITION + ORDER_CONDITION);
			ps.setString(1, status);
			ps.setString(2, lhy_c_id);
			ps.setString(3, "%" + stu_name + "%");
			ps.setInt(4, (pageNumber - 1) * PAGE_ROW_CNT);
			ps.setInt(5, PAGE_ROW_CNT);
			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === getLhyPaidFinaledOrder() : ", ps);
			
			LhyFormMain lhyFormMain = null;
			while (rs.next()) {
				lhyFormMain = new LhyFormMain();
				lhyFormMain.setExt_c_id(rs.getString("c_id"));
				lhyFormMain.setExt_stu_name(rs.getString("stu_info_name"));
				lhyFormMain.setExt_stu_dh(rs.getString("stu_info_dh"));
				lhyFormMain.setBatch_id(rs.getString("batch_id"));
				lhyFormMain.setF_no(rs.getString("f_no"));
				lhyFormMain.setSelected_zymc(rs.getString("selected_zymc"));
				lhyFormMain.setPc(rs.getString("pc"));
				lhyFormMain.setPc_code(rs.getString("pc_code"));
				lhyFormMain.setScore_xk(rs.getString("score_xk"));
				lhyFormMain.setScore_cj(rs.getInt("score_cj"));
				lhyFormMain.setScore_wc(rs.getInt("score_wc"));
				lhyFormMain.setOrder_id(rs.getString("order_id"));
				lhyFormMain.setExt_c_id(rs.getString("lhy_c_id"));
				lhyFormMain.setCreate_tm(rs.getTimestamp("create_tm"));
				lhyFormMain.setForm_name(rs.getString("form_name"));
				lhyFormMain.setStatus(rs.getInt("status"));
				lhyFormMain.setExt_c_name(rs.getString("c_name"));
				list.add(lhyFormMain);
			}
			resultVO.setResult(list);
			
			ps.close();
			ps = null;
			rs.close();
			rs = null;
			
			ps = conn.prepareStatement(SUM_CONDITION + FROM_CONDITION);
			ps.setString(1, status);
			ps.setString(2, lhy_c_id); 
			ps.setString(3, "%" + stu_name + "%");
			rs = ps.executeQuery();
			
			while (rs.next()) {
				resultVO.setRecordCnt(rs.getInt("cnt"));
			}
			
			resultVO.setCurrentPage(pageNumber);
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return resultVO;
	}
	
	
	
	public ResultVO getLhyPaidOrder(String lhy_c_id, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		ResultVO resultVO = new ResultVO();
		List<LhyOrder> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SUM_CONDITION = "SELECT count(*) AS cnt ";
			String SELECT_CONDITION = "SELECT * ";
			String FROM_CONDITION = " FROM lhy_order x WHERE x.lhy_c_id = ? and x.status in (21, 22) ";
			String ORDER_CONDITION = " ORDER BY status DESC, x.create_tm DESC LIMIT ?,? ";
			
			Tools.println(SELECT_CONDITION + FROM_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + FROM_CONDITION + ORDER_CONDITION);
			ps.setString(1, lhy_c_id);
			ps.setInt(2, (pageNumber - 1) * PAGE_ROW_CNT_TEN);
			ps.setInt(3, PAGE_ROW_CNT_TEN);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setLhyOrderAll(rs));
			}
			resultVO.setResult(list);
			
			ps.close();
			ps = null;
			rs.close();
			rs = null;
			
			ps = conn.prepareStatement(SUM_CONDITION + FROM_CONDITION);
			ps.setString(1, lhy_c_id);
			rs = ps.executeQuery();
			while (rs.next()) {
				resultVO.setRecordCnt(rs.getInt("cnt"));
			}
			
			resultVO.setCurrentPage(pageNumber);
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return resultVO;
	}
	
	public ResultVO getLhyPaidOrder(String lhy_c_id, int pageCnt, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		ResultVO resultVO = new ResultVO();
		List<LhyOrder> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SUM_CONDITION = "SELECT count(*) AS cnt ";
			String SELECT_CONDITION = "SELECT * ";
			String FROM_CONDITION = " FROM lhy_order x WHERE x.lhy_c_id = ? and x.status in (21, 22) ";
			String ORDER_CONDITION = " ORDER BY status DESC, x.create_tm DESC LIMIT ?,? ";
			
			Tools.println(SELECT_CONDITION + FROM_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + FROM_CONDITION + ORDER_CONDITION);
			ps.setString(1, lhy_c_id);
			ps.setInt(2, (pageNumber - 1) * pageCnt);
			ps.setInt(3, pageCnt);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setLhyOrderAll(rs));
			}
			resultVO.setResult(list);
			
			ps.close();
			ps = null;
			rs.close();
			rs = null;
			
			ps = conn.prepareStatement(SUM_CONDITION + FROM_CONDITION);
			ps.setString(1, lhy_c_id);
			rs = ps.executeQuery();
			while (rs.next()) {
				resultVO.setRecordCnt(rs.getInt("cnt"));
			}
			
			resultVO.setCurrentPage(pageNumber);
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return resultVO;
	}
	
	public LhyOrder getFirstLhyPaidOrder(String lhy_c_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * ";
			String FROM_CONDITION = " FROM lhy_order x WHERE x.lhy_c_id = ? and x.status in (21, 22) ";
			String ORDER_CONDITION = " ORDER BY status DESC, x.create_tm DESC LIMIT 1 ";

			ps = conn.prepareStatement(SELECT_CONDITION + FROM_CONDITION + ORDER_CONDITION);
			ps.setString(1, lhy_c_id);
			rs = ps.executeQuery();
			while (rs.next()) {
				return setLhyOrderAll(rs);
			}
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public ResultVO getLhyRefundOrder(String lhy_c_id, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		ResultVO resultVO = new ResultVO();
		List<LhyOrder> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SUM_CONDITION = "SELECT count(*) AS cnt ";
			String SELECT_CONDITION = "SELECT * ";
			String FROM_CONDITION = " FROM lhy_order x WHERE x.lhy_c_id = ? and x.status = 20 ";
			String ORDER_CONDITION = " ORDER BY status DESC, x.create_tm DESC LIMIT ?,? ";
			
			Tools.println(SELECT_CONDITION + FROM_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + FROM_CONDITION + ORDER_CONDITION);
			ps.setString(1, lhy_c_id);
			ps.setInt(2, (pageNumber - 1) * PAGE_ROW_CNT_TEN);
			ps.setInt(3, PAGE_ROW_CNT_TEN);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setLhyOrderAll(rs));
			}
			resultVO.setResult(list);
			
			ps.close();
			ps = null;
			rs.close();
			rs = null;
			
			ps = conn.prepareStatement(SUM_CONDITION + FROM_CONDITION);
			ps.setString(1, lhy_c_id);
			rs = ps.executeQuery();
			while (rs.next()) {
				resultVO.setRecordCnt(rs.getInt("cnt"));
			}
			
			resultVO.setCurrentPage(pageNumber);
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return resultVO;
	}
	
	public ResultVO getLhyUnpaidOrder(String lhy_c_id, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		ResultVO resultVO = new ResultVO();
		List<LhyOrder> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SUM_CONDITION = "SELECT count(*) AS cnt ";
			String SELECT_CONDITION = "SELECT * ";
			String FROM_CONDITION = " FROM lhy_order x WHERE x.lhy_c_id = ? and x.status < 20 ";
			String ORDER_CONDITION = " ORDER BY status DESC, x.create_tm DESC LIMIT ?,? ";
			
			Tools.println(SELECT_CONDITION + FROM_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + FROM_CONDITION + ORDER_CONDITION);
			ps.setString(1, lhy_c_id);
			ps.setInt(2, (pageNumber - 1) * PAGE_ROW_CNT_TEN);
			ps.setInt(3, PAGE_ROW_CNT_TEN);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setLhyOrderAll(rs));
			}
			resultVO.setResult(list);
			
			ps.close();
			ps = null;
			rs.close();
			rs = null;
			
			ps = conn.prepareStatement(SUM_CONDITION + FROM_CONDITION);
			ps.setString(1, lhy_c_id);
			rs = ps.executeQuery();
			while (rs.next()) {
				resultVO.setRecordCnt(rs.getInt("cnt"));
			}
			
			resultVO.setCurrentPage(pageNumber);
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return resultVO;
	}
	
	
	public ResultVO getLhyRelatedBaseCardInfo(String sfCode, String lhy_c_id, String class_id, String stu_name, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		ResultVO resultVO = new ResultVO();
		List<ZyzdFormMain> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SUM_CONDITION = "SELECT count(*) AS cnt ";
			String SELECT_CONDITION = "SELECT x.*, z.C_NICKNAME as nickname, z.C_LAST_LOGIN as card_last_login, k.c_name as k_c_name ";
			String FROM_CONDITION = " FROM lhy_card_school_student st LEFT JOIN " + sfCode + "_form_maker_main x ON st.base_c_id = x.c_id LEFT JOIN base_card z ON x.c_id = z.c_id LEFT JOIN lhy_card k on st.lhy_c_id = k.c_id WHERE (k.c_id = ? OR k.p_c_id = ?) AND st.lhy_c_id LIKE ? AND st.student_name LIKE ? AND x.score_cj IS NOT NULL";
			String ORDER_CONDITION = " ORDER BY x.create_tm DESC LIMIT ?,? ";
			
			SQLLogUtils.printSQL(ps);
			ps = conn.prepareStatement(SELECT_CONDITION + FROM_CONDITION + ORDER_CONDITION); 
			ps.setString(1, lhy_c_id);
			ps.setString(2, lhy_c_id);
			ps.setString(3, "%" + class_id + "%");
			ps.setString(4, "%" + stu_name + "%");
			ps.setInt(5, (pageNumber - 1) * PAGE_ROW_CNT);
			ps.setInt(6, PAGE_ROW_CNT);
			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === getLhyRelatedBaseCardInfo: ", ps);
			
			while (rs.next()) { 
				ZyzdFormMain main = setZyzdFormMain(rs);
				main.setExt_nickname(rs.getString("nickname"));
				main.setExt_last_login(rs.getTimestamp("card_last_login"));
				main.setExt_class_name(rs.getString("k_c_name"));
				list.add(main);
			}
			resultVO.setResult(list);
			
			ps.close();
			ps = null;
			rs.close();
			rs = null;
			
			ps = conn.prepareStatement(SUM_CONDITION + FROM_CONDITION);
			ps.setString(1, lhy_c_id);
			ps.setString(2, lhy_c_id);
			ps.setString(3, "%" + class_id + "%");
			ps.setString(4, "%" + stu_name + "%");
			rs = ps.executeQuery();
			while (rs.next()) {
				resultVO.setRecordCnt(rs.getInt("cnt"));
			}
			
			resultVO.setCurrentPage(pageNumber);
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return resultVO;
	}
	
	public ResultVO getLhyRelatedCpInfo(String sfCode, String lhy_c_id, String class_id,String stu_name, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		ResultVO resultVO = new ResultVO();
		List<ZyzdAssessResult> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SUM_CONDITION = "SELECT count(*) AS cnt ";
			String SELECT_CONDITION = "SELECT x.*, z.C_NICKNAME as nickname, z.C_LAST_LOGIN as card_last_login, k.c_name as k_c_name ";
			String FROM_CONDITION = " FROM zyzd_assess_result x LEFT JOIN base_card z ON x.c_id = z.c_id LEFT JOIN lhy_related_card y ON x.c_id = y.base_c_id LEFT JOIN lhy_card k on y.lhy_c_id = k.c_id WHERE x.assess_from is NULL and (k.c_id = ? OR k.p_c_id = ?) and k.c_id like ? AND (z.C_NICKNAME LIKE ? OR z.C_NICKNAME IS NULL)  ";
			String ORDER_CONDITION = " ORDER BY x.create_tm DESC LIMIT ?,? ";
			
			Tools.println(SELECT_CONDITION + FROM_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + FROM_CONDITION + ORDER_CONDITION);
			ps.setString(1, lhy_c_id);
			ps.setString(2, lhy_c_id);
			ps.setString(3, "%" + class_id + "%");
			ps.setString(4, "%" + stu_name + "%");
			ps.setInt(5, (pageNumber - 1) * PAGE_ROW_CNT);
			ps.setInt(6, PAGE_ROW_CNT);
			rs = ps.executeQuery();
			ZyzdAssessResult bean = null;
			while (rs.next()) {
				bean = new ZyzdAssessResult();
				bean.setAssess_name(rs.getString("assess_name"));
				bean.setC_id(rs.getString("c_id"));
				bean.setAssess_result(rs.getString("assess_result"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				bean.setExt_nickname(rs.getString("nickname"));
				bean.setExt_last_login(rs.getTimestamp("card_last_login"));
				bean.setExt_class_name(rs.getString("k_c_name"));
				list.add(bean);
			}
			resultVO.setResult(list);
			
			ps.close();
			ps = null;
			rs.close();
			rs = null;
			
			ps = conn.prepareStatement(SUM_CONDITION + FROM_CONDITION);
			ps.setString(1, lhy_c_id);
			ps.setString(2, lhy_c_id);
			ps.setString(3, "%" + class_id + "%");
			ps.setString(4, "%" + stu_name + "%");
			rs = ps.executeQuery();
			while (rs.next()) {
				resultVO.setRecordCnt(rs.getInt("cnt"));
			}
			
			resultVO.setCurrentPage(pageNumber);
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return resultVO;
	}
	
	public ResultVO getLhyRelatedScoreInfo(String sfCode, String lhy_c_id, String class_id,String stu_name, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		ResultVO resultVO = new ResultVO();
		List<ScoreHistory> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SUM_CONDITION = "SELECT count(*) AS cnt ";
			String SELECT_CONDITION = "SELECT x.*, z.C_NICKNAME as nickname, z.C_LAST_LOGIN as card_last_login, k.c_name as k_c_name ";
			String FROM_CONDITION = " FROM "+sfCode+"_score_history x LEFT JOIN base_card z ON x.c_id = z.c_id LEFT JOIN lhy_related_card y ON x.c_id = y.base_c_id LEFT JOIN lhy_card k on y.lhy_c_id = k.c_id WHERE x.status = 1 and (k.c_id = ? OR k.p_c_id = ?) and k.c_id like ? AND (z.C_NICKNAME LIKE ? OR z.C_NICKNAME IS NULL)  ";
			String ORDER_CONDITION = " ORDER BY x.create_tm DESC LIMIT ?,? ";
			
			Tools.println(SELECT_CONDITION + FROM_CONDITION + ORDER_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION + FROM_CONDITION + ORDER_CONDITION);
			ps.setString(1, lhy_c_id);
			ps.setString(2, lhy_c_id);
			ps.setString(3, "%" + class_id + "%");
			ps.setString(4, "%" + stu_name + "%");
			ps.setInt(5, (pageNumber - 1) * PAGE_ROW_CNT);
			ps.setInt(6, PAGE_ROW_CNT);
			rs = ps.executeQuery();
			ScoreHistory bean = null;
			while (rs.next()) {
				bean = new ScoreHistory();
				bean.setC_id(rs.getString("c_id"));
				bean.setScore_from(rs.getString("score_from"));
				bean.setXk(rs.getString("xk"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				bean.setId(rs.getInt("id"));
				bean.setS_sum(rs.getInt("s_sum"));
				bean.setS_yw(rs.getInt("s_yw"));
				bean.setS_sx(rs.getInt("s_sx"));
				bean.setS_yy(rs.getInt("s_yy"));
				bean.setS_xk_a(rs.getInt("s_xk_a"));
				bean.setS_xk_b(rs.getInt("s_xk_b"));
				bean.setS_xk_c(rs.getInt("s_xk_c"));
				bean.setStatus(rs.getInt("status"));
				
				bean.setExt_nickname(rs.getString("nickname"));
				bean.setExt_last_login(rs.getTimestamp("card_last_login"));
				bean.setExt_class_name(rs.getString("k_c_name"));
				list.add(bean);
			}
			resultVO.setResult(list);
			
			ps.close();
			ps = null;
			rs.close();
			rs = null;
			
			ps = conn.prepareStatement(SUM_CONDITION + FROM_CONDITION);
			ps.setString(1, lhy_c_id);
			ps.setString(2, lhy_c_id);
			ps.setString(3, "%" + class_id + "%");
			ps.setString(4, "%" + stu_name + "%");
			rs = ps.executeQuery();
			while (rs.next()) {
				resultVO.setRecordCnt(rs.getInt("cnt"));
			}
			
			resultVO.setCurrentPage(pageNumber);
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return resultVO;
	}
	
	private ZyzdFormMain setZyzdFormMain(ResultSet rs) throws SQLException{
		ZyzdFormMain bean = new ZyzdFormMain(); 
		bean.setBatch_id(rs.getString("batch_id"));
		bean.setC_id(rs.getString("c_id"));
		bean.setPc(rs.getString("pc"));
		bean.setChecker_id(rs.getString("last_checker_id"));
		bean.setChecker_name(rs.getString("last_checker_name"));
		bean.setChecker_remark(rs.getString("last_checker_remark"));
		bean.setJoined_checker_cnt(rs.getInt("joined_checker_cnt"));
		bean.setCreate_tm(rs.getTimestamp("create_tm"));
		bean.setF_no(rs.getString("f_no"));
		bean.setLast_update_tm(rs.getTimestamp("last_update_tm"));
		bean.setScore_cj(rs.getInt("score_cj"));
		bean.setScore_wc(rs.getInt("score_wc"));
		bean.setNf(rs.getInt("nf"));
		bean.setScore_xk(rs.getString("score_xk"));
		bean.setSelected_zymc(rs.getString("selected_zymc"));
		bean.setF_type(rs.getInt("f_type"));
		return bean;
	} 
	
	
	private static JHBean setJHBeanAll(int dataYear, ResultSet rs) throws Exception {
		return ZyzdFormJDBC.setJHBeanAll(dataYear, rs);
	}
	
	private static JHxBean setJHxBeanAll(int dataYear, ResultSet rs) throws Exception {
		JHxBean bean = new JHxBean();
		bean.setId(rs.getInt("id"));
		bean.setSf(rs.getString("sf"));
		bean.setNf(rs.getInt("nf"));
		bean.setXk(rs.getString("xk"));
		bean.setZx(rs.getString("zx"));
		bean.setPc(rs.getString("pc"));
		bean.setPc_code(rs.getString("pc_code"));
		bean.setYxdm(rs.getString("yxdm"));
		bean.setYxmc(rs.getString("yxmc"));
		bean.setYxmc_org(rs.getString("yxmc_org"));
		bean.setZyz(rs.getString("zyz"));
		bean.setZys(rs.getInt("zys"));
		bean.setZnzy(rs.getString("znzy"));
		bean.setZyzjhs(rs.getString("zyzjhs"));
		bean.setZyzzdf(rs.getString("zyzzdf"));
		bean.setZyzzdfwc(rs.getString("zyzzdfwc"));
		bean.setZdf_a(rs.getString("zdf_"+dataYear));
		bean.setZdf_b(rs.getString("zdf_"+(dataYear-1)));
		bean.setZdf_c(rs.getString("zdf_"+(dataYear-2)));
		bean.setZdfwc_a(rs.getString("zdfwc_"+dataYear));
		bean.setZdfwc_b(rs.getString("zdfwc_"+(dataYear-1)));
		bean.setZdfwc_c(rs.getString("zdfwc_"+(dataYear-2)));
		bean.setPjf_a(rs.getString("pjf_"+dataYear));
		bean.setPjf_b(rs.getString("pjf_"+(dataYear-1)));
		bean.setPjf_c(rs.getString("pjf_"+(dataYear-2)));
		bean.setPjfwc_a(rs.getString("pjfwc_"+dataYear));
		bean.setPjfwc_b(rs.getString("pjfwc_"+(dataYear-1)));
		bean.setPjfwc_c(rs.getString("pjfwc_"+(dataYear-2)));
		bean.setYxsf(rs.getString("yxsf"));
		bean.setYxcs(rs.getString("yxcs"));
		bean.setInd_catg(rs.getString("ind_catg"));
		bean.setInd_nature(rs.getString("ind_nature"));
		bean.setYx_tags(rs.getString("yx_tags"));
		bean.setCnt_company(rs.getInt("cnt_company"));
		bean.setCnt_employ(rs.getInt("cnt_employ"));
		bean.setCnt_grad(rs.getFloat("cnt_grad"));
		bean.setLqpc(rs.getString("lqpc"));

		return bean;
	}
	
	private static LhyOrder setLhyOrderAll(ResultSet rs) throws Exception {
		LhyOrder bean = new LhyOrder();
		bean.setId(rs.getInt("id"));
		bean.setOrder_id(rs.getString("order_id"));
		bean.setRef_c_id(rs.getString("ref_c_id"));
		bean.setLhy_c_id(rs.getString("lhy_c_id"));
		bean.setSf(rs.getString("sf"));
		bean.setStu_info_name(rs.getString("stu_info_name"));
		bean.setStu_info_xb(rs.getString("stu_info_xb"));
		bean.setStu_info_bj(rs.getString("stu_info_bj"));
		bean.setStu_info_xx(rs.getString("stu_info_xx"));
		bean.setStu_info_dh(rs.getString("stu_info_dh"));
		bean.setStu_info_mz(rs.getString("stu_info_mz"));
		bean.setStu_info_sl(rs.getString("stu_info_sl"));
		bean.setStu_info_sg(rs.getString("stu_info_sg"));
		bean.setStu_info_tz(rs.getString("stu_info_tz"));
		bean.setStu_score_xk(rs.getString("stu_score_xk"));
		bean.setStu_score_cj(rs.getInt("stu_score_cj"));
		bean.setStu_score_wc(rs.getInt("stu_score_wc"));
		bean.setStatus(rs.getInt("status"));
		bean.setCreate_tm(rs.getTimestamp("create_tm"));
		bean.setNext_resp_tm(rs.getTimestamp("next_resp_tm"));
		bean.setStudent_acc_id(rs.getString("student_acc_id"));
		bean.setStudent_acc_pas(rs.getString("student_acc_pas"));
		
		bean.setExt_father_name(rs.getString("ext_father_name"));
		bean.setExt_mother_name(rs.getString("ext_mother_name"));
		bean.setExt_other_name(rs.getString("ext_other_name"));
		bean.setExt_contact(rs.getString("ext_contact"));
		bean.setExt_economics(rs.getString("ext_economics"));
		bean.setExt_remark(rs.getString("ext_remark"));
		bean.setLatest_search_sf(rs.getString("latest_search_sf"));
		
		return bean;

	}
	
	private static void closeAllConnection(Connection conn, PreparedStatement ps, ResultSet rs) {
		/**
		try {
			if (rs != null)
				rs.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (ps != null)
				ps.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (conn != null)
				conn.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		rs = null;
		ps = null;
		conn = null;
		*/
		DatabaseUtils.closeAllResources(rs, ps, conn);
	}

	// 根据动态条件查询计划院校信息
		public ResultVO getMajorJhByDynamicCondition(int jhYear, String sfCode, String xkCode, 
			    String pc, String pc_code, String zymc, String yxmc, int wcStart, int wcEnd, 
			    String majorScope, String indNature, int pageNumber) {
			    
			    Connection conn = null;
			    PreparedStatement ps = null;
			    ResultSet rs = null;
			    ResultVO resultVO = new ResultVO();
			    int recordCnt = 0;
			    List<JHBean> list = new ArrayList<>();
			    
			    try {
			        conn = DatabaseUtils.getConnection();
			        
			        // 构建查询SQL
			        StringBuilder sql = new StringBuilder();
			        sql.append("SELECT * FROM ").append(sfCode).append("_jh_").append(jhYear)
			           .append(" WHERE pc = ? AND pc_code = ? AND xk_code LIKE ? ")
			           .append(" AND zdfwc BETWEEN ? AND ? ")
			           .append(" AND ").append(majorScope).append(" = ? ");
			        
			        // 动态添加院校名称条件
			        if (!Tools.isEmpty(yxmc)) {
			            sql.append(" AND yxmc like ? ");
			        }
			        
			        // 动态添加办学性质条件
			        if (!Tools.isEmpty(indNature)) {
			            sql.append(" AND ind_nature like ? ");
			        }
			        
			        sql.append(" ORDER BY zdfwc ASC LIMIT ?,? ");
			        
			        Tools.println(sql.toString());
			        
			        // 执行查询
			        ps = conn.prepareStatement(sql.toString());
			        int paramIndex = 1;
			        ps.setString(paramIndex++, pc);
			        ps.setString(paramIndex++, pc_code);
			        ps.setString(paramIndex++, "%" + xkCode + "%");
			        ps.setInt(paramIndex++, wcEnd);
			        ps.setInt(paramIndex++, wcStart);
			        ps.setString(paramIndex++, zymc);
			        
			        // 动态设置院校名称参数
			        if (!Tools.isEmpty(yxmc)) {
			            ps.setString(paramIndex++, "%" + yxmc + "%");
			        }
			        
			        // 动态设置办学性质参数
			        if (!Tools.isEmpty(indNature)) {
			            ps.setString(paramIndex++, "%" + indNature + "%");
			        }
			        
			        ps.setInt(paramIndex++, (pageNumber - 1) * PAGE_ROW_CNT);
			        ps.setInt(paramIndex++, PAGE_ROW_CNT);
			        
			        rs = ps.executeQuery();
			        
			        SQLLogUtils.printSQL(" === getMajorJhByDynamicCondition: ", ps);
			        
			        while(rs.next()) {
			            list.add(setJHBeanAll(jhYear-1, rs));
			        }
			        
			        // 获取总记录数
			        StringBuilder countSql = new StringBuilder();
			        countSql.append("SELECT COUNT(*) as cnt FROM ").append(sfCode).append("_jh_").append(jhYear)
			                .append(" WHERE pc = ? AND pc_code = ? AND xk_code LIKE ? ")
			                .append(" AND zdfwc BETWEEN ? AND ? ")
			                .append(" AND ").append(majorScope).append(" = ? ");
			        
			        // 动态添加院校名称条件
			        if (!Tools.isEmpty(yxmc)) {
			            countSql.append(" AND yxmc like ? ");
			        }
			        
			        // 动态添加办学性质条件
			        if (!Tools.isEmpty(indNature)) {
			            countSql.append(" AND ind_nature like  ? ");
			        }
			        
			        ps = conn.prepareStatement(countSql.toString());
			        paramIndex = 1;
			        ps.setString(paramIndex++, pc);
			        ps.setString(paramIndex++, pc_code);
			        ps.setString(paramIndex++, "%" + xkCode + "%");
			        ps.setInt(paramIndex++, wcEnd);
			        ps.setInt(paramIndex++, wcStart);
			        ps.setString(paramIndex++, zymc);
			        
			        // 动态设置院校名称参数
			        if (!Tools.isEmpty(yxmc)) {
			            ps.setString(paramIndex++, "%" + yxmc + "%");
			        }
			        
			        // 动态设置办学性质参数
			        if (!Tools.isEmpty(indNature)) {
			            ps.setString(paramIndex++, "%" + indNature + "%");
			        }
			        
			        
			        rs = ps.executeQuery();
			        if(rs.next()) {
			            recordCnt = rs.getInt("cnt");
			        }
			        
			        // 设置返回结果
			        resultVO.setCurrentPage(pageNumber);
			        resultVO.setRecordCnt(recordCnt);
			        resultVO.setResult(list);
			        
			    } catch(Exception ex) {
			        ex.printStackTrace();
			    } finally {
			        closeAllConnection(conn, ps, rs);
			    }
			    
			    return resultVO;
			}
	
		
		
		// 查询
	    public ResultVO getLhyCheckerUserFillRequestByCid(String lhy_c_id, String xm, String sf, HashSet<String> statusSets, HashSet<String> pay_statusSets, int pageNumber) {
	        List<LhyCheckerUserFillRequest> list = new ArrayList<>();
	        Connection conn = null;
	        PreparedStatement pstmt = null;
	        ResultSet rs = null;
	        ResultVO resultVO = new ResultVO();
	        resultVO.setRecordCnt(0);
	        resultVO.setCurrentPage(pageNumber);
	        try {
	            conn = DatabaseUtils.getConnection();
	            String sql = "SELECT * FROM lhy_checker_user_fill_request x WHERE x.sf like ? and x.status in ("+Tools.getSQLQueryin(statusSets)+") and x.pay_status in ("+Tools.getSQLQueryin(pay_statusSets)+") and f_name like ? and lhy_c_id = ? order by create_tm asc limit ?,?";
	            String sql_CNT = "SELECT count(*) as cnt FROM lhy_checker_user_fill_request x WHERE x.sf like ? and x.status in ("+Tools.getSQLQueryin(statusSets)+")  and x.pay_status in ("+Tools.getSQLQueryin(pay_statusSets)+") and f_name like ? and lhy_c_id = ? ";
	            pstmt = conn.prepareStatement(sql);
	            pstmt.setString(1, "%"+sf+"%");
	            pstmt.setString(2, "%"+xm+"%");
	            pstmt.setString(3, lhy_c_id);
	            pstmt.setInt(4, (pageNumber - 1) * PAGE_ROW_CNT);
	            pstmt.setInt(5, PAGE_ROW_CNT);
	            rs = pstmt.executeQuery();
	            
	            while (rs.next()) {
	                list.add(resultSetToLhyCheckerUserFillRequest(rs));
	            }
	            pstmt.close();rs.close();
	            pstmt = null; rs = null;
	            
	            resultVO.setResult(list);
	            
	            
	            pstmt = conn.prepareStatement(sql_CNT);
	            pstmt.setString(1, "%"+sf+"%");
	            pstmt.setString(2, "%"+xm+"%");
	            pstmt.setString(3, lhy_c_id);
	            rs = pstmt.executeQuery();
	            while(rs.next()) {
	            	resultVO.setRecordCnt(rs.getInt("cnt"));
	            }
	            
	        } catch (SQLException e) {
	            e.printStackTrace();
	        } finally {
	            closeAllConnection(conn, pstmt, rs);
	        }
	        return resultVO;
	    }

	   

	    // 插入记录
	    public boolean insertLhyCheckerUserFillRequest(LhyCheckerUserFillRequest request) {
	        Connection conn = null;
	        PreparedStatement pstmt = null;
	        
	        try {
	            conn = DatabaseUtils.getConnection();
	            String sql = "INSERT INTO lhy_checker_user_fill_request(fid, lhy_c_id, f_name, f_phone, f_remark, " +
	                          "create_tm, expire_tm, fill_end_tm, fill_start_tm, checked_tm, send_tm, audio_url, status, pay_status, fm_batch_id,sf,xk,score) " +
	                          "VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?)";
	            pstmt = conn.prepareStatement(sql);
	            
	            pstmt.setString(1, request.getFid());
		        pstmt.setString(2, request.getLhy_c_id());
		        pstmt.setString(3, request.getF_name());
		        pstmt.setString(4, request.getF_phone());
		        pstmt.setString(5, request.getF_remark());
		        pstmt.setString(6, request.getF_open_id());
		        pstmt.setString(7, request.getF_from());
		        pstmt.setTimestamp(8, new Timestamp(request.getCreate_tm().getTime()));
		        pstmt.setTimestamp(9, request.getFill_end_tm() != null ? new Timestamp(request.getFill_end_tm().getTime()) : null);
		        pstmt.setTimestamp(10, request.getFill_start_tm() != null ? new Timestamp(request.getFill_start_tm().getTime()) : null);
		        pstmt.setTimestamp(11, request.getChecked_tm() != null ? 
		                new Timestamp(request.getChecked_tm().getTime()) : null);
		        pstmt.setTimestamp(12, request.getSend_tm() != null ? 
		                new Timestamp(request.getSend_tm().getTime()) : null);
		        pstmt.setTimestamp(13, request.getPaid_tm() != null ? 
		                new Timestamp(request.getPaid_tm().getTime()) : null);
		        pstmt.setString(14, request.getAudio_url());
		        pstmt.setInt(15, request.getStatus());
		        pstmt.setInt(16, request.getPay_status());
		        pstmt.setString(17, request.getFm_batch_id());
		        pstmt.setString(18, request.getSf());
		        pstmt.setString(19, request.getXk());
		        pstmt.setInt(20, request.getScore());
	            
	            return pstmt.executeUpdate() > 0;
	        } catch (SQLException e) {
	            e.printStackTrace();
	            return false;
	        } finally {
	        	closeAllConnection(conn, pstmt, null);
	        }
	    }

	    
	    /**
	     * 
	     * 查询审核专家所有审核过程记录
	     * 
	     * @param sf_table
	     * @param batch_id
	     * @return
	     * 
	     */
	    public List<LhyFormMainChecker> getFormMakerMainCheckerByBatchId(String sf_table, String batchId) {
	        List<LhyFormMainChecker> resultList = new ArrayList<>();
	        PreparedStatement pstmt = null;
	        Connection conn = null;
	        ResultSet rs = null;
	        
	        try {
	            conn = DatabaseUtils.getConnection();
	            String sql = "SELECT * FROM lhy_" + sf_table + "_form_maker_main_checker WHERE fm_id = ? ORDER BY create_tm DESC ";
	            pstmt = conn.prepareStatement(sql);
	            pstmt.setString(1, batchId);
	            rs = pstmt.executeQuery();
	            
	            while (rs.next()) {
	                LhyFormMainChecker checker = new LhyFormMainChecker();
	                checker.setMc_id(rs.getInt("mc_id"));
	                checker.setMc_result(rs.getInt("mc_result"));
	                checker.setFm_id(rs.getString("fm_id"));
	                checker.setC_id(rs.getString("c_id"));
	                checker.setRemark(rs.getString("remark"));
	                checker.setTo_checker_remark(rs.getString("to_checker_remark"));
	                checker.setCreate_tm(rs.getTimestamp("create_tm"));
	                resultList.add(checker);
	            }
	            SQLLogUtils.printSQL(" === getFormMakerMainCheckerByBatchId() : ", pstmt);
	            
	        } catch (Exception e) {
	            e.printStackTrace();
	        } finally {
	            closeAllConnection(conn, pstmt, rs);
	        }
	        return resultList;
	    }
	    
	    
	    /***
	     * 
	     * 查询审核专家最近一条审核记录
	     * 
	     * @param sf_table
	     * @param batch_id
	     * @return
	     * 
	     */
	    public LhyFormMainChecker getFormMakerMainCheckerLastByBatchId(String sf_table, String batchId) {
	        PreparedStatement pstmt = null;
	        Connection conn = null;
	        ResultSet rs = null;
	        LhyFormMainChecker checker = null;
	        try {
	            conn = DatabaseUtils.getConnection();
	            String sql = "SELECT * " +
	                         "FROM lhy_" + sf_table + "_form_maker_main_checker " +
	                         "WHERE fm_id = ? " +
	                         "ORDER BY create_tm DESC LIMIT 1";
	            pstmt = conn.prepareStatement(sql);
	            pstmt.setString(1, batchId);
	            rs = pstmt.executeQuery();
	            if (rs.next()) {
	                checker = new LhyFormMainChecker();
	                checker.setMc_id(rs.getInt("mc_id"));
	                checker.setMc_result(rs.getInt("mc_result"));
	                checker.setFm_id(rs.getString("fm_id"));
	                checker.setC_id(rs.getString("c_id"));
	                checker.setRemark(rs.getString("remark"));
	                checker.setTo_checker_remark(rs.getString("to_checker_remark"));
	                checker.setCreate_tm(rs.getTimestamp("create_tm"));
	            }
	            
	            SQLLogUtils.printSQL(" === getFormMakerMainCheckerLastByBatchId() : ", pstmt);
	            
	        } catch (Exception e) {
	            e.printStackTrace();
	        } finally {
	            closeAllConnection(conn, pstmt, rs);
	        }
	        return checker;
	    }
	    
	    /**
	     * 添加志愿表审核记录
	     * @param checker 审核记录对象
	     * @return 插入操作是否成功
	     */
	    public boolean addFormMakerMainChecker(String sf_table, LhyFormMainChecker checker) {
	        PreparedStatement pstmt = null;
	        Connection conn = null;
	        boolean result = false;
	        
	        try {
	        	conn = DatabaseUtils.getConnection();
	            String sql = "INSERT INTO lhy_"+sf_table+"_form_maker_main_checker (mc_result, fm_id, c_id, remark, to_checker_remark, create_tm) VALUES (?,?,?,?,?, NOW())";
	            pstmt = conn.prepareStatement(sql);
	            pstmt.setInt(1, checker.getMc_result());
	            pstmt.setString(2, checker.getFm_id());
	            pstmt.setString(3, checker.getC_id());
	            pstmt.setString(4, checker.getRemark());
	            pstmt.setString(5, checker.getTo_checker_remark());
	            
	            int affectedRows = pstmt.executeUpdate();
	            result = (affectedRows > 0);
	            
	            SQLLogUtils.printSQL(" === addFormMakerMainChecker() : ", pstmt);
	            
	        } catch (Exception e) {
	            e.printStackTrace();
	        } finally {
	        	closeAllConnection(conn, pstmt, null);
	        }
	        
	        return result;
	    }

	    /**
	     * 更新志愿表状态
	     * @param batch_id 批次ID
	     * @param status 状态：3-审核不通过，4-审核通过
	     * @return 更新操作是否成功
	     * 
	     */
	    public boolean updateFormMakerMainStatus(String sf_table, String batch_id, int status) {
	        PreparedStatement pstmt = null;
	        Connection conn = null;
	        boolean result = false;
	        
	        try {
	        	conn = DatabaseUtils.getConnection();
	            String sql = "UPDATE lhy_"+sf_table+"_form_maker_main SET status = ? WHERE batch_id = ?";
	            pstmt = conn.prepareStatement(sql);
	            pstmt.setInt(1, status);
	            pstmt.setString(2, batch_id);
	            
	            int affectedRows = pstmt.executeUpdate();
	            result = (affectedRows > 0);
	            
	            SQLLogUtils.printSQL(" === updateFormMakerMainStatus() : ", pstmt);
	            
	        } catch (Exception e) {
	            e.printStackTrace();
	        } finally {
	        	closeAllConnection(conn, pstmt, null);
	        }
	        
	        return result;
	    }
	    
	    /**
	     * 
	     * 记录志愿历史
	     * 
	     * @param sf_table
	     * @param history_id
	     * @param batch_id
	     * @return
	     * 
	     */
	    public boolean insertLhyFormMakerToHistory(String sf_table, String history_id, String batch_id) {
	        Connection conn = null;
	        PreparedStatement pstmt = null;
	        
	        try {
	            conn = DatabaseUtils.getConnection();
	            String sql_main = "INSERT INTO lhy_"+sf_table+"_form_maker_main_history(history_id, stored_tm, batch_id, score_cj, score_wc, score_xk, f_no, order_id, selected_zymc, pc, pc_code, nf, create_tm, last_update_tm, joined_checker_cnt, last_checker_id, last_checker_name, last_checker_remark, status, f_type, form_name) "
	            		+ "SELECT ?, NOW(), batch_id, score_cj, score_wc, score_xk, f_no, order_id, selected_zymc, pc, pc_code, nf, create_tm, last_update_tm, joined_checker_cnt, last_checker_id, last_checker_name, last_checker_remark, status, f_type, form_name FROM lhy_"+sf_table+"_form_maker_main x WHERE x.batch_id = ?";
	            pstmt = conn.prepareStatement(sql_main);
	            pstmt.setString(1, history_id);
	            pstmt.setString(2, batch_id);
	            pstmt.executeUpdate();
	            
	            SQLLogUtils.printSQL(" === insertLhyFormMakerToHistory() form_maker_main_history: ", pstmt);
	            
	            String sql_maker = "INSERT INTO lhy_"+sf_table+"_form_maker_history(history_id, batch_id, batch_id_org, yxmc, yxmc_org, yxbz, yxdm, zyz, zymc, zymc_org, zybz, zydm, zdf_a, zdf_b, zdf_c, zdfwc_a, zdfwc_b, zdfwc_c, pjf_a, pjf_b, pjf_c, pjfwc_a, pjfwc_b, pjfwc_c, seq_no_zy, seq_no_yx, jhs_a, jhs_b, jhs_c, zgf_a, zgf_b, zgf_c, zgfwc_a, zgfwc_b, zgfwc_c, jhs, fee) "
	            		+ "SELECT ?, batch_id, batch_id_org, yxmc, yxmc_org, yxbz, yxdm, zyz, zymc, zymc_org, zybz, zydm, zdf_a, zdf_b, zdf_c, zdfwc_a, zdfwc_b, zdfwc_c, pjf_a, pjf_b, pjf_c, pjfwc_a, pjfwc_b, pjfwc_c, seq_no_zy, seq_no_yx, jhs_a, jhs_b, jhs_c, zgf_a, zgf_b, zgf_c, zgfwc_a, zgfwc_b, zgfwc_c, jhs, fee FROM lhy_"+sf_table+"_form_maker x WHERE x.batch_id_org = ?;";
	            pstmt = conn.prepareStatement(sql_maker);
	            pstmt.setString(1, history_id);
	            pstmt.setString(2, batch_id);
	            pstmt.executeUpdate();
	            
	            SQLLogUtils.printSQL(" === insertLhyFormMakerToHistory() form_maker_history: ", pstmt);
	        } catch (SQLException e) {
	            e.printStackTrace();
	            return false;
	        } finally {
	        	closeAllConnection(conn, pstmt, null);
	        }
	        return true;
	    }
	    
	    /**
		 * 
		 * @param sfCode
		 * @param batchId
		 * @return
		 * 
		 */
		public List<LhyFormMakerMainHistory> getMakerFormMainHistoryByBatchId(String sfCode, String batchId) {
			Connection conn = null;
			PreparedStatement ps = null;
			ResultSet rs = null;
			List<LhyFormMakerMainHistory> list = new ArrayList<>();
			
			try {
				conn = DatabaseUtils.getConnection();
				String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker_main_history x WHERE batch_id = ? ORDER BY stored_seq_no DESC";

				ps = conn.prepareStatement(SQL);
				ps.setString(1, batchId);
				rs = ps.executeQuery();
				
				SQLLogUtils.printSQL(" === getMakerFormMainByBatchId() : ", ps);
		
				while (rs.next()) {
					list.add(setLhyFormMakerMainHistory(rs));
				}
			} catch (Exception ex) {
				ex.printStackTrace();
			} finally {
				closeAllConnection(conn, ps, rs);
			}
			return list;
		}
		
		public LhyFormMakerMainHistory getMakerFormMainHistoryByHistoryId(String sfCode, String history) {
			Connection conn = null;
			PreparedStatement ps = null;
			ResultSet rs = null;
			
			try {
				conn = DatabaseUtils.getConnection();
				String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker_main_history x WHERE history_id = ? ORDER BY stored_seq_no DESC";

				ps = conn.prepareStatement(SQL);
				ps.setString(1, history);
				rs = ps.executeQuery();
				
				SQLLogUtils.printSQL(" === getMakerFormMainByBatchId() : ", ps);
		
				while (rs.next()) {
					return setLhyFormMakerMainHistory(rs);
				}
			} catch (Exception ex) {
				ex.printStackTrace();
			} finally {
				closeAllConnection(conn, ps, rs);
			}
			return null;
		}
		
		/**
		 * 
		 * @param sfCode
		 * @param batchId
		 * @return
		 * 
		 */
		public List<LhyFormMakerMainHistory> getMakerFormMainHistoryByOrderId(String sfCode, String order_id) {
			Connection conn = null;
			PreparedStatement ps = null;
			ResultSet rs = null;
			List<LhyFormMakerMainHistory> list = new ArrayList<>();
			
			try {
				conn = DatabaseUtils.getConnection();
				String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker_main_history x WHERE order_id = ? ORDER BY stored_tm DESC, stored_seq_no DESC";

				ps = conn.prepareStatement(SQL);
				ps.setString(1, order_id);
				rs = ps.executeQuery();
				
				SQLLogUtils.printSQL(" === getMakerFormMainHistoryByOrderId() : ", ps);
		
				while (rs.next()) {
					list.add(setLhyFormMakerMainHistory(rs));
				}
			} catch (Exception ex) {
				ex.printStackTrace();
			} finally {
				closeAllConnection(conn, ps, rs);
			}
			return list;
		}
		
		public void finishMakerFormMainWithBatchid(int nf, String sfCode, String batch_id, String status, String remark) {
		    Connection conn = null;
		    PreparedStatement ps = null;
		    ResultSet rs = null;
		    
		    try {
		        conn = DatabaseUtils.getConnection();
		        String SQL = "update lhy_" + sfCode + "_form_maker_main set status = ?, to_checker_remark = ? where batch_id = ?";
		        ps = conn.prepareStatement(SQL);
		        ps.setString(1, status);
		        ps.setString(2, remark);
		        ps.setString(3, batch_id);
		        //ps.setInt(4, nf); NO NEED
		        ps.executeUpdate();
		        
		        SQLLogUtils.printSQL(" === finishMakerFormMainWithBatchid() : ", ps);
		        
		    } catch (Exception ex) {
		        ex.printStackTrace();
		    } finally {
		        closeAllConnection(conn, ps, rs);
		    }
		}
		
		/**
		 * 
		 * @param sfCode
		 * @param historyId
		 * @return
		 * 
		 */
		public List<LhyFormMakerHistory> getLhyFormMakerHistory(String sfCode, String historyId) {
			Connection conn = null;
			PreparedStatement ps = null;
			ResultSet rs = null;
			List<LhyFormMakerHistory> list = new ArrayList<>();
			
			try {
				conn = DatabaseUtils.getConnection();
				
				String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker_history x WHERE history_id = ? ORDER BY seq_no_yx, seq_no_zy";

				ps = conn.prepareStatement(SQL);
				ps.setString(1, historyId);
				rs = ps.executeQuery();

				SQLLogUtils.printSQL(" === getLhyFormMakerHistory() : ", ps);
				
				while (rs.next()) {
					list.add(setLhyFormMakerHistory(rs));
				}
			} catch (Exception ex) {
				ex.printStackTrace();
			} finally {
				closeAllConnection(conn, ps, rs);
			}
			return list;
		}
	    
	  
	 // 将ResultSet转换为实体对象
	    private static LhyCheckerUserFillRequest resultSetToLhyCheckerUserFillRequest(ResultSet rs) throws SQLException {
	        LhyCheckerUserFillRequest request = new LhyCheckerUserFillRequest();
	        request.setFid(rs.getString("fid"));
	        request.setLhy_c_id(rs.getString("lhy_c_id"));
	        request.setF_name(rs.getString("f_name"));
	        request.setF_phone(rs.getString("f_phone"));
	        request.setF_remark(rs.getString("f_remark"));
	        request.setF_open_id(rs.getString("f_open_id"));
	        request.setF_from(rs.getString("f_from"));
	        request.setCreate_tm(rs.getTimestamp("create_tm"));
	        request.setFill_end_tm(rs.getTimestamp("fill_end_tm"));
	        request.setFill_start_tm(rs.getTimestamp("fill_start_tm"));
	        request.setChecked_tm(rs.getTimestamp("checked_tm"));
	        request.setSend_tm(rs.getTimestamp("send_tm"));
	        request.setPaid_tm(rs.getTimestamp("paid_tm"));
	        request.setAudio_url(rs.getString("audio_url"));
	        request.setStatus(rs.getInt("status"));
	        request.setPay_status(rs.getInt("pay_status"));
	        request.setFm_batch_id(rs.getString("fm_batch_id"));
	        request.setSf(rs.getString("sf"));
	        request.setXk(rs.getString("xk"));
	        request.setScore(rs.getInt("score"));
	        return request;
	    }

	    // 设置PreparedStatement参数
	    private static void setPreparedStatement(PreparedStatement pstmt, LhyCheckerUserFillRequest request) throws SQLException {
	        pstmt.setString(1, request.getFid());
	        pstmt.setString(2, request.getLhy_c_id());
	        pstmt.setString(3, request.getF_name());
	        pstmt.setString(4, request.getF_phone());
	        pstmt.setString(5, request.getF_remark());
	        pstmt.setString(6, request.getF_open_id());
	        pstmt.setString(7, request.getF_from());
	        pstmt.setTimestamp(8, new Timestamp(request.getCreate_tm().getTime()));
	        pstmt.setTimestamp(9, request.getFill_end_tm() != null ? 
	                new Timestamp(request.getFill_end_tm().getTime()) : null);
	        pstmt.setTimestamp(10, request.getFill_start_tm() != null ? 
	                new Timestamp(request.getFill_start_tm().getTime()) : null);
	        pstmt.setTimestamp(11, request.getChecked_tm() != null ? 
	                new Timestamp(request.getChecked_tm().getTime()) : null);
	        pstmt.setTimestamp(12, request.getSend_tm() != null ? 
	                new Timestamp(request.getSend_tm().getTime()) : null);
	        pstmt.setTimestamp(13, request.getPaid_tm() != null ? 
	                new Timestamp(request.getPaid_tm().getTime()) : null);
	        pstmt.setString(14, request.getAudio_url());
	        pstmt.setInt(15, request.getStatus());
	        pstmt.setInt(16, request.getPay_status());
	        pstmt.setString(17, request.getFm_batch_id());
	    }
	    
	    private LhyFormMakerMainHistory setLhyFormMakerMainHistory(ResultSet rs) throws SQLException {
			
			LhyFormMakerMainHistory bean = new LhyFormMakerMainHistory();
			
			bean.setHistoryId(rs.getString("history_Id"));
			bean.setStoredTm(rs.getTimestamp("stored_tm"));
			bean.setStoredSeqNo(rs.getInt("stored_seq_no"));
			bean.setBatchId(rs.getString("batch_id"));
			bean.setScoreCj(rs.getInt("score_cj"));
			bean.setScoreWc(rs.getInt("score_wc"));
			bean.setScoreXk(rs.getString("score_xk"));
			bean.setfNo(rs.getString("f_no"));
			bean.setOrderId(rs.getString("order_id"));
			bean.setSelectedZymc(rs.getString("selected_zymc"));
			bean.setPc(rs.getString("pc"));
			bean.setPc_code(rs.getString("pc_code"));
			bean.setNf(rs.getInt("nf"));
			bean.setCreateTm(rs.getTimestamp("create_tm"));
			bean.setLastUpdateTm(rs.getTimestamp("last_update_tm"));
			bean.setJoinedCheckerCnt(rs.getInt("joined_checker_cnt"));
			bean.setLastCheckerId(rs.getString("last_checker_id"));
			bean.setLastCheckerName(rs.getString("last_checker_name"));
			bean.setLastCheckerRemark(rs.getString("last_checker_remark"));
			bean.setStatus(rs.getInt("status"));
			bean.setfType(rs.getInt("f_type"));
			bean.setFormName(rs.getString("form_name"));
			
			return bean;

		}
	    
	    public List<LhyForm> getMakerFormWithSort(String sfCode, String batch_id_org, String AscOrDesc) {
			Connection conn = null;
			PreparedStatement ps = null;
			ResultSet rs = null;
			List<LhyForm> list = new ArrayList<>();
			try {
				conn = DatabaseUtils.getConnection();
				String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker x WHERE batch_id_org = ? ORDER BY zdfwc_a "+AscOrDesc+", zdfwc_b "+AscOrDesc+", zdfwc_c "+AscOrDesc+", pjfwc_a "+AscOrDesc+", pjfwc_b "+AscOrDesc+", pjfwc_c "+AscOrDesc+"";
				ps = conn.prepareStatement(SQL);
				ps.setString(1, batch_id_org);
				rs = ps.executeQuery();
				SQLLogUtils.printSQL(" === getMakerFormMainByBatchId() : ", ps);
				while (rs.next()) {
					list.add(setMakerFormAll(rs));
				}
			} catch (Exception ex) {
				ex.printStackTrace();
			} finally {
				closeAllConnection(conn, ps, rs);
			}
			return list;
		}
		
	    public List<LhyForm> getMakerFormWithQSFSort(int nf, String sfCode, String batch_id_org, String pc, String pc_code, String xk_code) {
			Connection conn = null;
			PreparedStatement ps = null;
			ResultSet rs = null;
			List<LhyForm> list = new ArrayList<>();
			try {
				conn = DatabaseUtils.getConnection();
				String SQL = "SELECT x.*, y.qsf FROM lhy_"+sfCode+"_form_maker x "
						+ "LEFT JOIN "+sfCode+"_jh_"+nf+" y ON x.yxdm = y.yxdm AND x.zyz = y.zyz AND x.zydm = y.zydm "
						+ "WHERE x.batch_id_org = ? AND y.pc = ? AND y.pc_code = ? AND y.xk_code like ? ORDER BY y.qsf > 0, y.qsf DESC";
				ps = conn.prepareStatement(SQL);
				ps.setString(1, batch_id_org);
				ps.setString(2, pc);
				ps.setString(3, pc_code);
				ps.setString(4, "%" + xk_code + "%");
				rs = ps.executeQuery();
				LhyForm lhyForm = null;
				SQLLogUtils.printSQL(" === getMakerFormMainByBatchId() : ", ps);
				while (rs.next()) {
					lhyForm = setMakerFormAll(rs);
					lhyForm.setExt_jh_qsf(rs.getInt("qsf"));
					list.add(lhyForm);
				}
			} catch (Exception ex) {
				ex.printStackTrace();
			} finally {
				closeAllConnection(conn, ps, rs);
			}
			return list;
		}
	    
	    public List<LhyForm> getMakerFormWithLatestYearWcSort(int nf, String sfCode, String batch_id_org, String pc, String pc_code, String xk_code) {
			Connection conn = null;
			PreparedStatement ps = null;
			ResultSet rs = null;
			int dataYear = nf - 1;
			List<LhyForm> list = new ArrayList<>();
			try {
				conn = DatabaseUtils.getConnection();
				String SQL = "SELECT x.*, y.qsf FROM lhy_"+sfCode+"_form_maker x "
						+ "LEFT JOIN "+sfCode+"_jh_"+nf+" y ON x.yxdm = y.yxdm AND x.zyz = y.zyz AND x.zydm = y.zydm "
						+ "WHERE x.batch_id_org = ? AND y.pc = ? AND y.pc_code = ? AND y.xk_code like ? "
						+ "ORDER BY y.zdfwc_"+dataYear+" IS NULL, CAST(COALESCE(y.zdfwc_"+(dataYear)+",99999999) AS SIGNED) > 0, "
						+ "CAST(COALESCE(y.zdfwc_"+(dataYear)+",99999999) AS SIGNED) ASC, CAST(COALESCE(y.zdfwc_"+(dataYear-1)+",99999999) AS SIGNED) ASC, CAST(COALESCE(y.zdfwc_"+(dataYear-2)+",99999999) AS SIGNED) ASC ";
				ps = conn.prepareStatement(SQL);
				ps.setString(1, batch_id_org);
				ps.setString(2, pc);
				ps.setString(3, pc_code);
				ps.setString(4, "%" + xk_code + "%");
				rs = ps.executeQuery();
				LhyForm lhyForm = null;
				SQLLogUtils.printSQL(" === getMakerFormMainByBatchId() : ", ps);
				while (rs.next()) {
					lhyForm = setMakerFormAll(rs);
					lhyForm.setExt_jh_qsf(rs.getInt("qsf"));
					list.add(lhyForm);
				}
			} catch (Exception ex) {
				ex.printStackTrace();
			} finally {
				closeAllConnection(conn, ps, rs);
			}
			return list;
		}
		
		private LhyFormMakerHistory setLhyFormMakerHistory(ResultSet rs) throws SQLException {
			
			LhyFormMakerHistory bean = new LhyFormMakerHistory();
			
			bean.setId(rs.getInt("id"));
			bean.setHistoryId(rs.getString("history_id"));
			bean.setBatchId(rs.getString("batch_id"));
			bean.setBatchIdOrg(rs.getString("batch_id_org"));
			bean.setYxmc(rs.getString("yxmc"));
			bean.setYxmcOrg(rs.getString("yxmc_org"));
			bean.setYxbz(rs.getString("yxbz"));
			bean.setYxdm(rs.getString("yxdm"));
			bean.setZyz(rs.getString("zyz"));
			bean.setZymc(rs.getString("zymc"));
			bean.setZymcOrg(rs.getString("zymc_org"));
			bean.setZybz(rs.getString("zybz"));
			bean.setZydm(rs.getString("zydm"));
			bean.setZdfA(rs.getInt("zdf_a"));
			bean.setZdfB(rs.getInt("zdf_b"));
			bean.setZdfC(rs.getInt("zdf_c"));
			bean.setZdfwcA(rs.getInt("zdfwc_a"));
			bean.setZdfwcB(rs.getInt("zdfwc_b"));
			bean.setZdfwcC(rs.getInt("zdfwc_c"));
			bean.setPjfA(rs.getInt("pjf_a"));
			bean.setPjfB(rs.getInt("pjf_b"));
			bean.setPjfC(rs.getInt("pjf_c"));
			bean.setPjfwcA(rs.getInt("pjfwc_a"));
			bean.setPjfwcB(rs.getInt("pjfwc_b"));
			bean.setPjfwcC(rs.getInt("pjfwc_c"));
			bean.setSeqNoZy(rs.getInt("seq_no_zy"));
			bean.setSeqNoYx(rs.getInt("seq_no_yx"));

			return bean;
		}
		
		/**
	     * 插入RtFillNeedsInfoBean对象到数据库
	     *
	     * @param form RtFillNeedsInfoBean对象
	     * @return 插入成功返回true，否则返回false
	     */
	    public boolean insertRtFillNeedsInfo(RtFillNeedsInfoBean form) {
	        Connection conn = null;
	        PreparedStatement pstmt = null;

	        String sql = "INSERT INTO lhy_rt_fill_needs_info (" +
	                "rt_id, rt_user_id, rt_agent_id, rt_expert_id, rt_order_price_org, rt_order_price_discount, rt_order_price, " +
	                "rt_province, rt_name, rt_phone, rt_gender, rt_graduate_status, rt_political_status, rt_ethnicity, " +
	                "rt_height, rt_weight, rt_body_type, rt_drawing_skill, rt_hand_habit, rt_physical_exam, " +
	                "rt_subject, rt_foreign_language, rt_score_chinese, rt_score_math, " +
	                "rt_score_foreign_language, rt_score_a, rt_score_b, rt_score_c, rt_total_score, rt_score_ext, " +
	                "rt_ranking, rt_interview, rt_is_eligible_for_special_plans, rt_accept_directed_plans, rt_university_budget, " +
	                "rt_deep_planning, rt_accept_sino_foreign, rt_accept_private, rt_strategy, rt_preferred_cities, " +
	                "rt_preferred_provinces, rt_major_level, rt_major_category, rt_major_subcategory, rt_preferred_majors, " +
	                "rt_status, rt_create_time, rt_submit_time) " +
	                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";

	        try {
	            conn = DatabaseUtils.getConnection();
	            pstmt = conn.prepareStatement(sql);
	            int index = 1;

	            pstmt.setString(index++, form.getRt_id());
	            pstmt.setString(index++, form.getRt_user_id());
	            pstmt.setString(index++, form.getRt_agent_id());
	            pstmt.setString(index++, form.getRt_expert_id());
	            pstmt.setObject(index++, form.getRt_order_price_org());
	            pstmt.setObject(index++, form.getRt_order_price_discount());
	            pstmt.setObject(index++, form.getRt_order_price());
	            pstmt.setString(index++, form.getRt_province());
	            pstmt.setString(index++, form.getRt_name());
	            pstmt.setString(index++, form.getRt_phone());
	            pstmt.setString(index++, form.getRt_gender());
	            pstmt.setString(index++, form.getRt_graduate_status());
	            pstmt.setString(index++, form.getRt_political_status());
	            pstmt.setString(index++, form.getRt_ethnicity());
	            pstmt.setString(index++, form.getRt_height());
	            pstmt.setString(index++, form.getRt_weight());
	            pstmt.setString(index++, form.getRt_body_type());
	            pstmt.setString(index++, form.getRt_drawing_skill());
	            pstmt.setString(index++, form.getRt_hand_habit());
	            pstmt.setString(index++, form.getRt_physical_exam());
	            pstmt.setString(index++, form.getRt_subject());
	            pstmt.setString(index++, form.getRt_foreign_language());
	            pstmt.setObject(index++, form.getRt_score_chinese());
	            pstmt.setObject(index++, form.getRt_score_math());
	            pstmt.setObject(index++, form.getRt_score_foreign_language());
	            pstmt.setObject(index++, form.getRt_score_a());
	            pstmt.setObject(index++, form.getRt_score_b());
	            pstmt.setObject(index++, form.getRt_score_c());
	            pstmt.setObject(index++, form.getRt_total_score());
	            pstmt.setObject(index++, form.getRt_score_ext());
	            pstmt.setObject(index++, form.getRt_ranking());
	            pstmt.setString(index++, form.getRt_interview());
	            pstmt.setString(index++, form.getRt_is_eligible_for_special_plans());
	            pstmt.setString(index++, form.getRt_accept_directed_plans());
	            pstmt.setString(index++, form.getRt_university_budget());
	            pstmt.setString(index++, form.getRt_deep_planning());
	            pstmt.setString(index++, form.getRt_accept_sino_foreign());
	            pstmt.setString(index++, form.getRt_accept_private());
	            pstmt.setString(index++, form.getRt_strategy());
	            pstmt.setString(index++, form.getRt_preferred_cities());
	            pstmt.setString(index++, form.getRt_preferred_provinces());
	            pstmt.setString(index++, form.getRt_major_level());
	            pstmt.setString(index++, form.getRt_major_category());
	            pstmt.setString(index++, form.getRt_major_subcategory());
	            pstmt.setString(index++, form.getRt_preferred_majors());
	            pstmt.setInt(index++, form.getRt_status() != null ? form.getRt_status() : 10); // 默认状态为10

	            int rowsAffected = pstmt.executeUpdate();

	            SQLLogUtils.printSQL(" ===insertRtFillNeedsInfo : ", pstmt);

	            return rowsAffected > 0;
	        } catch (SQLException e) {
	            e.printStackTrace();
	            return false;
	        } finally {
	            closeAllConnection(conn, pstmt, null);
	        }
	    }

	    /**
	     * 更新RtFillNeedsInfoBean对象到数据库
	     *
	     * @param form RtFillNeedsInfoBean对象
	     * @return 更新成功返回true，否则返回false
	     */
	    public boolean updateRtFillNeedsInfo(RtFillNeedsInfoBean form) {
	        Connection conn = null;
	        PreparedStatement pstmt = null;

	        String sql = "UPDATE lhy_rt_fill_needs_info SET " +
	                "rt_user_id = ?, rt_agent_id = ?, rt_expert_id = ?, rt_order_price_org = ?, rt_order_price_discount = ?, " +
	                "rt_order_price = ?, rt_province = ?, rt_name = ?, rt_phone = ?, rt_gender = ?, rt_graduate_status = ?, " +
	                "rt_political_status = ?, rt_ethnicity = ?, rt_height = ?, rt_weight = ?, rt_body_type = ?, " +
	                "rt_drawing_skill = ?, rt_hand_habit = ?, rt_physical_exam = ?, rt_subject = ?, " +
	                "rt_foreign_language = ?, rt_score_chinese = ?, rt_score_math = ?, " +
	                "rt_score_foreign_language = ?, rt_score_a = ?, rt_score_b = ?, rt_score_c = ?, " +
	                "rt_total_score = ?, rt_score_ext = ?, rt_ranking = ?, rt_interview = ?, rt_is_eligible_for_special_plans = ?, " +
	                "rt_accept_directed_plans = ?, rt_university_budget = ?, rt_deep_planning = ?, rt_accept_sino_foreign = ?, " +
	                "rt_accept_private = ?, rt_strategy = ?, rt_preferred_cities = ?, rt_preferred_provinces = ?, " +
	                "rt_major_level = ?, rt_major_category = ?, rt_major_subcategory = ?, rt_preferred_majors = ?, " +
	                "rt_status = ?, rt_update_time = NOW(), rt_submit_time = ? " +
	                "WHERE rt_id = ?";

	        try {
	            conn = DatabaseUtils.getConnection();
	            pstmt = conn.prepareStatement(sql);
	            int index = 1;

	            pstmt.setString(index++, form.getRt_user_id());
	            pstmt.setString(index++, form.getRt_agent_id());
	            pstmt.setString(index++, form.getRt_expert_id());
	            pstmt.setObject(index++, form.getRt_order_price_org());
	            pstmt.setObject(index++, form.getRt_order_price_discount());
	            pstmt.setObject(index++, form.getRt_order_price());
	            pstmt.setString(index++, form.getRt_province());
	            pstmt.setString(index++, form.getRt_name());
	            pstmt.setString(index++, form.getRt_phone());
	            pstmt.setString(index++, form.getRt_gender());
	            pstmt.setString(index++, form.getRt_graduate_status());
	            pstmt.setString(index++, form.getRt_political_status());
	            pstmt.setString(index++, form.getRt_ethnicity());
	            pstmt.setString(index++, form.getRt_height());
	            pstmt.setString(index++, form.getRt_weight());
	            pstmt.setString(index++, form.getRt_body_type());
	            pstmt.setString(index++, form.getRt_drawing_skill());
	            pstmt.setString(index++, form.getRt_hand_habit());
	            pstmt.setString(index++, form.getRt_physical_exam());
	            pstmt.setString(index++, form.getRt_subject());
	            pstmt.setString(index++, form.getRt_foreign_language());
	            pstmt.setObject(index++, form.getRt_score_chinese());
	            pstmt.setObject(index++, form.getRt_score_math());
	            pstmt.setObject(index++, form.getRt_score_foreign_language());
	            pstmt.setObject(index++, form.getRt_score_a());
	            pstmt.setObject(index++, form.getRt_score_b());
	            pstmt.setObject(index++, form.getRt_score_c());
	            pstmt.setObject(index++, form.getRt_total_score());
	            pstmt.setObject(index++, form.getRt_score_ext());
	            pstmt.setObject(index++, form.getRt_ranking());
	            pstmt.setString(index++, form.getRt_interview());
	            pstmt.setString(index++, form.getRt_is_eligible_for_special_plans());
	            pstmt.setString(index++, form.getRt_accept_directed_plans());
	            pstmt.setString(index++, form.getRt_university_budget());
	            pstmt.setString(index++, form.getRt_deep_planning());
	            pstmt.setString(index++, form.getRt_accept_sino_foreign());
	            pstmt.setString(index++, form.getRt_accept_private());
	            pstmt.setString(index++, form.getRt_strategy());
	            pstmt.setString(index++, form.getRt_preferred_cities());
	            pstmt.setString(index++, form.getRt_preferred_provinces());
	            pstmt.setString(index++, form.getRt_major_level());
	            pstmt.setString(index++, form.getRt_major_category());
	            pstmt.setString(index++, form.getRt_major_subcategory());
	            pstmt.setString(index++, form.getRt_preferred_majors());
	            pstmt.setInt(index++, form.getRt_status() != null ? form.getRt_status() : 10);
	            pstmt.setTimestamp(index++, form.getRt_submit_time());
	            pstmt.setString(index++, form.getRt_id()); // WHERE 条件

	            int rowsAffected = pstmt.executeUpdate();

	            SQLLogUtils.printSQL(" ===updateRtFillNeedsInfo : ", pstmt);

	            return rowsAffected > 0;
	        } catch (SQLException e) {
	            e.printStackTrace();
	            return false;
	        } finally {
	            closeAllConnection(conn, pstmt, null);
	        }
	    }

	    /**
	     * 根据手机号查询lhy_rt_fill_needs_info表数据 (返回列表)
	     *
	     * @param rtPhone 手机号码
	     * @return 匹配到的 RtFillNeedsInfoBean 对象列表，如果没有找到则返回空列表
	     */
	    public List<RtFillNeedsInfoBean> getRtFillNeedsInfosByPhone(String rtPhone) {
	        Connection conn = null;
	        PreparedStatement pstmt = null;
	        ResultSet rs = null;
	        List<RtFillNeedsInfoBean> formList = new ArrayList<>();

	        String sql = "SELECT rt_id, rt_user_id, rt_agent_id, rt_expert_id, rt_order_price_org, rt_order_price_discount, " +
	                     "rt_order_price, rt_province, rt_name, rt_phone, rt_gender, rt_graduate_status, rt_political_status, " +
	                     "rt_ethnicity, rt_height, rt_weight, rt_body_type, rt_drawing_skill, rt_hand_habit, rt_physical_exam, " +
	                     "rt_subject, rt_foreign_language, rt_score_chinese, rt_score_math, " +
	                     "rt_score_foreign_language, rt_score_a, rt_score_b, rt_score_c, rt_total_score, rt_score_ext, " +
	                     "rt_ranking, rt_interview, rt_is_eligible_for_special_plans, rt_accept_directed_plans, rt_university_budget, " +
	                     "rt_deep_planning, rt_accept_sino_foreign, rt_accept_private, rt_strategy, rt_preferred_cities, " +
	                     "rt_preferred_provinces, rt_major_level, rt_major_category, rt_major_subcategory, rt_preferred_majors, " +
	                     "rt_status, rt_create_time, rt_update_time, rt_submit_time " +
	                     "FROM lhy_rt_fill_needs_info WHERE rt_phone = ?";

	        try {
	            conn = DatabaseUtils.getConnection();
	            pstmt = conn.prepareStatement(sql);
	            pstmt.setString(1, rtPhone);

	            rs = pstmt.executeQuery();

	            SQLLogUtils.printSQL(" ===getRtFillNeedsInfosByPhone : ", pstmt);

	            while (rs.next()) {
	                formList.add(populateRtFillNeedsInfoFromResultSet(rs));
	            }

	        } catch (SQLException e) {
	            e.printStackTrace();
	        } finally {
	            closeAllConnection(conn, pstmt, rs);
	        }

	        return formList;
	    }

	    /**
	     * 从ResultSet中填充RtFillNeedsInfoBean对象
	     *
	     * @param rs ResultSet对象
	     * @return 填充好的RtFillNeedsInfoBean对象
	     * @throws SQLException 如果从ResultSet中读取数据出错
	     */
	    private RtFillNeedsInfoBean populateRtFillNeedsInfoFromResultSet(ResultSet rs) throws SQLException {
	        RtFillNeedsInfoBean form = new RtFillNeedsInfoBean();
	        form.setRt_id(rs.getString("rt_id"));
	        form.setRt_user_id(rs.getString("rt_user_id"));
	        form.setRt_agent_id(rs.getString("rt_agent_id"));
	        form.setRt_expert_id(rs.getString("rt_expert_id"));
	        form.setRt_order_price_org((Integer) rs.getObject("rt_order_price_org"));
	        form.setRt_order_price_discount((Integer) rs.getObject("rt_order_price_discount"));
	        form.setRt_order_price((Integer) rs.getObject("rt_order_price"));
	        form.setRt_province(rs.getString("rt_province"));
	        form.setRt_name(rs.getString("rt_name"));
	        form.setRt_phone(rs.getString("rt_phone"));
	        form.setRt_gender(rs.getString("rt_gender"));
	        form.setRt_graduate_status(rs.getString("rt_graduate_status"));
	        form.setRt_political_status(rs.getString("rt_political_status"));
	        form.setRt_ethnicity(rs.getString("rt_ethnicity"));
	        form.setRt_height(rs.getString("rt_height"));
	        form.setRt_weight(rs.getString("rt_weight"));
	        form.setRt_body_type(rs.getString("rt_body_type"));
	        form.setRt_drawing_skill(rs.getString("rt_drawing_skill"));
	        form.setRt_hand_habit(rs.getString("rt_hand_habit"));
	        form.setRt_physical_exam(rs.getString("rt_physical_exam"));
	        form.setRt_subject(rs.getString("rt_subject"));
	        form.setRt_foreign_language(rs.getString("rt_foreign_language"));
	        form.setRt_score_chinese((Integer) rs.getObject("rt_score_chinese"));
	        form.setRt_score_math((Integer) rs.getObject("rt_score_math"));
	        form.setRt_score_foreign_language((Integer) rs.getObject("rt_score_foreign_language"));
	        form.setRt_score_a((Integer) rs.getObject("rt_score_a"));
	        form.setRt_score_b((Integer) rs.getObject("rt_score_b"));
	        form.setRt_score_c((Integer) rs.getObject("rt_score_c"));
	        form.setRt_total_score((Integer) rs.getObject("rt_total_score"));
	        form.setRt_score_ext((Integer) rs.getObject("rt_score_ext"));
	        form.setRt_ranking((Integer) rs.getObject("rt_ranking"));
	        form.setRt_interview(rs.getString("rt_interview"));
	        form.setRt_is_eligible_for_special_plans(rs.getString("rt_is_eligible_for_special_plans"));
	        form.setRt_accept_directed_plans(rs.getString("rt_accept_directed_plans"));
	        form.setRt_university_budget(rs.getString("rt_university_budget"));
	        form.setRt_deep_planning(rs.getString("rt_deep_planning"));
	        form.setRt_accept_sino_foreign(rs.getString("rt_accept_sino_foreign"));
	        form.setRt_accept_private(rs.getString("rt_accept_private"));
	        form.setRt_strategy(rs.getString("rt_strategy"));
	        form.setRt_preferred_cities(rs.getString("rt_preferred_cities"));
	        form.setRt_preferred_provinces(rs.getString("rt_preferred_provinces"));
	        form.setRt_major_level(rs.getString("rt_major_level"));
	        form.setRt_major_category(rs.getString("rt_major_category"));
	        form.setRt_major_subcategory(rs.getString("rt_major_subcategory"));
	        form.setRt_preferred_majors(rs.getString("rt_preferred_majors"));
	        form.setRt_status((Integer) rs.getObject("rt_status"));
	        form.setRt_create_time(rs.getTimestamp("rt_create_time"));
	        form.setRt_update_time(rs.getTimestamp("rt_update_time"));
	        form.setRt_submit_time(rs.getTimestamp("rt_submit_time"));
	        return form;
	    }

	    /**
	     * 根据ID查询lhy_rt_fill_needs_info表数据 (返回单个对象)
	     *
	     * @param rtId 记录ID
	     * @return 匹配到的 RtFillNeedsInfoBean 对象，如果没有找到则返回null
	     */
	    public RtFillNeedsInfoBean getRtFillNeedsInfoById(String rtId) {
	        Connection conn = null;
	        PreparedStatement pstmt = null;
	        ResultSet rs = null;
	        RtFillNeedsInfoBean form = null;

	        String sql = "SELECT * FROM lhy_rt_fill_needs_info WHERE rt_id = ?";

	        try {
	            conn = DatabaseUtils.getConnection();
	            pstmt = conn.prepareStatement(sql);
	            pstmt.setString(1, rtId);

	            rs = pstmt.executeQuery();

	            SQLLogUtils.printSQL(" ===getRtFillNeedsInfoById : ", pstmt);

	            if (rs.next()) {
	                form = populateRtFillNeedsInfoFromResultSet(rs);
	            }

	        } catch (SQLException e) {
	            e.printStackTrace();
	        } finally {
	            closeAllConnection(conn, pstmt, rs);
	        }

	        return form;
	    }
	    
	    /**
	     * 根据用户ID查询lhy_rt_fill_needs_info表数据 (返回单个对象)
	     *
	     * @param order_id 用户ID
	     * @return 匹配到的 RtFillNeedsInfoBean 对象，如果没有找到则返回null
	     */
	    public List<RtFillNeedsInfoBean> getRtFillNeedsInfoByOrderId(String order_id) {
	        Connection conn = null;
	        PreparedStatement pstmt = null;
	        ResultSet rs = null;
	        List<RtFillNeedsInfoBean> list = new ArrayList<>();
	        String sql = "SELECT * FROM lhy_rt_fill_needs_info WHERE rt_user_id = ? ORDER BY rt_create_time DESC";

	        try {
	            conn = DatabaseUtils.getConnection();
	            pstmt = conn.prepareStatement(sql);
	            pstmt.setString(1, order_id);

	            rs = pstmt.executeQuery();

	            SQLLogUtils.printSQL(" ===getRtFillNeedsInfoByOrderid : ", pstmt);

	            while (rs.next()) {
	            	list.add(populateRtFillNeedsInfoFromResultSet(rs));
	            }

	        } catch (SQLException e) {
	            e.printStackTrace();
	        } finally {
	            closeAllConnection(conn, pstmt, rs);
	        }

	        return list;
	    }
	    
	    /**
	     * 
	     * @param saasId
	     * @return
	     * 
	     */
		public List<LhyCard> getLhyCardByStatusLevelAndSaasId(String cLevel, String saasId) {
	        Connection conn = null;
	        PreparedStatement pstmt = null;
	        ResultSet rs = null;
	        List<LhyCard> list = new ArrayList<>();
	        String sql = "SELECT * from lhy_card x WHERE x.c_status= '1' AND x.c_level = ? AND x.saas_id = ?";

	        try {
	            conn = DatabaseUtils.getConnection(); 
	            pstmt = conn.prepareStatement(sql);
	            pstmt.setString(1, cLevel);
	            pstmt.setString(2, saasId);

	            rs = pstmt.executeQuery();

	            SQLLogUtils.printSQL(" ===getLhyCardByStatusLevelAndSaasId : ", pstmt); 

	            while (rs.next()) {
	                list.add(setLhyBaseCard(rs));
	            }
	        } catch (Exception e) { 
	            e.printStackTrace();
	        } finally {
	            closeAllConnection(conn, pstmt, rs);
	        }
	        return list;
	    }
	    
	        /**
     * 根据用户ID查询用户的体检情况
     * 
     * @param rtUserId 用户ID (rt_user_id)
     * @return 用户的体检情况JSON字符串，如果没有找到则返回null
     */
    public String getUserPhysicalExamByUserId(String rtUserId) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        String physicalExam = null;
        
        String sql = "SELECT rt_physical_exam FROM lhy_rt_fill_needs_info WHERE rt_user_id = ? ORDER BY rt_create_time DESC LIMIT 1";
        
        try {
            conn = DatabaseUtils.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, rtUserId);
            
            rs = pstmt.executeQuery();
            
            SQLLogUtils.printSQL(" ===getUserPhysicalExamByUserId : ", pstmt);
            
            if (rs.next()) {
                physicalExam = rs.getString("rt_physical_exam");
            }
            
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, pstmt, rs);
        }
        
        return physicalExam;
    }

    /**
     * 获取新表指定字段的去重值列表（用于下拉框数据源）
     * @param jhYear 招考年份
     * @param sfCode 省份代码
     * @param fieldName 字段名
     * @return 去重值列表
     */
    public List<String> getDistinctValuesOrg(int jhYear, String sfCode, String fieldName) {
        List<String> list = new ArrayList<String>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtils.getConnection();
            String tableName = sfCode + "_jh_" + jhYear;
            String sql = "SELECT DISTINCT " + fieldName + " FROM " + tableName + " WHERE " + fieldName + " IS NOT NULL AND " + fieldName + " != '' ORDER BY " + fieldName;
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            
            SQLLogUtils.printSQL(" ===getDistinctValuesOrg : ", ps);
            
            while (rs.next()) {
                String value = rs.getString(1);
                if (value != null && !value.trim().isEmpty()) {
                    list.add(value.trim());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return list;
    }
    
    /**
     * 搜索新表数据
     * @param jhYear 招考年份
     * @param sfCode 省份代码
     * @param sf 院校省份
     * @param pc 批次
     * @param yxmc 院校名称
     * @param zymc 专业名称
     * @param zyml 专业门类
     * @param znzy 组类专业
     * @param zdf2024Min 2024年最低分最小值
     * @param zdf2024Max 2024年最低分最大值
     * @param zdfwc2024Min 2024年最低分位次最小值
     * @param zdfwc2024Max 2024年最低分位次最大值
     * @param pjf2024Min 2024年平均分最小值
     * @param pjf2024Max 2024年平均分最大值
     * @param indNature 学校性质
     * @param znzyls 组内专业类个数
     * @param pageNumber 页码
     * @return ResultVO包含查询结果和总记录数
     */
    public ResultVO searchJhOrgData(int jhYear, String sfCode, String sf, String pc, String yxmc, String zymc, 
            String zyml, String znzy, String zdf2024Min, String zdf2024Max, String zdfwc2024Min, 
            String zdfwc2024Max, String pjf2024Min, String pjf2024Max, String indNature, 
            String znzyls, int pageNumber) {
        
        List<JHBeanOrg> list = new ArrayList<JHBeanOrg>();
        int totalCount = 0;
        
        Connection conn = null;
        PreparedStatement ps = null;
        PreparedStatement psCount = null;
        ResultSet rs = null;
        ResultSet rsCount = null;
        
        try {
            conn = DatabaseUtils.getConnection();
            
            String tableName = sfCode + "_jh_" + jhYear;
            
            // 构建WHERE条件
            StringBuilder whereClause = new StringBuilder(" WHERE 1=1 ");
            List<Object> params = new ArrayList<Object>();
            
            if (sf != null && !sf.trim().isEmpty()) {
                whereClause.append(" AND sf LIKE ? ");
                params.add("%" + sf.trim() + "%");
            }
            
            if (pc != null && !pc.trim().isEmpty()) {
                whereClause.append(" AND pc LIKE ? ");
                params.add("%" + pc.trim() + "%");
            }
            
            if (yxmc != null && !yxmc.trim().isEmpty()) {
                whereClause.append(" AND yxmc LIKE ? ");
                params.add("%" + yxmc.trim() + "%");
            }
            
            if (zymc != null && !zymc.trim().isEmpty()) {
                whereClause.append(" AND zymc LIKE ? ");
                params.add("%" + zymc.trim() + "%");
            }
            
            if (zyml != null && !zyml.trim().isEmpty()) {
                whereClause.append(" AND zyml LIKE ? ");
                params.add("%" + zyml.trim() + "%");
            }
            
            if (znzy != null && !znzy.trim().isEmpty()) {
                whereClause.append(" AND znzy LIKE ? ");
                params.add("%" + znzy.trim() + "%");
            }
            
            if (zdf2024Min != null && !zdf2024Min.trim().isEmpty()) {
                whereClause.append(" AND CAST(zdf_2024 AS SIGNED) >= ? ");
                params.add(Integer.parseInt(zdf2024Min.trim()));
            }
            
            if (zdf2024Max != null && !zdf2024Max.trim().isEmpty()) {
                whereClause.append(" AND CAST(zdf_2024 AS SIGNED) <= ? ");
                params.add(Integer.parseInt(zdf2024Max.trim()));
            }
            
            if (zdfwc2024Min != null && !zdfwc2024Min.trim().isEmpty()) {
                whereClause.append(" AND CAST(zdfwc_2024 AS SIGNED) >= ? ");
                params.add(Integer.parseInt(zdfwc2024Min.trim()));
            }
            
            if (zdfwc2024Max != null && !zdfwc2024Max.trim().isEmpty()) {
                whereClause.append(" AND CAST(zdfwc_2024 AS SIGNED) <= ? ");
                params.add(Integer.parseInt(zdfwc2024Max.trim()));
            }
            
            if (pjf2024Min != null && !pjf2024Min.trim().isEmpty()) {
                whereClause.append(" AND CAST(pjf_2024 AS SIGNED) >= ? ");
                params.add(Integer.parseInt(pjf2024Min.trim()));
            }
            
            if (pjf2024Max != null && !pjf2024Max.trim().isEmpty()) {
                whereClause.append(" AND CAST(pjf_2024 AS SIGNED) <= ? ");
                params.add(Integer.parseInt(pjf2024Max.trim()));
            }
            
            if (indNature != null && !indNature.trim().isEmpty()) {
                whereClause.append(" AND ind_nature = ? ");
                params.add(indNature.trim());
            }
            
            if (znzyls != null && !znzyls.trim().isEmpty()) {
                whereClause.append(" AND znzyls = ? ");
                params.add(Integer.parseInt(znzyls.trim()));
            }
            
            // 查询总记录数
            String countSql = "SELECT COUNT(*) FROM " + tableName + whereClause.toString();
            psCount = conn.prepareStatement(countSql);
            for (int i = 0; i < params.size(); i++) {
                psCount.setObject(i + 1, params.get(i));
            }
            rsCount = psCount.executeQuery();
            if (rsCount.next()) {
                totalCount = rsCount.getInt(1);
            }
            
            // 分页查询数据
            int startIndex = (pageNumber - 1) * JHBean.PAGE_ROW_CNT_ORG;
            String sql = "SELECT * FROM " + tableName + whereClause.toString() + 
                    " ORDER BY id DESC LIMIT " + startIndex + ", " + JHBean.PAGE_ROW_CNT_ORG;
            
            ps = conn.prepareStatement(sql);
            for (int i = 0; i < params.size(); i++) {
                ps.setObject(i + 1, params.get(i));
            }
            rs = ps.executeQuery();
            
            SQLLogUtils.printSQL(" ===searchJhOrgData : ", ps);
            
            while (rs.next()) {
                JHBeanOrg bean = setJHBeanOrgAll(rs);
                list.add(bean);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(null, psCount, rsCount);
            closeAllConnection(conn, ps, rs);
        }
        
        ResultVO resultVO = new ResultVO();
        resultVO.setResult(list);
        resultVO.setRecordCnt(totalCount);
        return resultVO;
    }
    
    /**
     * 搜索新表数据总数（用于导出前的数量检查）
     */
    public ResultVO searchJhOrgDataCount(int jhYear, String sfCode, String sf, String pc, String yxmc, String zymc, 
            String zyml, String znzy, String zdf2024Min, String zdf2024Max, String zdfwc2024Min, 
            String zdfwc2024Max, String pjf2024Min, String pjf2024Max, String indNature, 
            String znzyls) {
        
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        ResultVO resultVO = new ResultVO();
        
        try {
            conn = DatabaseUtils.getConnection();
            
            String tableName = sfCode + "_jh_" + jhYear;
            
            // 构建WHERE条件
            StringBuilder whereClause = new StringBuilder(" WHERE 1=1 ");
            List<Object> params = new ArrayList<Object>();
            
            if (sf != null && !sf.trim().isEmpty()) {
                whereClause.append(" AND sf LIKE ? ");
                params.add("%" + sf.trim() + "%");
            }
            
            if (pc != null && !pc.trim().isEmpty()) {
                whereClause.append(" AND pc LIKE ? ");
                params.add("%" + pc.trim() + "%");
            }
            
            if (yxmc != null && !yxmc.trim().isEmpty()) {
                whereClause.append(" AND yxmc LIKE ? ");
                params.add("%" + yxmc.trim() + "%");
            }
            
            if (zymc != null && !zymc.trim().isEmpty()) {
                whereClause.append(" AND zymc LIKE ? ");
                params.add("%" + zymc.trim() + "%");
            }
            
            if (zyml != null && !zyml.trim().isEmpty()) {
                whereClause.append(" AND zyml LIKE ? ");
                params.add("%" + zyml.trim() + "%");
            }
            
            if (znzy != null && !znzy.trim().isEmpty()) {
                whereClause.append(" AND znzy LIKE ? ");
                params.add("%" + znzy.trim() + "%");
            }
            
            if (zdf2024Min != null && !zdf2024Min.trim().isEmpty()) {
                whereClause.append(" AND zdf_2024 >= ? ");
                params.add(Integer.parseInt(zdf2024Min.trim()));
            }
            
            if (zdf2024Max != null && !zdf2024Max.trim().isEmpty()) {
                whereClause.append(" AND zdf_2024 <= ? ");
                params.add(Integer.parseInt(zdf2024Max.trim()));
            }
            
            if (zdfwc2024Min != null && !zdfwc2024Min.trim().isEmpty()) {
                whereClause.append(" AND zdfwc_2024 >= ? ");
                params.add(Integer.parseInt(zdfwc2024Min.trim()));
            }
            
            if (zdfwc2024Max != null && !zdfwc2024Max.trim().isEmpty()) {
                whereClause.append(" AND zdfwc_2024 <= ? ");
                params.add(Integer.parseInt(zdfwc2024Max.trim()));
            }
            
            if (pjf2024Min != null && !pjf2024Min.trim().isEmpty()) {
                whereClause.append(" AND pjf_2024 >= ? ");
                params.add(Double.parseDouble(pjf2024Min.trim()));
            }
            
            if (pjf2024Max != null && !pjf2024Max.trim().isEmpty()) {
                whereClause.append(" AND pjf_2024 <= ? ");
                params.add(Double.parseDouble(pjf2024Max.trim()));
            }
            
            if (indNature != null && !indNature.trim().isEmpty()) {
                whereClause.append(" AND ind_nature LIKE ? ");
                params.add("%" + indNature.trim() + "%");
            }
            
            if (znzyls != null && !znzyls.trim().isEmpty()) {
                whereClause.append(" AND znzyls = ? ");
                params.add(Integer.parseInt(znzyls.trim()));
            }
            
            // 查询总记录数
            String countSql = "SELECT COUNT(*) FROM " + tableName + whereClause.toString();
            ps = conn.prepareStatement(countSql);
            for (int i = 0; i < params.size(); i++) {
                ps.setObject(i + 1, params.get(i));
            }
            rs = ps.executeQuery();
            
            if (rs.next()) {
                resultVO.setRecordCnt(rs.getInt(1));
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return resultVO;
    }
    
    /**
     * 搜索新表数据用于导出（不分页）
     */
    public ResultVO searchJhOrgDataForExport(int jhYear, String sfCode, String sf, String pc, String yxmc, String zymc, 
            String zyml, String znzy, String zdf2024Min, String zdf2024Max, String zdfwc2024Min, 
            String zdfwc2024Max, String pjf2024Min, String pjf2024Max, String indNature, 
            String znzyls, int maxRecords) {
        
        List<JHBeanOrg> list = new ArrayList<JHBeanOrg>();
        
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseUtils.getConnection();
            
            String tableName = sfCode + "_jh_" + jhYear;
            
            // 构建WHERE条件
            StringBuilder whereClause = new StringBuilder(" WHERE 1=1 ");
            List<Object> params = new ArrayList<Object>();
            
            if (sf != null && !sf.trim().isEmpty()) {
                whereClause.append(" AND sf LIKE ? ");
                params.add("%" + sf.trim() + "%");
            }
            
            if (pc != null && !pc.trim().isEmpty()) {
                whereClause.append(" AND pc LIKE ? ");
                params.add("%" + pc.trim() + "%");
            }
            
            if (yxmc != null && !yxmc.trim().isEmpty()) {
                whereClause.append(" AND yxmc LIKE ? ");
                params.add("%" + yxmc.trim() + "%");
            }
            
            if (zymc != null && !zymc.trim().isEmpty()) {
                whereClause.append(" AND zymc LIKE ? ");
                params.add("%" + zymc.trim() + "%");
            }
            
            if (zyml != null && !zyml.trim().isEmpty()) {
                whereClause.append(" AND zyml LIKE ? ");
                params.add("%" + zyml.trim() + "%");
            }
            
            if (znzy != null && !znzy.trim().isEmpty()) {
                whereClause.append(" AND znzy LIKE ? ");
                params.add("%" + znzy.trim() + "%");
            }
            
            if (zdf2024Min != null && !zdf2024Min.trim().isEmpty()) {
                whereClause.append(" AND zdf_2024 >= ? ");
                params.add(Integer.parseInt(zdf2024Min.trim()));
            }
            
            if (zdf2024Max != null && !zdf2024Max.trim().isEmpty()) {
                whereClause.append(" AND zdf_2024 <= ? ");
                params.add(Integer.parseInt(zdf2024Max.trim()));
            }
            
            if (zdfwc2024Min != null && !zdfwc2024Min.trim().isEmpty()) {
                whereClause.append(" AND zdfwc_2024 >= ? ");
                params.add(Integer.parseInt(zdfwc2024Min.trim()));
            }
            
            if (zdfwc2024Max != null && !zdfwc2024Max.trim().isEmpty()) {
                whereClause.append(" AND zdfwc_2024 <= ? ");
                params.add(Integer.parseInt(zdfwc2024Max.trim()));
            }
            
            if (pjf2024Min != null && !pjf2024Min.trim().isEmpty()) {
                whereClause.append(" AND pjf_2024 >= ? ");
                params.add(Double.parseDouble(pjf2024Min.trim()));
            }
            
            if (pjf2024Max != null && !pjf2024Max.trim().isEmpty()) {
                whereClause.append(" AND pjf_2024 <= ? ");
                params.add(Double.parseDouble(pjf2024Max.trim()));
            }
            
            if (indNature != null && !indNature.trim().isEmpty()) {
                whereClause.append(" AND ind_nature LIKE ? ");
                params.add("%" + indNature.trim() + "%");
            }
            
            if (znzyls != null && !znzyls.trim().isEmpty()) {
                whereClause.append(" AND znzyls = ? ");
                params.add(Integer.parseInt(znzyls.trim()));
            }
            
            // 查询数据（限制最大记录数）
            String sql = "SELECT * FROM " + tableName + whereClause.toString() + 
                    " ORDER BY id DESC LIMIT " + maxRecords;
            
            ps = conn.prepareStatement(sql);
            for (int i = 0; i < params.size(); i++) {
                ps.setObject(i + 1, params.get(i));
            }
            rs = ps.executeQuery();
            
            SQLLogUtils.printSQL(" ===searchJhOrgDataForExport : ", ps);
            
            while (rs.next()) {
                JHBeanOrg bean = setJHBeanOrgAll(rs);
                list.add(bean);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        ResultVO resultVO = new ResultVO();
        resultVO.setResult(list);
        resultVO.setRecordCnt(list.size());
        return resultVO;
    }

    /**
     * 从ResultSet设置JHBeanOrg对象的所有字段
     * @param rs ResultSet对象
     * @return JHBeanOrg对象
     * @throws Exception
     */
    private static JHBeanOrg setJHBeanOrgAll(ResultSet rs) throws Exception {
        JHBeanOrg bean = new JHBeanOrg();
        
        bean.setId(rs.getInt("id"));
        bean.setSf(rs.getString("sf"));
        bean.setNf(rs.getString("nf"));
        bean.setYxdm(rs.getString("yxdm"));
        bean.setYxmc(rs.getString("yxmc"));
        bean.setYxmc_org(rs.getString("yxmc_org"));
        bean.setZydm(rs.getString("zydm"));
        bean.setZymc(rs.getString("zymc"));
        bean.setZymc_org(rs.getString("zymc_org"));
        bean.setZyml(rs.getString("zyml"));
        bean.setZnzy(rs.getString("znzy"));
        bean.setZyzjhs(rs.getString("zyzjhs"));
        bean.setXzyx(rs.getString("xzyx"));
        bean.setZyzlqrs(rs.getString("zyzlqrs"));
        bean.setZyzzdf(rs.getString("zyzzdf"));
        bean.setZyzzdfwc(rs.getString("zyzzdfwc"));
        bean.setLqrs_2024(rs.getString("lqrs_2024"));
        bean.setLqrs_2023(rs.getString("lqrs_2023"));
        bean.setLqrs_2022(rs.getString("lqrs_2022"));
        bean.setZyz(rs.getString("zyz"));
        bean.setZyz_desc(rs.getString("zyz_desc"));
        bean.setZybz(rs.getString("zybz"));
        bean.setPc(rs.getString("pc"));
        bean.setPc_code(rs.getString("pc_code"));
        bean.setPc_desc(rs.getString("pc_desc"));
        bean.setXk(rs.getString("xk"));
        bean.setXk_code(rs.getString("xk_code"));
        bean.setXk_code_org(rs.getString("xk_code_org"));
        bean.setZx(rs.getString("zx"));
        bean.setXk_desc(rs.getString("xk_desc"));
        bean.setJhs(rs.getString("jhs"));
        bean.setFee(rs.getString("fee"));
        bean.setXz(rs.getString("xz"));
        bean.setZdf(rs.getInt("zdf"));
        bean.setZdfwc(rs.getInt("zdfwc"));
        bean.setFee_2023(rs.getString("fee_2023"));
        bean.setFee_2024(rs.getString("fee_2024"));
        bean.setXz_2024(rs.getString("xz_2024"));
        bean.setJhs_2024(rs.getString("jhs_2024"));
        bean.setJhs_2023(rs.getString("jhs_2023"));
        bean.setJhs_2022(rs.getString("jhs_2022"));
        bean.setZdf_2024(rs.getString("zdf_2024"));
        bean.setZdf_2023(rs.getString("zdf_2023"));
        bean.setZdf_2022(rs.getString("zdf_2022"));
        bean.setZdfwc_2024(rs.getString("zdfwc_2024"));
        bean.setZdfwc_2023(rs.getString("zdfwc_2023"));
        bean.setZdfwc_2022(rs.getString("zdfwc_2022"));
        bean.setQsf_a(rs.getInt("qsf_a"));
        bean.setQsf_b(rs.getInt("qsf_b"));
        bean.setQsf_c(rs.getInt("qsf_c"));
        bean.setQsf(rs.getInt("qsf"));
        bean.setZgf_2024(rs.getString("zgf_2024"));
        bean.setZgf_2023(rs.getString("zgf_2023"));
        bean.setZgf_2022(rs.getString("zgf_2022"));
        bean.setZgfwc_2024(rs.getString("zgfwc_2024"));
        bean.setZgfwc_2023(rs.getString("zgfwc_2023"));
        bean.setZgfwc_2022(rs.getString("zgfwc_2022"));
        bean.setPjf_2024(rs.getString("pjf_2024"));
        bean.setPjf_2023(rs.getString("pjf_2023"));
        bean.setPjf_2022(rs.getString("pjf_2022"));
        bean.setPjf_2021(rs.getString("pjf_2021"));
        bean.setPjfwc_2024(rs.getString("pjfwc_2024"));
        bean.setPjfwc_2023(rs.getString("pjfwc_2023"));
        bean.setPjfwc_2022(rs.getString("pjfwc_2022"));
        bean.setPjfwc_2021(rs.getString("pjfwc_2021"));
        bean.setLqpc(rs.getString("lqpc"));
        bean.setInd_nature(rs.getString("ind_nature"));
        bean.setInd_catg(rs.getString("ind_catg"));
        bean.setYxsf(rs.getString("yxsf"));
        bean.setYxcs(rs.getString("yxcs"));
        bean.setYx_tags(rs.getString("yx_tags"));
        bean.setYx_tags_all(rs.getString("yx_tags_all"));
        bean.setCnt_company(rs.getInt("cnt_company"));
        bean.setCnt_employ(rs.getInt("cnt_employ"));
        bean.setCnt_grad(rs.getFloat("cnt_grad"));
        bean.setIs_hz(rs.getInt("is_hz"));
        bean.setIs_first(rs.getInt("is_first"));
        bean.setZnzys(rs.getInt("znzys"));
        bean.setZnzyls(rs.getInt("znzyls"));
        bean.setZnjhs(rs.getInt("znjhs"));
        bean.setYcwc(rs.getInt("ycwc"));
        bean.setInfo_yxtag(rs.getString("info_yxtag"));
        bean.setInfo_yxsp(rs.getString("info_yxsp"));
        bean.setInfo_yxgmhzzs(rs.getString("info_yxgmhzzs"));
        bean.setInfo_zzy(rs.getString("info_zzy"));
        bean.setInfo_cssp(rs.getString("info_cssp"));
        bean.setInfo_lsdw(rs.getString("info_lsdw"));
        bean.setInfo_lx(rs.getString("info_lx"));
        bean.setInfo_gsxz(rs.getString("info_gsxz"));
        bean.setInfo_byl(rs.getString("info_byl"));
        bean.setInfo_yxpm(rs.getString("info_yxpm"));
        bean.setInfo_qxsszys(rs.getString("info_qxsszys"));
        bean.setInfo_qxsszy(rs.getString("info_qxsszy"));
        bean.setInfo_qxbszys(rs.getString("info_qxbszys"));
        bean.setInfo_qxbszy(rs.getString("info_qxbszy"));
        bean.setZsjz_2024(rs.getString("zsjz_2024"));
        bean.setInfozy_rkpj(rs.getString("infozy_rkpj"));
        bean.setInfozy_rkpm(rs.getString("infozy_rkpm"));
        bean.setInfozy_xkpg(rs.getString("infozy_xkpg"));
        bean.setInfozy_zysp(rs.getString("infozy_zysp"));
        bean.setInfozy_ssd(rs.getString("infozy_ssd"));
        bean.setInfozy_bsd(rs.getString("infozy_bsd"));
        
        return bean;
    }

    /**
     * 获取组长下所有专家的统计信息（包括组长自己）
     * @param parentCId 组长账号ID
     * @return GroupStatistics 组统计信息
     */
    public GroupStatistics getGroupStatistics(String parentCId) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        GroupStatistics groupStats = new GroupStatistics();
        List<ExpertStatistics> expertList = new ArrayList<ExpertStatistics>();
        
        try {
            conn = DatabaseUtils.getConnection();
            
            // 首先获取组长信息
            String leaderSql = "SELECT c_id, c_name, c_phone, last_login_tm, sub_acct_cnt FROM lhy_card WHERE c_id = ?";
            ps = conn.prepareStatement(leaderSql);
            ps.setString(1, parentCId);
            rs = ps.executeQuery();
            
            if (rs.next()) {
                groupStats.setGroup_leader_id(rs.getString("c_id"));
                groupStats.setGroup_leader_name(rs.getString("c_name"));
                groupStats.setGroup_leader_phone(rs.getString("c_phone"));
                groupStats.setGroup_leader_last_login(rs.getTimestamp("last_login_tm"));
                groupStats.setSub_account_count(rs.getInt("sub_acct_cnt"));
            }
            
            closeAllConnection(null, ps, rs);
            
            // 获取组长自己的订单统计（组长作为第一个"专家"）
            String leaderOrderSql = "SELECT " +
                "lc.c_id, lc.c_name, lc.c_phone, lc.last_login_tm, lc.create_tm, lc.c_status, " +
                "COUNT(lo.order_id) as total_orders, " +
                "SUM(CASE WHEN lo.status >= 10 AND lo.status <= 19 THEN 1 ELSE 0 END) as active_orders, " +
                "SUM(CASE WHEN lo.status >= 20 AND lo.status <= 29 THEN 1 ELSE 0 END) as paid_orders, " +
                "SUM(CASE WHEN lo.status = 30 THEN 1 ELSE 0 END) as deleted_orders, " +
                "SUM(CASE WHEN lo.status = 21 THEN 1 ELSE 0 END) as deposit_orders, " +
                "SUM(CASE WHEN lo.status = 22 THEN 1 ELSE 0 END) as full_payment_orders, " +
                "MAX(lo.create_tm) as last_order_time, " +
                "SUM(CASE WHEN lo.create_tm >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as orders_this_week, " +
                "SUM(CASE WHEN lo.create_tm >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as orders_this_month " +
                "FROM lhy_card lc " +
                "LEFT JOIN lhy_order lo ON lc.c_id = lo.lhy_c_id " +
                "WHERE lc.c_id = ? " +
                "GROUP BY lc.c_id, lc.c_name, lc.c_phone, lc.last_login_tm, lc.create_tm, lc.c_status";
            
            ps = conn.prepareStatement(leaderOrderSql);
            ps.setString(1, parentCId);
            rs = ps.executeQuery();
            
            SQLLogUtils.printSQL(" === getGroupStatistics Leader Orders : ", ps);
            
            // 统计变量初始化
            int totalExperts = 0;
            int activeExperts = 0;
            int totalCustomers = 0;
            int totalPaidCustomers = 0;
            int totalActiveCustomers = 0;
            int totalDeletedCustomers = 0;
            int totalThisMonth = 0;
            
            // 处理组长的订单统计
            if (rs.next()) {
                ExpertStatistics leader = new ExpertStatistics();
                leader.setC_id(rs.getString("c_id"));
                leader.setC_name("【组长】" + rs.getString("c_name")); // 标识为组长
                leader.setC_phone(rs.getString("c_phone"));
                leader.setLast_login_tm(rs.getTimestamp("last_login_tm"));
                leader.setCreate_tm(rs.getTimestamp("create_tm"));
                leader.setC_status(rs.getInt("c_status"));
                leader.setTotal_orders(rs.getInt("total_orders"));
                leader.setActive_orders(rs.getInt("active_orders"));
                leader.setPaid_orders(rs.getInt("paid_orders"));
                leader.setDeleted_orders(rs.getInt("deleted_orders"));
                leader.setDeposit_orders(rs.getInt("deposit_orders"));
                leader.setFull_payment_orders(rs.getInt("full_payment_orders"));
                leader.setLast_order_time(rs.getTimestamp("last_order_time"));
                leader.setOrders_this_week(rs.getInt("orders_this_week"));
                leader.setOrders_this_month(rs.getInt("orders_this_month"));
                
                // 计算转化率（排除已删除客户）
                int validCustomers = leader.getTotal_orders() - leader.getDeleted_orders();
                if (validCustomers > 0) {
                    leader.setConversion_rate((double) leader.getPaid_orders() / validCustomers * 100);
                } else {
                    leader.setConversion_rate(0.0);
                }
                
                // 组长作为第一个专家添加到列表
                expertList.add(leader);
                
                // 累计到组统计
                totalExperts++;
                totalCustomers += leader.getTotal_orders();
                totalPaidCustomers += leader.getPaid_orders();
                totalActiveCustomers += leader.getActive_orders();
                totalDeletedCustomers += leader.getDeleted_orders();
                totalThisMonth += leader.getOrders_this_month();
                
                // 检查组长是否为活跃专家
                if (leader.getLast_login_tm() != null) {
                    long diffInMillies = Math.abs(new Date().getTime() - leader.getLast_login_tm().getTime());
                    long diffInDays = diffInMillies / (24 * 60 * 60 * 1000);
                    if (diffInDays <= 30) {
                        activeExperts++;
                    }
                }
            }
            
            closeAllConnection(null, ps, rs);
            
            // 获取专家统计信息
            String expertSql = "SELECT " +
                "lc.c_id, lc.c_name, lc.c_phone, lc.last_login_tm, lc.create_tm, lc.c_status, " +
                "COUNT(lo.order_id) as total_orders, " +
                "SUM(CASE WHEN lo.status >= 10 AND lo.status <= 19 THEN 1 ELSE 0 END) as active_orders, " +
                "SUM(CASE WHEN lo.status >= 20 AND lo.status <= 29 THEN 1 ELSE 0 END) as paid_orders, " +
                "SUM(CASE WHEN lo.status = 30 THEN 1 ELSE 0 END) as deleted_orders, " +
                "SUM(CASE WHEN lo.status = 21 THEN 1 ELSE 0 END) as deposit_orders, " +
                "SUM(CASE WHEN lo.status = 22 THEN 1 ELSE 0 END) as full_payment_orders, " +
                "MAX(lo.create_tm) as last_order_time, " +
                "SUM(CASE WHEN lo.create_tm >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as orders_this_week, " +
                "SUM(CASE WHEN lo.create_tm >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as orders_this_month " +
                "FROM lhy_card lc " +
                "LEFT JOIN lhy_order lo ON lc.c_id = lo.lhy_c_id " +
                "WHERE lc.p_c_id = ? AND lc.c_level = 1 " +
                "GROUP BY lc.c_id, lc.c_name, lc.c_phone, lc.last_login_tm, lc.create_tm, lc.c_status " +
                "ORDER BY lc.c_id";
            
            ps = conn.prepareStatement(expertSql);
            ps.setString(1, parentCId);
            rs = ps.executeQuery();
            
            SQLLogUtils.printSQL(" === getGroupStatistics Expert Orders : ", ps);
            
            while (rs.next()) {
                ExpertStatistics expert = new ExpertStatistics();
                expert.setC_id(rs.getString("c_id"));
                expert.setC_name(rs.getString("c_name"));
                expert.setC_phone(rs.getString("c_phone"));
                expert.setLast_login_tm(rs.getTimestamp("last_login_tm"));
                expert.setCreate_tm(rs.getTimestamp("create_tm"));
                expert.setC_status(rs.getInt("c_status"));
                expert.setTotal_orders(rs.getInt("total_orders"));
                expert.setActive_orders(rs.getInt("active_orders"));
                expert.setPaid_orders(rs.getInt("paid_orders"));
                expert.setDeleted_orders(rs.getInt("deleted_orders"));
                expert.setDeposit_orders(rs.getInt("deposit_orders"));
                expert.setFull_payment_orders(rs.getInt("full_payment_orders"));
                expert.setLast_order_time(rs.getTimestamp("last_order_time"));
                expert.setOrders_this_week(rs.getInt("orders_this_week"));
                expert.setOrders_this_month(rs.getInt("orders_this_month"));
                
                // 计算转化率（排除已删除客户）
                int validCustomers = expert.getTotal_orders() - expert.getDeleted_orders();
                if (validCustomers > 0) {
                    expert.setConversion_rate((double) expert.getPaid_orders() / validCustomers * 100);
                } else {
                    expert.setConversion_rate(0.0);
                }
                
                expertList.add(expert);
                
                // 累计组统计
                totalExperts++;
                totalCustomers += expert.getTotal_orders();
                totalPaidCustomers += expert.getPaid_orders();
                totalActiveCustomers += expert.getActive_orders();
                totalDeletedCustomers += expert.getDeleted_orders();
                totalThisMonth += expert.getOrders_this_month();
                
                // 检查是否为活跃专家（最近30天有登录）
                if (expert.getLast_login_tm() != null) {
                    long diffInMillies = Math.abs(new Date().getTime() - expert.getLast_login_tm().getTime());
                    long diffInDays = diffInMillies / (24 * 60 * 60 * 1000);
                    if (diffInDays <= 30) {
                        activeExperts++;
                    }
                }
            }
            
            groupStats.setExpertList(expertList);
            groupStats.setTotal_experts(totalExperts);
            groupStats.setActive_experts(activeExperts);
            groupStats.setTotal_customers(totalCustomers);
            groupStats.setTotal_paid_customers(totalPaidCustomers);
            groupStats.setTotal_active_customers(totalActiveCustomers);
            groupStats.setTotal_deleted_customers(totalDeletedCustomers);
            groupStats.setTotal_this_month(totalThisMonth);
            
            // 计算组整体转化率（排除已删除客户）
            int totalValidCustomers = totalCustomers - totalDeletedCustomers;
            if (totalValidCustomers > 0) {
                groupStats.setGroup_conversion_rate((double) totalPaidCustomers / totalValidCustomers * 100);
            } else {
                groupStats.setGroup_conversion_rate(0.0);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return groupStats;
    }
    
    /**
     * 获取专家的客户详情（带筛选功能）
     * @param expertCId 专家账号ID
     * @param statusFilter 状态筛选（all, active, paid, deleted）
     * @param timeFilter 时间筛选（all, week, month）
     * @return List<LhyOrder> 客户订单列表
     */
    public List<LhyOrder> getOrdersByExpertIdWithFilter(String expertCId, String statusFilter, String timeFilter) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<LhyOrder> orderList = new ArrayList<LhyOrder>();
        
        try {
            conn = DatabaseUtils.getConnection();
            
            StringBuilder sql = new StringBuilder("SELECT * FROM lhy_order WHERE lhy_c_id = ?");
            List<Object> params = new ArrayList<Object>();
            params.add(expertCId);
            
            // 状态筛选
            if ("active".equals(statusFilter)) {
                sql.append(" AND status >= 10 AND status <= 19");
            } else if ("paid".equals(statusFilter)) {
                sql.append(" AND status >= 20 AND status <= 29");
            } else if ("deleted".equals(statusFilter)) {
                sql.append(" AND status = 30");
            }
            
            // 时间筛选
            if ("week".equals(timeFilter)) {
                sql.append(" AND create_tm >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)");
            } else if ("month".equals(timeFilter)) {
                sql.append(" AND create_tm >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)");
            }
            
            sql.append(" ORDER BY create_tm DESC");
            
            ps = conn.prepareStatement(sql.toString());
            for (int i = 0; i < params.size(); i++) {
                ps.setObject(i + 1, params.get(i));
            }
            rs = ps.executeQuery();
            
            SQLLogUtils.printSQL(" === getOrdersByExpertIdWithFilter : ", ps);
            
            while (rs.next()) {
                LhyOrder order = setLhyOrderAll(rs);
                orderList.add(order);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return orderList;
    }

    /**
     * 根据志愿信息精确查询对应的计划数据
     * @param jhYear 计划年份
     * @param sfCode 省份代码
     * @param xkCode 选科代码
     * @param pcCode 批次代码
     * @param pcName 批次名称
     * @param yxdm 院校代码
     * @param zydm 专业代码
     * @param zyz 专业组
     * @return 匹配的计划数据列表
     */
    public List<JHBean> searchJhDataByFormInfo(int jhYear, String sfCode, String xkCode, String pcCode, String pcName, String yxdm, String zydm, String zyz) {
    	List<JHBean> jhDataList = new ArrayList<JHBean>();
	
		// 动态构建查询条件
		String zyzSQL = Tools.isEmpty(zyz) ? "" : "and zyz = ?";
		
		String sql = "SELECT * FROM "  + sfCode + "_jh_" + jhYear  + " WHERE " +
		"xk_code like ? AND pc_code = ? AND pc = ? AND yxdm = ? AND zydm = ? " + zyzSQL;
		
		Connection conn = null;
		PreparedStatement pstmt = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			pstmt = conn.prepareStatement(sql);
			pstmt.setString(1, "%" + xkCode + "%");
			pstmt.setString(2, pcCode);
			pstmt.setString(3, pcName);
			pstmt.setString(4, yxdm);
			pstmt.setString(5, zydm);
			
			// 只有当zyz不为空时才设置参数
			if (!Tools.isEmpty(zyz)) {
				pstmt.setString(6, zyz);
			}
		
			SQLLogUtils.printSQL(" === searchJhDataByFormInfo : ", pstmt);
			
			rs = pstmt.executeQuery();
			
			while (rs.next()) {
			JHBean jhBean = setJHBeanAll(jhYear-1, rs);
			jhDataList.add(jhBean);
		}
		
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			closeAllConnection(conn, pstmt, rs);
		}
		
		return jhDataList;
	}

}
