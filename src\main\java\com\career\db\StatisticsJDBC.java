package com.career.db;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.career.utils.SQLLogUtils;

/**
 * 统计数据JDBC类
 * 支持学校统计和班级统计
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class StatisticsJDBC {
    
    static String URL = JDBCConstants.URL;
    static String USER = JDBCConstants.USER;
    static String PASSWD = JDBCConstants.PASSWD;
    
    static {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
    
    /**
     * 获取基础统计数据（支持学校和班级统计）
     * @param schoolLhyCId 学校ID，如果为null则统计所有学校
     * @param classLhyCId 班级ID，如果为null则统计学校级别
     * @return 基础统计数据Map
     */
    public Map<String, Object> getBasicStatistics(String schoolLhyCId, String classLhyCId) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        Map<String, Object> result = new HashMap<>();
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            // 构建WHERE条件
            String whereClause = "";
            if (classLhyCId != null) {
                whereClause = " WHERE class_lhy_c_id = ?";
            } else if (schoolLhyCId != null) {
                whereClause = " WHERE school_lhy_c_id = ?";
            }
            
            // 学生总数统计
            String sqlTotal = "SELECT COUNT(*) as total_count FROM lhy_school_statistics_form" + whereClause;
            ps = conn.prepareStatement(sqlTotal);
            if (classLhyCId != null) {
                ps.setString(1, classLhyCId);
            } else if (schoolLhyCId != null) {
                ps.setString(1, schoolLhyCId);
            }
            SQLLogUtils.printSQL(" ===getBasicStatistics-total : ", ps);
            rs = ps.executeQuery();
            if (rs.next()) {
                result.put("total_students", rs.getInt("total_count"));
            }
            rs.close();
            ps.close();
            
            // 激活人数统计（有志愿填报记录的）
            String sqlActive = "SELECT COUNT(DISTINCT c_id) as active_count FROM lhy_school_statistics_form" + whereClause;
            ps = conn.prepareStatement(sqlActive);
            if (classLhyCId != null) {
                ps.setString(1, classLhyCId);
            } else if (schoolLhyCId != null) {
                ps.setString(1, schoolLhyCId);
            }
            SQLLogUtils.printSQL(" ===getBasicStatistics-active : ", ps);
            rs = ps.executeQuery();
            if (rs.next()) {
                result.put("active_students", rs.getInt("active_count"));
            }
            rs.close();
            ps.close();
            
            // 模拟志愿统计（有院校填报的）
            String sqlSimulated = "SELECT COUNT(DISTINCT c_id) as simulated_count FROM lhy_school_statistics_form" 
                + whereClause + (whereClause.isEmpty() ? " WHERE" : " AND") + " lq_yxdm IS NOT NULL AND lq_yxdm != ''";
            ps = conn.prepareStatement(sqlSimulated);
            if (classLhyCId != null) {
                ps.setString(1, classLhyCId);
            } else if (schoolLhyCId != null) {
                ps.setString(1, schoolLhyCId);
            }
            SQLLogUtils.printSQL(" ===getBasicStatistics-simulated : ", ps);
            rs = ps.executeQuery();
            if (rs.next()) {
                result.put("simulated_students", rs.getInt("simulated_count"));
            }
            rs.close();
            ps.close();
            
            // 滑档风险统计（lq_yxsf为空表示滑档）
            String sqlRisk = "SELECT COUNT(DISTINCT c_id) as risk_count FROM lhy_school_statistics_form" 
                + whereClause + (whereClause.isEmpty() ? " WHERE" : " AND") + " (lq_yxsf IS NULL OR lq_yxsf = '')";
            ps = conn.prepareStatement(sqlRisk);
            if (classLhyCId != null) {
                ps.setString(1, classLhyCId);
            } else if (schoolLhyCId != null) {
                ps.setString(1, schoolLhyCId);
            }
            SQLLogUtils.printSQL(" ===getBasicStatistics-risk : ", ps);
            rs = ps.executeQuery();
            if (rs.next()) {
                result.put("risk_students", rs.getInt("risk_count"));
            }
            rs.close();
            ps.close();
            
            // 计算比例
            int totalStudents = (Integer) result.get("total_students");
            if (totalStudents > 0) {
                result.put("active_rate", String.format("%.1f", (Integer) result.get("active_students") * 100.0 / totalStudents));
                result.put("simulated_rate", String.format("%.1f", (Integer) result.get("simulated_students") * 100.0 / totalStudents));
                result.put("risk_rate", String.format("%.1f", (Integer) result.get("risk_students") * 100.0 / totalStudents));
            }
            
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return result;
    }
    
    /**
     * 获取基础统计数据（保持向后兼容）
     * @param schoolLhyCId 学校ID，如果为null则统计所有学校
     * @return 基础统计数据Map
     */
    public Map<String, Object> getBasicStatistics(String schoolLhyCId) {
        return getBasicStatistics(schoolLhyCId, null);
    }
    
    /**
     * 获取录取类型统计（支持学校和班级统计）
     * @param schoolLhyCId 学校ID，如果为null则统计所有学校
     * @param classLhyCId 班级ID，如果为null则统计学校级别
     * @return 录取类型统计数据Map
     */
    public Map<String, Object> getAdmissionTypeStatistics(String schoolLhyCId, String classLhyCId) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        Map<String, Object> result = new HashMap<>();
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            String whereClause = "";
            if (classLhyCId != null) {
                whereClause = " WHERE class_lhy_c_id = ?";
            } else if (schoolLhyCId != null) {
                whereClause = " WHERE school_lhy_c_id = ?";
            }
            String andClause = whereClause.isEmpty() ? " WHERE" : " AND";
            
            // 985高校录取统计
            String sql985 = "SELECT COUNT(DISTINCT c_id) as count_985 FROM lhy_school_statistics_form" 
                + whereClause + andClause + " lq_is_985 = '1'";
            ps = conn.prepareStatement(sql985);
            if (classLhyCId != null) {
                ps.setString(1, classLhyCId);
            } else if (schoolLhyCId != null) {
                ps.setString(1, schoolLhyCId);
            }
            SQLLogUtils.printSQL(" ===getAdmissionTypeStatistics-985 : ", ps);
            rs = ps.executeQuery();
            if (rs.next()) result.put("admission_985", rs.getInt("count_985"));
            rs.close(); ps.close();
            
            // 211高校录取统计
            String sql211 = "SELECT COUNT(DISTINCT c_id) as count_211 FROM lhy_school_statistics_form" 
                + whereClause + andClause + " lq_is_211 = '1'";
            ps = conn.prepareStatement(sql211);
            if (classLhyCId != null) {
                ps.setString(1, classLhyCId);
            } else if (schoolLhyCId != null) {
                ps.setString(1, schoolLhyCId);
            }
            SQLLogUtils.printSQL(" ===getAdmissionTypeStatistics-211 : ", ps);
            rs = ps.executeQuery();
            if (rs.next()) result.put("admission_211", rs.getInt("count_211"));
            rs.close(); ps.close();
            
            // 双一流高校录取统计
            String sqlSyl = "SELECT COUNT(DISTINCT c_id) as count_syl FROM lhy_school_statistics_form" 
                + whereClause + andClause + " lq_is_syl = '1'";
            ps = conn.prepareStatement(sqlSyl);
            if (classLhyCId != null) {
                ps.setString(1, classLhyCId);
            } else if (schoolLhyCId != null) {
                ps.setString(1, schoolLhyCId);
            }
            SQLLogUtils.printSQL(" ===getAdmissionTypeStatistics-syl : ", ps);
            rs = ps.executeQuery();
            if (rs.next()) result.put("admission_syl", rs.getInt("count_syl"));
            rs.close(); ps.close();
            
            // 中外合作办学录取统计
            String sqlHzbx = "SELECT COUNT(DISTINCT c_id) as count_hzbx FROM lhy_school_statistics_form" 
                + whereClause + andClause + " lq_is_hzbx = '1'";
            ps = conn.prepareStatement(sqlHzbx);
            if (classLhyCId != null) {
                ps.setString(1, classLhyCId);
            } else if (schoolLhyCId != null) {
                ps.setString(1, schoolLhyCId);
            }
            SQLLogUtils.printSQL(" ===getAdmissionTypeStatistics-hzbx : ", ps);
            rs = ps.executeQuery();
            if (rs.next()) result.put("admission_hzbx", rs.getInt("count_hzbx"));
            rs.close(); ps.close();
            
            // 获取总录取人数用于计算比例
            String sqlTotal = "SELECT COUNT(DISTINCT c_id) as total_admitted FROM lhy_school_statistics_form" 
                + whereClause + andClause + " lq_yxsf IS NOT NULL AND lq_yxsf != ''";
            ps = conn.prepareStatement(sqlTotal);
            if (classLhyCId != null) {
                ps.setString(1, classLhyCId);
            } else if (schoolLhyCId != null) {
                ps.setString(1, schoolLhyCId);
            }
            SQLLogUtils.printSQL(" ===getAdmissionTypeStatistics-totalAdmitted : ", ps);
            rs = ps.executeQuery();
            int totalAdmitted = 0;
            if (rs.next()) {
                totalAdmitted = rs.getInt("total_admitted");
                result.put("total_admitted", totalAdmitted);
            }
            
            // 计算比例
            if (totalAdmitted > 0) {
                result.put("admission_985_rate", String.format("%.1f", (Integer) result.get("admission_985") * 100.0 / totalAdmitted));
                result.put("admission_211_rate", String.format("%.1f", (Integer) result.get("admission_211") * 100.0 / totalAdmitted));
                result.put("admission_syl_rate", String.format("%.1f", (Integer) result.get("admission_syl") * 100.0 / totalAdmitted));
                result.put("admission_hzbx_rate", String.format("%.1f", (Integer) result.get("admission_hzbx") * 100.0 / totalAdmitted));
            }
            
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return result;
    }
    
    /**
     * 获取录取类型统计（保持向后兼容）
     * @param schoolLhyCId 学校ID，如果为null则统计所有学校
     * @return 录取类型统计数据Map
     */
    public Map<String, Object> getAdmissionTypeStatistics(String schoolLhyCId) {
        return getAdmissionTypeStatistics(schoolLhyCId, null);
    }
    
    /**
     * 获取录取结果统计（支持学校和班级统计）
     * @param schoolLhyCId 学校ID，如果为null则统计所有学校
     * @param classLhyCId 班级ID，如果为null则统计学校级别
     * @return 录取结果统计数据Map
     */
    public Map<String, Object> getAdmissionResultStatistics(String schoolLhyCId, String classLhyCId) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        Map<String, Object> result = new HashMap<>();
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            String whereClause = "";
            if (classLhyCId != null) {
                whereClause = " WHERE class_lhy_c_id = ?";
            } else if (schoolLhyCId != null) {
                whereClause = " WHERE school_lhy_c_id = ?";
            }
            String andClause = whereClause.isEmpty() ? " WHERE" : " AND";
            
            // 调剂录取统计 (is_adjust = 1)
            String sqlAdjust = "SELECT COUNT(DISTINCT c_id) as adjust_count FROM lhy_school_statistics_form" 
                + whereClause + andClause + " is_adjust = 1";
            ps = conn.prepareStatement(sqlAdjust);
            if (classLhyCId != null) {
                ps.setString(1, classLhyCId);
            } else if (schoolLhyCId != null) {
                ps.setString(1, schoolLhyCId);
            }
            SQLLogUtils.printSQL(" ===getAdmissionResultStatistics-adjust : ", ps);
            rs = ps.executeQuery();
            if (rs.next()) result.put("adjust_admission", rs.getInt("adjust_count"));
            rs.close(); ps.close();
            
            // 正常录取统计 (is_adjust = 2)
            String sqlNormal = "SELECT COUNT(DISTINCT c_id) as normal_count FROM lhy_school_statistics_form" 
                + whereClause + andClause + " is_adjust = 2";
            ps = conn.prepareStatement(sqlNormal);
            if (classLhyCId != null) {
                ps.setString(1, classLhyCId);
            } else if (schoolLhyCId != null) {
                ps.setString(1, schoolLhyCId);
            }
            SQLLogUtils.printSQL(" ===getAdmissionResultStatistics-normal : ", ps);
            rs = ps.executeQuery();
            if (rs.next()) result.put("normal_admission", rs.getInt("normal_count"));
            rs.close(); ps.close();
            
            // 落榜统计 (is_adjust = 999)
            String sqlFailed = "SELECT COUNT(DISTINCT c_id) as failed_count FROM lhy_school_statistics_form" 
                + whereClause + andClause + " is_adjust = 999";
            ps = conn.prepareStatement(sqlFailed);
            if (classLhyCId != null) {
                ps.setString(1, classLhyCId);
            } else if (schoolLhyCId != null) {
                ps.setString(1, schoolLhyCId);
            }
            SQLLogUtils.printSQL(" ===getAdmissionResultStatistics-failed : ", ps);
            rs = ps.executeQuery();
            if (rs.next()) result.put("failed_admission", rs.getInt("failed_count"));
            rs.close(); ps.close();
            
            // 获取总人数用于计算比例
            String sqlTotal = "SELECT COUNT(DISTINCT c_id) as total_students FROM lhy_school_statistics_form" + whereClause;
            ps = conn.prepareStatement(sqlTotal);
            if (classLhyCId != null) {
                ps.setString(1, classLhyCId);
            } else if (schoolLhyCId != null) {
                ps.setString(1, schoolLhyCId);
            }
            SQLLogUtils.printSQL(" ===getAdmissionResultStatistics-totalStudents : ", ps);
            rs = ps.executeQuery();
            int totalStudents = 0;
            if (rs.next()) {
                totalStudents = rs.getInt("total_students");
            }
            
            // 计算比例
            if (totalStudents > 0) {
                result.put("adjust_rate", String.format("%.1f", (Integer) result.get("adjust_admission") * 100.0 / totalStudents));
                result.put("normal_rate", String.format("%.1f", (Integer) result.get("normal_admission") * 100.0 / totalStudents));
                result.put("failed_rate", String.format("%.1f", (Integer) result.get("failed_admission") * 100.0 / totalStudents));
            }
            
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return result;
    }
    
    /**
     * 获取录取结果统计（保持向后兼容）
     * @param schoolLhyCId 学校ID，如果为null则统计所有学校
     * @return 录取结果统计数据Map
     */
    public Map<String, Object> getAdmissionResultStatistics(String schoolLhyCId) {
        return getAdmissionResultStatistics(schoolLhyCId, null);
    }
    
    /**
     * 获取去向省份排行榜（支持学校和班级统计）
     * @param schoolLhyCId 学校ID，如果为null则统计所有学校
     * @param classLhyCId 班级ID，如果为null则统计学校级别
     * @param limit 限制返回条数，默认前10名
     * @return 省份排行榜列表
     */
    public List<Map<String, Object>> getProvinceRanking(String schoolLhyCId, String classLhyCId, int limit) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<Map<String, Object>> result = new ArrayList<>();
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            String whereClause = "";
            if (classLhyCId != null) {
                whereClause = " WHERE class_lhy_c_id = ?";
            } else if (schoolLhyCId != null) {
                whereClause = " WHERE school_lhy_c_id = ?";
            } else {
                // 如果两个参数都为null，返回空结果，避免查询全部数据
                return result;
            }
            String andClause = whereClause.isEmpty() ? " WHERE" : " AND";
            
            String sql = "SELECT lq_yxsf, COUNT(DISTINCT c_id) as student_count, " +
                "GROUP_CONCAT(DISTINCT lq_yxcs ORDER BY lq_yxcs ASC SEPARATOR ', ') as cities " +
                "FROM lhy_school_statistics_form " + 
                whereClause + andClause + " lq_yxsf IS NOT NULL AND lq_yxsf != '' " +
                "GROUP BY lq_yxsf ORDER BY student_count DESC LIMIT " + limit;
            
            ps = conn.prepareStatement(sql);
            if (classLhyCId != null) {
                ps.setString(1, classLhyCId);
            } else if (schoolLhyCId != null) {
                ps.setString(1, schoolLhyCId);
            }
            SQLLogUtils.printSQL(" ===getProvinceRanking : ", ps);
            rs = ps.executeQuery();
            
            int rank = 1;
            while (rs.next()) {
                Map<String, Object> item = new HashMap<>();
                item.put("rank", rank++);
                item.put("province", rs.getString("lq_yxsf"));
                item.put("student_count", rs.getInt("student_count"));
                item.put("cities", rs.getString("cities"));
                result.add(item);
            }
            
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return result;
    }
    
    /**
     * 获取去向省份排行榜（保持向后兼容）
     * @param schoolLhyCId 学校ID，如果为null则统计所有学校
     * @param limit 限制返回条数，默认前10名
     * @return 省份排行榜列表
     */
    public List<Map<String, Object>> getProvinceRanking(String schoolLhyCId, int limit) {
        return getProvinceRanking(schoolLhyCId, null, limit);
    }
    
    /**
     * 获取去向院校排行榜（支持学校和班级统计）
     * @param schoolLhyCId 学校ID
     * @param classLhyCId 班级ID，如果为null则统计学校级别
     * @param limit 限制返回条数
     * @return 院校排行榜列表
     */
    public List<Map<String, Object>> getUniversityRanking(String schoolLhyCId, String classLhyCId, int limit) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<Map<String, Object>> result = new ArrayList<>();
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            String whereClause = "";
            if (classLhyCId != null) {
                whereClause = " WHERE class_lhy_c_id = ?";
            } else if (schoolLhyCId != null) {
                whereClause = " WHERE school_lhy_c_id = ?";
            } else {
                // 如果两个参数都为null，返回空结果，避免查询全部数据
                return result;
            }
            String andClause = whereClause.isEmpty() ? " WHERE" : " AND";
            
            String sql = "SELECT lq_yxmc, lq_yxdm, COUNT(DISTINCT c_id) as student_count " +
                "FROM lhy_school_statistics_form " + 
                whereClause + andClause + " lq_yxmc IS NOT NULL AND lq_yxmc != '' " +
                "GROUP BY lq_yxmc, lq_yxdm ORDER BY student_count DESC LIMIT " + limit;
            
            ps = conn.prepareStatement(sql);
            if (classLhyCId != null) {
                ps.setString(1, classLhyCId);
            } else if (schoolLhyCId != null) {
                ps.setString(1, schoolLhyCId);
            }
            SQLLogUtils.printSQL(" ===getUniversityRanking : ", ps);
            rs = ps.executeQuery();
            
            int rank = 1;
            while (rs.next()) {
                Map<String, Object> item = new HashMap<>();
                item.put("rank", rank++);
                item.put("university_name", rs.getString("lq_yxmc"));
                item.put("university_code", rs.getString("lq_yxdm"));
                item.put("student_count", rs.getInt("student_count"));
                result.add(item);
            }
            
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return result;
    }
    
    /**
     * 获取去向院校排行榜（保持向后兼容）
     * @param schoolLhyCId 学校ID
     * @param limit 限制返回条数
     * @return 院校排行榜列表
     */
    public List<Map<String, Object>> getUniversityRanking(String schoolLhyCId, int limit) {
        return getUniversityRanking(schoolLhyCId, null, limit);
    }
    

    
    /**
     * 获取去向专业排行榜（保持向后兼容）
     * @param schoolLhyCId 学校ID
     * @param limit 限制返回条数
     * @return 专业排行榜列表
     */
    public List<Map<String, Object>> getMajorRanking(String schoolLhyCId, int limit) {
        return getMajorRanking(schoolLhyCId, null, limit);
    }
    
    /**
     * 获取去向城市排行榜（支持学校和班级统计）
     * @param schoolLhyCId 学校ID
     * @param classLhyCId 班级ID，如果为null则统计学校级别
     * @param limit 限制返回条数
     * @return 城市排行榜列表
     */
    public List<Map<String, Object>> getCityRanking(String schoolLhyCId, String classLhyCId, int limit) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<Map<String, Object>> result = new ArrayList<>();
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            String whereClause = "";
            if (classLhyCId != null) {
                whereClause = " WHERE class_lhy_c_id = ?";
            } else if (schoolLhyCId != null) {
                whereClause = " WHERE school_lhy_c_id = ?";
            } else {
                // 如果两个参数都为null，返回空结果，避免查询全部数据
                return result;
            }
            String andClause = whereClause.isEmpty() ? " WHERE" : " AND";
            
            String sql = "SELECT lq_yxcs, COUNT(DISTINCT c_id) as student_count " +
                "FROM lhy_school_statistics_form " + 
                whereClause + andClause + " lq_yxcs IS NOT NULL AND lq_yxcs != '' " +
                "GROUP BY lq_yxcs ORDER BY student_count DESC LIMIT " + limit;
            
            ps = conn.prepareStatement(sql);
            if (classLhyCId != null) {
                ps.setString(1, classLhyCId);
            } else if (schoolLhyCId != null) {
                ps.setString(1, schoolLhyCId);
            }
            SQLLogUtils.printSQL(" ===getCityRanking : ", ps);
            rs = ps.executeQuery();
            
            int rank = 1;
            while (rs.next()) {
                Map<String, Object> item = new HashMap<>();
                item.put("rank", rank++);
                item.put("city_name", rs.getString("lq_yxcs"));
                item.put("student_count", rs.getInt("student_count"));
                result.add(item);
            }
            
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return result;
    }
    
    /**
     * 获取去向专业组排行榜（支持学校和班级统计）
     * @param schoolLhyCId 学校ID
     * @param classLhyCId 班级ID，如果为null则统计学校级别
     * @param limit 限制返回条数
     * @return 专业组排行榜列表
     */
    public List<Map<String, Object>> getSubjectGroupRanking(String schoolLhyCId, String classLhyCId, int limit) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<Map<String, Object>> result = new ArrayList<>();
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            String whereClause = "";
            if (classLhyCId != null) {
                whereClause = " WHERE class_lhy_c_id = ?";
            } else if (schoolLhyCId != null) {
                whereClause = " WHERE school_lhy_c_id = ?";
            } else {
                // 如果两个参数都为null，返回空结果，避免查询全部数据
                return result;
            }
            String andClause = whereClause.isEmpty() ? " WHERE" : " AND";
            
            String sql = "SELECT lq_zyz, COUNT(DISTINCT c_id) as student_count " +
                "FROM lhy_school_statistics_form " + 
                whereClause + andClause + " lq_zyz IS NOT NULL AND lq_zyz != '' " +
                "GROUP BY lq_zyz ORDER BY student_count DESC LIMIT " + limit;
            
            ps = conn.prepareStatement(sql);
            if (classLhyCId != null) {
                ps.setString(1, classLhyCId);
            } else if (schoolLhyCId != null) {
                ps.setString(1, schoolLhyCId);
            }
            SQLLogUtils.printSQL(" ===getSubjectGroupRanking : ", ps);
            rs = ps.executeQuery();
            
            int rank = 1;
            while (rs.next()) {
                Map<String, Object> item = new HashMap<>();
                item.put("rank", rank++);
                item.put("subject_group_name", rs.getString("lq_zyz")); // 只显示专业组名称
                item.put("student_count", rs.getInt("student_count"));
                result.add(item);
            }
            
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return result;
    }
    
    /**
     * 获取去向城市排行榜（保持向后兼容）
     * @param schoolLhyCId 学校ID
     * @param limit 限制返回条数
     * @return 城市排行榜列表
     */
    public List<Map<String, Object>> getCityRanking(String schoolLhyCId, int limit) {
        return getCityRanking(schoolLhyCId, null, limit);
    }
    
    /**
     * 获取去向专业组排行榜（保持向后兼容）
     * @param schoolLhyCId 学校ID
     * @param limit 限制返回条数
     * @return 专业组排行榜列表
     */
    public List<Map<String, Object>> getSubjectGroupRanking(String schoolLhyCId, int limit) {
        return getSubjectGroupRanking(schoolLhyCId, null, limit);
    }
    
    /**
     * 获取浪费分数统计（保持向后兼容）
     * @param schoolLhyCId 学校ID
     * @return 浪费分数统计数据
     */
    public Map<String, Object> getWasteScoreStatistics(String schoolLhyCId) {
        return getWasteScoreStatistics(schoolLhyCId, null);
    }
    
    /**
     * 获取批次统计（保持向后兼容）
     * @param schoolLhyCId 学校ID
     * @return 批次统计数据
     */
    public Map<String, Object> getBatchStatistics(String schoolLhyCId) {
        return getBatchStatistics(schoolLhyCId, null);
    }
    
    /**
     * 获取分数段统计
     * @param schoolLhyCId 学校ID
     * @return 分数段统计数据
     */
    public Map<String, Object> getScoreSegmentStatistics(String schoolLhyCId) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        Map<String, Object> result = new HashMap<>();
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            String whereClause = schoolLhyCId != null ? " WHERE school_lhy_c_id = ?" : "";
            String andClause = whereClause.isEmpty() ? " WHERE" : " AND";
            
            // 高分段：600分以上
            String sqlHigh = "SELECT form_score_xk, " +
                "COUNT(DISTINCT c_id) as student_count, " +
                "SUM(CASE WHEN lq_is_985 = '1' THEN 1 ELSE 0 END) as count_985, " +
                "SUM(CASE WHEN lq_is_211 = '1' THEN 1 ELSE 0 END) as count_211, " +
                "SUM(CASE WHEN lq_is_syl = '1' THEN 1 ELSE 0 END) as count_syl, " +
                "SUM(CASE WHEN lq_is_hzbx = '1' THEN 1 ELSE 0 END) as count_hzbx " +
                "FROM lhy_school_statistics_form " +
                whereClause + andClause + " form_score_cj >= 600 " +
                "GROUP BY form_score_xk ORDER BY student_count DESC";
            
            ps = conn.prepareStatement(sqlHigh);
            if (schoolLhyCId != null) ps.setString(1, schoolLhyCId);
            SQLLogUtils.printSQL(" ===getScoreSegmentStatistics : ", ps);
            rs = ps.executeQuery();
            
            List<Map<String, Object>> highSegment = new ArrayList<>();
            while (rs.next()) {
                Map<String, Object> item = new HashMap<>();
                item.put("subject_combination", rs.getString("form_score_xk"));
                item.put("student_count", rs.getInt("student_count"));
                item.put("count_985", rs.getInt("count_985"));
                item.put("count_211", rs.getInt("count_211"));
                item.put("count_syl", rs.getInt("count_syl"));
                item.put("count_hzbx", rs.getInt("count_hzbx"));
                highSegment.add(item);
            }
            result.put("high_segment", highSegment);
            rs.close(); ps.close();
            
            // 类似处理中高分段、中分段、专科层次
            // ... 这里可以继续添加其他分数段的统计
            
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return result;
    }
    

    
    /**
     * 获取滑档风险学生名单（支持学校和班级统计）
     * @param schoolLhyCId 学校ID，如果为null则统计所有学校
     * @param classLhyCId 班级ID，如果为null则统计学校级别
     * @return 滑档风险学生列表
     */
    public List<Map<String, Object>> getRiskStudentList(String schoolLhyCId, String classLhyCId) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<Map<String, Object>> result = new ArrayList<>();
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            String whereClause = "";
            if (classLhyCId != null) {
                whereClause = " WHERE class_lhy_c_id = ?";
            } else if (schoolLhyCId != null) {
                whereClause = " WHERE school_lhy_c_id = ?";
            }
            String andClause = whereClause.isEmpty() ? " WHERE" : " AND";
            
            String sql = "SELECT DISTINCT c_id, c_student_name, class_name, form_score_cj " +
                "FROM lhy_school_statistics_form " +
                whereClause + andClause + " (lq_yxsf IS NULL OR lq_yxsf = '') " +
                "ORDER BY form_score_cj DESC";
            
            ps = conn.prepareStatement(sql);
            if (classLhyCId != null) {
                ps.setString(1, classLhyCId);
            } else if (schoolLhyCId != null) {
                ps.setString(1, schoolLhyCId);
            }
            SQLLogUtils.printSQL(" ===getRiskStudentList : ", ps);
            rs = ps.executeQuery();
            
            while (rs.next()) {
                Map<String, Object> student = new HashMap<>();
                student.put("c_id", rs.getString("c_id"));
                student.put("student_name", rs.getString("c_student_name"));
                student.put("class_name", rs.getString("class_name"));
                student.put("score", rs.getInt("form_score_cj"));
                result.add(student);
            }
            
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return result;
    }
    
    /**
     * 获取滑档风险学生名单（保持向后兼容）
     * @param schoolLhyCId 学校ID，如果为null则统计所有学校
     * @return 滑档风险学生列表
     */
    public List<Map<String, Object>> getRiskStudentList(String schoolLhyCId) {
        return getRiskStudentList(schoolLhyCId, null);
    }
    
    /**
     * 获取学校下的班级列表（基于统计表数据）
     * @param schoolLhyCId 学校ID
     * @return 班级列表，每个班级包含class_lhy_c_id和class_name
     */
    public List<Map<String, Object>> getClassList(String schoolLhyCId) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<Map<String, Object>> result = new ArrayList<>();
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            String sql = "SELECT DISTINCT class_lhy_c_id, class_name " +
                "FROM lhy_school_statistics_form " +
                "WHERE school_lhy_c_id = ? AND class_name IS NOT NULL AND class_name != '' " +
                "ORDER BY class_name ASC";
            
            ps = conn.prepareStatement(sql);
            ps.setString(1, schoolLhyCId);
            SQLLogUtils.printSQL(" ===getClassList : ", ps);
            rs = ps.executeQuery();
            
            while (rs.next()) {
                Map<String, Object> classInfo = new HashMap<>();
                classInfo.put("class_lhy_c_id", rs.getString("class_lhy_c_id"));
                classInfo.put("class_name", rs.getString("class_name"));
                result.add(classInfo);
            }
            
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return result;
    }
    
    /**
     * 根据班级ID获取班级名称（基于统计表数据）
     * @param classLhyCId 班级ID
     * @return 班级名称，如果未找到返回null
     */
    public String getClassNameByClassId(String classLhyCId) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String className = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            String sql = "SELECT DISTINCT class_name " +
                "FROM lhy_school_statistics_form " +
                "WHERE class_lhy_c_id = ? AND class_name IS NOT NULL AND class_name != '' " +
                "LIMIT 1";
            
            ps = conn.prepareStatement(sql);
            ps.setString(1, classLhyCId);
            SQLLogUtils.printSQL(" ===getClassNameByClassId : ", ps);
            rs = ps.executeQuery();
            
            if (rs.next()) {
                className = rs.getString("class_name");
            }
            
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return className;
    }
    
    /**
     * 获取综合统计数据（支持学校和班级统计）
     * @param schoolLhyCId 学校ID
     * @param classLhyCId 班级ID，如果为null则统计学校级别
     * @return 综合统计数据
     */
    public Map<String, Object> getComprehensiveStatistics(String schoolLhyCId, String classLhyCId) {
        Map<String, Object> result = new HashMap<>();
        
        // 基础统计
        result.putAll(getBasicStatistics(schoolLhyCId, classLhyCId));
        
        // 录取类型统计
        result.putAll(getAdmissionTypeStatistics(schoolLhyCId, classLhyCId));
        
        // 录取结果统计
        result.putAll(getAdmissionResultStatistics(schoolLhyCId, classLhyCId));
        
        // 浪费分数统计
        result.putAll(getWasteScoreStatistics(schoolLhyCId, classLhyCId));
        
        // 批次统计
        result.putAll(getBatchStatistics(schoolLhyCId, classLhyCId));
        
        // 添加排行榜数据
        result.put("province_ranking", getProvinceRanking(schoolLhyCId, classLhyCId, 10));
        result.put("university_ranking", getUniversityRanking(schoolLhyCId, classLhyCId, 10));
        result.put("major_ranking", getMajorRanking(schoolLhyCId, classLhyCId, 10));
        result.put("city_ranking", getCityRanking(schoolLhyCId, classLhyCId, 10));
        
        return result;
    }
    
    /**
     * 获取浪费分数统计（支持学校和班级统计）
     * @param schoolLhyCId 学校ID
     * @param classLhyCId 班级ID，如果为null则统计学校级别
     * @return 浪费分数统计数据
     */
    public Map<String, Object> getWasteScoreStatistics(String schoolLhyCId, String classLhyCId) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        Map<String, Object> result = new HashMap<>();
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            String whereClause = "";
            if (classLhyCId != null) {
                whereClause = " WHERE class_lhy_c_id = ?";
            } else if (schoolLhyCId != null) {
                whereClause = " WHERE school_lhy_c_id = ?";
            }
            String andClause = whereClause.isEmpty() ? " WHERE" : " AND";
            
            String sql = "SELECT " +
                "AVG(waste_score_cnt) as avg_waste_score, " +
                "MIN(waste_score_cnt) as min_waste_score, " +
                "MAX(waste_score_cnt) as max_waste_score, " +
                "COUNT(CASE WHEN waste_score_cnt = 999 THEN 1 END) as failed_count, " +
                "COUNT(CASE WHEN waste_score_cnt BETWEEN 0 AND 10 THEN 1 END) as waste_0_10, " +
                "COUNT(CASE WHEN waste_score_cnt BETWEEN 11 AND 20 THEN 1 END) as waste_11_20, " +
                "COUNT(CASE WHEN waste_score_cnt BETWEEN 21 AND 50 THEN 1 END) as waste_21_50, " +
                "COUNT(CASE WHEN waste_score_cnt > 50 AND waste_score_cnt != 999 THEN 1 END) as waste_over_50 " +
                "FROM lhy_school_statistics_form " +
                whereClause + andClause + " waste_score_cnt IS NOT NULL";
            
            ps = conn.prepareStatement(sql);
            if (classLhyCId != null) {
                ps.setString(1, classLhyCId);
            } else if (schoolLhyCId != null) {
                ps.setString(1, schoolLhyCId);
            }
            SQLLogUtils.printSQL(" ===getWasteScoreStatistics : ", ps);
            rs = ps.executeQuery();
            
            if (rs.next()) {
                result.put("avg_waste_score", String.format("%.1f", rs.getDouble("avg_waste_score")));
                result.put("min_waste_score", rs.getInt("min_waste_score"));
                result.put("max_waste_score", rs.getInt("max_waste_score"));
                result.put("failed_count", rs.getInt("failed_count"));
                result.put("waste_0_10", rs.getInt("waste_0_10"));
                result.put("waste_11_20", rs.getInt("waste_11_20"));
                result.put("waste_21_50", rs.getInt("waste_21_50"));
                result.put("waste_over_50", rs.getInt("waste_over_50"));
            }
            
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return result;
    }
    
    /**
     * 获取批次统计（支持学校和班级统计）
     * @param schoolLhyCId 学校ID
     * @param classLhyCId 班级ID，如果为null则统计学校级别
     * @return 批次统计数据
     */
    public Map<String, Object> getBatchStatistics(String schoolLhyCId, String classLhyCId) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        Map<String, Object> result = new HashMap<>();
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            String whereClause = "";
            if (classLhyCId != null) {
                whereClause = " WHERE class_lhy_c_id = ?";
            } else if (schoolLhyCId != null) {
                whereClause = " WHERE school_lhy_c_id = ?";
            }
            
            String sql = "SELECT pc_code, pc, COUNT(DISTINCT c_id) as student_count " +
                "FROM lhy_school_statistics_form " + whereClause +
                " GROUP BY pc_code, pc ORDER BY student_count DESC";
            
            ps = conn.prepareStatement(sql);
            if (classLhyCId != null) {
                ps.setString(1, classLhyCId);
            } else if (schoolLhyCId != null) {
                ps.setString(1, schoolLhyCId);
            }
            SQLLogUtils.printSQL(" ===getBatchStatistics : ", ps);
            rs = ps.executeQuery();
            
            List<Map<String, Object>> batchList = new ArrayList<>();
            while (rs.next()) {
                Map<String, Object> item = new HashMap<>();
                item.put("batch_code", rs.getString("pc_code"));
                item.put("batch_name", rs.getString("pc"));
                item.put("student_count", rs.getInt("student_count"));
                batchList.add(item);
            }
            result.put("batch_distribution", batchList);
            
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return result;
    }
    
    /**
     * 获取专业排行榜（支持学校和班级统计）
     * @param schoolLhyCId 学校ID
     * @param classLhyCId 班级ID，如果为null则统计学校级别
     * @param limit 限制返回条数
     * @return 专业排行榜列表
     */
    public List<Map<String, Object>> getMajorRanking(String schoolLhyCId, String classLhyCId, int limit) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<Map<String, Object>> result = new ArrayList<>();
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            String whereClause = "";
            if (classLhyCId != null) {
                whereClause = " WHERE class_lhy_c_id = ?";
            } else if (schoolLhyCId != null) {
                whereClause = " WHERE school_lhy_c_id = ?";
            } else {
                // 如果两个参数都为null，返回空结果，避免查询全部数据
                return result;
            }
            String andClause = whereClause.isEmpty() ? " WHERE" : " AND";
            
            String sql = "SELECT lq_zymc, COUNT(DISTINCT c_id) as student_count " +
                "FROM lhy_school_statistics_form " + 
                whereClause + andClause + " lq_zymc IS NOT NULL AND lq_zymc != '' " +
                "GROUP BY lq_zymc ORDER BY student_count DESC LIMIT " + limit;
            
            ps = conn.prepareStatement(sql);
            if (classLhyCId != null) {
                ps.setString(1, classLhyCId);
            } else if (schoolLhyCId != null) {
                ps.setString(1, schoolLhyCId);
            }
            SQLLogUtils.printSQL(" ===getMajorRanking : ", ps);
            rs = ps.executeQuery();
            
            int rank = 1;
            while (rs.next()) {
                Map<String, Object> item = new HashMap<>();
                item.put("rank", rank++);
                item.put("major_name", rs.getString("lq_zymc"));
                item.put("student_count", rs.getInt("student_count"));
                result.add(item);
            }
            
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return result;
    }
    
    /**
     * 根据分数段获取学生名单（支持学校和班级统计）
     * @param schoolLhyCId 学校ID
     * @param classLhyCId 班级ID，如果为null则统计学校级别
     * @param segment 分数段名称
     * @return 学生列表
     */
    public List<Map<String, Object>> getWasteScoreStudentsBySegment(String schoolLhyCId, String classLhyCId, String segment) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<Map<String, Object>> result = new ArrayList<>();
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            String whereClause = "";
            if (classLhyCId != null) {
                whereClause = " WHERE class_lhy_c_id = ?";
            } else if (schoolLhyCId != null) {
                whereClause = " WHERE school_lhy_c_id = ?";
            }
            
            String wasteCondition = "";
            switch (segment) {
                case "0-10分":
                    wasteCondition = " AND waste_score_cnt BETWEEN 0 AND 10";
                    break;
                case "11-20分":
                    wasteCondition = " AND waste_score_cnt BETWEEN 11 AND 20";
                    break;
                case "21-50分":
                    wasteCondition = " AND waste_score_cnt BETWEEN 21 AND 50";
                    break;
                case "50分以上":
                    wasteCondition = " AND waste_score_cnt > 50 AND waste_score_cnt != 999";
                    break;
                case "落榜":
                    wasteCondition = " AND waste_score_cnt = 999";
                    break;
                default:
                    wasteCondition = " AND waste_score_cnt IS NOT NULL";
            }
            
            String sql = "SELECT DISTINCT " +
                "c_id, " +
                "c_student_name, " +
                "class_name, " +
                "form_score_cj, " +
                "waste_score_cnt " +
                "FROM lhy_school_statistics_form " +
                whereClause + wasteCondition +
                " ORDER BY form_score_cj DESC, waste_score_cnt ASC";
            
            ps = conn.prepareStatement(sql);
            if (classLhyCId != null) {
                ps.setString(1, classLhyCId);
            } else if (schoolLhyCId != null) {
                ps.setString(1, schoolLhyCId);
            }
            SQLLogUtils.printSQL(" ===getWasteScoreStudentsBySegment : ", ps);
            rs = ps.executeQuery();
            
            while (rs.next()) {
                Map<String, Object> student = new HashMap<>();
                student.put("c_id", rs.getString("c_id"));
                student.put("student_name", rs.getString("c_student_name"));
                student.put("class_name", rs.getString("class_name"));
                student.put("score", rs.getInt("form_score_cj"));
                student.put("waste_score", rs.getInt("waste_score_cnt"));
                result.add(student);
            }
            
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return result;
    }
    
    /**
     * 关闭数据库连接
     */
    private static void closeAllConnection(Connection conn, PreparedStatement ps, ResultSet rs) {
        try {
            if (rs != null) rs.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        try {
            if (ps != null) ps.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        try {
            if (conn != null) conn.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        rs = null;
        ps = null;
        conn = null;
    }
}
