package com.career.db;

import java.util.Date;

public class WecomGroupsMoveHistory {

	private String wecom_external_userid; // 外部联系人的userid
    private String wecom_chat_id; // 群ID
    private Integer move_type; // 移动类型 (0-入群，1-退群)
    private Date create_tm; // 记录创建时间
    private Date wecom_join_time; // 成员加入时间戳
    private Integer wecom_join_scene; // 入群方式 (1-由群成员邀请入群 2-由二维码扫码入群 3-管理员导入)
    private String wecom_group_nickname; // 成员在群里的昵称
    private String wecom_invitor_userid; // 邀请人成员ID (仅在由群成员邀请时有值)
	private String ext_wecom_chat_group_name; // 外部联系人的userid
    
	public String getExt_wecom_chat_group_name() {
		return ext_wecom_chat_group_name;
	}
	public void setExt_wecom_chat_group_name(String ext_wecom_chat_group_name) {
		this.ext_wecom_chat_group_name = ext_wecom_chat_group_name;
	}
	public String getWecom_external_userid() {
		return wecom_external_userid;
	}
	public void setWecom_external_userid(String wecom_external_userid) {
		this.wecom_external_userid = wecom_external_userid;
	}
	public String getWecom_chat_id() {
		return wecom_chat_id;
	}
	public void setWecom_chat_id(String wecom_chat_id) {
		this.wecom_chat_id = wecom_chat_id;
	}
	public Integer getMove_type() {
		return move_type;
	}
	public void setMove_type(Integer move_type) {
		this.move_type = move_type;
	}
	public Date getCreate_tm() {
		return create_tm;
	}
	public void setCreate_tm(Date create_tm) {
		this.create_tm = create_tm;
	}
	public Date getWecom_join_time() {
		return wecom_join_time;
	}
	public void setWecom_join_time(Date wecom_join_time) {
		this.wecom_join_time = wecom_join_time;
	}
	public Integer getWecom_join_scene() {
		return wecom_join_scene;
	}
	public void setWecom_join_scene(Integer wecom_join_scene) {
		this.wecom_join_scene = wecom_join_scene;
	}
	public String getWecom_group_nickname() {
		return wecom_group_nickname;
	}
	public void setWecom_group_nickname(String wecom_group_nickname) {
		this.wecom_group_nickname = wecom_group_nickname;
	}
	public String getWecom_invitor_userid() {
		return wecom_invitor_userid;
	}
	public void setWecom_invitor_userid(String wecom_invitor_userid) {
		this.wecom_invitor_userid = wecom_invitor_userid;
	}
    
    
    
}
