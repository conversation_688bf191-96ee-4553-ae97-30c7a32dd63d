package com.career.utils.wecom.service;

import java.util.List;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.career.utils.Tools;
import com.career.utils.wecom.WeComHttpClient;
import com.career.utils.wecom.WeComTokenManager;
import com.career.utils.wecom.config.WeComConfig;
import com.career.utils.wecom.exception.WeComApiException;

/**
 * 企业微信消息发送服务类
 */
public class WeComMessageService {

    // 发送应用消息给企业成员的API (也可以用于发送给单聊、群聊，但这里主要指应用消息)
    private static final String SEND_APP_MESSAGE_URL = "https://qyapi.weixin.qq.com/cgi-bin/message/send";
    // 创建企业群发消息（给客户群）的API
    private static final String ADD_MSG_TEMPLATE_URL = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/add_msg_template";
    // 发送客户消息 (通过企业成员给外部联系人发送消息)
     private static final String SEND_EXTERNAL_CONTACT_MESSAGE_URL = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/message/send";

    /**
     * 发送文本应用消息给企业成员
     * @param corpId 企业ID
     * @param agentId 应用ID
     * @param corpSecret 应用的Secret
     * @param touser 接收成员UserID列表，多个用|分隔 (最多1000个)
     * @param content 文本消息内容
     * @return 响应结果JSONObject
     */
    public static JSONObject sendTextAppMessage(String corpId, int agentId, String corpSecret, String touser, String content) {
        try {
            // 获取access_token
            String accessToken = WeComTokenManager.getAccessToken(corpId, corpSecret);

            // 构建请求参数
            JSONObject params = new JSONObject();
            params.put("touser", touser);
            params.put("msgtype", "text");
            params.put("agentid", agentId);
            JSONObject text = new JSONObject();
            text.put("content", content);
            params.put("text", text);

            String url = String.format("%s?access_token=%s", SEND_APP_MESSAGE_URL, accessToken);
            JSONObject response = WeComHttpClient.doPostWithRetry(url, params.toJSONString());

            // 根据配置输出API响应原始数据
            if (WeComConfig.isLogApiRawJsonResponse()) {
                Tools.println("=== 发送文本应用消息API原始响应数据 ===");
                Tools.println("接收用户: " + touser);
                Tools.println("请求URL: " + url);
                Tools.println("请求参数: " + params.toJSONString());
                Tools.println("原始响应: " + response.toString());
                Tools.println("======================================");
            }

             // 检查API调用是否成功
            int errcode = response.getIntValue("errcode");
            if (errcode != 0) {
                String errmsg = response.getString("errmsg");
                 // 细化错误日志，包含API和参数信息
                 Tools.println(String.format("发送文本应用消息失败. URL: %s, 参数: %s, 错误码: %d, 错误信息: %s", url, params.toJSONString(), errcode, errmsg));
                 throw new WeComApiException("发送文本应用消息失败: " + errmsg); // 抛出异常中断
            }

            Tools.println("成功发送文本应用消息给成员: " + touser);
            return response;

        } catch (Exception e) {
            throw new WeComApiException("发送文本应用消息异常: " + e.getMessage(), e);
        }
    }

     /**
     * 创建企业群发消息任务（给客户群）
     * @param corpId 企业ID
     * @param agentId 应用ID
     * @param corpSecret 应用的Secret
     * @param chatIds 目标客户群ID列表 (最多100个)
     * @param senderUserId 发送成员（执行人）的UserID
     * @param textContent 文本消息内容
     * @return 响应结果JSONObject，包含任务ID等信息
     */
    public static JSONObject createGroupChatMessage(String corpId, int agentId, String corpSecret, List<String> chatIds, String senderUserId, String textContent) {
        try {
            // 获取access_token
            String accessToken = WeComTokenManager.getAccessToken(corpId, corpSecret);

            // 构建请求参数
            JSONObject params = new JSONObject();
            params.put("chat_type", "group"); // 必须指定为group
            params.put("external_userid", new JSONArray()); // 给客户群发，external_userid为空数组
            params.put("chat_id", new JSONArray(chatIds)); // 客户群ID列表
            params.put("sender", senderUserId); // 发送人userid
            params.put("agentid", agentId); // 应用id

            // 消息内容
            JSONArray attachments = new JSONArray();
            JSONObject msg = new JSONObject();
            msg.put("msgtype", "text");
            JSONObject text = new JSONObject();
            text.put("content", textContent);
            msg.put("text", text);
            attachments.add(msg); // 文本消息作为附件添加到 attachments 中

            params.put("attachments", attachments);

            String url = String.format("%s?access_token=%s", ADD_MSG_TEMPLATE_URL, accessToken);
            JSONObject response = WeComHttpClient.doPostWithRetry(url, params.toJSONString());

            // 根据配置输出API响应原始数据
            if (WeComConfig.isLogApiRawJsonResponse()) {
                Tools.println("=== 创建企业群发消息任务API原始响应数据 ===");
                Tools.println("目标群组: " + chatIds.toString());
                Tools.println("发送者: " + senderUserId);
                Tools.println("请求URL: " + url);
                Tools.println("请求参数: " + params.toJSONString());
                Tools.println("原始响应: " + response.toString());
                Tools.println("==========================================");
            }

            // 检查API调用是否成功
            int errcode = response.getIntValue("errcode");
            if (errcode != 0) {
                String errmsg = response.getString("errmsg");
                 // 细化错误日志，包含API和参数信息
                 Tools.println(String.format("创建企业群发消息任务失败. URL: %s, 参数: %s, 错误码: %d, 错误信息: %s", url, params.toJSONString(), errcode, errmsg));
                 throw new WeComApiException("创建企业群发消息任务失败: " + errmsg); // 抛出异常中断
            }

            Tools.println("成功创建企业群发消息任务，任务ID: " + response.getString("msgid"));
            return response;

        } catch (Exception e) {
            throw new WeComApiException("创建企业群发消息任务异常: " + e.getMessage(), e);
        }
    }

     /**
     * 通过企业成员向单个外部联系人发送文本消息
     * 注意：此接口并非直接由第三方应用以自己的身份发送，而是模拟指定成员发送。需要确保应用有客户联系及代发消息权限。
     * 此外，发送前需要成员已添加该外部联系人。
     * @param corpId 企业ID
     * @param agentId 应用ID
     * @param corpSecret 应用的Secret
     * @param senderUserId 发送消息的企业成员UserID
     * @param externalUserId 接收消息的外部联系人UserID
     * @param content 文本消息内容
     * @return 响应结果JSONObject
     */
    public static JSONObject sendExternalContactTextMessage(String corpId, int agentId, String corpSecret, String senderUserId, String externalUserId, String content) {
         try {
            // 获取access_token
            String accessToken = WeComTokenManager.getAccessToken(corpId, corpSecret);

            // 构建请求参数
            JSONObject params = new JSONObject();
            params.put("chat_type", "single"); // 单聊
            JSONArray externalUserIds = new JSONArray();
            externalUserIds.add(externalUserId);
            params.put("external_userid", externalUserIds); // 外部联系人列表
            params.put("sender", senderUserId); // 发送人userid
            params.put("agentid", agentId); // 应用id

            // 消息内容
             JSONObject msg = new JSONObject();
             msg.put("msgtype", "text");
             JSONObject text = new JSONObject();
             text.put("content", content);
             msg.put("text", text);

            params.put("message", msg);

            String url = String.format("%s?access_token=%s", SEND_EXTERNAL_CONTACT_MESSAGE_URL, accessToken);
            JSONObject response = WeComHttpClient.doPostWithRetry(url, params.toJSONString());

            // 根据配置输出API响应原始数据
            if (WeComConfig.isLogApiRawJsonResponse()) {
                Tools.println("=== 发送客户消息API原始响应数据 ===");
                Tools.println("发送者: " + senderUserId);
                Tools.println("接收者: " + externalUserId);
                Tools.println("请求URL: " + url);
                Tools.println("请求参数: " + params.toJSONString());
                Tools.println("原始响应: " + response.toString());
                Tools.println("===================================");
            }

             // 检查API调用是否成功
            int errcode = response.getIntValue("errcode");
            if (errcode != 0) {
                String errmsg = response.getString("errmsg");
                 // 细化错误日志，包含API和参数信息
                 Tools.println(String.format("发送客户消息失败. URL: %s, 参数: %s, 错误码: %d, 错误信息: %s", url, params.toJSONString(), errcode, errmsg));
                 throw new WeComApiException("发送客户消息失败: " + errmsg); // 抛出异常中断
            }

            Tools.println("成功发送客户消息给 " + externalUserId + " (通过成员 " + senderUserId + ")");
            return response;

        } catch (Exception e) {
            throw new WeComApiException("发送客户消息异常: " + e.getMessage(), e);
        }
    }
} 