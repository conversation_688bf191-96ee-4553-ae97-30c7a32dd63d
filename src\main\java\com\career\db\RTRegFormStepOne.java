package com.career.db;

import java.util.List;

public class RTRegFormStepOne {

	 // 基本信息
    private String province;       // 所在省份
    private String name;           // 姓名/昵称
    private String phone;          // 联系电话
    
    // 个人信息
    private String gender;         // 性别（male/female）
    private String graduate_status; // 是否应届（current/previous）
    private String political_status;// 政治面貌（masses/league/party）
    private String ethnicity;      // 民族（han/other）
    
    // 身体特征
    private String height;         // 身高（lt160/160plus/170plus/180plus）
    private String weight;         // 体重（lt90/90plus/110plus/130plus/160plus）
    private String bodyType;       // 体型（average/thin/fat）
    
    // 专业技能
    private String drawing_skill;   // 绘画基础（yes/no）
    private String handHabit;      // 用手习惯（left/right）
    
    // 健康信息
    private List<String> vision_issues;    // 视力问题（nearsighted/strabismus/color_blind/color_weak）
    private List<String> physical_exams;   // 体检结果（hearing/smell/stutter/disabled/mental/disease）
    
    
}
