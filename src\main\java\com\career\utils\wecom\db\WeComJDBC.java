package com.career.utils.wecom.db;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.career.db.DatabaseUtils;
import com.career.utils.ResultVO;
import com.career.utils.SQLLogUtils;
import com.career.utils.wecom.config.WeComConfig;
import com.career.utils.wecom.model.FollowerInfo;
import com.career.utils.wecom.model.GroupChat;
import com.career.utils.wecom.model.GroupChatMember;
import com.career.utils.wecom.model.WeComCustomer;
import com.career.utils.wecom.model.WeComInternalContact;

/**
 * 企业微信数据库访问类
 * 提供企业微信相关数据的增删改查操作
 * 
 * 批量查询优化说明：
 * - 当批量查询的ID数量超过10000条时，自动分块处理，每块2000条
 * - 分块间有间隔时间控制，避免数据库压力过大
 * - 已优化的批量查询方法：
 *   1. batchGetWeComExternalContactFollowers() - 批量获取跟进人关系
 *   2. getWeComExternalContactsByIds() - 批量获取外部联系人
 *   3. getWeComGroupsByChatIds() - 批量获取群组信息
 * 
 * <AUTHOR> Team
 * @version 2.0 (批量查询优化版本)
 */
public class WeComJDBC {

    // 数据库连接配置
    static String URL = "***************************************************************************************************************************";
    static String USER = "cq_kimi_zyzd";
    static String PASSWD = "cq_kimi_zyzd**A";
    public static int PAGE_ROW_CNT = 20;
    
    // 批量查询优化配置
    private static final int DEFAULT_CHUNK_SIZE = 2000;           // 默认分块大小
    private static final int MAX_SINGLE_QUERY_SIZE = 10000;       // 单次查询最大数量
    private static final int MIN_CHUNK_INTERVAL_MS = 50;          // 最小分块间隔（毫秒）
    
    /**
     * 检查是否需要分块处理
     * @param dataSize 数据大小
     * @return 是否需要分块
     */
    private static boolean needsChunking(int dataSize) {
        return dataSize > MAX_SINGLE_QUERY_SIZE;
    }
    
    /**
     * 获取有效的分块间隔时间
     * @return 分块间隔时间（毫秒）
     */
    private static long getEffectiveChunkInterval() {
        long configInterval = WeComConfig.getBatchIntervalMs();
        return Math.max(configInterval, MIN_CHUNK_INTERVAL_MS);
    }
    
    /**
     * 记录分块处理日志
     * @param operation 操作名称
     * @param totalSize 总数据量
     * @param chunkSize 分块大小
     */
    private static void logChunkingInfo(String operation, int totalSize, int chunkSize) {
        if (WeComConfig.isEnableDetailedPhaseLogging()) {
            int totalChunks = (totalSize + chunkSize - 1) / chunkSize;
            System.out.println(operation + "：数据量(" + totalSize + ")超过" + MAX_SINGLE_QUERY_SIZE + 
                    "，将分" + totalChunks + "块处理，每块最多" + chunkSize + "条");
        }
    }
    
    /**
     * 记录分块进度日志
     * @param chunkIndex 当前块索引（从0开始）
     * @param startIndex 起始索引（从0开始）
     * @param endIndex 结束索引（不包含）
     * @param chunkSize 当前块大小
     */
    private static void logChunkProgress(int chunkIndex, int startIndex, int endIndex, int chunkSize) {
        if (WeComConfig.isEnableDetailedPhaseLogging()) {
            System.out.println("处理第" + (chunkIndex + 1) + "块，索引范围: " + startIndex + "-" + (endIndex - 1) + 
                    "，数量: " + chunkSize);
        }
    }

    // ==================== 群组管理 ====================

    /**
     * 添加企业微信群组
     * @param group 群组对象
     * @return 是否添加成功
     */
    public boolean addWeComGroup(GroupChat group) {
        Connection conn = null;
        PreparedStatement ps = null;

        try {
            // 使用连接池获取连接，替代直接DriverManager调用
            conn = DatabaseUtils.getConnection();

            String sql = "INSERT INTO wecom_groups (wecom_chat_id, wecom_name, wecom_owner_userid, wecom_create_time, " +
                    "wecom_member_count, wecom_member_version, wecom_created_at, wecom_updated_at, wecom_saas_id) " +
                    "VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW(), ?)";

            ps = conn.prepareStatement(sql);
            ps.setString(1, group.getChatId());
            ps.setString(2, group.getName());
            ps.setString(3, group.getOwner());
            ps.setTimestamp(4, group.getCreateTime() != null ? new Timestamp(group.getCreateTime().getTime()) : null);
            ps.setObject(5, group.getMemberCount());
            ps.setString(6, group.getMemberVersion());
            ps.setString(7, group.getWecom_saas_id());

            SQLLogUtils.printSQL("=== addWeComGroup: ", ps);

            return ps.executeUpdate() > 0;

        } catch (Exception e) {
            System.err.println("添加企业微信群组失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        } finally {
            // 使用统一的资源关闭方法
            DatabaseUtils.closeAllResources(null, ps, conn);
        }
    }

    /**
     * 删除企业微信群组
     * @param chatId 群组ID
     * @return 是否删除成功
     */
    public boolean deleteWeComGroup(String chatId) {
        Connection conn = null;
        PreparedStatement ps = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "DELETE FROM wecom_groups WHERE wecom_chat_id = ?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, chatId);
            
            SQLLogUtils.printSQL("=== deleteWeComGroup: ", ps);
            
            return ps.executeUpdate() > 0;
            
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            closeAllConnection(conn, ps, null);
        }
    }

    /**
     * 更新企业微信群组
     * @param group 群组对象
     * @return 是否更新成功
     */
    public boolean updateWeComGroup(GroupChat group) {
        Connection conn = null;
        PreparedStatement ps = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            String sql = "UPDATE wecom_groups SET wecom_name = ?, wecom_owner_userid = ?, wecom_create_time = ?, " +
                    "wecom_member_count = ?, wecom_member_version = ?, wecom_updated_at = NOW() " +
                    "WHERE wecom_chat_id = ? AND wecom_saas_id = ?";
            
            ps = conn.prepareStatement(sql);
            ps.setString(1, group.getName());
            ps.setString(2, group.getOwner());
            ps.setTimestamp(3, group.getCreateTime() != null ? new Timestamp(group.getCreateTime().getTime()) : null);
            ps.setObject(4, group.getMemberCount());
            ps.setString(5, group.getMemberVersion());
            ps.setString(6, group.getChatId());
            ps.setString(7, group.getWecom_saas_id());
            
            SQLLogUtils.printSQL("=== updateWeComGroup: ", ps);
            
            return ps.executeUpdate() > 0;
            
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            closeAllConnection(conn, ps, null);
        }
    }

    /**
     * 根据群组ID获取群组信息
     * @param chatId 群组ID
     * @return 群组对象
     */
    public GroupChat getWeComGroupById(String chatId) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "SELECT * FROM wecom_groups WHERE wecom_chat_id = ?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, chatId);
            
            SQLLogUtils.printSQL("=== getWeComGroupById: ", ps);
            
            rs = ps.executeQuery();
            if (rs.next()) {
                return convertToGroupChat(rs);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return null;
    }

    /**
     * 获取所有企业微信群组
     * @return 群组列表
     */
    public List<GroupChat> getAllWeComGroups() {
        List<GroupChat> list = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "SELECT * FROM wecom_groups ORDER BY wecom_created_at DESC";
            ps = conn.prepareStatement(sql);
            
            SQLLogUtils.printSQL("=== getAllWeComGroups: ", ps);
            
            rs = ps.executeQuery();
            while (rs.next()) {
                GroupChat group = convertToGroupChat(rs);
                list.add(group);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return list;
    }

    /**
     * 根据SaaS ID获取所有企业微信群组
     * @param wecom_saas_id SaaS ID
     * @return 群组列表
     */
    public List<GroupChat> getAllWeComGroups(String wecom_saas_id) {
        List<GroupChat> list = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "SELECT * FROM wecom_groups WHERE wecom_saas_id = ? ORDER BY wecom_created_at DESC";
            ps = conn.prepareStatement(sql);
            ps.setString(1, wecom_saas_id);
            
            SQLLogUtils.printSQL("=== getAllWeComGroups: ", ps);
            
            rs = ps.executeQuery();
            while (rs.next()) {
                GroupChat group = convertToGroupChat(rs);
                list.add(group);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return list;
    }

    /**
     * 分页获取企业微信群组
     * @param pageNumber 页码
     * @return 结果对象
     */
    public ResultVO getWeComGroupsByPage(int pageNumber) {
        ResultVO resultVO = new ResultVO();
        List<GroupChat> list = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            // 计算偏移量
            int offset = (pageNumber - 1) * PAGE_ROW_CNT;
            
            // 获取总数
            String countSql = "SELECT COUNT(*) as total FROM wecom_groups";
            PreparedStatement countPs = conn.prepareStatement(countSql);
            ResultSet countRs = countPs.executeQuery();
            int total = 0;
            if (countRs.next()) {
                total = countRs.getInt("total");
            }
            countRs.close();
            countPs.close();
            
            // 获取分页数据
            String sql = "SELECT * FROM wecom_groups ORDER BY wecom_created_at DESC LIMIT ? OFFSET ?";
            ps = conn.prepareStatement(sql);
            ps.setInt(1, PAGE_ROW_CNT);
            ps.setInt(2, offset);
            
            SQLLogUtils.printSQL("=== getWeComGroupsByPage: ", ps);
            
            rs = ps.executeQuery();
            while (rs.next()) {
                GroupChat group = convertToGroupChat(rs);
                list.add(group);
            }
            
            resultVO.setResult(list);
            resultVO.setRecordCnt(total);
            resultVO.setCurrentPage(pageNumber);
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return resultVO;
    }

    /**
     * 根据群主获取群组列表
     * @param ownerUserId 群主用户ID
     * @return 群组列表
     */
    public List<GroupChat> getWeComGroupsByOwner(String ownerUserId) {
        List<GroupChat> list = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "SELECT * FROM wecom_groups WHERE wecom_owner_userid = ? ORDER BY wecom_created_at DESC";
            ps = conn.prepareStatement(sql);
            ps.setString(1, ownerUserId);
            
            SQLLogUtils.printSQL("=== getWeComGroupsByOwner: ", ps);
            
            rs = ps.executeQuery();
            while (rs.next()) {
                GroupChat group = convertToGroupChat(rs);
                list.add(group);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return list;
    }

    /**
     * 将ResultSet转换为GroupChat对象
     * @param rs ResultSet对象
     * @return GroupChat对象
     */
    private GroupChat convertToGroupChat(ResultSet rs) throws SQLException {
        GroupChat group = new GroupChat();
        group.setChatId(rs.getString("wecom_chat_id"));
        group.setName(rs.getString("wecom_name"));
        group.setOwner(rs.getString("wecom_owner_userid"));
        group.setCreateTime(rs.getTimestamp("wecom_create_time"));
        group.setMemberCount(rs.getObject("wecom_member_count") != null ? rs.getInt("wecom_member_count") : null);
        group.setMemberVersion(rs.getString("wecom_member_version"));
        group.setCreatedAt(rs.getTimestamp("wecom_created_at"));
        group.setUpdatedAt(rs.getTimestamp("wecom_updated_at"));
        group.setWecom_saas_id(rs.getString("wecom_saas_id"));
        return group;
    }

    // ==================== 外部联系人管理 ====================

    /**
     * 添加外部联系人
     * @param contact 外部联系人对象
     * @return 是否添加成功
     */
    public boolean addWeComExternalContact(WeComCustomer contact) {
        Connection conn = null;
        PreparedStatement ps = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            String sql = "INSERT INTO wecom_external_contacts (wecom_external_userid, wecom_name, " +
                    "wecom_type, wecom_avatar, wecom_gender, wecom_follow_users_json, wecom_join_tm, " +
                    "sys_create_tm, sys_update_tm, wecom_saas_id, wecom_external_sync_time) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), ?, ?)";
            
            ps = conn.prepareStatement(sql);
            ps.setString(1, contact.getExternalUserId());
            ps.setString(2, contact.getName());
            ps.setObject(3, contact.getType());
            ps.setString(4, contact.getAvatar());
            ps.setObject(5, contact.getGender());
            ps.setString(6, contact.getFollowUsersJson());
            ps.setTimestamp(7, contact.getJoinTime() != null ? new Timestamp(contact.getJoinTime().getTime()) : null);
            ps.setString(8, contact.getWecom_saas_id());
            ps.setTimestamp(9, contact.getExternalSyncTime() != null ? new Timestamp(contact.getExternalSyncTime().getTime()) : null);
            
            SQLLogUtils.printSQL("=== addWeComExternalContact: ", ps);
            
            return ps.executeUpdate() > 0;
            
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            closeAllConnection(conn, ps, null);
        }
    }

    /**
     * 删除外部联系人
     * @param externalUserId 外部联系人ID
     * @return 是否删除成功
     */
    public boolean deleteWeComExternalContact(String externalUserId) {
        Connection conn = null;
        PreparedStatement ps = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "DELETE FROM wecom_external_contacts WHERE wecom_external_userid = ?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, externalUserId);
            
            return ps.executeUpdate() > 0;
            
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            closeAllConnection(conn, ps, null);
        }
    }

    /**
     * 更新外部联系人
     * @param contact 外部联系人对象
     * @return 是否更新成功
     */
    public boolean updateWeComExternalContact(WeComCustomer contact) {
        Connection conn = null;
        PreparedStatement ps = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            String sql = "UPDATE wecom_external_contacts SET " +
                    "wecom_name = ?, wecom_type = ?, wecom_avatar = ?, wecom_gender = ?, " +
                    "wecom_follow_users_json = ?, wecom_join_tm = ?, wecom_external_sync_time = ?, " +
                    "sys_update_tm = NOW() " +
                    "WHERE wecom_external_userid = ? AND wecom_saas_id = ?";
            
            ps = conn.prepareStatement(sql);
            ps.setString(1, contact.getName());
            ps.setObject(2, contact.getType());
            ps.setString(3, contact.getAvatar());
            ps.setObject(4, contact.getGender());
            ps.setString(5, contact.getFollowUsersJson());
            ps.setTimestamp(6, contact.getJoinTime() != null ? new Timestamp(contact.getJoinTime().getTime()) : null);
            ps.setTimestamp(7, contact.getExternalSyncTime() != null ? new Timestamp(contact.getExternalSyncTime().getTime()) : null);
            ps.setString(8, contact.getExternalUserId());
            ps.setString(9, contact.getWecom_saas_id());
            
            SQLLogUtils.printSQL("=== updateWeComExternalContact: ", ps);
            
            return ps.executeUpdate() > 0;
            
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            closeAllConnection(conn, ps, null);
        }
    }

    /**
     * 根据外部联系人ID获取联系人信息
     * @param externalUserId 外部联系人ID
     * @return 外部联系人对象
     */
    public WeComCustomer getWeComExternalContactById(String externalUserId) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "SELECT * FROM wecom_external_contacts WHERE wecom_external_userid = ?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, externalUserId);
            
            SQLLogUtils.printSQL("=== getWeComExternalContactById: ", ps);
            
            rs = ps.executeQuery();
            if (rs.next()) {
                return convertToWeComCustomer(rs);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return null;
    }

    /**
     * 获取所有外部联系人
     * @return 外部联系人列表
     */
    public List<WeComCustomer> getAllWeComExternalContacts() {
        List<WeComCustomer> list = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "SELECT * FROM wecom_external_contacts ORDER BY sys_create_tm DESC";
            ps = conn.prepareStatement(sql);
            
            SQLLogUtils.printSQL("=== getAllWeComExternalContacts: ", ps);
            
            rs = ps.executeQuery();
            while (rs.next()) {
                WeComCustomer customer = convertToWeComCustomer(rs);
                list.add(customer);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return list;
    }

    /**
     * 根据SaaS ID获取所有外部联系人
     * @param wecom_saas_id SaaS ID
     * @return 外部联系人列表
     */
    public List<WeComCustomer> getAllWeComExternalContacts(String wecom_saas_id) {
        List<WeComCustomer> list = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "SELECT * FROM wecom_external_contacts WHERE wecom_saas_id = ?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, wecom_saas_id);
            
            SQLLogUtils.printSQL("=== getAllWeComExternalContacts: ", ps);
            
            rs = ps.executeQuery();
            while (rs.next()) {
                WeComCustomer customer = convertToWeComCustomer(rs);
                list.add(customer);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return list;
    }

    /**
     * 分页获取外部联系人
     * @param pageNumber 页码
     * @return 结果对象
     */
    public ResultVO getWeComExternalContactsByPage(int pageNumber) {
        ResultVO resultVO = new ResultVO();
        List<WeComCustomer> list = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            // 计算偏移量
            int offset = (pageNumber - 1) * PAGE_ROW_CNT;
            
            // 获取总数
            String countSql = "SELECT COUNT(*) as total FROM wecom_external_contacts";
            PreparedStatement countPs = conn.prepareStatement(countSql);
            ResultSet countRs = countPs.executeQuery();
            int total = 0;
            if (countRs.next()) {
                total = countRs.getInt("total");
            }
            countRs.close();
            countPs.close();
            
            // 获取分页数据
            String sql = "SELECT * FROM wecom_external_contacts ORDER BY sys_create_tm DESC LIMIT ? OFFSET ?";
            ps = conn.prepareStatement(sql);
            ps.setInt(1, PAGE_ROW_CNT);
            ps.setInt(2, offset);
            
            SQLLogUtils.printSQL("=== getWeComExternalContactsByPage: ", ps);
            
            rs = ps.executeQuery();
            while (rs.next()) {
                WeComCustomer customer = convertToWeComCustomer(rs);
                list.add(customer);
            }
            
            resultVO.setResult(list);
            resultVO.setRecordCnt(total);
            resultVO.setCurrentPage(pageNumber);
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return resultVO;
    }

    /**
     * 根据姓名查询外部联系人
     * @param name 姓名
     * @return 外部联系人列表
     */
    public List<WeComCustomer> getWeComExternalContactsByName(String name) {
        List<WeComCustomer> list = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "SELECT * FROM wecom_external_contacts WHERE wecom_name LIKE ? ORDER BY wecom_created_at DESC";
            ps = conn.prepareStatement(sql);
            ps.setString(1, "%" + name + "%");
            
            SQLLogUtils.printSQL("=== getWeComExternalContactsByName: ", ps);
            
            rs = ps.executeQuery();
            while (rs.next()) {
                WeComCustomer customer = convertToWeComCustomer(rs);
                list.add(customer);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return list;
    }

    /**
     * 将ResultSet转换为WeComCustomer对象
     * @param rs ResultSet对象
     * @return WeComCustomer对象
     */
    private WeComCustomer convertToWeComCustomer(ResultSet rs) throws SQLException {
        WeComCustomer contact = new WeComCustomer();
        contact.setExternalUserId(rs.getString("wecom_external_userid"));
        contact.setName(rs.getString("wecom_name"));
        contact.setType(rs.getObject("wecom_type") != null ? rs.getInt("wecom_type") : null);
        contact.setAvatar(rs.getString("wecom_avatar"));
        contact.setGender(rs.getObject("wecom_gender") != null ? rs.getInt("wecom_gender") : null);
        contact.setFollowUsersJson(rs.getString("wecom_follow_users_json"));
        contact.setJoinTime(rs.getTimestamp("wecom_join_tm"));
        contact.setCreatedAt(rs.getTimestamp("sys_create_tm"));
        contact.setUpdatedAt(rs.getTimestamp("sys_update_tm"));
        contact.setWecom_saas_id(rs.getString("wecom_saas_id"));
        contact.setExternalSyncTime(rs.getTimestamp("wecom_external_sync_time"));
        return contact;
    }

    // ==================== 批量操作方法 ====================

    /**
     * 批量添加外部联系人
     * @param contacts 外部联系人列表
     * @return 添加成功的数量
     */
    public int batchAddWeComExternalContacts(List<WeComCustomer> contacts) {
        if (contacts == null || contacts.isEmpty()) {
            return 0;
        }
        
        Connection conn = null;
        PreparedStatement ps = null;
        int successCount = 0;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            conn.setAutoCommit(false);
            
            String sql = "INSERT INTO wecom_external_contacts (wecom_external_userid, wecom_name, " +
                    "wecom_type, wecom_avatar, wecom_gender, wecom_follow_users_json, wecom_join_tm, " +
                    "sys_create_tm, sys_update_tm, wecom_saas_id, wecom_external_sync_time) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), ?, ?)";
            
            ps = conn.prepareStatement(sql);
            
            int batchCount = 0;  // 添加批次计数器
            int maxBatchSize = Math.min(50, WeComConfig.getBatchSize()); // 限制最大批次大小
            
            for (WeComCustomer contact : contacts) {
                ps.setString(1, contact.getExternalUserId());
                ps.setString(2, contact.getName());
                ps.setObject(3, contact.getType());
                ps.setString(4, contact.getAvatar());
                ps.setObject(5, contact.getGender());
                ps.setString(6, contact.getFollowUsersJson());
                ps.setTimestamp(7, contact.getJoinTime() != null ? new Timestamp(contact.getJoinTime().getTime()) : null);
                ps.setString(8, contact.getWecom_saas_id());
                ps.setTimestamp(9, contact.getExternalSyncTime() != null ? new Timestamp(contact.getExternalSyncTime().getTime()) : null);
                
                ps.addBatch();
                batchCount++;  // 增加批次计数器
                
                if (batchCount % maxBatchSize == 0) {
                    try {
                        int[] results = ps.executeBatch();
                        for (int result : results) {
                            if (result > 0) successCount++;
                        }
                        conn.commit(); // 每个小批次提交一次事务
                        ps.clearBatch();
                    } catch (Exception e) {
                        // 处理批次错误，记录错误但继续处理
                        System.err.println("批量添加外部联系人时出现错误: " + e.getMessage());
                        try {
                            conn.rollback();
                        } catch (Exception rollbackEx) {
                            rollbackEx.printStackTrace();
                        }
                    }
                    batchCount = 0;  // 重置批次计数器
                }
            }
            
            // 执行剩余的批处理
            if (batchCount > 0) {
                try {
                    int[] results = ps.executeBatch();
                    for (int result : results) {
                        if (result > 0) successCount++;
                    }
                    conn.commit();
                } catch (Exception e) {
                    System.err.println("批量添加剩余外部联系人时出现错误: " + e.getMessage());
                    try {
                        conn.rollback();
                    } catch (Exception rollbackEx) {
                        rollbackEx.printStackTrace();
                    }
                }
            }
            
            return successCount;
            
        } catch (Exception e) {
            e.printStackTrace();
            try {
                if (conn != null) conn.rollback();
            } catch (Exception rollbackEx) {
                rollbackEx.printStackTrace();
            }
            return successCount; // 返回已成功处理的数量，而不是0
        } finally {
            try {
                if (conn != null) conn.setAutoCommit(true);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            closeAllConnection(conn, ps, null);
        }
    }

    /**
     * 批量更新外部联系人
     * @param contacts 外部联系人列表
     * @return 更新成功的数量
     */
    public int batchUpdateWeComExternalContacts(List<WeComCustomer> contacts) {
        if (contacts == null || contacts.isEmpty()) {
            return 0;
        }
        
        Connection conn = null;
        PreparedStatement ps = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            conn.setAutoCommit(false);
            
            String sql = "UPDATE wecom_external_contacts SET " +
                    "wecom_name = ?, wecom_type = ?, wecom_avatar = ?, wecom_gender = ?, " +
                    "wecom_follow_users_json = ?, wecom_join_tm = ?, wecom_external_sync_time = ?, " +
                    "sys_update_tm = NOW() " +
                    "WHERE wecom_external_userid = ? AND wecom_saas_id = ?";
            
            ps = conn.prepareStatement(sql);
            
            int successCount = 0;
            int batchCount = 0;  // 添加批次计数器
            
            for (WeComCustomer contact : contacts) {
                ps.setString(1, contact.getName());
                ps.setObject(2, contact.getType());
                ps.setString(3, contact.getAvatar());
                ps.setObject(4, contact.getGender());
                ps.setString(5, contact.getFollowUsersJson());
                ps.setTimestamp(6, contact.getJoinTime() != null ? new Timestamp(contact.getJoinTime().getTime()) : null);
                ps.setTimestamp(7, contact.getExternalSyncTime() != null ? new Timestamp(contact.getExternalSyncTime().getTime()) : null);
                ps.setString(8, contact.getExternalUserId());
                ps.setString(9, contact.getWecom_saas_id());
                
                ps.addBatch();
                batchCount++;  // 增加批次计数器
                
                if (batchCount % WeComConfig.getBatchSize() == 0) {
                    int[] results = ps.executeBatch();
                    for (int result : results) {
                        if (result > 0) successCount++;
                    }
                    ps.clearBatch();
                    batchCount = 0;  // 重置批次计数器
                }
            }
            
            // 执行剩余的批处理
            if (batchCount > 0) {
                int[] results = ps.executeBatch();
                for (int result : results) {
                    if (result > 0) successCount++;
                }
            }
            
            conn.commit();
            return successCount;
            
        } catch (Exception e) {
            e.printStackTrace();
            try {
                if (conn != null) conn.rollback();
            } catch (Exception rollbackEx) {
                rollbackEx.printStackTrace();
            }
            return 0;
        } finally {
            try {
                if (conn != null) conn.setAutoCommit(true);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            closeAllConnection(conn, ps, null);
        }
    }

    /**
     * 根据外部联系人ID列表批量获取外部联系人信息
     * @param externalUserIds 外部联系人ID列表
     * @return 外部联系人信息映射表，key为externalUserId
     */
    public Map<String, WeComCustomer> getWeComExternalContactsByIds(List<String> externalUserIds) {
        Map<String, WeComCustomer> result = new HashMap<>();
        if (externalUserIds == null || externalUserIds.isEmpty()) {
            return result;
        }
        
        // 如果数据量小于等于阈值，使用单次查询
        if (!needsChunking(externalUserIds.size())) {
            return executeSingleExternalContactQuery(externalUserIds);
        }
        
        // 数据量大时，分块处理
        logChunkingInfo("批量查询外部联系人", externalUserIds.size(), DEFAULT_CHUNK_SIZE);
        
        // 分块处理
        for (int i = 0; i < externalUserIds.size(); i += DEFAULT_CHUNK_SIZE) {
            int endIndex = Math.min(i + DEFAULT_CHUNK_SIZE, externalUserIds.size());
            List<String> chunk = externalUserIds.subList(i, endIndex);
            
            logChunkProgress(i / DEFAULT_CHUNK_SIZE, i, endIndex, chunk.size());
            
            Map<String, WeComCustomer> chunkResult = executeSingleExternalContactQuery(chunk);
            result.putAll(chunkResult);
            
            // 分块间隔，避免数据库压力过大
            if (endIndex < externalUserIds.size()) {
                try {
                    Thread.sleep(getEffectiveChunkInterval());
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        if (WeComConfig.isEnableDetailedPhaseLogging()) {
            System.out.println("批量查询外部联系人完成：总共获取" + result.size() + "条记录");
        }
        
        return result;
    }
    
    /**
     * 执行单次批量查询外部联系人
     */
    private Map<String, WeComCustomer> executeSingleExternalContactQuery(List<String> externalUserIds) {
        Map<String, WeComCustomer> result = new HashMap<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            // 构建IN语句的占位符
            StringBuilder inClause = new StringBuilder();
            for (int i = 0; i < externalUserIds.size(); i++) {
                if (i > 0) inClause.append(", ");
                inClause.append("?");
            }
            
            String sql = "SELECT * FROM wecom_external_contacts WHERE wecom_external_userid IN (" + inClause + ")";
            ps = conn.prepareStatement(sql);
            
            // 设置参数
            for (int i = 0; i < externalUserIds.size(); i++) {
                ps.setString(i + 1, externalUserIds.get(i));
            }
            
            SQLLogUtils.printSQL("=== getWeComExternalContactsByIds: ", ps);
            
            rs = ps.executeQuery();
            while (rs.next()) {
                WeComCustomer contact = convertToWeComCustomer(rs);
                result.put(contact.getExternalUserId(), contact);
            }
            
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return result;
    }

    /**
     * 获取需要更新跟进人信息的外部联系人列表
     * @return 跟进人信息为空的外部联系人列表
     */
    public List<WeComCustomer> getExternalContactsWithoutFollowers() {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "SELECT * FROM wecom_external_contacts WHERE wecom_follow_users_json IS NULL OR wecom_follow_users_json = ''";
            ps = conn.prepareStatement(sql);
            
            SQLLogUtils.printSQL("=== getExternalContactsWithoutFollowers: ", ps);
            
            rs = ps.executeQuery();
            List<WeComCustomer> result = new ArrayList<>();
            
            while (rs.next()) {
                WeComCustomer customer = convertToWeComCustomer(rs);
                result.add(customer);
            }
            
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        } finally {
            closeAllConnection(conn, ps, rs);
        }
    }

    // ==================== 支持WeComSyncService的查询方法 ====================

    /**
     * 根据群ID列表批量获取群组信息
     * @param chatIds 群ID列表
     * @return 群信息映射表，key为chatId
     */
    public Map<String, GroupChat> getWeComGroupsByChatIds(List<String> chatIds) {
        Map<String, GroupChat> result = new HashMap<>();
        if (chatIds == null || chatIds.isEmpty()) {
            return result;
        }
        
        // 如果数据量小于等于阈值，使用单次查询
        if (!needsChunking(chatIds.size())) {
            return executeSingleGroupQuery(chatIds);
        }
        
        // 数据量大时，分块处理
        logChunkingInfo("批量查询群组信息", chatIds.size(), DEFAULT_CHUNK_SIZE);
        
        // 分块处理
        for (int i = 0; i < chatIds.size(); i += DEFAULT_CHUNK_SIZE) {
            int endIndex = Math.min(i + DEFAULT_CHUNK_SIZE, chatIds.size());
            List<String> chunk = chatIds.subList(i, endIndex);
            
            logChunkProgress(i / DEFAULT_CHUNK_SIZE, i, endIndex, chunk.size());
            
            Map<String, GroupChat> chunkResult = executeSingleGroupQuery(chunk);
            result.putAll(chunkResult);
            
            // 分块间隔，避免数据库压力过大
            if (endIndex < chatIds.size()) {
                try {
                    Thread.sleep(getEffectiveChunkInterval());
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        if (WeComConfig.isEnableDetailedPhaseLogging()) {
            System.out.println("批量查询群组信息完成：总共获取" + result.size() + "条记录");
        }
        
        return result;
    }
    
    /**
     * 执行单次批量查询群组信息
     */
    private Map<String, GroupChat> executeSingleGroupQuery(List<String> chatIds) {
        Map<String, GroupChat> result = new HashMap<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            // 构建IN语句的占位符
            StringBuilder inClause = new StringBuilder();
            for (int i = 0; i < chatIds.size(); i++) {
                if (i > 0) inClause.append(", ");
                inClause.append("?");
            }
            
            String sql = "SELECT * FROM wecom_groups WHERE wecom_chat_id IN (" + inClause + ")";
            ps = conn.prepareStatement(sql);
            
            // 设置参数
            for (int i = 0; i < chatIds.size(); i++) {
                ps.setString(i + 1, chatIds.get(i));
            }
            
            SQLLogUtils.printSQL("=== getWeComGroupsByChatIds: ", ps);
            
            rs = ps.executeQuery();
            while (rs.next()) {
                GroupChat group = convertToGroupChat(rs);
                result.put(group.getChatId(), group);
            }
            
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return result;
    }

    // ==================== 内部联系人管理 ====================

    /**
     * 添加内部联系人
     * @param contact 内部联系人对象
     * @return 是否添加成功
     */
    public boolean addWeComInternalContact(WeComInternalContact contact) {
        Connection conn = null;
        PreparedStatement ps = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            String sql = "INSERT INTO wecom_internal_contacts (wecom_user_id, wecom_name, wecom_mobile, " +
                    "wecom_email, wecom_position, wecom_gender, wecom_avatar, wecom_thumb_avatar, " +
                    "wecom_telephone, wecom_alias, wecom_address, wecom_open_userid, wecom_main_department, " +
                    "wecom_status, wecom_qr_code, wecom_department_json, wecom_order_json, " +
                    "wecom_is_leader_in_dept_json, wecom_direct_leader_json, wecom_ext_attr_json, " +
                    "wecom_external_position, wecom_external_profile_json, wecom_saas_id, " +
                    "wecom_sync_time, wecom_created_at, wecom_updated_at) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
            
            ps = conn.prepareStatement(sql);
            ps.setString(1, contact.getUserId());
            ps.setString(2, contact.getName());
            ps.setString(3, contact.getMobile());
            ps.setString(4, contact.getEmail());
            ps.setString(5, contact.getPosition());
            ps.setObject(6, contact.getGender());
            ps.setString(7, contact.getAvatar());
            ps.setString(8, contact.getThumbAvatar());
            ps.setString(9, contact.getTelephone());
            ps.setString(10, contact.getAlias());
            ps.setString(11, contact.getAddress());
            ps.setString(12, contact.getOpenUserId());
            ps.setObject(13, contact.getMainDepartment());
            ps.setObject(14, contact.getStatus());
            ps.setString(15, contact.getQrCode());
            ps.setString(16, contact.getDepartmentJson());
            ps.setString(17, contact.getOrderJson());
            ps.setString(18, contact.getIsLeaderInDeptJson());
            ps.setString(19, contact.getDirectLeaderJson());
            ps.setString(20, contact.getExtAttrJson());
            ps.setString(21, contact.getExternalPosition());
            ps.setString(22, contact.getExternalProfileJson());
            ps.setString(23, contact.getWecom_saas_id());
            ps.setTimestamp(24, contact.getSyncTime() != null ? new Timestamp(contact.getSyncTime().getTime()) : null);
            
            SQLLogUtils.printSQL("=== addWeComInternalContact: ", ps);
            
            return ps.executeUpdate() > 0;
            
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            closeAllConnection(conn, ps, null);
        }
    }

    /**
     * 删除内部联系人
     * @param userId 用户ID
     * @return 是否删除成功
     */
    public boolean deleteWeComInternalContact(String userId) {
        Connection conn = null;
        PreparedStatement ps = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "DELETE FROM wecom_internal_contacts WHERE wecom_user_id = ?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, userId);
            
            SQLLogUtils.printSQL("=== deleteWeComInternalContact: ", ps);
            
            return ps.executeUpdate() > 0;
            
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            closeAllConnection(conn, ps, null);
        }
    }

    /**
     * 更新内部联系人
     * @param contact 内部联系人对象
     * @return 是否更新成功
     */
    public boolean updateWeComInternalContact(WeComInternalContact contact) {
        Connection conn = null;
        PreparedStatement ps = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            String sql = "UPDATE wecom_internal_contacts SET " +
                    "wecom_name = ?, wecom_mobile = ?, wecom_email = ?, wecom_position = ?, " +
                    "wecom_gender = ?, wecom_avatar = ?, wecom_thumb_avatar = ?, wecom_telephone = ?, " +
                    "wecom_alias = ?, wecom_address = ?, wecom_open_userid = ?, wecom_main_department = ?, " +
                    "wecom_status = ?, wecom_qr_code = ?, wecom_department_json = ?, wecom_order_json = ?, " +
                    "wecom_is_leader_in_dept_json = ?, wecom_direct_leader_json = ?, wecom_ext_attr_json = ?, " +
                    "wecom_external_position = ?, wecom_external_profile_json = ?, wecom_sync_time = ?, " +
                    "wecom_updated_at = NOW() " +
                    "WHERE wecom_user_id = ? AND wecom_saas_id = ?";
            
            ps = conn.prepareStatement(sql);
            ps.setString(1, contact.getName());
            ps.setString(2, contact.getMobile());
            ps.setString(3, contact.getEmail());
            ps.setString(4, contact.getPosition());
            ps.setObject(5, contact.getGender());
            ps.setString(6, contact.getAvatar());
            ps.setString(7, contact.getThumbAvatar());
            ps.setString(8, contact.getTelephone());
            ps.setString(9, contact.getAlias());
            ps.setString(10, contact.getAddress());
            ps.setString(11, contact.getOpenUserId());
            ps.setObject(12, contact.getMainDepartment());
            ps.setObject(13, contact.getStatus());
            ps.setString(14, contact.getQrCode());
            ps.setString(15, contact.getDepartmentJson());
            ps.setString(16, contact.getOrderJson());
            ps.setString(17, contact.getIsLeaderInDeptJson());
            ps.setString(18, contact.getDirectLeaderJson());
            ps.setString(19, contact.getExtAttrJson());
            ps.setString(20, contact.getExternalPosition());
            ps.setString(21, contact.getExternalProfileJson());
            ps.setTimestamp(22, contact.getSyncTime() != null ? new Timestamp(contact.getSyncTime().getTime()) : null);
            ps.setString(23, contact.getUserId());
            ps.setString(24, contact.getWecom_saas_id());
            
            SQLLogUtils.printSQL("=== updateWeComInternalContact: ", ps);
            
            return ps.executeUpdate() > 0;
            
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            closeAllConnection(conn, ps, null);
        }
    }

    /**
     * 根据用户ID获取内部联系人信息
     * @param userId 用户ID
     * @return 内部联系人对象
     */
    public WeComInternalContact getWeComInternalContactById(String userId) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "SELECT * FROM wecom_internal_contacts WHERE wecom_user_id = ?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, userId);
            
            SQLLogUtils.printSQL("=== getWeComInternalContactById: ", ps);
            
            rs = ps.executeQuery();
            if (rs.next()) {
                return convertToWeComInternalContact(rs);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return null;
    }

    /**
     * 获取所有内部联系人
     * @return 内部联系人列表
     */
    public List<WeComInternalContact> getAllWeComInternalContacts() {
        List<WeComInternalContact> list = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "SELECT * FROM wecom_internal_contacts ORDER BY wecom_created_at DESC";
            ps = conn.prepareStatement(sql);
            
            SQLLogUtils.printSQL("=== getAllWeComInternalContacts: ", ps);
            
            rs = ps.executeQuery();
            while (rs.next()) {
                WeComInternalContact contact = convertToWeComInternalContact(rs);
                list.add(contact);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return list;
    }

    /**
     * 根据SaaS ID获取所有内部联系人
     * @param wecom_saas_id SaaS ID
     * @return 内部联系人列表
     */
    public List<WeComInternalContact> getAllWeComInternalContacts(String wecom_saas_id) {
        List<WeComInternalContact> list = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "SELECT * FROM wecom_internal_contacts WHERE wecom_saas_id = ? ORDER BY wecom_created_at DESC";
            ps = conn.prepareStatement(sql);
            ps.setString(1, wecom_saas_id);
            
            SQLLogUtils.printSQL("=== getAllWeComInternalContacts: ", ps);
            
            rs = ps.executeQuery();
            while (rs.next()) {
                WeComInternalContact contact = convertToWeComInternalContact(rs);
                list.add(contact);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return list;
    }

    /**
     * 根据状态获取内部联系人
     * @param status 状态 (1=已激活，2=已禁用，4=未激活，5=退出企业)
     * @return 内部联系人列表
     */
    public List<WeComInternalContact> getWeComInternalContactsByStatus(Integer status) {
        List<WeComInternalContact> list = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "SELECT * FROM wecom_internal_contacts WHERE wecom_status = ? ORDER BY wecom_created_at DESC";
            ps = conn.prepareStatement(sql);
            ps.setInt(1, status);
            
            SQLLogUtils.printSQL("=== getWeComInternalContactsByStatus: ", ps);
            
            rs = ps.executeQuery();
            while (rs.next()) {
                WeComInternalContact contact = convertToWeComInternalContact(rs);
                list.add(contact);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return list;
    }

    /**
     * 根据部门获取内部联系人
     * @param departmentId 部门ID
     * @return 内部联系人列表
     */
    public List<WeComInternalContact> getWeComInternalContactsByDepartment(Integer departmentId) {
        List<WeComInternalContact> list = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "SELECT * FROM wecom_internal_contacts WHERE wecom_main_department = ? ORDER BY wecom_created_at DESC";
            ps = conn.prepareStatement(sql);
            ps.setInt(1, departmentId);
            
            SQLLogUtils.printSQL("=== getWeComInternalContactsByDepartment: ", ps);
            
            rs = ps.executeQuery();
            while (rs.next()) {
                WeComInternalContact contact = convertToWeComInternalContact(rs);
                list.add(contact);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return list;
    }

    /**
     * 根据姓名查询内部联系人
     * @param name 姓名
     * @return 内部联系人列表
     */
    public List<WeComInternalContact> getWeComInternalContactsByName(String name) {
        List<WeComInternalContact> list = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "SELECT * FROM wecom_internal_contacts WHERE wecom_name LIKE ? ORDER BY wecom_created_at DESC";
            ps = conn.prepareStatement(sql);
            ps.setString(1, "%" + name + "%");
            
            SQLLogUtils.printSQL("=== getWeComInternalContactsByName: ", ps);
            
            rs = ps.executeQuery();
            while (rs.next()) {
                WeComInternalContact contact = convertToWeComInternalContact(rs);
                list.add(contact);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return list;
    }

    /**
     * 批量添加内部联系人
     * @param contacts 内部联系人列表
     * @return 添加成功的数量
     */
    public int batchAddWeComInternalContacts(List<WeComInternalContact> contacts) {
        if (contacts == null || contacts.isEmpty()) {
            return 0;
        }
        
        Connection conn = null;
        PreparedStatement ps = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            conn.setAutoCommit(false);
            
            String sql = "INSERT INTO wecom_internal_contacts (wecom_user_id, wecom_name, wecom_mobile, " +
                    "wecom_email, wecom_position, wecom_gender, wecom_avatar, wecom_thumb_avatar, " +
                    "wecom_telephone, wecom_alias, wecom_address, wecom_open_userid, wecom_main_department, " +
                    "wecom_status, wecom_qr_code, wecom_department_json, wecom_order_json, " +
                    "wecom_is_leader_in_dept_json, wecom_direct_leader_json, wecom_ext_attr_json, " +
                    "wecom_external_position, wecom_external_profile_json, wecom_saas_id, " +
                    "wecom_sync_time, wecom_created_at, wecom_updated_at) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
            
            ps = conn.prepareStatement(sql);
            
            int successCount = 0;
            int batchCount = 0;  // 添加批次计数器
            
            for (WeComInternalContact contact : contacts) {
                ps.setString(1, contact.getUserId());
                ps.setString(2, contact.getName());
                ps.setString(3, contact.getMobile());
                ps.setString(4, contact.getEmail());
                ps.setString(5, contact.getPosition());
                ps.setObject(6, contact.getGender());
                ps.setString(7, contact.getAvatar());
                ps.setString(8, contact.getThumbAvatar());
                ps.setString(9, contact.getTelephone());
                ps.setString(10, contact.getAlias());
                ps.setString(11, contact.getAddress());
                ps.setString(12, contact.getOpenUserId());
                ps.setObject(13, contact.getMainDepartment());
                ps.setObject(14, contact.getStatus());
                ps.setString(15, contact.getQrCode());
                ps.setString(16, contact.getDepartmentJson());
                ps.setString(17, contact.getOrderJson());
                ps.setString(18, contact.getIsLeaderInDeptJson());
                ps.setString(19, contact.getDirectLeaderJson());
                ps.setString(20, contact.getExtAttrJson());
                ps.setString(21, contact.getExternalPosition());
                ps.setString(22, contact.getExternalProfileJson());
                ps.setString(23, contact.getWecom_saas_id());
                ps.setTimestamp(24, contact.getSyncTime() != null ? new Timestamp(contact.getSyncTime().getTime()) : null);
                
                ps.addBatch();
                batchCount++;  // 增加批次计数器
                
                if (batchCount % WeComConfig.getBatchSize() == 0) {
                    int[] results = ps.executeBatch();
                    for (int result : results) {
                        if (result > 0) successCount++;
                    }
                    ps.clearBatch();
                    batchCount = 0;  // 重置批次计数器
                }
            }
            
            // 执行剩余的批处理
            if (batchCount > 0) {
                int[] results = ps.executeBatch();
                for (int result : results) {
                    if (result > 0) successCount++;
                }
            }
            
            conn.commit();
            return successCount;
            
        } catch (Exception e) {
            e.printStackTrace();
            try {
                if (conn != null) conn.rollback();
            } catch (Exception rollbackEx) {
                rollbackEx.printStackTrace();
            }
            return 0;
        } finally {
            try {
                if (conn != null) conn.setAutoCommit(true);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            closeAllConnection(conn, ps, null);
        }
    }

    /**
     * 将ResultSet转换为WeComInternalContact对象
     * @param rs ResultSet对象
     * @return WeComInternalContact对象
     */
    private WeComInternalContact convertToWeComInternalContact(ResultSet rs) throws SQLException {
        WeComInternalContact contact = new WeComInternalContact();
        contact.setId(rs.getLong("id"));
        contact.setUserId(rs.getString("wecom_user_id"));
        contact.setName(rs.getString("wecom_name"));
        contact.setMobile(rs.getString("wecom_mobile"));
        contact.setEmail(rs.getString("wecom_email"));
        contact.setPosition(rs.getString("wecom_position"));
        contact.setGender(rs.getObject("wecom_gender") != null ? rs.getInt("wecom_gender") : null);
        contact.setAvatar(rs.getString("wecom_avatar"));
        contact.setThumbAvatar(rs.getString("wecom_thumb_avatar"));
        contact.setTelephone(rs.getString("wecom_telephone"));
        contact.setAlias(rs.getString("wecom_alias"));
        contact.setAddress(rs.getString("wecom_address"));
        contact.setOpenUserId(rs.getString("wecom_open_userid"));
        contact.setMainDepartment(rs.getObject("wecom_main_department") != null ? rs.getInt("wecom_main_department") : null);
        contact.setStatus(rs.getObject("wecom_status") != null ? rs.getInt("wecom_status") : null);
        contact.setQrCode(rs.getString("wecom_qr_code"));
        contact.setDepartmentJson(rs.getString("wecom_department_json"));
        contact.setOrderJson(rs.getString("wecom_order_json"));
        contact.setIsLeaderInDeptJson(rs.getString("wecom_is_leader_in_dept_json"));
        contact.setDirectLeaderJson(rs.getString("wecom_direct_leader_json"));
        contact.setExtAttrJson(rs.getString("wecom_ext_attr_json"));
        contact.setExternalPosition(rs.getString("wecom_external_position"));
        contact.setExternalProfileJson(rs.getString("wecom_external_profile_json"));
        contact.setCreatedAt(rs.getTimestamp("wecom_created_at"));
        contact.setUpdatedAt(rs.getTimestamp("wecom_updated_at"));
        contact.setWecom_saas_id(rs.getString("wecom_saas_id"));
        contact.setSyncTime(rs.getTimestamp("wecom_sync_time"));
        return contact;
    }

    // ==================== 群成员关系管理 ====================

    /**
     * 添加群成员关系
     * @param member 群成员对象
     * @return 是否添加成功
     */
    public boolean addWeComGroupMemberRelation(GroupChatMember member) {
        Connection conn = null;
        PreparedStatement ps = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            String sql = "INSERT INTO wecom_group_member_relations (wecom_chat_id, wecom_member_userid, " +
                    "wecom_member_type, wecom_join_time, wecom_join_scene, wecom_group_nickname, " +
                    "wecom_invitor_userid, wecom_group_version, " +
                    "wecom_saas_id, wecom_created_at, wecom_updated_at) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
            
            ps = conn.prepareStatement(sql);
            ps.setString(1, member.getChatId());
            ps.setString(2, member.getUserId());
            ps.setInt(3, member.getType());
            ps.setTimestamp(4, member.getJoinTime() != null ? new Timestamp(member.getJoinTime().getTime()) : null);
            ps.setObject(5, member.getJoinScene());
            ps.setString(6, member.getGroupNickname());
            ps.setString(7, member.getInvitorUserId());
            ps.setString(8, member.getGroupVersion());
            ps.setString(9, member.getWecom_saas_id());
            
            SQLLogUtils.printSQL("=== addWeComGroupMemberRelation: ", ps);
            
            return ps.executeUpdate() > 0;
            
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            closeAllConnection(conn, ps, null);
        }
    }

    /**
     * 删除群成员关系
     * @param chatId 群ID
     * @param userId 成员ID
     * @return 是否删除成功
     */
    public boolean deleteWeComGroupMemberRelation(String chatId, String userId) {
        Connection conn = null;
        PreparedStatement ps = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "DELETE FROM wecom_group_member_relations WHERE wecom_chat_id = ? AND wecom_member_userid = ?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, chatId);
            ps.setString(2, userId);
            
            SQLLogUtils.printSQL("=== deleteWeComGroupMemberRelation: ", ps);
            
            return ps.executeUpdate() > 0;
            
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            closeAllConnection(conn, ps, null);
        }
    }

    /**
     * 更新群成员关系
     * @param member 群成员对象
     * @return 是否更新成功
     */
    public boolean updateWeComGroupMemberRelation(GroupChatMember member) {
        Connection conn = null;
        PreparedStatement ps = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            String sql = "UPDATE wecom_group_member_relations SET " +
                    "wecom_member_type = ?, wecom_join_time = ?, wecom_join_scene = ?, " +
                    "wecom_group_nickname = ?, wecom_invitor_userid = ?, wecom_group_version = ?, " +
                    "wecom_updated_at = NOW() " +
                    "WHERE wecom_chat_id = ? AND wecom_member_userid = ? AND wecom_saas_id = ?";
            
            ps = conn.prepareStatement(sql);
            ps.setInt(1, member.getType());
            ps.setTimestamp(2, member.getJoinTime() != null ? new Timestamp(member.getJoinTime().getTime()) : null);
            ps.setObject(3, member.getJoinScene());
            ps.setString(4, member.getGroupNickname());
            ps.setString(5, member.getInvitorUserId());
            ps.setString(6, member.getGroupVersion());
            ps.setString(7, member.getChatId());
            ps.setString(8, member.getUserId());
            ps.setString(9, member.getWecom_saas_id());
            
            SQLLogUtils.printSQL("=== updateWeComGroupMemberRelation: ", ps);
            
            return ps.executeUpdate() > 0;
            
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            closeAllConnection(conn, ps, null);
        }
    }

    /**
     * 根据群ID获取群成员关系列表
     * @param chatId 群ID
     * @param saasId SaaS ID
     * @return 群成员列表
     */
    public List<GroupChatMember> getWeComGroupMemberRelationsByGroup(String chatId, String saasId) {
        List<GroupChatMember> list = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "SELECT * FROM wecom_group_member_relations WHERE wecom_chat_id = ? AND wecom_saas_id = ? ORDER BY wecom_join_time";
            ps = conn.prepareStatement(sql);
            ps.setString(1, chatId);
            ps.setString(2, saasId);
            
            SQLLogUtils.printSQL("=== getWeComGroupMemberRelationsByGroup: ", ps);
            
            rs = ps.executeQuery();
            while (rs.next()) {
                GroupChatMember member = convertToGroupChatMember(rs);
                list.add(member);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return list;
    }

    /**
     * 根据成员ID获取群成员关系列表
     * @param userId 成员ID
     * @param saasId SaaS ID
     * @return 群成员列表
     */
    public List<GroupChatMember> getWeComGroupMemberRelationsByUser(String userId, String saasId) {
        List<GroupChatMember> list = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "SELECT * FROM wecom_group_member_relations WHERE wecom_member_userid = ? AND wecom_saas_id = ? ORDER BY wecom_join_time";
            ps = conn.prepareStatement(sql);
            ps.setString(1, userId);
            ps.setString(2, saasId);
            
            SQLLogUtils.printSQL("=== getWeComGroupMemberRelationsByUser: ", ps);
            
            rs = ps.executeQuery();
            while (rs.next()) {
                GroupChatMember member = convertToGroupChatMember(rs);
                list.add(member);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return list;
    }

    /**
     * 批量添加群成员关系
     * @param members 群成员列表
     * @return 添加成功的数量
     */
    public int batchAddWeComGroupMemberRelations(List<GroupChatMember> members) {
        if (members == null || members.isEmpty()) {
            return 0;
        }
        
        Connection conn = null;
        PreparedStatement ps = null;
        int successCount = 0;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            conn.setAutoCommit(false);
            
            String sql = "INSERT INTO wecom_group_member_relations (wecom_chat_id, wecom_member_userid, " +
                    "wecom_member_type, wecom_join_time, wecom_join_scene, wecom_group_nickname, " +
                    "wecom_invitor_userid, wecom_group_version, " +
                    "wecom_saas_id, wecom_created_at, wecom_updated_at) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
            
            ps = conn.prepareStatement(sql);
            
            int batchCount = 0;  // 添加批次计数器
            int maxBatchSize = Math.min(50, WeComConfig.getBatchSize()); // 限制最大批次大小
            
            for (GroupChatMember member : members) {
                ps.setString(1, member.getChatId());
                ps.setString(2, member.getUserId());
                ps.setInt(3, member.getType());
                ps.setTimestamp(4, member.getJoinTime() != null ? new Timestamp(member.getJoinTime().getTime()) : null);
                ps.setObject(5, member.getJoinScene());
                ps.setString(6, member.getGroupNickname());
                ps.setString(7, member.getInvitorUserId());
                ps.setString(8, member.getGroupVersion());
                ps.setString(9, member.getWecom_saas_id());
                
                ps.addBatch();
                batchCount++;  // 增加批次计数器
                
                if (batchCount % maxBatchSize == 0) {
                    try {
                        int[] results = ps.executeBatch();
                        for (int result : results) {
                            if (result > 0) successCount++;
                        }
                        conn.commit(); // 每个小批次提交一次事务
                        ps.clearBatch();
                    } catch (Exception e) {
                        // 处理批次错误，记录错误但继续处理
                        System.err.println("批量添加群成员关系时出现错误: " + e.getMessage());
                        try {
                            conn.rollback();
                        } catch (Exception rollbackEx) {
                            rollbackEx.printStackTrace();
                        }
                    }
                    batchCount = 0;  // 重置批次计数器
                }
            }
            
            // 执行剩余的批处理
            if (batchCount > 0) {
                try {
                    int[] results = ps.executeBatch();
                    for (int result : results) {
                        if (result > 0) successCount++;
                    }
                    conn.commit();
                } catch (Exception e) {
                    System.err.println("批量添加剩余群成员关系时出现错误: " + e.getMessage());
                    try {
                        conn.rollback();
                    } catch (Exception rollbackEx) {
                        rollbackEx.printStackTrace();
                    }
                }
            }
            
            return successCount;
            
        } catch (Exception e) {
            e.printStackTrace();
            try {
                if (conn != null) conn.rollback();
            } catch (Exception rollbackEx) {
                rollbackEx.printStackTrace();
            }
            return successCount; // 返回已成功处理的数量，而不是0
        } finally {
            try {
                if (conn != null) conn.setAutoCommit(true);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            closeAllConnection(conn, ps, null);
        }
    }

    /**
     * 将ResultSet转换为GroupChatMember对象
     * @param rs ResultSet对象
     * @return GroupChatMember对象
     */
    private GroupChatMember convertToGroupChatMember(ResultSet rs) throws SQLException {
        GroupChatMember member = new GroupChatMember();
        member.setChatId(rs.getString("wecom_chat_id"));
        member.setUserId(rs.getString("wecom_member_userid"));
        member.setType(rs.getInt("wecom_member_type"));
        member.setJoinTime(rs.getTimestamp("wecom_join_time"));
        member.setJoinScene(rs.getObject("wecom_join_scene") != null ? rs.getInt("wecom_join_scene") : null);
        member.setGroupNickname(rs.getString("wecom_group_nickname"));
        member.setInvitorUserId(rs.getString("wecom_invitor_userid"));
        member.setGroupVersion(rs.getString("wecom_group_version"));
        member.setWecom_saas_id(rs.getString("wecom_saas_id"));
        return member;
    }

    // ==================== 跟进人关系管理 ====================

    /**
     * 添加跟进人关系
     * @param follower 跟进人对象
     * @return 是否添加成功
     */
    public boolean addWeComExternalContactFollower(FollowerInfo follower) {
        Connection conn = null;
        PreparedStatement ps = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            String sql = "INSERT INTO wecom_external_contact_followers (wecom_external_userid, wecom_follower_userid, " +
                    "wecom_remark, wecom_description, wecom_createtime, wecom_tags, wecom_remark_mobiles, " +
                    "wecom_remark_corp_name, wecom_add_way, wecom_oper_userid, wecom_followers_version, " +
                    "wecom_saas_id, wecom_created_at, wecom_updated_at) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
            
            ps = conn.prepareStatement(sql);
            ps.setString(1, follower.getExternalUserId());
            ps.setString(2, follower.getFollowerUserId());
            ps.setString(3, follower.getRemark());
            ps.setString(4, follower.getDescription());
            ps.setTimestamp(5, follower.getCreateTime() != null ? new Timestamp(follower.getCreateTime().getTime()) : null);
            ps.setString(6, follower.getTags());
            ps.setString(7, follower.getRemarkMobiles());
            ps.setString(8, follower.getRemarkCorpName());
            ps.setInt(9, follower.getAddWay());
            ps.setString(10, follower.getOperUserId());
            ps.setString(11, follower.getFollowersVersion());
            ps.setString(12, follower.getWecom_saas_id());
            
            SQLLogUtils.printSQL("=== addWeComExternalContactFollower: ", ps);
            
            return ps.executeUpdate() > 0;
            
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            closeAllConnection(conn, ps, null);
        }
    }

    /**
     * 删除跟进人关系
     * @param externalUserId 外部联系人ID
     * @param followerUserId 跟进人ID
     * @return 是否删除成功
     */
    public boolean deleteWeComExternalContactFollower(String externalUserId, String followerUserId) {
        Connection conn = null;
        PreparedStatement ps = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "DELETE FROM wecom_external_contact_followers WHERE wecom_external_userid = ? AND wecom_follower_userid = ?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, externalUserId);
            ps.setString(2, followerUserId);
            
            SQLLogUtils.printSQL("=== deleteWeComExternalContactFollower: ", ps);
            
            return ps.executeUpdate() > 0;
            
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            closeAllConnection(conn, ps, null);
        }
    }

    /**
     * 批量删除外部联系人的所有跟进人关系
     * @param externalUserId 外部联系人ID
     * @return 删除的数量
     */
    public int batchDeleteWeComExternalContactFollowers(String externalUserId) {
        Connection conn = null;
        PreparedStatement ps = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "DELETE FROM wecom_external_contact_followers WHERE wecom_external_userid = ?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, externalUserId);
            
            SQLLogUtils.printSQL("=== batchDeleteWeComExternalContactFollowers: ", ps);
            
            return ps.executeUpdate();
            
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        } finally {
            closeAllConnection(conn, ps, null);
        }
    }

    /**
     * 更新跟进人关系
     * @param follower 跟进人对象
     * @return 是否更新成功
     */
    public boolean updateWeComExternalContactFollower(FollowerInfo follower) {
        Connection conn = null;
        PreparedStatement ps = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            String sql = "UPDATE wecom_external_contact_followers SET " +
                    "wecom_remark = ?, wecom_description = ?, wecom_createtime = ?, wecom_tags = ?, " +
                    "wecom_remark_mobiles = ?, wecom_remark_corp_name = ?, wecom_add_way = ?, " +
                    "wecom_oper_userid = ?, wecom_followers_version = ?, " +
                    "wecom_updated_at = NOW() " +
                    "WHERE wecom_external_userid = ? AND wecom_follower_userid = ? AND wecom_saas_id = ?";
            
            ps = conn.prepareStatement(sql);
            ps.setString(1, follower.getRemark());
            ps.setString(2, follower.getDescription());
            ps.setTimestamp(3, follower.getCreateTime() != null ? new Timestamp(follower.getCreateTime().getTime()) : null);
            ps.setString(4, follower.getTags());
            ps.setString(5, follower.getRemarkMobiles());
            ps.setString(6, follower.getRemarkCorpName());
            ps.setInt(7, follower.getAddWay());
            ps.setString(8, follower.getOperUserId());
            ps.setString(9, follower.getFollowersVersion());
            ps.setString(10, follower.getExternalUserId());
            ps.setString(11, follower.getFollowerUserId());
            ps.setString(12, follower.getWecom_saas_id());
            
            SQLLogUtils.printSQL("=== updateWeComExternalContactFollower: ", ps);
            
            return ps.executeUpdate() > 0;
            
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            closeAllConnection(conn, ps, null);
        }
    }

    /**
     * 根据外部联系人ID获取跟进人关系列表
     * @param externalUserId 外部联系人ID
     * @param saasId SaaS ID
     * @return 跟进人列表
     */
    public List<FollowerInfo> getWeComExternalContactFollowersByExternalUser(String externalUserId, String saasId) {
        List<FollowerInfo> list = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "SELECT * FROM wecom_external_contact_followers WHERE wecom_external_userid = ? AND wecom_saas_id = ? ORDER BY wecom_createtime";
            ps = conn.prepareStatement(sql);
            ps.setString(1, externalUserId);
            ps.setString(2, saasId);
            
            // SQLLogUtils.printSQL("=== getWeComExternalContactFollowersByExternalUser: ", ps);
            
            rs = ps.executeQuery();
            while (rs.next()) {
                FollowerInfo follower = convertToFollowerInfo(rs);
                list.add(follower);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return list;
    }

    /**
     * 根据跟进人ID获取跟进人关系列表
     * @param followerUserId 跟进人ID
     * @param saasId SaaS ID
     * @return 跟进人列表
     */
    public List<FollowerInfo> getWeComExternalContactFollowersByFollower(String followerUserId, String saasId) {
        List<FollowerInfo> list = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            String sql = "SELECT * FROM wecom_external_contact_followers WHERE wecom_follower_userid = ? AND wecom_saas_id = ? ORDER BY wecom_createtime";
            ps = conn.prepareStatement(sql);
            ps.setString(1, followerUserId);
            ps.setString(2, saasId);
            
            SQLLogUtils.printSQL("=== getWeComExternalContactFollowersByFollower: ", ps);
            
            rs = ps.executeQuery();
            while (rs.next()) {
                FollowerInfo follower = convertToFollowerInfo(rs);
                list.add(follower);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return list;
    }

    /**
     * 批量添加跟进人关系
     * @param followers 跟进人列表
     * @return 添加成功的数量
     */
    public int batchAddWeComExternalContactFollowers(List<FollowerInfo> followers) {
        if (followers == null || followers.isEmpty()) {
            return 0;
        }
        
        Connection conn = null;
        PreparedStatement ps = null;
        int successCount = 0;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            conn.setAutoCommit(false);
            
            String sql = "INSERT INTO wecom_external_contact_followers (wecom_external_userid, wecom_follower_userid, " +
                    "wecom_remark, wecom_description, wecom_createtime, wecom_tags, wecom_remark_mobiles, " +
                    "wecom_remark_corp_name, wecom_add_way, wecom_oper_userid, wecom_followers_version, " +
                    "wecom_saas_id, wecom_created_at, wecom_updated_at) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
            
            ps = conn.prepareStatement(sql);
            
            int batchCount = 0;  // 添加批次计数器
            int maxBatchSize = Math.min(50, WeComConfig.getBatchSize()); // 限制最大批次大小
            
            for (FollowerInfo follower : followers) {
                ps.setString(1, follower.getExternalUserId());
                ps.setString(2, follower.getFollowerUserId());
                ps.setString(3, follower.getRemark());
                ps.setString(4, follower.getDescription());
                ps.setTimestamp(5, follower.getCreateTime() != null ? new Timestamp(follower.getCreateTime().getTime()) : null);
                ps.setString(6, follower.getTags());
                ps.setString(7, follower.getRemarkMobiles());
                ps.setString(8, follower.getRemarkCorpName());
                ps.setInt(9, follower.getAddWay());
                ps.setString(10, follower.getOperUserId());
                ps.setString(11, follower.getFollowersVersion());
                ps.setString(12, follower.getWecom_saas_id());
                
                ps.addBatch();
                batchCount++;  // 增加批次计数器
                
                if (batchCount % maxBatchSize == 0) {
                    try {
                        int[] results = ps.executeBatch();
                        for (int result : results) {
                            if (result > 0) successCount++;
                        }
                        conn.commit(); // 每个小批次提交一次事务
                        ps.clearBatch();
                    } catch (Exception e) {
                        // 处理批次错误，记录错误但继续处理
                        System.err.println("批量添加跟进人关系时出现错误: " + e.getMessage());
                        try {
                            conn.rollback();
                        } catch (Exception rollbackEx) {
                            rollbackEx.printStackTrace();
                        }
                    }
                    batchCount = 0;  // 重置批次计数器
                }
            }
            
            // 执行剩余的批处理
            if (batchCount > 0) {
                try {
                    int[] results = ps.executeBatch();
                    for (int result : results) {
                        if (result > 0) successCount++;
                    }
                    conn.commit();
                } catch (Exception e) {
                    System.err.println("批量添加剩余跟进人关系时出现错误: " + e.getMessage());
                    try {
                        conn.rollback();
                    } catch (Exception rollbackEx) {
                        rollbackEx.printStackTrace();
                    }
                }
            }
            
            return successCount;
            
        } catch (Exception e) {
            e.printStackTrace();
            try {
                if (conn != null) conn.rollback();
            } catch (Exception rollbackEx) {
                rollbackEx.printStackTrace();
            }
            return successCount; // 返回已成功处理的数量，而不是0
        } finally {
            try {
                if (conn != null) conn.setAutoCommit(true);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            closeAllConnection(conn, ps, null);
        }
    }

    /**
     * 将ResultSet转换为FollowerInfo对象
     * @param rs ResultSet对象
     * @return FollowerInfo对象
     */
    private FollowerInfo convertToFollowerInfo(ResultSet rs) throws SQLException {
        FollowerInfo follower = new FollowerInfo();
        follower.setExternalUserId(rs.getString("wecom_external_userid"));
        follower.setFollowerUserId(rs.getString("wecom_follower_userid"));
        follower.setRemark(rs.getString("wecom_remark"));
        follower.setDescription(rs.getString("wecom_description"));
        follower.setCreateTime(rs.getTimestamp("wecom_createtime"));
        follower.setTags(rs.getString("wecom_tags"));
        follower.setRemarkMobiles(rs.getString("wecom_remark_mobiles"));
        follower.setRemarkCorpName(rs.getString("wecom_remark_corp_name"));
        follower.setAddWay(rs.getInt("wecom_add_way"));
        follower.setOperUserId(rs.getString("wecom_oper_userid"));
        follower.setFollowersVersion(rs.getString("wecom_followers_version"));
        follower.setWecom_saas_id(rs.getString("wecom_saas_id"));
        return follower;
    }

    // ==================== 工具方法 ====================

    /**
     * 关闭数据库连接
     * @param conn 数据库连接
     * @param ps PreparedStatement对象
     * @param rs ResultSet对象
     */
    private static void closeAllConnection(Connection conn, PreparedStatement ps, ResultSet rs) {
        try {
            if (rs != null) {
                rs.close();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        try {
            if (ps != null) {
                ps.close();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        try {
            if (conn != null) {
                conn.close();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    /**
     * 批量获取多个外部联系人的跟进人关系
     * @param externalUserIds 外部联系人ID列表
     * @param saasId SaaS ID
     * @return 所有匹配的跟进人关系列表
     */
    public List<FollowerInfo> batchGetWeComExternalContactFollowers(List<String> externalUserIds, String saasId) {
        if (externalUserIds == null || externalUserIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<FollowerInfo> allFollowers = new ArrayList<>();
        
        // 如果数据量小于等于阈值，使用单次查询
        if (!needsChunking(externalUserIds.size())) {
            return executeSingleBatchQuery(externalUserIds, saasId);
        }
        
        // 数据量大时，分块处理
        logChunkingInfo("批量查询跟进人关系", externalUserIds.size(), DEFAULT_CHUNK_SIZE);
        
        // 分块处理
        for (int i = 0; i < externalUserIds.size(); i += DEFAULT_CHUNK_SIZE) {
            int endIndex = Math.min(i + DEFAULT_CHUNK_SIZE, externalUserIds.size());
            List<String> chunk = externalUserIds.subList(i, endIndex);
            
            logChunkProgress(i / DEFAULT_CHUNK_SIZE, i, endIndex, chunk.size());
            
            List<FollowerInfo> chunkResult = executeSingleBatchQuery(chunk, saasId);
            allFollowers.addAll(chunkResult);
            
            // 分块间隔，避免数据库压力过大
            if (endIndex < externalUserIds.size()) {
                try {
                    Thread.sleep(getEffectiveChunkInterval());
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        if (WeComConfig.isEnableDetailedPhaseLogging()) {
            System.out.println("批量查询跟进人关系完成：总共获取" + allFollowers.size() + "条记录");
        }
        
        return allFollowers;
    }
    
    /**
     * 执行单次批量查询跟进人关系
     */
    private List<FollowerInfo> executeSingleBatchQuery(List<String> externalUserIds, String saasId) {
        List<FollowerInfo> followers = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            // 构建IN子句参数占位符
            StringBuilder placeholders = new StringBuilder();
            for (int i = 0; i < externalUserIds.size(); i++) {
                if (i > 0) {
                    placeholders.append(",");
                }
                placeholders.append("?");
            }
            
            String sql = "SELECT * FROM wecom_external_contact_followers WHERE wecom_external_userid IN (" + 
                    placeholders.toString() + ") AND wecom_saas_id = ? ORDER BY wecom_createtime";
            
            ps = conn.prepareStatement(sql);
            
            // 设置IN子句的参数值
            int paramIndex = 1;
            for (String externalUserId : externalUserIds) {
                ps.setString(paramIndex++, externalUserId);
            }
            
            // 设置saasId参数
            ps.setString(paramIndex, saasId);
            
            SQLLogUtils.printSQL("=== batchGetWeComExternalContactFollowers: ", ps);
            
            rs = ps.executeQuery();
            while (rs.next()) {
                FollowerInfo follower = convertToFollowerInfo(rs);
                followers.add(follower);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return followers;
    }

    /**
     * 批量更新外部联系人跟进人关系
     * @param followers 跟进人关系列表
     * @return 更新成功的数量
     */
    public int batchUpdateWeComExternalContactFollowers(List<FollowerInfo> followers) {
        if (followers == null || followers.isEmpty()) {
            return 0;
        }
        
        Connection conn = null;
        PreparedStatement ps = null;
        int successCount = 0;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            conn.setAutoCommit(false);
            
            String sql = "UPDATE wecom_external_contact_followers SET " +
                    "wecom_remark = ?, wecom_description = ?, wecom_tags = ?, " +
                    "wecom_remark_mobiles = ?, wecom_remark_corp_name = ?, " +
                    "wecom_followers_version = ?, wecom_updated_at = NOW() " +
                    "WHERE wecom_external_userid = ? AND wecom_follower_userid = ? AND wecom_saas_id = ?";
            
            ps = conn.prepareStatement(sql);
            
            int batchCount = 0;
            int maxBatchSize = Math.min(50, WeComConfig.getBatchSize());
            
            for (FollowerInfo follower : followers) {
                ps.setString(1, follower.getRemark());
                ps.setString(2, follower.getDescription());
                ps.setString(3, follower.getTags());
                ps.setString(4, follower.getRemarkMobiles());
                ps.setString(5, follower.getRemarkCorpName());
                ps.setString(6, follower.getFollowersVersion());
                ps.setString(7, follower.getExternalUserId());
                ps.setString(8, follower.getFollowerUserId());
                ps.setString(9, follower.getWecom_saas_id());
                
                ps.addBatch();
                batchCount++;
                
                if (batchCount % maxBatchSize == 0) {
                    try {
                        int[] results = ps.executeBatch();
                        for (int result : results) {
                            if (result > 0) successCount++;
                        }
                        conn.commit();
                        ps.clearBatch();
                    } catch (Exception e) {
                        System.err.println("批量更新跟进人关系时出现错误: " + e.getMessage());
                        try {
                            conn.rollback();
                        } catch (Exception rollbackEx) {
                            rollbackEx.printStackTrace();
                        }
                    }
                    batchCount = 0;
                }
            }
            
            // 执行剩余的批处理
            if (batchCount > 0) {
                try {
                    int[] results = ps.executeBatch();
                    for (int result : results) {
                        if (result > 0) successCount++;
                    }
                    conn.commit();
                } catch (Exception e) {
                    System.err.println("批量更新剩余跟进人关系时出现错误: " + e.getMessage());
                    try {
                        conn.rollback();
                    } catch (Exception rollbackEx) {
                        rollbackEx.printStackTrace();
                    }
                }
            }
            
            return successCount;
            
        } catch (Exception e) {
            e.printStackTrace();
            try {
                if (conn != null) conn.rollback();
            } catch (Exception rollbackEx) {
                rollbackEx.printStackTrace();
            }
            return successCount;
        } finally {
            try {
                if (conn != null) conn.setAutoCommit(true);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            closeAllConnection(conn, ps, null);
        }
    }

    /**
     * 批量删除多个外部联系人的跟进人关系
     * @param followersToDeleteMap 待删除的跟进人关系映射表，键为外部联系人ID，值为该联系人下需要删除的跟进人ID集合
     * @return 删除成功的记录数
     */
    public int batchDeleteWeComExternalContactFollowers(Map<String, Set<String>> followersToDeleteMap) {
        if (followersToDeleteMap == null || followersToDeleteMap.isEmpty()) {
            return 0;
        }
        
        Connection conn = null;
        PreparedStatement ps = null;
        int successCount = 0;
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            conn.setAutoCommit(false);
            
            String sql = "DELETE FROM wecom_external_contact_followers WHERE wecom_external_userid = ? AND wecom_follower_userid = ?";
            ps = conn.prepareStatement(sql);
            
            int batchCount = 0;
            int maxBatchSize = Math.min(50, WeComConfig.getBatchSize());
            
            for (Map.Entry<String, Set<String>> entry : followersToDeleteMap.entrySet()) {
                String externalUserId = entry.getKey();
                Set<String> followerUserIds = entry.getValue();
                
                for (String followerUserId : followerUserIds) {
                    ps.setString(1, externalUserId);
                    ps.setString(2, followerUserId);
                    
                    ps.addBatch();
                    batchCount++;
                    
                    if (batchCount % maxBatchSize == 0) {
                        try {
                            int[] results = ps.executeBatch();
                            for (int result : results) {
                                if (result > 0) successCount++;
                            }
                            conn.commit();
                            ps.clearBatch();
                        } catch (Exception e) {
                            System.err.println("批量删除跟进人关系时出现错误: " + e.getMessage());
                            try {
                                conn.rollback();
                            } catch (Exception rollbackEx) {
                                rollbackEx.printStackTrace();
                            }
                        }
                        batchCount = 0;
                    }
                }
            }
            
            // 执行剩余的批处理
            if (batchCount > 0) {
                try {
                    int[] results = ps.executeBatch();
                    for (int result : results) {
                        if (result > 0) successCount++;
                    }
                    conn.commit();
                } catch (Exception e) {
                    System.err.println("批量删除剩余跟进人关系时出现错误: " + e.getMessage());
                    try {
                        conn.rollback();
                    } catch (Exception rollbackEx) {
                        rollbackEx.printStackTrace();
                    }
                }
            }
            
            SQLLogUtils.printSQL("=== batchDeleteWeComExternalContactFollowers: 删除了 " + successCount + " 条记录", ps);
            
            return successCount;
            
        } catch (Exception e) {
            e.printStackTrace();
            try {
                if (conn != null) conn.rollback();
            } catch (Exception rollbackEx) {
                rollbackEx.printStackTrace();
            }
            return successCount;
        } finally {
            try {
                if (conn != null) conn.setAutoCommit(true);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            closeAllConnection(conn, ps, null);
        }
    }
} 