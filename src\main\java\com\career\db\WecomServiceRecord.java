package com.career.db;

import java.util.Date;

public class WecomServiceRecord {
	private String serviceId; // 服务记录ID
    private String customerId; // 客户ID
    private String staffId; // 员工ID
    private Date serviceDate; // 服务日期和时间
    private String serviceType; // 服务类型（如咨询、维修、安装等）
    private String serviceDescription; // 服务详细描述
    private Integer serviceDuration; // 服务持续时间（分钟）
    private String serviceStatus; // 服务状态（如：已完成、进行中、计划中）
    private Date creationDate; // 记录创建时间
    private Date updateDate; // 记录更新时间
	public String getServiceId() {
		return serviceId;
	}
	public void setServiceId(String serviceId) {
		this.serviceId = serviceId;
	}
	public String getCustomerId() {
		return customerId;
	}
	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}
	public String getStaffId() {
		return staffId;
	}
	public void setStaffId(String staffId) {
		this.staffId = staffId;
	}
	public Date getServiceDate() {
		return serviceDate;
	}
	public void setServiceDate(Date serviceDate) {
		this.serviceDate = serviceDate;
	}
	public String getServiceType() {
		return serviceType;
	}
	public void setServiceType(String serviceType) {
		this.serviceType = serviceType;
	}
	public String getServiceDescription() {
		return serviceDescription;
	}
	public void setServiceDescription(String serviceDescription) {
		this.serviceDescription = serviceDescription;
	}
	public Integer getServiceDuration() {
		return serviceDuration;
	}
	public void setServiceDuration(Integer serviceDuration) {
		this.serviceDuration = serviceDuration;
	}
	public String getServiceStatus() {
		return serviceStatus;
	}
	public void setServiceStatus(String serviceStatus) {
		this.serviceStatus = serviceStatus;
	}
	public Date getCreationDate() {
		return creationDate;
	}
	public void setCreationDate(Date creationDate) {
		this.creationDate = creationDate;
	}
	public Date getUpdateDate() {
		return updateDate;
	}
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
    
    
}
