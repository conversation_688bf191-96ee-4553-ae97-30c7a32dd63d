package com.career.db;

import java.util.Date;

public class WecomExternalContactFollowers {

	private Long wecom_id; // 自增主键ID
    private String wecom_external_userid; // 外部联系人的userid
    private String wecom_follower_userid; // 跟进人（企业成员）的userid
    private String wecom_remark; // 跟进人备注
    private String wecom_description; // 跟进人描述
    private Date wecom_createtime; // 跟进人添加客户的时间戳
    private String wecom_tags; // 跟进人给客户添加的标签列表，字符串形式存储（如JSON或逗号分隔）
    private String wecom_remark_mobiles; // 跟进人给客户添加的备注手机号列表，字符串形式存储（如JSON或逗号分隔）
    private String wecom_remark_corp_name; // 跟进人备注公司名称
    private Integer wecom_add_way; // 跟进客户方式
    private String wecom_oper_userid; // 进行操作（如添加客户）的成员userid
    private Date wecom_created_at; // 记录创建时间
    private Date wecom_updated_at; // 记录更新时间
	public Long getWecom_id() {
		return wecom_id;
	}
	public void setWecom_id(Long wecom_id) {
		this.wecom_id = wecom_id;
	}
	public String getWecom_external_userid() {
		return wecom_external_userid;
	}
	public void setWecom_external_userid(String wecom_external_userid) {
		this.wecom_external_userid = wecom_external_userid;
	}
	public String getWecom_follower_userid() {
		return wecom_follower_userid;
	}
	public void setWecom_follower_userid(String wecom_follower_userid) {
		this.wecom_follower_userid = wecom_follower_userid;
	}
	public String getWecom_remark() {
		return wecom_remark;
	}
	public void setWecom_remark(String wecom_remark) {
		this.wecom_remark = wecom_remark;
	}
	public String getWecom_description() {
		return wecom_description;
	}
	public void setWecom_description(String wecom_description) {
		this.wecom_description = wecom_description;
	}
	public Date getWecom_createtime() {
		return wecom_createtime;
	}
	public void setWecom_createtime(Date wecom_createtime) {
		this.wecom_createtime = wecom_createtime;
	}
	public String getWecom_tags() {
		return wecom_tags;
	}
	public void setWecom_tags(String wecom_tags) {
		this.wecom_tags = wecom_tags;
	}
	public String getWecom_remark_mobiles() {
		return wecom_remark_mobiles;
	}
	public void setWecom_remark_mobiles(String wecom_remark_mobiles) {
		this.wecom_remark_mobiles = wecom_remark_mobiles;
	}
	public String getWecom_remark_corp_name() {
		return wecom_remark_corp_name;
	}
	public void setWecom_remark_corp_name(String wecom_remark_corp_name) {
		this.wecom_remark_corp_name = wecom_remark_corp_name;
	}
	public Integer getWecom_add_way() {
		return wecom_add_way;
	}
	public void setWecom_add_way(Integer wecom_add_way) {
		this.wecom_add_way = wecom_add_way;
	}
	public String getWecom_oper_userid() {
		return wecom_oper_userid;
	}
	public void setWecom_oper_userid(String wecom_oper_userid) {
		this.wecom_oper_userid = wecom_oper_userid;
	}
	public Date getWecom_created_at() {
		return wecom_created_at;
	}
	public void setWecom_created_at(Date wecom_created_at) {
		this.wecom_created_at = wecom_created_at;
	}
	public Date getWecom_updated_at() {
		return wecom_updated_at;
	}
	public void setWecom_updated_at(Date wecom_updated_at) {
		this.wecom_updated_at = wecom_updated_at;
	}
    
}
