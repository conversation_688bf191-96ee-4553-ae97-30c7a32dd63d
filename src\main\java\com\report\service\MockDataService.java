package com.report.service;

import com.report.entity.*;
import java.util.*;

/**
 * 模拟数据生成服务
 * 提供测试用的模拟数据，无需连接真实数据库
 */
public class MockDataService {
   
    /**
     * 生成模拟的大学四年规划数据
     */
    public static UniversityFourYearPlan generateMockUniversityFourYearPlan() {
        UniversityFourYearPlan plan = new UniversityFourYearPlan();
        
        // 大一规划
        UniversityFourYearPlan.FreshmanPlan freshmanPlan = new UniversityFourYearPlan.FreshmanPlan();
        freshmanPlan.setFirstSemesterGoals(Arrays.asList(
            "适应大学生活，建立良好的学习习惯",
            "掌握高等数学、线性代数等基础课程",
            "学习C语言程序设计，培养编程思维"
        ));
        freshmanPlan.setSecondSemesterGoals(Arrays.asList(
            "深入学习数据结构基础",
            "通过英语四级考试",
            "参加社团活动，提升综合素质"
        ));
        freshmanPlan.setKeyTasks(Arrays.asList(
            "保持GPA在3.5以上",
            "完成第一个编程项目",
            "建立学习小组"
        ));
        freshmanPlan.setSkillsToDevelop(Arrays.asList(
            "基础编程能力",
            "数学逻辑思维",
            "自主学习能力"
        ));
        freshmanPlan.setWinterVacationPlan("复习上学期课程，预习下学期内容，学习编程基础");
        freshmanPlan.setSummerVacationPlan("参加暑期编程训练营，做第一个完整项目");
        plan.setFreshmanPlan(freshmanPlan);
        
        // 大二规划
        UniversityFourYearPlan.SophomorePlan sophomorePlan = new UniversityFourYearPlan.SophomorePlan();
        sophomorePlan.setFirstSemesterGoals(Arrays.asList(
            "深入学习数据结构与算法",
            "掌握面向对象程序设计",
            "开始参与竞赛活动"
        ));
        sophomorePlan.setSecondSemesterGoals(Arrays.asList(
            "学习数据库原理与应用",
            "通过英语六级考试",
            "完成第一个团队项目"
        ));
        sophomorePlan.setExplorationDirections(Arrays.asList(
            "前端开发方向",
            "后端开发方向", 
            "数据科学方向"
        ));
        sophomorePlan.setResearchOpportunities(Arrays.asList(
            "参与导师科研项目",
            "申请大学生创新创业项目"
        ));
        sophomorePlan.setCompetitionsToJoin(Arrays.asList(
            "ACM程序设计竞赛",
            "蓝桥杯软件大赛"
        ));
        sophomorePlan.setWinterVacationPlan("深入学习算法，准备竞赛");
        sophomorePlan.setSummerVacationPlan("第一次实习体验，了解行业需求");
        plan.setSophomorePlan(sophomorePlan);
        
        // 大三规划
        UniversityFourYearPlan.JuniorPlan juniorPlan = new UniversityFourYearPlan.JuniorPlan();
        juniorPlan.setFirstSemesterGoals(Arrays.asList(
            "完成核心专业课程学习",
            "确定毕业设计方向",
            "开始准备实习简历"
        ));
        juniorPlan.setSecondSemesterGoals(Arrays.asList(
            "获得心仪公司实习机会",
            "参与重要项目开发",
            "准备就业或深造材料"
        ));
        juniorPlan.setAcademicFocus(Arrays.asList(
            "软件工程项目管理",
            "系统架构设计",
            "新技术框架学习"
        ));
        juniorPlan.setLanguageSkills(Arrays.asList(
            "提升英语口语能力",
            "学习技术英语文档阅读"
        ));
        juniorPlan.setResearchPapers(Arrays.asList(
            "参与导师论文研究",
            "撰写毕业设计相关论文"
        ));
        juniorPlan.setApplicationMaterials(Arrays.asList(
            "完善个人简历",
            "准备作品集",
            "收集推荐信"
        ));
        juniorPlan.setWinterVacationPlan("大厂实习准备，技术栈深度学习");
        juniorPlan.setSummerVacationPlan("在大厂实习，积累项目经验");
        plan.setJuniorPlan(juniorPlan);
        
        // 大四规划
        UniversityFourYearPlan.SeniorPlan seniorPlan = new UniversityFourYearPlan.SeniorPlan();
        seniorPlan.setFirstSemesterGoals(Arrays.asList(
            "完成毕业设计开题",
            "投递心仪公司岗位",
            "参加校园招聘"
        ));
        seniorPlan.setSecondSemesterGoals(Arrays.asList(
            "完成毕业设计答辩",
            "确定工作offer",
            "顺利毕业"
        ));
        seniorPlan.setApplicationProcesses(Arrays.asList(
            "秋季校园招聘",
            "春季补招",
            "社会招聘"
        ));
        seniorPlan.setExamPreparations(Arrays.asList(
            "毕业设计答辩准备",
            "技术面试准备"
        ));
        seniorPlan.setJobSearchActivities(Arrays.asList(
            "参加招聘会",
            "网络投递简历",
            "内推机会把握"
        ));
        seniorPlan.setGraduationRequirements(Arrays.asList(
            "修满学分要求",
            "通过毕业设计",
            "完成实习学分"
        ));
        seniorPlan.setWinterVacationPlan("准备春招，完善毕业设计");
        plan.setSeniorPlan(seniorPlan);
        
        // 学业发展规划 - 直接使用实体类中的真实数据
        AcademicDevelopmentPlan academicPlan = new AcademicDevelopmentPlan();
        plan.setAcademicPlan(academicPlan);
        
        // 考证规划 - 直接使用实体类中的真实数据
        CertificationPlan certPlan = new CertificationPlan();
        plan.setCertificationPlan(certPlan);
        
        // 竞赛规划 - 直接使用实体类中的真实数据
        CompetitionPlan compPlan = new CompetitionPlan();
        plan.setCompetitionPlan(compPlan);
        
        // 职业准备规划 - 直接使用实体类中的真实数据
        CareerPreparationPlan careerPrepPlan = new CareerPreparationPlan();
        plan.setCareerPreparationPlan(careerPrepPlan);
        
        return plan;
    }
}