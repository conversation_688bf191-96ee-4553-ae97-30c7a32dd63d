package com.career.db;

import java.util.Date;

public class CardBeanDZ {
	private String id;
	private String passwd;
	private String prov;
	private String city;
	private String cType;
	private int cjBishi;
	private int cjJineng;

	private Date create;
	private Date active;
	private Date lastLogin;
	private int year;
	private int status;
	
	private String remark;
	private String desc;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getPasswd() {
		return passwd;
	}

	public void setPasswd(String passwd) {
		this.passwd = passwd;
	}

	public String getProv() {
		return prov;
	}

	public void setProv(String prov) {
		this.prov = prov;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getcType() {
		return cType;
	}

	public void setcType(String cType) {
		this.cType = cType;
	}

	public int getCjBishi() {
		return cjBishi;
	}

	public void setCjBishi(int cjBishi) {
		this.cjBishi = cjBishi;
	}

	public int getCjJineng() {
		return cjJineng;
	}

	public void setCjJineng(int cjJineng) {
		this.cjJineng = cjJineng;
	}

	public Date getCreate() {
		return create;
	}

	public void setCreate(Date create) {
		this.create = create;
	}

	public Date getActive() {
		return active;
	}

	public void setActive(Date active) {
		this.active = active;
	}

	public Date getLastLogin() {
		return lastLogin;
	}

	public void setLastLogin(Date lastLogin) {
		this.lastLogin = lastLogin;
	}

	public int getYear() {
		return year;
	}

	public void setYear(int year) {
		this.year = year;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

}
