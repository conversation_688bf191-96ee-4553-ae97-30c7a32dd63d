package com.career.db;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import com.career.utils.Arith;
import com.career.utils.PCXScore;
import com.career.utils.ZDScore;

public class DealZdksSQL {
	
	static HashMap<String, String> xk_code_map = new HashMap<>();
	static {
		xk_code_map.put("物理", "14387D");
		xk_code_map.put("理科", "14387D");
		xk_code_map.put("历史", "WLRKQG");
		xk_code_map.put("文科", "WLRKQG");
		xk_code_map.put("综合", "123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");
	}

	
	public static List<String> runConvert(int curZdksNF, String sf, String city, String zdpc, String kl, int scoreZDFrom, int scoreZDTo, int gaokao23Start, int gaokao23To, String hxb_id, String history_id) {
		List<String> list = new ArrayList<>();
		String provinceCode = JDBC.HM_PROVINCE_CODE.get(sf);
		double newStart = gaokao23Start;
		for(int i = scoreZDFrom;i < scoreZDTo;i++) {
			if(i > scoreZDFrom) {
				newStart = Arith.add(newStart, Arith.div(gaokao23To - gaokao23Start, scoreZDTo - scoreZDFrom)); 
				if(newStart >= gaokao23To) {
					newStart = gaokao23To;
				}
			}
			list.add("INSERT INTO "+provinceCode+"_zdks_score_convert(nf, sf, city, ZDPC, KL, KL_CODE, SCORE_FROM, SCORE_TO,hxb_id, history_id) VALUES("+curZdksNF+", '"+sf+"', '"+city+"', '"+zdpc+"', '"+kl+"', '"+xk_code_map.get(kl)+"' , "+i+","+Math.round(newStart)+",'"+hxb_id+"','"+history_id+"');");	
		}
		return list;
	}
	
	public static String generatDeleteSQL(int curZdksNF, String sf, String city, String zdpc) {
		String citySQL = city == null ? "" : "and x.city = '"+city+"'";
		String str = "DELETE FROM "+JDBC.HM_PROVINCE_CODE.get(sf)+"_zdks_score_convert x WHERE x.nf = "+curZdksNF+" and x.sf = '"+sf+"' "+citySQL+" and x.zdpc = '"+zdpc+"';";
		return str;
	}
	
	
	public static void main(String args[]) {
		HashMap<String, List<com.career.db.CityBean>> mapProvCity = com.career.db.JDBC.HM_PROVINCE_CITY;
		String sf = "广东";
		String exp = "广州";
		String zdpc = "皖豫名校联盟(10月)";
		List<com.career.db.CityBean> list = mapProvCity.get(sf);
		String provinceCode = JDBC.HM_PROVINCE_CODE.get(sf);
		for(CityBean x : list) {
			if(x.getCityNameExt().equals(exp)) {
				continue;
			}
			System.out.println("INSERT INTO "+provinceCode+"_zdks_score_convert(NF,SF,CITY,ZDPC,KL,KL_CODE,SCORE_FROM,SCORE_TO) SELECT NF,SF,'"+x.getCityNameExt()+"',ZDPC,KL,KL_CODE,SCORE_FROM,SCORE_TO from "+provinceCode+"_zdks_score_convert where  city = '"+exp+"';");
		}
	}
}
