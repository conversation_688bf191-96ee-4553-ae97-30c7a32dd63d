<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page import="com.zsdwf.utils.*,com.zsdwf.db.*,java.util.*" %>
<%@include file="/WEB-INF/include/_session_admin.jsp" %>
<%
YGCardBean card = (YGCardBean)session.getAttribute("SES_ADMIN"); 

YGJDBC jdbc = new YGJDBC();
YGCardBean cardLatest = jdbc.getYGAdminByIDandPasswd(card.getId(), card.getPasswd());

HashMap<String, List<com.career.db.CityBean>> cityList = com.career.db.JDBC.HM_PROVINCE_CITY;
HashMap<String, List<com.career.db.ZDPCBean>> cityZdpcList = com.career.db.JDBC.HM_PROVINCE_CITY_ZDPC;

int CNT_HXB = 6;
%>
<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"> 
<meta name="format-dection" content="telephone=no"/>
<title><%=CCache.SYS_TITLE_NAME %></title>
<script src="<%=request.getContextPath()%>/sources/jquery-3.6.4.min.js"></script>
<style type="text/css">
html {
    font-family: sans-serif;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
}
 
body {
    margin: 10px;
    font-size:12px;
}
table {
    border-collapse: collapse;
    border-spacing: 0;
}
 
td,th {
    padding: 0;
}
 
.pure-table {
    border-collapse: collapse;
    border-spacing: 0;
    empty-cells: show;
    border: 1px solid #cbcbcb;
}
 
.pure-table caption {
    color: #000;
    font: italic 85%/1 arial,sans-serif;
    padding: 1em 0;
    text-align: center;
}
 
.pure-table td,.pure-table th {
    border-left: 1px solid #cbcbcb;
    border-width: 0 0 0 1px;
    font-size: inherit;
    margin: 0;
    overflow: visible;
    padding: .5em 1em;
}
 
.pure-table thead {
    background-color: #e0e0e0;
    color: #000;
    text-align: left;
    vertical-align: bottom;
}
 
.pure-table td {
    background-color: transparent;
}
 
.pure-table-bordered td {
    border-bottom: 1px solid #cbcbcb;
}
 
.pure-table-bordered tbody>tr:last-child>td {
    border-bottom-width: 0;
}

.query_rec_label{
	float:left;width:70px;height:25px;line-height:25px;font-weight:bold;margin:2px 1px;
}
.query_rec_label2{
	float:left;width:60px;height:25px;line-height:25px;font-weight:bold;margin:2px 1px;
}
.query_rec_aa{
	float:left;width:130px;font-size:10px;height:25px;line-height:25px;margin:2px;border:1px solid #7766cc;text-align:center;cursor:pointer;
}
.query_rec_a{
	float:left;width:100px;font-size:10px;height:25px;line-height:25px;margin:2px;border:1px solid #7766cc;text-align:center;cursor:pointer;
}
.query_rec_b{
	float:left;width:60px;font-size:10px;height:25px;line-height:25px;margin:2px;border:1px solid #7766cc;text-align:center;cursor:pointer;
}
.query_rec_c{
	float:left;width:40px;font-size:10px;height:25px;line-height:25px;margin:2px;border:1px solid #7766cc;text-align:center;cursor:pointer;
}
.query_rec_d{
	float:left;width:80px;font-size:10px;height:25px;line-height:25px;margin:2px;border:1px solid #7766cc;text-align:center;cursor:pointer;
}
.query_bind_event{
	;
}
.query_select_bind_event{
	;
}


.school_pick{
	float:left;width:200px;border:1px solid red;height:20px;text-align:left;line-height:20px;margin:1px 10px;font-size:12px;;
}

.major_one{
	cursor:pointer;float:left;width:80px;height:400px;text-align:center;margin:1px;
}
.major_two{
	cursor:pointer;float:left;width:140px;height:400px;text-align:center;margin:1px;
}
.major_three{
	cursor:pointer;float:left;width:140px;height:400px;text-align:center;margin:1px;
}

.school_list{
	cursor:pointer;height:18px;text-align:left;line-height:18px;margin:1px 2px;font-size:12px;;
}
.major_info{
	cursor:pointer;float:left;width:210px;text-align:left;margin:2px;border:1px solid #cc0000;padding:1px;
}
.major_info3{
	cursor:pointer;float:left;width:300px;text-align:left;margin:2px;border:1px solid #cc0000;padding:1px;
}
.major_info2{
	cursor:pointer;float:left;width:128px;text-align:left;margin:2px;border:1px solid #cc0000;padding:1px;
}
.major_info22{
	cursor:pointer;float:left;width:155px;text-align:left;margin:2px;border:1px solid #cc0000;padding:1px;
}
.major_info_title{
	cursor:pointer;float:left;width:200px;text-align:left;margin:2px;font-weight:bold;fon-size:16px;color:blue;;
}

.fixedLayer1 { 
	position:fixed; 
	left: 0px; 
	top: 0px; 
	width:100px; 
	line-height:40px; 
	height:40px;
	background: #FC6; 
	font-size:26px;
	text-align:center;
	font-weight:bold;
	color:#000;
	border:1px solid #F90; 
	filter: alpha(opacity = 90);
	-moz-opacity: 0.9;
	-khtml-opacity: 0.9;
	opacity: 0.9;
} 

.fixedLayer2 { 
	position:fixed; 
	left: 0px; 
	top: 0px; 
	width:100px; 
	line-height:30px; 
	background: #FC6; 
	border:1px solid #F90; 
} 

.fixedLayerMenu { 
	line-height:25px; 
	height:25px;
	margin:5px;
} 
</style>
</head>
<%

%>
<body>
<div>
	<div style="margin:5px;">
		<div style="font-size:16px;font-weight:bold;text-align:left;'">诊断考试自动换算系统 V2.0</div>
	</div>
	<div>	
		<div style="margin:10px;">
			<div class="query_rec_label">高考年份：</div>
			<div style="float:left;margin-right:10px;">
				<input type="text" style="width:120px;height:30px;font-size:16px;color:blue;font-weight:bold;" value="2025" id="nf"/>
			</div>
			
			<div style="clear:both;"></div>
		</div>
		
		<div style="margin:10px;">
			<div class="query_rec_label">所属省份：</div>
			<div>
				<div style="float:left;margin-right:10px;">
				<select style="width:140px;height:35px;font-size:16px;" id="prov" onchange="MM_change_prov(this.value);">
					<%
					Iterator<String> its = cityList.keySet().iterator();
					while(its.hasNext()){
						String val = its.next();
					%>
					<option value="<%=val%>"><%=val%></option>
					<%}%>
					
				</select> 
				</div>
				
				<div style="float:left;margin-left:10px;"> 
					<input type="text" style="width:100px;height:30px;font-size:12px;" id="MM_add_new_pc">
					<input style="height:35px;font-size:12px;margin-left:3px;" type="button" onclick="MM_add_new_pc();" value="新增批次"> 
				</div> 
				 
				<div style="clear:both;"></div>
			</div>  
			     
			<div style="width:400px;margin:5px;height:290px;border:1px solid #666;background-color:#ccc">  
				<div style="margin:5px 15px;">模考日期：<input type="text" style="width:100px;height:20px;font-size:12px;" id="MM_add_new_hxb_rq" value="<%=com.career.utils.Tools.getDateForMonth(new Date())%>"></div>  
				<div style="margin:5px 15px;">名称备注：<input type="text" style="width:120px;height:20px;font-size:12px;" id="MM_add_new_hxb_title">&nbsp;<input type="text" style="width:120px;height:20px;font-size:12px;" id="MM_add_new_hxb_desc"></div>  
				<%for(int i=1;i<=CNT_HXB;i++){ %>
				<div style="margin:4px;">  
					<div style="float:left;margin-left:10px;">划线名称/划线分数</div> 
					<div style="float:left;margin-left:10px;"><input type="text" style="width:100px;height:20px;font-size:12px;" id="MM_add_new_hxb_name_<%=i%>">-><input type="text" style="width:100px;height:20px;font-size:12px;" id="MM_add_new_hxb_score_<%=i%>"></div>
					<div style="clear:both;"></div>
				</div>  
				<%} %>
				<div style="margin:5px 15px;">
					<input style="height:30px;font-size:12px;" type="button" onclick="MM_add_new_hx();" value="新增划线"> 
				</div>
			</div>
			
		</div>
		<div id="prov_change_area">
		
		
		</div>
		
		<div style="clear:both;"></div>
	</div>	
	
	
	
</div>


<div style="margin:5px 5px;" id="result_html">

</div>

</body>
</html>
<script>

function MM_add_new_hx(){

	
	var new_rq = $("#MM_add_new_hxb_rq").val();
	if(new_rq == ""){
		alert("请输入新增划线的日期");
		return;
	}
	var new_title = $("#MM_add_new_hxb_title").val();
	if(new_title == ""){
		alert("请输入新增划线的标题名称");
		return;
	}
	
	var hxb_name_1 = $("#MM_add_new_hxb_name_1").val();
	if(hxb_name_1 == ""){
		alert("请至少输入一个划线名称");
		return;
	}
	
	var hxb_score_1 = $("#MM_add_new_hxb_score_1").val();
	if(hxb_score_1 == ""){
		alert("请至少输入一个划线分数");
		return;
	}
	
	var selectedHxbValues = [];
	for(var i=1;i<=<%=CNT_HXB%>;i++){ 
		var hx_name = $("#MM_add_new_hxb_name_" + i).val();
		var hx_score = $("#MM_add_new_hxb_score_" + i).val();
		if(hx_name == "" || hx_score == ""){
			break;
		}
		selectedHxbValues.push(hx_name+"->"+hx_score);
	}
	
	var prov = $("#prov option:selected").val();
	
	if(confirm("确定新增："+prov+"->"+new_title + "->" + selectedHxbValues)){
		$.ajax({
	        type : "POST",
	        url : "<%=request.getContextPath()%>/admin/_zdks_add_newhxb.jsp",
			data: {
				"prov" : prov, 
				"new_rq" : new_rq, 
				"new_title" : new_title, 
				"new_desc" : $("#MM_add_new_hxb_desc").val(),  
				"selectedHxbValues" : selectedHxbValues.join(","),  
				'rd' : new Date().getTime()
			}, 
			//dataType: "json",
	        //请求成功
	        success : function(result) {
	        	if(result.indexOf("reload:refresh-list") != -1){

	        	}else if(result.indexOf("ERR:LOGIN:REDIRECT-X_LOGIN") != -1){
					location.href = "<%=request.getContextPath()%>/admin/login.jsp";
	        	}else{
	        		alert("新增成功");
	        	}
	        },
	        //请求失败，包含具体的错误信息
	        error : function(e){
	            console.log(e.status);
	            console.log(e.responseText);
	        }
	    });
	}
}

function MM_add_new_pc(){
	var newpc = $("#MM_add_new_pc").val();
	if(newpc == ""){
		alert("请输入新增的批次名称");
		return;
	}
	var prov = $("#prov option:selected").val();
	
	if(confirm("确定新增："+prov+"->"+newpc)){
		$.ajax({
	        type : "POST",
	        url : "<%=request.getContextPath()%>/admin/_zdks_add_newpc.jsp",
			data: {
				"prov" : prov, 
				"newpc" : newpc, 
				'rd' : new Date().getTime()
			}, 
			//dataType: "json",
	        //请求成功
	        success : function(result) {
	        	if(result.indexOf("reload:refresh-list") != -1){

	        	}else if(result.indexOf("ERR:LOGIN:REDIRECT-X_LOGIN") != -1){
					location.href = "<%=request.getContextPath()%>/admin/login.jsp";
	        	}else{
	        		alert("新增成功");
	        	}
	        },
	        //请求失败，包含具体的错误信息
	        error : function(e){
	            console.log(e.status);
	            console.log(e.responseText);
	        }
	    });
	}
}

function MM_change_prov(prov){
	$.ajax({
        type : "POST",
        url : "<%=request.getContextPath()%>/admin/_zdks_generate_getzdpc.jsp",
		data: {
			"prov" : prov, 
			'rd' : new Date().getTime()
		}, 
		//dataType: "json",
        //请求成功
        success : function(result) {
        	if(result.indexOf("reload:refresh-list") != -1){

        	}else if(result.indexOf("ERR:LOGIN:REDIRECT-X_LOGIN") != -1){
				location.href = "<%=request.getContextPath()%>/admin/login.jsp";
        	}else{
        		var resultHTML = $.trim(result);
        		$("#prov_change_area").html(resultHTML);
        	}
        },
        //请求失败，包含具体的错误信息
        error : function(e){
            console.log(e.status);
            console.log(e.responseText);
        }
    });
	
}


function MM_addTel(){
	var nf = $("#nf").val();
	var hxb_id = $("#hxb_id option:selected").val();
	var prov = $("#prov option:selected").val();
	
	var city = $("#city option:selected").val();
	if(city == ""){
		alert("请先选择城市");
		return false;
	}
	var zdpc = $("#zdpc option:selected").val();
	if(zdpc == ""){
		alert("请先选择诊断批次");
		return false;
	}
	
	$.ajax({
        type : "POST",
        url : "<%=request.getContextPath()%>/admin/_zdks_generate_import.jsp",
		data: {
			"nf" : nf,
			"hxb_id" : hxb_id,
			"prov" : prov,
			"city" : city, 
			"zdpc" : zdpc, 
			"PCX_1" : $("#PCX_1").val(),
			"PCX_2" : $("#PCX_2").val(),
			"PCX_3" : $("#PCX_3").val(),
			"PCX_4" : $("#PCX_4").val(),
			"PCX_5" : $("#PCX_5").val(),
			"PCX_6" : $("#PCX_6").val(),
			"PCX_7" : $("#PCX_7").val(),
			"PCX_8" : $("#PCX_8").val(),
			"L_1" : $("#L_1").val(),
			"W_1" : $("#W_1").val(),
			"L_2" : $("#L_2").val(),
			"W_2" : $("#W_2").val(),
			"L_3" : $("#L_3").val(),
			"W_3" : $("#W_3").val(),
			"L_4" : $("#L_4").val(),
			"W_4" : $("#W_4").val(),
			"L_5" : $("#L_5").val(),
			"W_5" : $("#W_5").val(),
			"L_6" : $("#L_6").val(),
			"W_6" : $("#W_6").val(),
			"L_7" : $("#L_7").val(),
			"W_7" : $("#W_7").val(),
			"L_8" : $("#L_8").val(),
			"W_8" : $("#W_8").val(),
			"L_A_1" : $("#L_A_1").val(),
			"W_A_1" : $("#W_A_1").val(),
			"L_A_2" : $("#L_A_2").val(),
			"W_A_2" : $("#W_A_2").val(),
			"L_A_3" : $("#L_A_3").val(),
			"W_A_3" : $("#W_A_3").val(),
			"L_A_4" : $("#L_A_4").val(),
			"W_A_4" : $("#W_A_4").val(),
			"L_A_5" : $("#L_A_5").val(),
			"W_A_5" : $("#W_A_5").val(),
			"L_A_6" : $("#L_A_6").val(),
			"W_A_6" : $("#W_A_6").val(),
			"L_A_7" : $("#L_A_7").val(),
			"W_A_7" : $("#W_A_7").val(),
			"L_A_8" : $("#L_A_8").val(),
			"W_A_8" : $("#W_A_8").val(),
			'rd' : new Date().getTime()
		}, 
		//dataType: "json",
        //请求成功
        success : function(result) {
        	if(result.indexOf("reload:refresh-list") != -1){
        		MM_searchTel();
        	}else if(result.indexOf("ERR:LOGIN:REDIRECT-X_LOGIN") != -1){ 
				location.href = "<%=request.getContextPath()%>/admin/login.jsp"; 
        	}else{
        		var resultHTML = $.trim(result);
        		var executeBUTTON = "<a target='_blank' href='<%=request.getContextPath()%>/admin/_zdks_generate_import_runsql.jsp'>直接运行</a><br><br>";
        		$("#result_html").html(executeBUTTON + resultHTML);
        		
        	}
        },
        //请求失败，包含具体的错误信息
        error : function(e){
            console.log(e.status);
            console.log(e.responseText);
        }
    });
}
</script>