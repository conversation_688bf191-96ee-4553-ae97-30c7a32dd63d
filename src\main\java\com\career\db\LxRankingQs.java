package com.career.db;

public class LxRankingQs {

	private String rank_view;
	private int ranking;
	private String yxmc;
	private String yxmc_org;
	private String country;
	private float score_zh;
	private float score_xs;
	private float score_gz;
	private float score_ssb;
	private float score_yyl;
	private float score_gjjs;
	private float score_gjxs;
	private float score_wl;
	private float score_jy;
	private float score_cxx;
	private int nf;
	
	public String getRank_view() {
		return rank_view;
	}
	public void setRank_view(String rank_view) {
		this.rank_view = rank_view;
	}
	public int getRanking() {
		return ranking;
	}
	public void setRanking(int ranking) {
		this.ranking = ranking;
	}
	public String getYxmc() {
		return yxmc;
	}
	public void setYxmc(String yxmc) {
		this.yxmc = yxmc;
	}
	public String getYxmc_org() {
		return yxmc_org;
	}
	public void setYxmc_org(String yxmc_org) {
		this.yxmc_org = yxmc_org;
	}
	public String getCountry() {
		return country;
	}
	public void setCountry(String country) {
		this.country = country;
	}
	public float getScore_zh() {
		return score_zh;
	}
	public void setScore_zh(float score_zh) {
		this.score_zh = score_zh;
	}
	public float getScore_xs() {
		return score_xs;
	}
	public void setScore_xs(float score_xs) {
		this.score_xs = score_xs;
	}
	public float getScore_gz() {
		return score_gz;
	}
	public void setScore_gz(float score_gz) {
		this.score_gz = score_gz;
	}
	public float getScore_ssb() {
		return score_ssb;
	}
	public void setScore_ssb(float score_ssb) {
		this.score_ssb = score_ssb;
	}
	public float getScore_yyl() {
		return score_yyl;
	}
	public void setScore_yyl(float score_yyl) {
		this.score_yyl = score_yyl;
	}
	public float getScore_gjjs() {
		return score_gjjs;
	}
	public void setScore_gjjs(float score_gjjs) {
		this.score_gjjs = score_gjjs;
	}
	public float getScore_gjxs() {
		return score_gjxs;
	}
	public void setScore_gjxs(float score_gjxs) {
		this.score_gjxs = score_gjxs;
	}
	public float getScore_wl() {
		return score_wl;
	}
	public void setScore_wl(float score_wl) {
		this.score_wl = score_wl;
	}
	public float getScore_jy() {
		return score_jy;
	}
	public void setScore_jy(float score_jy) {
		this.score_jy = score_jy;
	}
	public float getScore_cxx() {
		return score_cxx;
	}
	public void setScore_cxx(float score_cxx) {
		this.score_cxx = score_cxx;
	}
	public int getNf() {
		return nf;
	}
	public void setNf(int nf) {
		this.nf = nf;
	}
	
	
}
