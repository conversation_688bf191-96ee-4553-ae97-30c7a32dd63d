package com.career.db;

public class CareerJYJobREQ {
	
	private String jobtitle;
	private String salary;
	private int salaryCvt;
	private String salaryCount;
	private String property; //"property": "国企"
	private String workCity;
	private String workType;
	private String number;
	private String jobId;
	private String industryName;
	private String firstPublishTime;
	private String education;
	private String jobSummary;
	private String jobSkillTags;
	private String companyName;

	private String majorCatg;
	private String majorName;
	
	public int getSalaryCvt() {
		return salaryCvt;
	}
	public void setSalaryCvt(int salaryCvt) {
		this.salaryCvt = salaryCvt;
	}
	public String getJobtitle() {
		return jobtitle;
	}
	public void setJobtitle(String jobtitle) {
		this.jobtitle = jobtitle;
	}
	public String getSalary() {
		return salary;
	}
	public void setSalary(String salary) {
		this.salary = salary;
	}
	public String getSalaryCount() {
		return salaryCount;
	}
	public void setSalaryCount(String salaryCount) {
		this.salaryCount = salaryCount;
	}
	public String getProperty() {
		return property;
	}
	public void setProperty(String property) {
		this.property = property;
	}
	public String getWorkCity() {
		return workCity;
	}
	public void setWorkCity(String workCity) {
		this.workCity = workCity;
	}
	public String getWorkType() {
		return workType;
	}
	public void setWorkType(String workType) {
		this.workType = workType;
	}
	public String getNumber() {
		return number;
	}
	public void setNumber(String number) {
		this.number = number;
	}
	public String getJobId() {
		return jobId;
	}
	public void setJobId(String jobId) {
		this.jobId = jobId;
	}
	public String getIndustryName() {
		return industryName;
	}
	public void setIndustryName(String industryName) {
		this.industryName = industryName;
	}
	public String getFirstPublishTime() {
		return firstPublishTime;
	}
	public void setFirstPublishTime(String firstPublishTime) {
		this.firstPublishTime = firstPublishTime;
	}
	public String getEducation() {
		return education;
	}
	public void setEducation(String education) {
		this.education = education;
	}
	public String getJobSummary() {
		return jobSummary;
	}
	public void setJobSummary(String jobSummary) {
		this.jobSummary = jobSummary;
	}
	public String getJobSkillTags() {
		return jobSkillTags;
	}
	public void setJobSkillTags(String jobSkillTags) {
		this.jobSkillTags = jobSkillTags;
	}
	public String getCompanyName() {
		return companyName;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	public String getMajorCatg() {
		return majorCatg;
	}
	public void setMajorCatg(String majorCatg) {
		this.majorCatg = majorCatg;
	}
	public String getMajorName() {
		return majorName;
	}
	public void setMajorName(String majorName) {
		this.majorName = majorName;
	}
	
	

}
