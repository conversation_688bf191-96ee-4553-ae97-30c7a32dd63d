package com.career.utils.report.Test;

import com.career.db.*;
import com.career.utils.*;
import com.career.utils.report.AdmissionProbabilityEvaluator;
import com.career.utils.report.FormReviewEvaluator;
import com.career.utils.report.FormReviewEvaluator.FormReviewContext;

import java.util.*;

/**
 * 录取概率评估器测试类
 */
public class AdmissionProbabilityTest {
    
    public static void main(String[] args) {
        System.out.println("=== 录取概率评估器使用示例 ===");
        
        try {
            // 1. 创建测试数据
            SuperFormMain superFormMain = createTestSuperFormMain();
            ZyzdProvinceConfig provinceConfig = createTestProvinceConfig();
            List<SuperForm> superFormList = createTestSuperFormList();
            
            // 2. 创建评估上下文
            FormReviewEvaluator.FormReviewParams params = new FormReviewEvaluator.FormReviewParams(
                superFormMain, provinceConfig, null);
            params.setSuperFormList(superFormList);
            
            FormReviewContext context = new FormReviewContext(
                superFormList, superFormMain, null, provinceConfig,
                params.getUserRank(), params.getRankYearA(), params.getRankYearB(), params.getRankYearC());
            
            // 模拟加载计划数据
            simulatePlanData(context, superFormList);
            
            // 3. 添加调试信息
            System.out.println("省份配置最新年份: " + provinceConfig.getLatest_year_jh());
            System.out.println("rankYearA: " + (params.getRankYearA() != null ? params.getRankYearA().getNf() : "null"));
            System.out.println("rankYearB: " + (params.getRankYearB() != null ? params.getRankYearB().getNf() : "null"));
            System.out.println("rankYearC: " + (params.getRankYearC() != null ? params.getRankYearC().getNf() : "null"));
            
            // 4. 执行录取概率评估
            AdmissionProbabilityEvaluator.AdmissionProbabilityResult result = 
                AdmissionProbabilityEvaluator.evaluateAdmissionProbability(context);
            
            // 5. 输出结果
            printResults(result);
            
        } catch (Exception e) {
            System.err.println("测试执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 创建测试用的SuperFormMain
     */
    private static SuperFormMain createTestSuperFormMain() {
        SuperFormMain main = new SuperFormMain();
        main.setScore_cj(580);  // 考生成绩
        main.setScore_wc(15000); // 位次
        main.setScore_xk("物理");  // 选科
        main.setPc("本科一批");
        main.setPc_code("01");
        return main;
    }
    
    /**
     * 创建测试用的省份配置
     */
    private static ZyzdProvinceConfig createTestProvinceConfig() {
        ZyzdProvinceConfig config = new ZyzdProvinceConfig();
        config.setP_name("四川");
        config.setP_table_code("s5_sc");
        config.setLatest_year_jh(2025);  // 设置正确的年份
        return config;
    }
    
    /**
     * 创建测试用的志愿表单列表（45个志愿）
     */
    private static List<SuperForm> createTestSuperFormList() {
        List<SuperForm> list = new ArrayList<>();
        
        // 定义院校和专业数据
        String[][] universityData = {
            // 冲刺院校（1-16）- 高分院校
            {"10001", "北京大学", "01", "080901", "计算机科学与技术"},
            {"10002", "清华大学", "01", "080901", "计算机科学与技术"},
            {"10003", "复旦大学", "02", "080901", "计算机科学与技术"},
            {"10004", "上海交通大学", "01", "080901", "计算机科学与技术"},
            {"10005", "浙江大学", "03", "080901", "计算机科学与技术"},
            {"10006", "南京大学", "01", "080901", "计算机科学与技术"},
            {"10007", "中国科学技术大学", "02", "080901", "计算机科学与技术"},
            {"10008", "哈尔滨工业大学", "01", "080901", "计算机科学与技术"},
            {"10009", "西安交通大学", "03", "080901", "计算机科学与技术"},
            {"10010", "华中科技大学", "01", "080901", "计算机科学与技术"},
            {"10011", "东南大学", "02", "080901", "计算机科学与技术"},
            {"10012", "天津大学", "01", "080901", "计算机科学与技术"},
            {"10013", "大连理工大学", "03", "080901", "计算机科学与技术"},
            {"10014", "华南理工大学", "01", "080901", "计算机科学与技术"},
            {"10015", "北京理工大学", "02", "080901", "计算机科学与技术"},
            {"10016", "西北工业大学", "01", "080901", "计算机科学与技术"},
            
            // 稳妥院校（17-30）- 中等分数院校
            {"10017", "电子科技大学", "01", "080901", "计算机科学与技术"},
            {"10018", "四川大学", "02", "080901", "计算机科学与技术"},
            {"10019", "重庆大学", "01", "080901", "计算机科学与技术"},
            {"10020", "西南交通大学", "03", "080901", "计算机科学与技术"},
            {"10021", "北京邮电大学", "01", "080901", "计算机科学与技术"},
            {"10022", "华东理工大学", "02", "080901", "计算机科学与技术"},
            {"10023", "南京理工大学", "01", "080901", "计算机科学与技术"},
            {"10024", "西安电子科技大学", "03", "080901", "计算机科学与技术"},
            {"10025", "北京交通大学", "01", "080901", "计算机科学与技术"},
            {"10026", "华北电力大学", "02", "080901", "计算机科学与技术"},
            {"10027", "南京航空航天大学", "01", "080901", "计算机科学与技术"},
            {"10028", "西北大学", "03", "080901", "计算机科学与技术"},
            {"10029", "郑州大学", "01", "080901", "计算机科学与技术"},
            {"10030", "湖南大学", "02", "080901", "计算机科学与技术"},
            
            // 保底院校（31-45）- 较低分数院校
            {"10031", "成都理工大学", "01", "080901", "计算机科学与技术"},
            {"10032", "西南石油大学", "02", "080901", "计算机科学与技术"},
            {"10033", "四川师范大学", "01", "080901", "计算机科学与技术"},
            {"10034", "西华大学", "03", "080901", "计算机科学与技术"},
            {"10035", "成都信息工程大学", "01", "080901", "计算机科学与技术"},
            {"10036", "四川理工学院", "02", "080901", "计算机科学与技术"},
            {"10037", "西南科技大学", "01", "080901", "计算机科学与技术"},
            {"10038", "成都大学", "03", "080901", "计算机科学与技术"},
            {"10039", "四川轻化工大学", "01", "080901", "计算机科学与技术"},
            {"10040", "西南民族大学", "02", "080901", "计算机科学与技术"},
            {"10041", "成都工业学院", "01", "080901", "计算机科学与技术"},
            {"10042", "四川文理学院", "03", "080901", "计算机科学与技术"},
            {"10043", "绵阳师范学院", "01", "080901", "计算机科学与技术"},
            {"10044", "内江师范学院", "02", "080901", "计算机科学与技术"},
            {"10045", "乐山师范学院", "01", "080901", "计算机科学与技术"}
        };
        
        // 创建45个志愿
        for (int i = 0; i < 45; i++) {
            SuperForm form = new SuperForm();
            String[] data = universityData[i];
            
            form.setYxdm(data[0]);                    // 院校代码
            form.setYxmc_org(data[1]);               // 院校名称
            form.setZyz(data[2]);                    // 专业组代码
            form.setZydm(data[3]);                   // 专业代码
            form.setZymc(data[4]);                   // 专业名称
            
            // 设置志愿序号
            form.setSeq_no_yx(i);
            
            list.add(form);
        }
        
        return list;
    }
    
    /**
     * 模拟计划数据加载
     * 由于测试环境可能没有真实数据，我们创建模拟的JHBean数据
     */
    private static void simulatePlanData(FormReviewContext context, List<SuperForm> superFormList) {
        System.out.println("[simulatePlanData] 开始模拟计划数据加载");
        
        // 通过反射访问私有字段来设置模拟数据
        try {
            java.lang.reflect.Field detailedPlanDataField = FormReviewContext.class.getDeclaredField("detailedPlanData");
            detailedPlanDataField.setAccessible(true);
            
            @SuppressWarnings("unchecked")
            Map<String, List<JHBean>> detailedPlanData = (Map<String, List<JHBean>>) detailedPlanDataField.get(context);
            
            if (detailedPlanData == null) {
                detailedPlanData = new HashMap<>();
                detailedPlanDataField.set(context, detailedPlanData);
            }
            
            // 为每个志愿创建模拟的JHBean数据
            for (SuperForm superForm : superFormList) {
                String key = Tools.trim(superForm.getYxdm()) + "|" + Tools.trim(superForm.getZyz());
                
                List<JHBean> planData = new ArrayList<>();
                JHBean jhBean = createMockJHBean(superForm);
                planData.add(jhBean);
                
                detailedPlanData.put(key, planData);
                System.out.println("[simulatePlanData] 为 " + key + " 创建模拟数据，预测分: " + jhBean.getQsf());
            }
            
            // 设置数据已加载标志
            java.lang.reflect.Field planDataLoadedField = FormReviewContext.class.getDeclaredField("planDataLoaded");
            planDataLoadedField.setAccessible(true);
            planDataLoadedField.set(context, true);
            
            System.out.println("[simulatePlanData] 模拟数据加载完成，共加载 " + detailedPlanData.size() + " 个专业组");
            
        } catch (Exception e) {
            System.err.println("[simulatePlanData] 模拟数据加载失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 创建模拟的JHBean数据
     */
    private static JHBean createMockJHBean(SuperForm superForm) {
        JHBean jhBean = new JHBean();
        
        // 基本信息
        jhBean.setYxdm(superForm.getYxdm());
        jhBean.setYxmc_org(superForm.getYxmc_org());
        jhBean.setZyz(superForm.getZyz());
        jhBean.setZydm(superForm.getZydm());
        jhBean.setZymc_org(superForm.getZymc());
        
        // 根据院校代码模拟不同的分数线
        String yxdm = superForm.getYxdm();
        int baseScore = getBaseScoreByUniversity(yxdm);
        
        // 设置三年趋势分（模拟数据）
        jhBean.setQsf_a(baseScore + 2);  // A年（最近年）
        jhBean.setQsf_b(baseScore + 1);  // B年
        jhBean.setQsf_c(baseScore);      // C年
        
        // 设置预测趋势分
        jhBean.setQsf(baseScore + 3);    // 预测分数
        
        // 设置招生计划
        jhBean.setJhs_a("50");
        jhBean.setJhs_b("48");
        jhBean.setJhs_c("45");
        
        return jhBean;
    }
    
    /**
     * 根据院校代码获取基础分数
     */
    private static int getBaseScoreByUniversity(String yxdm) {
        int code = Integer.parseInt(yxdm);
        
        if (code <= 10016) {
            // 冲刺院校：620-650分
            return 620 + (code - 10001) * 2;
        } else if (code <= 10030) {
            // 稳妥院校：580-620分
            return 580 + (code - 10017) * 3;
        } else {
            // 保底院校：520-580分
            return 520 + (code - 10031) * 4;
        }
    }
    
    /**
     * 打印评估结果
     */
    private static void printResults(AdmissionProbabilityEvaluator.AdmissionProbabilityResult result) {
        System.out.println("\n=== 录取概率评估结果 ===");
        System.out.println("考生成绩: " + result.getStudentScore());
        System.out.println("总志愿数: " + result.getTotalVolunteers());
        System.out.println("可录取志愿数: " + result.getAdmittableVolunteers());
        System.out.println("正常录取数: " + result.getNormalAdmissionCount());
        System.out.println("调剂录取数: " + result.getObedienceAdjustmentAdmissionCount());
        System.out.println("无法录取数: " + result.getNoAdmissionCount());
        System.out.printf("平均录取概率: %.1f%%\n", result.getAverageProbability());
        System.out.printf("最高录取概率: %.1f%%\n", result.getMaxProbability());
        System.out.printf("录取率: %.1f%%\n", result.getAdmissionRate() * 100);
        
        System.out.println("\n=== 录取预测结果 ===");
        List<AdmissionProbabilityEvaluator.AdmissionPrediction> predictions = result.getAdmissionPredictions();
        if (predictions.isEmpty()) {
            System.out.println("暂无录取预测结果");
        } else {
            for (AdmissionProbabilityEvaluator.AdmissionPrediction prediction : predictions) {
                System.out.printf("第%d概率录取: 院校%s 专业%s 概率%.1f%%\n", 
                    prediction.getPredictionRank(),
                    prediction.getYxmc(),
                    prediction.getAdmittedMajor(),
                    prediction.getAdmissionProbability());
            }
        }
        
        System.out.println("\n=== 详细志愿结果 ===");
        List<AdmissionProbabilityEvaluator.VolunteerAdmissionResult> volunteerResults = result.getVolunteerResults();
        if (volunteerResults.isEmpty()) {
            System.out.println("暂无详细志愿结果");
        } else {
            for (AdmissionProbabilityEvaluator.VolunteerAdmissionResult volunteerResult : volunteerResults) {
                System.out.printf("志愿%d: %s %s - %s (概率%.1f%%)\n",
                    volunteerResult.getVolunteerNumber(),
                    volunteerResult.getYxmc(),
                    volunteerResult.getFilledMajor(),
                    volunteerResult.getAdmissionType().getDescription(),
                    volunteerResult.getAdmissionProbability());
            }
        }
    }
} 