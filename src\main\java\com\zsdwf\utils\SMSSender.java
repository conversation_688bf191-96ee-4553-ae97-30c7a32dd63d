package com.zsdwf.utils;

import com.aliyun.teaopenapi.models.Config;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;


public class SMSSender {

	public static void main(String args[]) {
		//sendAliSMSCode("15982928237","9988");
		//sendAliSMSCardTrial("18681352993","S2232","9999");
		//sendAliSMSCardTrial("15982928237","S1111","1234");
		//sendAliSMSCardVIP("18681352993","S76434","3323");
		sendAliSMSCardVIP("15982928237","S76434","3323");
		//sendTrialCardNotification("19983196622","T0922","1234");
	}
	
	public static Client createAliClient() throws Exception {
        Config config = new Config()
                // 配置 AccessKey ID，请确保代码运行环境配置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
                .setAccessKeyId("LTAI5tHaquKX2NkiQqK8w8gz")
                // 配置 AccessKey Secret，请确保代码运行环境配置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
                .setAccessKeySecret("******************************");
                // System.getenv()方法表示获取系统环境变量，不要直接在getenv()中填入AccessKey信息。
        
        // 配置 Endpoint。中国站使用dysmsapi.aliyuncs.com
        config.endpoint = "dysmsapi.aliyuncs.com";

        return new Client(config);
    }
	
	public static final void sendAliSMSCode(String phone, String code) {
		
        try {
        	Client client = createAliClient();

            // 构造API请求对象，请替换请求参数值
            SendSmsRequest sendSmsRequest = new SendSmsRequest()
                    .setPhoneNumbers(phone)
                    .setSignName("领有教育")
                    .setTemplateCode("SMS_279267053")
                    .setTemplateParam("{\"code\":\""+code+"\"}"); // TemplateParam为序列化后的JSON字符串。

            // 获取响应对象
			SendSmsResponse sendSmsResponse = client.sendSms(sendSmsRequest);
			System.out.println("sendAliSMSCode sent - " + phone + ", " + code);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

        // 响应包含服务端响应的 body 和 headers
	}
	
	public static final void sendAliSMSCardTrial(String phone, String cid, String passwd) {
        try {
        	Client client = createAliClient();

            // 构造API请求对象，请替换请求参数值
            SendSmsRequest sendSmsRequest = new SendSmsRequest()
                    .setPhoneNumbers(phone)
                    .setSignName("领有教育")
                    .setTemplateCode("SMS_484080168")
                    .setTemplateParam("{\"cid\":\""+cid+"\",\"passwd\":\""+passwd+"\"}"); // TemplateParam为序列化后的JSON字符串。

            // 获取响应对象
			SendSmsResponse sendSmsResponse = client.sendSms(sendSmsRequest);
			System.out.println("sendAliSMSCardTrial sent - " + phone + ", " + cid);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

        // 响应包含服务端响应的 body 和 headers
	}

	public static final void sendAliSMSCardVIP(String phone, String cid, String passwd) {
		
	    try {
	    	Client client = createAliClient();
	
	        // 构造API请求对象，请替换请求参数值
	        SendSmsRequest sendSmsRequest = new SendSmsRequest()
	                .setPhoneNumbers(phone)
	                .setSignName("领有教育")
	                .setTemplateCode("SMS_484060160")
	                .setTemplateParam("{\"cid\":\""+cid+"\",\"passwd\":\""+passwd+"\"}"); // TemplateParam为序列化后的JSON字符串。
	
	        // 获取响应对象
			SendSmsResponse sendSmsResponse = client.sendSms(sendSmsRequest);
			System.out.println("sendAliSMSCardVIP sent - " + phone + ", " + cid);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	
	    // 响应包含服务端响应的 body 和 headers
	}
	
	public static final void sendSMSCode(String phone, String code) {
		String reStr = ""; //定义返回值
        // 短信应用SDK AppID  1400开头
        int appid = 1400828994 ;
        // 短信应用SDK AppKey
        String appkey = "25a335b63d9dee7431af770172460d56";
        // 短信模板ID，需要在短信应用中申请
        int templateId = 1826075 ;
        // 签名，使用的是签名内容，而不是签名ID
        String smsSign = "领有科技";

        try {
            //参数，一定要对应短信模板中的参数顺序和个数，
            String[] params = new String[1];
            params[0] = code;
            //创建ssender对象
            
            
            com.github.qcloudsms.SmsSingleSender ssender = new com.github.qcloudsms.SmsSingleSender(appid, appkey);
            //发送
            com.github.qcloudsms.SmsSingleSenderResult result = ssender.sendWithParam("86", phone, templateId, params, smsSign, "", "");
            System.out.println("****************sms service process("+phone+","+code+")****************");
        }catch (Exception e) {
            e.printStackTrace();
        }
	}
	
	public static final void sendTrialCardNotification(String phone, String cardNo, String cardPasswd) {
		String reStr = ""; //定义返回值
        // 短信应用SDK AppID  1400开头
        int appid = 1400828994 ;
        // 短信应用SDK AppKey
        String appkey = "25a335b63d9dee7431af770172460d56";
        // 短信模板ID，需要在短信应用中申请
        int templateId = 2410916 ;
        // 签名，使用的是签名内容，而不是签名ID
        String smsSign = "领有科技";

        try {
            //参数，一定要对应短信模板中的参数顺序和个数，
            String[] params = new String[2];
            params[0] = cardNo;
            params[1] = cardPasswd;
            //创建ssender对象
           
            com.github.qcloudsms.SmsSingleSender ssender = new com.github.qcloudsms.SmsSingleSender(appid, appkey);
            com.github.qcloudsms.SmsSingleSenderResult result = ssender.sendWithParam("86", phone, templateId, params, smsSign, "", "");
        }catch (Exception e) {
            e.printStackTrace();
        }
	}
	
	public static final void sendActiveCardNotification(String phone, String cardNo, String cardPasswd) {
		String reStr = ""; //定义返回值
        // 短信应用SDK AppID  1400开头
        int appid = 1400828994 ;
        // 短信应用SDK AppKey
        String appkey = "25a335b63d9dee7431af770172460d56";
        // 短信模板ID，需要在短信应用中申请
        int templateId = 1834994 ;
        // 签名，使用的是签名内容，而不是签名ID
        String smsSign = "领有科技";

        try {
            //参数，一定要对应短信模板中的参数顺序和个数，
            String[] params = new String[2];
            params[0] = cardNo;
            params[1] = cardPasswd;
            //创建ssender对象
           
            com.github.qcloudsms.SmsSingleSender ssender = new com.github.qcloudsms.SmsSingleSender(appid, appkey);
            com.github.qcloudsms.SmsSingleSenderResult result = ssender.sendWithParam("86", phone, templateId, params, smsSign, "", "");
        }catch (Exception e) {
            e.printStackTrace();
        }
	}
	
	public static final void sendCardNotification(String phone, String cardNo, String cardPasswd) {
		String reStr = ""; //定义返回值
        // 短信应用SDK AppID  1400开头
        int appid = 1400828994 ;
        // 短信应用SDK AppKey
        String appkey = "25a335b63d9dee7431af770172460d56";
        // 短信模板ID，需要在短信应用中申请
        int templateId = 2427313 ;
        // 签名，使用的是签名内容，而不是签名ID
        String smsSign = "领有科技";

        try {
            //参数，一定要对应短信模板中的参数顺序和个数，
            String[] params = new String[2];
            params[0] = cardNo;
            params[1] = cardPasswd;
            //创建ssender对象
           
            com.github.qcloudsms.SmsSingleSender ssender = new com.github.qcloudsms.SmsSingleSender(appid, appkey);
            com.github.qcloudsms.SmsSingleSenderResult result = ssender.sendWithParam("86", phone, templateId, params, smsSign, "", "");
        }catch (Exception e) {
            e.printStackTrace();
        }
	}
	
	public static final void sendPaidNotification(String phone, String cardNo, String cardType) {
		String reStr = ""; //定义返回值
        // 短信应用SDK AppID  1400开头
        int appid = 1400828994 ;
        // 短信应用SDK AppKey
        String appkey = "25a335b63d9dee7431af770172460d56";
        // 短信模板ID，需要在短信应用中申请
        int templateId = 2427633 ;
        // 签名，使用的是签名内容，而不是签名ID
        String smsSign = "领有科技";

        try {
            //参数，一定要对应短信模板中的参数顺序和个数，
            String[] params = new String[2];
            params[0] = cardType;
            params[1] = cardNo;
            //创建ssender对象
           
            com.github.qcloudsms.SmsSingleSender ssender = new com.github.qcloudsms.SmsSingleSender(appid, appkey);
            com.github.qcloudsms.SmsSingleSenderResult result = ssender.sendWithParam("86", phone, templateId, params, smsSign, "", "");
        }catch (Exception e) {
            e.printStackTrace();
        }
	}
	
}
