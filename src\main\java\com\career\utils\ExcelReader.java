package com.career.utils;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.career.db.DataPatchJDBC;
import com.career.db.JDBCConstants;
import com.career.db.YxzyStatistics;

import cn.hutool.core.lang.UUID;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.FileVisitResult;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.SimpleFileVisitor;
import java.nio.file.attribute.BasicFileAttributes;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class ExcelReader {

	
    public static void readExcelFile(String filePath, String fileName) throws SQLException {
        StringBuffer SQL = new StringBuffer();
        // 创建一个FileInputStream对象来读取Excel文件
        
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        
        List<YxzyStatistics> insertSQL = new ArrayList<>();
        
        try (FileInputStream file = new FileInputStream(new File(filePath))) {
        	
        	connection = DriverManager.getConnection(JDBCConstants.URL, JDBCConstants.USER, JDBCConstants.PASSWD);
        	
        	String sql = "INSERT INTO zyzd_base_yxzy_statistics " +
                    "(group_id, table_id, item_id, item_sort, column_title, " +
                    "column_title_standare, column_value, column_sort) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        	
            // 创建一个Workbook对象来读取Excel文件
            Workbook workbook = new XSSFWorkbook(file);
            // 获取第一个Sheet
            Sheet sheet = workbook.getSheetAt(0);
            String table_id = UUID.randomUUID().toString();
            String group_id = table_id;
            // 遍历所有行
            int item_sort = 10000;
            HashMap<Integer, String> MapTitle = new HashMap<>();
            for (Row row : sheet) {
            	item_sort--;
            	int column_sort = 1000;

            	String item_id = UUID.randomUUID().toString();
                // 遍历所有单元格
                for (Cell cell : row) {
                	column_sort--;
                	String column_value = "";
                    // 根据单元格类型读取数据
                    switch (cell.getCellType()) {
                        case STRING:
                        	column_value = String.valueOf(cell.getStringCellValue());
                            break;
                        case NUMERIC: 
                            if (DateUtil.isCellDateFormatted(cell)) {
                            	column_value = String.valueOf(cell.getDateCellValue());
                            } else {
                            	// 检查是否为整数
                                double value = cell.getNumericCellValue();
                                if (value == Math.floor(value) && !Double.isInfinite(value)) {
                                    // 如果是整数，去掉小数部分
                                	column_value = String.valueOf((int) value);
                                } else {
                                    // 如果是小数，保留小数部分
                                    DecimalFormat df = new DecimalFormat("#.####");
                                	column_value = String.valueOf(df.format(value));
                                }
                            }
                            break;
                        case BOOLEAN:
                            String.valueOf(cell.getBooleanCellValue());
                            break;
                        case FORMULA:
                            String.valueOf(cell.getCellFormula());
                            break;
                        case BLANK:
                        	continue;
                        case ERROR:
                            System.out.println(item_sort+","+column_sort);
                            System.out.println(cell.getCellType());
                            System.out.println(cell.getStringCellValue());
                        	throw new RuntimeException();
                        default:
                            System.out.println(cell.getCellType());
                            throw new RuntimeException();
                    }
                    
                    if(item_sort == 9999) {
                    	MapTitle.put(column_sort, column_value);
                    }else {
                    	String column_title = MapTitle.get(column_sort);
                    	
                    	// 创建YxzyStatistics对象
                    	YxzyStatistics bean = new YxzyStatistics();

                    	// 通过set方法存入变量
                    	bean.setGroup_id(group_id);
                    	bean.setTable_id(table_id);
                    	bean.setItem_id(item_id);
                    	bean.setItem_sort(item_sort);
                    	bean.setColumn_title(column_title);
                    	bean.setColumn_title_standare(column_title);
                    	bean.setColumn_value(column_value);
                    	bean.setColumn_sort(column_sort);
                    	insertSQL.add(bean);
                    	
                    }
                }
            }
            
            fileName = fileName.replaceAll("【高绩数据】", "").replaceAll("【高绩网】", "").replaceAll("【高绩网数据】", "");
            
            System.out.println("INSERT INTO zyzd_base_yxzy_statistics_main \n"
            		+ "(table_id, table_name, table_sort, group_id, group_name, group_sort)\n"
            		+ "VALUES \n"
            		+ "('"+table_id+"', '"+fileName+"', 1, '"+group_id+"', '"+group_id+"', 1);");
            
            
            preparedStatement = connection.prepareStatement(sql);
            
            // 4. Add batch for each YxzyStatistics object
            for (YxzyStatistics bean : insertSQL) {
                preparedStatement.setString(1, bean.getGroup_id());
                preparedStatement.setString(2, bean.getTable_id());
                preparedStatement.setString(3, bean.getItem_id());
                preparedStatement.setInt(4, bean.getItem_sort());
                preparedStatement.setString(5, bean.getColumn_title());
                preparedStatement.setString(6, bean.getColumn_title_standare());
                preparedStatement.setString(7, bean.getColumn_value());
                preparedStatement.setInt(8, bean.getColumn_sort());
                
                preparedStatement.addBatch();
            }
            
            preparedStatement.executeBatch();
            
        } catch (IOException e) {
            e.printStackTrace();
        }
        
        DataPatchJDBC.insert_zyzd_base_yxzy_statistics(insertSQL);
        
        writeTempFile(new File("D:\\base_data\\LBT_SQL_"+fileName+".txt"), SQL);
    }

    public static void main(String[] args) {
    	
    	// 指定目录路径
        Path startPath = Paths.get("D:\\base_data\\org");

        // 检查目录是否存在
        if (!Files.exists(startPath)) {
            System.out.println("指定的目录不存在：" + startPath);
            return;
        }

        // 使用Files.walkFileTree递归遍历目录
        try {
            Files.walkFileTree(startPath, new SimpleFileVisitor<Path>() {
                public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) {
                    // 打印文件路径
                	String fileName = file.getFileName().toString();
                    System.out.println("文件：" + file.getFileName());
                    String filePathRead = "D:\\base_data\\org\\" + file.getFileName();

                	fileName = fileName.replaceAll(".xlsx", "");
                	System.out.println("文件：" + file.getFileName()+" , " + fileName);
                	
                	try {
						readExcelFile(filePathRead, fileName);
					} catch (SQLException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
                    
                    return FileVisitResult.CONTINUE;
                }

                public FileVisitResult visitFileFailed(Path file, IOException exc) {
                    // 如果访问文件失败，打印错误信息
                    System.err.println("无法访问文件：" + file + "，错误：" + exc.getMessage());
                    return FileVisitResult.CONTINUE;
                }
            });
        } catch (IOException e) {
            System.err.println("遍历目录时发生错误：" + e.getMessage());
        }
    	
        // 替换为你的Excel文件路径
       // String filePath = "D:\\base_data\\"+fileName+".xlsx";
        //readExcelFile(filePath, fileName);
    }
    
    private static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
}
