<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page import="com.career.utils.*,com.career.db.*,java.util.*" %>

<%@include file="/WEB-INF/include/_session_admin_ajax.jsp" %>
<%
String cardno = Tools.trim(request.getParameter("cardno"));


if(Tools.isEmpty(cardno)){
	out.println("卡号都不输，你提交搞哪样");
	return;
}


JDBC jdbc = new JDBC();
CardBean cardBean = jdbc.getCardByID(cardno);
if(cardBean == null){
	out.println("卡号不存在，请确认输入是否正确");
	return;
}

if(cardBean.getStatus() != 1){
	out.println("该卡号已被冻结，无法继续使用");
	return;
}

out.println("卡号["+cardBean.getId()+"]，省份["+cardBean.getProv()+"]，城市["+cardBean.getCity()+"]，选科["+cardBean.getXk()+"]，录入位次["+cardBean.getScore()+"]，最后登录时间["+Tools.getDate(cardBean.getLastLogin())+"]");
%>



		