package com.career.utils.zsky;

public class GJXItem {
	
	private int id;
	private String code;
	private String name;
	private int total;
	private int single_100;
	private int single_150;
	private int degree_type;
	private int year;
	private String area_type;
	private int first_id;
	private String second_id;
	private int zhaogu;
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public int getTotal() {
		return total;
	}
	public void setTotal(int total) {
		this.total = total;
	}
	public int getSingle_100() {
		return single_100;
	}
	public void setSingle_100(int single_100) {
		this.single_100 = single_100;
	}
	public int getSingle_150() {
		return single_150;
	}
	public void setSingle_150(int single_150) {
		this.single_150 = single_150;
	}
	public int getDegree_type() {
		return degree_type;
	}
	public void setDegree_type(int degree_type) {
		this.degree_type = degree_type;
	}
	public int getYear() {
		return year;
	}
	public void setYear(int year) {
		this.year = year;
	}
	public String getArea_type() {
		return area_type;
	}
	public void setArea_type(String area_type) {
		this.area_type = area_type;
	}
	public int getFirst_id() {
		return first_id;
	}
	public void setFirst_id(int first_id) {
		this.first_id = first_id;
	}
	public String getSecond_id() {
		return second_id;
	}
	public void setSecond_id(String second_id) {
		this.second_id = second_id;
	}
	public int getZhaogu() {
		return zhaogu;
	}
	public void setZhaogu(int zhaogu) {
		this.zhaogu = zhaogu;
	}
	
	public String generateSQL() {
		String SQL = "insert into career_gjx(gjx_id, gjx_code, gjx_name, total, single_100, single_150, degree_type, gjx_year, area_type, first_id, second_id, zhaogu) values('" + id + "', '" + code
				+ "', '" + name + "','" + total + "', '" + single_100 + "', '" + single_150 + "', '" + degree_type + "','" + year + "', '" + area_type +"', '"+first_id+"', '"+second_id+"','"+zhaogu+"');";
		return SQL;
	}

	
	
}
