package com.career.utils.report.Test;

import com.career.db.*;
import com.career.utils.*;
import com.career.utils.report.UserNeedsEvaluator;
import com.career.utils.report.FormReviewEvaluator;
import com.career.utils.report.FormReviewEvaluator.FormReviewContext;

import java.util.*;

/**
 * UserNeedsEvaluator 单元测试类
 * 测试用户需求评估核心算法
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class UserNeedsEvaluatorTest {
    
    public static void main(String[] args) {
        System.out.println("=== UserNeedsEvaluator 单元测试 ===");
        
        try {
            // 测试个人适配性评估
            testPersonalFitnessEvaluation();
            
            // 测试经济适配性评估
            testEconomicFitnessEvaluation();
            
            // 测试地域偏好匹配
            testGeographicPreferenceEvaluation();
            
            // 测试专业偏好匹配
            testMajorPreferenceEvaluation();
            
            // 测试身体条件适配
            testPhysicalRequirementsEvaluation();
            
            // 测试边界条件
            testBoundaryConditions();
            
            System.out.println("=== 所有测试完成 ===");
            
        } catch (Exception e) {
            System.err.println("测试执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试个人适配性评估
     */
    public static void testPersonalFitnessEvaluation() {
        System.out.println("\n--- 测试个人适配性评估 ---");
        
        // 测试完全匹配情况
        FormReviewContext perfectMatchContext = createPersonalFitnessTestContext("男", "汉族", "是");
        String perfectResult = UserNeedsEvaluator.evaluatePersonalFitness(perfectMatchContext);
        assert Arrays.asList("A", "B", "C", "D").contains(perfectResult) : "个人适配性评估结果无效";
        System.out.println("✓ 完全匹配结果: " + perfectResult);
        
        // 测试部分匹配情况
        FormReviewContext partialMatchContext = createPersonalFitnessTestContext("女", "汉族", "否");
        String partialResult = UserNeedsEvaluator.evaluatePersonalFitness(partialMatchContext);
        assert Arrays.asList("A", "B", "C", "D").contains(partialResult) : "个人适配性评估结果无效";
        System.out.println("✓ 部分匹配结果: " + partialResult);
        
        // 测试无数据情况
        FormReviewContext noDataContext = createContextWithoutUserNeeds();
        String noDataResult = UserNeedsEvaluator.evaluatePersonalFitness(noDataContext);
        assert noDataResult.equals("C") : "无数据应返回C级";
        System.out.println("✓ 无数据处理: " + noDataResult);
        
        System.out.println("✓ 个人适配性评估测试通过");
    }
    
    /**
     * 测试经济适配性评估
     */
    public static void testEconomicFitnessEvaluation() {
        System.out.println("\n--- 测试经济适配性评估 ---");
        
        // 测试高预算情况
        FormReviewContext highBudgetContext = createEconomicTestContext("不限制", "是", "是");
        String highBudgetResult = UserNeedsEvaluator.evaluateEconomicFitness(highBudgetContext);
        assert Arrays.asList("A", "B", "C", "D").contains(highBudgetResult) : "经济适配性评估结果无效";
        System.out.println("✓ 高预算结果: " + highBudgetResult);
        
        // 测试低预算情况
        FormReviewContext lowBudgetContext = createEconomicTestContext("5万以下", "否", "否");
        String lowBudgetResult = UserNeedsEvaluator.evaluateEconomicFitness(lowBudgetContext);
        assert Arrays.asList("A", "B", "C", "D").contains(lowBudgetResult) : "经济适配性评估结果无效";
        System.out.println("✓ 低预算结果: " + lowBudgetResult);
        
        // 测试中等预算情况
        FormReviewContext mediumBudgetContext = createEconomicTestContext("5-10万", "是", "否");
        String mediumBudgetResult = UserNeedsEvaluator.evaluateEconomicFitness(mediumBudgetContext);
        assert Arrays.asList("A", "B", "C", "D").contains(mediumBudgetResult) : "经济适配性评估结果无效";
        System.out.println("✓ 中等预算结果: " + mediumBudgetResult);
        
        System.out.println("✓ 经济适配性评估测试通过");
    }
    
    /**
     * 测试地域偏好匹配
     */
    public static void testGeographicPreferenceEvaluation() {
        System.out.println("\n--- 测试地域偏好匹配 ---");
        
        // 测试有明确地域偏好
        FormReviewContext preferenceContext = createGeographicTestContext(
            "[\"成都\",\"重庆\"]", "[\"四川\",\"重庆\"]");
        String preferenceResult = UserNeedsEvaluator.evaluateGeographicPreference(preferenceContext);
        assert Arrays.asList("A", "B", "C", "D").contains(preferenceResult) : "地域偏好评估结果无效";
        System.out.println("✓ 有地域偏好结果: " + preferenceResult);
        
        // 测试无地域偏好
        FormReviewContext noPreferenceContext = createGeographicTestContext("", "");
        String noPreferenceResult = UserNeedsEvaluator.evaluateGeographicPreference(noPreferenceContext);
        assert noPreferenceResult.equals("C") : "无地域偏好应返回C级";
        System.out.println("✓ 无地域偏好处理: " + noPreferenceResult);
        
        // 测试部分匹配
        FormReviewContext partialGeoContext = createGeographicTestContext(
            "[\"北京\",\"上海\"]", "[\"广东\"]");
        String partialGeoResult = UserNeedsEvaluator.evaluateGeographicPreference(partialGeoContext);
        assert Arrays.asList("A", "B", "C", "D").contains(partialGeoResult) : "地域偏好评估结果无效";
        System.out.println("✓ 部分地域匹配结果: " + partialGeoResult);
        
        System.out.println("✓ 地域偏好匹配测试通过");
    }
    
    /**
     * 测试专业偏好匹配
     */
    public static void testMajorPreferenceEvaluation() {
        System.out.println("\n--- 测试专业偏好匹配 ---");
        
        // 测试有专业偏好
        FormReviewContext majorPrefContext = createMajorPreferenceTestContext(
            "[\"计算机科学与技术\",\"软件工程\"]", "工学");
        String majorPrefResult = UserNeedsEvaluator.evaluateMajorPreference(majorPrefContext);
        assert Arrays.asList("A", "B", "C", "D").contains(majorPrefResult) : "专业偏好评估结果无效";
        System.out.println("✓ 有专业偏好结果: " + majorPrefResult);
        
        // 测试无专业偏好
        FormReviewContext noMajorPrefContext = createMajorPreferenceTestContext("", "");
        String noMajorPrefResult = UserNeedsEvaluator.evaluateMajorPreference(noMajorPrefContext);
        assert noMajorPrefResult.equals("C") : "无专业偏好应返回C级";
        System.out.println("✓ 无专业偏好处理: " + noMajorPrefResult);
        
        System.out.println("✓ 专业偏好匹配测试通过");
    }
    
    /**
     * 测试身体条件适配
     */
    public static void testPhysicalRequirementsEvaluation() {
        System.out.println("\n--- 测试身体条件适配 ---");
        
        // 测试身体条件良好
        FormReviewContext goodPhysicalContext = createPhysicalTestContext("合格", "175", "有");
        String goodPhysicalResult = UserNeedsEvaluator.evaluatePhysicalRequirements(goodPhysicalContext);
        assert Arrays.asList("A", "B", "C", "D").contains(goodPhysicalResult) : "身体条件评估结果无效";
        System.out.println("✓ 身体条件良好结果: " + goodPhysicalResult);
        
        // 测试身体条件限制
        FormReviewContext limitedPhysicalContext = createPhysicalTestContext("色弱", "160", "无");
        String limitedPhysicalResult = UserNeedsEvaluator.evaluatePhysicalRequirements(limitedPhysicalContext);
        assert Arrays.asList("A", "B", "C", "D").contains(limitedPhysicalResult) : "身体条件评估结果无效";
        System.out.println("✓ 身体条件限制结果: " + limitedPhysicalResult);
        
        System.out.println("✓ 身体条件适配测试通过");
    }
    
    /**
     * 测试边界条件
     */
    public static void testBoundaryConditions() {
        System.out.println("\n--- 测试边界条件 ---");
        
        // 测试空字符串处理
        FormReviewContext emptyStringContext = createTestContextWithEmptyStrings();
        String emptyStringResult = UserNeedsEvaluator.evaluatePersonalFitness(emptyStringContext);
        assert Arrays.asList("A", "B", "C", "D").contains(emptyStringResult) : "空字符串处理失败";
        System.out.println("✓ 空字符串处理: " + emptyStringResult);
        
        // 测试null值处理
        FormReviewContext nullContext = createTestContextWithNullValues();
        String nullResult = UserNeedsEvaluator.evaluateEconomicFitness(nullContext);
        assert Arrays.asList("A", "B", "C", "D").contains(nullResult) : "null值处理失败";
        System.out.println("✓ null值处理: " + nullResult);
        
        // 测试异常数据格式
        FormReviewContext invalidFormatContext = createTestContextWithInvalidFormat();
        String invalidFormatResult = UserNeedsEvaluator.evaluateGeographicPreference(invalidFormatContext);
        assert Arrays.asList("A", "B", "C", "D").contains(invalidFormatResult) : "异常格式处理失败";
        System.out.println("✓ 异常格式处理: " + invalidFormatResult);
        
        System.out.println("✓ 边界条件测试通过");
    }
    
    // ==================== 辅助方法 ====================
    
    /**
     * 创建个人适配性测试上下文
     */
    private static FormReviewContext createPersonalFitnessTestContext(String gender, String ethnicity, String specialPlans) {
        RtFillNeedsInfoBean needsData = new RtFillNeedsInfoBean();
        needsData.setRt_gender(gender);
        needsData.setRt_ethnicity(ethnicity);
        needsData.setRt_is_eligible_for_special_plans(specialPlans);
        
        return createBasicTestContext(needsData);
    }
    
    /**
     * 创建经济适配性测试上下文
     */
    private static FormReviewContext createEconomicTestContext(String budget, String acceptSinoForeign, String acceptPrivate) {
        RtFillNeedsInfoBean needsData = new RtFillNeedsInfoBean();
        needsData.setRt_university_budget(budget);
        needsData.setRt_accept_sino_foreign(acceptSinoForeign);
        needsData.setRt_accept_private(acceptPrivate);
        
        return createBasicTestContext(needsData);
    }
    
    /**
     * 创建地域偏好测试上下文
     */
    private static FormReviewContext createGeographicTestContext(String preferredCities, String preferredProvinces) {
        RtFillNeedsInfoBean needsData = new RtFillNeedsInfoBean();
        needsData.setRt_preferred_cities(preferredCities);
        needsData.setRt_preferred_provinces(preferredProvinces);
        
        return createBasicTestContext(needsData);
    }
    
    /**
     * 创建专业偏好测试上下文
     */
    private static FormReviewContext createMajorPreferenceTestContext(String preferredMajors, String majorCategory) {
        RtFillNeedsInfoBean needsData = new RtFillNeedsInfoBean();
        needsData.setRt_preferred_majors(preferredMajors);
        needsData.setRt_major_category(majorCategory);
        
        return createBasicTestContext(needsData);
    }
    
    /**
     * 创建身体条件测试上下文
     */
    private static FormReviewContext createPhysicalTestContext(String physicalExam, String height, String drawingSkill) {
        RtFillNeedsInfoBean needsData = new RtFillNeedsInfoBean();
        needsData.setRt_physical_exam(physicalExam);
        needsData.setRt_height(height);
        needsData.setRt_drawing_skill(drawingSkill);
        
        return createBasicTestContext(needsData);
    }
    
    /**
     * 创建基础测试上下文
     */
    private static FormReviewContext createBasicTestContext(RtFillNeedsInfoBean needsData) {
        // 创建基本的SuperFormMain
        SuperFormMain superFormMain = new SuperFormMain();
        superFormMain.setScore_cj(580);
        superFormMain.setPc_code("01");
        
        // 创建省份配置
        ZyzdProvinceConfig provinceConfig = new ZyzdProvinceConfig();
        provinceConfig.setP_name("四川");
        
        // 创建测试志愿列表
        List<SuperForm> superFormList = createTestSuperFormList();
        
        return new FormReviewContext(
            superFormList, superFormMain, needsData, provinceConfig,
            null, null, null, null);
    }
    
    /**
     * 创建无用户需求的上下文
     */
    private static FormReviewContext createContextWithoutUserNeeds() {
        return createBasicTestContext(null);
    }
    
    /**
     * 创建包含空字符串的测试上下文
     */
    private static FormReviewContext createTestContextWithEmptyStrings() {
        RtFillNeedsInfoBean needsData = new RtFillNeedsInfoBean();
        needsData.setRt_gender("");
        needsData.setRt_ethnicity("");
        needsData.setRt_university_budget("");
        
        return createBasicTestContext(needsData);
    }
    
    /**
     * 创建包含null值的测试上下文
     */
    private static FormReviewContext createTestContextWithNullValues() {
        RtFillNeedsInfoBean needsData = new RtFillNeedsInfoBean();
        needsData.setRt_gender(null);
        needsData.setRt_university_budget(null);
        needsData.setRt_preferred_cities(null);
        
        return createBasicTestContext(needsData);
    }
    
    /**
     * 创建包含异常格式的测试上下文
     */
    private static FormReviewContext createTestContextWithInvalidFormat() {
        RtFillNeedsInfoBean needsData = new RtFillNeedsInfoBean();
        needsData.setRt_preferred_cities("无效JSON格式{[}");
        needsData.setRt_preferred_provinces("另一个无效格式");
        
        return createBasicTestContext(needsData);
    }
    
    /**
     * 创建测试用的志愿表单列表
     */
    private static List<SuperForm> createTestSuperFormList() {
        List<SuperForm> list = new ArrayList<>();
        
        String[] universities = {"成都理工大学", "四川大学", "重庆大学", "西南交通大学", "电子科技大学"};
        String[] majors = {"计算机科学与技术", "软件工程", "数据科学与大数据技术", "人工智能", "网络工程"};
        
        for (int i = 0; i < 5; i++) {
            SuperForm form = new SuperForm();
            form.setYxdm("1000" + i);
            form.setYxmc_org(universities[i % universities.length]);
            form.setZyz("0" + (i % 3 + 1));
            form.setZydm("08090" + i);
            form.setZymc(majors[i % majors.length]);
            form.setSeq_no_yx(i);
            list.add(form);
        }
        
        return list;
    }
} 