package com.career.db;

import java.util.Date;

import com.career.utils.BaseBean;

public class LhyOrder extends BaseBean{
	
	private String order_id;
	private String ref_c_id;
	private String lhy_c_id;
	private String lhy_c_id_org;
	private String sf;
	private String pc;
	private String pc_code;
	private String stu_info_name;
	private String stu_info_xb;
	private String stu_info_bj;
	private String stu_info_xx;
	private String stu_info_dh;
	private String stu_info_mz;
	private String stu_info_sl;
	private String stu_info_sg;
	private String stu_info_tz;
	private String stu_score_xk;
	private int stu_score_cj;
	private int stu_score_wc;
	private String ext_father_name;
	private String ext_mother_name;
	private String ext_other_name;
	private String ext_contact;
	private String ext_economics;
	private String ext_remark;
	private int status;
	private Date create_tm;
	private Date next_resp_tm;
	private int id;
	
	private String student_acc_id;
	private String student_acc_pas;
	private String latest_search_sf;
	
	public String getPc_code() {
		return pc_code;
	}


	public String getLatest_search_sf() {
		return latest_search_sf;
	}


	public void setLatest_search_sf(String latest_search_sf) {
		this.latest_search_sf = latest_search_sf;
	}


	public String getLhy_c_id_org() {
		return lhy_c_id_org;
	}


	public void setLhy_c_id_org(String lhy_c_id_org) {
		this.lhy_c_id_org = lhy_c_id_org;
	}


	public void setPc_code(String pc_code) {
		this.pc_code = pc_code;
	}


	public static void main(String args[]) {
		LhyOrder bean = new LhyOrder();
		printBeanProperties(bean);
	}
	
	
	public String getStudent_acc_id() {
		return student_acc_id;
	}


	public void setStudent_acc_id(String student_acc_id) {
		this.student_acc_id = student_acc_id;
	}


	public int getId() {
		return id;
	}


	public void setId(int id) {
		this.id = id;
	}


	public String getStudent_acc_pas() {
		return student_acc_pas;
	}


	public void setStudent_acc_pas(String student_acc_pas) {
		this.student_acc_pas = student_acc_pas;
	}


	public String getPc() {
		return pc;
	}


	public void setPc(String pc) {
		this.pc = pc;
	}


	public String getOrder_id() {
		return order_id;
	}
	public void setOrder_id(String order_id) {
		this.order_id = order_id;
	}
	public String getRef_c_id() {
		return ref_c_id;
	}
	public void setRef_c_id(String ref_c_id) {
		this.ref_c_id = ref_c_id;
	}
	public String getLhy_c_id() {
		return lhy_c_id;
	}
	public void setLhy_c_id(String lhy_c_id) {
		this.lhy_c_id = lhy_c_id;
	}
	public String getSf() {
		return sf;
	}
	public void setSf(String sf) {
		this.sf = sf;
	}
	public String getStu_info_name() {
		return stu_info_name;
	}
	public void setStu_info_name(String stu_info_name) {
		this.stu_info_name = stu_info_name;
	}
	public String getStu_info_xb() {
		return stu_info_xb;
	}
	public void setStu_info_xb(String stu_info_xb) {
		this.stu_info_xb = stu_info_xb;
	}
	public String getStu_info_bj() {
		return stu_info_bj;
	}
	public void setStu_info_bj(String stu_info_bj) {
		this.stu_info_bj = stu_info_bj;
	}
	public String getStu_info_xx() {
		return stu_info_xx;
	}
	public void setStu_info_xx(String stu_info_xx) {
		this.stu_info_xx = stu_info_xx;
	}
	public String getStu_info_dh() {
		return stu_info_dh;
	}
	public void setStu_info_dh(String stu_info_dh) {
		this.stu_info_dh = stu_info_dh;
	}
	public String getStu_info_mz() {
		return stu_info_mz;
	}
	public void setStu_info_mz(String stu_info_mz) {
		this.stu_info_mz = stu_info_mz;
	}
	public String getStu_info_sl() {
		return stu_info_sl;
	}
	public void setStu_info_sl(String stu_info_sl) {
		this.stu_info_sl = stu_info_sl;
	}
	public String getStu_info_sg() {
		return stu_info_sg;
	}
	public void setStu_info_sg(String stu_info_sg) {
		this.stu_info_sg = stu_info_sg;
	}
	public String getStu_info_tz() {
		return stu_info_tz;
	}
	public void setStu_info_tz(String stu_info_tz) {
		this.stu_info_tz = stu_info_tz;
	}
	public String getStu_score_xk() {
		return stu_score_xk;
	}
	public void setStu_score_xk(String stu_score_xk) {
		this.stu_score_xk = stu_score_xk;
	}
	public int getStu_score_cj() {
		return stu_score_cj;
	}
	public void setStu_score_cj(int stu_score_cj) {
		this.stu_score_cj = stu_score_cj;
	}
	public int getStu_score_wc() {
		return stu_score_wc;
	}
	public void setStu_score_wc(int stu_score_wc) {
		this.stu_score_wc = stu_score_wc;
	}
	public String getExt_father_name() {
		return ext_father_name;
	}
	public void setExt_father_name(String ext_father_name) {
		this.ext_father_name = ext_father_name;
	}
	public String getExt_mother_name() {
		return ext_mother_name;
	}
	public void setExt_mother_name(String ext_mother_name) {
		this.ext_mother_name = ext_mother_name;
	}
	public String getExt_other_name() {
		return ext_other_name;
	}
	public void setExt_other_name(String ext_other_name) {
		this.ext_other_name = ext_other_name;
	}
	public String getExt_contact() {
		return ext_contact;
	}
	public void setExt_contact(String ext_contact) {
		this.ext_contact = ext_contact;
	}
	public String getExt_economics() {
		return ext_economics;
	}
	public void setExt_economics(String ext_economics) {
		this.ext_economics = ext_economics;
	}
	public String getExt_remark() {
		return ext_remark;
	}
	public void setExt_remark(String ext_remark) {
		this.ext_remark = ext_remark;
	}
	public int getStatus() {
		return status;
	}
	public void setStatus(int status) {
		this.status = status;
	}
	public Date getCreate_tm() {
		return create_tm;
	}
	public void setCreate_tm(Date create_tm) {
		this.create_tm = create_tm;
	}
	public Date getNext_resp_tm() {
		return next_resp_tm;
	}
	public void setNext_resp_tm(Date next_resp_tm) {
		this.next_resp_tm = next_resp_tm;
	}

}
