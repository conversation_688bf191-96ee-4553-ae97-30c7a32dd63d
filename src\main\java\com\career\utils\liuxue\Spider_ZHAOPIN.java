package com.career.utils.liuxue;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.career.utils.HttpSendUtils;
import com.career.utils.zsky.ResultInfoBean;

public class Spider_ZHAOPIN {
	
	// 重庆人事考试网抓取网页
		public static StringBuffer YOUZHIYUAN() {
			Map<String, String> headers = new HashMap<>();
			Map<String, String> params = new HashMap<>();

			
			headers.put(":authority", "cgate.zhaopin.com");
			headers.put(":method","POST");
			headers.put(":path","/positionbusiness/searchrecommend/searchPositions?x-zp-page-request-id=716a98e004df45aa95614a2a30d34e14-1730525425444-973507&x-zp-client-id=6faea73c-6ec3-4a38-8a39-14ac799466fb");
			headers.put(":scheme","https");
			headers.put("accept","application/json, text/plain, */*");
			headers.put("accept-encoding","gzip, deflate, br, zstd");
			headers.put("accept-language","zh-CN,zh;q=0.9");
			headers.put("cache-control","no-cache");

			headers.put("content-type","application/json");
			headers.put("cookie","x-zp-client-id=6faea73c-6ec3-4a38-8a39-14ac799466fb; sensorsdata2015jssdkchannel=%7B%22prop%22%3A%7B%22_sa_channel_landing_url%22%3A%22%22%7D%7D; Hm_lvt_7fa4effa4233f03d11c7e2c710749600=**********; HMACCOUNT=F9E89F99DE4FFF7E; login-type=b; x-zp-device-id=3c91991460bc13a3f30031e0c946c9fa; zp_passport_deepknow_sessionId=25729ac9s45eaf4954a0181f39c63aa365f1; at=78e4506ea2c94e2596c2378b3ab237af; rt=5dd69dd225b543f3bb3f707c110330fe; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%************%22%2C%22first_id%22%3A%22191abd7f881e43-012ad79dac6c28c-********-5308416-191abd7f88217df%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTkxYWJkN2Y4ODFlNDMtMDEyYWQ3OWRhYzZjMjhjLTI2MDAxMTUxLTUzMDg0MTYtMTkxYWJkN2Y4ODIxN2RmIiwiJGlkZW50aXR5X2xvZ2luX2lkIjoiMTEzNTM2MjAxNyJ9%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%************%22%7D%2C%22%24device_id%22%3A%22191abd7f881e43-012ad79dac6c28c-********-5308416-191abd7f88217df%22%7D; rd-privacy-policy-checked=true; LastCity=%E6%88%90%E9%83%BD; LastCity%5Fid=801; acw_tc=ac11000117305245849106279e00ae18fbab902d0da2bb792b8870b02e2bd9; selectCity_search=801; Hm_lpvt_7fa4effa4233f03d11c7e2c710749600=1730524863; ZL_REPORT_GLOBAL={%22//www%22:{%22seid%22:%2278e4506ea2c94e2596c2378b3ab237af%22%2C%22actionid%22:%2210600eae-9b6c-4515-9381-3f8bd835f27b-cityPage%22}}; locationInfo_search={%22code%22:%22801%22%2C%22name%22:%22%E6%88%90%E9%83%BD%22}");
			headers.put("origin","https://xiaoyuan.zhaopin.com");
			headers.put("pragma","no-cache");
			headers.put("priority","u=1, i");
			headers.put("referer","https://xiaoyuan.zhaopin.com/");
			headers.put("sec-ch-ua","\"Chromium\";v=\"130\", \"Google Chrome\";v=\"130\", \"Not?A_Brand\";v=\"99\"");
			headers.put("sec-ch-ua-mobile","?0");
			headers.put("sec-ch-ua-platform","\"Windows\"");
			headers.put("sec-fetch-dest","empty");
			headers.put("sec-fetch-mode","cors");
			headers.put("sec-fetch-site","same-site");
			headers.put("user-agent","Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36");
			headers.put("x-zp-at","78e4506ea2c94e2596c2378b3ab237af");
			headers.put("x-zp-business-system","40");
			headers.put("x-zp-platform","14");
			headers.put("x-zp-rt","5dd69dd225b543f3bb3f707c110330fe");
			
			
			StringBuffer SQL = new StringBuffer();


			String resultPageList = HttpSendUtils.get("https://cgate.zhaopin.com/positionbusiness/searchrecommend/searchPositions?x-zp-page-request-id=716a98e004df45aa95614a2a30d34e14-1730525425444-973507&x-zp-client-id=6faea73c-6ec3-4a38-8a39-14ac799466fb", headers);
			System.out.println(resultPageList);
			
			
			return SQL;
		}
		
		
		static String u(String tt) {
			String en_key = "eFPIKDkOCHio8sVfprqdxt0jEw9gMb";
			String result = tt.toLowerCase() + '&' + en_key.toLowerCase();
			return result;
		}
		
		static String get_u_sign(String data) {
			String sign_data = u(data);
			String encodedString = Base64.getEncoder().encodeToString(sign_data.getBytes());
		    return calculateMD5(encodedString);
		}
		
		private static String calculateMD5(String originalString) {
	        try {
	            // 创建MD5加密实例
	            MessageDigest md = MessageDigest.getInstance("MD5");
	 
	            // 执行加密操作
	            byte[] messageDigest = md.digest(originalString.getBytes());
	 
	            // 将得到的散列值转换为十六进制
	            StringBuilder sb = new StringBuilder();
	            for (byte b : messageDigest) {
	                sb.append(String.format("%02x", b));
	            }
	 
	            // 返回MD5散列值
	            return sb.toString();
	        } catch (NoSuchAlgorithmException e) {
	            throw new RuntimeException("MD5加密算法不可用", e);
	        }
	    }

		

		public static void main(String[] args) {
			try {
				StringBuffer SQL = new StringBuffer();
				File fileX = new File("F:\\就业报告\\zhaopinCOM\\");
				File[] filesX = fileX.listFiles();
				for(int i=0;i<filesX.length;i++) {
					String folderName = filesX[i].getName();
					String[] folderSplit = folderName.split("-");
					String catg = folderSplit[0];
					String majorName = folderSplit[1];
					
					File file2 = new File("F:\\就业报告\\zhaopinCOM\\" + folderName);
					File[] files2 = file2.listFiles();
					for(int j=0;j<files2.length;j++) {
						File info = files2[j];
						
						BufferedReader br = new BufferedReader(new FileReader(info));
						StringBuffer sb = new StringBuffer();
						String line = null;
						while((line = br.readLine()) != null) {
							sb.append(line);
						}
						
						JSONObject object = JSONObject.parseObject(sb.toString());
						JSONObject JSONObject = object.getJSONObject("data");
						JSONArray JSONArray = JSONObject.getJSONArray("list");
						
						for(int ki=0;ki<JSONArray.size();ki++) {
							JSONObject bean = JSONArray.getJSONObject(ki);

							String jobtitle = bean.getString("name");
							String salary60 = bean.getString("salary60");
							String salaryCount = bean.getString("salaryCount");
							String property = bean.getString("property"); //"property": "国企"
							String workCity = bean.getString("workCity");
							String workType = bean.getString("workType");
							String number = bean.getString("number");
							String jobId = bean.getString("jobId");
							String industryName = bean.getString("industryName");
							String firstPublishTime = bean.getString("firstPublishTime");
							String education = bean.getString("education");
							String jobSummary = bean.getString("jobSummary");
							String jobSkillTags = bean.getString("jobSkillTags");
							String companyName = bean.getString("companyName");
							
							System.out.println(catg + "," + majorName + " | " +companyName + " -> " + jobtitle + "," + salary60 + "," + education + "," +firstPublishTime);
							
							SQL.append("INSERT INTO `zyzd2023`.`career_jy_job_req` (`job_title`, `company`, `work_city`, `req_major_name`, `req_major_catg`, `req_education`, `salary`, `salary_count`, `first_publish_time`, `industry_name`, `property`, `job_summary`, `display_ind`) "
									+ "VALUES ('"+jobtitle+"', '"+companyName+"', '"+workCity+"', '"+majorName+"', '"+catg+"', '"+education+"', '"+salary60+"', '"+salaryCount+"', '"+firstPublishTime+"', '"+industryName+"', '"+property+"', '"+jobSummary+"', 1);\r\n");
							
						}
					}
				}

				writeTempFile(new File("F:\\就业报告\\jiuye\\RESULT33.txt"), SQL);
				
				//YOUZHIYUAN();
				
			}catch(Exception ex) {
				ex.printStackTrace();
			}
		}
		
		private static void writeTempFile(File file, StringBuffer sb) {
			try {
				BufferedWriter bw = new BufferedWriter(new FileWriter(file));
				bw.write(sb.toString());
				bw.flush();
				bw.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}

}
