<%@page import="com.zsdwf.servlet.RunOnceThread"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page import="com.career.utils.*,com.career.db.*,java.util.*" %>
<%@include file="/WEB-INF/include/_session_admin.jsp" %>
<%
String cid = Tools.trim(request.getParameter("cid"));
String sf = Tools.trim(request.getParameter("sf"));
String city = Tools.trim(request.getParameter("city"));
String zdpc = Tools.trim(request.getParameter("zdpc"));
String xk = Tools.trim(request.getParameter("xk"));
int score = Tools.getInt(request.getParameter("score"));
JDBC jdbc = new JDBC();
jdbc.updateAutoPlayUserInput(cid, sf, city, zdpc, xk, score);
%>
