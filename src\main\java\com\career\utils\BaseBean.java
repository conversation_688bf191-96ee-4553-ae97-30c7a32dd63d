package com.career.utils;

import java.lang.reflect.Field;

public class BaseBean {

	public static void printBeanProperties(Object bean) {
        Class<?> clazz = bean.getClass();
        Field[] fields = clazz.getDeclaredFields();
 
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                //System.out.println(field.getName() + ": " + field.get(bean));
            	String type = field.getGenericType().toString();
                String propName = field.getName();
                String prefix = propName.substring(0,1);
                prefix = prefix.toUpperCase();
                String subfix = propName.substring(1);
                if(propName.startsWith("ext_")) {
                	continue;
                }
                if(type.equals("int")) {
                	System.out.println("bean.set"+prefix+subfix+"(rs.getInt(\"" + propName + "\"));");
                }else if(type.equals("float")) {
                	System.out.println("bean.set"+prefix+subfix+"(rs.getFloat(\"" + propName + "\"));");
                }else if(type.equals("class java.lang.String")) {
                	System.out.println("bean.set"+prefix+subfix+"(rs.getString(\"" + propName + "\"));");
                }else if(type.equals("class java.util.Date")) {
                	System.out.println("bean.set"+prefix+subfix+"(rs.getTimestamp(\"" + propName + "\"));");
                }else {
                	System.out.println(propName + "-->N.A - "+type);
                }
                
                
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
	
}
