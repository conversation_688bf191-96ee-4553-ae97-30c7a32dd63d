package com.career.db;

import java.util.Date;

/**
 * 审核表 rt_checker_apply_form_maker_main_checker 对应的Bean
 */
public class RtCheckerApplyFormMakerMainChecker {
    /**
     * 主键ID
     */
    private Integer mcId;

    /**
     * 审核结果：1-通过审核，2-审核驳回
     */
    private Integer mcResult = 0;

    /**
     * form_maker_main的ID
     */
    private String fmId;

    /**
     * 审核用户ID（LHY_CARD的ID）
     */
    private String cId;

    /**
     * 审核结果描述
     */
    private String remark;

    /**
     * 提审时候的描述
     */
    private String toCheckerRemark;

    /**
     * 审核提交时间
     */
    private Date createTm;

    // Getter and Setter methods

    public Integer getMcId() {
        return mcId;
    }

    public void setMcId(Integer mcId) {
        this.mcId = mcId;
    }

    public Integer getMcResult() {
        return mcResult;
    }

    public void setMcResult(Integer mcResult) {
        this.mcResult = mcResult;
    }

    public String getFmId() {
        return fmId;
    }

    public void setFmId(String fmId) {
        this.fmId = fmId;
    }

    public String getCId() {
        return cId;
    }

    public void setCId(String cId) {
        this.cId = cId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getToCheckerRemark() {
        return toCheckerRemark;
    }

    public void setToCheckerRemark(String toCheckerRemark) {
        this.toCheckerRemark = toCheckerRemark;
    }

    public Date getCreateTm() {
        return createTm;
    }

    public void setCreateTm(Date createTm) {
        this.createTm = createTm;
    }
}
