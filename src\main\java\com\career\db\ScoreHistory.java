package com.career.db;

import java.util.Date;

import com.career.utils.BaseBean;

public class ScoreHistory extends BaseBean{
	
	private String c_id;
	private String score_from;
	private String xk;
	private Date create_tm;
	
	private int id;
	private int s_sum;
	private int s_yw;
	private int s_sx;
	private int s_yy;
	private int s_xk_a;
	private int s_xk_b;
	private int s_xk_c;
	private int status;
	
	private String ext_nickname;
	private Date ext_last_login;
	private String ext_class_name;
	
	public static void main(String args[]) {
		ScoreHistory bean = new ScoreHistory();
		printBeanProperties(bean);
	}

	public String getExt_nickname() {
		return ext_nickname;
	}

	public void setExt_nickname(String ext_nickname) {
		this.ext_nickname = ext_nickname;
	}

	public Date getExt_last_login() {
		return ext_last_login;
	}

	public void setExt_last_login(Date ext_last_login) {
		this.ext_last_login = ext_last_login;
	}

	public String getExt_class_name() {
		return ext_class_name;
	}

	public void setExt_class_name(String ext_class_name) {
		this.ext_class_name = ext_class_name;
	}

	public String getC_id() {
		return c_id;
	}

	public void setC_id(String c_id) {
		this.c_id = c_id;
	}

	public String getScore_from() {
		return score_from;
	}

	public void setScore_from(String score_from) {
		this.score_from = score_from;
	}

	public String getXk() {
		return xk;
	}

	public void setXk(String xk) {
		this.xk = xk;
	}

	public Date getCreate_tm() {
		return create_tm;
	}

	public void setCreate_tm(Date create_tm) {
		this.create_tm = create_tm;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getS_sum() {
		return s_sum;
	}

	public void setS_sum(int s_sum) {
		this.s_sum = s_sum;
	}

	public int getS_yw() {
		return s_yw;
	}

	public void setS_yw(int s_yw) {
		this.s_yw = s_yw;
	}

	public int getS_sx() {
		return s_sx;
	}

	public void setS_sx(int s_sx) {
		this.s_sx = s_sx;
	}

	public int getS_yy() {
		return s_yy;
	}

	public void setS_yy(int s_yy) {
		this.s_yy = s_yy;
	}

	public int getS_xk_a() {
		return s_xk_a;
	}

	public void setS_xk_a(int s_xk_a) {
		this.s_xk_a = s_xk_a;
	}

	public int getS_xk_b() {
		return s_xk_b;
	}

	public void setS_xk_b(int s_xk_b) {
		this.s_xk_b = s_xk_b;
	}

	public int getS_xk_c() {
		return s_xk_c;
	}

	public void setS_xk_c(int s_xk_c) {
		this.s_xk_c = s_xk_c;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}
	

}
