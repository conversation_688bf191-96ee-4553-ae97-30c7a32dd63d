package com.career.utils.wecom.debug;

import com.alibaba.fastjson2.JSONObject;
import com.career.utils.Tools;
import com.career.utils.wecom.WeComHttpClient;
import com.career.utils.wecom.WeComTokenManager;
import java.util.List;

/**
 * 企业微信API调试工具
 * 用于排查API权限和配置问题
 */
public class WeComApiDebugTool {
    
    /**
     * 测试通讯录同步权限和基础API
     * 
     * @param corpId 企业ID
     * @param corpSecret 通讯录同步应用的Secret
     */
    public static void debugContactsApiPermissions(String corpId, String corpSecret) {
        Tools.println("=== 企业微信通讯录同步API权限检查 ===");
        Tools.println("企业ID: " + corpId);
        Tools.println("Secret: " + maskSecret(corpSecret));
        Tools.println("");
        
        try {
            // 1. 测试获取access_token
            Tools.println("1. 测试获取 access_token...");
            String accessToken = WeComTokenManager.getAccessToken(corpId, corpSecret);
            Tools.println("✅ access_token 获取成功: " + maskToken(accessToken));
            Tools.println("");
            
            // 2. 测试传统的部门列表接口
            Tools.println("2. 测试部门列表接口权限...");
            testDepartmentListApi(accessToken);
            Tools.println("");
            
            // 3. 测试传统的用户列表接口
            Tools.println("3. 测试传统用户列表接口权限...");
            testUserListApi(accessToken);
            Tools.println("");
            
            // 4. 测试单个用户详情接口
            Tools.println("4. 测试用户详情接口权限...");
            testUserDetailApi(accessToken);
            Tools.println("");
            
            // 5. 测试外部联系人列表接口
            Tools.println("5. 测试外部联系人列表接口权限...");
            testExternalContactListApi(accessToken);
            Tools.println("");
            
            // 6. 测试外部联系人详情接口
            Tools.println("6. 测试外部联系人详情接口权限...");
            testExternalContactDetailApi(accessToken);
            Tools.println("");
            
            // 7. 测试客户群列表接口
            Tools.println("7. 测试客户群列表接口权限...");
            testGroupChatListApi(accessToken);
            Tools.println("");
            
            // // 8. 测试应用消息发送接口
            // Tools.println("8. 测试应用消息发送接口权限...");
            // testAppMessageApi(accessToken);
            // Tools.println("");
            
            // // 9. 测试应用信息获取
            // Tools.println("9. 测试应用信息获取权限...");
            // testAppInfoApi(accessToken);
            // Tools.println("");
            
        } catch (Exception e) {
            Tools.println("❌ 调试过程出现异常: " + e.getMessage());
            e.printStackTrace();
        }
        
        Tools.println("=== 权限检查完成 ===");
        Tools.println("");
        Tools.println("如果看到API接口错误，请检查以下几点：");
        Tools.println("1. 确认使用的是【通讯录同步】应用的Secret，不是其他应用的Secret");
        Tools.println("2. 检查企业微信管理后台的IP白名单设置");
        Tools.println("3. 确认通讯录同步应用已启用且权限设置正确");
        Tools.println("4. 部分接口可能需要特定的应用配置，请查阅相应的企业微信文档");
    }
    
    /**
     * 测试部门列表接口
     */
    private static void testDepartmentListApi(String accessToken) {
        try {
            String url = "https://qyapi.weixin.qq.com/cgi-bin/department/list?access_token=" + accessToken;
            JSONObject response = WeComHttpClient.doGetWithRetry(url);
            
            int errcode = response.getIntValue("errcode");
            if (errcode == 0) {
                Tools.println("✅ 部门列表接口权限正常");
            } else {
                Tools.println("❌ 部门列表接口权限异常: " + errcode + " - " + response.getString("errmsg"));
            }
        } catch (Exception e) {
            Tools.println("❌ 部门列表接口测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试传统用户列表接口
     */
    private static void testUserListApi(String accessToken) {
        try {
            String url = "https://qyapi.weixin.qq.com/cgi-bin/user/list?access_token=" + accessToken + "&department_id=1";
            JSONObject response = WeComHttpClient.doGetWithRetry(url);
            
            int errcode = response.getIntValue("errcode");
            if (errcode == 0) {
                Tools.println("✅ 传统用户列表接口权限正常");
            } else {
                Tools.println("❌ 传统用户列表接口权限异常: " + errcode + " - " + response.getString("errmsg"));
            }
        } catch (Exception e) {
            Tools.println("❌ 传统用户列表接口测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试用户详情接口
     */
    private static void testUserDetailApi(String accessToken) {
        try {
            // 先获取部门下的用户列表
            String listUrl = "https://qyapi.weixin.qq.com/cgi-bin/user/list?access_token=" + accessToken + "&department_id=1";
            JSONObject listResponse = WeComHttpClient.doGetWithRetry(listUrl);
            
            if (listResponse.getIntValue("errcode") == 0) {
                if (listResponse.getJSONArray("userlist") != null && !listResponse.getJSONArray("userlist").isEmpty()) {
                    String userId = listResponse.getJSONArray("userlist").getJSONObject(0).getString("userid");
                    
                    String url = "https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token=" + accessToken + "&userid=" + userId;
                    JSONObject response = WeComHttpClient.doGetWithRetry(url);
                    
                    int errcode = response.getIntValue("errcode");
                    if (errcode == 0) {
                        Tools.println("✅ 用户详情接口权限正常 (测试用户ID: " + userId + ")");
                    } else {
                        Tools.println("❌ 用户详情接口权限异常: " + errcode + " - " + response.getString("errmsg"));
                    }
                } else {
                    Tools.println("❓ 未找到用户，跳过用户详情测试");
                }
            } else {
                Tools.println("❌ 无法获取用户列表，跳过用户详情测试");
            }
        } catch (Exception e) {
            Tools.println("❌ 用户详情接口测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试外部联系人列表接口
     */
    private static void testExternalContactListApi(String accessToken) {
        try {
            // 先获取部门下的用户列表，用于测试外部联系人
            String listUrl = "https://qyapi.weixin.qq.com/cgi-bin/user/list?access_token=" + accessToken + "&department_id=1";
            JSONObject listResponse = WeComHttpClient.doGetWithRetry(listUrl);
            
            if (listResponse.getIntValue("errcode") == 0) {
                if (listResponse.getJSONArray("userlist") != null && !listResponse.getJSONArray("userlist").isEmpty()) {
                    String userId = listResponse.getJSONArray("userlist").getJSONObject(0).getString("userid");
                    
                    String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/list?access_token=" + accessToken + "&userid=" + userId;
                    JSONObject response = WeComHttpClient.doGetWithRetry(url);
                    
                    int errcode = response.getIntValue("errcode");
                    if (errcode == 0) {
                        int contactCount = response.getJSONArray("external_userid") != null ? 
                                         response.getJSONArray("external_userid").size() : 0;
                        Tools.println("✅ 外部联系人列表接口权限正常 (测试用户ID: " + userId + ")");
                        if (contactCount > 0) {
                            Tools.println("   该用户有 " + contactCount + " 个外部联系人");
                        } else {
                            Tools.println("   该用户没有外部联系人");
                        }
                    } else if (errcode == 84061) {
                        // 84061: 不存在外部联系人
                        Tools.println("✅ 外部联系人列表接口权限正常，但该用户(" + userId + ")没有客户");
                    } else if (errcode == 60011 || errcode == 60102 || errcode == 60111) {
                        // 60011: API功能未授权
                        // 60102: 未开启客户联系功能
                        // 60111: 无权限使用该接口
                        Tools.println("❌ 外部联系人列表接口功能未授权: " + errcode + " - " + response.getString("errmsg"));
                        Tools.println("   解决方法: 请在企业微信管理后台开启「客户联系」功能，并授权应用");
                    } else if (errcode == 41001 || errcode == 48002) {
                        // 41001: 缺少access_token参数
                        // 48002: API接口无权限调用
                        Tools.println("❌ 外部联系人列表接口权限异常: " + errcode + " - " + response.getString("errmsg"));
                        Tools.println("   可能原因: 使用了错误的应用Secret或未授权访问通讯录");
                    } else {
                        Tools.println("❌ 外部联系人列表接口权限异常: " + errcode + " - " + response.getString("errmsg"));
                    }
                } else {
                    Tools.println("❓ 未找到用户，跳过外部联系人列表测试");
                }
            } else {
                Tools.println("❌ 无法获取用户列表，跳过外部联系人列表测试");
                Tools.println("   错误信息: " + listResponse.getIntValue("errcode") + " - " + listResponse.getString("errmsg"));
            }
        } catch (Exception e) {
            Tools.println("❌ 外部联系人列表接口测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试外部联系人详情接口
     */
    private static void testExternalContactDetailApi(String accessToken) {
        try {
            // 由于需要先获取外部联系人ID，这里做简化测试
            String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get?access_token=" + accessToken + "&external_userid=not_exist_just_test";
            JSONObject response = WeComHttpClient.doGetWithRetry(url);
            
            int errcode = response.getIntValue("errcode");
            if (errcode == 0) {
                Tools.println("✅ 外部联系人详情接口权限正常");
            } else if (errcode == 84061 || errcode == 40096 || errcode == 40050) {
                // 84061: 不存在外部联系人
                // 40096: 不合法的外部联系人
                // 40050: 不合法的外部联系人
                Tools.println("✅ 外部联系人详情接口权限正常 (错误码: " + errcode + " - " + response.getString("errmsg") + ")");
                Tools.println("   说明: 这是一个预期的错误，因为我们使用了不存在的测试ID。该错误表明接口本身是可访问的。");
            } else if (errcode == 48002 || errcode == 41001) {
                Tools.println("❌ 外部联系人详情接口权限异常: " + errcode + " - " + response.getString("errmsg"));
            } else {
                Tools.println("⚠️ 外部联系人详情接口返回: " + errcode + " - " + response.getString("errmsg"));
            }
        } catch (Exception e) {
            Tools.println("❌ 外部联系人详情接口测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试客户群列表接口
     */
    private static void testGroupChatListApi(String accessToken) {
        try {
            String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/list?access_token=" + accessToken;
            JSONObject params = new JSONObject();
            params.put("status_filter", 0);
            params.put("limit", 10);
            
            JSONObject response = WeComHttpClient.doPostWithRetry(url, params.toJSONString());
            
            int errcode = response.getIntValue("errcode");
            if (errcode == 0) {
                Tools.println("✅ 客户群列表接口权限正常");
            } else if (errcode == 42001 || errcode == 40014) {
                // 42001: access_token过期
                // 40014: 不合法的access_token
                Tools.println("❌ 客户群列表接口访问失败: " + errcode + " - " + response.getString("errmsg"));
                Tools.println("   原因: access_token可能已过期或无效，请重新获取");
            } else if (errcode == 48002 || errcode == 41001) {
                // 48002: API接口无权限调用
                // 41001: 缺少access_token参数
                Tools.println("❌ 客户群列表接口权限异常: " + errcode + " - " + response.getString("errmsg"));
                Tools.println("   可能原因: 应用缺少权限或Secret错误");
            } else if (errcode == 60011 || errcode == 60020) {
                // 60011: API功能未授权
                // 60020: 客户联系相关接口未开启
                Tools.println("❌ 客户群列表接口功能未授权: " + errcode + " - " + response.getString("errmsg"));
                Tools.println("   解决方法: 请在企业微信管理后台开启「客户联系」功能，并授权应用");
            } else {
                Tools.println("⚠️ 客户群列表接口返回: " + errcode + " - " + response.getString("errmsg"));
            }
        } catch (Exception e) {
            Tools.println("❌ 客户群列表接口测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试应用消息发送接口权限
     */
    private static void testAppMessageApi(String accessToken) {
        try {
            // 尝试先获取可用的应用ID
            String agentUrl = "https://qyapi.weixin.qq.com/cgi-bin/agent/list?access_token=" + accessToken;
            JSONObject agentListResponse = WeComHttpClient.doGetWithRetry(agentUrl);
            
            int agentId = 0;
            if (agentListResponse.getIntValue("errcode") == 0 && 
                agentListResponse.getJSONArray("agentlist") != null && 
                !agentListResponse.getJSONArray("agentlist").isEmpty()) {
                // 获取第一个应用的ID
                agentId = agentListResponse.getJSONArray("agentlist").getJSONObject(0).getIntValue("agentid");
                Tools.println("找到可用的应用ID: " + agentId + "，用于测试消息发送接口");
            } else {
                Tools.println("无法获取可用的应用列表，将使用测试ID进行消息发送测试");
                // 使用一个常见的测试ID
                agentId = 1000002;
            }
            
            String url = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=" + accessToken;
            
            // 构造一个仅用于测试权限的消息，不实际发送
            JSONObject params = new JSONObject();
            params.put("touser", "@all");
            params.put("msgtype", "text");
            params.put("agentid", agentId);
            
            JSONObject textContent = new JSONObject();
            textContent.put("content", "API权限测试，不会真正发送");
            params.put("text", textContent);
            params.put("safe", 0);
            params.put("enable_duplicate_check", 1);
            params.put("duplicate_check_interval", 1800);
            
            // 添加一个标记，确保消息不会真正发送
            params.put("enable_id_trans", 2); // 使用不合法的值来触发验证错误
            
            JSONObject response = WeComHttpClient.doPostWithRetry(url, params.toJSONString());
            
            int errcode = response.getIntValue("errcode");
            if (errcode == 0) {
                Tools.println("✅ 应用消息发送接口权限正常");
            } else if (errcode == 40056 || errcode == 40058 || errcode == 41002) {
                // 40056: 不合法的agentid
                // 40058: 不合法的参数
                // 41002: 缺少参数
                Tools.println("✅ 应用消息发送接口权限正常 (验证错误: " + errcode + " - " + response.getString("errmsg") + ")");
                Tools.println("   说明: 这是一个预期的错误，表明接口本身是可访问的");
            } else if (errcode == 48002 || errcode == 41001) {
                // 48002: API接口无权限调用
                // 41001: 缺少access_token参数
                Tools.println("❌ 应用消息发送接口权限异常: " + errcode + " - " + response.getString("errmsg"));
                Tools.println("   可能原因: 应用缺少权限或Secret错误");
            } else if (errcode == 42001 || errcode == 40014) {
                // 42001: access_token过期
                // 40014: 不合法的access_token
                Tools.println("❌ 应用消息发送接口访问失败: " + errcode + " - " + response.getString("errmsg"));
                Tools.println("   原因: access_token可能已过期或无效，请重新获取");
            } else {
                Tools.println("⚠️ 应用消息发送接口返回: " + errcode + " - " + response.getString("errmsg"));
            }
        } catch (Exception e) {
            Tools.println("❌ 应用消息发送接口测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试应用信息获取
     */
    private static void testAppInfoApi(String accessToken) {
        try {
            // 注意：这个接口需要应用的agentid，这里只是测试权限
            String url = "https://qyapi.weixin.qq.com/cgi-bin/agent/get?access_token=" + accessToken + "&agentid=1000002";
            JSONObject response = WeComHttpClient.doGetWithRetry(url);
            
            int errcode = response.getIntValue("errcode");
            if (errcode == 0) {
                Tools.println("✅ 应用信息获取接口权限正常");
            } else if (errcode == 40013 || errcode == 40056) {
                // 40013: 不合法的corpid或secret，40056: 不合法的agentid
                Tools.println("⚠️ 应用信息获取接口提示: " + response.getString("errmsg") + " - 接口权限正常但需要有效的应用ID");
            } else if (errcode == 48002 || errcode == 41001) {
                Tools.println("❌ 应用信息获取接口权限异常: " + errcode + " - " + response.getString("errmsg"));
            } else {
                Tools.println("⚠️ 应用信息获取接口返回: " + errcode + " - " + response.getString("errmsg"));
            }
        } catch (Exception e) {
            Tools.println("❌ 应用信息获取接口测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 掩码显示Secret
     */
    private static String maskSecret(String secret) {
        if (secret == null || secret.length() < 6) {
            return "***";
        }
        return secret.substring(0, 3) + "***" + secret.substring(secret.length() - 3);
    }
    
    /**
     * 掩码显示Token
     */
    private static String maskToken(String token) {
        if (token == null || token.length() < 10) {
            return "***";
        }
        return token.substring(0, 6) + "***" + token.substring(token.length() - 6);
    }
    
    /**
     * 测试企业微信API限流情况
     */
    private static void testApiRateLimits(String accessToken) {
        Tools.println("\n=== 企业微信API限流情况检测 ===");
        
        try {
            // 获取API调用频率限制信息
            String url = "https://qyapi.weixin.qq.com/cgi-bin/getcallbackip?access_token=" + accessToken;
            JSONObject response = WeComHttpClient.doGetWithRetry(url);
            
            int errcode = response.getIntValue("errcode");
            if (errcode == 0) {
                Tools.println("✅ API访问正常，当前QPS限制说明:");
                Tools.println("   - 企业微信开放API一般QPS=10，部分接口为5");
                Tools.println("   - 通讯录同步API每分钟1200次（即QPS=20）");
                Tools.println("   - 会话内容存档API每分钟1200次（即QPS=20）");
                Tools.println("   - 外部联系人管理API每分钟1200次（即QPS=20）");
                
                // 检查最近的响应头中是否有限流信息
                Tools.println("\n当前API使用情况:");
                Tools.println("   - API接口被限流时会返回 errcode: 45009");
                Tools.println("   - 请求返回头中的 X-RateLimit-Limit 和 X-RateLimit-Remaining 字段记录剩余可用量");
                Tools.println("   - 建议使用 WeComConfig.getApiQpsLimit() 配置合理的QPS值（默认为10）");
                
                // 检查系统当前QPS设置
                int currentQpsSetting = com.career.utils.wecom.config.WeComConfig.getApiQpsLimit();
                Tools.println("\n当前QPS配置: " + currentQpsSetting);
                if (currentQpsSetting <= 0) {
                    Tools.println("⚠️ 当前QPS设置小于等于0，这可能导致API调用无限速!");
                    Tools.println("   建议设置为1-10之间的合理值");
                } else if (currentQpsSetting > 20) {
                    Tools.println("⚠️ 当前QPS设置过高 (" + currentQpsSetting + ")，可能超过企业微信API限制!");
                    Tools.println("   建议设置为10以下，除非确认有特殊配置");
                } else if (currentQpsSetting >= 10) {
                    Tools.println("✓ 当前QPS设置在有效范围内，但接近限制值");
                    Tools.println("   如遇到限流问题，可考虑调低至8");
                } else {
                    Tools.println("✓ 当前QPS设置在安全范围内");
                }
            } else if (errcode == 48002 || errcode == 41001) {
                Tools.println("❌ API限流检测失败: " + errcode + " - " + response.getString("errmsg"));
            } else {
                Tools.println("⚠️ API限流检测返回: " + errcode + " - " + response.getString("errmsg"));
            }
        } catch (Exception e) {
            Tools.println("❌ API限流检测失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成配置检查报告
     */
    public static void generateConfigReport(String corpId, String corpSecret) {
        Tools.println("=== 企业微信配置检查报告 ===");
        Tools.println("时间: " + new java.util.Date());
        Tools.println("企业ID: " + corpId);
        Tools.println("Secret: " + maskSecret(corpSecret));
        Tools.println("");
        
        Tools.println("请核实以下配置：");
        Tools.println("1. 企业微信管理后台 > 应用管理 > 通讯录同步");
        Tools.println("2. 确认Secret是通讯录同步应用的Secret");
        Tools.println("3. 检查可信域名和IP白名单设置");
        Tools.println("4. 确认通讯录同步权限为【编辑】模式");
        Tools.println("");
        
        // 尝试获取服务器IP地址
        checkServerIpWhitelist();
        
        // 运行API权限测试
        debugContactsApiPermissions(corpId, corpSecret);
        
        // 获取一个token用于测试API限流
        try {
            String accessToken = WeComTokenManager.getAccessToken(corpId, corpSecret);
            // 测试API限流情况
            testApiRateLimits(accessToken);
        } catch (Exception e) {
            Tools.println("\n❌ 无法获取access_token进行API限流测试: " + e.getMessage());
        }
    }
    
    /**
     * 检测服务器IP是否已添加到企业微信IP白名单
     */
    public static void checkServerIpWhitelist() {
        try {
            // 尝试获取公网IP地址
            String url = "https://api.ipify.org?format=json";
            JSONObject response = WeComHttpClient.doGetWithRetry(url);
            String ip = response.getString("ip");
            
            if (ip != null && !ip.isEmpty()) {
                Tools.println("当前服务器公网IP: " + ip);
                Tools.println("请确认该IP已添加至企业微信管理后台 > 应用管理 > 通讯录同步 > IP白名单");
            } else {
                Tools.println("无法获取当前服务器公网IP，请手动确认IP白名单配置");
            }
        } catch (Exception e) {
            Tools.println("检测服务器IP失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试企业微信群组API和member_count字段
     * 
     * @param corpId 企业ID
     * @param corpSecret 客户联系应用的Secret
     */
    public static void debugGroupChatMemberCount(String corpId, String corpSecret) {
        Tools.println("=== 企业微信群组member_count字段调试 ===");
        Tools.println("企业ID: " + corpId);
        Tools.println("Secret: " + maskSecret(corpSecret));
        Tools.println("");
        
        try {
            // 先获取群组列表
            Tools.println("1. 获取群组列表...");
            List<com.alibaba.fastjson2.JSONObject> groupList = 
                com.career.utils.wecom.service.WeComGroupChatService.getGroupChatList(corpId, corpSecret, null, 0);
            
            if (groupList == null || groupList.isEmpty()) {
                Tools.println("❌ 未获取到任何群组数据");
                return;
            }
            
            Tools.println("✅ 成功获取到 " + groupList.size() + " 个群组");
            
            // 取前3个群组进行详细测试
            int testCount = Math.min(3, groupList.size());
            Tools.println("\n2. 测试前 " + testCount + " 个群组的详细信息...");
            
            for (int i = 0; i < testCount; i++) {
                com.alibaba.fastjson2.JSONObject groupItem = groupList.get(i);
                String chatId = groupItem.getString("chat_id");
                String owner = groupItem.getString("owner");
                
                Tools.println("\n--- 群组 " + (i + 1) + " ---");
                Tools.println("群ID: " + chatId);
                Tools.println("群主: " + owner);
                
                // 获取群组详情
                com.career.utils.wecom.model.GroupChat groupDetail = 
                    com.career.utils.wecom.service.WeComGroupChatService.getGroupChatDetail(corpId, corpSecret, chatId);
                
                if (groupDetail != null) {
                    Tools.println("✅ 群详情获取成功");
                    Tools.println("群名称: " + groupDetail.getName());
                    Tools.println("成员数量: " + groupDetail.getMemberCount());
                    Tools.println("成员版本: " + groupDetail.getMemberVersion());
                    Tools.println("创建时间: " + groupDetail.getCreateTime());
                    
                    // 检查member_count是否为null
                    if (groupDetail.getMemberCount() == null) {
                        Tools.println("⚠️ 警告: member_count为null！");
                    } else {
                        Tools.println("✅ member_count正常: " + groupDetail.getMemberCount());
                    }
                } else {
                    Tools.println("❌ 群详情获取失败");
                }
                
                // 添加延时避免API限流
                try {
                    Thread.sleep(200);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
            
            Tools.println("\n=== 群组member_count字段调试完成 ===");
            
        } catch (Exception e) {
            Tools.println("❌ 群组调试过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
} 