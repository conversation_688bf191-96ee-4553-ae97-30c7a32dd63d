package com.report.db;

import com.career.db.DatabaseUtils;
import com.career.db.JDBCConstants;
import com.career.utils.Tools;
import com.report.entity.*;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 报告数据库JDBC类
 * 用于报告相关的数据库操作
 */
public class ReportJDBC {
    
    static String URL = JDBCConstants.URL;
    static String USER = JDBCConstants.USER;
    static String PASSWD = JDBCConstants.PASSWD;
    
    static {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
    
    /**
     * 关闭数据库连接
     */
    private static void closeAllConnection(Connection conn, PreparedStatement ps, ResultSet rs) {
        /**
    	try {
            if (rs != null) rs.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        try {
            if (ps != null) ps.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        try {
            if (conn != null) conn.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        rs = null;
        ps = null;
        conn = null; */
    	DatabaseUtils.closeAllResources(rs, ps, conn);
    }
    
    /**
     * 根据院校名称查询就业统计数据（前10条）
     * @param yxmc 院校名称
     * @return 就业统计数据列表
     */
    public List<JobStatistics> getJobStatisticsBySchool(String yxmc) {
        List<JobStatistics> jobStatsList = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        if (yxmc == null || yxmc.trim().isEmpty()) {
            Tools.println("院校名称为空，跳过就业数据查询");
            return jobStatsList;
        }
        
        try {
            conn = DatabaseUtils.getConnection();
            
            String sql = "SELECT x.group_name, x.lsy, COUNT(*) as count_num " +
                        "FROM career_jy_all_2024 x " +
                        "WHERE x.yxmc = ? " +
                        "GROUP BY x.group_name, x.lsy " +
                        "ORDER BY COUNT(*) DESC " +
                        "LIMIT 0, 10";
            
            ps = conn.prepareStatement(sql);
            ps.setString(1, yxmc);
            
            Tools.println("执行按院校就业数据查询 - 院校: " + yxmc);
            
            rs = ps.executeQuery();
            
            while (rs.next()) {
                String groupName = rs.getString("group_name");
                String lsy = rs.getString("lsy");
                int count = rs.getInt("count_num");
                
                JobStatistics jobStats = new JobStatistics(groupName, lsy, count);
                jobStatsList.add(jobStats);
            }
            
            Tools.println("查询到院校就业数据条数: " + jobStatsList.size());
            
        } catch (SQLException e) {
            System.err.println("查询院校就业统计数据时发生错误: " + e.getMessage());
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return jobStatsList;
    }
    
    /**
     * 根据专业名称查询就业统计数据（前10条）
     * @param zymc 专业名称
     * @return 就业统计数据列表
     */
    public List<JobStatistics> getJobStatisticsByMajor(String zymc) {
        List<JobStatistics> jobStatsList = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        if (zymc == null || zymc.trim().isEmpty()) {
            Tools.println("专业名称为空，跳过就业数据查询");
            return jobStatsList;
        }
        
        try {
            conn = DatabaseUtils.getConnection();
            
            String sql = "SELECT x.group_name, x.lsy, COUNT(*) as count_num " +
                        "FROM career_jy_all_2024 x " +
                        "WHERE x.zymc = ? " +
                        "GROUP BY x.group_name, x.lsy " +
                        "ORDER BY COUNT(*) DESC " +
                        "LIMIT 0, 10";
            
            ps = conn.prepareStatement(sql);
            ps.setString(1, zymc);
            
            Tools.println("执行按专业就业数据查询 - 专业: " + zymc);
            
            rs = ps.executeQuery();
            
            while (rs.next()) {
                String groupName = rs.getString("group_name");
                String lsy = rs.getString("lsy");
                int count = rs.getInt("count_num");
                
                JobStatistics jobStats = new JobStatistics(groupName, lsy, count);
                jobStatsList.add(jobStats);
            }
            
            Tools.println("查询到专业就业数据条数: " + jobStatsList.size());
            
        } catch (SQLException e) {
            System.err.println("查询专业就业统计数据时发生错误: " + e.getMessage());
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return jobStatsList;
    }
    
    /**
     * 根据院校名称和专业名称查询就业统计数据
     * @param yxmc 院校名称
     * @param zymc 专业名称
     * @return 就业统计数据列表
     */
    public List<JobStatistics> getJobStatisticsBySchoolAndMajor(String yxmc, String zymc) {
        List<JobStatistics> jobStatsList = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        if (yxmc == null || yxmc.trim().isEmpty() || 
            zymc == null || zymc.trim().isEmpty()) {
            Tools.println("院校名称或专业名称为空，跳过就业数据查询");
            return jobStatsList;
        }
        
        try {
            conn = DatabaseUtils.getConnection();
            
            String sql = "SELECT x.group_name, x.lsy, COUNT(*) as count_num " +
                        "FROM career_jy_all_2024 x " +
                        "WHERE x.yxmc = ? AND x.zymc = ? " +
                        "GROUP BY x.group_name, x.lsy " +
                        "ORDER BY COUNT(*) DESC " +
                        "LIMIT 0, 20";
            
            ps = conn.prepareStatement(sql);
            ps.setString(1, yxmc);
            ps.setString(2, zymc);
            
            Tools.println("执行就业数据查询 - 院校: " + yxmc + ", 专业: " + zymc);
            
            rs = ps.executeQuery();
            
            while (rs.next()) {
                String groupName = rs.getString("group_name");
                String lsy = rs.getString("lsy");
                int count = rs.getInt("count_num");
                
                JobStatistics jobStats = new JobStatistics(groupName, lsy, count);
                jobStatsList.add(jobStats);
            }
            
            Tools.println("查询到就业数据条数: " + jobStatsList.size());
            
        } catch (SQLException e) {
            System.err.println("查询就业统计数据时发生错误: " + e.getMessage());
            e.printStackTrace();
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return jobStatsList;
    }
    
    /**
     * 批量查询院校就业统计数据
     * @param schoolNames 院校名称列表
     * @return 按院校分组的就业统计数据
     */
    public List<List<JobStatistics>> batchGetJobStatisticsBySchools(List<String> schoolNames) {
        List<List<JobStatistics>> allJobStats = new ArrayList<>();
        
        if (schoolNames == null || schoolNames.isEmpty()) {
            Tools.println("院校名称列表为空，跳过批量院校就业数据查询");
            return allJobStats;
        }
        
        for (String schoolName : schoolNames) {
            List<JobStatistics> jobStats = getJobStatisticsBySchool(schoolName);
            allJobStats.add(jobStats);
        }
        
        return allJobStats;
    }
    
    /**
     * 批量查询专业就业统计数据
     * @param majorNames 专业名称列表
     * @return 按专业分组的就业统计数据
     */
    public List<List<JobStatistics>> batchGetJobStatisticsByMajors(List<String> majorNames) {
        List<List<JobStatistics>> allJobStats = new ArrayList<>();
        
        if (majorNames == null || majorNames.isEmpty()) {
            Tools.println("专业名称列表为空，跳过批量专业就业数据查询");
            return allJobStats;
        }
        
        for (String majorName : majorNames) {
            List<JobStatistics> jobStats = getJobStatisticsByMajor(majorName);
            allJobStats.add(jobStats);
        }
        
        return allJobStats;
    }
    
    /**
     * 批量查询多个院校专业的就业统计数据
     * @param schoolMajorPairs 院校专业对列表（格式：院校名称|专业名称）
     * @return 按院校专业分组的就业统计数据
     */
    public List<List<JobStatistics>> batchGetJobStatistics(List<String[]> schoolMajorPairs) {
        List<List<JobStatistics>> allJobStats = new ArrayList<>();
        
        if (schoolMajorPairs == null || schoolMajorPairs.isEmpty()) {
            Tools.println("院校专业对列表为空，跳过批量就业数据查询");
            return allJobStats;
        }
        
        for (String[] pair : schoolMajorPairs) {
            if (pair.length >= 2) {
                String yxmc = pair[0];
                String zymc = pair[1];
                List<JobStatistics> jobStats = getJobStatisticsBySchoolAndMajor(yxmc, zymc);
                allJobStats.add(jobStats);
            } else {
                // 添加空列表保持索引对应
                allJobStats.add(new ArrayList<>());
            }
        }
        
        return allJobStats;
    }
   
} 