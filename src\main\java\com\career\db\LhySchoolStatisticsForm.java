package com.career.db;

import java.sql.Timestamp;

/**
 * 学校志愿填报统计表对应的JavaBean
 * 对应表：lhy_school_statistics_form
 */
public class LhySchoolStatisticsForm {
    
    private int id;
    private String schoolLhyCId;        // 学校ID（lhy_card表）
    private String classLhyCId;         // 班级ID（lhy_card_school_student表的lhy_c_id）
    private String className;           // 班级名称
    private String cId;                 // 学生卡ID（base_card表的c_id）
    private String cStudentName;        // 学生姓名
    private int formScoreCj;            // 成绩
    private int formScoreWc;            // 位次
    private String formScoreXk;         // 选科（如：物理+化学+生物）
    private String pcCode;              // 批次类（本科，专科，职教本科）
    private String pc;                  // 批次
    private String lqIs985;             // 是否985 1-是， 0 否
    private String lqIs211;             // 是否211 1-是， 0 否
    private String lqIsSyl;             // 是否双一流 1-是， 0 否
    private String lqIsHzbx;            // 是否中外合作办学 1-是， 0 否
    private Timestamp createTm;         // 志愿创建时间
    private Timestamp recordCreateTm;   // 插入本表时间
    private String lqYxsf;              // 录取院校省份（为空为未被录取/滑档）
    private String lqYxcs;              // 录取院校城市
    private String lqYxdm;              // 院校代码（为空为未被录取）
    private String lqYxmc;              // 院校全程（带括号）（为空为未被录取）
    private String lqYxmcOrg;           // 院校国标名称
    private String lqZyz;               // 专业组
    private String lqZydm;              // 专业代码
    private String lqZymc;              // 专业全程（带括号）
    private String lqZymcOrg;           // 专业国标名称
    private Integer isAdjust;           // 1-调剂录取，2-不调剂录取，999-落榜
    private Integer wasteScoreCnt;      // 浪费分，999为落榜
    
    // 无参构造器
    public LhySchoolStatisticsForm() {}
    
    // Getter and Setter methods
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;      
    }
    
    public String getSchoolLhyCId() {
        return schoolLhyCId;
    }
    
    public void setSchoolLhyCId(String schoolLhyCId) {
        this.schoolLhyCId = schoolLhyCId;
    }
    
    public String getClassLhyCId() {
        return classLhyCId;
    }
    
    public void setClassLhyCId(String classLhyCId) {
        this.classLhyCId = classLhyCId;
    }
    
    public String getClassName() {
        return className;
    }
    
    public void setClassName(String className) {
        this.className = className;
    }
    
    public String getCId() {
        return cId;
    }
    
    public void setCId(String cId) {
        this.cId = cId;
    }
    
    public String getCStudentName() {
        return cStudentName;
    }
    
    public void setCStudentName(String cStudentName) {
        this.cStudentName = cStudentName;
    }
    
    public int getFormScoreCj() {
        return formScoreCj;
    }
    
    public void setFormScoreCj(int formScoreCj) {
        this.formScoreCj = formScoreCj;
    }
    
    public int getFormScoreWc() {
        return formScoreWc;
    }
    
    public void setFormScoreWc(int formScoreWc) {
        this.formScoreWc = formScoreWc;
    }
    
    public String getFormScoreXk() {
        return formScoreXk;
    }
    
    public void setFormScoreXk(String formScoreXk) {
        this.formScoreXk = formScoreXk;
    }
    
    public String getPcCode() {
        return pcCode;
    }
    
    public void setPcCode(String pcCode) {
        this.pcCode = pcCode;
    }
    
    public String getPc() {
        return pc;
    }
    
    public void setPc(String pc) {
        this.pc = pc;
    }
    
    public String getLqIs985() {
        return lqIs985;
    }
    
    public void setLqIs985(String lqIs985) {
        this.lqIs985 = lqIs985;
    }
    
    public String getLqIs211() {
        return lqIs211;
    }
    
    public void setLqIs211(String lqIs211) {
        this.lqIs211 = lqIs211;
    }
    
    public String getLqIsSyl() {
        return lqIsSyl;
    }
    
    public void setLqIsSyl(String lqIsSyl) {
        this.lqIsSyl = lqIsSyl;
    }
    
    public String getLqIsHzbx() {
        return lqIsHzbx;
    }
    
    public void setLqIsHzbx(String lqIsHzbx) {
        this.lqIsHzbx = lqIsHzbx;
    }
    
    public Timestamp getCreateTm() {
        return createTm;
    }
    
    public void setCreateTm(Timestamp createTm) {
        this.createTm = createTm;
    }
    
    public Timestamp getRecordCreateTm() {
        return recordCreateTm;
    }
    
    public void setRecordCreateTm(Timestamp recordCreateTm) {
        this.recordCreateTm = recordCreateTm;
    }
    
    public String getLqYxsf() {
        return lqYxsf;
    }
    
    public void setLqYxsf(String lqYxsf) {
        this.lqYxsf = lqYxsf;
    }
    
    public String getLqYxcs() {
        return lqYxcs;
    }
    
    public void setLqYxcs(String lqYxcs) {
        this.lqYxcs = lqYxcs;
    }
    
    public String getLqYxdm() {
        return lqYxdm;
    }
    
    public void setLqYxdm(String lqYxdm) {
        this.lqYxdm = lqYxdm;
    }
    
    public String getLqYxmc() {
        return lqYxmc;
    }
    
    public void setLqYxmc(String lqYxmc) {
        this.lqYxmc = lqYxmc;
    }
    
    public String getLqYxmcOrg() {
        return lqYxmcOrg;
    }
    
    public void setLqYxmcOrg(String lqYxmcOrg) {
        this.lqYxmcOrg = lqYxmcOrg;
    }
    
    public String getLqZyz() {
        return lqZyz;
    }
    
    public void setLqZyz(String lqZyz) {
        this.lqZyz = lqZyz;
    }
    
    public String getLqZydm() {
        return lqZydm;
    }
    
    public void setLqZydm(String lqZydm) {
        this.lqZydm = lqZydm;
    }
    
    public String getLqZymc() {
        return lqZymc;
    }
    
    public void setLqZymc(String lqZymc) {
        this.lqZymc = lqZymc;
    }
    
    public String getLqZymcOrg() {
        return lqZymcOrg;
    }
    
    public void setLqZymcOrg(String lqZymcOrg) {
        this.lqZymcOrg = lqZymcOrg;
    }
    
    public Integer getIsAdjust() {
        return isAdjust;
    }
    
    public void setIsAdjust(Integer isAdjust) {
        this.isAdjust = isAdjust;
    }
    
    public Integer getWasteScoreCnt() {
        return wasteScoreCnt;
    }
    
    public void setWasteScoreCnt(Integer wasteScoreCnt) {
        this.wasteScoreCnt = wasteScoreCnt;
    }
    
    @Override
    public String toString() {
        return "LhySchoolStatisticsForm{" +
                "id=" + id +
                ", schoolLhyCId='" + schoolLhyCId + '\'' +
                ", classLhyCId='" + classLhyCId + '\'' +
                ", className='" + className + '\'' +
                ", cId='" + cId + '\'' +
                ", cStudentName='" + cStudentName + '\'' +
                ", formScoreCj=" + formScoreCj +
                ", formScoreWc=" + formScoreWc +
                ", formScoreXk='" + formScoreXk + '\'' +
                ", pcCode='" + pcCode + '\'' +
                ", pc='" + pc + '\'' +
                ", lqIs985='" + lqIs985 + '\'' +
                ", lqIs211='" + lqIs211 + '\'' +
                ", lqIsSyl='" + lqIsSyl + '\'' +
                ", lqIsHzbx='" + lqIsHzbx + '\'' +
                ", createTm=" + createTm +
                ", recordCreateTm=" + recordCreateTm +
                ", lqYxsf='" + lqYxsf + '\'' +
                ", lqYxcs='" + lqYxcs + '\'' +
                ", lqYxdm='" + lqYxdm + '\'' +
                ", lqYxmc='" + lqYxmc + '\'' +
                ", lqYxmcOrg='" + lqYxmcOrg + '\'' +
                ", lqZyz='" + lqZyz + '\'' +
                ", lqZydm='" + lqZydm + '\'' +
                ", lqZymc='" + lqZymc + '\'' +
                ", lqZymcOrg='" + lqZymcOrg + '\'' +
                ", isAdjust=" + isAdjust +
                ", wasteScoreCnt=" + wasteScoreCnt +
                '}';
    }
}
