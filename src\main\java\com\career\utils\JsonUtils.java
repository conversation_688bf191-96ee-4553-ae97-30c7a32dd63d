package com.career.utils;

import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import java.io.UnsupportedEncodingException;

/**
 * JSON响应工具类
 * 通用的JSON格式化工具，不包含任何业务逻辑
 */
public class JsonUtils {
    
    /**
     * JSON字符串转义
     */
    public static String escapeJson(String str) {
        return str != null ? str.replace("\"", "\\\"") : "";
    }
    
    /**
     * 确保字符串使用UTF-8编码
     * @param str 原始字符串
     * @return UTF-8编码的字符串
     */
    public static String ensureUtf8(String str) {
        if (str == null) {
            return "";
        }
        
        try {
            // 检查是否已经是正确的UTF-8编码
            byte[] bytes = str.getBytes("UTF-8");
            String utf8Str = new String(bytes, "UTF-8");
            
            // 如果转换后字符串相同，说明原本就是UTF-8
            if (str.equals(utf8Str)) {
                return str;
            }
            
            // 尝试从ISO-8859-1转换为UTF-8（常见的乱码情况）
            try {
                byte[] isoBytes = str.getBytes("ISO-8859-1");
                return new String(isoBytes, "UTF-8");
            } catch (Exception e) {
                // 如果转换失败，返回原字符串
                return str;
            }
            
        } catch (UnsupportedEncodingException e) {
            // UTF-8编码异常，返回原字符串
            return str;
        }
    }
    
    /**
     * 从JSON字符串中提取字符串值
     * @param json JSON字符串
     * @param key 要提取的键名
     * @return 对应的值，如果不存在则返回"未填写"
     */
    public static String getStringValue(String json, String key) {
        try {
            Pattern pattern = Pattern.compile("\"" + key + "\"\\s*:\\s*\"([^\"]*?)\"");
            Matcher matcher = pattern.matcher(json);
            if (matcher.find()) {
                String value = matcher.group(1);
                return ensureUtf8(value);
            }
            return "未填写";
        } catch (Exception e) {
            return "解析错误";
        }
    }
    
    /**
     * 从JSON字符串中提取数组值
     * @param json JSON字符串
     * @param key 要提取的键名
     * @return 对应的字符串数组，如果不存在则返回空数组
     */
    public static String[] getArrayValues(String json, String key) {
        try {
            Pattern pattern = Pattern.compile("\"" + key + "\"\\s*:\\s*\\[([^\\]]*?)\\]");
            Matcher matcher = pattern.matcher(json);
            if (matcher.find()) {
                String arrayContent = matcher.group(1);
                if (arrayContent.trim().isEmpty()) {
                    return new String[0];
                }
                // 移除引号并分割
                String[] items = arrayContent.split(",");
                String[] result = new String[items.length];
                for (int i = 0; i < items.length; i++) {
                    String item = items[i].trim().replaceAll("^\"|\"$", "");
                    result[i] = ensureUtf8(item);
                }
                return result;
            }
            return new String[0];
        } catch (Exception e) {
            return new String[]{"解析错误"};
        }
    }
    
    /**
     * 检查JSON字符串中是否包含指定的键
     * @param json JSON字符串
     * @param key 要检查的键名
     * @return 如果包含该键则返回true，否则返回false
     */
    public static boolean hasKey(String json, String key) {
        try {
            Pattern pattern = Pattern.compile("\"" + key + "\"\\s*:");
            Matcher matcher = pattern.matcher(json);
            return matcher.find();
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 从JSON字符串中提取数值
     * @param json JSON字符串
     * @param key 要提取的键名
     * @return 对应的数值，如果不存在或无法解析则返回null
     */
    public static String getNumberValue(String json, String key) {
        try {
            // 匹配数值类型（不带引号）
            Pattern pattern = Pattern.compile("\"" + key + "\"\\s*:\\s*([0-9.]+)");
            Matcher matcher = pattern.matcher(json);
            if (matcher.find()) {
                return matcher.group(1);
            }
            // 如果没找到数值型，尝试字符串型
            return getStringValue(json, key);
        } catch (Exception e) {
            return "解析错误";
        }
    }
    
    /**
     * 构建成功响应的JSON字符串
     * @param message 消息
     * @param dataList 数据列表，每个元素应该是Map<String, String>格式
     * @return JSON字符串
     */
    public static String buildSuccessResponse(String message, List<Map<String, String>> dataList) {
        StringBuilder json = new StringBuilder();
        json.append("{\"success\":true,\"message\":\"").append(escapeJson(ensureUtf8(message))).append("\",\"data\":[");
        
        if (dataList != null) {
            for (int i = 0; i < dataList.size(); i++) {
                if (i > 0) json.append(",");
                Map<String, String> item = dataList.get(i);
                
                json.append("{");
                boolean first = true;
                for (Map.Entry<String, String> entry : item.entrySet()) {
                    if (!first) json.append(",");
                    json.append("\"").append(escapeJson(ensureUtf8(entry.getKey()))).append("\":\"")
                        .append(escapeJson(ensureUtf8(entry.getValue()))).append("\"");
                    first = false;
                }
                json.append("}");
            }
        }
        
        json.append("]}");
        return json.toString();
    }
    
    /**
     * 构建错误响应的JSON字符串
     * @param message 错误消息
     * @return JSON字符串
     */
    public static String buildErrorResponse(String message) {
        return "{\"success\":false,\"message\":\"" + escapeJson(ensureUtf8(message)) + "\"}";
    }
    
    /**
     * 创建数据项Map的辅助方法
     * @param name 名称
     * @param code 代码
     * @return Map格式的数据项
     */
    public static Map<String, String> createDataItem(String name, String code) {
        Map<String, String> item = new java.util.HashMap<>();
        item.put("name", ensureUtf8(name));
        item.put("code", ensureUtf8(code));
        return item;
    }
    
    /**
     * 将字符串数组转换为JSON字符串
     * @param array 字符串数组
     * @return JSON字符串
     */
    public static String arrayToJsonString(String[] array) {
        if (array == null || array.length == 0) {
            return "[]";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("[");
        for (int i = 0; i < array.length; i++) {
            if (i > 0) {
                sb.append(",");
            }
            sb.append("\"").append(ensureUtf8(array[i]).replace("\"", "\\\"")).append("\"");
        }
        sb.append("]");
        return sb.toString();
    }
} 