package com.career.utils.liuxue;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.alibaba.fastjson2.JSONObject;
import com.career.utils.zsky.*;

public class DealWithLX {

	public static void main(String args[]) throws Exception {
		//dealWithXUEXINWANG();
		
		
		
		File files = new File("C:\\Users\\<USER>\\Desktop\\2024单招 普招\\24年普招展板");
		String[] lists = files.list();
		for(int i=0;i<lists.length;i++) {
			File inner = new File(files, lists[i]);
			String[] listInner = inner.list();

			String rd1 = String.valueOf(Math.random() * 30).substring(0,2);
			if(rd1.indexOf(".") != -1) {
				rd1 = "0" + rd1.substring(0,rd1.length() - 1);
			}
			for(int k=0;k<listInner.length;k++) {
				File innerIner = new File(new File(files, lists[i]),listInner[k]);
				String rd2 = String.valueOf(Math.random()).substring(4,10);
				
				innerIner.renameTo(new File(new File(files, lists[i]), "微信图片_202406"+rd1+""+rd2+".jpg"));
				
				System.out.println(innerIner.getName());
			}
		}
		
		
	}

	public static void dealWithInfo() throws Exception {
		StringBuffer SQL = new StringBuffer();
		for (int i = 2; i <= 1600; i++) {// 1574
			try {
				BufferedReader bw = new BufferedReader(
						new FileReader(new File("E://kaoyan//" + i + "//info_" + i + "_data.txt")));
				StringBuffer sb = new StringBuffer();
				String str = null;
				while ((str = bw.readLine()) != null) {
					sb.append(str);
				}
				ResultInfoBean resultBean = (ResultInfoBean) JSONObject.parseObject(sb.toString(), ResultInfoBean.class);
				SchoolInfo info = resultBean.getData();
				if (info == null) {
					continue;
				}
				SQL.append(info.generateSQL()+"\r\n");
				
				
			} catch (Exception ex) {
			}

		}

		writeTempFile(new File("E://kaoyan//KYschoolinfoSQL.txt"), SQL);
	}

	public static void dealWithMajorInfo() throws Exception {
		StringBuffer SQL = new StringBuffer();
		
		for (int i = 2; i <= 1600; i++) {// 1574
			try {
				BufferedReader bw = new BufferedReader(
						new FileReader(new File("E://kaoyan//" + i + "//runn2_3_"+i+"history_data.txt")));
				StringBuffer sb = new StringBuffer();
				String str = null;
				while ((str = bw.readLine()) != null) {
					sb.append(str);
				}
				ResultMajorBean bean = (ResultMajorBean) JSONObject.parseObject(sb.toString(), ResultMajorBean.class);
				SchoolMajor info = bean.getData();
				if (info == null) {
					continue;
				}
				
				SchoolMajorFirstClass[] elements = info.getFirst_class();
				if(elements != null && elements.length > 0) {
					for(SchoolMajorFirstClass infoBean : elements) {
						SQL.append(infoBean.generateSQL(i, info.getSyl_jianshe())+"\r\n");
					}
				}
				
				
			} catch (Exception ex) {
			}

		}

		writeTempFile(new File("E://kaoyan//KYSchoolMajorSQL.txt"), SQL);
	}
	
	
	public static void dealWithMajorDataHistory() throws Exception {
		StringBuffer SQL = new StringBuffer();
		
		for (int i = 2; i <= 1600; i++) {
			try {
				
				File file = new File("E://kaoyan//" + i + "//");
				File[] fileList = file.listFiles();
				
				for(File xx : fileList) {
					if(xx.getName().startsWith("runn2_2_2023")) {
						System.out.println(xx.getName());
						
						BufferedReader bw = new BufferedReader(new FileReader(xx));
						StringBuffer sb = new StringBuffer();
						String str = null;
						while ((str = bw.readLine()) != null) {
							sb.append(str);
						}
						ResultMajorHistoryBean bean = (ResultMajorHistoryBean) JSONObject.parseObject(sb.toString(), ResultMajorHistoryBean.class);
						MajorHistory info = bean.getData();
						if (info == null) {
							continue;
						}
						
						MajorHistoryItem[] elements = info.getItem();
						if(elements != null && elements.length > 0) {
							for(MajorHistoryItem item : elements) {
								SQL.append(item.generateSQL(i)+"\r\n");
							}
						}
						
					}
				}
			} catch (Exception ex) {
				ex.printStackTrace();
			}

		}

		writeTempFile(new File("E://kaoyan//KYSchoolMajorDataSQL.txt"), SQL);
	}
	
	
	public static void dealWithXUEXINWANG() throws Exception {
		StringBuffer SQL = new StringBuffer();
		
		File file = new File("F://学信网视频//");
		File[] fileList = file.listFiles();
		
		for(File xx : fileList) {
			
			
			BufferedReader bw = new BufferedReader(new FileReader(xx));
			String str = null;
			while ((str = bw.readLine()) != null) {
				if(str.indexOf("https://chsi-v.oss-cn-beijing.aliyuncs.com/xzpt/jy/") != -1) {
					String cont = str.substring(str.indexOf("resultJson: ") + "resultJson: ".length(), str.indexOf(",\"flag"));
					//System.out.println(cont);
					JSONObject object = JSONObject.parseObject(cont.trim()+"}");
					//System.out.println(object.getJSONObject("data").getString("desc"));
					//System.out.println(object.getJSONObject("data").getString("videoUrl"));
					
					SQL.append("insert into career_ext_url_xxw(title, catg, descp, vedio_url) values('"+object.getJSONObject("data").getString("title")+"',NULL,'"+object.getJSONObject("data").getString("desc")+"','"+object.getJSONObject("data").getString("videoUrl")+"');\r\n");
				}
			}
			
		}

		writeTempFile(new File("F://学信网视频_KYSchoolMajorDataSQL.txt"), SQL);
	}
	
	public static void dealWithMajorAdjust() throws Exception {
		StringBuffer SQL = new StringBuffer();
		
		for (int i = 2; i <= 1600; i++) {
			try {
				BufferedReader bw = new BufferedReader(
						new FileReader(new File("E://kaoyan//" + i + "//runn2_4_"+i+"adjust_data.txt")));
				StringBuffer sb = new StringBuffer();
				String str = null;
				while ((str = bw.readLine()) != null) {
					sb.append(str);
				}
				ResultAdjustBean bean = (ResultAdjustBean) JSONObject.parseObject(sb.toString(), ResultAdjustBean.class);
				MajorAdjust info = bean.getData();
				if (info == null) {
					continue;
				}
				
				MajorAdjustItem[] elements = info.getData();
				if(elements != null && elements.length > 0) {
					for(MajorAdjustItem item : elements) {
						SQL.append(item.generateSQL()+"\r\n");
					}
				}
				
				
			} catch (Exception ex) {
			}

		}

		writeTempFile(new File("E://kaoyan//KYSchoolMajorAdjustSQL.txt"), SQL);
	}

	private static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	private static String removeTag(String html) {

		// 定义要匹配的正则表达式模式
		Pattern pattern = Pattern.compile("<[^>]+>", Pattern.CASE_INSENSITIVE);

		// 创建 Matcher 对象并进行匹配操作
		Matcher matcher = pattern.matcher(html);

		// 将匹配到的 HTML 标签替换为空字符串
		String result = matcher.replaceAll("").replaceAll("&nbsp;", "");

		return result.trim();
	}

}
