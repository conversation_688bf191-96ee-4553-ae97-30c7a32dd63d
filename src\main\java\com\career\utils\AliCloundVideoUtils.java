// This file is auto-generated, don't edit it. Thanks.
package com.career.utils;

import com.alibaba.fastjson2.JSONObject;
import com.aliyun.tea.*;

public class AliCloundVideoUtils {

    /**
     * <b>description</b> :
     * <p>使用AK&amp;SK初始化账号Client</p>
     * @return Client
     * 
     * @throws Exception
     */
    public static com.aliyun.teaopenapi.Client createClient() throws Exception {
        // 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考。
        // 建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378657.html。
        //com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config().setAccessKeyId(System.getenv("ALIBABA_CLOUD_ACCESS_KEY_ID")).setAccessKeySecret(System.getenv("ALIBABA_CLOUD_ACCESS_KEY_SECRET"));
    	//我的阿里云ACCESSKEYID ： LTAI5tHaquKX2NkiQqK8w8gz
    	//SECRET： ******************************
    	com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config().setAccessKeyId("LTAI5tHaquKX2NkiQqK8w8gz").setAccessKeySecret("******************************");
        // Endpoint 请参考 https://api.aliyun.com/product/vod
        config.endpoint = "vod.cn-shenzhen.aliyuncs.com";
        return new com.aliyun.teaopenapi.Client(config);
    }

    /**
     * <b>description</b> :
     * <p>API 相关</p>
     * 
     * @param path string Path parameters
     * @return OpenApi.Params
     */
    public static com.aliyun.teaopenapi.models.Params createApiInfo() throws Exception {
        com.aliyun.teaopenapi.models.Params params = new com.aliyun.teaopenapi.models.Params()
                // 接口名称
                .setAction("GetVideoPlayAuth")
                // 接口版本
                .setVersion("2017-03-21")
                // 接口协议
                .setProtocol("HTTPS")
                // 接口 HTTP 方法
                .setMethod("POST")
                .setAuthType("AK")
                .setStyle("RPC")
                // 接口 PATH
                .setPathname("/")
                // 接口请求体内容格式
                .setReqBodyType("json")
                // 接口响应体内容格式
                .setBodyType("json");
        return params;
    }
    
    static com.aliyun.teaopenapi.Client client = null;
    public static AliCloundVideoBean getVedioResponseInfo(String videoID) {
    	AliCloundVideoBean bean = new AliCloundVideoBean();
    	try {
	    	java.util.List<String> args = java.util.Arrays.asList("");
	    	if(client == null) {
	    		client = AliCloundVideoUtils.createClient();
	    	}
	        com.aliyun.teaopenapi.models.Params params = AliCloundVideoUtils.createApiInfo();
	        // query params
	        java.util.Map<String, Object> queries = new java.util.HashMap<>();
	        queries.put("VideoId", videoID);
	        // runtime options
	        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
	        com.aliyun.teaopenapi.models.OpenApiRequest request = new com.aliyun.teaopenapi.models.OpenApiRequest().setQuery(com.aliyun.openapiutil.Client.query(queries));
	        // 复制代码运行请自行打印 API 的返回值
	        // 返回值实际为 Map 类型，可从 Map 中获得三类数据：响应体 body、响应头 headers、HTTP 返回的状态码 statusCode。
	        Object resp = client.callApi(params, request, runtime);
	        JSONObject object = JSONObject.parseObject(com.aliyun.teautil.Common.toJSONString(resp));
	        JSONObject body = object.getJSONObject("body");
	        bean.setPlayAuth(body.getString("PlayAuth"));
	        JSONObject VideoMeta = body.getJSONObject("VideoMeta");
	        bean.setVideoMeta_Title(VideoMeta.getString("Title"));
	        bean.setVideoMeta_Duration(VideoMeta.getFloatValue("Duration"));
	        bean.setVideoMeta_CoverURL(VideoMeta.getString("CoverURL"));
	        bean.setVid(videoID);
	        return bean;
    	}catch(Exception ex) {
    		ex.printStackTrace();
    	}
        return bean;
    }

    public static void main(String[] args_) throws Exception {
        java.util.List<String> args = java.util.Arrays.asList(args_);
        com.aliyun.teaopenapi.Client client = AliCloundVideoUtils.createClient();
        com.aliyun.teaopenapi.models.Params params = AliCloundVideoUtils.createApiInfo();
        // query params
        java.util.Map<String, Object> queries = new java.util.HashMap<>();
        queries.put("VideoId", "7028d8accc9771efb7ca0361c0c60102");
        // runtime options
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        com.aliyun.teaopenapi.models.OpenApiRequest request = new com.aliyun.teaopenapi.models.OpenApiRequest().setQuery(com.aliyun.openapiutil.Client.query(queries));
        // 复制代码运行请自行打印 API 的返回值
        // 返回值实际为 Map 类型，可从 Map 中获得三类数据：响应体 body、响应头 headers、HTTP 返回的状态码 statusCode。
        Object resp = client.callApi(params, request, runtime);
        JSONObject object = JSONObject.parseObject(com.aliyun.teautil.Common.toJSONString(resp));
        JSONObject body = object.getJSONObject("body");
        Tools.println(body.getString("PlayAuth"));
    }
}
