package com.career.db;

import java.util.Date;

public class WecomSalesCustomer {
	private String customer_id;            // 主键ID
    private String wecom_external_userid;   // 企业微信外部用户ID
    private String wecom_name;           // 外部联系人的名称
    private Integer wecom_type;           // 外部联系人的类型 (1表示微信用户，2表示企业微信联系人)
    private String wecom_avatar;          // 外部联系人头像
    private Integer wecom_gender;         // 外部联系人性别 (0-未知 1-男性 2-女性)
    private String wecom_follow_users_json;   // 跟进人信息列表的JSON字符串
    private Date wecom_join_tm;           // 加入时间
    private Date sys_create_tm;            // 进到我们系统的创建时间（只更新一次）
    private Date sys_update_tm;            // 进到我们系统的更新时间（每次拉取更新）
    private String customer_nickname;      // 备注昵称
    private Integer customer_roles;         // 角色（1母亲，2父亲，3孩子，4其他）
    private String customer_tags;         // 客户标签，多个逗号隔开
    private String staff_id;               // 销售员工ID(主力负责)
    private String ext_staff_id;            // 跟单销售员工ID（一个人多个人更）
    private Integer customer_gender;        // 客户性别，1-男，2-女
    private String customer_occupation;      // 客户职业
    private Integer customer_boss_ind;       // 客户是否是老板，1是，2-不是
    private Integer customer_highest_education;// 客户最高学历
    private Integer customer_work_year;       // 客户工作年限（1：工作，2：1-2年，3：3-5年，4：8年以上）
    private Integer customer_type;           // 客户类型1-初中生，2-高中生，3-大学生，4-研究生，5-在职人士
    private String customer_sf;              // 客户省份
    private String customer_cs;              // 客户城市
    private String customer_phone1;           // 客户电话1
    private String customer_phone2;           // 客户电话2
    private String customer_email;           // 客户EMAIL
    private String customer_wechat1;        // 客户微信1
    private String customer_wechat2;        // 客户微信2
    private String customer_qq;             // 客户QQ
    
    private String wecom_related_groups;  //所在的企业微信组（多个逗号隔开，每次更新需要更新）
    private String wecom_related_follower; //企业微信跟进人（多个逗号隔开，每次更新需要更新）
    private String wecom_related_tags; //企业微信标签（多个逗号隔开，每次更新需要更新）
    
    private Integer status;                // 1-有效，2-删除
    private Integer purchase_intention;    //购买意向new_indicator
    private Integer new_indicator;    //购买意向
    
    
	public Integer getNew_indicator() {
		return new_indicator;
	}
	public void setNew_indicator(Integer new_indicator) {
		this.new_indicator = new_indicator;
	}
	public Integer getPurchase_intention() {
		return purchase_intention;
	}
	public void setPurchase_intention(Integer purchase_intention) {
		this.purchase_intention = purchase_intention;
	}
	public String getCustomer_id() {
		return customer_id;
	}
	public void setCustomer_id(String customer_id) {
		this.customer_id = customer_id;
	}
	public String getWecom_external_userid() {
		return wecom_external_userid;
	}
	public void setWecom_external_userid(String wecom_external_userid) {
		this.wecom_external_userid = wecom_external_userid;
	}
	public String getWecom_name() {
		return wecom_name;
	}
	public void setWecom_name(String wecom_name) {
		this.wecom_name = wecom_name;
	}
	public Integer getWecom_type() {
		return wecom_type;
	}
	public void setWecom_type(Integer wecom_type) {
		this.wecom_type = wecom_type;
	}
	public String getWecom_avatar() {
		return wecom_avatar;
	}
	public void setWecom_avatar(String wecom_avatar) {
		this.wecom_avatar = wecom_avatar;
	}
	public Integer getWecom_gender() {
		return wecom_gender;
	}
	public void setWecom_gender(Integer wecom_gender) {
		this.wecom_gender = wecom_gender;
	}
	public String getWecom_follow_users_json() {
		return wecom_follow_users_json;
	}
	public void setWecom_follow_users_json(String wecom_follow_users_json) {
		this.wecom_follow_users_json = wecom_follow_users_json;
	}
	public Date getWecom_join_tm() {
		return wecom_join_tm;
	}
	public void setWecom_join_tm(Date wecom_join_tm) {
		this.wecom_join_tm = wecom_join_tm;
	}
	public Date getSys_create_tm() {
		return sys_create_tm;
	}
	public void setSys_create_tm(Date sys_create_tm) {
		this.sys_create_tm = sys_create_tm;
	}
	public Date getSys_update_tm() {
		return sys_update_tm;
	}
	public void setSys_update_tm(Date sys_update_tm) {
		this.sys_update_tm = sys_update_tm;
	}
	public String getCustomer_nickname() {
		return customer_nickname;
	}
	public void setCustomer_nickname(String customer_nickname) {
		this.customer_nickname = customer_nickname;
	}
	public Integer getCustomer_roles() {
		return customer_roles;
	}
	public void setCustomer_roles(Integer customer_roles) {
		this.customer_roles = customer_roles;
	}
	public String getCustomer_tags() {
		return customer_tags;
	}
	public void setCustomer_tags(String customer_tags) {
		this.customer_tags = customer_tags;
	}
	public String getStaff_id() {
		return staff_id;
	}
	public void setStaff_id(String staff_id) {
		this.staff_id = staff_id;
	}
	public String getExt_staff_id() {
		return ext_staff_id;
	}
	public void setExt_staff_id(String ext_staff_id) {
		this.ext_staff_id = ext_staff_id;
	}
	public Integer getCustomer_gender() {
		return customer_gender;
	}
	public void setCustomer_gender(Integer customer_gender) {
		this.customer_gender = customer_gender;
	}
	public String getCustomer_occupation() {
		return customer_occupation;
	}
	public void setCustomer_occupation(String customer_occupation) {
		this.customer_occupation = customer_occupation;
	}
	public Integer getCustomer_boss_ind() {
		return customer_boss_ind;
	}
	public void setCustomer_boss_ind(Integer customer_boss_ind) {
		this.customer_boss_ind = customer_boss_ind;
	}
	public Integer getCustomer_highest_education() {
		return customer_highest_education;
	}
	public void setCustomer_highest_education(Integer customer_highest_education) {
		this.customer_highest_education = customer_highest_education;
	}
	public Integer getCustomer_work_year() {
		return customer_work_year;
	}
	public void setCustomer_work_year(Integer customer_work_year) {
		this.customer_work_year = customer_work_year;
	}
	public Integer getCustomer_type() {
		return customer_type;
	}
	public void setCustomer_type(Integer customer_type) {
		this.customer_type = customer_type;
	}
	public String getCustomer_sf() {
		return customer_sf;
	}
	public void setCustomer_sf(String customer_sf) {
		this.customer_sf = customer_sf;
	}
	public String getCustomer_cs() {
		return customer_cs;
	}
	public void setCustomer_cs(String customer_cs) {
		this.customer_cs = customer_cs;
	}
	public String getCustomer_phone1() {
		return customer_phone1;
	}
	public void setCustomer_phone1(String customer_phone1) {
		this.customer_phone1 = customer_phone1;
	}
	public String getCustomer_phone2() {
		return customer_phone2;
	}
	public void setCustomer_phone2(String customer_phone2) {
		this.customer_phone2 = customer_phone2;
	}
	public String getCustomer_email() {
		return customer_email;
	}
	public void setCustomer_email(String customer_email) {
		this.customer_email = customer_email;
	}
	public String getCustomer_wechat1() {
		return customer_wechat1;
	}
	public void setCustomer_wechat1(String customer_wechat1) {
		this.customer_wechat1 = customer_wechat1;
	}
	public String getCustomer_wechat2() {
		return customer_wechat2;
	}
	public void setCustomer_wechat2(String customer_wechat2) {
		this.customer_wechat2 = customer_wechat2;
	}
	public String getCustomer_qq() {
		return customer_qq;
	}
	public void setCustomer_qq(String customer_qq) {
		this.customer_qq = customer_qq;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public String getWecom_related_groups() {
		return wecom_related_groups;
	}
	public void setWecom_related_groups(String wecom_related_groups) {
		this.wecom_related_groups = wecom_related_groups;
	}
	public String getWecom_related_follower() {
		return wecom_related_follower;
	}
	public void setWecom_related_follower(String wecom_related_follower) {
		this.wecom_related_follower = wecom_related_follower;
	}
	public String getWecom_related_tags() {
		return wecom_related_tags;
	}
	public void setWecom_related_tags(String wecom_related_tags) {
		this.wecom_related_tags = wecom_related_tags;
	}
    
    
}
