package com.career.utils;

import com.alibaba.fastjson2.JSONObject;
import com.career.db.AiTbForm;
import com.career.db.CCache;
import com.career.db.JHBean;
import com.career.db.JHxBean;
import com.career.db.LhyForm;
import com.career.db.LhyFormMakerHistory;
import com.career.db.MarjorBean;
import com.career.db.UniversityPeriod;
import com.career.db.ZyzdForm;
import com.career.db.jhdao.YfJHBean;
import com.career.db.jhdao.YfJHxBean;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Random;
import java.util.Set;


public class Tools {
	
	private static PrintWriter logWriter;
	private static final String LOG_FILE_NAME = "log.md";
	
	static {
		try {
			// 创建日志文件，设置为追加模式
			File logFile = new File(LOG_FILE_NAME);
			logWriter = new PrintWriter(new FileWriter(logFile, true), true);
			println("日志系统初始化完成，日志文件: " + logFile.getAbsolutePath());
		} catch (IOException e) {
			System.err.println("无法创建日志文件: " + e.getMessage());
		}
	}
	
	// 关闭日志文件
	public static void closeLogger() {
		if (logWriter != null) {
			logWriter.close();
		}
	}
	
	// 记录时间戳的日志
	public static void logWithTimestamp(String message) {
		String timestamp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date());
		String logMessage = "[" + timestamp + "] " + message;
		
		// 打印到控制台
		System.out.println(logMessage);
		
		// 写入日志文件
		if (logWriter != null) {
			logWriter.println(logMessage);
		}
	}

	
	public static List<UniversityPeriod> parseUniversityPeriods(String jsonString) {
        List<UniversityPeriod> periods = new ArrayList<>();
        
        try {
        // 将JSON字符串转换为JSONObject
        JSONObject jsonObject = JSONObject.parse(jsonString);

        // 获取各个字段的值
        int startYear = jsonObject.getIntValue("startYear");
        String startTimeText = jsonObject.getString("startTimeText");
        String endTimeText = jsonObject.getString("endTimeText");
        int endYear = jsonObject.getIntValue("endYear");
        String name = jsonObject.getString("name");

        // 创建UniversityPeriod对象并添加到列表
        UniversityPeriod period = new UniversityPeriod(String.valueOf(startYear), startTimeText, endTimeText, String.valueOf(endYear), name);
        periods.add(period);
        }catch(Exception ex) {}

        return periods;
    }
	
	public static String escapeForHtml(String input) {
        if (input == null) {
            return null;
        }
        return input.replace("&", "&amp;")
                    .replace("<", "&lt;")
                    .replace(">", "&gt;")
                    .replace("\"", "&quot;")
                    .replace("'", "&#39;"); // 或者使用 "&apos;" 也是可以的
    }
	
    /**
     * 转义JavaScript字符串中的特殊字符，确保其在JS中安全使用。
     * 主要处理单引号、双引号、反斜杠、换行符和回车符。
     * @param input 需要转义的字符串
     * @return 转义后的字符串
     */
    public static String escapeJsQuotes(String input) {
        if (input == null) {
            return "";
        }
        return input.replace("\\", "\\\\") // 先转义反斜杠
                    .replace("'", "\\\'")  // 转义单引号
                    .replace("\"", "\\\"") // 转义双引号
                    .replace("\n", "\\n")  // 转义换行符
                    .replace("\r", "\\r"); // 转义回车符
    }
    
	public static double qs_yc(int scoreQSA, int scoreQSB, int scoreQSC) {
        return AdmissionProbabilityCalculator.qs_yc(scoreQSA, scoreQSB, scoreQSC);
    }

	public static void xmlToJSON(String xmlStr) {
		cn.hutool.json.JSONObject jsonObject = cn.hutool.json.JSONUtil.xmlToJson(xmlStr);
		System.out.println(jsonObject.toString());
		System.out.println("ToUserName:" +jsonObject.get("ToUserName"));
		System.out.println("FromUserName:" +jsonObject.get("FromUserName"));
		System.out.println("CreateTime:" +jsonObject.get("CreateTime"));
		System.out.println("MsgType:" +jsonObject.get("MsgType"));
		System.out.println("Event:" +jsonObject.get("Event"));
		System.out.println("EventKey:" +jsonObject.get("EventKey"));
		System.out.println("Ticket:" +jsonObject.get("Ticket"));
		
		cn.hutool.json.JSONObject jsonObject1 = cn.hutool.json.XML.toJSONObject(xmlStr, true);
		System.out.println(jsonObject1.toString());
	}
	
	public static List<JHBean> getRandomElements(List<JHBean> list, int count) {
        List<JHBean> selected = new ArrayList<JHBean>();
        Random random = new Random();

        // 确保不会超出列表大小
        count = Math.min(count, list.size());
        HashMap<String, String> TEMP = new HashMap<>();
        while (selected.size() < count) {
            int index = random.nextInt(list.size());
            JHBean element = list.get(index);
            if (!TEMP.containsKey(String.valueOf(element.getId()))) {
                selected.add(element);
                TEMP.put(String.valueOf(element.getId()), String.valueOf(element.getId()));
            }
        }

        return selected;
    }
	
	public static String getTBUniqueKey(Object formBean, boolean is96) {
		
		if(formBean instanceof ZyzdForm) {
			ZyzdForm form = (ZyzdForm)formBean;
			if(is96){
				return Tools.trim(form.getYxdm()) + Tools.trim(form.getZydm());
			}else{
				return Tools.trim(form.getYxdm()) + Tools.trim(form.getZydm()) + Tools.trim(form.getZyz());
			}
		}else if(formBean instanceof LhyForm) {
			LhyForm form = (LhyForm)formBean;
			if(is96){
				return Tools.trim(form.getYxdm()) + Tools.trim(form.getZydm());
			}else{
				return Tools.trim(form.getYxdm()) + Tools.trim(form.getZydm()) + Tools.trim(form.getZyz());
			}
		}else if(formBean instanceof JHBean) {
			JHBean form = (JHBean)formBean;
			if(is96){
				return Tools.trim(form.getYxdm()) + Tools.trim(form.getZydm());
			}else{
				return Tools.trim(form.getYxdm()) + Tools.trim(form.getZydm()) + Tools.trim(form.getZyz());
			}
		}else if(formBean instanceof YfJHBean) {
			YfJHBean form = (YfJHBean)formBean;
			if(is96){
				return Tools.trim(form.getYxdm()) + Tools.trim(form.getZydm());
			}else{
				return Tools.trim(form.getYxdm()) + Tools.trim(form.getZydm()) + Tools.trim(form.getZyz());
			}
		}else if(formBean instanceof AiTbForm) {
			AiTbForm form = (AiTbForm)formBean;
			if(is96){
				return Tools.trim(form.getYxdm()) + Tools.trim(form.getZydm());
			}else{
				return Tools.trim(form.getYxdm()) + Tools.trim(form.getZydm()) + Tools.trim(form.getZyz());
			}
		}else if(formBean instanceof JHxBean) {
			JHxBean form = (JHxBean)formBean;
			if(is96){
				return Tools.trim(form.getYxdm());
			}else{
				return Tools.trim(form.getYxdm()) + Tools.trim(form.getZyz());
			}
		}else if(formBean instanceof YfJHxBean) {
			YfJHxBean form = (YfJHxBean)formBean;
			if(is96){
				return Tools.trim(form.getYxdm());
			}else{
				return Tools.trim(form.getYxdm()) + Tools.trim(form.getZyz());
			}
		}else if(formBean instanceof LhyFormMakerHistory) {
			LhyFormMakerHistory form = (LhyFormMakerHistory)formBean;
			if(is96){
				return Tools.trim(form.getYxdm());
			}else{
				return Tools.trim(form.getYxdm()) + Tools.trim(form.getZydm()) + Tools.trim(form.getZyz());
			}
		}
		return null;
	}
	
	public static String getTBUniqueGroupOnlyKey(Object formBean, boolean is96) {
		
		if(formBean instanceof ZyzdForm) {
			ZyzdForm form = (ZyzdForm)formBean;
			if(is96){
				return Tools.trim(form.getYxdm()) + Tools.trim(form.getZydm());
			}else{
				return Tools.trim(form.getYxdm()) + Tools.trim(form.getZyz());
			}
		}else if(formBean instanceof LhyForm) {
			LhyForm form = (LhyForm)formBean;
			if(is96){
				return Tools.trim(form.getYxdm()) + Tools.trim(form.getZydm());
			}else{
				return Tools.trim(form.getYxdm()) + Tools.trim(form.getZyz());
			}
		}else if(formBean instanceof JHBean) {
			JHBean form = (JHBean)formBean;
			if(is96){
				return Tools.trim(form.getYxdm()) + Tools.trim(form.getZydm());
			}else{
				return Tools.trim(form.getYxdm()) + Tools.trim(form.getZyz());
			}
		}else if(formBean instanceof YfJHBean) {
			YfJHBean form = (YfJHBean)formBean;
			if(is96){
				return Tools.trim(form.getYxdm()) + Tools.trim(form.getZydm());
			}else{
				return Tools.trim(form.getYxdm()) + Tools.trim(form.getZyz());
			}
		}else if(formBean instanceof AiTbForm) {
			AiTbForm form = (AiTbForm)formBean;
			if(is96){
				return Tools.trim(form.getYxdm()) + Tools.trim(form.getZydm());
			}else{
				return Tools.trim(form.getYxdm()) + Tools.trim(form.getZyz());
			}
		}else if(formBean instanceof YfJHxBean) {
			YfJHxBean form = (YfJHxBean)formBean;
			if(is96){
				return Tools.trim(form.getYxdm());
			}else{
				return Tools.trim(form.getYxdm()) + Tools.trim(form.getZyz());
			}
		}else if(formBean instanceof JHxBean) {
			JHxBean form = (JHxBean)formBean;
			if(is96){
				return Tools.trim(form.getYxdm());
			}else{
				return Tools.trim(form.getYxdm()) + Tools.trim(form.getZyz());
			}
		}else if(formBean instanceof LhyFormMakerHistory) {
			LhyFormMakerHistory form = (LhyFormMakerHistory)formBean;
			if(is96){
				return Tools.trim(form.getYxdm());
			}else{
				return Tools.trim(form.getYxdm()) + Tools.trim(form.getZyz());
			}
		}
		
		return null;
		
		
		
	}
	
	public static String formatToScale(float x, int size) {
		BigDecimal number = new BigDecimal(x);
		BigDecimal result = number.setScale(size, RoundingMode.CEILING);
		return result.toString();
	}
	
	public static String formatToThreeScale(float x) {
		return formatToScale(x, 3);
	}
	
	public static float parseToScale(float x, int size) {
		BigDecimal number = new BigDecimal(x);
		BigDecimal result = number.setScale(size, RoundingMode.CEILING);
		return result.floatValue();
	}
	
	public static int getLqrs(int jhs, int lqrs) {
		return jhs > 0 ? jhs : lqrs;
	}
	
	public static String get4DigitPassword() {
		String rd = (String.valueOf(Math.random()).substring(3))+(String.valueOf(Math.random()).substring(3))+(String.valueOf(Math.random()).substring(3));
		String passwd = "";
		for(int k = 0;k<rd.length();k++) {
			if(rd.charAt(k) == '0' || rd.charAt(k) == '4') {
				continue;
			}
			passwd += rd.charAt(k);
			if(passwd.length() > 3) {
				break;
			}
		}
		return passwd;
	}
	
	public static String get5DigitPassword() {
		String rd = (String.valueOf(Math.random()).substring(3))+(String.valueOf(Math.random()).substring(3))+(String.valueOf(Math.random()).substring(3));
		String passwd = "";
		for(int k = 0;k<rd.length();k++) {
			if(rd.charAt(k) == '0' || rd.charAt(k) == '4') {
				continue;
			}
			passwd += rd.charAt(k);
			if(passwd.length() > 4) {
				break;
			}
		}
		return passwd;
	}
	
	public static String get6DigitPassword() {
		String rd = (String.valueOf(Math.random()).substring(3))+(String.valueOf(Math.random()).substring(3))+(String.valueOf(Math.random()).substring(3));
		String passwd = "";
		for(int k = 0;k<rd.length();k++) {
			if(rd.charAt(k) == '0' || rd.charAt(k) == '4') {
				continue;
			}
			passwd += rd.charAt(k);
			if(passwd.length() > 5) {
				break;
			}
		}
		return passwd;
	}
	
	public static HashSet<String> parseToSet(String str, String reg){
		String[] strArray = str.split(reg);
		HashSet<String> sets = new HashSet<>();
		for(String x : strArray) {
			sets.add(x);
		}
		return sets;
	}
	
	public static String viewYxTag(List<String> tags){
		if(tags == null || tags.size() == 0) {
			return "-";
		}else {
			String str = "";
			for(String x : tags) {
				str += x + ",";
			}
			return str.substring(0,str.length() - 1);
		}
	}
	
	public static String viewScore(int score){
		if(score <= 0 || score > 1999999) {
			return "-";
		}else {
			return String.valueOf(score);
		}
	}
	
	public static String viewScoreHtml(int score, int user_score_cj){
		if(score <= 0 || score > 1999999) {
			return "-";
		}else {
			if(score >= user_score_cj) {
				return "<span style='color:red;font-size:12px;'>"+score+"<b style='margin-left:1px;'>("+(user_score_cj - score)+")</b></span>";
			}else {
				return "<span style='color:green;font-size:12px;'>"+score+"<b style='margin-left:1px;'>("+(user_score_cj - score)+")</b></span>";
			}
		} 
	}
	
	public static String viewWcHtml(int wc, int user_score_wc){
		if(wc <= 0 || wc > 1999999) {
			return "-";
		}else {
			if(wc <= user_score_wc) {
				return "<span style='color:red;font-size:12px;'>"+wc+"</span>";
			}else {
				return "<span style='color:green;font-size:12px;'>"+wc+"</span>";
			}
		} 
	}
	
	public static String viewScore(String score){
		int score_int = Tools.getInt(Tools.trim(score));
		if(score_int <= 0) {
			return "-";
		}else {
			return Tools.trim(score);
		}
	}
	
	public static String ViewInvitationType(int f) {
		String str = "激活后24小时有效";
		if(f == 1) {
			str = "激活后24小时有效";
		}else if(f == 2) {
			str = "激活后一直有效，特定月份除外";
		}else if(f == 3) {
			str = "有效期至高考当年8月30日";
		}
		return str;
	}
	
	public static String parseToInt(float f) {
		int i1 = (int) f;
		return String.valueOf(i1);
	}
	
	//补齐5位整数，不足前面加0
	public static String formatNumberWith4digit(int no) {
		if(no > 9999) {
			return String.valueOf(no);
		}
		String noView = "0000000" + no;
		return noView.substring(noView.length() - 4);
	}
	
	public static String formatNumberWith4digit(String no) {
		if(no.length() > 4) {
			return String.valueOf(no);
		}
		String noView = "0000000" + no;
		return noView.substring(noView.length() - 4);
	}
	
	public static String getDate3(Date dt) {
		try {
			DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
			return df.format(dt);
		} catch (Exception ex) {
			return null;
		}
	}
	
	public static Date getZyzdYearEndTime() {
		try {
			DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Calendar cal = Calendar.getInstance();
			int CUR_MONTH = cal.get(Calendar.MONTH) + 1;
			int CUR_YEAR = cal.get(Calendar.YEAR);
			//System.out.println(CUR_MONTH + " , " + CUR_YEAR);
			if(CUR_MONTH > 8) {
				return df.parse((CUR_YEAR + 1) + "-09-30 23:59:59");
			}else {
				return df.parse((CUR_YEAR) + "-09-30 23:59:59");
			}
		}catch(Exception ex) {
			return new Date();
		}
	}
	
	public static String trim(String str) {
		return (str == null) ? "" : str.trim();
	}
	
	public static String replaceX27(String str) {
		String strTemp = (str == null) ? "" : str.replaceAll("'", "&#x27;");
		if(strTemp.endsWith("\\")) {
			strTemp = strTemp.substring(0, strTemp.length() - 1);
		}
		return strTemp;
	}
	
	public static String getSplitUpperCaseStr(String str) {
		if(Tools.isEmpty(str)) {
			return str;
		}
		str = str.replaceAll("[A-Z]", "_$0");
        return str.replaceAll("_", " ").trim();
	}
	
	
	public static String trimAndDeleteDefaultValue(String str) {
		return (str == null) ? "" : str.replace("不限", "").trim();
	}

	public static boolean isEmpty(String str) {
		return (trim(str).length() == 0);
	}
	
	public static boolean isCardTrial(com.career.db.CardBean card) {
		if(card == null) {
			return false;
		}
		
		boolean trial = card.getSysInd().equals("TRIAL");
		if(trial) {
			if(card.getExpire() == null) {
				return true;
			}
			if(card.getExpire().before(new Date())) {
				return true;
			}else {
				return false;
			}
		}
		return false;
	}

	public static boolean isCardTrialOrg(com.career.db.CardBean card) {
		if(card == null || card.getSysInd() == null) {
			return false;
		}
		return card.getSysInd().equals("TRIAL");
	}
	
	public static boolean isCardSCA(com.career.db.CardBean card) {
		if(card == null || card.getSysInd() == null) {
			return false;
		}
		return card.getSysInd().startsWith("SCA");
	}
	
	public static boolean isCardShare(com.career.db.CardBean card) {
		if(card == null || card.getSysInd() == null) {
			return false;
		}
		return card.getSysInd().startsWith("SHARE");
	}
	
	public static boolean isCardJzy(com.career.db.CardBean card) {
		if(card == null || card.getSysInd() == null) {
			return false;
		}
		return card.getSysInd().equals("FJ");
	}
	
	public static boolean isCardDwf(com.career.db.CardBean card) {
		if(card == null || card.getSysInd() == null) {
			return false;
		}
		return card.getSysInd().equals("FD");
	}
	
	public static boolean isCardFormal(com.career.db.CardBean card) {
		if(card == null || card.getSysInd() == null) {
			return false;
		}
		return card.getSysInd().startsWith("F");
	}

	public static int getInt(String str) {
		try {
			return Integer.parseInt(str.trim());
		} catch (Exception ex) {
			return -1;
		}
	}
	
	public static String getPNstr(int a, int b) {
		if(a <= 0 || b <= 0 || a == b) {
			return "-";
		}else {
			if(a > b) {
				return "+" + (a - b);
			}else {
				return "-" + (b - a);
			}
		}
	}
	
	public static int getIntWithDefaultZero(String str) {
		try {
			return Integer.parseInt(str.trim());
		} catch (Exception ex) {
			return 0;
		}
	}
	
	public static float getFloat(String str) {
		try {
			return Float.parseFloat(str.trim());
		} catch (Exception ex) {
			return -1f;
		}
	}
	
	public static String getDate4(Date dt) {
		try {
			DateFormat df = new SimpleDateFormat("MM-dd");
			return df.format(dt);
		} catch (Exception ex) {
			return null;
		}
	}
	
	public static String getDate6(Date dt) {
		try {
			DateFormat df = new SimpleDateFormat("MM-dd HH:mm");
			return df.format(dt);
		} catch (Exception ex) {
			return null;
		}
	}
	
	public static String getDate5(Date dt) {
		try {
			DateFormat df = new SimpleDateFormat("HH:mm");
			return df.format(dt);
		} catch (Exception ex) {
			return null;
		}
	}
	
	public static List<String> getXkList(String provinceName){
		List<String> xkList = null;
		if("上海北京海南天津山东".indexOf(provinceName) != -1){
			xkList = CCache.CC_XK_LIST_63;
		}else if("浙江".indexOf(provinceName) != -1){
			xkList = CCache.CC_XK_LIST_73;
		}else{
			xkList = CCache.CC_XK_LIST_312;
		}
		return xkList;
	}

	public static String getDate2(Date dt) {
		try {
			DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
			return df.format(dt);
		} catch (Exception ex) {
			return null;
		}
	}
	
	/**
	 * 把给定日期字符串转为当日零点
	 * @param dt
	 * @return
	 */
	public static Date parseDateWithZeroTime(String yyyyMMdd) {
		try {
			DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			return df.parse(yyyyMMdd + " 00:00:00");
		} catch (Exception ex) {
			return null;
		}
	}
	
	/**
	 * 把给定日期字符串转为当日结束时间
	 * @param dt
	 * @return
	 */
	public static Date parseDateWithEndDayTime(String yyyyMMdd) {
		try {
			DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			return df.parse(yyyyMMdd + " 23:59:59");
		} catch (Exception ex) {
			return null;
		}
	}
	
	public static String getDateForMonth(Date dt) {
		try {
			DateFormat df = new SimpleDateFormat("yyyyMM");
			return df.format(dt);
		} catch (Exception ex) {
			return null;
		}
	}
	
	public static String getDate(Date dt) {
		try {
			DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
			return df.format(dt);
		} catch (Exception ex) {
			return null;
		}
	}
	
	public static String getHourDate(Date dt) {
		try {
			DateFormat df = new SimpleDateFormat("yyyyMMddHH");
			return df.format(dt);
		} catch (Exception ex) {
			return null;
		}
	}
	
	public static String getSecondDate(Date dt) {
		try {
			DateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
			return df.format(dt);
		} catch (Exception ex) {
			return null;
		}
	}
	
	public static String getSecondDateWithSSS(Date dt) {
		try {
			DateFormat df = new SimpleDateFormat("yyyyMMddHHmmssSSS");
			return df.format(dt);
		} catch (Exception ex) {
			return null;
		}
	}

	public static String getPercent(String str) {
		try {
			NumberFormat numberFormat = NumberFormat.getInstance();
			numberFormat.setMaximumFractionDigits(2);
			String result = numberFormat.format(Double.parseDouble(str) * 100.0D);
			if(result.indexOf(".") != -1) {
				return String.valueOf(result.substring(0, result.indexOf("."))) + "%";
			}else {
				return String.valueOf(result) + "%";
			}
		} catch (Exception ex) {
			return "-";
		}
	}

	public static String view(String str) {
		return isEmpty(str) ? "-" : str.trim();
	}
	
	public static String viewWithNoDefatult(String str) {
		return isEmpty(str) ? "" : str.trim();
	}
	
	public static String viewMajorDesc(String zymc, String zymc_org) {
		if(Tools.isEmpty(zymc_org)) {
			return "";
		}
		if(zymc.equalsIgnoreCase(zymc_org)) {
			return "";
		}
		return zymc.substring(zymc_org.length());
	}
	
	public static String viewForZyz(String str) {
		return isEmpty(str) ? "01" : str.trim();
	}
	
	public static String viewForZdf(int zdf) {
		return zdf <= 0 ? "-" : String.valueOf(zdf);
	}
	public static String viewForZdfwc(int zdfwc) {
		return zdfwc <= 0 ? "-" : String.valueOf(zdfwc);
	}
	
	public static String view3(String str) {
		String x = isEmpty(str) ? "-" : str.trim();
		if(x.length() > 5) {
			return x.substring(0,5) + "*";
		}else {
			return x;
		}
	}
	
	public static String view4(String str) {
		String x = isEmpty(str) ? "-" : str.trim();
		if(x.length() > 15) {
			return "***" + x.substring(5);
		}else {
			return x;
		}
	}
	
	public static String viewLast4(String str) {
		String x = isEmpty(str) ? "-" : str.trim();
		if(x.length() > 4) {
			return "**" + x.substring(str.length() - 4);
		}else {
			return x;
		}
	}
	
	public static String viewForZeorAndNull(String str) {
		String result = isEmpty(str) ? "-" : str.trim();
		return result.equals("0")?"-":result;
	}
	
	
	public static void main(String[] args) {
		System.out.println(getOffsetDT(new Date(), 1));
		System.out.println(getOffsetDT( 1));
	}
	
	public static String view2(String str) {
		String xx = isEmpty(str) ? "-" : str.trim();
		if(xx.length() > 5) {
			return xx.substring(0,3) + "*" + xx.substring(4);
		}else {
			return xx;
		}
	}
	
	public static String view23(String str) {
		String xx = isEmpty(str) ? "-" : str.trim();
		if(xx.length() > 5) {
			return xx.substring(0,2) + "***" + xx.substring(str.length()-3); 
		}else {
			return xx;
		}
	}
	 
	public static void println(String str) {
		System.out.println(str);  
	}
	
	public static void printlnRanking(String str) {
		//System.out.println(">>>>>>>>>>>>>>>>>>>>"+str); 
	}
	
	public static void println(StringBuffer str) {
		//System.out.println(str);
	}
	
	public static String view24(String str) {
		String xx = isEmpty(str) ? "-" : str.trim();
		if(xx.length() > 3) {
			return "****" + xx.substring(str.length()-3); 
		}else {
			return xx;
		}
	}

	public static int getWCPercent(String wc, int max) {
		int wcInt = getInt(wc);
		if (wcInt <= 0)
			return wcInt;
		int val = wcInt * 100 / max;
		if (val <= 0)
			val = 1;
		if (val > 100)
			val = 100;
		return val;
	}

	public static long timeSpan(Date from, Date to) {
		return (to.getTime() - from.getTime()) / 60000L;
	}
	
	public static Date getOffsetDT(int dayCnt) {
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.DATE, dayCnt);
		return cal.getTime();
	}
	
	public static Date getOffsetDT(Date from, int dayCnt) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(from);
		cal.add(Calendar.DATE, dayCnt);
		return cal.getTime();
	}
	
	public static Date getOffsetMinute(Date from, int minCnt) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(from);
		cal.add(Calendar.MINUTE, minCnt);
		return cal.getTime();
	}
	
	public static Date getOffsetSecond(Date from, int secCnt) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(from);
		cal.add(Calendar.SECOND, secCnt);
		return cal.getTime();
	}
	
	public static Date getOffsetMinSecond(Date from, int minsecCnt) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(from);
		cal.add(Calendar.MILLISECOND, minsecCnt);
		return cal.getTime();
	}

	public static String viewForLimitLength(String str) {
		return viewForLimitLength(str, 7);
	}

	public static String hideMiddle(String str) {
		if (isEmpty(str))
			return "-";
		str = str.trim();
		if (str.length() <= 5) {
			return "**" + str.substring(2);
		}

		return String.valueOf(str.substring(0, 1)) + "***" + str.substring(4);
	}

	public static String hidePre(String str) {
		if (isEmpty(str))
			return "后台查看";
		str = str.trim();
		if (str.length() <= 9) {
			return "后台查看";
		}

		return String.valueOf("****" + str.substring(5));
	}

	public static String getPCShort(String str) {
		if (isEmpty(str))
			return "-";
		str = str.trim();
		if(str.equals("本科提前批")) {
			return "本科提";
		}else if(str.equals("专科提前批")) {
			return "专科提";
		}else {
			return str;
		}
	}
	
	public static String setPCShortColor(String str) {
		if("本提".equals(str)) {
			return "<span style='color:blue'>"+str+"</span>";
		}else if("一本".equals(str)) {
			return "<span style='color:red;font-weight:bold;'>"+str+"</span>";
		}
		return str;
	}

	public static String viewForLimitLength(String str, int length) {
		if (isEmpty(str))
			return "-";
		str = str.trim();
		if (str.length() <= length)
			return str;
		return String.valueOf(str.substring(0, length)) + "*";
	}
	
	public static String viewForLimitLengthAndKeepStar(String str, int length) {
		if (isEmpty(str))
			return "-";
		str = str.trim();
		if (str.length() <= length)
			return str;
		String star = "";
		for(int i=0;i<(str.length() - length);i++) {
			star += "*";
		}
		return String.valueOf(star + str.substring(str.length()-length));
	}
	
	public static String viewForLimitLengthForCompany(String str, int length) {
		if (isEmpty(str))
			return "-";
		str = str.trim();
		String temp = str;
		if (str.length() > length) {
			temp = String.valueOf(str.substring(0, length)) + "...";
		}
		if(temp.length() < 3) {
			return temp;
		}
		return "**" + temp.substring(3);
	}

	public static boolean contain985211syl(String is985, String is211, String issyl) {
		if (isEmpty(is985) && isEmpty(is211) && isEmpty(issyl))
			return false;
		return true;
	}

	public static String viewFor985211syl(String is985, String is211, String issyl) {
		if (!contain985211syl(is985, is211, issyl))
			return "-";
		if (isEmpty(is985)) {
			if (isEmpty(is211))
				return issyl;
			return String.valueOf(is211) + "/" + issyl;
		}
		if (isEmpty(is211)) {
			if (isEmpty(issyl))
				return is985;
			return String.valueOf(is985) + "/" + issyl;
		}
		if (isEmpty(issyl))
			return String.valueOf(is985) + "/" + is211;
		return String.valueOf(is985) + "/" + is211 + "/" + issyl;
	}

	public static String containsTag(String str) {
		return isEmpty(str) ? "-" : str.trim();
	}

	public static HashSet<String> getSetByStrSplit(String str) {
		if(Tools.isEmpty(str)) {
			return new HashSet<>();
		}
		return getSetByStrSplit(str, ",");
	}

	public static HashSet<String> getSetByStrSplit(String str, String split) {
		HashSet<String> hs = new LinkedHashSet<>();
		if(Tools.isEmpty(str)) {
			return hs;
		}
		String[] st = trim(str).split(split);
		for (int i = 0; i < st.length; i++) {
			if(!isEmpty(st[i])) {
				hs.add(st[i]);
			}
		}
		return hs;
	}

	public static HashSet<String> convertSetToHashSet(Set sets) {
		HashSet<String> hs = new HashSet<>();
		Iterator<String> it = sets.iterator();
		while (it.hasNext())
			hs.add(it.next());
		return hs;
	}

	public static String getZYStar(String zymc) {
		if (isEmpty(zymc))
			return "***";
		zymc = getZyOffName(zymc);
		int cnt = CCache.CC_ZY_EXT.containsKey(zymc) ? ((MarjorBean) CCache.CC_ZY_EXT.get(zymc)).getTjxj() : 3;
		switch (cnt) {
		case 0:
			return "⭐";
		case 1:
			return "⭐";
		case 2:
			return "⭐⭐";
		case 3:
			return "⭐⭐⭐";
		case 4:
			return "⭐⭐⭐⭐";
		case 5:
			return "⭐⭐⭐⭐⭐";
		}
		return "⭐⭐⭐";
	}

	public static String getZYJs(String zymc) {
		MarjorBean mb = (MarjorBean) CCache.CC_ZY_EXT.get(zymc);
		return (mb == null) ? null : mb.getJs();
	}

	public static String getZYHot(String zymc) {
		zymc = getZyOffName(zymc);
		MarjorBean mb = (MarjorBean) CCache.CC_ZY_EXT.get(zymc);
		String zylr = (mb == null) ? null : mb.getZylr();
		if (isEmpty(zylr)) {
			String star = getZYStar(zymc);
			if (star.length() <= 2)
				return "冷门";
			if (star.length() < 4)
				return "普通";
			return "热门";
		}
		return zylr;
	}

	public static String getZyOffName(String str) {
		String s = trim(str);
		
		if(s.startsWith("中国石油大学") || s.startsWith("中国地质大学") || s.startsWith("中国人民公安大学")) {
			return s;
		}
		
		if (s.indexOf("(") == -1) {
			if (s.indexOf("（") == -1) {
				return s;
			} else {
				return s.substring(0, s.indexOf("（"));
			}
		}
		return s.substring(0, s.indexOf("("));
	}
	
	public static String getZyOffName2(String str) {
		String s = trim(str);
		
		if (s.indexOf("(") == -1) {
			if (s.indexOf("（") == -1) {
				return s;
			} else {
				return s.substring(0, s.indexOf("（"));
			}
		}
		return s.substring(0, s.indexOf("("));
	}
	
	public static String getZyOffNameAll(String str) {
		String s = trim(str);
		
		if (s.indexOf("(") == -1) {
			if (s.indexOf("（") == -1) {
				return s;
			} else {
				return s.substring(0, s.indexOf("（"));
			}
		}
		return s.substring(0, s.indexOf("("));
	}

	public static String getSQLQueryin(HashSet<String> sets) {
		if (sets == null || sets.size() == 0)
			return "";
		String str = "";
		Iterator<String> it = sets.iterator();
		int index = 1;
		while (it.hasNext()) {
			String temp = it.next();
			if (!isEmpty(temp)) {
				str = String.valueOf(str) + "'" + temp + "'";
				if (index != sets.size())
					str = String.valueOf(str) + ",";
			}
			index++;
		}
		return str;
	}
	
	public static String getSQLQueryinInt(HashSet<Integer> sets) {
		if (sets == null || sets.size() == 0)
			return "";
		String str = "";
		Iterator<Integer> it = sets.iterator();
		int index = 1;
		while (it.hasNext()) {
			Integer temp = it.next();
			if (temp != null) {
				str = String.valueOf(str) + "" + temp + "";
				if (index != sets.size())
					str = String.valueOf(str) + ",";
			}
			index++;
		}
		return str;
	}
	
	public static String getIpAddr(javax.servlet.http.HttpServletRequest request) {  
		return request.getRemoteAddr();   
    }  
	public static String getIpAddr(jakarta.servlet.http.HttpServletRequest request) {  
		return request.getRemoteAddr();   
    } 
	
	
	
	// 得到测评结果组合，用于拉取专业
	public static HashMap<Integer, List<String>> getHollandResultMap(String result) {  
		HashMap<String, HollandCpItem> RESULT_MAP = getHollandCpItemMap(result);
		List<HollandCpItem> list = new ArrayList<>(RESULT_MAP.values());

		Collections.sort(list, new Comparator<HollandCpItem>() {
            public int compare(HollandCpItem o1, HollandCpItem o2) {
            	return o2.getTotal() - o1.getTotal();//降序
            }
        });
		
		HollandCpItem c1 = list.get(0);
		HollandCpItem c2 = list.get(1);
		HollandCpItem c3 = list.get(2);
		HollandCpItem c4 = list.get(3);
		HollandCpItem c5 = list.get(4);
		HollandCpItem c6 = list.get(5);
		
		HashMap<Integer, List<String>> RESULT_NEW_MAP = new HashMap<>();
		RESULT_NEW_MAP.put(5, new ArrayList<>(Arrays.asList(c1.getCode()+c2.getCode(), c1.getCode()+c3.getCode(), c1.getCode()+c4.getCode())));
		//RESULT_NEW_MAP.put(4, new ArrayList<>(Arrays.asList(c2.getCode()+c3.getCode())));
		//RESULT_NEW_MAP.put(3, new ArrayList<>(Arrays.asList(c2.getCode()+c4.getCode())));
		//RESULT_NEW_MAP.put(2, new ArrayList<>(Arrays.asList(c3.getCode()+c1.getCode())));
		
		return RESULT_NEW_MAP;
    }
	
	//得到测评结果，前2个
	public static List<String> getHollandResult(String result) {  
		List<String> list = new ArrayList<>();
		String[] result_array = result.split(",");
		for(int i=6;i<result_array.length;i++) {
			list.add(result_array[i]);
		}
		return list;
    }  
	
	//得到测评每一个项的值
	public static HashMap<String, HollandCpItem> getHollandCpItemMap(String result) {  
		HashMap<String, HollandCpItem> MAP = new LinkedHashMap<>();
		String[] result_array = result.split(",");
		for(int i=0;i<result_array.length;i++) {
			if(i >= 6) {
				break;
			}
			String each = result_array[i];
			String code = each.substring(0,1);
			String[] values = each.substring(1).split("\\/");
			HollandCpItem item = new HollandCpItem(code, Tools.getInt(values[0]), Tools.getInt(values[1]), Tools.getInt(values[0]) - Tools.getInt(values[1]));
			MAP.put(code, item);
		}
		return MAP;
    }
	
	public static String viewXK(String xkStr) {
		StringBuffer sb = new StringBuffer();
		String[] arrays = xkStr.split("\\+");
		for (int i=0;i<arrays.length;i++) {
			String temp = arrays[i];
			if (temp.equals("物理")) {
				sb.append("物");
				continue;
			}
			if (temp.equals("历史")) {
				sb.append("史");
				continue;
			}
			if (temp.equals("理科")) {
				sb.append("理");
				continue;
			}
			if (temp.equals("文科")) {
				sb.append("文");
				continue;
			}
			if (temp.equals("地理")) {
				sb.append("地");
				continue;
			}
			if (temp.equals("生物")) {
				sb.append("生");
				continue;
			}
			if (temp.equals("政治")) {
				sb.append("政");
				continue;
			}
			if (temp.equals("化学")) {
				sb.append("化");
				continue;
			}
			if (temp.equals("技术")) {
				sb.append("技");
				continue;
			}
		}
		return sb.toString();
	}
	
	public static String viewCpName(String cp_name) {
		if(cp_name.equals(ZyzdCache.CP_NAME_HOLLAND)) {
			return "霍兰德兴趣测评";
		}else if(cp_name.equals(ZyzdCache.CP_NAME_MBTI)) {
			return "MBTI性格评估";
		}
		return "";
	}


	public static HashSet<String> convertXK(String xk) {
		LinkedHashSet<String> sets = new LinkedHashSet<>(); 
		String[] arrays = xk.split("\\+");
		for (int i=0;i<arrays.length;i++) {
			String temp = arrays[i];
			if (temp.equals("物理")) {
				sets.add("物");
				continue;
			}
			if (temp.equals("历史")) {
				sets.add("历");
				sets.add("史");
				continue;
			}
			if (temp.equals("理科")) {
				sets.add("理");
				continue;
			}
			if (temp.equals("文科")) {
				sets.add("文");
				continue;
			}
			if (temp.equals("地理")) {
				sets.add("地"); 
				continue;
			}
			if (temp.equals("生物")) {
				sets.add("生");
				continue;
			}
			if (temp.equals("政治")) {
				sets.add("政");
				continue;
			}
			if (temp.equals("化学")) {
				sets.add("化");
				continue;
			}
			if (temp.equals("技术")) {
				sets.add("技");
				continue;
			}
		}
		return sets;
	}
	
	
	/**
	 * 
	 * 处理 专科（高职）问题
	 * 
	 * @param cc
	 * @return
	 * 
	 */
	public static String getCc(String cc) {
	  
	    if (!isEmpty(cc) && cc.contains("专科")) {
	        return "专科（高职）";
	    }
	    return cc;
	}

}
