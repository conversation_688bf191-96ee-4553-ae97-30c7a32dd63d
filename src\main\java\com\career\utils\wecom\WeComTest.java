package com.career.utils.wecom;

import com.career.utils.Tools;
import com.career.utils.wecom.config.WeComConfig;
import com.career.utils.wecom.service.WeComSyncService;
import com.career.utils.wecom.service.WeComGroupChatService;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;


/**
 * 企业微信同步服务测试类
 * 
 * 主要测试场景：
 * 1. 全量同步测试
 * 2. 增量同步测试  
 * 3. 进度监控测试
 * 4. 异常恢复测试
 * 5. 配置验证测试
 * 6. 增量同步群和群组关系测试 (新增)
 * 
 * 使用方法：
 * - 无参数运行：根据配置自动选择同步模式
 * - java WeComTest full：执行全量同步测试
 * - java WeComTest incremental：执行增量同步测试
 * - java WeComTest group：执行增量同步群和群组关系测试
 * - java WeComTest suite：执行完整测试套件
 * - java WeComTest quick：执行快速测试
 * 
 * "ww2a382ee871b4bf4e";  "e96WdSTGPGX3rcPohw2pG3VePVf10TDw4ebGBQoDlrc";  "lx2b"
 * 
 * 
 * "wwe6995c35531e2b93";  "m59ZtfwGf5ncdHFz-j6N7PeLNmup6FiCxXpOvJ1q80o";  "welh"
 * 
 *
 * 
 */
public class WeComTest {
    
    /**
     * 模拟从配置源获取配置值的方法
     * 在实际项目中，应该从properties文件、环境变量、配置中心等读取
     * @param key 配置项的键
     * @return 配置值
     */
    public static String getConfig(String key) {
        switch (key) {
            case "wecom.corpId":
                return "wwe6995c35531e2b93"; 
            case "wecom.contactSecret":
                return "m59ZtfwGf5ncdHFz-j6N7PeLNmup6FiCxXpOvJ1q80o"; 
            case "wecom.saasId":
                return "welh"; 
            default:
                return null;
        }
    }
    
    /**
     * 测试全量同步
     */
    public static void testFullSync() {
        Tools.println("\n=== 开始测试全量同步 ===");
        
        String corpId = getConfig("wecom.corpId");
        String corpSecret = getConfig("wecom.contactSecret");
        String saasId = getConfig("wecom.saasId");
        
        // 验证配置
        if (corpId == null || corpSecret == null || saasId == null) {
            Tools.println("配置信息不完整，请检查配置");
            return;
        }
        
        // 显示配置摘要
        Tools.println("当前配置:");
        Tools.println(WeComConfig.getConfigSummary());
        
        // 验证配置合理性
        String configValidation = WeComConfig.validateConfig();
        if (configValidation != null) {
            Tools.println("配置验证失败: " + configValidation);
            return;
        }
        
        try {
            // 执行全量同步
            Date startTime = new Date();
            WeComSyncService.SyncResult result = WeComSyncService.performFullSync(corpId, corpSecret, saasId);
            Date endTime = new Date();
            
            // 输出测试结果
            Tools.println("\n=== 全量同步测试结果 ===");
            Tools.println("会话ID: " + result.getSessionId());
            Tools.println("同步状态: " + (result.isSuccess() ? "成功" : "失败"));
            Tools.println("开始时间: " + formatDate(startTime));
            Tools.println("结束时间: " + formatDate(endTime));
            Tools.println("总耗时: " + (endTime.getTime() - startTime.getTime()) + "ms");
            
            if (!result.isSuccess()) {
                Tools.println("错误信息: " + result.getErrorMessage());
                Map<String, String> errors = result.getErrors();
                if (!errors.isEmpty()) {
                    Tools.println("详细错误:");
                    for (Map.Entry<String, String> error : errors.entrySet()) {
                        Tools.println("  " + error.getKey() + ": " + error.getValue());
                    }
                }
            }
            
            // 输出详细统计
            Tools.println("\n--- 数据同步统计 ---");
            Tools.println("内部联系人: 新增=" + result.getNewInternalContactCount() + 
                    ", 更新=" + result.getUpdatedInternalContactCount() + 
                    ", 删除=" + result.getDeletedInternalContactCount());
            Tools.println("外部联系人: 新增=" + result.getNewExternalContactCount() + 
                    ", 更新=" + result.getUpdatedExternalContactCount() + 
                    ", 删除=" + result.getDeletedExternalContactCount());
            Tools.println("客户群: 新增=" + result.getNewGroupCount() + 
                    ", 更新=" + result.getUpdatedGroupCount() + 
                    ", 删除=" + result.getDeletedGroupCount());
            Tools.println("群成员关系: 新增=" + result.getNewMemberCount() + 
                    ", 更新=" + result.getUpdatedMemberCount() + 
                    ", 删除=" + result.getDeletedMemberCount());
            Tools.println("跟进人关系: 新增=" + result.getNewFollowerCount() + 
                    ", 更新=" + result.getUpdatedFollowerCount() + 
                    ", 删除=" + result.getDeletedFollowerCount());
            
        } catch (Exception e) {
            Tools.println("全量同步测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试增量同步
     */
    public static void testIncrementalSync() {
        Tools.println("\n=== 开始测试增量同步 ===");
        
        String corpId = getConfig("wecom.corpId");
        String corpSecret = getConfig("wecom.contactSecret");
        String saasId = getConfig("wecom.saasId");
        
        try {
            // 执行增量同步
            Date startTime = new Date();
            WeComSyncService.SyncResult result = WeComSyncService.performIncrementalSync(corpId, corpSecret, saasId);
            Date endTime = new Date();
            
            // 输出测试结果
            Tools.println("\n=== 增量同步测试结果 ===");
            Tools.println("会话ID: " + result.getSessionId());
            Tools.println("同步模式: " + result.getSyncMode());
            Tools.println("同步状态: " + (result.isSuccess() ? "成功" : "失败"));
            Tools.println("开始时间: " + formatDate(startTime));
            Tools.println("结束时间: " + formatDate(endTime));
            Tools.println("总耗时: " + (endTime.getTime() - startTime.getTime()) + "ms");
            
            if (!result.isSuccess()) {
                Tools.println("错误信息: " + result.getErrorMessage());
            }
            
            // 比较增量和全量的效率差异
            Tools.println("\n--- 增量同步效率分析 ---");
            long duration = endTime.getTime() - startTime.getTime();
            int totalChanges = result.getNewInternalContactCount() + result.getUpdatedInternalContactCount() + 
                             result.getDeletedInternalContactCount() + result.getNewExternalContactCount() + 
                             result.getUpdatedExternalContactCount() + result.getDeletedExternalContactCount() +
                             result.getNewGroupCount() + result.getUpdatedGroupCount() + result.getDeletedGroupCount() +
                             result.getNewMemberCount() + result.getUpdatedMemberCount() + result.getDeletedMemberCount();
            
            Tools.println("总变更数量: " + totalChanges);
            Tools.println("平均处理速度: " + (totalChanges > 0 ? (duration / totalChanges) : 0) + "ms/条");
            
        } catch (Exception e) {
            Tools.println("增量同步测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试进度监控功能
     */
    public static void testProgressMonitoring() {
        Tools.println("\n=== 开始测试进度监控 ===");
        
        String corpId = getConfig("wecom.corpId");
        String corpSecret = getConfig("wecom.contactSecret");
        String saasId = getConfig("wecom.saasId");
        
        try {
            // 在新线程中执行同步，以便监控进度
            Thread syncThread = new Thread(() -> {
                WeComSyncService.performFullSync(corpId, corpSecret, saasId);
            });
            
            syncThread.start();
            
            // 监控进度
            String sessionId = null;
            int maxWaitTimes = 100; // 最多等待10秒寻找会话
            int waitCount = 0;
            
            // 等待会话创建
            while (sessionId == null && waitCount < maxWaitTimes) {
                Map<String, WeComSyncService.SyncProgress> activeSessions = WeComSyncService.getActiveSyncSessions();
                if (!activeSessions.isEmpty()) {
                    sessionId = activeSessions.keySet().iterator().next();
                    break;
                }
                
                try {
                    Thread.sleep(100);
                    waitCount++;
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
            
            if (sessionId != null) {
                Tools.println("找到同步会话: " + sessionId);
                Tools.println("开始监控进度...\n");
                
                // 监控进度直到完成
                while (true) {
                    WeComSyncService.SyncProgress progress = WeComSyncService.getSyncProgress(sessionId);
                    
                    if (progress == null) {
                        Tools.println("同步会话已结束");
                        break;
                    }
                    
                    String progressDesc = progress.getProgressDescription();
                    Date lastUpdate = progress.getLastUpdateTime();
                    
                    Tools.println("[" + formatTime(lastUpdate) + "] " + progressDesc + 
                            " (已处理: " + progress.getTotalProcessedItems() + " 项)");
                    
                    if ("COMPLETED".equals(progress.getCurrentPhase()) || "ERROR".equals(progress.getCurrentPhase())) {
                        Tools.println("同步完成，阶段: " + progress.getCurrentPhase());
                        break;
                    }
                    
                    try {
                        Thread.sleep(1000); // 每秒更新一次
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            } else {
                Tools.println("未能找到活跃的同步会话");
            }
            
            // 等待同步线程完成
            syncThread.join(30000); // 最多等待30秒
            
        } catch (Exception e) {
            Tools.println("进度监控测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    
    /**
     * 测试配置验证
     */
    public static void testConfigValidation() {
        Tools.println("\n=== 开始测试配置验证 ===");
        
        try {
            // 验证去重配置
            String dedupValidation = WeComConfig.validateConfig();
            if (dedupValidation == null) {
                Tools.println("✓ 去重配置验证通过");
            } else {
                Tools.println("✗ 去重配置验证失败: " + dedupValidation);
            }
            
            // 验证扩展配置
            String extValidation = WeComConfig.validateConfig();
            if (extValidation == null) {
                Tools.println("✓ 扩展配置验证通过");
            } else {
                Tools.println("✗ 扩展配置验证失败: " + extValidation);
            }
            
            // 显示完整配置摘要
            Tools.println("\n当前完整配置摘要:");
            Tools.println(WeComConfig.getConfigSummary());
            
            // 测试各个配置项
            Tools.println("\n=== 去重配置项详情 ===");
            Tools.println("去重配置:");
            Tools.println("  内部联系人去重: " + WeComConfig.isEnableInternalContactDedup());
            Tools.println("  外部联系人去重: " + WeComConfig.isEnableExternalContactDedup());
            Tools.println("  客户群去重: " + WeComConfig.isEnableGroupDedup());
            Tools.println("  群成员关系去重: " + WeComConfig.isEnableGroupMemberDedup());
            Tools.println("  跟进人关系去重: " + WeComConfig.isEnableFollowerDedup());
            
            Tools.println("\n基础配置:");
            Tools.println("  增量抓取模式: " + WeComConfig.isUseIncrementalApiFetch());
            Tools.println("  批处理大小: " + WeComConfig.getBatchSize());
            Tools.println("  最大重试次数: " + WeComConfig.getMaxRetryCount());
            Tools.println("  40096错误跳过重试: " + WeComConfig.isSkipRetryOn40096());
            Tools.println("  40096错误详细日志: " + WeComConfig.isLog40096Details());
            
            Tools.println("\n=== 扩展配置项详情 ===");
            Tools.println("同步模式配置:");
            Tools.println("  默认增量同步: " + WeComConfig.isDefaultSyncModeIncremental());
            Tools.println("  智能同步模式: " + WeComConfig.isEnableSmartSyncMode());
            Tools.println("  智能同步阈值: " + WeComConfig.getSmartSyncThreshold());
            
            Tools.println("\n性能配置:");
            Tools.println("  API调用间隔: " + WeComConfig.getApiCallIntervalMs() + "ms");
            Tools.println("  批处理间隔: " + WeComConfig.getBatchIntervalMs() + "ms");
            
            Tools.println("\n监控配置:");
            Tools.println("  进度监控: " + WeComConfig.isEnableProgressMonitoring());
            Tools.println("  进度更新频率: " + WeComConfig.getProgressUpdateFrequency());
            Tools.println("  详细阶段日志: " + WeComConfig.isEnableDetailedPhaseLogging());
            Tools.println("  控制台进度显示: " + WeComConfig.isEnableConsoleProgress());
            
            Tools.println("\n数据完整性配置:");
            Tools.println("  数据完整性检查: " + WeComConfig.isEnableDataIntegrityCheck());
            Tools.println("  同步后验证: " + WeComConfig.isEnablePostSyncValidation());
            Tools.println("  自动修复不一致: " + WeComConfig.isAutoFixInconsistency());
            
            Tools.println("\n会话管理配置:");
            Tools.println("  会话超时时间: " + WeComConfig.getSyncSessionTimeoutMs() / 1000 + "秒");
            Tools.println("  自动清理过期会话: " + WeComConfig.isAutoCleanupExpiredSessions());
            Tools.println("  最大并发会话数: " + WeComConfig.getMaxConcurrentSyncSessions());
            
            Tools.println("\n日志和调试配置:");
            Tools.println("  详细日志模式: " + WeComConfig.isEnableVerboseLogging());
            Tools.println("  API响应详情: " + WeComConfig.isLogApiResponseDetails());
            Tools.println("  数据库操作详情: " + WeComConfig.isLogDatabaseOperations());
            Tools.println("  生成详细报告: " + WeComConfig.isGenerateDetailedReport());
            
        } catch (Exception e) {
            Tools.println("配置验证测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    
    /**
     * 执行完整的测试套件
     */
    public static void runFullTestSuite() {
        Tools.println("=== 企业微信同步服务完整测试套件 ===");
        Tools.println("开始时间: " + formatDate(new Date()));
        
        try {
            // 1. 配置验证测试
            testConfigValidation();
            // 3. 进度监控测试（如果配置启用）
            if (WeComConfig.isEnableProgressMonitoring()) {
                testProgressMonitoring();
            }
            
            // 4. 增量同步测试
            testIncrementalSync();
            
            // 5. 全量同步测试
            testFullSync();
            
            Tools.println("\n=== 测试套件执行完成 ===");
            Tools.println("结束时间: " + formatDate(new Date()));
            
        } catch (Exception e) {
            Tools.println("测试套件执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 简单的同步测试（适合快速验证）
     */
    public static void runQuickTest() {
        Tools.println("=== 快速同步测试 ===");
        
        String corpId = getConfig("wecom.corpId");
        String corpSecret = getConfig("wecom.contactSecret");
        String saasId = getConfig("wecom.saasId");
        
        if (corpId == null || corpSecret == null || saasId == null) {
            Tools.println("配置信息不完整，无法执行测试");
            return;
        }
        
        try {
            // 执行增量同步（通常比全量同步更快）
            WeComSyncService.SyncResult result = WeComSyncService.performIncrementalSync(corpId, corpSecret, saasId);
            
            Tools.println("快速测试结果:");
            Tools.println("  同步状态: " + (result.isSuccess() ? "成功" : "失败"));
            Tools.println("  同步模式: " + result.getSyncMode());
            Tools.println("  会话ID: " + result.getSessionId());
            Tools.println("  耗时: " + result.getSyncDurationMs() + "ms");
            
            if (!result.isSuccess()) {
                Tools.println("  错误: " + result.getErrorMessage());
            }
            
        } catch (Exception e) {
            Tools.println("快速测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试增量同步群和群组关系
     * 专门测试WeComSyncService中的群组和群成员关系增量同步功能
     */
    public static void testIncrementalGroupAndMemberSync() {
        Tools.println("\n=== 开始测试增量同步群和群组关系 ===");
        
        String corpId = getConfig("wecom.corpId");
        String corpSecret = getConfig("wecom.contactSecret");
        String saasId = getConfig("wecom.saasId");
        
        // 验证配置
        if (corpId == null || corpSecret == null || saasId == null) {
            Tools.println("配置信息不完整，请检查配置");
            return;
        }
        
        // 显示当前配置摘要
        Tools.println("当前配置:");
        Tools.println("  企业ID: " + corpId);
        Tools.println("  SaaS ID: " + saasId);
        Tools.println("  增量同步跳过天数: " + WeComConfig.getIncrementalSyncSkipDays());
        Tools.println("  智能跳过优化: " + (WeComConfig.getIncrementalSyncSkipDays() > 0 ? "启用" : "禁用"));
        Tools.println("  详细阶段日志: " + WeComConfig.isEnableDetailedPhaseLogging());
        Tools.println("  进度监控: " + WeComConfig.isEnableProgressMonitoring());
        
        try {
            Date startTime = new Date();
            Tools.println("\n开始时间: " + formatDate(startTime));
            
            // 创建一个模拟的同步结果对象来收集统计信息
            WeComSyncService.SyncResult result = new WeComSyncService.SyncResult();
            result.setSessionId("test_group_member_" + System.currentTimeMillis());
            result.setSyncMode("INCREMENTAL_GROUP_MEMBER_TEST");
            result.setSyncStartTime(startTime);
            result.setSuccess(true);
            
            // 创建进度监控对象
            WeComSyncService.SyncProgress progress = new WeComSyncService.SyncProgress();
            
            Tools.println("\n=== 第一阶段：测试增量同步客户群 ===");
            
            // 通过反射调用WeComSyncService的私有方法syncGroups
            try {
                java.lang.reflect.Method syncGroupsMethod = WeComSyncService.class.getDeclaredMethod(
                    "syncGroups", String.class, String.class, String.class, boolean.class, 
                    WeComSyncService.SyncResult.class, WeComSyncService.SyncProgress.class);
                syncGroupsMethod.setAccessible(true);
                
                Tools.println("调用增量同步客户群方法...");
                syncGroupsMethod.invoke(null, corpId, corpSecret, saasId, true, result, progress);
                
                Tools.println("客户群增量同步完成:");
                Tools.println("  新增群数: " + result.getNewGroupCount());
                Tools.println("  更新群数: " + result.getUpdatedGroupCount());
                Tools.println("  删除群数: " + result.getDeletedGroupCount());
                
            } catch (Exception e) {
                Tools.println("客户群增量同步测试失败: " + e.getMessage());
                if (WeComConfig.isEnableVerboseLogging()) {
                    e.printStackTrace();
                }
                result.addError("GROUP_SYNC_TEST", e.getMessage());
            }
            
            // 等待一段时间，模拟真实场景
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            Tools.println("\n=== 第二阶段：测试增量同步群成员关系 ===");
            
            // 通过反射调用WeComSyncService的私有方法syncGroupMembers
            try {
                java.lang.reflect.Method syncGroupMembersMethod = WeComSyncService.class.getDeclaredMethod(
                    "syncGroupMembers", String.class, String.class, String.class, boolean.class, 
                    WeComSyncService.SyncResult.class, WeComSyncService.SyncProgress.class);
                syncGroupMembersMethod.setAccessible(true);
                
                Tools.println("调用增量同步群成员关系方法...");
                syncGroupMembersMethod.invoke(null, corpId, corpSecret, saasId, true, result, progress);
                
                Tools.println("群成员关系增量同步完成:");
                Tools.println("  新增成员关系: " + result.getNewMemberCount());
                Tools.println("  更新成员关系: " + result.getUpdatedMemberCount());
                Tools.println("  删除成员关系: " + result.getDeletedMemberCount());
                
            } catch (Exception e) {
                Tools.println("群成员关系增量同步测试失败: " + e.getMessage());
                if (WeComConfig.isEnableVerboseLogging()) {
                    e.printStackTrace();
                }
                result.addError("GROUP_MEMBER_SYNC_TEST", e.getMessage());
            }
            
            Date endTime = new Date();
            result.setSyncEndTime(endTime);
            
            // 输出测试结果汇总
            Tools.println("\n=== 增量同步群和群组关系测试结果汇总 ===");
            Tools.println("测试会话ID: " + result.getSessionId());
            Tools.println("开始时间: " + formatDate(startTime));
            Tools.println("结束时间: " + formatDate(endTime));
            Tools.println("总耗时: " + (endTime.getTime() - startTime.getTime()) + "ms");
            Tools.println("测试状态: " + (result.isSuccess() && result.getErrors().isEmpty() ? "成功" : "部分成功或失败"));
            
            Tools.println("\n--- 详细统计 ---");
            Tools.println("客户群同步:");
            Tools.println("  新增: " + result.getNewGroupCount());
            Tools.println("  更新: " + result.getUpdatedGroupCount());
            Tools.println("  删除: " + result.getDeletedGroupCount());
            
            Tools.println("群成员关系同步:");
            Tools.println("  新增: " + result.getNewMemberCount());
            Tools.println("  更新: " + result.getUpdatedMemberCount());
            Tools.println("  删除: " + result.getDeletedMemberCount());
            
            // 显示错误信息（如果有）
            if (!result.getErrors().isEmpty()) {
                Tools.println("\n--- 错误详情 ---");
                for (Map.Entry<String, String> error : result.getErrors().entrySet()) {
                    Tools.println("  " + error.getKey() + ": " + error.getValue());
                }
            }
            
            // 性能分析
            long totalDuration = endTime.getTime() - startTime.getTime();
            int totalOperations = result.getNewGroupCount() + result.getUpdatedGroupCount() + result.getDeletedGroupCount() +
                                result.getNewMemberCount() + result.getUpdatedMemberCount() + result.getDeletedMemberCount();
            
            Tools.println("\n--- 性能分析 ---");
            Tools.println("总操作数: " + totalOperations);
            Tools.println("平均处理时间: " + (totalOperations > 0 ? (totalDuration / totalOperations) : 0) + "ms/操作");
            
            // 增量同步优化效果分析
            if (WeComConfig.getIncrementalSyncSkipDays() > 0) {
                Tools.println("\n--- 增量同步优化效果 ---");
                Tools.println("智能跳过优化: 已启用 (阈值: " + WeComConfig.getIncrementalSyncSkipDays() + "天)");
                Tools.println("说明: 在时间阈值内且无版本变化的数据将被智能跳过，减少API调用");
                
                if (totalOperations == 0) {
                    Tools.println("结果: 所有数据都在时间阈值内，完全跳过了API调用，优化效果显著");
                } else {
                    Tools.println("结果: 检测到数据变化，执行了必要的同步操作");
                }
            } else {
                Tools.println("\n--- 增量同步优化效果 ---");
                Tools.println("智能跳过优化: 未启用");
                Tools.println("建议: 可以通过配置 incrementalSyncSkipDays 来启用智能跳过优化");
            }
            
        } catch (Exception e) {
            Tools.println("增量同步群和群组关系测试失败: " + e.getMessage());
            if (WeComConfig.isEnableVerboseLogging()) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 测试客户群列表API的分页功能
     */
    public static void testGroupChatListApi() {
        Tools.println("\n=== 测试客户群列表API ===");
        
        String corpId = getConfig("wecom.corpId");
        String corpSecret = getConfig("wecom.contactSecret");
        
        try {
            String testResult = WeComGroupChatService.testGroupChatListApi(corpId, corpSecret);
            Tools.println(testResult);
        } catch (Exception e) {
            Tools.println("测试客户群列表API时发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    // ==================== 辅助方法 ====================
    private static String formatDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }
    
    private static String formatTime(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
        return sdf.format(date);
    }
    
    /**
     * 主方法 - 可以直接运行测试
     */
    public static void main(String[] args) {
        if (args.length == 0) {
            if (WeComConfig.isDefaultSyncModeIncremental()) {
                Tools.println("根据配置使用默认增量同步模式");
                testIncrementalSync();
            } else {
                Tools.println("根据配置使用默认全量同步模式");
                testFullSync();
            }
        } else {
            String testType = args[0].toLowerCase();
            switch (testType) {
                case "full":
                    testFullSync();
                    break;
                case "incremental":
                    testIncrementalSync();
                    break;
                case "group":
                case "groupmember":
                case "incrementalgroup":
                    testIncrementalGroupAndMemberSync();
                    break;
                case "api":
                case "groupapi":
                case "testapi":
                    testGroupChatListApi();
                    break;
                case "progress":
                    testProgressMonitoring();
                    break;
                case "config":
                    testConfigValidation();
                    break;
                case "suite":
                    runFullTestSuite();
                    break;
                case "quick":
                default:
                    runQuickTest();
                    break;
            }
        }
    }
}
