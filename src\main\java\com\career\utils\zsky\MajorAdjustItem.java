package com.career.utils.zsky;

import java.util.Arrays;

public class MajorAdjustItem {

	private int tj_id;
	private int spe_id;
	private int school_id;
	private int year;
	private int province_id;
	private String recruit_number;
	private String school_name;
	private String special_code;
	private String special_name;	
	private String recruit_type_name;
	private String depart_name;
	private String province_name;
	private int is_view;
	private int is_ads;
	private int buy_view;
	private String area;
	
	
	public int getIs_ads() {
		return is_ads;
	}
	public void setIs_ads(int is_ads) {
		this.is_ads = is_ads;
	}
	public int getTj_id() {
		return tj_id;
	}
	public void setTj_id(int tj_id) {
		this.tj_id = tj_id;
	}
	public int getSpe_id() {
		return spe_id;
	}
	public void setSpe_id(int spe_id) {
		this.spe_id = spe_id;
	}
	public int getSchool_id() {
		return school_id;
	}
	public void setSchool_id(int school_id) {
		this.school_id = school_id;
	}
	public int getYear() {
		return year;
	}
	public void setYear(int year) {
		this.year = year;
	}
	public int getProvince_id() {
		return province_id;
	}
	public void setProvince_id(int province_id) {
		this.province_id = province_id;
	}
	public String getRecruit_number() {
		return recruit_number;
	}
	public void setRecruit_number(String recruit_number) {
		this.recruit_number = recruit_number;
	}
	public String getSchool_name() {
		return school_name;
	}
	public void setSchool_name(String school_name) {
		this.school_name = school_name;
	}
	public String getSpecial_code() {
		return special_code;
	}
	public void setSpecial_code(String special_code) {
		this.special_code = special_code;
	}
	public String getSpecial_name() {
		return special_name;
	}
	public void setSpecial_name(String special_name) {
		this.special_name = special_name;
	}
	public String getRecruit_type_name() {
		return recruit_type_name;
	}
	public void setRecruit_type_name(String recruit_type_name) {
		this.recruit_type_name = recruit_type_name;
	}
	public String getDepart_name() {
		return depart_name;
	}
	public void setDepart_name(String depart_name) {
		this.depart_name = depart_name;
	}
	public String getProvince_name() {
		return province_name;
	}
	public void setProvince_name(String province_name) {
		this.province_name = province_name;
	}
	public int getIs_view() {
		return is_view;
	}
	public void setIs_view(int is_view) {
		this.is_view = is_view;
	}
	public int getBuy_view() {
		return buy_view;
	}
	public void setBuy_view(int buy_view) {
		this.buy_view = buy_view;
	}
	public String getArea() {
		return area;
	}
	public void setArea(String area) {
		this.area = area;
	}
	
	public String generateSQL() {
		String SQL = "insert into career_university_major_adjust(school_id, adjust_year, tj_id, spe_id, province_id, recruit_number, special_code, special_name, recruit_type_name, depart_name, province_name, is_view, buy_view, area,is_ads) values(" + school_id + ", " + year
				+ ", " + tj_id + "," + spe_id + "," + province_id + ",'" + recruit_number + "','" + special_code + "','" + special_name + "','" + recruit_type_name +"', '" + depart_name + "', '"+province_name+"',"+is_view+","+buy_view+", '"+area+"',"+is_ads+");";
		return SQL;
	}
	
}
