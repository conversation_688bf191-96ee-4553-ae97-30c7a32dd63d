package com.career.db;

import com.career.utils.BaseBean;

public class LhyFormMakerHistory extends BaseBean {
	
	public static void main(String args[]) {
		LhyFormMakerHistory bean = new LhyFormMakerHistory();
		printBeanProperties(bean);
	}
	
	
    private int id;
    private String historyId;
    private String batchId;
    private String batchIdOrg;
    private String yxmc;
    private String yxmcOrg;
    private String yxbz;
    private String yxdm;
    private String zyz;
    private String zymc;
    private String zymcOrg;
    private String zybz;
    private String zydm;
    private int zdfA;
    private int zdfB;
    private int zdfC;
    private int zdfwcA;
    private int zdfwcB;
    private int zdfwcC;
    private int pjfA;
    private int pjfB;
    private int pjfC;
    private int pjfwcA;
    private int pjfwcB;
    private int pjfwcC;
    private int seqNoZy;
    private int seqNoYx;
    
    
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getHistoryId() {
		return historyId;
	}
	public void setHistoryId(String historyId) {
		this.historyId = historyId;
	}
	public String getBatchId() {
		return batchId;
	}
	public void setBatchId(String batchId) {
		this.batchId = batchId;
	}
	public String getBatchIdOrg() {
		return batchIdOrg;
	}
	public void setBatchIdOrg(String batchIdOrg) {
		this.batchIdOrg = batchIdOrg;
	}
	public String getYxmc() {
		return yxmc;
	}
	public void setYxmc(String yxmc) {
		this.yxmc = yxmc;
	}
	public String getYxmcOrg() {
		return yxmcOrg;
	}
	public void setYxmcOrg(String yxmcOrg) {
		this.yxmcOrg = yxmcOrg;
	}
	public String getYxbz() {
		return yxbz;
	}
	public void setYxbz(String yxbz) {
		this.yxbz = yxbz;
	}
	public String getYxdm() {
		return yxdm;
	}
	public void setYxdm(String yxdm) {
		this.yxdm = yxdm;
	}
	public String getZyz() {
		return zyz;
	}
	public void setZyz(String zyz) {
		this.zyz = zyz;
	}
	public String getZymc() {
		return zymc;
	}
	public void setZymc(String zymc) {
		this.zymc = zymc;
	}
	public String getZymcOrg() {
		return zymcOrg;
	}
	public void setZymcOrg(String zymcOrg) {
		this.zymcOrg = zymcOrg;
	}
	public String getZybz() {
		return zybz;
	}
	public void setZybz(String zybz) {
		this.zybz = zybz;
	}
	public String getZydm() {
		return zydm;
	}
	public void setZydm(String zydm) {
		this.zydm = zydm;
	}
	public int getZdfA() {
		return zdfA;
	}
	public void setZdfA(int zdfA) {
		this.zdfA = zdfA;
	}
	public int getZdfB() {
		return zdfB;
	}
	public void setZdfB(int zdfB) {
		this.zdfB = zdfB;
	}
	public int getZdfC() {
		return zdfC;
	}
	public void setZdfC(int zdfC) {
		this.zdfC = zdfC;
	}
	public int getZdfwcA() {
		return zdfwcA;
	}
	public void setZdfwcA(int zdfwcA) {
		this.zdfwcA = zdfwcA;
	}
	public int getZdfwcB() {
		return zdfwcB;
	}
	public void setZdfwcB(int zdfwcB) {
		this.zdfwcB = zdfwcB;
	}
	public int getZdfwcC() {
		return zdfwcC;
	}
	public void setZdfwcC(int zdfwcC) {
		this.zdfwcC = zdfwcC;
	}
	public int getPjfA() {
		return pjfA;
	}
	public void setPjfA(int pjfA) {
		this.pjfA = pjfA;
	}
	public int getPjfB() {
		return pjfB;
	}
	public void setPjfB(int pjfB) {
		this.pjfB = pjfB;
	}
	public int getPjfC() {
		return pjfC;
	}
	public void setPjfC(int pjfC) {
		this.pjfC = pjfC;
	}
	public int getPjfwcA() {
		return pjfwcA;
	}
	public void setPjfwcA(int pjfwcA) {
		this.pjfwcA = pjfwcA;
	}
	public int getPjfwcB() {
		return pjfwcB;
	}
	public void setPjfwcB(int pjfwcB) {
		this.pjfwcB = pjfwcB;
	}
	public int getPjfwcC() {
		return pjfwcC;
	}
	public void setPjfwcC(int pjfwcC) {
		this.pjfwcC = pjfwcC;
	}
	public int getSeqNoZy() {
		return seqNoZy;
	}
	public void setSeqNoZy(int seqNoZy) {
		this.seqNoZy = seqNoZy;
	}
	public int getSeqNoYx() {
		return seqNoYx;
	}
	public void setSeqNoYx(int seqNoYx) {
		this.seqNoYx = seqNoYx;
	}
    

}
