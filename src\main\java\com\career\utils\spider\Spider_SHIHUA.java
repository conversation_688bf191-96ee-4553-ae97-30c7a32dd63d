package com.career.utils.spider;

import java.io.*;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.career.utils.HttpSendUtils;

public class Spider_SHIHUA {
	
	// 中石化
		public static StringBuffer YOUZHIYUAN()throws Exception {

			
			StringBuffer SQL = new StringBuffer();
			File folder = new File("E:\\kaogong\\k5\\");
			
			File[] each = folder.listFiles();
			for(File file : each) {
				BufferedReader br = new BufferedReader(new FileReader(file));
				String[] fileName = file.getName().split("_");
				String src = fileName[0];
				String dw = fileName[1];
				String sj = fileName[2];
				sj = sj.substring(0, sj.length() - 4);
				String lsy = dw;
				String group_name = "中国石化";
				StringBuffer ss = new StringBuffer();
				String line = null;
				while((line = br.readLine()) != null) {
					ss.append(line);
				}
				Document detailPage = Jsoup.parse(ss.toString());
				Elements ue_table = detailPage.getElementsByClass("div_rymd");
	
				for(int i=0;i<ue_table.size();i++) {
					Element el = ue_table.get(i);
					String gw = el.getElementsByClass("total_box").get(0).text();
					Elements tables = el.getElementsByTag("table");
					
					//录取
					Element tableLQ = tables.get(0);
					Element tbody_tableLQ = tableLQ.getElementsByTag("tbody").get(0);
					Elements tr_tbody_tableLQ = tbody_tableLQ.getElementsByTag("tr");
					for(int k = 0;k<tr_tbody_tableLQ.size();k++) {
						Elements td_tbody_tableLQ = tr_tbody_tableLQ.get(k).getElementsByTag("td");
						String xm = td_tbody_tableLQ.get(0).text();
						String yxmc = td_tbody_tableLQ.get(1).text();
						String zymc = td_tbody_tableLQ.get(2).text();
						String xl = td_tbody_tableLQ.get(3).text();
						String bz = td_tbody_tableLQ.get(4).text();
						
						SQL.append("INSERT INTO `career_jy_all_2025` (`xh`, `dw`, `gw`, `tempx`, `sr`, `xm`, `xb`, `xl`, `yxmc`, `yxmc_diploma`, `yxmx_bachelor`, `yxmc_master`, `yxmc_phd`, `zymc`, `ydw`, `bz`, `bz2`, `is_bx`, `lx`, `lsy`, `lsy_view`, `group_name`, `group_name_view`, `province`, `city`, `area`, `nf`, `emp_cnt`, `sj`, `src`, `is985`, `is211`, `isGN`, `create_tm`) VALUES (NULL, '"+dw+"', '"+gw+"', NULL, NULL, '"+xm+"', NULL, '"+xl+"', '"+yxmc+"', NULL, NULL, NULL, NULL, '"+zymc+"', NULL, '拟录用', NULL, NULL, NULL, '"+lsy+"', NULL, '"+group_name+"', NULL, NULL, NULL, NULL, 2025, NULL, '"+sj+"', '"+src+"', NULL, NULL, NULL, NULL);\r\n");
						
						System.out.println(fileName+"="+gw + "录取->" + xm + ","+yxmc+"->"+zymc);
					}
					
					//递补
					if(tables.size() == 1) {
						continue;
					}
					Element tableDB = tables.get(1);
					Element tbody_tableDB = tableDB.getElementsByTag("tbody").get(0);
					Elements tr_tbody_tableDB = tbody_tableDB.getElementsByTag("tr");
					for(int k = 0;k<tr_tbody_tableDB.size();k++) {
						Elements td_tbody_tableDB = tr_tbody_tableDB.get(k).getElementsByTag("td");
						String xm = td_tbody_tableDB.get(0).text();
						String yxmc = td_tbody_tableDB.get(1).text();
						String zymc = td_tbody_tableDB.get(2).text();
						String xl = td_tbody_tableDB.get(3).text();
						String bz = td_tbody_tableDB.get(4).text();
						SQL.append("INSERT INTO `career_jy_all_2025` (`xh`, `dw`, `gw`, `tempx`, `sr`, `xm`, `xb`, `xl`, `yxmc`, `yxmc_diploma`, `yxmx_bachelor`, `yxmc_master`, `yxmc_phd`, `zymc`, `ydw`, `bz`, `bz2`, `is_bx`, `lx`, `lsy`, `lsy_view`, `group_name`, `group_name_view`, `province`, `city`, `area`, `nf`, `emp_cnt`, `sj`, `src`, `is985`, `is211`, `isGN`, `create_tm`) VALUES (NULL, '"+dw+"', '"+gw+"', NULL, NULL, '"+xm+"', NULL, '"+xl+"', '"+yxmc+"', NULL, NULL, NULL, NULL, '"+zymc+"', NULL, '递补', NULL, '递补', NULL, '"+lsy+"', NULL, '"+group_name+"', NULL, NULL, NULL, NULL, 2025, NULL, '"+sj+"', '"+src+"', NULL, NULL, NULL, NULL);\r\n");
						
					}
					
				}
			}
			
			writeTempFile(new File("E:\\kaogong\\SH_0220.txt"), SQL);
			return SQL;
		}
		
		
		static String u(String tt) {
			String en_key = "eFPIKDkOCHio8sVfprqdxt0jEw9gMb";
			String result = tt.toLowerCase() + '&' + en_key.toLowerCase();
			return result;
		}
		
		static String get_u_sign(String data) {
			String sign_data = u(data);
			String encodedString = Base64.getEncoder().encodeToString(sign_data.getBytes());
		    return calculateMD5(encodedString);
		}
		
		private static String calculateMD5(String originalString) {
	        try {
	            // 创建MD5加密实例
	            MessageDigest md = MessageDigest.getInstance("MD5");
	 
	            // 执行加密操作
	            byte[] messageDigest = md.digest(originalString.getBytes());
	 
	            // 将得到的散列值转换为十六进制
	            StringBuilder sb = new StringBuilder();
	            for (byte b : messageDigest) {
	                sb.append(String.format("%02x", b));
	            }
	 
	            // 返回MD5散列值
	            return sb.toString();
	        } catch (NoSuchAlgorithmException e) {
	            throw new RuntimeException("MD5加密算法不可用", e);
	        }
	    }

		

		public static void main(String[] args) {
			try {
				YOUZHIYUAN();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		private static void writeTempFile(File file, StringBuffer sb) {
			try {
				BufferedWriter bw = new BufferedWriter(new FileWriter(file));
				bw.write(sb.toString());
				bw.flush();
				bw.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}

}
