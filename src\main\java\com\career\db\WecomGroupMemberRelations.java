package com.career.db;

import java.util.Date;

public class WecomGroupMemberRelations {
	private Long wecom_id; // 自增主键ID
    private String wecom_chat_id; // 所属群ID
    private String wecom_member_userid; // 群成员的userid (可能是内部成员或外部客户)
    private Integer wecom_member_type; // 成员类型 (1-内部成员, 2-外部客户)
    private Date wecom_join_time; // 成员加入时间戳
    private Integer wecom_join_scene; // 入群方式 (1-由群成员邀请入群 2-由二维码扫码入群 3-管理员导入)
    private String wecom_group_nickname; // 成员在群里的昵称
    private String wecom_invitor_userid; // 邀请人成员ID (仅在由群成员邀请时有值)
    private Date wecom_created_at; // 记录创建时间
    private Date wecom_updated_at; // 记录更新时间
	public Long getWecom_id() {
		return wecom_id;
	}
	public void setWecom_id(Long wecom_id) {
		this.wecom_id = wecom_id;
	}
	public String getWecom_chat_id() {
		return wecom_chat_id;
	}
	public void setWecom_chat_id(String wecom_chat_id) {
		this.wecom_chat_id = wecom_chat_id;
	}
	public String getWecom_member_userid() {
		return wecom_member_userid;
	}
	public void setWecom_member_userid(String wecom_member_userid) {
		this.wecom_member_userid = wecom_member_userid;
	}
	public Integer getWecom_member_type() {
		return wecom_member_type;
	}
	public void setWecom_member_type(Integer wecom_member_type) {
		this.wecom_member_type = wecom_member_type;
	}
	public Date getWecom_join_time() {
		return wecom_join_time;
	}
	public void setWecom_join_time(Date wecom_join_time) {
		this.wecom_join_time = wecom_join_time;
	}
	public Integer getWecom_join_scene() {
		return wecom_join_scene;
	}
	public void setWecom_join_scene(Integer wecom_join_scene) {
		this.wecom_join_scene = wecom_join_scene;
	}
	public String getWecom_group_nickname() {
		return wecom_group_nickname;
	}
	public void setWecom_group_nickname(String wecom_group_nickname) {
		this.wecom_group_nickname = wecom_group_nickname;
	}
	public String getWecom_invitor_userid() {
		return wecom_invitor_userid;
	}
	public void setWecom_invitor_userid(String wecom_invitor_userid) {
		this.wecom_invitor_userid = wecom_invitor_userid;
	}
	public Date getWecom_created_at() {
		return wecom_created_at;
	}
	public void setWecom_created_at(Date wecom_created_at) {
		this.wecom_created_at = wecom_created_at;
	}
	public Date getWecom_updated_at() {
		return wecom_updated_at;
	}
	public void setWecom_updated_at(Date wecom_updated_at) {
		this.wecom_updated_at = wecom_updated_at;
	}

}
