<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page import="com.zsdwf.utils.*,com.zsdwf.db.*,java.util.*" %>
<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"> 
<meta name="format-dection" content="telephone=no"/>
<title><%=CCache.SYS_TITLE_NAME %></title>  
<script src="<%=request.getContextPath()%>/sources/jquery-3.6.4.min.js"></script>
<style type="text/css">
html {
    font-family: sans-serif;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
}
 
body {
    margin: 10px;
    font-size:12px;
}
table {
    border-collapse: collapse;
    border-spacing: 0;
}
 
td,th {
    padding: 0;
}
 
.pure-table {
    border-collapse: collapse;
    border-spacing: 0;
    empty-cells: show;
    border: 1px solid #cbcbcb;
}
 
.pure-table caption {
    color: #000;
    font: italic 85%/1 arial,sans-serif;
    padding: 1em 0;
    text-align: center;
}
 
.pure-table td,.pure-table th {
    border-left: 1px solid #cbcbcb;
    border-width: 0 0 0 1px;
    font-size: inherit;
    margin: 0;
    overflow: visible;
    padding: .5em 1em;
}
 
.pure-table thead {
    background-color: #e0e0e0;
    color: #000;
    text-align: left;
    vertical-align: bottom;
}
 
.pure-table td {
    background-color: transparent;
}
 
.pure-table-bordered td {
    border-bottom: 1px solid #cbcbcb;
}
 
.pure-table-bordered tbody>tr:last-child>td {
    border-bottom-width: 0;
}

.query_rec_label{
	float:left;width:90px;height:25px;line-height:25px;font-weight:bold;margin:2px 1px;
}
.query_rec_label2{
	float:left;width:90px;height:25px;line-height:25px;font-weight:bold;margin:2px 1px;
}
.query_rec_aa{
	float:left;width:130px;font-size:10px;height:25px;line-height:25px;margin:2px;border:1px solid #7766cc;text-align:center;cursor:pointer;
}
.query_rec_a{
	float:left;width:100px;font-size:10px;height:25px;line-height:25px;margin:2px;border:1px solid #7766cc;text-align:center;cursor:pointer;
}
.query_rec_b{
	float:left;width:60px;font-size:10px;height:25px;line-height:25px;margin:2px;border:1px solid #7766cc;text-align:center;cursor:pointer;
}
.query_rec_c{
	float:left;width:40px;font-size:10px;height:25px;line-height:25px;margin:2px;border:1px solid #7766cc;text-align:center;cursor:pointer;
}
.query_rec_d{
	float:left;width:80px;font-size:10px;height:25px;line-height:25px;margin:2px;border:1px solid #7766cc;text-align:center;cursor:pointer;
}
.query_bind_event{
	;
}
.query_select_bind_event{
	;
}


.school_pick{
	float:left;width:200px;border:1px solid red;height:20px;text-align:left;line-height:20px;margin:1px 10px;font-size:12px;;
}

.major_one{
	cursor:pointer;float:left;width:80px;height:400px;text-align:center;margin:1px;
}
.major_two{
	cursor:pointer;float:left;width:140px;height:400px;text-align:center;margin:1px;
}
.major_three{
	cursor:pointer;float:left;width:140px;height:400px;text-align:center;margin:1px;
}

.school_list{
	cursor:pointer;height:18px;text-align:left;line-height:18px;margin:1px 2px;font-size:12px;;
}
.major_info{
	cursor:pointer;float:left;width:210px;text-align:left;margin:2px;border:1px solid #cc0000;padding:1px;
}
.major_info3{
	cursor:pointer;float:left;width:300px;text-align:left;margin:2px;border:1px solid #cc0000;padding:1px;
}
.major_info2{
	cursor:pointer;float:left;width:128px;text-align:left;margin:2px;border:1px solid #cc0000;padding:1px;
}
.major_info22{
	cursor:pointer;float:left;width:155px;text-align:left;margin:2px;border:1px solid #cc0000;padding:1px;
}
.major_info_title{
	cursor:pointer;float:left;width:200px;text-align:left;margin:2px;font-weight:bold;fon-size:16px;color:blue;;
}

.fixedLayer1 { 
	position:fixed; 
	left: 0px; 
	top: 0px; 
	width:100px; 
	line-height:40px; 
	height:40px;
	background: #FC6; 
	font-size:26px;
	text-align:center;
	font-weight:bold;
	color:#000;
	border:1px solid #F90; 
	filter: alpha(opacity = 90);
	-moz-opacity: 0.9;
	-khtml-opacity: 0.9;
	opacity: 0.9;
} 

.fixedLayer2 { 
	position:fixed; 
	left: 0px; 
	top: 0px; 
	width:100px; 
	line-height:30px; 
	background: #FC6; 
	border:1px solid #F90; 
} 

.fixedLayerMenu { 
	line-height:25px; 
	height:25px;
	margin:5px;
} 
</style>
</head>
<body>
<div>

	
	<div style="margin:5px;">
		<div style="font-size:16px;font-weight:bold;text-align:left;'">全程学业规划系统-后台管理</div>
	</div>
	
	<div style="margin:3px;">
			<div class="query_rec_label">账号：</div>
			<div style="float:left;">
				<input type="text" style="width:140px;height:23px;font-size:16px;color:blue;font-weight:bold;" id="no"/>
			</div>
			<div style="clear:both;"></div>
		</div>
		
		<div style="margin:3px;">
			<div class="query_rec_label">密码：</div>
			<div style="float:left;">
				<input type="password" style="width:140px;height:23px;font-size:16px;color:blue;font-weight:bold;" id="passwd"/>
			</div>
			<div style="clear:both;"></div>
		</div>

		<div style="margin:3px;">
			<div class="query_rec_label">验证码：</div>
			<div style="float:left;">
				<input type="text" style="width:140px;height:23px;font-size:16px;color:blue;font-weight:bold;" id="code"/><br>
				<img onclick="MM_reloadImg();" id="verf-code-generation" src="<%=request.getContextPath() %>/verf-code-generation.jsp?rd=<%=System.currentTimeMillis() %>">
			</div>
			<div style="clear:both;"></div>
		</div>
		
		<div style="margin:3px;">
			<div class="query_rec_label"></div>
			<div style="float:left;">
				<input type="button" style="width:100px;font-size:14px;" value="提交" onclick="MM_login();"/>
			</div>
			<div style="clear:both;"></div>
		</div>
</div>
</body>
</html>
<script>

function MM_reloadImg(){
	$("#verf-code-generation").prop("src","<%=request.getContextPath() %>/verf-code-generation.jsp?rd=" + (new Date()).getTime());
}

function MM_login(){
	var code = $("#code").val();
	if(code == ""){
		alert("请输入验证码");
		$("#code").focus();
		return false;
	}
	var cardNO = $("#no").val();
	if(cardNO == ""){
		alert("请输入卡号");
		$("#no").focus();
		return false;
	}
	var cardPasswd = $("#passwd").val();
	if(cardPasswd == ""){
		alert("请输入密码");
		$("#passwd").focus();
		return false;
	}
	
	var para = {"code": code, "no" : cardNO , "passwd" : cardPasswd};
	
	$.ajax({
        type : "POST",
        url : "<%=request.getContextPath()%>/admin/_login.jsp",
		data: para, 
		//dataType: "json",
        //请求成功redirect:freeze
        success : function(result) {
        	if($.trim(result).indexOf("redirect:main") != -1){
        		location.href = "<%=request.getContextPath()%>/admin/card598_tel_import.jsp?rd=<%=System.currentTimeMillis()%>";
        	}else if($.trim(result).indexOf("redirect:freeze") != -1){
        		location.href = "<%=request.getContextPath()%>/admin/freeze.jsp?rd=<%=System.currentTimeMillis()%>";
        	}else{
        		$("#verf-code-generation").prop("src","<%=request.getContextPath() %>/verf-code-generation.jsp?rd=" + (new Date()).getTime());
        		alert($.trim(result));
        	}
        },
        //请求失败，包含具体的错误信息
        error : function(e){
            console.log(e.status);
            console.log(e.responseText);
        }
    });
}

var loginType = "card";

$(function(){
	$(".query_bind_event").each(function(index,item) {
		var cList = $(this).children();
		for(var i=0;i<cList.length;i++){
			if(i==0){
				$(cList[i]).css("background-color","#FFFF00");
				$(cList[i]).css("font-weight","bold");
			}else{
				$(cList[i]).css("background-color","#fff");
				$(cList[i]).css("font-weight","normal");
			}
			$(cList[i]).bind('click', function(){
				var ccList = $(this).parent().children();
				for(var k=0;k<ccList.length;k++){
					$(ccList[k]).css("background-color","#fff");
					$(ccList[k]).css("font-weight","normal");
				}
				$(this).css("background-color","#FFFF00");
				$(this).css("font-weight","bold");
				if($(this).html() == "卡密"){
					$("#cardLogin-div").show();
					$("#phoneLogin-div").hide();
					loginType = "card";
				}else{
					$("#cardLogin-div").hide();
					$("#phoneLogin-div").show();
					loginType = "phone";
				}
			});
		}
	});
});
</script>