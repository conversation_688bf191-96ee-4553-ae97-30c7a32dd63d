package com.career.utils;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.career.db.CardBean;
import com.career.db.LhyForm;
import com.career.db.ZyzdForm;
import com.career.db.ZyzdMajorRanking;

import cn.hutool.core.lang.UUID;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;


public class ExcelUtilsForUser {
	
	public static void main(String args[]) {
	}

	
	public static void createCardExcel(String filePathAndName,  List<CardBean> dataList) {
		// 创建一个新的工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        Tools.println(filePathAndName);
        // 创建一个工作表
        Sheet sheet = workbook.createSheet("卡密");
 
        // 创建一个行，并在其中创建一个单元格
        Row rowOne = sheet.createRow(0);
		Cell rowOneCell1 = rowOne.createCell(0);
		rowOneCell1.setCellValue("卡号");
        Cell rowOneCell2 = rowOne.createCell(1);
        rowOneCell2.setCellValue("密码");
        
        for(int i= 0; i < dataList.size(); i++) {
        	CardBean cardBean = dataList.get(i);
	        Row row = sheet.createRow(i+1);
	        Cell cell1 = row.createCell(0);
	        cell1.setCellValue(cardBean.getId());
	        Cell cell2 = row.createCell(1);
	        cell2.setCellValue(cardBean.getPasswd());
        }
 
        // 写出到文件
        try{
        	FileOutputStream fileOut = new FileOutputStream(filePathAndName);
            workbook.write(fileOut);
            workbook.close();
        } catch (Exception e){
        	e.printStackTrace();
        }finally {
        	try {
            	workbook.close();
            } catch (IOException e){
                e.printStackTrace();
            } 
        }
    }
	
	public static void createYxZyRankingExcel(String filePathAndName,  HashMap<String, List<ZyzdMajorRanking>> dataListMap) {
		// 创建一个新的工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        Tools.println(filePathAndName);
        createYxZyRankingExcel(workbook, dataListMap);
 
        // 写出到文件
        try{
        	FileOutputStream fileOut = new FileOutputStream(filePathAndName);
            workbook.write(fileOut);
            workbook.close();
        } catch (Exception e){
        	e.printStackTrace();
        }finally {
        	try {
            	workbook.close();
            } catch (IOException e){
                e.printStackTrace();
            } 
        }
    }
	
	private static void createYxZyRankingExcel(XSSFWorkbook workbook, HashMap<String, List<ZyzdMajorRanking>> dataListMap) {
		Iterator<String> it = dataListMap.keySet().iterator();
        while(it.hasNext()) {
        	String tabName = it.next();
        	List<ZyzdMajorRanking> dataList = dataListMap.get(tabName);
        	// 创建一个工作表
            Sheet sheet = workbook.createSheet(tabName);
            sheet.setColumnWidth(0, 8*256);
            sheet.setColumnWidth(1, 8*256);
            sheet.setColumnWidth(2, 20*256);
            sheet.setColumnWidth(3, 10*256);
            sheet.setColumnWidth(4, 16*256);
            sheet.setColumnWidth(5, 25*256);
            sheet.setColumnWidth(6, 15*256);
            
            CellStyle cellStyleFontGreen = workbook.createCellStyle();
            //必须设置 否则背景色不生效
            cellStyleFontGreen.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            Font fontGreen = workbook.createFont();
            fontGreen.setFontHeightInPoints((short) 12);
            fontGreen.setBold(true);
            fontGreen.setColor(IndexedColors.GREEN.getIndex());
            cellStyleFontGreen.setFont(fontGreen);
            
            CellStyle cellStyleFontRed = workbook.createCellStyle();
            //必须设置 否则背景色不生效
            cellStyleFontRed.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            Font fontRed = workbook.createFont();
            fontRed.setFontHeightInPoints((short) 12);
            fontRed.setColor(IndexedColors.RED.getIndex());
            cellStyleFontRed.setFont(fontRed);
            
            // 创建一个行，并在其中创建一个单元格
            Row rowOne = sheet.createRow(0);
    		Cell rowOneCell1 = rowOne.createCell(0);
    		rowOneCell1.setCellValue("排名");
            Cell rowOneCell2 = rowOne.createCell(1);
            rowOneCell2.setCellValue("院校名称");
            Cell rowOneCell3 = rowOne.createCell(2);
            rowOneCell3.setCellValue("院校属性");
            Cell rowOneCell4 = rowOne.createCell(3);
            rowOneCell4.setCellValue("院校最低");
            Cell rowOneCell5 = rowOne.createCell(4);
            rowOneCell5.setCellValue("专业名称");
            Cell rowOneCell6 = rowOne.createCell(5);
            rowOneCell6.setCellValue("专业最低");
            
            for(int i= 0; i < dataList.size(); i++) {
            	ZyzdMajorRanking bean = dataList.get(i);
    	        Row row = sheet.createRow(i+1);
    	        Cell cell1 = row.createCell(0);
    	        cell1.setCellValue(bean.getRanking());
    	        Cell cell2 = row.createCell(1);
    	        cell2.setCellValue(bean.getYxmc());
    	        Cell cell3 = row.createCell(2);
    	        cell3.setCellValue(bean.getExt_tags());
    	        Cell cell4 = row.createCell(3);
    	        if(Tools.isEmpty(bean.getExt_zdf())) {
    	        	cell4.setCellValue("--");
    	        }else {
    	        	cell4.setCellStyle(bean.isExt_is_overload() ? cellStyleFontRed:cellStyleFontGreen);
    	        	cell4.setCellValue(bean.getExt_zdf()+"("+bean.getExt_zdfwc()+")");
    	        }
    	        Cell cell5 = row.createCell(4);
    	        cell5.setCellValue(bean.getZymc());
    	        Cell cell6 = row.createCell(5);
    	        if(Tools.isEmpty(bean.getExt_zy_zdf())) {
    	        	cell6.setCellValue("--");
    	        }else {
    	        	cell6.setCellValue(bean.getExt_zy_zdf()+"("+bean.getExt_zy_zdfwc()+")");
    	        }
            }
        }
    }
	
	public static void createFormReportExcel(int jhYear, String filePathAndName, String score, List<ZyzdForm> dataList, HashMap<String, List<ZyzdMajorRanking>> dataListMap) {
		createFormReportExcel("S5",jhYear,filePathAndName,score,dataList,dataListMap);
	}
	
	public static void createFormReportExcel(String sfCode, int jhYear, String filePathAndName, String score, List<ZyzdForm> dataList, HashMap<String, List<ZyzdMajorRanking>> dataListMap) {
		// 创建一个新的工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        Tools.println(filePathAndName);
        // 创建一个工作表
        Sheet sheet = workbook.createSheet("志愿表_"+score+"");
        sheet.setColumnWidth(0, 10*256);
        sheet.setColumnWidth(1, 10*256);
        sheet.setColumnWidth(2, 30*256);
        sheet.setColumnWidth(3, 10*256);
        sheet.setColumnWidth(4, 10*256);
        sheet.setColumnWidth(5, 30*256);
        sheet.setColumnWidth(6, 20*256);
        sheet.setColumnWidth(7, 20*256);
        sheet.setColumnWidth(8, 20*256);
        sheet.setColumnWidth(9, 30*256);
        
        XSSFCellStyle style_content = workbook.createCellStyle();
        style_content.setBorderBottom(BorderStyle.THIN);//下边框
        style_content.setBorderTop(BorderStyle.THIN);//上边框
        style_content.setBorderLeft(BorderStyle.THIN);//左边框
        style_content.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontBold = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontBold.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontBold = workbook.createFont();
        fontBold.setFontHeightInPoints((short) 10);
        fontBold.setBold(true);
        fontBold.setColor(IndexedColors.BLACK.getIndex());
        fontBold.setFontName("微软雅黑");
        cellStyleFontBold.setFont(fontBold);
        cellStyleFontBold.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontBold.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontBold.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontBold.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontNormal0 = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontNormal0.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontNormal0 = workbook.createFont();
        fontNormal0.setFontHeightInPoints((short) 10);
        fontNormal0.setBold(false);
        fontNormal0.setColor(IndexedColors.BLACK.getIndex());
        fontNormal0.setFontName("微软雅黑");
        cellStyleFontNormal0.setFont(fontNormal0);
        cellStyleFontNormal0.setAlignment(HorizontalAlignment.CENTER);
        cellStyleFontNormal0.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyleFontNormal0.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontNormal0.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontNormal0.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontNormal0.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontNormal2 = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontNormal2.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontNormal2 = workbook.createFont();
        fontNormal2.setFontHeightInPoints((short) 10);
        fontNormal2.setBold(false);
        fontNormal2.setColor(IndexedColors.BLACK.getIndex());
        fontNormal2.setFontName("微软雅黑");
        cellStyleFontNormal2.setFont(fontNormal2);
        cellStyleFontNormal2.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontNormal2.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontNormal2.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontNormal2.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontNormal1 = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontNormal1.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontNormal = workbook.createFont();
        fontNormal.setFontHeightInPoints((short) 10);
        fontNormal.setBold(false);
        fontNormal.setColor(IndexedColors.BLACK.getIndex());
        fontNormal.setFontName("微软雅黑");
        cellStyleFontNormal1.setFont(fontNormal);
        cellStyleFontNormal1.setFillForegroundColor(IndexedColors.LEMON_CHIFFON.getIndex());
        cellStyleFontNormal1.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontNormal1.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontNormal1.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontNormal1.setBorderRight(BorderStyle.THIN);//右边框
        

        CellStyle cellStyleFontHighLight = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontHighLight.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontHighLight = workbook.createFont();
        fontHighLight.setFontHeightInPoints((short) 10);
        fontHighLight.setBold(true);
        fontHighLight.setColor(IndexedColors.BLACK.getIndex());
        fontHighLight.setFontName("微软雅黑");
        cellStyleFontHighLight.setFont(fontHighLight);
        cellStyleFontHighLight.setAlignment(HorizontalAlignment.CENTER);
        cellStyleFontHighLight.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyleFontHighLight.setFillForegroundColor(IndexedColors.TAN.getIndex());
        cellStyleFontHighLight.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontHighLight.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontHighLight.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontHighLight.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontGreen = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontGreen.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontGreen = workbook.createFont();
        fontGreen.setFontHeightInPoints((short) 10);
        fontGreen.setBold(true);
        fontGreen.setColor(IndexedColors.GREEN.getIndex());
        fontGreen.setFontName("微软雅黑");
        cellStyleFontGreen.setFont(fontGreen);
        
        CellStyle cellStyleFontRed = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontRed.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontRed = workbook.createFont();
        fontRed.setFontHeightInPoints((short) 10);
        fontRed.setColor(IndexedColors.RED.getIndex());
        fontRed.setFontName("微软雅黑");
        cellStyleFontRed.setFont(fontRed);
        
        CellStyle cellStyleFontBlueBig = workbook.createCellStyle();
        //必须设置 否则背景色不生效
        cellStyleFontBlueBig.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontBlueBig = workbook.createFont();
        fontBlueBig.setFontHeightInPoints((short) 18);
        fontBlueBig.setBold(true);
        fontBlueBig.setColor(IndexedColors.ORANGE.getIndex());
        fontBlueBig.setFontName("微软雅黑");
        cellStyleFontBlueBig.setFont(fontBlueBig);
        cellStyleFontBlueBig.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontBlueBig.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontBlueBig.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontBlueBig.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontBlack14 = workbook.createCellStyle();
        cellStyleFontBlueBig.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontBlackBig14 = workbook.createFont();
        fontBlackBig14.setFontHeightInPoints((short) 14);
        fontBlackBig14.setBold(true);
        fontBlackBig14.setColor(IndexedColors.BLACK.getIndex());
        fontBlackBig14.setFontName("微软雅黑");
        cellStyleFontBlack14.setFont(fontBlackBig14);
        cellStyleFontBlack14.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        cellStyleFontBlack14.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyleFontBlack14.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontBlack14.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontBlack14.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontBlack14.setBorderRight(BorderStyle.THIN);//右边框
        
        CellStyle cellStyleFontThinBlack14 = workbook.createCellStyle();
        cellStyleFontThinBlack14.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontBlackThin14 = workbook.createFont();
        fontBlackThin14.setFontHeightInPoints((short) 14);
        fontBlackThin14.setBold(false);
        fontBlackThin14.setColor(IndexedColors.BLACK.getIndex());
        fontBlackThin14.setFontName("微软雅黑");
        cellStyleFontThinBlack14.setFont(fontBlackThin14);
        cellStyleFontThinBlack14.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        cellStyleFontThinBlack14.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyleFontThinBlack14.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontThinBlack14.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontThinBlack14.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontThinBlack14.setBorderRight(BorderStyle.THIN);//右边框
        
        
        CellStyle cellStyleFontColumnHeader = workbook.createCellStyle();
        cellStyleFontColumnHeader.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font fontColumnHeader = workbook.createFont();
        fontColumnHeader.setFontHeightInPoints((short) 12);
        fontColumnHeader.setBold(true);
        fontColumnHeader.setColor(IndexedColors.BLACK.getIndex());
        fontColumnHeader.setFontName("微软雅黑");
        cellStyleFontColumnHeader.setFont(fontColumnHeader);
        cellStyleFontColumnHeader.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        cellStyleFontColumnHeader.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyleFontColumnHeader.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyleFontColumnHeader.setBorderTop(BorderStyle.THIN);//上边框
        cellStyleFontColumnHeader.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyleFontColumnHeader.setBorderRight(BorderStyle.THIN);//右边框
        

        Row rowTitle = sheet.createRow(0);
        Cell rowTitleCell1 = rowTitle.createCell(0);
        rowTitleCell1.setCellValue(jhYear + "年志愿方案-");
        
        CellStyle cellStyle = workbook.createCellStyle();
        CreationHelper creationHelper = workbook.getCreationHelper();
        cellStyle.setDataFormat(creationHelper.createDataFormat().getFormat("text")); // 对于文本，使用"text"格式以保持文本格式不变
        cellStyle.setAlignment(HorizontalAlignment.CENTER); // 设置水平居中对齐
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直居中对齐（如果需要）
        fontBlueBig.setFontName("微软雅黑");
        cellStyle.setFont(fontBlueBig);
        rowTitleCell1.setCellStyle(cellStyle); // 应用样式到单元格
        
        CellRangeAddress deviceCellRangeTitle = new CellRangeAddress(0, 0, 0, 9);
		sheet.addMergedRegion(deviceCellRangeTitle);
		
		
        //必须设置 否则背景色不生效
        cellStyleFontBlueBig.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        
        
        
        Row rowSubTitle = sheet.createRow(1);
        
        for(int i=0;i<2;i++) {
	        Cell rowSubTitleCell1 = rowSubTitle.createCell(i);
	        rowSubTitleCell1.setCellValue("成绩");
	        rowSubTitleCell1.setCellStyle(cellStyleFontBlack14);
        }
        for(int i=2;i<5;i++) {
	        Cell rowSubTitleCell1 = rowSubTitle.createCell(i);
	        rowSubTitleCell1.setCellValue(score);
	        rowSubTitleCell1.setCellStyle(cellStyleFontThinBlack14);
        }
        
        CellRangeAddress deviceCellRangeSubTitle1 = new CellRangeAddress(1, 1, 0, 1);
		sheet.addMergedRegion(deviceCellRangeSubTitle1);
		
		CellRangeAddress deviceCellRangeSubTitle2 = new CellRangeAddress(1, 1, 2, 4);
		sheet.addMergedRegion(deviceCellRangeSubTitle2);
 
        // 创建一个行，并在其中创建一个单元格
        Row rowOne = sheet.createRow(2);
		Cell rowOneCell1 = rowOne.createCell(0);
		rowOneCell1.setCellValue("志愿顺序");
		rowOneCell1.setCellStyle(cellStyleFontColumnHeader);
		
        Cell rowOneCell2 = rowOne.createCell(1);
        rowOneCell2.setCellValue("院校代码");
        rowOneCell2.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell3 = rowOne.createCell(2);
        rowOneCell3.setCellValue("院校名称");
        rowOneCell3.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell4 = rowOne.createCell(3);
        rowOneCell4.setCellValue("专业顺序");
        rowOneCell4.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell5 = rowOne.createCell(4);
        rowOneCell5.setCellValue("专业代码");
        rowOneCell5.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell6 = rowOne.createCell(5);
        rowOneCell6.setCellValue("专业名称");
        rowOneCell6.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell7 = rowOne.createCell(6);
        rowOneCell7.setCellValue(String.valueOf(jhYear-1)+"年最低分");
        rowOneCell7.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell8 = rowOne.createCell(7);
        rowOneCell8.setCellValue(String.valueOf(jhYear-2)+"年最低分");
        rowOneCell8.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell9 = rowOne.createCell(8);
        rowOneCell9.setCellValue(String.valueOf(jhYear-3)+"年最低分");
        rowOneCell9.setCellStyle(cellStyleFontColumnHeader);
        
        Cell rowOneCell10 = rowOne.createCell(9);
        rowOneCell10.setCellValue("排名");
        rowOneCell10.setCellStyle(cellStyleFontColumnHeader);
        
        HashMap<Integer, Integer> MAP_YX_ZYZ_SPAN = new HashMap<>(); 
        
        for(int i= 0; i < dataList.size(); i++) {
        	ZyzdForm bean = dataList.get(i);
        	if("重庆贵州河北山东浙江辽宁青海C1G4H2L1S1Z1Q1".indexOf(sfCode) == -1) {
        		MAP_YX_ZYZ_SPAN.put(bean.getSeq_no_yx(), bean.getSeq_no_zy());
        	}
	        Row row = sheet.createRow(i+3);
	        Cell cell1 = row.createCell(0);
	        cell1.setCellValue(bean.getSeq_no_yx());
	        cell1.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        
	        Cell cell2 = row.createCell(1);
	        cell2.setCellValue(bean.getYxdm());
	        cell2.setCellStyle(cellStyleFontHighLight);
	        
	        Cell cell3 = row.createCell(2);
	        cell3.setCellValue(bean.getExt_yxmc());
	        cell3.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        
	        Cell cell4 = row.createCell(3);
	        cell4.setCellValue(bean.getSeq_no_zy());
	        cell4.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);

	        Cell cell5 = row.createCell(4);
	        cell5.setCellValue(bean.getZydm());
	        cell5.setCellStyle(cellStyleFontHighLight);
	        
	        Cell cell6 = row.createCell(5);
	        cell6.setCellValue(bean.getZymc());
	        cell6.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        
	        Cell cell7 = row.createCell(6);
	        cell7.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        
	        if(bean.getZdf_a() < 10) {
	        	cell7.setCellValue("-");
	        }else {
	        	cell7.setCellValue(bean.getZdf_a()+"("+bean.getZdfwc_a()+")");
	        }
	        Cell cell8 = row.createCell(7);
	        cell8.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        
	        if(bean.getZdf_b() < 10) {
	        	cell8.setCellValue("-");
	        }else {
	        	cell8.setCellValue(bean.getZdf_b()+"("+bean.getZdfwc_b()+")");
	        }
	        Cell cell9 = row.createCell(8);
	        cell9.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        
	        if(bean.getZdf_c() < 10) {
	        	cell9.setCellValue("-");
	        }else {
	        	cell9.setCellValue(bean.getZdf_c()+"("+bean.getZdfwc_c()+")");
	        }
	        Cell cell10 = row.createCell(9);
	        cell10.setCellStyle(bean.getSeq_no_yx() % 2 == 0 ? cellStyleFontNormal1 : cellStyleFontNormal2);
	        
	        if(Tools.isEmpty(bean.getExt_ranking())) {
	        	cell10.setCellValue("-");
	        }else {
	        	cell10.setCellValue(bean.getExt_ranking()+"("+bean.getExt_level()+")");
	        }
        }
        
        
        int startIndex = 2;
        Iterator<Integer> its = MAP_YX_ZYZ_SPAN.keySet().iterator();
        while(its.hasNext()) {
        	int form_no = its.next();
        	int zy_count = MAP_YX_ZYZ_SPAN.get(form_no);
        	if(zy_count > 1) {
        		CellRangeAddress deviceCellRange0 = new CellRangeAddress(startIndex + 1, startIndex + zy_count, 0, 0);
        		sheet.addMergedRegion(deviceCellRange0);
        		CellRangeAddress deviceCellRange1 = new CellRangeAddress(startIndex + 1, startIndex + zy_count, 1, 1);
        		sheet.addMergedRegion(deviceCellRange1);
        		CellRangeAddress deviceCellRange2 = new CellRangeAddress(startIndex + 1, startIndex + zy_count, 2, 2);
        		sheet.addMergedRegion(deviceCellRange2);
        	}
            startIndex += zy_count;
        }
        
        createYxZyRankingExcel(workbook, dataListMap);
 
        // 写出到文件
        try{
        	FileOutputStream fileOut = new FileOutputStream(filePathAndName);
            workbook.write(fileOut);
            workbook.close();
        } catch (Exception e){
        	e.printStackTrace();
        }finally {
        	try {
            	workbook.close();
            } catch (IOException e){
                e.printStackTrace();
            } 
        }
    }
	
	public static void createForm96ReportExcel(int jhYear, String filePathAndName,  List<ZyzdForm> dataList) {
		// 创建一个新的工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        Tools.println(filePathAndName);
        // 创建一个工作表
        Sheet sheet = workbook.createSheet("志愿表");
 
        // 创建一个行，并在其中创建一个单元格
        Row rowOne = sheet.createRow(0);
		Cell rowOneCell1 = rowOne.createCell(0);
		rowOneCell1.setCellValue("志愿顺序");
        Cell rowOneCell2 = rowOne.createCell(1);
        rowOneCell2.setCellValue("院校名称");
        Cell rowOneCell3 = rowOne.createCell(2);
        rowOneCell3.setCellValue("排名");
        Cell rowOneCell4 = rowOne.createCell(3);
        rowOneCell4.setCellValue("专业名称");
        Cell rowOneCell5 = rowOne.createCell(4);
        rowOneCell5.setCellValue(String.valueOf(jhYear-1));
        Cell rowOneCell6 = rowOne.createCell(5);
        rowOneCell6.setCellValue(String.valueOf(jhYear-2));
        Cell rowOneCell7 = rowOne.createCell(6);
        rowOneCell7.setCellValue(String.valueOf(jhYear-3));
        
        for(int i= 0; i < dataList.size(); i++) {
        	ZyzdForm bean = dataList.get(i);
	        Row row = sheet.createRow(i+1);
	        Cell cell1 = row.createCell(0);
	        cell1.setCellValue(bean.getSeq_no_yx());
	        Cell cell2 = row.createCell(1);
	        cell2.setCellValue(bean.getYxmc());
	        Cell cell3 = row.createCell(2);
	        cell3.setCellValue(bean.getExt_ranking()+"("+bean.getExt_level()+")");
	        Cell cell4 = row.createCell(3);
	        cell4.setCellValue(bean.getZymc());
	        Cell cell5 = row.createCell(4);
	        cell5.setCellValue(bean.getZdf_a()+"("+bean.getZdfwc_a()+")");
	        Cell cell6 = row.createCell(5);
	        cell6.setCellValue(bean.getZdf_b()+"("+bean.getZdfwc_b()+")");
	        Cell cell7 = row.createCell(6);
	        cell7.setCellValue(bean.getZdf_c()+"("+bean.getZdfwc_c()+")");
        }
 
        // 写出到文件
        try{
        	FileOutputStream fileOut = new FileOutputStream(filePathAndName);
            workbook.write(fileOut);
            workbook.close();
        } catch (Exception e){
        	e.printStackTrace();
        }finally {
        	try {
            	workbook.close();
            } catch (IOException e){
                e.printStackTrace();
            } 
        }
    }

}
