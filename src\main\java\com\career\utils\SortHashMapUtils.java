package com.career.utils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map.Entry;
import java.util.Set;

public class SortHashMapUtils { 

	public static LinkedHashMap<String, Integer> sortHashMap(HashMap<String, Integer> map)
	{
		//首先拿到map的键值对集合
		Set<Entry<String, Integer>> entrySet = map.entrySet();
		//将set集合转为list集合
		List<Entry<String, Integer>> list=new ArrayList<Entry<String, Integer>>(entrySet);
		
		//使用Collections集合工具类对list进行排序，排序规则使用匿名内部类实现	
		Collections.sort(list,new Comparator<Entry<String, Integer>>() {
 
 
			@Override
			public int compare(Entry<String, Integer> o1, Entry<String, Integer> o2) {
				//按照要求根据User的age的倒序进行排序
				return o2.getValue() - o1.getValue();
			}
		});
		
		//创建一个新的有序的hashMap集合
		LinkedHashMap<String, Integer> linkedHashMap = new LinkedHashMap<String, Integer>();
		//将list中的数据存储在linkedHashMap中
		for(Entry<String, Integer> entry:list)
		{
			linkedHashMap.put(entry.getKey(),entry.getValue());
		}
		return linkedHashMap;
	}

}
