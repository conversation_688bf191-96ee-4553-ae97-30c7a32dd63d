<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page import="com.zsdwf.utils.*,com.zsdwf.db.*,java.util.*" %>

<%@include file="/WEB-INF/include/_session_admin_ajax.jsp" %>
<%
String tel = Tools.trim(request.getParameter("tel"));
if(Tools.isEmpty(tel) || tel.trim().length() != 11){
	out.println("客户手机号输入不正确，必须是11位整数。");
	return;
}

String orderID = Tools.trim(request.getParameter("orderID"));
if(Tools.isEmpty(orderID)){
	out.println("识别码不能为空。");
	return;
}

String module = Tools.trim(request.getParameter("module"));
if(Tools.isEmpty(module)){
	out.println("开通业务不能为空。");
	return;
}


YGCardBean cardAdmin = (YGCardBean)session.getAttribute("SES_ADMIN");
YGJDBC jdbc = new YGJDBC();
List<BaseCardBean> list = jdbc.getCardsByOrderID(orderID, cardAdmin.getId());
if(list.size() > 0){
	out.println("识别码["+orderID+"]已存在系统中，不能重复录入");
	return;
}

if("A".equals(module)){ //（单系统）优志愿卡系统
	String cid = jdbc.updateBaseCardOrgForAgentOrderOnlyA(tel, orderID, cardAdmin.getId());
	
	if(!Tools.isEmpty(cid)){
		BaseCardBean baseCardBean = jdbc.getCardByCID2(cid);
		SMSSender.sendActiveCardNotification(tel, baseCardBean.getId(), baseCardBean.getPasswd());
		out.print("reload:refresh-list");
	}else{
		out.println("发卡失败，原因：卡号库用完，请尽快补卡");
	}
}else if("B".equals(module)){ //（单系统）规划师辅助系统
	String cid = jdbc.updateBaseCardOrgForAgentOrderOnlyB(tel, orderID, cardAdmin.getId());

	if(!Tools.isEmpty(cid)){
		BaseCardBean baseCardBean = jdbc.getCardByCID2(cid);
		SMSSender.sendActiveCardNotification(tel, baseCardBean.getId(), baseCardBean.getPasswd());
		out.print("reload:refresh-list");
	}else{
		out.println("发卡失败，原因：卡号库用完，请尽快补卡");
	}
}else if("C".equals(module)){ //（单系统）四川升学规划指导
	String cid = jdbc.updateBaseCardOrgForAgentOrderOnlyC(tel, orderID, cardAdmin.getId());

	if(!Tools.isEmpty(cid)){
		BaseCardBean baseCardBean = jdbc.getCardByCID2(cid);
		SMSSender.sendActiveCardNotification(tel, baseCardBean.getId(), baseCardBean.getPasswd());
		out.print("reload:refresh-list");
	}else{
		out.println("发卡失败，原因：卡号库用完，请尽快补卡");
	}
}else if("D".equals(module)){ //（双系统）优志愿卡系统+规划师辅助系统
	String cid = jdbc.updateBaseCardOrgForAgentOrderOnlyD(tel, orderID, cardAdmin.getId());

	if(!Tools.isEmpty(cid)){
		BaseCardBean baseCardBean = jdbc.getCardByCID2(cid);
		SMSSender.sendActiveCardNotification(tel, baseCardBean.getId(), baseCardBean.getPasswd());
		out.print("reload:refresh-list");
	}else{
		out.println("发卡失败，原因：卡号库用完，请尽快补卡");
	}
}else if("E".equals(module)){ //（双系统）四川升学规划指导+规划师辅助系统
	String cid = jdbc.updateBaseCardOrgForAgentOrderOnlyE(tel, orderID, cardAdmin.getId());

	if(!Tools.isEmpty(cid)){
		BaseCardBean baseCardBean = jdbc.getCardByCID2(cid);
		SMSSender.sendActiveCardNotification(tel, baseCardBean.getId(), baseCardBean.getPasswd());
		out.print("reload:refresh-list");
	}else{
		out.println("发卡失败，原因：卡号库用完，请尽快补卡");
	}
}else if("F".equals(module)){ //（单系统）全程
	String cid = jdbc.updateBaseCardOrgForAgentOrderOnlyF(tel, orderID, cardAdmin.getId());

	if(!Tools.isEmpty(cid)){
		BaseCardBean baseCardBean = jdbc.getCardByCID2(cid);
		SMSSender.sendActiveCardNotification(tel, baseCardBean.getId(), baseCardBean.getPasswd());
		out.print("reload:refresh-list");
	}else{
		out.println("发卡失败，原因：卡号库用完，请尽快补卡");
	}
}else if("G".equals(module)){ //（单系统）全程
	String cid = jdbc.updateBaseCardOrgForAgentOrderOnlyG(tel, orderID, cardAdmin.getId());

	if(!Tools.isEmpty(cid)){
		BaseCardBean baseCardBean = jdbc.getCardByCID2(cid);
		SMSSender.sendActiveCardNotification(tel, baseCardBean.getId(), baseCardBean.getPasswd());
		out.print("reload:refresh-list");
	}else{
		out.println("发卡失败，原因：卡号库用完，请尽快补卡");
	}
}else{
	out.println("暂不支持");
}




%>


		