package com.career.db;

import java.util.ArrayList;
import java.util.List;

public class SuperFormMain{

	private int id;
	private String batch_id;
	private int score_cj;
	private int score_wc;
	private String score_xk;
	private int nf;
	private String pc;
	private String pc_code;
	

	public void initMakerMain(Object makerMain) {
		if(makerMain instanceof RtCheckerApplyFormMakerMain) {
			RtCheckerApplyFormMakerMain formMain = (RtCheckerApplyFormMakerMain)makerMain;
			this.id = formMain.getId();
	        this.batch_id = formMain.getBatch_id();
	        this.score_cj = formMain.getScore_cj();
	        this.score_wc = formMain.getScore_wc();
	        this.score_xk = formMain.getScore_xk();
	        this.nf = formMain.getNf();
	        this.pc = formMain.getPc();
	        this.pc_code = formMain.getPc_code();
		}else if(makerMain instanceof LhyFormMain) {
			LhyFormMain formMain = (LhyFormMain)makerMain;
			this.id = formMain.getId();
	        this.batch_id = formMain.getBatch_id();
	        this.score_cj = formMain.getScore_cj();
	        this.score_wc = formMain.getScore_wc();
	        this.score_xk = formMain.getScore_xk();
	        this.nf = formMain.getNf();
	        this.pc = formMain.getPc();
	        this.pc_code = formMain.getPc_code();
		}else if(makerMain instanceof ZyzdFormMain) {
			ZyzdFormMain formMain = (ZyzdFormMain)makerMain;
			this.id = formMain.getId();
	        this.batch_id = formMain.getBatch_id();
	        this.score_cj = formMain.getScore_cj();
	        this.score_wc = formMain.getScore_wc();
	        this.score_xk = formMain.getScore_xk();
	        this.nf = formMain.getNf();
	        this.pc = formMain.getPc();
	        this.pc_code = formMain.getPc_code();
		}else if(makerMain instanceof AiTbFormMain) {
			AiTbFormMain formMain = (AiTbFormMain)makerMain;
			this.id = formMain.getId();
	        this.batch_id = formMain.getBatch_id();
	        this.score_cj = formMain.getScore_cj();
	        this.score_wc = formMain.getScore_wc();
	        this.score_xk = formMain.getScore_xk();
	        this.nf = formMain.getNf();
	        this.pc = formMain.getPc();
	        this.pc_code = formMain.getPc_code();
		}
	}
	
	public void initMakerFormList(List<?> makerFormList) {
		for(Object bean : makerFormList) {
			SuperForm superForm = null;
			if(bean instanceof RtCheckerApplyFormMaker) {
				RtCheckerApplyFormMaker makerForm = (RtCheckerApplyFormMaker)bean;
				superForm = new SuperForm();
				superForm.setId(makerForm.getId());
		        superForm.setYxmc(makerForm.getYxmc());
		        superForm.setZymc(makerForm.getZymc());
		        superForm.setYxdm(makerForm.getYxdm());
		        superForm.setZydm(makerForm.getZydm());
		        superForm.setZyz(makerForm.getZyz());
		        superForm.setZymc_org(makerForm.getZymc_org());
		        superForm.setYxmc_org(makerForm.getYxmc_org());
		        
		        superForm.setSeq_no_yx(makerForm.getSeq_no_yx());
		        superForm.setSeq_no_zy(makerForm.getSeq_no_zy());
		        superForm.setAdjust_dx(makerForm.getAdjust_dx());
		        superForm.setAdjust_zy(makerForm.getAdjust_zy());
			}else if(bean instanceof LhyForm) {
				LhyForm makerForm = (LhyForm)bean;
				superForm = new SuperForm();
				superForm.setId(makerForm.getId());
		        superForm.setYxmc(makerForm.getYxmc());
		        superForm.setZymc(makerForm.getZymc());
		        superForm.setYxdm(makerForm.getYxdm());
		        superForm.setZydm(makerForm.getZydm());
		        superForm.setZyz(makerForm.getZyz());
		        superForm.setZymc_org(makerForm.getZymc_org());
		        superForm.setYxmc_org(makerForm.getYxmc_org());
			}else if(bean instanceof ZyzdForm) {
				ZyzdForm makerForm = (ZyzdForm)bean;
				superForm = new SuperForm();
				superForm.setId(makerForm.getId());
		        superForm.setYxmc(makerForm.getYxmc());
		        superForm.setZymc(makerForm.getZymc());
		        superForm.setYxdm(makerForm.getYxdm());
		        superForm.setZydm(makerForm.getZydm());
		        superForm.setZyz(makerForm.getZyz());
		        superForm.setZymc_org(makerForm.getZymc_org());
		        superForm.setYxmc_org(makerForm.getYxmc_org());
			}else if(bean instanceof AiTbForm) {
				AiTbForm makerForm = (AiTbForm)bean;
				superForm = new SuperForm();
				superForm.setId(makerForm.getId());
		        superForm.setYxmc(makerForm.getYxmc());
		        superForm.setZymc(makerForm.getZymc());
		        superForm.setYxdm(makerForm.getYxdm());
		        superForm.setZydm(makerForm.getZydm());
		        superForm.setZyz(makerForm.getZyz());
		        superForm.setZymc_org(makerForm.getZymc_org());
		        superForm.setYxmc_org(makerForm.getYxmc_org());
			}
			superFormList.add(superForm);
		}
	}
	
	private List<SuperForm> superFormList = new ArrayList<>();
	
	public List<SuperForm> getSuperFormList() {
		return superFormList;
	}
	public void setSuperFormList(List<SuperForm> superFormList) {
		this.superFormList = superFormList;
	}
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getBatch_id() {
		return batch_id;
	}
	public void setBatch_id(String batch_id) {
		this.batch_id = batch_id;
	}
	public int getScore_cj() {
		return score_cj;
	}
	public void setScore_cj(int score_cj) {
		this.score_cj = score_cj;
	}
	public int getScore_wc() {
		return score_wc;
	}
	public void setScore_wc(int score_wc) {
		this.score_wc = score_wc;
	}
	public String getScore_xk() {
		return score_xk;
	}
	public void setScore_xk(String score_xk) {
		this.score_xk = score_xk;
	}
	public int getNf() {
		return nf;
	}
	public void setNf(int nf) {
		this.nf = nf;
	}
	public String getPc() {
		return pc;
	}
	public void setPc(String pc) {
		this.pc = pc;
	}
	public String getPc_code() {
		return pc_code;
	}
	public void setPc_code(String pc_code) {
		this.pc_code = pc_code;
	}
	
	
}
