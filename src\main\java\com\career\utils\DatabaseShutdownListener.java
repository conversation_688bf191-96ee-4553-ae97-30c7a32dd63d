package com.career.utils;

import jakarta.servlet.ServletContextEvent;
import jakarta.servlet.ServletContextListener;

/**
 * 数据库关闭监听器
 * 负责在应用启动和关闭时管理数据库连接
 */
public class DatabaseShutdownListener implements ServletContextListener {
    
    @Override
    public void contextInitialized(ServletContextEvent sce) {
        // 启动数据库监控
        DatabaseMonitor.startMonitoring();
        System.out.println("数据库监控已启动");
        
        // 添加JVM关闭钩子作为额外保障
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            DatabaseMonitor.stopMonitoring();
            com.career.db.ConnectionPoolManager.shutdown();
        }));
    }
    
    @Override
    public void contextDestroyed(ServletContextEvent sce) {
        // 应用关闭时的清理工作
        // DatabaseMonitor.stopMonitoring();
    	try {
    		com.career.db.ConnectionPoolManager.shutdown();
    	} catch(Exception e) {
    		e.printStackTrace();
    	}
        
        System.out.println("数据库连接池已关闭");
    }
}