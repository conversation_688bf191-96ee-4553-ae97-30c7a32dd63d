package com.zsdwf.pdf;

import com.career.db.CareerJdbc;
import com.career.db.CareerJiuye;
import com.career.db.HZBXBean;
import com.career.db.JDBC;
import com.career.db.LXSchoolBean;
import com.career.db.LXShoolRank;
import com.career.db.SQABean;
import com.career.db.SchoolMarjorCommonBean;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.*;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.Date;
import java.util.HashSet;

import com.itextpdf.text.pdf.AcroFields;
import com.itextpdf.text.pdf.PdfContentByte;
import com.zsdwf.db.PredictBean;
import com.zsdwf.db.YGDataPatchDBTools;
import com.zsdwf.utils.Tools;


public class PDFUtils {
	
	public static void main(String args[]) {
		try {
			File file = new File("G:\\2024年最新数据\\s_data_major.sql");
			File file2 = new File("G:\\2024年最新数据\\s_data_major_new.sql");
			BufferedReader br = new BufferedReader(new FileReader(file));
			StringBuffer sb = new StringBuffer();
			String line = null;
			while((line = br.readLine()) != null) {
				sb.append(line.replaceAll("\"", ""));
			}
			BufferedWriter bw = new BufferedWriter(new FileWriter(file2));
			bw.write(sb.toString());
			bw.flush();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	public static void addImg(Document document, String imgPath, float newWidth, float newHeight,
            float absoluteX, float absoluteY) throws IOException, DocumentException {
		Image img1 = Image.getInstance(imgPath);
		img1.setAbsolutePosition(absoluteX, absoluteY);
		img1.scaleAbsolute(newWidth, newHeight);
		document.add(img1);
	}

	List<SchoolMarjorCommonBean> listLatest = null;
	List<LXSchoolBean> listLXSchoolBean = null;
	List<CareerJiuye> listCareerJiuye = null;
	List<SQABean> sqaBeanList = null;
	List<HZBXBean> hzbxBeanList = null;
	public void createPdfFile(String fileName, PDFGaoZhongStudentInfo info) throws Exception {
		Document document = new Document();
		Rectangle pageSize = PageSize.A4;
		document.setPageSize(pageSize);
		String folder = fileName.substring(fileName.length() - 2);
		File file = new File("C:\\nginx-1.26.0\\html\\gh2024\\" + folder + "\\");
		if(!file.exists()) {
			file.mkdirs();
		}
		PdfWriter writer = PdfWriter.getInstance(document, new FileOutputStream("C:\\nginx-1.26.0\\html\\gh2024\\"+folder+"\\lx2b-"+fileName+".pdf"));
		BaseFont bfChinese = BaseFont.createFont("STSong-Light","UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
		
        ArrayList<BaseFont> fontList = new ArrayList<>();
        fontList.add(bfChinese);

        document.open();
        
        JDBC jdbc = new JDBC();
        listLatest = jdbc.getPDFGaoKaoUniversityList(info, 2023);
        
        Collections.sort(listLatest);
        
        
        listCareerJiuye = jdbc.getPDFGaoKaoUniversityWorkList(info, 2023);
        
        hzbxBeanList = jdbc.getPDFGaokaoHZBXUniversityWorkList(info, 2023);
        
        CareerJdbc careerJdbc = new CareerJdbc();
		String gpa = "不限",eng = "不限",bgt = "不限";
		if(info.getScore_all() >= 480 && info.getScore_all() < 550) {
			gpa = "较好";
		}else if(info.getScore_all() > 380 && info.getScore_all() < 480) {
			gpa = "中等";
		}else if(info.getScore_all() >= 550){
			gpa = "不限";
		}else {
			gpa = "中下游";
		}
		
		if(info.getScore_eng().equals("60分以下")) {
			eng = "较差";
		}else if(info.getScore_eng().equals("60~90分")) {
			eng = "一般";
		}else if(info.getScore_eng().equals("90~120分")) {
			eng = "4级水平";
		}else {
			eng = "不限";
		}
		
		if(info.getLx_budget().equals("5万/年")) {
			bgt = "5~9万";
		}else if(info.getLx_budget().equals("8万/年")) {
			bgt = "12万/年";
		}else if(info.getLx_budget().equals("15万/年")) {
			bgt = "18万/年";
		}else if(info.getLx_budget().equals("25万/年")) {
			bgt = "30万以上";
		}else {
			bgt = "不限";
		}
		listLXSchoolBean = careerJdbc.listLXSchoolByCondition2("%", gpa, eng, bgt, "%", "%", 1);
		
		sqaBeanList = jdbc.getPDFSQAUniversityForLX();
		
        createBlankTable(writer, document, bfChinese, 140);
        addImg(document, "C://lx2b_bg.png", pageSize.getWidth(), pageSize.getHeight(),0,0);
        createTableStudentInfo(writer, document, info);
        createBlankTable(writer, document, bfChinese, 30);
        createTableMainSummary(writer, document, info);
        
        //addImg(document, "G://0518.png", pageSize.getWidth(), pageSize.getHeight(),0,0);
 
        createBlankTable(writer, document, bfChinese, 100);
        createTableGaoKao(writer, document, info);
        createBlankTable(writer, document, bfChinese, 30);
        createTableJiuye(writer, document, info);
        createBlankTable(writer, document, bfChinese, 30);
        createTableHZBX(writer, document, info);
        createBlankTable(writer, document, bfChinese, 30);
        createTableSQA(writer, document, info);
        createBlankTable(writer, document, bfChinese, 30);
        createTableLiuxue(writer, document, info);
        
        document.close();
        //outputStream.close();
    }
 
    //为一个表格添加内容
    public PdfPCell createSetCell(String value, Font font) {
        PdfPCell cell = new PdfPCell();
        cell.setPhrase(new Phrase(value, font));
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        return cell;
    }
 
 
    //添加表格
    public void createTableGaoKao(PdfWriter writer, Document document, PDFGaoZhongStudentInfo info) throws DocumentException, IOException {
 
    	
    	
        PdfPTable table = new PdfPTable(new float[]{15, 80, 80, 20, 20, 20, 25});
        table.setTotalWidth(520);
        table.setPaddingTop(500);
        table.setLockedWidth(true);
        table.setHorizontalAlignment(Element.ALIGN_CENTER);//居中
        table.writeSelectedRows(0, -1, 500, 800, writer.getDirectContentUnder());
        //每页都显示表头,输入几就是第几行的表头固定
        table.setHeaderRows(1);
        table.setHeaderRows(2);
 
        //定义数据的字体
        BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        Font textFont = new Font(baseFont, 10, Font.NORMAL);
        Font textFontSpec = new Font(baseFont, 10, Font.NORMAL);
        textFontSpec.setColor(0, 0, 0);
        
        Font textFontTitle = new Font(baseFont, 12, Font.BOLD);
        textFontTitle.setColor(255, 0, 0);
        
        Font textFontYCRed = new Font(baseFont, 10, Font.BOLD);
        textFontYCRed.setColor(255, 0, 0);
        Font textFontYCGreen = new Font(baseFont, 10, Font.BOLD);
        textFontYCGreen.setColor(0, 0, 255);
        
 
        //表头信息
        PdfPCell heandCell = new PdfPCell();
        heandCell.setRowspan(1);
        heandCell.setColspan(7);
        heandCell.setFixedHeight(30);
        heandCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        heandCell.setHorizontalAlignment(Element.ALIGN_CENTER);
        heandCell.setPhrase(new Phrase("推荐院校/专业清单", textFontTitle));
        table.addCell(heandCell);
 
        //表字段
        String title[] = {"序号", "院校名称", "专业名称", "21位次", "22位次","23位次","预测位次"};
        for (int i = 0; i < title.length; i++) {
            PdfPCell heardCell = new PdfPCell();
            heardCell.setRowspan(1);
            heardCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            heardCell.setHorizontalAlignment(Element.ALIGN_CENTER);
            heardCell.setPhrase(new Phrase(title[i], textFont));
            heardCell.setMinimumHeight(20);
            table.addCell(heardCell);
        }
 
 
        //列表数据
        List<RecInfo> recInfoList = new ArrayList<>();
        for (int i = 1; i <= listLatest.size(); i++) {
        	SchoolMarjorCommonBean majorBean = listLatest.get(i-1);
        	RecInfo recInfo = new RecInfo();
        	recInfo.setXh(String.valueOf(i+1));
        	recInfo.setYxmc(majorBean.getYxmc());
        	recInfo.setZymc(majorBean.getZymc());
        	recInfo.setWc2021(majorBean.getZdfwc21());
        	recInfo.setWc2022(majorBean.getZdfwc22());
        	recInfo.setWc2023(majorBean.getZdfwc23());
        	recInfo.setWc2024(majorBean.getZdfwc24());
        	recInfoList.add(recInfo);
        }
 
        for (int i = 0; i < recInfoList.size(); i++) {
        	RecInfo recInfo = recInfoList.get(i);
            PdfPCell setCell1 = createSetCell(recInfo.getXh(), textFont);
            PdfPCell setCell2 = createSetCell(recInfo.getYxmc(), textFont);
            PdfPCell setCell3 = createSetCell(recInfo.getZymc(), textFont);
            PdfPCell setCell4 = createSetCell(recInfo.getWc2021(), textFont);
            PdfPCell setCell5 = createSetCell(recInfo.getWc2022(), textFont);
            PdfPCell setCell6 = createSetCell(recInfo.getWc2023(), textFont);
            PdfPCell setCell7 = createSetCell(recInfo.getWc2024(), Tools.getInt(recInfo.getWc2024()) > info.getScore_wc() ? textFontYCGreen : textFontYCRed);
            table.addCell(setCell1);
            table.addCell(setCell2);
            table.addCell(setCell3);
            table.addCell(setCell4);
            table.addCell(setCell5);
            table.addCell(setCell6);
            table.addCell(setCell7);
        }
        document.add(table);
    }
    
    
    public void createTableJiuye(PdfWriter writer, Document document, PDFGaoZhongStudentInfo info) throws DocumentException, IOException {

        PdfPTable table = new PdfPTable(new float[]{15, 120, 120, 40});
        table.setTotalWidth(520);
        table.setPaddingTop(500);
        table.setLockedWidth(true);
        table.setHorizontalAlignment(Element.ALIGN_CENTER);//居中
        table.writeSelectedRows(0, -1, 500, 800, writer.getDirectContentUnder());
        //每页都显示表头,输入几就是第几行的表头固定
        table.setHeaderRows(1);
        table.setHeaderRows(2);
 
        //定义数据的字体
        BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        Font textFont = new Font(baseFont, 10, Font.NORMAL);
        
        BaseFont baseFontYC = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        Font textFontYC = new Font(baseFont, 10, Font.BOLD);
        textFontYC.setColor(255, 0, 0);
        
        Font textFontTitle = new Font(baseFont, 12, Font.BOLD);
        textFontTitle.setColor(255, 0, 0);
 
        //表头信息
        PdfPCell heandCell = new PdfPCell();
        heandCell.setRowspan(1);
        heandCell.setColspan(7);
        heandCell.setFixedHeight(30);
        heandCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        heandCell.setHorizontalAlignment(Element.ALIGN_CENTER);
        heandCell.setPhrase(new Phrase("推荐院校就业情况（按录取人数降序）", textFontTitle));
        table.addCell(heandCell);
 
        //表字段
        String title[] = {"序号", "院校名称", "行业/企业", "录取人数"};
        for (int i = 0; i < title.length; i++) {
            PdfPCell heardCell = new PdfPCell();
            heardCell.setRowspan(1);
            heardCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            heardCell.setHorizontalAlignment(Element.ALIGN_CENTER);
            heardCell.setPhrase(new Phrase(title[i], textFont));
            heardCell.setMinimumHeight(20);
            table.addCell(heardCell);
        }
 
 
        //列表数据
        for (int i = 0; i < listCareerJiuye.size(); i++) {
        	CareerJiuye careerJiuye = listCareerJiuye.get(i);
        	PdfPCell setCell1 = createSetCell((String.valueOf(i+1)), textFont);
            PdfPCell setCell2 = createSetCell(careerJiuye.getYxmc(), textFont);
            PdfPCell setCell3 = createSetCell(careerJiuye.getQymc(), textFont);
            PdfPCell setCell4 = createSetCell(String.valueOf(careerJiuye.getCnt()), textFont);
            table.addCell(setCell1);
            table.addCell(setCell2);
            table.addCell(setCell3);
            table.addCell(setCell4);
        }
        document.add(table);
    }
    
    
    
    public void createTableLiuxue(PdfWriter writer, Document document, PDFGaoZhongStudentInfo info) throws DocumentException, IOException {
    	
        PdfPTable table = new PdfPTable(new float[]{15, 120, 120, 120, 40});
        table.setTotalWidth(520);
        table.setPaddingTop(500);
        table.setLockedWidth(true);
        table.setHorizontalAlignment(Element.ALIGN_CENTER);//居中
        table.writeSelectedRows(0, -1, 500, 800, writer.getDirectContentUnder());
        //每页都显示表头,输入几就是第几行的表头固定
        table.setHeaderRows(1);
        table.setHeaderRows(2);
 
        //定义数据的字体
        BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        Font textFont = new Font(baseFont, 10, Font.NORMAL);
        Font textFontRed = new Font(baseFont, 8, Font.NORMAL);
        textFontRed.setColor(255, 0, 0);
        
        BaseFont baseFontYC = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        Font textFontYC = new Font(baseFont, 10, Font.BOLD);
        textFontYC.setColor(255, 0, 0);
        
        Font textFontTitle = new Font(baseFont, 12, Font.BOLD);
        textFontTitle.setColor(255, 0, 0);
 
        //表头信息
        PdfPCell heandCell = new PdfPCell();
        heandCell.setRowspan(1);
        heandCell.setColspan(7);
        heandCell.setFixedHeight(30);
        heandCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        heandCell.setHorizontalAlignment(Element.ALIGN_CENTER);
        heandCell.setPhrase(new Phrase("出国留学院校推荐", textFontTitle));
        table.addCell(heandCell);
 
        //表字段
        String title[] = {"序号", "院校名称", "英文名称", "对标国内", "所在国家"};
        for (int i = 0; i < title.length; i++) {
            PdfPCell heardCell = new PdfPCell();
            heardCell.setRowspan(1);
            heardCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            heardCell.setHorizontalAlignment(Element.ALIGN_CENTER);
            heardCell.setPhrase(new Phrase(title[i], textFont));
            heardCell.setMinimumHeight(20);
            table.addCell(heardCell);
        }
        
        CareerJdbc careerJdbc = new CareerJdbc();
        List<LXShoolRank> rankList = careerJdbc.loadAllChinaUniversityRank("QS世界大学排名",2024);
 
        //列表数据
        for (int i = 0; i < listLXSchoolBean.size(); i++) {
        	LXSchoolBean bean = listLXSchoolBean.get(i);
        	
        	LXShoolRank rankOut = null;
    		for(LXShoolRank rank : rankList){
    			int RANK_SPAN = 100; 
    			if(bean.getWorldRankValue() < 150){
    				RANK_SPAN = 30; 
    			}else if(bean.getWorldRankValue() < 601){
    				RANK_SPAN = 60; 
    			}
    			if(rank.getRankNum() >= (bean.getWorldRankValue() - (int)(RANK_SPAN / 3)) && rank.getRankNum() <= (bean.getWorldRankValue() + RANK_SPAN)){
    				rankOut = rank;
    				break;
    			}
    		}
        	
        	PdfPCell setCell1 = createSetCell((String.valueOf(i+1)), textFont);
            PdfPCell setCell2 = createSetCell(bean.getSchool_name_cn(), textFont);
            PdfPCell setCell3 = createSetCell(bean.getSchool_name(), textFont);
            PdfPCell setCell4 = createSetCell(rankOut == null ? "--" : rankOut.getYxmc(), textFontRed);
            PdfPCell setCell5 = createSetCell(bean.getCountry(), textFont);
            table.addCell(setCell1);
            table.addCell(setCell2);
            table.addCell(setCell3);
            table.addCell(setCell4);
            table.addCell(setCell5);
        }
        document.add(table);
    }
 
    public void createTableSQA(PdfWriter writer, Document document, PDFGaoZhongStudentInfo info) throws DocumentException, IOException {
    	
        PdfPTable table = new PdfPTable(new float[]{20,70, 120, 120, 60});
        table.setTotalWidth(520);
        table.setPaddingTop(500);
        table.setLockedWidth(true);
        table.setHorizontalAlignment(Element.ALIGN_CENTER);//居中
        table.writeSelectedRows(0, -1, 500, 800, writer.getDirectContentUnder());
        //每页都显示表头,输入几就是第几行的表头固定
        table.setHeaderRows(1);
        table.setHeaderRows(2);
 
        //定义数据的字体
        BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        Font textFont = new Font(baseFont, 10, Font.NORMAL);
        
        BaseFont baseFontYC = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        Font textFontYC = new Font(baseFont, 10, Font.BOLD);
        textFontYC.setColor(255, 0, 0);
        
        Font textFontTitle = new Font(baseFont, 12, Font.BOLD);
        textFontTitle.setColor(255, 0, 0);
 
        //表头信息
        PdfPCell heandCell = new PdfPCell();
        heandCell.setRowspan(1);
        heandCell.setColspan(7);
        heandCell.setFixedHeight(30);
        heandCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        heandCell.setHorizontalAlignment(Element.ALIGN_CENTER);
        heandCell.setPhrase(new Phrase("国际本科SQA/2+2/3+1/4+0推荐", textFontTitle));
        table.addCell(heandCell);
 
        //表字段
        String title[] = {"序号", "院校名称", "招生专业", "学费介绍", "录取要求"};
        for (int i = 0; i < title.length; i++) {
            PdfPCell heardCell = new PdfPCell();
            heardCell.setRowspan(1);
            heardCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            heardCell.setHorizontalAlignment(Element.ALIGN_CENTER);
            heardCell.setPhrase(new Phrase(title[i], textFont));
            heardCell.setMinimumHeight(20);
            table.addCell(heardCell);
        }
        
 
        //列表数据
        for (int i = 0; i < sqaBeanList.size(); i++) {
        	SQABean bean = sqaBeanList.get(i);
        	PdfPCell setCell1 = createSetCell((String.valueOf(i+1)), bean.getYxmc_in().equals("四川大学") ? textFontYC:textFont);
            PdfPCell setCell2 = createSetCell(bean.getYxmc_in(), bean.getYxmc_in().equals("四川大学") ? textFontYC:textFont);
            PdfPCell setCell3 = createSetCell(bean.getZymc(), bean.getYxmc_in().equals("四川大学") ? textFontYC:textFont);
            PdfPCell setCell4 = createSetCell(bean.getXf(), bean.getYxmc_in().equals("四川大学") ? textFontYC:textFont);
            PdfPCell setCell5 = createSetCell(bean.getZstj(), bean.getYxmc_in().equals("四川大学") ? textFontYC:textFont);
            table.addCell(setCell1);
            table.addCell(setCell2);
            table.addCell(setCell3);
            table.addCell(setCell4);
            table.addCell(setCell5);
        }
        document.add(table);
    }
    
    public void createTableHZBX(PdfWriter writer, Document document, PDFGaoZhongStudentInfo info) throws DocumentException, IOException {
    	
        PdfPTable table = new PdfPTable(new float[]{20,50, 100, 90, 30, 60});
        table.setTotalWidth(520);
        table.setPaddingTop(500);
        table.setLockedWidth(true);
        table.setHorizontalAlignment(Element.ALIGN_CENTER);//居中
        table.writeSelectedRows(0, -1, 500, 800, writer.getDirectContentUnder());
        //每页都显示表头,输入几就是第几行的表头固定
        table.setHeaderRows(1);
        table.setHeaderRows(2);
 
        //定义数据的字体
        BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        Font textFont = new Font(baseFont, 8, Font.NORMAL);
        
        BaseFont baseFontYC = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        Font textFontYC = new Font(baseFont, 10, Font.BOLD);
        textFontYC.setColor(255, 0, 0);
        
        Font textFontTitle = new Font(baseFont, 12, Font.BOLD);
        textFontTitle.setColor(255, 0, 0);
 
        //表头信息
        PdfPCell heandCell = new PdfPCell();
        heandCell.setRowspan(1);
        heandCell.setColspan(7);
        heandCell.setFixedHeight(30);
        heandCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        heandCell.setHorizontalAlignment(Element.ALIGN_CENTER);
        heandCell.setPhrase(new Phrase("合作办学（计划内）推荐", textFontTitle));
        table.addCell(heandCell);
 
        //表字段
        String title[] = {"序号", "院校名称", "合作项目", "开设专业", "学费", "有效期"};
        for (int i = 0; i < title.length; i++) {
            PdfPCell heardCell = new PdfPCell();
            heardCell.setRowspan(1);
            heardCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            heardCell.setHorizontalAlignment(Element.ALIGN_CENTER);
            heardCell.setPhrase(new Phrase(title[i], textFont));
            heardCell.setMinimumHeight(20);
            table.addCell(heardCell);
        }
        
 
        //列表数据
        for (int i = 0; i < hzbxBeanList.size(); i++) {
        	HZBXBean bean = hzbxBeanList.get(i);
        	PdfPCell setCell1 = createSetCell((String.valueOf(i+1)), textFont);
            PdfPCell setCell2 = createSetCell(bean.getYxmc_in_org(), textFont);
            PdfPCell setCell3 = createSetCell(bean.getMc(), textFont);
            PdfPCell setCell4 = createSetCell(bean.getKskc(), textFont);
            PdfPCell setCell5 = createSetCell(bean.getXf(), textFont);
            PdfPCell setCell6 = createSetCell(bean.getZsks(), textFont);
            table.addCell(setCell1);
            table.addCell(setCell2);
            table.addCell(setCell3);
            table.addCell(setCell4);
            table.addCell(setCell5);
            table.addCell(setCell6);
        }
        document.add(table);
    }
    
    public void createTableStudentInfo(PdfWriter writer, Document document, PDFGaoZhongStudentInfo info) throws DocumentException, IOException {
 
        PdfPTable table = new PdfPTable(new float[]{50, 90});
        table.setTotalWidth(420);
        table.setPaddingTop(500);
        table.setLockedWidth(true);
        table.setHorizontalAlignment(Element.ALIGN_CENTER);//居中
        table.writeSelectedRows(0, -1, 500, 800, writer.getDirectContentUnder());
        //每页都显示表头,输入几就是第几行的表头固定
        table.setHeaderRows(2);
        table.setHeaderRows(3);
 
        //定义数据的字体
        BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        Font textFont = new Font(baseFont, 12, Font.NORMAL);
        Font textFontValue = new Font(baseFont, 10, Font.NORMAL);
        
        Font textFontTitle = new Font(baseFont, 12, Font.BOLD);
        textFontTitle.setColor(255, 0, 0);
 
        //表头信息
        PdfPCell heandCell = new PdfPCell();
        heandCell.setRowspan(1);
        heandCell.setColspan(2);
        heandCell.setFixedHeight(30);
        heandCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        heandCell.setHorizontalAlignment(Element.ALIGN_CENTER);
        heandCell.setPhrase(new Phrase("学生基本信息（高考志愿推荐规划报告）", textFontTitle));
        table.addCell(heandCell);
 
        //表字段
        /**
        String title[] = {"升学路径", "可选数量"};
        for (int i = 0; i < title.length; i++) {
            PdfPCell heardCell = new PdfPCell();
            heardCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            heardCell.setHorizontalAlignment(Element.ALIGN_CENTER);
            heardCell.setPhrase(new Phrase(title[i], textFont));
            heardCell.setMinimumHeight(20);
            table.addCell(heardCell);
        }
        */
 
        //列表数据
 
        List<MainInfo> mainTables = new ArrayList<>();
        MainInfo mainInfo = new MainInfo();
    	mainInfo.setLabel("高考位次");
    	mainInfo.setValue(info.getProvinceName() + " " + info.getXk() + " (位次:"+info.getScore_wc()+")");
        mainTables.add(mainInfo);
        
        mainInfo = new MainInfo();
    	mainInfo.setLabel("英语成绩");
    	mainInfo.setValue(info.getScore_eng());
        mainTables.add(mainInfo);

        mainInfo = new MainInfo();
    	mainInfo.setLabel("数学成绩");
    	mainInfo.setValue(info.getScore_math());
        mainTables.add(mainInfo);
        
        
        mainInfo = new MainInfo();
    	mainInfo.setLabel("意向专业");
    	mainInfo.setValue(Tools.getSQLQueryin2(info.getMajor_catg_list()));
        mainTables.add(mainInfo);
        
        mainInfo = new MainInfo();
    	mainInfo.setLabel("留学预算");
    	mainInfo.setValue(info.getLx_budget());
        mainTables.add(mainInfo);
        
 
        for (int i = 0; i < mainTables.size(); i++) {
        	MainInfo mainInfo2 = mainTables.get(i);
            PdfPCell setCell2 = createSetCell(mainInfo2.getLabel(), textFont);
            setCell2.setFixedHeight(25);
            PdfPCell setCell3 = createSetCell(mainInfo2.getValue(), textFontValue);
            setCell3.setFixedHeight(25);
            table.addCell(setCell2);
            table.addCell(setCell3);
        }
        document.add(table);
    }
    
    public void createTableMainSummary(PdfWriter writer, Document document, PDFGaoZhongStudentInfo info) throws DocumentException, IOException {
    	
        PdfPTable table = new PdfPTable(new float[]{80, 50});
        table.setTotalWidth(420);
        table.setPaddingTop(500);
        table.setLockedWidth(true);
        table.setHorizontalAlignment(Element.ALIGN_CENTER);//居中
        table.writeSelectedRows(0, -1, 500, 800, writer.getDirectContentUnder());
        //每页都显示表头,输入几就是第几行的表头固定
        table.setHeaderRows(2);
        table.setHeaderRows(3);
 
        //定义数据的字体
        BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        Font textFont = new Font(baseFont, 12, Font.NORMAL);
        Font textFontValue = new Font(baseFont, 10, Font.NORMAL);
        textFontValue.setColor(255, 0, 0);
        
        Font textFontTitle = new Font(baseFont, 12, Font.BOLD);
        textFontTitle.setColor(255, 0, 0);
        
        //表头信息
        PdfPCell heandCell = new PdfPCell();
        heandCell.setRowspan(1);
        heandCell.setColspan(2);
        heandCell.setFixedHeight(30);
        heandCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        heandCell.setHorizontalAlignment(Element.ALIGN_CENTER);
        heandCell.setPhrase(new Phrase("高考升学全路径规划报告(总览)", textFontTitle));
        table.addCell(heandCell);
 
        //表字段
        /**
        String title[] = {"升学路径", "可选数量"};
        for (int i = 0; i < title.length; i++) {
            PdfPCell heardCell = new PdfPCell();
            heardCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            heardCell.setHorizontalAlignment(Element.ALIGN_CENTER);
            heardCell.setPhrase(new Phrase(title[i], textFont));
            heardCell.setMinimumHeight(20);
            table.addCell(heardCell);
        }
        */
 
        //列表数据
        HashSet<String> schoolList = new HashSet<>();
        HashSet<String> majorList = new HashSet<>();
        SchoolMarjorCommonBean nearestSchool = null;
        
        for(int i = 0;i < listLatest.size();i++) {
        	SchoolMarjorCommonBean schoolMarjorCommonBean = listLatest.get(i);
        	schoolList.add(schoolMarjorCommonBean.getYxmc_org());
        	majorList.add(schoolMarjorCommonBean.getZymc_org());
        	if(Tools.getInt(schoolMarjorCommonBean.getZdfwc24()) > info.getScore_wc() || nearestSchool == null) {
        		nearestSchool = schoolMarjorCommonBean;
        	}
        }
 
        List<MainInfo> mainTables = new ArrayList<>();
        MainInfo mainInfo = new MainInfo();
    	mainInfo.setLabel("志愿填报-可选大学");
    	mainInfo.setValue(schoolList.size() + "所");
        mainTables.add(mainInfo);
        
        mainInfo = new MainInfo();
        mainInfo.setLabel("志愿填报-可选专业");
    	mainInfo.setValue(majorList.size() + "个");
        mainTables.add(mainInfo);

        mainInfo = new MainInfo();
    	mainInfo.setLabel("志愿填报-预估录取学校");
    	mainInfo.setValue(nearestSchool == null ? "--" : nearestSchool.getYxmc());
        mainTables.add(mainInfo);

        mainInfo = new MainInfo();
    	mainInfo.setLabel("志愿填报-预估录取专业");
    	mainInfo.setValue(nearestSchool == null ? "--" : nearestSchool.getZymc_org());
        mainTables.add(mainInfo);
        
        HashSet<String> schoolHZBXList = new HashSet<>();
        for(int i = 0;i < hzbxBeanList.size();i++) {
        	HZBXBean sqaBean = hzbxBeanList.get(i);
        	schoolHZBXList.add(sqaBean.getYxmc_in_org());
        }
        
        mainInfo = new MainInfo();
    	mainInfo.setLabel("合作办学（计划内）-可选项目");
    	mainInfo.setValue(hzbxBeanList.size() + "个");
        mainTables.add(mainInfo);
        
        mainInfo = new MainInfo();
    	mainInfo.setLabel("合作办学（计划内）-涉及大学");
    	mainInfo.setValue(schoolHZBXList.size() + "所");
        mainTables.add(mainInfo);
        
        HashSet<String> schoolSQAList = new HashSet<>();
        for(int i = 0;i < sqaBeanList.size();i++) {
        	SQABean sqaBean = sqaBeanList.get(i);
        	schoolSQAList.add(sqaBean.getYxmc_in());
        }
        
        mainInfo = new MainInfo();
    	mainInfo.setLabel("国际本科/SQA-可选项目");
    	mainInfo.setValue(sqaBeanList.size() + "个");
        mainTables.add(mainInfo);
        
        mainInfo = new MainInfo();
    	mainInfo.setLabel("国际本科/SQA-涉及大学");
    	mainInfo.setValue(schoolSQAList.size() + "所");
        mainTables.add(mainInfo);
        
        HashSet<String> schoolLXList = new HashSet<>();
        HashSet<String> countryLXList = new HashSet<>();
        for(int i = 0;i < listLXSchoolBean.size();i++) {
        	LXSchoolBean lxSchoolBean = listLXSchoolBean.get(i);
        	schoolLXList.add(lxSchoolBean.getSchool_name_cn());
        	countryLXList.add(lxSchoolBean.getCountry());
        }
        
        mainInfo = new MainInfo();
    	mainInfo.setLabel("直出留学-可选项目");
    	mainInfo.setValue(listLXSchoolBean.size() + "个");
        mainTables.add(mainInfo);
        
        mainInfo = new MainInfo();
    	mainInfo.setLabel("直出留学-涉及大学");
    	mainInfo.setValue(schoolLXList.size() + "所");
        mainTables.add(mainInfo);
        
        mainInfo = new MainInfo();
    	mainInfo.setLabel("直出留学-涉及国家");
    	mainInfo.setValue(countryLXList.size() + "个");
        mainTables.add(mainInfo);
        

        HashSet<String> jiuyeQYList = new HashSet<>();
        
        for(int i = 0;i < listCareerJiuye.size();i++) {
        	CareerJiuye careerJiuye = listCareerJiuye.get(i);
        	jiuyeQYList.add(careerJiuye.getQymc());
        }
        mainInfo = new MainInfo();
    	mainInfo.setLabel("考公编制-可去企业");
    	mainInfo.setValue(jiuyeQYList.size() + "家");
        mainTables.add(mainInfo);
        
        
 
        for (int i = 0; i < mainTables.size(); i++) {
        	MainInfo mainInfo2 = mainTables.get(i);
            PdfPCell setCell2 = createSetCell(mainInfo2.getLabel(), textFont);
            setCell2.setFixedHeight(25);
            PdfPCell setCell3 = createSetCell(mainInfo2.getValue(), textFontValue);
            setCell3.setFixedHeight(25);
            table.addCell(setCell2);
            table.addCell(setCell3);
        }
        document.add(table);
    }
    
    
 
    /**
     * 创建表格跟表格之间的空白间隔
     */
    public void createBlankTable(PdfWriter writer, Document document, BaseFont font, int height) throws DocumentException {
        PdfPTable table = new PdfPTable(new float[]{30});
        table.setTotalWidth(520);
        table.setPaddingTop(500);
        table.setLockedWidth(true);
        table.setHorizontalAlignment(Element.ALIGN_CENTER);//居中
        table.writeSelectedRows(0, -1, 500, 800, writer.getDirectContentUnder());
        Font textFont = new Font(font, 10, Font.NORMAL);
        PdfPCell cell = new PdfPCell(new Paragraph(" ", textFont));
 
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
 
        cell.setVerticalAlignment(Element.ALIGN_BOTTOM);
 
        cell.setBorder(Rectangle.NO_BORDER);
        cell.setFixedHeight(height);
 
        cell.setColspan(1);
 
        table.addCell(cell);
        document.add(table);
    }


}
