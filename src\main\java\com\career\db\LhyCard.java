package com.career.db;

import java.util.Date;

import com.career.utils.BaseBean;

public class Lhy<PERSON>ard extends BaseBean{
	
	public static void main(String args[]) {
		printBeanProperties(new ZyzdScoreConvert());
	}
	
	private String c_id;
    private String c_sub_prefix;
    private String p_c_id;
    private String c_passwd;
    private String c_name;
    private String c_remark;
    private String c_phone;
    private String c_prov;
    private String c_allow_prov;
    private String c_function;
    private int c_status;
    private int c_level;
    private int c_type;
    private int order_cnt;
    private int card_cnt;
    private int sub_acct_cnt;
    private int zd_query_cnt;
    private int sys_ind;
    private Date create_tm;
    private Date active_tm;
    private Date last_login_tm;
    private Date expire_tm;
    private String saas_id;
    private String c_batch_id;
    private String c_auto_ind;
	public String getC_id() {
		return c_id;
	}
	public void setC_id(String c_id) {
		this.c_id = c_id;
	}
	public String getC_sub_prefix() {
		return c_sub_prefix;
	}
	public void setC_sub_prefix(String c_sub_prefix) {
		this.c_sub_prefix = c_sub_prefix;
	}
	public String getP_c_id() {
		return p_c_id;
	}
	public void setP_c_id(String p_c_id) {
		this.p_c_id = p_c_id;
	}
	public String getC_passwd() {
		return c_passwd;
	}
	public void setC_passwd(String c_passwd) {
		this.c_passwd = c_passwd;
	}
	public String getC_name() {
		return c_name;
	}
	public void setC_name(String c_name) {
		this.c_name = c_name;
	}
	public String getC_remark() {
		return c_remark;
	}
	public void setC_remark(String c_remark) {
		this.c_remark = c_remark;
	}
	public String getC_phone() {
		return c_phone;
	}
	public void setC_phone(String c_phone) {
		this.c_phone = c_phone;
	}
	public String getC_prov() {
		return c_prov;
	}
	public void setC_prov(String c_prov) {
		this.c_prov = c_prov;
	}
	public String getC_allow_prov() {
		return c_allow_prov;
	}
	public void setC_allow_prov(String c_allow_prov) {
		this.c_allow_prov = c_allow_prov;
	}
	public String getC_function() {
		return c_function;
	}
	public void setC_function(String c_function) {
		this.c_function = c_function;
	}
	public int getC_status() {
		return c_status;
	}
	public void setC_status(int c_status) {
		this.c_status = c_status;
	}
	public int getC_level() {
		return c_level;
	}
	public void setC_level(int c_level) {
		this.c_level = c_level;
	}
	public int getC_type() {
		return c_type;
	}
	public void setC_type(int c_type) {
		this.c_type = c_type;
	}
	public int getOrder_cnt() {
		return order_cnt;
	}
	public void setOrder_cnt(int order_cnt) {
		this.order_cnt = order_cnt;
	}
	public int getCard_cnt() {
		return card_cnt;
	}
	public void setCard_cnt(int card_cnt) {
		this.card_cnt = card_cnt;
	}
	public int getSub_acct_cnt() {
		return sub_acct_cnt;
	}
	public void setSub_acct_cnt(int sub_acct_cnt) {
		this.sub_acct_cnt = sub_acct_cnt;
	}
	public int getZd_query_cnt() {
		return zd_query_cnt;
	}
	public void setZd_query_cnt(int zd_query_cnt) {
		this.zd_query_cnt = zd_query_cnt;
	}
	public int getSys_ind() {
		return sys_ind;
	}
	public void setSys_ind(int sys_ind) {
		this.sys_ind = sys_ind;
	}
	public Date getCreate_tm() {
		return create_tm;
	}
	public void setCreate_tm(Date create_tm) {
		this.create_tm = create_tm;
	}
	public Date getActive_tm() {
		return active_tm;
	}
	public void setActive_tm(Date active_tm) {
		this.active_tm = active_tm;
	}
	public Date getLast_login_tm() {
		return last_login_tm;
	}
	public void setLast_login_tm(Date last_login_tm) {
		this.last_login_tm = last_login_tm;
	}
	public Date getExpire_tm() {
		return expire_tm;
	}
	public void setExpire_tm(Date expire_tm) {
		this.expire_tm = expire_tm;
	}
	public String getSaas_id() {
		return saas_id;
	}
	public void setSaas_id(String saas_id) {
		this.saas_id = saas_id;
	}
	public String getC_batch_id() {
		return c_batch_id;
	}
	public void setC_batch_id(String c_batch_id) {
		this.c_batch_id = c_batch_id;
	}
	public String getC_auto_ind() {
		return c_auto_ind;
	}
	public void setC_auto_ind(String c_auto_ind) {
		this.c_auto_ind = c_auto_ind;
	}
	
}
