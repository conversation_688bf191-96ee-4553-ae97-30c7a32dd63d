package com.career.utils.liuxue;

import java.util.Arrays;

public class Major {

	private String ad_count;
	private String cn_name;
	private String count;
	private String description;
	private String full_name;
	private String gpa;
	private String gre;
	private String id;
	private String lastupdate;
	private String major_id;
	private String seo_url;
	private String short_name;
	private String toefl;
	
	public String getAd_count() {
		return ad_count;
	}
	public void setAd_count(String ad_count) {
		this.ad_count = ad_count;
	}
	public String getCn_name() {
		return cn_name;
	}
	public void setCn_name(String cn_name) {
		this.cn_name = cn_name;
	}
	public String getCount() {
		return count;
	}
	public void setCount(String count) {
		this.count = count;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public String getFull_name() {
		return full_name;
	}
	public void setFull_name(String full_name) {
		this.full_name = full_name;
	}
	public String getGpa() {
		return gpa;
	}
	public void setGpa(String gpa) {
		this.gpa = gpa;
	}
	public String getGre() {
		return gre;
	}
	public void setGre(String gre) {
		this.gre = gre;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getLastupdate() {
		return lastupdate;
	}
	public void setLastupdate(String lastupdate) {
		this.lastupdate = lastupdate;
	}
	public String getMajor_id() {
		return major_id;
	}
	public void setMajor_id(String major_id) {
		this.major_id = major_id;
	}
	public String getSeo_url() {
		return seo_url;
	}
	public void setSeo_url(String seo_url) {
		this.seo_url = seo_url;
	}
	public String getShort_name() {
		return short_name;
	}
	public void setShort_name(String short_name) {
		this.short_name = short_name;
	}
	public String getToefl() {
		return toefl;
	}
	public void setToefl(String toefl) {
		this.toefl = toefl;
	}
	
	public String generateSQL() {
		String SQL = "insert into career_lx_major(ad_count, cn_name, count, description, full_name, gpa, gre, id, major_id, seo_url, short_name, toefl) values('" + ad_count + "', '" + cn_name
				+ "', '" + count + "','" + description + "', '" + full_name + "', '" + gpa + "', '" + gre + "', "
				+ id + ", " + major_id +", '"+seo_url+"','"+short_name+"','"+toefl+"');";
		return SQL;
	}

}
