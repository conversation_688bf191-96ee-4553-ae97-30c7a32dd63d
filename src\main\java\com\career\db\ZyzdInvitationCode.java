package com.career.db;

import java.util.Date;

public class ZyzdInvitationCode {
	
	private String ivt_id;
	private String agent_c_id;
	private String agent_child_c_id;
	private int ivt_type;
	private Date create_tm;
	private String user_c_id;
	private Date active_tm;
	private String user_phone;
	
	private String ext_sys_ind;
	private String ext_xk;
	private int ext_score_org;
	private String ext_prov;
	private Date ext_last_login;
	
	
	
	public String getExt_prov() {
		return ext_prov;
	}
	public void setExt_prov(String ext_prov) {
		this.ext_prov = ext_prov;
	}
	public Date getExt_last_login() {
		return ext_last_login;
	}
	public void setExt_last_login(Date ext_last_login) {
		this.ext_last_login = ext_last_login;
	}
	public String getExt_sys_ind() {
		return ext_sys_ind;
	}
	public void setExt_sys_ind(String ext_sys_ind) {
		this.ext_sys_ind = ext_sys_ind;
	}
	public String getExt_xk() {
		return ext_xk;
	}
	public void setExt_xk(String ext_xk) {
		this.ext_xk = ext_xk;
	}
	public int getExt_score_org() {
		return ext_score_org;
	}
	public void setExt_score_org(int ext_score_org) {
		this.ext_score_org = ext_score_org;
	}
	public String getAgent_child_c_id() {
		return agent_child_c_id;
	}
	public void setAgent_child_c_id(String agent_child_c_id) {
		this.agent_child_c_id = agent_child_c_id;
	}
	public String getUser_phone() {
		return user_phone;
	}
	public void setUser_phone(String user_phone) {
		this.user_phone = user_phone;
	}
	public String getIvt_id() {
		return ivt_id;
	}
	public void setIvt_id(String ivt_id) {
		this.ivt_id = ivt_id;
	}
	public String getAgent_c_id() {
		return agent_c_id;
	}
	public void setAgent_c_id(String agent_c_id) {
		this.agent_c_id = agent_c_id;
	}
	public int getIvt_type() {
		return ivt_type;
	}
	public void setIvt_type(int ivt_type) {
		this.ivt_type = ivt_type;
	}
	public Date getCreate_tm() {
		return create_tm;
	}
	public void setCreate_tm(Date create_tm) {
		this.create_tm = create_tm;
	}
	public String getUser_c_id() {
		return user_c_id;
	}
	public void setUser_c_id(String user_c_id) {
		this.user_c_id = user_c_id;
	}
	public Date getActive_tm() {
		return active_tm;
	}
	public void setActive_tm(Date active_tm) {
		this.active_tm = active_tm;
	}
	

}
