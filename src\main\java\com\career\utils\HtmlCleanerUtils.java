package com.career.utils;

import java.util.HashMap;
import java.util.Map;

public class HtmlCleanerUtils {
    // 危险标签定义
    private static final String[] DANGEROUS_TAGS = {"script", "style", "iframe", "frame", "object", "embed", "form"};
    // 事件属性定义
    private static final String[] EVENT_ATTRIBUTES = {"onclick", "onload", "onerror", "onmouseover", "onmouseout"};
    
    // Bootstrap样式映射
    private static final Map<String, String> TAG_STYLES = new HashMap<String, String>();
    
    // 特殊内容样式映射
    private static final Map<String, String[]> SPECIAL_CONTENT_STYLES = new HashMap<String, String[]>();
    
    static {
    	TAG_STYLES.put("h1", "display-4 mb-4 fw-bold text-primary text-center");
    	TAG_STYLES.put("h2", "h2 mb-4 fw-bold text-dark");
    	TAG_STYLES.put("h3", "h3 mb-3 fw-bold text-dark");
        TAG_STYLES.put("h4", "h4 mb-3 text-dark");
        TAG_STYLES.put("p", "mb-3 text-break lh-lg text-secondary");
        TAG_STYLES.put("ul", "list-group list-group-flush mb-4");
        TAG_STYLES.put("ol", "list-group list-group-numbered mb-4");
        TAG_STYLES.put("li", "list-group-item d-flex align-items-center py-2");
        TAG_STYLES.put("table", "table table-hover align-middle mb-4");
        TAG_STYLES.put("thead", "table-light");
        TAG_STYLES.put("blockquote", "blockquote ps-3 border-start border-primary border-3");
        
        
        SPECIAL_CONTENT_STYLES.put("招生咨询", new String[]{"alert alert-info mb-3", "bi-info-circle"});
        SPECIAL_CONTENT_STYLES.put("电子邮箱", new String[]{"alert alert-info mb-3", "bi-envelope"});
        SPECIAL_CONTENT_STYLES.put("通讯地址", new String[]{"alert alert-light mb-3", "bi-geo-alt"});
    }

    /**
     * 清理HTML内容
     */
    public static String cleanHtmlContent(String content) {
        if(content == null || content.trim().isEmpty()) {
            return "";
        }
        
        try {
            StringBuilder result = new StringBuilder(content);
            
            // 1. 安全清理
            removeDangerousContent(result);
            
            content = result.toString();
            
            // 2. 基础清理
            content = cleanBasicContent(content);
            
            // 3. 应用样式
            content = applyStyles(content);
            
            // 4. 特殊内容处理
            content = handleSpecialContent(content);
            
            // 5. 最终清理
            content = content.replaceAll("\\s+", " ").trim();
            
            return content;
            
        } catch (Exception e) {
            return "<div class=\"alert alert-danger\">内容加载失败</div>";
        }
    }
    
    /**
     * 移除危险内容
     */
    private static void removeDangerousContent(StringBuilder content) {
        // 移除危险标签
        for (String tag : DANGEROUS_TAGS) {
            removeTag(content, tag);
        }
        
        // 移除事件属性
        for (String attr : EVENT_ATTRIBUTES) {
            removeAttribute(content, attr);
        }
    }
    
    /**
     * 基础内容清理
     */
    private static String cleanBasicContent(String content) {
        return content.replaceAll(" style=\"[^\"]*?\"", "")
                     .replaceAll(" class=\"[^\"]*?\"", "")
                     .replaceAll("<p>\\s*&nbsp;\\s*</p>", "")
                     .replaceAll("\\s+", " ")
                     .replaceAll("&nbsp;", " ")
                     .replaceAll("<a\\s+href=\"[^\"]*\"[^>]*>(.*?)</a>", "$1");
    }
    
    /**
     * 应用样式
     */
    private static String applyStyles(String content) {
        // 应用基础标签样式
        for (Map.Entry<String, String> entry : TAG_STYLES.entrySet()) {
            content = content.replaceAll(
                "<" + entry.getKey() + "[^>]*>",
                "<" + entry.getKey() + " class=\"" + entry.getValue() + "\">"
            );
        }
        
        // 处理章节标题
        content = content.replaceAll(
            "第([一二三四五六七八九十]*)章\\s*([^<]*)",
            "<h2 class=\"chapter-title py-3 bg-light rounded-3 ps-4 position-relative\">" +
            "<div class=\"position-absolute top-0 start-0 h-100 bg-primary\" style=\"width:5px;\"></div>" +
            "第$1章 $2</h2>"
        );
        
        // 处理条目标题
        content = content.replaceAll(
            "第([\\d]*)条\\s*([^<]*)",
            "<h3 class=\"article-title mb-3 d-flex align-items-center\">" +
            "<span class=\"badge bg-secondary me-2\">第$1条</span>$2</h3>"
        );
        
        // 处理列表项
        content = content.replaceAll(
            "<li[^>]*>",
            "<li class=\"list-group-item d-flex align-items-center py-2\">" +
            "<i class=\"bi bi-dot me-2 text-primary\"></i>"
        );
        
        // 处理图片
        content = content.replaceAll(
            "<img([^>]*)>",
            "<img$1 class=\"img-fluid rounded\" loading=\"lazy\">"
        );
        
        return content;
    }
    
    /**
     * 处理特殊内容
     */
    private static String handleSpecialContent(String content) {
        for (Map.Entry<String, String[]> entry : SPECIAL_CONTENT_STYLES.entrySet()) {
            content = content.replaceAll(
                "(" + entry.getKey() + "[^：]*：[^<]*)",
                "<div class=\"" + entry.getValue()[0] + "\">" +
                "<i class=\"bi " + entry.getValue()[1] + " me-2\"></i>$1</div>"
            );
        }
        return content;
    }
    
    /**
     * 移除HTML标签
     */
    private static void removeTag(StringBuilder content, String tag) {
        String startTag = "<" + tag;
        String endTag = "</" + tag + ">";
        int startIndex, endIndex;
        
        while ((startIndex = content.indexOf(startTag)) != -1) {
            endIndex = content.indexOf(endTag, startIndex);
            if (endIndex != -1) {
                content.delete(startIndex, endIndex + endTag.length());
            }
        }
    }
    
    /**
     * 移除HTML属性
     */
    private static void removeAttribute(StringBuilder content, String attr) {
        int startIndex;
        String attrPrefix = " " + attr + "=\"";
        
        while ((startIndex = content.indexOf(attrPrefix)) != -1) {
            int endIndex = content.indexOf("\"", startIndex + attrPrefix.length());
            if (endIndex != -1) {
                content.delete(startIndex, endIndex + 1);
            }
        }
    }
}