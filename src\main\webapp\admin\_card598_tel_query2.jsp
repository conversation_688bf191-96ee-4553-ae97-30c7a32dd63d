<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page import="com.zsdwf.utils.*,com.zsdwf.db.*,java.util.*" %>

<%@include file="/WEB-INF/include/_session_admin_ajax.jsp" %>
<%
YGCardBean card = (YGCardBean)session.getAttribute("SES_ADMIN");
String val = Tools.trim(request.getParameter("val"));
String f = Tools.trim(request.getParameter("f"));


if(Tools.isEmpty(val)){
	val = "%";
}

YGJDBC jdbc = new YGJDBC();
List<BaseCardBean> list = null;
if("A".equals(f)){
	list = jdbc.searchCardsByPhoneAndOrderId_A(val, card.getId()); 	
}else{
	list = jdbc.searchCardsByPhoneAndOrderId_B(val, card.getId()); 	
}


for(int i=0;i<list.size();i++){
	BaseCardBean bean = list.get(i);
%>
<tr><td><%=bean.getId() %></td><td><%=bean.getPasswd() %></td><td><%=bean.getPhone() %></td><td><%=bean.getExt() %></td><td><%=bean.getCtype() %></td><td><%=Tools.getDateTm(bean.getActive()) %></td><td><%=bean.getAgent() %></td></tr>         
<%
}

%>



		