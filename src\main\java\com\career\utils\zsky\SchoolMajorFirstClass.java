package com.career.utils.zsky;

import java.util.Arrays;

import com.alibaba.fastjson2.JSONObject;

public class SchoolMajorFirstClass {

	private int cnf_spe_id;
	private int root_id;
	private String name;
	private String code;
	private int professional;
	private int academic;
	private int year;
	private int recruit_type;

	public int getCnf_spe_id() {
		return cnf_spe_id;
	}
	public void setCnf_spe_id(int cnf_spe_id) {
		this.cnf_spe_id = cnf_spe_id;
	}
	public int getRoot_id() {
		return root_id;
	}
	public void setRoot_id(int root_id) {
		this.root_id = root_id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public int getProfessional() {
		return professional;
	}
	public void setProfessional(int professional) {
		this.professional = professional;
	}
	public int getAcademic() {
		return academic;
	}
	public void setAcademic(int academic) {
		this.academic = academic;
	}
	public int getYear() {
		return year;
	}
	public void setYear(int year) {
		this.year = year;
	}
	public int getRecruit_type() {
		return recruit_type;
	}
	public void setRecruit_type(int recruit_type) {
		this.recruit_type = recruit_type;
	}
	
	public String generateSQL(int school_id, String[] syl) {
		String SQL = "insert into career_university_major(school_id, cnf_spe_id, root_id, major_name, major_code, professional, academic, major_year, recruit_type, syl) values('" + school_id + "', " + cnf_spe_id
				+ ", " + root_id + ",'" + name + "', '" + code + "', " + professional + ", " + academic + ", "
				+ year + ", " + recruit_type +", '"+Arrays.toString(syl)+"');";
		return SQL;
	}
	
	
	
}
