package com.career.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class WeComCustomerFetcher {

    private static final String GET_TOKEN_URL = "https://qyapi.weixin.qq.com/cgi-bin/gettoken";
    private static final String GET_CUSTOMER_LIST_URL = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/list";
    private static final String GET_CUSTOMER_DETAIL_URL = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get";

    /**
     * 获取企业微信所有客户信息
     * @param corpId 企业ID
     * @param corpSecret 应用的Secret
     * @param userId 企业成员UserID
     * @return 客户信息列表
     */
    public static List<Map<String, Object>> fetchAllCustomers(String corpId, String corpSecret, String userId) {
        List<Map<String, Object>> customers = new ArrayList<>();
        
        try {
            // 1. 获取access_token
            String accessToken = getAccessToken(corpId, corpSecret);
            if (accessToken == null) {
                throw new RuntimeException("获取access_token失败");
            }

            // 2. 获取客户列表
            List<String> externalUserIds = getCustomerList(accessToken, userId);

            // 3. 获取每个客户的详细信息
            for (String externalUserId : externalUserIds) {
                Map<String, Object> customerInfo = getCustomerDetail(accessToken, externalUserId);
                if (customerInfo != null) {
                    customers.add(customerInfo);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("获取企业微信客户信息失败: " + e.getMessage());
        }

        return customers;
    }

    /**
     * 获取access_token
     */
    public static String getAccessToken(String corpId, String corpSecret) throws IOException {
        String url = String.format("%s?corpid=%s&corpsecret=%s", GET_TOKEN_URL, corpId, corpSecret);
        String response = doGetRequest(url);
        JSONObject json = JSON.parseObject(response);
        return json.getInteger("errcode") == 0 ? json.getString("access_token") : null;
    }

    /**
     * 获取客户列表
     */
    private static List<String> getCustomerList(String accessToken, String userId) throws IOException {
        List<String> externalUserIds = new ArrayList<>();
        String url = String.format("%s?access_token=%s&userid=%s", GET_CUSTOMER_LIST_URL, accessToken, userId);
        String response = doGetRequest(url);
        JSONObject json = JSON.parseObject(response);
        
        if (json.getInteger("errcode") == 0) {
            JSONArray externalUserIdList = json.getJSONArray("external_userid");
            for (int i = 0; i < externalUserIdList.size(); i++) {
                externalUserIds.add(externalUserIdList.getString(i));
            }
        }
        return externalUserIds;
    }

    /**
     * 获取客户详细信息
     */
    private static Map<String, Object> getCustomerDetail(String accessToken, String externalUserId) throws IOException {
        String url = String.format("%s?access_token=%s&external_userid=%s", GET_CUSTOMER_DETAIL_URL, accessToken, externalUserId);
        String response = doGetRequest(url);
        JSONObject json = JSON.parseObject(response);
        
        if (json.getInteger("errcode") == 0) {
            Map<String, Object> customerInfo = new HashMap<>();
            customerInfo.put("external_userid", json.getString("external_userid"));
            customerInfo.put("name", json.getString("name"));
            customerInfo.put("position", json.getString("position"));
            customerInfo.put("avatar", json.getString("avatar"));
            customerInfo.put("corp_name", json.getString("corp_name"));
            customerInfo.put("corp_full_name", json.getString("corp_full_name"));
            
            // 处理跟进人信息
            JSONArray followers = json.getJSONArray("followers");
            List<Map<String, String>> followerList = new ArrayList<>();
            for (int i = 0; followers != null && i < followers.size(); i++) {
                JSONObject follower = followers.getJSONObject(i);
                Map<String, String> followerInfo = new HashMap<>();
                followerInfo.put("userid", follower.getString("userid"));
                followerInfo.put("remark", follower.getString("remark"));
                followerInfo.put("description", follower.getString("description"));
                followerInfo.put("createtime", follower.getString("createtime"));
                followerList.add(followerInfo);
            }
            customerInfo.put("followers", followerList);
            
            // 处理标签信息
            JSONArray tags = json.getJSONArray("tags");
            List<Map<String, String>> tagList = new ArrayList<>();
            for (int i = 0; tags != null && i < tags.size(); i++) {
                JSONObject tag = tags.getJSONObject(i);
                Map<String, String> tagInfo = new HashMap<>();
                tagInfo.put("group_name", tag.getString("group_name"));
                tagInfo.put("tag_name", tag.getString("tag_name"));
                tagInfo.put("tag_id", tag.getString("tag_id"));
                tagInfo.put("type", tag.getString("type"));
                tagList.add(tagInfo);
            }
            customerInfo.put("tags", tagList);
            
            return customerInfo;
        }
        return null;
    }

    /**
     * 执行GET请求
     */
    private static String doGetRequest(String url) throws IOException {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet httpGet = new HttpGet(url);
            HttpResponse response = httpClient.execute(httpGet);
            return EntityUtils.toString(response.getEntity());
        }
    }

    private static final String GET_USER_LIST_URL = "https://qyapi.weixin.qq.com/cgi-bin/user/list";
    public static void main(String[] args) throws IOException {
        // 使用示例
        String corpId = "ww2a382ee871b4bf4e";
        String corpSecret = "e96WdSTGPGX3rcPohw2pG3VePVf10TDw4ebGBQoDlrc";
        
        String accessToken = getAccessToken(corpId, corpSecret);
        String url = String.format("%s?access_token=%s&department_id=1", GET_USER_LIST_URL, accessToken);
        System.out.println("成员列表API: " + doGetRequest(url));
        
        // 1. 获取所有成员UserID
        List<String> allUserIds = WeComUserFetcher.getAllUserIds(corpId, corpSecret, 1L);
        for(String x : allUserIds) {
        	System.out.println("x - > "+x);
        }
        
        // 2. 获取客户列表
        
        List<Map<String, Object>> customers = new ArrayList<>();
        
        List<String> externalUserIds = getCustomerList(accessToken, "WangHua");

        
        // 3. 获取每个客户的详细信息
        for (String externalUserId : externalUserIds) {
        	System.out.println(externalUserIds.size() + "客户ID: " + externalUserId);
        	
        	
            Map<String, Object> customerInfo = getCustomerDetail(accessToken, externalUserId);
            if (customerInfo != null) {
                customers.add(customerInfo);
            }
            
        }
        
        System.out.println("获取到客户数量: " + customers.size());
        for (Map<String, Object> customer : customers) {
            System.out.println("客户ID: " + customer.get("external_userid"));
            System.out.println("客户名称: " + customer.get("name"));
            System.out.println("公司名称: " + customer.get("corp_name"));
            System.out.println("跟进人数量: " + ((List<?>)customer.get("followers")).size());
            System.out.println("标签数量: " + ((List<?>)customer.get("tags")).size());
            System.out.println("----------------------");
        }

        
    }
}

