package com.career.db;

import java.util.Date;

import com.career.utils.BaseBean;

public class AiTbFormMain extends BaseBean{
	
	public static void main(String args[]) {
		AiTbFormMain main = new AiTbFormMain();
		printBeanProperties(main);
	}
	
	private int id;
	private int jh_version;
	private String batch_id;
	private String batch_id_org;
	private String order_id;
	private String f_no;
	private int score_cj;
	private int score_wc;
	private String score_xk;
	private String checker_id;
	private String checker_name;
	private String checker_remark;
	private String selected_zymc;
	private Date create_tm;
	private Date last_update_tm;
	private int joined_checker_cnt;
	private int nf;
	private int status;
	private int f_type;
	private String pc;
	private String pc_code;
	private String form_name;
	
	private String ext_stu_name;
	private String ext_stu_dh;
	private String ext_c_id;
	private String ext_c_name;
	private String toCheckerRemark;
	
	public String getPc_code() {
		return pc_code;
	}
	public void setPc_code(String pc_code) {
		this.pc_code = pc_code;
	}
	public int getJh_version() {
		return jh_version;
	}
	public void setJh_version(int jh_version) {
		this.jh_version = jh_version;
	}
	public String getToCheckerRemark() {
		return toCheckerRemark;
	}
	public void setToCheckerRemark(String toCheckerRemark) {
		this.toCheckerRemark = toCheckerRemark;
	}
	public String getExt_c_name() {
		return ext_c_name;
	}
	public void setExt_c_name(String ext_c_name) {
		this.ext_c_name = ext_c_name;
	}
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getForm_name() {
		return form_name;
	}
	public void setForm_name(String form_name) {
		this.form_name = form_name;
	}
	public String getExt_c_id() {
		return ext_c_id;
	}
	public void setExt_c_id(String ext_c_id) {
		this.ext_c_id = ext_c_id;
	}
	public String getExt_stu_name() {
		return ext_stu_name;
	}
	public void setExt_stu_name(String ext_stu_name) {
		this.ext_stu_name = ext_stu_name;
	}
	public String getExt_stu_dh() {
		return ext_stu_dh;
	}
	public void setExt_stu_dh(String ext_stu_dh) {
		this.ext_stu_dh = ext_stu_dh;
	}
	public int getF_type() {
		return f_type;
	}
	public void setF_type(int f_type) {
		this.f_type = f_type;
	}
	public int getStatus() {
		return status;
	}
	public void setStatus(int status) {
		this.status = status;
	}
	public int getNf() {
		return nf;
	}
	public void setNf(int nf) {
		this.nf = nf;
	}
	public String getPc() {
		return pc;
	}
	public void setPc(String pc) {
		this.pc = pc;
	}
	public String getSelected_zymc() {
		return selected_zymc;
	}
	public void setSelected_zymc(String selected_zymc) {
		this.selected_zymc = selected_zymc;
	}
	public String getChecker_name() {
		return checker_name;
	}
	public void setChecker_name(String checker_name) {
		this.checker_name = checker_name;
	}
	public String getF_no() {
		return f_no;
	}
	public void setF_no(String f_no) {
		this.f_no = f_no;
	}
	public int getScore_cj() {
		return score_cj;
	}
	public void setScore_cj(int score_cj) {
		this.score_cj = score_cj;
	}
	public int getScore_wc() {
		return score_wc;
	}
	public void setScore_wc(int score_wc) {
		this.score_wc = score_wc;
	}
	public String getScore_xk() {
		return score_xk;
	}
	public void setScore_xk(String score_xk) {
		this.score_xk = score_xk;
	}
	public String getBatch_id() {
		return batch_id;
	}
	public void setBatch_id(String batch_id) {
		this.batch_id = batch_id;
	}
	public String getBatch_id_org() {
		return batch_id_org;
	}
	public void setBatch_id_org(String batch_id_org) {
		this.batch_id_org = batch_id_org;
	}

	public String getOrder_id() {
		return order_id;
	}
	public void setOrder_id(String order_id) {
		this.order_id = order_id;
	}
	public String getChecker_id() {
		return checker_id;
	}
	public void setChecker_id(String checker_id) {
		this.checker_id = checker_id;
	}
	public String getChecker_remark() {
		return checker_remark;
	}
	public void setChecker_remark(String checker_remark) {
		this.checker_remark = checker_remark;
	}
	public Date getCreate_tm() {
		return create_tm;
	}
	public void setCreate_tm(Date create_tm) {
		this.create_tm = create_tm;
	}
	public Date getLast_update_tm() {
		return last_update_tm;
	}
	public void setLast_update_tm(Date last_update_tm) {
		this.last_update_tm = last_update_tm;
	}
	public int getJoined_checker_cnt() {
		return joined_checker_cnt;
	}
	public void setJoined_checker_cnt(int joined_checker_cnt) {
		this.joined_checker_cnt = joined_checker_cnt;
	}

	
}
