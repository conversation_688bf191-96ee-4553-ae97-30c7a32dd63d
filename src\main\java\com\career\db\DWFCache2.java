package com.career.db;

import java.util.HashMap;
import java.util.Iterator;

public class DWFCache2 {

	public static final java.util.HashMap<String, String> MAPYY = new HashMap<>();
	
	public static void main(String args[]) {
		
		Iterator<String> it = MAPYY.keySet().iterator();
		for(int i=368;i<=966;i++) {
			String xx = MAPYY.get("W" + i);
			System.out.println("MAPYY.put(\"W" + i + "\",\"" +xx +"\");");
		}
	}
	
	static {
		MAPYY.put("L315","150");
		MAPYY.put("L316","150");
		MAPYY.put("L317","151");
		MAPYY.put("L318","152");
		MAPYY.put("L319","153");
		MAPYY.put("L320","153");
		MAPYY.put("L321","154");
		MAPYY.put("L322","155");
		MAPYY.put("L323","156");
		MAPYY.put("L324","156");
		MAPYY.put("L325","157");
		MAPYY.put("L326","158");
		MAPYY.put("L327","159");
		MAPYY.put("L328","159");
		MAPYY.put("L329","160");
		MAPYY.put("L330","161");
		MAPYY.put("L331","162");
		MAPYY.put("L332","163");
		MAPYY.put("L333","163");
		MAPYY.put("L334","164");
		MAPYY.put("L335","165");
		MAPYY.put("L336","166");
		MAPYY.put("L337","167");
		MAPYY.put("L338","168");
		MAPYY.put("L339","169");
		MAPYY.put("L340","170");
		MAPYY.put("L341","171");
		MAPYY.put("L342","171");
		MAPYY.put("L343","172");
		MAPYY.put("L344","172");
		MAPYY.put("L345","173");
		MAPYY.put("L346","174");
		MAPYY.put("L347","175");
		MAPYY.put("L348","175");
		MAPYY.put("L349","176");
		MAPYY.put("L350","177");
		MAPYY.put("L351","178");
		MAPYY.put("L352","179");
		MAPYY.put("L353","179");
		MAPYY.put("L354","180");
		MAPYY.put("L355","181");
		MAPYY.put("L356","182");
		MAPYY.put("L357","183");
		MAPYY.put("L358","184");
		MAPYY.put("L359","185");
		MAPYY.put("L360","186");
		MAPYY.put("L361","186");
		MAPYY.put("L362","187");
		MAPYY.put("L363","188");
		MAPYY.put("L364","189");
		MAPYY.put("L365","190");
		MAPYY.put("L366","190");
		MAPYY.put("L367","191");
		MAPYY.put("L368","192");
		MAPYY.put("L369","193");
		MAPYY.put("L370","194");
		MAPYY.put("L371","195");
		MAPYY.put("L372","196");
		MAPYY.put("L373","196");
		MAPYY.put("L374","197");
		MAPYY.put("L375","198");
		MAPYY.put("L376","199");
		MAPYY.put("L377","200");
		MAPYY.put("L378","201");
		MAPYY.put("L379","202");
		MAPYY.put("L380","203");
		MAPYY.put("L381","204");
		MAPYY.put("L382","205");
		MAPYY.put("L383","206");
		MAPYY.put("L384","207");
		MAPYY.put("L385","208");
		MAPYY.put("L386","209");
		MAPYY.put("L387","210");
		MAPYY.put("L388","212");
		MAPYY.put("L389","213");
		MAPYY.put("L390","214");
		MAPYY.put("L391","215");
		MAPYY.put("L392","216");
		MAPYY.put("L393","217");
		MAPYY.put("L394","218");
		MAPYY.put("L395","219");
		MAPYY.put("L396","220");
		MAPYY.put("L397","221");
		MAPYY.put("L398","222");
		MAPYY.put("L399","223");
		MAPYY.put("L400","224");
		MAPYY.put("L401","225");
		MAPYY.put("L402","227");
		MAPYY.put("L403","228");
		MAPYY.put("L404","229");
		MAPYY.put("L405","230");
		MAPYY.put("L406","231");
		MAPYY.put("L407","232");
		MAPYY.put("L408","234");
		MAPYY.put("L409","235");
		MAPYY.put("L410","236");
		MAPYY.put("L411","237");
		MAPYY.put("L412","238");
		MAPYY.put("L413","239");
		MAPYY.put("L414","241");
		MAPYY.put("L415","242");
		MAPYY.put("L416","243");
		MAPYY.put("L417","244");
		MAPYY.put("L418","246");
		MAPYY.put("L419","247");
		MAPYY.put("L420","248");
		MAPYY.put("L421","249");
		MAPYY.put("L422","251");
		MAPYY.put("L423","252");
		MAPYY.put("L424","253");
		MAPYY.put("L425","255");
		MAPYY.put("L426","256");
		MAPYY.put("L427","257");
		MAPYY.put("L428","259");
		MAPYY.put("L429","260");
		MAPYY.put("L430","261");
		MAPYY.put("L431","263");
		MAPYY.put("L432","264");
		MAPYY.put("L433","265");
		MAPYY.put("L434","267");
		MAPYY.put("L435","268");
		MAPYY.put("L436","269");
		MAPYY.put("L437","271");
		MAPYY.put("L438","272");
		MAPYY.put("L439","273");
		MAPYY.put("L440","275");
		MAPYY.put("L441","276");
		MAPYY.put("L442","278");
		MAPYY.put("L443","279");
		MAPYY.put("L444","281");
		MAPYY.put("L445","282");
		MAPYY.put("L446","283");
		MAPYY.put("L447","285");
		MAPYY.put("L448","286");
		MAPYY.put("L449","288");
		MAPYY.put("L450","289");
		MAPYY.put("L451","290");
		MAPYY.put("L452","292");
		MAPYY.put("L453","293");
		MAPYY.put("L454","295");
		MAPYY.put("L455","296");
		MAPYY.put("L456","298");
		MAPYY.put("L457","299");
		MAPYY.put("L458","300");
		MAPYY.put("L459","302");
		MAPYY.put("L460","303");
		MAPYY.put("L461","305");
		MAPYY.put("L462","306");
		MAPYY.put("L463","307");
		MAPYY.put("L464","309");
		MAPYY.put("L465","310");
		MAPYY.put("L466","312");
		MAPYY.put("L467","313");
		MAPYY.put("L468","314");
		MAPYY.put("L469","316");
		MAPYY.put("L470","317");
		MAPYY.put("L471","319");
		MAPYY.put("L472","320");
		MAPYY.put("L473","322");
		MAPYY.put("L474","323");
		MAPYY.put("L475","324");
		MAPYY.put("L476","326");
		MAPYY.put("L477","327");
		MAPYY.put("L478","329");
		MAPYY.put("L479","330");
		MAPYY.put("L480","331");
		MAPYY.put("L481","333");
		MAPYY.put("L482","334");
		MAPYY.put("L483","336");
		MAPYY.put("L484","337");
		MAPYY.put("L485","339");
		MAPYY.put("L486","340");
		MAPYY.put("L487","341");
		MAPYY.put("L488","343");
		MAPYY.put("L489","344");
		MAPYY.put("L490","345");
		MAPYY.put("L491","347");
		MAPYY.put("L492","348");
		MAPYY.put("L493","349");
		MAPYY.put("L494","351");
		MAPYY.put("L495","352");
		MAPYY.put("L496","353");
		MAPYY.put("L497","355");
		MAPYY.put("L498","356");
		MAPYY.put("L499","357");
		MAPYY.put("L500","359");
		MAPYY.put("L501","360");
		MAPYY.put("L502","361");
		MAPYY.put("L503","362");
		MAPYY.put("L504","364");
		MAPYY.put("L505","365");
		MAPYY.put("L506","366");
		MAPYY.put("L507","368");
		MAPYY.put("L508","369");
		MAPYY.put("L509","370");
		MAPYY.put("L510","371");
		MAPYY.put("L511","373");
		MAPYY.put("L512","374");
		MAPYY.put("L513","375");
		MAPYY.put("L514","376");
		MAPYY.put("L515","377");
		MAPYY.put("L516","378");
		MAPYY.put("L517","380");
		MAPYY.put("L518","381");
		MAPYY.put("L519","382");
		MAPYY.put("L520","383");
		MAPYY.put("L521","384");
		MAPYY.put("L522","385");
		MAPYY.put("L523","387");
		MAPYY.put("L524","388");
		MAPYY.put("L525","389");
		MAPYY.put("L526","390");
		MAPYY.put("L527","391");
		MAPYY.put("L528","392");
		MAPYY.put("L529","393");
		MAPYY.put("L530","394");
		MAPYY.put("L531","395");
		MAPYY.put("L532","397");
		MAPYY.put("L533","398");
		MAPYY.put("L534","399");
		MAPYY.put("L535","400");
		MAPYY.put("L536","401");
		MAPYY.put("L537","402");
		MAPYY.put("L538","403");
		MAPYY.put("L539","404");
		MAPYY.put("L540","405");
		MAPYY.put("L541","406");
		MAPYY.put("L542","407");
		MAPYY.put("L543","408");
		MAPYY.put("L544","409");
		MAPYY.put("L545","410");
		MAPYY.put("L546","410");
		MAPYY.put("L547","412");
		MAPYY.put("L548","414");
		MAPYY.put("L549","415");
		MAPYY.put("L550","415");
		MAPYY.put("L551","416");
		MAPYY.put("L552","417");
		MAPYY.put("L553","418");
		MAPYY.put("L554","419");
		MAPYY.put("L555","420");
		MAPYY.put("L556","421");
		MAPYY.put("L557","422");
		MAPYY.put("L558","423");
		MAPYY.put("L559","424");
		MAPYY.put("L560","425");
		MAPYY.put("L561","426");
		MAPYY.put("L562","427");
		MAPYY.put("L563","428");
		MAPYY.put("L564","429");
		MAPYY.put("L565","430");
		MAPYY.put("L566","431");
		MAPYY.put("L567","432");
		MAPYY.put("L568","433");
		MAPYY.put("L569","434");
		MAPYY.put("L570","435");
		MAPYY.put("L571","436");
		MAPYY.put("L572","437");
		MAPYY.put("L573","438");
		MAPYY.put("L574","439");
		MAPYY.put("L575","440");
		MAPYY.put("L576","441");
		MAPYY.put("L577","442");
		MAPYY.put("L578","443");
		MAPYY.put("L579","444");
		MAPYY.put("L580","445");
		MAPYY.put("L581","446");
		MAPYY.put("L582","446");
		MAPYY.put("L583","447");
		MAPYY.put("L584","448");
		MAPYY.put("L585","449");
		MAPYY.put("L586","450");
		MAPYY.put("L587","451");
		MAPYY.put("L588","452");
		MAPYY.put("L589","453");
		MAPYY.put("L590","454");
		MAPYY.put("L591","455");
		MAPYY.put("L592","456");
		MAPYY.put("L593","457");
		MAPYY.put("L594","458");
		MAPYY.put("L595","459");
		MAPYY.put("L596","460");
		MAPYY.put("L597","460");
		MAPYY.put("L598","461");
		MAPYY.put("L599","462");
		MAPYY.put("L600","463");
		MAPYY.put("L601","464");
		MAPYY.put("L602","465");
		MAPYY.put("L603","466");
		MAPYY.put("L604","467");
		MAPYY.put("L605","468");
		MAPYY.put("L606","469");
		MAPYY.put("L607","470");
		MAPYY.put("L608","471");
		MAPYY.put("L609","472");
		MAPYY.put("L610","409");
		MAPYY.put("L611","473");
		MAPYY.put("L612","474");
		MAPYY.put("L613","475");
		MAPYY.put("L614","476");
		MAPYY.put("L615","477");
		MAPYY.put("L616","478");
		MAPYY.put("L617","479");
		MAPYY.put("L618","480");
		MAPYY.put("L619","481");
		MAPYY.put("L620","482");
		MAPYY.put("L621","483");
		MAPYY.put("L622","484");
		MAPYY.put("L623","485");
		MAPYY.put("L624","486");
		MAPYY.put("L625","487");
		MAPYY.put("L626","429");
		MAPYY.put("L627","488");
		MAPYY.put("L628","489");
		MAPYY.put("L629","490");
		MAPYY.put("L630","491");
		MAPYY.put("L631","492");
		MAPYY.put("L632","493");
		MAPYY.put("L633","494");
		MAPYY.put("L634","495");
		MAPYY.put("L635","496");
		MAPYY.put("L636","497");
		MAPYY.put("L637","498");
		MAPYY.put("L638","499");
		MAPYY.put("L639","500");
		MAPYY.put("L640","501");
		MAPYY.put("L641","502");
		MAPYY.put("L642","503");
		MAPYY.put("L643","504");
		MAPYY.put("L644","505");
		MAPYY.put("L645","506");
		MAPYY.put("L646","506");
		MAPYY.put("L647","507");
		MAPYY.put("L648","508");
		MAPYY.put("L649","509");
		MAPYY.put("L650","510");
		MAPYY.put("L651","511");
		MAPYY.put("L652","512");
		MAPYY.put("L653","513");
		MAPYY.put("L654","514");
		MAPYY.put("L655","515");
		MAPYY.put("L656","516");
		MAPYY.put("L657","517");
		MAPYY.put("L658","518");
		MAPYY.put("L659","519");
		MAPYY.put("L660","520");
		MAPYY.put("L661","521");
		MAPYY.put("L662","522");
		MAPYY.put("L663","523");
		MAPYY.put("L664","524");
		MAPYY.put("L665","525");
		MAPYY.put("L666","526");
		MAPYY.put("L667","527");
		MAPYY.put("L668","528");
		MAPYY.put("L669","528");
		MAPYY.put("L670","529");
		MAPYY.put("L671","530");
		MAPYY.put("L672","531");
		MAPYY.put("L673","532");
		MAPYY.put("L674","533");
		MAPYY.put("L675","534");
		MAPYY.put("L676","535");
		MAPYY.put("L677","536");
		MAPYY.put("L678","537");
		MAPYY.put("L679","538");
		MAPYY.put("L680","539");
		MAPYY.put("L681","540");
		MAPYY.put("L682","541");
		MAPYY.put("L683","542");
		MAPYY.put("L684","543");
		MAPYY.put("L685","544");
		MAPYY.put("L686","545");
		MAPYY.put("L687","546");
		MAPYY.put("L688","546");
		MAPYY.put("L689","547");
		MAPYY.put("L690","548");
		MAPYY.put("L691","549");
		MAPYY.put("L692","550");
		MAPYY.put("L693","551");
		MAPYY.put("L694","552");
		MAPYY.put("L695","553");
		MAPYY.put("L696","554");
		MAPYY.put("L697","555");
		MAPYY.put("L698","556");
		MAPYY.put("L699","557");
		MAPYY.put("L700","557");
		MAPYY.put("L701","558");
		MAPYY.put("L702","559");
		MAPYY.put("L703","560");
		MAPYY.put("L704","561");
		MAPYY.put("L705","562");
		MAPYY.put("L706","563");
		MAPYY.put("L707","564");
		MAPYY.put("L708","565");
		MAPYY.put("L709","565");
		MAPYY.put("L710","566");
		MAPYY.put("L711","567");
		MAPYY.put("L712","568");
		MAPYY.put("L713","569");
		MAPYY.put("L714","570");
		MAPYY.put("L715","571");
		MAPYY.put("L716","571");
		MAPYY.put("L717","572");
		MAPYY.put("L718","573");
		MAPYY.put("L719","574");
		MAPYY.put("L720","575");
		MAPYY.put("L721","576");
		MAPYY.put("L722","576");
		MAPYY.put("L723","577");
		MAPYY.put("L724","578");
		MAPYY.put("L725","579");
		MAPYY.put("L726","580");
		MAPYY.put("L727","581");
		MAPYY.put("L728","581");
		MAPYY.put("L729","582");
		MAPYY.put("L730","583");
		MAPYY.put("L731","584");
		MAPYY.put("L732","585");
		MAPYY.put("L733","586");
		MAPYY.put("L734","586");
		MAPYY.put("L735","587");
		MAPYY.put("L736","588");
		MAPYY.put("L737","589");
		MAPYY.put("L738","589");
		MAPYY.put("L739","590");
		MAPYY.put("L740","591");
		MAPYY.put("L741","592");
		MAPYY.put("L742","593");
		MAPYY.put("L743","593");
		MAPYY.put("L744","594");
		MAPYY.put("L745","595");
		MAPYY.put("L746","596");
		MAPYY.put("L747","536");
		MAPYY.put("L748","597");
		MAPYY.put("L749","598");
		MAPYY.put("L750","598");
		MAPYY.put("L751","599");
		MAPYY.put("L752","600");
		MAPYY.put("L753","601");
		MAPYY.put("L754","601");
		MAPYY.put("L755","602");
		MAPYY.put("L756","603");
		MAPYY.put("L757","604");
		MAPYY.put("L758","605");
		MAPYY.put("L759","605");
		MAPYY.put("L760","606");
		MAPYY.put("L761","607");
		MAPYY.put("L762","607");
		MAPYY.put("L763","608");
		MAPYY.put("L764","609");
		MAPYY.put("L765","609");
		MAPYY.put("L766","610");
		MAPYY.put("L767","611");
		MAPYY.put("L768","611");
		MAPYY.put("L769","612");
		MAPYY.put("L770","613");
		MAPYY.put("L771","614");
		MAPYY.put("L772","614");
		MAPYY.put("L773","615");
		MAPYY.put("L774","616");
		MAPYY.put("L775","616");
		MAPYY.put("L776","617");
		MAPYY.put("L777","618");
		MAPYY.put("L778","618");
		MAPYY.put("L779","619");
		MAPYY.put("L780","619");
		MAPYY.put("L781","620");
		MAPYY.put("L782","621");
		MAPYY.put("L783","621");
		MAPYY.put("L784","622");
		MAPYY.put("L785","623");
		MAPYY.put("L786","623");
		MAPYY.put("L787","624");
		MAPYY.put("L788","625");
		MAPYY.put("L789","625");
		MAPYY.put("L790","626");
		MAPYY.put("L791","626");
		MAPYY.put("L792","627");
		MAPYY.put("L793","628");
		MAPYY.put("L794","628");
		MAPYY.put("L795","629");
		MAPYY.put("L796","629");
		MAPYY.put("L797","630");
		MAPYY.put("L798","631");
		MAPYY.put("L799","631");
		MAPYY.put("L800","632");
		MAPYY.put("L801","633");
		MAPYY.put("L802","633");
		MAPYY.put("L803","634");
		MAPYY.put("L804","634");
		MAPYY.put("L805","635");
		MAPYY.put("L806","636");
		MAPYY.put("L807","636");
		MAPYY.put("L808","637");
		MAPYY.put("L809","637");
		MAPYY.put("L810","638");
		MAPYY.put("L811","639");
		MAPYY.put("L812","639");
		MAPYY.put("L813","640");
		MAPYY.put("L814","640");
		MAPYY.put("L815","641");
		MAPYY.put("L816","641");
		MAPYY.put("L817","642");
		MAPYY.put("L818","643");
		MAPYY.put("L819","643");
		MAPYY.put("L820","644");
		MAPYY.put("L821","644");
		MAPYY.put("L822","645");
		MAPYY.put("L823","645");
		MAPYY.put("L824","646");
		MAPYY.put("L825","646");
		MAPYY.put("L826","647");
		MAPYY.put("L827","648");
		MAPYY.put("L828","648");
		MAPYY.put("L829","649");
		MAPYY.put("L830","649");
		MAPYY.put("L831","650");
		MAPYY.put("L832","650");
		MAPYY.put("L833","651");
		MAPYY.put("L834","651");
		MAPYY.put("L835","652");
		MAPYY.put("L836","652");
		MAPYY.put("L837","653");
		MAPYY.put("L838","653");
		MAPYY.put("L839","654");
		MAPYY.put("L840","655");
		MAPYY.put("L841","655");
		MAPYY.put("L842","655");
		MAPYY.put("L843","656");
		MAPYY.put("L844","657");
		MAPYY.put("L845","657");
		MAPYY.put("L846","658");
		MAPYY.put("L847","658");
		MAPYY.put("L848","659");
		MAPYY.put("L849","660");
		MAPYY.put("L850","660");
		MAPYY.put("L851","661");
		MAPYY.put("L852","661");
		MAPYY.put("L853","661");
		MAPYY.put("L854","662");
		MAPYY.put("L855","662");
		MAPYY.put("L856","663");
		MAPYY.put("L857","663");
		MAPYY.put("L858","664");
		MAPYY.put("L859","665");
		MAPYY.put("L860","666");
		MAPYY.put("L861","666");
		MAPYY.put("L862","667");
		MAPYY.put("L863","667");
		MAPYY.put("L864","668");
		MAPYY.put("L865","668");
		MAPYY.put("L866","669");
		MAPYY.put("L867","669");
		MAPYY.put("L868","669");
		MAPYY.put("L869","669");
		MAPYY.put("L870","669");
		MAPYY.put("L871","669");
		MAPYY.put("L872","669");
		MAPYY.put("L873","669");
		MAPYY.put("L874","670");
		MAPYY.put("L875","670");
		MAPYY.put("L876","670");
		MAPYY.put("L877","671");
		MAPYY.put("L878","671");
		MAPYY.put("L879","671");
		MAPYY.put("L880","671");
		MAPYY.put("L881","672");
		MAPYY.put("L882","672");
		MAPYY.put("L883","673");
		MAPYY.put("L884","673");
		MAPYY.put("L885","673");
		MAPYY.put("L886","674");
		MAPYY.put("L887","674");
		MAPYY.put("L888","674");
		MAPYY.put("L889","675");
		MAPYY.put("L890","675");
		MAPYY.put("L891","675");
		MAPYY.put("L892","676");
		MAPYY.put("L893","676");
		MAPYY.put("L894","676");
		MAPYY.put("L895","677");
		MAPYY.put("L896","677");
		MAPYY.put("L897","678");
		MAPYY.put("L898","678");
		MAPYY.put("L899","678");
		MAPYY.put("L900","679");
		MAPYY.put("L901","679");
		MAPYY.put("L902","679");
		MAPYY.put("L903","680");
		MAPYY.put("L904","680");
		MAPYY.put("L905","680");
		MAPYY.put("L906","681");
		MAPYY.put("L907","681");
		MAPYY.put("L908","681");
		MAPYY.put("L909","682");
		MAPYY.put("L910","628");
		MAPYY.put("L911","682");
		MAPYY.put("L912","683");
		MAPYY.put("L913","683");
		MAPYY.put("L914","683");
		MAPYY.put("L915","684");
		MAPYY.put("L916","684");
		MAPYY.put("L917","684");
		MAPYY.put("L918","685");
		MAPYY.put("L919","685");
		MAPYY.put("L920","685");
		MAPYY.put("L921","685");
		MAPYY.put("L922","685");
		MAPYY.put("L923","686");
		MAPYY.put("L924","686");
		MAPYY.put("L925","686");
		MAPYY.put("L926","687");
		MAPYY.put("L927","687");
		MAPYY.put("L928","687");
		MAPYY.put("L929","687");
		MAPYY.put("L930","688");
		MAPYY.put("L931","688");
		MAPYY.put("L932","688");
		MAPYY.put("L933","689");
		MAPYY.put("L934","689");
		MAPYY.put("L935","689");
		MAPYY.put("L936","690");
		MAPYY.put("L937","690");
		MAPYY.put("L938","690");
		MAPYY.put("L939","690");
		MAPYY.put("L940","691");
		MAPYY.put("L941","691");
		MAPYY.put("L942","692");
		MAPYY.put("L943","692");
		MAPYY.put("L944","692");
		MAPYY.put("L945","692");
		MAPYY.put("L946","693");
		MAPYY.put("L947","693");
		MAPYY.put("L948","693");
		MAPYY.put("L949","693");
		MAPYY.put("L950","694");
		MAPYY.put("L951","694");
		MAPYY.put("L952","694");
		MAPYY.put("L953","694");
		MAPYY.put("L954","695");
		MAPYY.put("L955","695");
		MAPYY.put("L956","695");
		MAPYY.put("L957","695");
		MAPYY.put("L958","695");
		MAPYY.put("L959","696");
		MAPYY.put("L960","697");
		MAPYY.put("L961","699");
		MAPYY.put("L962","700");
		MAPYY.put("L963","701");
		MAPYY.put("L964","702");

		MAPYY.put("W368","150");
		MAPYY.put("W369","151");
		MAPYY.put("W370","152");
		MAPYY.put("W371","152");
		MAPYY.put("W372","154");
		MAPYY.put("W373","155");
		MAPYY.put("W374","156");
		MAPYY.put("W375","157");
		MAPYY.put("W376","158");
		MAPYY.put("W377","159");
		MAPYY.put("W378","160");
		MAPYY.put("W379","161");
		MAPYY.put("W380","162");
		MAPYY.put("W381","163");
		MAPYY.put("W382","164");
		MAPYY.put("W383","165");
		MAPYY.put("W384","166");
		MAPYY.put("W385","167");
		MAPYY.put("W386","168");
		MAPYY.put("W387","169");
		MAPYY.put("W388","169");
		MAPYY.put("W389","170");
		MAPYY.put("W390","171");
		MAPYY.put("W391","172");
		MAPYY.put("W392","173");
		MAPYY.put("W393","174");
		MAPYY.put("W394","175");
		MAPYY.put("W395","176");
		MAPYY.put("W396","177");
		MAPYY.put("W397","178");
		MAPYY.put("W398","179");
		MAPYY.put("W399","180");
		MAPYY.put("W400","180");
		MAPYY.put("W401","181");
		MAPYY.put("W402","182");
		MAPYY.put("W403","183");
		MAPYY.put("W404","184");
		MAPYY.put("W405","185");
		MAPYY.put("W406","186");
		MAPYY.put("W407","187");
		MAPYY.put("W408","188");
		MAPYY.put("W409","189");
		MAPYY.put("W410","190");
		MAPYY.put("W411","191");
		MAPYY.put("W412","192");
		MAPYY.put("W413","192");
		MAPYY.put("W414","193");
		MAPYY.put("W415","193");
		MAPYY.put("W416","195");
		MAPYY.put("W417","197");
		MAPYY.put("W418","198");
		MAPYY.put("W419","199");
		MAPYY.put("W420","200");
		MAPYY.put("W421","201");
		MAPYY.put("W422","202");
		MAPYY.put("W423","203");
		MAPYY.put("W424","204");
		MAPYY.put("W425","205");
		MAPYY.put("W426","206");
		MAPYY.put("W427","207");
		MAPYY.put("W428","208");
		MAPYY.put("W429","209");
		MAPYY.put("W430","210");
		MAPYY.put("W431","210");
		MAPYY.put("W432","211");
		MAPYY.put("W433","212");
		MAPYY.put("W434","213");
		MAPYY.put("W435","214");
		MAPYY.put("W436","216");
		MAPYY.put("W437","217");
		MAPYY.put("W438","217");
		MAPYY.put("W439","218");
		MAPYY.put("W440","220");
		MAPYY.put("W441","221");
		MAPYY.put("W442","222");
		MAPYY.put("W443","223");
		MAPYY.put("W444","224");
		MAPYY.put("W445","225");
		MAPYY.put("W446","226");
		MAPYY.put("W447","227");
		MAPYY.put("W448","228");
		MAPYY.put("W449","229");
		MAPYY.put("W450","230");
		MAPYY.put("W451","231");
		MAPYY.put("W452","232");
		MAPYY.put("W453","233");
		MAPYY.put("W454","234");
		MAPYY.put("W455","235");
		MAPYY.put("W456","236");
		MAPYY.put("W457","237");
		MAPYY.put("W458","238");
		MAPYY.put("W459","239");
		MAPYY.put("W460","240");
		MAPYY.put("W461","241");
		MAPYY.put("W462","242");
		MAPYY.put("W463","243");
		MAPYY.put("W464","244");
		MAPYY.put("W465","246");
		MAPYY.put("W466","247");
		MAPYY.put("W467","248");
		MAPYY.put("W468","249");
		MAPYY.put("W469","250");
		MAPYY.put("W470","251");
		MAPYY.put("W471","252");
		MAPYY.put("W472","253");
		MAPYY.put("W473","254");
		MAPYY.put("W474","255");
		MAPYY.put("W475","257");
		MAPYY.put("W476","258");
		MAPYY.put("W477","259");
		MAPYY.put("W478","260");
		MAPYY.put("W479","261");
		MAPYY.put("W480","262");
		MAPYY.put("W481","263");
		MAPYY.put("W482","264");
		MAPYY.put("W483","265");
		MAPYY.put("W484","267");
		MAPYY.put("W485","268");
		MAPYY.put("W486","269");
		MAPYY.put("W487","270");
		MAPYY.put("W488","271");
		MAPYY.put("W489","272");
		MAPYY.put("W490","273");
		MAPYY.put("W491","274");
		MAPYY.put("W492","275");
		MAPYY.put("W493","276");
		MAPYY.put("W494","277");
		MAPYY.put("W495","279");
		MAPYY.put("W496","280");
		MAPYY.put("W497","281");
		MAPYY.put("W498","282");
		MAPYY.put("W499","283");
		MAPYY.put("W500","284");
		MAPYY.put("W501","285");
		MAPYY.put("W502","286");
		MAPYY.put("W503","287");
		MAPYY.put("W504","288");
		MAPYY.put("W505","290");
		MAPYY.put("W506","291");
		MAPYY.put("W507","292");
		MAPYY.put("W508","293");
		MAPYY.put("W509","294");
		MAPYY.put("W510","295");
		MAPYY.put("W511","296");
		MAPYY.put("W512","297");
		MAPYY.put("W513","299");
		MAPYY.put("W514","300");
		MAPYY.put("W515","301");
		MAPYY.put("W516","302");
		MAPYY.put("W517","303");
		MAPYY.put("W518","304");
		MAPYY.put("W519","305");
		MAPYY.put("W520","306");
		MAPYY.put("W521","308");
		MAPYY.put("W522","309");
		MAPYY.put("W523","310");
		MAPYY.put("W524","311");
		MAPYY.put("W525","312");
		MAPYY.put("W526","313");
		MAPYY.put("W527","314");
		MAPYY.put("W528","315");
		MAPYY.put("W529","316");
		MAPYY.put("W530","318");
		MAPYY.put("W531","319");
		MAPYY.put("W532","320");
		MAPYY.put("W533","321");
		MAPYY.put("W534","322");
		MAPYY.put("W535","323");
		MAPYY.put("W536","324");
		MAPYY.put("W537","325");
		MAPYY.put("W538","326");
		MAPYY.put("W539","327");
		MAPYY.put("W540","328");
		MAPYY.put("W541","330");
		MAPYY.put("W542","331");
		MAPYY.put("W543","332");
		MAPYY.put("W544","333");
		MAPYY.put("W545","334");
		MAPYY.put("W546","335");
		MAPYY.put("W547","336");
		MAPYY.put("W548","337");
		MAPYY.put("W549","338");
		MAPYY.put("W550","339");
		MAPYY.put("W551","340");
		MAPYY.put("W552","342");
		MAPYY.put("W553","343");
		MAPYY.put("W554","344");
		MAPYY.put("W555","345");
		MAPYY.put("W556","346");
		MAPYY.put("W557","347");
		MAPYY.put("W558","348");
		MAPYY.put("W559","349");
		MAPYY.put("W560","350");
		MAPYY.put("W561","351");
		MAPYY.put("W562","353");
		MAPYY.put("W563","354");
		MAPYY.put("W564","355");
		MAPYY.put("W565","356");
		MAPYY.put("W566","357");
		MAPYY.put("W567","358");
		MAPYY.put("W568","359");
		MAPYY.put("W569","360");
		MAPYY.put("W570","361");
		MAPYY.put("W571","362");
		MAPYY.put("W572","364");
		MAPYY.put("W573","365");
		MAPYY.put("W574","366");
		MAPYY.put("W575","367");
		MAPYY.put("W576","368");
		MAPYY.put("W577","369");
		MAPYY.put("W578","371");
		MAPYY.put("W579","372");
		MAPYY.put("W580","373");
		MAPYY.put("W581","374");
		MAPYY.put("W582","375");
		MAPYY.put("W583","377");
		MAPYY.put("W584","378");
		MAPYY.put("W585","379");
		MAPYY.put("W586","380");
		MAPYY.put("W587","381");
		MAPYY.put("W588","383");
		MAPYY.put("W589","384");
		MAPYY.put("W590","385");
		MAPYY.put("W591","386");
		MAPYY.put("W592","387");
		MAPYY.put("W593","389");
		MAPYY.put("W594","390");
		MAPYY.put("W595","391");
		MAPYY.put("W596","392");
		MAPYY.put("W597","394");
		MAPYY.put("W598","395");
		MAPYY.put("W599","396");
		MAPYY.put("W600","397");
		MAPYY.put("W601","399");
		MAPYY.put("W602","400");
		MAPYY.put("W603","401");
		MAPYY.put("W604","403");
		MAPYY.put("W605","404");
		MAPYY.put("W606","405");
		MAPYY.put("W607","406");
		MAPYY.put("W608","408");
		MAPYY.put("W609","409");
		MAPYY.put("W610","410");
		MAPYY.put("W611","411");
		MAPYY.put("W612","413");
		MAPYY.put("W613","414");
		MAPYY.put("W614","415");
		MAPYY.put("W615","417");
		MAPYY.put("W616","418");
		MAPYY.put("W617","419");
		MAPYY.put("W618","420");
		MAPYY.put("W619","422");
		MAPYY.put("W620","423");
		MAPYY.put("W621","424");
		MAPYY.put("W622","425");
		MAPYY.put("W623","427");
		MAPYY.put("W624","428");
		MAPYY.put("W625","429");
		MAPYY.put("W626","430");
		MAPYY.put("W627","432");
		MAPYY.put("W628","433");
		MAPYY.put("W629","434");
		MAPYY.put("W630","435");
		MAPYY.put("W631","437");
		MAPYY.put("W632","438");
		MAPYY.put("W633","439");
		MAPYY.put("W634","440");
		MAPYY.put("W635","441");
		MAPYY.put("W636","443");
		MAPYY.put("W637","444");
		MAPYY.put("W638","445");
		MAPYY.put("W639","446");
		MAPYY.put("W640","447");
		MAPYY.put("W641","448");
		MAPYY.put("W642","449");
		MAPYY.put("W643","450");
		MAPYY.put("W644","451");
		MAPYY.put("W645","453");
		MAPYY.put("W646","454");
		MAPYY.put("W647","455");
		MAPYY.put("W648","456");
		MAPYY.put("W649","457");
		MAPYY.put("W650","458");
		MAPYY.put("W651","459");
		MAPYY.put("W652","460");
		MAPYY.put("W653","461");
		MAPYY.put("W654","462");
		MAPYY.put("W655","463");
		MAPYY.put("W656","464");
		MAPYY.put("W657","465");
		MAPYY.put("W658","466");
		MAPYY.put("W659","467");
		MAPYY.put("W660","468");
		MAPYY.put("W661","469");
		MAPYY.put("W662","470");
		MAPYY.put("W663","471");
		MAPYY.put("W664","472");
		MAPYY.put("W665","473");
		MAPYY.put("W666","474");
		MAPYY.put("W667","475");
		MAPYY.put("W668","476");
		MAPYY.put("W669","476");
		MAPYY.put("W670","477");
		MAPYY.put("W671","478");
		MAPYY.put("W672","479");
		MAPYY.put("W673","480");
		MAPYY.put("W674","481");
		MAPYY.put("W675","482");
		MAPYY.put("W676","483");
		MAPYY.put("W677","484");
		MAPYY.put("W678","484");
		MAPYY.put("W679","485");
		MAPYY.put("W680","486");
		MAPYY.put("W681","487");
		MAPYY.put("W682","488");
		MAPYY.put("W683","489");
		MAPYY.put("W684","489");
		MAPYY.put("W685","490");
		MAPYY.put("W686","491");
		MAPYY.put("W687","492");
		MAPYY.put("W688","493");
		MAPYY.put("W689","493");
		MAPYY.put("W690","494");
		MAPYY.put("W691","495");
		MAPYY.put("W692","496");
		MAPYY.put("W693","497");
		MAPYY.put("W694","498");
		MAPYY.put("W695","498");
		MAPYY.put("W696","499");
		MAPYY.put("W697","500");
		MAPYY.put("W698","501");
		MAPYY.put("W699","502");
		MAPYY.put("W700","502");
		MAPYY.put("W701","503");
		MAPYY.put("W702","504");
		MAPYY.put("W703","505");
		MAPYY.put("W704","505");
		MAPYY.put("W705","506");
		MAPYY.put("W706","507");
		MAPYY.put("W707","508");
		MAPYY.put("W708","508");
		MAPYY.put("W709","509");
		MAPYY.put("W710","510");
		MAPYY.put("W711","511");
		MAPYY.put("W712","511");
		MAPYY.put("W713","512");
		MAPYY.put("W714","513");
		MAPYY.put("W715","514");
		MAPYY.put("W716","514");
		MAPYY.put("W717","515");
		MAPYY.put("W718","516");
		MAPYY.put("W719","517");
		MAPYY.put("W720","517");
		MAPYY.put("W721","518");
		MAPYY.put("W722","519");
		MAPYY.put("W723","520");
		MAPYY.put("W724","520");
		MAPYY.put("W725","521");
		MAPYY.put("W726","522");
		MAPYY.put("W727","522");
		MAPYY.put("W728","523");
		MAPYY.put("W729","524");
		MAPYY.put("W730","525");
		MAPYY.put("W731","525");
		MAPYY.put("W732","526");
		MAPYY.put("W733","527");
		MAPYY.put("W734","527");
		MAPYY.put("W735","528");
		MAPYY.put("W736","529");
		MAPYY.put("W737","530");
		MAPYY.put("W738","530");
		MAPYY.put("W739","531");
		MAPYY.put("W740","532");
		MAPYY.put("W741","532");
		MAPYY.put("W742","533");
		MAPYY.put("W743","534");
		MAPYY.put("W744","535");
		MAPYY.put("W745","535");
		MAPYY.put("W746","536");
		MAPYY.put("W747","537");
		MAPYY.put("W748","537");
		MAPYY.put("W749","538");
		MAPYY.put("W750","539");
		MAPYY.put("W751","539");
		MAPYY.put("W752","540");
		MAPYY.put("W753","541");
		MAPYY.put("W754","542");
		MAPYY.put("W755","542");
		MAPYY.put("W756","543");
		MAPYY.put("W757","544");
		MAPYY.put("W758","544");
		MAPYY.put("W759","545");
		MAPYY.put("W760","546");
		MAPYY.put("W761","546");
		MAPYY.put("W762","547");
		MAPYY.put("W763","548");
		MAPYY.put("W764","548");
		MAPYY.put("W765","549");
		MAPYY.put("W766","550");
		MAPYY.put("W767","550");
		MAPYY.put("W768","551");
		MAPYY.put("W769","552");
		MAPYY.put("W770","552");
		MAPYY.put("W771","553");
		MAPYY.put("W772","554");
		MAPYY.put("W773","554");
		MAPYY.put("W774","555");
		MAPYY.put("W775","556");
		MAPYY.put("W776","556");
		MAPYY.put("W777","557");
		MAPYY.put("W778","558");
		MAPYY.put("W779","558");
		MAPYY.put("W780","559");
		MAPYY.put("W781","560");
		MAPYY.put("W782","560");
		MAPYY.put("W783","561");
		MAPYY.put("W784","562");
		MAPYY.put("W785","562");
		MAPYY.put("W786","563");
		MAPYY.put("W787","564");
		MAPYY.put("W788","564");
		MAPYY.put("W789","565");
		MAPYY.put("W790","565");
		MAPYY.put("W791","566");
		MAPYY.put("W792","567");
		MAPYY.put("W793","567");
		MAPYY.put("W794","568");
		MAPYY.put("W795","569");
		MAPYY.put("W796","569");
		MAPYY.put("W797","570");
		MAPYY.put("W798","571");
		MAPYY.put("W799","571");
		MAPYY.put("W800","572");
		MAPYY.put("W801","572");
		MAPYY.put("W802","573");
		MAPYY.put("W803","574");
		MAPYY.put("W804","574");
		MAPYY.put("W805","575");
		MAPYY.put("W806","576");
		MAPYY.put("W807","576");
		MAPYY.put("W808","577");
		MAPYY.put("W809","578");
		MAPYY.put("W810","578");
		MAPYY.put("W811","579");
		MAPYY.put("W812","580");
		MAPYY.put("W813","580");
		MAPYY.put("W814","581");
		MAPYY.put("W815","581");
		MAPYY.put("W816","582");
		MAPYY.put("W817","583");
		MAPYY.put("W818","583");
		MAPYY.put("W819","584");
		MAPYY.put("W820","584");
		MAPYY.put("W821","585");
		MAPYY.put("W822","585");
		MAPYY.put("W823","586");
		MAPYY.put("W824","586");
		MAPYY.put("W825","587");
		MAPYY.put("W826","588");
		MAPYY.put("W827","588");
		MAPYY.put("W828","589");
		MAPYY.put("W829","589");
		MAPYY.put("W830","590");
		MAPYY.put("W831","590");
		MAPYY.put("W832","591");
		MAPYY.put("W833","591");
		MAPYY.put("W834","592");
		MAPYY.put("W835","593");
		MAPYY.put("W836","593");
		MAPYY.put("W837","594");
		MAPYY.put("W838","594");
		MAPYY.put("W839","595");
		MAPYY.put("W840","596");
		MAPYY.put("W841","596");
		MAPYY.put("W842","597");
		MAPYY.put("W843","597");
		MAPYY.put("W844","598");
		MAPYY.put("W845","598");
		MAPYY.put("W846","599");
		MAPYY.put("W847","599");
		MAPYY.put("W848","600");
		MAPYY.put("W849","600");
		MAPYY.put("W850","601");
		MAPYY.put("W851","601");
		MAPYY.put("W852","602");
		MAPYY.put("W853","602");
		MAPYY.put("W854","603");
		MAPYY.put("W855","603");
		MAPYY.put("W856","604");
		MAPYY.put("W857","604");
		MAPYY.put("W858","605");
		MAPYY.put("W859","605");
		MAPYY.put("W860","606");
		MAPYY.put("W861","606");
		MAPYY.put("W862","607");
		MAPYY.put("W863","607");
		MAPYY.put("W864","608");
		MAPYY.put("W865","609");
		MAPYY.put("W866","609");
		MAPYY.put("W867","609");
		MAPYY.put("W868","610");
		MAPYY.put("W869","611");
		MAPYY.put("W870","611");
		MAPYY.put("W871","611");
		MAPYY.put("W872","612");
		MAPYY.put("W873","612");
		MAPYY.put("W874","613");
		MAPYY.put("W875","613");
		MAPYY.put("W876","614");
		MAPYY.put("W877","614");
		MAPYY.put("W878","615");
		MAPYY.put("W879","615");
		MAPYY.put("W880","616");
		MAPYY.put("W881","616");
		MAPYY.put("W882","616");
		MAPYY.put("W883","617");
		MAPYY.put("W884","617");
		MAPYY.put("W885","618");
		MAPYY.put("W886","618");
		MAPYY.put("W887","618");
		MAPYY.put("W888","619");
		MAPYY.put("W889","619");
		MAPYY.put("W890","620");
		MAPYY.put("W891","620");
		MAPYY.put("W892","621");
		MAPYY.put("W893","621");
		MAPYY.put("W894","621");
		MAPYY.put("W895","622");
		MAPYY.put("W896","623");
		MAPYY.put("W897","623");
		MAPYY.put("W898","623");
		MAPYY.put("W899","624");
		MAPYY.put("W900","624");
		MAPYY.put("W901","624");
		MAPYY.put("W902","625");
		MAPYY.put("W903","626");
		MAPYY.put("W904","626");
		MAPYY.put("W905","627");
		MAPYY.put("W906","627");
		MAPYY.put("W907","627");
		MAPYY.put("W908","628");
		MAPYY.put("W909","628");
		MAPYY.put("W910","629");
		MAPYY.put("W911","629");
		MAPYY.put("W912","630");
		MAPYY.put("W913","630");
		MAPYY.put("W914","630");
		MAPYY.put("W915","631");
		MAPYY.put("W916","631");
		MAPYY.put("W917","631");
		MAPYY.put("W918","632");
		MAPYY.put("W919","632");
		MAPYY.put("W920","633");
		MAPYY.put("W921","633");
		MAPYY.put("W922","633");
		MAPYY.put("W923","633");
		MAPYY.put("W924","634");
		MAPYY.put("W925","635");
		MAPYY.put("W926","635");
		MAPYY.put("W927","636");
		MAPYY.put("W928","636");
		MAPYY.put("W929","637");
		MAPYY.put("W930","637");
		MAPYY.put("W931","637");
		MAPYY.put("W932","637");
		MAPYY.put("W933","638");
		MAPYY.put("W934","638");
		MAPYY.put("W935","639");
		MAPYY.put("W936","639");
		MAPYY.put("W937","639");
		MAPYY.put("W938","639");
		MAPYY.put("W939","640");
		MAPYY.put("W940","640");
		MAPYY.put("W941","640");
		MAPYY.put("W942","640");
		MAPYY.put("W943","641");
		MAPYY.put("W944","641");
		MAPYY.put("W945","642");
		MAPYY.put("W946","642");
		MAPYY.put("W947","643");
		MAPYY.put("W948","643");
		MAPYY.put("W949","643");
		MAPYY.put("W950","644");
		MAPYY.put("W951","644");
		MAPYY.put("W952","644");
		MAPYY.put("W953","645");
		MAPYY.put("W954","645");
		MAPYY.put("W955","645");
		MAPYY.put("W956","646");
		MAPYY.put("W957","646");
		MAPYY.put("W958","646");
		MAPYY.put("W959","646");
		MAPYY.put("W960","647");
		MAPYY.put("W961","647");
		MAPYY.put("W962","647");
		MAPYY.put("W963","647");
		MAPYY.put("W964","647");
		MAPYY.put("W965","648");
		MAPYY.put("W966","649");

	}
}
