package com.career.db;

import com.career.utils.SQLLogUtils;
import com.career.utils.Tools;
import com.career.utils.XKCombineUtils;
import com.career.utils.ZyzdCache;
import com.career.utils.ZyzdProvince;
import com.zsdwf.db.BaseCardBean;
import com.zsdwf.db.PredictBean;
import com.zsdwf.db.YGDataPatchDBTools;
import com.zsdwf.pdf.PDFGaoZhongStudentInfo;
import com.career.db.SchoolBean;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.UUID;

public class JDBC {
	static String URL = JDBCConstants.URL;
	static String USER = JDBCConstants.USER;
	static String PASSWD = JDBCConstants.PASSWD;

	public static HashMap<String, String> HM_PROVINCE = new HashMap<>();

	public static HashMap<String, String> HM_PROVINCE_CODE_NAME = new LinkedHashMap<>();
	
	public static List<String> LIST_CHINA_PROVINCE_NAME = new ArrayList<>();

	public static HashMap<String, String> HM_SC_CITY_CODE_NAME = new LinkedHashMap<>();

	public static HashMap<String, String> HM_CQ_CITY_CODE_NAME = new LinkedHashMap<>();
	public static HashMap<String, String> HM_GZ_CITY_CODE_NAME = new LinkedHashMap<>();

	public static HashMap<String, String> HM_PROVINCE_LATEST_YEAR = new HashMap<>();

	public static HashMap<String, String> HM_XK = new HashMap<>();

	public static HashMap<String, String> HM_PROVINCE_CODE = new HashMap<>();

	public static HashMap<String, String> HM_LHPY = new HashMap<>();

	public static HashMap<String, String> HM_LMGX = new HashMap<>();

	public static HashMap<String, String> HM_HYMX = new HashMap<>();

	public static HashMap<String, String> HM_YBWS = new HashMap<>();

	public static HashMap<String, String> HM_HYHP = new HashMap<>();

	public static HashSet<String> YX_LHPY = new HashSet<>();

	public static HashSet<String> YX_XZX = new HashSet<>();

	public static HashSet<String> YX_FX = new HashSet<>();

	public static HashSet<String> YX_GAJX = new HashSet<>();

	public static HashSet<String> YX_SFJX = new HashSet<>();
	public static HashMap<String, String> ONE_SENT = new HashMap<>();
	
	public static HashMap<String, SchoolBean> YX_SCHOOL = new HashMap<>();
	public static HashMap<String, List<CityBean>> HM_PROVINCE_CITY = new HashMap<>();
	public static HashMap<String, List<ZDPCBean>> HM_PROVINCE_CITY_ZDPC = new HashMap<>();

	static {
		try {
			Class.forName("com.mysql.cj.jdbc.Driver");
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		YX_SFJX.add("中央司法警官学院");
		YX_SFJX.add("江西司法警官职业学院");
		YX_SFJX.add("黑龙江司法警官职业学院");
		YX_SFJX.add("河北司法警官职业学院");
		YX_SFJX.add("吉林司法警官职业学院");
		YX_SFJX.add("云南司法警官职业学院");
		YX_SFJX.add("四川司法警官职业学院");
		YX_SFJX.add("山东司法警官职业学院");
		YX_SFJX.add("山西警官职业学院");
		YX_SFJX.add("河南司法警官职业学院");
		YX_SFJX.add("浙江警官职业学院");
		YX_SFJX.add("湖南司法警官职业学院");
		YX_SFJX.add("安徽警官职业学院");
		YX_SFJX.add("广东司法警官职业学院");
		YX_SFJX.add("武汉警官职业学院");

		YX_GAJX.add("中国人民公安大学");
		YX_GAJX.add("中国刑事警察学院");
		YX_GAJX.add("中国人民警察大学");
		YX_GAJX.add("南京森林警察学院");
		YX_GAJX.add("铁道警察学院");
		YX_GAJX.add("湖北警官学院");
		YX_GAJX.add("江苏警官学院");
		YX_GAJX.add("北京警察学院");
		YX_GAJX.add("广东警官学院");
		YX_GAJX.add("上海公安学院");
		YX_GAJX.add("湖南警察学院");
		YX_GAJX.add("浙江警察学院");
		YX_GAJX.add("云南警官学院");
		YX_GAJX.add("山东警察学院");
		YX_GAJX.add("山西警察学院");
		YX_GAJX.add("江西警察学院");
		YX_GAJX.add("河南警察学院");
		YX_GAJX.add("四川警察学院");
		YX_GAJX.add("重庆警察学院");
		YX_GAJX.add("广西警察学院");
		YX_GAJX.add("贵州警察学院");
		YX_GAJX.add("福建警察学院");
		YX_GAJX.add("吉林警察学院");
		YX_GAJX.add("辽宁警察学院");
		YX_GAJX.add("新疆警察学院");

		YX_XZX.add("苏州科技大学天平学院");
		YX_XZX.add("南华大学船山学院");
		YX_XZX.add("杭州师范大学钱江学院");
		YX_XZX.add("湖北工业大学工程技术学院");
		YX_XZX.add("厦门大学嘉庚学院");
		YX_XZX.add("湖南中医药大学湘杏学院");
		YX_XZX.add("新乡医学院三全学院");
		YX_XZX.add("温州医科大学仁济学院");
		YX_XZX.add("天津师范大学津沽学院");
		YX_XZX.add("阜阳师范大学信息工程学院");
		YX_XZX.add("内蒙古大学创业学院");
		YX_XZX.add("湖南科技大学潇湘学院");
		YX_XZX.add("三峡大学科技学院");
		YX_XZX.add("天津外国语大学滨海外事学院");
		YX_XZX.add("河北农业大学现代科技学院");
		YX_XZX.add("广西中医药大学赛恩斯新医药学院");
		YX_XZX.add("南通大学杏林学院");
		YX_XZX.add("湘潭大学兴湘学院");
		YX_XZX.add("贵州中医药大学时珍学院");
		YX_XZX.add("江苏大学京江学院");
		YX_XZX.add("浙江中医药大学滨江学院");
		YX_XZX.add("燕山大学里仁学院");
		YX_XZX.add("江苏科技大学苏州理工学院");
		YX_XZX.add("江苏师范大学科文学院");
		YX_XZX.add("华北理工大学冀唐学院");
		YX_XZX.add("河北医科大学临床学院");

		YX_FX.add("哈尔滨工业大学");
		YX_FX.add("北京师范大学");
		YX_FX.add("中国人民大学");
		YX_FX.add("大连理工大学");
		YX_FX.add("山东大学");
		YX_FX.add("电子科技大学");
		YX_FX.add("东北大学");
		YX_FX.add("合肥工业大学");
		YX_FX.add("华北电力大学");
		YX_FX.add("中国石油大学");
		YX_FX.add("中国地质大学");
		YX_FX.add("中国矿业大学");
		YX_FX.add("北京邮电大学");
		YX_FX.add("北京交通大学");
		YX_FX.add("成都理工大学");
		YX_FX.add("西南大学");
		YX_FX.add("遵义医科大学");
		YX_FX.add("西华大学");
		YX_FX.add("四川外国语大学成都学院");
		YX_FX.add("东北石油大学");
		YX_FX.add("哈尔滨医科大学");
		YX_FX.add("厦门大学");
		YX_FX.add("中国农业大学");
		YX_FX.add("中国传媒大学");
		YX_FX.add("浙江大学");
		YX_FX.add("香港中文大学");
		YX_FX.add("西南交通大学");
		YX_FX.add("哈尔滨理工大学");

		HM_PROVINCE.put("A1", "A1_AH");
		HM_PROVINCE.put("B1", "B1_BJ");
		HM_PROVINCE.put("C1", "C1_CQ");
		HM_PROVINCE.put("F1", "F1_FJ");
		HM_PROVINCE.put("G1", "G1_GS");
		HM_PROVINCE.put("G2", "G2_GD");
		HM_PROVINCE.put("G3", "G3_GX");
		HM_PROVINCE.put("H1", "H1_HN");
		HM_PROVINCE.put("H2", "H2_HB");
		HM_PROVINCE.put("H3", "H3_HN");
		HM_PROVINCE.put("H4", "H4_HL");
		HM_PROVINCE.put("H6", "H6_HN");
		HM_PROVINCE.put("J2", "J2_JS");
		HM_PROVINCE.put("J3", "J3_JX");
		HM_PROVINCE.put("L1", "L1_LN");
		HM_PROVINCE.put("N1", "N1_NM");
		HM_PROVINCE.put("N2", "N2_NX");
		HM_PROVINCE.put("Q1", "Q1_QH");
		HM_PROVINCE.put("S1", "S1_SD");
		HM_PROVINCE.put("S4", "S4_SH");
		HM_PROVINCE.put("S5", "S5_SC");
		HM_PROVINCE.put("Y1", "Y1_YN");
		HM_PROVINCE.put("T1", "T1_TJ");
		HM_PROVINCE.put("G4", "G4_GZ");
		HM_PROVINCE.put("H5", "H5_HB");
		HM_PROVINCE.put("J1", "J1_JL");
		HM_PROVINCE.put("S2", "S2_SX");
		HM_PROVINCE.put("S3", "S3_SX");
		HM_PROVINCE.put("X1", "X1_XJ");
		HM_PROVINCE.put("Z1", "Z1_ZJ");

		

		HM_PROVINCE_LATEST_YEAR.put("S4", "_2024");
		HM_PROVINCE_LATEST_YEAR.put("Y1", "_2024");
		HM_PROVINCE_LATEST_YEAR.put("B1", "_2024");
		HM_PROVINCE_LATEST_YEAR.put("S5", "_2024");
		HM_PROVINCE_LATEST_YEAR.put("T1", "_2024");
		HM_PROVINCE_LATEST_YEAR.put("A1", "_2024");
		HM_PROVINCE_LATEST_YEAR.put("S1", "_2024");
		HM_PROVINCE_LATEST_YEAR.put("S2", "_2024");
		HM_PROVINCE_LATEST_YEAR.put("G2", "_2024");
		HM_PROVINCE_LATEST_YEAR.put("G3", "_2024");
		HM_PROVINCE_LATEST_YEAR.put("J2", "_2024");
		HM_PROVINCE_LATEST_YEAR.put("J3", "_2024");
		HM_PROVINCE_LATEST_YEAR.put("H2", "_2024");
		HM_PROVINCE_LATEST_YEAR.put("H3", "_2024");
		HM_PROVINCE_LATEST_YEAR.put("Z1", "_2024");
		HM_PROVINCE_LATEST_YEAR.put("H1", "_2024");
		HM_PROVINCE_LATEST_YEAR.put("H5", "_2024");
		HM_PROVINCE_LATEST_YEAR.put("H6", "_2024");
		HM_PROVINCE_LATEST_YEAR.put("F1", "_2024");
		HM_PROVINCE_LATEST_YEAR.put("G4", "_2024");
		HM_PROVINCE_LATEST_YEAR.put("L1", "_2024");
		HM_PROVINCE_LATEST_YEAR.put("C1", "_2024");
		HM_PROVINCE_LATEST_YEAR.put("S3", "_2024");
		HM_PROVINCE_LATEST_YEAR.put("H4", "_2024");
		

		HM_XK.put("lk", "理");
		HM_XK.put("wk", "文");
		HM_XK.put("wl", "物");
		HM_XK.put("ls", "史");
		HM_XK.put("hx", "化");
		HM_XK.put("dl", "地");
		HM_XK.put("zz", "政");
		HM_XK.put("sw", "生");
		HM_XK.put("xx", "信");

		ONE_SENT.put("哲学类",
				"哲学是人文科学领域内的基础学科，学起难且枯燥，培养周期长，对学生的综合能力，感悟能力要求极高。对口就业岗位极少，主要方向是考公、当老师。文史哲不分家，就业岗位十分类似。本科不建议直接学哲学，除非是非常热爱哲学、有一定的知识底蕴，或者家境殷实，愿意出国深造。本科最好有其他学科背景，毕业后有知识的广度，研究生学哲学能够扩展思维的深度，是比较合适的。");
		ONE_SENT.put("经济学类",
				"经济学，核心是研究稀缺资源的配置，毕业进入银行、保险、证券、信托、跨境电商这5个领域较多。经济学类专业就业的主要影响因素有三点，第一是院校层次，第二是家庭资源，第三是工作地域。经济学类专业不少对口单位的学历门槛就是硕士了，如果家里没有资源，院校层次也一般，本科就业大概率是金融产品销售。");
		ONE_SENT.put("财政学类",
				"财政学，学习国家宏观经济运行、财政收支、税收、会计、法律问题，毕业对口的财政、税务公务员，其次是从事会计工作。考公对口财政局和税务局，听上去很好，但事实是，要接收财政学类的单位，几乎都会接收会计学，但是接收会计学的单位，不一定接收财政学类。而且会计专业的同学非常多，所以该专业考公并没有那么容易。如果不考公务员，财政学的就业和经济学类是差不多的。主要影响因素有三点，第一是院校层次，第二是家庭资源，第三是工作地域。");
		ONE_SENT.put("金融学类",
				"金融学，研究经济活动中的投资、保险、融资，学习金融学类专业，你会成为一个“懂得用钱生钱”的人，但是首先你得能募集到资金。更适合家里有资源、高情商、性格外向的同学。如果没有足够的家庭资源或名校背景，那么在金融行业中大概率就是打工仔，向上晋升受到资源限制，同行竞争又非常赤裸，同时金融行业的KPI考核是比较严格的，岗位当然是压力比较大的。");
		ONE_SENT.put("经济与贸易类",
				"经济与贸易，在经济学的基础上，学习贸易、金融、管理学、法学等课程。学习经贸类专业，你会成为一个“懂得国际贸易规则的经济学人才”。经济与贸易类专业的典型对口行业有：外贸、券商、银行、保险、信托、跨境电商、互联网。该专业就业岗位少，最对口的外贸专员岗位，其实不需要很高的学历，工作比较枯燥，但是压力没那么大。大多数学生转行去到金融行业，那么无论是券商、银行、保险、信托，还是做财务工作，那就压力比较大了。");
		ONE_SENT.put("法学类",
				"法学专业是最古老的三大专业之一，只要人类社会还存在，这个专业就会一直存在。但是因为只要有老师就能开设法学专业，造成这个专业的毕业生严重的供大于求。法学类专业的专业壁垒属于中等偏高的，因为法律要记忆大量的条文，还要通过法考这道大关。一般外行想要进入法学是比较困难的。法学类专业的典型对口行业有：体制内：公检法、税务局、工商局\r\n"
						+ "    体制外：律师事务所、企业法务部\r\n"
						+ "    律师是非常注重就业地域的，一般都会在念书的当地工作。因为校友圈子是律师很重要的资源，特别是五院四系这样的顶级法律学府，一般都不会脱离学校区域就业。\r\n"
						+ "    学法律其实挣钱的是非诉讼业务，比如跨国公司法务部、金融机构、高新技术、环境保护、知识产权、反倾销、反补贴、反垄断等领域的律师。\r\n"
						+ "    国内对于复合型律师是比较稀缺的，比如对某个专业非常了解，英语也很好，可以直接和外商交流谈判的。\r\n"
						+ "    律师还有一个特点，就是子承父业的现象比较多，因为律师的资源是可以转移给自己子女的。\r\n"
						+ "    如果不是名校毕业，家里也没有资源，那么当律师会有漫长的成长期，在熬出来之前，小律师的收入是比较低的，工作也比较苦。");
		ONE_SENT.put("政治学类",
				"政治学类专业，是从西方传到中国来的一门学问，培养具有一定政治学、行政学方面的基本知识，能在相关行业工作的政治学和行政学高级专门人才。理论性较强的专业，就业范围比较窄，对口行业中的最好的就业方向是考公务员或者国家及事业单位。因为专业课程的特殊性，不太适合偏科的考生报考。这个专业适合考研或者出国深造，适合家境殷实的考生。");
		ONE_SENT.put("社会学类",
				"社会学，是学习社会怎么改变人，人怎么研究社会的一门学科。社会学主要偏向于社会和人群的相互作用本身的，比如社会保障、社会福利。社会学类专业的专业壁垒属于很低的，想要学习或者从事社会学类专业都比较容易。社会学类专业的典型对口行业有：体制内：民政局、社区、街道等\r\n"
						+ "    体制外：企业行政管理，社会生活类媒体。");
		ONE_SENT.put("民族学类",
				"民族学，以民族为研究对象的学科。它民族学专业是研究民族的发生、发展和变化的专业。民族学本身就是注重交流的学科，对语言能力的要求很高，但是整体就业岗位少，薪水平均水平低，不建议普通家庭孩子报考，主要就业方向是高校和公务员。1. 教育科研类：这类岗位主要是在高校、科研院所、社会科学普及机构等单位从事民族学或相关学科的教学、科研、咨询、培训等工作。\r\n"
						+ "    2. 政府机关类：这类岗位主要是在国家民委、地方民族事务委员会、统战部、外交部等单位。");
		ONE_SENT.put("马克思主义理论类",
				"马克思主义理论，这个专业类主要就一个专业“思想政治教育”，就业方向成为一名政治老师或者党建人员。马克思主义理论类专业的专业壁垒是很低的，在校期间建议加入学生会，多参加和组织同学活动，锻炼发现和解决问题的能力，多写写材料也是很有必要的。对口岗位：思想教育老师（高校、中学、小学），党建人员。");
		ONE_SENT.put("公安学类",
				"公安学类属于一级学科，下面共包含15个专业，那么这些专业在我们生活中乃至国家安保管理中是主要人才培养的摇篮。公安学类专业的毕业生几乎都有很好的去处，用我们老话儿说就是铁饭碗，一般在检察院、国家安全、军队保卫以及公安、工商、税务、审判、海关、纪检、监察、移民局等部门从事侦查、稽查、刑事、执法工作，预防和控制犯罪以及侦查学教学、科研，管理等方面的工作。.值得注意的是，如果是选择的是警校提前批的公安学类专业，是可以参加公安联考，未来更容易进入公安系统；如果我们选择的是一般院校（非部署或者地方警校），那么未来进入公检法系统，需要参加公务员考试。");

		ONE_SENT.put("教育学类",
				"教育学专业，是研究教育本身的，不是教你当老师的。主要学习内容是教育学、心理学等等，在大学的课程设置中可能没有教学实践环节。如果目标是当老师，需要选择学科类专业（比如数学与应用数学、汉语言文学、英语、物理等等）。因为出生人口的减少，已经城市化进入收尾阶段，目前小学老师已经接近饱和，整个教师行业的从业学历也在不断提高，对于院校层次以及学历要求越来越高。教育学类专业的典型对口职业有：\r\n"
						+ "    教育学：辅导机构、编辑、教育局公务员\r\n" + "    学前教育：幼儿园老师\r\n" + "    小学教育：小学老师");

		ONE_SENT.put("体育学类",
				"体育学类，学习体育教育，毕业后当体育老师、教练，或者进入体育产业。学习这个专业类，你会懂得怎么去开展体育训练、如何运作一场赛事、如何做运动康复等等。体育学类专业的典型对口职业有：\r\n"
						+ "    体育老师、体育教练、体育课程顾问、体育赛事营销运作\r\n" + "    体育学类专业的典型对口行业有：\r\n" + "    学校、培训机构、体育公司、体育媒体");

		ONE_SENT.put("植物生产类",
				"植物生产类专业，也就是农作物的栽培与育种。主要分为四大方向：一是遗传育种理论方法；二是新品种；三是高效栽培技术；四是耕作制度。就作物而言，主要包括粮食作物、纤维作物、油料作物、糖料作物、香料作物、药用作物、能源作物、植物胶作物。整体上，该专业本科毕业的就业情况一般，大多数对口的毕业生去了种子、化肥、农药等相关的公司做销售，跑业务一类的岗位。也有的去了种子、化肥生产企业做管理、行政工作，就业面很窄，不建议报考。");

		ONE_SENT.put("自然保护与环境生态类",
				"自然保护与生态环境类，拥有3个基本专业，农业资源与环境研究土壤，野生动物与自然保护区管理，水土保持与荒漠化防治研究的是自然资源与环境。这个专业类的特点就是基本上都要在野外多工作，靠在实验室、办公室那点儿时间，恐怕很难干好自然保护与环境生态相关工作。毕竟，大家的学习、研究和工作对象是大自然。由于专业属性原因，自然保护与环境生态类专业都有自己的对口专业机构，除了大家都知道的自然保护区之外，农业资源与环境、水土保持与荒漠化防治也都有自己的对口机构，这些机构大多数是事业单位，与之相联系还有对应的服务公司。");
		ONE_SENT.put("动物生产类",
				"动物生产类，主要研究农业动物，也就是猪、牛、羊、禽等畜禽动物及家蚕、蜜蜂、水产等特种经济动物的遗传育种、营养饲料、产品加工等，以得到“好的品种”——“好的饲料”——“好的产品”。从就业方向来说：\r\n"
						+ "    （1）养殖场。一般从事基础的管理工作，像接产、打疫苗之类的劳动等。这类工作的缺陷是现代养殖场都是封闭管理，工作环境赃、臭，工作地点偏远，好处是稳定，工作没什么压力。\r\n"
						+ "    （2）销售。饲料厂、兽药公司、动物保健公司有大量销售岗位。压力大、强度高。\r\n"
						+ "    （3）考研或者考公务员，出入境检验检疫局、畜牧局、防疫站、乡镇一级的畜牧兽医站。");

		ONE_SENT.put("动物医学类", "动物医学类，通俗地讲就是培养兽医的专业。动物医学专业最受欢迎的就业出路的考公务员，如畜牧局、动物防疫检验站。此外最广的就业工种是宠物医生，发展趋势看好。");
		ONE_SENT.put("林学类",
				"是一门研究如何认识森林、培育森林、经营森林、保护森林和合理利用森林的学科，是在其他自然学科发展的基础上，形成和发展起来的综合性的应用学科。主要就业方向，政府、事业类单位：森林资源调查、森林资源资产评估、森林资源保护、森林灾害防治、森林管理、自然保护区管理。");
		ONE_SENT.put("水产类",
				"水产类专业是从动物科学领域分出来的一类专业，主要研究水产养殖、育种、病害防治、水环境、生产和管理等水产相关知识技能的专业。就业地点主要是沿海地区，尤其像山东省、福建省、海南省等沿海地区的人才需要会更加突出。水产类专业的本科毕业生一般有几下几个就业途径。水产养殖、饲料生产等业务的各类企业");

		ONE_SENT.put("草学类",
				"草业类专业是一门研究草地资源的开发、利用和保护的学科，主要服务于农业、畜牧业、园林绿化和生态环境治理等领域。未来就业方向\r\n" + "    1、草地畜牧业，农牧部门、农牧企业、农牧合作社\r\n"
						+ "    2、草原生态保护，草原管理部门、草原监测站、草原生态工程公司\r\n" + "    3、草坪绿化建设，园林部门、园林公司、高尔夫球场、体育场馆");

		ONE_SENT.put("基础医学类",
				"基础医学主要研究人为什么会生病，怎么才能保持健康等等内容，基础医学专业学习内容涉及生物学、解剖学、病理学、生理学、药理学等多个学科，虽然也属于医学大类，但是本专业拿不了执业医生资格证，本专业毕业的学生主要就业方向是在高等医学院校和医学科研机构等部门，从事基础医学各学科的教学、科学研究工作。本科生就业质量很一般，只有考研或者考博才能进研究所或者高校当老师");

		ONE_SENT.put("临床医学类",
				"临床医学类是一个医学大类，包含了临床医学、麻醉学、医学影像学、眼视光医学、精神医学、放射医学和儿科学，这个专业的学生主要学习基础医学、临床医学、手术学等方面的基本知识和技能。临床医学类专业毕业的学生都可以拿到执业医师资格证，也就是我们熟悉的能治病救人的医生，主要就业方向就是医疗卫生单位。比如各类医院、医学科研部门、卫生所、诊所等等。目前医生的就业前景还是不错的，社会地位高，越老越吃香、收入也很可观。但是想进入好的医院或者单位，考研考博也是必须得。");

		ONE_SENT.put("口腔医学类",
				"口腔医学类就是口腔医学，主要学习口腔及颌面部疾病的诊断、治疗、预防等方面的基本知识和技能，比如:口腔内龋齿的充填，智齿的拔除，假牙的种植，牙齿的矫正，口腔溃疡、牙周炎等口腔疾病的诊疗等。本专业毕业的学生可以进入公里或者私立医院当口腔科医生，也可以自己开口腔诊所、同时还可以选择私立医院做整形科医生，真因为口腔医学毕业的学生，选择多，学历要求也还不那么卷，所以很多人选择口腔医学专业。");

		ONE_SENT.put("公共卫生与预防医学类",
				"公共卫生与预防医学类专业主要研究传染病和流行病的病因、预防、筛查、控制和消灭等方面的基本知识和技能，毕业生可以从事卫生防疫、控制传染病、监督食品卫生等工作。比如研究流感、甲流等疾病怎么预防和控制等等。本专业毕业生就业方向一般是卫生防疫部门、环境卫生或食品卫生监测等机构。从就业方向可以看得出来，这些单位都是铁饭碗，还要靠自己去考的。");

		ONE_SENT.put("中医学类",
				"中医学就是从中医的角度进行疾病的诊断、治疗、或者调理人体亚健康等等。例如:把脉诊断病情，针灸、拔罐、艾灸等，同时中医学还包含中药药方的制定，中药制剂的研发等等。本专业毕业的学生可以去各级中医院当医生，也可以到中医科研机构做研究，或者自己开中医诊所等等。想报考这个专业的孩子一定要对中医有兴趣，而且最好家里有一定的中医背景或者资源，同时中医专业属于大后期专业，真是越老越吃香，需要耐得住寂寞。国家对于中医也是非常的重视，前景还是非常看好的。");

		ONE_SENT.put("中西医结合类",
				"中西医临床医学既要学习传统中医学相关知识、又要学习西方现代医学技术等方面的基本知识和技能，将中医药与西医技术结合，进行疾病的预防、临床诊断和治疗。虽然听上去感觉很厉害的样子，实际上目前这个专业比较尴尬，属于不中不西，本科生就业一般从事社区保健、卫生防疫、卫生行政事业管理等工作。想要有更好的发展就必须考研考博，或者在读研的时候选择具体的方向去深造。");

		ONE_SENT.put("药学类",
				"药学主要研究药品的研发、生产、加工、质检、销售、管理等等方面的知识和技能。毕业生的就业方向有各类药企、药品研发机构、医药代表、各类药店或者医院药房等等。本专业的本科生就业质量一般，如果进药企的话，主要做一些生产类的工作，想要研发药品的话，必须要考研，甚至考博，留学。如果能读到研究生或者博士前景还是很不错的。想要选择本专业的孩子化学成绩不能太差哦。");

		ONE_SENT.put("中药学类",
				"中药学主要学习中药的鉴定、分析、炮制、制剂制备，配药等方面的知识和技能，例如:各种中成药的成分分析，中药制剂的研发，或者根据中药药方进行配药、抓药、煎药等等。主要的就业方向是中医院的药房、中医诊所的药房做药剂师。或者中药企业从事药品研发、生产等工作。选择本专业需要对中药感兴趣，因为本科生就业质量一般，考到研究生阶段的话就业面会更广，待遇也会更高。");

		ONE_SENT.put("法医学类",
				"法医学主要学习法医相关的知识和技能，就业方向可以在公安机关当法医，协助警察检案监定、也可以在医院当医生、或者司法鉴定机构从事司法监定、以及保险公司从事保险服务等。例如:死者的尸检，案发现场不明血迹、残留物的鉴定，事故中人身伤害程度的鉴定，医疗纠纷中过错和不良后果的鉴定等等。想报考本专业的孩子，需要有过硬的心理素质。");

		ONE_SENT.put("医学技术类",
				"医学检验技术主要学习医学检验方面的知识和技能，比如: 检验血液中血糖、血红蛋白等各种物质含量的检验，亲子间DNA的检验，食品中细菌含量的检验，动植物疫病的检验、检疫等。就业的方向一般在各级医院、血站、防疫、检验等部门进行医学检验、卫生检验等。这些单位一般要么需要考试、要么需要资源，工资不能算高，但是很稳定，比较适合女孩子报考。");

		ONE_SENT.put("护理学类",
				"护理学就是学习如何进行临床护理、预防保健、或者护理管理、护理教学等知识和技能。比如:医院内的病人护理，新生儿和产妇的护理，老年人的保健和疾病预防等。护理学专业就业前景非常不错，本科生就能就业，尤其是男护，非常吃香，工作虽然辛苦但是收入很不错，工作稳定且受人尊敬。如果不想那么辛苦，建议考研，之后可以从事教学或者研究工作，也可以在医院从事护理科行政工作。");

		ONE_SENT.put("管理科学与工程类",
				"管理科学主要学习数学、经济学、管理学、计算机等方面的基本知识和技能，毕业生可以从事企业管理、项目的规划、决策、管理等工作，具体的内容根据涉及的行业不同而有所区别。管理包括公共管理、企业管理、项目管理、后勤管理、信息管理、采购管理等多个领域。本专业毕业想要更好的就业最好有资源。或者在大学期间多参加社会实践，提升自己的综合能力，现目前本专业毕业生选择考公的比较多。");

		ONE_SENT.put("工商管理类",
				"包含的细分专业非常多，主要学习的是管理学、经济学和现代企业管理等方面的基本知识和技能，包括企业的经营战略制定和内部行为管理等等，运用现代管理的方法和手段进行有效的企业管理和经营决策，制定企业的战略性目标，以保证企业的生存和发展。主要就业方向是各级企、事业单位及政府部门从事管理以及教学、科研方面的工作。选择本专业大类的孩子，本科阶段学习的内容多而不精，考研的时候可以选择具体方向深造，目前前景比较好的几个方向包括，会计学、财务管理、审计学、资产评估等等。本专业对于证书和经验要求比较高。");

		ONE_SENT.put("农业经济管理类",
				"农林经济管理主要学习经济学、管理学、农学、林学等方面的基本知识和披能，本专业毕业的学生可以进入农林类企业工作，比如进行农林业的企业的经营管理、市场营销、流通贸易等。例如:牛奶、大米等农副产品的营销、生态农庄、农田景观等农业投资项目的评估等等。因为行业特殊性，毕业生考公的也比较多。想要自主创业，需要雄厚的背景资源做支撑。");
		ONE_SENT.put("公共管理类",
				"公共事业管理类的细分专业也非常的多，公共事业包括交通、科技、体育、卫生、环保、社会保险等等领域。本专业的学生就是通过学习现代管理理论，技术与方法等方面的知识和能力，进行公共事业的规划、建设和管理等工作。毕业生比较常见的就业单位有:电力、供水、垃圾处理、污水处理、燃气供应、公共交通等等单位或者公司。这些单位一般都需要考试或者有相关资源，工作稳定，待遇好。");
		ONE_SENT.put("图书情报与档案管理类",
				"图书馆学主要研究信息管理、文献检索、文献编目等知识，毕业生可在图书馆、档案馆等进行图书和档案的整理、文献和文件的编目和检索、其他信息的咨询和检索等工作。信息资源管理则主要研究管理学、信息科学与技术、信息资源集成管理、电子政务系统等方面的基本知识和技能，毕业生可以在国家机关、企事业单位的信息部门进行信息采集、加工、分析、管理、检索与保护以及信息系统的建设与维护等工作。本专业毕业的学生需要考公才能就业。");

		ONE_SENT.put("物流管理与工程类",
				"物流管理主要学习管理学、经济学、信息技术、现代物流管理等方面的基本知识和技能。毕业生可以在贸易、物流类企业单位进行物流活动的计划、组织.指挥、协调、控制和监督等工作。比如:供应货品的采购入库，货品的拣取和记录，快递的运输和配送，物流系统的规划设计等。就业面比较窄，所以前景一般，考公比较多。");

		ONE_SENT.put("工业工程类",
				"工业工程主要研究怎么提高工业的劳动生产率、怎么保证质量的同时降低成本等内容。毕业生多在各种工业、制造业企业，进行生产过程和系统的规划、设计、运行、控制、以及产品质量的管理和生产成本的控制等工作。本科生就业质量一般，想进入企业的管理层，需要过硬的专业知识和丰富的经验，需要考研。");

		ONE_SENT.put("电子商务类",
				"电子商务就是学习怎么利用互联网进行各种商务活动，包括网上营销、网上客户服务、网上广告、网上调查等。例如:天猫、京东等店铺优惠活动的策划，各种商品的在线售卖和售后，朋友圈、微博的广告的投放推广，商品销量、关注度等数据的分析等。目前电子商务开设院校非常多，整个行业也趋于成熟。学校学的内容很容易跟不上企业的要求，所以选择本专业需慎重，最好是有相关资源，想报考本专业的学生尽量选择电商发达的城市，可以感受更多的电商氛围和实习机会。");
		ONE_SENT.put("旅游管理类",
				"旅游管理专业主要学习经济学、旅游学、文化学等方面的基本知识和技能，毕业生可在旅行社、旅游咨询公司、景区公园等企事业单位进行旅游咨询、策划、开发、管理等工作。例如:旅游景区的开发与管理，旅游路线的规划设计，旅游行程的引导和景区介绍等。旅游业一直是一个不错的行业，虽然由于口罩原因，过去两年行业不景气，但是只要一放开，旅游行业复苏非常快，就业前景比较看好，想要稳定就选择考公，想要更多的收入，就选择旅游类公司。");

		ONE_SENT.put("中国语言文学类",
				"语言文学类专业，毕业后当语文老师或者从事文字媒体，以及考公等体制内的职业方向；这个专业呢你会懂得如何去审视中文和中国文学作品，去理解中文之美。在本专业中招生人数较多的专业是汉语言文学、汉语言和汉语言国际教育。汉语言文学专业就是最常见的中文专业，它偏向于研究中文文学作品。汉语言专业主要是研究汉语本身的专业。汉语言国际教育专业本质上它是一个特殊的师范专业，目的是为了出国当汉语老师。师本专业类的学习压力是比较小的，就是要读很多很多各种各样的书。抗拒阅读的同学，你学习起来可能比较痛苦哦\r\n"
						+ "    因为语言只是工具，工具要配合其他行业，才能发挥出更大的作用。语言的的专业都存在这个规律。比如你本科学中文，那你硕士学新闻传播就能把两者优势结合在一起，成为你你个人的独特优势。");

		ONE_SENT.put("外国语言文学类",
				"外语类专业学习英语小语种翻译，毕业后进入中小学或者培训机构。当英语老师。去企业的海外部门当翻译，或者进入跨境电商，外贸专员等等。但是；外语对专业的学习压力不大，主要就是熟能生巧，只会外语的话竞争力不强。\r\n"
						+ "    外贸是外语专业的出路之一，外语外贸是紧密结合的。在外语类中招生人数较多的专业是英语小语种翻译和商务英语。英语和小语种专业就不多说了，就是学习语言。文学翻译专业的专业课更多的就是笔译和口译。商务英语的专业课就是增设了商务理念、国际贸易之类的课程很多同学会在大学期间再学习一个专业的知识，为自己找工作创造优势等专业的主要课程包括听力、语言、绘画、翻译、写作和文学课程。");

		ONE_SENT.put("新闻传播学类",
				"毕业后进入传统媒体或者新媒体行业学习这个专业内，你会成为一个有同理心的通过文字、图片、视频去讲故事的人。同时也是一个经历大于学历的专业；本专业的典型对口职业有新媒体运营编导、编辑、记者、广告策划、文案设计师、主持人、评论员、节目策划。新闻学更加偏重于新闻采访、编辑和写作，\r\n"
						+ "    广播电视学，更加偏重于台前的播报和幕后的节目制作。\r\n"
						+ "    传播学更加偏重于研究、传播理论和控制。广告学则更偏向于将传播理论用于实践以及学习市场营销的相关课程。\r\n"
						+ "    网络与新媒体，这是一个新专业培养目标，更接近于新媒体运营，但是实际教学和实践是比较脱节的。新闻传播学类的学习是很轻松的，毕竟新闻类的工作要背负的责任并不大，只是要掌握的媒体软件数量比较多。在培养周期方面，新闻传播学类是比较短的，因为大部分自媒体都只需要本科学历。如果你想进入电视台等知名媒体工作，硕士是门槛，而且还得度过很长的实习期。本专业的专业壁垒属于中等偏低，因为本专业没有过硬的技能，其他专业的同学也可以从事本专业。现在只要打开抖音，谁都可以当记者了。");

		ONE_SENT.put("历史学类",
				"历史学类专业主要就是学习，研究历史，毕业后成为历史老师，考工或者进入历史文创产业。学习这个专业内，你会成为一个更加理性睿智的懂得以史为鉴的人。如果你对历史没有热爱，这个过程是漫长而痛苦的。因为历史学是非常讲究和严谨的，正史一致是基本的素质要求。这里必须提醒各位同学，你喜欢读历史类的读物和真正研究历史是两码事。主要的课程包括中国通史、世界通史、历史文献学、历史地理学、中国断代史、世界史和考古学。\r\n"
						+ "    推荐考研。因为历史学历专业，如果你要进中学，当历史老师，一般都会要求硕士了。如果你要进博物馆或者考古研究院，那也是硕士起步。本专业的专业壁垒属于比较低的，因为没有特别的硬核技能或者职业证书作为壁垒。历史学专业的典型对口职业有历史老师、公务员、记者、导游、策划和编辑。");

		ONE_SENT.put("数学类",
				"数学类专业研究和学习数学，毕业后，当数学老师成为企业的数学技术人员，或者转入其他需要数学的行业，比如金融、保险、大数据、人工智能行业。学习这个专业类，你将掌握理解世界的基石。数学在本专业类中招生人数较多的专业是数学与应用，数学和信息与计算科学。数学与应用数学专业的学习内容分为两种，第一种是纯粹的数学研究，内容是数学本身的问题。第二种是应用数学，研究的是数学。现在已经演变成了用计算机和数学手段去研究信息中的计算问题。本专业不会学习计算机相关的硬件知识，但是会学习编程。你可以认为这个专业是应用数学在信息领域的分支，也就是应用数学和计算机科学的交叉学科。数学专业的学习是很困难的。数学分析、抽象代数、泛函分析等等课程几乎是大学最难的一类课程了。这时大家的数学天赋就会显现出来。数学系的主要课程包括数学分析、高等代数、解析几何、抽象代数、概率论识别函数、半函分析、拓扑学等等。。因为本科课的数学内容比较杂，如果想要深入学习某个领域，那么读研是很有必要的。");

		ONE_SENT.put("物理学类",
				"物理学类专业学习物理，毕业后成为物理老师，或者成为企业的物理技术人员。如果你打算考研，转去其他工科，那么本科打下的物理技术会是很好的积累。学习这个专业类，你会成为一个思维缜密，掌握工科底层原理的人。物理学类的专业的学习压力是比较大的，因为物理需要运用大量的数学知识，学习的数学也比较难，还需要比较强大的逻辑思考能力。它的主要课程包括力学、热学、光学、电子学、原子物理论、数学、物理、方法理论、力学、量子力学、物理实验等等。\r\n"
						+ "    推荐你至少读一个硕士。如果只是本科学历，本专业直接对口的岗位比较少，除了当物理老师，大部分同学都需要跨入物理相关的行业工作，那么最好的方式就是在硕士阶段学习。\r\n"
						+ "    一般工科、理科、医学的毕业高，社会人文学科的壁垒低，物理学的壁垒，围城的专业就太多了。除了化学、化工、生物、食品这一类以化学为主的专业，其他的专业都可以作为物理学的同学进一步发展的备选，包括热门专业，电器、电子信息等等。本专业的典型对口职业有物理老师、科研人员，各行业中的物理技术人才。这些职业对口的行业是学校、金融、军工、医疗、能源、电信、仪器、科技、半导体行业。");

		ONE_SENT.put("化学类", "一句话描述");
		ONE_SENT.put("",
				"化学类专业学习化学毕业后，当化学老师或者进入化工制药企业，本科进入生产岗位或者销售岗位，硕士以上可以搞研发学习。职业 对口的行业是学校、化工企业、制药企业和环保企业这个专业类，你会掌握物质相互转换的神奇规律，成为一名炼金术士在化学类中招生人数较多的专业是化学和应用化学。化学专业主要是研究化学本身的问题。应用化学专业主要是研究将化学的知识用到具体的行业上。在这里给大家讲一下这两个专业和工科的化工之间的区别。比如生产一袋洗衣粉，化学研究能够去污渍的有效的成分，利用化学研究，怎么将这种有效的成分做成洗衣粉。化工则是研究怎么大批量的去生产洗衣粉；考研本专业无论是学习还是就业，都会有大量的时间泡在实验室。我无法接受这一点的同学，我建议你早日考虑转行，或者就不要报化学专业了。");

		ONE_SENT.put("天文学类", "一句话描述,就是研究天体和宇宙的科学");

		ONE_SENT.put("地理科学类",
				"地理科学类专业后常见岗位是地理老师或者从事遥感等地理信息的工作。学习这个专业类，你会成为一个下知地理的人。大家抱怨比较多的点在于野外实践太累了，爬山都爬，爬了，日晒雨淋的专业，如果你在乎你就放弃吧！在地理科学类中招生人数较多的专业是地理科学、人文地理与城乡规划和地理信息科学。地理科学专业主要偏向于研究自然环境是如何形成的。学习大气圈、水圈、岩石圈的相互作用和关系，偏重于自然地理学人文地理与城乡规划。这个专业过去叫资源环境与城乡规划管理，属于人文地理学科，既学习地理知识，又学习规划的知识，既可以认为是地理类的万金油专业，也可以认为是一个四不像专业。本专业做规划当然比不上建祝你的城乡规划了，人家才是正规军好吗？地理信息科学专业是专门研究地理信息系统的专业，主要是将遥感r s全球定位系统g p s的数据进行收集、分析和加工，是地理学、测绘计算机专业的交叉学科。在。地理科学类的专业壁垒属于中等偏高，毕竟有数学、物理和编程作为门槛。要提醒大家的是，地理信息专业是要学习计算机相关的课程的，相当于为转入计算机行业打开了一扇门。本专业的典型对口职业有地理老师、工程测量人员、g i s技术人员、旅游开发公务员这些职业对口的行业是学校、工程行业、地理信息公司、旅游o t a气象局、测绘局、水务局、地震局、规划局、林业局、国土资源局等等。");

		ONE_SENT.put("大气科学类",
				"大气科学说白了就是气象学。这个专业呢是我们在平时报考专业的时候可能想不到的一个专业。大气科学呢是研究大气的各种各样的现象，那么说白了就是大气的一个变化呀，包括像人类的活动对它的一些影响哈。然后这些现象的演变规律，以及如何利用这些规律为人类服务的一门学科，分为大气科学和应用。气象学。其实这个我们气象学是现在目前大气科学对我们现在应用最多最广泛的。其实你干的工作主要就是气象局及其他气象的应用单位啊，应用单位可多了。");
		ONE_SENT.put("海洋科学类",
				"因为专业具有较强的专业性，以及应用的范围广泛，所以海洋科学类专业就业领域广泛，发展前景良好。1、科研工作：可以在海洋科研部门、环保部门从事海洋资源调查与开发利用、环境保护、水产养殖、海洋事务管理、海洋新技术等方面进行研究；其次在化工、石油、地质、水产、交通等领域从事化学实验及化学研究方面；但进行这一部分工作需要学生做好深造的准备，本科层次可能比较困难。\r\n"
						+ "    2、气象局、海洋局系统以及交通、军事、环保等部门从事海洋调查预报、环境评价方面的工作；\r\n"
						+ "    3、从事海洋科学相关的科学研究、教学、技术开发和管理方面的工作");

		ONE_SENT.put("地球物理学类",
				"地球物理学偏重于用物理学的方法和原理去研究地球，比如地质、地磁、地电等等。地质学偏重于研究地壳和岩石圈，主要是为了找矿找油。地理科学类专业的学习压力属于中等。虽然地理在高中属于文科，但是大学同样需要学习数学哟，而且数学还比较重要。");
		ONE_SENT.put("地质学类",
				"地质学是一门综合性很强的学科，看你进的学校，如果你选择的专业就是地质学，且学校在大二大三不分专业的话，你会学到岩石、矿床、矿物、勘探各个方面，偏理论的知识，不过不专。地球信息应该主要偏向于地理信息，就是包括遥感技术、测绘等偏工科，偏实用的东西。地质工程跟传统的地质已经不太相关了，他应用于建筑、桥梁、隧道、水文、环境、地质灾害的防治等。总的说来，你如果想搞科研，读博，那么选什么都无所谓，看学习以后的兴趣，如果想能有好的就业，非要选择的话，请选地质工程和地球信息技术。千万不要选地质学");

		ONE_SENT.put("生物科学类",
				"生物学本科期间学的东西过于广，而且对实际科研和应用的能力培养的太少了，所以读研几乎是必经之路。硕士毕业后继续攻读博士，都是非常正常的。就读本专业，你还要做好留学的准备；生物科学类专业其实和高中生物的区别是挺大的，但是和化学的联系更多，本专业的核心科目是生物。化学；低学历，毕业后成为生物老师医药代表，生物仪器销售高学历，可以进入生物医药公司做研发学习这个专业类你会成为一个懂得万千生物奥秘的人。\r\n"
						+ "    不适合报考的同学注意是不适合抗拒化学，抗拒生物，不喜欢做实验。没有读研打算的同学，你们就不要报考生物科学类了");

		ONE_SENT.put("心理学类",
				"心理学类专业学习心理学毕业常见岗位是学校的心理健康，老师不能成为心理医生，只能成为心理咨询师。这意味着什么呢？你只能和别人聊天，没有处方权，也不能在医院的心理科室做人。学习这个专业内，你会成为一个更能理解自己内心情绪的人，知道自己的情绪为什么会发生，以及会向哪个方向发展。");
		ONE_SENT.put("统计学类",
				"统计学技能可以结合各个专业发挥作用，统计学本质上是和数据进行对话，扮演的收集、清理、解释数据的工具角色。本专业的典型对口职业有金融量化人员、互联网统计、生物统计、风险分析计算、用户分析、数据挖掘和公务员这些职业对口的行业是金融、保险、互联网、生物、科学，各个行业都有对口岗位，公务员岗位它也不少主要课程包括数理统计、随机过程、概率论、数据结构识别函数等等。一般都会选择跨考研究生。统计学其实就是和各种数据对话的工具，这些工具可以用于各种行业中统计学专业的专业壁垒属于中等偏高。我们可以总结统计学历专业不适合报考的同学，注意是不适合抗拒数学，抗拒编程，不喜欢和数据打交道，逻辑思维比较差的同学，你们就不要报考统计学专业了。统计学专业应用和大数据、人工智能、生物医药等国家政策倾向的产业深度结合。");
		ONE_SENT.put("力学类",
				"这是一门理论性较强的工科专业，主要是运用力学相关的知识，解决各行各业的跟力有关的问题。对于物理和数学有较高的要求。该专业放在不同背景院校，研究的方向不一样，比如放在北航南航，则是航空航天方向的，放在西南交大则是土建，轨道交通运输方向的");
		ONE_SENT.put("机械类",
				"这类专业，主要是培养具备机械设计、制造、使用维护等方面的人才。普遍来说，本科毕业就业不愁，但是工作环境不好，收入待遇不高，今后收入上限也较低。随着时代的发展，该类专业，根据实际社会需求，也逐渐形成了很多的分支领域，以及交叉学科。比如说，材料成型，车辆工程，机械设计制造及其自动化等");

		ONE_SENT.put("仪器类",
				"主要学习能设计、制造、使用精密仪器设备的专业人才，同时也需要具备测量和控制方面的基本能力。是一个高精尖的行业，学科交叉性非常强，电子，光学，精密机械，计算机等等都要学，建议是读研深造。比如说，我们的卫星发射了之后，如何监控他飞到哪儿了。本科毕业，院校一般的话，大多去一些仪表厂之类的。");

		ONE_SENT.put("材料类",
				"研究研发生产制造各类材料的一类专业，下到我们平常生活中能够看得到的几乎任何东西，比如说衣服的材质，手机的材质，也包含了建筑材料，甚至，飞机火箭用的材料，当然也包含了比较前沿的，高分子材料，纳米材料，新能源材料等等。但是，生化环材，四大天坑之一。材料就是其中之一，这里指的是，本科毕业找工作没问题，但是环境差，待遇低。该专业两级分化严重。本科毕业搞生产，研究生及以上毕业有机会涉及到研发。新能源材料算是风口，但是本科不够。");

		ONE_SENT.put("能源动力类",
				"这是一个合成后的学科，要拆分来看，能源和动力两大板块，四个方向。比如说，热能方向，火力发电厂，新能源的研发与应用方向，空调制冷，发动机的动力传感方向等等。毕业同样可以进国家电网，五大发电集团等等，就业不愁");

		ONE_SENT.put("电气类",
				"凡是带电的行业，电气类都可以涉及得到，分为强电和弱点两个方向，强电是指，我们能够摸的着的，感应得到，有“触电”这种感觉，弱点更多的是控制方向，比如说，我们手机上面刷视频，往下滑，其实本身是有很微弱的电流产生，只是我们身体感觉不到。整体来说，这是目前最热门的专业大类之一，毕业好找工作，可以进类似于国家电网，南方电网这样的优质国央企，也几乎可以进到你能够见到的任何生产制造类企业。");

		ONE_SENT.put("电子信息类",
				"这是目前公认的三大最热门最前沿的专业大类之一。对于物理和数学要求极高。本科毕业更多的是去到一些基础生产岗位，就业岗位多，薪资待遇不低，但是如果能深造下去，读研甚至读博，这个专业的投收比非常非常高，收入远超大部分其他专业。可以进入到移动联通电信这类的传统三大运营商，也可以进到华为，小米，中兴等等新兴的大厂及其配套商。");

		ONE_SENT.put("自动化类",
				"在研究生阶段叫控制科学与工程，机械，计算机，电子信息都要学，交叉性较强，对于数学、物理要求高。选择这类专业，能读研就读研，研究生以上起步年薪不会低于15-20W。本科毕业就业的话，更多的走机械方向，做设备的数据调教检修等工作，或者学习编程去走计算机方向。");
		ONE_SENT.put("计算机类",
				"这是一个热门的不能再热门的专业，各行各业都离不开计算机的助力。只要你有能力有技术，哪怕你是民办本科毕业，这个行业都一定不会亏待你。计算机类专业主要是对于逻辑思维能力有比较高的要求，所以数学好，基本上学计算机今后都不会太差。需要注意到的是，这个专业，既适合考公考编，岗位选择多，也可以把技术学好，在社会上谋求一份高薪工作。本科毕业都能拿到上万的月薪，研究生的收入上限会更高。当然也正是这些优势，所以说，报考热度最高，没有之一。所以，很卷。");

		ONE_SENT.put("土木类",
				"过去十年之前，最热门的专业之一。搞房建，搞基础建设工作，修房子，修桥修路。大家肯定不陌生。但是现在突然跌落神坛，现在行情，岗位急剧减少，收入也急剧减少。常年随项目跑，在工地工作。特点是，本科毕业就有机会进到像中铁中交中建中水这类央国企，收入待遇普遍还是不算低。但是正因为常年不着家，工作环境苦，所以离职率高。");

		ONE_SENT.put("公安技术类",
				"这是一个公安类专业，在公安警校的提前批报考，需要政审，体检，体能测试。毕业那年可以参加个公安联考，成为人民警察。具体到公安司法等部门的不同岗位，开设的专业比如说，像刑事科学技术，是搞案发现场的勘察，比如说指纹识别等等，网络安全与执法，比如说就是网络警察，这个大家应该不陌生。最好的学校，就是中国人民公安大学，中国人民警察大学。");

		ONE_SENT.put("水利类",
				"水利类专业，毕业找工作问题不大，工作地点多在一些大小型水电站，水利局，农业局等等相关的国央企或者事业单位。从工程的设计到施工，再到具体的运营维护，都可以涉及。整体来说，工作不算忙，收入不算高，很稳定。当然，如果你能涉及到具体的偏设计或者施工方向。搞工程嘛，懂得都懂。");

		ONE_SENT.put("测绘类",
				"测绘类专业，尤其是测绘工程，一般来说，很多人可能见到过，就是那里要修路，修房子，都会现有一群人哪个设备在外边，甚至荒郊野岭到处测量。找工作容易，工作环境不好，离职率不低。当然也有遥感科学与技术，导航工程等这些要“高端点的”专业，但是名校+硕博基本上是一个初步门槛。");

		ONE_SENT.put("化工与制药类",
				"两大方向，化工能源环保方向，以及化学制药方向。以制药工程为例，几乎99%的人毕业都是进制药厂从事生产工作，收入不高，但是比较稳定。读研的话，国内的研发环境，短期内，应该是没有国外好的。另外，就是化学工程与工艺，毕业更多的去化工厂，炼油厂等等之类的单位工作，对于女生不太友好");

		ONE_SENT.put("地质类", "主要是研究地质结构、地下水等方面的勘察的利用。一般在市政、水利水电、交通工程领域会涉及得到。受基建行业大环境影响，现阶段这类专业，岗位不多，对口工作不太好找。普遍不太推荐。");

		ONE_SENT.put("矿业类", "学习怎么做地质调查，找矿找油，做基础建设的勘察，地质环境和地质灾害的评估调查。成为一个挖掘地底宝藏的高手。研究如何找到矿产资源，如何开采出来，然后如何加工运输等等。");

		ONE_SENT.put("纺织类", "偏于材料类，对化学要求较高，但是整体行业不景气，大部分和服装行业相关，但是航天器材和医疗行业的支架和口罩的熔喷布都和这个专业相关。（东华大学最牛）");

		ONE_SENT.put("轻工类",
				"学习如何染布、造纸、造皮革、搞印刷和设计包装的专业，就业一般去轻工类企业，高学历可以去研究院。属于化工的分支，生产企业有一定污染性，工作地点一般在郊区，就业对女生也不太友好，整体收入水平比较低。");

		ONE_SENT.put("交通运输类",
				"研究怎么把东西从一个一个地方运输到另一个地方，分为水路、公路、铁路、民航四个方向，就业对口相关管理单位、建设单位、运营维护单位，核心是如何将东西又快又省的送到远方。行业全部依靠政府投资，基建放缓对行业冲击较大");

		ONE_SENT.put("海洋工程类", "就业面比较窄，大部分去造船厂或者船舶配套企业，整体薪资一般，工作环境差。海洋资源开发侧重海洋资源的开发（生物、矿产），可往水产和机械方向发展。");

		ONE_SENT.put("航空航天类",
				"国家战略储备人才，大型飞行器设计和制造集团（国企），或者民用飞行器设计制造企业（大疆），学习计算机电子自动化机械力学材料等课程，本科毕业一般偏向维修，研究生以上去设计院或者相关科研院所。工作比较稳定，待遇较好，但是大富大贵难。");

		ONE_SENT.put("兵器类", "研究军事对抗中所使用的武器系统和军事技术器材的科学技术。国家战略储备人才，就业比较不错，一般为央国企，但是就业地点一般不在市区。");

		ONE_SENT.put("核工程类", "本科毕业基本往核电站和核电厂方向，就业不错，但是大概率会涉及三班倒，研究生以上可进相关研究院所，可控核聚变突破之日就是这个专业腾飞之时。（哈工程很牛逼）");

		ONE_SENT.put("农业工程类", "主要就是相关工科专业在农业上的应用。一般在农林类院校，如果不想往农业方向发展，可以去往土木和水利水电方向，本科毕业一般也是生产一线或者农田，研究生毕业可以往设计院方向走。");

		ONE_SENT.put("林业工程类", "“伐木类”专业，森林工程主要学习林业和机械电气类知识，和运输工程类，木材科学与工程主要就是往家具行业走，林产化工偏林业产品的化学加工，可往化妆品，日用品方向延展。");

		ONE_SENT.put("环境科学与工程类",
				"学习环境问题的控制和处理，毕业后对口污水处理厂、给排水、环境检测、环境评价、环保局等。对口岗位较少。学习难度不大，物理化学生物都要学，化学偏多。本科只能去处理环境污染一线，对口职业环境工程师，环评人员，销售，公务员，环保人员，大部分对口水污染。");

		ONE_SENT.put("生物医学工程类",
				"与人口老龄化趋势吻合，主要研究医疗器械，是未来的风口专业，现在大部分医疗器械依赖进口，所以国产医疗器械前景很好。实际学习重物理，重电学。主要研究有源医疗器械（带电）就业情况不错。");
		ONE_SENT.put("食品科学与工程类",
				"研究食品健康和营养、工艺、生产、储藏、质检，毕业后对口食品企业的研发和质检、第三方检验机构以及食药监局、海关、卫生防疫等公务员岗位，本科食品生产岗位，酒厂偏多，研发和质检一般需要研究生以上。就业率和收入都一般。本科实验课程涉及杀鸡杀兔子");

		ONE_SENT.put("建筑类",
				"学习建筑、城市规划、风景园林的理论和实践，五年制，毕业对口建设集团、房地产、设计院等，行业下行。建筑负责设计，土木负责建造。建筑学主要研究外观、空间、功能和使用，城乡规划主要研究区域内所有建筑、道路、公园等的布置，风景园林主要研究户外步行和休憩空间的打造。就业看中学校层次（建筑老八校），工作压力较大，一般为乙方单位。");

		ONE_SENT.put("安全科学与工程类",
				"学习保证企业安全生产的理论、技术和应用，就业主要是建筑施工单位、工厂、企业中做安全管理，在机构中做安全评价。学习工学、心理学加管理学。主要就业1.考公进应急管理局、应急管理厅2.进企业往安全总监走（适合男生）3.第三方安全机构往高级工程师、安全专家走（女生也可以）整体就业率很高，但是收入一般，风险大。");

		ONE_SENT.put("生物工程类",
				"学习如何利用微生物代谢的产物来生产产品（啤酒和味精)，或者利用生物技术开发药物，和生物技术相比，更偏向在工厂中大批量实现技术，培养周期长，适合考研考博。本科一般做技术员、发酵工程师或者分析检测工程师，医药代表，读研后才有机会做研发岗。");

		HM_SC_CITY_CODE_NAME.put("AB", "阿坝");
		HM_SC_CITY_CODE_NAME.put("BZ", "巴中");
		HM_SC_CITY_CODE_NAME.put("CD", "成都");
		HM_SC_CITY_CODE_NAME.put("DY", "德阳");
		HM_SC_CITY_CODE_NAME.put("DZ", "达州");
		HM_SC_CITY_CODE_NAME.put("GA", "广安");
		HM_SC_CITY_CODE_NAME.put("GY", "广元");
		HM_SC_CITY_CODE_NAME.put("GZ", "甘孜");
		HM_SC_CITY_CODE_NAME.put("LZ", "泸州");
		HM_SC_CITY_CODE_NAME.put("LS", "乐山");
		HM_SC_CITY_CODE_NAME.put("LH", "凉山");
		HM_SC_CITY_CODE_NAME.put("MY", "绵阳");
		HM_SC_CITY_CODE_NAME.put("MS", "眉山");
		HM_SC_CITY_CODE_NAME.put("NC", "南充");
		HM_SC_CITY_CODE_NAME.put("NJ", "内江");
		HM_SC_CITY_CODE_NAME.put("PZ", "攀枝花");
		HM_SC_CITY_CODE_NAME.put("SN", "遂宁");
		HM_SC_CITY_CODE_NAME.put("YB", "宜宾");
		HM_SC_CITY_CODE_NAME.put("YA", "雅安");
		HM_SC_CITY_CODE_NAME.put("ZG", "自贡");
		HM_SC_CITY_CODE_NAME.put("ZY", "资阳");


		HM_CQ_CITY_CODE_NAME.put("B1", "巴南");
		HM_CQ_CITY_CODE_NAME.put("B2", "北碚");
		HM_CQ_CITY_CODE_NAME.put("B3", "璧山");
		HM_CQ_CITY_CODE_NAME.put("C1", "长寿");
		HM_CQ_CITY_CODE_NAME.put("C2", "城口");
		HM_CQ_CITY_CODE_NAME.put("D1", "大渡口");
		HM_CQ_CITY_CODE_NAME.put("D2", "大足");
		HM_CQ_CITY_CODE_NAME.put("D3", "垫江");
		HM_CQ_CITY_CODE_NAME.put("F1", "涪陵");
		HM_CQ_CITY_CODE_NAME.put("F2", "丰都");
		HM_CQ_CITY_CODE_NAME.put("F3", "奉节");
		HM_CQ_CITY_CODE_NAME.put("H1", "合川");
		HM_CQ_CITY_CODE_NAME.put("J1", "江北");
		HM_CQ_CITY_CODE_NAME.put("J2", "九龙坡");
		HM_CQ_CITY_CODE_NAME.put("J3", "江津");
		HM_CQ_CITY_CODE_NAME.put("K1", "开州");
		HM_CQ_CITY_CODE_NAME.put("L1", "梁平");
		HM_CQ_CITY_CODE_NAME.put("N1", "南岸");
		HM_CQ_CITY_CODE_NAME.put("N2", "南川");
		HM_CQ_CITY_CODE_NAME.put("P1", "彭水");
		HM_CQ_CITY_CODE_NAME.put("Q1", "黔江");
		HM_CQ_CITY_CODE_NAME.put("Q2", "綦江");
		HM_CQ_CITY_CODE_NAME.put("R1", "荣昌");
		HM_CQ_CITY_CODE_NAME.put("S1", "沙坪坝");
		HM_CQ_CITY_CODE_NAME.put("S2", "石柱");
		HM_CQ_CITY_CODE_NAME.put("T1", "铜梁");
		HM_CQ_CITY_CODE_NAME.put("T2", "潼南");
		HM_CQ_CITY_CODE_NAME.put("W1", "万州");
		HM_CQ_CITY_CODE_NAME.put("W2", "武隆");
		HM_CQ_CITY_CODE_NAME.put("W3", "巫山");
		HM_CQ_CITY_CODE_NAME.put("W4", "巫溪");
		HM_CQ_CITY_CODE_NAME.put("X1", "秀山");
		HM_CQ_CITY_CODE_NAME.put("Y1", "渝中");
		HM_CQ_CITY_CODE_NAME.put("Y2", "渝北");
		HM_CQ_CITY_CODE_NAME.put("Y3", "云阳");
		HM_CQ_CITY_CODE_NAME.put("Y4", "永川");
		HM_CQ_CITY_CODE_NAME.put("Y5", "酉阳");
		HM_CQ_CITY_CODE_NAME.put("Z1", "忠县");
		
		HM_GZ_CITY_CODE_NAME.put("GY", "贵阳");
		HM_GZ_CITY_CODE_NAME.put("ZY", "遵义");
		HM_GZ_CITY_CODE_NAME.put("LP", "六盘水");
		HM_GZ_CITY_CODE_NAME.put("AS", "安顺");
		HM_GZ_CITY_CODE_NAME.put("BJ", "毕节");
		HM_GZ_CITY_CODE_NAME.put("TR", "铜仁");
		HM_GZ_CITY_CODE_NAME.put("QN", "黔南");
		HM_GZ_CITY_CODE_NAME.put("QD", "黔东");
		HM_GZ_CITY_CODE_NAME.put("QX", "黔西南");

		HM_PROVINCE_CODE_NAME.put("A1", "安徽");
		HM_PROVINCE_CODE_NAME.put("B1", "北京");
		HM_PROVINCE_CODE_NAME.put("C1", "重庆");
		HM_PROVINCE_CODE_NAME.put("F1", "福建");
		HM_PROVINCE_CODE_NAME.put("G1", "甘肃");
		HM_PROVINCE_CODE_NAME.put("G2", "广东");
		HM_PROVINCE_CODE_NAME.put("G3", "广西");
		HM_PROVINCE_CODE_NAME.put("G4", "贵州");
		HM_PROVINCE_CODE_NAME.put("H1", "海南");
		HM_PROVINCE_CODE_NAME.put("H2", "河北");
		HM_PROVINCE_CODE_NAME.put("H3", "河南");
		HM_PROVINCE_CODE_NAME.put("H4", "黑龙江");
		HM_PROVINCE_CODE_NAME.put("H5", "湖北");
		HM_PROVINCE_CODE_NAME.put("H6", "湖南");
		HM_PROVINCE_CODE_NAME.put("J1", "吉林");
		HM_PROVINCE_CODE_NAME.put("J2", "江苏");
		HM_PROVINCE_CODE_NAME.put("J3", "江西");
		HM_PROVINCE_CODE_NAME.put("L1", "辽宁");
		HM_PROVINCE_CODE_NAME.put("N1", "内蒙古");
		HM_PROVINCE_CODE_NAME.put("N2", "宁夏");
		HM_PROVINCE_CODE_NAME.put("Q1", "青海");
		HM_PROVINCE_CODE_NAME.put("S1", "山东");
		HM_PROVINCE_CODE_NAME.put("S2", "山西");
		HM_PROVINCE_CODE_NAME.put("S3", "陕西");
		HM_PROVINCE_CODE_NAME.put("S4", "上海");
		HM_PROVINCE_CODE_NAME.put("S5", "四川");
		HM_PROVINCE_CODE_NAME.put("T1", "天津");
		HM_PROVINCE_CODE_NAME.put("X1", "新疆");
		HM_PROVINCE_CODE_NAME.put("Y1", "云南");
		HM_PROVINCE_CODE_NAME.put("Z1", "浙江");

		HM_PROVINCE_CODE.put("安徽", "A1_AH");
		HM_PROVINCE_CODE.put("北京", "B1_BJ");
		HM_PROVINCE_CODE.put("重庆", "C1_CQ");
		HM_PROVINCE_CODE.put("福建", "F1_FJ");
		HM_PROVINCE_CODE.put("甘肃", "G1_GS");
		HM_PROVINCE_CODE.put("广东", "G2_GD");
		HM_PROVINCE_CODE.put("广西", "G3_GX");
		HM_PROVINCE_CODE.put("海南", "H1_HN");
		HM_PROVINCE_CODE.put("河北", "H2_HB");
		HM_PROVINCE_CODE.put("河南", "H3_HN");
		HM_PROVINCE_CODE.put("黑龙江", "H4_HL");
		HM_PROVINCE_CODE.put("湖南", "H6_HN");
		HM_PROVINCE_CODE.put("江苏", "J2_JS");
		HM_PROVINCE_CODE.put("江西", "J3_JX");
		HM_PROVINCE_CODE.put("辽宁", "L1_LN");
		HM_PROVINCE_CODE.put("内蒙古", "N1_NM");
		HM_PROVINCE_CODE.put("宁夏", "N2_NX");
		HM_PROVINCE_CODE.put("青海", "Q1_QH");
		HM_PROVINCE_CODE.put("山东", "S1_SD");
		HM_PROVINCE_CODE.put("上海", "S4_SH");
		HM_PROVINCE_CODE.put("四川", "S5_SC");
		HM_PROVINCE_CODE.put("云南", "Y1_YN");
		HM_PROVINCE_CODE.put("天津", "T1_TJ");
		HM_PROVINCE_CODE.put("贵州", "G4_GZ");
		HM_PROVINCE_CODE.put("湖北", "H5_HB");
		HM_PROVINCE_CODE.put("吉林", "J1_JL");
		HM_PROVINCE_CODE.put("山西", "S2_SX");
		HM_PROVINCE_CODE.put("新疆", "X1_XJ");
		HM_PROVINCE_CODE.put("浙江", "Z1_ZJ");
		HM_PROVINCE_CODE.put("河北", "H2_HB");
		HM_PROVINCE_CODE.put("吉林", "J1_JL");
		HM_PROVINCE_CODE.put("辽宁", "L1_LN");
		HM_PROVINCE_CODE.put("内蒙古", "N1_NM");
		HM_PROVINCE_CODE.put("山东", "S1_SD");
		HM_PROVINCE_CODE.put("上海", "S4_SH");
		HM_PROVINCE_CODE.put("浙江", "Z1_ZJ");
		HM_PROVINCE_CODE.put("重庆", "C1_CQ");
		HM_PROVINCE_CODE.put("陕西", "S3_SX");

		HM_LMGX.put("C9联盟", "北京大学,清华大学,哈尔滨工业大学,复旦大学,上海交通大学,南京大学,浙江大学,中国科学技术大学,西安交通大学");
		HM_LMGX.put("长三角医学教育联盟", "复旦大学,上海交通大学,上海中医药大学,南京医科大学,苏州大学,南京中医药大学,浙江大学,温州医科大学,中国科学技术大学,安徽医科大学");
		HM_LMGX.put("北京卓越医学人才联盟", "首都医科大学,北京协和医学院,北京大学医学部,北京中医药大学,天津医科大学,河北医科大学");
		HM_LMGX.put("西部中医药高校联盟",
				"成都中医药大学,陕西中医药大学,云南中医药大学,广西中医药大学,贵州中医药大学,甘肃中医药大学,西藏藏医药大学,内蒙古医科大学,青海大学,宁夏医科大学,重庆医科大学,新疆医科大学");
		HM_LMGX.put("北京高科联盟",
				"北京邮电大学,西安电子科技大学,北京交通大学,北京科技大学,北京化工大学,北京林业大学,华北电力大学,哈尔滨工程大学,中国地质大学（北京）,中国矿业大学（北京）,中国石油大学（北京）,燕山大学,大连海事大学");
		HM_LMGX.put("行业优质高校联盟",
				"中国地质大学,华东理工大学,中国矿业大学,中国石油大学（华东）,东华大学,河海大学,江南大学,南京农业大学,东北林业大学,合肥工业大学,西南交通大学,西安电子科技大学,长安大学");
		HM_LMGX.put("E9联盟", "北京理工大学,重庆大学,大连理工大学,东南大学,哈尔滨工业大学,华南理工大学,天津大学,同济大学,西北工业大学");
		HM_LMGX.put("Z14联盟", "河北大学,山西大学,内蒙古大学,南昌大学,郑州大学,广西大学,海南大学,贵州大学,云南大学,西藏大学,青海大学,宁夏大学,新疆大学,石河子大学");
		HM_LMGX.put("G7联盟", "北京航空航天大学,北京理工大学,哈尔滨工业大学,哈尔滨工程大学,南京航空航天大学,南京理工大学,西北工业大学");
		HM_LMGX.put("武汉七校联盟", "武汉大学,华中科技大学,武汉理工大学,中南财经政法大学,华中师范大学,中国地质大学,华中农业大学");
		HM_LMGX.put("长安联盟", "陕西师范大学,西北大学,西安外国语大学,西北政法大学,西安邮电大学");
		HM_LMGX.put("延河联盟", "中国人民大学,北京理工大学,中国农业大学,北京外国语大学,中央音乐学院,中央美术学院,中央戏剧学院,中央民族大学,延安大学");
		HM_LMGX.put("华东五校联盟", "复旦大学,浙江大学,上海交通大学,南京大学,中国科技大学");
		HM_LMGX.put("行星科学联盟",
				"中国科学院大学,北京大学,中国科学技术大学,中山大学,澳门科技大学,北京航空航天大学,哈尔滨工业大学（深圳）,南京大学,中国地质大学（武汉）,山东大学,桂林理工大学,南京信息工程大学,南方科技大学,武汉大学,清华大学,北京师范大学,中国地质大学（北京）,同济大学,洛阳师范学院,中南民族大学,南京航空航天大学,华北电力大学,南昌大学,香港大学,重庆大学,吉林大学,浙江大学");
		HM_LMGX.put("长三角特色大学联盟",
				"中国计量大学,安徽工业大学,安徽理工大学,杭州电子科技大学,江苏海洋大学,江苏科技大学,苏州科技大学,南京审计大学,南京信息工程大学,上海电力大学,上海海关学院,浙江海洋大学,浙江理工大学");
		HM_LMGX.put("长三角高校合作联盟", "复旦大学,上海交通大学,华东师范大学,同济大学,南京大学,东南大学,浙江大学,中国科学技术大学");
		HM_LMGX.put("地方C9联盟", "山西大学,云南大学,内蒙古大学,辽宁大学,西北大学,苏州大学,郑州大学,南昌大学,新疆大学");
		HM_LMGX.put("地方高水平大学联盟", "苏州大学,上海大学,郑州大学,南昌大学");
		HM_LMGX.put("重庆市大学联盟", "重庆大学,西南大学,陆军军医大学,西南政法大学,重庆医科大学,四川外国语大学");

		HM_LHPY.put("中山大学", "香港大学");
		HM_LHPY.put("电子科技大学", "西南财经大学");
		HM_LHPY.put("西安电子科技大学", "西北工业大学,西北大学");
		HM_LHPY.put("西南交通大学", "中科院高能物理研究所");
		HM_LHPY.put("西北大学", "西北工业大学,西安电子科技大学");
		HM_LHPY.put("云南大学", "复旦大学,中国科学院");
		HM_LHPY.put("安徽大学", "中科院动物研究所");
		HM_LHPY.put("南京信息工程大学", "中国科学院大学");
		HM_LHPY.put("华南农业大学", "澳门科技大学");
		HM_LHPY.put("广西大学", "华南理工大学");
		HM_LHPY.put("新疆大学", "西安交通大学");
		HM_LHPY.put("西南科技大学", "中国工程物理研究院");
		HM_LHPY.put("青岛农业大学", "中国海洋大学");
		HM_LHPY.put("河南工业大学", "台湾中原大学");
		HM_LHPY.put("西藏大学", "西南交通大学");
		HM_LHPY.put("贵州师范大学", "厦门大学");
		HM_LHPY.put("新疆师范大学", "华东师范,河北师范,西北师范,浙江师范,大连理工大学");
		HM_LHPY.put("吉首大学", "中山大学");
		HM_LHPY.put("长江师范学院", "山东科技大学");
		HM_LHPY.put("闽南师范大学", "台湾高校群");
		HM_LHPY.put("福建工程学院", "台湾高校群");
		HM_LHPY.put("井冈山大学", "同济大学,厦门大学");
		HM_LHPY.put("泉州师范学院", "台湾辅仁大学,台湾龙华科技大学");
		HM_LHPY.put("黑龙江工程学院", "中兴通讯股份有限公司");
		HM_LHPY.put("乐山师范学院", "武汉大学");
		HM_LHPY.put("重庆三峡学院", "东南大学");
		HM_LHPY.put("天水师范学院", "西南交通大学");
		HM_LHPY.put("河西学院", "复旦大学");
		HM_LHPY.put("六盘水师范学院", "上海工程技术大学");
		HM_LHPY.put("兰州文理学院", "中国传媒大学");
		HM_LHPY.put("贵州工程应用技术学院", "西南大学,中国矿业大学");
		HM_LHPY.put("新疆工程学院", "北京科技大学,北京邮电大学,中国矿业大学");
		HM_LHPY.put("新疆理工学院", "浙江工业大学");
		HM_LHPY.put("天津医科大学", "天津大学");
		HM_LHPY.put("北方民族大学", "合肥工业大学");
		HM_LHPY.put("北京外国语大学", "中国政法大学");
		HM_LHPY.put("宁夏理工学院", "东北大学,浙江工业大学,陕西师范大学");
		HM_LHPY.put("遵义医科大学", "广州医科大学");
		HM_LHPY.put("重庆医科大学", "西南大学,复旦大学");
		HM_LHPY.put("中国政法大学", "北京外国语大学");
		HM_LHPY.put("西南财经大学", "电子科技大学");
		HM_LHPY.put("浙江警察学院", "华东政法大学");
		HM_LHPY.put("呼和浩特民族学院", "中央民族大学");
		HM_LHPY.put("电子科技大学中山学院", "电子科技大学");
		HM_LHPY.put("中国消防救援学院", "中国民用航空飞行学院");
		HM_LHPY.put("中国石油大学（北京）克拉玛依校区", "电子科技大学");
		HM_LHPY.put("兰州财经大学", "中央财经大学,对外经济贸易大学");
		HM_LHPY.put("新疆科技学院", "河北科技大学");
		HM_LHPY.put("山东工艺美术学院", "山东大学");
		HM_LHPY.put("贵州医科大学", "北京协和医学院");
		HM_LHPY.put("银川能源学院", "福州大学");
		HM_LHPY.put("上海中医药大学", "上海交通大学,华东师范大学");

		YX_LHPY = Tools.convertSetToHashSet(HM_LHPY.keySet());

		HM_HYHP.put("船舶行业", "上海交通大学");
		HM_HYHP.put("航空", "西北工业大学");
		HM_HYHP.put("装备制造行业", "上海理工大学");
		HM_HYHP.put("粮食行业", "河南工业大学");
		HM_HYHP.put("高电压行业", "西安交通大学");
		HM_HYHP.put("核潜艇行业", "哈尔滨工程大学");
		HM_HYHP.put("轻工食品行业", "江南大学");
		HM_HYHP.put("外语外贸行业", "北京外国语大学,外交学院,上海外国语大学");
		HM_HYHP.put("矿产行业", "中国矿业大学");
		HM_HYHP.put("电线电缆行业", "哈尔滨理工大学");
		HM_HYHP.put("橡胶行业", "青岛科技大学");
		HM_HYHP.put("煤炭行业", "辽宁工程技术大学");
		HM_HYHP.put("气象行业", "南京信息工程大学");
		HM_HYHP.put("核工业", "南华大学");
		HM_HYHP.put("轨道交通行业", "西南交通大学");
		HM_HYHP.put("轴承行业", "河南科技大学");
		HM_HYHP.put("质检行业", "中国计量大学");
		HM_HYHP.put("钢铁行业", "北京科技大学,东北大学");
		HM_HYHP.put("税务行业", "吉林财经大学");
		HM_HYHP.put("民航管理干部", "中国民用航空飞行学院");
		HM_HYHP.put("银行行业", "中央财经大学");
		HM_HYHP.put("有色金属", "中南大学");
		HM_HYHP.put("通信行业", "北京邮电大学,西安电子科技大学,电子科技大学,桂林电子科技大学,杭州电子科技大学");
		HM_HYHP.put("化工行业", "天津大学");
		HM_HYHP.put("审计行业", "南京审计大学");
		HM_HYHP.put("公路运输行业", "长安大学");
		HM_HYHP.put("外交外事", "外交学院");
		HM_HYHP.put("地震系统", "防灾科技学院");
		HM_HYHP.put("烹饪行业", "扬州大学，四川旅游学院，济南大学");
		HM_HYHP.put("海事行业", "大连海事大学");
		HM_HYHP.put("生物医学", "东南大学");
		HM_HYHP.put("建筑设计行业", "东南大学");
		HM_HYHP.put("计算机行业", "国防科技大学");
		HM_HYHP.put("纺织行业", "东华大学");
		HM_HYHP.put("烟草行业", "河南农业大学");
		HM_HYHP.put("法律行业", "西南政法大学");
		HM_HYHP.put("高铁行业", "西南交通大学");
		HM_HYHP.put("铁路建设行业", "石家庄铁道大学");
		HM_HYHP.put("葡萄酒行业", "西北农林科技大学");
		HM_HYHP.put("交通运输", "东南大学");
		HM_HYHP.put("铁路行业", "北京交通大学");
		HM_HYHP.put("土木行业", "同济大学");
		HM_HYHP.put("制药行业", "中国药科大学");
		HM_HYHP.put("电力行业", "华北电力大学,东北电力大学,长沙理工大学,三峡大学,南京工程学院,沈阳工程学院");
		HM_HYHP.put("遥感测绘", "武汉大学");
		HM_HYHP.put("石油行业", "东北石油大学");
		HM_HYHP.put("心理咨询", "北京师范大学,华中师范大学,西南大学");
		HM_HYHP.put("民航行业", "中国民航大学");
		HM_HYHP.put("包装行业", "湖南工业大学");
		HM_HYHP.put("陶瓷行业", "景德镇陶瓷大学");
		HM_HYHP.put("新能源汽车", "北京理工大学");
		HM_HYHP.put("机器人和航天", "哈尔滨工业大学");
		HM_HYHP.put("风景园林", "北京林业大学");
		HM_HYHP.put("汽车行业", "合肥工业大学");
		HM_HYHP.put("新闻传播", "中国传媒大学");
		HM_HYHP.put("对外汉语行业", "北京外国语大学");
		HM_HYHP.put("车辆行业", "吉林大学");
		HM_HYHP.put("制造业", "上海理工大学");
		HM_HYHP.put("水利行业", "河海大学");
		HM_HYHP.put("茶行业", "安徽农业大学");
		HM_HYHP.put("金融行业", "西南财经大学,对外经济贸易大学,中央财经大学");
		HM_HYHP.put("建材建工", "武汉理工大学");
		HM_HYHP.put("兵器行业", "北京理工大学");

		HM_YBWS.put("A安监:原安监局", "华北科技学院");
		HM_YBWS.put("B包装:原包装公司", "湖南工业大学");
		HM_YBWS.put("C材料:原冶金部",
				"北京科技大学,东北大学,西安建筑科技大学,武汉科技大学,辽宁科技大学,安徽工业大学,内蒙古科技大学,青岛理工大学,辽宁科技学院,长春工业大学,沈阳大学,重庆科技学院,黑龙江工程学院");
		HM_YBWS.put("C材料:原有色金属", "中南大学,昆明理工大学,北方工业大学,长春师范大学,江西理工大学,桂林理工大学,北华大学,长春工程学院,嘉兴学院");
		HM_YBWS.put("C财经:原财政部", "上海财经大学,中央财经大学,中南财经政法大学,东北财经大学,江西财经大学,山东财经大学");
		HM_YBWS.put("C财经:原供销社", "山西财经大学,安徽财经大学");
		HM_YBWS.put("C财经:原人民银行",
				"西南财经大学,对外经济贸易大学,湖南大学,西安交通大学,河北金融学院,长春金融高等专科学校,哈尔滨金融学院,上海立信会计金融学院,南京审计大学,湖北经济学院,广东金融学院");
		HM_YBWS.put("C财经:原商业部", "浙江工商大学,哈尔滨商业大学,北京工商大学,天津商业大学,河南工业大学,武汉轻工大学,北京物资学院,南京财经大学,重庆工商大学,兰州财经大学,四川旅游学院");
		HM_YBWS.put("C财经:原统计局", "西安财经大学");
		HM_YBWS.put("C财税:原税务局", "吉林财经大学");
		HM_YBWS.put("C财政:原审计署", "南京审计大学");
		HM_YBWS.put("C测绘:原测绘局", "武汉大学");
		HM_YBWS.put("C船舶:原船舶集团", "哈尔滨工程大学,江苏科技大学,武汉船舶职业技术学院");
		HM_YBWS.put("D地质:原地震局", "防灾科技学院");
		HM_YBWS.put("D地质:原地质部", "中国地质大学,吉林大学,成都理工大学,长安大学,河北地质大学");
		HM_YBWS.put("D电力:原电力部",
				"华北电力大学,武汉大学,北京交通大学,东北电力大学,三峡大学,长沙理工大学,上海电力学院,山西工程学院,沈阳工程学院,长春工程学院,南京工程学院,山东电力高等专科学校,郑州电力高等专科学校,西安电力高等专科学校,重庆电力高等专科学校");
		HM_YBWS.put("D电信:原邮电部", "北京邮电大学,南京邮电大学,吉林大学,重庆邮电大学,西安邮电学院,石家庄邮电职业技术学院");
		HM_YBWS.put("D电子:原电子部", "电子科技大学,西安电子科技大学,杭州电子科技大学,桂林电子科技大学,北京信息科技大学");
		HM_YBWS.put("F法学:原司法部", "中央司法警官学院,中国政法大学,中南财经政法大学,西南政法大学,华东政法大学,西北政法大学");
		HM_YBWS.put("F纺织:原纺织部", "东华大学,天津工业大学,苏州大学,西安工程大学,浙江理工大学,北京服装学院,中原工学院,武汉纺织大学,南通大学,苏州大学");
		HM_YBWS.put("H航天:原航天部",
				"哈尔滨工业大学,北京航空航天大学,西北工业大学,南京航空航天大学,南昌航空大学,沈阳航空航天大学,郑州航空工业管理学院,华北航天工业学院,成都航空职业技术学院,桂林航空工业学院,西安航空学院");
		HM_YBWS.put("H核能:原核工部", "南华大学,东华理工大学,苏州大学,内蒙古工业大学,杭州电子科技大学");
		HM_YBWS.put("H化工:原化工部", "北京化工大学,南京工业大学,郑州大学,青岛科技大学,沈阳化工大学,武汉工程大学,吉林化工学院,南京师范大学,江苏海洋大学");
		HM_YBWS.put("J机械:原机械部",
				"湖南大学,合肥工业大学,吉林大学,武汉理工大学,江苏大学,燕山大学,西安理工大学,上海理工大学,沈阳工业大学,哈尔滨理工大学,兰州理工大学,河南科技大学,太原科技大学,北京信息科技大学,湖北汽车工业学院,沈阳理工大学,湖南工程学院,河南工业大学,南京工程学院,长春汽车工业高等专科学校");
		HM_YBWS.put("J建材:原建材局", "武汉理工大学,同济大学,西南科技大学,济南大学,洛阳理工学院");
		HM_YBWS.put("J建筑:原建设部", "重庆大学,长安大学,华中科技大学,哈尔滨工业大学,沈阳建筑大学,南京工业大学,苏州科技大学");
		HM_YBWS.put("J交通:原交通部", "大连海事大学,长安大学,武汉理工大学,东南大学,上海海事大学,长沙理工大学,南通大学,重庆交通大学,山东交通学院,广州航海学院");
		HM_YBWS.put("J交通:原铁道部", "西南交通大学,北京交通大学,中南大学,同济大学,东南大学,大连交通大学,兰州交通大学,华东交通大学,石家庄铁道大学,苏州科技大学");
		HM_YBWS.put("J经贸:原外贸部", "对外经济贸易大学,南开大学,上海对外经贸大学,广东外语外贸大学");
		HM_YBWS.put("J军工:原兵工部", "北京理工大学,南京理工大学,长春理工大学,中北大学,西安工业大学,沈阳理工大学,重庆理工大学,包头职业技术学院");
		HM_YBWS.put("L林业:原林业部", "北京林业大学,东北林业大学,西北农林科技大学,南京森林警察学院,南京林业大学,中南林业科技大学,西南林业大学");
		HM_YBWS.put("L旅游:原旅游局", "北京第二外国语学院,上海旅游高等专科学校");
		HM_YBWS.put("L农业:原农业部",
				"中国农业大学,南京农业大学,西北农林科技大学,华中农业大学,西南大学,沈阳农业大学,华南农业大学,东北农业大学,海南大学,上海海洋大学,石河子大学,大连海洋大学,塔里木大学");
		HM_YBWS.put("L烟草:原烟草公司", "中国科学技术大学");
		HM_YBWS.put("M媒体:原出版总署", "北京印刷学院,上海出版印刷高等专科学校");
		HM_YBWS.put("M煤炭:原煤炭部",
				"中国矿业大学,辽宁工程技术大学,山东科技大学,西安科技大学,河南理工大学,太原理工大学,安徽理工大学,华北理工大学,河北工程大学,山东工商学院,湖南科技大学,黑龙江科技大学,淮北师范大学,北京工业职业技术学院,哈尔滨医科大学");
		HM_YBWS.put("Q气象:原气象局", "南京信息工程大学,成都信息工程大学");
		HM_YBWS.put("Q轻工:原轻工部", "清华大学,江南大学,天津科技大学,陕西科技大学,北京工商大学,景德镇陶瓷大学,郑州轻工业大学,大连工业大学");
		HM_YBWS.put("S石油:原石油部", "中国石油大学,东北石油大学,西安石油大学,长江大学,辽宁石油化工大学,常州大学,北京石油化工学院,承德石油高等专科学校,广东石油化工学院,山东石油化工学院,西南石油大学");
		HM_YBWS.put("S水利:原水利部", "河海大学,华北水利水电大学,南昌工程学院,黄河水利职业技术学院");
		HM_YBWS.put("T体育:原体委", "北京体育大学,上海体育学院,成都体育学院,武汉体育学院,广州体育学院,沈阳体育学院,西安体育学院");
		HM_YBWS.put("Y药学:原药监局", "中国药科大学,沈阳药科大学");
		HM_YBWS.put("Y医学:原卫生部", "北京大学,复旦大学,中山大学,四川大学,华中科技大学,中南大学,吉林大学,山东大学,西安交通大学,北京协和医学院,中国医科大学");
		HM_YBWS.put("Y医学:原中医药", "北京中医药大学,广州中医药大学");
		HM_YBWS.put("Y艺术:原文化部", "中央音乐学院,中央美术学院,中央戏剧学院,中国美术学院,上海音乐学院,上海戏剧学院,北京电影学院,中国音乐学院,中国戏曲学院,北京舞蹈学院");
		HM_YBWS.put("Z政法:原劳动部", "中国人民大学,天津职业技术师范大学");
		HM_YBWS.put("Z政法:原民政部", "长沙民政职业技术学院");
		HM_YBWS.put("Z质检:原质检局", "中国计量大学");

		HM_HYMX.put("A安全:应急部", "中国消防救援学院,华北科技学院");
		HM_HYMX.put("C财经:两财一贸", "中央财经大学,对外经济贸易大学,上海财经大学");
		HM_HYMX.put("C财经:金融四校", "西南财经大学,西安交通大学,对外经济贸易大学,湖南大学");
		HM_HYMX.put("C财经:老八校", "中央财经大学,对外经济贸易大学,上海财经大学,中南财经政法大学,东北财经大学,江西财经大学,山东财经大学,西南财经大学");
		HM_HYMX.put("C财经:六星财大", "中央财经大学,上海财经大学,对外经济贸易大学,西南财经大学,中南财经政法大学,东北财经大学");
		HM_HYMX.put("C财经:五朵金花", "中央财经大学,对外经济贸易大学,上海财经大学,中南财经政法大学,西南财经大学");
		HM_HYMX.put("C财经:国贸三星", "对外经济贸易大学,上海对外经贸大学,广东外语外贸大学");
		HM_HYMX.put("D电气:二龙四虎", "华北电力大学,武汉大学,清华大学,西安交通大学,浙江大学,华中科技大学");
		HM_HYMX.put("D电信:电信六子", "北京邮电大学,南京邮电大学,西安邮电大学,重庆邮电大学,长春邮电大学,石家庄邮电职业技术学院");
		HM_HYMX.put("D电信:两电一邮", "电子科技大学,西安电子科技大学,北京邮电大学");
		HM_HYMX.put("D电信:四电四邮", "电子科技大学,西安电子科技大学,杭州电子科技大学,桂林电子科技大学,京邮电大学,南京邮电大学,重庆邮电大学,西安邮电大学");
		HM_HYMX.put("D电子:五朵金花", "电子科技大学,西安电子科技大学,杭州电子科技大学,桂林电子科技大学,北京信息科技大学");
		HM_HYMX.put("F法学:五院四系", "北京大学,中国人民大学,武汉大学,吉林大学,中国政法大学,中南财经政法大学,华东政法大学,西南政法大学,西北政法大学");
		HM_HYMX.put("G工科:八大工院", "北京理工大学,大连理工大学,东南大学,四川大学,东北大学,华中理工大学,华中科技大学,华南理工大学,西北工业大学");
		HM_HYMX.put("G工科:工科七子", "清华大学,浙江大学,哈尔滨工业大学,上海交通大学,华中科技大学,天津大学,西安交通大学");
		HM_HYMX.put("H海关:海关总署", "上海海关学院");
		HM_HYMX.put("H航天:国防七子", "北京航空航天大学,北京理工大学,哈尔滨工业大学,西北工业大学,哈尔滨工程大学,南京航空航天大学,南京理工大学");
		HM_HYMX.put("H航天:工息部",
				"哈尔滨工业大学,北京航空航天大学,西北工业大学,南京航空航天大学,南昌航空大学,沈阳航空航天大学,郑州航空工业管理学院,华北航天工业学院,成都航空职业技术学院,桂林航空工业学院,西安航空学院");
		HM_HYMX.put("J建筑:三驾马车", "清华大学,同济大学,湖南大学");
		HM_HYMX.put("J建筑:老八校", "清华大学,东南大学,天津大学,同济大学,哈尔滨工业大学,华南理工大学,重庆大学,西安建筑科技大学");
		HM_HYMX.put("J建筑:新八校", "浙江大学,湖南大学,沈阳建筑大学,大连理工大学,深圳大学,华中科技大学,上海交通大学,南京大学");
		HM_HYMX.put("J建筑:新四军", "南京大学,浙江大学,湖南大学,沈阳建筑大学");
		HM_HYMX.put("J建筑:总公司", "重庆大学");
		HM_HYMX.put("J军工:军工六校", "国防科技大学,哈尔滨工程大学,南京理工大学,陆军工程大学,陆军装甲兵学院,陆军防化学院");
		HM_HYMX.put("J军工:总装备部", "长沙航空职业技术学院");
		HM_HYMX.put("J机械:五虎", "清华大学,上海交通大学,华中科技大学,西安交通大学,哈尔滨工业大学");
		HM_HYMX.put("J机械:四小龙", "吉林大学,湖南大学,燕山大学,合肥工业大学");
		HM_HYMX.put("K科研:中科院", "中国科学技术大学,中国科学院,大学");
		HM_HYMX.put("M民航:民航局", "中国民航大学,中国民用航空飞行学院,上海民航职业技术学院,广州民航职业技术学院");
		HM_HYMX.put("M民族:国家民委", "中央民族大学,大连民族大学,中南民族大学,西南民族大学,西北民族大学,北方民族大学");
		HM_HYMX.put("Q侨办:侨办", "暨南大学,华侨大学");
		HM_HYMX.put("S师范:三南一首", "湖南师范大学,南京师范大学,华南师范大学,首都师范大学");
		HM_HYMX.put("S师范:六姐妹", "北京师范大学,华东师范大学,华中师范大学,东北师范大学,陕西师范大学,西南大学");
		HM_HYMX.put("T统计:四大天王", "中国人民大学,厦门大学,天津财经大学,浙江工商大学");
		HM_HYMX.put("W外交:外交部", "外交学院");
		HM_HYMX.put("W外语:九大外院", "北京外国语大学,上海外国语大学,北京语言大学,北京第二外国语学院,广东外语外贸大学,四川外国语大学,西安外国语大学,天津外国语大学,大连外国语大学");
		HM_HYMX.put("W文理:文理八校", "北京大学,复旦大学,南京大学,武汉大学,中山大学,南开大学,北京师范大学,厦门大学");
		HM_HYMX.put("X新闻:南北新闻", "复旦大学,中国人民大学");
		HM_HYMX.put("Y药学:南药北药", "中国药科大学,沈阳药科大学");
		HM_HYMX.put("Y医学:四大军医", "南方医科大学,海军军医大学,陆军军医大学,空军军医大学");
		HM_HYMX.put("Y医学:霸主", "北京协和医学院");
		HM_HYMX.put("Y医学:三雄", "北京大学,上海交通大学,复旦大学,上");
		HM_HYMX.put("Y医学:十大金刚", "浙江大学,首都医科大学,天津医科大学,南京医科大学,山东大学,哈尔滨医科大学,西安交通大学,吉林大学,中国医科大学,重庆医科大学");
		HM_HYMX.put("Y医学:四精英", "华中科技大学,中山大学,四川大学,中南大学");
		HM_HYMX.put("Y艺术:八大美院", "中央美术学院,中国美术学院,西安美术学院,四川美术学院,鲁迅美术学院,广州美术学院,湖北美术学院,天津美术学院");
		HM_HYMX.put("Z政法:办公厅", "北京电子科技学院");
		HM_HYMX.put("Z政法:公安部", "中国人民公安大学,中国刑事警察学院,中国人民警察大学,铁道警察学院");
		HM_HYMX.put("Z政法:全国妇联", "中华女子学院");
		HM_HYMX.put("Z政法:团中央", "中国青年政治学院");
		HM_HYMX.put("Z政法:总工会", "中国劳动关系学院");
		HM_HYMX.put("R软件:特色示范",
				"北京大学,清华大学,北京交通大学,北京航空航天大学,北京理工大学,北京邮电大学,天津大学,大连理工大学,东北大学,吉林大学,哈尔滨工业大学,哈尔滨工程大学,复旦大学,同济大学,上海交通大学,华东师范大学,南京大学,苏州大学,南京航空航天大学,浙江大学,中国科学技术大学,厦门大学,山东大学,中国石油大学,武汉大学,湖南大学,中南大学,电子科技大学,重庆大学,西安交通大学,西北工业大学,西安电子科技大学,国防科技大学");
		HM_HYMX.put("G高薪:烟草行业", "郑州轻工业大学,河南农业大学,山东农业大学,湖南农业大学,贵州大学,四川农业大学,云南农业大学,西昌学院,青岛农业大学,安徽农业大学");
		HM_HYMX.put("G高薪:白酒行业",
				"四川轻化工大学,茅台学院,江南大学,贵州大学,湖北工业大学,陕西科技大学,北京工商大学,齐鲁工业大学,吉林农业大学,武汉轻工大学,北京农学院,绍兴文理学院,河北科技师范学院,贵州理工学院,河南牧业经济学院,四川旅游学院,四川工业科技学院,毫州学院,四川大学,锦江学院,湖北大学,知行学院");
		HM_HYMX.put("G高薪:化妆品行业", "上海应用技术大学,北京工商大学,广东工业大学,齐鲁工业大学,郑州轻工业大学,湖南理工学院,江南大学,华东理工大学,暨南大学");
		HM_HYMX.put("G高薪:殡葬行业", "长沙民政职业技术学院,武汉民政职业学院,北京社会管理职业学院,重庆城市管理职业学院,安徽城市职业管理学院");
		HM_HYMX.put("J交通:铁路行业",
				"苏州科技学院,同济大学,东南大学,兰州交通大学,中南大学,大连交通大学,华东交通大学,西南交通大学,北京交通大学,郑州铁路职业技术学院,西安铁路职业技术学院,南京铁道职业技术学院,陕西铁路职业技术学院,武汉铁路职业技术学院,山东职业学院,武汉铁路桥梁职业技术学院,湖南铁道职业技术学院,哈尔滨铁道职业技术学院,湖南高速铁路职业技术学院,黑龙江交通职业技术学院,湖南铁道科技职业技术学院,吉林铁道职业技术学院,广州铁路职业技术学院,辽宁铁道职业技术学院,柳州铁道职业技术学院,辽宁轨道交通职业学院,安徽交通职业技术学院,包头铁道职业技术学院,昆明铁道职业技术学院,天津铁道职业技术学院,四川铁道职业技术学院,石家庄铁路职业技术学院,湖北铁道运输职业学院,新疆铁道职业技术学院,山西铁道职业技术学院,河北轨道交通职业学院");

		YX_SCHOOL = getAllSchoolsAsMap();
		HM_PROVINCE_CITY = getAllCitiesAsMap();
		HM_PROVINCE_CITY_ZDPC = getAllCitiesZDPCAsMap();
		LIST_CHINA_PROVINCE_NAME = getAllSFList();
		;
	}

	public static void main(String[] args) {
		Iterator<String> itKey = JDBC.HM_LHPY.keySet().iterator();
		int CNT = 0;
		while(itKey.hasNext()) {
			String key = itKey.next();
			String value = JDBC.HM_LHPY.get(key);
			String[] valArray = value.split(",");
			for(String x : valArray) {
				CNT++;
			}
			//Tools.println(key+"->>>"+JDBC.HM_HYMX.get(key));
		}
		Tools.println(CNT+"");
	}

	public List<SchoolMarjorCommonBean> getSchoolMajorListByScoreScope(String province, String xk, int scoreFrom,
			int scoreTo) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolMarjorCommonBean> list = new ArrayList<>();
		String pName = HM_PROVINCE_CODE_NAME.get(province);
		String xkQuery = "";
		if ("浙江".indexOf(pName) != -1 || "山东".indexOf(pName) != -1) {
			xkQuery = String.valueOf(xkQuery) + " or x.xk like '%%'";
		} else {
			Iterator<String> studentXKList = Tools.convertXK(xk).iterator();
			while (studentXKList.hasNext()) {
				String value = studentXKList.next();
				xkQuery = String.valueOf(xkQuery) + " or x.xk like '%" + value + "%'";
			}
		}
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.yxmc, x.yxbz, x.zdf22,x.zdfwc22,x.zdf21,x.zdfwc21,x.zdf20,x.zdfwc20, y.sf  FROM "
					+ (String) HM_PROVINCE.get(province)
					+ " x LEFT JOIN base_university y ON (CASE WHEN INSTR(x.yxmc,'（')> 0 THEN LEFT(x.yxmc, INSTR(x.yxmc,'（')- 1) WHEN INSTR(x.yxmc,'(') > 0 THEN LEFT(x.yxmc, INSTR(x.yxmc,'(') - 1) ELSE x.yxmc END) = y.yxmc WHERE (x.xk is NULL or x.xk = '不限' "
					+ xkQuery + ") and x.zdf22 between ? and ? order by x.zdf22 desc";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, scoreFrom);
			ps.setInt(2, scoreTo);
			rs = ps.executeQuery();
			SchoolMarjorCommonBean bean = null;
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZdf(rs.getString("zdf22"));
				bean.setZdfwc(rs.getString("zdfwc22"));
				bean.setZdf22(rs.getString("zdf22"));
				bean.setZdfwc22(rs.getString("zdfwc22"));
				bean.setZdf21(rs.getString("zdf21"));
				bean.setZdfwc21(rs.getString("zdfwc21"));
				bean.setZdf20(rs.getString("zdf20"));
				bean.setZdfwc20(rs.getString("zdfwc20"));
				bean.setSf(rs.getString("sf"));
				bean.setYxbz(rs.getString("yxbz"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	private static void closeAllConnection(Connection conn, PreparedStatement ps, ResultSet rs) {
		/**
		try {
			if (rs != null)
				rs.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (ps != null)
				ps.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (conn != null)
				conn.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		rs = null;
		ps = null;
		conn = null;
		*/
		DatabaseUtils.closeAllResources(rs, ps, conn);
	}

	public List<SchoolMarjorCommonBean> pickSchoolBySchoolName(String province, String school, String xk) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolMarjorCommonBean> list = new ArrayList<>();
		String latest_year = HM_PROVINCE_LATEST_YEAR.containsKey(province) ? HM_PROVINCE_LATEST_YEAR.get(province)
				: "_2021";
		String pName = HM_PROVINCE_CODE_NAME.get(province);
		String xkQuery = "";
		Iterator<String> studentXKList = Tools.convertXK(xk).iterator();
		while (studentXKList.hasNext()) {
			String value = studentXKList.next();
			xkQuery = String.valueOf(xkQuery) + " or x.xk like '%" + value + "%'";
		}
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT *  FROM " + (String) HM_PROVINCE.get(province) + latest_year
					+ " x WHERE (yxmc = ? or yxmc like ?)  and (xk is NULL or xk = '不限' " + xkQuery
					+ ") order by zdf DESC";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, school);
			ps.setString(2, String.valueOf(school) + "(%");
			rs = ps.executeQuery();
			SchoolMarjorCommonBean bean = null;
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZdf(rs.getString("zdf"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setPc(rs.getString("pc"));
				bean.setXk(rs.getString("xk"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public List<SchoolMarjorCommonBean> pickSchoolBySchoolNameWithGivingYear(String province, String school, String xk,
			String givingYear) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolMarjorCommonBean> list = new ArrayList<>();
		String pName = HM_PROVINCE_CODE_NAME.get(province);
		String xkQuery = "";
		if ("浙江".indexOf(pName) != -1 || "山东".indexOf(pName) != -1) {
			xkQuery = String.valueOf(xkQuery) + " or x.xk like '%%'";
		} else {
			Iterator<String> studentXKList = Tools.convertXK(xk).iterator();
			while (studentXKList.hasNext()) {
				String value = studentXKList.next();
				xkQuery = String.valueOf(xkQuery) + " or x.xk like '%" + value + "%'";
			}
		}
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT *  FROM " + (String) HM_PROVINCE.get(province) + "_" + givingYear
					+ " x WHERE yxmc = ?  and (xk is NULL or xk = '不限' " + xkQuery + ") order by zdf desc";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, school);
			rs = ps.executeQuery();
			SchoolMarjorCommonBean bean = null;
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZdf(rs.getString("zdf"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setPc(rs.getString("pc"));
				bean.setXk(rs.getString("xk"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public List<SchoolMarjorCommonBean> pickMajorBySchoolName(String province, String school, String xk,
			String givingYear) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolMarjorCommonBean> list = new ArrayList<>();
		String pName = HM_PROVINCE_CODE_NAME.get(province);
		String xkQuery = "";
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT *  FROM " + (String) HM_PROVINCE.get(province) + "_" + givingYear
					+ " x WHERE yxmc_org = ?  and xk_code like '%"+xk+"%' order by yxmc, zdf desc";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, school);
			rs = ps.executeQuery();
			SchoolMarjorCommonBean bean = null;
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZdf(rs.getString("zdf"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setPc(rs.getString("pc"));
				bean.setXk(rs.getString("xk"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public String getCookie() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_yx_temp_cookie";
			
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			
			rs = ps.executeQuery();
			SchoolMarjorCommonBean bean = null;
			while (rs.next()) {
				return rs.getString("t_cookie");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	
	public List<SchoolMarjorCommonBean> pickMajorBySchoolNameAndYearSpan(String province, String school, String xk, int givingYearFrom, int givingYearTo) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolMarjorCommonBean> list = new ArrayList<>();
		String pName = HM_PROVINCE_CODE_NAME.get(province);
		String xkQuery = "";
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT yxmc,nf,zymc,zdf,zdfwc,pc,xk FROM (";
			String selectedColumn = "yxmc,nf,zymc,pc,xk,zdf,zdfwc";
			for(int i = givingYearFrom; i <= givingYearTo; i++) {
				SQL += " SELECT "+selectedColumn+" FROM " + (String) HM_PROVINCE.get(province) + "_" + i + " x WHERE yxmc_org = ? and xk_code like '%"+xk+"%'";
				if(i != givingYearTo) {
					SQL += " UNION ALL ";
				}
			}
			
			SQL += ") t ORDER BY t.nf,t.yxmc,t.zdf DESC ";
			
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			for(int i = 1;i <= (givingYearTo - givingYearFrom + 1); i++) {
				ps.setString(i, school);
			}
			rs = ps.executeQuery();
			SchoolMarjorCommonBean bean = null;
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZdf(rs.getString("zdf"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setPc(rs.getString("pc"));
				bean.setXk(rs.getString("xk"));
				bean.setNf(rs.getInt("nf"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	//随机出来十个企业
	public List<JiuyeBean> pickJiuyeBySchoolName(String school) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<JiuyeBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT group_name, lsy, COUNT(*) as ct from career_jy_all_2024 x WHERE x.yxmc LIKE ? GROUP BY group_name, lsy ORDER BY COUNT(*) DESC LIMIT 15";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + school + "%");
			rs = ps.executeQuery();
			JiuyeBean bean = null;
			while (rs.next()) {
				bean = new JiuyeBean();
				bean.setDw(rs.getString("lsy"));
				bean.setCnt(rs.getInt("ct"));
				bean.setSshy(rs.getString("group_name"));
				bean.setSsgs(rs.getString("lsy"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	//随机出来十个企业
	public List<JiuyeBean> pickJiuyeByZyName(String zymc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<JiuyeBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT group_name, lsy, zymc, COUNT(*) as ct from career_jy_all_2024 x WHERE x.zymc LIKE ? GROUP BY group_name, lsy, zymc ORDER BY COUNT(*) DESC LIMIT 15";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + zymc + "%");
			rs = ps.executeQuery();
			JiuyeBean bean = null;
			while (rs.next()) {
				bean = new JiuyeBean();
				bean.setZymc(rs.getString("zymc"));
				bean.setCnt(rs.getInt("ct"));
				bean.setSshy(rs.getString("group_name"));
				bean.setSsgs(rs.getString("lsy"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}



	public List<MarjorBean> getRelativeZyByName(String zymc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MarjorBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT  distinct Y.`专业名称`, Y.`专业类`,  y.`第一印象`, y.`层次` from base_major y WHERE y.`专业类` IN (SELECT distinct x.`专业类` from base_major x WHERE x.`专业名称` = ?) or y.`专业名称` LIKE ? order by y.`层次` DESC, y.`专业类`, y.`专业名称`";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, zymc);
			ps.setString(2, "%" + zymc.substring(0, 2) + "%");
			rs = ps.executeQuery();
			MarjorBean bean = null;
			while (rs.next()) {
				bean = new MarjorBean();
				bean.setZymc(rs.getString("专业名称"));
				bean.setZyl(rs.getString("专业类"));
				bean.setDyyx(rs.getString("第一印象"));
				bean.setCc(rs.getString("层次"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public List<MarjorBean> getRelativeZKZyByName(String zymc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MarjorBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT  distinct Y.`专业名称`, Y.`专业类`,  y.`第一印象`  from base_major y WHERE y.`专业类` IN (SELECT distinct x.`专业类` from base_major x WHERE x.`专业名称` LIKE ? AND X.`层次` = '专科')";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + zymc + "%");
			rs = ps.executeQuery();
			MarjorBean bean = null;
			while (rs.next()) {
				bean = new MarjorBean();
				bean.setZymc(rs.getString("专业名称"));
				bean.setZyl(rs.getString("专业类"));
				bean.setDyyx(rs.getString("第一印象"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public List<MarjorBean> getRelativeZyByDalei(String zydalei) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MarjorBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT  distinct Y.`专业名称`, Y.`专业类`,  y.`第一印象`,  y.`层次`  from base_major y WHERE y.`专业类` = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, zydalei);
			rs = ps.executeQuery();
			MarjorBean bean = null;
			while (rs.next()) {
				bean = new MarjorBean();
				bean.setZymc(rs.getString("专业名称"));
				bean.setZyl(rs.getString("专业类"));
				bean.setDyyx(rs.getString("第一印象"));
				bean.setCc(rs.getString("层次"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public MarjorBean pickZyByName(String zy) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		MarjorBean bean = new MarjorBean();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT *  FROM base_major WHERE `专业名称`= ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, zy);
			rs = ps.executeQuery();
			while (rs.next()) {
				bean.setJydq(rs.getString("就业地区分布"));
				bean.setJygw(rs.getString("就业岗位分布"));
				bean.setJyhy(rs.getString("就业行业分布"));
				bean.setJyl(rs.getString("就业率"));
				bean.setGsm(rs.getString("专业干什么"));
				bean.setJyqx(rs.getString("就业去向"));
				bean.setXsm(rs.getString("专业学什么"));
				bean.setSsm(rs.getString("专业是什么"));
				bean.setXkjy((rs.getString("选考（学科）建议") == null) ? "不限" : rs.getString("选考（学科）建议"));
				bean.setXbbl(rs.getString("性别比例"));
				bean.setDyyx(rs.getString("第一印象"));
				bean.setZymc(rs.getString("专业名称"));
				bean.setZyl(rs.getString("专业类"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}
	

	public List<MarjorTsBean> getZytsByName(String zy) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MarjorTsBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT distinct `院校` FROM base_university_major_tese WHERE `专业`= ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, zy);
			rs = ps.executeQuery();
			MarjorTsBean bean = null;
			while (rs.next()) {
				bean = new MarjorTsBean();
				bean.setYxmc(rs.getString("院校"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public List<MarjorTsBean> getZydltsByName(String zy) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MarjorTsBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT distinct `院校`,`专业` FROM base_university_major_tese WHERE `大类` in (select `学科门类` from base_major where `专业名称`= ?) ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, zy);
			rs = ps.executeQuery();
			MarjorTsBean bean = null;
			while (rs.next()) {
				bean = new MarjorTsBean();
				bean.setYxmc(rs.getString("院校"));
				bean.setZymc(rs.getString("专业"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public List<MarjorTsBean> getZydltsByName2(String zy) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MarjorTsBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT distinct `院校` FROM base_university_major_tese WHERE `大类` in (select `学科门类` from base_major where `专业名称`= ?) ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, zy);
			rs = ps.executeQuery();
			MarjorTsBean bean = null;
			while (rs.next()) {
				bean = new MarjorTsBean();
				bean.setYxmc(rs.getString("院校"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public List<MarjorXkRankBean> getZyXkRankByName(String zy) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MarjorXkRankBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT *  FROM base_university_major_rank2 WHERE `一级学科名称`= ?  ORDER BY SORT";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, zy);
			rs = ps.executeQuery();
			MarjorXkRankBean bean = null;
			while (rs.next()) {
				bean = new MarjorXkRankBean();
				bean.setRank(rs.getString("评估结果"));
				bean.setYxmc(rs.getString("院校名称"));
				bean.setZymc(rs.getString("一级学科名称"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public List<MarjorXkRankBean> getZyXkRankByName2(String zy) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MarjorXkRankBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT *  FROM base_university_major_rank2 WHERE zydl = (SELECT zydl FROM base_university_major_rank2 WHERE `一级学科名称` = ?) ORDER BY SORT";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, zy);
			rs = ps.executeQuery();
			MarjorXkRankBean bean = null;
			while (rs.next()) {
				bean = new MarjorXkRankBean();
				bean.setRank(rs.getString("评估结果"));
				bean.setYxmc(rs.getString("院校名称"));
				bean.setZymc(rs.getString("一级学科名称"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public List<MarjorXkRankBean> getZyXkRankByName3(String zydalei) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MarjorXkRankBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.*, y.sf as sf, y.cs as cs, y.`985工程` as is985, y.`211工程` as is211, y.`双一流` as issyl FROM base_university_major_rank2 x left join base_university y on x.`院校名称` = y.yxmc WHERE zydl = ? ORDER BY SORT";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, zydalei);
			rs = ps.executeQuery();
			MarjorXkRankBean bean = null;
			while (rs.next()) {
				bean = new MarjorXkRankBean();
				bean.setRank(rs.getString("评估结果"));
				bean.setYxmc(rs.getString("院校名称"));
				bean.setZymc(rs.getString("一级学科名称"));
				bean.setZydl(rs.getString("zydl"));
				bean.setSf(rs.getString("sf"));
				bean.setCs(rs.getString("cs"));
				bean.setIs985(rs.getString("is985"));
				bean.setIs211(rs.getString("is211"));
				bean.setIssyl(rs.getString("issyl"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public int getWCByYearAndScore(String sf, String xk, int nf, int zdf) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM base_wc WHERE sf = ?, nf = ?, xk = ?, zdf = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, sf);
			ps.setInt(2, nf);
			ps.setString(3, xk);
			ps.setInt(4, zdf);
			rs = ps.executeQuery();
			if (rs.next())
				return rs.getInt("wc");
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return -1;
	}

	public List<MarjorXkRankBean> getZyXkRankByYxmc(String yx) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MarjorXkRankBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT *  FROM base_university_major_rank2 WHERE `院校名称`= ? ORDER BY SORT";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yx);
			rs = ps.executeQuery();
			MarjorXkRankBean bean = null;
			while (rs.next()) {
				bean = new MarjorXkRankBean();
				bean.setRank(rs.getString("评估结果"));
				bean.setYxmc(rs.getString("院校名称"));
				bean.setZymc(rs.getString("一级学科名称"));
				bean.setZydl(rs.getString("zydl"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public List<KuaPCBean> getKuaPCByProvinceAndYxmc(String p, String yxmc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<KuaPCBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT *  FROM data_university_kuapici WHERE sf = ? and yxmc = ? ORDER BY zdf22a";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, p);
			ps.setString(2, yxmc);
			rs = ps.executeQuery();
			KuaPCBean bean = null;
			while (rs.next()) {
				bean = new KuaPCBean();
				bean.setPc1(rs.getString("pc1"));
				bean.setPc2(rs.getString("pc2"));
				bean.setSf(rs.getString("sf"));
				bean.setXk(rs.getString("xk"));
				bean.setYxbz1(rs.getString("yxbz1"));
				bean.setYxbz2(rs.getString("yxbz2"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZdf22a(rs.getString("zdf22a"));
				bean.setZdf22b(rs.getString("zdf22b"));
				bean.setZdfwc22a(rs.getString("zdfwc22a"));
				bean.setZdfwc22b(rs.getString("zdfwc22b"));
				bean.setZx(rs.getString("zx"));
				bean.setZyz(rs.getString("zyz"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public List<MarjorXkRankBean> getZyXkRankJPGByYxmc(String yx) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MarjorXkRankBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT *  FROM base_university_major_rank1 WHERE `学校名称`= ? ORDER BY `排名`";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yx);
			rs = ps.executeQuery();
			MarjorXkRankBean bean = null;
			while (rs.next()) {
				bean = new MarjorXkRankBean();
				bean.setRank(rs.getString("排名"));
				bean.setYxmc(rs.getString("学校名称"));
				bean.setZymc(rs.getString("专业名称"));
				bean.setLevel(rs.getString("评级"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public List<MarjorXkRankBean> getZyXkRankJPGByZymc(String zy) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MarjorXkRankBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.*, y.sf as sf, y.cs as cs, y.`985工程` as is985, y.`211工程` as is211, y.`双一流` as issyl FROM base_university_major_rank1 x left join base_university y on x.`学校名称` = y.yxmc WHERE `专业名称`= ? ORDER BY `排名`";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, zy);
			rs = ps.executeQuery();
			MarjorXkRankBean bean = null;
			while (rs.next()) {
				bean = new MarjorXkRankBean();
				bean.setRank(rs.getString("排名"));
				bean.setYxmc(rs.getString("学校名称"));
				bean.setZymc(rs.getString("专业名称"));
				bean.setLevel(rs.getString("评级"));
				bean.setSf(rs.getString("sf"));
				bean.setCs(rs.getString("cs"));
				bean.setIs985(rs.getString("is985"));
				bean.setIs211(rs.getString("is211"));
				bean.setIssyl(rs.getString("issyl"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public List<MarjorXkRankBean> getZyXkTeseByYxmc(String yx) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MarjorXkRankBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT *  FROM base_university_major_rank3 WHERE `学校名称` = ? ORDER BY `排名`";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yx);
			rs = ps.executeQuery();
			MarjorXkRankBean bean = null;
			while (rs.next()) {
				bean = new MarjorXkRankBean();
				bean.setRank(rs.getString("排名"));
				bean.setYxmc(rs.getString("学校名称"));
				bean.setZymc(rs.getString("专业名称"));
				bean.setLevel(rs.getString("评级"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<MarjorXkRankBean> getZyXkTeseByZYMCAndRankRange(String zymc, int rank) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MarjorXkRankBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT *  FROM base_university_major_rank3 WHERE `专业名称` = ? and `排名` between ? and ? ORDER BY `排名`";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, zymc);
			ps.setInt(2, rank - 5);
			ps.setInt(3, rank + 5);
			rs = ps.executeQuery();
			MarjorXkRankBean bean = null;
			while (rs.next()) {
				bean = new MarjorXkRankBean();
				bean.setRank(rs.getString("排名"));
				bean.setYxmc(rs.getString("学校名称"));
				bean.setZymc(rs.getString("专业名称"));
				bean.setLevel(rs.getString("评级"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<CareerJYJobREQ> getZyJiuyeREQ(String zymc, int limit) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CareerJYJobREQ> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT *  FROM career_jy_job_req WHERE req_major_name = ? and salary <> '面议' and display_ind = 1 ORDER BY RAND() LIMIT " +limit;
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, zymc);
			rs = ps.executeQuery();
			CareerJYJobREQ bean = null;
			while (rs.next()) {
				bean = new CareerJYJobREQ();
				bean.setCompanyName(rs.getString("company"));
				bean.setEducation(rs.getString("req_education"));
				bean.setFirstPublishTime(rs.getString("first_publish_time"));
				bean.setIndustryName(rs.getString("industry_name"));
				bean.setJobSummary(rs.getString("job_summary"));
				bean.setJobtitle(rs.getString("job_title"));
				bean.setMajorCatg(rs.getString("req_major_catg"));
				bean.setMajorName(rs.getString("req_major_name"));
				bean.setProperty(rs.getString("property"));
				bean.setSalary(rs.getString("salary"));
				bean.setSalaryCount(rs.getString("salary_count"));
				bean.setWorkCity(rs.getString("work_city"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<CareerJYJobREQ> getZyCatgJiuyeREQ(String zymc, int limit) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CareerJYJobREQ> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM career_jy_job_req WHERE req_major_catg = ? and salary <> '面议' and display_ind = 1 ORDER BY RAND() LIMIT " +limit;
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, zymc);
			rs = ps.executeQuery();
			CareerJYJobREQ bean = null;
			while (rs.next()) {
				bean = new CareerJYJobREQ();
				bean.setCompanyName(rs.getString("company"));
				bean.setEducation(rs.getString("req_education"));
				bean.setFirstPublishTime(rs.getString("first_publish_time"));
				bean.setIndustryName(rs.getString("industry_name"));
				bean.setJobSummary(rs.getString("job_summary"));
				bean.setJobtitle(rs.getString("job_title"));
				bean.setMajorCatg(rs.getString("req_major_catg"));
				bean.setMajorName(rs.getString("req_major_name"));
				bean.setProperty(rs.getString("property"));
				bean.setSalary(rs.getString("salary"));
				bean.setSalaryCount(rs.getString("salary_count"));
				bean.setWorkCity(rs.getString("work_city"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public HashMap<String, String> getAllZYMCbyJiuyeREQ() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, String> MAP = new HashMap<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT req_major_name FROM career_jy_job_req WHERE salary <> '面议' and display_ind = 1 group by req_major_name";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			while (rs.next()) {
				MAP.put(rs.getString("req_major_name"), rs.getString("req_major_name"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return MAP;
	}
	
	public HashMap<String, String> getAllZYCatgbyJiuyeREQ() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, String> MAP = new HashMap<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT req_major_catg FROM career_jy_job_req WHERE salary <> '面议' and display_ind = 1 group by req_major_catg";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			while (rs.next()) {
				MAP.put(rs.getString("req_major_catg"), rs.getString("req_major_catg"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return MAP;
	}

	public List<MarjorXkRankBean> getZyXkTeseByZymc(String zy) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MarjorXkRankBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.*, y.sf as sf, y.cs as cs, y.`985工程` as is985, y.`211工程` as is211, y.`双一流` as issyl FROM base_university_major_tese x left join base_university y on x.`院校` = y.yxmc WHERE `专业`= ? ORDER BY `院校`";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, zy);
			rs = ps.executeQuery();
			MarjorXkRankBean bean = null;
			while (rs.next()) {
				bean = new MarjorXkRankBean();
				bean.setRank(rs.getString("特色"));
				bean.setYxmc(rs.getString("院校"));
				bean.setZymc(rs.getString("专业"));
				bean.setLevel(rs.getString("特色"));
				bean.setSf(rs.getString("sf"));
				bean.setCs(rs.getString("cs"));
				bean.setIs985(rs.getString("is985"));
				bean.setIs211(rs.getString("is211"));
				bean.setIssyl(rs.getString("issyl"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	private CardBean getCardBean(ResultSet rs) throws Exception{
		CardBean bean = new CardBean();
		bean.setId(rs.getString("C_ID"));
		bean.setPasswd(rs.getString("C_PASSWD"));
		bean.setPhone(rs.getString("C_PHONE"));
		bean.setProv(rs.getString("C_PROV"));
		bean.setCity(rs.getString("C_CITY"));
		bean.setXk(rs.getString("C_XK"));
		bean.setScore(rs.getInt("C_SCORE"));
		bean.setScore_org(rs.getInt("C_SCORE_ORG"));
		bean.setYear(rs.getInt("C_YEAR"));
		bean.setStatus(rs.getInt("C_STATUS"));
		bean.setCreate(rs.getTimestamp("C_CREATE"));
		bean.setActive(rs.getTimestamp("C_ACTIVE"));
		bean.setLastLogin(rs.getTimestamp("C_LAST_LOGIN"));
		bean.setOpenID(rs.getString("C_OPEN_ID"));
		bean.setSysInd(rs.getString("C_SYS_IND"));
		bean.setExpire(rs.getTimestamp("C_EXPIRE"));
		bean.setAllowedProv(rs.getString("C_ALLOWED_PROV"));
		bean.setVersion(rs.getString("C_VERSION"));
		bean.setAdmin(rs.getString("C_ADMIN"));
		bean.setOrderId(rs.getString("C_ORDER_ID"));
		bean.setDesc(rs.getString("C_DESC"));
		bean.setRemark(rs.getString("C_REMARK"));
		bean.setBossId(rs.getString("C_BOSS_ID"));
		bean.setNickName(rs.getString("C_NICKNAME"));
		bean.setIntroduce(rs.getString("C_INTRODUCE"));
		bean.setIntroduceTm(rs.getTimestamp("C_INTRODUCE_TM"));
		bean.setAgent(rs.getString("C_AGENT"));
		bean.setAgentTm(rs.getTimestamp("C_AGENT_TM"));
		bean.setLast_modified(rs.getTimestamp("C_LAST_MODIFIED"));
		bean.setTokenId(rs.getString("TOKEN_ID"));
		bean.setForm_dnld_cnt(rs.getInt("C_FORM_DNLD_CNT"));
		bean.setSaas_id(rs.getString("saas_id"));
		
		return bean;
	}

	public CardBean getCardByIDandPasswd(String id, String passwd) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		CardBean bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT *  FROM base_card WHERE C_ID = ? and C_PASSWD = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, id.toUpperCase());
			ps.setString(2, passwd.toUpperCase());
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = getCardBean(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}

	public CardBean getCardByOPENID(String openID) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		CardBean bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT *  FROM base_card WHERE C_OPEN_ID = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, openID);
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = getCardBean(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}
	
	public CardBean getCardByID(String id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		CardBean bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT *  FROM base_card WHERE C_ID = ? ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, id.toUpperCase());
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = getCardBean(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}
	
	

	public List<CardBean> getAllCards(String cid, int pager) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		CardBean bean = null;
		List<CardBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT *  FROM base_card WHERE C_ID like ? order by C_ID limit "
					+ ((pager - 1) * CCache.CC_PAGER_SIZE) + ", " + CCache.CC_PAGER_SIZE;
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + cid + "%");
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = getCardBean(rs);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public boolean updateCard(CardBean bean) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE base_card SET C_PROV = ?, C_SCORE = ?, C_XK = ?, C_YEAR = ?, C_ACTIVE = ?, C_STATUS = ?, C_LAST_LOGIN = ?, C_CITY = ?, C_SCORE_ORG = ? WHERE C_ID = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, bean.getProv());
			ps.setInt(2, bean.getScore());
			ps.setString(3, bean.getXk());
			ps.setInt(4, bean.getYear());
			if (bean.getActive() == null) {
				bean.setActive(new Date());
			}
			ps.setTimestamp(5, new Timestamp(bean.getActive().getTime()));
			ps.setInt(6, bean.getStatus());
			ps.setTimestamp(7, new Timestamp(bean.getLastLogin().getTime()));
			ps.setString(8, bean.getCity());
			ps.setInt(9, bean.getScore_org());
			ps.setString(10, bean.getId());
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}

	public boolean updateCardForOpenIDOnly(String id, String openID) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE base_card SET C_OPEN_ID = ? WHERE C_ID = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, openID);
			ps.setString(2, id);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}

	public boolean frozenCard(String cid) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE base_card SET C_STATUS = 2 WHERE C_ID = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}

	public boolean insertSysVisitLog(String cid, String request) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into sys_trans_visit(C_ID, VISIT_TM, VISIT_MOD) values(?, unix_timestamp(NOW()) , ?)";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.setString(2, request);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}

	public boolean deleteSysVisitLog(String cid) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "DELETE FROM sys_trans_visit WHERE c_id = ? AND VISIT_TM < (unix_timestamp(NOW()) - 60 * 60)";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}

	public int getLatest30SecsVisitLogCount(String cid) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT COUNT(1) as ct FROM sys_trans_visit WHERE c_id = ? AND VISIT_TM > (unix_timestamp(NOW()) - 10)";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			rs = ps.executeQuery();
			if (rs.next())
				return rs.getInt(1);
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}

	public List<SchoolMarjorCommonBean> listSchoolByCondition(HashMap<String, String> condition, String studentProvince,
			int queryYear, String xk, int pager) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolMarjorCommonBean> list = new ArrayList<>();
		int wcUp = Tools.getInt(condition.get("wcUp"));
		int wcDown = Tools.getInt(condition.get("wcDown"));
		int scoreUp = Tools.getInt(condition.get("scoreUp"));
		int scoreDown = Tools.getInt(condition.get("scoreDown"));
		String yxzyName = Tools.trim(condition.get("yxzyName"));
		String szdq = Tools.trim(condition.get("szdq"));
		String yxlx = Tools.trim(condition.get("yxlx"));
		String yxtx = Tools.trim(condition.get("yxtx"));
		String yxcc = Tools.trim(condition.get("yxcc"));
		String bkcl = Tools.trim(condition.get("bkcl"));
		String bxlx = Tools.trim(condition.get("bxlx"));
		String zysl = Tools.trim(condition.get("zysl"));
		String yxys = Tools.trim(condition.get("yxys"));
		pager = (pager < 1) ? 1 : pager;
		int recordCnt = 0;
		HashSet<String> yxmcQueryList = new HashSet<>();
		HashSet<String> lmgxKey = Tools.getSetByStrSplit(condition.get("lmgx"));
		Iterator<String> it = lmgxKey.iterator();
		while (it.hasNext())
			yxmcQueryList.addAll(Tools.getSetByStrSplit(HM_LMGX.get(it.next())));
		HashSet<String> hymxKey = Tools.getSetByStrSplit(condition.get("hymx"));
		it = hymxKey.iterator();
		while (it.hasNext())
			yxmcQueryList.addAll(Tools.getSetByStrSplit(HM_HYMX.get(it.next())));
		HashSet<String> ybwsKey = Tools.getSetByStrSplit(condition.get("ybws"));
		it = ybwsKey.iterator();
		while (it.hasNext())
			yxmcQueryList.addAll(Tools.getSetByStrSplit(HM_YBWS.get(it.next())));
		HashSet<String> hyhpKey = Tools.getSetByStrSplit(condition.get("hyhp"));
		it = hyhpKey.iterator();
		while (it.hasNext())
			yxmcQueryList.addAll(Tools.getSetByStrSplit(HM_HYHP.get(it.next())));
		if (bkcl.indexOf("联合培养") != -1)
			yxmcQueryList.addAll(YX_LHPY);
		if (bkcl.indexOf("校中校") != -1)
			yxmcQueryList.addAll(YX_XZX);
		if (bkcl.indexOf("分校通道") != -1)
			yxmcQueryList.addAll(YX_FX);
		if (bkcl.indexOf("公安警校") != -1)
			yxmcQueryList.addAll(YX_GAJX);
		if (bkcl.indexOf("司法警校") != -1)
			yxmcQueryList.addAll(YX_SFJX);
		HashSet<String> yxtxKey = Tools.getSetByStrSplit(yxtx);
		it = yxtxKey.iterator();
		while (it.hasNext()) {
			String temp = it.next();
			if (temp.equals("985"))
				yxmcQueryList.addAll(CCache.CC_RY_985);
			if (temp.equals("211"))
				yxmcQueryList.addAll(CCache.CC_RY_211);
		}
		if (yxtx.indexOf("双一流") != -1)
			yxmcQueryList.addAll(CCache.CC_RY_SYL);
		if (yxtx.indexOf("强基计划") != -1)
			yxmcQueryList.addAll(CCache.CC_RY_QJ);
		String pName = HM_PROVINCE_CODE_NAME.get(studentProvince.substring(0, 2));
		
		String allMatchedYxmc = Tools.getSQLQueryin(yxmcQueryList);
		Tools.println("allMatchedYxmc = " + allMatchedYxmc);
		try {
			String SQLSum = "SELECT count(1) ";
			String SQLList = "SELECT x.*, y.*, z.*, k.yxmc as kpc ";
			String SQL = " FROM " + studentProvince + "_" + queryYear + " x LEFT JOIN base_university y ON x.yxmc_org = y.yxmc LEFT JOIN base_university_baoyan z ON x.yxmc_org = z.`学校名称` LEFT JOIN data_university_kuapici_temp k ON x.yxmc_org = k.yxmc AND k.sf = '" + pName + "' WHERE x.xk_code like '%"+xk+"%' and (x.zdf BETWEEN ? AND ? AND x.zdfwc BETWEEN ? AND ? )";
			if (!Tools.isEmpty(allMatchedYxmc))
				SQL = String.valueOf(SQL) + " and x.yxmc_org in (" + allMatchedYxmc + ")";
			if (!Tools.isEmpty(yxcc))
				SQL = String.valueOf(SQL) + " and y.`办学类型` like '%" + yxcc + "%'";
			if (!Tools.isEmpty(bxlx))
				SQL = String.valueOf(SQL) + " and y.`办学性质` = '" + bxlx + "'";
			if (!Tools.isEmpty(yxlx))
				SQL = String.valueOf(SQL) + " and y.yxlx in (" + Tools.getSQLQueryin(Tools.getSetByStrSplit(yxlx))
						+ ")";
			if (!Tools.isEmpty(szdq))
				SQL = String.valueOf(SQL) + " and y.`省份` in (" + Tools.getSQLQueryin(Tools.getSetByStrSplit(szdq))
						+ ")";

			if (!Tools.isEmpty(yxzyName))
		        SQL = String.valueOf(SQL) + " and (x.yxmc like ? or x.zymc like ? or zybz like ?)"; 
		      
		      if (bkcl.indexOf("公费师范生") != -1)
		          SQL = String.valueOf(SQL) + " and (x.zybz like '%公费师范%' or x.zybz like '%优师%' or x.zymc like '%公费师范%' or x.zymc like '%优师%')"; 
		        if (bkcl.indexOf("提前批") != -1)
		            SQL = String.valueOf(SQL) + " and (x.pc like '%提%')"; 
		        if (bkcl.indexOf("军警司法") != -1)
		            SQL = String.valueOf(SQL) + " and (x.yxmc like '%司法%' or x.yxmc like '%警察%' or x.yxmc like '%消防%' or x.yxmc like '%军%')"; 
		      
		      if (yxtx.indexOf("中外合作") != -1)
		        SQL = String.valueOf(SQL) + " and (x.yxmc like '%中外合作%' or x.zymc like '%中外合作%' %or zybz like '%中外合作%')"; 
		      if (yxtx.indexOf("小985") != -1)
		        SQL = String.valueOf(SQL) + " and (y.yxry like '%985%')"; 
		      if (yxtx.indexOf("小211") != -1)
		        SQL = String.valueOf(SQL) + " and (y.yxry like '%211%')"; 

			Tools.println(SQL);
			conn = DatabaseUtils.getConnection();
			ps = conn.prepareStatement(String.valueOf(SQLSum) + SQL);
			ps.setInt(1, scoreDown);
			ps.setInt(2, (scoreUp < 10) ? 999999 : scoreUp);
			ps.setInt(3, wcDown);
			ps.setInt(4, (wcUp < 10) ? 999999 : wcUp);
			if (!Tools.isEmpty(yxzyName)) {
				ps.setString(5, "%" + yxzyName + "%");
				ps.setString(6, "%" + yxzyName + "%");
				ps.setString(7, "%" + yxzyName + "%");
			}
			rs = ps.executeQuery();
			while (rs.next())
				recordCnt = rs.getInt(1);
			ps.close();
			rs.close();
			ps = null;
			rs = null;
			ps = conn.prepareStatement(String.valueOf(SQLList) + SQL + "  ORDER BY x.zdf DESC limit "
					+ ((pager - 1) * CCache.CC_PAGER_SIZE) + "," + CCache.CC_PAGER_SIZE);
			ps.setInt(1, scoreDown);
			ps.setInt(2, (scoreUp < 10) ? 999999 : scoreUp);
			ps.setInt(3, wcDown);
			ps.setInt(4, (wcUp < 10) ? 999999 : wcUp);
			if (!Tools.isEmpty(yxzyName)) {
				ps.setString(5, "%" + yxzyName + "%");
				ps.setString(6, "%" + yxzyName + "%");
				ps.setString(7, "%" + yxzyName + "%");
			}
			rs = ps.executeQuery();
			SchoolMarjorCommonBean bean = null;
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setZdf(rs.getString("zdf"));
				bean.setZydm(rs.getString("zydm"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZymc_org(rs.getString("zymc_org"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setPjf(rs.getString("pjf"));
				bean.setPjfwc(rs.getString("pjfwc"));
				bean.setZgf(rs.getString("zgf"));
				bean.setZybz(rs.getString("zybz"));
				bean.setPc(rs.getString("pc"));
				bean.setXk(rs.getString("xk"));

				bean.setYxlx(rs.getString("院校类型"));
				bean.setBxxz(rs.getString("办学性质"));
				bean.setLsy(rs.getString("隶属于"));
				bean.setHyhp(rs.getString("hyhp"));
				bean.setHymx(rs.getString("hymx"));
				bean.setYbws(rs.getString("ybws"));
				bean.setIs985("是".equals(rs.getString("985工程")) ? "985" : null);
				bean.setIs211("是".equals(rs.getString("211工程")) ? "211" : null);
				bean.setIsSyl("是".equals(rs.getString("双一流")) ? "双一流" : null);
				bean.setIsQj("是".equals(rs.getString("强基")) ? "强基" : null);
				bean.setLsy(rs.getString("隶属于"));
				bean.setCs(rs.getString("城市"));
				bean.setQy(rs.getString("区域"));
				if (rs.getString("2023届免率") == null) {
					bean.setYxbyl((rs.getString("2022届推免率") == null) ? "--" : rs.getString("2022届推免率"));
				} else {
					bean.setYxbyl(rs.getString("2023届免率"));
				}

				bean.setKuapc(rs.getString("kpc"));
				bean.setRecordCnt(recordCnt);
				bean.setCurPageNum(pager);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public List<SchoolBean> listSchoolByConditionYXOnly(HashMap<String, String> condition, String studentProvince,
			String xk, int pager) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolBean> list = new ArrayList<>();
		int wcUp = Tools.getInt(condition.get("wcUp"));
		int wcDown = Tools.getInt(condition.get("wcDown"));
		int scoreUp = Tools.getInt(condition.get("scoreUp"));
		int scoreDown = Tools.getInt(condition.get("scoreDown"));
		String yxzyName = Tools.trim(condition.get("yxzyName"));
		String szdq = Tools.trim(condition.get("szdq"));
		String yxlx = Tools.trim(condition.get("yxlx"));
		String yxtx = Tools.trim(condition.get("yxtx"));
		String yxcc = Tools.trim(condition.get("yxcc"));
		String bkcl = Tools.trim(condition.get("bkcl"));
		String bxlx = Tools.trim(condition.get("bxlx"));
		String zysl = Tools.trim(condition.get("zysl"));
		String yxys = Tools.trim(condition.get("yxys"));
		pager = (pager < 1) ? 1 : pager;
		int recordCnt = 0;
		HashSet<String> yxmcQueryList = new HashSet<>();
		HashSet<String> lmgxKey = Tools.getSetByStrSplit(condition.get("lmgx"));
		Iterator<String> it = lmgxKey.iterator();
		while (it.hasNext())
			yxmcQueryList.addAll(Tools.getSetByStrSplit(HM_LMGX.get(it.next())));
		HashSet<String> hymxKey = Tools.getSetByStrSplit(condition.get("hymx"));
		it = hymxKey.iterator();
		while (it.hasNext())
			yxmcQueryList.addAll(Tools.getSetByStrSplit(HM_HYMX.get(it.next())));
		HashSet<String> ybwsKey = Tools.getSetByStrSplit(condition.get("ybws"));
		it = ybwsKey.iterator();
		while (it.hasNext())
			yxmcQueryList.addAll(Tools.getSetByStrSplit(HM_YBWS.get(it.next())));
		HashSet<String> hyhpKey = Tools.getSetByStrSplit(condition.get("hyhp"));
		it = hyhpKey.iterator();
		while (it.hasNext())
			yxmcQueryList.addAll(Tools.getSetByStrSplit(HM_HYHP.get(it.next())));
		if (bkcl.indexOf("联合培养") != -1)
			yxmcQueryList.addAll(YX_LHPY);
		if (bkcl.indexOf("校中校") != -1)
			yxmcQueryList.addAll(YX_XZX);
		if (bkcl.indexOf("分校通道") != -1)
			yxmcQueryList.addAll(YX_FX);
		if (bkcl.indexOf("公安警校") != -1)
			yxmcQueryList.addAll(YX_GAJX);
		if (bkcl.indexOf("司法警校") != -1)
			yxmcQueryList.addAll(YX_SFJX);
		HashSet<String> yxtxKey = Tools.getSetByStrSplit(yxtx);
		it = yxtxKey.iterator();
		while (it.hasNext()) {
			String temp = it.next();
			if (temp.equals("985"))
				yxmcQueryList.addAll(CCache.CC_RY_985);
			if (temp.equals("211"))
				yxmcQueryList.addAll(CCache.CC_RY_211);
		}
		if (yxtx.indexOf("双一流") != -1)
			yxmcQueryList.addAll(CCache.CC_RY_SYL);
		if (yxtx.indexOf("强基计划") != -1)
			yxmcQueryList.addAll(CCache.CC_RY_QJ);
		String pName = HM_PROVINCE_CODE_NAME.get(studentProvince.substring(0, 2));
		String xkQuery = "";
		if ("浙江".indexOf(pName) != -1 || "山东".indexOf(pName) != -1) {
			xkQuery = String.valueOf(xkQuery) + " or x.xk like '%%'";
		} else {
			Iterator<String> studentXKList = Tools.convertXK(xk).iterator();
			while (studentXKList.hasNext()) {
				String value = studentXKList.next();
				xkQuery = String.valueOf(xkQuery) + " or x.xk like '%" + value + "%'";
			}
		}
		String allMatchedYxmc = Tools.getSQLQueryin(yxmcQueryList);
		try {
			String SQLSum = "SELECT count(1) ";
			String SQLList = "SELECT x.*, y.*, z.*, k.yxmc as kpc ";
			String SQL = " FROM " + studentProvince + " x LEFT JOIN base_university y ON x.yxmc_org = y.yxmc LEFT JOIN base_university_baoyan z ON x.yxmc_org = z.`学校名称` LEFT JOIN data_university_kuapici_temp k ON x.yxmc_org = k.yxmc AND k.sf = '" + pName + "' WHERE (x.xk is NULL or x.xk = '不限' " + xkQuery
					+ ") and ((x.zdf22 BETWEEN ? AND ? AND x.zdfwc22 BETWEEN ? AND ? ) OR x.zdf22 is null)";
			if (!Tools.isEmpty(allMatchedYxmc))
				SQL = String.valueOf(SQL) + " and x.yxmc_org in (" + allMatchedYxmc + ")";
			if (!Tools.isEmpty(yxcc))
				SQL = String.valueOf(SQL) + " and y.`办学类型` like '%" + yxcc + "%'";
			if (!Tools.isEmpty(bxlx))
				SQL = String.valueOf(SQL) + " and y.`办学性质` = '" + bxlx + "'";
			if (!Tools.isEmpty(yxlx))
				SQL = String.valueOf(SQL) + " and y.yxlx in (" + Tools.getSQLQueryin(Tools.getSetByStrSplit(yxlx))
						+ ")";
			if (!Tools.isEmpty(szdq))
				SQL = String.valueOf(SQL) + " and y.`省份` in (" + Tools.getSQLQueryin(Tools.getSetByStrSplit(szdq))
						+ ")";
			if (!Tools.isEmpty(yxzyName))
				SQL = String.valueOf(SQL) + " and (x.yxmc like ? or x.yxbz like ?)";
			if (bkcl.indexOf("公费师范生") != -1)
				SQL = String.valueOf(SQL)
						+ " and (x.yxbz like '%公费师范%' or x.yxbz like '%优师%' or x.yxmc like '%公费师范%' or x.yxmc like '%优师%')";
			if (yxtx.indexOf("中外合作") != -1)
				SQL = String.valueOf(SQL) + " and (x.yxmc like '%中外合作%' or x.yxbz like '%中外合作%')";
			if (yxtx.indexOf("小985") != -1)
				SQL = String.valueOf(SQL) + " and (y.yxry like '%985%')";
			if (yxtx.indexOf("小211") != -1)
				SQL = String.valueOf(SQL) + " and (y.yxry like '%211%')";
			Tools.println(SQL);
			conn = DatabaseUtils.getConnection();
			ps = conn.prepareStatement(String.valueOf(SQLSum) + SQL);
			ps.setInt(1, scoreDown);
			ps.setInt(2, (scoreUp < 10) ? 999999 : scoreUp);
			ps.setInt(3, wcDown);
			ps.setInt(4, (wcUp < 10) ? 999999 : wcUp);
			if (!Tools.isEmpty(yxzyName)) {
				ps.setString(5, "%" + yxzyName + "%");
				ps.setString(6, "%" + yxzyName + "%");
			}
			rs = ps.executeQuery();
			while (rs.next())
				recordCnt = rs.getInt(1);
			ps.close();
			rs.close();
			ps = null;
			rs = null;
			ps = conn.prepareStatement(String.valueOf(SQLList) + SQL + "  ORDER BY x.zdf22 DESC limit "
					+ ((pager - 1) * CCache.CC_PAGER_SIZE) + "," + CCache.CC_PAGER_SIZE);
			ps.setInt(1, scoreDown);
			ps.setInt(2, (scoreUp < 10) ? 999999 : scoreUp);
			ps.setInt(3, wcDown);
			ps.setInt(4, (wcUp < 10) ? 999999 : wcUp);
			if (!Tools.isEmpty(yxzyName)) {
				ps.setString(5, "%" + yxzyName + "%");
				ps.setString(6, "%" + yxzyName + "%");
			}
			rs = ps.executeQuery();
			SchoolBean bean = null;
			while (rs.next()) {
				bean = new SchoolBean();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZdf(rs.getString("zdf23"));
				bean.setZdfwc(rs.getString("zdfwc23"));
				bean.setZdfa(rs.getString("zdf22"));
				bean.setZdfwca(rs.getString("zdfwc22"));
				bean.setZdfb(rs.getString("zdf21"));
				bean.setZdfwcb(rs.getString("zdfwc21"));
				bean.setZdfc(rs.getString("zdf20"));
				bean.setZdfwcc(rs.getString("zdfwc20"));
				bean.setYxbz(rs.getString("yxbz"));
				bean.setZyz(rs.getString("zyz"));
				bean.setPc(rs.getString("pc"));
				bean.setXk(rs.getString("xk"));
				bean.setYxlx(rs.getString("院校类型"));
				bean.setBxxz(rs.getString("办学性质"));
				bean.setLsy(rs.getString("隶属于"));
				bean.setHyhp(rs.getString("hyhp"));
				bean.setHymx(rs.getString("hymx"));
				bean.setYbws(rs.getString("ybws"));
				bean.setIs985("是".equals(rs.getString("985工程")) ? "985" : null);
				bean.setIs211("是".equals(rs.getString("211工程")) ? "211" : null);
				bean.setIsSyl("是".equals(rs.getString("双一流")) ? "双一流" : null);
				bean.setIsQj("是".equals(rs.getString("强基")) ? "强基" : null);
				bean.setLsy(rs.getString("隶属于"));
				bean.setCs(rs.getString("城市"));
				bean.setQy(rs.getString("区域"));
				bean.setKuapc(rs.getString("kpc"));
				if (rs.getString("2023届免率") == null) {
					bean.setYxbyl((rs.getString("2022届推免率") == null) ? "--" : rs.getString("2022届推免率"));
				} else {
					bean.setYxbyl(rs.getString("2023届免率"));
				}
				bean.setRecordCnt(recordCnt);
				bean.setCurPageNum(pager);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public List<SchoolBean> listSchoolByConditionYXOnly2(HashMap<String, String> condition, int queryYear,
			String studentProvince, String xk, int pager) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolBean> list = new ArrayList<>();
		int wcUp = Tools.getInt(condition.get("wcUp"));
		int wcDown = Tools.getInt(condition.get("wcDown"));
		int scoreUp = Tools.getInt(condition.get("scoreUp"));
		int scoreDown = Tools.getInt(condition.get("scoreDown"));
		String yxzyName = Tools.trim(condition.get("yxzyName"));
		String szdq = Tools.trim(condition.get("szdq"));
		String yxlx = Tools.trim(condition.get("yxlx"));
		String yxtx = Tools.trim(condition.get("yxtx"));
		String yxcc = Tools.trim(condition.get("yxcc"));
		String bkcl = Tools.trim(condition.get("bkcl"));
		String bxlx = Tools.trim(condition.get("bxlx"));
		String zysl = Tools.trim(condition.get("zysl"));
		String yxys = Tools.trim(condition.get("yxys"));
		pager = (pager < 1) ? 1 : pager;
		int recordCnt = 0;
		HashSet<String> yxmcQueryList = new HashSet<>();
		HashSet<String> lmgxKey = Tools.getSetByStrSplit(condition.get("lmgx"));
		Iterator<String> it = lmgxKey.iterator();
		while (it.hasNext())
			yxmcQueryList.addAll(Tools.getSetByStrSplit(HM_LMGX.get(it.next())));
		HashSet<String> hymxKey = Tools.getSetByStrSplit(condition.get("hymx"));
		it = hymxKey.iterator();
		while (it.hasNext())
			yxmcQueryList.addAll(Tools.getSetByStrSplit(HM_HYMX.get(it.next())));
		HashSet<String> ybwsKey = Tools.getSetByStrSplit(condition.get("ybws"));
		it = ybwsKey.iterator();
		while (it.hasNext())
			yxmcQueryList.addAll(Tools.getSetByStrSplit(HM_YBWS.get(it.next())));
		HashSet<String> hyhpKey = Tools.getSetByStrSplit(condition.get("hyhp"));
		it = hyhpKey.iterator();
		while (it.hasNext())
			yxmcQueryList.addAll(Tools.getSetByStrSplit(HM_HYHP.get(it.next())));
		
		
		if (bkcl.indexOf("联合培养") != -1)
			yxmcQueryList.addAll(YX_LHPY);
		if (bkcl.indexOf("校中校") != -1)
			yxmcQueryList.addAll(YX_XZX);
		if (bkcl.indexOf("分校通道") != -1)
			yxmcQueryList.addAll(YX_FX);
		if (bkcl.indexOf("公安警校") != -1)
			yxmcQueryList.addAll(YX_GAJX);
		if (bkcl.indexOf("司法警校") != -1)
			yxmcQueryList.addAll(YX_SFJX);
		HashSet<String> yxtxKey = Tools.getSetByStrSplit(yxtx);
		it = yxtxKey.iterator();
		while (it.hasNext()) {
			String temp = it.next();
			if (temp.equals("985"))
				yxmcQueryList.addAll(CCache.CC_RY_985);
			if (temp.equals("211"))
				yxmcQueryList.addAll(CCache.CC_RY_211);
		}
		if (yxtx.indexOf("双一流") != -1)
			yxmcQueryList.addAll(CCache.CC_RY_SYL);
		if (yxtx.indexOf("强基计划") != -1)
			yxmcQueryList.addAll(CCache.CC_RY_QJ);
		String pName = HM_PROVINCE_CODE_NAME.get(studentProvince.substring(0, 2));
		
		String allMatchedYxmc = Tools.getSQLQueryin(yxmcQueryList);
		Tools.println("allMatchedYxmc = " + allMatchedYxmc);
		try {
			String SQLSum = "SELECT count(1) ";
			String SQLList = "SELECT x.*, y.*, z.*, k.yxmc as kpc ";
			String SQL = " FROM " + studentProvince + "_" + queryYear + "x x LEFT JOIN base_university y ON x.yxmc_org = y.yxmc LEFT JOIN base_university_baoyan z ON x.yxmc_org = z.`学校名称` LEFT JOIN data_university_kuapici_temp k ON x.yxmc_org = k.yxmc AND k.sf = '" + pName + "' WHERE x.xk_code like '%"+xk+"%' and ((x.zdf BETWEEN ? AND ? AND x.zdfwc BETWEEN ? AND ? ))";
			if (!Tools.isEmpty(allMatchedYxmc))
				SQL = String.valueOf(SQL) + " and x.yxmc_org in (" + allMatchedYxmc + ")";
			if (!Tools.isEmpty(yxcc))
				SQL = String.valueOf(SQL) + " and y.`办学类型` like '%" + yxcc + "%'";
			if (!Tools.isEmpty(bxlx))
				SQL = String.valueOf(SQL) + " and y.`办学性质` = '" + bxlx + "'";
			if (!Tools.isEmpty(yxlx))
				SQL = String.valueOf(SQL) + " and y.yxlx in (" + Tools.getSQLQueryin(Tools.getSetByStrSplit(yxlx))
						+ ")";
			if (!Tools.isEmpty(szdq))
				SQL = String.valueOf(SQL) + " and y.`省份` in (" + Tools.getSQLQueryin(Tools.getSetByStrSplit(szdq))
						+ ")";
			if (!Tools.isEmpty(yxzyName))
				SQL = String.valueOf(SQL) + " and (x.yxmc like ? or x.yxbz like ?)";
			if (bkcl.indexOf("公费师范生") != -1)
		          SQL = String.valueOf(SQL) + " and (x.zybz like '%公费师范%' or x.zybz like '%优师%' or x.zymc like '%公费师范%' or x.zymc like '%优师%')"; 
		        if (bkcl.indexOf("提前批") != -1)
		            SQL = String.valueOf(SQL) + " and (x.pc like '%提%')"; 
		        if (bkcl.indexOf("军警司法") != -1)
		            SQL = String.valueOf(SQL) + " and (x.yxmc like '%司法%' or x.yxmc like '%警察%' or x.yxmc like '%消防%' or x.yxmc like '%军%')"; 
		      
		      if (yxtx.indexOf("中外合作") != -1)
		        SQL = String.valueOf(SQL) + " and (x.yxmc like '%中外合作%' or x.zymc like '%中外合作%' %or zybz like '%中外合作%')"; 
		      if (yxtx.indexOf("小985") != -1)
		        SQL = String.valueOf(SQL) + " and (y.yxry like '%985%')"; 
		      if (yxtx.indexOf("小211") != -1)
		        SQL = String.valueOf(SQL) + " and (y.yxry like '%211%')"; 
		      
			Tools.println(SQL);
			conn = DatabaseUtils.getConnection();
			ps = conn.prepareStatement(String.valueOf(SQLSum) + SQL);
			ps.setInt(1, scoreDown);
			ps.setInt(2, (scoreUp < 10) ? 999999 : scoreUp);
			ps.setInt(3, wcDown);
			ps.setInt(4, (wcUp < 10) ? 999999 : wcUp);
			if (!Tools.isEmpty(yxzyName)) {
				ps.setString(5, "%" + yxzyName + "%");
				ps.setString(6, "%" + yxzyName + "%");
			}
			rs = ps.executeQuery();
			while (rs.next())
				recordCnt = rs.getInt(1);
			ps.close();
			rs.close();
			ps = null;
			rs = null;
			ps = conn.prepareStatement(String.valueOf(SQLList) + SQL + "  ORDER BY x.zdf DESC limit "
					+ ((pager - 1) * CCache.CC_PAGER_SIZE) + "," + CCache.CC_PAGER_SIZE);
			ps.setInt(1, scoreDown);
			ps.setInt(2, (scoreUp < 10) ? 999999 : scoreUp);
			ps.setInt(3, wcDown);
			ps.setInt(4, (wcUp < 10) ? 999999 : wcUp);
			if (!Tools.isEmpty(yxzyName)) {
				ps.setString(5, "%" + yxzyName + "%");
				ps.setString(6, "%" + yxzyName + "%");
			}
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			SchoolBean bean = null;
			while (rs.next()) {
				bean = new SchoolBean();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setYxdm(rs.getString("yxdm"));
				bean.setZdf(rs.getString("zdf"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setYxbz(rs.getString("yxbz"));
				bean.setZyz(rs.getString("zyz"));
				bean.setPc(rs.getString("pc"));
				bean.setXk(rs.getString("xk"));
				bean.setYxlx(rs.getString("院校类型"));
				bean.setBxxz(rs.getString("办学性质"));
				bean.setLsy(rs.getString("隶属于"));
				bean.setHyhp(rs.getString("hyhp"));
				bean.setHymx(rs.getString("hymx"));
				bean.setYbws(rs.getString("ybws"));
				bean.setIs985("是".equals(rs.getString("985工程")) ? "985" : null);
				bean.setIs211("是".equals(rs.getString("211工程")) ? "211" : null);
				bean.setIsSyl("是".equals(rs.getString("双一流")) ? "双一流" : null);
				bean.setIsQj("是".equals(rs.getString("强基")) ? "强基" : null);
				bean.setLsy(rs.getString("隶属于"));
				bean.setCs(rs.getString("城市"));
				bean.setQy(rs.getString("区域"));
				bean.setKuapc(rs.getString("kpc"));
				if (rs.getString("2023届免率") == null) {
					bean.setYxbyl((rs.getString("2022届推免率") == null) ? "--" : rs.getString("2022届推免率"));
				} else {
					bean.setYxbyl(rs.getString("2023届免率"));
				}
				bean.setRecordCnt(recordCnt);
				bean.setCurPageNum(pager);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public List<KaoyanBean> listKaoyanByCondition(HashMap<String, String> condition, int pager) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<KaoyanBean> list = new ArrayList<>();
		String sf = Tools.trim(condition.get("sf"));
		String km = Tools.trim(condition.get("km"));
		String yxzy = Tools.trim(condition.get("yxzy"));
		String ml = Tools.trim(condition.get("ml"));
		km = Tools.isEmpty(km) ? "%" : km;
		yxzy = Tools.isEmpty(yxzy) ? "%" : yxzy;
		pager = (pager < 1) ? 1 : pager;
		int recordCnt = 0;
		try {
			String SQLSum = "SELECT count(1) ";
			String SQLList = "SELECT * ";
			String SQL = " FROM base_university_major_kaoyan x WHERE 1 = 1 and (x.k1 like ? or x.k2 like ? or x.k3 like ? or x.k4 like ?) and (x.zymc like ? or x.xkmc like ? or x.yxmc like ?) ";
			if (!Tools.isEmpty(ml))
				SQL = String.valueOf(SQL) + " and x.mlmc in (" + Tools.getSQLQueryin(Tools.getSetByStrSplit(ml)) + ")";
			if (!Tools.isEmpty(sf))
				SQL = String.valueOf(SQL) + " and x.sf in (" + Tools.getSQLQueryin(Tools.getSetByStrSplit(sf)) + ")";
			Tools.println(SQL);
			conn = DatabaseUtils.getConnection();
			ps = conn.prepareStatement(String.valueOf(SQLSum) + SQL);
			ps.setString(1, "%" + km + "%");
			ps.setString(2, "%" + km + "%");
			ps.setString(3, "%" + km + "%");
			ps.setString(4, "%" + km + "%");
			ps.setString(5, "%" + yxzy + "%");
			ps.setString(6, "%" + yxzy + "%");
			ps.setString(7, "%" + yxzy + "%");
			rs = ps.executeQuery();
			while (rs.next())
				recordCnt = rs.getInt(1);
			ps.close();
			rs.close();
			ps = null;
			rs = null;
			String listSql = String.valueOf(SQLList) + SQL + "  ORDER BY x.yxmc limit "
					+ ((pager - 1) * CCache.CC_PAGER_SIZE) + "," + CCache.CC_PAGER_SIZE;
			ps = conn.prepareStatement(listSql);
			ps.setString(1, "%" + km + "%");
			ps.setString(2, "%" + km + "%");
			ps.setString(3, "%" + km + "%");
			ps.setString(4, "%" + km + "%");
			ps.setString(5, "%" + yxzy + "%");
			ps.setString(6, "%" + yxzy + "%");
			ps.setString(7, "%" + yxzy + "%");
			rs = ps.executeQuery();
			KaoyanBean bean = null;
			while (rs.next()) {
				bean = new KaoyanBean();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setBsd(rs.getString("bsd"));
				bean.setK1(rs.getString("k1"));
				bean.setK2(rs.getString("k2"));
				bean.setK3(rs.getString("k3"));
				bean.setK4(rs.getString("k4"));
				bean.setMlmc(rs.getString("mlmc"));
				bean.setSf(rs.getString("sf"));
				bean.setXkmc(rs.getString("xkmc"));
				bean.setXkpg(rs.getString("xkpg"));
				bean.setYjfx(rs.getString("yjfx"));
				bean.setYjsy(rs.getString("yjsy"));
				bean.setYxs(rs.getString("yxs"));
				bean.setZhx(rs.getString("zhx"));
				bean.setZymc(rs.getString("zymc"));
				bean.setSylxk(rs.getString("sylxk"));
				bean.setRecordCnt(recordCnt);
				bean.setCurPageNum(pager);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public List<BaoyanBean> listBaoyanByCondition(HashMap<String, String> condition, int pager) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<BaoyanBean> list = new ArrayList<>();
		String sf = Tools.trim(condition.get("sf"));
		String yxmc = Tools.trim(condition.get("yxmc"));
		String xyzy = Tools.trim(condition.get("xyzy"));
		yxmc = Tools.isEmpty(yxmc) ? "%" : yxmc;
		xyzy = Tools.isEmpty(xyzy) ? "%" : xyzy;
		pager = (pager < 1) ? 1 : pager;
		int recordCnt = 0;
		try {
			String SQLSum = "SELECT count(1) ";
			String SQLList = "SELECT * ";
			String SQL = " FROM base_major_baoyan x WHERE (x. yxmc like ?) and  (x.xymc like ? or x.zymc like ?) ";
			if (!Tools.isEmpty(sf))
				SQL = String.valueOf(SQL) + " and x.sf in (" + Tools.getSQLQueryin(Tools.getSetByStrSplit(sf)) + ")";
			Tools.println(SQL);
			conn = DatabaseUtils.getConnection();
			ps = conn.prepareStatement(String.valueOf(SQLSum) + SQL);
			ps.setString(1, "%" + yxmc + "%");
			ps.setString(2, "%" + xyzy + "%");
			ps.setString(3, "%" + xyzy + "%");
			rs = ps.executeQuery();
			while (rs.next())
				recordCnt = rs.getInt(1);
			ps.close();
			rs.close();
			ps = null;
			rs = null;
			String listSql = String.valueOf(SQLList) + SQL
					+ "  ORDER BY convert(x.qxbyrs, DECIMAL) desc, convert(x.xybyrs, DECIMAL) desc, convert(x.zybyrs, DECIMAL) desc limit "
					+ ((pager - 1) * CCache.CC_PAGER_SIZE) + "," + CCache.CC_PAGER_SIZE;
			ps = conn.prepareStatement(listSql);
			ps.setString(1, "%" + yxmc + "%");
			ps.setString(2, "%" + xyzy + "%");
			ps.setString(3, "%" + xyzy + "%");
			rs = ps.executeQuery();
			BaoyanBean bean = null;
			while (rs.next()) {
				bean = new BaoyanBean();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setQxbyrs(rs.getString("qxbyrs"));
				bean.setQxbyzs(rs.getString("qxbyzs"));
				bean.setQxbybl(rs.getString("qxbybl"));
				bean.setXymc(rs.getString("xymc"));
				bean.setXybyrs(rs.getString("xybyrs"));
				bean.setXybyzs(rs.getString("xybyzs"));
				bean.setXybybl(rs.getString("xybybl"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZybyrs(rs.getString("zybyrs"));
				bean.setZybyzs(rs.getString("zybyzs"));
				bean.setZybybl(rs.getString("zybybl"));
				bean.setSf(rs.getString("sf"));
				bean.setNf(rs.getString("nf"));
				bean.setRecordCnt(recordCnt);
				bean.setCurPageNum(pager);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public List<SchoolMarjorCommonBean> getYxZyByStudentsPersonalInfo(String zy, String xk, String studentProvince,
			String queryYear, int scoreDown, int scoreUp) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolMarjorCommonBean> list = new ArrayList<>();
		boolean isDalei = zy.substring(zy.length() - 2).indexOf("类") == -1 ? false : true;

		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT pc,yxmc, yxdm, zydm, zymc , zdf, zdfwc, xk  FROM "
					+ (String) HM_PROVINCE.get(studentProvince) + "_" + queryYear + " x WHERE xk_code like ? and "
					+ (isDalei ? "zymc like ?" : "zymc_org = ?")
					+ " and zdf between ? and ? order by zdf desc limit 0,200";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%"+xk+"%");
			if (isDalei) {
				ps.setString(2, zy.substring(0, zy.length() - 2) + "%");
			} else {
				ps.setString(2, zy);
			}
			ps.setInt(3, scoreDown);
			ps.setInt(4, scoreUp);
			rs = ps.executeQuery();
			SchoolMarjorCommonBean bean = null;
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZymc(rs.getString("zymc"));
				bean.setYxdm(rs.getString("yxdm"));
				bean.setZydm(rs.getString("zydm"));
				bean.setZdf(rs.getString("zdf"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setXk(rs.getString("xk"));
				bean.setPc(rs.getString("pc"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	

	public List<AdvInfoBean> getAllAdv(String orderBy) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<AdvInfoBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT *  FROM adv_info order by " + orderBy;
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			AdvInfoBean bean = null;
			while (rs.next()) {
				bean = new AdvInfoBean();
				bean.setNo(rs.getString("AD_NO"));
				bean.setName(rs.getString("AD_NAME"));
				bean.setRemark(rs.getString("AD_REMARK"));
				bean.setPage(rs.getString("AD_PAGE"));
				bean.setPosition(rs.getString("AD_POSITION"));
				bean.setContractExpDt(rs.getString("AD_COTR_EXP_DT"));
				bean.setFirstResvDT(rs.getString("AD_FIRST_RESV_DT"));
				bean.setFristResvAgent(rs.getString("AD_FIRST_RESV_AGENT"));
				bean.setPrice(rs.getString("AD_PRICE"));
				bean.setSignAgent(rs.getString("AD_SIGN_AGENT"));
				bean.setSignDt(rs.getString("AD_SIGN_DT"));
				bean.setSignPrice(rs.getString("AD_SIGN_PRICE"));
				bean.setSignUni(rs.getString("AD_SIGN_UNI"));
				bean.setStatus(rs.getInt("AD_STATUS"));
				bean.setSignAgentContact(rs.getString("AD_SIGN_AGENT_CONTACT"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public List<SchoolQJJHBean> getQJbyprovince(String province, int nf, int sort) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolQJJHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT *  FROM base_qjjh WHERE sf = ? and nf = ? ";
			if (sort == 1) {
				SQL = String.valueOf(SQL) + " order by rwfs desc";
			} else {
				SQL = String.valueOf(SQL) + " order by yxmc desc";
			}
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, province);
			ps.setInt(2, nf);
			rs = ps.executeQuery();
			SchoolQJJHBean bean = null;
			while (rs.next()) {
				bean = new SchoolQJJHBean();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZymc(rs.getString("zymc"));
				bean.setNf(rs.getString("nf"));
				bean.setPcx(rs.getString("pcx"));
				bean.setRwfs(rs.getString("rwfs"));
				bean.setSf(rs.getString("sf"));
				bean.setXc(rs.getString("xc"));
				bean.setXk(rs.getString("xk"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public List<SchoolQJJHBean> getQJbyprovince2(String yxmc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolQJJHBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM base_qjjh WHERE sf = '四川' and yxmc like ? order by yxmc, zymc, nf desc";

			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yxmc + "%");

			rs = ps.executeQuery();
			SchoolQJJHBean bean = null;
			while (rs.next()) {
				bean = new SchoolQJJHBean();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZymc(rs.getString("zymc"));
				bean.setNf(rs.getString("nf"));
				bean.setPcx(rs.getString("pcx"));
				bean.setRwfs(rs.getString("rwfs"));
				bean.setSf(rs.getString("sf"));
				bean.setXc(rs.getString("xc"));
				bean.setXk(rs.getString("xk"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public List<RecruitBean> getRecruit(String gsm, String zy, String dd, int pager) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<RecruitBean> list = new ArrayList<>();
		int recordCnt = 0;

		if (Tools.isEmpty(gsm)) {
			gsm = "%";
		}
		if (Tools.isEmpty(zy)) {
			zy = "%";
		}
		if (Tools.isEmpty(dd)) {
			dd = "%";
		}
		try {
			conn = DatabaseUtils.getConnection();
			String SQLCnt = "SELECT count(*) FROM base_recruit WHERE gsm like ? and zy like ? and (dd like ? or dd  = '全国' or dd= '多地') ";
			String SQL = "SELECT * FROM base_recruit WHERE gsm like ? and zy like ? and (dd like ? or dd  = '全国' or dd= '多地') order by SQ ASC limit "
					+ ((pager - 1) * CCache.CC_PAGER_SIZE) + ", " + CCache.CC_PAGER_SIZE;

			Tools.println(SQL);
			ps = conn.prepareStatement(SQLCnt);
			ps.setString(1, "%" + gsm + "%");
			ps.setString(2, "%" + zy + "%");
			ps.setString(3, "%" + dd + "%");
			rs = ps.executeQuery();
			while (rs.next()) {
				recordCnt = rs.getInt(1);
			}

			ps.close();

			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + gsm + "%");
			ps.setString(2, "%" + zy + "%");
			ps.setString(3, "%" + dd + "%");
			rs = ps.executeQuery();
			RecruitBean bean = null;
			while (rs.next()) {
				bean = new RecruitBean();
				bean.setDd(rs.getString("dd"));
				bean.setFbrq(rs.getString("fbrq"));
				bean.setGsm(rs.getString("gsm"));
				bean.setJzrq(rs.getString("jzrq"));
				bean.setLj(rs.getString("lj"));
				bean.setQyxz(rs.getString("qyxz"));
				bean.setZpdx(rs.getString("zpdx"));
				bean.setZplx(rs.getString("zplx"));
				bean.setZy(rs.getString("zy"));

				bean.setRecordCnt(recordCnt);
				bean.setCurPageNum(pager);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public List<ShoolYJBean> getYJbyprovince(String province, String yxmc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ShoolYJBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT *  FROM base_university_fake WHERE yxmc like ? and sf like ? order by sf";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + yxmc + "%");
			ps.setString(2, "%" + province + "%");
			rs = ps.executeQuery();
			ShoolYJBean bean = null;
			while (rs.next()) {
				bean = new ShoolYJBean();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setSf(rs.getString("sf"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public HashMap<Integer, String> getScoreDurByWcAndYear(String province, String xk, int wcDown, int wcUp, int year) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<Integer, String> hm = new HashMap<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT min(zdf) as zdfmin, max(zdf) as zdfmax  FROM " + (String) HM_PROVINCE.get(province)
					+ "_" + year + " WHERE (x.xk is NULL or x.xk = ? or x.xk like ?) and zdfwc between ? and ? ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "不限");
			ps.setString(2, "%" + xk + "%");
			ps.setInt(3, wcDown);
			ps.setInt(4, wcUp);
			rs = ps.executeQuery();
			while (rs.next()) {
				int zdfmin = rs.getInt("zdfmin");
				int zdfmax = rs.getInt("zdfmax");
				hm.put(Integer.valueOf(year), String.valueOf(zdfmin) + "," + zdfmax);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return hm;
	}

	public YGCardBean getYGCardByIDandPasswd(String id, String passwd) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		YGCardBean bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT *  FROM yg_card WHERE C_ID = ? and C_PASSWD = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, id.toUpperCase());
			ps.setString(2, passwd.toUpperCase());
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = new YGCardBean();
				bean.setId(rs.getString("C_ID"));
				bean.setPasswd(rs.getString("C_PASSWD"));
				bean.setPhone(rs.getString("C_PHONE"));
				bean.setProv(rs.getString("C_PROV"));
				bean.setXk(rs.getString("C_XK"));
				bean.setWc(rs.getInt("C_WC"));
				bean.setCnt(rs.getInt("C_CNT"));
				bean.setUsed(rs.getInt("C_USED"));
				bean.setStatus(rs.getInt("C_STATUS"));
				bean.setCreate(rs.getTimestamp("C_CREATE"));
				bean.setLastQuery(rs.getTimestamp("C_LAST_QUERY"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}

	public YGCardBean getYGCardByPhone(String phone) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		YGCardBean bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT *  FROM yg_card WHERE C_PHONE = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, phone);
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = new YGCardBean();
				bean.setId(rs.getString("C_ID"));
				bean.setPasswd(rs.getString("C_PASSWD"));
				bean.setPhone(rs.getString("C_PHONE"));
				bean.setProv(rs.getString("C_PROV"));
				bean.setXk(rs.getString("C_XK"));
				bean.setWc(rs.getInt("C_WC"));
				bean.setCnt(rs.getInt("C_CNT"));
				bean.setUsed(rs.getInt("C_USED"));
				bean.setStatus(rs.getInt("C_STATUS"));
				bean.setCreate(rs.getTimestamp("C_CREATE"));
				bean.setLastQuery(rs.getTimestamp("C_LAST_QUERY"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}

	public YGCardBean getYGCardByID(String cid) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		YGCardBean bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT *  FROM yg_card WHERE C_ID = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = new YGCardBean();
				bean.setId(rs.getString("C_ID"));
				bean.setPasswd(rs.getString("C_PASSWD"));
				bean.setPhone(rs.getString("C_PHONE"));
				bean.setProv(rs.getString("C_PROV"));
				bean.setXk(rs.getString("C_XK"));
				bean.setWc(rs.getInt("C_WC"));
				bean.setCnt(rs.getInt("C_CNT"));
				bean.setUsed(rs.getInt("C_USED"));
				bean.setStatus(rs.getInt("C_STATUS"));
				bean.setCreate(rs.getTimestamp("C_CREATE"));
				bean.setLastQuery(rs.getTimestamp("C_LAST_QUERY"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}

	public boolean updateYGCard(YGCardBean bean) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE yg_card SET C_PHONE = ?, C_PROV = ?, C_XK = ?, C_WC = ?, C_CNT = ?, C_USED = ?, C_STATUS = ?, C_LAST_QUERY = ? WHERE C_ID = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, bean.getPhone());
			ps.setString(2, bean.getProv());
			ps.setString(3, bean.getXk());
			ps.setInt(4, bean.getWc());
			ps.setInt(5, bean.getCnt());
			ps.setInt(6, bean.getUsed());
			ps.setInt(7, bean.getStatus());
			if (bean.getLastQuery() == null) {
				bean.setLastQuery(new Date());
			}
			ps.setTimestamp(8, new Timestamp(bean.getLastQuery().getTime()));

			ps.setString(9, bean.getId());
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}

	public boolean updateYGCardUsedOnly(String id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE yg_card SET C_LAST_QUERY = NOW() , C_USED = (C_USED + 1) WHERE C_ID = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, id);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}

	public boolean insertYGQuery(YGQueryBean bean) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "INSERT INTO yg_query(C_ID, Q_TYPE, Q_A, Q_B, Q_C, Q_ZYMC, Q_CREATE) VALUES(?,?,?,?,?,?,now())";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, bean.getCid());
			ps.setInt(2, bean.getType());
			ps.setString(3, bean.getQa());
			ps.setString(4, bean.getQb());
			ps.setString(5, bean.getQc());
			ps.setString(6, bean.getQzymc());

			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}

	public boolean insertZDKSScore(int nf, String sf, String cid, String cs, String kl, String zdpc, int zdscore) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into zdks_score_history(SH_ID, NF, SF, C_ID, CITY, SCORE, KL, ZDPC, CREATE_DT, STATUS) values(?,?,?,?,?,?,?,?,NOW(),1)";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, UUID.randomUUID().toString());
			ps.setInt(2, nf);
			ps.setString(3, sf);
			ps.setString(4, cid);
			ps.setString(5, cs);
			ps.setInt(6, zdscore);
			ps.setString(7, kl);
			ps.setString(8, zdpc);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}

	public boolean checkZDKSScoreExist(int nf, String sf, String cid, String cs, String kl, String pc, String score) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "select count(*) as cnt from zdks_score_history where sf = ? and c_id = ? and CITY = ? and KL = ? and ZDPC = ? and SCORE = ? and nf = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, sf);
			ps.setString(2, cid);
			ps.setString(3, cs);
			ps.setString(4, kl);
			ps.setString(5, pc);
			ps.setString(6, score);
			ps.setInt(7, nf);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("cnt") > 0;
			}
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}

	public int getZDKSScoreCnt(int nf, String sf, String cid, String cs, String kl, String zdpc, String zdScore) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "select count(*) as cnt from zdks_score_history where nf = ? and sf = ? and c_id = ? and CITY = ? and KL = ? and ZDPC = ? and SCORE <> ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, nf);
			ps.setString(2, sf);
			ps.setString(3, cid);
			ps.setString(4, cs);
			ps.setString(5, kl);
			ps.setString(6, zdpc);
			ps.setString(7, zdScore);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("cnt");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}

	public ZDKSFollowBean getFollowById(String cid, int fsId) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		ZDKSFollowBean bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zdks_score_follow_school WHERE C_ID = ? and FS_ID = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.setInt(2, fsId);
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = new ZDKSFollowBean();
				bean.setFsId(rs.getInt("FS_ID"));
				bean.setCid(rs.getString("C_ID"));
				bean.setCvtwc(rs.getString("CVTWC"));
				bean.setCvtzdf(rs.getString("CVTSCORE"));
				bean.setKl(rs.getString("KL"));
				bean.setPc(rs.getString("PC"));
				bean.setYxmc(rs.getString("YXMC"));
				bean.setZdcs(rs.getString("ZDCITY"));
				bean.setZdkl(rs.getString("ZDKL"));
				bean.setZdpc(rs.getString("ZDPC"));
				bean.setZdscore(rs.getString("ZDSCORE"));
				bean.setZymc(rs.getString("ZYMC"));
				bean.setCreate(rs.getTimestamp("CREATE_DT"));
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}

	public List<ZDKSFollowBean> listFollows(String cid, boolean isYX) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZDKSFollowBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.* FROM zdks_score_follow_school x WHERE x.C_ID = ? "
					+ (isYX ? "and x.ZYMC is null" : "and x.ZYMC is not null")
					+ " ORDER BY x.ZDPC desc, x.CREATE_DT DESC ,x.ZDSCORE DESC ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			rs = ps.executeQuery();
			ZDKSFollowBean bean = null;
			while (rs.next()) {
				bean = new ZDKSFollowBean();
				bean.setFsId(rs.getInt("FS_ID"));
				bean.setCid(rs.getString("C_ID"));
				bean.setCvtwc(rs.getString("CVTWC"));
				bean.setCvtzdf(rs.getString("CVTSCORE"));
				bean.setKl(rs.getString("KL"));
				bean.setPc(rs.getString("PC"));
				bean.setYxmc(rs.getString("YXMC"));
				bean.setZdcs(rs.getString("ZDCITY"));
				bean.setZdkl(rs.getString("ZDKL"));
				bean.setZdpc(rs.getString("ZDPC"));
				bean.setZdscore(rs.getString("ZDSCORE"));
				bean.setZymc(rs.getString("ZYMC"));
				bean.setCreate(rs.getTimestamp("CREATE_DT"));
				list.add(bean);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public int checkFollowStatus(String cid, String yxmc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		YGCardBean bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zdks_score_follow_school WHERE C_ID = ? and YXMC = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.setString(2, yxmc);
			rs = ps.executeQuery();
			while (rs.next()) {
				return 1;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return -1;
	}

	public int checkFollowStatus(String cid, String zdsf, String zdcs, String zdkl, String zdpc, int zdscore, String kl,
			String pc, String yxmc, String zymc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		YGCardBean bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			if (Tools.isEmpty(zymc)) {
				String SQL = "SELECT * FROM zdks_score_follow_school WHERE C_ID = ? and SF = ? and ZDCITY = ? and ZDPC = ? and ZDKL = ? and ZDSCORE = ? and YXMC = ? and KL = ? and PC = ?";
				Tools.println(SQL);
				ps = conn.prepareStatement(SQL);
				ps.setString(1, cid);
				ps.setString(2, zdsf);
				ps.setString(3, zdcs);
				ps.setString(4, zdpc);
				ps.setString(5, zdkl);
				ps.setInt(6, zdscore);
				ps.setString(7, yxmc);
				ps.setString(8, kl);
				ps.setString(9, pc);
				rs = ps.executeQuery();
				while (rs.next()) {
					return 1;
				}
			} else {
				String SQL = "SELECT * FROM zdks_score_follow_school WHERE C_ID = ? and SF = ? and ZDCITY = ? and ZDPC = ? and ZDKL = ? and ZDSCORE = ? and YXMC = ? and KL = ? and PC = ? and ZYMC = ?";
				Tools.println(SQL);
				ps = conn.prepareStatement(SQL);
				ps.setString(1, cid);
				ps.setString(2, zdsf);
				ps.setString(3, zdcs);
				ps.setString(4, zdpc);
				ps.setString(5, zdkl);
				ps.setInt(6, zdscore);
				ps.setString(7, yxmc);
				ps.setString(8, kl);
				ps.setString(9, pc);
				ps.setString(10, zymc);
				rs = ps.executeQuery();
				while (rs.next()) {
					return 1;
				}
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return -1;
	}

	public int checkSchoolFollowStatus(String cid, String yxmc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		YGCardBean bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zdks_score_follow_school WHERE C_ID = ? and yxmc = ? ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.setString(2, yxmc);
			rs = ps.executeQuery();
			while (rs.next()) {
				return 1;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return -1;
	}

	public static int BLOCK_PARAM_CNT_FOR_CUSTOMER = 15;
	public static int BLOCK_PARAM_CNT_FOR_SCA = 25;
	public int checkBlockCount(String cid, String action) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		YGCardBean bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT count(*) as ct FROM zdks_block WHERE C_ID = ? and FROM_ACTION = ? and DATE_FORMAT(CREATE_DT, '%Y%m%d%H%i') = DATE_FORMAT(now(), '%Y%m%d%H%i')";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.setString(2, action);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("ct");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return -1;
	}

	public boolean insertBlockCount(String cid, String action, String spec) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into zdks_block(FROM_ACTION, SPEC_VALUE, C_ID, CREATE_DT) values(?,?,?,NOW())";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, action);
			ps.setString(2, spec);
			ps.setString(3, cid);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}

	public boolean deleteFollow(String cid, int fsId) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from zdks_score_follow_school where C_ID = ? and FS_ID = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.setInt(2, fsId);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}

	public boolean executeSQL(String SQL) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}

	public boolean insertZDKSFollowSchool(String cid, String zdsf, String zdcs, String zdkl, String zdpc, int zdscore,
			int cvtzdf, int cvtwc, String kl, String pc, String yxmc, String zymc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into zdks_score_follow_school(SF, C_ID, ZDCITY, ZDPC, ZDKL, ZDSCORE, CVTSCORE, CVTWC, YXMC, KL, PC, zymc, CREATE_DT) values(?,?,?,?,?,?,?,?,?,?,?,?,NOW())";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, zdsf);
			ps.setString(2, cid);
			ps.setString(3, zdcs);
			ps.setString(4, zdpc);
			ps.setString(5, zdkl);
			ps.setInt(6, zdscore);
			ps.setInt(7, cvtzdf);
			ps.setInt(8, cvtwc);
			ps.setString(9, yxmc);
			ps.setString(10, kl);
			ps.setString(11, pc);
			ps.setString(12, Tools.isEmpty(zymc) ? null : zymc);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean insertZDKSFollowSchool(String cid, String sf, String yxmc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into zdks_score_follow_school(SF, C_ID, YXMC, CREATE_DT) values(?,?,?,NOW())";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, sf);
			ps.setString(2, cid);
			ps.setString(3, yxmc);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}

	public int getZDKSScoreConvert(int year, String sfCode, String sf, String city, String kl, String zdpc, int score) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		YGCardBean bean = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT *  FROM "+sfCode+"_zdks_score_convert WHERE nf = ? and sf = ? and city = ? and kl_code like ? and ZDPC = ? and score_from = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, year);
			ps.setString(2, sf);
			ps.setString(3, city);
			ps.setString(4, "%"+kl+"%");
			ps.setString(5, zdpc);
			ps.setInt(6, score);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("SCORE_TO");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return -1;
	}
	
	public ZyzdScoreConvert getZDKSScoreConvertBean(int year, String sfCode, String sf, String city, String kl, String zdpc, int score) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT *  FROM "+sfCode+"_zdks_score_convert WHERE nf = ? and sf = ? and city = ? and kl_code like ? and ZDPC = ? and score_from = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, year);
			ps.setString(2, sf);
			ps.setString(3, city);
			ps.setString(4, "%"+kl+"%");
			ps.setString(5, zdpc);
			ps.setInt(6, score);
			rs = ps.executeQuery();
			ZyzdScoreConvert bean = null;
			while (rs.next()) {
				bean = new ZyzdScoreConvert(); 
				bean.setNf(rs.getInt("nf"));
				bean.setSf(rs.getString("sf"));
				bean.setCity(rs.getString("city"));
				bean.setZdpc(rs.getString("zdpc"));
				bean.setKl(rs.getString("kl"));
				bean.setKl_code(rs.getString("kl_code"));
				bean.setScore_from(rs.getInt("score_from"));
				bean.setScore_to(rs.getInt("score_to"));
				bean.setHxb_id(rs.getString("hxb_id"));
				return bean;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}

	public ZDKSRank getZDKSNearestScoreByWC(int year, String sf, String kl, int givingWC) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		ZDKSRank bean = null;
		try {
			ZyzdProvince zyzdProvince = ZyzdCache.getUserCardProvinceName(sf);
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+zyzdProvince.getTableName()+"_zdks_rank WHERE nf = ? and sf = ? and kl_code like ? and WC >= ? order by WC ASC LIMIT 1";
			Tools.printlnRanking(SQL+","+year+","+kl+","+givingWC);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, year);
			ps.setString(2, sf);
			ps.setString(3, "%"+kl+"%");
			ps.setInt(4, givingWC);
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = new ZDKSRank();
				bean.setCnt(rs.getInt("cnt"));
				bean.setFd(rs.getString("fd"));
				bean.setKl(rs.getString("kl"));
				bean.setNf(rs.getInt("nf"));
				bean.setScore(rs.getInt("SCORE"));
				bean.setSf(rs.getString("sf"));
				bean.setWc(rs.getInt("wc"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}
	
	public HashMap<String, ZDKSRank> getAllTongWF(String sf, String kl, int givingWC) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, ZDKSRank> BEAN_MAP = new HashMap<>();
		try {
			ZyzdProvince zyzdProvince = ZyzdCache.getUserCardProvinceName(sf);
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+zyzdProvince.getTableName()+"_zdks_rank WHERE  sf = ? and kl_code LIKE ? and (WC-CNT) < ? AND WC >= ? ";
			Tools.printlnRanking(SQL+","+sf+","+kl+","+givingWC);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, sf);
			ps.setString(2, "%"+kl+"%");
			ps.setInt(3, givingWC);
			ps.setInt(4, givingWC);
			rs = ps.executeQuery();
			while (rs.next()) {
				ZDKSRank bean = new ZDKSRank();
				bean.setCnt(rs.getInt("cnt"));
				bean.setFd(rs.getString("fd"));
				bean.setKl(rs.getString("kl"));
				bean.setNf(rs.getInt("nf"));
				bean.setScore(rs.getInt("SCORE"));
				bean.setSf(rs.getString("sf"));
				bean.setWc(rs.getInt("wc"));
				BEAN_MAP.put(String.valueOf(bean.getNf()), bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return BEAN_MAP;
	}
	

	public ZDKSRank getZDKSWCByScore(int year, String sf, String kl, int score) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		ZDKSRank bean = null;
		try {
			ZyzdProvince zyzdProvince = ZyzdCache.getUserCardProvinceName(sf);
			
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM "+zyzdProvince.getTableName()+"_zdks_rank WHERE nf = ? and sf = ? and kl_code like ? and score = ?";
			Tools.printlnRanking(SQL+","+year+","+kl+","+score);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, year);
			ps.setString(2, sf);
			ps.setString(3, "%"+kl+"%");
			ps.setInt(4, score);
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = new ZDKSRank();
				bean.setCnt(rs.getInt("cnt"));
				bean.setFd(rs.getString("fd"));
				bean.setKl(rs.getString("kl"));
				bean.setNf(rs.getInt("nf"));
				bean.setScore(rs.getInt("SCORE"));
				bean.setSf(rs.getString("sf"));
				bean.setWc(rs.getInt("wc"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}
	
	
	public List<SchoolBean> getZDKSSchoolBySfAndYearAndArea(String sfCode, int year, String schoolSF, String kl, int scoreFrom,
			int scoreTo, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolBean> list = new ArrayList<SchoolBean>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT distinct x.* ,y.bxxz,y.yxlx FROM "+sfCode+"_" + year
					+ "x x left join base_university y on x.yxmc = y.yxmc WHERE x.nf = ? and y.sf like ? and x.xk_code like ? and x.zdf between ? and ? ORDER BY x.zdf DESC limit ?,?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, year);
			ps.setString(2, "%" + schoolSF + "%");
			ps.setString(3, "%" + kl + "%");
			ps.setInt(4, scoreFrom);
			ps.setInt(5, scoreTo);

			ps.setInt(6, (pageNumber - 1) * 10); // 1开始
			ps.setInt(7, 20);

			rs = ps.executeQuery();
			SchoolBean sb = null;
			while (rs.next()) {
				sb = new SchoolBean();
				sb.setYxmc(rs.getString("yxmc"));
				sb.setYxmc_org(rs.getString("yxmc_org"));
				sb.setSf(rs.getString("yxsf"));
				sb.setXk(rs.getString("XK"));
				sb.setPc(rs.getString("PC"));
				sb.setNf(year);
				sb.setZdf(rs.getString("ZDF"));
				sb.setZdfwc(rs.getString("ZDFWC"));
				sb.setYxlx(rs.getString("yxlx"));
				sb.setBxxz(rs.getString("bxxz"));
				sb.setYxbz(rs.getString("yxbz"));
				list.add(sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<SchoolBean> getZDKSSchoolBySfAndYearAndArea(String sfCode, int year, String schoolSF, String schoolYXLX, String kl, int scoreFrom,
			int scoreTo, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolBean> list = new ArrayList<SchoolBean>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT distinct x.* ,y.bxxz,y.yxlx FROM "+sfCode+"_" + year
					+ "x x left join base_university y on x.yxmc = y.yxmc WHERE y.yxlx like ? and x.nf = ? and y.sf like ? and x.xk_code like ? and x.zdf between ? and ? ORDER BY x.zdf DESC limit ?,?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + schoolYXLX + "%");
			ps.setInt(2, year);
			ps.setString(3, "%" + schoolSF + "%");
			ps.setString(4, "%" + kl + "%");
			ps.setInt(5, scoreFrom);
			ps.setInt(6, scoreTo);

			ps.setInt(7, (pageNumber - 1) * 10); // 1开始
			ps.setInt(8, 20);

			rs = ps.executeQuery();
			SchoolBean sb = null;
			while (rs.next()) {
				sb = new SchoolBean();
				sb.setYxmc(rs.getString("yxmc"));
				sb.setYxmc_org(rs.getString("yxmc_org"));
				sb.setSf(rs.getString("yxsf"));
				sb.setXk(rs.getString("XK"));
				sb.setPc(rs.getString("PC"));
				sb.setNf(year);
				sb.setZdf(rs.getString("ZDF"));
				sb.setZdfwc(rs.getString("ZDFWC"));
				sb.setYxlx(rs.getString("yxlx"));
				sb.setBxxz(rs.getString("bxxz"));
				sb.setYxbz(rs.getString("yxbz"));
				list.add(sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<SchoolBean> getZDKSSchoolBySfAndYearAndArea2(String sfCode, String searchSchoolName,int year, String schoolSF, String schoolYXLX, String kl, int scoreFrom,
			int scoreTo, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolBean> list = new ArrayList<SchoolBean>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT distinct x.* ,y.bxxz,y.yxlx FROM "+sfCode+"_" + year
					+ "x x left join base_university y on x.yxmc = y.yxmc WHERE y.yxlx like ? and x.nf = ? and y.sf like ? and x.xk_code like ? and x.zdf between ? and ? and x.yxmc like ? ORDER BY x.zdf DESC limit ?,?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + schoolYXLX + "%");
			ps.setInt(2, year);
			ps.setString(3, "%" + schoolSF + "%");
			ps.setString(4, "%" + kl + "%");
			ps.setInt(5, scoreFrom);
			ps.setInt(6, scoreTo);
			

			ps.setString(7, "%" + (Tools.isEmpty(searchSchoolName)? "" : searchSchoolName) + "%");

			ps.setInt(8, (pageNumber - 1) * 20); // 1开始
			ps.setInt(9, 20);

			rs = ps.executeQuery();
			SchoolBean sb = null; 
			while (rs.next()) {
				sb = new SchoolBean();
				sb.setYxmc(rs.getString("yxmc"));
				sb.setYxdm(rs.getString("yxdm"));
				if(!Tools.isEmpty(sb.getYxdm())) {
					String temp = "0000" + sb.getYxdm();
					sb.setYxdm(temp.substring(temp.length() - 4));
				}
				sb.setYxmc_org(rs.getString("yxmc_org"));
				sb.setSf(rs.getString("yxsf"));
				sb.setXk(rs.getString("XK"));
				sb.setPc(rs.getString("PC"));
				sb.setZyz(rs.getString("zyz"));
				sb.setNf(year);
				sb.setZdf(rs.getString("ZDF"));
				sb.setZdfwc(rs.getString("ZDFWC"));
				sb.setYxlx(rs.getString("yxlx"));
				sb.setBxxz(rs.getString("bxxz"));
				sb.setYxbz(rs.getString("yxbz"));
				list.add(sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public HashMap<String, List<SchoolBean>> getALLYxmcORGCode(String sfCode,String year, HashSet<String> nameSets, String xk_code) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, List<SchoolBean>> MAP = new HashMap<>();
		try {
			String yxmcOrgSQl = nameSets.size() == 0 ? "" : "x.yxmc_org IN ("+Tools.getSQLQueryin(nameSets)+") AND";
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT yxmc_org, yxdm, pc, zdf,zdfwc,yxbz,zyz FROM "+sfCode+"_"+year+"x x WHERE "+yxmcOrgSQl+" xk_code LIKE ? GROUP BY yxmc_org, yxdm, pc, zdf,zdfwc,yxbz,zyz";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + xk_code + "%");

			rs = ps.executeQuery();
			SchoolBean sb = null; 
			while (rs.next()) {
				sb = new SchoolBean();
				sb.setYxmc_org(rs.getString("yxmc_org"));
				sb.setYxdm(rs.getString("yxdm"));
				if(!Tools.isEmpty(sb.getYxdm())) {
					String temp = "0000" + sb.getYxdm();
					sb.setYxdm(temp.substring(temp.length() - 4));
				}

				sb.setPc(rs.getString("PC"));
				sb.setZdf(rs.getString("ZDF"));
				sb.setZdfwc(rs.getString("ZDFWC"));
				sb.setYxbz(rs.getString("yxbz"));
				sb.setZyz(rs.getString("zyz"));
				
				List<SchoolBean> list = null;
				if(MAP.containsKey(sb.getYxmc_org())) {
					list = MAP.get(sb.getYxmc_org());
				}else {
					list = new ArrayList<SchoolBean>();
				}
				list.add(sb);
				MAP.put(sb.getYxmc_org(), list);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return MAP;
	}
	
	public List<SchoolBean> getZDKSSchoolBySfAndYearAndAreaCQ(int year, String schoolSF, String kl, int scoreFrom,
			int scoreTo, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolBean> list = new ArrayList<SchoolBean>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT distinct x.* ,y.bxxz FROM c1_cq_" + year
					+ "x x left join base_university y on x.yxmc = y.yxmc WHERE x.nf = ? and y.sf like ? and x.xk = ? and x.zdf between ? and ? ORDER BY x.zdf DESC limit ?,?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, year);
			ps.setString(2, "%" + schoolSF + "%");
			ps.setString(3, kl);
			ps.setInt(4, scoreFrom);
			ps.setInt(5, scoreTo);

			ps.setInt(6, pageNumber * 10); // 0开始
			ps.setInt(7, 10);

			rs = ps.executeQuery();
			SchoolBean sb = null;
			while (rs.next()) {
				sb = new SchoolBean();
				sb.setYxmc(rs.getString("yxmc"));
				sb.setSf(rs.getString("yxsf"));
				sb.setXk(rs.getString("XK"));
				sb.setPc(rs.getString("PC"));
				sb.setNf(year);
				sb.setZdf(rs.getString("ZDF"));
				sb.setZdfwc(rs.getString("ZDFWC"));
				sb.setYxlx(rs.getString("bxxz"));
				sb.setYxbz(rs.getString("yxbz"));
				list.add(sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<SchoolBean> getZDKSSchoolBySfAndYearAndAreaGZ(int year, String schoolSF, String kl, int scoreFrom,
			int scoreTo, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolBean> list = new ArrayList<SchoolBean>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT distinct x.* ,y.bxxz FROM g4_gz_" + year
					+ "x x left join base_university y on x.yxmc = y.yxmc WHERE x.nf = ? and y.sf like ? and x.xk = ? and x.zdf between ? and ? ORDER BY x.zdf DESC limit ?,?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, year);
			ps.setString(2, "%" + schoolSF + "%");
			ps.setString(3, kl);
			ps.setInt(4, scoreFrom);
			ps.setInt(5, scoreTo);

			ps.setInt(6, pageNumber * 10); // 0开始
			ps.setInt(7, 10);

			rs = ps.executeQuery();
			SchoolBean sb = null;
			while (rs.next()) {
				sb = new SchoolBean();
				sb.setYxmc(rs.getString("yxmc"));
				sb.setSf(rs.getString("yxsf"));
				sb.setXk(rs.getString("XK"));
				sb.setPc(rs.getString("PC"));
				sb.setNf(year);
				sb.setZdf(rs.getString("ZDF"));
				sb.setZdfwc(rs.getString("ZDFWC"));
				sb.setYxlx(rs.getString("bxxz"));
				sb.setYxbz(rs.getString("yxbz"));
				list.add(sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public int getZDKSSchoolBySfAndYearAndAreaSUM(int year, String schoolSF, String kl, int scoreFrom, int scoreTo) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolBean> list = new ArrayList<SchoolBean>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT count(distinct concat(x.yxmc, x.yxbz)) FROM s5_sc_" + year
					+ "x x left join base_university y on x.yxmc = y.yxmc WHERE x.nf = ? and y.sf like ? and x.xk = ? and x.zdf between ? and ? ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, year);
			ps.setString(2, "%" + schoolSF + "%");
			ps.setString(3, kl);
			ps.setInt(4, scoreFrom);
			ps.setInt(5, scoreTo);

			rs = ps.executeQuery();
			SchoolBean sb = null;
			while (rs.next()) {
				return rs.getInt(1);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}

	public List<SchoolMarjorCommonBean> getZDKSMajorBySfAndYearAndArea(int year, String zymc, String schoolSF,
			String kl, int scoreFrom, int scoreTo, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolMarjorCommonBean> list = new ArrayList<SchoolMarjorCommonBean>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.*, y.bxxz, fs.C_ID as fsyxmc FROM s5_sc_" + year
					+ " x left join base_university y on x.yxmc = y.yxmc LEFT JOIN zdks_score_follow_school fs ON x.yxmc = fs.yxmc and x.zymc = fs.zymc and x.xk = fs.kl and x.pc = fs.pc WHERE x.nf = ? and y.sf like ? and x.zymc like ? and x.xk = ? and x.zdf between ? and ? ORDER BY x.zdf DESC limit ?,?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, year);
			ps.setString(2, "%" + schoolSF + "%");
			ps.setString(3, "%" + zymc + "%");
			ps.setString(4, kl);
			ps.setInt(5, scoreFrom);
			ps.setInt(6, scoreTo);

			ps.setInt(7, pageNumber); // 0开始
			ps.setInt(8, (pageNumber + 1) * 10);

			rs = ps.executeQuery();
			SchoolMarjorCommonBean sb = null;
			while (rs.next()) {
				sb = new SchoolMarjorCommonBean();
				sb.setYxmc(rs.getString("yxmc"));
				sb.setZymc(rs.getString("zymc"));
				sb.setSf(rs.getString("yxsf"));
				sb.setXk(rs.getString("XK"));
				sb.setPc(rs.getString("PC"));
				sb.setNf(year);
				sb.setZdf(rs.getString("ZDF"));
				sb.setZdfwc(rs.getString("ZDFWC"));
				sb.setYxlx(rs.getString("bxxz"));
				sb.setYxdm(rs.getString("fsyxmc"));
				list.add(sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public int getZDKSMajorBySfAndYearAndAreaSUM(int year, String zymc, String schoolSF, String kl, int scoreFrom,
			int scoreTo) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolMarjorCommonBean> list = new ArrayList<SchoolMarjorCommonBean>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT count(*) FROM s5_sc_" + year
					+ " x left join base_university y on x.yxmc = y.yxmc LEFT JOIN zdks_score_follow_school fs ON x.yxmc = fs.yxmc and x.zymc = fs.zymc and x.xk = fs.kl and x.pc = fs.pc WHERE x.nf = ? and y.sf like ? and x.zymc like ? and x.xk = ? and x.zdf between ? and ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, year);
			ps.setString(2, "%" + schoolSF + "%");
			ps.setString(3, "%" + zymc + "%");
			ps.setString(4, kl);
			ps.setInt(5, scoreFrom);
			ps.setInt(6, scoreTo);
			rs = ps.executeQuery();
			SchoolMarjorCommonBean sb = null;
			while (rs.next()) {
				return rs.getInt(1);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}

	public List<ZDKSKggk> getZDKSKggk(int year, String zymc, String gzdd, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZDKSKggk> list = new ArrayList<ZDKSKggk>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT x.nf_real as nf, x.kslx as kslx, x.bmmc, x.yrsj, COUNT(*) as ct,SUM(x.bkrs) as sum FROM career_kg x WHERE x.gzdd like ? and x.nf = ? and x.zy LIKE ? GROUP BY x.nf_real, x.kslx, x.bmmc, x.yrsj ORDER BY x.nf_real desc, count(*) DESC limit ?,?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + gzdd + "%");
			ps.setInt(2, year);
			ps.setString(3, "%" + zymc + "%");
			ps.setInt(4, (pageNumber - 1) * 20);
			ps.setInt(5, 20);
			rs = ps.executeQuery();
			ZDKSKggk sb = null;
			while (rs.next()) {
				sb = new ZDKSKggk();
				sb.setBmmc(rs.getString("bmmc"));
				sb.setYrsj(rs.getString("yrsj"));
				sb.setCnt(rs.getInt("ct"));
				sb.setSum(rs.getInt("sum"));
				sb.setNf(rs.getString("nf"));
				sb.setKslx(rs.getString("kslx"));
				list.add(sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public int getZDKSKggkCnt(int year, String zymc, String gzdd) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZDKSKggk> list = new ArrayList<ZDKSKggk>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT COUNT(*) FROM (SELECT x.bmmc, x.yrsj, COUNT(*) as ct,SUM(x.bkrs) as sum FROM career_kg x WHERE x.gzdd like ? and x.nf = ? and x.zy LIKE ? GROUP BY x.bmmc, x.yrsj) F";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + gzdd + "%");
			ps.setInt(2, year);
			ps.setString(3, "%" + zymc + "%");
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt(1);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public List<ZDKSKggk> getZDKSKggkList(int year, String yrsj, String gzdd, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZDKSKggk> list = new ArrayList<ZDKSKggk>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM career_kg x WHERE x.nf = ? and (yrsj like ? OR jgxz like ? OR bmmc like ? OR gzdd like ?) and x.gzdd like ? order by x.bkrs desc,x.yrsj, x.bkzw limit ?,?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, year);
			ps.setString(2, "%" + yrsj + "%");
			ps.setString(3, "%" + yrsj + "%");
			ps.setString(4, "%" + yrsj + "%");
			ps.setString(5, "%" + yrsj + "%");
			ps.setString(6, "%" + gzdd + "%");
			ps.setInt(7, (pageNumber - 1) * 20);
			ps.setInt(8, 20);
			rs = ps.executeQuery();
			ZDKSKggk sb = null;
			while (rs.next()) {
				sb = new ZDKSKggk();
				sb.setBkrs(rs.getString("bkrs"));
				sb.setBkzw(rs.getString("bkzw"));
				sb.setBmdm(rs.getString("bmdm"));
				sb.setBmmc(rs.getString("bmmc"));
				sb.setBz(rs.getString("bz"));
				sb.setSf(rs.getString("sf"));
				sb.setGwly(rs.getString("gwly"));
				sb.setGzdd(rs.getString("gzdd"));
				sb.setJcgzjl(rs.getString("jcgzjl"));
				sb.setJcgznx(rs.getString("jcgznx"));
				sb.setJgcj(rs.getString("jgcj"));
				sb.setJgxz(rs.getString("jgxz"));
				sb.setKslb(rs.getString("kslb"));
				sb.setKslx(rs.getString("kslx"));
				sb.setLhdd(rs.getString("lhdd"));
				sb.setMsbl(rs.getString("msbl"));
				sb.setMszycs(rs.getString("mszycs"));
				sb.setNf(rs.getString("nf"));
				sb.setNf_real(rs.getString("nf_real"));
				sb.setXl(rs.getString("xl"));
				sb.setXw(rs.getString("xw"));
				sb.setYrsj(rs.getString("yrsj"));
				sb.setZwdm(rs.getString("zwdm"));
				sb.setZwfb(rs.getString("zwfb"));
				sb.setZwjj(rs.getString("zwjj"));
				sb.setZwsx(rs.getString("zwsx"));
				sb.setZy(rs.getString("zy"));
				sb.setZy_org(rs.getString("zy_org"));
				sb.setZzmm(rs.getString("zzmm"));
				list.add(sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<String> getZDKSKggkListMostMajor(int year, String yrsj, String gzdd) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<String> list = new ArrayList<String>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT zy FROM career_kg x WHERE x.nf = ? and (yrsj like ? OR jgxz like ? OR bmmc like ? OR gzdd like ?) and x.gzdd like ? ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, year);
			ps.setString(2, "%" + yrsj + "%");
			ps.setString(3, "%" + yrsj + "%");
			ps.setString(4, "%" + yrsj + "%");
			ps.setString(5, "%" + yrsj + "%");
			ps.setString(6, "%" + gzdd + "%");
			rs = ps.executeQuery();
			String sb = null;
			while (rs.next()) {
				list.add(rs.getString("zy"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public List<ZDKSKHow> getZDKSKHow(int year, String khType, String keyword) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZDKSKHow> list = new ArrayList<ZDKSKHow>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zdks_khow x WHERE x.nf = ? and (x.TITLE LIKE ? OR x.CONTENT LIKE ?) and x.KH_TYPE = ? ORDER BY x.sort desc, x.PUBLISH_DT DESC";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, year);
			ps.setString(2, "%" + keyword + "%");
			ps.setString(3, "%" + keyword + "%");
			ps.setString(4, khType);
			rs = ps.executeQuery();
			ZDKSKHow sb = null;
			while (rs.next()) {
				sb = new ZDKSKHow();
				sb.setContent(rs.getString("CONTENT"));
				sb.setCreateDt(rs.getTimestamp("CREATE_DT"));
				sb.setDocUrl(rs.getString("DOC_URL"));
				sb.setKhId(rs.getInt("KH_ID"));
				sb.setKhType(rs.getString("KH_TYPE"));
				sb.setNf(rs.getInt("NF"));
				sb.setPublishDt(rs.getTimestamp("PUBLISH_DT"));
				sb.setTitle(rs.getString("TITLE"));

				list.add(sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public static HashMap<String, List<ZDPCBean>> getAllCitiesZDPCAsMap() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, List<ZDPCBean>> cityMap = new HashMap<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zdks_city_zdpc order by sf_name asc,sort desc";
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			ZDPCBean bean = null;
			while (rs.next()) {
				bean = new ZDPCBean();
				bean.setZdpc(rs.getString("zdpc"));
				bean.setSfCode(rs.getString("sf_code"));
				bean.setSfName(rs.getString("sf_name"));
				
				String key = bean.getSfName();
				if(cityMap.containsKey(key)) {
					List<ZDPCBean> list = cityMap.get(key);
					list.add(bean);
					cityMap.put(key, list);
				}else {
					List<ZDPCBean> list = new ArrayList<>();
					list.add(bean);
					cityMap.put(key, list);
				}
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return cityMap;
	}
	
	
	public static HashMap<String, List<CityBean>> getAllCitiesAsMap() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, List<CityBean>> cityMap = new HashMap<>();

		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zdks_city order by sf_name asc, sort desc";
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			CityBean bean = null;
			while (rs.next()) {
				bean = new CityBean();
				bean.setCityCode(rs.getString("city_code"));
				bean.setCityName(rs.getString("city_name"));
				bean.setSfCode(rs.getString("sf_code"));
				bean.setSfName(rs.getString("sf_name"));
				bean.setCityNameExt(rs.getString("city_name_ext"));
				
				String key = bean.getSfName();
				if(cityMap.containsKey(key)) {
					List<CityBean> list = cityMap.get(key);
					list.add(bean);
					cityMap.put(key, list);
				}else {
					List<CityBean> list = new ArrayList<>();
					list.add(bean);
					cityMap.put(key, list);
				}
			
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return cityMap;
	}
	
	
	public static List<String> getAllSFList() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<String> sfList = new ArrayList<>();

		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT DISTINCT x.SF_NAME FROM zdks_city x";
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			while (rs.next()) {
				sfList.add(rs.getString(1));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return sfList;
	}
	
	public static HashMap<String, SchoolBean> getAllSchoolsAsMap() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, SchoolBean> schoolMap = new HashMap<>();

		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM BASE_UNIVERSITY";
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			SchoolBean bean = null;
			while (rs.next()) {
				bean = new SchoolBean();

				bean.setYxmc(rs.getString("yxmc"));
				bean.setYbws(rs.getString("ybws"));// 原部委属
				bean.setYxlx(rs.getString("yxlx"));// 理工,综合
				bean.setHyhp(rs.getString("yxmc")); // 行业黄埔
				bean.setHymx(rs.getString("yxmc")); // 行业名校

				bean.setIs211(rs.getString("211工程"));
				bean.setIs985(rs.getString("985工程"));
				bean.setIsQj(rs.getString("强基"));
				bean.setIsSyl(rs.getString("双一流"));
				bean.setBxxz(rs.getString("yxmc")); // 公办，民办

				bean.setSf(rs.getString("sf"));
				bean.setCs(rs.getString("cs"));
				bean.setQy(rs.getString("qy"));

				bean.setBsd(rs.getString("博士点"));
				bean.setSsd(rs.getString("硕士点"));
				bean.setGjzdxk(rs.getString("国家重点学科"));
				bean.setZdsys(rs.getString("重点实验室"));

				bean.setQs(rs.getString("QS排行"));
				bean.setLmgx(rs.getString("lmgx"));

				bean.setJj(rs.getString("简介"));
				bean.setLsy(rs.getString("隶属于"));

				schoolMap.put(bean.getYxmc(), bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return schoolMap;
	}
	
	
	
	public List<ZDKSXkBean> listXuanKe(String sf, String yxsf, String xk, String ts, String cc, String keywords, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZDKSXkBean> list = new ArrayList<>();
		
		if(sf.indexOf("四川") != -1 || sf.indexOf("重庆") != -1) {
			
		}else {
			sf = "四川";
		}
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zdks_xk x WHERE x.kmyq_ext like ? and x.zscc = ? and x.sf = ? and (x.yxmc like ? OR x.zymc like ? or x.bhzy like ?) and (x.yxsf like ? or x.yxsf is NULL) order by x.yxmc, x.zymc limit ?,?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + xk + "%");
			ps.setString(2, cc);
			ps.setString(3, sf);
			ps.setString(4, "%" + keywords + "%");
			ps.setString(5, "%" + keywords + "%");
			ps.setString(6, "%" + keywords + "%");
			ps.setString(7, "%" + yxsf + "%");
			ps.setInt(8, (pageNumber - 1) * 10);
			ps.setInt(9, 10);
			rs = ps.executeQuery();
			ZDKSXkBean bean = null;
			while (rs.next()) {
				bean = new ZDKSXkBean();
				bean.setBhzy(rs.getString("bhzy"));
				bean.setKmyq(rs.getString("kmyq"));
				bean.setKmyqExt(rs.getString("kmyq_ext"));
				bean.setNf(rs.getString("nf"));
				bean.setSf(rs.getString("sf"));
				bean.setYxdm(rs.getString("yxdm"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZkfx(rs.getString("zkfx"));
				bean.setZscc(rs.getString("zscc"));
				bean.setZydm(rs.getString("zydm"));
				bean.setZymc(rs.getString("zymc"));
				bean.setYxsf(rs.getString("yxsf"));
				list.add(bean);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZDKSXkBean> listXuanKeByYx(String sf, String yxmc, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZDKSXkBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zdks_xk x WHERE x.yxmc = ? and x.sf = ? order by x.zymc limit ?,?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yxmc);
			ps.setString(2, sf);

			ps.setInt(3, (pageNumber - 1) * 10);
			ps.setInt(4, 10);
			rs = ps.executeQuery();
			ZDKSXkBean bean = null;
			while (rs.next()) {
				bean = new ZDKSXkBean();
				bean.setBhzy(rs.getString("bhzy"));
				bean.setKmyq(rs.getString("kmyq"));
				bean.setKmyqExt(rs.getString("kmyq_ext"));
				bean.setNf(rs.getString("nf"));
				bean.setSf(rs.getString("sf"));
				bean.setYxdm(rs.getString("yxdm"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZkfx(rs.getString("zkfx"));
				bean.setZscc(rs.getString("zscc"));
				bean.setZydm(rs.getString("zydm"));
				bean.setZymc(rs.getString("zymc"));
				bean.setYxsf(rs.getString("yxsf"));
				list.add(bean);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<CareerJY> listJiuyeYXMCByDW(String group_name, String lsy, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CareerJY> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT yxmc, COUNT(*) as ct FROM career_jy_all_2024 x WHERE x.group_name = ? AND x.lsy = ? GROUP BY x.yxmc ORDER BY COUNT(*) desc limit ?,?;";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, group_name);
			ps.setString(2, lsy);

			ps.setInt(3, (pageNumber - 1) * 10);
			ps.setInt(4, 10);
			rs = ps.executeQuery();
			CareerJY bean = null;
			while (rs.next()) {
				bean = new CareerJY();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setCnt(rs.getInt("ct"));
				list.add(bean);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public HashMap<String, List<SchoolBean>> listJiuyeZDFByYXMC(HashSet<String> inYXMC, String xk_code, String province, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, List<SchoolBean>> MAP = new HashMap<>();
		try {
			conn = DatabaseUtils.getConnection();
			String year_query = "2024x";
			if(!JDBC.HM_PROVINCE_LATEST_YEAR.containsKey(province.substring(0,2))) {
				year_query = "2023x";
			}
			String SQL = "SELECT yxmc_org, zdf, zdfwc, pc FROM "+province+"_"+year_query+" x WHERE x.yxmc_org IN ("+Tools.getSQLQueryin(inYXMC)+") AND xk_code LIKE ? GROUP BY yxmc_org, pc, zdf, zdfwc order by yxmc_org, zdf desc ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%"+xk_code+"%");
			rs = ps.executeQuery();
			
			while (rs.next()) {
				String yxmc = rs.getString("yxmc_org");
				List<SchoolBean> list = null;
				
				if(MAP.containsKey(yxmc)){
					list = MAP.get(yxmc);
				}else {
					list = new ArrayList<>();
				}
				
				SchoolBean schoolBean = new SchoolBean();
				schoolBean.setZdf(rs.getString("zdf"));
				schoolBean.setZdfwc(rs.getString("zdfwc"));
				schoolBean.setPc(rs.getString("pc"));
				
				list.add(schoolBean);
				
				MAP.put(yxmc, list);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return MAP;
	}
	
	public List<ZDKSXkBean> listXuanKeByZy(String sf, String zymc, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZDKSXkBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zdks_xk x WHERE (x.zymc = ? or x.bhzy like ? or x.zkfx like ?) and x.sf = ? order by x.yxmc limit ?,?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, zymc);
			ps.setString(2, "%" + zymc + "%");
			ps.setString(3, "%" + zymc + "%");
			ps.setString(4, sf);
			ps.setInt(5, (pageNumber - 1) * 10);
			ps.setInt(6, 10);
			rs = ps.executeQuery();
			ZDKSXkBean bean = null;
			while (rs.next()) {
				bean = new ZDKSXkBean();
				bean.setBhzy(rs.getString("bhzy"));
				bean.setKmyq(rs.getString("kmyq"));
				bean.setKmyqExt(rs.getString("kmyq_ext"));
				bean.setNf(rs.getString("nf"));
				bean.setSf(rs.getString("sf"));
				bean.setYxdm(rs.getString("yxdm"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZkfx(rs.getString("zkfx"));
				bean.setZscc(rs.getString("zscc"));
				bean.setZydm(rs.getString("zydm"));
				bean.setZymc(rs.getString("zymc"));
				bean.setYxsf(rs.getString("yxsf"));
				list.add(bean);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<AutoPlayBean> getAutoPlayLatestUserZDInput(String cid, int size) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    List<AutoPlayBean> list = new ArrayList<>();
	    try {
	      conn = DatabaseUtils.getConnection();
	      String SQL = "SELECT * FROM zdks_autoplay_score WHERE STATUS = 1 and C_ID = ? ORDER BY SORT DESC, CREATE_DT ASC limit 0," + size;
	      Tools.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, cid);
	      rs = ps.executeQuery();
	      AutoPlayBean bean = null;
	      while (rs.next()) {
	    	bean = new AutoPlayBean();
	        bean.setId(rs.getInt("ID"));
	        bean.setSf(rs.getString("SF"));
	        bean.setCid(rs.getString("C_ID"));
	        bean.setCity(rs.getString("CITY"));
	        bean.setZdpc(rs.getString("ZDPC"));
	        bean.setXk(rs.getString("XK"));
	        bean.setXk_code(rs.getString("XK_CODE"));
	        bean.setScore(rs.getInt("SCORE"));
	        bean.setStatus(rs.getInt("STATUS"));
	        bean.setRemark(rs.getString("REMARK"));
	        list.add(bean);
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return list;
	}
	
	public boolean updateAutoPlayLatestUserZDInput(int id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE zdks_autoplay_score SET STATUS = 2 WHERE ID = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, id);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean updateAutoPlayUserInput(String cid, String sf, String city, String zdpc, String xk, int score) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE zdks_autoplay_score SET STATUS = 1, sf = ?, city = ?, zdpc = ?, xk = ?, score = ? WHERE c_id = ? and sort = 99999999";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, sf);
			ps.setString(2, city);
			ps.setString(3, zdpc);
			ps.setString(4, xk);
			ps.setInt(5, score);
			ps.setString(6, cid);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean updateAutoPlayRefresh() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "UPDATE zdks_autoplay_score SET STATUS = 1 WHERE sort < 99999999";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	
	public List<AutoPlayBean> getAutoPlayAllZDPCInfo() {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    List<AutoPlayBean> list = new ArrayList<>();
	    try {
	      conn = DatabaseUtils.getConnection();
	      StringBuffer SQL = new StringBuffer();
	      Iterator<String> llist = (Iterator<String>) JDBC.HM_PROVINCE.keySet().iterator();
	      int index = 0;
	      while(llist.hasNext()) {
	    	  	index++;
	    	  	if(index > 1) {
	    	  		SQL.append("UNION ALL ");
				}
	    	  	SQL.append("SELECT x.SF, x.CITY, x.ZDPC FROM "+JDBC.HM_PROVINCE.get(llist.next())+"_zdks_score_convert x WHERE x.NF = 2024 GROUP BY  x.SF, x.CITY, x.ZDPC ");
				
	      }
	      Tools.println(SQL);
	      ps = conn.prepareStatement(SQL.toString());
	      rs = ps.executeQuery();
	      AutoPlayBean bean = null;
	      while (rs.next()) {
	    	bean = new AutoPlayBean();
	        bean.setSf(rs.getString("SF"));
	        bean.setCity(rs.getString("CITY"));
	        bean.setZdpc(rs.getString("ZDPC"));
	        list.add(bean);
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return list;
	}
	

	
	
	public int listXuanKeCNT(String sf, String yxsf, String xk, String ts, String cc, String keywords) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT count(*) FROM zdks_xk x WHERE x.kmyq_ext like ? and x.zscc = ? and x.sf = ? and (x.yxmc like ? OR x.zymc like ? or x.bhzy like ?) and (x.yxsf like ? or x.yxsf is NULL) ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + xk + "%");
			ps.setString(2, cc);
			ps.setString(3, sf);
			ps.setString(4, "%" + keywords + "%");
			ps.setString(5, "%" + keywords + "%");
			ps.setString(6, "%" + keywords + "%");
			ps.setString(7, "%" + yxsf + "%");
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt(1);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	
	
	public List<SchoolMarjorCommonBean> getPDFGaoKaoUniversityList(PDFGaoZhongStudentInfo info, int year) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolMarjorCommonBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String where_by = "WHERE x.zyml in ("+Tools.getSQLQueryin(info.getMajor_catg_list())+") AND x.zdfwc between ? AND ? and xk_code like ? ";
			String order_by = "Order by x.yxmc";
			String SQL_YXMC = "SELECT distinct yxmc FROM "+info.getProvinceCode()+"_"+year+" x " + where_by + order_by;
			
			String SQL2 = "SELECT * FROM "+info.getProvinceCode()+"_"+(year - 1)+" x WHERE x.yxmc in ("+SQL_YXMC+")";
			Tools.println(SQL2);
			ps = conn.prepareStatement(SQL2);
			ps.setInt(1, info.getWc_min());
			ps.setInt(2, info.getWc_max());
			ps.setString(3, "%"+XKCombineUtils.getXKCodeByStudentSelection(info.getXk())+"%");
			rs = ps.executeQuery();
			SchoolMarjorCommonBean bean = null;
			HashMap<String, SchoolMarjorCommonBean> map_dt_2022 = new HashMap<>();
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setYxdm(rs.getString("yxdm"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setZymc_org(rs.getString("zymc_org"));
				bean.setZydm(rs.getString("zydm"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZybz(rs.getString("zybz"));
				bean.setPc(rs.getString("pc"));
				bean.setXk(rs.getString("xk"));
				bean.setXk_code(rs.getString("xk_code"));
				bean.setNf(year);
				bean.setZdf(rs.getString("zdf"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setZdfwc22(rs.getString("zdfwc"));
				String key = bean.getYxmc()+bean.getZymc_org();
				map_dt_2022.put(key, bean);
			}
			
			ps.close();
			rs.close();
			
			String SQL3 = "SELECT * FROM "+info.getProvinceCode()+"_"+(year - 2)+" x WHERE x.yxmc in ("+SQL_YXMC+")";
			Tools.println(SQL3);
			ps = conn.prepareStatement(SQL3);
			ps.setInt(1, info.getWc_min());
			ps.setInt(2, info.getWc_max());
			ps.setString(3, "%"+XKCombineUtils.getXKCodeByStudentSelection(info.getXk())+"%");
			rs = ps.executeQuery();
			HashMap<String, SchoolMarjorCommonBean> map_dt_2021 = new HashMap<>();
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setYxdm(rs.getString("yxdm"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZydm(rs.getString("zydm"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZybz(rs.getString("zybz"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setZymc_org(rs.getString("zymc_org"));
				bean.setPc(rs.getString("pc"));
				bean.setXk(rs.getString("xk"));
				bean.setXk(rs.getString("xk"));
				bean.setXk_code(rs.getString("xk_code"));
				bean.setNf(year);
				bean.setZdf(rs.getString("zdf"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setZdfwc21(rs.getString("zdfwc"));
				String key = bean.getYxmc()+bean.getZymc_org();
				map_dt_2021.put(key, bean);
			}
			
			
			
			String SQL = "SELECT * FROM "+info.getProvinceCode()+"_"+year+" x " + where_by + order_by;
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, info.getWc_min());
			ps.setInt(2, info.getWc_max());
			ps.setString(3, "%"+XKCombineUtils.getXKCodeByStudentSelection(info.getXk())+"%");
			rs = ps.executeQuery();
			YGDataPatchDBTools dp = new YGDataPatchDBTools();
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setYxdm(rs.getString("yxdm"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZydm(rs.getString("zydm"));
				bean.setZymc(rs.getString("zymc"));
				bean.setZybz(rs.getString("zybz"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setZymc_org(rs.getString("zymc_org"));
				bean.setPc(rs.getString("pc"));
				bean.setXk(rs.getString("xk"));
				bean.setXk_code(rs.getString("xk_code"));
				bean.setNf(year);
				bean.setZdf(rs.getString("zdf"));
				bean.setZdfwc(rs.getString("zdfwc"));
				
				bean.setZdfwc23(rs.getString("zdfwc"));
				String key = bean.getYxmc()+bean.getZymc_org();

				if(map_dt_2021.containsKey(key)) {
					bean.setZdfwc21(map_dt_2021.get(key).getZdfwc());
				}
				if(map_dt_2022.containsKey(key)) {
					bean.setZdfwc22(map_dt_2022.get(key).getZdfwc());
				}
				
		    	PredictBean predictBean = dp.yc2023WC721(Tools.getInt(bean.getZdfwc21()), Tools.getInt(bean.getZdfwc22()), Tools.getInt(bean.getZdfwc23()), "1", 1);
		    	//bean.setZdfwc24(String.valueOf(predictBean.getResultMin()));
		    	bean.setZdfwc24(String.valueOf(predictBean.getResultMax()));
				
				list.add(bean);
			}
			ps.close();
			rs.close();

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public List<SchoolMarjorCommonBean> getPDFGaoKaoUniversityListForyuce(PDFGaoZhongStudentInfo info, int year) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolMarjorCommonBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String where_by = "WHERE x.zdfwc between ? AND ? and xk_code like ? ";
			String order_by = "Order by x.yxmc";
			String SQL_YXMC = "SELECT distinct yxmc FROM "+info.getProvinceCode()+"_"+year+"x x " + where_by + order_by;
			
			String SQL2 = "SELECT * FROM "+info.getProvinceCode()+"_"+(year - 1)+"x x WHERE x.yxmc in ("+SQL_YXMC+")";
			Tools.println(SQL2);
			ps = conn.prepareStatement(SQL2);
			ps.setInt(1, info.getWc_min());
			ps.setInt(2, info.getWc_max());
			ps.setString(3, "%"+XKCombineUtils.getXKCodeByStudentSelection(info.getXk())+"%");
			rs = ps.executeQuery();
			SchoolMarjorCommonBean bean = null;
			HashMap<String, SchoolMarjorCommonBean> map_dt_2022 = new HashMap<>();
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setYxdm(rs.getString("yxdm"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setYxbz(rs.getString("yxbz"));
				bean.setPc(rs.getString("pc"));
				bean.setXk(rs.getString("xk"));
				bean.setXk_code(rs.getString("xk_code"));
				bean.setNf(year);
				bean.setZdf(rs.getString("zdf"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setZdfwc22(rs.getString("zdfwc"));
				String key = bean.getYxmc()+bean.getYxbz();
				map_dt_2022.put(key, bean);
			}
			
			ps.close();
			rs.close();
			
			String SQL3 = "SELECT * FROM "+info.getProvinceCode()+"_"+(year - 2)+"x x WHERE x.yxmc in ("+SQL_YXMC+")";
			Tools.println(SQL3);
			ps = conn.prepareStatement(SQL3);
			ps.setInt(1, info.getWc_min());
			ps.setInt(2, info.getWc_max());
			ps.setString(3, "%"+XKCombineUtils.getXKCodeByStudentSelection(info.getXk())+"%");
			rs = ps.executeQuery();
			HashMap<String, SchoolMarjorCommonBean> map_dt_2021 = new HashMap<>();
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setYxdm(rs.getString("yxdm"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setYxbz(rs.getString("yxbz"));
				bean.setPc(rs.getString("pc"));
				bean.setXk(rs.getString("xk"));
				bean.setXk_code(rs.getString("xk_code"));
				bean.setNf(year);
				bean.setZdf(rs.getString("zdf"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setZdfwc21(rs.getString("zdfwc"));
				String key = bean.getYxmc()+bean.getYxbz();
				map_dt_2021.put(key, bean);
			}
			
			
			
			String SQL = "SELECT * FROM "+info.getProvinceCode()+"_"+year+"x x " + where_by + order_by;
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, info.getWc_min());
			ps.setInt(2, info.getWc_max());
			ps.setString(3, "%"+XKCombineUtils.getXKCodeByStudentSelection(info.getXk())+"%");
			rs = ps.executeQuery();
			YGDataPatchDBTools dp = new YGDataPatchDBTools();
			while (rs.next()) {
				bean = new SchoolMarjorCommonBean();
				bean.setYxdm(rs.getString("yxdm"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setYxmc_org(rs.getString("yxmc_org"));
				bean.setYxbz(rs.getString("yxbz"));
				bean.setPc(rs.getString("pc"));
				bean.setXk(rs.getString("xk"));
				bean.setXk_code(rs.getString("xk_code"));
				bean.setNf(year);
				bean.setZdf(rs.getString("zdf"));
				bean.setZdfwc(rs.getString("zdfwc"));
				bean.setZdfwc23(rs.getString("zdfwc"));
				String key = bean.getYxmc()+bean.getYxbz();

				if(map_dt_2021.containsKey(key)) {
					bean.setZdfwc21(map_dt_2021.get(key).getZdfwc());
				}
				if(map_dt_2022.containsKey(key)) {
					bean.setZdfwc22(map_dt_2022.get(key).getZdfwc());
				}
				
		    	PredictBean predictBean = dp.yc2023WC721(Tools.getInt(bean.getZdfwc21()), Tools.getInt(bean.getZdfwc22()), Tools.getInt(bean.getZdfwc23()), "1", 1);
		    	//bean.setZdfwc24(String.valueOf(predictBean.getResultMin()));
		    	bean.setZdfwc24(String.valueOf(predictBean.getResultMax()));
				
				list.add(bean);
			}
			ps.close();
			rs.close();

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public List<CareerJiuye> getPDFGaoKaoUniversityWorkList(PDFGaoZhongStudentInfo info, int year) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CareerJiuye> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String where_by = "WHERE x.zyml in ("+Tools.getSQLQueryin(info.getMajor_catg_list())+") AND x.zdfwc between ? AND ? and xk_code like ? ";
			String order_by = "Order by x.yxmc ";
			String SQL_YXMC = "SELECT distinct yxmc_org FROM "+info.getProvinceCode()+"_"+year+" x " + where_by;
			
			String SQL2 = "SELECT x.yxmc,x.ssgs,count(*) as ct FROM career_jiuye x WHERE x.yxmc in ("+SQL_YXMC+") group by x.yxmc,x.ssgs order by count(*) desc limit 0,100";
			Tools.println(SQL2);
			ps = conn.prepareStatement(SQL2);
			ps.setInt(1, info.getWc_min());
			ps.setInt(2, info.getWc_max());
			ps.setString(3, "%"+XKCombineUtils.getXKCodeByStudentSelection(info.getXk())+"%");
			rs = ps.executeQuery();
			CareerJiuye bean = null;
			while (rs.next()) {
				bean = new CareerJiuye();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setQymc(rs.getString("ssgs"));
				bean.setCnt(rs.getInt("ct"));
				list.add(bean);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public List<HZBXBean> getPDFGaokaoHZBXUniversityWorkList(PDFGaoZhongStudentInfo info, int year) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<HZBXBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String where_by = "WHERE x.zdfwc between ? AND ? and xk_code like ? ";
			String order_by = "Order by x.yxmc ";
			String SQL_YXMC = "SELECT distinct yxmc_org FROM "+info.getProvinceCode()+"_"+year+" x " + where_by;
			
			String SQL2 = "SELECT * FROM career_lx_hezuo x WHERE x.yxmc_in_org in ("+SQL_YXMC+") and x.src = '本科' AND x.expire_dt >= 2024 AND x.zsfs_org = '计划内' order by yxmc_in_org desc limit 0,100";
			Tools.println(SQL2);
			ps = conn.prepareStatement(SQL2);
			ps.setInt(1, info.getWc_min());
			ps.setInt(2, info.getWc_max());
			ps.setString(3, "%"+XKCombineUtils.getXKCodeByStudentSelection(info.getXk())+"%");
			rs = ps.executeQuery();
			HZBXBean bean = null;
			while (rs.next()) {
				bean = new HZBXBean();
				bean.setYxmc_in_org(rs.getString("yxmc_in_org"));
				bean.setYxmc_out_org(rs.getString("yxmc_out_org"));
				bean.setDq(rs.getString("dq"));
				bean.setKskc(rs.getString("kskc"));
				bean.setMc(rs.getString("mc"));
				bean.setZsks(rs.getString("zsks"));
				bean.setXf(rs.getString("xf"));
				list.add(bean);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<SQABean> getPDFSQAUniversityForLX() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SQABean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM career_lx_sqa x";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			
			rs = ps.executeQuery();
			SQABean lx = null;
			while (rs.next()) {
				lx = new SQABean();
				lx.setYxmc_in(rs.getString("yxmc_in"));
				lx.setZymc(rs.getString("zymc"));
				lx.setXf(rs.getString("xf"));
				lx.setZstj(rs.getString("zstj"));
				list.add(lx);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public List<YXDWFBean> getYXDWFBean() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<YXDWFBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM wh_dwf_550 x";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			
			rs = ps.executeQuery();
			YXDWFBean lx = null;
			while (rs.next()) {
				lx = new YXDWFBean();
				lx.setDwf21(rs.getInt("dwf21"));
				lx.setDwf22(rs.getInt("dwf22"));
				lx.setDwf23(rs.getInt("dwf23"));
				lx.setId(rs.getInt("id"));
				lx.setKl(rs.getString("kl"));
				lx.setPc(rs.getString("pc"));
				lx.setYxmc(rs.getString("yxmc"));
				list.add(lx);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public boolean updateYXDWFBean(int id, int dwf24_min, int dwf24_max) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "update wh_dwf_550 set dwf24_min = ?, dwf24_max  = ? where id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, dwf24_min);
			ps.setInt(2, dwf24_max);
			ps.setInt(3, id);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
  	
  	
}
