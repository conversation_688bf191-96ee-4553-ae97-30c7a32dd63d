package com.career.db;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Hashtable;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Vector;

import com.career.db.jhdao.YfJHDao;
import com.career.utils.SQLLogUtils;
import com.career.utils.Tools;

public class FormMgrJDBC {
	
	static String URL = JDBCConstants.URL;
	static String USER = JDBCConstants.USER;
	static String PASSWD = JDBCConstants.PASSWD;
	
	static {
		try {
			Class.forName("com.mysql.cj.jdbc.Driver");
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
	
	public LhyForm getLhyFormById(String sfCode, int id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker x WHERE id = ? ORDER BY seq_no_yx, seq_no_zy";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, id);

			rs = ps.executeQuery();
			LhyForm bean = null;
			while (rs.next()) {
				return setLhyForm(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public List<LhyForm> getLhyFormByBatchId(String sfCode, String batch_id_org) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyForm> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker x WHERE batch_id_org = ? ORDER BY seq_no_yx, seq_no_zy";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setLhyForm(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<LhyForm> getLhyFormByBatchIdAndSeqNoYx(String sfCode, String batch_id_org, int seq_no_yx) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LhyForm> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker x WHERE batch_id_org = ? and seq_no_yx = ? ORDER BY seq_no_yx, seq_no_zy";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			ps.setInt(2, seq_no_yx);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setLhyForm(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	private static void closeAllConnection(Connection conn, PreparedStatement ps, ResultSet rs) {
		DatabaseUtils.closeAllResources(rs, ps, conn);
	}
	
	private static LhyForm setLhyForm(ResultSet rs) throws SQLException {
		LhyForm bean = new LhyForm();
		bean.setBatch_id(rs.getString("batch_id"));
		bean.setId(rs.getInt("id"));
		bean.setBatch_id_org(rs.getString("batch_id_org"));
		bean.setSeq_no_yx(rs.getInt("seq_no_yx"));
		bean.setSeq_no_zy(rs.getInt("seq_no_zy"));
		bean.setYxdm(rs.getString("yxdm"));
		bean.setZydm(rs.getString("zydm"));
		bean.setZyz(rs.getString("zyz"));
		return bean;
	}

	
	/**
	 * 拖动事务控制
	 * @param sfCode
	 * @param updateList
	 */
	public static void adjustLhyFormYxNoWithFormIdWithTransaction(String sfCode, String xkCode, String batch_id_org, boolean is96, int src_form_id, int target_form_id, String direction) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			conn.setAutoCommit(false);

			List<LhyForm> formList = new ArrayList<>();
			String SQL = "SELECT * FROM lhy_"+sfCode+"_form_maker x WHERE batch_id_org = ? ORDER BY seq_no_yx, seq_no_zy";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);

			int srcLhyFormNo = 0;
			int targetLhyFormNo = 0;
			rs = ps.executeQuery();
			while (rs.next()) {
				LhyForm lhyForm = setLhyForm(rs);
				lhyForm.setExt_org_seq_no_yx(lhyForm.getSeq_no_yx());
				formList.add(lhyForm);
				if(lhyForm.getId() == src_form_id) {
					srcLhyFormNo = lhyForm.getSeq_no_yx();
					
					if(direction.equals("up")) {
						targetLhyFormNo = srcLhyFormNo - 1;
						direction = "exchange";
					}else if(direction.equals("down")) {
						targetLhyFormNo = srcLhyFormNo + 1;
						direction = "exchange";
					}
				}
				
				if(lhyForm.getId() == target_form_id) {
					targetLhyFormNo  = lhyForm.getSeq_no_yx();
				}
				
			}

			if(srcLhyFormNo == 0 || targetLhyFormNo == 0) {
				return;
			}
			
			List<LhyForm> updateList = new ArrayList<>(); 
			
			//计算插入的位置
			if(direction.equals("before")) {
				for(LhyForm bean : formList){
					if(bean.getSeq_no_yx() == srcLhyFormNo) {
						bean.setSeq_no_yx(9999999);
					}
				}
				for(LhyForm bean : formList){
					if(bean.getSeq_no_yx() != 9999999 && bean.getSeq_no_yx() >= targetLhyFormNo) {
						bean.setSeq_no_yx(bean.getSeq_no_yx() + 1);
					}
				}
				for(LhyForm bean : formList){
					if(bean.getSeq_no_yx() == 9999999) {
						bean.setSeq_no_yx(targetLhyFormNo);
					}
				}
			}else if(direction.equals("after")) {
				for(LhyForm bean : formList){
					if(bean.getSeq_no_yx() == srcLhyFormNo) {
						bean.setSeq_no_yx(9999999);
					}
				}
				for(LhyForm bean : formList){
					if(bean.getSeq_no_yx() != 9999999 && bean.getSeq_no_yx() > targetLhyFormNo) {
						bean.setSeq_no_yx(bean.getSeq_no_yx() + 1);
					}
				}
				for(LhyForm bean : formList){
					if(bean.getSeq_no_yx() == 9999999) {
						bean.setSeq_no_yx(targetLhyFormNo + 1);
					}
				}
			}else if(direction.equals("exchange")) {
				for(LhyForm bean : formList){
					if(srcLhyFormNo == bean.getSeq_no_yx()) {
						bean.setSeq_no_yx(targetLhyFormNo);
					}else if(targetLhyFormNo == bean.getSeq_no_yx()) {
						bean.setSeq_no_yx(srcLhyFormNo);
					}
				}
			}else {
				return;
			}
			
			formList.sort(Comparator.comparingInt(LhyForm::getSeq_no_yx));
			
			//重构顺序
			HashMap<String, List<LhyForm>> recompute_list = new LinkedHashMap<>();
			for(LhyForm bean : formList){
				String key_group = Tools.getTBUniqueGroupOnlyKey(bean, is96);
				recompute_list.computeIfAbsent(key_group, k -> new ArrayList<LhyForm>()).add(bean);
			}
			
			int seq_yx_no = 0;
			List<LhyForm> new_entry_list = new ArrayList<>();
			for(List<LhyForm> lhyFormList : recompute_list.values()){
				seq_yx_no++;
				for (LhyForm bean : lhyFormList) {
					bean.setSeq_no_yx(seq_yx_no);
					new_entry_list.add(bean);
				}
			}
			
			for(LhyForm bean : new_entry_list){
				if(bean.getExt_org_seq_no_yx() == bean.getSeq_no_yx()) {
					continue;
				}
				updateList.add(bean);
			}
			if(updateList.size() > 0) {
				String SQL_UPDATE = "update lhy_"+sfCode+"_form_maker set seq_no_yx = ? where id = ?";
				ps = conn.prepareStatement(SQL_UPDATE);
				for(LhyForm form : updateList) {
					ps.setInt(1, form.getSeq_no_yx());
					ps.setInt(2, form.getId());
					ps.addBatch();
				}
				ps.executeBatch();
			}
			conn.commit();
		} catch (Exception ex) {
			ex.printStackTrace();
			try {
				conn.rollback();
			}catch(Exception exx) {}
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void updateMakerFormSeqInBatch(String sfCode, List<LhyForm> updateList) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL_UPDATE = "update lhy_"+sfCode+"_form_maker set seq_no_yx = ?, seq_no_zy = ? where id = ?";
			ps = conn.prepareStatement(SQL_UPDATE);
			for(LhyForm form : updateList) {
				ps.setInt(1, form.getSeq_no_yx());
				ps.setInt(2, form.getSeq_no_zy());
				ps.setInt(3, form.getId());
				ps.addBatch();
			}
			ps.executeBatch();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void updateMakerFormZySeqInBatch(String sfCode, List<LhyForm> updateList) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL_UPDATE = "update lhy_"+sfCode+"_form_maker set seq_no_zy = ? where id = ?";
			ps = conn.prepareStatement(SQL_UPDATE);
			for(LhyForm form : updateList) {
				ps.setInt(1, form.getSeq_no_zy());
				ps.setInt(2, form.getId());
				ps.addBatch();
			}
			ps.executeBatch();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	/**
	 * 把某个专业移入回收站
	 * @param batch_id
	 * @param seq_no_yx
	 * @param seq_no_zy
	 * @param sfCode
	 */
	public void deleteMakerFormToRecycle(String batch_id, int seq_no_yx, int seq_no_zy, String sfCode) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			conn.setAutoCommit(false);
			
			String SQL_recycle = "insert into lhy_"+sfCode+"_form_maker_recycle(batch_id, batch_id_org, yxdm, zyz, zydm, seq_no_yx, seq_no_zy, create_tm, recycle_tm, remark) "
					+ "select batch_id, batch_id_org, yxdm, zyz, zydm, seq_no_yx, seq_no_zy, create_tm, now(), 'delete_ZY' from lhy_"+sfCode+"_form_maker where batch_id_org = ? and seq_no_yx = ? and seq_no_zy = ?";
			ps = conn.prepareStatement(SQL_recycle);
			ps.setString(1, batch_id);
			ps.setInt(2, seq_no_yx);
			ps.setInt(3, seq_no_zy);
			ps.executeUpdate();
			
			String SQL_delete = "delete from lhy_"+sfCode+"_form_maker where batch_id_org = ? and seq_no_yx = ? and seq_no_zy = ?";
			ps = conn.prepareStatement(SQL_delete);
			ps.setString(1, batch_id);
			ps.setInt(2, seq_no_yx);
			ps.setInt(3, seq_no_zy);
			ps.executeUpdate();
			
			conn.commit();
		} catch (Exception ex) {
			ex.printStackTrace();
			try {
				conn.rollback();
			}catch(Exception exx) {}
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	/**
	 * 把某个专业组移入回收站
	 * @param batch_id
	 * @param seq_no_yx
	 * @param sfCode
	 */
	public void deleteMakerFormToRecycle(String batch_id, int seq_no_yx, String sfCode) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			conn.setAutoCommit(false);
			
			String SQL_recycle = "insert into lhy_"+sfCode+"_form_maker_recycle(batch_id, batch_id_org, yxdm, zyz, zydm, seq_no_yx, seq_no_zy, create_tm, recycle_tm, remark) "
					+ "select batch_id, batch_id_org, yxdm, zyz, zydm, seq_no_yx, seq_no_zy, create_tm, now(), 'delete_YX' from lhy_"+sfCode+"_form_maker where batch_id_org = ? and seq_no_yx = ?";
			ps = conn.prepareStatement(SQL_recycle);
			ps.setString(1, batch_id);
			ps.setInt(2, seq_no_yx);
			ps.executeUpdate();
			
			String SQL_delete = "delete from lhy_"+sfCode+"_form_maker where batch_id_org = ? and seq_no_yx = ?";
			ps = conn.prepareStatement(SQL_delete);
			ps.setString(1, batch_id);
			ps.setInt(2, seq_no_yx);
			ps.executeUpdate();
			conn.commit();
		} catch (Exception ex) {
			ex.printStackTrace();
			try {
				conn.rollback();
			}catch(Exception exx) {}
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	/**
	 * 从回收站中删除某个专业组
	 * @param batch_id
	 * @param seq_no_yx
	 * @param sfCode
	 */
	public void deleteMakerFormFromRecycle(String batch_id, int seq_no_yx, String sfCode) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from lhy_"+sfCode+"_form_maker_recycle where batch_id_org = ? and seq_no_yx = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			ps.setInt(2, seq_no_yx);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	/**
	 * 从给定的SEQ_NO开始，把SEQ_NO的值减一
	 * @param sfCode
	 * @param batch_id
	 * @param fromSeqYxNo
	 */
	public void decreaseMakerFormSeqYxNoWithBatchId(String sfCode, String batch_id, int fromSeqYxNo) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "update lhy_"+sfCode+"_form_maker set seq_no_yx = seq_no_yx - 1 where batch_id_org = ? and seq_no_yx >= ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			ps.setInt(2, fromSeqYxNo);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void deleteMakerFormMainByBatchId(String batch_id, String sfCode) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "delete from lhy_"+sfCode+"_form_maker_main where batch_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public LhyForm insertMakerForm(String sfCode, LhyForm form) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into lhy_"+sfCode+"_form_maker(batch_id, batch_id_org, seq_no_yx, seq_no_zy, yxdm, zyz, zydm, create_tm) values(?,?,?,?,?,?,?,now())";
			ps = conn.prepareStatement(SQL, Statement.RETURN_GENERATED_KEYS); 
			int index = 1;
			ps.setString(index++, form.getBatch_id());
			ps.setString(index++, form.getBatch_id_org());
			ps.setInt(index++, form.getSeq_no_yx());
			ps.setInt(index++, form.getSeq_no_zy());
			ps.setString(index++, form.getYxdm());
			ps.setString(index++, form.getZyz());
			ps.setString(index++, form.getZydm());
			ps.executeUpdate();
			rs = ps.getGeneratedKeys(); // 获取生成的键值
	        if (rs.next()) {
	            int generatedId = rs.getInt(1); 
	            form.setId(generatedId);
	        }
	        
			SQLLogUtils.printSQL("Check: ", ps);
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return form;
	}
	
	
	public void insertMakerFormByBatch(String sfCode, List<LhyForm> formList) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "insert into lhy_"+sfCode+"_form_maker(batch_id, batch_id_org, seq_no_yx, seq_no_zy, yxdm, zyz, zydm, create_tm) values(?,?,?,?,?,?,?,now())";
			ps = conn.prepareStatement(SQL);
			
			for(LhyForm form : formList) {
				int index = 1;
				ps.setString(index++, form.getBatch_id());
				ps.setString(index++, form.getBatch_id_org());
				ps.setInt(index++, form.getSeq_no_yx());
				ps.setInt(index++, form.getSeq_no_zy());
				ps.setString(index++, form.getYxdm());
				ps.setString(index++, form.getZyz());
				ps.setString(index++, form.getZydm());
				
				ps.addBatch();
			}
			ps.executeBatch();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	
}
