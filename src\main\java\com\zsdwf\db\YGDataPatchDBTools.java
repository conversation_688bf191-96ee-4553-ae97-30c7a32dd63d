package com.zsdwf.db;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.career.db.JDBCConstants;
import com.zsdwf.utils.Tools;

public class YGDataPatchDBTools {
	static String URL = JDBCConstants.URL;
	static String USER = JDBCConstants.USER;
	static String PASSWD = JDBCConstants.PASSWD;

	// TYPE = 1 : 学校， 2=专业
	public PredictBean yc2023Dwf(int dwf19, int dwf20, int dwf21, String id, int type) {
		PredictBean pd = new PredictBean();
		pd.setId(id);
		if ((dwf19 + dwf20 + dwf21) == -3) {
			pd.setResultMin(-1);
			pd.setResultMax(999);
			pd.setSrc("三年无");
		} else if ((dwf19 + dwf20) == -2) {
			pd.setResultMin(dwf21 + 10);
			pd.setResultMax(pd.getResultMin() + 10);
			pd.setSrc("1920无数据");
		} else if ((dwf20 + dwf21) == -2) {
			pd.setResultMin(dwf19 + 10);
			pd.setResultMax(pd.getResultMin() + 10);
			pd.setSrc("A2120无数据");
		} else if ((dwf19 + dwf21) == -2) {
			pd.setResultMin(dwf20 + 10);
			pd.setResultMax(pd.getResultMin() + 10);
			pd.setSrc("A1921无数据");
		} else if (dwf19 == -1) {
			if (dwf21 < dwf20) {
				pd.setResultMin(dwf20);
				pd.setResultMax(pd.getResultMin() + 10);
				pd.setSrc("B19无数据");
			} else {
				pd.setResultMin(dwf21 + (dwf21 - dwf20));
				pd.setResultMax(pd.getResultMin() + 10);
				pd.setSrc("B19无数据");
			}
		} else if (dwf20 == -1) {
			if (dwf21 < dwf19) {
				pd.setResultMin(dwf19);
				pd.setResultMax(pd.getResultMin() + 10);
				pd.setSrc("B20无数据");
			} else {
				pd.setResultMin(dwf21 + (dwf21 - dwf19));
				pd.setResultMax(pd.getResultMin() + 10);
				pd.setSrc("B20无数据");
			}
		} else if (dwf21 == -1) {
			if (dwf20 < dwf19) {
				pd.setResultMin(dwf19);
				pd.setResultMax(pd.getResultMin() + 10);
				pd.setSrc("B21无数据");
			} else {
				pd.setResultMin(dwf20 + (dwf20 - dwf19));
				pd.setResultMax(pd.getResultMin() + 10);
				pd.setSrc("B21无数据");
			}
		} else {

			if (dwf19 > dwf20 && dwf20 < dwf21) {
				// 高地位，低
				if (dwf21 < dwf19) {
					pd.setResultMin(dwf21);
					pd.setResultMax(dwf19);
					pd.setSrc("C大小大");
				} else {
					pd.setResultMin(dwf19);
					pd.setResultMax(dwf21);
					pd.setSrc("C大小大");
				}
			} else if (dwf19 < dwf20 && dwf20 > dwf21) {
				// 高地位，高
				if (dwf21 < dwf19) {
					pd.setResultMin(dwf20);
					pd.setResultMax(pd.getResultMin() + 5);
					pd.setSrc("C小大小");
				} else {
					pd.setResultMin(dwf20 + (dwf21 - dwf19));
					pd.setResultMax(pd.getResultMin() + 5);
					pd.setSrc("C小大小");
				}
			} else if (dwf19 > dwf20 && dwf20 > dwf21) {
				// 连续降
				pd.setResultMin(dwf20);
				pd.setResultMax(dwf19);
				pd.setSrc("D连续降");
			} else if (dwf19 < dwf20 && dwf20 < dwf21) {
				// 连续涨

				int span2021 = dwf21 - dwf20;
				int span1920 = dwf20 - dwf19;
				if(span2021 > 5 && span1920 > 5 && span2021/span1920 > 3) {
					pd.setResultMin(dwf20);
					pd.setResultMax(dwf21);
				}else {
					if (span2021 > 15 && span1920 < 5) { // 断崖式上涨
						pd.setResultMin(dwf20);
						pd.setResultMax(dwf21);
						pd.setSrc("断崖式上涨");
					} else if (span1920 > 15 && span2021 < 5) { // 断崖式上涨
						pd.setResultMin(dwf21);
						pd.setResultMax(dwf21 + span2021);
						pd.setSrc("断崖式上涨");
					}else{
						int zf = span2021 > span1920 ? (span2021 - span1920) : 0;
						int zdzf = span2021 > span1920 ? span2021 : span1920;
						pd.setResultMin(dwf21 + zdzf);
						pd.setResultMax(dwf21 + zdzf + (zf == 0 ? 5 : zf));
						pd.setSrc("D连续涨");
					}
				}
			} else {
				pd.setResultMin((Math.max(dwf21, Math.max(dwf20, dwf19))));
				pd.setResultMax(pd.getResultMin() + 5);
				pd.setSrc("F其他");
			}
			
			if(pd.getResultMax() - pd.getResultMin() < 5) {
				pd.setResultMax(pd.getResultMin() + 5);
			}
		}

		if (type == 2) { // 专业
			if (pd.getResultMax() - 2 > pd.getResultMin()) {
				pd.setResultMax(pd.getResultMax() - 2);
			}
		}
		return pd;
	}
	
	// TYPE = 1 : 学校， 2=专业
		public PredictBean ycQSDwf(int dwf19, int dwf20, int dwf21, String id, int type) {
			PredictBean pd = new PredictBean();
			pd.setId(id);
			if ((dwf19 + dwf20 + dwf21) == -3) {
				pd.setResultMin(-1);
				pd.setResultMax(999);
				pd.setSrc("三年无");
			} else if ((dwf19 + dwf20) == -2) {
				pd.setResultMin(dwf21 + 5);
				pd.setResultMax(pd.getResultMin() + 3);
				pd.setSrc("1920无数据");
			} else if ((dwf20 + dwf21) == -2) {
				pd.setResultMin(dwf19 + 5);
				pd.setResultMax(pd.getResultMin() + 3);
				pd.setSrc("A2120无数据");
			} else if ((dwf19 + dwf21) == -2) {
				pd.setResultMin(dwf20 + 5);
				pd.setResultMax(pd.getResultMin() + 3);
				pd.setSrc("A1921无数据");
			} else if (dwf19 == -1) {
				if (dwf21 < dwf20) {
					pd.setResultMin(dwf20);
					pd.setResultMax(pd.getResultMin() + 10);
					pd.setSrc("B19无数据");
				} else {
					pd.setResultMin(dwf21 + (dwf21 - dwf20));
					pd.setResultMax(pd.getResultMin() + 10);
					pd.setSrc("B19无数据");
				}
			} else if (dwf20 == -1) {
				if (dwf21 < dwf19) {
					pd.setResultMin(dwf19);
					pd.setResultMax(pd.getResultMin() + 10);
					pd.setSrc("B20无数据");
				} else {
					pd.setResultMin(dwf21 + (dwf21 - dwf19));
					pd.setResultMax(pd.getResultMin() + 10);
					pd.setSrc("B20无数据");
				}
			} else if (dwf21 == -1) {
				if (dwf20 < dwf19) {
					pd.setResultMin(dwf19);
					pd.setResultMax(pd.getResultMin() + 10);
					pd.setSrc("B21无数据");
				} else {
					pd.setResultMin(dwf20 + (dwf20 - dwf19));
					pd.setResultMax(pd.getResultMin() + 10);
					pd.setSrc("B21无数据");
				}
			} else {

				if (dwf19 > dwf20 && dwf20 < dwf21) {
					// 高地位，低
					if (dwf21 < dwf19) {
						pd.setResultMin(dwf21);
						pd.setResultMax(dwf19);
						pd.setSrc("C大小大");
					} else {
						pd.setResultMin(dwf19);
						pd.setResultMax(dwf21);
						pd.setSrc("C大小大");
					}
				} else if (dwf19 < dwf20 && dwf20 > dwf21) {
					// 高地位，高
					if (dwf21 < dwf19) {
						pd.setResultMin(dwf20);
						pd.setResultMax(pd.getResultMin() + 5);
						pd.setSrc("C小大小");
					} else {
						pd.setResultMin(dwf20 + (dwf21 - dwf19));
						pd.setResultMax(pd.getResultMin() + 5);
						pd.setSrc("C小大小");
					}
				} else if (dwf19 > dwf20 && dwf20 > dwf21) {
					// 连续降
					pd.setResultMin(dwf20);
					pd.setResultMax(dwf19);
					pd.setSrc("D连续降");
				} else if (dwf19 < dwf20 && dwf20 < dwf21) {
					// 连续涨

					int span2021 = dwf21 - dwf20;
					int span1920 = dwf20 - dwf19;
					if(span2021 > 5 && span1920 > 5 && span2021/span1920 > 3) {
						pd.setResultMin(dwf20);
						pd.setResultMax(dwf21);
					}else {
						if (span2021 > 15 && span1920 < 5) { // 断崖式上涨
							pd.setResultMin(dwf20);
							pd.setResultMax(dwf21);
							pd.setSrc("断崖式上涨");
						} else if (span1920 > 15 && span2021 < 5) { // 断崖式上涨
							pd.setResultMin(dwf21);
							pd.setResultMax(dwf21 + span2021);
							pd.setSrc("断崖式上涨");
						}else{
							int zf = span2021 > span1920 ? (span2021 - span1920) : 0;
							int zdzf = span2021 > span1920 ? span2021 : span1920;
							pd.setResultMin(dwf21 + zdzf);
							pd.setResultMax(dwf21 + zdzf + (zf == 0 ? 5 : zf));
							pd.setSrc("D连续涨");
						}
					}
				} else {
					pd.setResultMin((Math.max(dwf21, Math.max(dwf20, dwf19))));
					pd.setResultMax(pd.getResultMin() + 5);
					pd.setSrc("F其他");
				}
				
				if(pd.getResultMax() - pd.getResultMin() < 5) {
					pd.setResultMax(pd.getResultMin() + 5);
				}
			}

			if (type == 2) { // 专业
				if (pd.getResultMax() - 2 > pd.getResultMin()) {
					pd.setResultMax(pd.getResultMax() - 2);
				}
			}
			return pd;
		}

	public PredictBean yc2023WC721(int wc19, int wc20, int wc21, String id, int type) {
		PredictBean pd = new PredictBean();
		pd.setId(id);
		if ((wc19 + wc20 + wc21) == -3) {
			pd.setResultMin(-1);
			pd.setResultMax(9999999);
			pd.setSrc("三年无");
		} else if ((wc19 + wc20) == -2) {
			pd.setResultMin((int) (wc21 * 0.95));
			pd.setResultMax((int) (pd.getResultMin() * 0.96));
			pd.setSrc("1920无数据");
		} else if ((wc20 + wc21) == -2) {
			pd.setResultMin((int) (wc19 * 0.95));
			pd.setResultMax((int) (pd.getResultMin() * 0.96));
			pd.setSrc("A2120无数据");
		} else if ((wc19 + wc21) == -2) {
			pd.setResultMin((int) (wc20 * 0.95));
			pd.setResultMax((int) (pd.getResultMin() * 0.96));
			pd.setSrc("A1921无数据");
		} else if (wc19 == -1) {
			pd.setResultMin((int) (wc21 * 0.8) + (int) (wc20 * 0.2));
			pd.setResultMax((int) (pd.getResultMin() * 0.96));
			pd.setSrc("B19无数据");
		} else if (wc20 == -1) {
			pd.setResultMin((int) (wc21 * 0.8) + (int) (wc19 * 0.2));
			pd.setResultMax((int) (pd.getResultMin() * 0.96));
			pd.setSrc("B20无数据");
		} else if (wc21 == -1) {
			pd.setResultMin((int) (wc20 * 0.8) + (int) (wc19 * 0.2));
			pd.setResultMax((int) (pd.getResultMin() * 0.96));
			pd.setSrc("B21无数据");
		} else {
			if (wc19 > wc20 && wc20 < wc21) {
				pd.setResultMin((int) (wc21 * 0.7) + (int) (wc20 * 0.2) + (int) (wc19 * 0.1));
				pd.setResultMax((int) (pd.getResultMin() * 0.96));
				pd.setSrc("大小大");
			} else if (wc19 < wc20 && wc20 > wc21) {
				pd.setResultMin((int) (wc21 * 0.7) + (int) (wc20 * 0.2) + (int) (wc19 * 0.1));
				pd.setResultMax((int) (pd.getResultMin() * 0.96));
				pd.setSrc("小大小");
			} else if (wc19 > wc20 && wc20 > wc21) {
				pd.setResultMin((int) (wc21 * 0.7) + (int) (wc20 * 0.2) + (int) (wc19 * 0.1));
				pd.setResultMax((int) (pd.getResultMin() * 0.96));
				pd.setSrc("D连续降");
			} else if (wc19 < wc20 && wc20 < wc21) {
				int latest = wc20 - wc21;
				int previos = wc19 - wc20;

				if (previos * 3 < latest) { // 断崖式上涨
					pd.setResultMin((int) (wc21 * 0.5) + (int) (wc20 * 0.4) + (int) (wc19 * 0.1));
					pd.setResultMax((int) (pd.getResultMin() * 0.96));
					pd.setSrc("断崖式上涨");
				} else {
					pd.setResultMin((int) (wc21 * 0.7) + (int) (wc20 * 0.2) + (int) (wc19 * 0.1));
					pd.setResultMax((int) (pd.getResultMin() * 0.96));
					pd.setSrc("D连续涨");
				}
			} else {
				pd.setResultMin((int) (wc21 * 0.7) + (int) (wc20 * 0.2) + (int) (wc19 * 0.1));
				pd.setResultMax((int) (pd.getResultMin() * 0.96));
				pd.setSrc("F其他");
			}
		}

		if (type == 2) {
			if ((int) (pd.getResultMax() * 1.08) < pd.getResultMin()) {
				pd.setResultMax((int) (pd.getResultMax() * 1.08));
			}
		}
		return pd;
	}

	public static boolean isChinese(char c) {
		Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
		if (ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
				|| ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
				|| ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A
				|| ub == Character.UnicodeBlock.GENERAL_PUNCTUATION
				|| ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION
				|| ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS) {
			return true;
		}
		return false;
	}

	public static boolean isChinese(String strName) {
		char[] ch = strName.toCharArray();
		for (int i = 0; i < ch.length; i++) {
			char c = ch[i];
			if (isChinese(c) == true) {
				return true;
			}
		}
		return false;
	}

	public static void main(String[] args) {
		PredictBean yx = new YGDataPatchDBTools().yc2023Dwf(607, 619, 620, "1", 1);
		PredictBean zy = new YGDataPatchDBTools().yc2023Dwf(607, 619, 620, "1", 2);
		System.out.println(yx.getResultMin()+" -YX " + yx.getResultMax());
		System.out.println(zy.getResultMin()+" -ZY " + zy.getResultMax());
	}

	public static void convertRESULTC() {
		try {
			List<String> listx = new java.util.ArrayList<>();
			listx.add("L");
			listx.add("W");
			List<String> list = new java.util.ArrayList<>();
			YGJDBC jdbc = new YGJDBC();
			int count = 0;
			for (String x : listx) {
				for (int k = 2020; k <= 2022; k++) {
					String path = x + k;
					File file = new File("E://T0613//K//" + path+".txt");
	
					BufferedReader br = new BufferedReader(new FileReader(file));
					String tt = null;
					StringBuffer sb = new StringBuffer();
					while ((tt = br.readLine()) != null) {
						count++;
						
						if(tt.length() < "W1,2020|600.116|600.116|604|D857.653".length()) {
							continue;
						}
						
						
						//350.092 |350.092|373|D567.023|380379183 4279 175
						//150.000|155.066 309 D371.910
						String prefix = tt.substring(0,7);
						String subfix = tt.substring(7).trim();
						String[] xt = prefix.split(",");
						if(subfix.substring(0,1).equals("|")) {
							subfix = subfix.substring(1).trim();
						}
						String[] xt2 = subfix.split("\\|");
						if(xt2.length < 4) {
							continue;
						}
						String slx = "";
						if(xt2[1].length() == 7) {
							slx = xt2[1].substring(0,3);
						}else {
							continue;
						}
						
						String slxdwf;
						if(xt2[3].length() == 8) {
							slxdwf = xt2[3].substring(1,4);
						}else {
							continue;
						}
						
						//150.000|200.065|303|D418.565
						
						String ddx = "";
						String pjf = "";
						
						String listForUpdate = xt[1] + "," + ddx+ "," + slx+ "," + pjf+ "," + slxdwf+ "," + xt[0]+ "," + path;

						
						list.add(listForUpdate);

					}
					
					
				}
			}
			
		
			
			jdbc.insert2023DwfTemp(list);

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static void convertRESULTB(String path) {
		try {
			File file = new File("E://DWF//RESULT//" + path);
			String[] fileTemp = file.list();
			YGJDBC jdbc = new YGJDBC();
			List<String> list = new java.util.ArrayList<>();

			for (int i = 0; i < fileTemp.length; i++) {
				System.out.println(fileTemp[i]);

				BufferedReader br = new BufferedReader(new FileReader(new File(file, fileTemp[i])));
				String tt = null;
				StringBuffer sb = new StringBuffer();

				while ((tt = br.readLine()) != null) {

					// String nf, String ddx, String slx, String pjf, String slxdwf,String pckl
					if (tt.length() == "L1,2021,532.104,532.104,D666.787".length() && tt.indexOf("D") != -1) {
						String[] el = tt.split(",");
						String nf = el[1];
						String ddx = el[2];
						String slx = el[3];
						String pjf = "999";
						String slxdwf = el[4];
						String pckl = el[0];
						String filename = fileTemp[i];

						list.add(nf + "," + ddx + "," + slx + "," + pjf + "," + slxdwf + "," + pckl + "," + filename);

					}

				}
			}

			jdbc.insert2023DwfTemp(list);

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static void convertKA() {
		try {

			StringBuffer L2020 = new StringBuffer();
			StringBuffer W2020 = new StringBuffer();
			StringBuffer L2021 = new StringBuffer();
			StringBuffer W2021 = new StringBuffer();
			StringBuffer L2022 = new StringBuffer();
			StringBuffer W2022 = new StringBuffer();

			List<String> list = new java.util.ArrayList<>();
			list.add("L");
			list.add("W");
			for (String x : list) {

				File file = new File("E://T0613//" + x);
				String[] fileTemp = file.list();

				// L:0-30 一本， 31-82 二本，
				// W:0-18一本， 19-62 二本

				for (int i = 0; i < fileTemp.length; i++) {
					System.out.println(fileTemp[i]);
					String klpc = "";
					int sub = fileTemp[i].indexOf(".txt");
					int ind = Tools.getInt(fileTemp[i].substring(sub - 2, sub));
					if (x.equals("L")) {
						if (ind <= 30) {
							klpc = "L1";
						} else if (ind > 30 && ind <= 82) {
							klpc = "L2";
						} else {
							klpc = "L3";
						}
					}

					if (x.equals("W")) {
						if (ind <= 18) {
							klpc = "W1";
						} else if (ind > 18 && ind <= 62) {
							klpc = "W2";
						} else {
							klpc = "W3";
						}
					}

					BufferedReader br = new BufferedReader(
							new InputStreamReader(new FileInputStream(new File(file, fileTemp[i])), "UTF-8"));
					String tt = null;
					StringBuffer sb = new StringBuffer();

					boolean validStart = false;

					// 读每个文件
					while ((tt = br.readLine()) != null) {
						if (tt.indexOf("(V2") != -1) {
							validStart = true;
						}

						if (!validStart) {
							continue;
						}

						if (tt.indexOf("2022、2021、2020") != -1) {
							break;
						}

						if (tt.indexOf("2020") != -1) {
							int f = tt.indexOf("2020");
							int slx = tt.indexOf(".");
							int dwf = tt.indexOf("D");
							if (slx == -1 || dwf == -1 || f == -1) {
								continue;
							}
							// System.out.println(tt.length() +","+f+","+dwf);
							String temp = tt.substring(f);

							if (klpc.indexOf("L") != -1) {
								L2020.append(klpc + "," + temp + "\r\n");
							} else {
								W2020.append(klpc + "," + temp + "\r\n");
							}

						}

						if (tt.indexOf("2021") != -1) {
							int f = tt.indexOf("2021");
							int slx = tt.indexOf(".");
							int dwf = tt.indexOf("D");
							if (slx == -1 || dwf == -1 || f == -1) {
								continue;
							}
							// System.out.println(tt.length() +","+f+","+dwf);
							String temp = tt.substring(f);

							if (klpc.indexOf("L") != -1) {
								L2021.append(klpc + "," + temp + "\r\n");
							} else {
								W2021.append(klpc + "," + temp + "\r\n");
							}
						}

						if (tt.indexOf("2022") != -1) {
							int f = tt.indexOf("2022");
							int slx = tt.indexOf(".");
							int dwf = tt.indexOf("D");
							if (slx == -1 || dwf == -1 || f == -1) {
								continue;
							}
							// System.out.println(tt.length() +","+f+","+dwf);
							String temp = tt.substring(f);

							if (klpc.indexOf("L") != -1) {
								L2022.append(klpc + "," + temp + "\r\n");
							} else {
								W2022.append(klpc + "," + temp + "\r\n");
							}
						}

					}

				}
				writeTempFile(new File("E://T0613//K", "L2020.txt"), L2020);
				writeTempFile(new File("E://T0613//K", "L2021.txt"), L2021);
				writeTempFile(new File("E://T0613//K", "L2022.txt"), L2022);
				writeTempFile(new File("E://T0613//K", "W2020.txt"), W2020);
				writeTempFile(new File("E://T0613//K", "W2021.txt"), W2021);
				writeTempFile(new File("E://T0613//K", "W2022.txt"), W2022);

			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static void convertA(String path) {
		try {

			File file = new File("E://DWF//SRC//" + path);
			String[] fileTemp = file.list();

			for (int i = 0; i < fileTemp.length; i++) {
				System.out.println(fileTemp[i]);
				if (fileTemp[i].indexOf("_text.txt") == -1) {
					continue;
				}
				BufferedReader br = new BufferedReader(new FileReader(new File(file, fileTemp[i])));
				String tt = null;
				StringBuffer sb = new StringBuffer();
				YGJDBC jdbc = new YGJDBC();

				boolean isStart = false;
				boolean validStart = false;
				String klpc = "";
				while ((tt = br.readLine()) != null) {
					if (tt.indexOf("(V2.0)") != -1) {
						validStart = true;
					}

					if (tt.equals("录取资料统计篇(下)——(表一)理科本科第一批")) {
						klpc = "L1";
					} else if (tt.equals("录取资料统计篇(下)——(表一)理科本科第二批")) {
						klpc = "L2";
					} else if (tt.equals("录取资料统计篇(下)——(表一)理科高专批")) {
						klpc = "L3";
					} else if (tt.equals("录取资料统计篇(下)——(表一)文科本科第一批")) {
						klpc = "W1";
					} else if (tt.equals("录取资料统计篇(下)——(表一)文科本科第二批")) {
						klpc = "W2";
					} else if (tt.equals("录取资料统计篇(下)——(表一)文科高专批")) {
						klpc = "W3";
					}

					if (!validStart) {
						continue;
					}

					if (tt.indexOf("2022、2021、2020") != -1) {
						break;
					}

					String line = "";
					if (tt.length() == 4 && (tt.equals("2020") || tt.equals("2021") || tt.equals("2022"))) {
						// String ddx = br.readLine();
						// String slx = br.readLine();
						// String pjf = br.readLine();
						// String slxdwf = br.readLine();
						// jdbc.insert2023DwfTemp(tt, ddx, slx, pjf, slxdwf, "L1");
						sb.append(klpc + "," + tt);
						isStart = true;
					}

					if ((tt.indexOf("学") != -1) && tt.length() > 3) {
						sb.append("," + tt + "\r\n");
					}

					if (tt.length() == 7 && tt.substring(3, 4).equals(".") && isStart) {
						sb.append("," + tt);
					}

					if (isStart && tt.length() == 8 && tt.substring(4, 5).equals(".")
							&& tt.substring(0, 1).equals("D")) {
						sb.append("," + tt + "\r\n");
						isStart = false;
					}

				}

				writeTempFile(new File("E://DWF//RESULT//" + path, fileTemp[i]), sb);
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static void convertAAA(String path) {
		try {

			File file = new File("E://DWF//SRC//" + path);
			String[] fileTemp = file.list();

			for (int i = 0; i < fileTemp.length; i++) {
				System.out.println(fileTemp[i]);
				if (fileTemp[i].indexOf("_text.txt") == -1) {
					continue;
				}
				BufferedReader br = new BufferedReader(new FileReader(new File(file, fileTemp[i])));
				String tt = null;
				StringBuffer sb = new StringBuffer();
				YGJDBC jdbc = new YGJDBC();

				boolean isStart = false;
				boolean validStart = false;
				String klpc = "";
				while ((tt = br.readLine()) != null) {
					if (tt.indexOf("(V2.0)") != -1) {
						validStart = true;
					}

					if (tt.equals("录取资料统计篇(下)——(表一)理科本科第一批")) {
						klpc = "L1";
					} else if (tt.equals("录取资料统计篇(下)——(表一)理科本科第二批")) {
						klpc = "L2";
					} else if (tt.equals("录取资料统计篇(下)——(表一)理科高专批")) {
						klpc = "L3";
					} else if (tt.equals("录取资料统计篇(下)——(表一)文科本科第一批")) {
						klpc = "W1";
					} else if (tt.equals("录取资料统计篇(下)——(表一)文科本科第二批")) {
						klpc = "W2";
					} else if (tt.equals("录取资料统计篇(下)——(表一)文科高专批")) {
						klpc = "W3";
					}

					if (!validStart) {
						continue;
					}

					if (tt.indexOf("2022、2021、2020") != -1) {
						break;
					}

					String line = "";
					if (tt.length() == 4 && (tt.equals("2020") || tt.equals("2021") || tt.equals("2022"))) {
						// String ddx = br.readLine();
						// String slx = br.readLine();
						// String pjf = br.readLine();
						// String slxdwf = br.readLine();
						// jdbc.insert2023DwfTemp(tt, ddx, slx, pjf, slxdwf, "L1");
						sb.append(klpc + "," + tt);
						isStart = true;
					}

					if ((tt.indexOf("学") != -1) && tt.length() > 3) {
						sb.append("," + tt + "\r\n");
					}

					if (tt.length() == 7 && tt.substring(3, 4).equals(".") && isStart) {
						sb.append("," + tt);
					}

					if (isStart && tt.length() == 8 && tt.substring(4, 5).equals(".")
							&& tt.substring(0, 1).equals("D")) {
						sb.append("," + tt + "\r\n");
						isStart = false;
					}

				}

				writeTempFile(new File("E://DWF//RESULT//" + path, fileTemp[i]), sb);
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private static void writeTempFile(File file, StringBuffer sb) {
		try {
			if (!file.exists()) {
				file.createNewFile();
			}
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
}
