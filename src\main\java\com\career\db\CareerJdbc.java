package com.career.db;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.UUID;

import com.career.db.career.CareerCard;
import com.career.db.career.MajorHistory;
import com.career.db.career.MajorPlan;
import com.career.db.career.SchoolMajor;
import com.career.db.career.University;
import com.career.utils.Tools;

public class CareerJdbc {
	
	static String URL = JDBCConstants.URL;
	static String USER = JDBCConstants.USER;
	static String PASSWD = JDBCConstants.PASSWD;
	static HashMap<String, Integer> LX_SEARCH_CONDITION_MAP = new HashMap<>();
	public static HashMap<String, University> MAP_UNIVERSITY = new HashMap<>();
	
	static {
		try {
			Class.forName("com.mysql.cj.jdbc.Driver");
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		
		LX_SEARCH_CONDITION_MAP.put("GPA-不限", 0);
		LX_SEARCH_CONDITION_MAP.put("GPA-4.0-5.0", 4);
		LX_SEARCH_CONDITION_MAP.put("GPA-3.0-3.9", 3);
		LX_SEARCH_CONDITION_MAP.put("GPA-2.0-2.9", 2);
		LX_SEARCH_CONDITION_MAP.put("GPA-2.0以下", 1);
		
		LX_SEARCH_CONDITION_MAP.put("YYSP-不限", 1);
		LX_SEARCH_CONDITION_MAP.put("YYSP-雅思8.0-9.0/托福110-120", 5);
		LX_SEARCH_CONDITION_MAP.put("YYSP-雅思6.5-7.5/托福79-109", 4);
		LX_SEARCH_CONDITION_MAP.put("YYSP-雅思5.0-6.0/托福35-78", 3);
		LX_SEARCH_CONDITION_MAP.put("YYSP-雅思低于5.0/托福低于35", 2);
		
		List<University> uList = listAllSchool();
		for(University u : uList) {
			MAP_UNIVERSITY.put(u.getSchool_name(), u);
		}
	}
	
	//随机出来十个企业
	public List<JiuyeBean> pickJiuyeBySchoolName(String school) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<JiuyeBean> list = new ArrayList<>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT distinct * from career_jiuye x WHERE x.yxmc LIKE ? ORDER BY RAND() LIMIT 10";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + school + "%");
			rs = ps.executeQuery();
			JiuyeBean bean = null;
			while (rs.next()) {
				bean = new JiuyeBean();
				bean.setDw(rs.getString("dw"));
				bean.setGw(rs.getString("gw"));
				bean.setXb(rs.getString("xb"));
				bean.setXl(rs.getString("xl"));
				bean.setXm(rs.getString("xm"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZyfx(rs.getString("zyfx"));
				bean.setZymc(rs.getString("zymc"));
				bean.setSshy(rs.getString("sshy"));
				bean.setSsgs(rs.getString("ssgs"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}


	//随机出来十个企业
	public List<JiuyeBean> pickJiuyeByZyName(String zymc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<JiuyeBean> list = new ArrayList<>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT distinct * from career_jiuye x WHERE x.zymc LIKE ? or x.zyfx LIKE ? ORDER BY RAND() LIMIT 10";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + zymc + "%");
			ps.setString(2, "%" + zymc + "%");
			rs = ps.executeQuery();
			JiuyeBean bean = null;
			while (rs.next()) {
				bean = new JiuyeBean();
				bean.setDw(rs.getString("dw"));
				bean.setGw(rs.getString("gw"));
				bean.setXb(rs.getString("xb"));
				bean.setXl(rs.getString("xl"));
				bean.setXm(rs.getString("xm"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZyfx(rs.getString("zyfx"));
				bean.setZymc(rs.getString("zymc"));
				bean.setSshy(rs.getString("sshy"));
				bean.setSsgs(rs.getString("ssgs"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public List<LXSchoolBean> listLXSchoolByCondition(String dq, String gpa, String eng, String bgt, String yszy, String keywords, int pager) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LXSchoolBean> list = new ArrayList<>();
		
		int resultCnt = 0;
		try {
			String COUNT = "SELECT count(*) ";
			String SELECT = "SELECT * ";
			String SQL = "FROM career_lx_university_xdf_temp x where country_ext like ? and x.search_pscj <= ? and x.search_yysp <= ? and x.search_jtys <= ? and (x.ename like ? or x.cname like ?) ";
			if(yszy.length() > 1) {
				SQL = "FROM career_lx_university_xdf_temp x where x.cname in (select distinct k1.yxmc from career_lx_major_qs_rank k1,career_lx_major_qs k2 where k1.major_cn = k2.major_cn and k2.major_in_china = '"+yszy+"') and country_ext like ? and x.search_pscj <= ? and x.search_yysp <= ? and x.search_jtys <= ? and (x.ename like ? or x.cname like ?) ";
			}
			String ORDER = "order by worldRankValue ASC, localRankValue ASC, ename ASC LIMIT ?,?";

			System.out.println(SQL);
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			ps = conn.prepareStatement(COUNT + SQL);
			ps.setString(1, "%" + dq + "%");
			ps.setInt(2, LX_SEARCH_CONDITION_MAP.get("PSCJ-" + gpa));
			ps.setInt(3, LX_SEARCH_CONDITION_MAP.get("YYSP-" + eng));
			ps.setInt(4, LX_SEARCH_CONDITION_MAP.get("JTYS-" + bgt));
			ps.setString(5, "%" + keywords + "%");
			ps.setString(6, "%" + keywords + "%");
			
			rs = ps.executeQuery();
			while(rs.next()) {
				resultCnt = rs.getInt(1);
			}
			rs.close();
			rs = null;
			ps.close();
			ps = null;
			
			ps = conn.prepareStatement(SELECT + SQL + ORDER);
			ps.setString(1, "%" + dq + "%");
			ps.setInt(2, LX_SEARCH_CONDITION_MAP.get("PSCJ-" + gpa));
			ps.setInt(3, LX_SEARCH_CONDITION_MAP.get("YYSP-" + eng));
			ps.setInt(4, LX_SEARCH_CONDITION_MAP.get("JTYS-" + bgt));
			ps.setString(5, "%" + keywords + "%");
			ps.setString(6, "%" + keywords + "%");
			
			ps.setInt(7, (pager - 1) * 15);
			ps.setInt(8, 15);
			
			rs = ps.executeQuery();
			LXSchoolBean bean = null;
			while (rs.next()) {
				bean = new LXSchoolBean();
				bean.setCountry(rs.getString("country"));
				bean.setRegion(rs.getString("region"));
				
				bean.setGpa(rs.getString("gpa"));
				bean.setIetls(rs.getString("ietls"));
				bean.setToefl(rs.getString("toefl"));
				
				bean.setNature(rs.getString("nature"));
				bean.setWebsite(rs.getString("website"));
				
				bean.setWorldRankName(rs.getString("worldRankName"));
				bean.setWorldRankValue(rs.getInt("worldRankValue"));
				
				bean.setLocalRankName(rs.getString("localRankName"));
				bean.setLocalRankValue(rs.getInt("localRankValue"));
				
				bean.setSchool_name(rs.getString("ename"));
				bean.setSchool_name_cn(rs.getString("cname"));
				
				bean.setSummary(rs.getString("summary"));
				bean.setMajorExcellent(rs.getString("major_excellent"));
				bean.setMajorHot(rs.getString("major_hot"));
				bean.setYxTese(rs.getString("yx_tese"));
				bean.setIdOuter(rs.getString("id_outer"));
				
				bean.setRecordSUM(resultCnt);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	
	public List<LXSchoolBean> listLXSchoolByCondition2(String dq, String gpa, String eng, String bgt, String yszy, String keywords, int pager) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LXSchoolBean> list = new ArrayList<>();
		
		int resultCnt = 0;
		try {
			String COUNT = "SELECT count(*) ";
			String SELECT = "SELECT * ";
			String SQL = "FROM career_lx_university_xdf_temp x where country_ext like ? and x.search_pscj <= ? and x.search_yysp <= ? and x.search_jtys <= ? and (x.ename like ? or x.cname like ?) ";
			if(yszy.length() > 1) {
				SQL = "FROM career_lx_university_xdf_temp x where x.cname in (select distinct k1.yxmc from career_lx_major_qs_rank k1,career_lx_major_qs k2 where k1.major_cn = k2.major_cn and k2.major_in_china = '"+yszy+"') and country_ext like ? and x.search_pscj <= ? and x.search_yysp <= ? and x.search_jtys <= ? and (x.ename like ? or x.cname like ?) ";
			}
			String ORDER = "order by worldRankValue ASC, localRankValue ASC, ename ASC LIMIT ?,?";

			System.out.println(SQL);
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			ps = conn.prepareStatement(COUNT + SQL);
			ps.setString(1, "%" + dq + "%");
			ps.setInt(2, LX_SEARCH_CONDITION_MAP.get("PSCJ-" + gpa));
			ps.setInt(3, LX_SEARCH_CONDITION_MAP.get("YYSP-" + eng));
			ps.setInt(4, LX_SEARCH_CONDITION_MAP.get("JTYS-" + bgt));
			ps.setString(5, "%" + keywords + "%");
			ps.setString(6, "%" + keywords + "%");
			
			rs = ps.executeQuery();
			while(rs.next()) {
				resultCnt = rs.getInt(1);
			}
			rs.close();
			rs = null;
			ps.close();
			ps = null;
			
			ps = conn.prepareStatement(SELECT + SQL + ORDER);
			ps.setString(1, "%" + dq + "%");
			ps.setInt(2, LX_SEARCH_CONDITION_MAP.get("PSCJ-" + gpa));
			ps.setInt(3, LX_SEARCH_CONDITION_MAP.get("YYSP-" + eng));
			ps.setInt(4, LX_SEARCH_CONDITION_MAP.get("JTYS-" + bgt));
			ps.setString(5, "%" + keywords + "%");
			ps.setString(6, "%" + keywords + "%");
			
			ps.setInt(7, (pager - 1) * 40);
			ps.setInt(8, 40);
			
			rs = ps.executeQuery();
			LXSchoolBean bean = null;
			while (rs.next()) {
				bean = new LXSchoolBean();
				bean.setCountry(rs.getString("country"));
				bean.setRegion(rs.getString("region"));
				
				bean.setGpa(rs.getString("gpa"));
				bean.setIetls(rs.getString("ietls"));
				bean.setToefl(rs.getString("toefl"));
				
				bean.setNature(rs.getString("nature"));
				bean.setWebsite(rs.getString("website"));
				
				bean.setWorldRankName(rs.getString("worldRankName"));
				bean.setWorldRankValue(rs.getInt("worldRankValue"));
				
				bean.setLocalRankName(rs.getString("localRankName"));
				bean.setLocalRankValue(rs.getInt("localRankValue"));
				
				bean.setSchool_name(rs.getString("ename"));
				bean.setSchool_name_cn(rs.getString("cname"));
				
				bean.setSummary(rs.getString("summary"));
				bean.setMajorExcellent(rs.getString("major_excellent"));
				bean.setMajorHot(rs.getString("major_hot"));
				bean.setYxTese(rs.getString("yx_tese"));
				bean.setIdOuter(rs.getString("id_outer"));
				
				bean.setRecordSUM(resultCnt);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<LXShoolRank> loadAllChinaUniversityRank(String rankType, int year) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LXShoolRank> list = new ArrayList<>();
		try {
			String SQL = "SELECT * FROM career_lx_university_qs_rank x where rk_type = ? and rk_year = ? and country = 'China (Mainland)'";

			System.out.println(SQL);
			conn = DriverManager.getConnection(URL, USER, PASSWD);

			ps = conn.prepareStatement(SQL);
			ps.setString(1, rankType);
			ps.setInt(2, year);
			
			rs = ps.executeQuery();
			LXShoolRank bean = null;
			while (rs.next()) {
				bean = new LXShoolRank();
				bean.setRankDesc(rs.getString("rk_desc"));
				bean.setRankNum(rs.getInt("rk_num"));
				bean.setRankType(rs.getString("rk_type"));
				bean.setRankYear(rs.getInt("rk_year"));
				bean.setYxmc(rs.getString("yxmc_cn"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public List<LXSchoolMajorRank> loadUniversityMajorQSRank(String selectedSchoolCnName, int year) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LXSchoolMajorRank> list = new ArrayList<>();
		try {
			String SQL = "SELECT x.*, y.major_in_china, y.major_in_china_label FROM career_lx_major_qs_rank x left join career_lx_major_qs y on x.major_cn = y.major_cn where x.yxmc = ? and x.rank_year = ? order by y.major_in_china, y.major_in_china_label";

			System.out.println(SQL);
			conn = DriverManager.getConnection(URL, USER, PASSWD);

			ps = conn.prepareStatement(SQL);
			ps.setString(1, selectedSchoolCnName);
			ps.setInt(2, year);
			
			rs = ps.executeQuery();
			LXSchoolMajorRank bean = null;
			while (rs.next()) {
				bean = new LXSchoolMajorRank();
				bean.setRankDesc(rs.getString("rank_desc"));
				bean.setRankNum(rs.getInt("rank_num"));
				bean.setRankYear(rs.getInt("rank_year"));
				bean.setMajorCn(rs.getString("major_cn"));
				bean.setYxmc(selectedSchoolCnName);
				bean.setMajorInChina(rs.getString("major_in_china"));
				bean.setMajorInChinaLabel(rs.getString("major_in_china_label"));
				
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public List<LXSchoolMajorRank> loadAllChinaUniversityMajorQSRank(int year) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LXSchoolMajorRank> list = new ArrayList<>();
		try {
			String SQL = "SELECT * from career_lx_major_qs_rank x WHERE x.country = 'China (Mainland)' and rank_year = ?";

			System.out.println(SQL);
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, year);
			
			rs = ps.executeQuery();
			LXSchoolMajorRank bean = null;
			while (rs.next()) {
				bean = new LXSchoolMajorRank();
				bean.setRankDesc(rs.getString("rank_desc"));
				bean.setRankNum(rs.getInt("rank_num"));
				bean.setRankYear(rs.getInt("rank_year"));
				bean.setMajorCn(rs.getString("major_cn"));
				bean.setYxmc(rs.getString("yxmc"));
				
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<LXSchoolMajorRank> loadUniversityMajorBestQSRank(String selectedSchoolCnName, int year) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LXSchoolMajorRank> list = new ArrayList<>();
		try {
			String SQL = "SELECT x.*, y.major_in_china, y.major_in_china_label FROM career_lx_major_qs_rank x left join career_lx_major_qs y on x.major_cn = y.major_cn where x.yxmc_cn_org = ? and x.rank_year = ? order by x.rank_num ASC, y.major_in_china, y.major_in_china_label limit 0,3";

			System.out.println(SQL);
			conn = DriverManager.getConnection(URL, USER, PASSWD);

			ps = conn.prepareStatement(SQL);
			ps.setString(1, selectedSchoolCnName);
			ps.setInt(2, year);
			
			rs = ps.executeQuery();
			LXSchoolMajorRank bean = null;
			while (rs.next()) {
				bean = new LXSchoolMajorRank();
				bean.setRankDesc(rs.getString("rank_desc"));
				bean.setRankNum(rs.getInt("rank_num"));
				bean.setRankYear(rs.getInt("rank_year"));
				bean.setYxmc(selectedSchoolCnName);
				bean.setMajorInChina(rs.getString("major_in_china"));
				bean.setMajorInChinaLabel(rs.getString("major_in_china_label"));
				
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public LXSchoolBean pickLXSchoolByOuterID(String outerID) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			String SQL = "SELECT * FROM career_lx_university_xdf_temp x where id_outer = ?";

			System.out.println(SQL);
			conn = DriverManager.getConnection(URL, USER, PASSWD);

			ps = conn.prepareStatement(SQL);
			ps.setString(1, outerID);
			rs = ps.executeQuery();
			LXSchoolBean bean = null;
			while (rs.next()) {
				bean = new LXSchoolBean();
				bean.setCountry(rs.getString("country"));
				bean.setRegion(rs.getString("region"));
				
				bean.setGpa(rs.getString("gpa"));
				bean.setIetls(rs.getString("ietls"));
				bean.setToefl(rs.getString("toefl"));
				
				bean.setNature(rs.getString("nature"));
				bean.setWebsite(rs.getString("website"));
				
				bean.setWorldRankName(rs.getString("worldRankName"));
				bean.setWorldRankValue(rs.getInt("worldRankValue"));
				
				bean.setLocalRankName(rs.getString("localRankName"));
				bean.setLocalRankValue(rs.getInt("localRankValue"));
				
				bean.setSchool_name(rs.getString("ename"));
				bean.setSchool_name_cn(rs.getString("cname"));
				
				bean.setSummary(rs.getString("summary"));
				bean.setMajorExcellent(rs.getString("major_excellent"));
				bean.setMajorHot(rs.getString("major_hot"));
				bean.setYxTese(rs.getString("yx_tese"));
				bean.setIdOuter(rs.getString("id_outer"));
				
				return bean;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	
	public List<LXSchoolAttrBean> pickLXSchoolAttrByOuterID(String outerID) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<LXSchoolAttrBean> list = new ArrayList<>();

		try {
			String SQL = "SELECT * FROM career_lx_university_xdf_attr_temp x where x.id_outer = ? order by x.attr_type, x.attr_ind, x.attr_name";
			System.out.println(SQL);
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, outerID);
			rs = ps.executeQuery();
			LXSchoolAttrBean bean = null;
			while (rs.next()) {
				bean = new LXSchoolAttrBean();
				bean.setAttrInd(rs.getString("attr_ind"));
				bean.setAttrName(rs.getString("attr_name"));
				bean.setAttrType(rs.getString("attr_type"));
				bean.setAttrValue(rs.getString("attr_value"));
				bean.setIdOuter(rs.getString("id_outer"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<University> listSchoolByCondition(String sf, String ts, String lx, String keywords, int pager) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<University> list = new ArrayList<>();
		int resultCnt = 0;
		try {
			String COUNT = "SELECT count(*) ";
			String SELECT = "SELECT * ";
			String SQL = "FROM career_university x where x.province like ? and x.feature like ? and x.feature like ? and x.school_name like ? ";
			String ORDER = "order by num_master DESC, num_doctor DESC, school_name LIMIT ?,?";

			System.out.println(SQL);
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			ps = conn.prepareStatement(COUNT + SQL);
			ps.setString(1, "%" + sf + "%");
			ps.setString(2, "%" + ts + "%");
			ps.setString(3, "%" + lx + "%");
			ps.setString(4, "%" + keywords + "%");
			rs = ps.executeQuery();
			while(rs.next()) {
				resultCnt = rs.getInt(1);
			}
			rs.close();
			rs = null;
			ps.close();
			ps = null;
			
			ps = conn.prepareStatement(SELECT + SQL + ORDER);
			ps.setString(1, "%" + sf + "%");
			ps.setString(2, "%" + ts + "%");
			ps.setString(3, "%" + lx + "%");
			ps.setString(4, "%" + keywords + "%");
			
			ps.setInt(5, (pager - 1) * 20);
			ps.setInt(6, 20);
			
			rs = ps.executeQuery();
			University bean = null;
			while (rs.next()) {
				bean = new University();
				bean.setBelongsTo(rs.getString("belongsTo"));
				bean.setContent_id(rs.getString("content_id"));
				bean.setFeature(rs.getString("feature"));
				bean.setCreate_date(rs.getInt("create_date"));
				bean.setIntro(rs.getString("intro"));
				bean.setIs_apply(rs.getInt("is_apply"));
				bean.setNum_doctor(rs.getInt("num_doctor"));
				bean.setNum_doctor_2nd(rs.getInt("num_doctor_2nd"));
				bean.setNum_lab(rs.getInt("num_lab"));
				bean.setNum_master(rs.getInt("num_master"));
				bean.setNum_master_2nd(rs.getInt("num_master_2nd"));
				bean.setNum_subject(rs.getInt("num_subject"));
				bean.setProvince(rs.getString("province"));
				bean.setRank(rs.getString("rank_info"));
				bean.setSchool_address(rs.getString("school_address"));
				bean.setSchool_email(rs.getString("school_email"));
				bean.setSchool_id(rs.getInt("school_id"));
				bean.setSchool_name(rs.getString("school_name"));
				bean.setSchool_phone(rs.getString("school_phone"));
				bean.setSchool_site(rs.getString("school_site"));
				bean.setSchool_space(rs.getInt("school_space"));
				bean.setTop_value(rs.getInt("top_value"));
				bean.setRecordSUM(resultCnt);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public static List<University> listAllSchool() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<University> list = new ArrayList<>();
		int resultCnt = 0;
		try {

			String SQL = "SELECT * FROM career_university x";

			System.out.println(SQL);
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			ps = conn.prepareStatement(SQL);
			
			rs = ps.executeQuery();
			University bean = null;
			while (rs.next()) {
				bean = new University();
				bean.setBelongsTo(rs.getString("belongsTo"));
				bean.setContent_id(rs.getString("content_id"));
				bean.setFeature(rs.getString("feature"));
				bean.setCreate_date(rs.getInt("create_date"));
				bean.setIntro(rs.getString("intro"));
				bean.setIs_apply(rs.getInt("is_apply"));
				bean.setNum_doctor(rs.getInt("num_doctor"));
				bean.setNum_doctor_2nd(rs.getInt("num_doctor_2nd"));
				bean.setNum_lab(rs.getInt("num_lab"));
				bean.setNum_master(rs.getInt("num_master"));
				bean.setNum_master_2nd(rs.getInt("num_master_2nd"));
				bean.setNum_subject(rs.getInt("num_subject"));
				bean.setProvince(rs.getString("province"));
				bean.setRank(rs.getString("rank_info"));
				bean.setSchool_address(rs.getString("school_address"));
				bean.setSchool_email(rs.getString("school_email"));
				bean.setSchool_id(rs.getInt("school_id"));
				bean.setSchool_name(rs.getString("school_name"));
				bean.setSchool_phone(rs.getString("school_phone"));
				bean.setSchool_site(rs.getString("school_site"));
				bean.setSchool_space(rs.getInt("school_space"));
				bean.setTop_value(rs.getInt("top_value"));
				bean.setRecordSUM(resultCnt);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public University pickSchool(String schoolName, int pager) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		University bean = null;
		try {
			String SQL = "SELECT * FROM career_university x where x.school_name = ? ";

			System.out.println(SQL);
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, schoolName);
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = new University();
				bean.setBelongsTo(rs.getString("belongsTo"));
				bean.setContent_id(rs.getString("content_id"));
				bean.setFeature(rs.getString("feature"));
				bean.setCreate_date(rs.getInt("create_date"));
				bean.setIntro(rs.getString("intro"));
				bean.setIs_apply(rs.getInt("is_apply"));
				bean.setNum_doctor(rs.getInt("num_doctor"));
				bean.setNum_doctor_2nd(rs.getInt("num_doctor_2nd"));
				bean.setNum_lab(rs.getInt("num_lab"));
				bean.setNum_master(rs.getInt("num_master"));
				bean.setNum_master_2nd(rs.getInt("num_master_2nd"));
				bean.setNum_subject(rs.getInt("num_subject"));
				bean.setProvince(rs.getString("province"));
				bean.setRank(rs.getString("rank_info"));
				bean.setSchool_address(rs.getString("school_address"));
				bean.setSchool_email(rs.getString("school_email"));
				bean.setSchool_id(rs.getInt("school_id"));
				bean.setSchool_name(rs.getString("school_name"));
				bean.setSchool_phone(rs.getString("school_phone"));
				bean.setSchool_site(rs.getString("school_site"));
				bean.setSchool_space(rs.getInt("school_space"));
				bean.setTop_value(rs.getInt("top_value"));
				bean.setSyl(rs.getString("syl"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}
	
	public List<SchoolMajor> pickSchoolMajor(String schoolName, int year, int pager) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<SchoolMajor> list = new ArrayList<>();

		try {
			String SQL = "SELECT * FROM career_university_major x where x.school_name = ? and x.major_year = ? ORDER BY major_code ";
			System.out.println(SQL);
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, schoolName);
			ps.setInt(2, year);
			rs = ps.executeQuery();
			SchoolMajor bean = null;
			while (rs.next()) {
				bean = new SchoolMajor();
				bean.setAcademic(rs.getInt("academic"));
				bean.setCnf_spe_id(rs.getInt("cnf_spe_id"));
				bean.setCode(rs.getString("major_code"));
				bean.setName(rs.getString("major_name"));
				bean.setProfessional(rs.getInt("professional"));
				bean.setRecruit_type(rs.getInt("recruit_type"));
				bean.setRoot_id(rs.getInt("root_id"));
				bean.setYear(rs.getInt("major_year"));
				bean.setSchool_id(rs.getString("school_id"));
				bean.setSchool_name(rs.getString("school_name")); 
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<MajorHistory> pickSchoolMajorDataHistory(String schoolName, int year, int pager) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MajorHistory> list = new ArrayList<>();

		try {
			String SQL = "SELECT * FROM career_university_major_data x where x.school_name = ? and x.data_year = ? ORDER BY total,special_code ";
			System.out.println(SQL);
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, schoolName);
			ps.setInt(2, year);
			rs = ps.executeQuery();
			MajorHistory bean = null;
			while (rs.next()) {
				bean = new MajorHistory();
				bean.setDegree_type(rs.getInt("degree_type"));
				bean.setDegree_type_name(rs.getString("degree_type_name"));
				bean.setDiff_politics(rs.getInt("diff_politics"));
				bean.setDiff_english(rs.getInt("diff_english"));
				bean.setDiff_special_one(rs.getInt("diff_special_one"));
				bean.setDiff_special_two(rs.getInt("diff_special_two"));
				bean.setDiff_total(rs.getInt("diff_total"));
				bean.setEnglish(rs.getInt("english"));
				bean.setNote(rs.getString("note"));
				bean.setPolitics(rs.getInt("politics"));
				bean.setSpecial_code(rs.getString("special_code"));
				bean.setSpecial_name(rs.getString("special_name"));
				bean.setSpecial_one(rs.getInt("special_one"));
				bean.setSpecial_two(rs.getInt("special_two"));
				bean.setTotal(rs.getInt("total"));
				bean.setYear(rs.getInt("data_year"));
				bean.setSchool_id(rs.getString("school_id"));
				bean.setSchool_name(rs.getString("school_name"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<MajorHistory> getSchoolMajorDataHistoryByMajorCodeAndScore(String majorCode, int score, int diffTop, int diffBottom) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MajorHistory> list = new ArrayList<>();

		try {
			String SQL = "SELECT x.*, y.school_name as tj_school_name FROM career_university_major_data x left join career_university_major_adjust2024 y on x.school_name = y.school_name and LEFT(x.special_code, 4) = LEFT(y.special_code, 4) where x.special_code = ? and total between ? and ? ORDER BY data_year DESC, total DESC, special_code DESC";
			System.out.println(SQL);
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, majorCode);
			ps.setInt(2, score - diffBottom);
			ps.setInt(3, score + diffTop);
			rs = ps.executeQuery();
			MajorHistory bean = null;
			while (rs.next()) {
				bean = new MajorHistory();
				bean.setDegree_type(rs.getInt("degree_type"));
				bean.setDegree_type_name(rs.getString("degree_type_name"));
				bean.setDiff_politics(rs.getInt("diff_politics"));
				bean.setDiff_english(rs.getInt("diff_english"));
				bean.setDiff_special_one(rs.getInt("diff_special_one"));
				bean.setDiff_special_two(rs.getInt("diff_special_two"));
				bean.setDiff_total(rs.getInt("diff_total"));
				bean.setEnglish(rs.getInt("english"));
				bean.setNote(rs.getString("note"));
				bean.setPolitics(rs.getInt("politics"));
				bean.setSpecial_code(rs.getString("special_code"));
				bean.setSpecial_name(rs.getString("special_name"));
				bean.setSpecial_one(rs.getInt("special_one"));
				bean.setSpecial_two(rs.getInt("special_two"));
				bean.setTotal(rs.getInt("total"));
				bean.setYear(rs.getInt("data_year"));
				bean.setSchool_id(rs.getString("school_id"));
				bean.setSchool_name(rs.getString("school_name"));
				bean.setSchool_tiaoji(rs.getString("tj_school_name"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<MajorHistory> getRelatedSchoolMajorDataHistoryByMajorCodeAndScore(String majorCode4digit, int score, int diffTop, int diffBottom) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MajorHistory> list = new ArrayList<>();

		try {
			String SQL = "SELECT x.*, y.school_name as tj_school_name FROM career_university_major_data x left join career_university_major_adjust2024 y on x.school_name = y.school_name and LEFT(x.special_code, 4) = LEFT(y.special_code, 4) where x.special_code like ? and total between ? and ? ORDER BY special_code, data_year DESC, total DESC, special_code DESC";
			System.out.println(SQL);
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, majorCode4digit + "%");
			ps.setInt(2, score - diffBottom);
			ps.setInt(3, score + diffTop);
			rs = ps.executeQuery();
			MajorHistory bean = null;
			while (rs.next()) {
				bean = new MajorHistory();
				bean.setDegree_type(rs.getInt("degree_type"));
				bean.setDegree_type_name(rs.getString("degree_type_name"));
				bean.setDiff_politics(rs.getInt("diff_politics"));
				bean.setDiff_english(rs.getInt("diff_english"));
				bean.setDiff_special_one(rs.getInt("diff_special_one"));
				bean.setDiff_special_two(rs.getInt("diff_special_two"));
				bean.setDiff_total(rs.getInt("diff_total"));
				bean.setEnglish(rs.getInt("english"));
				bean.setNote(rs.getString("note"));
				bean.setPolitics(rs.getInt("politics"));
				bean.setSpecial_code(rs.getString("special_code"));
				bean.setSpecial_name(rs.getString("special_name"));
				bean.setSpecial_one(rs.getInt("special_one"));
				bean.setSpecial_two(rs.getInt("special_two"));
				bean.setTotal(rs.getInt("total"));
				bean.setYear(rs.getInt("data_year"));
				bean.setSchool_id(rs.getString("school_id"));
				bean.setSchool_name(rs.getString("school_name"));
				bean.setSchool_tiaoji(rs.getString("tj_school_name"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<MajorPlan> pickSchoolMajorPlan(String schoolName, int year, int pager) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MajorPlan> list = new ArrayList<>();

		try {
			String SQL = "SELECT * FROM career_university_major_plan x where x.school_name = ? and x.plan_year = ? ORDER BY special_code,degree_type ";
			System.out.println(SQL);
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, schoolName);
			ps.setInt(2, year);
			rs = ps.executeQuery();
			MajorPlan bean = null;
			while (rs.next()) {
				bean = new MajorPlan();
				bean.setDegree_type(rs.getString("degree_type"));
				bean.setDegree_type_name(rs.getString("degree_type_name"));
				bean.setDepart_id(rs.getString("depart_id"));
				bean.setDepart_name(rs.getString("depart_name"));
				bean.setIs_statistic_direction(rs.getString("is_statistic_direction"));
				bean.setPlan_id(rs.getString("plan_id"));
				bean.setRecruit_number(rs.getString("recruit_number"));
				bean.setRecruit_type_name(rs.getString("recruit_type_name"));
				bean.setRemark(rs.getString("remark"));
				bean.setResearch_area(rs.getString("research_area"));
				bean.setSpe_id(rs.getString("spe_id"));
				bean.setSpecial_code(rs.getString("special_code"));
				bean.setSpecial_name(rs.getString("special_name"));
				bean.setYear(rs.getString("plan_year"));
				bean.setSchool_id(rs.getString("school_id"));
				bean.setSchool_name(rs.getString("school_name"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public CareerCard getCardByIDandPasswd(String id, String passwd) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		CareerCard bean = null;
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT * FROM career_card WHERE C_ID = ? and C_PASSWD = ?";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, id.toUpperCase());
			ps.setString(2, passwd.toUpperCase());
			rs = ps.executeQuery();
			while (rs.next()) {
				bean = new CareerCard();
				bean.setId(rs.getString("C_ID"));
				bean.setPasswd(rs.getString("C_PASSWD"));
				bean.setYear(rs.getInt("C_YEAR"));
				bean.setStatus(rs.getInt("C_STATUS"));
				bean.setCreate(rs.getTimestamp("C_CREATE"));
				bean.setActive(rs.getTimestamp("C_ACTIVE"));
				bean.setLastLogin(rs.getTimestamp("C_LAST_LOGIN"));
				bean.setOpenID(rs.getString("C_OPEN_ID"));
				bean.setAdmin(rs.getString("C_ADMIN"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return bean;
	}
	
	public boolean updateCard(String id, int year, int status) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "UPDATE career_card SET C_YEAR = ?, C_STATUS = ? WHERE C_ID = ?";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, year);
			ps.setInt(2, status);
			ps.setString(3, id);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}

	public boolean updateCardForTimeOnly(String id, Date activeTime, Date lastLoginTime) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "UPDATE career_card SET C_ACTIVE = ?, C_LAST_LOGIN = ? WHERE C_ID = ?";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setTimestamp(1, new Timestamp(activeTime.getTime()));
			ps.setTimestamp(2, new Timestamp(lastLoginTime.getTime()));
			ps.setString(3, id);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public boolean updateCardForOpenIDOnly(String id, String openID) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "UPDATE career_card SET C_OPEN_ID = ? WHERE C_ID = ?";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, openID);
			ps.setString(2, id);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}

	public boolean frozenCard(String cid) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "UPDATE career_card SET C_STATUS = 2 WHERE C_ID = ?";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}

	public boolean insertSysVisitLog(String cid, String requestMode) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "insert into career_sys_trans_visit(C_ID, VISIT_TM, VISIT_MOD) values(?, unix_timestamp(NOW()) , ?)";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.setString(2, requestMode);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}

	public boolean deleteSysVisitLog(String cid) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "DELETE FROM career_sys_trans_visit WHERE c_id = ? AND VISIT_TM < (unix_timestamp(NOW()) - 60 * 60)";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}

	public int getLatest30SecsVisitLogCount(String cid) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT COUNT(1) as ct FROM career_sys_trans_visit WHERE c_id = ? AND VISIT_TM > (unix_timestamp(NOW()) - 10)";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			rs = ps.executeQuery();
			if (rs.next())
				return rs.getInt(1);
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public List<MarjorXkRankBean> getZyXkRankJPGByYxmc(String yx) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MarjorXkRankBean> list = new ArrayList<>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT *  FROM base_university_major_rank1 WHERE `学校名称`= ? ORDER BY `排名`";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yx);
			rs = ps.executeQuery();
			MarjorXkRankBean bean = null;
			while (rs.next()) {
				bean = new MarjorXkRankBean();
				bean.setRank(rs.getString("排名"));
				bean.setYxmc(rs.getString("学校名称"));
				bean.setZymc(rs.getString("专业名称"));
				bean.setLevel(rs.getString("评级"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public List<MarjorXkRankBean> getZyXkRankJPGByZymc(String zy) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MarjorXkRankBean> list = new ArrayList<>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT *  FROM base_university_major_rank1 WHERE `专业名称`= ? ORDER BY `排名`";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, zy);
			rs = ps.executeQuery();
			MarjorXkRankBean bean = null;
			while (rs.next()) {
				bean = new MarjorXkRankBean();
				bean.setRank(rs.getString("排名"));
				bean.setYxmc(rs.getString("学校名称"));
				bean.setZymc(rs.getString("专业名称"));
				bean.setLevel(rs.getString("评级"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<MarjorXkRankBean> getZyXkRankByYxmc(String yx) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MarjorXkRankBean> list = new ArrayList<>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT *  FROM base_university_major_rank2 WHERE `院校名称`= ? ORDER BY SORT";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yx);
			rs = ps.executeQuery();
			MarjorXkRankBean bean = null;
			while (rs.next()) {
				bean = new MarjorXkRankBean();
				bean.setRank(rs.getString("评估结果"));
				bean.setYxmc(rs.getString("院校名称"));
				bean.setZymc(rs.getString("一级学科名称"));
				bean.setZydl(rs.getString("zydl"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public List<MarjorXkRankBean> getZyXkTeseByYxmc(String yx) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<MarjorXkRankBean> list = new ArrayList<>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT *  FROM base_university_major_tese WHERE `院校` = ? ORDER BY `专业`";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yx);
			rs = ps.executeQuery();
			MarjorXkRankBean bean = null;
			while (rs.next()) {
				bean = new MarjorXkRankBean();
				bean.setRank(rs.getString("特色"));
				bean.setYxmc(rs.getString("院校"));
				bean.setZymc(rs.getString("专业"));
				bean.setLevel(rs.getString("特色"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public int checkBlockCount(String cid, String action, String spec) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		YGCardBean bean = null;
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT count(*) as ct FROM zdks_block WHERE C_ID = ? and FROM_ACTION = ? and SPEC_VALUE = ? and DATE_FORMAT(CREATE_DT, '%Y%m%d') = DATE_FORMAT(now(), '%Y%m%d')";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.setString(2, action);
			ps.setString(3, spec);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("ct");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return -1;
	}

	public boolean insertBlockCount(String cid, String action, String spec) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "insert into zdks_block(FROM_ACTION, SPEC_VALUE, C_ID, CREATE_DT) values(?,?,?,NOW())";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, action);
			ps.setString(2, spec);
			ps.setString(3, cid);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	
	public int getCardFeedTraceCountByToday(String cid, String action, String desc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CareerCardFeedTraceBean> list = new ArrayList<>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT count(*) FROM career_card_feed_trace WHERE C_ID = ? and C_ACT = ? and C_DESC = ? and DATE_FORMAT(C_CREATE, '%Y%m%d') = DATE_FORMAT(now(), '%Y%m%d')";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.setString(2, action);
			ps.setString(3, desc);
			rs = ps.executeQuery();
			CareerCardFeedTraceBean bean = null;
			while (rs.next()) {
				return rs.getInt(1);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public int getCardFeedTraceCountByTodayWithoutDesc(String cid, String action) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CareerCardFeedTraceBean> list = new ArrayList<>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT count(*) FROM career_card_feed_trace WHERE C_ID = ? and C_ACT = ? and DATE_FORMAT(C_CREATE, '%Y%m%d') = DATE_FORMAT(now(), '%Y%m%d')";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.setString(2, action);
			rs = ps.executeQuery();
			CareerCardFeedTraceBean bean = null;
			while (rs.next()) {
				return rs.getInt(1);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public List<CareerCardFeedTraceBean> getCardFeedTraceList(String cid, String action, String desc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CareerCardFeedTraceBean> list = new ArrayList<>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT * FROM career_card_feed_trace WHERE C_ID = ? and C_ACT = ? and C_DESC = ? ORDER BY C_CREATE DESC limit 0,5";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.setString(2, action);
			ps.setString(3, desc);
			rs = ps.executeQuery();
			CareerCardFeedTraceBean bean = null;
			while (rs.next()) {
				bean = new CareerCardFeedTraceBean();
				bean.setAction(rs.getString("C_ACT"));
				bean.setCid(rs.getString("C_ID"));
				bean.setCreate(rs.getTimestamp("C_CREATE"));
				bean.setDesc(rs.getString("C_DESC"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public boolean updateCardFeedTraceTm(String cid, String action, String desc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "update career_card_feed_trace set C_CREATE = now() WHERE C_ID = ? and C_ACT = ? and C_DESC = ?";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.setString(2, action);
			ps.setString(3, desc);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return true;
	}
	
	public List<CareerCardFeedTraceBean> getCardFeedTraceListWithoutDesc(String cid, String action) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CareerCardFeedTraceBean> list = new ArrayList<>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT * FROM career_card_feed_trace WHERE C_ID = ? and C_ACT = ? ORDER BY C_CREATE DESC limit 0,5";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.setString(2, action);
			rs = ps.executeQuery();
			CareerCardFeedTraceBean bean = null;
			while (rs.next()) {
				bean = new CareerCardFeedTraceBean();
				bean.setAction(rs.getString("C_ACT"));
				bean.setCid(rs.getString("C_ID"));
				bean.setCreate(rs.getTimestamp("C_CREATE"));
				bean.setDesc(rs.getString("C_DESC"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}

	public boolean insertCardFeedTrace(String cid, String action, String desc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "insert into career_card_feed_trace(C_ACT, C_DESC, C_ID, C_CREATE) values(?,?,?,NOW())";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, action);
			ps.setString(2, desc);
			ps.setString(3, cid);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public List<ZDKSKggk> getZDKSKggk(int year, String zymc, String gzdd, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZDKSKggk> list = new ArrayList<ZDKSKggk>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT x.nf as nf, x.kslx as kslx, x.bmmc, x.yrsj, COUNT(*) as ct,SUM(x.bkrs) as sum FROM career_kg x WHERE x.gzdd like ? and x.nf = ? and x.zy LIKE ? GROUP BY x.nf, x.kslx, x.bmmc, x.yrsj ORDER BY count(*) DESC limit ?,?";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + gzdd + "%");
			ps.setInt(2, year);
			ps.setString(3, "%" + zymc + "%");
			ps.setInt(4, (pageNumber - 1) * 20);
			ps.setInt(5, 20);
			rs = ps.executeQuery();
			ZDKSKggk sb = null;
			while (rs.next()) {
				sb = new ZDKSKggk();
				sb.setBmmc(rs.getString("bmmc"));
				sb.setYrsj(rs.getString("yrsj"));
				sb.setCnt(rs.getInt("ct"));
				sb.setSum(rs.getInt("sum"));
				sb.setNf(rs.getString("nf"));
				sb.setKslx(rs.getString("kslx"));
				list.add(sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public int getZDKSKggkCnt(int year, String zymc, String gzdd) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZDKSKggk> list = new ArrayList<ZDKSKggk>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT COUNT(*) FROM (SELECT x.bmmc, x.yrsj, COUNT(*) as ct,SUM(x.bkrs) as sum FROM career_kg x WHERE x.gzdd like ? and x.nf = ? and x.zy LIKE ? GROUP BY x.bmmc, x.yrsj) F";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + gzdd + "%");
			ps.setInt(2, year);
			ps.setString(3, "%" + zymc + "%");
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt(1);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return 0;
	}
	
	public List<ZDKSKggk> getZDKSKggkList(int year, String yrsj, String gzdd, int pageNumber) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZDKSKggk> list = new ArrayList<ZDKSKggk>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT * FROM career_kg x WHERE x.nf = ? and (yrsj like ? OR jgxz like ? OR bmmc like ? OR gzdd like ?) and x.gzdd like ? order by x.yrsj, x.bkzw limit ?,?";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, year);
			ps.setString(2, "%" + yrsj + "%");
			ps.setString(3, "%" + yrsj + "%");
			ps.setString(4, "%" + yrsj + "%");
			ps.setString(5, "%" + yrsj + "%");
			ps.setString(6, "%" + gzdd + "%");
			ps.setInt(7, (pageNumber - 1) * 20);
			ps.setInt(8, 20);
			rs = ps.executeQuery();
			ZDKSKggk sb = null;
			while (rs.next()) {
				sb = new ZDKSKggk();
				sb.setBkrs(rs.getString("bkrs"));
				sb.setBkzw(rs.getString("bkzw"));
				sb.setBmdm(rs.getString("bmdm"));
				sb.setBmmc(rs.getString("bmmc"));
				sb.setBz(rs.getString("bz"));
				sb.setSf(rs.getString("sf"));
				sb.setGwly(rs.getString("gwly"));
				sb.setGzdd(rs.getString("gzdd"));
				sb.setJcgzjl(rs.getString("jcgzjl"));
				sb.setJcgznx(rs.getString("jcgznx"));
				sb.setJgcj(rs.getString("jgcj"));
				sb.setJgxz(rs.getString("jgxz"));
				sb.setKslb(rs.getString("kslb"));
				sb.setKslx(rs.getString("kslx"));
				sb.setLhdd(rs.getString("lhdd"));
				sb.setMsbl(rs.getString("msbl"));
				sb.setMszycs(rs.getString("mszycs"));
				sb.setNf(rs.getString("nf"));
				sb.setXl(rs.getString("xl"));
				sb.setXw(rs.getString("xw"));
				sb.setYrsj(rs.getString("yrsj"));
				sb.setZwdm(rs.getString("zwdm"));
				sb.setZwfb(rs.getString("zwfb"));
				sb.setZwjj(rs.getString("zwjj"));
				sb.setZwsx(rs.getString("zwsx"));
				sb.setZy(rs.getString("zy"));
				sb.setZy_org(rs.getString("zy_org"));
				sb.setZzmm(rs.getString("zzmm"));
				list.add(sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZDKSKggk> pickZDKSKggk(String yrsj) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZDKSKggk> list = new ArrayList<ZDKSKggk>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT * FROM career_kg x WHERE x.yrsj = ? order by nf desc";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yrsj);
			rs = ps.executeQuery();
			ZDKSKggk sb = null;
			while (rs.next()) {
				sb = new ZDKSKggk();
				sb.setBkrs(rs.getString("bkrs"));
				sb.setBkzw(rs.getString("bkzw"));
				sb.setBmdm(rs.getString("bmdm"));
				sb.setBmmc(rs.getString("bmmc"));
				sb.setBz(rs.getString("bz"));
				sb.setSf(rs.getString("sf"));
				sb.setGwly(rs.getString("gwly"));
				sb.setGzdd(rs.getString("gzdd"));
				sb.setJcgzjl(rs.getString("jcgzjl"));
				sb.setJcgznx(rs.getString("jcgznx"));
				sb.setJgcj(rs.getString("jgcj"));
				sb.setJgxz(rs.getString("jgxz"));
				sb.setKslb(rs.getString("kslb"));
				sb.setKslx(rs.getString("kslx"));
				sb.setLhdd(rs.getString("lhdd"));
				sb.setMsbl(rs.getString("msbl"));
				sb.setMszycs(rs.getString("mszycs"));
				sb.setNf(rs.getString("nf"));
				sb.setXl(rs.getString("xl"));
				sb.setXw(rs.getString("xw"));
				sb.setYrsj(rs.getString("yrsj"));
				sb.setZwdm(rs.getString("zwdm"));
				sb.setZwfb(rs.getString("zwfb"));
				sb.setZwjj(rs.getString("zwjj"));
				sb.setZwsx(rs.getString("zwsx"));
				sb.setZy(rs.getString("zy"));
				sb.setZy_org(rs.getString("zy_org"));
				sb.setZzmm(rs.getString("zzmm"));
				list.add(sb);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<String> getZDKSKggkListMostMajor(int year, String yrsj, String gzdd) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<String> list = new ArrayList<String>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT zy FROM career_kg x WHERE x.nf = ? and (yrsj like ? OR jgxz like ? OR bmmc like ? OR gzdd like ?) and x.gzdd like ? ";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, year);
			ps.setString(2, "%" + yrsj + "%");
			ps.setString(3, "%" + yrsj + "%");
			ps.setString(4, "%" + yrsj + "%");
			ps.setString(5, "%" + yrsj + "%");
			ps.setString(6, "%" + gzdd + "%");
			rs = ps.executeQuery();
			String sb = null;
			while (rs.next()) {
				list.add(rs.getString("zy"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	
	public List<OffcnExtUrlBean> getLatestOffcnExtUrl(String selectedSF, int status, int pageNumber , int pageSize) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<OffcnExtUrlBean> list = new ArrayList<>();
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "SELECT * FROM career_ext_url where view_status = "+status+" and url_sf = ? order by offcn_id desc limit ?,?";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, selectedSF);
			ps.setInt(2, (pageNumber - 1) * pageSize);
			ps.setInt(3, pageSize);
			rs = ps.executeQuery();
			OffcnExtUrlBean bean = null;
			while (rs.next()) {
				bean = new OffcnExtUrlBean();
				bean.setCreate_time(new Date(rs.getTimestamp("create_time").getTime()));
				bean.setOffcn_id(rs.getInt("offcn_id"));
				bean.setPublish_time(rs.getString("publish_time"));
				bean.setTitle(rs.getString("title"));
				bean.setTitle_org(rs.getString("title_org"));
				bean.setUrl(rs.getString("url"));
				bean.setUrl_domain(rs.getString("url_domain"));
				bean.setUrl_from(rs.getString("url_from"));
				bean.setSf(rs.getString("url_sf"));
				bean.setCs(rs.getString("url_cs"));
				bean.setView_status(status);
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	private static void closeAllConnection(Connection conn, PreparedStatement ps, ResultSet rs) {
		try {
			if (rs != null)
				rs.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (ps != null)
				ps.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		try {
			if (conn != null)
				conn.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		rs = null;
		ps = null;
		conn = null;
	}

}