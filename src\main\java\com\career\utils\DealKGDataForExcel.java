package com.career.utils;


import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.UUID;

import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jsoup.nodes.Element;

import com.career.db.CareerJobGwyReqOrg;
import com.career.db.JDBC;
import com.career.db.KGExcelDealJDBC;

public class DealKGDataForExcel {

	public static void main(String args[]) {
		try {

			read("安徽省2025年度考试录用公务员职位表_安徽_公务员_省考_2025.xlsx", false);
			read("安徽省2025年高校毕业生“三支一扶”计划招募岗位表_安徽_三支一扶_省考_2025.xlsx", false);
			
			
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	public static void check() {
		Iterator<String> list = JDBC.HM_PROVINCE_CODE.values().iterator();
		while(list.hasNext()) {
			String tablePrefix = list.next();
			for(int i = 2024;i<=2024;i++) {
				//System.out.println("\r\n"
				//		+ "select * from " + tablePrefix + "_"+i+" x where x.yxmc like '%（%';");
				
				System.out.println("UPDATE "+tablePrefix+"_"+i+"x x SET x.yxmc_org = yxmc where x.yxmc like '%（%';");
			}
			
			
			
			
		}
	}
	
	
	static void read(String fileName, boolean includeLY) throws IOException {
	    // 创建一个文件输入流，用于读取指定路径的Excel文件
		//fileName:  重庆市公务员考试.xls_重庆_公务员_省考_2025
		String[] fileNameSplit = fileName.split("_");
		String src = fileNameSplit[0];
		String sfName = fileNameSplit[1];
		String lx = fileNameSplit[2]; // 公务员
		String tpe = fileNameSplit[3]; //省考/国考
		int nf = Tools.getInt(fileNameSplit[4].substring(0,4));
		
		System.out.println("fileName>>>>"+fileName);
		
	    FileInputStream in = new FileInputStream(new File("F:\\就业报告\\省考公务员\\"+fileName));
	    StringBuffer SQL = new StringBuffer();
	    String TABLE_PROFIX = JDBC.HM_PROVINCE_CODE.get(sfName);
	    
	    
	    // 使用文件输入流创建一个XSSFWorkbook对象，该对象代表整个Excel工作簿
	    XSSFWorkbook excel = new XSSFWorkbook(in);
	    
	    int sheetNum = excel.getNumberOfSheets();
	    
	    for(int sheetIndex = 0; sheetIndex < sheetNum; sheetIndex++) {
	    
		    // 从工作簿中获取第一个工作表，索引为0
		    XSSFSheet sheet = excel.getSheetAt(sheetIndex);
		 
		    // 获取工作表中最后一行的编号
		    int coloumNum = sheet.getRow(0).getPhysicalNumberOfCells();
		    int rowNum = sheet.getPhysicalNumberOfRows();//获得总行数
		 
		    XSSFRow rowHeader = sheet.getRow(0);
		    
		    HashMap<Integer, String> MAP_TITLE = new LinkedHashMap<>();
			for(int i=0;i<coloumNum;i++) {
				XSSFCell thCell = rowHeader.getCell(i);
				MAP_TITLE.put(i, thCell.getStringCellValue());
			}
		    
		    // 遍历工作表中的所有行，包括空行和有数据的行
			KGExcelDealJDBC jdbc = new KGExcelDealJDBC();
			List<CareerJobGwyReqOrg> list = new ArrayList<>();
		    for(int i = 1; i < rowNum; i++) {
		        // 获取指定编号的行
		        XSSFRow row = sheet.getRow(i);
		        if(row != null) {
		        	 System.out.println("sheet>>"+sheetIndex+", row:>>>>>>>"+i);
			        String uuid = UUID.randomUUID().toString();
			        for(int cellIndex = 0; cellIndex < coloumNum; cellIndex++) {
						XSSFCell tdCell = row.getCell(cellIndex);
						String cellHeaderName = MAP_TITLE.get(cellIndex);
						String cellValue = "";
						if(tdCell != null) {
							if(tdCell.getCellType() == CellType.NUMERIC) {
								cellValue = String.valueOf((int)tdCell.getNumericCellValue());
							}else {
								cellValue = tdCell.getStringCellValue();
							}
						}
						
						CareerJobGwyReqOrg req = new CareerJobGwyReqOrg();
						req.setNf(nf);
						req.setGroup_id(uuid);
						req.setSf(sfName);
						req.setJob_lx(lx);
						req.setJob_type(tpe);
						req.setJob_title(cellHeaderName);
						req.setJob_descp(cellValue);
						list.add(req);
					}
			        
			        if(!includeLY) {
				        CareerJobGwyReqOrg req = new CareerJobGwyReqOrg();
						req.setNf(nf);
						req.setGroup_id(uuid);
						req.setSf(sfName);
						req.setJob_lx(lx);
						req.setJob_type(tpe);
						req.setJob_title("来源");
						req.setJob_descp(src);
						list.add(req);
			        }
			        
		        }
		        
		        if(i % 200 == 0) {
		        	if(list.size() > 0) {
			        	jdbc.executeBatchForcareer_job_gwy_req_org(list);
			        	list = new ArrayList<>();
		        	}
		        }
		    }
		    
		    if(list.size() > 0) {
		    	jdbc.executeBatchForcareer_job_gwy_req_org(list);
		    }
	    
	    }
	    
	    //writeTempFile(new File("F:\\就业报告\\省考公务员\\result_"+src+".txt"), SQL);
	}
	
	public static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
}
