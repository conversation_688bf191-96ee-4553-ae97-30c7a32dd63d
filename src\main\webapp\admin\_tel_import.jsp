<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page import="com.zsdwf.utils.*,com.zsdwf.db.*,java.util.*" %>

<%@include file="/WEB-INF/include/_session_admin_ajax.jsp" %>
<%
YGCardBean card = (YGCardBean)session.getAttribute("SES_ADMIN");

String tel = Tools.trim(request.getParameter("tel"));
if(Tools.isEmpty(tel) || tel.length() != 11){
	out.print("手机号码输入不正确，必须是11位数字。");
	return;
}

int cnt = Tools.getInt(request.getParameter("cnt"));
if(cnt < 10 || cnt > 200){
	out.print("参数错误。");
	return;
}

YGJDBC jdbc = new YGJDBC();
YGCardBean ygBean = jdbc.getYGCardByPhone(tel);
if(ygBean != null){
	//out.print("手机号码已存在，该号码不能再注册。");
	int orgCnt = ygBean.getCnt();
	ygBean.setCnt(orgCnt + cnt);
	String remark = ygBean.getRemark();
	if(Tools.isEmpty(remark)){
		ygBean.setRemark(card.getId()+"("+cnt+")");
	}else{
		ygBean.setRemark(remark + ";" + card.getId()+"("+cnt+")");	
	}
	
	jdbc.updateYGCardWithAdmin(ygBean);
}else{
	ygBean = new YGCardBean();
	ygBean.setId(tel);
	ygBean.setPasswd(tel.substring(8));
	ygBean.setPhone(tel);
	ygBean.setCnt(cnt);
	ygBean.setRemark(card.getId()+"("+cnt+")");
	jdbc.insertYGCard(ygBean);
}

out.print("reload:refresh-list");

%>


		