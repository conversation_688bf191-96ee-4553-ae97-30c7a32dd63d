package com.career.db;

import java.util.Date;

public class PDFReport {

	private String id;
	private String phone;
	private Date create_tm;
	private Date generate_tm;
	private Date expire_tm;
	private int pdf_type;
	private String json_input;
	private int status;
	
	public int getPdf_type() {
		return pdf_type;
	}
	public void setPdf_type(int pdf_type) {
		this.pdf_type = pdf_type;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public Date getGenerate_tm() {
		return generate_tm;
	}
	public void setGenerate_tm(Date generate_tm) {
		this.generate_tm = generate_tm;
	}
	public Date getCreate_tm() {
		return create_tm;
	}
	public void setCreate_tm(Date create_tm) {
		this.create_tm = create_tm;
	}
	public Date getExpire_tm() {
		return expire_tm;
	}
	public void setExpire_tm(Date expire_tm) {
		this.expire_tm = expire_tm;
	}
	public String getJson_input() {
		return json_input;
	}
	public void setJson_input(String json_input) {
		this.json_input = json_input;
	}
	public int getStatus() {
		return status;
	}
	public void setStatus(int status) {
		this.status = status;
	}
	
}
