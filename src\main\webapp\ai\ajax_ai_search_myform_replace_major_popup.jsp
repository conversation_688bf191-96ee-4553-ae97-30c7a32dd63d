<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ page import="com.career.utils.*,com.career.db.*,java.util.*"%>

<%@include file="/WEB-INF/include/_session_ajax_ai.jsp"%>
<%
AiCard aiCard = (AiCard)session.getAttribute(ZyzdCache.SES_KEY_AI_BASE_CARD); 
AiJDBC aiJDBC = new AiJDBC();
ZyzdJDBC zyzdJDBC = new ZyzdJDBC();

int id = Tools.getInt(request.getParameter("id"));

ZyzdProvinceConfig provinceConfig = ZyzdCache.getProvinceConfig(aiCard.getC_prov());
String provinceTableName = provinceConfig.getP_table_code();
String provinceName = provinceConfig.getP_name();

int LATEST_JH_YEAR = provinceConfig.getLatest_year_jh(); 

int jh_version = provinceConfig.getJh_version();

AiTbForm aiTbForm = aiJDBC.getMakerForm(provinceTableName, id);
if(aiTbForm == null){
	out.print("ERR:EXCEED");
	return;
}
AiTbFormMain aiTbFormMain = aiJDBC.getFormMainByBatchId(provinceTableName, aiTbForm.getBatch_id_org());
session.setAttribute("temp_for_replace_AiTbFormMain", aiTbFormMain);
session.setAttribute("temp_for_replace_AiTbForm", aiTbForm);

List<AiTbForm> existList = aiJDBC.getMakerFormBySeqYxNo(provinceTableName, aiTbForm.getBatch_id_org(), aiTbForm.getSeq_no_yx());
//检查填报记录
HashMap<String, AiTbForm> MAP_ALREADY_TB = new HashMap<>();
for(AiTbForm form : existList){
	String jh_key = null;
	if(provinceConfig.getForm_type() == 2){
		jh_key = Tools.trim(form.getYxdm()) + Tools.trim(form.getYxmc()) + Tools.trim(form.getZydm()) + Tools.trim(form.getZymc()) + Tools.trim(form.getZybz());
	}else{
		jh_key = Tools.trim(form.getYxdm()) + Tools.trim(form.getYxmc()) + Tools.trim(form.getZydm()) + Tools.trim(form.getZymc()) + Tools.trim(form.getZyz());
	}
	MAP_ALREADY_TB.put(jh_key, form);
}
ZyzdProvincePcConfig zyzdProvincePcConfig = new ZyzdJDBC().getProvincePcConfigByParams(aiCard.getC_prov(), aiTbFormMain.getPc_code(), aiTbFormMain.getPc());
int MAJOR_CNT = provinceConfig.getCal_MajorCnt(aiTbFormMain.getPc_code(), null);
int FORM_CNT = provinceConfig.getCal_FormCnt(aiTbFormMain.getPc_code(), null);

String xkCode = XKCombineUtils.getXKCodeByStudentSelection(aiTbFormMain.getScore_xk());
List<JHBean> jhBeanList = aiJDBC.getJhYxmcAndZyz(LATEST_JH_YEAR, provinceTableName, xkCode, aiTbFormMain.getPc(), aiTbFormMain.getPc_code(), aiTbForm.getYxdm(), aiTbForm.getYxmc(), aiTbForm.getZyz());


//匹配三年的分
HashMap<String, ZDKSRank> TONGWF_MAP = zyzdJDBC.getAllTongWF(provinceName, xkCode, aiTbFormMain.getScore_wc());

ZDKSRank rankYearA = TONGWF_MAP.get(String.valueOf(LATEST_JH_YEAR - 1));
ZDKSRank rankYearB = TONGWF_MAP.get(String.valueOf(LATEST_JH_YEAR - 2));
ZDKSRank rankYearC = TONGWF_MAP.get(String.valueOf(LATEST_JH_YEAR - 3));
ZDKSRank rankYearD = TONGWF_MAP.get(String.valueOf(LATEST_JH_YEAR - 4));
%>

<script>var DEFAULT_MAJOR_CHECKED = [];</script>
<ul class="list-group list-group-flush mb-0" style="padding:0px;">
    <li class="list-group-item" style="padding:0px;">
      	<table class="table text-nowrap table-bordered">
		    <thead>
		    	<tr><th colspan="6">
		    		<span class="form-check-label fs-10">[<%=Tools.view(aiTbForm.getYxdm()) %>]</span>
		    		<span class="mb-0 fw-semibold"><%=Tools.view(aiTbForm.getYxmc()) %><%if(provinceConfig.getForm_type() != 2){ %>第<%=aiTbForm.getZyz() %>组<%} %></span>
		    	</th></tr>
		        <tr class="table-secondary">
		            <th style="width:5%;">年份</th>      
		            <th style="width:5%;">计划</th>    
		            <th style="width:6%;">等效差</th>   
		            <th style="width:8%;">最低分</th> 
		            <th style="width:8%;">平均分</th>  
		            <th style="width:8%;">最高分</th> 
		        </tr>
		    </thead>
		    <tbody> 
		    	<%
		    	for(int i = 0;i < jhBeanList.size();i++){ 
		    		JHBean bean = jhBeanList.get(i);
		    		String jh_key = null;
		    		if(provinceConfig.getForm_type() == 2){
		    			jh_key = Tools.trim(bean.getYxdm()) + Tools.trim(bean.getYxmc()) + Tools.trim(bean.getZydm()) + Tools.trim(bean.getZymc()) + Tools.trim(bean.getZybz());
		    		}else{
		    			jh_key = Tools.trim(bean.getYxdm()) + Tools.trim(bean.getYxmc()) + Tools.trim(bean.getZydm()) + Tools.trim(bean.getZymc()) + Tools.trim(bean.getZyz());
		    		}
		    		AiTbForm formExist = MAP_ALREADY_TB.get(jh_key);
		    	%> 
		    	<tr>   
		        	<td colspan="6">
		        		<div>
		        			<p class="mb-0 fw-semibold">
		        			<input type="<%=provinceConfig.getForm_type() == 2 ? "radio" : "checkbox" %>" class="form-check-input form-checked-outline" onclick="MM_checkOverflow(this, 'pop_yx_major_check',<%=MAJOR_CNT %>);" style="border:1px solid #BE77FF;" value="<%=bean.getId() %>" <%=formExist==null?"":"checked" %> name="pop_yx_major_check">
		        				<span class="form-check-label fs-10">[<%=Tools.view(bean.getZydm()) %>]</span>  
		            			<span class="mb-0 fw-semibold"><b><%=bean.getZymc_org() %></b> 
			        			<%String temp_major_desc = Tools.viewMajorDesc(bean.getZymc(),bean.getZymc_org()); %>  
			        			<span class="text-dark fs-10"><span style="font-weight:normal;mrgin-right:3px;"><%=Tools.viewForLimitLength(temp_major_desc, 15) %></span></span></span>
		            			<span class="mb-0 fs-10 text-dark fw-semibold">学费<%=Tools.view(bean.getFee()) %>，学制<%=Tools.view(bean.getXz()) %>年</span> 
		            		</p>
		                </div>
		        	</td>
		        </tr>
		        <tr>   
		            <td class="table-active"><%=LATEST_JH_YEAR - 1 %></td>  
		            <td><%=Tools.view(bean.getJhs_a()) %></td>
		            <td class="table-danger"> 
		            	<div> 
		                    <span class="mb-0 fw-semibold">   
		            			<span class="mb-0 fw-semibold <%=Tools.getInt(bean.getZdfwc_a()) > aiCard.getC_score_wc() ? "text-success" : "text-danger"%>">
		            			<span style="font-weight:normal;font-size:10px;color:#666;margin-right:1px;"><%=Tools.getInt(bean.getZdfwc_a()) > aiCard.getC_score_wc() ? "" : "差"%></span><%=rankYearA == null ? "-" : Tools.view(String.valueOf(Math.abs(rankYearA.getScore() - Tools.getInt(bean.getZdf_a())))) %></span> 
		            		</span>  
		                    <span class="fs-9  fw-semibold <%=Tools.getInt(bean.getZdfwc_a()) > aiCard.getC_score_wc() ? "text-success" : "text-danger"%>"><%=rankYearA == null ? "-" : Tools.view(String.valueOf(Math.abs(Tools.getInt(bean.getZdfwc_a()) - rankYearA.getWc()))) %></span>
		                </div> 
		            </td>    
		            <td>  
		            	<div>
		                    <span class="mb-0 fw-semibold"> 
		            			<span class="mb-0 fw-semibold"><%=Tools.view(bean.getZdf_a()) %></span>
		            		</span> 
		                    <span class="fs-10 text-primary fw-semibold"><%=Tools.view(bean.getZdfwc_a()) %></span>
		                </div>
		            </td>
		            <td>
		            	<div>
		                    <span class="mb-0 fw-semibold"> 
		            			<span class="mb-0 fw-semibold"><%=Tools.view(bean.getPjf_a()) %></span>
		            		</span> 
		                    <span class="fs-10 text-primary fw-semibold"><%=Tools.view(bean.getPjfwc_a()) %></span>
		                </div>
		            </td>
		            <td>
		            	<div>
		                    <span class="mb-0 fw-semibold"> 
		            			<span class="mb-0 fw-semibold"><%=Tools.view(bean.getZgf_a()) %></span>
		            		</span>  
		                    <span class="fs-10 text-primary fw-semibold"><%=Tools.view(bean.getZgfwc_a()) %></span>
		                </div>
		            </td>
		         </tr>    
		         
		         <tr>   
		            <td class="table-active"><%=LATEST_JH_YEAR - 2%></td> 
		            <td><%=Tools.view(bean.getJhs_b()) %></td>
		            <td class="table-danger"> 
		            	<div> 
		                    <span class="mb-0 fw-semibold">  
		            			<span class="mb-0 fw-semibold <%=Tools.getInt(bean.getZdfwc_b()) > aiCard.getC_score_wc() ? "text-success" : "text-danger"%>">
		            			<span style="font-weight:normal;font-size:10px;color:#666;margin-right:1px;"><%=Tools.getInt(bean.getZdfwc_b()) > aiCard.getC_score_wc() ? "" : "差"%></span><%=rankYearB == null ? "-" : Tools.view(String.valueOf(Math.abs(rankYearB.getScore() - Tools.getInt(bean.getZdf_b())))) %></span> 
		            		</span>  
		                    <span class="fs-9  fw-semibold <%=Tools.getInt(bean.getZdfwc_b()) > aiCard.getC_score_wc() ? "text-success" : "text-danger"%>"><%=rankYearB == null ? "-" : Tools.view(String.valueOf(Math.abs(Tools.getInt(bean.getZdfwc_b()) - rankYearA.getWc()))) %></span>
		                </div> 
		            </td>
		            <td> 
		            	<div>
		                    <span class="mb-0 fw-semibold"> 
		            			<span class="mb-0 fw-semibold"><%=Tools.view(bean.getZdf_b()) %></span>
		            		</span> 
		                    <span class="fs-10 text-primary fw-semibold"><%=Tools.view(bean.getZdfwc_b()) %></span>
		                </div>
		            </td>
		            <td>
		            	<div>
		                    <span class="mb-0 fw-semibold"> 
		            			<span class="mb-0 fw-semibold"><%=Tools.view(bean.getPjf_b()) %></span>
		            		</span> 
		                    <span class="fs-10 text-primary fw-semibold"><%=Tools.view(bean.getPjfwc_b()) %></span>
		                </div>
		            </td>
		            <td>
		            	<div>
		                    <span class="mb-0 fw-semibold"> 
		            			<span class="mb-0 fw-semibold"><%=Tools.view(bean.getZgf_b()) %></span>
		            		</span>  
		                    <span class="fs-10 text-primary fw-semibold"><%=Tools.view(bean.getZgfwc_b()) %></span>
		                </div>
		            </td>
		         </tr>   
		           
		         <tr>     
		            <td class="table-active"><%=LATEST_JH_YEAR - 3%></td>   
		            <td><%=Tools.view(bean.getJhs_c()) %></td>
		            <td class="table-danger"> 
		            	<div>   
		                    <span class="mb-0 fw-semibold">   
		            			<span class="mb-0 fw-semibold <%=Tools.getInt(bean.getZdfwc_c()) > aiCard.getC_score_wc() ? "text-success" : "text-danger"%>">
		            				<span style="font-weight:normal;font-size:10px;color:#666;margin-right:1px;"><%=Tools.getInt(bean.getZdfwc_c()) > aiCard.getC_score_wc() ? "" : "差"%></span><%=rankYearC == null ? "-" : Tools.view(String.valueOf(Math.abs(rankYearC.getScore() - Tools.getInt(bean.getZdf_c())))) %></span> 
		            		</span>  
		                    <span class="fs-9  fw-semibold <%=Tools.getInt(bean.getZdfwc_c()) > aiCard.getC_score_wc() ? "text-success" : "text-danger"%>"><%=rankYearC == null ? "-" : Tools.view(String.valueOf(Math.abs(Tools.getInt(bean.getZdfwc_c()) - rankYearA.getWc()))) %></span>
		                </div>  
		            </td>   
		            <td> 
		            	<div>
		                    <span class="mb-0 fw-semibold"> 
		            			<span class="mb-0 fw-semibold"><%=Tools.view(bean.getZdf_c()) %></span>
		            		</span> 
		                    <span class="fs-10 text-primary fw-semibold"><%=Tools.view(bean.getZdfwc_c()) %></span>
		                </div>
		            </td> 
		            <td>
		            	<div>
		                    <span class="mb-0 fw-semibold">  
		            			<span class="mb-0 fw-semibold"><%=Tools.view(bean.getPjf_c()) %></span> 
		            		</span> 
		                    <span class="fs-10 text-primary fw-semibold"><%=Tools.view(bean.getPjfwc_c()) %></span>
		                </div>
		            </td>
		            <td> 
		            	<div>
		                    <span class="mb-0 fw-semibold"> 
		            			<span class="mb-0 fw-semibold"><%=Tools.view(bean.getZgf_c()) %></span>
		            		</span>  
		                    <span class="fs-10 text-primary fw-semibold"><%=Tools.view(bean.getZgfwc_c()) %></span>
		                </div>
		            </td> 
		         </tr>  
		           
		        <%} %> 
		        <tr><td colspan="6">
		        <div class="btn-list">
			        <div class="d-grid gap-2 mb-4">
			            <button class="btn btn-primary btn-wave waves-effect waves-light" type="button" onclick="MM_form_ajust_univ_major(<%=MAJOR_CNT%>);">确定选择</button>
			        </div>
			     </div>
		        </td></tr>
		    </tbody>
		</table>
    </li>
</ul>

	
















         

                    
            

