<%@page import="com.zsdwf.servlet.RunOnceThread"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page import="com.career.utils.*,com.career.db.*,java.util.*" %>
<%@include file="/WEB-INF/include/_session_admin.jsp" %>


<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title></title>

<script src="<%=request.getContextPath()%>/sources/jquery-3.6.4.min.js"></script>
<style>
        table {
            width: 100%;
            border-collapse: collapse;
        }
        table, th, td {
            border: 1px solid black;
        }
        th, td {
            padding: 8px;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body style="font-size:12px;">


<div class="row" style="margin-top:20px;">
<table>
<tr>
	<th>用户操作</th>
	<th>管理操作</th>
</tr>
<tr>
	<td>
		<a href="<%=request.getContextPath()%>/admin/freeze.jsp" style="margin:10px;">冻结/解锁/重置</a><br><br>
	</td>
	<td> 
		<a href="<%=request.getContextPath()%>/admin/clear_cache_PROVINCE.jsp" style="margin:10px;">更新省份缓存</a><br><br>
		<a href="<%=request.getContextPath()%>/admin/clear_cache_SERVER.jsp" style="margin:10px;">更新诊断批次缓存</a> <br><br>
		<a href="<%=request.getContextPath()%>/admin/reload_offcn_data_SERVER.jsp" style="margin:10px;">重启OFFCN进程</a><br><br>
		<a href="<%=request.getContextPath()%>/admin/zdks_generate.jsp" style="margin:10px;">诊断换算</a>
	</td>
</tr>
</table>
</div>

</body>
</html>


<script>
function MM_join(){
	var cid = $("#cid").val();
	if(cid == ""){
		alert("请输入");
		$("#cid").focus();
		return false;
	}
	
	var sf = $("#sf").val();
	if(sf == ""){
		alert("请输入");
		$("#sf").focus();
		return false;
	}
	
	var city = $("#city").val();
	if(city == ""){
		alert("请输入");
		$("#city").focus();
		return false;
	}
	
	var zdpc = $("#zdpc").val();
	if(zdpc == ""){
		alert("请输入");
		$("#zdpc").focus();
		return false;
	}
	
	var xk = $("#xk").val();
	if(xk == ""){
		alert("请输入");
		$("#xk").focus();
		return false;
	}
	
	var score = $("#score").val();
	if(score == ""){
		alert("请输入");
		$("#score").focus();
		return false;
	}
	
	$.ajax({
        type : "POST",
        url : "<%=request.getContextPath()%>/admin/join_autorun_data_SERVER.jsp",
		data: {"cid" : cid, "sf" : sf, "city" : city, "zdpc" : zdpc, "xk" : xk, "score" : score}, 
		//dataType: "json",
        //请求成功
        success : function(result) {
        	if(result.indexOf("ERR:LOGIN:REDIRECT-X_LOGIN") != -1){
				location.href = "<%=request.getContextPath()%>/admin/login.jsp";
        	}else{
        		alert("OK");
        	}
        },
        //请求失败，包含具体的错误信息
        error : function(e){
            console.log(e.status);
            console.log(e.responseText);
        }
    });
}
</script>