package com.career.utils.excel.annotation;

import java.lang.annotation.*;

/**
 * Excel列注解
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ExcelColumn {
    /**
     * 列名
     */
    String name() default "";
    
    /**
     * 列宽（字符数）
     */
    int width() default 15;
    
    /**
     * 排序
     */
    int sort() default 0;
    
    /**
     * 是否隐藏
     */
    boolean hidden() default false;
    
    /**
     * 是否年度数据
     */
    boolean isYearlyData() default false;
    
    /**
     * 分组名称
     */
    String group() default "";
    
    /**
     * 父表头名称 (用于多级表头)
     */
    String parent() default "";
    
    /**
     * 表头层级 (用于多级表头)
     * 0: 最顶层表头
     * 1: 第二层表头
     * 2: 第三层表头
     * ...以此类推
     */
    int level() default 0;
    
    /**
     * 单元格合并列数
     */
    int colspan() default 1;
    
    /**
     * 单元格合并行数
     */
    int rowspan() default 1;
}