package com.report.entity;

import java.util.List;
import java.util.ArrayList;

/**
 * 大学四年规划书实体类
 * 基于《大学四年各阶段详细规划》设计
 */
public class UniversityFourYearPlan {
    
    // 基本信息
    private String studentId;
    private String studentName;
    private String major;
    private String targetDirection; // 保研/考研/留学/求职
    
    // 各学年规划
    private FreshmanPlan freshmanPlan;        // 大一规划
    private SophomorePlan sophomorePlan;      // 大二规划
    private JuniorPlan juniorPlan;            // 大三规划
    private SeniorPlan seniorPlan;            // 大四规划
    
    // 考证规划
    private CertificationPlan certificationPlan;
    
    // 竞赛规划
    private CompetitionPlan competitionPlan;
    
    // 学术发展规划
    private AcademicDevelopmentPlan academicPlan;
    
    // 职业发展规划
    private CareerPreparationPlan careerPreparationPlan;
    
    // 构造函数
    public UniversityFourYearPlan() {
        this.freshmanPlan = new FreshmanPlan();
        this.sophomorePlan = new SophomorePlan();
        this.juniorPlan = new JuniorPlan();
        this.seniorPlan = new SeniorPlan();
        this.certificationPlan = new CertificationPlan();
        this.competitionPlan = new CompetitionPlan();
        this.academicPlan = new AcademicDevelopmentPlan();
        this.careerPreparationPlan = new CareerPreparationPlan();
    }
    
    // Getters and Setters
    public String getStudentId() { return studentId; }
    public void setStudentId(String studentId) { this.studentId = studentId; }
    
    public String getStudentName() { return studentName; }
    public void setStudentName(String studentName) { this.studentName = studentName; }
    
    public String getMajor() { return major; }
    public void setMajor(String major) { this.major = major; }
    
    public String getTargetDirection() { return targetDirection; }
    public void setTargetDirection(String targetDirection) { this.targetDirection = targetDirection; }
    
    public FreshmanPlan getFreshmanPlan() { return freshmanPlan; }
    public void setFreshmanPlan(FreshmanPlan freshmanPlan) { this.freshmanPlan = freshmanPlan; }
    
    public SophomorePlan getSophomorePlan() { return sophomorePlan; }
    public void setSophomorePlan(SophomorePlan sophomorePlan) { this.sophomorePlan = sophomorePlan; }
    
    public JuniorPlan getJuniorPlan() { return juniorPlan; }
    public void setJuniorPlan(JuniorPlan juniorPlan) { this.juniorPlan = juniorPlan; }
    
    public SeniorPlan getSeniorPlan() { return seniorPlan; }
    public void setSeniorPlan(SeniorPlan seniorPlan) { this.seniorPlan = seniorPlan; }
    
    public CertificationPlan getCertificationPlan() { return certificationPlan; }
    public void setCertificationPlan(CertificationPlan certificationPlan) { this.certificationPlan = certificationPlan; }
    
    public CompetitionPlan getCompetitionPlan() { return competitionPlan; }
    public void setCompetitionPlan(CompetitionPlan competitionPlan) { this.competitionPlan = competitionPlan; }
    
    public AcademicDevelopmentPlan getAcademicPlan() { return academicPlan; }
    public void setAcademicPlan(AcademicDevelopmentPlan academicPlan) { this.academicPlan = academicPlan; }
    
    public CareerPreparationPlan getCareerPreparationPlan() { return careerPreparationPlan; }
    public void setCareerPreparationPlan(CareerPreparationPlan careerPreparationPlan) { this.careerPreparationPlan = careerPreparationPlan; }
    
    /**
     * 大一规划 - 小白阶段
     */
    public static class FreshmanPlan {
        private String phase = "大一小白阶段";
        private List<String> firstSemesterGoals;  // 大一上学期目标
        private List<String> secondSemesterGoals; // 大一下学期目标
        private List<String> keyTasks;            // 关键任务
        private List<String> skillsToDevelop;     // 需要培养的技能
        private String winterVacationPlan;        // 寒假建议
        private String summerVacationPlan;        // 暑假建议
        
        public FreshmanPlan() {
            this.firstSemesterGoals = new ArrayList<>();
            this.secondSemesterGoals = new ArrayList<>();
            this.keyTasks = new ArrayList<>();
            this.skillsToDevelop = new ArrayList<>();
        }
        
        // Getters and Setters
        public String getPhase() { return phase; }
        public List<String> getFirstSemesterGoals() { return firstSemesterGoals; }
        public void setFirstSemesterGoals(List<String> firstSemesterGoals) { this.firstSemesterGoals = firstSemesterGoals; }
        public List<String> getSecondSemesterGoals() { return secondSemesterGoals; }
        public void setSecondSemesterGoals(List<String> secondSemesterGoals) { this.secondSemesterGoals = secondSemesterGoals; }
        public List<String> getKeyTasks() { return keyTasks; }
        public void setKeyTasks(List<String> keyTasks) { this.keyTasks = keyTasks; }
        public List<String> getSkillsToDevelop() { return skillsToDevelop; }
        public void setSkillsToDevelop(List<String> skillsToDevelop) { this.skillsToDevelop = skillsToDevelop; }
        public String getWinterVacationPlan() { return winterVacationPlan; }
        public void setWinterVacationPlan(String winterVacationPlan) { this.winterVacationPlan = winterVacationPlan; }
        public String getSummerVacationPlan() { return summerVacationPlan; }
        public void setSummerVacationPlan(String summerVacationPlan) { this.summerVacationPlan = summerVacationPlan; }
    }
    
    /**
     * 大二规划 - 拉开差距，埋下伏笔
     */
    public static class SophomorePlan {
        private String phase = "大二拉开差距，埋下伏笔";
        private List<String> firstSemesterGoals;
        private List<String> secondSemesterGoals;
        private List<String> explorationDirections;  // 探索方向
        private List<String> researchOpportunities;  // 科研机会
        private List<String> competitionsToJoin;     // 参加竞赛
        private String winterVacationPlan;
        private String summerVacationPlan;
        
        public SophomorePlan() {
            this.firstSemesterGoals = new ArrayList<>();
            this.secondSemesterGoals = new ArrayList<>();
            this.explorationDirections = new ArrayList<>();
            this.researchOpportunities = new ArrayList<>();
            this.competitionsToJoin = new ArrayList<>();
        }
        
        // Getters and Setters
        public String getPhase() { return phase; }
        public List<String> getFirstSemesterGoals() { return firstSemesterGoals; }
        public void setFirstSemesterGoals(List<String> firstSemesterGoals) { this.firstSemesterGoals = firstSemesterGoals; }
        public List<String> getSecondSemesterGoals() { return secondSemesterGoals; }
        public void setSecondSemesterGoals(List<String> secondSemesterGoals) { this.secondSemesterGoals = secondSemesterGoals; }
        public List<String> getExplorationDirections() { return explorationDirections; }
        public void setExplorationDirections(List<String> explorationDirections) { this.explorationDirections = explorationDirections; }
        public List<String> getResearchOpportunities() { return researchOpportunities; }
        public void setResearchOpportunities(List<String> researchOpportunities) { this.researchOpportunities = researchOpportunities; }
        public List<String> getCompetitionsToJoin() { return competitionsToJoin; }
        public void setCompetitionsToJoin(List<String> competitionsToJoin) { this.competitionsToJoin = competitionsToJoin; }
        public String getWinterVacationPlan() { return winterVacationPlan; }
        public void setWinterVacationPlan(String winterVacationPlan) { this.winterVacationPlan = winterVacationPlan; }
        public String getSummerVacationPlan() { return summerVacationPlan; }
        public void setSummerVacationPlan(String summerVacationPlan) { this.summerVacationPlan = summerVacationPlan; }
    }
    
    /**
     * 大三规划 - 马拉松的冲刺阶段
     */
    public static class JuniorPlan {
        private String phase = "大三马拉松的冲刺阶段";
        private List<String> firstSemesterGoals;
        private List<String> secondSemesterGoals;
        private List<String> academicFocus;          // 学术重点
        private List<String> languageSkills;         // 语言技能
        private List<String> researchPapers;         // 论文发表
        private List<String> applicationMaterials;   // 申请材料准备
        private String winterVacationPlan;
        private String summerVacationPlan;
        
        public JuniorPlan() {
            this.firstSemesterGoals = new ArrayList<>();
            this.secondSemesterGoals = new ArrayList<>();
            this.academicFocus = new ArrayList<>();
            this.languageSkills = new ArrayList<>();
            this.researchPapers = new ArrayList<>();
            this.applicationMaterials = new ArrayList<>();
        }
        
        // Getters and Setters
        public String getPhase() { return phase; }
        public List<String> getFirstSemesterGoals() { return firstSemesterGoals; }
        public void setFirstSemesterGoals(List<String> firstSemesterGoals) { this.firstSemesterGoals = firstSemesterGoals; }
        public List<String> getSecondSemesterGoals() { return secondSemesterGoals; }
        public void setSecondSemesterGoals(List<String> secondSemesterGoals) { this.secondSemesterGoals = secondSemesterGoals; }
        public List<String> getAcademicFocus() { return academicFocus; }
        public void setAcademicFocus(List<String> academicFocus) { this.academicFocus = academicFocus; }
        public List<String> getLanguageSkills() { return languageSkills; }
        public void setLanguageSkills(List<String> languageSkills) { this.languageSkills = languageSkills; }
        public List<String> getResearchPapers() { return researchPapers; }
        public void setResearchPapers(List<String> researchPapers) { this.researchPapers = researchPapers; }
        public List<String> getApplicationMaterials() { return applicationMaterials; }
        public void setApplicationMaterials(List<String> applicationMaterials) { this.applicationMaterials = applicationMaterials; }
        public String getWinterVacationPlan() { return winterVacationPlan; }
        public void setWinterVacationPlan(String winterVacationPlan) { this.winterVacationPlan = winterVacationPlan; }
        public String getSummerVacationPlan() { return summerVacationPlan; }
        public void setSummerVacationPlan(String summerVacationPlan) { this.summerVacationPlan = summerVacationPlan; }
    }
    
    /**
     * 大四规划 - 收获的季节
     */
    public static class SeniorPlan {
        private String phase = "大四收获的季节";
        private List<String> firstSemesterGoals;
        private List<String> secondSemesterGoals;
        private List<String> applicationProcesses;   // 申请流程
        private List<String> examPreparations;       // 考试准备
        private List<String> jobSearchActivities;    // 求职活动
        private List<String> graduationRequirements; // 毕业要求
        private String winterVacationPlan;
        
        public SeniorPlan() {
            this.firstSemesterGoals = new ArrayList<>();
            this.secondSemesterGoals = new ArrayList<>();
            this.applicationProcesses = new ArrayList<>();
            this.examPreparations = new ArrayList<>();
            this.jobSearchActivities = new ArrayList<>();
            this.graduationRequirements = new ArrayList<>();
        }
        
        // Getters and Setters
        public String getPhase() { return phase; }
        public List<String> getFirstSemesterGoals() { return firstSemesterGoals; }
        public void setFirstSemesterGoals(List<String> firstSemesterGoals) { this.firstSemesterGoals = firstSemesterGoals; }
        public List<String> getSecondSemesterGoals() { return secondSemesterGoals; }
        public void setSecondSemesterGoals(List<String> secondSemesterGoals) { this.secondSemesterGoals = secondSemesterGoals; }
        public List<String> getApplicationProcesses() { return applicationProcesses; }
        public void setApplicationProcesses(List<String> applicationProcesses) { this.applicationProcesses = applicationProcesses; }
        public List<String> getExamPreparations() { return examPreparations; }
        public void setExamPreparations(List<String> examPreparations) { this.examPreparations = examPreparations; }
        public List<String> getJobSearchActivities() { return jobSearchActivities; }
        public void setJobSearchActivities(List<String> jobSearchActivities) { this.jobSearchActivities = jobSearchActivities; }
        public List<String> getGraduationRequirements() { return graduationRequirements; }
        public void setGraduationRequirements(List<String> graduationRequirements) { this.graduationRequirements = graduationRequirements; }
        public String getWinterVacationPlan() { return winterVacationPlan; }
        public void setWinterVacationPlan(String winterVacationPlan) { this.winterVacationPlan = winterVacationPlan; }
    }
} 