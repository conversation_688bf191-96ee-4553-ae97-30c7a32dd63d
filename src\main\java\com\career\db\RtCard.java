package com.career.db;

import java.util.Date;

public class RtCard {
	
	private String c_id;
    private String p_c_id;
    private String c_passwd;
    private String c_name;
    private String c_remark;
    private String c_phone;
    private int c_status;
    private int c_level;
    private Date create_tm;
    private Date active_tm;
    private Date last_login_tm;
    private Date expire_tm;
    private String saas_id;
    private String promotion_code;
	public String getC_id() {
		return c_id;
	}
	public void setC_id(String c_id) {
		this.c_id = c_id;
	}
	public String getP_c_id() {
		return p_c_id;
	}
	public void setP_c_id(String p_c_id) {
		this.p_c_id = p_c_id;
	}
	public String getC_passwd() {
		return c_passwd;
	}
	public void setC_passwd(String c_passwd) {
		this.c_passwd = c_passwd;
	}
	public String getC_name() {
		return c_name;
	}
	public void setC_name(String c_name) {
		this.c_name = c_name;
	}
	public String getC_remark() {
		return c_remark;
	}
	public void setC_remark(String c_remark) {
		this.c_remark = c_remark;
	}
	public String getC_phone() {
		return c_phone;
	}
	public void setC_phone(String c_phone) {
		this.c_phone = c_phone;
	}
	public int getC_status() {
		return c_status;
	}
	public void setC_status(int c_status) {
		this.c_status = c_status;
	}
	public int getC_level() {
		return c_level;
	}
	public void setC_level(int c_level) {
		this.c_level = c_level;
	}
	public Date getCreate_tm() {
		return create_tm;
	}
	public void setCreate_tm(Date create_tm) {
		this.create_tm = create_tm;
	}
	public Date getActive_tm() {
		return active_tm;
	}
	public void setActive_tm(Date active_tm) {
		this.active_tm = active_tm;
	}
	public Date getLast_login_tm() {
		return last_login_tm;
	}
	public void setLast_login_tm(Date last_login_tm) {
		this.last_login_tm = last_login_tm;
	}
	public Date getExpire_tm() {
		return expire_tm;
	}
	public void setExpire_tm(Date expire_tm) {
		this.expire_tm = expire_tm;
	}
	public String getSaas_id() {
		return saas_id;
	}
	public void setSaas_id(String saas_id) {
		this.saas_id = saas_id;
	}
	public String getPromotion_code() {
		return promotion_code;
	}
	public void setPromotion_code(String promotion_code) {
		this.promotion_code = promotion_code;
	}
    

}
