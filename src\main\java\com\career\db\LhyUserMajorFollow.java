package com.career.db;

import java.util.Date;

import com.career.utils.BaseBean;

public class LhyUserMajorFollow extends BaseBean{
	
	private int id;
	private String order_id;
	private String zymc_org;
	private String zyml;
	private String zyl;
	private String cc;
	private Date create_tm;
	
	public static void main(String args[]) {
		LhyUserMajorFollow bean = new LhyUserMajorFollow();
		printBeanProperties(bean);
	}
	
	public String getZyl() {
		return zyl;
	}

	public void setZyl(String zyl) {
		this.zyl = zyl;
	}

	public String getZyml() {
		return zyml;
	}

	public void setZyml(String zyml) {
		this.zyml = zyml;
	}

	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getOrder_id() {
		return order_id;
	}
	public void setOrder_id(String order_id) {
		this.order_id = order_id;
	}
	public String getZymc_org() {
		return zymc_org;
	}
	public void setZymc_org(String zymc_org) {
		this.zymc_org = zymc_org;
	}
	public String getCc() {
		return cc;
	}
	public void setCc(String cc) {
		this.cc = cc;
	}
	public Date getCreate_tm() {
		return create_tm;
	}
	public void setCreate_tm(Date create_tm) {
		this.create_tm = create_tm;
	}
	
}
