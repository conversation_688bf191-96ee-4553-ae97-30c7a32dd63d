package com.career.utils.liuxue;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.cookie.Cookie;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Attribute;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.select.Elements;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.mysql.cj.util.Util;
import com.tencentcloudapi.iotvideo.v20191126.models.Data;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class HttpSendUtils2 {

	public static String post(String url, Map<String, String> params, Map<String, String> headers) {
		CloseableHttpClient client = HttpClients.createDefault();
		HttpPost httpPost = new HttpPost(url);
		UrlEncodedFormEntity entity;
		List<NameValuePair> paramPairs = new ArrayList<NameValuePair>();
		if (params != null) {
			for (Map.Entry<String, String> en : params.entrySet()) {
				paramPairs.add(new BasicNameValuePair(en.getKey(), en.getValue()));
			}
		}
		if (headers != null) {
			for (Map.Entry<String, String> en : headers.entrySet()) {
				httpPost.setHeader(en.getKey(), en.getValue());
			}
		}
		try {
			entity = new UrlEncodedFormEntity(paramPairs, "UTF-8");
			httpPost.setEntity(entity);
			HttpResponse resp = client.execute(httpPost);
			HttpEntity respEntity = resp.getEntity();
			if (null != respEntity) {
				return EntityUtils.toString(respEntity, "UTF-8");
			}

		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		} catch (ClientProtocolException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			try {
				client.close();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return null;
	}

	public static String get(String url, Map<String, String> headers) {
		CloseableHttpClient client = HttpClients.createDefault();
		HttpGet httpGet = new HttpGet(url);
		if (headers != null) {
			for (Map.Entry<String, String> en : headers.entrySet()) {
				httpGet.setHeader(en.getKey(), en.getValue());
			}
		}
		try {
			HttpResponse resp = client.execute(httpGet);
			HttpEntity respEntity = resp.getEntity();
			if (null != respEntity) {
				return EntityUtils.toString(respEntity, "UTF-8");
			}
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		} catch (ClientProtocolException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			try {
				client.close();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return null;
	}
	
	public static byte[] getimg(String url, Map<String, String> headers) {
		CloseableHttpClient client = HttpClients.createDefault();
		HttpGet httpGet = new HttpGet(url);
		if (headers != null) {
			for (Map.Entry<String, String> en : headers.entrySet()) {
				httpGet.setHeader(en.getKey(), en.getValue());
			}
		}
		try {
			HttpResponse resp = client.execute(httpGet);
			HttpEntity respEntity = resp.getEntity();
			if (null != respEntity) {
				byte[] data = EntityUtils.toByteArray(respEntity);
				return data;
			}
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		} catch (ClientProtocolException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			try {
				client.close();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return null;
	}
	
	public Cookie Cookie(){
        String url = "http://L.xueya63.com";
        BasicCookieStore cookieStore = new BasicCookieStore();
        Cookie cookie = null;  
        try {
	        CloseableHttpClient httpClient = HttpClients.custom().setDefaultCookieStore(cookieStore).build();
	        HttpPost post = new HttpPost(url);
	        List<NameValuePair> list = new ArrayList<>();
	        list.add(new BasicNameValuePair("",""));
	 
	        post.addHeader("content-Ttpe","application/json; charset=UTF-8");
	        post.setEntity(new UrlEncodedFormEntity(list,"UTF-8"));
	        CloseableHttpResponse httpResponse = httpClient.execute(post);
	        List<Cookie> cookies = cookieStore.getCookies();
	        cookie =cookies.get(0);
	        httpClient.close();
        }catch(Exception ex) {
        	ex.printStackTrace();
        }
        return  cookie;
    }
	
	
	
	//东吴CHECKE（）
	public static void runxuhangDYCheck() throws Exception {
		File files = new File("F://就业报告//DW/");
		File[] list = files.listFiles();
		for(int i=0;i<list.length;i++) {
			File x = list[i];
			String fileName = x.getName();

			
			BufferedReader reader = new BufferedReader(new FileReader(x));
			String el = null;
			StringBuffer str = new StringBuffer();
			while((el = reader.readLine()) != null) {
				str.append(el);
			}
			
			
			Document detailPage = Jsoup.parse(str.toString());

			Elements tbody = detailPage.getElementsByTag("tbody");
			System.out.println(fileName + "->" +tbody.size());
			Elements trList = tbody.get(0).getElementsByTag("tr");
			for(int k = 0; k < trList.size(); k++) {
				Element tr = trList.get(k);
				String cxt = tr.text().trim();

				
				if(cxt.equals("")) {
					continue;
				}
				
				String colspan = tr.getElementsByTag("td").get(0).attr("colspan");
				
				if(colspan != null && colspan.trim().length() > 0) {
					if(cxt.indexOf("递补人选") == -1 && cxt.indexOf("拟录用人选") == -1) {
						System.out.println("ERR->"+cxt);
						break;
					}
				}
			}
		}
	}
	
	
	public static StringBuffer runxuhangDYInsert() throws Exception {

		StringBuffer SQL = new StringBuffer();
		File files = new File("F://就业报告//DW/");
		File[] list = files.listFiles();
		for(int i=0;i<list.length;i++) {
			File x = list[i];
			String fileName = x.getName();

			String[] fl = fileName.split("\\$");
			
			String content = fl[2];
			
			String group_name = "中国石化";
			String dw = content.substring(5, content.indexOf("招聘") - 2);
			String lx = content.substring(content.indexOf("招聘") - 2, content.indexOf("招聘") + 2);
			
			if(!dw.equals("中原油田")) {
				continue;
			}
			
			BufferedReader reader = new BufferedReader(new FileReader(x));
			String el = null;
			StringBuffer str = new StringBuffer();
			while((el = reader.readLine()) != null) {
				str.append(el);
			}
			
			Document detailPage = Jsoup.parse(str.toString());
			String db = "", gw = "";
			int index = 0;
			Elements tbody = detailPage.getElementsByTag("tbody");
			System.out.println(fileName + "->" +tbody.size());
			Elements trList = tbody.get(0).getElementsByTag("tr");
			for(int k = 0; k < trList.size(); k++) {
				Element tr = trList.get(k);
				String cxt = tr.text().trim();
				if(cxt.equals("") || (cxt.indexOf("姓名") != -1 && cxt.indexOf("院校") != -1)) {
					continue;
				}
				
				String colspan = tr.getElementsByTag("td").get(0).attr("colspan");
				
				if(colspan != null && colspan.trim().length() > 0) {
				
					if(cxt.indexOf("递补人选") != -1) {
						db = "递补";
						continue;
					}else {
						
						if(cxt.indexOf("拟录用人选") == -1 && cxt.indexOf("拟*用人选") == -1) {
							gw = cxt;
							db = "";
							k++;
							continue;
						}else {
							gw = cxt.substring(0, cxt.indexOf("拟录用人选"));
							db = "";
							continue;
						}
					}
				}
				
				
				Elements tds = tr.getElementsByTag("td");
				
				
				String xm = tds.get(0).text();
				String yxmc = tds.get(1).text();
				String zymc = tds.get(2).text();
				String xl = tds.get(3).text();
				yxmc = yxmc.replaceAll("\'", " ");
				
				index++;
				
				SQL.append("insert into career_jy_all_2024_tp2(lx,bz, xh, group_name, lsy, dw, gw, xm, yxmc, zymc, xl, src, nf, sj) values('"+lx+"','"+db+"',"+index+",'中国石化','"+dw+"','"+dw+"','"+gw+"','"+xm+"','"+yxmc+"','"+zymc+"','"+xl+"','"+content+"','2024','"+fl[0]+"');\r\n");
			}
		}
		return SQL;
	}
	
	
	
	
	//东吴网站抓网页
		public static void runxuhangDY22(String URL) {
			Map<String, String> headers = new HashMap<>();

			
			String resultPageList = HttpSendUtils2.get(URL, headers);
			//System.out.println(resultPageList);
			Document documentList = Jsoup.parse(resultPageList);
			
			Elements aiticleList = documentList.getElementsByClass("article_list");
			
			Elements items = aiticleList.get(0).getElementsByClass("item");

			for(int i=0;i<items.size();i++) {
				Element element = items.get(i);
				Elements span = element.getElementsByTag("span");
				Elements a = element.getElementsByTag("a");
				String sj = span.get(0).text();
				Element a_element = a.get(0);
				String url = a_element.attr("href");
				String content = a_element.text();
				if(content.indexOf("人选和递补人选公示") != -1) {
					String group_name = "中国石化";
					String dw = content.substring(5, content.indexOf("招聘") - 2);
					String lx = content.substring(content.indexOf("招聘") - 2, content.indexOf("招聘") + 2);
					System.out.println(sj+"->http://www.dwjy.com/"+url+"->"+content+" - >" + dw+" #"+lx);
					
					
					if(!dw.equals("中原油田")) {
						//continue;
					}
					
					String gw = "", db = null;
					int index = 0;
					Document detailPage = Jsoup.parse(HttpSendUtils2.get("http://www.dwjy.com/" + url, headers));
					Elements article_content_box = detailPage.getElementsByClass("article_content_box");
					StringBuffer SQL = new StringBuffer();
					
					Elements table = article_content_box.get(0).getElementsByTag("table");
					String tableTxt = table.get(0).html();
					SQL.append("<table>"+tableTxt+"</table>");
					String mt = String.valueOf(Math.random()).substring(2,7);
					writeTempFile(new File("F://就业报告//DW/"+sj+"$"+mt+"$" +content+".txt"), SQL);
					
				}
				
			}
			
			/**
			
			String resultPage = HttpSendUtils2.get(URL, headers);
			
			
			//System.out.println(resultPage);
			Document document = Jsoup.parse(resultPage);
			
			Elements elementstitle = document.getElementsByTag("title");
			String txt = elementstitle.get(0).text();
			
			String prefix = txt.substring(0, txt.indexOf("【"));
			String subfix = txt.substring(txt.indexOf("【") + 1, txt.indexOf("】"));
			
			System.out.println(txt+" -> "+ prefix + " , " + subfix);
			
			Elements elements = document.getElementsByTag("article");
			if(elements == null || elements.size() == 0) {
				return SQL;
			}
			String mt = String.valueOf(Math.random()).substring(2,7);
			
			Elements elementsP = elements.get(0).getElementsByTag("p");
			String title = null, tempGW = null, dbu = "";
			int index = 0;
			for(int i=0;i<elementsP.size();i++){
				Element element = elementsP.get(i);
				String str = element.text();
				
				if(i == 0) {
					title = str;
					continue;
				}
				
				if(str.indexOf("招聘岗位") != -1) {
					String t[] = str.split("岗位");
					tempGW = t[1].substring(1);
					dbu = "";
					continue;
				}
				
				if(str.indexOf("递补人选") != -1) {
					dbu = "递补";
				}
				
				String[] tt = str.split(" ");
				if(tt.length < 3) {
					continue;
				}
				
				if(tt[1].equals("院校") && tt[2].equals("专业")) {
					continue;
				}
				
				String xm = tt[0];
				String xl = tt[tt.length - 1];
				String zymc = tt[tt.length - 2];
				String yxmc = tt[1];//str.substring(str.indexOf(xm) + xm.length(), str.indexOf(zymc));
				yxmc = yxmc.replaceAll("\'", " ");
				
				index++;
				
				SQL.append("insert into career_jy_all_2024_tp(bz, xh, group_name, lsy, dw, gw, xm, yxmc, zymc, xl, src, nf, sj) values('"+dbu+"',"+index+",'"+prefix+"','"+subfix+"','"+subfix+"','"+tempGW+"','"+xm+"','"+yxmc+"','"+zymc+"','"+xl+"','"+txt+"','2024','20240401');\r\n");
				
				//System.out.println(tempGW + "-> " + str);
			}
			return SQL;
			//writeTempFile(new File("F://就业报告//"+txt +mt+".txt"), SQL);
			 * 
			 * */
			 
		}
	
	//重庆人事考试网抓取网页
	public static StringBuffer rlsbj_cq_gov(String URL, int pageNumber) {
		Map<String, String> headers = new HashMap<>();
		StringBuffer SQL = new StringBuffer();
		
		System.out.println(URL);
		
		String resultPageList = HttpSendUtils2.get(URL, headers);
		//System.out.println(resultPageList);
		Document documentList = Jsoup.parse(resultPageList);
		
		Elements aiticleList = documentList.getElementsByClass("rsj-list1");
		
		Elements items = aiticleList.get(0).getElementsByTag("a");
		Elements spans = aiticleList.get(0).getElementsByTag("span");

		for(int i=0;i<items.size();i++) {
			Element item = items.get(i);
			Element span = spans.get(i);
			String title = item.text();
			String date = span.text();
			String href = "https://rlsbj.cq.gov.cn/zwxx_182/gsxx" + item.attr("href").substring(1);

			if(title.indexOf("2024") != -1 && title.indexOf("拟聘人员公示") != -1) {
				if(date.indexOf("2024-09-1") == -1) {
					continue;
				}
				Document detailPage = Jsoup.parse(HttpSendUtils2.get(href, headers));
				Elements ue_table = detailPage.getElementsByClass("ue_table");
				
				Elements tbody = ue_table.get(0).getElementsByTag("tbody");
				Elements tableTR = tbody.get(0).getElementsByTag("tr");
				Elements tableTDTitle = tableTR.get(0).getElementsByTag("td");// 标题
				HashMap<Integer, String> titleMap = new HashMap<>();
				
				for(int k = 0; k < tableTDTitle.size(); k++) {
					titleMap.put(k, tableTDTitle.get(k).text());
				}
				
				String groupID = UUID.randomUUID().toString();
				for(int k = 1; k < tableTR.size(); k++) {
					Element tr = tableTR.get(k);
					Elements tableTD = tr.getElementsByTag("td");
					
					for(int kk = 0; kk < tableTD.size(); kk++) {
						String table_title = titleMap.get(kk);
						String table_content = tableTD.get(kk).text();
						//System.out.println(date+"."+title+"->"+tl+" - "+value);
						String id = UUID.randomUUID().toString();
						SQL.append("insert into career_rlsbj_cq_gov_org_data(group_id, table_title, table_content, publish_dt, url_link, title) values('"+groupID+"','"+table_title+"','"+table_content+"','"+date+"','"+href+"','"+title+"');\r\n");
					}
				}
				
			}
		}
		return SQL;
	}
	
	//重庆人事考试网抓取网页
	public static StringBuffer kaoshiyuan_sc_gov(String URL, int pageNumber) {
		Map<String, String> headers = new HashMap<>();
		StringBuffer SQL = new StringBuffer();
		
		String resultPageList = HttpSendUtils2.get(URL, headers);
		//System.out.println(resultPageList);
		Document documentList = Jsoup.parse(resultPageList);
		System.out.println(resultPageList);
		Elements aiticleList = documentList.getElementsByClass("rich_pages wxw-img js_insertlocalimg");
		for(int i=0;i<aiticleList.size();i++) {
			Element item = aiticleList.get(i);
			String img_url = item.attr("src");
			System.out.println(img_url);
			byte[] data = getimg(img_url, headers);
			FileOutputStream fos = null;
			try {
				fos = new FileOutputStream("F://就业报告//CQ//ksy_img" + i +".jpg");
				fos.write(data);
				fos.close();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return SQL;
	}
	
	//重庆人事考试网抓取网页
	public static StringBuffer lanzhou_jiaotong_daxue(String URL, int pageNumber) {
		Map<String, String> headers = new HashMap<>();
		
		headers.put("cookie", "C1726577157520=bee0bc54d5c9c4e88ecfd9c2a13f5aab795; C1726577164619=3914432505920351d680564a0990a713847");
		headers.put("host", "jyzx.lzjtu.edu.cn");
		headers.put("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
		
		Map<String, String> params = new HashMap<>();
		params.put("token", "ebd08c303d2d47bb1c85655b8d984537466");
		params.put("entry", "com_hired_order");
		params.put("url", "https://jyzx.lzjtu.edu.cn/jy/?token=67d21c1f3c78afc40ceed1a4ef0b1872979#layout=public&page=home/univ");
		params.put("c_degree", "本科生毕业");
		params.put("c_academy", "新能源与动力工程学院");
		params.put("c_major", "新能源科学与工程");
		params.put("c_year", "2024");
		params.put("scope", "page");
		
		StringBuffer SQL = new StringBuffer();
		
		String resultPageList = HttpSendUtils2.post(URL, params, headers);
		System.out.println(resultPageList);
		
		return SQL;
	}
	
	public static void getXUEXINWANGVedioURI(String vedioPage,String id) {
		StringBuffer sb = new StringBuffer();
		int index = 1;
		Map<String, String> headers = new HashMap<>();
		String res = HttpSendUtils2.get(vedioPage, headers);
		sb.append(res);
		writeTempFile(new File("F://学信网视频//" + id +".txt"), sb);
		
		int indexx = res.indexOf("https://chsi-v.oss-cn-beijing.aliyuncs.com/xzpt/jy/");
		int size = "https://chsi-v.oss-cn-beijing.aliyuncs.com/xzpt/jy/".length();
		if(indexx != -1) {
			//System.out.println(res.substring(indexx, indexx + size + 7));
		}
	}
	
	
	public static void runXUEXINWANG() {
		StringBuffer sb = new StringBuffer();
		int index = 1;
		for(int i=0;i<=13;i++) {
			Map<String, String> headers = new HashMap<>();
			String res = HttpSendUtils2.get("https://xz.chsi.com.cn/jyzdk/list.action?tagId=&start="+12*i+"&_t=1708765847159", headers);
			System.out.println(res);
			
			JSONObject object = JSONObject.parseObject(res);
			JSONObject object2 = object.getJSONObject("data");
			JSONArray array = object2.getJSONArray("videoInfos");
			for(int x = 0; x < array.size(); x++) {
				JSONObject each = array.getJSONObject(x);
				//System.out.println(each.getString("videoId"));
				//System.out.println(each.getString("title"));
				getXUEXINWANGVedioURI("https://xz.chsi.com.cn/jyzdk/detail.action?videoId=" + each.getString("videoId"), each.getString("videoId"));
				
			}
		}	
			//writeTempFile(new File(file, "getids_"+index+"_ids.txt"), sb);
	}
	
	
	public static void runJIUYE() {
		StringBuffer sb = new StringBuffer();
		
		for(int i=1;i<=215;i++) {
			Map<String, String> headers = new HashMap<>();
			String res = HttpSendUtils2.get("https://www.salary.top/employment-report?page=" + i, headers);
			Document document = Jsoup.parse(res);
			Elements img64 = document.getElementsByClass("img-64");
			
			for(int x = 0; x < img64.size(); x++) {
				Element item = img64.get(x);
				Element parent = item.parent();
				Element h3 = parent.getElementsByTag("h3").get(0);
				Element p = parent.getElementsByTag("p").get(0);
				
				Attribute atr = parent.attribute("href");
				//System.out.println(h3.html() + atr.getValue());
				sb.append("kimi-NAME:" + h3.html() + "kimi-TITLE:" + p.html() + "kimi-URI:" + atr.getValue()+"\r\n");
				if(atr.getValue().indexOf("https://ooyyee-static.qiniu.ooyyee.com/employment/report/") != -1) {
					int yearStartIndex = atr.getValue().indexOf("report/20") + 7;
					String year = atr.getValue().substring(yearStartIndex, yearStartIndex + 4);
					String fileName = atr.getValue().substring(yearStartIndex + 5);
					//System.out.println(year + "," + fileName);
					try {
						DownloadPdf.downLoadByUrl(atr.getValue(),fileName, "F://就业报告//" + year);
					} catch (Exception e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
				
			}
		}	
		writeTempFile(new File("F://就业报告//result.txt"), sb);
	}
	
	
	//教育部合作办学
	public static void runHEZUOBANXUE() {
		
		
		for(int i=1;i<=215;i++) {
			Map<String, String> headers = new HashMap<>();
			String res = HttpSendUtils2.get("http://qt.liuxueqiao.net/school/ajax_sch_list?p=" + i, headers);
			StringBuffer sb = new StringBuffer();
			sb.append(res);
			/**
			Document document = Jsoup.parse(res);
			Elements img64 = document.getElementsByClass("img-64");
			
			for(int x = 0; x < img64.size(); x++) {
				Element item = img64.get(x);
				Element parent = item.parent();
				Element h3 = parent.getElementsByTag("h3").get(0);
				Element p = parent.getElementsByTag("p").get(0);
				
				Attribute atr = parent.attribute("href");
				//System.out.println(h3.html() + atr.getValue());
				sb.append("kimi-NAME:" + h3.html() + "kimi-TITLE:" + p.html() + "kimi-URI:" + atr.getValue()+"\r\n");
				if(atr.getValue().indexOf("https://ooyyee-static.qiniu.ooyyee.com/employment/report/") != -1) {
					int yearStartIndex = atr.getValue().indexOf("report/20") + 7;
					String year = atr.getValue().substring(yearStartIndex, yearStartIndex + 4);
					String fileName = atr.getValue().substring(yearStartIndex + 5);
					//System.out.println(year + "," + fileName);
					try {
						DownloadPdf.downLoadByUrl(atr.getValue(),fileName, "F://就业报告//" + year);
					} catch (Exception e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
				
			}
			*/
			writeTempFile(new File("G://LX//LXresult"+i+".txt"), sb);
		}	
		
	}
	
	public static void runxuhang_list(int page) {
		Map<String, String> headers = new HashMap<>();
		headers.put(":authority", "www.forwardpathway.com");
		headers.put(":method", "POST");
		headers.put(":path", "/wp-admin/admin-ajax.php?action=get_wdtable&table_id=27");
		headers.put("Accept", "application/json, text/javascript, */*; q=0.01");
		headers.put("Accept-Encoding", "gzip, deflate, br");
		headers.put("Accept-Language", "en-US,en;q=0.9");
		headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
		headers.put("Cookie", "_ga=GA1.1.536031483.1707529578; cf_clearance=yLBReco.Lk7.SzRenfb2gAdzsdWrxkJ9CRDnh8uMpRE-1707531557-1-AXobRZ/n28DZr8w59JSxIxRqqtwU9Y9eOx12TzZxlMznDzL41Dx7QtKKnJL2158pUJX8GjQrrZutHsNDZocNNLI=; _ga_XXKYVTBWNE=GS1.1.1707529577.1.1.1707531561.3.0.0");
		headers.put("Origin", "https://www.forwardpathway.com");
		headers.put("Referer", "thttps://www.forwardpathway.com/us-college-database");
		headers.put("Sec-Ch-Ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"");
		headers.put("Sec-Ch-Ua-Mobile", "?0");
		headers.put("Sec-Ch-Ua-Platform", "\"macOS\"");
		headers.put("Sec-Ch-Ua-Dest", "empty");
		headers.put("Sec-Ch-Ua-Mode", "cors");
		headers.put("Sec-Fetch-Site", "same-origin");
		headers.put("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 7_1_2 like Mac OS X) App leWebKit/537.51.2 (KHTML, like Gecko) Version/7.0 Mobile/11D257 Safari/9537.53");
		headers.put("X-Requested-With", "XMLHttpRequest");
		
      headers.put("draw", "6");
      headers.put("columns[0][data]", "0");
      headers.put("columns[0][name]", "ranking_now");
      headers.put("columns[0][searchable]", "true");
      headers.put("columns[0][orderable]", "false");
      headers.put("columns[0][search][value]", "|");
      headers.put("columns[0][search][regex]", "false");
      headers.put("columns[1][data]", "1");
      headers.put("columns[1][name]", "cname");
      headers.put("columns[1][searchable]", "true");
      headers.put("columns[1][orderable]", "false");
      headers.put("columns[1][search][value]", "");
      headers.put("columns[1][search][regex]", "false");
      headers.put("columns[2][data]", "2");
      headers.put("columns[2][name]", "name");
      headers.put("columns[2][searchable]", "true");
      headers.put("columns[2][orderable]", "false");
      headers.put("columns[2][search][value]", "");
      headers.put("columns[2][search][regex]", "false");
      headers.put("columns[3][data]", "3");
      headers.put("columns[3][name]", "logo");
      headers.put("columns[3][searchable]", "true");
      headers.put("columns[3][orderable]", "false");
      headers.put("columns[3][search][value]", "");
      headers.put("columns[3][search][regex]", "false");
      headers.put("columns[4][data]", "4");
      headers.put("columns[4][name]", "statename");
      headers.put("columns[4][searchable]", "true");
      headers.put("columns[4][orderable]", "false");
      headers.put("columns[4][search][value]", "");
      headers.put("columns[4][search][regex]", "false");
      headers.put("columns[5][data]", "5");
      headers.put("columns[5][name]", "ranking_prev");
      headers.put("columns[5][searchable]", "true");
      headers.put("columns[5][orderable]", "false");
      headers.put("columns[5][search][value]", "");
      headers.put("columns[5][search][regex]", "false");
      headers.put("columns[6][data]", "6");
      headers.put("columns[6][name]", "rankchange");
      headers.put("columns[6][searchable]", "true");
      headers.put("columns[6][orderable]", "false");
      headers.put("columns[6][search][value]", "");
      headers.put("columns[6][search][regex]", "false");
      headers.put("start", "300");
      headers.put("length", "100");
      headers.put("search[value]", "");
      headers.put("search[regex]", "false");
      headers.put("wdtNonce", "2509ab85c5");
      headers.put("sRangeSeparat", "｜");
    	headers.put("start", "100");
		headers.put("length", "100");
		
		
		Map<String, String> params = new HashMap<>();
		
		params.put("action", "get_wdtable");
		params.put("table_id", "27");
		
		
		String res = HttpSendUtils2.post("https://www.forwardpathway.com/wp-admin/admin-ajax.php?action=get_wdtable&table_id=27", params, headers);
		File file = new File("/Users/<USER>/eclipse-workspace/XU_HANG/");
		if(!file.exists()) {
			try {
				file.mkdirs();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		writeTempFile(new File(file, "list_"+page+"_page.txt"), new StringBuffer(res));
		System.out.println(res);
	}
	
	
	public static void runOFFER(int page) {
		Map<String, String> headers = new HashMap<>();
		headers.put("cookie", "_gid=GA1.2.1195793678.1703995751; 4Oaf_61d6_saltkey=Wme6za1e; 4Oaf_61d6_auth=0f41PVhxQ8zwk1eqfL0OcRMOY8702p97TUjGR1WScXFZlucFHOR9CjF7sVgBY63DM3PhQ51vR1%2FK6ZWFekDJsSHGdQew; 4Oaf_61d6_cuid=1125791; _ga_HHZQ34796R=GS1.1.1703995751.1.1.1703996156.0.0.0; _ga=GA1.2.2137876744.1703995751; _gat_gtag_UA_145120380_1=1");
		headers.put(":authority", "api.1point3acres.com");
		headers.put(":method", "POST");
		headers.put(":path", "/offer/colleges-search?ps=20&pg="+page);
		headers.put(":scheme", "https");
		headers.put("Accept", "application/json, text/plain, */*");
		headers.put("Accept-Encoding", "gzip, deflate, br");
		headers.put("Accept-Language", "en-US,en;q=0.9");
		headers.put("Content-Type", "application/json;charset=UTF-8");
		headers.put("Origin", "https://offer.1point3acres.com");
		headers.put("Referer", "https://offer.1point3acres.com/db/colleges");
		headers.put("Sec-Ch-Ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"");
		headers.put("Sec-Ch-Ua-Mobile", "?0");
		headers.put("Sec-Fetch-Dest:", "empty");
		headers.put("Sec-Ch-Ua-Platform", "\"macOS\"");
		headers.put("Sec-Fetch-Mode", "cors");
		headers.put("Sec-Fetch-Site", "same-site");
		headers.put("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
		headers.put("Remote Address", "************:443");
		headers.put("Referrer Policy", "no-referrer-when-downgrade");
		
		
		Map<String, String> params = new HashMap<>();
		params.put("pg", String.valueOf(page));
		params.put("ps", "20");
		
		String res = HttpSendUtils2.post("https://api.1point3acres.com/offer/colleges-search?ps=20&pg="+page, params, headers);
		File file = new File("/Users/<USER>/eclipse-workspace/OFFDUO/");
		if(!file.exists()) {
			try {
				file.mkdirs();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		writeTempFile(new File(file, "info_"+page+"_data.txt"), new StringBuffer(res));
		System.out.println(res);
	}
	
	public static void runZAILAIREN(int page) {
		Map<String, String> headers = new HashMap<>();
		//headers.put("cookie", "_gid=GA1.2.1994311884.1703907595; 4Oaf_61d6_saltkey=G9J6aKPS; 4Oaf_61d6_auth=b641MRRp1l1aWPZFQgf4jKdpCxUMp08GaK3d5EBh%2BfGM2aF49HrQGF8gJv1ZAowT2NQKBS2ysJfTZLLJExrh9bGCZgfT; 4Oaf_61d6_cuid=1125791; _ga=GA1.1.2120020469.1703907594; _ga_HHZQ34796R=GS1.1.1703907594.1.1.1703909112.0.0.0");
		
		Map<String, String> params = new HashMap<>();
		params.put("currentPage", String.valueOf(page));
		params.put("country", null);
		params.put("pageSize", "200");
		params.put("qsRankStrList", "[\"51:100\"]");
		
		String res = HttpSendUtils2.post("https://open.valeon.com/api/publicity-p/university/list", params, headers);
		File file = new File("/Users/<USER>/eclipse-workspace/zailairen/");
		if(!file.exists()) {
			try {
				file.mkdirs();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		writeTempFile(new File(file, "info_"+page+"_data.txt"), new StringBuffer(res));
		System.out.println(res);
	}
	
	public static void runZAILAIRENRealoffer(String schoolname) {
		Map<String, String> headers = new HashMap<>();
		//headers.put("cookie", "_gid=GA1.2.1994311884.1703907595; 4Oaf_61d6_saltkey=G9J6aKPS; 4Oaf_61d6_auth=b641MRRp1l1aWPZFQgf4jKdpCxUMp08GaK3d5EBh%2BfGM2aF49HrQGF8gJv1ZAowT2NQKBS2ysJfTZLLJExrh9bGCZgfT; 4Oaf_61d6_cuid=1125791; _ga=GA1.1.2120020469.1703907594; _ga_HHZQ34796R=GS1.1.1703907594.1.1.1703909112.0.0.0");
		headers.put(":authority", "open.valeon.com");
		headers.put(":method", "POST");
		headers.put(":path", "/api/publicity-p/getUniversityRelaOffer");
		headers.put(":scheme", "https");
		headers.put("Accept", "application/json, text/plain, */*");
		headers.put("Accept-Encoding", "gzip, deflate, br");
		headers.put("Accept-Language", "en-US,en;q=0.9");
		headers.put("Content-Type", "application/json");
		headers.put("Origin", "https://liuxue.zailairen.com/");
		headers.put("Referer", "https://liuxue.zailairen.com/");
		headers.put("Sec-Ch-Ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"");
		headers.put("Sec-Ch-Ua-Mobile", "?0");
		headers.put("Sec-Fetch-Dest:", "empty");
		headers.put("Sec-Ch-Ua-Platform", "\"macOS\"");
		headers.put("Sec-Fetch-Mode", "cors");
		headers.put("Sec-Fetch-Site", "cross-site");
		headers.put("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
		headers.put("Remote Address", "************:443");
		headers.put("Referrer Policy", "strict-origin-when-cross-origin");
		
		Map<String, String> params = new HashMap<>();
		params.put("univer", "Massachusetts Institute of Technology");
		
		String res = HttpSendUtils2.post("https://open.valeon.com/api/publicity-p/university/list", params, headers);
		File file = new File("/Users/<USER>/eclipse-workspace/zailairen/");
		if(!file.exists()) {
			try {
				file.mkdirs();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		writeTempFile(new File(file, "info_"+schoolname+"_data.txt"), new StringBuffer(res));
		System.out.println(res);
	}
	
	
	public static void getmajorcode(String yxmc,  int year) {
		Map<String, String> headers = new HashMap<>();
		
		String url = "https://offer.1point3acres.com/_next/data/g_sHXvlmlTOuJ_qDcxxC2/db/cn-colleges/"+yxmc+"/"+year+".json";
		System.out.println(url);
		
	}
	
	//抓取掌上考研的招生专业数据（双一六建设数据）
	public static void runnmajorcode(String yxmc,  int year) {
		Map<String, String> headers = new HashMap<>();
		headers.put(":authority", "offer.1point3acres.com");
		headers.put(":method", "GET");
		headers.put(":path", "/_next/data/g_sHXvlmlTOuJ_qDcxxC2/db/cn-colleges/"+yxmc+"/"+year+".json");
		headers.put(":scheme", "https");
		headers.put("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
		headers.put("Accept-Encoding", "gzip, deflate, br");
		headers.put("Accept-Language", "en-US,en;q=0.9");
		headers.put("Content-Type", "application/json");
		headers.put("Origin", "https://liuxue.zailairen.com/");
		headers.put("Referer", "https://liuxue.zailairen.com/");
		headers.put("Sec-Ch-Ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"");
		headers.put("Sec-Ch-Ua-Mobile", "?0");
		headers.put("Sec-Fetch-Dest:", "document");
		headers.put("Sec-Fetch-User:", "?1");
		headers.put("Sec-Ch-Ua-Platform", "\"macOS\"");
		headers.put("Upgrade-Insecure-Requests", "1");
		headers.put("Sec-Fetch-Mode", "navigate");
		headers.put("Sec-Fetch-Site", "none");
		headers.put("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
		headers.put("Remote Address", "************:443");
		headers.put("Referrer Policy", "strict-origin-when-cross-origin");
		headers.put("Cookie", "_gid=GA1.2.1195793678.1703995751; 4Oaf_61d6_saltkey=Wme6za1e; 4Oaf_61d6_auth=0f41PVhxQ8zwk1eqfL0OcRMOY8702p97TUjGR1WScXFZlucFHOR9CjF7sVgBY63DM3PhQ51vR1%2FK6ZWFekDJsSHGdQew; 4Oaf_61d6_cuid=1125791; 4Oaf_61d6_lastvisit=1703994085; 4Oaf_61d6_visitedfid=71; 4Oaf_61d6_ulastactivity=1703997685%7C0; 4Oaf_61d6_cookie_hash=9fae8980971008dda43165f5f6f4530c; FCNEC=%5B%5B%22AKsRol87QVnvLIb5vR6_mTkWIymx6HTWNW1iFXc_mn2riMiQyvTBHMXv6Cky0KGKg5RtoiLKvK-TCAbZK4WoeUmeAcGq6Xg2gNgvLnMzat6rrNgBcQnsi7rfbUaXklHTQlAfzs50YP1cnzeVU5l-5D0yhyBcrXWsNA%3D%3D%22%5D%5D; 4Oaf_61d6_tz_offset=-420; _pubcid=4426892d-2747-4f0e-a249-1e2cd785b9b0; __qca=P0-1401463406-1703997691055; _cc_id=24962f9e260007ca52791d4ae7f503e7; panoramaId_expiry=1704084091516; cto_bundle=G4D7319tREVBR2N2OXJ4RCUyQkJTdTklMkJsVjdscmxNbyUyQnduRkFvVmdVWElISkY0QW5NMDBYM1RqZyUyQkNIZVF3dHpxTGZyd2xBeFNQOElwYnZaSjI1cklNa0pOZjRmQnpDaCUyQkVxM3pyclBuZk1qYkFwck1PZ1pwWm9iWGNWR25ablF6enhHJTJCRGRFVnFYUUwlMkYlMkJDemtGaUUlMkJpcHhnSU5PQ083JTJCVnMlMkJwcFI2aWdUNHhWaVFsVFFMUXhJUXdBcDA3ZHZJSEJjcEUlMkZHU1lSajVJQzBieWQ1WVM4UGN6YnlRJTNEJTNE; cto_bidid=kijly19PUHE3ZUttS2NSOTVUYm5iVXN2TW1yeWwwQWw3WjY1WFJ1R2s0b0UwdUx0RVQ1UHozWFlVQTlMY0lTaUJ6d1FWNkFyY0ZxTDF1cDdDVzJNc2ViT3o0dVVnMHUyckttOHlmdDY4N0M5ZyUyRjAwMDlOaDBnTVY3SEZjRFNpeHNXYkppVFZwaDlTeHFzcTJJNVRRUVYyQyUyQm9nJTNEJTNE; 4Oaf_61d6_lastact=1703997702%09forum.php%09image; __gads=ID=ba286e012f7ae92b:T=1703997687:RT=1703998086:S=ALNI_MbQznsJyY4hCnthF95dxNzoE-prQw; __gpi=UID=00000ccc9cb0fbc7:T=1703997687:RT=1703998086:S=ALNI_MYtMDQSvNPTr6hTDx31jiH-0n05jQ; _ga_R36F9DJGPT=GS1.1.1703997687.1.1.1703998086.60.0.0; _ga=GA1.1.2137876744.1703995751; _ga_HHZQ34796R=GS1.1.1704035490.2.1.1704044542.0.0.0");
		
		String url = "https://offer.1point3acres.com/_next/data/g_sHXvlmlTOuJ_qDcxxC2/db/cn-colleges/"+yxmc+"/"+year+".json";
		System.out.println(url);
		String res = HttpSendUtils2.get(url, headers);
		File file = new File("/Users/<USER>/eclipse-workspace/cncolleges/");
		if(!file.exists()) {
			try {
				file.mkdirs();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		writeTempFile(new File(file, "chinese_"+yxmc+"_"+year+".txt"), new StringBuffer(res));
		System.out.println(res);
	}
	
	
///	
	//调剂
	public static void runn2_4(int schoolCode) {
		Map<String, String> headers = new HashMap<>();
		Map<String, String> params = new HashMap<>();
		params.put("school_id", String.valueOf(schoolCode));
		params.put("keyword", "");
		params.put("page", "1");
		params.put("limit", "100");
	
		String res = HttpSendUtils2.post("https://api.kaoyan.cn/pc/adjust/schoolAdjustList", params, headers);
		
		File file = new File("E://kaoyan//"+schoolCode);
		if(!file.exists()) {
			try {
				file.mkdirs();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		writeTempFile(new File(file, "runn2_4_"+schoolCode+"adjust_data.txt"), new StringBuffer(res));
		System.out.println(res.substring(0,20));
	}
	
	
	
	private static void writeTempFile(File file, StringBuffer sb) {
	    try {
	      BufferedWriter bw = new BufferedWriter(new FileWriter(file));
	      bw.write(sb.toString());
	      bw.flush();
	      bw.close();
	    } catch (IOException e) {
	      e.printStackTrace();
	    } 
	  }
	//https://www.xunlugaokao.com/convert_exam_scores/0?exam_name=2023%E5%B1%8A%E6%B3%B8%E5%B7%9E%E4%B8%80%E8%AF%8A&exam_score=551&student_type=%E7%90%86%E7%A7%91
	public static String test(String aredID, String wl, String exam, int score) {
		System.out.println(score+"");
		Map<String, String> params = new HashMap<>();
		Map<String, String> headers = new HashMap<>();
		params.put("areaId", aredID);
		params.put("wenLi", wl);
		params.put("exam", exam);
		params.put("score", String.valueOf(score));
		headers.put("Content-Type", "application/json;charset=UTF-8");
		headers.put("Set-Cookie", "JSESSIONID=0AB8A0851D75C8D3EB15E8CE340B3C0A");
		String res = HttpSendUtils2.post("http://l.xueya63.com/diagnose/data/score?_=1703604900236", params, headers);
		System.out.println(res+"");
		
		int start = res.indexOf("convertScore") + "convertScore\":\"".length();
		String cvt = res.substring(start, start + 3);
		System.out.println(aredID+","+wl+","+exam+","+score + " - " + cvt);
		return cvt;
	}
	
	public static String testXUNLU(int score) {
		Map<String, String> headers = new HashMap<>();
		//headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
		//headers.put("Cookie", "Hm_lvt_89058dc85076e76bc971f6b58d30b5e8=1700832699,1701134686,1701307779,1702214910; JSESSIONID=EC53AEC17F5D79D0816FB194B3D921D0; Hm_lpvt_89058dc85076e76bc971f6b58d30b5e8=1703386280");
		headers.put("Set-Cookie", "_gaokao_session=qJ%2BRRHgxAFdDcczo1GfTg533WGIvcczT%2Fii5EvYdx9EzQnc4PKeFWsBMoj72lcTmnVTXaY7eSBfUN2Z9i%2BQGgMlwfE19hVZ7TEsfyRmYVsmewVn1lbsaVq9PQm3%2BXHsmKQFjcBPefpyEq3dfXm2OEPGtceKOQbpYVsPgfJelzGPYkAXGBKxM91%2FYXu7vgHqWYu0sAbMNsjeQeE%2FTW31tdAiJ9hSouFEe8%2Brwxp%2FWG6OrdtY80bfGP5trB1pf0RAd6UIIiqst5C3OU2ZwNrn6yC1O3TvDA7o3OYI79HiuZq4Mpc30lkJBnNiYDl6evQZ7caM%2BZIgastEOsuvZP09dcIB4GQyxgVWSRbQI%2FbZqmhY8tzUSFltDqkRw2Usuex70ksRnOXGD3FLj%2B0D%2F9JgNyDn9yhswdWzkOK%2B8hFpJGuPXZij9P%2B594T97hzuFB74VZMCMEB0RVKOdR8pKlb6%2BcOlO%2FnjN%2FGjDwxwb%2B3vxi4T1lhqM5CAjlAW3a0UyE2db%2FRZJ8%2BqT6FxM%2BhZHVkXphVtB5gPGM0Hs4CBSWWDI%2FOd1M0kEHqm%2FYOqtiYORAelZKgppO99c5Rulzq2JVHaCzbZdhkhWqCuvi3%2FP1pCVVBXO1mi9BhDQnZoKBxfrub2v8lXQQgCrWnpFrJFyCZWRDu1CFgxbSZfDSso7uw7b5sG0RBQG96AsBmz7KciErhYEnab6VHxtMCfNHFfwqvzrJnl2Ny2QzFSB3USXuEqRt%2BSITSGIhtb286LGgHHW4s5ZgIA7ALQD72zNh8P2%2Fv7ZfZ1AHFTJ5b%2BQmJ9ICWrYgtPAQQg%3D--XZ%2BzXpv2qaB8OzM1--GirXIsmmHxTARpG7dnD02w%3D%3D; path=/; HttpOnly\r\n"
				+ "\r\n"
				+ "");
		String res = HttpSendUtils2.get("https://www.xunlugaokao.com/convert_exam_scores/0?exam_name=2024%E5%B1%8A%E8%B5%84%E9%98%B3%E4%B8%80%E8%AF%8A&exam_score="+score+"&student_type=%E6%96%87%E7%A7%91", headers);
		
		System.out.println(""+score + " - " + res);
		return res;
	}
	
	public static void runXUNLU() {
		List<String> strList = new ArrayList<>();
		for(int i=150; i<= 700;i++) { //692
			String xx = testXUNLU(i);
			strList.add("#"+ i + "," + xx + "*");
			try {
				Thread.sleep(100);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		StringBuffer sb = new StringBuffer();
		for(String x : strList) {
			sb.append(x + "\r\n");
		}
		writeTempFile(new File("E://ZY-W1.txt"), sb);
	}

	
	public static void run() {
		List<String> strList = new ArrayList<>();
		for(int i=180; i<= 660;i++) { //692
			String xx = test("5115","1","1", i);
			strList.add("#"+ i + "," + xx + "*");
			try {
				Thread.sleep(100);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		StringBuffer sb = new StringBuffer();
		for(String x : strList) {
			sb.append(x + "\r\n");
		}
		writeTempFile(new File("E://zdksMY_W0.txt"), sb);
		strList.clear();
		strList = new ArrayList<>();
		
		for(int i=180; i<= 703;i++) { //692
			String xx = test("5115","5","1", i);
			strList.add("#"+ i + "," + xx + "*");
			try {
				Thread.sleep(100);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		sb = new StringBuffer();
		for(String x : strList) {
			sb.append(x + "\r\n");
		}
		writeTempFile(new File("E://zdksDZ_WL1.txt"), sb);
	}
	
	
	//循证 //{"ret_code":"1000","ret_msg":"","data":{"rank":"26407","fenshu":591},"total_lines":1}
	public static String test2(String aredID, String wl, String exam, int score) {
		System.out.println(score+"");
		Map<String, String> params = new HashMap<>();
		Map<String, String> headers = new HashMap<>();
		params.put("city", aredID);
		params.put("km", wl);
		params.put("zd", exam);
		params.put("fen", String.valueOf(score));
		headers.put("Content-Type", "application/json, text/javascript, */*; q=0.01");
		//headers.put("Set-Cookie", "PHPSESSID=diemm7lqt60qtktjnvrkfb0g24");
		headers.put("Set-Cookie", "PHPSESSID=diemm7lqt60qtktjnvrkfb0g24; userlogin=dNnnzKmqUD0Khhp2hNZwVj34z_x_gYXj_i_aqV1aAkS8JXA6E8U%3D; subject=%E6%96%87%E7%A7%91; score=550; km=1; ztype=1; rank=10591; valueType=T; pcType=0; zyType=%E6%9C%AC%E7%A7%91%E6%8F%90%E5%89%8D%E6%89%B9; pc=0");
		String res = HttpSendUtils2.post("https://www.forwardpathway.com/8413", params, headers);
		System.out.println(res+"");
		
		int start = res.indexOf("fenshu") + "fenshu\":".length();
		String cvt = res.substring(start, start + 3);
		System.out.println(aredID+","+wl+","+exam+","+score + " - " + cvt);
		return cvt;
	}
	
	public static void run2() {
		List<String> strList = new ArrayList<>();
		for(int i=180; i<= 660;i++) { //692
			String xx = test2("dz","2","1", i);
			strList.add("#"+ i + "," + xx + "*");
			try {
				Thread.sleep(2000);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		StringBuffer sb = new StringBuffer();
		for(String x : strList) {
			sb.append(x + "\r\n");
		}
		writeTempFile(new File("E://zdksDZ_W1.txt"), sb);
		strList.clear();
		strList = new ArrayList<>();
		
		for(int i=180; i<= 703;i++) { //692
			String xx = test2("dz","1","1", i);
			strList.add("#"+ i + "," + xx + "*");
			try {
				Thread.sleep(100);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		sb = new StringBuffer();
		for(String x : strList) {
			sb.append(x + "\r\n");
		}
		writeTempFile(new File("E://zdksDZ_L1.txt"), sb);
	}

	//抓取掌上考研的数据
		public static void runxuhang(String URL) {
			Map<String, String> headers = new HashMap<>();
			headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
			String res = HttpSendUtils2.get(URL, headers);
			
			// 使用 Jsoup 解析 HTML
	        Document doc = Jsoup.parse(res);
	        Elements rows = doc.select("figure table tbody tr");

	        int rowCnt = 0;
	        for (Element row : rows) {
	        	rowCnt++;
	        	
	        	if(rowCnt == 1) {
	        		continue;
	        	}
		        int columnIndex = 0;
	            Elements tds = row.select("td");
	            
	            String yxmc = null;
	            String rptName = null;
	            String rptURL = null;
	            
	            for (Element td : tds) {
		        	columnIndex++;
	                // 3-1 打印纯文本
	                //System.out.println("文本：" + td.text().trim());

		        	Elements links = td.select("a");
		        	
		        	if(columnIndex == 1) {
		        		//学校名称
		        		if(links.size() > 1) {
			        		Element a = links.get(1);
			        		//System.out.println(columnIndex + "学校  URL：" + a.text().trim() + ", " + a.attr("abs:href"));
			        		yxmc = a.text().trim() ;
		        		}else if(links.size() == 1) {
		        			Element a = links.get(0);
			        		//System.out.println(columnIndex + "学校  URL：" + a.text().trim() + ", " + a.attr("abs:href"));
			        		yxmc = a.text().trim() ;
		        		}
		        	}else if(columnIndex == 2) {
		        		// 报告名称
		        		for(Element a : links) {
		        			//System.out.println(columnIndex + "报告  URL：" + a.text().trim() + ", " + a.attr("abs:href"));
		        			rptName = a.text().trim();
		        			rptURL = a.attr("abs:href");
		        			
		        			if("江苏旅游职业学院".equals(yxmc)) {
		        				
		        				rptName = "2021届毕业生就业质量报告";
		        				rptURL = "https://jstc.91job.org.cn/sub-station/detail?xwid=D467EE37A1A9BB6DE0530100007F60C1&xxdm=14604";
		        			}
		        			
		        			try {
		        			System.out.println("insert into yf_rpt_yx_jyzlbg(rpt_id, yxmc_org, rpt_name, rpt_url, nf, create_tm) "
		        					+ "values('"+UUID.randomUUID().toString()+"','"+yxmc+"','"+rptName+"','"+rptURL+"', "+Integer.parseInt(rptName.substring(0,4))+" ,now());");
		        			}catch(Exception ex) {
		        				ex.printStackTrace();
		        				System.out.println("rptName:"+rptName);
		        			}
		        		}
		        	}
		        	
	            }
	           // System.out.println("---");  // 行分隔符
	        }
		}

		
		
	public static void main(String[] args) {
		runxuhang("https://www.xiaoyuanzhaopin.net/gxbg/");
		runxuhang("https://www.xiaoyuanzhaopin.net/hanbg/");
		runxuhang("https://www.xiaoyuanzhaopin.net/ynbg/");
		runxuhang("https://www.xiaoyuanzhaopin.net/gzbg/");
		runxuhang("https://www.xiaoyuanzhaopin.net/xzbg/");
	}
	
	


}
