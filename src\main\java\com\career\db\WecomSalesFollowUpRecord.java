package com.career.db;

import java.util.Date;

public class WecomSalesFollowUpRecord {

	private String record_id;
    private String staff_id;
    private String customer_id;
    private Date follow_up_date;
    private Integer follow_up_method;
    private String follow_up_notes;
    private Date next_follow_up_date;
    private Integer status;
    private Date create_tm;
    private Date updated_tm;
    private String ext_customer_name;
    private String wecom_saas_id;
    private Integer purchase_intention;
    private String recommend_product_id;
    private Integer follow_up_type;
    
	public Integer getFollow_up_type() {
		return follow_up_type;
	}
	public void setFollow_up_type(Integer follow_up_type) {
		this.follow_up_type = follow_up_type;
	}
	public String getWecom_saas_id() {
		return wecom_saas_id;
	}
	public void setWecom_saas_id(String wecom_saas_id) {
		this.wecom_saas_id = wecom_saas_id;
	}
	public Integer getPurchase_intention() {
		return purchase_intention;
	}
	public void setPurchase_intention(Integer purchase_intention) {
		this.purchase_intention = purchase_intention;
	}
	public String getRecommend_product_id() {
		return recommend_product_id;
	}
	public void setRecommend_product_id(String recommend_product_id) {
		this.recommend_product_id = recommend_product_id;
	}
	public String getRecord_id() {
		return record_id;
	}
	public void setRecord_id(String record_id) {
		this.record_id = record_id;
	}
	public String getStaff_id() {
		return staff_id;
	}
	public void setStaff_id(String staff_id) {
		this.staff_id = staff_id;
	}
	public String getCustomer_id() {
		return customer_id;
	}
	public void setCustomer_id(String customer_id) {
		this.customer_id = customer_id;
	}
	public Date getFollow_up_date() {
		return follow_up_date;
	}
	public void setFollow_up_date(Date follow_up_date) {
		this.follow_up_date = follow_up_date;
	}
	public Integer getFollow_up_method() {
		return follow_up_method;
	}
	public void setFollow_up_method(Integer follow_up_method) {
		this.follow_up_method = follow_up_method;
	}
	public String getFollow_up_notes() {
		return follow_up_notes;
	}
	public void setFollow_up_notes(String follow_up_notes) {
		this.follow_up_notes = follow_up_notes;
	}
	public Date getNext_follow_up_date() {
		return next_follow_up_date;
	}
	public void setNext_follow_up_date(Date next_follow_up_date) {
		this.next_follow_up_date = next_follow_up_date;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public Date getCreate_tm() {
		return create_tm;
	}
	public void setCreate_tm(Date create_tm) {
		this.create_tm = create_tm;
	}
	public Date getUpdated_tm() {
		return updated_tm;
	}
	public void setUpdated_tm(Date updated_tm) {
		this.updated_tm = updated_tm;
	}
	public String getExt_customer_name() {
		return ext_customer_name;
	}
	public void setExt_customer_name(String ext_customer_name) {
		this.ext_customer_name = ext_customer_name;
	}
    
    
}
