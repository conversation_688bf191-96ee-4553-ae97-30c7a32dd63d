package com.career.db;

import java.util.Date;

public class LXCard {
	
	private String cell_phone;
	private String cardno;
	private String passwd;
	private String prov;
	private float gpa;
	private float eng;
	private float ielts;
	private int toefl;
	private int nf;
	private int gpa_score;
	
	private Date create_tm;
	private Date expire_tm;
	private Date last_login;
	private int status;
	
	
	public float getEng() {
		return eng;
	}
	public void setEng(float eng) {
		this.eng = eng;
	}
	public int getNf() {
		return nf;
	}
	public void setNf(int nf) {
		this.nf = nf;
	}
	public int getGpa_score() {
		return gpa_score;
	}
	public void setGpa_score(int gpa_score) {
		this.gpa_score = gpa_score;
	}
	public String getProv() {
		return prov;
	}
	public void setProv(String prov) {
		this.prov = prov;
	}
	public String getCell_phone() {
		return cell_phone;
	}
	public void setCell_phone(String cell_phone) {
		this.cell_phone = cell_phone;
	}
	public Date getCreate_tm() {
		return create_tm;
	}
	public void setCreate_tm(Date create_tm) {
		this.create_tm = create_tm;
	}
	public Date getExpire_tm() {
		return expire_tm;
	}
	public void setExpire_tm(Date expire_tm) {
		this.expire_tm = expire_tm;
	}
	public Date getLast_login() {
		return last_login;
	}
	public void setLast_login(Date last_login) {
		this.last_login = last_login;
	}
	public int getStatus() {
		return status;
	}
	public void setStatus(int status) {
		this.status = status;
	}
	public String getCardno() {
		return cardno;
	}
	public void setCardno(String cardno) {
		this.cardno = cardno;
	}
	public String getPasswd() {
		return passwd;
	}
	public void setPasswd(String passwd) {
		this.passwd = passwd;
	}
	public float getGpa() {
		return gpa;
	}
	public void setGpa(float gpa) {
		this.gpa = gpa;
	}
	public float getIelts() {
		return ielts;
	}
	public void setIelts(float ielts) {
		this.ielts = ielts;
	}
	public int getToefl() {
		return toefl;
	}
	public void setToefl(int toefl) {
		this.toefl = toefl;
	}

	
}
