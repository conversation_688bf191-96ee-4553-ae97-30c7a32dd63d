package com.career.db;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;

import com.career.utils.ResultVO;
import com.career.utils.SQLLogUtils;
import com.career.utils.Tools;

public class AiJDBC {

	static String URL = JDBCConstants.URL;
	static String USER = JDBCConstants.USER;
	static String PASSWD = JDBCConstants.PASSWD;
	public static int PAGE_ROW_CNT = 10;

	static {
		try {
			Class.forName("com.mysql.cj.jdbc.Driver");
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
	
	public void adjustMakerFormYxNoWithBatchIddForDrag(String sfCode, List<AiTbForm> updateList) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "update ai_tb_"+sfCode+"_form_maker set seq_no_yx = ? where id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			for(AiTbForm form : updateList) {
				ps.setInt(1, form.getSeq_no_yx());
				ps.setInt(2, form.getId());
				ps.addBatch();
			}
			ps.executeBatch();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	
	public ResultVO searchJhxDataInAiCardForSearch(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> selectedSfValuesSets, HashSet<String> zymlSets, 
			HashSet<String> selectedYXTAGValuesSets, HashSet<String> selectedYXLXValuesSets, HashSet<String> selectedBXXZValuesSet, String keywords, int selected_znzyls, int selected_znzys, int wc_start, int wc_to, int pageNumber) {
	
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHxBean> list = new ArrayList<>();
		ResultVO resultVO = new ResultVO();
		try {
			conn =DatabaseUtils.getConnection();
			String selectedSfValuesSetsSQL = selectedSfValuesSets.size() == 0 ? "" : " AND x.yxsf in ("+Tools.getSQLQueryin(selectedSfValuesSets)+")";
			String selectedYXLXValuesSetsSQL = selectedYXLXValuesSets.size() == 0 ? "" : " AND x.ind_catg in ("+Tools.getSQLQueryin(selectedYXLXValuesSets)+")";
			
			HashSet<String> new_selectedBXXZValuesSet = new HashSet<>();
			boolean contained_hz = false;
			if(selectedBXXZValuesSet.size() > 0) {
				Iterator<String> it = selectedBXXZValuesSet.iterator();
				while(it.hasNext()) {
					String itStr = it.next();
					if(itStr.indexOf("合作") != -1) {
						contained_hz = true;
					}else {
						new_selectedBXXZValuesSet.add(itStr);
					}
				}
			}
			
			
			String zymlSetsSQL = "";
			if(zymlSets.size() > 0) {
				Iterator<String> it = zymlSets.iterator();
				StringBuffer temp_sql = new StringBuffer();
				while(it.hasNext()) {
					String itStr = it.next();
					temp_sql.append(" OR x.zyml like '%"+itStr+"%' ");
				}
				
				if(!Tools.isEmpty(temp_sql.toString())) {
					zymlSetsSQL = " AND (" + temp_sql.substring(4) + ")";
				}
			}
			
			//是否需要中外合作办学
			String hzbxSQL = contained_hz ? " AND x.znzy like '%中外%' " : "";
			
			
			String selectedBXXZValuesSetSQL = new_selectedBXXZValuesSet.size() == 0 ? "" : " AND x.ind_nature in ("+Tools.getSQLQueryin(new_selectedBXXZValuesSet)+")";
			
			
			String yxTagsSQL = "";
			if(selectedYXTAGValuesSets.size() > 0) {
				Iterator<String> it = selectedYXTAGValuesSets.iterator();
				StringBuffer temp_sql = new StringBuffer();
				while(it.hasNext()) {
					String itStr = it.next();
					temp_sql.append(" OR x.yx_tags_all like '%"+itStr+"%' ");
				}
				
				if(!Tools.isEmpty(temp_sql.toString())) {
					yxTagsSQL = " AND (" + temp_sql.substring(4) + ")";
				}
			}
			
			
			
			String SEARCH_CONDITION = " FROM " + sfCode + "_jh_" + jhYear + "x x WHERE x.xk_code like ? AND x.pc = ? AND x.pc_code = ? AND (x.yxmc like ?) AND (x.znzy IS NULL OR x.znzy like ?) AND (znzyls is null OR znzyls <= ?) AND (x.zyzzdfwc is null OR x.zyzzdfwc between ? AND ?) " 
					+ selectedSfValuesSetsSQL + zymlSetsSQL + selectedYXLXValuesSetsSQL + selectedBXXZValuesSetSQL + hzbxSQL + yxTagsSQL ;
			String ORDER = " ORDER BY x.zyzzdfwc is NULL,  x.zyzzdfwc ASC LIMIT ?,?"; 
			String SELECT_RECORD = "SELECT * " + SEARCH_CONDITION + ORDER;
			String SELECT_CNT = "SELECT count(*) as cnt " + SEARCH_CONDITION ;
			
			ps = conn.prepareStatement(SELECT_RECORD);
			int index = 1;
			ps.setString(index++, "%"+xkCode+"%");
			ps.setString(index++, pc);
			ps.setString(index++, pc_code);
			ps.setString(index++, "%"+keywords+"%");
			ps.setString(index++, "%"+keywords+"%");
			ps.setInt(index++, selected_znzyls);
			ps.setInt(index++, wc_start);
			ps.setInt(index++, wc_to);
			ps.setInt(index++, (pageNumber - 1) * PAGE_ROW_CNT);
			ps.setInt(index++, PAGE_ROW_CNT);
			SQLLogUtils.printSQL(ps);
			rs = ps.executeQuery();
			JHBean bean = null;
			while (rs.next()) {
				list.add(ZyzdFormJDBC.setJHxBeanAll(dataYear, rs));
			}
			
			
			index = 1;
			ps = conn.prepareStatement(SELECT_CNT);
			ps.setString(index++, "%"+xkCode+"%");
			ps.setString(index++, pc);
			ps.setString(index++, pc_code);
			ps.setString(index++, "%"+keywords+"%");
			ps.setString(index++, "%"+keywords+"%");
			ps.setInt(index++, selected_znzyls);
			ps.setInt(index++, wc_start);
			ps.setInt(index++, wc_to);
			rs = ps.executeQuery();
			while (rs.next()) {
				resultVO.setRecordCnt(rs.getInt("cnt"));
			}
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		resultVO.setResult(list);
		resultVO.setCurrentPage(pageNumber);
		return resultVO;
	}
	
	
	
	public ResultVO searchOriginalJhData(int jhYear, String sfCode, String xkCode, String pc, String pc_code, HashSet<String> yxsfSets, HashSet<String> zymlSets, HashSet<String> catgSets, HashSet<String> natureSets, 
			boolean is985, boolean is211, boolean issyl, boolean isgzd, boolean isszd, boolean isssyx, boolean isbyzg, boolean hasbsd, boolean hasssd, boolean isgjts, String keywords, int zdfwc_from, int zdfwc_to, int pageNumber) {
		
		
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		ResultVO resultVO = new ResultVO();
		try {
			conn =DatabaseUtils.getConnection();
			String sfSQL = yxsfSets.size() == 0 ? "" : " and x.yxsf in ("+Tools.getSQLQueryin(yxsfSets)+")";
			String natureSQL = natureSets.size() == 0 ? "" : " and x.ind_nature in ("+Tools.getSQLQueryin(natureSets)+")";
			String zymlSQL = zymlSets.size() == 0 ? "" : " and x.zyml in ("+Tools.getSQLQueryin(zymlSets)+")";
			String catgSQL = catgSets.size() == 0 ? "" : " and x.ind_catg in ("+Tools.getSQLQueryin(catgSets)+")";
			String is985SQL = is985 ? " and yx_tags like '%985%" : "";
			String is211SQL = is211 ? " and yx_tags like '%211%" : "";
			String issylSQL = issyl ? " and yx_tags like '%双一流%" : "";
			String isgzdSQL = isgzd ? " and yx_tags like '%%'" : "";
			String isszdSQL = isszd ? " and yx_tags like '%%'" : "";
			String isssyxSQL = isssyx ? " and yx_tags like '%%'" : "";
			String isbyzgSQL = isbyzg ? " and yx_tags like '%%'" : "";
			String isgjtsSQL = isgjts ? " and yx_tags like '%%'" : "";
			hasbsd = false;
			hasssd = false;
			String hasbsdSQL = hasbsd ? " and CAST(yx_bsd AS UNSIGNED) > 0" : "";
			String hasssdSQL = hasssd ? " and CAST(yx_ssd AS UNSIGNED) > 0" : "";
			
			String SEARCH_CONDITION = " FROM " + sfCode + "_jh_" + jhYear + " x WHERE xk_code like ? and pc = ? and pc_code = ? and (yxmc like ? or zymc like ? ) and zdfwc between ? and ? " 
					+ sfSQL + zymlSQL + natureSQL + catgSQL + is985SQL + is211SQL + issylSQL + isgzdSQL  + isszdSQL  + isssyxSQL  + isbyzgSQL  + isgjtsSQL  + hasbsdSQL  + hasssdSQL ;
			String ORDER = " ORDER BY zdfwc ASC LIMIT ?,?";
			String SELECT_RECORD = "SELECT * " + SEARCH_CONDITION + ORDER;
			String SELECT_CNT = "SELECT count(*) as cnt " + SEARCH_CONDITION;
			
			ps = conn.prepareStatement(SELECT_RECORD);
			int index = 1;
			ps.setString(index++, "%"+xkCode+"%");
			ps.setString(index++, pc);
			ps.setString(index++, pc_code);
			ps.setString(index++, "%"+keywords+"%");
			ps.setString(index++, "%"+keywords+"%");

			ps.setInt(index++, zdfwc_from);
			ps.setInt(index++, zdfwc_to);
			
			ps.setInt(index++, (pageNumber - 1) * PAGE_ROW_CNT);
			ps.setInt(index++, PAGE_ROW_CNT);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				list.add(ZyzdFormJDBC.setJHBeanAll(dataYear, rs));
			}
			
			resultVO.setResult(list);
			
			index = 1;
			ps = conn.prepareStatement(SELECT_CNT);
			ps.setString(index++, "%"+xkCode+"%");
			ps.setString(index++, pc);
			ps.setString(index++, pc_code);
			ps.setString(index++, "%"+keywords+"%");
			ps.setString(index++, "%"+keywords+"%");

			ps.setInt(index++, zdfwc_from);
			ps.setInt(index++, zdfwc_to);
			
			rs = ps.executeQuery();
			while (rs.next()) {
				resultVO.setRecordCnt(rs.getInt("cnt"));
			}
			
			resultVO.setCurrentPage(pageNumber);
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return resultVO;
	}
	
	
	public List<JHBean> searchOriginalJhData(int jhYear, String sfCode, String xkCode, String pc, String pc_code,HashSet<String> yxsfSets, HashSet<String> zymlSets, HashSet<String> catgSets, HashSet<String> natureSets, 
			boolean is985, boolean is211, boolean issyl, boolean isgzd, boolean isszd, boolean isssyx, boolean isbyzg, boolean hasbsd, boolean hasssd, boolean isgjts, String keywords, int zdfwc_from, int zdfwc_to) {
		
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		List<JHBean> list_all = new ArrayList<>();
		ResultVO resultVO = new ResultVO();
		try {
			conn =DatabaseUtils.getConnection();
			String sfSQL = yxsfSets.size() == 0 ? "" : " and x.yxsf in ("+Tools.getSQLQueryin(yxsfSets)+")";
			String natureSQL = natureSets.size() == 0 ? "" : " and x.ind_nature in ("+Tools.getSQLQueryin(natureSets)+")";
			String zymlSQL = zymlSets.size() == 0 ? "" : " and x.zyml in ("+Tools.getSQLQueryin(zymlSets)+")";
			String catgSQL = catgSets.size() == 0 ? "" : " and x.ind_catg in ("+Tools.getSQLQueryin(catgSets)+")";
			String is985SQL = is985 ? " and yx_tags like '%985%" : "";
			String is211SQL = is211 ? " and yx_tags like '%211%" : "";
			String issylSQL = issyl ? " and yx_tags like '%双一流%" : "";
			String isgzdSQL = isgzd ? " and yx_tags like '%%'" : "";
			String isszdSQL = isszd ? " and yx_tags like '%%'" : "";
			String isssyxSQL = isssyx ? " and yx_tags like '%%'" : "";
			String isbyzgSQL = isbyzg ? " and yx_tags like '%%'" : "";
			String isgjtsSQL = isgjts ? " and yx_tags like '%%'" : "";
			hasbsd = false;
			hasssd = false;
			String hasbsdSQL = hasbsd ? " and CAST(yx_bsd AS UNSIGNED) > 0" : "";
			String hasssdSQL = hasssd ? " and CAST(yx_ssd AS UNSIGNED) > 0" : "";
			
			String SEARCH_CONDITION = " FROM " + sfCode + "_jh_" + jhYear + " x WHERE xk_code like ? and pc = ? and pc_code = ? and (yxmc like ? or zymc like ? ) and zdfwc between ? and ? " 
					+ sfSQL + zymlSQL + natureSQL + catgSQL + is985SQL + is211SQL + issylSQL + isgzdSQL  + isszdSQL  + isssyxSQL  + isbyzgSQL  + isgjtsSQL  + hasbsdSQL  + hasssdSQL ;
			String ORDER = " ORDER BY zdfwc ASC LIMIT ?,?";
			String SELECT_RECORD_ALL = "SELECT * " + SEARCH_CONDITION;
			
			
			int index = 1;
			ps = conn.prepareStatement(SELECT_RECORD_ALL);
			ps.setString(index++, "%"+xkCode+"%");
			ps.setString(index++, pc);
			ps.setString(index++, pc_code);
			ps.setString(index++, "%"+keywords+"%");
			ps.setString(index++, "%"+keywords+"%");

			ps.setInt(index++, zdfwc_from);
			ps.setInt(index++, zdfwc_to);
			
			rs = ps.executeQuery();
			while (rs.next()) {
				list_all.add(ZyzdFormJDBC.setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list_all;
	}
	
	public JHBean getOriginalJhData(int jhYear, String sfCode, int jh_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		int dataYear = jhYear-1;
		ResultSet rs = null;
		try {
			conn =DatabaseUtils.getConnection();
			String SELECT_RECORD_ALL = "SELECT * FROM " + sfCode + "_jh_" + jhYear + " x WHERE id = ? " ;
			
			ps = conn.prepareStatement(SELECT_RECORD_ALL);
			int index = 1;
			ps.setInt(index++, jh_id);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				return ZyzdFormJDBC.setJHBeanAll(dataYear, rs);
			}
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public List<JHBean> getJHByIds(int jhYear, String sfCode, HashSet<String> ids) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<JHBean> list = new ArrayList<>();
		int dataYear = jhYear-1;
		try {
			conn =DatabaseUtils.getConnection();
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.id in ("+Tools.getSQLQueryin(ids)+") ORDER BY x.yxmc, x.zyz, x.zdf DESC";
			Tools.println(SELECT_CONDITION);
			ps = conn.prepareStatement(SELECT_CONDITION);

			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(ZyzdFormJDBC.setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JHBean> getJhYxmcAndZyz(int jhYear, String sfCode, String xkCode, String pc, String pc_code, String yxdm, String yxmc, String zyz) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int dataYear = jhYear-1;
		List<JHBean> list = new ArrayList<>();
		try {
			conn =DatabaseUtils.getConnection();
			String zyzSQL = Tools.isEmpty(zyz)? "" : "and zyz = ?";
			String SELECT_CONDITION = "SELECT * FROM "+sfCode+"_jh_" + jhYear + " x WHERE x.pc = ? and pc_code = ? and xk_code like ? and x.yxdm = ? and x.yxmc = ? " + zyzSQL;
			String ORDER_CONDITION = " ORDER BY x.zdf DESC";
		
			ps = conn.prepareStatement(SELECT_CONDITION + ORDER_CONDITION);
			ps.setString(1, pc);
			ps.setString(2, pc_code);
			ps.setString(3, "%"+xkCode+"%");
			ps.setString(4, yxdm);
			ps.setString(5, yxmc);
			if(!Tools.isEmpty(zyzSQL)){
				ps.setString(6, zyz);
			}
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				list.add(ZyzdFormJDBC.setJHBeanAll(dataYear, rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JHTbpc> getAllOriginalPcs(int jhYear, String sfTable) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<JHTbpc> list = new ArrayList<>();
		
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "SELECT pc_code, pc, count(*) as cnt FROM " + sfTable + "_jh_" + jhYear + " WHERE pc_code is not null group by pc_code, pc ORDER by FIELD(pc_code, '本科', '专科', '职教本科'), count(*) DESC";
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			JHTbpc tbpc = null;
			while (rs.next()) {
				tbpc = new JHTbpc();
				tbpc.setPc_code(rs.getString("pc_code"));
				tbpc.setPc(rs.getString("pc"));
				tbpc.setExt_cnt(rs.getInt("cnt"));
				list.add(tbpc);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<JHTbpc> getAllFilledPcs(int jh_version, int jhYear, String sfTable, String order_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<JHTbpc> list = new ArrayList<>();
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "SELECT pc_code, pc, count(*) as cnt FROM ai_tb_"+sfTable+"_form_maker_main x WHERE order_id = ? and nf = ? and jh_version = ? group by pc_code, pc ORDER by FIELD(pc_code, '本科', '专科', '职教本科'), pc";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, order_id);
			ps.setInt(2, jhYear);
			ps.setInt(3, jh_version);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			JHTbpc tbpc = null;
			while (rs.next()) {
				tbpc = new JHTbpc();
				tbpc.setPc(rs.getString("pc"));
				tbpc.setPc_code(rs.getString("pc_code"));
				tbpc.setExt_cnt(rs.getInt("cnt"));
				list.add(tbpc);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<ZyzdProvinceConfig> getAllProvinceConfig() {
		return new ZyzdJDBC().getAllProvinceConfig();
	}
	
	
	private AiCard mapResultSetToAiCard(ResultSet rs) throws SQLException {
	    AiCard aiCard = new AiCard();
	    aiCard.setC_id(rs.getString("c_id"));
	    aiCard.setC_type(rs.getString("c_type"));
	    aiCard.setC_passwd(rs.getString("c_passwd"));
	    aiCard.setC_prov(rs.getString("c_prov"));
	    aiCard.setC_city(rs.getString("c_city"));
	    aiCard.setC_score(rs.getInt("c_score"));
	    aiCard.setC_score_wc(rs.getInt("c_score_wc"));
	    aiCard.setC_xk(rs.getString("c_xk"));
	    aiCard.setCreate_tm(rs.getTimestamp("create_tm"));
	    aiCard.setActive_tm(rs.getTimestamp("active_tm"));
	    aiCard.setLast_login_tm(rs.getTimestamp("last_login_tm"));
	    aiCard.setLast_modified_tm(rs.getTimestamp("last_modified_tm"));
	    aiCard.setExpire_tm(rs.getTimestamp("expire_tm"));
	    aiCard.setRemark(rs.getString("remark"));
	    aiCard.setC_phone(rs.getString("c_phone"));
	    aiCard.setC_function(rs.getString("c_function"));
	    aiCard.setC_allowed_prov(rs.getString("c_allowed_prov"));
	    aiCard.setAgent_id(rs.getString("agent_id"));
	    aiCard.setStatus(rs.getInt("status"));
	    aiCard.setCnt_download(rs.getInt("cnt_download"));
	    return aiCard;
	}
	
	public AiCard findAiCardById(String c_id) {
		Connection conn = null;
		PreparedStatement pstmt = null;
		ResultSet rs = null;
		
        AiCard aiCard = null;
        try{
        	String SQL = "SELECT * FROM ai_card WHERE c_id = ?";
        	
        	conn =DatabaseUtils.getConnection();
        	pstmt = conn.prepareStatement(SQL);
			
            // 设置查询参数
            pstmt.setString(1, c_id);
            rs = pstmt.executeQuery();
            if (rs.next()) {
                aiCard = mapResultSetToAiCard(rs);
            }
        } catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, pstmt, rs);
		}

        return aiCard;
    }
	
	public AiCard findAiCardByIdAndPassword(String c_id, String c_passwd) {
		AiCard aiCard = findAiCardById(c_id);
		if(aiCard != null && aiCard.getC_passwd().equals(c_passwd)) {
			return aiCard;
		}
		return null;
    }
	
	public boolean updateAiCardFull(AiCard aiCard) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	        conn =DatabaseUtils.getConnection();
	        String SQL = "update ai_card set c_passwd=?, c_prov=?, c_city=?, " +
	                    "c_score=?, c_score_wc=?, c_xk=?, create_tm=?, active_tm=?, " +
	                    "last_login_tm=?, expire_tm=?, last_modified_tm=?, remark=?, c_phone=?, c_function=?, c_allowed_prov=?, " +
	                    "agent_id=?, status=?, cnt_download=? where c_id= ?";
	        ps = conn.prepareStatement(SQL);
	        int index = 1;
	        ps.setString(index++, aiCard.getC_passwd());
	        ps.setString(index++, aiCard.getC_prov());
	        ps.setString(index++, aiCard.getC_city());
	        ps.setInt(index++, aiCard.getC_score());
	        ps.setInt(index++, aiCard.getC_score_wc());
	        ps.setString(index++, aiCard.getC_xk());
	        ps.setTimestamp(index++, new Timestamp(aiCard.getCreate_tm().getTime()));
	        ps.setTimestamp(index++, aiCard.getActive_tm() == null ? null : new Timestamp(aiCard.getActive_tm().getTime()));
	        ps.setTimestamp(index++, aiCard.getLast_login_tm() == null ? null : new Timestamp(aiCard.getLast_login_tm().getTime()));
	        ps.setTimestamp(index++, aiCard.getExpire_tm() == null ? null : new Timestamp(aiCard.getExpire_tm().getTime()));
	        ps.setTimestamp(index++, aiCard.getLast_modified_tm() == null ? null : new Timestamp(aiCard.getLast_modified_tm().getTime()));
	        ps.setString(index++, aiCard.getRemark());
	        ps.setString(index++, aiCard.getC_phone());
	        ps.setString(index++, aiCard.getC_function());
	        ps.setString(index++, aiCard.getC_allowed_prov());
	        ps.setString(index++, aiCard.getAgent_id());
	        ps.setInt(index++, aiCard.getStatus());
	        ps.setInt(index++, aiCard.getCnt_download());
	        // 条件参数
	        ps.setString(index++, aiCard.getC_id());
	        
	        int affectedRows = ps.executeUpdate();
	        SQLLogUtils.printSQL(ps);
	        
	        return affectedRows > 0;
	    } catch (Exception ex) {
	        ex.printStackTrace();
	    } finally {
	        closeAllConnection(conn, ps, rs);
	    }
	    return false;
	}
	
	private AiTbFormMain setOriginalAiTbFormMain(ResultSet rs) throws SQLException{
		AiTbFormMain bean = new AiTbFormMain(); 
		bean.setId(rs.getInt("id"));
		bean.setJh_version(rs.getInt("jh_version"));
		bean.setBatch_id(rs.getString("batch_id"));
		bean.setOrder_id(rs.getString("order_id"));
		bean.setF_no(rs.getString("f_no"));
		bean.setScore_cj(rs.getInt("score_cj"));
		bean.setScore_wc(rs.getInt("score_wc"));
		bean.setScore_xk(rs.getString("score_xk"));
		bean.setChecker_id(rs.getString("last_checker_id"));
		bean.setChecker_name(rs.getString("last_checker_name"));
		bean.setChecker_remark(rs.getString("last_checker_remark"));
		bean.setSelected_zymc(rs.getString("selected_zymc"));
		bean.setCreate_tm(rs.getTimestamp("create_tm"));
		bean.setLast_update_tm(rs.getTimestamp("last_update_tm"));
		bean.setJoined_checker_cnt(rs.getInt("joined_checker_cnt"));
		bean.setNf(rs.getInt("nf"));
		bean.setStatus(rs.getInt("status"));
		bean.setF_type(rs.getInt("f_type"));
		bean.setPc(rs.getString("pc"));
		bean.setPc_code(rs.getString("pc_code"));
		bean.setForm_name(rs.getString("form_name"));
		bean.setToCheckerRemark(rs.getString("to_checker_remark"));
		return bean;
	} 
	
	public AiTbFormMain getFormMainByBatchId(String sfCode, String batchId) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM ai_tb_"+sfCode+"_form_maker_main x WHERE batch_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batchId);
			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === getFormMainByBatchId(): ", ps);
			
			while (rs.next()) {
				return setOriginalAiTbFormMain(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	//不同版本的都拿出來
	public List<AiTbFormMain> getAllUserFormMain(String sfCode, String c_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<AiTbFormMain> list = new ArrayList<>();
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM ai_tb_"+sfCode+"_form_maker_main x WHERE order_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, c_id);
			SQLLogUtils.printSQL(" === getFormMainByBatchId(): ", ps);
			rs = ps.executeQuery();
			while (rs.next()) {
				list.add(setOriginalAiTbFormMain(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public void updateMakerFormMainFno(int nf, String sfCode, String batch_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "update ai_tb_"+sfCode+"_form_maker_main set f_no = id where batch_id = ? and nf = ?";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			ps.setInt(2, nf);
			ps.executeUpdate();
			SQLLogUtils.printSQL(" === updateMakerFormMainFno(): ", ps);
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void deleteMakerFormMainAllExceptionCurrentJhVersion(int jh_version, int nf, String sfCode, String order_id, String pc, String pc_code) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "delete from ai_tb_"+sfCode+"_form_maker_main where jh_version <> ? and order_id = ? and pc = ? and pc_code = ? and nf = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			int index = 1;
			ps.setInt(index++, jh_version);
			ps.setString(index++, order_id);
			ps.setString(index++, pc);
			ps.setString(index++, pc_code);
			ps.setInt(index++, nf);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void swapMakerFormZyNoWithFormId(String sfCode, List<AiTbForm> updateList) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "update ai_tb_"+sfCode+"_form_maker set seq_no_zy = ? where id = ?";
			ps = conn.prepareStatement(SQL);
			for(AiTbForm form : updateList) {
				ps.setInt(1, form.getSeq_no_zy());
				ps.setInt(2, form.getId());
				ps.addBatch();
			}
			SQLLogUtils.printSQL(ps);
			ps.executeBatch();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void swapMakerFormYxNoWithBatchId(String sfCode, List<AiTbForm> updateList) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "update ai_tb_"+sfCode+"_form_maker set seq_no_yx = ? where id = ?";
			ps = conn.prepareStatement(SQL);
			for(AiTbForm form : updateList) {
				ps.setInt(1, form.getSeq_no_yx());
				ps.setInt(2, form.getId());
				ps.addBatch();
			}
			SQLLogUtils.printSQL(ps);
			ps.executeBatch();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public AiTbForm getMakerForm(String sfCode, int id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM ai_tb_"+sfCode+"_form_maker x WHERE id = ? ";
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, id);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				return setOriginalAiTbForm(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public List<AiTbForm> getMakerFormFor96(String sfCode, String batch_id_org, String yxdm, String yxmc, String zydm, String zymc, String zybz) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<AiTbForm> list = new ArrayList<>();
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM ai_tb_"+sfCode+"_form_maker x WHERE batch_id_org = ? and yxdm = ? and yxmc = ? and zydm = ? and zymc = ?";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			ps.setString(2, yxdm);
			ps.setString(3, yxmc);
			ps.setString(4, zydm);
			ps.setString(5, zymc);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				list.add(setOriginalAiTbForm(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<AiTbForm> getAllMakerFormFor96ByBatchId(String sfCode, String batch_id_org) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<AiTbForm> list = new ArrayList<>();
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM ai_tb_"+sfCode+"_form_maker x WHERE batch_id_org = ? ORDER BY seq_no_yx, seq_no_zy";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				list.add(setOriginalAiTbForm(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<AiTbForm> getMakerFormForYxAndZyz(String sfCode, String batch_id_org, String yxmc, String zydm, String zymc, String zyz) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<AiTbForm> list = new ArrayList<>();
		try {
			conn =DatabaseUtils.getConnection();
			String zyzSQL = Tools.isEmpty(zyz) ? "and zyz is null" : "and zyz = ?";
			String SQL = "SELECT * FROM ai_tb_"+sfCode+"_form_maker x WHERE batch_id_org = ? and yxmc = ? and zydm = ? and zymc = ? "+zyzSQL+" ORDER BY seq_no_yx, seq_no_zy";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			ps.setString(2, yxmc);
			ps.setString(3, zydm);
			ps.setString(4, zymc);
			if(!Tools.isEmpty(zyz)) {
				ps.setString(5, zyz);
			}
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				list.add(setOriginalAiTbForm(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<AiTbForm> getMakerFormBySeqYxNo(String sfCode, String batch_id_org, int seq_yx_no) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<AiTbForm> list = new ArrayList<>();
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM ai_tb_"+sfCode+"_form_maker x WHERE batch_id_org = ? and seq_no_yx = ? ORDER BY seq_no_yx, seq_no_zy";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			ps.setInt(2, seq_yx_no);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				list.add(setOriginalAiTbForm(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<AiTbForm> getMakerFormByZyz(String sfCode, String batch_id_org, String zyz) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<AiTbForm> list = new ArrayList<>();
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM ai_tb_"+sfCode+"_form_maker x WHERE batch_id_org = ? and zyz = ? ORDER BY seq_no_yx, seq_no_zy";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			ps.setString(2, zyz);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				list.add(setOriginalAiTbForm(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<AiTbForm> getAllMakerFormForYxAndZyzById(String sfCode, String batch_id_org) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<AiTbForm> list = new ArrayList<>();
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM ai_tb_"+sfCode+"_form_maker x WHERE batch_id_org = ? ORDER BY seq_no_yx, seq_no_zy";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id_org);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				list.add(setOriginalAiTbForm(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public List<AiTbForm> getAllMakerFormByBatchId(String sfCode, String batch_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<AiTbForm> list = new ArrayList<>();
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM ai_tb_"+sfCode+"_form_maker x WHERE batch_id = ? ORDER BY seq_no_yx, seq_no_zy";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			while (rs.next()) {
				list.add(setOriginalAiTbForm(rs));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return list;
	}
	
	public static AiTbForm setOriginalAiTbForm(ResultSet rs) throws SQLException {
		AiTbForm bean = new AiTbForm();
		bean.setBatch_id(rs.getString("batch_id"));
		bean.setId(rs.getInt("id"));
		bean.setBatch_id_org(rs.getString("batch_id_org"));
		bean.setSeq_no_yx(rs.getInt("seq_no_yx"));
		bean.setSeq_no_zy(rs.getInt("seq_no_zy"));
		bean.setYxbz(rs.getString("yxbz"));
		bean.setYxdm(rs.getString("yxdm"));
		bean.setYxmc(rs.getString("yxmc"));
		bean.setZybz(rs.getString("zybz"));
		bean.setZydm(rs.getString("zydm"));
		bean.setZymc(rs.getString("zymc"));
		bean.setZyz(rs.getString("zyz"));
		bean.setYxmc_org(rs.getString("yxmc_org"));
		bean.setZymc_org(rs.getString("zymc_org"));
		
		bean.setZdf_a(rs.getInt("zdf_a"));
		bean.setZdf_b(rs.getInt("zdf_b"));
		bean.setZdf_c(rs.getInt("zdf_c"));
		bean.setZdfwc_a(rs.getInt("zdfwc_a"));
		bean.setZdfwc_b(rs.getInt("zdfwc_b"));
		bean.setZdfwc_c(rs.getInt("zdfwc_c"));

		bean.setPjf_a(rs.getInt("pjf_a"));
		bean.setPjf_b(rs.getInt("pjf_b"));
		bean.setPjf_c(rs.getInt("pjf_c"));
		bean.setPjfwc_a(rs.getInt("pjfwc_a"));
		bean.setPjfwc_b(rs.getInt("pjfwc_b"));
		bean.setPjfwc_c(rs.getInt("pjfwc_c"));
		bean.setJhs(rs.getInt("jhs"));
		bean.setFee(rs.getString("fee"));
		
		bean.setZgf_a(rs.getInt("zgf_a"));
		bean.setZgf_b(rs.getInt("zgf_b"));
		bean.setZgf_c(rs.getInt("zgf_c"));
		bean.setZgfwc_a(rs.getInt("zgfwc_a"));
		bean.setZgfwc_b(rs.getInt("zgfwc_b"));
		bean.setZgfwc_c(rs.getInt("zgfwc_c"));
		
		return bean;
	}
	
	public AiTbFormMain getFormMainByOrderIdAndPc(int jh_version, int nf, String sfCode, String order_id, String pc, String pc_code) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM ai_tb_"+sfCode+"_form_maker_main x WHERE jh_version = ? and order_id = ? and pc = ? and pc_code = ? and nf = ?";
			ps = conn.prepareStatement(SQL);
			int index = 1;
			ps.setInt(index++, jh_version);
			ps.setString(index++, order_id);
			ps.setString(index++, pc);
			ps.setString(index++, pc_code);
			ps.setInt(index++, nf);
			SQLLogUtils.printSQL(" === getFormMainByOrderIdAndPc(): ", ps);
			rs = ps.executeQuery();
			
			while (rs.next()) {
				return setOriginalAiTbFormMain(rs);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return null;
	}
	
	public boolean insertBlockCount(String cid, String action, String spec) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "insert into zdks_block(FROM_ACTION, SPEC_VALUE, C_ID, CREATE_DT) values(?,?,?,NOW())";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, action);
			ps.setString(2, spec);
			ps.setString(3, cid);
			ps.executeUpdate();
			SQLLogUtils.printSQL(ps);
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
	
	public int checkBlockCount(String cid, String action) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "SELECT count(*) as ct FROM zdks_block WHERE C_ID = ? and FROM_ACTION = ? and DATE_FORMAT(CREATE_DT, '%Y%m%d%H%i') = DATE_FORMAT(now(), '%Y%m%d%H%i')";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.setString(2, action);
			SQLLogUtils.printSQL(ps);
			rs = ps.executeQuery();
			while (rs.next()) {
				return rs.getInt("ct");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return -1;
	}
	
	public void insertMakerFormMain(String sfCode, AiTbFormMain OriginalAiTbFormMain) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "insert into ai_tb_"+sfCode+"_form_maker_main(batch_id, score_cj, score_wc, score_xk, f_no, order_id, selected_zymc, pc, pc_code, nf, f_type, jh_version, status, create_tm) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,1,now())";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, OriginalAiTbFormMain.getBatch_id());
			ps.setInt(2, OriginalAiTbFormMain.getScore_cj());
			ps.setInt(3, OriginalAiTbFormMain.getScore_wc());
			ps.setString(4, OriginalAiTbFormMain.getScore_xk());
			ps.setString(5, OriginalAiTbFormMain.getF_no());
			ps.setString(6, OriginalAiTbFormMain.getOrder_id());
			ps.setString(7, OriginalAiTbFormMain.getSelected_zymc());
			ps.setString(8, OriginalAiTbFormMain.getPc());
			ps.setString(9, OriginalAiTbFormMain.getPc_code());
			ps.setInt(10, OriginalAiTbFormMain.getNf());
			ps.setInt(11, OriginalAiTbFormMain.getF_type());
			ps.setInt(12, OriginalAiTbFormMain.getJh_version());
			SQLLogUtils.printSQL(ps);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void deleteMakerForm(String batch_id, int seq_no_yx, String sfCode) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "delete from ai_tb_"+sfCode+"_form_maker where batch_id_org = ? and seq_no_yx = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			ps.setInt(2, seq_no_yx);
			SQLLogUtils.printSQL(ps);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void deleteMakerFormMain(String batch_id, String sfCode) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "delete from ai_tb_"+sfCode+"_form_maker_main where batch_id = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			SQLLogUtils.printSQL(ps);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void deleteMakerFormMainByUser(String c_id, String pc, String pc_code, String sfCode, int fType) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "delete from ai_tb_"+sfCode+"_form_maker_main where order_id = ? and pc = ? and pc_code = ? and f_type = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, c_id);
			ps.setString(2, pc);
			ps.setString(3, pc_code);
			ps.setInt(4, fType);
			SQLLogUtils.printSQL(ps);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	
	public void deleteMakerFormWithId(int form_id, String sfCode) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "delete from ai_tb_"+sfCode+"_form_maker where id = ?";
			ps = conn.prepareStatement(SQL);
			ps.setInt(1, form_id);
			SQLLogUtils.printSQL(ps);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void deleteMakerFormWithBatchId(String batch_id, String sfCode) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "delete from ai_tb_"+sfCode+"_form_maker where batch_id_org = ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			SQLLogUtils.printSQL(ps);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void increaseMakerFormSeqYxNoWithBatchId(String sfCode, String batch_id, int fromSeqYxNo) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "update ai_tb_"+sfCode+"_form_maker set seq_no_yx = seq_no_yx + 1 where batch_id_org = ? and seq_no_yx >= ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			ps.setInt(2, fromSeqYxNo);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void decreaseMakerFormSeqYxNoWithBatchId(String sfCode, String batch_id, int fromSeqYxNo) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "update ai_tb_"+sfCode+"_form_maker set seq_no_yx = seq_no_yx - 1 where batch_id_org = ? and seq_no_yx >= ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			ps.setInt(2, fromSeqYxNo);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void decreaseMakerFormSeqZyNoWithBatchIdAndSeqYxNo(String sfCode, String batch_id, int seq_no_yx, int fromSeqZyNo) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "update ai_tb_"+sfCode+"_form_maker set seq_no_zy = seq_no_zy - 1 where batch_id_org = ? and seq_no_yx = ? and seq_no_zy >= ?";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, batch_id);
			ps.setInt(2, seq_no_yx);
			ps.setInt(3, fromSeqZyNo);
			ps.executeUpdate();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void insertMakerForm(String sfCode, AiTbForm form) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "insert into ai_tb_"+sfCode+"_form_maker(batch_id, batch_id_org, seq_no_yx, yxmc, yxbz, yxdm, zyz, seq_no_zy, zymc, zybz, zydm, "
					+ "zdf_a, zdf_b, zdf_c, zdfwc_a, zdfwc_b, zdfwc_c, "
					+ "pjf_a, pjf_b, pjf_c, pjfwc_a, pjfwc_b, pjfwc_c,"
					+ " yxmc_org, zymc_org, jhs_a, jhs_b, jhs_c, "
					+ "zgf_a, zgf_b, zgf_c, zgfwc_a, zgfwc_b, zgfwc_c, "
					+ "jhs, fee) values(?,?,?,?,?,?,?,?,?,?,?,"
					+ "?,?,?,?,?,?,"
					+ "?,?,?,?,?,?,"
					+ "?,?,?,?,?,"
					+ "?,?,?,?,?,?,"
					+ "?,?)";
			//Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			
			int index = 1;
			
			ps.setString(index++, form.getBatch_id());
			ps.setString(index++, form.getBatch_id_org());
			ps.setInt(index++, form.getSeq_no_yx());
			ps.setString(index++, form.getYxmc());
			ps.setString(index++, form.getYxbz());
			ps.setString(index++, form.getYxdm());
			ps.setString(index++, form.getZyz());
			ps.setInt(index++, form.getSeq_no_zy());
			ps.setString(index++, form.getZymc());
			ps.setString(index++, form.getZybz());
			ps.setString(index++, form.getZydm());

			ps.setInt(index++, form.getZdf_a());
			ps.setInt(index++, form.getZdf_b());
			ps.setInt(index++, form.getZdf_c());
			
			ps.setInt(index++, form.getZdfwc_a());
			ps.setInt(index++, form.getZdfwc_b());
			ps.setInt(index++, form.getZdfwc_c());
			
			ps.setInt(index++, form.getPjf_a());
			ps.setInt(index++, form.getPjf_b());
			ps.setInt(index++, form.getPjf_c());
			
			ps.setInt(index++, form.getPjfwc_a());
			ps.setInt(index++, form.getPjfwc_b());
			ps.setInt(index++, form.getPjfwc_c());
			
			ps.setString(index++, form.getYxmc_org());
			ps.setString(index++, form.getZymc_org());
			
			ps.setInt(index++, form.getJhs_a());
			ps.setInt(index++, form.getJhs_b());
			ps.setInt(index++, form.getJhs_c());
			
			ps.setInt(index++, form.getZgf_a());
			ps.setInt(index++, form.getZgf_b());
			ps.setInt(index++, form.getZgf_c());
			
			ps.setInt(index++, form.getZgfwc_a());
			ps.setInt(index++, form.getZgfwc_b());
			ps.setInt(index++, form.getZgfwc_c());
			
			ps.setInt(index++, form.getJhs());
			ps.setString(index++, form.getFee());
			
			ps.executeUpdate();
			System.out.println(">>>>>>>>>>>>>");
			SQLLogUtils.printSQL("Check: ", ps);
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	public void insertMakerFormByBatch(String sfCode, List<AiTbForm> formList) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn =DatabaseUtils.getConnection();
			String SQL = "insert into ai_tb_"+sfCode+"_form_maker(batch_id, batch_id_org, seq_no_yx, yxmc, yxbz, yxdm, zyz, seq_no_zy, zymc, zybz, zydm, zdf_a, zdf_b, zdf_c, zdfwc_a, zdfwc_b, zdfwc_c, pjf_a, pjf_b, pjf_c, pjfwc_a, pjfwc_b, pjfwc_c, yxmc_org, zymc_org, jhs_a, jhs_b, jhs_c, zgf_a, zgf_b, zgf_c, zgfwc_a, zgfwc_b, zgfwc_c, jhs, fee) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
			//Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			
			for(AiTbForm form : formList) {
				int index = 1;
				ps.setString(index++, form.getBatch_id());
				ps.setString(index++, form.getBatch_id_org());
				ps.setInt(index++, form.getSeq_no_yx());
				ps.setString(index++, form.getYxmc());
				ps.setString(index++, form.getYxbz());
				ps.setString(index++, form.getYxdm());
				ps.setString(index++, form.getZyz());
				ps.setInt(index++, form.getSeq_no_zy());
				ps.setString(index++, form.getZymc());
				ps.setString(index++, form.getZybz());
				ps.setString(index++, form.getZydm());
	
				ps.setInt(index++, form.getZdf_a());
				ps.setInt(index++, form.getZdf_b());
				ps.setInt(index++, form.getZdf_c());
				
				ps.setInt(index++, form.getZdfwc_a());
				ps.setInt(index++, form.getZdfwc_b());
				ps.setInt(index++, form.getZdfwc_c());
				
				ps.setInt(index++, form.getPjf_a());
				ps.setInt(index++, form.getPjf_b());
				ps.setInt(index++, form.getPjf_c());
				
				ps.setInt(index++, form.getPjfwc_a());
				ps.setInt(index++, form.getPjfwc_b());
				ps.setInt(index++, form.getPjfwc_c());
				
				ps.setString(index++, form.getYxmc_org());
				ps.setString(index++, form.getZymc_org());
				
				ps.setInt(index++, form.getJhs_a());
				ps.setInt(index++, form.getJhs_b());
				ps.setInt(index++, form.getJhs_c());
				
				ps.setInt(index++, form.getZgf_a());
				ps.setInt(index++, form.getZgf_b());
				ps.setInt(index++, form.getZgf_c());
				
				ps.setInt(index++, form.getZgfwc_a());
				ps.setInt(index++, form.getZgfwc_b());
				ps.setInt(index++, form.getZgfwc_c());
				ps.setInt(index++, form.getJhs());
				ps.setString(index++, form.getFee());
				
				ps.addBatch();
			}
			ps.executeBatch();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
	}
	
	
	/**
	 * 
	 * 用户修改个人信息时删除志愿数据
	 * 
	 * @param sfCode
	 * @param c_id
	 * 
	 */
	public void deleteAllUserFormsByCId(String sfCode, String c_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		
		try {
			conn = DatabaseUtils.getConnection();
			conn.setAutoCommit(false); // 开启事务
			
			// 1. 查询所有用户的主志愿记录
			String mainSQL = "SELECT batch_id FROM ai_tb_" + sfCode + "_form_maker_main WHERE order_id = ?";
			ps = conn.prepareStatement(mainSQL);
			ps.setString(1, c_id);
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(ps);
			
			List<String> batchIds = new ArrayList<>();
			while (rs.next()) {
				batchIds.add(rs.getString("batch_id"));
			}
			
			// 2. 批量删除志愿详细记录 (ai_tb_xxx_form_maker)
			if (!batchIds.isEmpty()) {
				String deleteFormSQL = "DELETE FROM ai_tb_" + sfCode + "_form_maker WHERE batch_id_org IN (" + Tools.getSQLQueryin(new HashSet<>(batchIds)) + ")";
				ps = conn.prepareStatement(deleteFormSQL);
				int deletedFormCount = ps.executeUpdate();
				SQLLogUtils.printSQL(" === deleteAllUserFormsByCId form_maker batch delete (" + deletedFormCount + " records) ：", ps);
			}
			
			// 3. 删除主志愿记录 (ai_tb_xxx_form_maker_main)
			String deleteMainSQL = "DELETE FROM ai_tb_" + sfCode + "_form_maker_main WHERE order_id = ?";
			ps = conn.prepareStatement(deleteMainSQL);
			ps.setString(1, c_id);
			int deletedMainCount = ps.executeUpdate();
			
			SQLLogUtils.printSQL(" === deleteAllUserFormsByCId maker_main (" + deletedMainCount + " records) ：", ps);
			
			conn.commit(); // 提交事务
			Tools.println("Successfully deleted all forms for user: " + c_id + " (Main: " + deletedMainCount + ", Detail: " + (batchIds.isEmpty() ? 0 : "batch") + ")");
			
		} catch (Exception ex) {
			try {
				if (conn != null) {
					conn.rollback(); // 回滚事务
				}
			} catch (SQLException rollbackEx) {
				rollbackEx.printStackTrace();
			}
			ex.printStackTrace();
		} finally {
			try {
				if (conn != null) {
					conn.setAutoCommit(true); // 恢复自动提交
				}
			} catch (SQLException autoCommitEx) {
				autoCommitEx.printStackTrace();
			}
			closeAllConnection(conn, ps, rs);
		}
	}
	
	private static void closeAllConnection(Connection conn, PreparedStatement ps, ResultSet rs) {
//		try {
//			if (rs != null)
//				rs.close();
//		} catch (Exception ex) {
//			ex.printStackTrace();
//		}
//		try {
//			if (ps != null)
//				ps.close();
//		} catch (Exception ex) {
//			ex.printStackTrace();
//		}
//		try {
//			if (conn != null)
//				conn.close();
//		} catch (Exception ex) {
//			ex.printStackTrace();
//		}
//		rs = null;
//		ps = null;
//		conn = null;
		
		DatabaseUtils.closeAllResources(rs, ps, conn);
	}
}
