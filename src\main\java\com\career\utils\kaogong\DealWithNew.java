package com.career.utils.kaogong;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.alibaba.fastjson2.JSONObject;
import com.career.utils.Tools;
import com.career.utils.zsky.*;

public class DealWithNew {

	public static void main(String args[]) throws Exception {
		deal();
	}
	
	public static void deal() throws Exception {
		StringBuffer SQL = new StringBuffer();
		String fileName = null;
		int index = 0;
		for (int i = 0; i <= 999; i++) {
			try {
				
				File file = new File("E://offcn//" + i + "//");
				File[] fileList = file.listFiles();
				
				for(File xx : fileList) {
					fileName = xx.getName();
					
					int offcnID = Tools.getInt(fileName.substring("runOFFCN_".length(), fileName.length() - 4));
					if(offcnID <= 1281438) {
						continue;
					}
						
					BufferedReader bw = new BufferedReader(new FileReader(xx));

					String str = null;
					boolean start = false;
					
					String title = null, from = null, titleORG = null, url = null, time = null;
					
					while ((str = bw.readLine()) != null) {
						if(str.indexOf("<h1 class=\"tit\">") != -1){
							
							
							if(str.indexOf("</h1>") == -1) {
								title = str.substring(str.indexOf("<h1 class=\"tit\">") + 16);
								str = bw.readLine();
								if(str.indexOf("</h1>") == -1) {
									title += str.trim();
									str = bw.readLine();
								}
								if(str.indexOf("</h1>") == -1) {
									title += str.trim();
									str = bw.readLine();
								}
								if(str.indexOf("</h1>") == -1) {
									title += str.trim();
									str = bw.readLine();
								}
								if(str.indexOf("</h1>") == -1) {
									title += str.trim();
									str = bw.readLine();
								}
								if(str.indexOf("</h1>") == -1) {
									title += str.trim();
									str = bw.readLine();
								}
								
								title += str.substring(0, str.indexOf("</h1>"));
							}else {
								title = str.substring(str.indexOf("<h1 class=\"tit\">") + 16, str.indexOf("</h1>"));
							}
							
							
							while((str = bw.readLine()) != null) {
								if(str.indexOf("来源：") != -1){
									from = str.substring(str.indexOf("来源：")+3, str.indexOf("&nbsp;"));
									time = str.substring(str.indexOf("发布时间：") + 5);
									break;
								}
							}
							
							start = true;
						}
						
						if(start) {
							
							if(str.indexOf("原标题:") != -1) {
								if(str.indexOf("</p>") == -1) {
									titleORG = str.substring(str.indexOf("原标题:") + 4);
									str = bw.readLine();
									if(str.indexOf("</p>") == -1) {
										titleORG += str.trim();
										str = bw.readLine();
									}
									if(str.indexOf("</p>") == -1) {
										titleORG += str.trim();
										str = bw.readLine();
									}
									if(str.indexOf("</p>") == -1) {
										titleORG += str.trim();
										str = bw.readLine();
									}
									if(str.indexOf("</p>") == -1) {
										titleORG += str.trim();
										str = bw.readLine();
									}
									if(str.indexOf("</p>") == -1) {
										titleORG += str.trim();
										str = bw.readLine();
									}
									if(str.indexOf("</p>") == -1) {
										titleORG += str.trim();
										str = bw.readLine();
									}
									
									titleORG += str.substring(0, str.indexOf("</p>"));
								}else {
									titleORG = str.substring(str.indexOf("原标题:") + 4, str.indexOf("</p>"));
								}
							}
							
							if(str.indexOf("文章来源:") != -1) {
								url = str.substring(str.indexOf("文章来源:") + 5, str.indexOf("</p>"));
								
								String urldomain = url.substring(10).indexOf("/") == -1 ? url : url.substring(0,url.substring(10).indexOf("/") + 10);
								
								SQL.append("insert into career_ext_url(offcn_id,url_from, title, title_org, url,url_domain, publish_time, create_time) values('"+offcnID+"','"+from+"','"+title+"','"+titleORG+"','"+url+"','"+urldomain+"','"+time+"',now()); \r\n");
								
								//SQL.append("update career_ext_url SET url_from = '"+from+"' where  offcn_id = '"+fileName.substring("runOFFCN_".length(), fileName.length() - 4)+"'; \r\n");
							}
						}
						
						
						
					}
					
					/**
					ResultMajorHistoryBean bean = (ResultMajorHistoryBean) JSONObject.parseObject(sb.toString(), ResultMajorHistoryBean.class);
					MajorHistory info = bean.getData();
					if (info == null) {
						continue;
					}
					
					MajorHistoryItem[] elements = info.getItem();
					if(elements != null && elements.length > 0) {
						for(MajorHistoryItem item : elements) {
							SQL.append(item.generateSQL(i)+"\r\n");
						}
					}
					*/
					
					
				}
			} catch (Exception ex) {
				ex.printStackTrace();
				System.out.println("ERROR:"+fileName);
			}

		}
		

		writeTempFile(new File("E://offcn//offcn_urls_update.txt"), SQL);
	}
	
	public static void dealWithMajorAdjust() throws Exception {
		StringBuffer SQL = new StringBuffer();
		
		for (int i = 2; i <= 1600; i++) {
			try {
				BufferedReader bw = new BufferedReader(
						new FileReader(new File("E://kaoyan//" + i + "//runn2_4_"+i+"adjust_data.txt")));
				StringBuffer sb = new StringBuffer();
				String str = null;
				while ((str = bw.readLine()) != null) {
					sb.append(str);
				}
				ResultAdjustBean bean = (ResultAdjustBean) JSONObject.parseObject(sb.toString(), ResultAdjustBean.class);
				MajorAdjust info = bean.getData();
				if (info == null) {
					continue;
				}
				
				MajorAdjustItem[] elements = info.getData();
				if(elements != null && elements.length > 0) {
					for(MajorAdjustItem item : elements) {
						SQL.append(item.generateSQL()+"\r\n");
					}
				}
				
				
			} catch (Exception ex) {
			}

		}

		writeTempFile(new File("E://kaoyan//KYSchoolMajorAdjustSQL.txt"), SQL);
	}

	private static void writeTempFile(File file, StringBuffer sb) {
		try {
			BufferedWriter bw = new BufferedWriter(new FileWriter(file));
			bw.write(sb.toString());
			bw.flush();
			bw.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	private static String removeTag(String html) {

		// 定义要匹配的正则表达式模式
		Pattern pattern = Pattern.compile("<[^>]+>", Pattern.CASE_INSENSITIVE);

		// 创建 Matcher 对象并进行匹配操作
		Matcher matcher = pattern.matcher(html);

		// 将匹配到的 HTML 标签替换为空字符串
		String result = matcher.replaceAll("").replaceAll("&nbsp;", "");

		return result.trim();
	}

}
