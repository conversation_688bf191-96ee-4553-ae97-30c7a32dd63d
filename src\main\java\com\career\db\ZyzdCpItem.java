package com.career.db;

import com.career.utils.BaseBean;

public class ZyzdCpItem extends BaseBean{
	
	public static void main(String args[]) {
		printBeanProperties(new ZyzdCpItem());
	}
	private int id;
	private String m_id;
	private int item_no;
	private String item_name;
	private int group_no;
	private String group_name;
	private int group_sub_no;
	private String group_sub_name;
	private int item_type;
	private String item_option_a;
	private String item_option_b;
	private String item_option_c;
	private String item_option_d;
	private String item_option_e;
	private float item_score_a;
	private float item_score_b;
	private float item_score_c;
	private float item_score_d;
	private float item_score_e;
	
	
	public int getGroup_sub_no() {
		return group_sub_no;
	}
	public void setGroup_sub_no(int group_sub_no) {
		this.group_sub_no = group_sub_no;
	}
	public String getGroup_sub_name() {
		return group_sub_name;
	}
	public void setGroup_sub_name(String group_sub_name) {
		this.group_sub_name = group_sub_name;
	}
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getM_id() {
		return m_id;
	}
	public void setM_id(String m_id) {
		this.m_id = m_id;
	}
	public int getItem_no() {
		return item_no;
	}
	public void setItem_no(int item_no) {
		this.item_no = item_no;
	}
	public String getItem_name() {
		return item_name;
	}
	public void setItem_name(String item_name) {
		this.item_name = item_name;
	}
	public int getGroup_no() {
		return group_no;
	}
	public void setGroup_no(int group_no) {
		this.group_no = group_no;
	}
	public String getGroup_name() {
		return group_name;
	}
	public void setGroup_name(String group_name) {
		this.group_name = group_name;
	}
	public int getItem_type() {
		return item_type;
	}
	public void setItem_type(int item_type) {
		this.item_type = item_type;
	}
	public String getItem_option_a() {
		return item_option_a;
	}
	public void setItem_option_a(String item_option_a) {
		this.item_option_a = item_option_a;
	}
	public String getItem_option_b() {
		return item_option_b;
	}
	public void setItem_option_b(String item_option_b) {
		this.item_option_b = item_option_b;
	}
	public String getItem_option_c() {
		return item_option_c;
	}
	public void setItem_option_c(String item_option_c) {
		this.item_option_c = item_option_c;
	}
	public String getItem_option_d() {
		return item_option_d;
	}
	public void setItem_option_d(String item_option_d) {
		this.item_option_d = item_option_d;
	}
	public String getItem_option_e() {
		return item_option_e;
	}
	public void setItem_option_e(String item_option_e) {
		this.item_option_e = item_option_e;
	}
	public float getItem_score_a() {
		return item_score_a;
	}
	public void setItem_score_a(float item_score_a) {
		this.item_score_a = item_score_a;
	}
	public float getItem_score_b() {
		return item_score_b;
	}
	public void setItem_score_b(float item_score_b) {
		this.item_score_b = item_score_b;
	}
	public float getItem_score_c() {
		return item_score_c;
	}
	public void setItem_score_c(float item_score_c) {
		this.item_score_c = item_score_c;
	}
	public float getItem_score_d() {
		return item_score_d;
	}
	public void setItem_score_d(float item_score_d) {
		this.item_score_d = item_score_d;
	}
	public float getItem_score_e() {
		return item_score_e;
	}
	public void setItem_score_e(float item_score_e) {
		this.item_score_e = item_score_e;
	}
	
	
	
}
