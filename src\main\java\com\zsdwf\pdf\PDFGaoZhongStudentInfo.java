package com.zsdwf.pdf;

import java.util.HashSet;
import java.util.List;

public class PDFGaoZhongStudentInfo {

	private String xk;
	private String provinceName;
	private String provinceCode;
	private int wc_min;
	private int wc_max;
	private HashSet<String> major_catg_list;
	private String score_math;
	private String score_eng;
	private int score_all;
	private int score_wc;
	private HashSet<String> org_major_catg_list;
	private String lx_budget;
	
	public String getXk() {
		return xk;
	}
	public void setXk(String xk) {
		this.xk = xk;
	}

	public String getProvinceName() {
		return provinceName;
	}
	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}
	public String getProvinceCode() {
		return provinceCode;
	}
	public void setProvinceCode(String provinceCode) {
		this.provinceCode = provinceCode;
	}
	public String getScore_math() {
		return score_math;
	}
	public void setScore_math(String score_math) {
		this.score_math = score_math;
	}
	public String getScore_eng() {
		return score_eng;
	}
	public void setScore_eng(String score_eng) {
		this.score_eng = score_eng;
	}
	public int getScore_all() {
		return score_all;
	}
	public void setScore_all(int score_all) {
		this.score_all = score_all;
	}
	public int getScore_wc() {
		return score_wc;
	}
	public void setScore_wc(int score_wc) {
		this.score_wc = score_wc;
	}
	public HashSet<String> getOrg_major_catg_list() {
		return org_major_catg_list;
	}
	public void setOrg_major_catg_list(HashSet<String> org_major_catg_list) {
		this.org_major_catg_list = org_major_catg_list;
	}
	public String getLx_budget() {
		return lx_budget;
	}
	public void setLx_budget(String lx_budget) {
		this.lx_budget = lx_budget;
	}
	public int getWc_min() {
		return wc_min;
	}
	public void setWc_min(int wc_min) {
		this.wc_min = wc_min;
	}
	public int getWc_max() {
		return wc_max;
	}
	public void setWc_max(int wc_max) {
		this.wc_max = wc_max;
	}
	public HashSet<String> getMajor_catg_list() {
		return major_catg_list;
	}
	public void setMajor_catg_list(HashSet<String> major_catg_list) {
		this.major_catg_list = major_catg_list;
	}
	
	
	
	
}
