<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ page import="com.career.utils.*,com.career.db.*,java.util.*"%>

<%


ZyzdJDBC jdbc = new ZyzdJDBC();

int selected_ranking = Tools.getInt(request.getParameter("selected_ranking"));
String selected_ranking_yxmc = Tools.trim(request.getParameter("selected_ranking_yxmc"));
String selected_ranking_zymc = Tools.trim(request.getParameter("selected_ranking_zymc"));

List<ZyzdMajorRanking> rankingRKList = jdbc.getRankingByMajorAndRangeRuanKe(selected_ranking_zymc, selected_ranking);


%>

<div class="card-body" style="padding:0px;">
  <div class="table-responsive">
      <table class="table text-nowrap table-bordered border-primary">
          <thead>
              <tr style="background-color:#cfe2ff"> 
                  <th scope="col">院校名称</th>
	              <th scope="col">排名</th>
              </tr>
          </thead>
          <tbody>
          		<%
				for(int i=0;i<rankingRKList.size();i++){ 
					ZyzdMajorRanking ranking = rankingRKList.get(i); 
				%>
              <tr <%=ranking.getYxmc().equals(selected_ranking_yxmc) ? "style='background-color:#B0C4DE'" : "" %>>
                  <th scope="row">
                      <%=Tools.view(ranking.getYxmc()) %> 
                      
                  </th>
                  <td>
                  	  <%=ranking.getRanking() %>
                  </td>
                  
              </tr>
              <%} %>
            </tbody>
        </table>
    </div>
</div>



