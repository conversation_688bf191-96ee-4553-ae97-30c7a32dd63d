package com.zsdwf.db;

import java.util.Date;

public class YGCardBean {
	private String id;
	private String passwd;
	private String phone;
	private String prov;
	private String xk;
	private int wc;
	private int cnt;
	private int used;
	private Date create;
	private Date update;
	private Date lastQuery;
	private String remark;
	private String admin;
	private String openID;
	private int status;
	

	private int cardACnt;
	private int cardBCnt;
	private int cardCCnt;
	private int cardDCnt;
	private int cardECnt;
	private int cardFCnt;
	private int cardGCnt;
	
	
	public int getCardFCnt() {
		return cardFCnt;
	}

	public void setCardFCnt(int cardFCnt) {
		this.cardFCnt = cardFCnt;
	}

	public int getCardGCnt() {
		return cardGCnt;
	}

	public void setCardGCnt(int cardGCnt) {
		this.cardGCnt = cardGCnt;
	}

	public int getCardACnt() {
		return cardACnt;
	}

	public void setCardACnt(int cardACnt) {
		this.cardACnt = cardACnt;
	}

	public int getCardBCnt() {
		return cardBCnt;
	}

	public void setCardBCnt(int cardBCnt) {
		this.cardBCnt = cardBCnt;
	}

	public int getCardCCnt() {
		return cardCCnt;
	}

	public void setCardCCnt(int cardCCnt) {
		this.cardCCnt = cardCCnt;
	}

	public int getCardDCnt() {
		return cardDCnt;
	}

	public void setCardDCnt(int cardDCnt) {
		this.cardDCnt = cardDCnt;
	}

	public int getCardECnt() {
		return cardECnt;
	}

	public void setCardECnt(int cardECnt) {
		this.cardECnt = cardECnt;
	}

	public String getOpenID() {
		return openID;
	}

	public void setOpenID(String openID) {
		this.openID = openID;
	}

	public String getAdmin() {
		return admin;
	}

	public void setAdmin(String admin) {
		this.admin = admin;
	}

	public Date getUpdate() {
		return update;
	}

	public void setUpdate(Date update) {
		this.update = update;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getPasswd() {
		return passwd;
	}

	public void setPasswd(String passwd) {
		this.passwd = passwd;
	}

	public String getProv() {
		return prov;
	}

	public void setProv(String prov) {
		this.prov = prov;
	}

	public String getXk() {
		return xk;
	}

	public void setXk(String xk) {
		this.xk = xk;
	}

	public int getWc() {
		return wc;
	}

	public void setWc(int wc) {
		this.wc = wc;
	}

	public int getCnt() {
		return cnt;
	}

	public void setCnt(int cnt) {
		this.cnt = cnt;
	}

	public int getUsed() {
		return used;
	}

	public void setUsed(int used) {
		this.used = used;
	}

	public Date getCreate() {
		return create;
	}

	public void setCreate(Date create) {
		this.create = create;
	}

	public Date getLastQuery() {
		return lastQuery;
	}

	public void setLastQuery(Date lastQuery) {
		this.lastQuery = lastQuery;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

}
