package com.career.db;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import com.career.utils.Tools;

public class DataPatchDBTools {
	static String URL = JDBCConstants.URL;
	static String USER = JDBCConstants.USER;
	static String PASSWD = JDBCConstants.PASSWD;
  
  public static void main(String args[]) {
	  JDBC jdbc = new JDBC();
	  DataPatchDBTools tool = new DataPatchDBTools();
	  List<YXDWFBean> list = new ArrayList<>();
	  list = jdbc.getYXDWFBean();
	  for(int i=0;i<list.size();i++) {
		  YXDWFBean bean = list.get(i);
		  int dwf21 = bean.getDwf21() < 5 ? -1 : bean.getDwf21();
		  int dwf22 = bean.getDwf22() < 5 ? -1 : bean.getDwf22();
		  int dwf23 = bean.getDwf23() < 5 ? -1 : bean.getDwf23();
		  PredictDwfBean be = tool.yc2023Dwf(dwf21,dwf22,dwf23, String.valueOf(bean.getId()));
		  bean.setDwf24(be.getResultMin());
		  jdbc.updateYXDWFBean(bean.getId(), be.getResultMin(), be.getResultMax());
	  }
  }
  
  public static void generateCard() {
    for (int i = 5001; i <= 5030; i++) {
      String seq = "000000" + i;
      String rd = String.valueOf(Math.random());
      seq = seq.substring(seq.length() - 4);
      String prefixSEQ = seq.substring(0, 2);
      String subfixSEQ = seq.substring(2);
      String passwd = rd.substring(2, 6);
      if(passwd.indexOf("0") != -1) {
    	  passwd = "8" + passwd;
    	  passwd  = passwd.substring(0, 4);
    	  if(passwd.startsWith("0")) {
    		  passwd  = passwd.substring(1, 4) + "5";
    	  }
      }
      String cardid = "G" + prefixSEQ + rd.substring(7, 9) + subfixSEQ;
      
      String rd2 = String.valueOf(Math.random());
      cardid = cardid.replaceAll("4", rd2.substring(3, 4));
      passwd = passwd.replaceAll("4", "8");
      
      String rd3 = String.valueOf(Math.random());
      cardid = cardid.replaceAll("0", rd3.substring(3, 4));
      passwd = passwd.replaceAll("0", "5");
      
      //System.out.println("insert into base_card_org(C_ID,C_PASSWD,C_CREATE,C_STATUS,C_TYPE_ORG) VALUES('" + cardid + "','" + passwd + "',NOW(),2, 2);");
      
      System.out.println("insert into base_card_org(C_ID,C_PASSWD,C_CREATE,C_STATUS,C_TYPE_ORG, C_AGENT) VALUES('" + cardid + "','" + passwd + "',NOW(),2, 2, 'B9113');");
      
      //System.out.println("INSERT INTO `base_card` (`C_ID`, `C_PASSWD`, `C_PROV`, `C_SCORE`, `C_XK`, `C_CREATE`, `C_ACTIVE`, `C_LAST_LOGIN`, `C_YEAR`, `C_STATUS`, `C_REMARK`, `C_PHONE`, `C_ORDER_ID`, `C_MD`, `C_OPEN_ID`, `C_ADMIN`, `C_DESC`) VALUES ('" + cardid + "','" + passwd + "',NULL, 0, NULL, NOW(), NULL, NULL, 0, 1, '2024 GHSNH', NULL, NULL, NULL, NULL, NULL, NULL);");
    } 
  }
  
  
  
  static HashMap<String, String> hss = new HashMap<>();
	static {
		hss.put("文科", "WLRKQG");
		hss.put("历史", "WLRKQG");
		hss.put("理科", "14387D");
		hss.put("物理", "14387D");
	}
	
	
	
	
	
	public static void addColumnYXMCORG() {
	    Set<String> sets = JDBC.HM_PROVINCE.keySet();
	    Iterator<String> it = null;
	    StringBuffer sb = new StringBuffer();
	    it = sets.iterator();
	    while (it.hasNext()) {
	      String key = it.next();
	      String value = JDBC.HM_PROVINCE.get(key);
	      String value2 = JDBC.HM_PROVINCE_CODE_NAME.get(key);
	
	      
	      
	      for(int i=2023;i<=2023;i++) {
	    	  
	    	  if(value.startsWith("S5")) {
	    		  continue;
		      }
	    	  

	    	  sb.append("ALTER TABLE `"+value+"`\r\n"
	    	  		+ "	ADD COLUMN `zdf23` VARCHAR(255) NULL DEFAULT NULL AFTER `zdf22`,\r\n"
	    	  		+ "	ADD COLUMN `zdfwc23` VARCHAR(255) NULL DEFAULT NULL AFTER `zdfwc22`;\r\n");
	      }
	      
	      
	      
	      
	    } 
	    writeTempFile(new File("E://XXXaddColumnYXMCORG819.txt"), sb);
	  }

  public static void checkAllMajorData() {
	    Set<String> sets = JDBC.HM_PROVINCE.keySet();
	    Iterator<String> it = null;
	    StringBuffer sb = new StringBuffer();
	    it = sets.iterator();
	    while (it.hasNext()) {
	      String key = it.next();
	      String value = JDBC.HM_PROVINCE.get(key);
	      String value2 = JDBC.HM_PROVINCE_CODE_NAME.get(key);

	      for(int i=2023;i<=2023;i++) {
	    	  sb.append("update " + value + "_"+i+" x set xk_code = '14387D' where x.xk in ('理科','物理');\r\n");
	    	  sb.append("update " + value + "_"+i+" x set xk_code = 'WLRKQG' where x.xk in ('文科','历史');\r\n");
	    	  sb.append("update " + value + "_"+i+" x set xk_code = '123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ' where x.xk in ('综合');\r\n");
	    	  
	    	  sb.append("update " + value + "_"+i+"x x set xk_code = '14387D' where x.xk in ('理科','物理');\r\n");
	    	  sb.append("update " + value + "_"+i+"x x set xk_code = 'WLRKQG' where x.xk in ('文科','历史');\r\n");
	    	  sb.append("update " + value + "_"+i+"x x set xk_code = '123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ' where x.xk in ('综合');\r\n");
	      }
	    } 
	    writeTempFile(new File("E://checkAllMajorData222sqlyy.sql"), sb);
	  }
  
  public static void insertAllMajorDataToOneTable() {
    Set<String> sets = JDBC.HM_PROVINCE.keySet();
    Iterator<String> it = null;
    StringBuffer sb = new StringBuffer();
    it = sets.iterator();
    while (it.hasNext()) {
      String key = it.next();
      String value = JDBC.HM_PROVINCE.get(key);
      String value2 = JDBC.HM_PROVINCE_CODE_NAME.get(key);

      
      String str2020 = "INSERT INTO s_data_major2(`sf` ,`nf` ,`pc` ,`yxdm` ,`yxmc` ,`zyz` ,`zydm` ,`xk` ,`zx` ,`zymc` ,`zybz` ,`zdf` ,`zdfwc` ,`pjf` ,`pjfwc` ,`lqrs` ,`zgf` ) SELECT '" + 
        value2 + "', `nf` ,`pc` ,`yxdm` ,`yxmc` ,`zyz` ,`zydm` ,`xk` ,`zx` ,`zymc` ,`zybz` ,`zdf` ,`zdfwc` ,`pjf` ,`pjfwc` ,`lqrs` ,`zgf` FROM " + value + "_2020;\r\n";
      String str2021 = "INSERT INTO s_data_major2(`sf` ,`nf` ,`pc` ,`yxdm` ,`yxmc` ,`zyz` ,`zydm` ,`xk` ,`zx` ,`zymc` ,`zybz` ,`zdf` ,`zdfwc` ,`pjf` ,`pjfwc` ,`lqrs` ,`zgf` ) SELECT '" + 
        value2 + "', `nf` ,`pc` ,`yxdm` ,`yxmc` ,`zyz` ,`zydm` ,`xk` ,`zx` ,`zymc` ,`zybz` ,`zdf` ,`zdfwc` ,`pjf` ,`pjfwc` ,`lqrs` ,`zgf` FROM " + value + "_2021;\r\n";
      String str2022 = "INSERT INTO s_data_major2(`sf` ,`nf` ,`pc` ,`yxdm` ,`yxmc` ,`zyz` ,`zydm` ,`xk` ,`zx` ,`zymc` ,`zybz` ,`zdf` ,`zdfwc` ,`pjf` ,`pjfwc` ,`lqrs` ,`zgf` ) SELECT '" + 
        value2 + "', `nf` ,`pc` ,`yxdm` ,`yxmc` ,`zyz` ,`zydm` ,`xk` ,`zx` ,`zymc` ,`zybz` ,`zdf` ,`zdfwc` ,`pjf` ,`pjfwc` ,`lqrs` ,`zgf` FROM " + value + "_2022;\r\n";
      sb.append(str2020);
      sb.append(str2021);
      sb.append(str2022);
    } 
    writeTempFile(new File("E://sqlyy.sql"), sb);
  }
  
  public static void insertAllUniversityDataToOneTable() {
    Set<String> sets = JDBC.HM_PROVINCE.keySet();
    Iterator<String> it = null;
    StringBuffer sb = new StringBuffer();
    it = sets.iterator();
    while (it.hasNext()) {
      String key = it.next();
      String value = JDBC.HM_PROVINCE.get(key);
      String value2 = JDBC.HM_PROVINCE_CODE_NAME.get(key);
      String str = "insert into data_university_all(`sf`,`yxdm`,`yxmc`,`pc`,`xk`,`zx`,`zyz`,`zdf20`,`zdf21`,`zdf22`,`zdfwc20`,`zdfwc21`,`zdfwc22`,`yxbz`) SELECT '" + 
        value2 + "',`yxdm`,`yxmc`,`pc`,`xk`,`zx`,`zyz`,`zdf20`,`zdf21`,`zdf22`,`zdfwc20`,`zdfwc21`,`zdfwc22`,`yxbz` FROM " + value + ";\r\n";
      sb.append(str);
    } 
    writeTempFile(new File("E://sqlyy2.txt"), sb);
  }
  
  public static void checkAllTableColumnComplete() {
    Set<String> sets = JDBC.HM_PROVINCE.keySet();
    Iterator<String> it = null;
    for (int k = 2020; k <= 2022; k++) {
      it = sets.iterator();
      while (it.hasNext()) {
        String key = it.next();
        String value = JDBC.HM_PROVINCE.get(key);
        System.out.println("INSERT INTO sss_data_major(SF,NF,PC,YXDM,YXMC,ZYZ,ZYDM,ZYMC,XK,ZYBZ,ZDF,ZDFWC,ZGF,LQRS) SELECT '"+JDBC.HM_PROVINCE_CODE_NAME.get(key)+"',NF,PC,YXDM,YXMC,ZYZ,ZYDM,ZYMC,XK,ZYBZ,ZDF,ZDFWC,ZGF,LQRS FROM " + value + "_" + k+";");
      } 
    } 
  }
  
  public static void deleteAllUnuseData() {
    Set<String> sets = JDBC.HM_PROVINCE.keySet();
    Iterator<String> it = null;
    for (int k = 2020; k <= 2022; k++) {
      it = sets.iterator();
      while (it.hasNext()) {
        String key = it.next();
        String value = JDBC.HM_PROVINCE.get(key);
        System.out.println("DELETE from " + value + "_" + k + " WHERE yxmc = '学校名'");
      } 
    } 
  }
  
  public static void getAllYxMC() {
	    Set<String> sets = JDBC.HM_PROVINCE.keySet();
	    Iterator<String> it = null;
	    StringBuffer sb = new StringBuffer();
	    it = sets.iterator();
	    while (it.hasNext()) {
	      String key = it.next();
	      String value = JDBC.HM_PROVINCE.get(key);
	      sb.append("insert into sys_yx_all(yxmc, sf) select yxmc, sf from " + value + "_2020;\r\n");
	      sb.append("insert into sys_yx_all(yxmc, sf) select yxmc, sf from " + value + "_2021;\r\n");
	      sb.append("insert into sys_yx_all(yxmc, sf) select yxmc, sf from " + value + "_2022;\r\n");
	    } 
	    try {
	      BufferedWriter bw = new BufferedWriter(new FileWriter(new File("E://sqlxkko.txt")));
	      bw.write(sb.toString());
	      bw.flush();
	      bw.close();
	    } catch (IOException e) {
	      e.printStackTrace();
	    } 
	  }
  
  public static void getAllYxbz() {
    Set<String> sets = JDBC.HM_PROVINCE.keySet();
    Iterator<String> it = null;
    StringBuffer sb = new StringBuffer();
    sb.append("SELECT * FROM (\r\n");
    it = sets.iterator();
    while (it.hasNext()) {
      String key = it.next();
      String value = JDBC.HM_PROVINCE.get(key);
      String value2 = JDBC.HM_PROVINCE_CODE_NAME.get(key);
      sb.append("SELECT '" + 
          value2 + "' as sf, '" + value + "_2020x' AS nf,x.pc, x.xk, x.yxbz, COUNT(*) FROM " + value + "_2020x x GROUP BY x.pc, x.xk, x.yxbz\r\n" + 
          "UNION ALL \r\n" + 
          "SELECT '" + value2 + "' as sf, '" + value + "_2021x' AS nf,x.pc, x.xk, x.yxbz, COUNT(*) FROM " + value + "_2021x x GROUP BY x.pc, x.xk, x.yxbz\r\n" + 
          "UNION ALL \r\n" + 
          "SELECT '" + value2 + "' as sf, '" + value + "_2022x' AS nf,x.pc, x.xk, x.yxbz, COUNT(*) FROM " + value + "_2022x x GROUP BY x.pc, x.xk, x.yxbz\r\n" + 
          "UNION ALL \r\n");
    } 
    sb.append(")AS temp ORDER BY temp.sf,temp.nf, x.pc, temp.xk, temp.yxbz;\r\n");
    try {
      BufferedWriter bw = new BufferedWriter(new FileWriter(new File("E://sqlx.txt")));
      bw.write(sb.toString());
      bw.flush();
      bw.close();
    } catch (IOException e) {
      e.printStackTrace();
    } 
  }
  
  public static void copyFile() {
    String fileFrom = "C:\\Users\\<USER>\\Desktop\\\\\\";
    File file = new File(fileFrom);
    String[] str = file.list();
    byte b;
    int i;
    String[] arrayOfString1;
    for (i = (arrayOfString1 = str).length, b = 0; b < i; ) {
      String s = arrayOfString1[b];
      File file2 = new File(fileFrom, s);
      String[] str2 = file2.list();
      if (str2 == null) {
        System.out.println(s);
      } else {
        byte b1;
        int j;
        String[] arrayOfString;
        for (j = (arrayOfString = str2).length, b1 = 0; b1 < j; ) {
          String ss = arrayOfString[b1];
          if (ss.indexOf("一分一段") != -1) {
            File file3 = new File(String.valueOf(fileFrom) + "\\" + s, ss);
            File to = new File(fileFrom, ss);
            copyFileUsingStream(file3, to);
          } 
          b1++;
        } 
      } 
      b++;
    } 
  }
  
  private static void copyFileUsingStream(File source, File dest) {
    InputStream is = null;
    OutputStream os = null;
    try {
      is = new FileInputStream(source);
      os = new FileOutputStream(dest);
      byte[] buffer = new byte[1024];
      int length;
      while ((length = is.read(buffer)) > 0)
        os.write(buffer, 0, length); 
    } catch (Exception exception) {
    
    } finally {
      try {
        is.close();
      } catch (IOException e) {
        e.printStackTrace();
      } 
      try {
        os.close();
      } catch (IOException e) {
        e.printStackTrace();
      } 
    } 
  }
  
  public static void getAllXk() {
    Set<String> sets = JDBC.HM_PROVINCE.keySet();
    Iterator<String> it = null;
    StringBuffer sb = new StringBuffer();
    sb.append("SELECT * FROM (\r\n");
    it = sets.iterator();
    while (it.hasNext()) {
      String key = it.next();
      String value = JDBC.HM_PROVINCE.get(key);
      String value2 = JDBC.HM_PROVINCE_CODE_NAME.get(key);
      sb.append("SELECT concat(xk,'-',zx), COUNT(*) FROM " + 
          value + "_2020x x GROUP BY concat(xk,zx)\r\n" + 
          "UNION ALL \r\n" + 
          "SELECT concat(xk,'-',zx), COUNT(*) FROM " + value + "_2021x x GROUP BY concat(xk,zx)\r\n" + 
          "UNION ALL \r\n" + 
          "SELECT concat(xk,'-',zx), COUNT(*) FROM " + value + "_2022x x GROUP BY concat(xk,zx)\r\n" + 
          "UNION ALL \r\n");
    } 
    sb.append(")AS temp;\r\n");
    try {
      BufferedWriter bw = new BufferedWriter(new FileWriter(new File("E://sqlx1.txt")));
      bw.write(sb.toString());
      bw.flush();
      bw.close();
    } catch (IOException e) {
      e.printStackTrace();
    } 
  }
  
  private static void writeTempFile(File file, StringBuffer sb) {
    try {
      BufferedWriter bw = new BufferedWriter(new FileWriter(file));
      bw.write(sb.toString());
      bw.flush();
      bw.close();
    } catch (IOException e) {
      e.printStackTrace();
    } 
  }
  
  public static void changeAllTableZDWCtoZDFWC() {
    Set<String> sets = JDBC.HM_PROVINCE.keySet();
    Iterator<String> it = null;
    for (int k = 2020; k <= 2022; k++) {
      it = sets.iterator();
      while (it.hasNext()) {
        String key = it.next();
        String value = JDBC.HM_PROVINCE.get(key);
        System.out.println("ALTER table  " + value + "_" + k + " CHANGE COLUMN `zdwc` `zdfwc` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci' AFTER `zdf`;");
      } 
    } 
  }
  
  public static void replaceAllKuohaoToChineseCharater() {
    Set<String> sets = JDBC.HM_PROVINCE.keySet();
    Iterator<String> it = sets.iterator();
    StringBuffer sb = new StringBuffer();
    while (it.hasNext()) {
      String key = it.next();
      String value = JDBC.HM_PROVINCE.get(key);
      //sb.append("UPDATE " + value + " set yxmc = REPLACE(yxmc, '）',')');\r\n");
      //sb.append("UPDATE " + value + " set yxmc = REPLACE(yxmc,'（', '(');\r\n");
      for (int i = 2020; i <= 2022; i++) {
        sb.append("UPDATE " + value + "_" + i + " set yxmc = REPLACE(yxmc, '）',')');\r\n");
        sb.append("UPDATE " + value + "_" + i + " set yxmc = REPLACE(yxmc,'（', '(');\r\n");
      } 
    } 
    try {
      BufferedWriter bw = new BufferedWriter(new FileWriter(new File("E://sql2.txt")));
      bw.write(sb.toString());
      bw.flush();
      bw.close();
    } catch (IOException e) {
      e.printStackTrace();
    } 
  }
  
  public static void createAllYXTable() {
    Set<String> sets = JDBC.HM_PROVINCE.keySet();
    Iterator<String> it = sets.iterator();
    StringBuffer sb = new StringBuffer();
    while (it.hasNext()) {
      String key = it.next();
      String value = JDBC.HM_PROVINCE.get(key);
      sb.append("DROP TABLE `" + value + "`;\r\n");
      sb.append("CREATE TABLE `" + value + "` (\r\n" + 
          "\t`yxdm` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n" + 
          "\t`yxmc` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n" + 
          "\t`pc` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n" + 
          "\t`xk` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n" + 
          "\t`zx` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n" + 
          "\t`zyz` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n" + 
          "\t`zdf20` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n" + 
          "\t`zdf21` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n" + 
          "\t`zdf22` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n" + 
          "\t`zdfwc20` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n" + 
          "\t`zdfwc21` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n" + 
          "\t`zdfwc22` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci',\r\n" + 
          "\t`yxbz` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8_general_ci'\r\n" + 
          ")\r\n" + 
          "COLLATE='utf8_general_ci'\r\n" + 
          "ENGINE=InnoDB\r\n" + 
          ";\r\n\r\n");
      sb.append("INSERT INTO `" + value + "`(yxmc, pc, xk, yxbz) \r\n" + 
          "SELECT DISTINCT * FROM (\r\n" + 
          "SELECT yxmc, pc, xk, yxbz from " + value + "_2020x x GROUP BY nf,yxmc, pc, xk, yxbz\r\n" + 
          "UNION all\r\n" + 
          "SELECT yxmc, pc, xk, yxbz from " + value + "_2021x y GROUP BY nf,yxmc, pc, xk, yxbz\r\n" + 
          "UNION all\r\n" + 
          "SELECT yxmc, pc, xk, yxbz from " + value + "_2022x z GROUP BY nf,yxmc, pc, xk, yxbz\r\n" + 
          ")\r\n" + 
          "as temp;\r\n\r\n");
      sb.append("UPDATE " + value + " x ,\r\n" + 
          "(SELECT * FROM (\r\n" + 
          "SELECT yxmc, pc, xk, yxbz, MIN(zdf) AS a, MAX(zdfwc) AS b from " + value + "_2020x x GROUP BY nf,yxmc, pc, xk, yxbz) AS Y) z\r\n" + 
          "SET x.zdf20 = z.a, x.zdfwc20 = z.b\r\n" + 
          "WHERE CONCAT(x.yxmc, x.pc, x.xk, x.yxbz) = CONCAT(z.yxmc, z.pc, z.xk, z.yxbz) ;\r\n\r\n");
      sb.append("UPDATE " + value + " x ,\r\n" + 
          "(SELECT * FROM (\r\n" + 
          "SELECT yxmc, pc, xk, yxbz, MIN(zdf) AS a, MAX(zdfwc) AS b from " + value + "_2021x x GROUP BY nf,yxmc, pc, xk, yxbz) AS Y) z\r\n" + 
          "SET x.zdf21 = z.a, x.zdfwc21 = z.b\r\n" + 
          "WHERE CONCAT(x.yxmc, x.pc, x.xk, x.yxbz) = CONCAT(z.yxmc, z.pc, z.xk, z.yxbz) ;\r\n\r\n");
      sb.append("UPDATE " + value + " x ,\r\n" + 
          "(SELECT * FROM (\r\n" + 
          "SELECT yxmc, pc, xk, yxbz, MIN(zdf) AS a, MAX(zdfwc) AS b from " + value + "_2022x x GROUP BY nf,yxmc, pc, xk, yxbz) AS Y) z\r\n" + 
          "SET x.zdf22 = z.a, x.zdfwc22 = z.b\r\n" + 
          "WHERE CONCAT(x.yxmc, x.pc, x.xk, x.yxbz) = CONCAT(z.yxmc, z.pc, z.xk, z.yxbz) ;\r\n\r\n");
    } 
    try {
      BufferedWriter bw = new BufferedWriter(new FileWriter(new File("D://sql.txt")));
      bw.write(sb.toString());
      bw.flush();
      bw.close();
    } catch (IOException e) {
      e.printStackTrace();
    } 
  }
  
  private void closeAllConnection(Connection conn, PreparedStatement ps, ResultSet rs) {
    try {
      if (rs != null)
        rs.close(); 
    } catch (Exception ex) {
      ex.printStackTrace();
    } 
    try {
      if (ps != null)
        ps.close(); 
    } catch (Exception ex) {
      ex.printStackTrace();
    } 
    try {
      if (conn != null)
        conn.close(); 
    } catch (Exception ex) {
      ex.printStackTrace();
    } 
    rs = null;
    ps = null;
    conn = null;
  }
  
  
  public PredictDwfBean yc2023Dwf(int dwf19, int dwf20, int dwf21, String id) {
	  PredictDwfBean pd = new PredictDwfBean();
	  pd.setId(id);
	  if((dwf19 + dwf20 + dwf21) == -3) {
		  pd.setResultMin(-1);
		  pd.setResultMax(999);
		  pd.setSrc("三年无");
	  }else if((dwf19 + dwf20) == -2) {
		  pd.setResultMin(dwf21+10);
		  pd.setResultMax(pd.getResultMin()+10);
		  pd.setSrc("1920无数据");
	  }else if((dwf20 + dwf21) == -2) {
		  pd.setResultMin(dwf19+10);
		  pd.setResultMax(pd.getResultMin()+10);
		  pd.setSrc("A2120无数据");
	  }else if((dwf19 + dwf21) == -2) {
		  pd.setResultMin(dwf20+10);
		  pd.setResultMax(pd.getResultMin()+10);
		  pd.setSrc("A1921无数据");
	  }else if(dwf19 == -1) {
		  if(dwf21 < dwf20) {
			  pd.setResultMin(dwf20);
			  pd.setResultMax(pd.getResultMin()+10);
			  pd.setSrc("B19无数据");
		  }else {
			  pd.setResultMin(dwf21 + (dwf21 - dwf20));
			  pd.setResultMax(pd.getResultMin()+10);
			  pd.setSrc("B19无数据");
		  }
	  }else if(dwf20 == -1) {
		  if(dwf21 < dwf19) {
			  pd.setResultMin(dwf19);
			  pd.setResultMax(pd.getResultMin()+10);
			  pd.setSrc("B20无数据");
		  }else {
			  pd.setResultMin(dwf21 + (dwf21 - dwf19));
			  pd.setResultMax(pd.getResultMin()+10);
			  pd.setSrc("B20无数据");
		  }
	  }else if(dwf21 == -1) {
		  if(dwf20 < dwf19) {
			  pd.setResultMin(dwf19);
			  pd.setResultMax(pd.getResultMin()+10);
			  pd.setSrc("B21无数据");
		  }else {
			  pd.setResultMin(dwf20 + (dwf20 - dwf19));
			  pd.setResultMax(pd.getResultMin()+10);
			  pd.setSrc("B21无数据");
		  }
	  }else {
		  if(dwf19 > dwf20 && dwf20 < dwf21) {
			  //高地位，低
			  if(dwf21 < dwf19) {
				  pd.setResultMin(dwf19);
				  pd.setResultMax(pd.getResultMin()+5);
				  pd.setSrc("C大小大");
    		  }else {
    			  pd.setResultMin(dwf21);
    			  pd.setResultMax(pd.getResultMin()+5);
				  pd.setSrc("C大小大");
    		  }
			  
			  
		  }else if(dwf19 < dwf20 && dwf20 > dwf21) {
			//高地位，高
			  if(dwf21 < dwf19) {
				  pd.setResultMin(dwf20);
				  pd.setResultMax(pd.getResultMin()+5);
				  pd.setSrc("C小大小");
    		  }else {
    			  pd.setResultMin(dwf20 + (dwf21 - dwf19));
    			  pd.setResultMax(pd.getResultMin()+5);
				  pd.setSrc("C小大小");
    		  }
			  
		  }else if(dwf19 > dwf20 && dwf20 > dwf21) {
    			//连续降
			  pd.setResultMin(dwf19);
			  pd.setResultMax(pd.getResultMin()+5);
			  pd.setSrc("D连续降");
		  }else if(dwf19 < dwf20 && dwf20 < dwf21) {
    			//连续涨
			  
			  int span2021 = dwf21 - dwf20;
			  int span1920 = dwf20 - dwf19;
			  pd.setResultMin((5 + dwf21 + (span2021 > span1920 ? (span2021 + span2021 - span1920):(span1920 + span1920 - span2021))));
			  pd.setResultMax(pd.getResultMin()+10);
			  pd.setSrc("D连续涨");
			  
		  }else {
			  
			  int prefix = (int)(Math.random() * 6);
			  
			  pd.setResultMin(prefix + (Math.max(dwf21, Math.max(dwf20,dwf19))));
			  pd.setResultMax(pd.getResultMin()+10);
			  pd.setSrc("F其他");
		  }
	  }
	  return pd;
  }
  
  public void updateWC() {
	    Connection conn = null;
	    PreparedStatement ps = null,ps2 = null;
	    ResultSet rs = null;
	    List<PredictDwfBean> list = new ArrayList<>();
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT * FROM wh_dwf_yx_2021";
	      String SQLUpdate = "UPDATE wh_dwf_yx_2021 SET src = ?, test = ? WHERE sq = ?";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      
	      rs = ps.executeQuery();
	      while (rs.next()) {
	    	  int sq = rs.getInt("sq");
	    	  int dwf19 = Tools.getInt(rs.getString("2019dwf"));
	    	  int dwf20 = Tools.getInt(rs.getString("2020dwf"));
	    	  int dwf21 = Tools.getInt(rs.getString("2021dwf"));
	    	  list.add(yc2023Dwf(dwf19, dwf20, dwf21, String.valueOf(sq)));
	      } 
	      

	      ps2 = conn.prepareStatement(SQLUpdate);
	      for(PredictDwfBean s : list) {
	    	  String f = s.getSrc();
	    	  int SQ = Tools.getInt(s.getId());
	    	  int testDwf = s.getResultMin();
	    	  ps2.setString(1,f);
	    	  ps2.setString(2,String.valueOf(testDwf));
	    	  ps2.setInt(3, SQ);
	    	  ps2.addBatch();
	      }
	      
	      ps2.executeBatch();
	      
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	  }
  
  
  public void test2022Dwf721() {
	    Connection conn = null;
	    PreparedStatement ps = null,ps2 = null;
	    ResultSet rs = null;
	    List<String> list = new ArrayList<>();
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT * FROM wh_dwf_yx_2021";
	      String SQLUpdate = "UPDATE wh_dwf_yx_2021 SET src = ?, test = ? WHERE sq = ?";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      
	      rs = ps.executeQuery();
	      while (rs.next()) {
	    	  int sq = rs.getInt("sq");
	    	  int dwf19 = Tools.getInt(rs.getString("2019dwf"));
	    	  int dwf20 = Tools.getInt(rs.getString("2020dwf"));
	    	  int dwf21 = Tools.getInt(rs.getString("2021dwf"));
	    	  if((dwf19 + dwf20 + dwf21) == -3) {
	    		  //
	    	  }else if((dwf19 + dwf20) == -2) {
	    		  list.add(String.valueOf("a1-"+sq + "-" + Math.ceil(dwf21*1.015)));
	    	  }else if((dwf20 + dwf21) == -2) {
	    		  list.add(String.valueOf("a2-"+sq + "-" + Math.ceil(dwf19*1.015)));
	    	  }else if((dwf19 + dwf21) == -2) {
	    		  list.add(String.valueOf("a3-"+sq + "-" + Math.ceil(dwf20*1.015)));
	    	  }else if(dwf19 == -1) {
	    		  if(dwf21 < dwf20) {
	    			  list.add(String.valueOf("a-"+sq + "-" + Math.ceil(dwf21*0.85) + Math.ceil(dwf20*0.15)));
	    		  }else {
	    			  list.add(String.valueOf("a-"+sq + "-" + Math.ceil(dwf21*0.8) + Math.ceil(dwf20*0.2)));
	    		  }
	    	  }else if(dwf20 == -1) {
	    		  if(dwf21 < dwf19) {
	    			  list.add(String.valueOf("b-"+sq + "-" + Math.ceil(dwf21*0.85) + Math.ceil(dwf19*0.15)));
	    		  }else {
	    			  list.add(String.valueOf("b-"+sq + "-" + Math.ceil(dwf21*0.8) + Math.ceil(dwf20*0.2)));
	    		  }
	    	  }else if(dwf21 == -1) {
	    		  if(dwf20 < dwf19) {
	    			  list.add(String.valueOf("c-"+sq + "-" + Math.ceil(dwf20*0.85) + Math.ceil(dwf19*0.15)));
	    		  }else {
	    			  list.add(String.valueOf("c-"+sq + "-" + Math.ceil(dwf20*0.8) + Math.ceil(dwf19*0.2)));
	    		  }
	    	  }else {
	    		  if(dwf19 > dwf20 && dwf20 < dwf21) {
	    			  //高地位，低
	    			  if(dwf21 < dwf19) {
		    			  list.add(String.valueOf("d-"+sq + "-" + dwf19));
		    		  }else {
		    			  list.add(String.valueOf("d-"+sq + "-" + dwf21));
		    		  }
	    			  
	    			  
	    		  }else if(dwf19 < dwf20 && dwf20 > dwf21) {
	    			//高地位，高
	    			  if(dwf21 < dwf19) {
		    			  list.add(String.valueOf("e-"+sq + "-" + dwf20));
		    		  }else {
		    			  list.add(String.valueOf("e-"+sq + "-" + (dwf20 + (dwf21 - dwf19)))); //TODO:可以用低位冲
		    		  }
	    			  
	    		  }else if(dwf19 > dwf20 && dwf20 > dwf21) {
		    			//连续降
	    			  list.add(String.valueOf("f-"+sq + "-" + Math.ceil(dwf21*0.7) + Math.ceil(dwf20*0.2) + Math.ceil(dwf19*0.1)));
	    		  }else if(dwf19 < dwf20 && dwf20 < dwf21) {
		    			//连续涨
	    			  
	    			  int span2021 = dwf21 - dwf20;
	    			  int span1920 = dwf20 - dwf19;
	    			  
	    			  list.add(String.valueOf("g-"+sq + "-" + Math.ceil(dwf21*0.5) + Math.ceil(dwf20*0.35) + Math.ceil(dwf19*0.15)));
	    		  }else {
	    			  list.add(String.valueOf("h-"+sq + "-" + (Math.max(dwf21, Math.max(dwf20,dwf19)) + 5))); //增加30%命中率
	    		  }
	    	  }
	      } 
	      

	      ps2 = conn.prepareStatement(SQLUpdate);
	      for(String s : list) {
	    	  String f = s.split("-")[0];
	    	  int SQ = Tools.getInt(s.split("-")[1]);
	    	  String testDwf = s.split("-")[2];
	    	  ps2.setString(1,f);
	    	  ps2.setString(2,testDwf);
	    	  ps2.setInt(3, SQ);
	    	  ps2.addBatch();
	      }
	      
	      ps2.executeBatch();
	      
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	  }
}
