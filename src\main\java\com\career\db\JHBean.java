package com.career.db;

import java.lang.reflect.Field;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

import com.career.utils.BaseBean;
import com.career.utils.ResultVO;

public class JHBean extends BaseBean{
	private int id;
	private String sf;
	private int nf;
	private String xk;
	private String zx;
	private String pc;
	private String pc_code;
	private String pc_type;
	private String pc_desc;
	private String yxdm;
	private String yxmc;
	private String yxmc_org;
	private String yxbz;
	private String zydm;
	private String zymc;
	private String zymc_org;
	private String zyz;
	private String znzy;
	private String zybz;
	private String jhs;
	private String xz;
	private String fee;
	private String jhs_a;
	private String jhs_b;
	private String jhs_c;
	private String lqrs_a;
	private String lqrs_b;
	private String lqrs_c;
	private String xz_a;
	private String fee_a;
	private String zdf_a;
	private String zdf_b;
	private String zdf_c;
	private String zdfwc_a;
	private String zdfwc_b;
	private String zdfwc_c;
	private String zgf_a;
	private String zgf_b;
	private String zgf_c;
	private String zgfwc_a;
	private String zgfwc_b;
	private String zgfwc_c;
	private String pjf_a;
	private String pjf_b;
	private String pjf_c;
	private String pjfwc_a;
	private String pjfwc_b;
	private String pjfwc_c;
	private String yxsf;
	private String yxcs;
	private String zyml;
	private String ind_catg;
	private String ind_nature;
	private String yx_tags;
	private String yx_tags_all;
	private int cnt_company;
	private int cnt_employ;
	private int is_hz;
	private int is_first;
	private float cnt_grad;
	private String lqpc;
	private int ycwc;
	private int znzys;
	private int znzyls;
	private int znjhs;
	
	int qsf_a;
	int qsf_b;
	int qsf_c;
	int qsf;
	

	public String getYxbz() {
		return yxbz;
	}


	public void setYxbz(String yxbz) {
		this.yxbz = yxbz;
	}


	public String getPc_desc() {
		return pc_desc;
	}


	public void setPc_desc(String pc_desc) {
		this.pc_desc = pc_desc;
	}


	public String getPc_type() {
		return pc_type;
	}


	public void setPc_type(String pc_type) {
		this.pc_type = pc_type;
	}


	public String getLqrs_a() {
		return lqrs_a;
	}


	public void setLqrs_a(String lqrs_a) {
		this.lqrs_a = lqrs_a;
	}


	public String getLqrs_b() {
		return lqrs_b;
	}


	public void setLqrs_b(String lqrs_b) {
		this.lqrs_b = lqrs_b;
	}


	public String getLqrs_c() {
		return lqrs_c;
	}


	public void setLqrs_c(String lqrs_c) {
		this.lqrs_c = lqrs_c;
	}


	public static int getPageRowCntOrg() {
		return PAGE_ROW_CNT_ORG;
	}


	public int getQsf_a() {
		return qsf_a;
	}


	public void setQsf_a(int qsf_a) {
		this.qsf_a = qsf_a;
	}


	public int getQsf_b() {
		return qsf_b;
	}


	public void setQsf_b(int qsf_b) {
		this.qsf_b = qsf_b;
	}


	public int getQsf_c() {
		return qsf_c;
	}


	public void setQsf_c(int qsf_c) {
		this.qsf_c = qsf_c;
	}


	public int getQsf() {
		return qsf;
	}


	public void setQsf(int qsf) {
		this.qsf = qsf;
	}


	public int getZnzyls() {
		return znzyls;
	}


	public void setZnzyls(int znzyls) {
		this.znzyls = znzyls;
	}


	public int getZnjhs() {
		return znjhs;
	}


	public void setZnjhs(int znjhs) {
		this.znjhs = znjhs;
	}


	public String getYx_tags_all() {
		return yx_tags_all;
	}


	public void setYx_tags_all(String yx_tags_all) {
		this.yx_tags_all = yx_tags_all;
	}


	public int getYcwc() {
		return ycwc;
	}


	public void setYcwc(int ycwc) {
		this.ycwc = ycwc;
	}


	public int getZnzys() {
		return znzys;
	}


	public void setZnzys(int znzys) {
		this.znzys = znzys;
	}

	public int getIs_first() {
		return is_first;
	}


	public void setIs_first(int is_first) {
		this.is_first = is_first;
	}


	public int getIs_hz() {
		return is_hz;
	}


	public void setIs_hz(int is_hz) {
		this.is_hz = is_hz;
	}


	public String getZgf_a() {
		return zgf_a;
	}


	public void setZgf_a(String zgf_a) {
		this.zgf_a = zgf_a;
	}


	public String getZgf_b() {
		return zgf_b;
	}


	public void setZgf_b(String zgf_b) {
		this.zgf_b = zgf_b;
	}


	public String getZgf_c() {
		return zgf_c;
	}


	public void setZgf_c(String zgf_c) {
		this.zgf_c = zgf_c;
	}


	public String getZgfwc_a() {
		return zgfwc_a;
	}


	public void setZgfwc_a(String zgfwc_a) {
		this.zgfwc_a = zgfwc_a;
	}


	public String getZgfwc_b() {
		return zgfwc_b;
	}


	public void setZgfwc_b(String zgfwc_b) {
		this.zgfwc_b = zgfwc_b;
	}


	public String getZgfwc_c() {
		return zgfwc_c;
	}


	public void setZgfwc_c(String zgfwc_c) {
		this.zgfwc_c = zgfwc_c;
	}
	private String info_byl;

	private int zdf;
	private int zdfwc;
	
	public String getZnzy() {
		return znzy;
	}


	public String getInfo_byl() {
		return info_byl;
	}


	public void setInfo_byl(String info_byl) {
		this.info_byl = info_byl;
	}


	public void setZnzy(String znzy) {
		this.znzy = znzy;
	}


	public int getZdf() {
		return zdf;
	}


	public void setZdf(int zdf) {
		this.zdf = zdf;
	}


	public int getZdfwc() {
		return zdfwc;
	}


	public void setZdfwc(int zdfwc) {
		this.zdfwc = zdfwc;
	}


	public int getId() {
		return id;
	}


	public void setId(int id) {
		this.id = id;
	}


	public String getJhs() {
		return jhs;
	}


	public void setJhs(String jhs) {
		this.jhs = jhs;
	}


	public String getXz() {
		return xz;
	}


	public void setXz(String xz) {
		this.xz = xz;
	}


	public String getFee() {
		return fee;
	}


	public void setFee(String fee) {
		this.fee = fee;
	}


	public static void main(String args[]) {
		JHBean bean = new JHBean();
		printBeanProperties(bean);
	}
	
	
	public String getInd_catg() {
		return ind_catg;
	}
	public void setInd_catg(String ind_catg) {
		this.ind_catg = ind_catg;
	}
	public String getInd_nature() {
		return ind_nature;
	}
	public void setInd_nature(String ind_nature) {
		this.ind_nature = ind_nature;
	}
	public String getYx_tags() {
		return yx_tags;
	}
	public void setYx_tags(String yx_tags) {
		this.yx_tags = yx_tags;
	}
	public int getCnt_company() {
		return cnt_company;
	}
	public void setCnt_company(int cnt_company) {
		this.cnt_company = cnt_company;
	}
	public int getCnt_employ() {
		return cnt_employ;
	}
	public void setCnt_employ(int cnt_employ) {
		this.cnt_employ = cnt_employ;
	}
	public float getCnt_grad() {
		return cnt_grad;
	}
	public void setCnt_grad(float cnt_grad) {
		this.cnt_grad = cnt_grad;
	}
	public String getLqpc() {
		return lqpc;
	}
	public void setLqpc(String lqpc) {
		this.lqpc = lqpc;
	}
	public String getSf() {
		return sf;
	}
	public void setSf(String sf) {
		this.sf = sf;
	}
	public int getNf() {
		return nf;
	}
	public void setNf(int nf) {
		this.nf = nf;
	}
	public String getXk() {
		return xk;
	}
	public void setXk(String xk) {
		this.xk = xk;
	}
	public String getZx() {
		return zx;
	}
	public void setZx(String zx) {
		this.zx = zx;
	}
	public String getPc() {
		return pc;
	}
	public void setPc(String pc) {
		this.pc = pc;
	}
	public String getPc_code() {
		return pc_code;
	}
	public void setPc_code(String pc_code) {
		this.pc_code = pc_code;
	}
	public String getYxdm() {
		return yxdm;
	}
	public void setYxdm(String yxdm) {
		this.yxdm = yxdm;
	}
	public String getYxmc() {
		return yxmc;
	}
	public void setYxmc(String yxmc) {
		this.yxmc = yxmc;
	}
	public String getYxmc_org() {
		return yxmc_org;
	}
	public void setYxmc_org(String yxmc_org) {
		this.yxmc_org = yxmc_org;
	}
	public String getZydm() {
		return zydm;
	}
	public void setZydm(String zydm) {
		this.zydm = zydm;
	}
	public String getZymc() {
		return zymc;
	}
	public void setZymc(String zymc) {
		this.zymc = zymc;
	}
	public String getZymc_org() {
		return zymc_org;
	}
	public void setZymc_org(String zymc_org) {
		this.zymc_org = zymc_org;
	}
	public String getZyz() {
		return zyz;
	}
	public void setZyz(String zyz) {
		this.zyz = zyz;
	}
	public String getZybz() {
		return zybz;
	}
	public void setZybz(String zybz) {
		this.zybz = zybz;
	}
	public String getJhs_a() {
		return jhs_a;
	}
	public void setJhs_a(String jhs_a) {
		this.jhs_a = jhs_a;
	}
	public String getJhs_b() {
		return jhs_b;
	}
	public void setJhs_b(String jhs_b) {
		this.jhs_b = jhs_b;
	}
	public String getJhs_c() {
		return jhs_c;
	}
	public void setJhs_c(String jhs_c) {
		this.jhs_c = jhs_c;
	}
	public String getXz_a() {
		return xz_a;
	}
	public void setXz_a(String xz_a) {
		this.xz_a = xz_a;
	}
	public String getFee_a() {
		return fee_a;
	}
	public void setFee_a(String fee_a) {
		this.fee_a = fee_a;
	}
	public String getZdf_a() {
		return zdf_a;
	}
	public void setZdf_a(String zdf_a) {
		this.zdf_a = zdf_a;
	}
	public String getZdf_b() {
		return zdf_b;
	}
	public void setZdf_b(String zdf_b) {
		this.zdf_b = zdf_b;
	}
	public String getZdf_c() {
		return zdf_c;
	}
	public void setZdf_c(String zdf_c) {
		this.zdf_c = zdf_c;
	}
	public String getZdfwc_a() {
		return zdfwc_a;
	}
	public void setZdfwc_a(String zdfwc_a) {
		this.zdfwc_a = zdfwc_a;
	}
	public String getZdfwc_b() {
		return zdfwc_b;
	}
	public void setZdfwc_b(String zdfwc_b) {
		this.zdfwc_b = zdfwc_b;
	}
	public String getZdfwc_c() {
		return zdfwc_c;
	}
	public void setZdfwc_c(String zdfwc_c) {
		this.zdfwc_c = zdfwc_c;
	}
	public String getPjf_a() {
		return pjf_a;
	}
	public void setPjf_a(String pjf_a) {
		this.pjf_a = pjf_a;
	}
	public String getPjf_b() {
		return pjf_b;
	}
	public void setPjf_b(String pjf_b) {
		this.pjf_b = pjf_b;
	}
	public String getPjf_c() {
		return pjf_c;
	}
	public void setPjf_c(String pjf_c) {
		this.pjf_c = pjf_c;
	}
	public String getPjfwc_a() {
		return pjfwc_a;
	}
	public void setPjfwc_a(String pjfwc_a) {
		this.pjfwc_a = pjfwc_a;
	}
	public String getPjfwc_b() {
		return pjfwc_b;
	}
	public void setPjfwc_b(String pjfwc_b) {
		this.pjfwc_b = pjfwc_b;
	}
	public String getPjfwc_c() {
		return pjfwc_c;
	}
	public void setPjfwc_c(String pjfwc_c) {
		this.pjfwc_c = pjfwc_c;
	}
	public String getYxsf() {
		return yxsf;
	}
	public void setYxsf(String yxsf) {
		this.yxsf = yxsf;
	}
	public String getYxcs() {
		return yxcs;
	}
	public void setYxcs(String yxcs) {
		this.yxcs = yxcs;
	}
	public String getZyml() {
		return zyml;
	}
	public void setZyml(String zyml) {
		this.zyml = zyml;
	}

	// 新增：针对新表的分页行数
	public static final int PAGE_ROW_CNT_ORG = 20;
}
