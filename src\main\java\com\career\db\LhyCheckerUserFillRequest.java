package com.career.db;

import java.util.Date;

public class LhyCheckerUserFillRequest {
	private String fid;
    private String lhy_c_id;
    private String f_name;
    private String f_phone;
    private String f_remark;
    private String f_open_id;
    private String f_from;
    private Date create_tm;
    private Date fill_end_tm;
    private Date fill_start_tm;
    private Date checked_tm;
    private Date send_tm;
    private Date paid_tm;
    private String audio_url;
    private int status;
    private int pay_status;
    private String fm_batch_id;
    private String sf;
    private String xk;
    private int score;
    
	public String getSf() {
		return sf;
	}
	public void setSf(String sf) {
		this.sf = sf;
	}
	public String getXk() {
		return xk;
	}
	public void setXk(String xk) {
		this.xk = xk;
	}
	public int getScore() {
		return score;
	}
	public void setScore(int score) {
		this.score = score;
	}
	public String getFm_batch_id() {
		return fm_batch_id;
	}
	public void setFm_batch_id(String fm_batch_id) {
		this.fm_batch_id = fm_batch_id;
	}
	public String getFid() {
		return fid;
	}
	public void setFid(String fid) {
		this.fid = fid;
	}
	
	public String getLhy_c_id() {
		return lhy_c_id;
	}
	public void setLhy_c_id(String lhy_c_id) {
		this.lhy_c_id = lhy_c_id;
	}
	public String getF_name() {
		return f_name;
	}
	public void setF_name(String f_name) {
		this.f_name = f_name;
	}
	public String getF_phone() {
		return f_phone;
	}
	public void setF_phone(String f_phone) {
		this.f_phone = f_phone;
	}
	public String getF_remark() {
		return f_remark;
	}
	public void setF_remark(String f_remark) {
		this.f_remark = f_remark;
	}
	public String getF_open_id() {
		return f_open_id;
	}
	public void setF_open_id(String f_open_id) {
		this.f_open_id = f_open_id;
	}
	public String getF_from() {
		return f_from;
	}
	public void setF_from(String f_from) {
		this.f_from = f_from;
	}
	public Date getCreate_tm() {
		return create_tm;
	}
	public void setCreate_tm(Date create_tm) {
		this.create_tm = create_tm;
	}
	public Date getFill_end_tm() {
		return fill_end_tm;
	}
	public void setFill_end_tm(Date fill_end_tm) {
		this.fill_end_tm = fill_end_tm;
	}
	public Date getFill_start_tm() {
		return fill_start_tm;
	}
	public void setFill_start_tm(Date fill_start_tm) {
		this.fill_start_tm = fill_start_tm;
	}
	public Date getChecked_tm() {
		return checked_tm;
	}
	public void setChecked_tm(Date checked_tm) {
		this.checked_tm = checked_tm;
	}
	public Date getSend_tm() {
		return send_tm;
	}
	public void setSend_tm(Date send_tm) {
		this.send_tm = send_tm;
	}
	public Date getPaid_tm() {
		return paid_tm;
	}
	public void setPaid_tm(Date paid_tm) {
		this.paid_tm = paid_tm;
	}
	public String getAudio_url() {
		return audio_url;
	}
	public void setAudio_url(String audio_url) {
		this.audio_url = audio_url;
	}
	public int getStatus() {
		return status;
	}
	public void setStatus(int status) {
		this.status = status;
	}
	public int getPay_status() {
		return pay_status;
	}
	public void setPay_status(int pay_status) {
		this.pay_status = pay_status;
	}


}
