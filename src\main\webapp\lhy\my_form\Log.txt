SELECT * FROM lhy_S5_SC_form_maker_main x WHERE batch_id = ?
[SQL- === getMakerFormMainByBatchId() : ] SELECT * FROM lhy_S5_SC_form_maker_main x WHERE batch_id = 'S5_f8a758af-45e8-4931-b10b-cf452528beb2'
[SQL- === getMakerFormMainByBatchId() : ] SELECT * FROM lhy_S5_SC_form_maker x WHERE batch_id_org = 'S5_f8a758af-45e8-4931-b10b-cf452528beb2' ORDER BY seq_no_yx, seq_no_zy
=== 调试日志1：基础数据检查 ===
batch_id: S5_f8a758af-45e8-4931-b10b-cf452528beb2
provinceTableName: S5_SC
lhyFormMain: 存在
formList.size(): 230
[SQL] SELECT * FROM S5_SC_zdks_rank  WHERE  sf = '四川' and kl_code LIKE '%1%' and (WC-CNT) < 31541 AND WC >= 31541 
=== 调试日志2：构建MAP_XX ===
处理志愿: seq_no_yx=1, yxdm=0048, yxmc=null, zyz=101
处理志愿: seq_no_yx=1, yxdm=0048, yxmc=null, zyz=101
处理志愿: seq_no_yx=1, yxdm=0048, yxmc=null, zyz=101
处理志愿: seq_no_yx=1, yxdm=0048, yxmc=null, zyz=101
处理志愿: seq_no_yx=1, yxdm=0048, yxmc=null, zyz=101
处理志愿: seq_no_yx=1, yxdm=0048, yxmc=null, zyz=101
处理志愿: seq_no_yx=2, yxdm=3389, yxmc=null, zyz=101
处理志愿: seq_no_yx=2, yxdm=3389, yxmc=null, zyz=101
处理志愿: seq_no_yx=2, yxdm=3389, yxmc=null, zyz=101
处理志愿: seq_no_yx=2, yxdm=3389, yxmc=null, zyz=101
处理志愿: seq_no_yx=2, yxdm=3389, yxmc=null, zyz=101
处理志愿: seq_no_yx=2, yxdm=3389, yxmc=null, zyz=101
处理志愿: seq_no_yx=3, yxdm=4508, yxmc=null, zyz=104
处理志愿: seq_no_yx=4, yxdm=2308, yxmc=null, zyz=104
处理志愿: seq_no_yx=4, yxdm=2308, yxmc=null, zyz=104
处理志愿: seq_no_yx=4, yxdm=2308, yxmc=null, zyz=104
处理志愿: seq_no_yx=4, yxdm=2308, yxmc=null, zyz=104
处理志愿: seq_no_yx=4, yxdm=2308, yxmc=null, zyz=104
处理志愿: seq_no_yx=4, yxdm=2308, yxmc=null, zyz=104
处理志愿: seq_no_yx=5, yxdm=4437, yxmc=null, zyz=101
处理志愿: seq_no_yx=5, yxdm=4437, yxmc=null, zyz=101
处理志愿: seq_no_yx=5, yxdm=4437, yxmc=null, zyz=101
处理志愿: seq_no_yx=5, yxdm=4437, yxmc=null, zyz=101
处理志愿: seq_no_yx=5, yxdm=4437, yxmc=null, zyz=101
处理志愿: seq_no_yx=5, yxdm=4437, yxmc=null, zyz=101
处理志愿: seq_no_yx=6, yxdm=5102, yxmc=null, zyz=108
处理志愿: seq_no_yx=6, yxdm=5102, yxmc=null, zyz=108
处理志愿: seq_no_yx=6, yxdm=5102, yxmc=null, zyz=108
处理志愿: seq_no_yx=6, yxdm=5102, yxmc=null, zyz=108
处理志愿: seq_no_yx=6, yxdm=5102, yxmc=null, zyz=108
处理志愿: seq_no_yx=6, yxdm=5102, yxmc=null, zyz=108
处理志愿: seq_no_yx=7, yxdm=3117, yxmc=null, zyz=104
处理志愿: seq_no_yx=7, yxdm=3117, yxmc=null, zyz=104
处理志愿: seq_no_yx=7, yxdm=3117, yxmc=null, zyz=104
处理志愿: seq_no_yx=8, yxdm=3620, yxmc=null, zyz=101
处理志愿: seq_no_yx=8, yxdm=3620, yxmc=null, zyz=101
处理志愿: seq_no_yx=8, yxdm=3620, yxmc=null, zyz=101
处理志愿: seq_no_yx=8, yxdm=3620, yxmc=null, zyz=101
处理志愿: seq_no_yx=8, yxdm=3620, yxmc=null, zyz=101
处理志愿: seq_no_yx=8, yxdm=3620, yxmc=null, zyz=101
处理志愿: seq_no_yx=9, yxdm=5201, yxmc=null, zyz=107
处理志愿: seq_no_yx=9, yxdm=5201, yxmc=null, zyz=107
处理志愿: seq_no_yx=9, yxdm=5201, yxmc=null, zyz=107
处理志愿: seq_no_yx=9, yxdm=5201, yxmc=null, zyz=107
处理志愿: seq_no_yx=10, yxdm=0042, yxmc=null, zyz=107
处理志愿: seq_no_yx=11, yxdm=9227, yxmc=null, zyz=102
处理志愿: seq_no_yx=11, yxdm=9227, yxmc=null, zyz=102
处理志愿: seq_no_yx=11, yxdm=9227, yxmc=null, zyz=102
处理志愿: seq_no_yx=12, yxdm=4605, yxmc=null, zyz=104
处理志愿: seq_no_yx=12, yxdm=4605, yxmc=null, zyz=104
处理志愿: seq_no_yx=12, yxdm=4605, yxmc=null, zyz=104
处理志愿: seq_no_yx=12, yxdm=4605, yxmc=null, zyz=104
处理志愿: seq_no_yx=12, yxdm=4605, yxmc=null, zyz=104
处理志愿: seq_no_yx=12, yxdm=4605, yxmc=null, zyz=104
处理志愿: seq_no_yx=13, yxdm=4617, yxmc=null, zyz=102
处理志愿: seq_no_yx=13, yxdm=4617, yxmc=null, zyz=102
处理志愿: seq_no_yx=13, yxdm=4617, yxmc=null, zyz=102
处理志愿: seq_no_yx=14, yxdm=6101, yxmc=null, zyz=102
处理志愿: seq_no_yx=14, yxdm=6101, yxmc=null, zyz=102
处理志愿: seq_no_yx=14, yxdm=6101, yxmc=null, zyz=102
处理志愿: seq_no_yx=14, yxdm=6101, yxmc=null, zyz=102
处理志愿: seq_no_yx=14, yxdm=6101, yxmc=null, zyz=102
处理志愿: seq_no_yx=14, yxdm=6101, yxmc=null, zyz=102
处理志愿: seq_no_yx=15, yxdm=5201, yxmc=null, zyz=106
处理志愿: seq_no_yx=15, yxdm=5201, yxmc=null, zyz=106
处理志愿: seq_no_yx=15, yxdm=5201, yxmc=null, zyz=106
处理志愿: seq_no_yx=15, yxdm=5201, yxmc=null, zyz=106
处理志愿: seq_no_yx=15, yxdm=5201, yxmc=null, zyz=106
处理志愿: seq_no_yx=15, yxdm=5201, yxmc=null, zyz=106
处理志愿: seq_no_yx=16, yxdm=4301, yxmc=null, zyz=102
处理志愿: seq_no_yx=16, yxdm=4301, yxmc=null, zyz=102
处理志愿: seq_no_yx=16, yxdm=4301, yxmc=null, zyz=102
处理志愿: seq_no_yx=16, yxdm=4301, yxmc=null, zyz=102
处理志愿: seq_no_yx=16, yxdm=4301, yxmc=null, zyz=102
处理志愿: seq_no_yx=16, yxdm=4301, yxmc=null, zyz=102
处理志愿: seq_no_yx=17, yxdm=0023, yxmc=null, zyz=102
处理志愿: seq_no_yx=17, yxdm=0023, yxmc=null, zyz=102
处理志愿: seq_no_yx=17, yxdm=0023, yxmc=null, zyz=102
处理志愿: seq_no_yx=17, yxdm=0023, yxmc=null, zyz=102
处理志愿: seq_no_yx=17, yxdm=0023, yxmc=null, zyz=102
处理志愿: seq_no_yx=17, yxdm=0023, yxmc=null, zyz=102
处理志愿: seq_no_yx=18, yxdm=3620, yxmc=null, zyz=103
处理志愿: seq_no_yx=18, yxdm=3620, yxmc=null, zyz=103
处理志愿: seq_no_yx=18, yxdm=3620, yxmc=null, zyz=103
处理志愿: seq_no_yx=18, yxdm=3620, yxmc=null, zyz=103
处理志愿: seq_no_yx=18, yxdm=3620, yxmc=null, zyz=103
处理志愿: seq_no_yx=18, yxdm=3620, yxmc=null, zyz=103
处理志愿: seq_no_yx=19, yxdm=3618, yxmc=null, zyz=101
处理志愿: seq_no_yx=19, yxdm=3618, yxmc=null, zyz=101
处理志愿: seq_no_yx=19, yxdm=3618, yxmc=null, zyz=101
处理志愿: seq_no_yx=19, yxdm=3618, yxmc=null, zyz=101
处理志愿: seq_no_yx=19, yxdm=3618, yxmc=null, zyz=101
处理志愿: seq_no_yx=19, yxdm=3618, yxmc=null, zyz=101
处理志愿: seq_no_yx=20, yxdm=4501, yxmc=null, zyz=102
处理志愿: seq_no_yx=20, yxdm=4501, yxmc=null, zyz=102
处理志愿: seq_no_yx=20, yxdm=4501, yxmc=null, zyz=102
处理志愿: seq_no_yx=20, yxdm=4501, yxmc=null, zyz=102
处理志愿: seq_no_yx=20, yxdm=4501, yxmc=null, zyz=102
处理志愿: seq_no_yx=20, yxdm=4501, yxmc=null, zyz=102
处理志愿: seq_no_yx=21, yxdm=0056, yxmc=null, zyz=108
处理志愿: seq_no_yx=21, yxdm=0056, yxmc=null, zyz=108
处理志愿: seq_no_yx=21, yxdm=0056, yxmc=null, zyz=108
处理志愿: seq_no_yx=21, yxdm=0056, yxmc=null, zyz=108
处理志愿: seq_no_yx=21, yxdm=0056, yxmc=null, zyz=108
处理志愿: seq_no_yx=21, yxdm=0056, yxmc=null, zyz=108
处理志愿: seq_no_yx=22, yxdm=4501, yxmc=null, zyz=101
处理志愿: seq_no_yx=22, yxdm=4501, yxmc=null, zyz=101
处理志愿: seq_no_yx=22, yxdm=4501, yxmc=null, zyz=101
处理志愿: seq_no_yx=23, yxdm=0009, yxmc=null, zyz=105
处理志愿: seq_no_yx=23, yxdm=0009, yxmc=null, zyz=105
处理志愿: seq_no_yx=23, yxdm=0009, yxmc=null, zyz=105
处理志愿: seq_no_yx=23, yxdm=0009, yxmc=null, zyz=105
处理志愿: seq_no_yx=23, yxdm=0009, yxmc=null, zyz=105
处理志愿: seq_no_yx=23, yxdm=0009, yxmc=null, zyz=105
处理志愿: seq_no_yx=24, yxdm=0058, yxmc=null, zyz=105
处理志愿: seq_no_yx=25, yxdm=6501, yxmc=null, zyz=102
处理志愿: seq_no_yx=25, yxdm=6501, yxmc=null, zyz=102
处理志愿: seq_no_yx=25, yxdm=6501, yxmc=null, zyz=102
处理志愿: seq_no_yx=25, yxdm=6501, yxmc=null, zyz=102
处理志愿: seq_no_yx=25, yxdm=6501, yxmc=null, zyz=102
处理志愿: seq_no_yx=25, yxdm=6501, yxmc=null, zyz=102
处理志愿: seq_no_yx=26, yxdm=3921, yxmc=null, zyz=101
处理志愿: seq_no_yx=26, yxdm=3921, yxmc=null, zyz=101
处理志愿: seq_no_yx=26, yxdm=3921, yxmc=null, zyz=101
处理志愿: seq_no_yx=27, yxdm=2104, yxmc=null, zyz=108
处理志愿: seq_no_yx=27, yxdm=2104, yxmc=null, zyz=108
处理志愿: seq_no_yx=27, yxdm=2104, yxmc=null, zyz=108
处理志愿: seq_no_yx=27, yxdm=2104, yxmc=null, zyz=108
处理志愿: seq_no_yx=28, yxdm=4501, yxmc=null, zyz=103
处理志愿: seq_no_yx=28, yxdm=4501, yxmc=null, zyz=103
处理志愿: seq_no_yx=29, yxdm=3207, yxmc=null, zyz=102
处理志愿: seq_no_yx=29, yxdm=3207, yxmc=null, zyz=102
处理志愿: seq_no_yx=29, yxdm=3207, yxmc=null, zyz=102
处理志愿: seq_no_yx=29, yxdm=3207, yxmc=null, zyz=102
处理志愿: seq_no_yx=29, yxdm=3207, yxmc=null, zyz=102
处理志愿: seq_no_yx=29, yxdm=3207, yxmc=null, zyz=102
处理志愿: seq_no_yx=30, yxdm=4405, yxmc=null, zyz=102
处理志愿: seq_no_yx=30, yxdm=4405, yxmc=null, zyz=102
处理志愿: seq_no_yx=30, yxdm=4405, yxmc=null, zyz=102
处理志愿: seq_no_yx=30, yxdm=4405, yxmc=null, zyz=102
处理志愿: seq_no_yx=30, yxdm=4405, yxmc=null, zyz=102
处理志愿: seq_no_yx=30, yxdm=4405, yxmc=null, zyz=102
处理志愿: seq_no_yx=31, yxdm=6202, yxmc=null, zyz=102
处理志愿: seq_no_yx=31, yxdm=6202, yxmc=null, zyz=102
处理志愿: seq_no_yx=31, yxdm=6202, yxmc=null, zyz=102
处理志愿: seq_no_yx=31, yxdm=6202, yxmc=null, zyz=102
处理志愿: seq_no_yx=31, yxdm=6202, yxmc=null, zyz=102
处理志愿: seq_no_yx=31, yxdm=6202, yxmc=null, zyz=102
处理志愿: seq_no_yx=32, yxdm=0037, yxmc=null, zyz=101
处理志愿: seq_no_yx=32, yxdm=0037, yxmc=null, zyz=101
处理志愿: seq_no_yx=32, yxdm=0037, yxmc=null, zyz=101
处理志愿: seq_no_yx=32, yxdm=0037, yxmc=null, zyz=101
处理志愿: seq_no_yx=32, yxdm=0037, yxmc=null, zyz=101
处理志愿: seq_no_yx=32, yxdm=0037, yxmc=null, zyz=101
处理志愿: seq_no_yx=33, yxdm=3516, yxmc=null, zyz=103
处理志愿: seq_no_yx=33, yxdm=3516, yxmc=null, zyz=103
处理志愿: seq_no_yx=33, yxdm=3516, yxmc=null, zyz=103
处理志愿: seq_no_yx=33, yxdm=3516, yxmc=null, zyz=103
处理志愿: seq_no_yx=33, yxdm=3516, yxmc=null, zyz=103
处理志愿: seq_no_yx=33, yxdm=3516, yxmc=null, zyz=103
处理志愿: seq_no_yx=34, yxdm=1310, yxmc=null, zyz=102
处理志愿: seq_no_yx=34, yxdm=1310, yxmc=null, zyz=102
处理志愿: seq_no_yx=34, yxdm=1310, yxmc=null, zyz=102
处理志愿: seq_no_yx=34, yxdm=1310, yxmc=null, zyz=102
处理志愿: seq_no_yx=34, yxdm=1310, yxmc=null, zyz=102
处理志愿: seq_no_yx=34, yxdm=1310, yxmc=null, zyz=102
处理志愿: seq_no_yx=35, yxdm=2117, yxmc=null, zyz=101
处理志愿: seq_no_yx=35, yxdm=2117, yxmc=null, zyz=101
处理志愿: seq_no_yx=35, yxdm=2117, yxmc=null, zyz=101
处理志愿: seq_no_yx=35, yxdm=2117, yxmc=null, zyz=101
处理志愿: seq_no_yx=35, yxdm=2117, yxmc=null, zyz=101
处理志愿: seq_no_yx=35, yxdm=2117, yxmc=null, zyz=101
处理志愿: seq_no_yx=36, yxdm=4306, yxmc=null, zyz=102
处理志愿: seq_no_yx=36, yxdm=4306, yxmc=null, zyz=102
处理志愿: seq_no_yx=36, yxdm=4306, yxmc=null, zyz=102
处理志愿: seq_no_yx=36, yxdm=4306, yxmc=null, zyz=102
处理志愿: seq_no_yx=36, yxdm=4306, yxmc=null, zyz=102
处理志愿: seq_no_yx=36, yxdm=4306, yxmc=null, zyz=102
处理志愿: seq_no_yx=37, yxdm=0009, yxmc=null, zyz=106
处理志愿: seq_no_yx=37, yxdm=0009, yxmc=null, zyz=106
处理志愿: seq_no_yx=37, yxdm=0009, yxmc=null, zyz=106
处理志愿: seq_no_yx=37, yxdm=0009, yxmc=null, zyz=106
处理志愿: seq_no_yx=37, yxdm=0009, yxmc=null, zyz=106
处理志愿: seq_no_yx=37, yxdm=0009, yxmc=null, zyz=106
处理志愿: seq_no_yx=38, yxdm=1123, yxmc=null, zyz=102
处理志愿: seq_no_yx=38, yxdm=1123, yxmc=null, zyz=102
处理志愿: seq_no_yx=38, yxdm=1123, yxmc=null, zyz=102
处理志愿: seq_no_yx=38, yxdm=1123, yxmc=null, zyz=102
处理志愿: seq_no_yx=38, yxdm=1123, yxmc=null, zyz=102
处理志愿: seq_no_yx=38, yxdm=1123, yxmc=null, zyz=102
处理志愿: seq_no_yx=39, yxdm=0056, yxmc=null, zyz=110
处理志愿: seq_no_yx=39, yxdm=0056, yxmc=null, zyz=110
处理志愿: seq_no_yx=39, yxdm=0056, yxmc=null, zyz=110
处理志愿: seq_no_yx=39, yxdm=0056, yxmc=null, zyz=110
处理志愿: seq_no_yx=39, yxdm=0056, yxmc=null, zyz=110
处理志愿: seq_no_yx=39, yxdm=0056, yxmc=null, zyz=110
处理志愿: seq_no_yx=40, yxdm=0062, yxmc=null, zyz=102
处理志愿: seq_no_yx=40, yxdm=0062, yxmc=null, zyz=102
处理志愿: seq_no_yx=40, yxdm=0062, yxmc=null, zyz=102
处理志愿: seq_no_yx=40, yxdm=0062, yxmc=null, zyz=102
处理志愿: seq_no_yx=40, yxdm=0062, yxmc=null, zyz=102
处理志愿: seq_no_yx=40, yxdm=0062, yxmc=null, zyz=102
处理志愿: seq_no_yx=41, yxdm=4239, yxmc=null, zyz=101
处理志愿: seq_no_yx=41, yxdm=4239, yxmc=null, zyz=101
处理志愿: seq_no_yx=41, yxdm=4239, yxmc=null, zyz=101
处理志愿: seq_no_yx=41, yxdm=4239, yxmc=null, zyz=101
处理志愿: seq_no_yx=42, yxdm=4239, yxmc=null, zyz=102
处理志愿: seq_no_yx=42, yxdm=4239, yxmc=null, zyz=102
处理志愿: seq_no_yx=42, yxdm=4239, yxmc=null, zyz=102
处理志愿: seq_no_yx=42, yxdm=4239, yxmc=null, zyz=102
处理志愿: seq_no_yx=42, yxdm=4239, yxmc=null, zyz=102
处理志愿: seq_no_yx=42, yxdm=4239, yxmc=null, zyz=102
处理志愿: seq_no_yx=43, yxdm=0064, yxmc=null, zyz=103
处理志愿: seq_no_yx=43, yxdm=0064, yxmc=null, zyz=103
处理志愿: seq_no_yx=43, yxdm=0064, yxmc=null, zyz=103
处理志愿: seq_no_yx=43, yxdm=0064, yxmc=null, zyz=103
处理志愿: seq_no_yx=43, yxdm=0064, yxmc=null, zyz=103
处理志愿: seq_no_yx=43, yxdm=0064, yxmc=null, zyz=103
处理志愿: seq_no_yx=44, yxdm=3304, yxmc=null, zyz=102
处理志愿: seq_no_yx=44, yxdm=3304, yxmc=null, zyz=102
处理志愿: seq_no_yx=44, yxdm=3304, yxmc=null, zyz=102
处理志愿: seq_no_yx=44, yxdm=3304, yxmc=null, zyz=102
处理志愿: seq_no_yx=44, yxdm=3304, yxmc=null, zyz=102
处理志愿: seq_no_yx=44, yxdm=3304, yxmc=null, zyz=102
处理志愿: seq_no_yx=45, yxdm=6301, yxmc=null, zyz=105
处理志愿: seq_no_yx=45, yxdm=6301, yxmc=null, zyz=105
处理志愿: seq_no_yx=45, yxdm=6301, yxmc=null, zyz=105
处理志愿: seq_no_yx=45, yxdm=6301, yxmc=null, zyz=105
处理志愿: seq_no_yx=45, yxdm=6301, yxmc=null, zyz=105
处理志愿: seq_no_yx=45, yxdm=6301, yxmc=null, zyz=105
MAP_XX构建完成，包含 45 个院校序号分组
=== 调试日志3：提取院校和专业组信息 ===
处理分组: yxdm=0048, zyz=101
处理分组: yxdm=3389, zyz=101
处理分组: yxdm=4508, zyz=104
处理分组: yxdm=2308, zyz=104
处理分组: yxdm=4437, zyz=101
处理分组: yxdm=5102, zyz=108
处理分组: yxdm=3117, zyz=104
处理分组: yxdm=3620, zyz=101
处理分组: yxdm=5201, zyz=107
处理分组: yxdm=0042, zyz=107
处理分组: yxdm=9227, zyz=102
处理分组: yxdm=4605, zyz=104
处理分组: yxdm=4617, zyz=102
处理分组: yxdm=6101, zyz=102
处理分组: yxdm=5201, zyz=106
处理分组: yxdm=4301, zyz=102
处理分组: yxdm=0023, zyz=102
处理分组: yxdm=3620, zyz=103
处理分组: yxdm=3618, zyz=101
处理分组: yxdm=4501, zyz=102
处理分组: yxdm=0056, zyz=108
处理分组: yxdm=4501, zyz=101
处理分组: yxdm=0009, zyz=105
处理分组: yxdm=0058, zyz=105
处理分组: yxdm=6501, zyz=102
处理分组: yxdm=3921, zyz=101
处理分组: yxdm=2104, zyz=108
处理分组: yxdm=4501, zyz=103
处理分组: yxdm=3207, zyz=102
处理分组: yxdm=4405, zyz=102
处理分组: yxdm=6202, zyz=102
处理分组: yxdm=0037, zyz=101
处理分组: yxdm=3516, zyz=103
处理分组: yxdm=1310, zyz=102
处理分组: yxdm=2117, zyz=101
处理分组: yxdm=4306, zyz=102
处理分组: yxdm=0009, zyz=106
处理分组: yxdm=1123, zyz=102
处理分组: yxdm=0056, zyz=110
处理分组: yxdm=0062, zyz=102
处理分组: yxdm=4239, zyz=101
处理分组: yxdm=4239, zyz=102
处理分组: yxdm=0064, zyz=103
处理分组: yxdm=3304, zyz=102
处理分组: yxdm=6301, zyz=105
yxdmSet.size(): 38, 内容: [9227, 5102, 5201, 3516, 4508, 0048, 3117, 2104, 1310, 0023, 4301, 6501, 4501, 3618, 4405, 4306, 2308, 4605, 0009, 6101, 6202, 6301, 0042, 0064, 0062, 4617, 3207, 0037, 2117, 3304, 0058, 3389, 3620, 1123, 0056, 4437, 4239, 3921]
zyzSet.size(): 9, 内容: [110, 101, 102, 103, 104, 105, 106, 107, 108]
userGroupSet.size(): 45
=== 调试日志4：查询计划数据 ===
查询参数: LATEST_JH_YEAR=2025, provinceTableName=S5_SC, xkCode=1
查询参数: pc=本科批B段, pc_code=本科
SELECT * FROM S5_SC_jh_2025 x WHERE x.pc = ? and pc_code = ? and xk_code like ? and x.yxdm in ('9227','5102','5201','3516','4508','0048','3117','2104','1310','0023','4301','6501','4501','3618','4405','4306','2308','4605','0009','6101','6202','6301','0042','0064','0062','4617','3207','0037','2117','3304','0058','3389','3620','1123','0056','4437','4239','3921') and x.zyz in ('110','101','102','103','104','105','106','107','108') ORDER BY x.zdfwc ASC
查询到的计划数据数量: 1073
处理计划数据: yxdm=0058, zyz=102, qsf_a=674, qsf_b=677, qsf_c=673
处理计划数据: yxdm=0058, zyz=102, qsf_a=666, qsf_b=668, qsf_c=665
处理计划数据: yxdm=0058, zyz=102, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0058, zyz=102, qsf_a=664, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0058, zyz=102, qsf_a=664, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0058, zyz=102, qsf_a=664, qsf_b=664, qsf_c=663
处理计划数据: yxdm=0058, zyz=102, qsf_a=665, qsf_b=0, qsf_c=664
处理计划数据: yxdm=0058, zyz=102, qsf_a=665, qsf_b=664, qsf_c=660
处理计划数据: yxdm=0058, zyz=102, qsf_a=667, qsf_b=662, qsf_c=659
处理计划数据: yxdm=0058, zyz=102, qsf_a=664, qsf_b=663, qsf_c=659
处理计划数据: yxdm=0058, zyz=102, qsf_a=664, qsf_b=0, qsf_c=658
处理计划数据: yxdm=0058, zyz=102, qsf_a=664, qsf_b=0, qsf_c=657
处理计划数据: yxdm=0058, zyz=103, qsf_a=660, qsf_b=659, qsf_c=655
处理计划数据: yxdm=0058, zyz=102, qsf_a=664, qsf_b=0, qsf_c=658
处理计划数据: yxdm=0058, zyz=102, qsf_a=0, qsf_b=0, qsf_c=657
处理计划数据: yxdm=0058, zyz=104, qsf_a=650, qsf_b=0, qsf_c=664
处理计划数据: yxdm=0058, zyz=104, qsf_a=649, qsf_b=0, qsf_c=665
处理计划数据: yxdm=0058, zyz=104, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0058, zyz=104, qsf_a=647, qsf_b=671, qsf_c=656
处理计划数据: yxdm=0058, zyz=104, qsf_a=649, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0042, zyz=101, qsf_a=653, qsf_b=654, qsf_c=649
处理计划数据: yxdm=0058, zyz=104, qsf_a=653, qsf_b=0, qsf_c=640
处理计划数据: yxdm=0058, zyz=104, qsf_a=646, qsf_b=645, qsf_c=657
处理计划数据: yxdm=0058, zyz=103, qsf_a=645, qsf_b=654, qsf_c=651
处理计划数据: yxdm=0058, zyz=101, qsf_a=645, qsf_b=0, qsf_c=0
处理计划数据: yxdm=2117, zyz=101, qsf_a=642, qsf_b=642, qsf_c=650
处理计划数据: yxdm=0042, zyz=103, qsf_a=640, qsf_b=643, qsf_c=648
处理计划数据: yxdm=0042, zyz=105, qsf_a=647, qsf_b=635, qsf_c=0
处理计划数据: yxdm=9227, zyz=101, qsf_a=638, qsf_b=641, qsf_c=667
处理计划数据: yxdm=0042, zyz=105, qsf_a=651, qsf_b=646, qsf_c=658
处理计划数据: yxdm=0042, zyz=101, qsf_a=637, qsf_b=650, qsf_c=641
处理计划数据: yxdm=0042, zyz=103, qsf_a=639, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0058, zyz=103, qsf_a=639, qsf_b=639, qsf_c=640
处理计划数据: yxdm=0058, zyz=108, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0042, zyz=102, qsf_a=639, qsf_b=641, qsf_c=638
处理计划数据: yxdm=0058, zyz=103, qsf_a=637, qsf_b=635, qsf_c=0
处理计划数据: yxdm=0048, zyz=102, qsf_a=639, qsf_b=635, qsf_c=633
处理计划数据: yxdm=0058, zyz=103, qsf_a=637, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0058, zyz=103, qsf_a=638, qsf_b=637, qsf_c=638
处理计划数据: yxdm=0042, zyz=103, qsf_a=632, qsf_b=641, qsf_c=638
处理计划数据: yxdm=2308, zyz=104, qsf_a=633, qsf_b=637, qsf_c=639
处理计划数据: yxdm=0042, zyz=101, qsf_a=632, qsf_b=639, qsf_c=640
处理计划数据: yxdm=0042, zyz=103, qsf_a=630, qsf_b=641, qsf_c=638
处理计划数据: yxdm=2117, zyz=101, qsf_a=627, qsf_b=639, qsf_c=643
处理计划数据: yxdm=0042, zyz=103, qsf_a=626, qsf_b=642, qsf_c=640
处理计划数据: yxdm=0042, zyz=102, qsf_a=630, qsf_b=639, qsf_c=635
处理计划数据: yxdm=0042, zyz=105, qsf_a=625, qsf_b=640, qsf_c=650
处理计划数据: yxdm=3921, zyz=101, qsf_a=638, qsf_b=635, qsf_c=597
处理计划数据: yxdm=2308, zyz=104, qsf_a=627, qsf_b=629, qsf_c=635
处理计划数据: yxdm=2117, zyz=101, qsf_a=619, qsf_b=631, qsf_c=637
处理计划数据: yxdm=0042, zyz=102, qsf_a=619, qsf_b=636, qsf_c=634
处理计划数据: yxdm=0042, zyz=101, qsf_a=617, qsf_b=637, qsf_c=637
处理计划数据: yxdm=0042, zyz=104, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0042, zyz=104, qsf_a=617, qsf_b=643, qsf_c=651
处理计划数据: yxdm=2308, zyz=104, qsf_a=619, qsf_b=631, qsf_c=633
处理计划数据: yxdm=3620, zyz=106, qsf_a=624, qsf_b=624, qsf_c=630
处理计划数据: yxdm=0048, zyz=102, qsf_a=620, qsf_b=627, qsf_c=631
处理计划数据: yxdm=0009, zyz=101, qsf_a=625, qsf_b=624, qsf_c=0
处理计划数据: yxdm=0048, zyz=102, qsf_a=623, qsf_b=627, qsf_c=619
处理计划数据: yxdm=6202, zyz=102, qsf_a=624, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0009, zyz=106, qsf_a=625, qsf_b=621, qsf_c=0
处理计划数据: yxdm=0042, zyz=105, qsf_a=604, qsf_b=615, qsf_c=654
处理计划数据: yxdm=0056, zyz=102, qsf_a=615, qsf_b=618, qsf_c=629
处理计划数据: yxdm=0048, zyz=101, qsf_a=615, qsf_b=629, qsf_c=630
处理计划数据: yxdm=3516, zyz=103, qsf_a=620, qsf_b=616, qsf_c=623
处理计划数据: yxdm=9227, zyz=101, qsf_a=605, qsf_b=638, qsf_c=651
处理计划数据: yxdm=0058, zyz=106, qsf_a=617, qsf_b=617, qsf_c=624
处理计划数据: yxdm=0058, zyz=107, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0048, zyz=102, qsf_a=616, qsf_b=623, qsf_c=612
处理计划数据: yxdm=0064, zyz=102, qsf_a=616, qsf_b=618, qsf_c=620
处理计划数据: yxdm=0062, zyz=102, qsf_a=618, qsf_b=619, qsf_c=616
处理计划数据: yxdm=2117, zyz=101, qsf_a=621, qsf_b=608, qsf_c=629
处理计划数据: yxdm=0064, zyz=102, qsf_a=617, qsf_b=616, qsf_c=612
处理计划数据: yxdm=0056, zyz=107, qsf_a=616, qsf_b=613, qsf_c=622
处理计划数据: yxdm=0056, zyz=102, qsf_a=611, qsf_b=611, qsf_c=630
处理计划数据: yxdm=0062, zyz=102, qsf_a=619, qsf_b=619, qsf_c=614
处理计划数据: yxdm=3620, zyz=104, qsf_a=618, qsf_b=618, qsf_c=609
处理计划数据: yxdm=0056, zyz=101, qsf_a=616, qsf_b=615, qsf_c=614
处理计划数据: yxdm=0048, zyz=101, qsf_a=612, qsf_b=616, qsf_c=616
处理计划数据: yxdm=0056, zyz=101, qsf_a=616, qsf_b=614, qsf_c=0
处理计划数据: yxdm=0056, zyz=106, qsf_a=618, qsf_b=615, qsf_c=614
处理计划数据: yxdm=0042, zyz=104, qsf_a=608, qsf_b=636, qsf_c=0
处理计划数据: yxdm=3304, zyz=102, qsf_a=631, qsf_b=636, qsf_c=599
处理计划数据: yxdm=0048, zyz=103, qsf_a=611, qsf_b=616, qsf_c=617
处理计划数据: yxdm=0048, zyz=103, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0048, zyz=103, qsf_a=611, qsf_b=616, qsf_c=617
处理计划数据: yxdm=3620, zyz=102, qsf_a=612, qsf_b=615, qsf_c=614
处理计划数据: yxdm=0048, zyz=102, qsf_a=613, qsf_b=615, qsf_c=612
处理计划数据: yxdm=0056, zyz=101, qsf_a=0, qsf_b=0, qsf_c=606
处理计划数据: yxdm=0058, zyz=106, qsf_a=612, qsf_b=611, qsf_c=622
处理计划数据: yxdm=0048, zyz=102, qsf_a=607, qsf_b=616, qsf_c=621
处理计划数据: yxdm=3620, zyz=104, qsf_a=611, qsf_b=611, qsf_c=609
处理计划数据: yxdm=3618, zyz=102, qsf_a=0, qsf_b=0, qsf_c=612
处理计划数据: yxdm=3618, zyz=107, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3618, zyz=108, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0056, zyz=102, qsf_a=608, qsf_b=615, qsf_c=613
处理计划数据: yxdm=0042, zyz=102, qsf_a=590, qsf_b=637, qsf_c=635
处理计划数据: yxdm=3921, zyz=101, qsf_a=613, qsf_b=607, qsf_c=612
处理计划数据: yxdm=2117, zyz=101, qsf_a=611, qsf_b=605, qsf_c=632
处理计划数据: yxdm=0048, zyz=103, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0048, zyz=103, qsf_a=606, qsf_b=618, qsf_c=616
处理计划数据: yxdm=0064, zyz=102, qsf_a=610, qsf_b=615, qsf_c=612
处理计划数据: yxdm=3620, zyz=102, qsf_a=608, qsf_b=615, qsf_c=614
处理计划数据: yxdm=0064, zyz=102, qsf_a=611, qsf_b=614, qsf_c=612
处理计划数据: yxdm=0064, zyz=102, qsf_a=607, qsf_b=617, qsf_c=613
处理计划数据: yxdm=3620, zyz=104, qsf_a=611, qsf_b=612, qsf_c=612
处理计划数据: yxdm=0064, zyz=102, qsf_a=606, qsf_b=615, qsf_c=614
处理计划数据: yxdm=0056, zyz=103, qsf_a=610, qsf_b=611, qsf_c=611
处理计划数据: yxdm=0062, zyz=102, qsf_a=608, qsf_b=618, qsf_c=609
处理计划数据: yxdm=0056, zyz=103, qsf_a=608, qsf_b=608, qsf_c=612
处理计划数据: yxdm=0048, zyz=101, qsf_a=603, qsf_b=615, qsf_c=616
处理计划数据: yxdm=0056, zyz=107, qsf_a=610, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3620, zyz=102, qsf_a=611, qsf_b=611, qsf_c=610
处理计划数据: yxdm=0056, zyz=106, qsf_a=611, qsf_b=610, qsf_c=609
处理计划数据: yxdm=3620, zyz=102, qsf_a=608, qsf_b=614, qsf_c=610
处理计划数据: yxdm=0056, zyz=107, qsf_a=610, qsf_b=609, qsf_c=607
处理计划数据: yxdm=9227, zyz=102, qsf_a=601, qsf_b=630, qsf_c=630
处理计划数据: yxdm=0042, zyz=102, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0042, zyz=104, qsf_a=586, qsf_b=637, qsf_c=635
处理计划数据: yxdm=0056, zyz=102, qsf_a=609, qsf_b=611, qsf_c=0
处理计划数据: yxdm=0056, zyz=107, qsf_a=609, qsf_b=605, qsf_c=612
处理计划数据: yxdm=0048, zyz=101, qsf_a=605, qsf_b=613, qsf_c=612
处理计划数据: yxdm=0064, zyz=102, qsf_a=608, qsf_b=611, qsf_c=0
处理计划数据: yxdm=0056, zyz=101, qsf_a=609, qsf_b=606, qsf_c=618
处理计划数据: yxdm=5201, zyz=104, qsf_a=610, qsf_b=606, qsf_c=611
处理计划数据: yxdm=0037, zyz=102, qsf_a=608, qsf_b=610, qsf_c=611
处理计划数据: yxdm=0009, zyz=104, qsf_a=607, qsf_b=611, qsf_c=605
处理计划数据: yxdm=0009, zyz=104, qsf_a=607, qsf_b=611, qsf_c=605
处理计划数据: yxdm=0037, zyz=102, qsf_a=608, qsf_b=610, qsf_c=612
处理计划数据: yxdm=9227, zyz=102, qsf_a=609, qsf_b=599, qsf_c=635
处理计划数据: yxdm=0064, zyz=102, qsf_a=607, qsf_b=608, qsf_c=611
处理计划数据: yxdm=0056, zyz=106, qsf_a=611, qsf_b=603, qsf_c=0
处理计划数据: yxdm=0064, zyz=102, qsf_a=604, qsf_b=612, qsf_c=612
处理计划数据: yxdm=3304, zyz=102, qsf_a=615, qsf_b=625, qsf_c=598
处理计划数据: yxdm=0009, zyz=102, qsf_a=608, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0064, zyz=102, qsf_a=608, qsf_b=0, qsf_c=0
处理计划数据: yxdm=5201, zyz=104, qsf_a=608, qsf_b=0, qsf_c=0
处理计划数据: yxdm=5201, zyz=104, qsf_a=609, qsf_b=605, qsf_c=610
处理计划数据: yxdm=4501, zyz=102, qsf_a=610, qsf_b=611, qsf_c=606
处理计划数据: yxdm=0056, zyz=107, qsf_a=609, qsf_b=605, qsf_c=607
处理计划数据: yxdm=0009, zyz=104, qsf_a=607, qsf_b=610, qsf_c=603
处理计划数据: yxdm=0037, zyz=102, qsf_a=609, qsf_b=609, qsf_c=607
处理计划数据: yxdm=0009, zyz=104, qsf_a=609, qsf_b=610, qsf_c=605
处理计划数据: yxdm=4605, zyz=104, qsf_a=608, qsf_b=609, qsf_c=608
处理计划数据: yxdm=0062, zyz=102, qsf_a=605, qsf_b=615, qsf_c=607
处理计划数据: yxdm=4306, zyz=102, qsf_a=607, qsf_b=615, qsf_c=607
处理计划数据: yxdm=0056, zyz=106, qsf_a=607, qsf_b=605, qsf_c=608
处理计划数据: yxdm=3620, zyz=103, qsf_a=606, qsf_b=611, qsf_c=608
处理计划数据: yxdm=0037, zyz=102, qsf_a=607, qsf_b=607, qsf_c=607
处理计划数据: yxdm=6301, zyz=105, qsf_a=610, qsf_b=608, qsf_c=605
处理计划数据: yxdm=0056, zyz=103, qsf_a=607, qsf_b=604, qsf_c=607
处理计划数据: yxdm=0064, zyz=102, qsf_a=606, qsf_b=608, qsf_c=611
处理计划数据: yxdm=2117, zyz=101, qsf_a=607, qsf_b=603, qsf_c=615
处理计划数据: yxdm=0048, zyz=101, qsf_a=602, qsf_b=612, qsf_c=611
处理计划数据: yxdm=2104, zyz=102, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4301, zyz=102, qsf_a=610, qsf_b=610, qsf_c=597
处理计划数据: yxdm=0064, zyz=102, qsf_a=607, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0048, zyz=102, qsf_a=603, qsf_b=612, qsf_c=608
处理计划数据: yxdm=3620, zyz=103, qsf_a=605, qsf_b=611, qsf_c=608
处理计划数据: yxdm=0056, zyz=103, qsf_a=607, qsf_b=603, qsf_c=608
处理计划数据: yxdm=0037, zyz=102, qsf_a=606, qsf_b=610, qsf_c=605
处理计划数据: yxdm=0056, zyz=106, qsf_a=608, qsf_b=602, qsf_c=607
处理计划数据: yxdm=0062, zyz=102, qsf_a=602, qsf_b=616, qsf_c=605
处理计划数据: yxdm=4501, zyz=102, qsf_a=606, qsf_b=609, qsf_c=609
处理计划数据: yxdm=0037, zyz=102, qsf_a=607, qsf_b=605, qsf_c=608
处理计划数据: yxdm=3620, zyz=103, qsf_a=606, qsf_b=610, qsf_c=607
处理计划数据: yxdm=0056, zyz=107, qsf_a=607, qsf_b=602, qsf_c=607
处理计划数据: yxdm=0009, zyz=106, qsf_a=606, qsf_b=608, qsf_c=0
处理计划数据: yxdm=3620, zyz=103, qsf_a=602, qsf_b=611, qsf_c=611
处理计划数据: yxdm=2308, zyz=104, qsf_a=607, qsf_b=605, qsf_c=0
处理计划数据: yxdm=4306, zyz=102, qsf_a=608, qsf_b=608, qsf_c=606
处理计划数据: yxdm=0056, zyz=106, qsf_a=607, qsf_b=602, qsf_c=606
处理计划数据: yxdm=0009, zyz=105, qsf_a=601, qsf_b=611, qsf_c=609
处理计划数据: yxdm=3618, zyz=101, qsf_a=598, qsf_b=608, qsf_c=615
处理计划数据: yxdm=0062, zyz=102, qsf_a=602, qsf_b=617, qsf_c=0
处理计划数据: yxdm=0064, zyz=101, qsf_a=606, qsf_b=606, qsf_c=606
处理计划数据: yxdm=5201, zyz=104, qsf_a=609, qsf_b=600, qsf_c=608
处理计划数据: yxdm=5201, zyz=108, qsf_a=608, qsf_b=602, qsf_c=0
处理计划数据: yxdm=0009, zyz=104, qsf_a=606, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0023, zyz=102, qsf_a=606, qsf_b=0, qsf_c=0
处理计划数据: yxdm=2117, zyz=101, qsf_a=606, qsf_b=599, qsf_c=615
处理计划数据: yxdm=0064, zyz=102, qsf_a=603, qsf_b=606, qsf_c=609
处理计划数据: yxdm=0023, zyz=102, qsf_a=609, qsf_b=606, qsf_c=604
处理计划数据: yxdm=0037, zyz=101, qsf_a=602, qsf_b=607, qsf_c=607
处理计划数据: yxdm=0037, zyz=101, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3620, zyz=104, qsf_a=598, qsf_b=611, qsf_c=611
处理计划数据: yxdm=0064, zyz=102, qsf_a=605, qsf_b=607, qsf_c=604
处理计划数据: yxdm=6101, zyz=102, qsf_a=608, qsf_b=604, qsf_c=598
处理计划数据: yxdm=4605, zyz=104, qsf_a=607, qsf_b=608, qsf_c=603
处理计划数据: yxdm=3620, zyz=103, qsf_a=605, qsf_b=609, qsf_c=603
处理计划数据: yxdm=0062, zyz=102, qsf_a=602, qsf_b=611, qsf_c=606
处理计划数据: yxdm=3620, zyz=103, qsf_a=599, qsf_b=611, qsf_c=611
处理计划数据: yxdm=0064, zyz=101, qsf_a=604, qsf_b=605, qsf_c=612
处理计划数据: yxdm=0064, zyz=102, qsf_a=608, qsf_b=603, qsf_c=602
处理计划数据: yxdm=0037, zyz=102, qsf_a=603, qsf_b=607, qsf_c=608
处理计划数据: yxdm=0009, zyz=104, qsf_a=605, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0064, zyz=102, qsf_a=605, qsf_b=0, qsf_c=0
处理计划数据: yxdm=5201, zyz=101, qsf_a=605, qsf_b=0, qsf_c=0
处理计划数据: yxdm=5201, zyz=105, qsf_a=605, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0009, zyz=102, qsf_a=608, qsf_b=605, qsf_c=594
处理计划数据: yxdm=4605, zyz=104, qsf_a=605, qsf_b=607, qsf_c=603
处理计划数据: yxdm=0009, zyz=102, qsf_a=600, qsf_b=605, qsf_c=607
处理计划数据: yxdm=5201, zyz=106, qsf_a=607, qsf_b=599, qsf_c=608
处理计划数据: yxdm=0037, zyz=102, qsf_a=602, qsf_b=607, qsf_c=607
处理计划数据: yxdm=0009, zyz=101, qsf_a=594, qsf_b=610, qsf_c=610
处理计划数据: yxdm=0064, zyz=101, qsf_a=602, qsf_b=605, qsf_c=610
处理计划数据: yxdm=0064, zyz=101, qsf_a=602, qsf_b=605, qsf_c=610
处理计划数据: yxdm=4501, zyz=102, qsf_a=602, qsf_b=608, qsf_c=606
处理计划数据: yxdm=0042, zyz=108, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3620, zyz=103, qsf_a=603, qsf_b=606, qsf_c=605
处理计划数据: yxdm=3620, zyz=103, qsf_a=603, qsf_b=608, qsf_c=603
处理计划数据: yxdm=2104, zyz=101, qsf_a=602, qsf_b=600, qsf_c=611
处理计划数据: yxdm=0064, zyz=102, qsf_a=601, qsf_b=608, qsf_c=609
处理计划数据: yxdm=0064, zyz=102, qsf_a=604, qsf_b=602, qsf_c=608
处理计划数据: yxdm=0037, zyz=101, qsf_a=603, qsf_b=604, qsf_c=606
处理计划数据: yxdm=0062, zyz=102, qsf_a=600, qsf_b=616, qsf_c=605
处理计划数据: yxdm=0062, zyz=102, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=5201, zyz=104, qsf_a=606, qsf_b=596, qsf_c=609
处理计划数据: yxdm=6202, zyz=102, qsf_a=610, qsf_b=605, qsf_c=602
处理计划数据: yxdm=0064, zyz=102, qsf_a=604, qsf_b=604, qsf_c=605
处理计划数据: yxdm=0009, zyz=102, qsf_a=602, qsf_b=606, qsf_c=602
处理计划数据: yxdm=0037, zyz=101, qsf_a=598, qsf_b=605, qsf_c=611
处理计划数据: yxdm=3516, zyz=103, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3516, zyz=103, qsf_a=604, qsf_b=596, qsf_c=602
处理计划数据: yxdm=0009, zyz=104, qsf_a=604, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6202, zyz=102, qsf_a=604, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0064, zyz=102, qsf_a=602, qsf_b=605, qsf_c=609
处理计划数据: yxdm=0064, zyz=102, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0009, zyz=103, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0009, zyz=103, qsf_a=601, qsf_b=609, qsf_c=600
处理计划数据: yxdm=0042, zyz=106, qsf_a=0, qsf_b=0, qsf_c=604
处理计划数据: yxdm=0042, zyz=106, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0042, zyz=106, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4605, zyz=104, qsf_a=606, qsf_b=605, qsf_c=602
处理计划数据: yxdm=0037, zyz=102, qsf_a=604, qsf_b=602, qsf_c=605
处理计划数据: yxdm=3620, zyz=105, qsf_a=603, qsf_b=605, qsf_c=602
处理计划数据: yxdm=0064, zyz=102, qsf_a=602, qsf_b=601, qsf_c=607
处理计划数据: yxdm=0037, zyz=102, qsf_a=603, qsf_b=605, qsf_c=606
处理计划数据: yxdm=0037, zyz=102, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0064, zyz=102, qsf_a=604, qsf_b=602, qsf_c=607
处理计划数据: yxdm=0064, zyz=102, qsf_a=601, qsf_b=604, qsf_c=609
处理计划数据: yxdm=4405, zyz=102, qsf_a=603, qsf_b=603, qsf_c=605
处理计划数据: yxdm=3117, zyz=101, qsf_a=596, qsf_b=599, qsf_c=614
处理计划数据: yxdm=0009, zyz=105, qsf_a=602, qsf_b=606, qsf_c=602
处理计划数据: yxdm=4605, zyz=104, qsf_a=604, qsf_b=603, qsf_c=602
处理计划数据: yxdm=3618, zyz=105, qsf_a=609, qsf_b=600, qsf_c=598
处理计划数据: yxdm=4605, zyz=105, qsf_a=605, qsf_b=604, qsf_c=602
处理计划数据: yxdm=0064, zyz=101, qsf_a=603, qsf_b=602, qsf_c=611
处理计划数据: yxdm=0064, zyz=101, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4405, zyz=102, qsf_a=604, qsf_b=603, qsf_c=603
处理计划数据: yxdm=0064, zyz=102, qsf_a=602, qsf_b=604, qsf_c=605
处理计划数据: yxdm=1310, zyz=102, qsf_a=603, qsf_b=602, qsf_c=605
处理计划数据: yxdm=0023, zyz=102, qsf_a=606, qsf_b=603, qsf_c=602
处理计划数据: yxdm=4301, zyz=102, qsf_a=602, qsf_b=606, qsf_c=602
处理计划数据: yxdm=4605, zyz=104, qsf_a=603, qsf_b=604, qsf_c=602
处理计划数据: yxdm=0064, zyz=102, qsf_a=602, qsf_b=603, qsf_c=604
处理计划数据: yxdm=0064, zyz=101, qsf_a=603, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4405, zyz=102, qsf_a=603, qsf_b=0, qsf_c=0
处理计划数据: yxdm=5201, zyz=101, qsf_a=603, qsf_b=0, qsf_c=0
处理计划数据: yxdm=5201, zyz=101, qsf_a=603, qsf_b=0, qsf_c=0
处理计划数据: yxdm=5201, zyz=105, qsf_a=603, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3618, zyz=102, qsf_a=0, qsf_b=0, qsf_c=603
处理计划数据: yxdm=4605, zyz=104, qsf_a=604, qsf_b=604, qsf_c=601
处理计划数据: yxdm=4605, zyz=104, qsf_a=603, qsf_b=605, qsf_c=602
处理计划数据: yxdm=0064, zyz=103, qsf_a=605, qsf_b=600, qsf_c=605
处理计划数据: yxdm=0064, zyz=103, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3620, zyz=103, qsf_a=600, qsf_b=607, qsf_c=604
处理计划数据: yxdm=0064, zyz=102, qsf_a=602, qsf_b=603, qsf_c=605
处理计划数据: yxdm=0062, zyz=101, qsf_a=600, qsf_b=605, qsf_c=601
处理计划数据: yxdm=0037, zyz=102, qsf_a=603, qsf_b=601, qsf_c=603
处理计划数据: yxdm=0048, zyz=101, qsf_a=599, qsf_b=608, qsf_c=602
处理计划数据: yxdm=2308, zyz=104, qsf_a=584, qsf_b=609, qsf_c=633
处理计划数据: yxdm=6101, zyz=102, qsf_a=603, qsf_b=601, qsf_c=0
处理计划数据: yxdm=0064, zyz=102, qsf_a=602, qsf_b=602, qsf_c=604
处理计划数据: yxdm=0009, zyz=107, qsf_a=597, qsf_b=605, qsf_c=606
处理计划数据: yxdm=0037, zyz=101, qsf_a=600, qsf_b=603, qsf_c=605
处理计划数据: yxdm=0037, zyz=102, qsf_a=600, qsf_b=602, qsf_c=606
处理计划数据: yxdm=0037, zyz=101, qsf_a=600, qsf_b=599, qsf_c=606
处理计划数据: yxdm=3620, zyz=104, qsf_a=600, qsf_b=606, qsf_c=603
处理计划数据: yxdm=3620, zyz=103, qsf_a=600, qsf_b=606, qsf_c=602
处理计划数据: yxdm=0064, zyz=102, qsf_a=602, qsf_b=603, qsf_c=603
处理计划数据: yxdm=4306, zyz=102, qsf_a=0, qsf_b=0, qsf_c=602
处理计划数据: yxdm=4501, zyz=102, qsf_a=600, qsf_b=605, qsf_c=605
处理计划数据: yxdm=0037, zyz=102, qsf_a=602, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0023, zyz=102, qsf_a=603, qsf_b=603, qsf_c=602
处理计划数据: yxdm=2104, zyz=108, qsf_a=602, qsf_b=603, qsf_c=602
处理计划数据: yxdm=6501, zyz=102, qsf_a=606, qsf_b=602, qsf_c=598
处理计划数据: yxdm=1123, zyz=102, qsf_a=604, qsf_b=606, qsf_c=600
处理计划数据: yxdm=0023, zyz=102, qsf_a=603, qsf_b=599, qsf_c=603
处理计划数据: yxdm=0023, zyz=102, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=1123, zyz=102, qsf_a=604, qsf_b=602, qsf_c=601
处理计划数据: yxdm=5201, zyz=105, qsf_a=605, qsf_b=596, qsf_c=603
处理计划数据: yxdm=3207, zyz=102, qsf_a=604, qsf_b=602, qsf_c=602
处理计划数据: yxdm=0064, zyz=102, qsf_a=601, qsf_b=603, qsf_c=602
处理计划数据: yxdm=0062, zyz=102, qsf_a=592, qsf_b=613, qsf_c=605
处理计划数据: yxdm=0037, zyz=102, qsf_a=602, qsf_b=604, qsf_c=602
处理计划数据: yxdm=3304, zyz=102, qsf_a=605, qsf_b=605, qsf_c=595
处理计划数据: yxdm=4605, zyz=104, qsf_a=604, qsf_b=600, qsf_c=599
处理计划数据: yxdm=0062, zyz=102, qsf_a=599, qsf_b=605, qsf_c=601
处理计划数据: yxdm=0062, zyz=102, qsf_a=597, qsf_b=610, qsf_c=602
处理计划数据: yxdm=5201, zyz=105, qsf_a=606, qsf_b=590, qsf_c=606
处理计划数据: yxdm=0037, zyz=101, qsf_a=602, qsf_b=598, qsf_c=604
处理计划数据: yxdm=6101, zyz=102, qsf_a=606, qsf_b=603, qsf_c=598
处理计划数据: yxdm=0064, zyz=102, qsf_a=601, qsf_b=604, qsf_c=605
处理计划数据: yxdm=4306, zyz=102, qsf_a=601, qsf_b=608, qsf_c=600
处理计划数据: yxdm=5102, zyz=108, qsf_a=602, qsf_b=600, qsf_c=603
处理计划数据: yxdm=0023, zyz=101, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0023, zyz=102, qsf_a=602, qsf_b=600, qsf_c=0
处理计划数据: yxdm=4405, zyz=102, qsf_a=602, qsf_b=600, qsf_c=602
处理计划数据: yxdm=0037, zyz=102, qsf_a=598, qsf_b=608, qsf_c=602
处理计划数据: yxdm=3620, zyz=103, qsf_a=598, qsf_b=606, qsf_c=602
处理计划数据: yxdm=5201, zyz=105, qsf_a=603, qsf_b=590, qsf_c=608
处理计划数据: yxdm=6202, zyz=102, qsf_a=602, qsf_b=0, qsf_c=0
处理计划数据: yxdm=5102, zyz=107, qsf_a=602, qsf_b=601, qsf_c=602
处理计划数据: yxdm=5201, zyz=106, qsf_a=603, qsf_b=597, qsf_c=602
处理计划数据: yxdm=0009, zyz=106, qsf_a=599, qsf_b=605, qsf_c=596
处理计划数据: yxdm=4405, zyz=102, qsf_a=601, qsf_b=600, qsf_c=602
处理计划数据: yxdm=0037, zyz=102, qsf_a=600, qsf_b=601, qsf_c=603
处理计划数据: yxdm=4306, zyz=102, qsf_a=600, qsf_b=605, qsf_c=603
处理计划数据: yxdm=4306, zyz=102, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4301, zyz=102, qsf_a=599, qsf_b=605, qsf_c=605
处理计划数据: yxdm=0009, zyz=106, qsf_a=600, qsf_b=608, qsf_c=603
处理计划数据: yxdm=0037, zyz=102, qsf_a=598, qsf_b=604, qsf_c=603
处理计划数据: yxdm=0042, zyz=104, qsf_a=574, qsf_b=637, qsf_c=637
处理计划数据: yxdm=3620, zyz=101, qsf_a=589, qsf_b=613, qsf_c=611
处理计划数据: yxdm=0037, zyz=102, qsf_a=600, qsf_b=600, qsf_c=603
处理计划数据: yxdm=6101, zyz=102, qsf_a=604, qsf_b=601, qsf_c=597
处理计划数据: yxdm=4605, zyz=104, qsf_a=602, qsf_b=602, qsf_c=597
处理计划数据: yxdm=1310, zyz=102, qsf_a=602, qsf_b=600, qsf_c=602
处理计划数据: yxdm=5201, zyz=106, qsf_a=603, qsf_b=592, qsf_c=604
处理计划数据: yxdm=0037, zyz=102, qsf_a=599, qsf_b=602, qsf_c=602
处理计划数据: yxdm=3620, zyz=101, qsf_a=594, qsf_b=610, qsf_c=603
处理计划数据: yxdm=0023, zyz=102, qsf_a=602, qsf_b=601, qsf_c=600
处理计划数据: yxdm=3620, zyz=103, qsf_a=593, qsf_b=608, qsf_c=606
处理计划数据: yxdm=2308, zyz=104, qsf_a=579, qsf_b=624, qsf_c=623
处理计划数据: yxdm=0048, zyz=101, qsf_a=596, qsf_b=607, qsf_c=602
处理计划数据: yxdm=0048, zyz=102, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3618, zyz=102, qsf_a=0, qsf_b=0, qsf_c=601
处理计划数据: yxdm=3618, zyz=103, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3618, zyz=104, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3618, zyz=108, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6301, zyz=105, qsf_a=0, qsf_b=0, qsf_c=601
处理计划数据: yxdm=1123, zyz=102, qsf_a=602, qsf_b=599, qsf_c=602
处理计划数据: yxdm=0037, zyz=101, qsf_a=601, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0064, zyz=102, qsf_a=601, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3620, zyz=103, qsf_a=595, qsf_b=606, qsf_c=602
处理计划数据: yxdm=0009, zyz=105, qsf_a=594, qsf_b=605, qsf_c=601
处理计划数据: yxdm=0009, zyz=102, qsf_a=594, qsf_b=605, qsf_c=601
处理计划数据: yxdm=0009, zyz=105, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0009, zyz=106, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0009, zyz=106, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6101, zyz=102, qsf_a=603, qsf_b=601, qsf_c=597
处理计划数据: yxdm=3207, zyz=102, qsf_a=0, qsf_b=600, qsf_c=603
处理计划数据: yxdm=0037, zyz=102, qsf_a=598, qsf_b=600, qsf_c=602
处理计划数据: yxdm=0037, zyz=101, qsf_a=598, qsf_b=599, qsf_c=604
处理计划数据: yxdm=3207, zyz=102, qsf_a=601, qsf_b=599, qsf_c=601
处理计划数据: yxdm=0037, zyz=102, qsf_a=600, qsf_b=600, qsf_c=602
处理计划数据: yxdm=4605, zyz=104, qsf_a=600, qsf_b=600, qsf_c=600
处理计划数据: yxdm=0037, zyz=102, qsf_a=598, qsf_b=599, qsf_c=602
处理计划数据: yxdm=2104, zyz=102, qsf_a=598, qsf_b=604, qsf_c=602
处理计划数据: yxdm=3117, zyz=101, qsf_a=591, qsf_b=596, qsf_c=611
处理计划数据: yxdm=3117, zyz=101, qsf_a=591, qsf_b=596, qsf_c=610
处理计划数据: yxdm=2104, zyz=101, qsf_a=594, qsf_b=601, qsf_c=605
处理计划数据: yxdm=2104, zyz=103, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0023, zyz=101, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0023, zyz=102, qsf_a=599, qsf_b=601, qsf_c=601
处理计划数据: yxdm=0023, zyz=102, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4306, zyz=102, qsf_a=600, qsf_b=600, qsf_c=602
处理计划数据: yxdm=4405, zyz=101, qsf_a=600, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4405, zyz=102, qsf_a=600, qsf_b=600, qsf_c=0
处理计划数据: yxdm=2104, zyz=108, qsf_a=596, qsf_b=602, qsf_c=601
处理计划数据: yxdm=3207, zyz=102, qsf_a=601, qsf_b=600, qsf_c=597
处理计划数据: yxdm=0009, zyz=101, qsf_a=591, qsf_b=604, qsf_c=602
处理计划数据: yxdm=4306, zyz=102, qsf_a=599, qsf_b=602, qsf_c=598
处理计划数据: yxdm=6101, zyz=102, qsf_a=603, qsf_b=601, qsf_c=596
处理计划数据: yxdm=6101, zyz=102, qsf_a=602, qsf_b=600, qsf_c=595
处理计划数据: yxdm=1310, zyz=102, qsf_a=602, qsf_b=597, qsf_c=599
处理计划数据: yxdm=5201, zyz=107, qsf_a=603, qsf_b=588, qsf_c=602
处理计划数据: yxdm=5201, zyz=107, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4605, zyz=102, qsf_a=599, qsf_b=595, qsf_c=599
处理计划数据: yxdm=5201, zyz=106, qsf_a=606, qsf_b=582, qsf_c=602
处理计划数据: yxdm=3620, zyz=101, qsf_a=590, qsf_b=609, qsf_c=605
处理计划数据: yxdm=5201, zyz=101, qsf_a=603, qsf_b=586, qsf_c=602
处理计划数据: yxdm=5201, zyz=101, qsf_a=603, qsf_b=586, qsf_c=602
处理计划数据: yxdm=0042, zyz=104, qsf_a=574, qsf_b=640, qsf_c=640
处理计划数据: yxdm=3117, zyz=101, qsf_a=591, qsf_b=598, qsf_c=604
处理计划数据: yxdm=6101, zyz=102, qsf_a=602, qsf_b=600, qsf_c=594
处理计划数据: yxdm=5201, zyz=107, qsf_a=607, qsf_b=582, qsf_c=0
处理计划数据: yxdm=5102, zyz=108, qsf_a=599, qsf_b=599, qsf_c=601
处理计划数据: yxdm=5102, zyz=108, qsf_a=599, qsf_b=599, qsf_c=601
处理计划数据: yxdm=5102, zyz=108, qsf_a=599, qsf_b=599, qsf_c=601
处理计划数据: yxdm=0009, zyz=104, qsf_a=592, qsf_b=608, qsf_c=597
处理计划数据: yxdm=0023, zyz=102, qsf_a=599, qsf_b=0, qsf_c=600
处理计划数据: yxdm=3618, zyz=101, qsf_a=0, qsf_b=596, qsf_c=591
处理计划数据: yxdm=0009, zyz=105, qsf_a=595, qsf_b=606, qsf_c=602
处理计划数据: yxdm=0062, zyz=102, qsf_a=590, qsf_b=606, qsf_c=603
处理计划数据: yxdm=5201, zyz=101, qsf_a=604, qsf_b=581, qsf_c=603
处理计划数据: yxdm=4306, zyz=102, qsf_a=599, qsf_b=598, qsf_c=602
处理计划数据: yxdm=0037, zyz=101, qsf_a=598, qsf_b=599, qsf_c=0
处理计划数据: yxdm=6202, zyz=102, qsf_a=605, qsf_b=0, qsf_c=585
处理计划数据: yxdm=4306, zyz=102, qsf_a=599, qsf_b=599, qsf_c=595
处理计划数据: yxdm=5201, zyz=106, qsf_a=603, qsf_b=582, qsf_c=603
处理计划数据: yxdm=3620, zyz=103, qsf_a=589, qsf_b=607, qsf_c=605
处理计划数据: yxdm=6101, zyz=102, qsf_a=602, qsf_b=600, qsf_c=595
处理计划数据: yxdm=4301, zyz=102, qsf_a=596, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6101, zyz=102, qsf_a=602, qsf_b=600, qsf_c=594
处理计划数据: yxdm=5102, zyz=108, qsf_a=594, qsf_b=600, qsf_c=603
处理计划数据: yxdm=6101, zyz=101, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6101, zyz=101, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6101, zyz=102, qsf_a=601, qsf_b=600, qsf_c=597
处理计划数据: yxdm=4301, zyz=102, qsf_a=597, qsf_b=604, qsf_c=598
处理计划数据: yxdm=4605, zyz=104, qsf_a=600, qsf_b=600, qsf_c=592
处理计划数据: yxdm=3389, zyz=101, qsf_a=0, qsf_b=595, qsf_c=594
处理计划数据: yxdm=4501, zyz=102, qsf_a=597, qsf_b=599, qsf_c=598
处理计划数据: yxdm=0037, zyz=102, qsf_a=598, qsf_b=598, qsf_c=602
处理计划数据: yxdm=0023, zyz=101, qsf_a=597, qsf_b=600, qsf_c=598
处理计划数据: yxdm=0023, zyz=101, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6101, zyz=102, qsf_a=602, qsf_b=600, qsf_c=594
处理计划数据: yxdm=0037, zyz=103, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0037, zyz=103, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0037, zyz=104, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0037, zyz=104, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0048, zyz=101, qsf_a=589, qsf_b=607, qsf_c=607
处理计划数据: yxdm=3389, zyz=101, qsf_a=598, qsf_b=595, qsf_c=594
处理计划数据: yxdm=2308, zyz=104, qsf_a=592, qsf_b=590, qsf_c=617
处理计划数据: yxdm=4437, zyz=101, qsf_a=598, qsf_b=580, qsf_c=612
处理计划数据: yxdm=5102, zyz=108, qsf_a=594, qsf_b=600, qsf_c=603
处理计划数据: yxdm=3117, zyz=104, qsf_a=598, qsf_b=589, qsf_c=596
处理计划数据: yxdm=3620, zyz=101, qsf_a=593, qsf_b=608, qsf_c=602
处理计划数据: yxdm=5201, zyz=107, qsf_a=604, qsf_b=580, qsf_c=602
处理计划数据: yxdm=0042, zyz=107, qsf_a=0, qsf_b=0, qsf_c=598
处理计划数据: yxdm=9227, zyz=102, qsf_a=601, qsf_b=570, qsf_c=631
处理计划数据: yxdm=4605, zyz=104, qsf_a=595, qsf_b=601, qsf_c=595
处理计划数据: yxdm=4617, zyz=102, qsf_a=596, qsf_b=603, qsf_c=599
处理计划数据: yxdm=6101, zyz=102, qsf_a=601, qsf_b=599, qsf_c=594
处理计划数据: yxdm=5201, zyz=106, qsf_a=603, qsf_b=578, qsf_c=603
处理计划数据: yxdm=4301, zyz=102, qsf_a=594, qsf_b=605, qsf_c=598
处理计划数据: yxdm=0023, zyz=102, qsf_a=598, qsf_b=597, qsf_c=596
处理计划数据: yxdm=0023, zyz=102, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3620, zyz=103, qsf_a=586, qsf_b=608, qsf_c=606
处理计划数据: yxdm=3618, zyz=101, qsf_a=583, qsf_b=616, qsf_c=611
处理计划数据: yxdm=0056, zyz=108, qsf_a=598, qsf_b=598, qsf_c=0
处理计划数据: yxdm=4501, zyz=101, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4501, zyz=102, qsf_a=598, qsf_b=598, qsf_c=0
处理计划数据: yxdm=0009, zyz=105, qsf_a=591, qsf_b=610, qsf_c=0
处理计划数据: yxdm=0058, zyz=105, qsf_a=602, qsf_b=589, qsf_c=0
处理计划数据: yxdm=6501, zyz=102, qsf_a=602, qsf_b=598, qsf_c=595
处理计划数据: yxdm=3921, zyz=101, qsf_a=0, qsf_b=600, qsf_c=593
处理计划数据: yxdm=2104, zyz=108, qsf_a=593, qsf_b=601, qsf_c=600
处理计划数据: yxdm=4501, zyz=103, qsf_a=596, qsf_b=597, qsf_c=599
处理计划数据: yxdm=6202, zyz=102, qsf_a=598, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0037, zyz=101, qsf_a=598, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3207, zyz=102, qsf_a=598, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4405, zyz=102, qsf_a=598, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0048, zyz=101, qsf_a=587, qsf_b=610, qsf_c=610
处理计划数据: yxdm=3516, zyz=103, qsf_a=602, qsf_b=595, qsf_c=578
处理计划数据: yxdm=1310, zyz=102, qsf_a=597, qsf_b=597, qsf_c=599
处理计划数据: yxdm=2117, zyz=101, qsf_a=602, qsf_b=599, qsf_c=607
处理计划数据: yxdm=2104, zyz=108, qsf_a=593, qsf_b=601, qsf_c=600
处理计划数据: yxdm=4306, zyz=102, qsf_a=603, qsf_b=594, qsf_c=592
处理计划数据: yxdm=0009, zyz=106, qsf_a=590, qsf_b=605, qsf_c=596
处理计划数据: yxdm=1123, zyz=102, qsf_a=602, qsf_b=597, qsf_c=594
处理计划数据: yxdm=0056, zyz=110, qsf_a=599, qsf_b=597, qsf_c=595
处理计划数据: yxdm=0062, zyz=102, qsf_a=587, qsf_b=610, qsf_c=602
处理计划数据: yxdm=6101, zyz=102, qsf_a=601, qsf_b=600, qsf_c=595
处理计划数据: yxdm=3207, zyz=102, qsf_a=598, qsf_b=597, qsf_c=0
处理计划数据: yxdm=3207, zyz=102, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4239, zyz=101, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4239, zyz=102, qsf_a=595, qsf_b=592, qsf_c=603
处理计划数据: yxdm=0064, zyz=103, qsf_a=595, qsf_b=600, qsf_c=601
处理计划数据: yxdm=3304, zyz=102, qsf_a=606, qsf_b=597, qsf_c=592
处理计划数据: yxdm=4605, zyz=104, qsf_a=595, qsf_b=600, qsf_c=598
处理计划数据: yxdm=6301, zyz=105, qsf_a=598, qsf_b=596, qsf_c=0
处理计划数据: yxdm=0009, zyz=102, qsf_a=589, qsf_b=607, qsf_c=594
处理计划数据: yxdm=2117, zyz=102, qsf_a=593, qsf_b=591, qsf_c=605
处理计划数据: yxdm=1123, zyz=102, qsf_a=599, qsf_b=596, qsf_c=595
处理计划数据: yxdm=3207, zyz=102, qsf_a=599, qsf_b=595, qsf_c=594
处理计划数据: yxdm=1310, zyz=103, qsf_a=597, qsf_b=597, qsf_c=599
处理计划数据: yxdm=4306, zyz=102, qsf_a=599, qsf_b=600, qsf_c=594
处理计划数据: yxdm=4306, zyz=102, qsf_a=599, qsf_b=600, qsf_c=594
处理计划数据: yxdm=4605, zyz=104, qsf_a=597, qsf_b=596, qsf_c=595
处理计划数据: yxdm=4605, zyz=104, qsf_a=596, qsf_b=600, qsf_c=594
处理计划数据: yxdm=6101, zyz=102, qsf_a=600, qsf_b=599, qsf_c=594
处理计划数据: yxdm=0064, zyz=103, qsf_a=592, qsf_b=603, qsf_c=603
处理计划数据: yxdm=6101, zyz=102, qsf_a=601, qsf_b=599, qsf_c=593
处理计划数据: yxdm=6101, zyz=101, qsf_a=599, qsf_b=599, qsf_c=593
处理计划数据: yxdm=6101, zyz=102, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4306, zyz=102, qsf_a=595, qsf_b=601, qsf_c=593
处理计划数据: yxdm=6101, zyz=102, qsf_a=599, qsf_b=600, qsf_c=594
处理计划数据: yxdm=3304, zyz=102, qsf_a=603, qsf_b=601, qsf_c=585
处理计划数据: yxdm=4605, zyz=104, qsf_a=0, qsf_b=597, qsf_c=594
处理计划数据: yxdm=3207, zyz=102, qsf_a=597, qsf_b=597, qsf_c=594
处理计划数据: yxdm=6101, zyz=101, qsf_a=599, qsf_b=599, qsf_c=594
处理计划数据: yxdm=4605, zyz=104, qsf_a=595, qsf_b=597, qsf_c=594
处理计划数据: yxdm=0048, zyz=101, qsf_a=587, qsf_b=608, qsf_c=602
处理计划数据: yxdm=1123, zyz=102, qsf_a=597, qsf_b=595, qsf_c=597
处理计划数据: yxdm=3516, zyz=103, qsf_a=601, qsf_b=590, qsf_c=577
处理计划数据: yxdm=4301, zyz=102, qsf_a=590, qsf_b=605, qsf_c=598
处理计划数据: yxdm=2308, zyz=104, qsf_a=589, qsf_b=588, qsf_c=617
处理计划数据: yxdm=4605, zyz=104, qsf_a=596, qsf_b=595, qsf_c=591
处理计划数据: yxdm=0023, zyz=102, qsf_a=594, qsf_b=597, qsf_c=597
处理计划数据: yxdm=4605, zyz=104, qsf_a=0, qsf_b=597, qsf_c=588
处理计划数据: yxdm=6301, zyz=104, qsf_a=596, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4605, zyz=104, qsf_a=0, qsf_b=598, qsf_c=586
处理计划数据: yxdm=1310, zyz=102, qsf_a=600, qsf_b=588, qsf_c=595
处理计划数据: yxdm=1310, zyz=103, qsf_a=600, qsf_b=588, qsf_c=595
处理计划数据: yxdm=0056, zyz=108, qsf_a=596, qsf_b=592, qsf_c=606
处理计划数据: yxdm=1123, zyz=102, qsf_a=598, qsf_b=595, qsf_c=592
处理计划数据: yxdm=3117, zyz=101, qsf_a=588, qsf_b=592, qsf_c=599
处理计划数据: yxdm=4501, zyz=102, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4501, zyz=102, qsf_a=589, qsf_b=599, qsf_c=599
处理计划数据: yxdm=1123, zyz=102, qsf_a=596, qsf_b=595, qsf_c=594
处理计划数据: yxdm=0064, zyz=103, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0064, zyz=103, qsf_a=592, qsf_b=596, qsf_c=602
处理计划数据: yxdm=1310, zyz=102, qsf_a=598, qsf_b=597, qsf_c=588
处理计划数据: yxdm=1310, zyz=103, qsf_a=598, qsf_b=597, qsf_c=588
处理计划数据: yxdm=1123, zyz=102, qsf_a=597, qsf_b=592, qsf_c=0
处理计划数据: yxdm=1123, zyz=102, qsf_a=598, qsf_b=594, qsf_c=591
处理计划数据: yxdm=4617, zyz=102, qsf_a=593, qsf_b=599, qsf_c=597
处理计划数据: yxdm=4239, zyz=102, qsf_a=595, qsf_b=0, qsf_c=0
处理计划数据: yxdm=5102, zyz=101, qsf_a=593, qsf_b=592, qsf_c=600
处理计划数据: yxdm=2117, zyz=102, qsf_a=593, qsf_b=589, qsf_c=605
处理计划数据: yxdm=1123, zyz=102, qsf_a=596, qsf_b=594, qsf_c=593
处理计划数据: yxdm=4605, zyz=104, qsf_a=594, qsf_b=596, qsf_c=590
处理计划数据: yxdm=4605, zyz=104, qsf_a=593, qsf_b=599, qsf_c=0
处理计划数据: yxdm=6501, zyz=102, qsf_a=599, qsf_b=598, qsf_c=592
处理计划数据: yxdm=4239, zyz=102, qsf_a=592, qsf_b=588, qsf_c=603
处理计划数据: yxdm=4501, zyz=102, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4501, zyz=103, qsf_a=593, qsf_b=591, qsf_c=596
处理计划数据: yxdm=4605, zyz=104, qsf_a=595, qsf_b=601, qsf_c=583
处理计划数据: yxdm=4605, zyz=101, qsf_a=592, qsf_b=595, qsf_c=594
处理计划数据: yxdm=4501, zyz=101, qsf_a=592, qsf_b=600, qsf_c=0
处理计划数据: yxdm=4306, zyz=102, qsf_a=590, qsf_b=598, qsf_c=595
处理计划数据: yxdm=3618, zyz=101, qsf_a=583, qsf_b=602, qsf_c=595
处理计划数据: yxdm=4605, zyz=102, qsf_a=592, qsf_b=600, qsf_c=587
处理计划数据: yxdm=3117, zyz=101, qsf_a=588, qsf_b=587, qsf_c=602
处理计划数据: yxdm=4605, zyz=104, qsf_a=593, qsf_b=595, qsf_c=589
处理计划数据: yxdm=3207, zyz=102, qsf_a=0, qsf_b=595, qsf_c=592
处理计划数据: yxdm=4605, zyz=104, qsf_a=596, qsf_b=597, qsf_c=584
处理计划数据: yxdm=4605, zyz=107, qsf_a=592, qsf_b=598, qsf_c=588
处理计划数据: yxdm=2117, zyz=101, qsf_a=595, qsf_b=598, qsf_c=611
处理计划数据: yxdm=4605, zyz=104, qsf_a=593, qsf_b=603, qsf_c=584
处理计划数据: yxdm=4605, zyz=104, qsf_a=593, qsf_b=596, qsf_c=588
处理计划数据: yxdm=1123, zyz=102, qsf_a=595, qsf_b=593, qsf_c=594
处理计划数据: yxdm=3389, zyz=101, qsf_a=588, qsf_b=595, qsf_c=594
处理计划数据: yxdm=4605, zyz=102, qsf_a=593, qsf_b=595, qsf_c=589
处理计划数据: yxdm=5102, zyz=103, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0023, zyz=102, qsf_a=591, qsf_b=597, qsf_c=594
处理计划数据: yxdm=0056, zyz=108, qsf_a=594, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0023, zyz=102, qsf_a=594, qsf_b=594, qsf_c=0
处理计划数据: yxdm=1310, zyz=102, qsf_a=600, qsf_b=575, qsf_c=599
处理计划数据: yxdm=1310, zyz=103, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4617, zyz=102, qsf_a=594, qsf_b=596, qsf_c=594
处理计划数据: yxdm=3620, zyz=103, qsf_a=587, qsf_b=606, qsf_c=603
处理计划数据: yxdm=3117, zyz=105, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4605, zyz=104, qsf_a=0, qsf_b=595, qsf_c=582
处理计划数据: yxdm=4306, zyz=102, qsf_a=594, qsf_b=595, qsf_c=590
处理计划数据: yxdm=3304, zyz=101, qsf_a=592, qsf_b=591, qsf_c=0
处理计划数据: yxdm=4605, zyz=104, qsf_a=595, qsf_b=596, qsf_c=584
处理计划数据: yxdm=4605, zyz=104, qsf_a=592, qsf_b=598, qsf_c=0
处理计划数据: yxdm=4605, zyz=104, qsf_a=592, qsf_b=598, qsf_c=586
处理计划数据: yxdm=4605, zyz=104, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=1123, zyz=102, qsf_a=595, qsf_b=592, qsf_c=589
处理计划数据: yxdm=3207, zyz=102, qsf_a=594, qsf_b=587, qsf_c=595
处理计划数据: yxdm=3117, zyz=101, qsf_a=589, qsf_b=605, qsf_c=0
处理计划数据: yxdm=0056, zyz=108, qsf_a=590, qsf_b=593, qsf_c=596
处理计划数据: yxdm=4501, zyz=102, qsf_a=590, qsf_b=592, qsf_c=594
处理计划数据: yxdm=3516, zyz=103, qsf_a=601, qsf_b=584, qsf_c=576
处理计划数据: yxdm=4501, zyz=102, qsf_a=592, qsf_b=596, qsf_c=0
处理计划数据: yxdm=4301, zyz=101, qsf_a=588, qsf_b=596, qsf_c=589
处理计划数据: yxdm=4301, zyz=101, qsf_a=588, qsf_b=596, qsf_c=589
处理计划数据: yxdm=4301, zyz=101, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4301, zyz=102, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3389, zyz=101, qsf_a=590, qsf_b=589, qsf_c=594
处理计划数据: yxdm=4501, zyz=102, qsf_a=589, qsf_b=593, qsf_c=595
处理计划数据: yxdm=0009, zyz=105, qsf_a=587, qsf_b=605, qsf_c=594
处理计划数据: yxdm=4605, zyz=104, qsf_a=593, qsf_b=596, qsf_c=585
处理计划数据: yxdm=0023, zyz=102, qsf_a=590, qsf_b=594, qsf_c=594
处理计划数据: yxdm=4437, zyz=101, qsf_a=0, qsf_b=580, qsf_c=612
处理计划数据: yxdm=6301, zyz=105, qsf_a=593, qsf_b=588, qsf_c=592
处理计划数据: yxdm=4605, zyz=104, qsf_a=593, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0056, zyz=108, qsf_a=593, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3304, zyz=101, qsf_a=593, qsf_b=594, qsf_c=581
处理计划数据: yxdm=3618, zyz=101, qsf_a=0, qsf_b=589, qsf_c=594
处理计划数据: yxdm=3618, zyz=101, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3618, zyz=101, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=1123, zyz=101, qsf_a=594, qsf_b=593, qsf_c=590
处理计划数据: yxdm=4501, zyz=101, qsf_a=585, qsf_b=593, qsf_c=602
处理计划数据: yxdm=6501, zyz=102, qsf_a=594, qsf_b=594, qsf_c=589
处理计划数据: yxdm=0009, zyz=105, qsf_a=587, qsf_b=605, qsf_c=594
处理计划数据: yxdm=3618, zyz=105, qsf_a=584, qsf_b=588, qsf_c=606
处理计划数据: yxdm=3618, zyz=106, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3618, zyz=106, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4605, zyz=104, qsf_a=594, qsf_b=595, qsf_c=582
处理计划数据: yxdm=9227, zyz=103, qsf_a=601, qsf_b=552, qsf_c=630
处理计划数据: yxdm=2308, zyz=104, qsf_a=587, qsf_b=586, qsf_c=609
处理计划数据: yxdm=2308, zyz=104, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=2308, zyz=105, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4605, zyz=104, qsf_a=593, qsf_b=595, qsf_c=582
处理计划数据: yxdm=6301, zyz=105, qsf_a=593, qsf_b=589, qsf_c=0
处理计划数据: yxdm=4605, zyz=104, qsf_a=593, qsf_b=595, qsf_c=582
处理计划数据: yxdm=4306, zyz=102, qsf_a=588, qsf_b=596, qsf_c=591
处理计划数据: yxdm=4501, zyz=102, qsf_a=586, qsf_b=593, qsf_c=595
处理计划数据: yxdm=4301, zyz=102, qsf_a=586, qsf_b=601, qsf_c=596
处理计划数据: yxdm=0042, zyz=102, qsf_a=572, qsf_b=637, qsf_c=638
处理计划数据: yxdm=4501, zyz=102, qsf_a=587, qsf_b=591, qsf_c=595
处理计划数据: yxdm=1123, zyz=101, qsf_a=593, qsf_b=592, qsf_c=589
处理计划数据: yxdm=0062, zyz=102, qsf_a=575, qsf_b=605, qsf_c=602
处理计划数据: yxdm=0062, zyz=102, qsf_a=575, qsf_b=605, qsf_c=602
处理计划数据: yxdm=3389, zyz=101, qsf_a=0, qsf_b=589, qsf_c=585
处理计划数据: yxdm=4306, zyz=102, qsf_a=587, qsf_b=597, qsf_c=592
处理计划数据: yxdm=0062, zyz=102, qsf_a=576, qsf_b=605, qsf_c=599
处理计划数据: yxdm=3618, zyz=101, qsf_a=581, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3618, zyz=101, qsf_a=579, qsf_b=600, qsf_c=0
处理计划数据: yxdm=4306, zyz=102, qsf_a=590, qsf_b=595, qsf_c=0
处理计划数据: yxdm=4301, zyz=101, qsf_a=578, qsf_b=604, qsf_c=600
处理计划数据: yxdm=2308, zyz=104, qsf_a=571, qsf_b=602, qsf_c=622
处理计划数据: yxdm=3117, zyz=101, qsf_a=584, qsf_b=591, qsf_c=590
处理计划数据: yxdm=3207, zyz=102, qsf_a=593, qsf_b=588, qsf_c=591
处理计划数据: yxdm=3207, zyz=102, qsf_a=584, qsf_b=588, qsf_c=600
处理计划数据: yxdm=4306, zyz=102, qsf_a=589, qsf_b=594, qsf_c=590
处理计划数据: yxdm=4306, zyz=102, qsf_a=587, qsf_b=596, qsf_c=594
处理计划数据: yxdm=0062, zyz=102, qsf_a=575, qsf_b=605, qsf_c=599
处理计划数据: yxdm=6501, zyz=102, qsf_a=591, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0023, zyz=102, qsf_a=585, qsf_b=594, qsf_c=593
处理计划数据: yxdm=4306, zyz=102, qsf_a=588, qsf_b=595, qsf_c=591
处理计划数据: yxdm=0023, zyz=102, qsf_a=585, qsf_b=595, qsf_c=594
处理计划数据: yxdm=0023, zyz=102, qsf_a=585, qsf_b=592, qsf_c=596
处理计划数据: yxdm=4306, zyz=101, qsf_a=587, qsf_b=594, qsf_c=591
处理计划数据: yxdm=4501, zyz=102, qsf_a=584, qsf_b=593, qsf_c=594
处理计划数据: yxdm=4306, zyz=102, qsf_a=588, qsf_b=597, qsf_c=0
处理计划数据: yxdm=3117, zyz=102, qsf_a=581, qsf_b=586, qsf_c=602
处理计划数据: yxdm=4239, zyz=102, qsf_a=589, qsf_b=583, qsf_c=599
处理计划数据: yxdm=3207, zyz=102, qsf_a=590, qsf_b=586, qsf_c=593
处理计划数据: yxdm=0023, zyz=102, qsf_a=580, qsf_b=599, qsf_c=595
处理计划数据: yxdm=4301, zyz=102, qsf_a=588, qsf_b=600, qsf_c=580
处理计划数据: yxdm=3389, zyz=101, qsf_a=580, qsf_b=593, qsf_c=595
处理计划数据: yxdm=3389, zyz=101, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6301, zyz=104, qsf_a=597, qsf_b=593, qsf_c=586
处理计划数据: yxdm=0023, zyz=104, qsf_a=581, qsf_b=597, qsf_c=594
处理计划数据: yxdm=3618, zyz=101, qsf_a=577, qsf_b=598, qsf_c=602
处理计划数据: yxdm=3618, zyz=105, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4437, zyz=101, qsf_a=590, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4501, zyz=102, qsf_a=587, qsf_b=597, qsf_c=0
处理计划数据: yxdm=3516, zyz=103, qsf_a=598, qsf_b=582, qsf_c=576
处理计划数据: yxdm=2117, zyz=102, qsf_a=595, qsf_b=588, qsf_c=605
处理计划数据: yxdm=0023, zyz=101, qsf_a=579, qsf_b=596, qsf_c=596
处理计划数据: yxdm=3304, zyz=102, qsf_a=586, qsf_b=611, qsf_c=581
处理计划数据: yxdm=0056, zyz=110, qsf_a=593, qsf_b=585, qsf_c=590
处理计划数据: yxdm=6301, zyz=105, qsf_a=591, qsf_b=0, qsf_c=587
处理计划数据: yxdm=4239, zyz=102, qsf_a=586, qsf_b=581, qsf_c=596
处理计划数据: yxdm=0056, zyz=110, qsf_a=590, qsf_b=588, qsf_c=593
处理计划数据: yxdm=3304, zyz=101, qsf_a=587, qsf_b=599, qsf_c=570
处理计划数据: yxdm=3207, zyz=102, qsf_a=589, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0062, zyz=102, qsf_a=569, qsf_b=609, qsf_c=600
处理计划数据: yxdm=3618, zyz=101, qsf_a=573, qsf_b=599, qsf_c=597
处理计划数据: yxdm=3207, zyz=102, qsf_a=587, qsf_b=588, qsf_c=589
处理计划数据: yxdm=5102, zyz=101, qsf_a=586, qsf_b=582, qsf_c=594
处理计划数据: yxdm=3117, zyz=101, qsf_a=578, qsf_b=586, qsf_c=594
处理计划数据: yxdm=3620, zyz=103, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3620, zyz=103, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3620, zyz=104, qsf_a=566, qsf_b=610, qsf_c=603
处理计划数据: yxdm=3620, zyz=104, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=5102, zyz=110, qsf_a=587, qsf_b=586, qsf_c=592
处理计划数据: yxdm=3620, zyz=101, qsf_a=563, qsf_b=610, qsf_c=609
处理计划数据: yxdm=3207, zyz=102, qsf_a=585, qsf_b=588, qsf_c=590
处理计划数据: yxdm=0056, zyz=104, qsf_a=588, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3389, zyz=101, qsf_a=588, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0056, zyz=105, qsf_a=587, qsf_b=584, qsf_c=593
处理计划数据: yxdm=1123, zyz=103, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3207, zyz=102, qsf_a=0, qsf_b=0, qsf_c=586
处理计划数据: yxdm=3921, zyz=103, qsf_a=587, qsf_b=571, qsf_c=0
处理计划数据: yxdm=3117, zyz=101, qsf_a=579, qsf_b=587, qsf_c=601
处理计划数据: yxdm=5102, zyz=103, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=5102, zyz=104, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4301, zyz=101, qsf_a=584, qsf_b=589, qsf_c=584
处理计划数据: yxdm=1310, zyz=102, qsf_a=590, qsf_b=577, qsf_c=587
处理计划数据: yxdm=6501, zyz=102, qsf_a=591, qsf_b=586, qsf_c=573
处理计划数据: yxdm=0056, zyz=110, qsf_a=593, qsf_b=578, qsf_c=587
处理计划数据: yxdm=3117, zyz=101, qsf_a=576, qsf_b=586, qsf_c=593
处理计划数据: yxdm=4437, zyz=101, qsf_a=0, qsf_b=0, qsf_c=586
处理计划数据: yxdm=0062, zyz=101, qsf_a=567, qsf_b=605, qsf_c=599
处理计划数据: yxdm=3207, zyz=102, qsf_a=586, qsf_b=585, qsf_c=588
处理计划数据: yxdm=6301, zyz=104, qsf_a=591, qsf_b=587, qsf_c=581
处理计划数据: yxdm=3117, zyz=101, qsf_a=569, qsf_b=592, qsf_c=602
处理计划数据: yxdm=0023, zyz=102, qsf_a=575, qsf_b=595, qsf_c=595
处理计划数据: yxdm=3207, zyz=101, qsf_a=581, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3620, zyz=101, qsf_a=564, qsf_b=608, qsf_c=605
处理计划数据: yxdm=0056, zyz=110, qsf_a=588, qsf_b=586, qsf_c=588
处理计划数据: yxdm=4301, zyz=102, qsf_a=581, qsf_b=595, qsf_c=586
处理计划数据: yxdm=4301, zyz=102, qsf_a=587, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4306, zyz=101, qsf_a=587, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6202, zyz=102, qsf_a=587, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6301, zyz=104, qsf_a=587, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3207, zyz=102, qsf_a=0, qsf_b=588, qsf_c=577
处理计划数据: yxdm=1310, zyz=102, qsf_a=587, qsf_b=566, qsf_c=600
处理计划数据: yxdm=3207, zyz=102, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3207, zyz=102, qsf_a=585, qsf_b=577, qsf_c=593
处理计划数据: yxdm=4301, zyz=102, qsf_a=579, qsf_b=592, qsf_c=586
处理计划数据: yxdm=3117, zyz=101, qsf_a=585, qsf_b=588, qsf_c=594
处理计划数据: yxdm=0023, zyz=105, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3620, zyz=104, qsf_a=562, qsf_b=610, qsf_c=603
处理计划数据: yxdm=4239, zyz=102, qsf_a=578, qsf_b=0, qsf_c=593
处理计划数据: yxdm=1310, zyz=102, qsf_a=591, qsf_b=561, qsf_c=595
处理计划数据: yxdm=3389, zyz=101, qsf_a=578, qsf_b=581, qsf_c=590
处理计划数据: yxdm=3620, zyz=103, qsf_a=563, qsf_b=606, qsf_c=602
处理计划数据: yxdm=3207, zyz=102, qsf_a=583, qsf_b=579, qsf_c=588
处理计划数据: yxdm=4239, zyz=102, qsf_a=587, qsf_b=570, qsf_c=591
处理计划数据: yxdm=4239, zyz=102, qsf_a=580, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0056, zyz=110, qsf_a=593, qsf_b=579, qsf_c=576
处理计划数据: yxdm=3207, zyz=102, qsf_a=585, qsf_b=580, qsf_c=586
处理计划数据: yxdm=3389, zyz=104, qsf_a=578, qsf_b=584, qsf_c=585
处理计划数据: yxdm=2308, zyz=105, qsf_a=584, qsf_b=581, qsf_c=608
处理计划数据: yxdm=3207, zyz=102, qsf_a=586, qsf_b=583, qsf_c=0
处理计划数据: yxdm=0056, zyz=108, qsf_a=580, qsf_b=587, qsf_c=609
处理计划数据: yxdm=0056, zyz=110, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0056, zyz=110, qsf_a=588, qsf_b=580, qsf_c=586
处理计划数据: yxdm=1310, zyz=102, qsf_a=592, qsf_b=570, qsf_c=578
处理计划数据: yxdm=3207, zyz=102, qsf_a=579, qsf_b=0, qsf_c=588
处理计划数据: yxdm=0009, zyz=110, qsf_a=0, qsf_b=0, qsf_c=585
处理计划数据: yxdm=3117, zyz=104, qsf_a=585, qsf_b=0, qsf_c=0
处理计划数据: yxdm=5102, zyz=101, qsf_a=584, qsf_b=575, qsf_c=590
处理计划数据: yxdm=3618, zyz=101, qsf_a=566, qsf_b=599, qsf_c=596
处理计划数据: yxdm=6202, zyz=102, qsf_a=589, qsf_b=591, qsf_c=575
处理计划数据: yxdm=3389, zyz=104, qsf_a=578, qsf_b=590, qsf_c=588
处理计划数据: yxdm=3304, zyz=101, qsf_a=586, qsf_b=587, qsf_c=579
处理计划数据: yxdm=6501, zyz=102, qsf_a=590, qsf_b=573, qsf_c=0
处理计划数据: yxdm=3516, zyz=103, qsf_a=594, qsf_b=586, qsf_c=556
处理计划数据: yxdm=3207, zyz=102, qsf_a=586, qsf_b=581, qsf_c=0
处理计划数据: yxdm=3389, zyz=104, qsf_a=572, qsf_b=588, qsf_c=592
处理计划数据: yxdm=3207, zyz=102, qsf_a=0, qsf_b=584, qsf_c=0
处理计划数据: yxdm=3207, zyz=102, qsf_a=0, qsf_b=0, qsf_c=581
处理计划数据: yxdm=6301, zyz=105, qsf_a=587, qsf_b=585, qsf_c=0
处理计划数据: yxdm=6202, zyz=102, qsf_a=585, qsf_b=588, qsf_c=583
处理计划数据: yxdm=6202, zyz=103, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=5102, zyz=110, qsf_a=584, qsf_b=581, qsf_c=584
处理计划数据: yxdm=6501, zyz=102, qsf_a=576, qsf_b=591, qsf_c=591
处理计划数据: yxdm=4605, zyz=104, qsf_a=602, qsf_b=605, qsf_c=0
处理计划数据: yxdm=0023, zyz=102, qsf_a=584, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3117, zyz=104, qsf_a=584, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4301, zyz=102, qsf_a=570, qsf_b=600, qsf_c=585
处理计划数据: yxdm=6301, zyz=104, qsf_a=586, qsf_b=578, qsf_c=0
处理计划数据: yxdm=3304, zyz=101, qsf_a=584, qsf_b=586, qsf_c=583
处理计划数据: yxdm=3921, zyz=103, qsf_a=588, qsf_b=576, qsf_c=568
处理计划数据: yxdm=3921, zyz=104, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3207, zyz=102, qsf_a=0, qsf_b=575, qsf_c=585
处理计划数据: yxdm=0023, zyz=102, qsf_a=568, qsf_b=593, qsf_c=594
处理计划数据: yxdm=5102, zyz=103, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3304, zyz=102, qsf_a=590, qsf_b=595, qsf_c=582
处理计划数据: yxdm=0023, zyz=102, qsf_a=568, qsf_b=592, qsf_c=594
处理计划数据: yxdm=5102, zyz=110, qsf_a=580, qsf_b=574, qsf_c=587
处理计划数据: yxdm=3207, zyz=102, qsf_a=584, qsf_b=579, qsf_c=589
处理计划数据: yxdm=0023, zyz=102, qsf_a=583, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4437, zyz=101, qsf_a=583, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3117, zyz=102, qsf_a=575, qsf_b=576, qsf_c=591
处理计划数据: yxdm=4239, zyz=102, qsf_a=587, qsf_b=572, qsf_c=0
处理计划数据: yxdm=4239, zyz=102, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3207, zyz=102, qsf_a=578, qsf_b=577, qsf_c=585
处理计划数据: yxdm=0056, zyz=108, qsf_a=580, qsf_b=585, qsf_c=584
处理计划数据: yxdm=3207, zyz=102, qsf_a=580, qsf_b=581, qsf_c=577
处理计划数据: yxdm=1310, zyz=102, qsf_a=588, qsf_b=568, qsf_c=575
处理计划数据: yxdm=6301, zyz=104, qsf_a=596, qsf_b=575, qsf_c=571
处理计划数据: yxdm=4605, zyz=104, qsf_a=593, qsf_b=598, qsf_c=589
处理计划数据: yxdm=1310, zyz=102, qsf_a=587, qsf_b=571, qsf_c=0
处理计划数据: yxdm=4605, zyz=104, qsf_a=594, qsf_b=599, qsf_c=584
处理计划数据: yxdm=6501, zyz=102, qsf_a=571, qsf_b=593, qsf_c=592
处理计划数据: yxdm=6301, zyz=103, qsf_a=589, qsf_b=586, qsf_c=569
处理计划数据: yxdm=3207, zyz=102, qsf_a=577, qsf_b=578, qsf_c=586
处理计划数据: yxdm=3304, zyz=101, qsf_a=587, qsf_b=586, qsf_c=578
处理计划数据: yxdm=3117, zyz=101, qsf_a=581, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3921, zyz=104, qsf_a=581, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6202, zyz=102, qsf_a=581, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3207, zyz=102, qsf_a=579, qsf_b=0, qsf_c=576
处理计划数据: yxdm=3207, zyz=102, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4301, zyz=101, qsf_a=560, qsf_b=601, qsf_c=599
处理计划数据: yxdm=6202, zyz=102, qsf_a=571, qsf_b=588, qsf_c=581
处理计划数据: yxdm=1310, zyz=101, qsf_a=578, qsf_b=574, qsf_c=586
处理计划数据: yxdm=3207, zyz=102, qsf_a=577, qsf_b=575, qsf_c=586
处理计划数据: yxdm=3207, zyz=102, qsf_a=579, qsf_b=576, qsf_c=576
处理计划数据: yxdm=3921, zyz=104, qsf_a=577, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3516, zyz=103, qsf_a=592, qsf_b=577, qsf_c=559
处理计划数据: yxdm=5102, zyz=110, qsf_a=581, qsf_b=577, qsf_c=578
处理计划数据: yxdm=6501, zyz=102, qsf_a=589, qsf_b=575, qsf_c=567
处理计划数据: yxdm=3207, zyz=102, qsf_a=0, qsf_b=0, qsf_c=576
处理计划数据: yxdm=4301, zyz=102, qsf_a=569, qsf_b=588, qsf_c=586
处理计划数据: yxdm=4301, zyz=102, qsf_a=580, qsf_b=0, qsf_c=0
处理计划数据: yxdm=5102, zyz=110, qsf_a=583, qsf_b=574, qsf_c=578
处理计划数据: yxdm=2308, zyz=102, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=2308, zyz=105, qsf_a=571, qsf_b=583, qsf_c=611
处理计划数据: yxdm=2104, zyz=106, qsf_a=579, qsf_b=580, qsf_c=587
处理计划数据: yxdm=0009, zyz=110, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0009, zyz=110, qsf_a=0, qsf_b=0, qsf_c=580
处理计划数据: yxdm=3207, zyz=102, qsf_a=577, qsf_b=0, qsf_c=586
处理计划数据: yxdm=5102, zyz=110, qsf_a=580, qsf_b=574, qsf_c=577
处理计划数据: yxdm=3516, zyz=103, qsf_a=569, qsf_b=583, qsf_c=575
处理计划数据: yxdm=5102, zyz=110, qsf_a=581, qsf_b=575, qsf_c=577
处理计划数据: yxdm=6301, zyz=105, qsf_a=578, qsf_b=585, qsf_c=588
处理计划数据: yxdm=5102, zyz=103, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0062, zyz=102, qsf_a=558, qsf_b=610, qsf_c=606
处理计划数据: yxdm=3921, zyz=104, qsf_a=579, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6501, zyz=102, qsf_a=583, qsf_b=571, qsf_c=586
处理计划数据: yxdm=6501, zyz=102, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4301, zyz=102, qsf_a=568, qsf_b=589, qsf_c=581
处理计划数据: yxdm=2308, zyz=105, qsf_a=568, qsf_b=586, qsf_c=609
处理计划数据: yxdm=6501, zyz=102, qsf_a=586, qsf_b=566, qsf_c=0
处理计划数据: yxdm=3207, zyz=102, qsf_a=577, qsf_b=575, qsf_c=576
处理计划数据: yxdm=0056, zyz=104, qsf_a=578, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3618, zyz=101, qsf_a=578, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4301, zyz=102, qsf_a=578, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6501, zyz=101, qsf_a=578, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6301, zyz=104, qsf_a=585, qsf_b=582, qsf_c=569
处理计划数据: yxdm=0048, zyz=105, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6301, zyz=104, qsf_a=589, qsf_b=570, qsf_c=567
处理计划数据: yxdm=2308, zyz=106, qsf_a=568, qsf_b=582, qsf_c=604
处理计划数据: yxdm=3117, zyz=102, qsf_a=565, qsf_b=580, qsf_c=590
处理计划数据: yxdm=6301, zyz=104, qsf_a=583, qsf_b=575, qsf_c=575
处理计划数据: yxdm=6301, zyz=105, qsf_a=588, qsf_b=571, qsf_c=572
处理计划数据: yxdm=1310, zyz=102, qsf_a=580, qsf_b=564, qsf_c=576
处理计划数据: yxdm=1310, zyz=103, qsf_a=580, qsf_b=564, qsf_c=576
处理计划数据: yxdm=4301, zyz=102, qsf_a=558, qsf_b=593, qsf_c=594
处理计划数据: yxdm=1310, zyz=102, qsf_a=585, qsf_b=561, qsf_c=569
处理计划数据: yxdm=6301, zyz=104, qsf_a=579, qsf_b=579, qsf_c=577
处理计划数据: yxdm=6301, zyz=101, qsf_a=574, qsf_b=583, qsf_c=578
处理计划数据: yxdm=3207, zyz=102, qsf_a=0, qsf_b=575, qsf_c=582
处理计划数据: yxdm=3207, zyz=102, qsf_a=0, qsf_b=578, qsf_c=576
处理计划数据: yxdm=1310, zyz=102, qsf_a=589, qsf_b=566, qsf_c=557
处理计划数据: yxdm=1310, zyz=102, qsf_a=585, qsf_b=560, qsf_c=569
处理计划数据: yxdm=6301, zyz=104, qsf_a=585, qsf_b=579, qsf_c=566
处理计划数据: yxdm=6301, zyz=104, qsf_a=588, qsf_b=575, qsf_c=569
处理计划数据: yxdm=3389, zyz=101, qsf_a=565, qsf_b=578, qsf_c=584
处理计划数据: yxdm=4301, zyz=102, qsf_a=562, qsf_b=590, qsf_c=582
处理计划数据: yxdm=6301, zyz=105, qsf_a=577, qsf_b=581, qsf_c=0
处理计划数据: yxdm=2104, zyz=105, qsf_a=583, qsf_b=549, qsf_c=594
处理计划数据: yxdm=4301, zyz=102, qsf_a=562, qsf_b=593, qsf_c=582
处理计划数据: yxdm=6301, zyz=104, qsf_a=578, qsf_b=581, qsf_c=579
处理计划数据: yxdm=3207, zyz=102, qsf_a=0, qsf_b=576, qsf_c=577
处理计划数据: yxdm=6301, zyz=104, qsf_a=579, qsf_b=592, qsf_c=566
处理计划数据: yxdm=0023, zyz=105, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3207, zyz=102, qsf_a=577, qsf_b=575, qsf_c=576
处理计划数据: yxdm=4301, zyz=101, qsf_a=558, qsf_b=593, qsf_c=583
处理计划数据: yxdm=6301, zyz=104, qsf_a=588, qsf_b=570, qsf_c=566
处理计划数据: yxdm=4239, zyz=102, qsf_a=567, qsf_b=565, qsf_c=592
处理计划数据: yxdm=3304, zyz=102, qsf_a=590, qsf_b=586, qsf_c=571
处理计划数据: yxdm=1310, zyz=102, qsf_a=586, qsf_b=557, qsf_c=0
处理计划数据: yxdm=2104, zyz=107, qsf_a=575, qsf_b=575, qsf_c=580
处理计划数据: yxdm=6301, zyz=104, qsf_a=574, qsf_b=579, qsf_c=574
处理计划数据: yxdm=1310, zyz=101, qsf_a=580, qsf_b=561, qsf_c=569
处理计划数据: yxdm=6202, zyz=102, qsf_a=580, qsf_b=574, qsf_c=569
处理计划数据: yxdm=6301, zyz=104, qsf_a=574, qsf_b=578, qsf_c=0
处理计划数据: yxdm=6501, zyz=102, qsf_a=583, qsf_b=565, qsf_c=574
处理计划数据: yxdm=6501, zyz=102, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=1310, zyz=101, qsf_a=583, qsf_b=558, qsf_c=571
处理计划数据: yxdm=6501, zyz=102, qsf_a=581, qsf_b=568, qsf_c=563
处理计划数据: yxdm=6202, zyz=102, qsf_a=580, qsf_b=578, qsf_c=566
处理计划数据: yxdm=1310, zyz=102, qsf_a=577, qsf_b=561, qsf_c=573
处理计划数据: yxdm=4239, zyz=102, qsf_a=577, qsf_b=548, qsf_c=586
处理计划数据: yxdm=6501, zyz=102, qsf_a=581, qsf_b=567, qsf_c=562
处理计划数据: yxdm=6301, zyz=104, qsf_a=580, qsf_b=572, qsf_c=569
处理计划数据: yxdm=6202, zyz=102, qsf_a=576, qsf_b=576, qsf_c=572
处理计划数据: yxdm=3389, zyz=102, qsf_a=556, qsf_b=578, qsf_c=591
处理计划数据: yxdm=4239, zyz=102, qsf_a=571, qsf_b=550, qsf_c=595
处理计划数据: yxdm=6301, zyz=101, qsf_a=576, qsf_b=573, qsf_c=567
处理计划数据: yxdm=6301, zyz=104, qsf_a=578, qsf_b=572, qsf_c=569
处理计划数据: yxdm=6301, zyz=104, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3389, zyz=101, qsf_a=561, qsf_b=575, qsf_c=581
处理计划数据: yxdm=6301, zyz=105, qsf_a=575, qsf_b=578, qsf_c=569
处理计划数据: yxdm=4239, zyz=102, qsf_a=567, qsf_b=0, qsf_c=0
处理计划数据: yxdm=1310, zyz=102, qsf_a=577, qsf_b=555, qsf_c=571
处理计划数据: yxdm=6501, zyz=101, qsf_a=571, qsf_b=563, qsf_c=592
处理计划数据: yxdm=6301, zyz=101, qsf_a=573, qsf_b=580, qsf_c=573
处理计划数据: yxdm=6501, zyz=102, qsf_a=575, qsf_b=570, qsf_c=573
处理计划数据: yxdm=0023, zyz=103, qsf_a=570, qsf_b=588, qsf_c=569
处理计划数据: yxdm=6301, zyz=104, qsf_a=573, qsf_b=587, qsf_c=569
处理计划数据: yxdm=2104, zyz=104, qsf_a=572, qsf_b=562, qsf_c=586
处理计划数据: yxdm=6301, zyz=103, qsf_a=578, qsf_b=581, qsf_c=566
处理计划数据: yxdm=6301, zyz=104, qsf_a=578, qsf_b=574, qsf_c=569
处理计划数据: yxdm=6301, zyz=104, qsf_a=573, qsf_b=581, qsf_c=568
处理计划数据: yxdm=6301, zyz=105, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4437, zyz=101, qsf_a=573, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6501, zyz=101, qsf_a=575, qsf_b=562, qsf_c=579
处理计划数据: yxdm=1310, zyz=102, qsf_a=578, qsf_b=562, qsf_c=0
处理计划数据: yxdm=1310, zyz=102, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6301, zyz=104, qsf_a=576, qsf_b=577, qsf_c=568
处理计划数据: yxdm=6301, zyz=104, qsf_a=578, qsf_b=573, qsf_c=567
处理计划数据: yxdm=4239, zyz=101, qsf_a=568, qsf_b=553, qsf_c=588
处理计划数据: yxdm=4239, zyz=101, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3117, zyz=103, qsf_a=566, qsf_b=561, qsf_c=584
处理计划数据: yxdm=3117, zyz=102, qsf_a=566, qsf_b=562, qsf_c=581
处理计划数据: yxdm=6202, zyz=102, qsf_a=579, qsf_b=569, qsf_c=561
处理计划数据: yxdm=1310, zyz=101, qsf_a=576, qsf_b=557, qsf_c=569
处理计划数据: yxdm=6301, zyz=104, qsf_a=578, qsf_b=570, qsf_c=566
处理计划数据: yxdm=6301, zyz=104, qsf_a=573, qsf_b=582, qsf_c=568
处理计划数据: yxdm=6301, zyz=104, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6301, zyz=104, qsf_a=573, qsf_b=581, qsf_c=569
处理计划数据: yxdm=6301, zyz=104, qsf_a=573, qsf_b=583, qsf_c=567
处理计划数据: yxdm=6501, zyz=102, qsf_a=572, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6301, zyz=104, qsf_a=574, qsf_b=576, qsf_c=567
处理计划数据: yxdm=0062, zyz=102, qsf_a=539, qsf_b=606, qsf_c=599
处理计划数据: yxdm=3516, zyz=103, qsf_a=556, qsf_b=582, qsf_c=566
处理计划数据: yxdm=0062, zyz=102, qsf_a=539, qsf_b=606, qsf_c=599
处理计划数据: yxdm=6301, zyz=104, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4239, zyz=102, qsf_a=569, qsf_b=554, qsf_c=581
处理计划数据: yxdm=4239, zyz=102, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6301, zyz=101, qsf_a=575, qsf_b=570, qsf_c=567
处理计划数据: yxdm=6301, zyz=101, qsf_a=575, qsf_b=570, qsf_c=567
处理计划数据: yxdm=6301, zyz=101, qsf_a=575, qsf_b=570, qsf_c=567
处理计划数据: yxdm=6301, zyz=104, qsf_a=574, qsf_b=576, qsf_c=567
处理计划数据: yxdm=6301, zyz=104, qsf_a=575, qsf_b=570, qsf_c=566
处理计划数据: yxdm=3117, zyz=102, qsf_a=563, qsf_b=561, qsf_c=584
处理计划数据: yxdm=4437, zyz=101, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4437, zyz=101, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4437, zyz=101, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4437, zyz=101, qsf_a=558, qsf_b=555, qsf_c=585
处理计划数据: yxdm=4437, zyz=101, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4437, zyz=101, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4437, zyz=101, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4437, zyz=101, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6202, zyz=102, qsf_a=578, qsf_b=572, qsf_c=559
处理计划数据: yxdm=4239, zyz=102, qsf_a=568, qsf_b=558, qsf_c=588
处理计划数据: yxdm=6501, zyz=102, qsf_a=571, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6301, zyz=104, qsf_a=574, qsf_b=571, qsf_c=566
处理计划数据: yxdm=6301, zyz=104, qsf_a=573, qsf_b=572, qsf_c=566
处理计划数据: yxdm=4605, zyz=103, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4605, zyz=106, qsf_a=575, qsf_b=561, qsf_c=0
处理计划数据: yxdm=6301, zyz=104, qsf_a=573, qsf_b=575, qsf_c=567
处理计划数据: yxdm=6301, zyz=104, qsf_a=574, qsf_b=571, qsf_c=569
处理计划数据: yxdm=6301, zyz=102, qsf_a=573, qsf_b=574, qsf_c=566
处理计划数据: yxdm=6501, zyz=102, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6501, zyz=102, qsf_a=570, qsf_b=564, qsf_c=0
处理计划数据: yxdm=6301, zyz=104, qsf_a=573, qsf_b=575, qsf_c=566
处理计划数据: yxdm=6301, zyz=104, qsf_a=573, qsf_b=575, qsf_c=566
处理计划数据: yxdm=6202, zyz=102, qsf_a=576, qsf_b=569, qsf_c=562
处理计划数据: yxdm=6301, zyz=104, qsf_a=573, qsf_b=0, qsf_c=566
处理计划数据: yxdm=1310, zyz=102, qsf_a=576, qsf_b=555, qsf_c=557
处理计划数据: yxdm=6501, zyz=102, qsf_a=571, qsf_b=570, qsf_c=564
处理计划数据: yxdm=6501, zyz=101, qsf_a=570, qsf_b=561, qsf_c=574
处理计划数据: yxdm=0023, zyz=103, qsf_a=570, qsf_b=585, qsf_c=564
处理计划数据: yxdm=4239, zyz=102, qsf_a=568, qsf_b=540, qsf_c=586
处理计划数据: yxdm=4239, zyz=102, qsf_a=569, qsf_b=540, qsf_c=582
处理计划数据: yxdm=4239, zyz=102, qsf_a=569, qsf_b=540, qsf_c=582
处理计划数据: yxdm=6202, zyz=101, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6202, zyz=102, qsf_a=573, qsf_b=572, qsf_c=559
处理计划数据: yxdm=6202, zyz=102, qsf_a=565, qsf_b=574, qsf_c=570
处理计划数据: yxdm=6501, zyz=102, qsf_a=571, qsf_b=561, qsf_c=566
处理计划数据: yxdm=4437, zyz=101, qsf_a=562, qsf_b=548, qsf_c=583
处理计划数据: yxdm=4437, zyz=101, qsf_a=562, qsf_b=548, qsf_c=583
处理计划数据: yxdm=4437, zyz=101, qsf_a=562, qsf_b=548, qsf_c=583
处理计划数据: yxdm=4239, zyz=101, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4239, zyz=102, qsf_a=567, qsf_b=540, qsf_c=582
处理计划数据: yxdm=3618, zyz=101, qsf_a=540, qsf_b=588, qsf_c=593
处理计划数据: yxdm=6501, zyz=101, qsf_a=574, qsf_b=562, qsf_c=557
处理计划数据: yxdm=3389, zyz=103, qsf_a=561, qsf_b=575, qsf_c=567
处理计划数据: yxdm=4437, zyz=101, qsf_a=567, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4437, zyz=101, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6202, zyz=102, qsf_a=567, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6202, zyz=102, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6501, zyz=101, qsf_a=571, qsf_b=560, qsf_c=564
处理计划数据: yxdm=4239, zyz=102, qsf_a=567, qsf_b=538, qsf_c=577
处理计划数据: yxdm=0023, zyz=102, qsf_a=566, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6202, zyz=101, qsf_a=564, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6501, zyz=102, qsf_a=571, qsf_b=561, qsf_c=552
处理计划数据: yxdm=6501, zyz=102, qsf_a=571, qsf_b=562, qsf_c=553
处理计划数据: yxdm=6202, zyz=102, qsf_a=569, qsf_b=557, qsf_c=559
处理计划数据: yxdm=6202, zyz=102, qsf_a=572, qsf_b=569, qsf_c=546
处理计划数据: yxdm=3516, zyz=102, qsf_a=563, qsf_b=569, qsf_c=542
处理计划数据: yxdm=6202, zyz=102, qsf_a=567, qsf_b=563, qsf_c=553
处理计划数据: yxdm=3618, zyz=105, qsf_a=551, qsf_b=585, qsf_c=596
处理计划数据: yxdm=4437, zyz=101, qsf_a=0, qsf_b=545, qsf_c=584
处理计划数据: yxdm=6202, zyz=102, qsf_a=563, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4239, zyz=102, qsf_a=568, qsf_b=539, qsf_c=567
处理计划数据: yxdm=6202, zyz=102, qsf_a=570, qsf_b=564, qsf_c=554
处理计划数据: yxdm=1123, zyz=103, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4239, zyz=102, qsf_a=567, qsf_b=539, qsf_c=567
处理计划数据: yxdm=2117, zyz=103, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=2117, zyz=103, qsf_a=567, qsf_b=558, qsf_c=550
处理计划数据: yxdm=1123, zyz=103, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3516, zyz=103, qsf_a=555, qsf_b=570, qsf_c=546
处理计划数据: yxdm=6501, zyz=102, qsf_a=572, qsf_b=562, qsf_c=539
处理计划数据: yxdm=6202, zyz=102, qsf_a=568, qsf_b=555, qsf_c=553
处理计划数据: yxdm=6202, zyz=102, qsf_a=563, qsf_b=558, qsf_c=0
处理计划数据: yxdm=6202, zyz=102, qsf_a=560, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3389, zyz=101, qsf_a=552, qsf_b=577, qsf_c=584
处理计划数据: yxdm=3389, zyz=101, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3921, zyz=102, qsf_a=0, qsf_b=566, qsf_c=546
处理计划数据: yxdm=3516, zyz=101, qsf_a=548, qsf_b=581, qsf_c=541
处理计划数据: yxdm=4508, zyz=101, qsf_a=535, qsf_b=597, qsf_c=571
处理计划数据: yxdm=4437, zyz=101, qsf_a=567, qsf_b=548, qsf_c=0
处理计划数据: yxdm=6202, zyz=102, qsf_a=563, qsf_b=559, qsf_c=554
处理计划数据: yxdm=6501, zyz=101, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6501, zyz=102, qsf_a=569, qsf_b=561, qsf_c=537
处理计划数据: yxdm=6202, zyz=102, qsf_a=565, qsf_b=557, qsf_c=553
处理计划数据: yxdm=6202, zyz=102, qsf_a=568, qsf_b=561, qsf_c=543
处理计划数据: yxdm=3389, zyz=103, qsf_a=557, qsf_b=572, qsf_c=549
处理计划数据: yxdm=6501, zyz=101, qsf_a=569, qsf_b=560, qsf_c=538
处理计划数据: yxdm=6202, zyz=102, qsf_a=567, qsf_b=555, qsf_c=549
处理计划数据: yxdm=6202, zyz=102, qsf_a=561, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6202, zyz=102, qsf_a=567, qsf_b=556, qsf_c=544
处理计划数据: yxdm=3618, zyz=101, qsf_a=545, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6202, zyz=102, qsf_a=564, qsf_b=551, qsf_c=0
处理计划数据: yxdm=6202, zyz=102, qsf_a=561, qsf_b=551, qsf_c=558
处理计划数据: yxdm=6202, zyz=101, qsf_a=561, qsf_b=569, qsf_c=545
处理计划数据: yxdm=6202, zyz=102, qsf_a=567, qsf_b=553, qsf_c=545
处理计划数据: yxdm=4437, zyz=101, qsf_a=558, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4437, zyz=101, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=6202, zyz=102, qsf_a=561, qsf_b=553, qsf_c=553
处理计划数据: yxdm=0023, zyz=103, qsf_a=549, qsf_b=578, qsf_c=562
处理计划数据: yxdm=6202, zyz=101, qsf_a=562, qsf_b=551, qsf_c=0
处理计划数据: yxdm=6202, zyz=101, qsf_a=558, qsf_b=552, qsf_c=550
处理计划数据: yxdm=6202, zyz=102, qsf_a=565, qsf_b=552, qsf_c=543
处理计划数据: yxdm=3516, zyz=102, qsf_a=544, qsf_b=569, qsf_c=541
处理计划数据: yxdm=5201, zyz=103, qsf_a=0, qsf_b=0, qsf_c=0
处理计划数据: yxdm=0023, zyz=103, qsf_a=545, qsf_b=580, qsf_c=559
处理计划数据: yxdm=5201, zyz=110, qsf_a=558, qsf_b=552, qsf_c=550
处理计划数据: yxdm=6202, zyz=102, qsf_a=561, qsf_b=551, qsf_c=546
处理计划数据: yxdm=6202, zyz=102, qsf_a=565, qsf_b=551, qsf_c=543
处理计划数据: yxdm=3516, zyz=101, qsf_a=544, qsf_b=581, qsf_c=546
处理计划数据: yxdm=6202, zyz=102, qsf_a=561, qsf_b=555, qsf_c=542
处理计划数据: yxdm=4437, zyz=101, qsf_a=560, qsf_b=545, qsf_c=0
处理计划数据: yxdm=6202, zyz=102, qsf_a=561, qsf_b=551, qsf_c=543
处理计划数据: yxdm=6202, zyz=101, qsf_a=561, qsf_b=556, qsf_c=544
处理计划数据: yxdm=6202, zyz=102, qsf_a=558, qsf_b=551, qsf_c=542
处理计划数据: yxdm=6202, zyz=102, qsf_a=560, qsf_b=552, qsf_c=544
处理计划数据: yxdm=6202, zyz=102, qsf_a=560, qsf_b=551, qsf_c=545
处理计划数据: yxdm=6202, zyz=102, qsf_a=560, qsf_b=0, qsf_c=544
处理计划数据: yxdm=3389, zyz=101, qsf_a=552, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3618, zyz=101, qsf_a=552, qsf_b=0, qsf_c=0
处理计划数据: yxdm=5201, zyz=102, qsf_a=554, qsf_b=542, qsf_c=546
处理计划数据: yxdm=3516, zyz=101, qsf_a=544, qsf_b=0, qsf_c=0
处理计划数据: yxdm=3618, zyz=105, qsf_a=544, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4508, zyz=103, qsf_a=522, qsf_b=568, qsf_c=552
处理计划数据: yxdm=4508, zyz=101, qsf_a=522, qsf_b=567, qsf_c=535
处理计划数据: yxdm=4617, zyz=103, qsf_a=540, qsf_b=539, qsf_c=545
处理计划数据: yxdm=4617, zyz=103, qsf_a=541, qsf_b=0, qsf_c=0
处理计划数据: yxdm=2308, zyz=107, qsf_a=519, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4617, zyz=101, qsf_a=544, qsf_b=541, qsf_c=532
处理计划数据: yxdm=6501, zyz=102, qsf_a=0, qsf_b=0, qsf_c=537
处理计划数据: yxdm=4508, zyz=101, qsf_a=527, qsf_b=537, qsf_c=546
处理计划数据: yxdm=3618, zyz=105, qsf_a=535, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4617, zyz=101, qsf_a=539, qsf_b=538, qsf_c=524
处理计划数据: yxdm=5102, zyz=102, qsf_a=534, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4617, zyz=103, qsf_a=527, qsf_b=537, qsf_c=532
处理计划数据: yxdm=4617, zyz=104, qsf_a=528, qsf_b=535, qsf_c=535
处理计划数据: yxdm=4617, zyz=104, qsf_a=527, qsf_b=535, qsf_c=534
处理计划数据: yxdm=4508, zyz=104, qsf_a=515, qsf_b=548, qsf_c=540
处理计划数据: yxdm=4617, zyz=104, qsf_a=526, qsf_b=534, qsf_c=534
处理计划数据: yxdm=2308, zyz=102, qsf_a=528, qsf_b=527, qsf_c=532
处理计划数据: yxdm=2308, zyz=102, qsf_a=526, qsf_b=527, qsf_c=534
处理计划数据: yxdm=4617, zyz=106, qsf_a=531, qsf_b=532, qsf_c=524
处理计划数据: yxdm=5102, zyz=105, qsf_a=529, qsf_b=530, qsf_c=527
处理计划数据: yxdm=4617, zyz=104, qsf_a=524, qsf_b=537, qsf_c=531
处理计划数据: yxdm=5102, zyz=106, qsf_a=529, qsf_b=527, qsf_c=521
处理计划数据: yxdm=4617, zyz=106, qsf_a=521, qsf_b=532, qsf_c=522
处理计划数据: yxdm=2308, zyz=102, qsf_a=522, qsf_b=522, qsf_c=525
处理计划数据: yxdm=4617, zyz=106, qsf_a=520, qsf_b=533, qsf_c=0
处理计划数据: yxdm=4617, zyz=105, qsf_a=515, qsf_b=532, qsf_c=528
处理计划数据: yxdm=2308, zyz=102, qsf_a=522, qsf_b=519, qsf_c=525
处理计划数据: yxdm=4617, zyz=105, qsf_a=515, qsf_b=533, qsf_c=524
处理计划数据: yxdm=2308, zyz=102, qsf_a=521, qsf_b=519, qsf_c=524
处理计划数据: yxdm=2308, zyz=102, qsf_a=521, qsf_b=516, qsf_c=521
处理计划数据: yxdm=2308, zyz=102, qsf_a=522, qsf_b=516, qsf_c=518
处理计划数据: yxdm=2308, zyz=102, qsf_a=520, qsf_b=516, qsf_c=520
处理计划数据: yxdm=2308, zyz=103, qsf_a=519, qsf_b=518, qsf_c=524
处理计划数据: yxdm=2308, zyz=101, qsf_a=519, qsf_b=516, qsf_c=518
处理计划数据: yxdm=4508, zyz=104, qsf_a=508, qsf_b=537, qsf_c=532
处理计划数据: yxdm=2308, zyz=101, qsf_a=520, qsf_b=517, qsf_c=0
处理计划数据: yxdm=2308, zyz=101, qsf_a=519, qsf_b=516, qsf_c=514
处理计划数据: yxdm=4508, zyz=105, qsf_a=469, qsf_b=536, qsf_c=527
处理计划数据: yxdm=4508, zyz=102, qsf_a=471, qsf_b=536, qsf_c=532
处理计划数据: yxdm=4508, zyz=104, qsf_a=464, qsf_b=537, qsf_c=536
处理计划数据: yxdm=4508, zyz=106, qsf_a=499, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4617, zyz=107, qsf_a=492, qsf_b=0, qsf_c=0
处理计划数据: yxdm=4617, zyz=107, qsf_a=492, qsf_b=466, qsf_c=0
处理计划数据: yxdm=4617, zyz=107, qsf_a=492, qsf_b=458, qsf_c=0
groupMajorBeanMap.size(): 1073
=== 调试日志5：开始渲染页面数据 ===
渲染志愿 1: seq_no_yx=1, yxdm=0048, yxmc_org=null
查找计划数据: uniqueKey=004829101, 找到JHBean=有数据
分数线数据: qsf_a=615, qsf_b=629, qsf_c=630
渲染志愿 2: seq_no_yx=2, yxdm=3389, yxmc_org=null
查找计划数据: uniqueKey=338940101, 找到JHBean=有数据
分数线数据: qsf_a=0, qsf_b=595, qsf_c=594
渲染志愿 3: seq_no_yx=3, yxdm=4508, yxmc_org=null
查找计划数据: uniqueKey=45081K104, 找到JHBean=有数据
分数线数据: qsf_a=515, qsf_b=548, qsf_c=540
渲染志愿 4: seq_no_yx=4, yxdm=2308, yxmc_org=null
查找计划数据: uniqueKey=230803104, 找到JHBean=有数据
分数线数据: qsf_a=633, qsf_b=637, qsf_c=639
渲染志愿 5: seq_no_yx=5, yxdm=4437, yxmc_org=null
查找计划数据: uniqueKey=443705101, 找到JHBean=有数据
分数线数据: qsf_a=598, qsf_b=580, qsf_c=612
渲染志愿 6: seq_no_yx=6, yxdm=5102, yxmc_org=null
查找计划数据: uniqueKey=51021Y108, 找到JHBean=有数据
分数线数据: qsf_a=602, qsf_b=600, qsf_c=603
渲染志愿 7: seq_no_yx=7, yxdm=3117, yxmc_org=null
查找计划数据: uniqueKey=311734104, 找到JHBean=有数据
分数线数据: qsf_a=598, qsf_b=589, qsf_c=596
渲染志愿 8: seq_no_yx=8, yxdm=3620, yxmc_org=null
查找计划数据: uniqueKey=362042101, 找到JHBean=有数据
分数线数据: qsf_a=589, qsf_b=613, qsf_c=611
渲染志愿 9: seq_no_yx=9, yxdm=5201, yxmc_org=null
查找计划数据: uniqueKey=5201B4107, 找到JHBean=有数据
分数线数据: qsf_a=603, qsf_b=588, qsf_c=602
渲染志愿 10: seq_no_yx=10, yxdm=0042, yxmc_org=null
查找计划数据: uniqueKey=0042X1107, 找到JHBean=有数据
分数线数据: qsf_a=0, qsf_b=0, qsf_c=598
渲染志愿 11: seq_no_yx=11, yxdm=9227, yxmc_org=null
查找计划数据: uniqueKey=922705102, 找到JHBean=有数据
分数线数据: qsf_a=601, qsf_b=630, qsf_c=630
渲染志愿 12: seq_no_yx=12, yxdm=4605, yxmc_org=null
查找计划数据: uniqueKey=46050W104, 找到JHBean=有数据
分数线数据: qsf_a=608, qsf_b=609, qsf_c=608
渲染志愿 13: seq_no_yx=13, yxdm=4617, yxmc_org=null
查找计划数据: uniqueKey=461706102, 找到JHBean=有数据
分数线数据: qsf_a=596, qsf_b=603, qsf_c=599
渲染志愿 14: seq_no_yx=14, yxdm=6101, yxmc_org=null
查找计划数据: uniqueKey=610166102, 找到JHBean=有数据
分数线数据: qsf_a=608, qsf_b=604, qsf_c=598
渲染志愿 15: seq_no_yx=15, yxdm=5201, yxmc_org=null
查找计划数据: uniqueKey=5201AT106, 找到JHBean=有数据
分数线数据: qsf_a=607, qsf_b=599, qsf_c=608
渲染志愿 16: seq_no_yx=16, yxdm=4301, yxmc_org=null
查找计划数据: uniqueKey=430138102, 找到JHBean=有数据
分数线数据: qsf_a=610, qsf_b=610, qsf_c=597
渲染志愿 17: seq_no_yx=17, yxdm=0023, yxmc_org=null
查找计划数据: uniqueKey=002350102, 找到JHBean=有数据
分数线数据: qsf_a=606, qsf_b=0, qsf_c=0
渲染志愿 18: seq_no_yx=18, yxdm=3620, yxmc_org=null
查找计划数据: uniqueKey=362062103, 找到JHBean=有数据
分数线数据: qsf_a=606, qsf_b=611, qsf_c=608
渲染志愿 19: seq_no_yx=19, yxdm=3618, yxmc_org=null
查找计划数据: uniqueKey=361829101, 找到JHBean=有数据
分数线数据: qsf_a=598, qsf_b=608, qsf_c=615
渲染志愿 20: seq_no_yx=20, yxdm=4501, yxmc_org=null
查找计划数据: uniqueKey=450115102, 找到JHBean=有数据
分数线数据: qsf_a=610, qsf_b=611, qsf_c=606
渲染志愿 21: seq_no_yx=21, yxdm=0056, yxmc_org=null
查找计划数据: uniqueKey=0056R2108, 找到JHBean=有数据
分数线数据: qsf_a=598, qsf_b=598, qsf_c=0
渲染志愿 22: seq_no_yx=22, yxdm=4501, yxmc_org=null
查找计划数据: uniqueKey=450156101, 找到JHBean=无数据
分数线数据: qsf_a=0, qsf_b=0, qsf_c=0
渲染志愿 23: seq_no_yx=23, yxdm=0009, yxmc_org=null
查找计划数据: uniqueKey=000919105, 找到JHBean=有数据
分数线数据: qsf_a=601, qsf_b=611, qsf_c=609
渲染志愿 24: seq_no_yx=24, yxdm=0058, yxmc_org=null
查找计划数据: uniqueKey=00588G105, 找到JHBean=有数据
分数线数据: qsf_a=602, qsf_b=589, qsf_c=0
渲染志愿 25: seq_no_yx=25, yxdm=6501, yxmc_org=null
查找计划数据: uniqueKey=650179102, 找到JHBean=有数据
分数线数据: qsf_a=606, qsf_b=602, qsf_c=598
渲染志愿 26: seq_no_yx=26, yxdm=3921, yxmc_org=null
查找计划数据: uniqueKey=392101101, 找到JHBean=有数据
分数线数据: qsf_a=638, qsf_b=635, qsf_c=597
渲染志愿 27: seq_no_yx=27, yxdm=2104, yxmc_org=null
查找计划数据: uniqueKey=210452108, 找到JHBean=有数据
分数线数据: qsf_a=602, qsf_b=603, qsf_c=602
渲染志愿 28: seq_no_yx=28, yxdm=4501, yxmc_org=null
查找计划数据: uniqueKey=450132103, 找到JHBean=有数据
分数线数据: qsf_a=596, qsf_b=597, qsf_c=599
渲染志愿 29: seq_no_yx=29, yxdm=3207, yxmc_org=null
查找计划数据: uniqueKey=32070J102, 找到JHBean=有数据
分数线数据: qsf_a=604, qsf_b=602, qsf_c=602
渲染志愿 30: seq_no_yx=30, yxdm=4405, yxmc_org=null
查找计划数据: uniqueKey=440575102, 找到JHBean=有数据
分数线数据: qsf_a=603, qsf_b=603, qsf_c=605
渲染志愿 31: seq_no_yx=31, yxdm=6202, yxmc_org=null
查找计划数据: uniqueKey=620263102, 找到JHBean=有数据
分数线数据: qsf_a=624, qsf_b=0, qsf_c=0
渲染志愿 32: seq_no_yx=32, yxdm=0037, yxmc_org=null
查找计划数据: uniqueKey=003734101, 找到JHBean=有数据
分数线数据: qsf_a=602, qsf_b=607, qsf_c=607
渲染志愿 33: seq_no_yx=33, yxdm=3516, yxmc_org=null
查找计划数据: uniqueKey=351608103, 找到JHBean=有数据
分数线数据: qsf_a=620, qsf_b=616, qsf_c=623
渲染志愿 34: seq_no_yx=34, yxdm=1310, yxmc_org=null
查找计划数据: uniqueKey=1310Z1102, 找到JHBean=有数据
分数线数据: qsf_a=603, qsf_b=602, qsf_c=605
渲染志愿 35: seq_no_yx=35, yxdm=2117, yxmc_org=null
查找计划数据: uniqueKey=211701101, 找到JHBean=有数据
分数线数据: qsf_a=642, qsf_b=642, qsf_c=650
渲染志愿 36: seq_no_yx=36, yxdm=4306, yxmc_org=null
查找计划数据: uniqueKey=430636102, 找到JHBean=有数据
分数线数据: qsf_a=607, qsf_b=615, qsf_c=607
渲染志愿 37: seq_no_yx=37, yxdm=0009, yxmc_org=null
查找计划数据: uniqueKey=000912106, 找到JHBean=有数据
分数线数据: qsf_a=625, qsf_b=621, qsf_c=0
渲染志愿 38: seq_no_yx=38, yxdm=1123, yxmc_org=null
查找计划数据: uniqueKey=11230H102, 找到JHBean=有数据
分数线数据: qsf_a=604, qsf_b=606, qsf_c=600
渲染志愿 39: seq_no_yx=39, yxdm=0056, yxmc_org=null
查找计划数据: uniqueKey=0056Z1110, 找到JHBean=有数据
分数线数据: qsf_a=599, qsf_b=597, qsf_c=595
渲染志愿 40: seq_no_yx=40, yxdm=0062, yxmc_org=null
查找计划数据: uniqueKey=006251102, 找到JHBean=有数据
分数线数据: qsf_a=618, qsf_b=619, qsf_c=616
渲染志愿 41: seq_no_yx=41, yxdm=4239, yxmc_org=null
查找计划数据: uniqueKey=423911101, 找到JHBean=无数据
分数线数据: qsf_a=0, qsf_b=0, qsf_c=0
渲染志愿 42: seq_no_yx=42, yxdm=4239, yxmc_org=null
查找计划数据: uniqueKey=42390K102, 找到JHBean=有数据
分数线数据: qsf_a=595, qsf_b=592, qsf_c=603
渲染志愿 43: seq_no_yx=43, yxdm=0064, yxmc_org=null
查找计划数据: uniqueKey=006403103, 找到JHBean=有数据
分数线数据: qsf_a=605, qsf_b=600, qsf_c=605
渲染志愿 44: seq_no_yx=44, yxdm=3304, yxmc_org=null
查找计划数据: uniqueKey=330411102, 找到JHBean=有数据
分数线数据: qsf_a=631, qsf_b=636, qsf_c=599
渲染志愿 45: seq_no_yx=45, yxdm=6301, yxmc_org=null
查找计划数据: uniqueKey=630150105, 找到JHBean=有数据
分数线数据: qsf_a=610, qsf_b=608, qsf_c=605
328, drag page time used
