package com.career.utils;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.career.db.LhyJDBC;

import java.util.ArrayList;
import java.util.Arrays;

/**
 * 体检数据缓存类
 * 存放体检代码枚举和专业限制匹配数据
 */
public class HealthDataUtils {
    
    /**
     * 体检项目枚举类
     */
    public static class HealthItem {
        private String code;
        private String name;
        private String type;
        
        public HealthItem(String code, String name, String type) {
            this.code = code;
            this.name = name;
            this.type = type;
        }
        
        public String getCode() { return code; }
        public String getName() { return name; }
        public String getType() { return type; }
    }
    
    // 体检代码映射表
    private static final Map<String, HealthItem> HEALTH_DATA_MAP = new HashMap<>();
    
    // 体检限制对应的专业匹配关系
    private static final Map<String, Set<String>> HEALTH_MAJOR_RESTRICTIONS = new HashMap<>();
    
    static {
        initHealthData();
        initMajorRestrictions();
    }
    
    /**
     * 初始化体检数据
     */
    private static void initHealthData() {
        HealthItem[] healthItems = {
            new HealthItem("00", "正常", "正常"),
            new HealthItem("11", "严重心脏病（先天性心脏病经手术治愈，或房室间隔缺损分流量少，动脉导管未闭返流血量少，经二级以上医院专科检查确定无需手术者除外），心肌病、高血压病。", "不予录取"),
            new HealthItem("12", "重症支气管扩张、哮喘、恶性肿瘤、慢性肾炎、尿毒症。", "不予录取"),
            new HealthItem("13", "严重的血液、内分泌及代谢系统疾病、风湿性疾病。", "不予录取"),
            new HealthItem("14", "重症或难治性癫痫或其他神经系统疾病；严重精神病未治愈、精神活性物质滥用和依赖。", "不予录取"),
            new HealthItem("15", "慢性肝炎病人并且肝功能不正常者（肝炎病原携带者但肝功能正常者除外）。", "不予录取"),
            new HealthItem("16", "结核病除下列情况外可以不予录取：（1）原发型肺结核、浸润性肺结核已硬结稳定；结核型胸膜炎已治愈或治愈后遗有胸膜肥厚者；（2）一切肺外结核（肾结核、骨结核、腹膜结核等等），血行性播散型肺结核治愈后一年以上未复发，经二级以上医院（或结核病防治所）专科检查无变化者；（3）淋巴腺结核已临床治愈无症状。", "不予录取"),
            new HealthItem("17", "乙型肝炎表面抗原携带者", "限制录取"),
            new HealthItem("21", "轻度色觉异常（俗称色弱）不能录取的专业：以颜色波长作为严格技术标准的化学类、化工与制药类、药学类、生物科学类、公安技术类、地质学类各专业，医学类各专业；生物工程、生物医学工程、动物医学、动物科学、野生动物与自然保护区管理、心理学、应用心理学、生态学、侦察学、特种能源工程与烟火技术、考古学、海洋科学、海洋技术、轮机工程、食品科学与工程、轻化工程、林产化工、农学、园艺、植物保护、茶学、林学、园林、蚕学、农业资源与环境、水产养殖学、海洋渔业科学与技术、材料化学、环境工程、高分子材料与工程、过程装备与控制工程、学前教育、特殊教育、体育教育、运动训练、运动人体科学、民族传统体育各专业。", "限制录取"),
            new HealthItem("22", "色觉异常II度（俗称色盲）不能录取的专业，除同轻度色觉异常外，还包括美术学、绘画、艺术设计、摄影、动画、博物馆学、应用物理学、天文学、地理科学、应用气象学、材料物理、矿物加工工程、资源勘探工程、冶金工程、无机非金属材料工程、交通运输、油气储运工程等专业。专科专业与以上专业相同或相近专业。", "限制录取"),
            new HealthItem("23-1", "不能准确识别红、黄、绿、兰、紫各种颜色中任何一种颜色的导线、按键、信号灯、几何图形者不能录取的专业：除同轻度色觉异常、色觉异常II度两类列出专业外，还包括经济学类、管理科学与工程类、工商管理类、公共管理类、农业经济管理类、图书档案学类各专业。不能准确在显示器上识别红、黄、绿、兰、紫各颜色中任何一种颜色的数码、字母者不能录取到计算机科学与技术等专业。", "限制录取"),
            new HealthItem("23-2", "不能准确识别红、黄、绿、兰、紫各种颜色中任何一种颜色的导线、按键、信号灯、几何图形者不能录取的专业：除同轻度色觉异常、色觉异常II度两类列出专业外，还包括经济学类、管理科学与工程类、工商管理类、公共管理类、农业经济管理类、图书档案学类各专业。不能准确在显示器上识别红、黄、绿、兰、紫各颜色中任何一种颜色的数码、字母者不能录取到计算机科学与技术等专业。", "限制录取"),
            new HealthItem("24", "裸眼视力任何一眼低于5.0者，不能录取的专业：飞行技术、航海技术、消防工程、刑事科学技术、侦察。专科专业：海洋船舶驾驶及与以上专业相同或相近专业（如民航空中交通管制）。", "限制录取"),
            new HealthItem("25", "裸眼视力任何一眼低于4.8者，不能录取的专业：轮机工程、运动训练、民族传统体育。专科专业：烹饪与营养、烹饪工艺等。", "限制录取"),
            new HealthItem("31", "主要脏器：肺、肝、肾、脾、胃肠等动过较大手术，功能恢复良好，或曾患有心肌炎、胃或十二指肠溃疡、慢性支气管炎、风湿性关节炎等病史、甲状腺机能亢进已治愈一年，不宜就读地矿类、水利类、交通运输类、能源动力类、公安学类、体育学类、海洋科学类、大气科学类、水产类、测绘类、海洋工程类、林业工程类、武器类、森林资源类、环境科学类、环境生态类、旅游管理类、草业科学类各专业，及土木工程、消防工程、农业水利工程、农学、法医学、水土保持与荒漠化防治、动物科学各专业。专科专业不宜就读烹饪工艺、西餐工艺、面点工艺、烹饪与营养、表演、舞蹈学、雕塑、考古学、地质学、建筑工程、交通土建工程、工业设备安装工程、铁道与桥梁工程、公路与城市道路工程、公路与桥梁工程、铁道工程、工业与民用建筑工程专业。", "不宜就读"),
            new HealthItem("32", "先天性心脏病手术治愈，或房室间隔缺损分流量少、动脉导管未闭，返流血量少，经二级以上医院专科检查确定无需手术者不宜就读的专业同第三部分第一条。", "不宜就读"),
            new HealthItem("33", "肢体残疾，生活能自理，受限专业同31条。", "不宜就读"),
            new HealthItem("34", "屈光不正（近视眼或远视眼，下同）任何一眼矫正到4.8镜片度数大于400度的，不宜就读海洋技术、海洋科学、测控技术与仪器、核工程与核技术、生物医学工程、服装设计与工程、飞行器制造工程。专科专业；与以上相同或相近专业。", "不宜就读"),
            new HealthItem("35", "任何一眼矫正到4.8镜片度数大于800度，不宜就读地矿类、水利类、土建类、动物生产类、水产类、材料类、能源动力类、化工与制药类、武器类、农业工程类、林业工程类、植物生产类、森林资源类、环境生态类、医学类、心理学类、环境与安全类、环境科学类、电子信息科学类、材料科学类、地质学类、大气科学类及地理科学、测绘工程、交通工程、交通运输、油气储运工程、船舶与海洋工程、生物工程、草业科学、动物医学各专业。专科专业：与以上相同或相近专业。", "不宜就读"),
            new HealthItem("36", "一眼失明，另一眼矫正到4.8镜片度数大于400度的，不宜就读工学、农学、医学、法学各专业及应用物理学、应用化学、生物技术、地质学、生态学、环境科学、海洋科学、海洋技术、生物科学、应用心理学等专业。", "不宜就读"),
            new HealthItem("37", "两耳听力均在3米以内，或一耳听力在5米另一耳全聋的，不宜就读法学各专业、外国语言文学各专业以及外交学、新闻学、侦察学、学前教育、音乐学、录音艺术、土木工程、交通运输、动物科学、动物医学各专业、医学各专业。", "不宜就读"),
            new HealthItem("38", "嗅觉迟钝、口吃、步态异常、驼背，面部有疤痕、血管瘤、黑色素痣、白癜风的，不宜就读教育学类、公安学类各专业以及外交学、法学、新闻学、音乐表演、表演各专业。", "不宜就读"),
            new HealthItem("39", "斜视、嗅觉迟钝、口吃不宜就读医学类专业。", "不宜就读")
        };
        
        for (HealthItem item : healthItems) {
            HEALTH_DATA_MAP.put(item.getCode(), item);
        }
    }
    
    /**
     * 初始化专业限制匹配关系
     */
    private static void initMajorRestrictions() {
        // 不予录取的专业限制（代码11-16对所有专业都有限制）
        Set<String> noAdmissionCodes = new HashSet<>(Arrays.asList("11", "12", "13", "14", "15", "16"));
        HEALTH_MAJOR_RESTRICTIONS.put("ALL_MAJORS", noAdmissionCodes);
        
        
        // 17 - 乙型肝炎表面抗原携带者不能录取的专业
        Set<String> hepatitisBCodes = new HashSet<>(Arrays.asList("17"));
        HEALTH_MAJOR_RESTRICTIONS.put("学前教育", hepatitisBCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("航海技术", hepatitisBCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("飞行技术", hepatitisBCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("面点工艺", hepatitisBCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("西餐工艺", hepatitisBCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("烹饪与营养", hepatitisBCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("烹饪工艺", hepatitisBCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("食品科学与工程", hepatitisBCodes);
        
        // 21 - 轻度色觉异常限制的专业
        HEALTH_MAJOR_RESTRICTIONS.put("化学", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("化工", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("制药", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("药学", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("生物科学", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("公安技术", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("地质学", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("医学", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("生物工程", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("生物医学工程", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("动物医学", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("动物科学", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("野生动物与自然保护区管理", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("心理学", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("应用心理学", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("生态学", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("侦察学", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("特种能源工程与烟火技术", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("考古学", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("海洋科学", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("海洋技术", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("轮机工程", new HashSet<>(Arrays.asList("21", "25")));
        HEALTH_MAJOR_RESTRICTIONS.put("食品科学与工程", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("轻化工程", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("林产化工", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("农学", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("园艺", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("植物保护", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("茶学", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("林学", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("园林", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("蚕学", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("农业资源与环境", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("水产养殖学", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("海洋渔业科学与技术", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("材料化学", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("环境工程", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("高分子材料与工程", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("过程装备与控制工程", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("学前教育", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("特殊教育", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("体育教育", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("运动训练", new HashSet<>(Arrays.asList("21", "25")));
        HEALTH_MAJOR_RESTRICTIONS.put("运动人体科学", new HashSet<>(Arrays.asList("21")));
        HEALTH_MAJOR_RESTRICTIONS.put("民族传统体育", new HashSet<>(Arrays.asList("21", "25")));
        
        // 22 - 色觉异常II度（色盲）额外限制的专业
        HEALTH_MAJOR_RESTRICTIONS.put("美术学", new HashSet<>(Arrays.asList("22")));
        HEALTH_MAJOR_RESTRICTIONS.put("绘画", new HashSet<>(Arrays.asList("22")));
        HEALTH_MAJOR_RESTRICTIONS.put("艺术设计", new HashSet<>(Arrays.asList("22")));
        HEALTH_MAJOR_RESTRICTIONS.put("摄影", new HashSet<>(Arrays.asList("22")));
        HEALTH_MAJOR_RESTRICTIONS.put("动画", new HashSet<>(Arrays.asList("22")));
        HEALTH_MAJOR_RESTRICTIONS.put("博物馆学", new HashSet<>(Arrays.asList("22")));
        HEALTH_MAJOR_RESTRICTIONS.put("应用物理学", new HashSet<>(Arrays.asList("22")));
        HEALTH_MAJOR_RESTRICTIONS.put("天文学", new HashSet<>(Arrays.asList("22")));
        HEALTH_MAJOR_RESTRICTIONS.put("地理科学", new HashSet<>(Arrays.asList("22")));
        HEALTH_MAJOR_RESTRICTIONS.put("应用气象学", new HashSet<>(Arrays.asList("22")));
        HEALTH_MAJOR_RESTRICTIONS.put("材料物理", new HashSet<>(Arrays.asList("22")));
        HEALTH_MAJOR_RESTRICTIONS.put("矿物加工工程", new HashSet<>(Arrays.asList("22")));
        HEALTH_MAJOR_RESTRICTIONS.put("资源勘探工程", new HashSet<>(Arrays.asList("22")));
        HEALTH_MAJOR_RESTRICTIONS.put("冶金工程", new HashSet<>(Arrays.asList("22")));
        HEALTH_MAJOR_RESTRICTIONS.put("无机非金属材料工程", new HashSet<>(Arrays.asList("22")));
        HEALTH_MAJOR_RESTRICTIONS.put("交通运输", new HashSet<>(Arrays.asList("22")));
        HEALTH_MAJOR_RESTRICTIONS.put("油气储运工程", new HashSet<>(Arrays.asList("22")));
        
        // 23-1, 23-2 - 颜色识别异常限制的专业
        Set<String> colorBlindCodes = new HashSet<>(Arrays.asList("23-1", "23-2"));
        HEALTH_MAJOR_RESTRICTIONS.put("经济学", colorBlindCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("管理科学与工程", colorBlindCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("工商管理", colorBlindCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("公共管理", colorBlindCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("农业经济管理", colorBlindCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("图书档案学", colorBlindCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("计算机科学与技术", colorBlindCodes);
        
        // 24 - 裸眼视力5.0以下限制的专业
        HEALTH_MAJOR_RESTRICTIONS.put("飞行技术", new HashSet<>(Arrays.asList("24")));
        HEALTH_MAJOR_RESTRICTIONS.put("航海技术", new HashSet<>(Arrays.asList("24")));
        HEALTH_MAJOR_RESTRICTIONS.put("消防工程", new HashSet<>(Arrays.asList("24")));
        HEALTH_MAJOR_RESTRICTIONS.put("刑事科学技术", new HashSet<>(Arrays.asList("24")));
        HEALTH_MAJOR_RESTRICTIONS.put("侦察", new HashSet<>(Arrays.asList("24")));
        HEALTH_MAJOR_RESTRICTIONS.put("海洋船舶驾驶", new HashSet<>(Arrays.asList("24")));
        HEALTH_MAJOR_RESTRICTIONS.put("民航空中交通管制", new HashSet<>(Arrays.asList("24")));
        
        // 25 - 裸眼视力4.8以下限制的专业（已在上面添加到相关专业）
        HEALTH_MAJOR_RESTRICTIONS.put("烹饪与营养", new HashSet<>(Arrays.asList("25")));
        HEALTH_MAJOR_RESTRICTIONS.put("烹饪工艺", new HashSet<>(Arrays.asList("25")));

        // 31-39 - 不宜就读的专业限制
        initNotSuitableMajorRestrictions();
    }
    
    /**
     * 初始化不宜就读的专业限制
     */
    private static void initNotSuitableMajorRestrictions() {
        // 31, 32, 33 - 主要脏器手术/先天性心脏病/肢体残疾不宜就读的专业
        Set<String> organSurgeryCodes = new HashSet<>(Arrays.asList("31", "32", "33"));
        
        // 专业大类
        HEALTH_MAJOR_RESTRICTIONS.put("地矿", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("水利", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("交通运输", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("能源动力", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("公安学", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("体育学", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("海洋科学", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("大气科学", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("水产", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("测绘", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("海洋工程", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("林业工程", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("武器", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("森林资源", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("环境科学", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("环境生态", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("旅游管理", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("草业科学", organSurgeryCodes);
        
        // 具体专业 - 使用新的Set避免冲突
        Set<String> organSurgerySpecificCodes = new HashSet<>(Arrays.asList("31", "32", "33"));
        HEALTH_MAJOR_RESTRICTIONS.put("土木工程", organSurgerySpecificCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("农业水利工程", organSurgerySpecificCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("法医学", organSurgerySpecificCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("水土保持与荒漠化防治", organSurgerySpecificCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("农学", organSurgerySpecificCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("动物科学", organSurgerySpecificCodes);
        
        // 专科专业
        HEALTH_MAJOR_RESTRICTIONS.put("烹饪工艺", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("西餐工艺", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("面点工艺", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("烹饪与营养", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("表演", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("舞蹈学", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("雕塑", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("考古学", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("地质学", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("建筑工程", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("交通土建工程", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("工业设备安装工程", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("铁道与桥梁工程", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("公路与城市道路工程", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("公路与桥梁工程", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("铁道工程", organSurgeryCodes);
        HEALTH_MAJOR_RESTRICTIONS.put("工业与民用建筑工程", organSurgeryCodes);
        
        // 34 - 屈光不正400度以上不宜就读的专业
        Set<String> visionCode34 = new HashSet<>(Arrays.asList("34"));
        HEALTH_MAJOR_RESTRICTIONS.put("海洋技术", visionCode34);
        HEALTH_MAJOR_RESTRICTIONS.put("海洋科学", visionCode34);
        HEALTH_MAJOR_RESTRICTIONS.put("测控技术与仪器", visionCode34);
        HEALTH_MAJOR_RESTRICTIONS.put("核工程与核技术", visionCode34);
        HEALTH_MAJOR_RESTRICTIONS.put("生物医学工程", visionCode34);
        HEALTH_MAJOR_RESTRICTIONS.put("服装设计与工程", visionCode34);
        HEALTH_MAJOR_RESTRICTIONS.put("飞行器制造工程", visionCode34);
        
        // 35 - 屈光不正800度以上不宜就读的专业
        Set<String> visionCode35 = new HashSet<>(Arrays.asList("35"));
        HEALTH_MAJOR_RESTRICTIONS.put("地矿", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("水利", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("土建", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("动物生产", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("水产", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("材料", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("能源动力", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("化工与制药", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("武器", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("农业工程", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("林业工程", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("植物生产", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("森林资源", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("环境生态", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("医学", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("心理学", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("环境与安全", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("环境科学", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("电子信息科学", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("材料科学", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("地质学", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("大气科学", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("地理科学", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("测绘工程", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("交通工程", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("交通运输", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("油气储运工程", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("船舶与海洋工程", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("生物工程", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("草业科学", visionCode35);
        HEALTH_MAJOR_RESTRICTIONS.put("动物医学", visionCode35);
        
        // 36 - 一眼失明另一眼矫正400度以上不宜就读的专业
        Set<String> visionCode36 = new HashSet<>(Arrays.asList("36"));
        HEALTH_MAJOR_RESTRICTIONS.put("工学", visionCode36);
        HEALTH_MAJOR_RESTRICTIONS.put("农学", visionCode36);
        HEALTH_MAJOR_RESTRICTIONS.put("医学", visionCode36);
        HEALTH_MAJOR_RESTRICTIONS.put("法学", visionCode36);
        HEALTH_MAJOR_RESTRICTIONS.put("应用物理学", visionCode36);
        HEALTH_MAJOR_RESTRICTIONS.put("应用化学", visionCode36);
        HEALTH_MAJOR_RESTRICTIONS.put("生物技术", visionCode36);
        HEALTH_MAJOR_RESTRICTIONS.put("地质学", visionCode36);
        HEALTH_MAJOR_RESTRICTIONS.put("生态学", visionCode36);
        HEALTH_MAJOR_RESTRICTIONS.put("环境科学", visionCode36);
        HEALTH_MAJOR_RESTRICTIONS.put("海洋科学", visionCode36);
        HEALTH_MAJOR_RESTRICTIONS.put("海洋技术", visionCode36);
        HEALTH_MAJOR_RESTRICTIONS.put("生物科学", visionCode36);
        HEALTH_MAJOR_RESTRICTIONS.put("应用心理学", visionCode36);
        
        // 37 - 听力障碍不宜就读的专业
        Set<String> hearingCode37 = new HashSet<>(Arrays.asList("37"));
        HEALTH_MAJOR_RESTRICTIONS.put("法学", hearingCode37);
        HEALTH_MAJOR_RESTRICTIONS.put("外国语言文学", hearingCode37);
        HEALTH_MAJOR_RESTRICTIONS.put("外交学", hearingCode37);
        HEALTH_MAJOR_RESTRICTIONS.put("新闻学", hearingCode37);
        HEALTH_MAJOR_RESTRICTIONS.put("侦察学", hearingCode37);
        HEALTH_MAJOR_RESTRICTIONS.put("学前教育", hearingCode37);
        HEALTH_MAJOR_RESTRICTIONS.put("音乐学", hearingCode37);
        HEALTH_MAJOR_RESTRICTIONS.put("录音艺术", hearingCode37);
        HEALTH_MAJOR_RESTRICTIONS.put("土木工程", hearingCode37);
        HEALTH_MAJOR_RESTRICTIONS.put("交通运输", hearingCode37);
        HEALTH_MAJOR_RESTRICTIONS.put("动物科学", hearingCode37);
        HEALTH_MAJOR_RESTRICTIONS.put("动物医学", hearingCode37);
        HEALTH_MAJOR_RESTRICTIONS.put("医学", hearingCode37);
        
        // 38 - 外观异常不宜就读的专业
        Set<String> appearanceCode38 = new HashSet<>(Arrays.asList("38"));
        HEALTH_MAJOR_RESTRICTIONS.put("教育学", appearanceCode38);
        HEALTH_MAJOR_RESTRICTIONS.put("公安学", appearanceCode38);
        HEALTH_MAJOR_RESTRICTIONS.put("外交学", appearanceCode38);
        HEALTH_MAJOR_RESTRICTIONS.put("法学", appearanceCode38);
        HEALTH_MAJOR_RESTRICTIONS.put("新闻学", appearanceCode38);
        HEALTH_MAJOR_RESTRICTIONS.put("音乐表演", appearanceCode38);
        HEALTH_MAJOR_RESTRICTIONS.put("表演", appearanceCode38);
        
        // 39 - 斜视、嗅觉迟钝、口吃不宜就读医学类专业
        Set<String> medicalCode39 = new HashSet<>(Arrays.asList("39"));
        HEALTH_MAJOR_RESTRICTIONS.put("医学", medicalCode39);
    }
    

	/**
	 * 根据用户ID和专业名称判断是否适合就读该专业
	 * 
	 * @param rtUserId 用户ID
	 * @param majorName 专业名称
	 * @return true表示适合就读，false表示不适合就读
	 */
	public static boolean isMajorSuitableForUser(String rtUserId, String majorName) {
		try {
			// 1. 查询用户体检情况
			LhyJDBC jdbc = new LhyJDBC();
			String physicalExamJson = jdbc.getUserPhysicalExamByUserId(rtUserId);
			
			if (Tools.isEmpty(physicalExamJson)) {
				// 如果没有体检数据，默认认为适合
				return true;
			}
			
			// 2. 解析体检代码
			List<String> healthCodes = parsePhysicalExamCodes(physicalExamJson);
			
			if (healthCodes == null || healthCodes.isEmpty()) {
				// 如果没有解析到体检代码，默认认为适合
				return true;
			}
			
			// 3. 检查是否有不予录取的限制（代码11-16对所有专业都有限制）
			if (hasNoAdmissionRestriction(healthCodes)) {
				return false;
			}
			
			// 4. 检查专业特定限制
			return !hasMajorSpecificRestriction(healthCodes, majorName);
			
		} catch (Exception e) {
			e.printStackTrace();
			// 异常情况下默认返回true，避免误判
			return true;
		}
	}
	
	/**
	 * 解析体检情况JSON，提取体检代码列表
	 * 
	 * @param physicalExamJson 体检情况JSON字符串，格式如：["24_25_34"]
	 * @return 体检代码列表
	 */
	private static List<String> parsePhysicalExamCodes(String physicalExamJson) {
		List<String> codes = new ArrayList<>();
		
		try {
			if (Tools.isEmpty(physicalExamJson)) {
				return codes;
			}
			
			// 去除JSON格式的方括号和引号
			String content = physicalExamJson.trim();
			if (content.startsWith("[") && content.endsWith("]")) {
				content = content.substring(1, content.length() - 1);
			}
			if (content.startsWith("\"") && content.endsWith("\"")) {
				content = content.substring(1, content.length() - 1);
			}
			
			// 按下划线分割代码
			if (!Tools.isEmpty(content)) {
				String[] codeArray = content.split("_");
				for (String code : codeArray) {
					if (!Tools.isEmpty(code.trim())) {
						codes.add(code.trim());
					}
				}
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		return codes;
	}
	
	/**
	 * 检查是否有不予录取的限制
	 * 
	 * @param healthCodes 用户的体检代码列表
	 * @return true表示有不予录取的限制
	 */
	private static boolean hasNoAdmissionRestriction(List<String> healthCodes) {
		java.util.Set<String> noAdmissionCodes = HealthDataUtils.getAllMajorRestrictions().get("ALL_MAJORS");
		if (noAdmissionCodes != null) {
			for (String code : healthCodes) {
				if (noAdmissionCodes.contains(code)) {
					return true;
				}
			}
		}
		return false;
	}
	
	/**
	 * 检查专业特定限制
	 * 
	 * @param healthCodes 用户的体检代码列表
	 * @param majorName 专业名称
	 * @return true表示有专业特定限制
	 */
	private static boolean hasMajorSpecificRestriction(List<String> healthCodes, String majorName) {
		if (Tools.isEmpty(majorName) || healthCodes == null || healthCodes.isEmpty()) {
			return false;
		}
		
		// 获取所有专业限制映射
		java.util.Map<String, java.util.Set<String>> allRestrictions = HealthDataUtils.getAllMajorRestrictions();
		
		// 遍历所有专业限制规则，使用包含匹配
		for (java.util.Map.Entry<String, java.util.Set<String>> entry : allRestrictions.entrySet()) {
			String restrictionKey = entry.getKey();
			java.util.Set<String> restrictionCodes = entry.getValue();
			
			// 跳过"ALL_MAJORS"这个特殊的键
			if ("ALL_MAJORS".equals(restrictionKey)) {
				continue;
			}
			
			// 使用包含匹配：如果专业名称包含限制关键词
			if (majorName.contains(restrictionKey)) {
				// 检查用户的体检代码是否在限制列表中
				for (String healthCode : healthCodes) {
					if (restrictionCodes.contains(healthCode)) {
						return true; // 找到限制，不适合就读
					}
				}
			}
		}
		
		return false;
	}
	
    /**
     * 获取体检项目信息
     */
    public static HealthItem getHealthItem(String code) {
        return HEALTH_DATA_MAP.get(code);
    }
    
    /**
     * 获取专业的体检限制代码集合
     */
    public static Set<String> getMajorRestrictions(String majorName) {
        return HEALTH_MAJOR_RESTRICTIONS.get(majorName);
    }
    
    /**
     * 获取所有专业限制映射
     */
    public static Map<String, Set<String>> getAllMajorRestrictions() {
        return HEALTH_MAJOR_RESTRICTIONS;
    }
    
    /**
     * 获取所有体检数据映射
     */
    public static Map<String, HealthItem> getAllHealthData() {
        return HEALTH_DATA_MAP;
    }
    
    
    //  =================================================
    //  测试代码区域 （临时）
    //  =================================================
    public static void testHealthCheckFunction() {
        System.out.println("========== 体检限制功能测试 ==========");
        
        // 测试1：无体检限制的情况
        System.out.println("\n测试1：用户无体检数据");
        boolean result1 = HealthDataUtils.isMajorSuitableForUser("test_user_2", "计算机科学与技术");
        System.out.println("专业：计算机科学与技术，结果：" + (result1 ? "适合" : "不适合"));
        
        // 测试2：测试JSON解析功能
        System.out.println("\n测试2：测试JSON解析功能");
        java.util.List<String> codes = parsePhysicalExamCodesTest("[\"24_25_34\"]");
        System.out.println("解析结果：" + codes);
        
        // 测试3：测试不予录取限制
        System.out.println("\n测试3：测试不予录取限制");
        java.util.List<String> noAdmissionCodes = java.util.Arrays.asList("11", "12");
        boolean hasNoAdmission = hasNoAdmissionRestrictionTest(noAdmissionCodes);
        System.out.println("代码11,12 - 是否有不予录取限制：" + hasNoAdmission);
        
        // 测试4：测试专业特定限制
        System.out.println("\n测试4：测试专业特定限制");
        java.util.List<String> colorBlindCodes = java.util.Arrays.asList("21", "22");
        boolean hasColorBlindRestriction = hasMajorSpecificRestrictionTest(colorBlindCodes, "化学工程与工艺");
        System.out.println("代码21,22 + 化学工程与工艺专业 - 是否有限制：" + hasColorBlindRestriction);
        
        // 测试5：测试视力限制
        System.out.println("\n测试5：测试视力限制");
        java.util.List<String> visionCodes = java.util.Arrays.asList("24");
        boolean hasVisionRestriction = hasMajorSpecificRestrictionTest(visionCodes, "飞行技术");
        System.out.println("代码24 + 飞行技术专业 - 是否有限制：" + hasVisionRestriction);
        
        // 测试6：测试正常专业（无限制）
        System.out.println("\n测试6：测试正常专业");
        java.util.List<String> normalCodes = java.util.Arrays.asList("00");
        boolean hasNormalRestriction = hasMajorSpecificRestrictionTest(normalCodes, "软件工程");
        System.out.println("代码00 + 软件工程专业 - 是否有限制：" + hasNormalRestriction);
        
        System.out.println("\n========== 测试完成 ==========");
    }
    
    // 测试用的辅助方法
    private static java.util.List<String> parsePhysicalExamCodesTest(String physicalExamJson) {
        java.util.List<String> codes = new java.util.ArrayList<>();
        
        try {
            if (Tools.isEmpty(physicalExamJson)) {
                return codes;
            }
            
            String content = physicalExamJson.trim();
            if (content.startsWith("[") && content.endsWith("]")) {
                content = content.substring(1, content.length() - 1);
            }
            if (content.startsWith("\"") && content.endsWith("\"")) {
                content = content.substring(1, content.length() - 1);
            }
            
            if (!Tools.isEmpty(content)) {
                String[] codeArray = content.split("_");
                for (String code : codeArray) {
                    if (!Tools.isEmpty(code.trim())) {
                        codes.add(code.trim());
                    }
                }
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return codes;
    }
    
    private static boolean hasNoAdmissionRestrictionTest(java.util.List<String> healthCodes) {
        java.util.Set<String> noAdmissionCodes = HealthDataUtils.getAllMajorRestrictions().get("ALL_MAJORS");
        if (noAdmissionCodes != null) {
            for (String code : healthCodes) {
                if (noAdmissionCodes.contains(code)) {
                    return true;
                }
            }
        }
        return false;
    }
    
    private static boolean hasMajorSpecificRestrictionTest(java.util.List<String> healthCodes, String majorName) {
        if (Tools.isEmpty(majorName) || healthCodes == null || healthCodes.isEmpty()) {
            return false;
        }
        
        java.util.Map<String, java.util.Set<String>> allRestrictions = HealthDataUtils.getAllMajorRestrictions();
        
        for (java.util.Map.Entry<String, java.util.Set<String>> entry : allRestrictions.entrySet()) {
            String restrictionKey = entry.getKey();
            java.util.Set<String> restrictionCodes = entry.getValue();
            
            if ("ALL_MAJORS".equals(restrictionKey)) {
                continue;
            }
            
            if (majorName.contains(restrictionKey)) {
                for (String healthCode : healthCodes) {
                    if (restrictionCodes.contains(healthCode)) {
                        return true;
                    }
                }
            }
        }
        
        return false;
    }
    
    public static void main(String[] args) {
        testHealthCheckFunction();
    }
    
}