package com.career.utils.zsky;

import java.util.Arrays;

import com.alibaba.fastjson2.JSONObject;


public class SchoolInfo {

	private int school_id;
	private String school_name;
	private String province;
	private int top_value;
	private String[] feature;
	private int is_apply;
	private String[] school_site;
	private String[] school_phone;
	private String[] school_email;
	private String intro;
	private String content_id;
	private Rank[] rank;
	private int num_master;
	private int num_master_2nd;
	private int num_doctor;
	private int num_doctor_2nd;
	private int num_subject;
	private int num_lab;
	private int create_date;
	private String belongsTo;
	private int school_space;
	private String school_address;

	public int getSchool_id() {
		return school_id;
	}

	public void setSchool_id(int school_id) {
		this.school_id = school_id;
	}

	public String getSchool_name() {
		return school_name;
	}

	public void setSchool_name(String school_name) {
		this.school_name = school_name;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public int getTop_value() {
		return top_value;
	}

	public void setTop_value(int top_value) {
		this.top_value = top_value;
	}

	public String[] getFeature() {
		return feature;
	}

	public void setFeature(String[] feature) {
		this.feature = feature;
	}

	public int getIs_apply() {
		return is_apply;
	}

	public void setIs_apply(int is_apply) {
		this.is_apply = is_apply;
	}

	public String[] getSchool_site() {
		return school_site;
	}

	public void setSchool_site(String[] school_site) {
		this.school_site = school_site;
	}

	public String[] getSchool_phone() {
		return school_phone;
	}

	public void setSchool_phone(String[] school_phone) {
		this.school_phone = school_phone;
	}

	public String[] getSchool_email() {
		return school_email;
	}

	public void setSchool_email(String[] school_email) {
		this.school_email = school_email;
	}

	public String getIntro() {
		return intro;
	}

	public void setIntro(String intro) {
		this.intro = intro;
	}

	public String getContent_id() {
		return content_id;
	}

	public void setContent_id(String content_id) {
		this.content_id = content_id;
	}

	public Rank[] getRank() {
		return rank;
	}

	public void setRank(Rank[] rank) {
		this.rank = rank;
	}

	public int getNum_master() {
		return num_master;
	}

	public void setNum_master(int num_master) {
		this.num_master = num_master;
	}

	public int getNum_master_2nd() {
		return num_master_2nd;
	}

	public void setNum_master_2nd(int num_master_2nd) {
		this.num_master_2nd = num_master_2nd;
	}

	public int getNum_doctor() {
		return num_doctor;
	}

	public void setNum_doctor(int num_doctor) {
		this.num_doctor = num_doctor;
	}

	public int getNum_doctor_2nd() {
		return num_doctor_2nd;
	}

	public void setNum_doctor_2nd(int num_doctor_2nd) {
		this.num_doctor_2nd = num_doctor_2nd;
	}

	public int getNum_subject() {
		return num_subject;
	}

	public void setNum_subject(int num_subject) {
		this.num_subject = num_subject;
	}

	public int getNum_lab() {
		return num_lab;
	}

	public void setNum_lab(int num_lab) {
		this.num_lab = num_lab;
	}

	public int getCreate_date() {
		return create_date;
	}

	public void setCreate_date(int create_date) {
		this.create_date = create_date;
	}

	public String getBelongsTo() {
		return belongsTo;
	}

	public void setBelongsTo(String belongsTo) {
		this.belongsTo = belongsTo;
	}

	public int getSchool_space() {
		return school_space;
	}

	public void setSchool_space(int school_space) {
		this.school_space = school_space;
	}

	public String getSchool_address() {
		return school_address;
	}

	public void setSchool_address(String school_address) {
		this.school_address = school_address;
	}

	public String generateSQL() {
		intro = intro.replaceAll(" ", "").replaceAll("'", "").replaceAll("‘", "");
		intro = intro.trim();
		String SQL = "insert into career_university(school_id, school_name, province, top_value, feature, is_apply, school_site, school_phone, school_email, intro, content_id, rank_info, num_master, num_master_2nd, num_doctor, num_doctor_2nd, num_subject, num_lab"
				+ ", create_date, belongsTo, school_space, school_address) values(" + school_id + ", '" + school_name.trim()
				+ "', '" + province + "', " + top_value + ", '" + Arrays.toString(feature) + "', " + is_apply + ", '" + JSONObject.toJSONString(school_site) + "', '"
				+ Arrays.toString(school_phone) + "', '" + Arrays.toString(school_email) + "', '" + intro + "', '" + content_id + "', '" + JSONObject.toJSONString(rank) + "', "
				+ num_master + ", " + num_master_2nd + ", " + num_doctor + ", " + num_doctor_2nd + ", " + num_subject
				+ ", " + num_lab + ", " + create_date + ", '" + belongsTo + "', " + school_space + ", '" + school_address
				+ "');";
		return SQL;
	}


}
