package com.zsdwf.db;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;

public class CCache2 {
  public static void main(String[] args) {
    
  }
  
  public static HashMap<String, MarjorBean> CC_ZY_EXT = new HashMap<>();
  
  public static HashMap<String, String> CC_YX_FX = new HashMap<>();
  
  public static HashMap<String, String> CC_ZY_CATEGORY = new HashMap<>();
  
  public static HashMap<String, List<String>> CC_XK = new HashMap<>();
  
  public static List<String> CC_XK_LIST_63 = new ArrayList<>();
  
  public static List<String> CC_XK_LIST_73 = new ArrayList<>();
  
  public static List<String> CC_XK_LIST_312 = new ArrayList<>();
  
  public static List<String> CC_XK_LIST_11 = new ArrayList<>();
  
  public static HashSet<String> CC_RY_985 = new HashSet<>();
  
  public static HashSet<String> CC_RY_211 = new HashSet<>();
  
  public static HashSet<String> CC_RY_SYL = new HashSet<>();
  
  public static HashSet<String> CC_RY_QJ = new HashSet<>();
  
  public static HashSet<String> CC_RY_X985 = new HashSet<>();
  
  public static HashSet<String> CC_RY_X211 = new HashSet<>();
  
  public static int CC_PAGER_SIZE = 30;
  
  public static int CC_VISIT_LOCK_CNT = 15;
  
  public static String SYS_NAME = "规划师一对一辅助系统"; 
  
  public static HashMap<String, Integer> HM_MAX_WC = new HashMap<>();
  
  static {
	  CC_RY_X985.add("哈尔滨工程大学");
	  CC_RY_X985.add("南京理工大学");
	  CC_RY_X985.add("南京航空航天大学");
	  CC_RY_X985.add("西安电子科技大学");
	  CC_RY_X985.add("北京邮电大学");
	  CC_RY_X985.add("中国传媒大学");
	  CC_RY_X985.add("北京科技大学");
	  CC_RY_X985.add("武汉理工大学");
	  CC_RY_X985.add("北京交通大学");
	  CC_RY_X985.add("西南交通大学");
	  CC_RY_X985.add("长安大学");
	  CC_RY_X985.add("华东理工大学");
	  CC_RY_X985.add("北京化工大学");
	  CC_RY_X985.add("中国矿业大学");
	  CC_RY_X985.add("中国地质大学");
	  CC_RY_X985.add("中国石油大学");
	  CC_RY_X985.add("华北电力大学");
	  CC_RY_X985.add("河海大学");
	  CC_RY_X985.add("合肥工业大学");
	  CC_RY_X985.add("江南大学");
	  CC_RY_X985.add("中央财经大学");
	  CC_RY_X985.add("上海财经大学");
	  CC_RY_X985.add("中南财经政法大学");
	  CC_RY_X985.add("西南财经大学");
	  CC_RY_X985.add("北京林业大学");
	  CC_RY_X985.add("东北林业大学");
	  CC_RY_X985.add("华中农业大学");
	  CC_RY_X985.add("南京农业大学");
	  CC_RY_X985.add("西南大学");
	  CC_RY_X985.add("暨南大学");
	  CC_RY_X985.add("中国政法大学");
	  CC_RY_X985.add("中国药科大学");
	  CC_RY_X985.add("北京中医药大学");
	  CC_RY_X985.add("陕西师范大学");
	  CC_RY_X985.add("华中师范大学");
	  CC_RY_X985.add("东北师范大学");
	  CC_XK_LIST_11.add("理科");
	  CC_XK_LIST_11.add("文科");
	  CC_XK_LIST_73.add("");
	  CC_XK_LIST_63.add("物理+政治+化学");
	  CC_XK_LIST_63.add("物理+政治+生物");
	  CC_XK_LIST_63.add("物理+政治+地理");
	  CC_XK_LIST_63.add("物理+政治+历史");
	  CC_XK_LIST_63.add("物理+历史+化学");
	  CC_XK_LIST_63.add("物理+历史+生物");
	  CC_XK_LIST_63.add("物理+地理+化学");
	  CC_XK_LIST_63.add("物理+地理+生物");
	  CC_XK_LIST_63.add("物理+化学+生物");
	  CC_XK_LIST_63.add("物理+历史+地理");
	  CC_XK_LIST_63.add("化学+政治+地理");
	  CC_XK_LIST_63.add("化学+政治+历史");
	  CC_XK_LIST_63.add("化学+政治+生物");
	  CC_XK_LIST_63.add("化学+历史+地理");
	  CC_XK_LIST_63.add("化学+历史+生物");
	  CC_XK_LIST_63.add("化学+地理+生物");
	  CC_XK_LIST_63.add("生物+政治+地理");
	  CC_XK_LIST_63.add("生物+历史+地理");
	  CC_XK_LIST_63.add("生物+政治+历史");
	  CC_XK_LIST_63.add("政治+历史+地理");
	  CC_XK_LIST_312.add("物理+化学+生物");
	  CC_XK_LIST_312.add("物理+化学+地理");
	  CC_XK_LIST_312.add("物理+化学+政治");
	  CC_XK_LIST_312.add("物理+生物+地理");
	  CC_XK_LIST_312.add("物理+生物+政治");
	  CC_XK_LIST_312.add("物理+地理+政治");
	  CC_XK_LIST_312.add("历史+地理+政治");
	  CC_XK_LIST_312.add("历史+地理+化学");
	  CC_XK_LIST_312.add("历史+地理+生物");
	  CC_XK_LIST_312.add("历史+政治+化学");
	  CC_XK_LIST_312.add("历史+政治+生物");
	  CC_XK_LIST_312.add("历史+化学+生物");

	  CC_RY_QJ.add("武汉大学");
	  CC_RY_QJ.add("哈尔滨工业大学");
	  CC_RY_QJ.add("北京大学");
	  CC_RY_QJ.add("中央民族大学");
	  CC_RY_QJ.add("北京航空航天大学");
	  CC_RY_QJ.add("中国人民大学");
	  CC_RY_QJ.add("北京师范大学");
	  CC_RY_QJ.add("中国海洋大学");
	  CC_RY_QJ.add("南开大学");
	  CC_RY_QJ.add("同济大学");
	  CC_RY_QJ.add("中国科学技术大学");
	  CC_RY_QJ.add("兰州大学");
	  CC_RY_QJ.add("天津大学");
	  CC_RY_QJ.add("四川大学");
	  CC_RY_QJ.add("厦门大学");
	  CC_RY_QJ.add("西北工业大学");
	  CC_RY_QJ.add("东南大学");
	  CC_RY_QJ.add("南京大学");
	  CC_RY_QJ.add("华南理工大学");
	  CC_RY_QJ.add("浙江大学");
	  CC_RY_QJ.add("上海交通大学");
	  CC_RY_QJ.add("中山大学");
	  CC_RY_QJ.add("重庆大学");
	  CC_RY_QJ.add("华中科技大学");
	  CC_RY_QJ.add("大连理工大学");
	  CC_RY_QJ.add("中南大学");
	  CC_RY_QJ.add("华东师范大学");
	  CC_RY_QJ.add("清华大学");
	  CC_RY_QJ.add("山东大学");
	  CC_RY_QJ.add("复旦大学");
	  CC_RY_QJ.add("吉林大学");
	  CC_RY_QJ.add("北京理工大学");
	  CC_RY_QJ.add("西安交通大学");
	  CC_RY_QJ.add("中国农业大学");
	  CC_RY_QJ.add("电子科技大学");
	  CC_RY_QJ.add("国防科技大学");
	  CC_RY_SYL.add("西北大学");
	  CC_RY_SYL.add("武汉大学");
	  CC_RY_SYL.add("内蒙古大学");
	  CC_RY_SYL.add("湖南大学");
	  CC_RY_SYL.add("云南大学");
	  CC_RY_SYL.add("哈尔滨工业大学");
	  CC_RY_SYL.add("北京邮电大学");
	  CC_RY_SYL.add("北京中医药大学");
	  CC_RY_SYL.add("北京交通大学");
	  CC_RY_SYL.add("北京林业大学");
	  CC_RY_SYL.add("北京大学");
	  CC_RY_SYL.add("中央民族大学");
	  CC_RY_SYL.add("北京外国语大学");
	  CC_RY_SYL.add("河北工业大学");
	  CC_RY_SYL.add("北京航空航天大学");
	  CC_RY_SYL.add("长安大学");
	  CC_RY_SYL.add("大连海事大学");
	  CC_RY_SYL.add("北京工业大学");
	  CC_RY_SYL.add("中国人民大学");
	  CC_RY_SYL.add("北京师范大学");
	  CC_RY_SYL.add("中国海洋大学");
	  CC_RY_SYL.add("南开大学");
	  CC_RY_SYL.add("辽宁大学");
	  CC_RY_SYL.add("新疆大学");
	  CC_RY_SYL.add("同济大学");
	  CC_RY_SYL.add("上海大学");
	  CC_RY_SYL.add("安徽大学");
	  CC_RY_SYL.add("对外经济贸易大学");
	  CC_RY_SYL.add("西南交通大学");
	  CC_RY_SYL.add("江南大学");
	  CC_RY_SYL.add("湖南师范大学");
	  CC_RY_SYL.add("合肥工业大学");
	  CC_RY_SYL.add("中国科学技术大学");
	  CC_RY_SYL.add("郑州大学");
	  CC_RY_SYL.add("西安电子科技大学");
	  CC_RY_SYL.add("兰州大学");
	  CC_RY_SYL.add("天津大学");
	  CC_RY_SYL.add("四川大学");
	  CC_RY_SYL.add("南京航空航天大学");
	  CC_RY_SYL.add("西南财经大学");
	  CC_RY_SYL.add("天津工业大学");
	  CC_RY_SYL.add("四川农业大学");
	  CC_RY_SYL.add("厦门大学");
	  CC_RY_SYL.add("华南师范大学");
	  CC_RY_SYL.add("天津医科大学");
	  CC_RY_SYL.add("西北工业大学");
	  CC_RY_SYL.add("福州大学");
	  CC_RY_SYL.add("天津中医药大学");
	  CC_RY_SYL.add("南昌大学");
	  CC_RY_SYL.add("东南大学");
	  CC_RY_SYL.add("南京大学");
	  CC_RY_SYL.add("南京理工大学");
	  CC_RY_SYL.add("华南理工大学");
	  CC_RY_SYL.add("南京师范大学");
	  CC_RY_SYL.add("浙江大学");
	  CC_RY_SYL.add("广西大学");
	  CC_RY_SYL.add("中国药科大学");
	  CC_RY_SYL.add("河海大学");
	  CC_RY_SYL.add("暨南大学");
	  CC_RY_SYL.add("哈尔滨工程大学");
	  CC_RY_SYL.add("南京农业大学");
	  CC_RY_SYL.add("上海交通大学");
	  CC_RY_SYL.add("中山大学");
	  CC_RY_SYL.add("重庆大学");
	  CC_RY_SYL.add("武汉理工大学");
	  CC_RY_SYL.add("上海财经大学");
	  CC_RY_SYL.add("中国石油大学");
	  CC_RY_SYL.add("中国地质大学");
	  CC_RY_SYL.add("苏州大学");
	  CC_RY_SYL.add("华中科技大学");
	  CC_RY_SYL.add("上海外国语大学");
	  CC_RY_SYL.add("大连理工大学");
	  CC_RY_SYL.add("中南大学");
	  CC_RY_SYL.add("华东师范大学");
	  CC_RY_SYL.add("清华大学");
	  CC_RY_SYL.add("东北农业大学");
	  CC_RY_SYL.add("东北师范大学");
	  CC_RY_SYL.add("北京科技大学");
	  CC_RY_SYL.add("太原理工大学");
	  CC_RY_SYL.add("山东大学");
	  CC_RY_SYL.add("复旦大学");
	  CC_RY_SYL.add("东北大学");
	  CC_RY_SYL.add("吉林大学");
	  CC_RY_SYL.add("东华大学");
	  CC_RY_SYL.add("华东理工大学");
	  CC_RY_SYL.add("北京理工大学");
	  CC_RY_SYL.add("南京信息工程大学");
	  CC_RY_SYL.add("南京中医药大学");
	  CC_RY_SYL.add("南京林业大学");
	  CC_RY_SYL.add("南京邮电大学");
	  CC_RY_SYL.add("石河子大学");
	  CC_RY_SYL.add("成都理工大学");
	  CC_RY_SYL.add("宁波大学");
	  CC_RY_SYL.add("西南石油大学");
	  CC_RY_SYL.add("广州中医药大学");
	  CC_RY_SYL.add("上海中医药大学");
	  CC_RY_SYL.add("西安交通大学");
	  CC_RY_SYL.add("陕西师范大学");
	  CC_RY_SYL.add("西北农林科技大学");
	  CC_RY_SYL.add("青海大学");
	  CC_RY_SYL.add("西藏大学");
	  CC_RY_SYL.add("东北林业大学");
	  CC_RY_SYL.add("华中师范大学");
	  CC_RY_SYL.add("中南财经政法大学");
	  CC_RY_SYL.add("华中农业大学");
	  CC_RY_SYL.add("河南大学");
	  CC_RY_SYL.add("海南大学");
	  CC_RY_SYL.add("宁夏大学");
	  CC_RY_SYL.add("北京化工大学");
	  CC_RY_SYL.add("中国传媒大学");
	  CC_RY_SYL.add("中国政法大学");
	  CC_RY_SYL.add("北京协和医学院");
	  CC_RY_SYL.add("中国农业大学");
	  CC_RY_SYL.add("中央戏剧学院");
	  CC_RY_SYL.add("外交学院");
	  CC_RY_SYL.add("中央财经大学");
	  CC_RY_SYL.add("首都师范大学");
	  CC_RY_SYL.add("电子科技大学");
	  CC_RY_SYL.add("中国地质大学");
	  CC_RY_SYL.add("中国石油大学");
	  CC_RY_SYL.add("北京体育大学");
	  CC_RY_SYL.add("西南大学");
	  CC_RY_SYL.add("贵州大学");
	  CC_RY_SYL.add("中国人民解放军海军军医大学");
	  CC_RY_SYL.add("中国矿业大学");
	  CC_RY_SYL.add("中国科学院大学");
	  CC_RY_SYL.add("延边大学");
	  CC_RY_SYL.add("上海海洋大学");
	  CC_RY_SYL.add("中国人民大学");
	  CC_RY_SYL.add("中国矿业大学");
	  CC_RY_SYL.add("华北电力大学保定校区");
	  CC_RY_SYL.add("华北电力大学");
	  CC_RY_SYL.add("国防科技大学");
	  CC_RY_SYL.add("山东大学");
	  CC_RY_SYL.add("首都师范大学继续教育学院");
	  CC_RY_SYL.add("合肥工业大学宣城校区");
	  CC_RY_SYL.add("电子科技大学格拉斯哥学院");
	  CC_RY_SYL.add("成都中医药大学");
	  CC_RY_SYL.add("中国美术学院");
	  CC_RY_SYL.add("中央音乐学院");
	  CC_RY_SYL.add("中国人民公安大学");
	  CC_RY_SYL.add("哈尔滨工业大学");
	  CC_RY_SYL.add("中国石油大学（北京）克拉玛依校区");
	  CC_RY_SYL.add("上海音乐学院");
	  CC_RY_SYL.add("中央美术学院");
	  CC_RY_SYL.add("上海体育学院");
	  CC_RY_SYL.add("中国音乐学院");
	  CC_RY_985.add("武汉大学");
	  CC_RY_985.add("湖南大学");
	  CC_RY_985.add("哈尔滨工业大学");
	  CC_RY_985.add("北京大学");
	  CC_RY_985.add("中央民族大学");
	  CC_RY_985.add("北京航空航天大学");
	  CC_RY_985.add("中国人民大学");
	  CC_RY_985.add("北京师范大学");
	  CC_RY_985.add("中国海洋大学");
	  CC_RY_985.add("南开大学");
	  CC_RY_985.add("同济大学");
	  CC_RY_985.add("中国科学技术大学");
	  CC_RY_985.add("兰州大学");
	  CC_RY_985.add("天津大学");
	  CC_RY_985.add("四川大学");
	  CC_RY_985.add("厦门大学");
	  CC_RY_985.add("西北工业大学");
	  CC_RY_985.add("东南大学");
	  CC_RY_985.add("南京大学");
	  CC_RY_985.add("华南理工大学");
	  CC_RY_985.add("浙江大学");
	  CC_RY_985.add("上海交通大学");
	  CC_RY_985.add("中山大学");
	  CC_RY_985.add("重庆大学");
	  CC_RY_985.add("华中科技大学");
	  CC_RY_985.add("大连理工大学");
	  CC_RY_985.add("中南大学");
	  CC_RY_985.add("华东师范大学");
	  CC_RY_985.add("清华大学");
	  CC_RY_985.add("山东大学");
	  CC_RY_985.add("复旦大学");
	  CC_RY_985.add("东北大学");
	  CC_RY_985.add("吉林大学");
	  CC_RY_985.add("北京理工大学");
	  CC_RY_985.add("西安交通大学");
	  CC_RY_985.add("西北农林科技大学");
	  CC_RY_985.add("中国农业大学");
	  CC_RY_985.add("电子科技大学");
	  CC_RY_985.add("北京大学医学部");
	  CC_RY_985.add("东北大学秦皇岛分校");
	  CC_RY_985.add("复旦大学上海医学院");
	  CC_RY_985.add("国防科技大学");
	  CC_RY_985.add("山东大学");
	  CC_RY_985.add("电子科技大学格拉斯哥学院");
	  CC_RY_985.add("哈尔滨工业大学");
	  CC_RY_211.add("西北大学");
	  CC_RY_211.add("武汉大学");
	  CC_RY_211.add("内蒙古大学");
	  CC_RY_211.add("湖南大学");
	  CC_RY_211.add("云南大学");
	  CC_RY_211.add("哈尔滨工业大学");
	  CC_RY_211.add("北京邮电大学");
	  CC_RY_211.add("北京中医药大学");
	  CC_RY_211.add("北京交通大学");
	  CC_RY_211.add("北京林业大学");
	  CC_RY_211.add("北京大学");
	  CC_RY_211.add("中央民族大学");
	  CC_RY_211.add("北京外国语大学");
	  CC_RY_211.add("河北工业大学");
	  CC_RY_211.add("北京航空航天大学");
	  CC_RY_211.add("长安大学");
	  CC_RY_211.add("大连海事大学");
	  CC_RY_211.add("北京工业大学");
	  CC_RY_211.add("中国人民大学");
	  CC_RY_211.add("北京师范大学");
	  CC_RY_211.add("中国海洋大学");
	  CC_RY_211.add("南开大学");
	  CC_RY_211.add("辽宁大学");
	  CC_RY_211.add("新疆大学");
	  CC_RY_211.add("同济大学");
	  CC_RY_211.add("上海大学");
	  CC_RY_211.add("安徽大学");
	  CC_RY_211.add("对外经济贸易大学");
	  CC_RY_211.add("西南交通大学");
	  CC_RY_211.add("江南大学");
	  CC_RY_211.add("湖南师范大学");
	  CC_RY_211.add("合肥工业大学");
	  CC_RY_211.add("中国科学技术大学");
	  CC_RY_211.add("郑州大学");
	  CC_RY_211.add("西安电子科技大学");
	  CC_RY_211.add("兰州大学");
	  CC_RY_211.add("天津大学");
	  CC_RY_211.add("四川大学");
	  CC_RY_211.add("南京航空航天大学");
	  CC_RY_211.add("西南财经大学");
	  CC_RY_211.add("四川农业大学");
	  CC_RY_211.add("厦门大学");
	  CC_RY_211.add("华南师范大学");
	  CC_RY_211.add("天津医科大学");
	  CC_RY_211.add("西北工业大学");
	  CC_RY_211.add("福州大学");
	  CC_RY_211.add("南昌大学");
	  CC_RY_211.add("东南大学");
	  CC_RY_211.add("南京大学");
	  CC_RY_211.add("南京理工大学");
	  CC_RY_211.add("华南理工大学");
	  CC_RY_211.add("南京师范大学");
	  CC_RY_211.add("浙江大学");
	  CC_RY_211.add("广西大学");
	  CC_RY_211.add("中国药科大学");
	  CC_RY_211.add("河海大学");
	  CC_RY_211.add("暨南大学");
	  CC_RY_211.add("哈尔滨工程大学");
	  CC_RY_211.add("南京农业大学");
	  CC_RY_211.add("上海交通大学");
	  CC_RY_211.add("中山大学");
	  CC_RY_211.add("重庆大学");
	  CC_RY_211.add("武汉理工大学");
	  CC_RY_211.add("上海财经大学");
	  CC_RY_211.add("中国石油大学");
	  CC_RY_211.add("中国地质大学");
	  CC_RY_211.add("苏州大学");
	  CC_RY_211.add("华中科技大学");
	  CC_RY_211.add("上海外国语大学");
	  CC_RY_211.add("大连理工大学");
	  CC_RY_211.add("中南大学");
	  CC_RY_211.add("华东师范大学");
	  CC_RY_211.add("清华大学");
	  CC_RY_211.add("东北农业大学");
	  CC_RY_211.add("东北师范大学");
	  CC_RY_211.add("北京科技大学");
	  CC_RY_211.add("太原理工大学");
	  CC_RY_211.add("山东大学");
	  CC_RY_211.add("复旦大学");
	  CC_RY_211.add("东北大学");
	  CC_RY_211.add("吉林大学");
	  CC_RY_211.add("东华大学");
	  CC_RY_211.add("华东理工大学");
	  CC_RY_211.add("北京理工大学");
	  CC_RY_211.add("石河子大学");
	  CC_RY_211.add("西安交通大学");
	  CC_RY_211.add("陕西师范大学");
	  CC_RY_211.add("西北农林科技大学");
	  CC_RY_211.add("青海大学");
	  CC_RY_211.add("西藏大学");
	  CC_RY_211.add("东北林业大学");
	  CC_RY_211.add("华中师范大学");
	  CC_RY_211.add("中南财经政法大学");
	  CC_RY_211.add("华中农业大学");
	  CC_RY_211.add("海南大学");
	  CC_RY_211.add("宁夏大学");
	  CC_RY_211.add("北京化工大学");
	  CC_RY_211.add("中国传媒大学");
	  CC_RY_211.add("中国政法大学");
	  CC_RY_211.add("中国农业大学");
	  CC_RY_211.add("中央财经大学");
	  CC_RY_211.add("电子科技大学");
	  CC_RY_211.add("中国地质大学");
	  CC_RY_211.add("中国石油大学");
	  CC_RY_211.add("北京体育大学");
	  CC_RY_211.add("西南大学");
	  CC_RY_211.add("贵州大学");
	  CC_RY_211.add("中国人民解放军海军军医大学");
	  CC_RY_211.add("中国矿业大学");
	  CC_RY_211.add("北京大学医学部");
	  CC_RY_211.add("延边大学");
	  CC_RY_211.add("东北大学秦皇岛分校");
	  CC_RY_211.add("复旦大学上海医学院");
	  CC_RY_211.add("中国矿业大学");
	  CC_RY_211.add("华北电力大学保定校区");
	  CC_RY_211.add("华北电力大学");
	  CC_RY_211.add("空军军医大学");
	  CC_RY_211.add("国防科技大学");
	  CC_RY_211.add("山东大学");
	  CC_RY_211.add("合肥工业大学宣城校区");
	  CC_RY_211.add("西南财经大学特拉华数据科学学院");
	  CC_RY_211.add("电子科技大学格拉斯哥学院");
	  CC_RY_211.add("中央音乐学院");
	  CC_RY_211.add("哈尔滨工业大学");
	  CC_RY_211.add("哈尔滨工业大学");

	  
	  
	  CC_XK.put("浙江", CC_XK_LIST_73 );
	  CC_XK.put("上海", CC_XK_LIST_63 );
	  CC_XK.put("北京", CC_XK_LIST_63 );
	  CC_XK.put("天津", CC_XK_LIST_63 );
	  CC_XK.put("山东", CC_XK_LIST_63 );
	  CC_XK.put("海南", CC_XK_LIST_63 );
	  CC_XK.put("重庆", CC_XK_LIST_312 );
	  CC_XK.put("辽宁", CC_XK_LIST_312 );
	  CC_XK.put("河北", CC_XK_LIST_312 );
	  CC_XK.put("湖南", CC_XK_LIST_312 );
	  CC_XK.put("福建", CC_XK_LIST_312 );
	  CC_XK.put("广东", CC_XK_LIST_312 );
	  CC_XK.put("江苏", CC_XK_LIST_312 );
	  CC_XK.put("湖北", CC_XK_LIST_312 );
    
	  CC_YX_FX.put("哈尔滨工业大学", "（本部、深圳校区、威海校区）");
	  CC_YX_FX.put("北京师范大学", "（本部、珠海校区）");
	  CC_YX_FX.put("中国人民大学", "（本部、苏州校区）");
	  CC_YX_FX.put("大连理工大学", "（本部、盘锦校区、开发校区）");
	  CC_YX_FX.put("山东大学", "（本部，威海校区）");
	  CC_YX_FX.put("电子科技大学", "（本部、沙河校区）");
	  CC_YX_FX.put("东北大学", "（本部、秦皇岛分校）");
	  CC_YX_FX.put("合肥工业大学", "（本部、宣城校区）");
	  CC_YX_FX.put("华北电力大学", "（本部、保定校区）");
	  CC_YX_FX.put("中国石油大学", "（本部、克拉玛依校区、华东校区）");
	  CC_YX_FX.put("中国地质大学", "（本部、武汉校区）");
	  CC_YX_FX.put("中国矿业大学", "（本部、徐州校区）");
	  CC_YX_FX.put("北京邮电大学", "（本部、沙河校区、宏福校区）");
	  CC_YX_FX.put("北京交通大学", "（威海校区）");
	  CC_YX_FX.put("成都理工大学", "（宜宾校区）");
	  CC_YX_FX.put("西南大学", "（荣昌校区）");
	  CC_YX_FX.put("遵义医科大学", "（珠海校区）");
	  CC_YX_FX.put("西华大学", "（宜宾校区、彭州校区）");
	  CC_YX_FX.put("四川外国语大学成都学院", "（宜宾校区）");
	  CC_YX_FX.put("东北石油大学", "（秦皇岛分校）");
	  CC_YX_FX.put("哈尔滨医科大学", "（大庆校区）");
	  CC_YX_FX.put("厦门大学", "（马来西亚校区）");
	  CC_YX_FX.put("中国农业大学", "（烟台校区）");
	  CC_YX_FX.put("中国传媒大学", "（海南国际园区）");
	  CC_YX_FX.put("浙江大学", "（海宁国际校区）");
	  CC_YX_FX.put("香港中文大学", "（深圳校区）");
	  CC_YX_FX.put("西南交通大学", "（峨眉校区）");
	  CC_YX_FX.put("哈尔滨理工大学", "（荣成校区）");
	  CC_ZY_CATEGORY.put("哲学", "哲学类");
	  CC_ZY_CATEGORY.put("逻辑学", "哲学类");
	  CC_ZY_CATEGORY.put("宗教学", "哲学类");
	  CC_ZY_CATEGORY.put("伦理学", "哲学类");
	  CC_ZY_CATEGORY.put("经济学", "经济学类");
	  CC_ZY_CATEGORY.put("经济统计学", "经济学类");
	  CC_ZY_CATEGORY.put("国民经济管理", "经济学类");
	  CC_ZY_CATEGORY.put("资源与环境经济学", "经济学类");
	  CC_ZY_CATEGORY.put("商务经济学", "经济学类");
	  CC_ZY_CATEGORY.put("能源经济", "经济学类");
	  CC_ZY_CATEGORY.put("劳动经济学", "经济学类");
	  CC_ZY_CATEGORY.put("经济工程", "经济学类");
	  CC_ZY_CATEGORY.put("数字经济", "经济学类");
	  CC_ZY_CATEGORY.put("财政学", "财政学类");
	  CC_ZY_CATEGORY.put("税收学", "财政学类");
	  CC_ZY_CATEGORY.put("金融学", "金融学类");
	  CC_ZY_CATEGORY.put("金融工程", "金融学类");
	  CC_ZY_CATEGORY.put("保险学", "金融学类");
	  CC_ZY_CATEGORY.put("投资学", "金融学类");
	  CC_ZY_CATEGORY.put("金融数学", "金融学类");
	  CC_ZY_CATEGORY.put("社会学", "社会学类");
	  CC_ZY_CATEGORY.put("社会工作", "社会学类");
	  CC_ZY_CATEGORY.put("人类学", "社会学类");
	  CC_ZY_CATEGORY.put("女性学", "社会学类");
	  CC_ZY_CATEGORY.put("家政学", "社会学类");
	  CC_ZY_CATEGORY.put("民族学", "民族学类");
	  CC_ZY_CATEGORY.put("科学社会主义", "马克思主义理论类");
	  CC_ZY_CATEGORY.put("中国共产党历史", "马克思主义理论类");
	  CC_ZY_CATEGORY.put("思想政治教育", "马克思主义理论类");
	  CC_ZY_CATEGORY.put("马克思主义理论", "马克思主义理论类");
	  CC_ZY_CATEGORY.put("治安学", "公安学类");
	  CC_ZY_CATEGORY.put("侦查学", "公安学类");
	  CC_ZY_CATEGORY.put("边防管理", "公安学类");
	  CC_ZY_CATEGORY.put("禁毒学", "公安学类");
	  CC_ZY_CATEGORY.put("警犬技术", "公安学类");
	  CC_ZY_CATEGORY.put("经济犯罪侦查", "公安学类");
	  CC_ZY_CATEGORY.put("边防指挥", "公安学类");
	  CC_ZY_CATEGORY.put("消防指挥", "公安学类");
	  CC_ZY_CATEGORY.put("警卫学", "公安学类");
	  CC_ZY_CATEGORY.put("公安情报学", "公安学类");
	  CC_ZY_CATEGORY.put("卫生教育", "教育学类");
	  CC_ZY_CATEGORY.put("认知科学与技术", "教育学类");
	  CC_ZY_CATEGORY.put("体育教育", "体育学类");
	  CC_ZY_CATEGORY.put("运动训练", "体育学类");
	  CC_ZY_CATEGORY.put("社会体育指导与管理", "体育学类");
	  CC_ZY_CATEGORY.put("武术与民族传统体育", "体育学类");
	  CC_ZY_CATEGORY.put("运动人体科学", "体育学类");
	  CC_ZY_CATEGORY.put("运动康复", "体育学类");
	  CC_ZY_CATEGORY.put("休闲体育", "体育学类");
	  CC_ZY_CATEGORY.put("体能训练", "体育学类");
	  CC_ZY_CATEGORY.put("冰雪运动", "体育学类");
	  CC_ZY_CATEGORY.put("电子竞技运动与管理", "体育学类");
	  CC_ZY_CATEGORY.put("智能体育工程", "体育学类");
	  CC_ZY_CATEGORY.put("体育旅游", "体育学类");
	  CC_ZY_CATEGORY.put("汉语言文学", "中国语言文学类");
	  CC_ZY_CATEGORY.put("汉语言", "中国语言文学类");
	  CC_ZY_CATEGORY.put("汉语国际教育", "中国语言文学类");
	  CC_ZY_CATEGORY.put("中国少数民族语言文学", "中国语言文学类");
	  CC_ZY_CATEGORY.put("古典文献学", "中国语言文学类");
	  CC_ZY_CATEGORY.put("应用语言学", "中国语言文学类");
	  CC_ZY_CATEGORY.put("信用管理", "金融学类");
	  CC_ZY_CATEGORY.put("经济与金融", "金融学类");
	  CC_ZY_CATEGORY.put("精算学", "金融学类");
	  CC_ZY_CATEGORY.put("互联网金融", "金融学类");
	  CC_ZY_CATEGORY.put("金融科技", "金融学类");
	  CC_ZY_CATEGORY.put("国际经济与贸易", "经济与贸易类");
	  CC_ZY_CATEGORY.put("贸易经济", "经济与贸易类");
	  CC_ZY_CATEGORY.put("法学", "法学类");
	  CC_ZY_CATEGORY.put("知识产权", "法学类");
	  CC_ZY_CATEGORY.put("监狱学", "法学类");
	  CC_ZY_CATEGORY.put("信用风险管理与法律防控", "法学类");
	  CC_ZY_CATEGORY.put("国际经贸规则", "法学类");
	  CC_ZY_CATEGORY.put("司法警察学", "法学类");
	  CC_ZY_CATEGORY.put("社区矫正", "法学类");
	  CC_ZY_CATEGORY.put("政治学与行政学", "政治学类");
	  CC_ZY_CATEGORY.put("国际政治", "政治学类");
	  CC_ZY_CATEGORY.put("外交学", "政治学类");
	  CC_ZY_CATEGORY.put("国际事务与国际关系", "政治学类");
	  CC_ZY_CATEGORY.put("政治学、经济学与哲学", "政治学类");
	  CC_ZY_CATEGORY.put("国际组织与全球治理", "政治学类");
	  CC_ZY_CATEGORY.put("犯罪学", "公安学类");
	  CC_ZY_CATEGORY.put("公安管理学", "公安学类");
	  CC_ZY_CATEGORY.put("涉外警务", "公安学类");
	  CC_ZY_CATEGORY.put("国内安全保卫", "公安学类");
	  CC_ZY_CATEGORY.put("警务指挥与战术", "公安学类");
	  CC_ZY_CATEGORY.put("技术侦查学", "公安学类");
	  CC_ZY_CATEGORY.put("海警执法", "公安学类");
	  CC_ZY_CATEGORY.put("公安政治工作", "公安学类");
	  CC_ZY_CATEGORY.put("移民管理", "公安学类");
	  CC_ZY_CATEGORY.put("出入境管理", "公安学类");
	  CC_ZY_CATEGORY.put("教育学", "教育学类");
	  CC_ZY_CATEGORY.put("科学教育", "教育学类");
	  CC_ZY_CATEGORY.put("人文教育", "教育学类");
	  CC_ZY_CATEGORY.put("教育技术学", "教育学类");
	  CC_ZY_CATEGORY.put("艺术教育", "教育学类");
	  CC_ZY_CATEGORY.put("学前教育", "教育学类");
	  CC_ZY_CATEGORY.put("小学教育", "教育学类");
	  CC_ZY_CATEGORY.put("特殊教育", "教育学类");
	  CC_ZY_CATEGORY.put("华文教育", "教育学类");
	  CC_ZY_CATEGORY.put("教育康复学", "教育学类");
	  CC_ZY_CATEGORY.put("秘书学", "中国语言文学类");
	  CC_ZY_CATEGORY.put("中国语言与文化", "中国语言文学类");
	  CC_ZY_CATEGORY.put("手语翻译", "中国语言文学类");
	  CC_ZY_CATEGORY.put("桑戈语", "中国语言文学类");
	  CC_ZY_CATEGORY.put("英语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("俄语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("德语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("法语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("西班牙语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("阿拉伯语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("日语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("波斯语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("朝鲜语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("菲律宾语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("梵语巴利语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("印度尼西亚语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("印地语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("柬埔寨语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("老挝语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("缅甸语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("马来语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("蒙古语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("僧伽罗语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("泰语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("乌尔都语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("希伯来语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("越南语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("豪萨语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("斯瓦希里语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("阿尔巴尼亚语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("保加利亚语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("波兰语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("捷克语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("斯洛伐克语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("罗马尼亚语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("葡萄牙语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("瑞典语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("塞尔维亚语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("土耳其语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("希腊语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("匈牙利语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("意大利语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("泰米尔语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("普什图语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("世界语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("孟加拉语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("尼泊尔语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("克罗地亚语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("荷兰语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("芬兰语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("乌克兰语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("挪威语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("丹麦语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("冰岛语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("爱尔兰语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("拉脱维亚语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("立陶宛语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("斯洛文尼亚语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("爱沙尼亚语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("马耳他语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("哈萨克语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("乌兹别克语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("祖鲁语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("拉丁语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("翻译", "外国语言文学类");
	  CC_ZY_CATEGORY.put("商务英语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("阿姆哈拉语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("吉尔吉斯语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("索马里语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("土库曼语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("加泰罗尼亚语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("约鲁巴语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("亚美尼亚语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("马达加斯加语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("格鲁吉亚语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("阿塞拜疆语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("阿非利卡语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("马其顿语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("塔吉克语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("语言学", "外国语言文学类");
	  CC_ZY_CATEGORY.put("塔玛齐格特语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("爪哇语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("旁遮普语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("茨瓦纳语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("恩德贝莱语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("科摩罗语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("克里奥尔语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("绍纳语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("提格雷尼亚语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("白俄罗斯语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("毛利语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("汤加语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("萨摩亚语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("库尔德语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("比斯拉马语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("达里语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("德顿语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("迪维希语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("斐济语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("库克群岛毛利语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("隆迪语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("卢森堡语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("卢旺达语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("纽埃语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("皮金语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("切瓦语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("塞苏陀语", "外国语言文学类");
	  CC_ZY_CATEGORY.put("新闻学", "新闻传播学类");
	  CC_ZY_CATEGORY.put("广播电视学", "新闻传播学类");
	  CC_ZY_CATEGORY.put("广告学", "新闻传播学类");
	  CC_ZY_CATEGORY.put("传播学", "新闻传播学类");
	  CC_ZY_CATEGORY.put("编辑出版学", "新闻传播学类");
	  CC_ZY_CATEGORY.put("网络与新媒体", "新闻传播学类");
	  CC_ZY_CATEGORY.put("数字出版", "新闻传播学类");
	  CC_ZY_CATEGORY.put("时尚传播", "新闻传播学类");
	  CC_ZY_CATEGORY.put("国际新闻与传播", "新闻传播学类");
	  CC_ZY_CATEGORY.put("历史学", "历史学类");
	  CC_ZY_CATEGORY.put("世界史", "历史学类");
	  CC_ZY_CATEGORY.put("考古学", "历史学类");
	  CC_ZY_CATEGORY.put("文物与博物馆学", "历史学类");
	  CC_ZY_CATEGORY.put("文物保护技术", "历史学类");
	  CC_ZY_CATEGORY.put("外国语言与外国历史", "历史学类");
	  CC_ZY_CATEGORY.put("文化遗产", "历史学类");
	  CC_ZY_CATEGORY.put("数学与应用数学", "数学类");
	  CC_ZY_CATEGORY.put("信息与计算科学", "数学类");
	  CC_ZY_CATEGORY.put("数理基础科学", "数学类");
	  CC_ZY_CATEGORY.put("数据计算及应用", "数学类");
	  CC_ZY_CATEGORY.put("物理学", "物理学类");
	  CC_ZY_CATEGORY.put("应用物理学", "物理学类");
	  CC_ZY_CATEGORY.put("核物理", "物理学类");
	  CC_ZY_CATEGORY.put("声学", "物理学类");
	  CC_ZY_CATEGORY.put("系统科学与工程", "物理学类");
	  CC_ZY_CATEGORY.put("化学", "化学类");
	  CC_ZY_CATEGORY.put("应用化学", "化学类");
	  CC_ZY_CATEGORY.put("化学生物学", "化学类");
	  CC_ZY_CATEGORY.put("分子科学与工程", "化学类");
	  CC_ZY_CATEGORY.put("能源化学", "化学类");
	  CC_ZY_CATEGORY.put("天文学", "天文学类");
	  CC_ZY_CATEGORY.put("地理科学", "地理科学类");
	  CC_ZY_CATEGORY.put("自然地理与资源环境", "地理科学类");
	  CC_ZY_CATEGORY.put("人文地理与城乡规划", "地理科学类");
	  CC_ZY_CATEGORY.put("地理信息科学", "地理科学类");
	  CC_ZY_CATEGORY.put("大气科学", "大气科学类");
	  CC_ZY_CATEGORY.put("应用气象学", "大气科学类");
	  CC_ZY_CATEGORY.put("海洋科学", "海洋科学类");
	  CC_ZY_CATEGORY.put("海洋技术", "海洋科学类");
	  CC_ZY_CATEGORY.put("海洋资源与环境", "海洋科学类");
	  CC_ZY_CATEGORY.put("军事海洋学", "海洋科学类");
	  CC_ZY_CATEGORY.put("地球物理学", "地球物理学类");
	  CC_ZY_CATEGORY.put("空间科学与技术", "地球物理学类");
	  CC_ZY_CATEGORY.put("防灾减灾科学与工程", "地球物理学类");
	  CC_ZY_CATEGORY.put("地质学", "地质学类");
	  CC_ZY_CATEGORY.put("地球化学", "地质学类");
	  CC_ZY_CATEGORY.put("地球信息科学与技术", "地质学类");
	  CC_ZY_CATEGORY.put("古生物学", "地质学类");
	  CC_ZY_CATEGORY.put("生物科学", "生物科学类");
	  CC_ZY_CATEGORY.put("生物技术", "生物科学类");
	  CC_ZY_CATEGORY.put("生物信息学", "生物科学类");
	  CC_ZY_CATEGORY.put("生态学", "生物科学类");
	  CC_ZY_CATEGORY.put("整合科学", "生物科学类");
	  CC_ZY_CATEGORY.put("神经科学", "生物科学类");
	  CC_ZY_CATEGORY.put("心理学", "心理学类");
	  CC_ZY_CATEGORY.put("应用心理学", "心理学类");
	  CC_ZY_CATEGORY.put("统计学", "统计学类");
	  CC_ZY_CATEGORY.put("应用统计学", "统计学类");
	  CC_ZY_CATEGORY.put("理论与应用力学", "力学类");
	  CC_ZY_CATEGORY.put("工程力学", "力学类");
	  CC_ZY_CATEGORY.put("机械工程", "机械类");
	  CC_ZY_CATEGORY.put("机械设计制造及其自动化", "机械类");
	  CC_ZY_CATEGORY.put("材料成型及控制工程", "机械类");
	  CC_ZY_CATEGORY.put("机械电子工程", "机械类");
	  CC_ZY_CATEGORY.put("工业设计", "机械类");
	  CC_ZY_CATEGORY.put("过程装备与控制工程", "机械类");
	  CC_ZY_CATEGORY.put("车辆工程", "机械类");
	  CC_ZY_CATEGORY.put("汽车服务工程", "机械类");
	  CC_ZY_CATEGORY.put("机械工艺技术", "机械类");
	  CC_ZY_CATEGORY.put("微机电系统工程", "机械类");
	  CC_ZY_CATEGORY.put("机电技术教育", "机械类");
	  CC_ZY_CATEGORY.put("汽车维修工程教育", "机械类");
	  CC_ZY_CATEGORY.put("智能制造工程", "机械类");
	  CC_ZY_CATEGORY.put("智能车辆工程", "机械类");
	  CC_ZY_CATEGORY.put("仿生科学与工程", "机械类");
	  CC_ZY_CATEGORY.put("新能源汽车工程", "机械类");
	  CC_ZY_CATEGORY.put("测控技术与仪器", "仪器类");
	  CC_ZY_CATEGORY.put("精密仪器", "仪器类");
	  CC_ZY_CATEGORY.put("材料科学与工程", "材料类");
	  CC_ZY_CATEGORY.put("材料物理", "材料类");
	  CC_ZY_CATEGORY.put("材料化学", "材料类");
	  CC_ZY_CATEGORY.put("冶金工程", "材料类");
	  CC_ZY_CATEGORY.put("金属材料工程", "材料类");
	  CC_ZY_CATEGORY.put("无机非金属材料工程", "材料类");
	  CC_ZY_CATEGORY.put("高分子材料与工程", "材料类");
	  CC_ZY_CATEGORY.put("复合材料与工程", "材料类");
	  CC_ZY_CATEGORY.put("粉体材料科学与工程", "材料类");
	  CC_ZY_CATEGORY.put("宝石及材料工艺学", "材料类");
	  CC_ZY_CATEGORY.put("焊接技术与工程", "材料类");
	  CC_ZY_CATEGORY.put("功能材料", "材料类");
	  CC_ZY_CATEGORY.put("纳米材料与技术", "材料类");
	  CC_ZY_CATEGORY.put("新能源材料与器件", "材料类");
	  CC_ZY_CATEGORY.put("材料设计科学与工程", "材料类");
	  CC_ZY_CATEGORY.put("复合材料成型工程", "材料类");
	  CC_ZY_CATEGORY.put("能源与动力工程", "能源动力类");
	  CC_ZY_CATEGORY.put("新能源科学与工程", "能源动力类");
	  CC_ZY_CATEGORY.put("能源与环境系统工程", "能源动力类");
	  CC_ZY_CATEGORY.put("电气工程及其自动化", "电气类");
	  CC_ZY_CATEGORY.put("智能电网信息工程", "电气类");
	  CC_ZY_CATEGORY.put("光源与照明", "电气类");
	  CC_ZY_CATEGORY.put("电气工程与智能控制", "电气类");
	  CC_ZY_CATEGORY.put("电机电器智能化", "电气类");
	  CC_ZY_CATEGORY.put("电缆工程", "电气类");
	  CC_ZY_CATEGORY.put("电子信息工程", "电子信息类");
	  CC_ZY_CATEGORY.put("电子科学与技术", "电子信息类");
	  CC_ZY_CATEGORY.put("通信工程", "电子信息类");
	  CC_ZY_CATEGORY.put("微电子科学与工程", "电子信息类");
	  CC_ZY_CATEGORY.put("光电信息科学与工程", "电子信息类");
	  CC_ZY_CATEGORY.put("信息工程", "电子信息类");
	  CC_ZY_CATEGORY.put("广播电视工程", "电子信息类");
	  CC_ZY_CATEGORY.put("水声工程", "电子信息类");
	  CC_ZY_CATEGORY.put("电子封装技术", "电子信息类");
	  CC_ZY_CATEGORY.put("集成电路设计与集成系统", "电子信息类");
	  CC_ZY_CATEGORY.put("医学信息工程", "电子信息类");
	  CC_ZY_CATEGORY.put("电磁场与无线技术", "电子信息类");
	  CC_ZY_CATEGORY.put("电波传播与天线", "电子信息类");
	  CC_ZY_CATEGORY.put("电子信息科学与技术", "电子信息类");
	  CC_ZY_CATEGORY.put("电信工程及管理", "电子信息类");
	  CC_ZY_CATEGORY.put("应用电子技术教育", "电子信息类");
	  CC_ZY_CATEGORY.put("人工智能", "电子信息类");
	  CC_ZY_CATEGORY.put("自动化", "自动化类");
	  CC_ZY_CATEGORY.put("轨道交通信号与控制", "自动化类");
	  CC_ZY_CATEGORY.put("机器人工程", "自动化类");
	  CC_ZY_CATEGORY.put("邮政工程", "自动化类");
	  CC_ZY_CATEGORY.put("核电技术与控制工程", "自动化类");
	  CC_ZY_CATEGORY.put("计算机科学与技术", "计算机类");
	  CC_ZY_CATEGORY.put("软件工程", "计算机类");
	  CC_ZY_CATEGORY.put("网络工程", "计算机类");
	  CC_ZY_CATEGORY.put("信息安全", "计算机类");
	  CC_ZY_CATEGORY.put("物联网工程", "计算机类");
	  CC_ZY_CATEGORY.put("数字媒体技术", "计算机类");
	  CC_ZY_CATEGORY.put("智能科学与技术", "计算机类");
	  CC_ZY_CATEGORY.put("空间信息与数字技术", "计算机类");
	  CC_ZY_CATEGORY.put("电子与计算机工程", "计算机类");
	  CC_ZY_CATEGORY.put("数据科学与大数据技术", "计算机类");
	  CC_ZY_CATEGORY.put("网络空间安全", "计算机类");
	  CC_ZY_CATEGORY.put("新媒体技术", "计算机类");
	  CC_ZY_CATEGORY.put("电影制作", "计算机类");
	  CC_ZY_CATEGORY.put("保密技术", "计算机类");
	  CC_ZY_CATEGORY.put("土木工程", "土木类");
	  CC_ZY_CATEGORY.put("建筑环境与能源应用工程", "土木类");
	  CC_ZY_CATEGORY.put("给排水科学与工程", "土木类");
	  CC_ZY_CATEGORY.put("建筑电气与智能化", "土木类");
	  CC_ZY_CATEGORY.put("城市地下空间工程", "土木类");
	  CC_ZY_CATEGORY.put("道路桥梁与渡河工程", "土木类");
	  CC_ZY_CATEGORY.put("铁道工程", "土木类");
	  CC_ZY_CATEGORY.put("智能建造", "土木类");
	  CC_ZY_CATEGORY.put("土木、水利与海洋工程", "土木类");
	  CC_ZY_CATEGORY.put("水利水电工程", "水利类");
	  CC_ZY_CATEGORY.put("水文与水资源工程", "水利类");
	  CC_ZY_CATEGORY.put("港口航道与海岸工程", "水利类");
	  CC_ZY_CATEGORY.put("水务工程", "水利类");
	  CC_ZY_CATEGORY.put("水利科学与工程", "水利类");
	  CC_ZY_CATEGORY.put("测绘工程", "测绘类");
	  CC_ZY_CATEGORY.put("遥感科学与技术", "测绘类");
	  CC_ZY_CATEGORY.put("导航工程", "测绘类");
	  CC_ZY_CATEGORY.put("地理国情监测", "测绘类");
	  CC_ZY_CATEGORY.put("地理空间信息工程", "测绘类");
	  CC_ZY_CATEGORY.put("化学工程与工艺", "化工与制药类");
	  CC_ZY_CATEGORY.put("制药工程", "化工与制药类");
	  CC_ZY_CATEGORY.put("资源循环科学与工程", "化工与制药类");
	  CC_ZY_CATEGORY.put("能源化学工程", "化工与制药类");
	  CC_ZY_CATEGORY.put("化学工程与工业生物工程", "化工与制药类");
	  CC_ZY_CATEGORY.put("化工安全工程", "化工与制药类");
	  CC_ZY_CATEGORY.put("涂料工程", "化工与制药类");
	  CC_ZY_CATEGORY.put("精细化工", "化工与制药类");
	  CC_ZY_CATEGORY.put("地质工程", "地质类");
	  CC_ZY_CATEGORY.put("勘查技术与工程", "地质类");
	  CC_ZY_CATEGORY.put("资源勘查工程", "地质类");
	  CC_ZY_CATEGORY.put("地下水科学与工程", "地质类");
	  CC_ZY_CATEGORY.put("采矿工程", "矿业类");
	  CC_ZY_CATEGORY.put("石油工程", "矿业类");
	  CC_ZY_CATEGORY.put("矿物加工工程", "矿业类");
	  CC_ZY_CATEGORY.put("油气储运工程", "矿业类");
	  CC_ZY_CATEGORY.put("矿物资源工程", "矿业类");
	  CC_ZY_CATEGORY.put("海洋油气工程", "矿业类");
	  CC_ZY_CATEGORY.put("纺织工程", "纺织类");
	  CC_ZY_CATEGORY.put("服装设计与工程", "纺织类");
	  CC_ZY_CATEGORY.put("非织造材料与工程", "纺织类");
	  CC_ZY_CATEGORY.put("服装设计与工艺教育", "纺织类");
	  CC_ZY_CATEGORY.put("丝绸设计与工程", "纺织类");
	  CC_ZY_CATEGORY.put("轻化工程", "轻工类");
	  CC_ZY_CATEGORY.put("包装工程", "轻工类");
	  CC_ZY_CATEGORY.put("印刷工程", "轻工类");
	  CC_ZY_CATEGORY.put("香料香精技术与工程", "轻工类");
	  CC_ZY_CATEGORY.put("化妆品技术与工程", "轻工类");
	  CC_ZY_CATEGORY.put("交通运输", "交通运输类");
	  CC_ZY_CATEGORY.put("交通工程", "交通运输类");
	  CC_ZY_CATEGORY.put("航海技术", "交通运输类");
	  CC_ZY_CATEGORY.put("轮机工程", "交通运输类");
	  CC_ZY_CATEGORY.put("飞行技术", "交通运输类");
	  CC_ZY_CATEGORY.put("交通设备与控制工程", "交通运输类");
	  CC_ZY_CATEGORY.put("救助与打捞工程", "交通运输类");
	  CC_ZY_CATEGORY.put("船舶电子电气工程", "交通运输类");
	  CC_ZY_CATEGORY.put("轨道交通电气与控制", "交通运输类");
	  CC_ZY_CATEGORY.put("邮轮工程与管理", "交通运输类");
	  CC_ZY_CATEGORY.put("船舶与海洋工程", "海洋工程类");
	  CC_ZY_CATEGORY.put("海洋工程与技术", "海洋工程类");
	  CC_ZY_CATEGORY.put("海洋资源开发技术", "海洋工程类");
	  CC_ZY_CATEGORY.put("海洋机器人", "海洋工程类");
	  CC_ZY_CATEGORY.put("航空航天工程", "航空航天类");
	  CC_ZY_CATEGORY.put("飞行器设计与工程", "航空航天类");
	  CC_ZY_CATEGORY.put("飞行器制造工程", "航空航天类");
	  CC_ZY_CATEGORY.put("飞行器动力工程", "航空航天类");
	  CC_ZY_CATEGORY.put("飞行器环境与生命保障工程", "航空航天类");
	  CC_ZY_CATEGORY.put("飞行器质量与可靠性", "航空航天类");
	  CC_ZY_CATEGORY.put("飞行器适航技术", "航空航天类");
	  CC_ZY_CATEGORY.put("飞行器控制与信息工程", "航空航天类");
	  CC_ZY_CATEGORY.put("无人驾驶航空器系统工程", "航空航天类");
	  CC_ZY_CATEGORY.put("武器系统与工程", "兵器类");
	  CC_ZY_CATEGORY.put("武器发射工程", "兵器类");
	  CC_ZY_CATEGORY.put("探测制导与控制技术", "兵器类");
	  CC_ZY_CATEGORY.put("弹药工程与爆炸技术", "兵器类");
	  CC_ZY_CATEGORY.put("特种能源技术与工程", "兵器类");
	  CC_ZY_CATEGORY.put("装甲车辆工程", "兵器类");
	  CC_ZY_CATEGORY.put("信息对抗技术", "兵器类");
	  CC_ZY_CATEGORY.put("核工程与核技术", "核工程类");
	  CC_ZY_CATEGORY.put("辐射防护与核安全", "核工程类");
	  CC_ZY_CATEGORY.put("工程物理", "核工程类");
	  CC_ZY_CATEGORY.put("核化工与核燃料工程", "核工程类");
	  CC_ZY_CATEGORY.put("农业工程", "农业工程类");
	  CC_ZY_CATEGORY.put("农业机械化及其自动化", "农业工程类");
	  CC_ZY_CATEGORY.put("农业电气化", "农业工程类");
	  CC_ZY_CATEGORY.put("农业建筑环境与能源工程", "农业工程类");
	  CC_ZY_CATEGORY.put("农业水利工程", "农业工程类");
	  CC_ZY_CATEGORY.put("土地整治工程", "农业工程类");
	  CC_ZY_CATEGORY.put("森林工程", "林业工程类");
	  CC_ZY_CATEGORY.put("木材科学与工程", "林业工程类");
	  CC_ZY_CATEGORY.put("林产化工", "林业工程类");
	  CC_ZY_CATEGORY.put("家具设计与工程", "林业工程类");
	  CC_ZY_CATEGORY.put("环境科学与工程", "环境科学与工程类");
	  CC_ZY_CATEGORY.put("环境工程", "环境科学与工程类");
	  CC_ZY_CATEGORY.put("环境科学", "环境科学与工程类");
	  CC_ZY_CATEGORY.put("环境生态工程", "环境科学与工程类");
	  CC_ZY_CATEGORY.put("环保设备工程", "环境科学与工程类");
	  CC_ZY_CATEGORY.put("资源环境科学", "环境科学与工程类");
	  CC_ZY_CATEGORY.put("水质科学与技术", "环境科学与工程类");
	  CC_ZY_CATEGORY.put("生物医学工程", "生物医学工程类");
	  CC_ZY_CATEGORY.put("假肢矫形工程", "生物医学工程类");
	  CC_ZY_CATEGORY.put("临床工程技术", "生物医学工程类");
	  CC_ZY_CATEGORY.put("食品科学与工程", "食品科学与工程类");
	  CC_ZY_CATEGORY.put("食品质量与安全", "食品科学与工程类");
	  CC_ZY_CATEGORY.put("粮食工程", "食品科学与工程类");
	  CC_ZY_CATEGORY.put("乳品工程", "食品科学与工程类");
	  CC_ZY_CATEGORY.put("酿酒工程", "食品科学与工程类");
	  CC_ZY_CATEGORY.put("葡萄与葡萄酒工程", "食品科学与工程类");
	  CC_ZY_CATEGORY.put("食品营养与检验教育", "食品科学与工程类");
	  CC_ZY_CATEGORY.put("烹饪与营养教育", "食品科学与工程类");
	  CC_ZY_CATEGORY.put("食品安全与检测", "食品科学与工程类");
	  CC_ZY_CATEGORY.put("建筑学", "建筑类");
	  CC_ZY_CATEGORY.put("城乡规划", "建筑类");
	  CC_ZY_CATEGORY.put("风景园林", "建筑类");
	  CC_ZY_CATEGORY.put("历史建筑保护工程", "建筑类");
	  CC_ZY_CATEGORY.put("人居环境科学与技术", "建筑类");
	  CC_ZY_CATEGORY.put("安全工程", "安全科学与工程类");
	  CC_ZY_CATEGORY.put("应急技术与管理", "安全科学与工程类");
	  CC_ZY_CATEGORY.put("职业卫生工程", "安全科学与工程类");
	  CC_ZY_CATEGORY.put("生物工程", "生物工程类");
	  CC_ZY_CATEGORY.put("生物制药", "生物工程类");
	  CC_ZY_CATEGORY.put("刑事科学技术", "公安技术类");
	  CC_ZY_CATEGORY.put("消防工程", "公安技术类");
	  CC_ZY_CATEGORY.put("交通管理工程", "公安技术类");
	  CC_ZY_CATEGORY.put("安全防范工程", "公安技术类");
	  CC_ZY_CATEGORY.put("公安视听技术", "公安技术类");
	  CC_ZY_CATEGORY.put("抢险救援指挥与技术", "公安技术类");
	  CC_ZY_CATEGORY.put("火灾勘查", "公安技术类");
	  CC_ZY_CATEGORY.put("网络安全与执法", "公安技术类");
	  CC_ZY_CATEGORY.put("核生化消防", "公安技术类");
	  CC_ZY_CATEGORY.put("海警舰艇指挥与技术", "公安技术类");
	  CC_ZY_CATEGORY.put("数据警务技术", "公安技术类");
	  CC_ZY_CATEGORY.put("农学", "植物生产类");
	  CC_ZY_CATEGORY.put("园艺", "植物生产类");
	  CC_ZY_CATEGORY.put("植物保护", "植物生产类");
	  CC_ZY_CATEGORY.put("植物科学与技术", "植物生产类");
	  CC_ZY_CATEGORY.put("种子科学与工程", "植物生产类");
	  CC_ZY_CATEGORY.put("设施农业科学与工程", "植物生产类");
	  CC_ZY_CATEGORY.put("茶学", "植物生产类");
	  CC_ZY_CATEGORY.put("烟草", "植物生产类");
	  CC_ZY_CATEGORY.put("应用生物科学", "植物生产类");
	  CC_ZY_CATEGORY.put("农艺教育", "植物生产类");
	  CC_ZY_CATEGORY.put("园艺教育", "植物生产类");
	  CC_ZY_CATEGORY.put("农业资源与环境", "自然保护与环境生态类");
	  CC_ZY_CATEGORY.put("野生动物与自然保护区管理", "自然保护与环境生态类");
	  CC_ZY_CATEGORY.put("水土保持与荒漠化防治", "自然保护与环境生态类");
	  CC_ZY_CATEGORY.put("动物科学", "动物生产类");
	  CC_ZY_CATEGORY.put("蚕学", "动物生产类");
	  CC_ZY_CATEGORY.put("蜂学", "动物生产类");
	  CC_ZY_CATEGORY.put("经济动物学", "动物生产类");
	  CC_ZY_CATEGORY.put("马业科学", "动物生产类");
	  CC_ZY_CATEGORY.put("动物医学", "动物医学类");
	  CC_ZY_CATEGORY.put("动物药学", "动物医学类");
	  CC_ZY_CATEGORY.put("动植物检疫", "动物医学类");
	  CC_ZY_CATEGORY.put("实验动物学", "动物医学类");
	  CC_ZY_CATEGORY.put("中兽医学", "动物医学类");
	  CC_ZY_CATEGORY.put("林学", "林学类");
	  CC_ZY_CATEGORY.put("园林", "林学类");
	  CC_ZY_CATEGORY.put("森林保护", "林学类");
	  CC_ZY_CATEGORY.put("经济林", "林学类");
	  CC_ZY_CATEGORY.put("水产养殖学", "水产类");
	  CC_ZY_CATEGORY.put("海洋渔业科学与技术", "水产类");
	  CC_ZY_CATEGORY.put("水族科学与技术", "水产类");
	  CC_ZY_CATEGORY.put("水生动物医学", "水产类");
	  CC_ZY_CATEGORY.put("草业科学", "草学类");
	  CC_ZY_CATEGORY.put("基础医学", "基础医学类");
	  CC_ZY_CATEGORY.put("生物医学", "基础医学类");
	  CC_ZY_CATEGORY.put("生物医学科学", "基础医学类");
	  CC_ZY_CATEGORY.put("临床医学", "临床医学类");
	  CC_ZY_CATEGORY.put("麻醉学", "临床医学类");
	  CC_ZY_CATEGORY.put("医学影像学", "临床医学类");
	  CC_ZY_CATEGORY.put("眼视光医学", "临床医学类");
	  CC_ZY_CATEGORY.put("精神医学", "临床医学类");
	  CC_ZY_CATEGORY.put("放射医学", "临床医学类");
	  CC_ZY_CATEGORY.put("儿科学", "临床医学类");
	  CC_ZY_CATEGORY.put("口腔医学", "口腔医学类");
	  CC_ZY_CATEGORY.put("预防医学", "公共卫生与预防医学类");
	  CC_ZY_CATEGORY.put("食品卫生与营养学", "公共卫生与预防医学类");
	  CC_ZY_CATEGORY.put("妇幼保健医学", "公共卫生与预防医学类");
	  CC_ZY_CATEGORY.put("卫生监督", "公共卫生与预防医学类");
	  CC_ZY_CATEGORY.put("全球健康学", "公共卫生与预防医学类");
	  CC_ZY_CATEGORY.put("中医学", "中医学类");
	  CC_ZY_CATEGORY.put("针灸推拿学", "中医学类");
	  CC_ZY_CATEGORY.put("藏医学", "中医学类");
	  CC_ZY_CATEGORY.put("蒙医学", "中医学类");
	  CC_ZY_CATEGORY.put("维医学", "中医学类");
	  CC_ZY_CATEGORY.put("壮医学", "中医学类");
	  CC_ZY_CATEGORY.put("哈医学", "中医学类");
	  CC_ZY_CATEGORY.put("傣医学", "中医学类");
	  CC_ZY_CATEGORY.put("回医学", "中医学类");
	  CC_ZY_CATEGORY.put("中医康复学", "中医学类");
	  CC_ZY_CATEGORY.put("中医养生学", "中医学类");
	  CC_ZY_CATEGORY.put("中医儿科学", "中医学类");
	  CC_ZY_CATEGORY.put("中医骨伤科学", "中医学类");
	  CC_ZY_CATEGORY.put("中西医临床医学", "中西医结合类");
	  CC_ZY_CATEGORY.put("药学", "药学类");
	  CC_ZY_CATEGORY.put("药物制剂", "药学类");
	  CC_ZY_CATEGORY.put("临床药学", "药学类");
	  CC_ZY_CATEGORY.put("药事管理", "药学类");
	  CC_ZY_CATEGORY.put("药物分析", "药学类");
	  CC_ZY_CATEGORY.put("药物化学", "药学类");
	  CC_ZY_CATEGORY.put("海洋药学", "药学类");
	  CC_ZY_CATEGORY.put("化妆品科学与技术", "药学类");
	  CC_ZY_CATEGORY.put("中药学", "中药学类");
	  CC_ZY_CATEGORY.put("中药资源与开发", "中药学类");
	  CC_ZY_CATEGORY.put("藏药学", "中药学类");
	  CC_ZY_CATEGORY.put("蒙药学", "中药学类");
	  CC_ZY_CATEGORY.put("中药制药", "中药学类");
	  CC_ZY_CATEGORY.put("中草药栽培与鉴定", "中药学类");
	  CC_ZY_CATEGORY.put("法医学", "法医学类");
	  CC_ZY_CATEGORY.put("医学检验技术", "医学技术类");
	  CC_ZY_CATEGORY.put("医学实验技术", "医学技术类");
	  CC_ZY_CATEGORY.put("医学影像技术", "医学技术类");
	  CC_ZY_CATEGORY.put("眼视光学", "医学技术类");
	  CC_ZY_CATEGORY.put("康复治疗学", "医学技术类");
	  CC_ZY_CATEGORY.put("口腔医学技术", "医学技术类");
	  CC_ZY_CATEGORY.put("卫生检验与检疫", "医学技术类");
	  CC_ZY_CATEGORY.put("听力与言语康复学", "医学技术类");
	  CC_ZY_CATEGORY.put("康复物理治疗", "医学技术类");
	  CC_ZY_CATEGORY.put("康复作业治疗", "医学技术类");
	  CC_ZY_CATEGORY.put("智能医学工程", "医学技术类");
	  CC_ZY_CATEGORY.put("护理学", "护理学类");
	  CC_ZY_CATEGORY.put("助产学", "护理学类");
	  CC_ZY_CATEGORY.put("管理科学", "管理科学与工程类");
	  CC_ZY_CATEGORY.put("信息管理与信息系统", "管理科学与工程类");
	  CC_ZY_CATEGORY.put("工程管理", "管理科学与工程类");
	  CC_ZY_CATEGORY.put("房地产开发与管理", "管理科学与工程类");
	  CC_ZY_CATEGORY.put("工程造价", "管理科学与工程类");
	  CC_ZY_CATEGORY.put("保密管理", "管理科学与工程类");
	  CC_ZY_CATEGORY.put("邮政管理", "管理科学与工程类");
	  CC_ZY_CATEGORY.put("大数据管理与应用", "管理科学与工程类");
	  CC_ZY_CATEGORY.put("工程审计", "管理科学与工程类");
	  CC_ZY_CATEGORY.put("计算金融", "管理科学与工程类");
	  CC_ZY_CATEGORY.put("工商管理", "工商管理类");
	  CC_ZY_CATEGORY.put("市场营销", "工商管理类");
	  CC_ZY_CATEGORY.put("会计学", "工商管理类");
	  CC_ZY_CATEGORY.put("财务管理", "工商管理类");
	  CC_ZY_CATEGORY.put("国际商务", "工商管理类");
	  CC_ZY_CATEGORY.put("人力资源管理", "工商管理类");
	  CC_ZY_CATEGORY.put("审计学", "工商管理类");
	  CC_ZY_CATEGORY.put("资产评估", "工商管理类");
	  CC_ZY_CATEGORY.put("物业管理", "工商管理类");
	  CC_ZY_CATEGORY.put("文化产业管理", "工商管理类");
	  CC_ZY_CATEGORY.put("劳动关系", "工商管理类");
	  CC_ZY_CATEGORY.put("体育经济与管理", "工商管理类");
	  CC_ZY_CATEGORY.put("财务会计教育", "工商管理类");
	  CC_ZY_CATEGORY.put("市场营销教育", "工商管理类");
	  CC_ZY_CATEGORY.put("零售业管理", "工商管理类");
	  CC_ZY_CATEGORY.put("农林经济管理", "农业经济管理类");
	  CC_ZY_CATEGORY.put("农村区域发展", "农业经济管理类");
	  CC_ZY_CATEGORY.put("公共事业管理", "公共管理类");
	  CC_ZY_CATEGORY.put("行政管理", "公共管理类");
	  CC_ZY_CATEGORY.put("劳动与社会保障", "公共管理类");
	  CC_ZY_CATEGORY.put("土地资源管理", "公共管理类");
	  CC_ZY_CATEGORY.put("城市管理", "公共管理类");
	  CC_ZY_CATEGORY.put("海关管理", "公共管理类");
	  CC_ZY_CATEGORY.put("交通管理", "公共管理类");
	  CC_ZY_CATEGORY.put("海事管理", "公共管理类");
	  CC_ZY_CATEGORY.put("公共关系学", "公共管理类");
	  CC_ZY_CATEGORY.put("健康服务与管理", "公共管理类");
	  CC_ZY_CATEGORY.put("海警后勤管理", "公共管理类");
	  CC_ZY_CATEGORY.put("医疗产品管理", "公共管理类");
	  CC_ZY_CATEGORY.put("图书馆学", "图书情报与档案管理类");
	  CC_ZY_CATEGORY.put("档案学", "图书情报与档案管理类");
	  CC_ZY_CATEGORY.put("信息资源管理", "图书情报与档案管理类");
	  CC_ZY_CATEGORY.put("物流管理", "物流管理与工程类");
	  CC_ZY_CATEGORY.put("物流工程", "物流管理与工程类");
	  CC_ZY_CATEGORY.put("采购管理", "物流管理与工程类");
	  CC_ZY_CATEGORY.put("供应链管理", "物流管理与工程类");
	  CC_ZY_CATEGORY.put("工业工程", "工业工程类");
	  CC_ZY_CATEGORY.put("标准化工程", "工业工程类");
	  CC_ZY_CATEGORY.put("质量管理工程", "工业工程类");
	  CC_ZY_CATEGORY.put("电子商务", "电子商务类");
	  CC_ZY_CATEGORY.put("电子商务及法律", "电子商务类");
	  CC_ZY_CATEGORY.put("旅游管理", "旅游管理类");
	  CC_ZY_CATEGORY.put("酒店管理", "旅游管理类");
	  CC_ZY_CATEGORY.put("会展经济与管理", "旅游管理类");
	  CC_ZY_CATEGORY.put("旅游管理与服务教育", "旅游管理类");
	  CC_ZY_CATEGORY.put("艺术史论", "艺术学理论类");
	  CC_ZY_CATEGORY.put("艺术管理", "艺术学理论类");
	  CC_ZY_CATEGORY.put("音乐表演", "音乐与舞蹈学类");
	  CC_ZY_CATEGORY.put("音乐学", "音乐与舞蹈学类");
	  CC_ZY_CATEGORY.put("作曲与作曲技术理论", "音乐与舞蹈学类");
	  CC_ZY_CATEGORY.put("舞蹈表演", "音乐与舞蹈学类");
	  CC_ZY_CATEGORY.put("舞蹈学", "音乐与舞蹈学类");
	  CC_ZY_CATEGORY.put("舞蹈编导", "音乐与舞蹈学类");
	  CC_ZY_CATEGORY.put("舞蹈教育", "音乐与舞蹈学类");
	  CC_ZY_CATEGORY.put("航空服务艺术与管理", "音乐与舞蹈学类");
	  CC_ZY_CATEGORY.put("流行音乐", "音乐与舞蹈学类");
	  CC_ZY_CATEGORY.put("音乐治疗", "音乐与舞蹈学类");
	  CC_ZY_CATEGORY.put("流行舞蹈", "音乐与舞蹈学类");
	  CC_ZY_CATEGORY.put("表演", "戏剧与影视学类");
	  CC_ZY_CATEGORY.put("戏剧学", "戏剧与影视学类");
	  CC_ZY_CATEGORY.put("电影学", "戏剧与影视学类");
	  CC_ZY_CATEGORY.put("戏剧影视文学", "戏剧与影视学类");
	  CC_ZY_CATEGORY.put("视觉传达设计", "设计学类");
	  CC_ZY_CATEGORY.put("环境设计", "设计学类");
	  CC_ZY_CATEGORY.put("产品设计", "设计学类");
	  CC_ZY_CATEGORY.put("服装与服饰设计", "设计学类");
	  CC_ZY_CATEGORY.put("公共艺术", "设计学类");
	  CC_ZY_CATEGORY.put("工艺美术", "设计学类");
	  CC_ZY_CATEGORY.put("数字媒体艺术", "设计学类");
	  CC_ZY_CATEGORY.put("艺术与科技", "设计学类");
	  CC_ZY_CATEGORY.put("陶瓷艺术设计", "设计学类");
	  CC_ZY_CATEGORY.put("新媒体艺术", "设计学类");
	  CC_ZY_CATEGORY.put("包装设计", "设计学类");
	  CC_ZY_CATEGORY.put("作物生产技术", "农业类");
	  CC_ZY_CATEGORY.put("种子生产与经营", "农业类");
	  CC_ZY_CATEGORY.put("设施农业与装备", "农业类");
	  CC_ZY_CATEGORY.put("现代农业技术", "农业类");
	  CC_ZY_CATEGORY.put("休闲农业", "农业类");
	  CC_ZY_CATEGORY.put("生态农业技术", "农业类");
	  CC_ZY_CATEGORY.put("园艺技术", "农业类");
	  CC_ZY_CATEGORY.put("植物保护与检疫技术", "农业类");
	  CC_ZY_CATEGORY.put("茶树栽培与茶叶加工", "农业类");
	  CC_ZY_CATEGORY.put("广播电视编导", "戏剧与影视学类");
	  CC_ZY_CATEGORY.put("戏剧影视导演", "戏剧与影视学类");
	  CC_ZY_CATEGORY.put("戏剧影视美术设计", "戏剧与影视学类");
	  CC_ZY_CATEGORY.put("录音艺术", "戏剧与影视学类");
	  CC_ZY_CATEGORY.put("播音与主持艺术", "戏剧与影视学类");
	  CC_ZY_CATEGORY.put("动画", "戏剧与影视学类");
	  CC_ZY_CATEGORY.put("影视摄影与制作", "戏剧与影视学类");
	  CC_ZY_CATEGORY.put("影视技术", "戏剧与影视学类");
	  CC_ZY_CATEGORY.put("戏剧教育", "戏剧与影视学类");
	  CC_ZY_CATEGORY.put("美术学", "美术学类");
	  CC_ZY_CATEGORY.put("绘画", "美术学类");
	  CC_ZY_CATEGORY.put("雕塑", "美术学类");
	  CC_ZY_CATEGORY.put("摄影", "美术学类");
	  CC_ZY_CATEGORY.put("书法学", "美术学类");
	  CC_ZY_CATEGORY.put("中国画", "美术学类");
	  CC_ZY_CATEGORY.put("实验艺术", "美术学类");
	  CC_ZY_CATEGORY.put("跨媒体艺术", "美术学类");
	  CC_ZY_CATEGORY.put("文物保护与修复", "美术学类");
	  CC_ZY_CATEGORY.put("漫画", "美术学类");
	  CC_ZY_CATEGORY.put("艺术设计学", "设计学类");
	  CC_ZY_CATEGORY.put("中草药栽培技术", "农业类");
	  CC_ZY_CATEGORY.put("烟草栽培与加工", "农业类");
	  CC_ZY_CATEGORY.put("棉花加工与经营管理", "农业类");
	  CC_ZY_CATEGORY.put("农产品加工与质量检测", "农业类");
	  CC_ZY_CATEGORY.put("绿色食品生产与检验", "农业类");
	  CC_ZY_CATEGORY.put("农资营销与服务", "农业类");
	  CC_ZY_CATEGORY.put("农产品流通与管理", "农业类");
	  CC_ZY_CATEGORY.put("农业装备应用技术", "农业类");
	  CC_ZY_CATEGORY.put("农业经济管理", "农业类");
	  CC_ZY_CATEGORY.put("农村经营管理", "农业类");
	  CC_ZY_CATEGORY.put("食用菌生产与加工", "农业类");
	  CC_ZY_CATEGORY.put("林业技术", "林业类");
	  CC_ZY_CATEGORY.put("园林技术", "林业类");
	  CC_ZY_CATEGORY.put("森林资源保护", "林业类");
	  CC_ZY_CATEGORY.put("经济林培育与利用", "林业类");
	  CC_ZY_CATEGORY.put("野生植物资源保护与利用", "林业类");
	  CC_ZY_CATEGORY.put("野生动物资源保护与利用", "林业类");
	  CC_ZY_CATEGORY.put("森林生态旅游", "林业类");
	  CC_ZY_CATEGORY.put("森林防火指挥与通讯", "林业类");
	  CC_ZY_CATEGORY.put("自然保护区建设与管理", "林业类");
	  CC_ZY_CATEGORY.put("木材加工技术", "林业类");
	  CC_ZY_CATEGORY.put("林业调查与信息处理", "林业类");
	  CC_ZY_CATEGORY.put("林业信息技术与管理", "林业类");
	  CC_ZY_CATEGORY.put("木工设备应用技术", "林业类");
	  CC_ZY_CATEGORY.put("畜牧兽医", "畜牧业类");
	  CC_ZY_CATEGORY.put("动物医学", "畜牧业类");
	  CC_ZY_CATEGORY.put("动物药学", "畜牧业类");
	  CC_ZY_CATEGORY.put("动物防疫与检疫", "畜牧业类");
	  CC_ZY_CATEGORY.put("动物医学检验技术", "畜牧业类");
	  CC_ZY_CATEGORY.put("宠物养护与驯导", "畜牧业类");
	  CC_ZY_CATEGORY.put("实验动物技术", "畜牧业类");
	  CC_ZY_CATEGORY.put("饲料与动物营养", "畜牧业类");
	  CC_ZY_CATEGORY.put("特种动物养殖", "畜牧业类");
	  CC_ZY_CATEGORY.put("畜牧工程技术", "畜牧业类");
	  CC_ZY_CATEGORY.put("蚕桑技术", "畜牧业类");
	  CC_ZY_CATEGORY.put("草业技术", "畜牧业类");
	  CC_ZY_CATEGORY.put("养蜂与蜂产品加工", "畜牧业类");
	  CC_ZY_CATEGORY.put("畜牧业经济管理", "畜牧业类");
	  CC_ZY_CATEGORY.put("宠物临床诊疗技术", "畜牧业类");
	  CC_ZY_CATEGORY.put("水产养殖技术", "渔业类");
	  CC_ZY_CATEGORY.put("海洋渔业技术", "渔业类");
	  CC_ZY_CATEGORY.put("水族科学与技术", "渔业类");
	  CC_ZY_CATEGORY.put("水生动物医学", "渔业类");
	  CC_ZY_CATEGORY.put("渔业经济管理", "渔业类");
	  CC_ZY_CATEGORY.put("国土资源调查与管理", "资源勘查类");
	  CC_ZY_CATEGORY.put("地质调查与矿产普查", "资源勘查类");
	  CC_ZY_CATEGORY.put("矿产地质与勘查", "资源勘查类");
	  CC_ZY_CATEGORY.put("岩矿分析与鉴定", "资源勘查类");
	  CC_ZY_CATEGORY.put("宝玉石鉴定与加工", "资源勘查类");
	  CC_ZY_CATEGORY.put("煤田地质与勘查技术", "资源勘查类");
	  CC_ZY_CATEGORY.put("权籍信息化管理", "资源勘查类");
	  CC_ZY_CATEGORY.put("工程地质勘查", "地质类");
	  CC_ZY_CATEGORY.put("水文与工程地质", "地质类");
	  CC_ZY_CATEGORY.put("钻探技术", "地质类");
	  CC_ZY_CATEGORY.put("矿山地质", "地质类");
	  CC_ZY_CATEGORY.put("地球物理勘探技术", "地质类");
	  CC_ZY_CATEGORY.put("地质灾害调查与防治", "地质类");
	  CC_ZY_CATEGORY.put("环境地质工程", "地质类");
	  CC_ZY_CATEGORY.put("岩土工程技术", "地质类");
	  CC_ZY_CATEGORY.put("工程测量技术", "测绘地理信息类");
	  CC_ZY_CATEGORY.put("摄影测量与遥感技术", "测绘地理信息类");
	  CC_ZY_CATEGORY.put("测绘工程技术", "测绘地理信息类");
	  CC_ZY_CATEGORY.put("测绘地理信息技术", "测绘地理信息类");
	  CC_ZY_CATEGORY.put("地籍测绘与土地管理", "测绘地理信息类");
	  CC_ZY_CATEGORY.put("矿山测量", "测绘地理信息类");
	  CC_ZY_CATEGORY.put("测绘与地质工程技术", "测绘地理信息类");
	  CC_ZY_CATEGORY.put("导航与位置服务", "测绘地理信息类");
	  CC_ZY_CATEGORY.put("地图制图与数字传播技术", "测绘地理信息类");
	  CC_ZY_CATEGORY.put("地理国情监测技术", "测绘地理信息类");
	  CC_ZY_CATEGORY.put("国土测绘与规划", "测绘地理信息类");
	  CC_ZY_CATEGORY.put("钻井技术", "石油与天然气类");
	  CC_ZY_CATEGORY.put("油气开采技术", "石油与天然气类");
	  CC_ZY_CATEGORY.put("油气储运技术", "石油与天然气类");
	  CC_ZY_CATEGORY.put("油气地质勘探技术", "石油与天然气类");
	  CC_ZY_CATEGORY.put("油田化学应用技术", "石油与天然气类");
	  CC_ZY_CATEGORY.put("石油工程技术", "石油与天然气类");
	  CC_ZY_CATEGORY.put("煤矿开采技术", "煤炭类");
	  CC_ZY_CATEGORY.put("矿井建设", "煤炭类");
	  CC_ZY_CATEGORY.put("矿山机电技术", "煤炭类");
	  CC_ZY_CATEGORY.put("矿井通风与安全", "煤炭类");
	  CC_ZY_CATEGORY.put("综合机械化采煤", "煤炭类");
	  CC_ZY_CATEGORY.put("选煤技术", "煤炭类");
	  CC_ZY_CATEGORY.put("煤炭深加工与利用", "煤炭类");
	  CC_ZY_CATEGORY.put("煤化分析与检验", "煤炭类");
	  CC_ZY_CATEGORY.put("煤层气采输技术", "煤炭类");
	  CC_ZY_CATEGORY.put("矿井运输与提升", "煤炭类");
	  CC_ZY_CATEGORY.put("金属与非金属矿开采技术", "金属与非金属矿类");
	  CC_ZY_CATEGORY.put("矿物加工技术", "金属与非金属矿类");
	  CC_ZY_CATEGORY.put("矿业装备维护技术", "金属与非金属矿类");
	  CC_ZY_CATEGORY.put("大气科学技术", "气象类");
	  CC_ZY_CATEGORY.put("大气探测技术", "气象类");
	  CC_ZY_CATEGORY.put("应用气象技术", "气象类");
	  CC_ZY_CATEGORY.put("防雷技术", "气象类");
	  CC_ZY_CATEGORY.put("环境监测与控制技术", "环境保护类");
	  CC_ZY_CATEGORY.put("农村环境保护", "环境保护类");
	  CC_ZY_CATEGORY.put("室内环境检测与控制技术", "环境保护类");
	  CC_ZY_CATEGORY.put("环境工程技术", "环境保护类");
	  CC_ZY_CATEGORY.put("环境信息技术", "环境保护类");
	  CC_ZY_CATEGORY.put("核与辐射检测防护技术", "环境保护类");
	  CC_ZY_CATEGORY.put("环境规划与管理", "环境保护类");
	  CC_ZY_CATEGORY.put("环境评价与咨询服务", "环境保护类");
	  CC_ZY_CATEGORY.put("污染修复与生态工程技术", "环境保护类");
	  CC_ZY_CATEGORY.put("清洁生产与减排技术", "环境保护类");
	  CC_ZY_CATEGORY.put("资源综合利用与管理技术", "环境保护类");
	  CC_ZY_CATEGORY.put("水净化与安全技术", "环境保护类");
	  CC_ZY_CATEGORY.put("安全健康与环保", "安全类");
	  CC_ZY_CATEGORY.put("化工安全技术", "安全类");
	  CC_ZY_CATEGORY.put("救援技术", "安全类");
	  CC_ZY_CATEGORY.put("安全技术与管理", "安全类");
	  CC_ZY_CATEGORY.put("工程安全评价与监理", "安全类");
	  CC_ZY_CATEGORY.put("安全生产监测监控", "安全类");
	  CC_ZY_CATEGORY.put("职业卫生技术与管理", "安全类");
	  CC_ZY_CATEGORY.put("发电厂及电力系统", "电力技术类");
	  CC_ZY_CATEGORY.put("供用电技术", "电力技术类");
	  CC_ZY_CATEGORY.put("电力系统自动化技术", "电力技术类");
	  CC_ZY_CATEGORY.put("高压输配电线路施工运行与维护", "电力技术类");
	  CC_ZY_CATEGORY.put("电力系统继电保护与自动化技术", "电力技术类");
	  CC_ZY_CATEGORY.put("水电站机电设备与自动化", "电力技术类");
	  CC_ZY_CATEGORY.put("电网监控技术", "电力技术类");
	  CC_ZY_CATEGORY.put("电力客户服务与管理", "电力技术类");
	  CC_ZY_CATEGORY.put("水电站与电力网", "电力技术类");
	  CC_ZY_CATEGORY.put("电源变换技术与应用", "电力技术类");
	  CC_ZY_CATEGORY.put("农业电气化技术", "电力技术类");
	  CC_ZY_CATEGORY.put("分布式发电与微电网技术", "电力技术类");
	  CC_ZY_CATEGORY.put("机场电工技术", "电力技术类");
	  CC_ZY_CATEGORY.put("电厂热能动力装置", "热能与发电工程类");
	  CC_ZY_CATEGORY.put("城市热能应用技术", "热能与发电工程类");
	  CC_ZY_CATEGORY.put("核电站动力设备运行与维护", "热能与发电工程类");
	  CC_ZY_CATEGORY.put("火电厂集控运行", "热能与发电工程类");
	  CC_ZY_CATEGORY.put("电厂化学与环保技术", "热能与发电工程类");
	  CC_ZY_CATEGORY.put("电厂热工自动化技术", "热能与发电工程类");
	  CC_ZY_CATEGORY.put("风力发电工程技术", "新能源发电工程类");
	  CC_ZY_CATEGORY.put("风电系统运行与维护", "新能源发电工程类");
	  CC_ZY_CATEGORY.put("生物质能应用技术", "新能源发电工程类");
	  CC_ZY_CATEGORY.put("光伏发电技术与应用", "新能源发电工程类");
	  CC_ZY_CATEGORY.put("工业节能技术", "新能源发电工程类");
	  CC_ZY_CATEGORY.put("节电技术与管理", "新能源发电工程类");
	  CC_ZY_CATEGORY.put("太阳能光热技术与应用", "新能源发电工程类");
	  CC_ZY_CATEGORY.put("农村能源与环境技术", "新能源发电工程类");
	  CC_ZY_CATEGORY.put("氢能技术应用", "新能源发电工程类");
	  CC_ZY_CATEGORY.put("建筑装饰材料技术", "建筑材料类");
	  CC_ZY_CATEGORY.put("建筑材料设备应用", "建筑材料类");
	  CC_ZY_CATEGORY.put("新型建筑材料技术", "建筑材料类");
	  CC_ZY_CATEGORY.put("建筑材料生产与管理", "建筑材料类");
	  CC_ZY_CATEGORY.put("建筑设计", "建筑设计类");
	  CC_ZY_CATEGORY.put("建筑装饰工程技术", "建筑设计类");
	  CC_ZY_CATEGORY.put("古建筑工程技术", "建筑设计类");
	  CC_ZY_CATEGORY.put("建筑室内设计", "建筑设计类");
	  CC_ZY_CATEGORY.put("风景园林设计", "建筑设计类");
	  CC_ZY_CATEGORY.put("园林工程技术", "建筑设计类");
	  CC_ZY_CATEGORY.put("建筑动画与模型制作", "建筑设计类");
	  CC_ZY_CATEGORY.put("城乡规划", "城乡规划与管理类");
	  CC_ZY_CATEGORY.put("村镇建设与管理", "城乡规划与管理类");
	  CC_ZY_CATEGORY.put("城市信息化管理", "城乡规划与管理类");
	  CC_ZY_CATEGORY.put("建筑工程技术", "土建施工类");
	  CC_ZY_CATEGORY.put("地下与隧道工程技术", "土建施工类");
	  CC_ZY_CATEGORY.put("土木工程检测技术", "土建施工类");
	  CC_ZY_CATEGORY.put("建筑钢结构工程技术", "土建施工类");
	  CC_ZY_CATEGORY.put("建筑设备工程技术", "建筑设备类");
	  CC_ZY_CATEGORY.put("供热通风与空调工程技术", "建筑设备类");
	  CC_ZY_CATEGORY.put("黑色冶金技术", "黑色金属材料类");
	  CC_ZY_CATEGORY.put("轧钢工程技术", "黑色金属材料类");
	  CC_ZY_CATEGORY.put("钢铁冶金设备应用技术", "黑色金属材料类");
	  CC_ZY_CATEGORY.put("金属材料质量检测", "黑色金属材料类");
	  CC_ZY_CATEGORY.put("铁矿资源综合利用", "黑色金属材料类");
	  CC_ZY_CATEGORY.put("有色冶金技术", "有色金属材料类");
	  CC_ZY_CATEGORY.put("有色冶金设备应用技术", "有色金属材料类");
	  CC_ZY_CATEGORY.put("金属压力加工", "有色金属材料类");
	  CC_ZY_CATEGORY.put("金属精密成型技术", "有色金属材料类");
	  CC_ZY_CATEGORY.put("储能材料技术", "有色金属材料类");
	  CC_ZY_CATEGORY.put("材料工程技术", "非金属材料类");
	  CC_ZY_CATEGORY.put("高分子材料工程技术", "非金属材料类");
	  CC_ZY_CATEGORY.put("复合材料工程技术", "非金属材料类");
	  CC_ZY_CATEGORY.put("非金属矿物材料技术", "非金属材料类");
	  CC_ZY_CATEGORY.put("光伏材料制备技术", "非金属材料类");
	  CC_ZY_CATEGORY.put("炭素加工技术", "非金属材料类");
	  CC_ZY_CATEGORY.put("硅材料制备技术", "非金属材料类");
	  CC_ZY_CATEGORY.put("橡胶工程技术", "非金属材料类");
	  CC_ZY_CATEGORY.put("建筑材料工程技术", "建筑材料类");
	  CC_ZY_CATEGORY.put("建筑材料检测技术", "建筑材料类");
	  CC_ZY_CATEGORY.put("建筑电气工程技术", "建筑设备类");
	  CC_ZY_CATEGORY.put("建筑智能化工程技术", "建筑设备类");
	  CC_ZY_CATEGORY.put("工业设备安装工程技术", "建筑设备类");
	  CC_ZY_CATEGORY.put("消防工程技术", "建筑设备类");
	  CC_ZY_CATEGORY.put("建设工程管理", "建设工程管理类");
	  CC_ZY_CATEGORY.put("工程造价", "建设工程管理类");
	  CC_ZY_CATEGORY.put("建筑经济管理", "建设工程管理类");
	  CC_ZY_CATEGORY.put("建设项目信息化管理", "建设工程管理类");
	  CC_ZY_CATEGORY.put("建设工程监理", "建设工程管理类");
	  CC_ZY_CATEGORY.put("市政工程技术", "市政工程类");
	  CC_ZY_CATEGORY.put("城市燃气工程技术", "市政工程类");
	  CC_ZY_CATEGORY.put("给排水工程技术", "市政工程类");
	  CC_ZY_CATEGORY.put("环境卫生工程技术", "市政工程类");
	  CC_ZY_CATEGORY.put("房地产经营与管理", "房地产类");
	  CC_ZY_CATEGORY.put("房地产检测与估价", "房地产类");
	  CC_ZY_CATEGORY.put("物业管理", "房地产类");
	  CC_ZY_CATEGORY.put("水文与水资源工程", "水文水资源类");
	  CC_ZY_CATEGORY.put("水文测报技术", "水文水资源类");
	  CC_ZY_CATEGORY.put("水政水资源管理", "水文水资源类");
	  CC_ZY_CATEGORY.put("水利工程", "水利工程与管理类");
	  CC_ZY_CATEGORY.put("水利水电工程技术", "水利工程与管理类");
	  CC_ZY_CATEGORY.put("水利水电工程管理", "水利工程与管理类");
	  CC_ZY_CATEGORY.put("水利水电建筑工程", "水利工程与管理类");
	  CC_ZY_CATEGORY.put("机电排灌工程技术", "水利工程与管理类");
	  CC_ZY_CATEGORY.put("港口航道与治河工程", "水利工程与管理类");
	  CC_ZY_CATEGORY.put("水务管理", "水利工程与管理类");
	  CC_ZY_CATEGORY.put("水电站动力设备", "水利水电设备类");
	  CC_ZY_CATEGORY.put("水电站电气设备", "水利水电设备类");
	  CC_ZY_CATEGORY.put("水电站运行与管理", "水利水电设备类");
	  CC_ZY_CATEGORY.put("水利机电设备运行与管理", "水利水电设备类");
	  CC_ZY_CATEGORY.put("水土保持技术", "水土保持与水环境类");
	  CC_ZY_CATEGORY.put("水环境监测与治理", "水土保持与水环境类");
	  CC_ZY_CATEGORY.put("机械设计与制造", "机械设计制造类");
	  CC_ZY_CATEGORY.put("机械制造与自动化", "机械设计制造类");
	  CC_ZY_CATEGORY.put("数控技术", "机械设计制造类");
	  CC_ZY_CATEGORY.put("精密机械技术", "机械设计制造类");
	  CC_ZY_CATEGORY.put("特种加工技术", "机械设计制造类");
	  CC_ZY_CATEGORY.put("材料成型与控制技术", "机械设计制造类");
	  CC_ZY_CATEGORY.put("金属材料与热处理技术", "机械设计制造类");
	  CC_ZY_CATEGORY.put("铸造技术", "机械设计制造类");
	  CC_ZY_CATEGORY.put("锻压技术", "机械设计制造类");
	  CC_ZY_CATEGORY.put("焊接技术与自动化", "机械设计制造类");
	  CC_ZY_CATEGORY.put("机械产品检测检验技术", "机械设计制造类");
	  CC_ZY_CATEGORY.put("理化测试与质检技术", "机械设计制造类");
	  CC_ZY_CATEGORY.put("模具设计与制造", "机械设计制造类");
	  CC_ZY_CATEGORY.put("电机与电器技术", "机械设计制造类");
	  CC_ZY_CATEGORY.put("电线电缆制造技术", "机械设计制造类");
	  CC_ZY_CATEGORY.put("内燃机制造与维修", "机械设计制造类");
	  CC_ZY_CATEGORY.put("机械装备制造技术", "机械设计制造类");
	  CC_ZY_CATEGORY.put("工业设计", "机械设计制造类");
	  CC_ZY_CATEGORY.put("工业工程技术", "机械设计制造类");
	  CC_ZY_CATEGORY.put("自动化生产设备应用", "机电设备类");
	  CC_ZY_CATEGORY.put("机电设备安装技术", "机电设备类");
	  CC_ZY_CATEGORY.put("机电设备维修与管理", "机电设备类");
	  CC_ZY_CATEGORY.put("数控设备应用与维护", "机电设备类");
	  CC_ZY_CATEGORY.put("制冷与空调技术", "机电设备类");
	  CC_ZY_CATEGORY.put("光电制造与应用技术", "机电设备类");
	  CC_ZY_CATEGORY.put("新能源装备技术", "机电设备类");
	  CC_ZY_CATEGORY.put("机电一体化技术", "自动化类");
	  CC_ZY_CATEGORY.put("电气自动化技术", "自动化类");
	  CC_ZY_CATEGORY.put("工业过程自动化技术", "自动化类");
	  CC_ZY_CATEGORY.put("智能控制技术", "自动化类");
	  CC_ZY_CATEGORY.put("工业网络技术", "自动化类");
	  CC_ZY_CATEGORY.put("工业自动化仪表", "自动化类");
	  CC_ZY_CATEGORY.put("液压与气动技术", "自动化类");
	  CC_ZY_CATEGORY.put("电梯工程技术", "自动化类");
	  CC_ZY_CATEGORY.put("工业机器人技术", "自动化类");
	  CC_ZY_CATEGORY.put("铁道机车车辆制造与维护", "铁道装备类");
	  CC_ZY_CATEGORY.put("铁道通信信号设备制造与维护", "铁道装备类");
	  CC_ZY_CATEGORY.put("铁道施工和养路机械制造与维护", "铁道装备类");
	  CC_ZY_CATEGORY.put("船舶工程技术", "船舶与海洋工程装备类");
	  CC_ZY_CATEGORY.put("船舶机械工程技术", "船舶与海洋工程装备类");
	  CC_ZY_CATEGORY.put("船舶电气工程技术", "船舶与海洋工程装备类");
	  CC_ZY_CATEGORY.put("船舶舾装工程技术", "船舶与海洋工程装备类");
	  CC_ZY_CATEGORY.put("船舶涂装工程技术", "船舶与海洋工程装备类");
	  CC_ZY_CATEGORY.put("游艇设计与制造", "船舶与海洋工程装备类");
	  CC_ZY_CATEGORY.put("海洋工程技术", "船舶与海洋工程装备类");
	  CC_ZY_CATEGORY.put("船舶通信与导航", "船舶与海洋工程装备类");
	  CC_ZY_CATEGORY.put("船舶动力工程技术", "船舶与海洋工程装备类");
	  CC_ZY_CATEGORY.put("飞行器制造技术", "航空装备类");
	  CC_ZY_CATEGORY.put("飞行器维修技术", "航空装备类");
	  CC_ZY_CATEGORY.put("航空发动机制造技术", "航空装备类");
	  CC_ZY_CATEGORY.put("航空发动机装试技术", "航空装备类");
	  CC_ZY_CATEGORY.put("航空发动机维修技术", "航空装备类");
	  CC_ZY_CATEGORY.put("飞机机载设备制造技术", "航空装备类");
	  CC_ZY_CATEGORY.put("飞机机载设备维修技术", "航空装备类");
	  CC_ZY_CATEGORY.put("航空电子电气技术", "航空装备类");
	  CC_ZY_CATEGORY.put("航空材料精密成型技术", "航空装备类");
	  CC_ZY_CATEGORY.put("无人机应用技术", "航空装备类");
	  CC_ZY_CATEGORY.put("导弹维修", "航空装备类");
	  CC_ZY_CATEGORY.put("汽车制造与装配技术", "汽车制造类");
	  CC_ZY_CATEGORY.put("汽车检测与维修技术", "汽车制造类");
	  CC_ZY_CATEGORY.put("汽车电子技术", "汽车制造类");
	  CC_ZY_CATEGORY.put("汽车造型技术", "汽车制造类");
	  CC_ZY_CATEGORY.put("汽车试验技术", "汽车制造类");
	  CC_ZY_CATEGORY.put("汽车改装技术", "汽车制造类");
	  CC_ZY_CATEGORY.put("新能源汽车技术", "汽车制造类");
	  CC_ZY_CATEGORY.put("食品生物技术", "生物技术类");
	  CC_ZY_CATEGORY.put("化工生物技术", "生物技术类");
	  CC_ZY_CATEGORY.put("药品生物技术", "生物技术类");
	  CC_ZY_CATEGORY.put("农业生物技术", "生物技术类");
	  CC_ZY_CATEGORY.put("生物产品检验检疫", "生物技术类");
	  CC_ZY_CATEGORY.put("应用化工技术", "化工技术类");
	  CC_ZY_CATEGORY.put("石油炼制技术", "化工技术类");
	  CC_ZY_CATEGORY.put("石油化工技术", "化工技术类");
	  CC_ZY_CATEGORY.put("高分子合成技术", "化工技术类");
	  CC_ZY_CATEGORY.put("精细化工技术", "化工技术类");
	  CC_ZY_CATEGORY.put("海洋化工技术", "化工技术类");
	  CC_ZY_CATEGORY.put("工业分析技术", "化工技术类");
	  CC_ZY_CATEGORY.put("化工装备技术", "化工技术类");
	  CC_ZY_CATEGORY.put("化工自动化技术", "化工技术类");
	  CC_ZY_CATEGORY.put("涂装防护技术", "化工技术类");
	  CC_ZY_CATEGORY.put("烟花爆竹技术与管理", "化工技术类");
	  CC_ZY_CATEGORY.put("煤化工技术", "化工技术类");
	  CC_ZY_CATEGORY.put("高分子材料加工技术", "轻化工类");
	  CC_ZY_CATEGORY.put("制浆造纸技术", "轻化工类");
	  CC_ZY_CATEGORY.put("香料香精工艺", "轻化工类");
	  CC_ZY_CATEGORY.put("表面精饰工艺", "轻化工类");
	  CC_ZY_CATEGORY.put("家具设计与制造", "轻化工类");
	  CC_ZY_CATEGORY.put("化妆品技术", "轻化工类");
	  CC_ZY_CATEGORY.put("皮革加工技术", "轻化工类");
	  CC_ZY_CATEGORY.put("皮具制作与工艺", "轻化工类");
	  CC_ZY_CATEGORY.put("鞋类设计与工艺", "轻化工类");
	  CC_ZY_CATEGORY.put("乐器制造与维护", "轻化工类");
	  CC_ZY_CATEGORY.put("陶瓷制造工艺", "轻化工类");
	  CC_ZY_CATEGORY.put("珠宝首饰技术与管理", "轻化工类");
	  CC_ZY_CATEGORY.put("包装工程技术", "包装类");
	  CC_ZY_CATEGORY.put("包装策划与设计", "包装类");
	  CC_ZY_CATEGORY.put("包装设备应用技术", "包装类");
	  CC_ZY_CATEGORY.put("食品包装技术", "包装类");
	  CC_ZY_CATEGORY.put("数字图文信息技术", "印刷类");
	  CC_ZY_CATEGORY.put("印刷设备应用技术", "印刷类");
	  CC_ZY_CATEGORY.put("印刷媒体设计与制作", "印刷类");
	  CC_ZY_CATEGORY.put("印刷媒体技术", "印刷类");
	  CC_ZY_CATEGORY.put("数字印刷技术", "印刷类");
	  CC_ZY_CATEGORY.put("现代纺织技术", "纺织服装类");
	  CC_ZY_CATEGORY.put("丝绸技术", "纺织服装类");
	  CC_ZY_CATEGORY.put("染整技术", "纺织服装类");
	  CC_ZY_CATEGORY.put("纺织机电技术", "纺织服装类");
	  CC_ZY_CATEGORY.put("纺织品检验与贸易", "纺织服装类");
	  CC_ZY_CATEGORY.put("纺织品设计", "纺织服装类");
	  CC_ZY_CATEGORY.put("家用纺织品设计", "纺织服装类");
	  CC_ZY_CATEGORY.put("纺织材料与应用", "纺织服装类");
	  CC_ZY_CATEGORY.put("针织技术与针织服装", "纺织服装类");
	  CC_ZY_CATEGORY.put("服装设计与工艺", "纺织服装类");
	  CC_ZY_CATEGORY.put("皮革服装制作与工艺", "纺织服装类");
	  CC_ZY_CATEGORY.put("服装陈列与展示设计", "纺织服装类");
	  CC_ZY_CATEGORY.put("食品加工技术", "食品工业类");
	  CC_ZY_CATEGORY.put("酿酒技术", "食品工业类");
	  CC_ZY_CATEGORY.put("食品质量与安全", "食品工业类");
	  CC_ZY_CATEGORY.put("食品贮运与营销", "食品工业类");
	  CC_ZY_CATEGORY.put("食品检测技术", "食品工业类");
	  CC_ZY_CATEGORY.put("食品营养与卫生", "食品工业类");
	  CC_ZY_CATEGORY.put("食品营养与检测", "食品工业类");
	  CC_ZY_CATEGORY.put("中药生产与加工", "药品制造类");
	  CC_ZY_CATEGORY.put("药品生产技术", "药品制造类");
	  CC_ZY_CATEGORY.put("兽药制药技术", "药品制造类");
	  CC_ZY_CATEGORY.put("药品质量与安全", "药品制造类");
	  CC_ZY_CATEGORY.put("制药设备应用技术", "药品制造类");
	  CC_ZY_CATEGORY.put("化学制药技术", "药品制造类");
	  CC_ZY_CATEGORY.put("生物制药技术", "药品制造类");
	  CC_ZY_CATEGORY.put("中药制药技术", "药品制造类");
	  CC_ZY_CATEGORY.put("药物制剂技术", "药品制造类");
	  CC_ZY_CATEGORY.put("药品经营与管理", "食品药品管理类");
	  CC_ZY_CATEGORY.put("药品服务与管理", "食品药品管理类");
	  CC_ZY_CATEGORY.put("保健品开发与管理", "食品药品管理类");
	  CC_ZY_CATEGORY.put("化妆品经营与管理", "食品药品管理类");
	  CC_ZY_CATEGORY.put("食品药品监督管理", "食品药品管理类");
	  CC_ZY_CATEGORY.put("粮食工程技术", "粮食工业类");
	  CC_ZY_CATEGORY.put("粮油储藏与检测技术", "粮食储检类");
	  CC_ZY_CATEGORY.put("铁道机车", "铁道运输类");
	  CC_ZY_CATEGORY.put("铁道车辆", "铁道运输类");
	  CC_ZY_CATEGORY.put("铁道供电技术", "铁道运输类");
	  CC_ZY_CATEGORY.put("铁道工程技术", "铁道运输类");
	  CC_ZY_CATEGORY.put("铁道机械化维修技术", "铁道运输类");
	  CC_ZY_CATEGORY.put("铁道信号自动控制", "铁道运输类");
	  CC_ZY_CATEGORY.put("铁道通信与信息化技术", "铁道运输类");
	  CC_ZY_CATEGORY.put("铁道交通运营管理", "铁道运输类");
	  CC_ZY_CATEGORY.put("铁路物流管理", "铁道运输类");
	  CC_ZY_CATEGORY.put("铁路桥梁与隧道工程技术", "铁道运输类");
	  CC_ZY_CATEGORY.put("高速铁道工程技术", "铁道运输类");
	  CC_ZY_CATEGORY.put("高速铁路客运乘务", "铁道运输类");
	  CC_ZY_CATEGORY.put("动车组检修技术", "铁道运输类");
	  CC_ZY_CATEGORY.put("高铁综合维修技术", "铁道运输类");
	  CC_ZY_CATEGORY.put("智能交通技术运用", "道路运输类");
	  CC_ZY_CATEGORY.put("道路桥梁工程技术", "道路运输类");
	  CC_ZY_CATEGORY.put("道路运输与路政管理", "道路运输类");
	  CC_ZY_CATEGORY.put("道路养护与管理", "道路运输类");
	  CC_ZY_CATEGORY.put("公路机械化施工技术", "道路运输类");
	  CC_ZY_CATEGORY.put("工程机械运用技术", "道路运输类");
	  CC_ZY_CATEGORY.put("交通运营管理", "道路运输类");
	  CC_ZY_CATEGORY.put("交通枢纽运营管理", "道路运输类");
	  CC_ZY_CATEGORY.put("汽车运用与维修技术", "道路运输类");
	  CC_ZY_CATEGORY.put("汽车车身维修技术", "道路运输类");
	  CC_ZY_CATEGORY.put("汽车运用安全管理", "道路运输类");
	  CC_ZY_CATEGORY.put("新能源汽车运用与维修", "道路运输类");
	  CC_ZY_CATEGORY.put("航海技术", "水上运输类");
	  CC_ZY_CATEGORY.put("国际邮轮乘务管理", "水上运输类");
	  CC_ZY_CATEGORY.put("船舶电子电气技术", "水上运输类");
	  CC_ZY_CATEGORY.put("船舶检验", "水上运输类");
	  CC_ZY_CATEGORY.put("港口机械与自动控制", "水上运输类");
	  CC_ZY_CATEGORY.put("港口电气技术", "水上运输类");
	  CC_ZY_CATEGORY.put("港口与航道工程技术", "水上运输类");
	  CC_ZY_CATEGORY.put("港口与航运管理", "水上运输类");
	  CC_ZY_CATEGORY.put("港口物流管理", "水上运输类");
	  CC_ZY_CATEGORY.put("轮机工程技术", "水上运输类");
	  CC_ZY_CATEGORY.put("水上救捞技术", "水上运输类");
	  CC_ZY_CATEGORY.put("水路运输与海事管理", "水上运输类");
	  CC_ZY_CATEGORY.put("集装箱运输管理", "水上运输类");
	  CC_ZY_CATEGORY.put("民航运输", "航空运输类");
	  CC_ZY_CATEGORY.put("民航通信技术", "航空运输类");
	  CC_ZY_CATEGORY.put("定翼机驾驶技术", "航空运输类");
	  CC_ZY_CATEGORY.put("直升机驾驶技术", "航空运输类");
	  CC_ZY_CATEGORY.put("空中乘务", "航空运输类");
	  CC_ZY_CATEGORY.put("民航安全技术管理", "航空运输类");
	  CC_ZY_CATEGORY.put("民航空中安全保卫", "航空运输类");
	  CC_ZY_CATEGORY.put("机场运行", "航空运输类");
	  CC_ZY_CATEGORY.put("飞机机电设备维修", "航空运输类");
	  CC_ZY_CATEGORY.put("飞机电子设备维修", "航空运输类");
	  CC_ZY_CATEGORY.put("飞机部件修理", "航空运输类");
	  CC_ZY_CATEGORY.put("航空地面设备维修", "航空运输类");
	  CC_ZY_CATEGORY.put("机场场务技术与管理", "航空运输类");
	  CC_ZY_CATEGORY.put("航空油料", "航空运输类");
	  CC_ZY_CATEGORY.put("航空物流", "航空运输类");
	  CC_ZY_CATEGORY.put("通用航空器维修", "航空运输类");
	  CC_ZY_CATEGORY.put("通用航空航务技术", "航空运输类");
	  CC_ZY_CATEGORY.put("飞机结构修理", "航空运输类");
	  CC_ZY_CATEGORY.put("管道工程技术", "管道运输类");
	  CC_ZY_CATEGORY.put("管道运输管理", "管道运输类");
	  CC_ZY_CATEGORY.put("城市轨道交通车辆技术", "城市轨道交通类");
	  CC_ZY_CATEGORY.put("城市轨道交通机电技术", "城市轨道交通类");
	  CC_ZY_CATEGORY.put("城市轨道交通通信信号技术", "城市轨道交通类");
	  CC_ZY_CATEGORY.put("城市轨道交通供配电技术", "城市轨道交通类");
	  CC_ZY_CATEGORY.put("城市轨道交通工程技术", "城市轨道交通类");
	  CC_ZY_CATEGORY.put("城市轨道交通运营管理", "城市轨道交通类");
	  CC_ZY_CATEGORY.put("邮政通信管理", "邮政类");
	  CC_ZY_CATEGORY.put("快递运营管理", "邮政类");
	  CC_ZY_CATEGORY.put("电子信息工程技术", "电子信息类");
	  CC_ZY_CATEGORY.put("应用电子技术", "电子信息类");
	  CC_ZY_CATEGORY.put("微电子技术", "电子信息类");
	  CC_ZY_CATEGORY.put("智能产品开发", "电子信息类");
	  CC_ZY_CATEGORY.put("智能终端技术与应用", "电子信息类");
	  CC_ZY_CATEGORY.put("智能监控技术应用", "电子信息类");
	  CC_ZY_CATEGORY.put("汽车智能技术", "电子信息类");
	  CC_ZY_CATEGORY.put("电子产品质量检测", "电子信息类");
	  CC_ZY_CATEGORY.put("电子产品营销与服务", "电子信息类");
	  CC_ZY_CATEGORY.put("电子电路设计与工艺", "电子信息类");
	  CC_ZY_CATEGORY.put("电子制造技术与设备", "电子信息类");
	  CC_ZY_CATEGORY.put("电子测量技术与仪器", "电子信息类");
	  CC_ZY_CATEGORY.put("电子工艺与管理", "电子信息类");
	  CC_ZY_CATEGORY.put("声像工程技术", "电子信息类");
	  CC_ZY_CATEGORY.put("移动互联应用技术", "电子信息类");
	  CC_ZY_CATEGORY.put("光电技术应用", "电子信息类");
	  CC_ZY_CATEGORY.put("光伏工程技术", "电子信息类");
	  CC_ZY_CATEGORY.put("光电显示技术", "电子信息类");
	  CC_ZY_CATEGORY.put("物联网应用技术", "电子信息类");
	  CC_ZY_CATEGORY.put("集成电路技术应用", "电子信息类");
	  CC_ZY_CATEGORY.put("计算机应用技术", "计算机类");
	  CC_ZY_CATEGORY.put("计算机网络技术", "计算机类");
	  CC_ZY_CATEGORY.put("计算机信息管理", "计算机类");
	  CC_ZY_CATEGORY.put("计算机系统与维护", "计算机类");
	  CC_ZY_CATEGORY.put("软件技术", "计算机类");
	  CC_ZY_CATEGORY.put("软件与信息服务", "计算机类");
	  CC_ZY_CATEGORY.put("动漫制作技术", "计算机类");
	  CC_ZY_CATEGORY.put("嵌入式技术与应用", "计算机类");
	  CC_ZY_CATEGORY.put("数字展示技术", "计算机类");
	  CC_ZY_CATEGORY.put("数字媒体应用技术", "计算机类");
	  CC_ZY_CATEGORY.put("信息安全与管理", "计算机类");
	  CC_ZY_CATEGORY.put("移动应用开发", "计算机类");
	  CC_ZY_CATEGORY.put("云计算技术与应用", "计算机类");
	  CC_ZY_CATEGORY.put("电子商务技术", "计算机类");
	  CC_ZY_CATEGORY.put("大数据技术与应用", "计算机类");
	  CC_ZY_CATEGORY.put("虚拟现实应用技术", "计算机类");
	  CC_ZY_CATEGORY.put("人工智能技术服务", "计算机类");
	  CC_ZY_CATEGORY.put("通信技术", "通信类");
	  CC_ZY_CATEGORY.put("移动通信技术", "通信类");
	  CC_ZY_CATEGORY.put("通信系统运行管理", "通信类");
	  CC_ZY_CATEGORY.put("通信工程设计与监理", "通信类");
	  CC_ZY_CATEGORY.put("光通信技术", "通信类");
	  CC_ZY_CATEGORY.put("物联网工程技术", "通信类");
	  CC_ZY_CATEGORY.put("电信服务与管理", "通信类");
	  CC_ZY_CATEGORY.put("临床医学", "临床医学类");
	  CC_ZY_CATEGORY.put("口腔医学", "口腔医学类");
	  CC_ZY_CATEGORY.put("中医学", "临床医学类");
	  CC_ZY_CATEGORY.put("中医骨伤", "临床医学类");
	  CC_ZY_CATEGORY.put("针灸推拿", "临床医学类");
	  CC_ZY_CATEGORY.put("蒙医学", "临床医学类");
	  CC_ZY_CATEGORY.put("藏医学", "临床医学类");
	  CC_ZY_CATEGORY.put("维医学", "临床医学类");
	  CC_ZY_CATEGORY.put("傣医学", "临床医学类");
	  CC_ZY_CATEGORY.put("哈医学", "临床医学类");
	  CC_ZY_CATEGORY.put("朝医学", "临床医学类");
	  CC_ZY_CATEGORY.put("护理", "护理类");
	  CC_ZY_CATEGORY.put("助产", "护理类");
	  CC_ZY_CATEGORY.put("药学", "药学类");
	  CC_ZY_CATEGORY.put("中药学", "药学类");
	  CC_ZY_CATEGORY.put("蒙药学", "药学类");
	  CC_ZY_CATEGORY.put("维药学", "药学类");
	  CC_ZY_CATEGORY.put("藏药学", "药学类");
	  CC_ZY_CATEGORY.put("医学检验技术", "医学技术类");
	  CC_ZY_CATEGORY.put("医学生物技术", "医学技术类");
	  CC_ZY_CATEGORY.put("医学影像技术", "医学技术类");
	  CC_ZY_CATEGORY.put("医学美容技术", "医学技术类");
	  CC_ZY_CATEGORY.put("口腔医学技术", "医学技术类");
	  CC_ZY_CATEGORY.put("卫生检验与检疫技术", "医学技术类");
	  CC_ZY_CATEGORY.put("眼视光技术", "医学技术类");
	  CC_ZY_CATEGORY.put("放射治疗技术", "医学技术类");
	  CC_ZY_CATEGORY.put("呼吸治疗技术", "医学技术类");
	  CC_ZY_CATEGORY.put("康复治疗技术", "康复治疗类");
	  CC_ZY_CATEGORY.put("言语听觉康复技术", "康复治疗类");
	  CC_ZY_CATEGORY.put("中医康复技术", "康复治疗类");
	  CC_ZY_CATEGORY.put("预防医学", "公共卫生与卫生管理类");
	  CC_ZY_CATEGORY.put("公共卫生管理", "公共卫生与卫生管理类");
	  CC_ZY_CATEGORY.put("卫生监督", "公共卫生与卫生管理类");
	  CC_ZY_CATEGORY.put("卫生信息管理", "公共卫生与卫生管理类");
	  CC_ZY_CATEGORY.put("人口与家庭发展服务", "人口与计划生育类");
	  CC_ZY_CATEGORY.put("生殖健康服务与管理", "人口与计划生育类");
	  CC_ZY_CATEGORY.put("健康管理", "健康管理与促进类");
	  CC_ZY_CATEGORY.put("医学营养", "健康管理与促进类");
	  CC_ZY_CATEGORY.put("中医养生保健", "健康管理与促进类");
	  CC_ZY_CATEGORY.put("心理咨询", "健康管理与促进类");
	  CC_ZY_CATEGORY.put("医疗设备应用技术", "健康管理与促进类");
	  CC_ZY_CATEGORY.put("精密医疗器械技术", "健康管理与促进类");
	  CC_ZY_CATEGORY.put("医疗器械维护与管理", "健康管理与促进类");
	  CC_ZY_CATEGORY.put("康复工程技术", "健康管理与促进类");
	  CC_ZY_CATEGORY.put("康复辅助器具技术", "健康管理与促进类");
	  CC_ZY_CATEGORY.put("假肢与矫形器技术", "健康管理与促进类");
	  CC_ZY_CATEGORY.put("老年保健与管理", "健康管理与促进类");
	  CC_ZY_CATEGORY.put("医疗器械经营与管理", "健康管理与促进类");
	  CC_ZY_CATEGORY.put("财政", "财政税务类");
	  CC_ZY_CATEGORY.put("税务", "财政税务类");
	  CC_ZY_CATEGORY.put("资产评估与管理", "财政税务类");
	  CC_ZY_CATEGORY.put("政府采购管理", "财政税务类");
	  CC_ZY_CATEGORY.put("金融管理", "金融类");
	  CC_ZY_CATEGORY.put("国际金融", "金融类");
	  CC_ZY_CATEGORY.put("证券与期货", "金融类");
	  CC_ZY_CATEGORY.put("信托与租赁", "金融类");
	  CC_ZY_CATEGORY.put("保险", "金融类");
	  CC_ZY_CATEGORY.put("投资与理财", "金融类");
	  CC_ZY_CATEGORY.put("信用管理", "金融类");
	  CC_ZY_CATEGORY.put("农村金融", "金融类");
	  CC_ZY_CATEGORY.put("互联网金融", "金融类");
	  CC_ZY_CATEGORY.put("财务管理", "财务会计类");
	  CC_ZY_CATEGORY.put("会计", "财务会计类");
	  CC_ZY_CATEGORY.put("审计", "财务会计类");
	  CC_ZY_CATEGORY.put("会计信息管理", "财务会计类");
	  CC_ZY_CATEGORY.put("信息统计与分析", "统计类");
	  CC_ZY_CATEGORY.put("统计与会计核算", "统计类");
	  CC_ZY_CATEGORY.put("国际贸易实务", "经济贸易类");
	  CC_ZY_CATEGORY.put("国际经济与贸易", "经济贸易类");
	  CC_ZY_CATEGORY.put("国际商务", "经济贸易类");
	  CC_ZY_CATEGORY.put("服务外包", "经济贸易类");
	  CC_ZY_CATEGORY.put("经济信息管理", "经济贸易类");
	  CC_ZY_CATEGORY.put("报关与国际货运", "经济贸易类");
	  CC_ZY_CATEGORY.put("商务经纪与代理", "经济贸易类");
	  CC_ZY_CATEGORY.put("国际文化贸易", "经济贸易类");
	  CC_ZY_CATEGORY.put("工商企业管理", "工商管理类");
	  CC_ZY_CATEGORY.put("商务管理", "工商管理类");
	  CC_ZY_CATEGORY.put("商检技术", "工商管理类");
	  CC_ZY_CATEGORY.put("连锁经营管理", "工商管理类");
	  CC_ZY_CATEGORY.put("市场管理与服务", "工商管理类");
	  CC_ZY_CATEGORY.put("品牌代理经营", "工商管理类");
	  CC_ZY_CATEGORY.put("中小企业创业与经营", "工商管理类");
	  CC_ZY_CATEGORY.put("市场营销", "市场营销类");
	  CC_ZY_CATEGORY.put("汽车营销与服务", "市场营销类");
	  CC_ZY_CATEGORY.put("广告策划与营销", "市场营销类");
	  CC_ZY_CATEGORY.put("茶艺与茶叶营销", "市场营销类");
	  CC_ZY_CATEGORY.put("电子商务", "电子商务类");
	  CC_ZY_CATEGORY.put("移动商务", "电子商务类");
	  CC_ZY_CATEGORY.put("网络营销", "电子商务类");
	  CC_ZY_CATEGORY.put("商务数据分析与应用", "电子商务类");
	  CC_ZY_CATEGORY.put("跨境电子商务", "电子商务类");
	  CC_ZY_CATEGORY.put("物流工程技术", "物流类");
	  CC_ZY_CATEGORY.put("物流信息技术", "物流类");
	  CC_ZY_CATEGORY.put("物流管理", "物流类");
	  CC_ZY_CATEGORY.put("物流金融管理", "物流类");
	  CC_ZY_CATEGORY.put("工程物流管理", "物流类");
	  CC_ZY_CATEGORY.put("冷链物流技术与管理", "物流类");
	  CC_ZY_CATEGORY.put("采购与供应管理", "物流类");
	  CC_ZY_CATEGORY.put("旅游管理", "旅游类");
	  CC_ZY_CATEGORY.put("导游", "旅游类");
	  CC_ZY_CATEGORY.put("旅行社经营管理", "旅游类");
	  CC_ZY_CATEGORY.put("景区开发与管理", "旅游类");
	  CC_ZY_CATEGORY.put("酒店管理", "旅游类");
	  CC_ZY_CATEGORY.put("休闲服务与管理", "旅游类");
	  CC_ZY_CATEGORY.put("研学旅行管理与服务", "旅游类");
	  CC_ZY_CATEGORY.put("葡萄酒营销与服务", "旅游类");
	  CC_ZY_CATEGORY.put("餐饮管理", "餐饮类");
	  CC_ZY_CATEGORY.put("烹调工艺与营养", "餐饮类");
	  CC_ZY_CATEGORY.put("营养配餐", "餐饮类");
	  CC_ZY_CATEGORY.put("中西面点工艺", "餐饮类");
	  CC_ZY_CATEGORY.put("西餐工艺", "餐饮类");
	  CC_ZY_CATEGORY.put("会展策划与管理", "会展类");
	  CC_ZY_CATEGORY.put("艺术设计", "艺术设计类");
	  CC_ZY_CATEGORY.put("视觉传播设计与制作", "艺术设计类");
	  CC_ZY_CATEGORY.put("广告设计与制作", "艺术设计类");
	  CC_ZY_CATEGORY.put("数字媒体艺术设计", "艺术设计类");
	  CC_ZY_CATEGORY.put("产品艺术设计", "艺术设计类");
	  CC_ZY_CATEGORY.put("家具艺术设计", "艺术设计类");
	  CC_ZY_CATEGORY.put("皮具艺术设计", "艺术设计类");
	  CC_ZY_CATEGORY.put("服装与服饰设计", "艺术设计类");
	  CC_ZY_CATEGORY.put("室内艺术设计", "艺术设计类");
	  CC_ZY_CATEGORY.put("展示艺术设计", "艺术设计类");
	  CC_ZY_CATEGORY.put("环境艺术设计", "艺术设计类");
	  CC_ZY_CATEGORY.put("公共艺术设计", "艺术设计类");
	  CC_ZY_CATEGORY.put("雕刻艺术设计", "艺术设计类");
	  CC_ZY_CATEGORY.put("包装艺术设计", "艺术设计类");
	  CC_ZY_CATEGORY.put("陶瓷设计与工艺", "艺术设计类");
	  CC_ZY_CATEGORY.put("刺绣设计与工艺", "艺术设计类");
	  CC_ZY_CATEGORY.put("玉器设计与工艺", "艺术设计类");
	  CC_ZY_CATEGORY.put("首饰设计与工艺", "艺术设计类");
	  CC_ZY_CATEGORY.put("工艺美术品设计", "艺术设计类");
	  CC_ZY_CATEGORY.put("动漫设计", "艺术设计类");
	  CC_ZY_CATEGORY.put("游戏设计", "艺术设计类");
	  CC_ZY_CATEGORY.put("人物形象设计", "艺术设计类");
	  CC_ZY_CATEGORY.put("美容美体艺术", "艺术设计类");
	  CC_ZY_CATEGORY.put("摄影与摄像艺术", "艺术设计类");
	  CC_ZY_CATEGORY.put("美术", "艺术设计类");
	  CC_ZY_CATEGORY.put("表演艺术", "表演艺术类");
	  CC_ZY_CATEGORY.put("戏剧影视表演", "表演艺术类");
	  CC_ZY_CATEGORY.put("歌舞表演", "表演艺术类");
	  CC_ZY_CATEGORY.put("戏曲表演", "表演艺术类");
	  CC_ZY_CATEGORY.put("曲艺表演", "表演艺术类");
	  CC_ZY_CATEGORY.put("音乐剧表演", "表演艺术类");
	  CC_ZY_CATEGORY.put("舞蹈表演", "表演艺术类");
	  CC_ZY_CATEGORY.put("国际标准舞", "表演艺术类");
	  CC_ZY_CATEGORY.put("服装表演", "表演艺术类");
	  CC_ZY_CATEGORY.put("模特与礼仪", "表演艺术类");
	  CC_ZY_CATEGORY.put("现代流行音乐", "表演艺术类");
	  CC_ZY_CATEGORY.put("作曲技术", "表演艺术类");
	  CC_ZY_CATEGORY.put("音乐制作", "表演艺术类");
	  CC_ZY_CATEGORY.put("钢琴伴奏", "表演艺术类");
	  CC_ZY_CATEGORY.put("钢琴调律", "表演艺术类");
	  CC_ZY_CATEGORY.put("舞蹈编导", "表演艺术类");
	  CC_ZY_CATEGORY.put("戏曲导演", "表演艺术类");
	  CC_ZY_CATEGORY.put("舞台艺术设计与制作", "表演艺术类");
	  CC_ZY_CATEGORY.put("音乐表演", "表演艺术类");
	  CC_ZY_CATEGORY.put("音乐传播", "表演艺术类");
	  CC_ZY_CATEGORY.put("民族表演艺术", "民族文化类");
	  CC_ZY_CATEGORY.put("民族美术", "民族文化类");
	  CC_ZY_CATEGORY.put("民族服装与服饰", "民族文化类");
	  CC_ZY_CATEGORY.put("民族民居装饰", "民族文化类");
	  CC_ZY_CATEGORY.put("民族传统技艺", "民族文化类");
	  CC_ZY_CATEGORY.put("少数民族古籍修复", "民族文化类");
	  CC_ZY_CATEGORY.put("中国少数民族语言文化", "民族文化类");
	  CC_ZY_CATEGORY.put("文化创意与策划", "文化服务类");
	  CC_ZY_CATEGORY.put("文化市场经营管理", "文化服务类");
	  CC_ZY_CATEGORY.put("公共文化服务与管理", "文化服务类");
	  CC_ZY_CATEGORY.put("文物修复与保护", "文化服务类");
	  CC_ZY_CATEGORY.put("考古探掘技术", "文化服务类");
	  CC_ZY_CATEGORY.put("文物博物馆服务与管理", "文化服务类");
	  CC_ZY_CATEGORY.put("图书档案管理", "文化服务类");
	  CC_ZY_CATEGORY.put("图文信息处理", "新闻出版类");
	  CC_ZY_CATEGORY.put("网络新闻与传播", "新闻出版类");
	  CC_ZY_CATEGORY.put("版面编辑与校对", "新闻出版类");
	  CC_ZY_CATEGORY.put("出版商务", "新闻出版类");
	  CC_ZY_CATEGORY.put("出版与电脑编辑技术", "新闻出版类");
	  CC_ZY_CATEGORY.put("出版信息管理", "新闻出版类");
	  CC_ZY_CATEGORY.put("数字出版", "新闻出版类");
	  CC_ZY_CATEGORY.put("数字媒体设备管理", "新闻出版类");
	  CC_ZY_CATEGORY.put("新闻采编与制作", "广播影视类");
	  CC_ZY_CATEGORY.put("播音与主持", "广播影视类");
	  CC_ZY_CATEGORY.put("广播影视节目制作", "广播影视类");
	  CC_ZY_CATEGORY.put("广播电视技术", "广播影视类");
	  CC_ZY_CATEGORY.put("影视制片管理", "广播影视类");
	  CC_ZY_CATEGORY.put("影视编导", "广播影视类");
	  CC_ZY_CATEGORY.put("影视美术", "广播影视类");
	  CC_ZY_CATEGORY.put("影视多媒体技术", "广播影视类");
	  CC_ZY_CATEGORY.put("影视动画", "广播影视类");
	  CC_ZY_CATEGORY.put("影视照明技术与艺术", "广播影视类");
	  CC_ZY_CATEGORY.put("音像技术", "广播影视类");
	  CC_ZY_CATEGORY.put("录音技术与艺术", "广播影视类");
	  CC_ZY_CATEGORY.put("摄影摄像技术", "广播影视类");
	  CC_ZY_CATEGORY.put("传播与策划", "广播影视类");
	  CC_ZY_CATEGORY.put("媒体营销", "广播影视类");
	  CC_ZY_CATEGORY.put("早期教育", "教育类");
	  CC_ZY_CATEGORY.put("学前教育", "教育类");
	  CC_ZY_CATEGORY.put("小学教育", "教育类");
	  CC_ZY_CATEGORY.put("语文教育", "教育类");
	  CC_ZY_CATEGORY.put("数学教育", "教育类");
	  CC_ZY_CATEGORY.put("英语教育", "教育类");
	  CC_ZY_CATEGORY.put("物理教育", "教育类");
	  CC_ZY_CATEGORY.put("化学教育", "教育类");
	  CC_ZY_CATEGORY.put("生物教育", "教育类");
	  CC_ZY_CATEGORY.put("历史教育", "教育类");
	  CC_ZY_CATEGORY.put("地理教育", "教育类");
	  CC_ZY_CATEGORY.put("音乐教育", "教育类");
	  CC_ZY_CATEGORY.put("美术教育", "教育类");
	  CC_ZY_CATEGORY.put("体育教育", "教育类");
	  CC_ZY_CATEGORY.put("思想政治教育", "教育类");
	  CC_ZY_CATEGORY.put("舞蹈教育", "教育类");
	  CC_ZY_CATEGORY.put("艺术教育", "教育类");
	  CC_ZY_CATEGORY.put("特殊教育", "教育类");
	  CC_ZY_CATEGORY.put("科学教育", "教育类");
	  CC_ZY_CATEGORY.put("现代教育技术", "教育类");
	  CC_ZY_CATEGORY.put("心理健康教育", "教育类");
	  CC_ZY_CATEGORY.put("汉语", "语言类");
	  CC_ZY_CATEGORY.put("商务英语", "语言类");
	  CC_ZY_CATEGORY.put("应用英语", "语言类");
	  CC_ZY_CATEGORY.put("旅游英语", "语言类");
	  CC_ZY_CATEGORY.put("商务日语", "语言类");
	  CC_ZY_CATEGORY.put("应用日语", "语言类");
	  CC_ZY_CATEGORY.put("旅游日语", "语言类");
	  CC_ZY_CATEGORY.put("应用韩语", "语言类");
	  CC_ZY_CATEGORY.put("应用俄语", "语言类");
	  CC_ZY_CATEGORY.put("应用法语", "语言类");
	  CC_ZY_CATEGORY.put("应用德语", "语言类");
	  CC_ZY_CATEGORY.put("应用西班牙语", "语言类");
	  CC_ZY_CATEGORY.put("应用越南语", "语言类");
	  CC_ZY_CATEGORY.put("应用泰语", "语言类");
	  CC_ZY_CATEGORY.put("应用阿拉伯语", "语言类");
	  CC_ZY_CATEGORY.put("应用外语", "语言类");
	  CC_ZY_CATEGORY.put("文秘", "文秘类");
	  CC_ZY_CATEGORY.put("文秘速录", "文秘类");
	  CC_ZY_CATEGORY.put("运动训练", "体育类");
	  CC_ZY_CATEGORY.put("运动防护", "体育类");
	  CC_ZY_CATEGORY.put("社会体育", "体育类");
	  CC_ZY_CATEGORY.put("休闲体育", "体育类");
	  CC_ZY_CATEGORY.put("高尔夫球运动与管理", "体育类");
	  CC_ZY_CATEGORY.put("民族传统体育", "体育类");
	  CC_ZY_CATEGORY.put("体育艺术表演", "体育类");
	  CC_ZY_CATEGORY.put("体育运营与管理", "体育类");
	  CC_ZY_CATEGORY.put("体育保健与康复", "体育类");
	  CC_ZY_CATEGORY.put("健身指导与管理", "体育类");
	  CC_ZY_CATEGORY.put("电子竞技运动与管理", "体育类");
	  CC_ZY_CATEGORY.put("冰雪设施运维与管理", "体育类");
	  CC_ZY_CATEGORY.put("治安管理", "公安管理类");
	  CC_ZY_CATEGORY.put("交通管理", "公安管理类");
	  CC_ZY_CATEGORY.put("信息网络安全监察", "公安管理类");
	  CC_ZY_CATEGORY.put("防火管理", "公安管理类");
	  CC_ZY_CATEGORY.put("边防检查", "公安管理类");
	  CC_ZY_CATEGORY.put("边境管理", "公安管理类");
	  CC_ZY_CATEGORY.put("特警", "公安管理类");
	  CC_ZY_CATEGORY.put("警察管理", "公安管理类");
	  CC_ZY_CATEGORY.put("公共安全管理", "公安管理类");
	  CC_ZY_CATEGORY.put("森林消防", "公安管理类");
	  CC_ZY_CATEGORY.put("部队后勤管理", "公安管理类");
	  CC_ZY_CATEGORY.put("部队政治工作", "公安管理类");
	  CC_ZY_CATEGORY.put("警察指挥与战术", "公安指挥类");
	  CC_ZY_CATEGORY.put("边防指挥", "公安指挥类");
	  CC_ZY_CATEGORY.put("通信指挥", "公安指挥类");
	  CC_ZY_CATEGORY.put("消防指挥", "公安指挥类");
	  CC_ZY_CATEGORY.put("参谋业务", "公安指挥类");
	  CC_ZY_CATEGORY.put("抢险救援", "公安指挥类");
	  CC_ZY_CATEGORY.put("船艇指挥", "公安指挥类");
	  CC_ZY_CATEGORY.put("刑事科学技术", "公安技术类");
	  CC_ZY_CATEGORY.put("警犬技术", "公安技术类");
	  CC_ZY_CATEGORY.put("国内安全保卫", "侦查类");
	  CC_ZY_CATEGORY.put("经济犯罪侦查", "侦查类");
	  CC_ZY_CATEGORY.put("禁毒", "侦查类");
	  CC_ZY_CATEGORY.put("刑事侦查", "侦查类");
	  CC_ZY_CATEGORY.put("司法助理", "法律实务类");
	  CC_ZY_CATEGORY.put("法律文秘", "法律实务类");
	  CC_ZY_CATEGORY.put("法律事务", "法律实务类");
	  CC_ZY_CATEGORY.put("检察事务", "法律实务类");
	  CC_ZY_CATEGORY.put("刑事执行", "法律执行类");
	  CC_ZY_CATEGORY.put("民事执行", "法律执行类");
	  CC_ZY_CATEGORY.put("行政执行", "法律执行类");
	  CC_ZY_CATEGORY.put("司法警务", "法律执行类");
	  CC_ZY_CATEGORY.put("社区矫正", "法律执行类");
	  CC_ZY_CATEGORY.put("刑事侦查技术", "司法技术类");
	  CC_ZY_CATEGORY.put("安全防范技术", "司法技术类");
	  CC_ZY_CATEGORY.put("司法信息技术", "司法技术类");
	  CC_ZY_CATEGORY.put("司法鉴定技术", "司法技术类");
	  CC_ZY_CATEGORY.put("司法信息安全", "司法技术类");
	  CC_ZY_CATEGORY.put("罪犯心理测量与矫正技术", "司法技术类");
	  CC_ZY_CATEGORY.put("戒毒矫治技术", "司法技术类");
	  CC_ZY_CATEGORY.put("职务犯罪预防与控制", "司法技术类");
	  CC_ZY_CATEGORY.put("社会工作", "公共事业类");
	  CC_ZY_CATEGORY.put("社会福利事业管理", "公共事业类");
	  CC_ZY_CATEGORY.put("青少年工作与管理", "公共事业类");
	  CC_ZY_CATEGORY.put("社区管理与服务", "公共事业类");
	  CC_ZY_CATEGORY.put("公共关系", "公共事业类");
	  CC_ZY_CATEGORY.put("人民武装", "公共事业类");
	  CC_ZY_CATEGORY.put("民政管理", "公共管理类");
	  CC_ZY_CATEGORY.put("人力资源管理", "公共管理类");
	  CC_ZY_CATEGORY.put("劳动与社会保障", "公共管理类");
	  CC_ZY_CATEGORY.put("网络舆情监测", "公共管理类");
	  CC_ZY_CATEGORY.put("公共事务管理", "公共管理类");
	  CC_ZY_CATEGORY.put("行政管理", "公共管理类");
	  CC_ZY_CATEGORY.put("质量管理与认证", "公共管理类");
	  CC_ZY_CATEGORY.put("知识产权管理", "公共管理类");
	  CC_ZY_CATEGORY.put("公益慈善事业管理", "公共管理类");
	  CC_ZY_CATEGORY.put("老年服务与管理", "公共服务类");
	  CC_ZY_CATEGORY.put("家政服务与管理", "公共服务类");
	  CC_ZY_CATEGORY.put("婚庆服务与管理", "公共服务类");
	  CC_ZY_CATEGORY.put("社区康复", "公共服务类");
	  CC_ZY_CATEGORY.put("现代殡葬技术与管理", "公共服务类");
	  CC_ZY_CATEGORY.put("幼儿发展与健康管理", "公共服务类");
	  CC_ZY_CATEGORY.put("陵园服务与管理", "公共服务类");
	  CC_ZY_CATEGORY.put("哲学类", "哲学");
	  CC_ZY_CATEGORY.put("经济学类", "经济学");
	  CC_ZY_CATEGORY.put("财政学类", "经济学");
	  CC_ZY_CATEGORY.put("金融学类", "经济学");
	  CC_ZY_CATEGORY.put("经济与贸易类", "经济学");
	  CC_ZY_CATEGORY.put("法学类", "法学");
	  CC_ZY_CATEGORY.put("政治学类", "法学");
	  CC_ZY_CATEGORY.put("社会学类", "法学");
	  CC_ZY_CATEGORY.put("民族学类", "法学");
	  CC_ZY_CATEGORY.put("马克思主义理论类", "法学");
	  CC_ZY_CATEGORY.put("公安学类", "法学");
	  CC_ZY_CATEGORY.put("教育学类", "教育学");
	  CC_ZY_CATEGORY.put("体育学类", "教育学");
	  CC_ZY_CATEGORY.put("中国语言文学类", "文学");
	  CC_ZY_CATEGORY.put("外国语言文学类", "文学");
	  CC_ZY_CATEGORY.put("新闻传播学类", "文学");
	  CC_ZY_CATEGORY.put("历史学类", "历史学");
	  CC_ZY_CATEGORY.put("数学类", "理学");
	  CC_ZY_CATEGORY.put("物理学类", "理学");
	  CC_ZY_CATEGORY.put("化学类", "理学");
	  CC_ZY_CATEGORY.put("天文学类", "理学");
	  CC_ZY_CATEGORY.put("地理科学类", "理学");
	  CC_ZY_CATEGORY.put("大气科学类", "理学");
	  CC_ZY_CATEGORY.put("海洋科学类", "理学");
	  CC_ZY_CATEGORY.put("地球物理学类", "理学");
	  CC_ZY_CATEGORY.put("地质学类", "理学");
	  CC_ZY_CATEGORY.put("生物科学类", "理学");
	  CC_ZY_CATEGORY.put("心理学类", "理学");
	  CC_ZY_CATEGORY.put("统计学类", "理学");
	  CC_ZY_CATEGORY.put("力学类", "工学");
	  CC_ZY_CATEGORY.put("机械类", "工学");
	  CC_ZY_CATEGORY.put("仪器类", "工学");
	  CC_ZY_CATEGORY.put("材料类", "工学");
	  CC_ZY_CATEGORY.put("能源动力类", "工学");
	  CC_ZY_CATEGORY.put("电气类", "工学");
	  CC_ZY_CATEGORY.put("电子信息类", "工学");
	  CC_ZY_CATEGORY.put("自动化类", "工学");
	  CC_ZY_CATEGORY.put("计算机类", "工学");
	  CC_ZY_CATEGORY.put("土木类", "工学");
	  CC_ZY_CATEGORY.put("水利类", "工学");
	  CC_ZY_CATEGORY.put("测绘类", "工学");
	  CC_ZY_CATEGORY.put("化工与制药类", "工学");
	  CC_ZY_CATEGORY.put("地质类", "工学");
	  CC_ZY_CATEGORY.put("矿业类", "工学");
	  CC_ZY_CATEGORY.put("纺织类", "工学");
	  CC_ZY_CATEGORY.put("轻工类", "工学");
	  CC_ZY_CATEGORY.put("交通运输类", "工学");
	  CC_ZY_CATEGORY.put("海洋工程类", "工学");
	  CC_ZY_CATEGORY.put("航空航天类", "工学");
	  CC_ZY_CATEGORY.put("兵器类", "工学");
	  CC_ZY_CATEGORY.put("核工程类", "工学");
	  CC_ZY_CATEGORY.put("农业工程类", "工学");
	  CC_ZY_CATEGORY.put("林业工程类", "工学");
	  CC_ZY_CATEGORY.put("环境科学与工程类", "工学");
	  CC_ZY_CATEGORY.put("生物医学工程类", "工学");
	  CC_ZY_CATEGORY.put("食品科学与工程类", "工学");
	  CC_ZY_CATEGORY.put("建筑类", "工学");
	  CC_ZY_CATEGORY.put("安全科学与工程类", "工学");
	  CC_ZY_CATEGORY.put("生物工程类", "工学");
	  CC_ZY_CATEGORY.put("公安技术类", "工学");
	  CC_ZY_CATEGORY.put("植物生产类", "农学");
	  CC_ZY_CATEGORY.put("自然保护与环境生态类", "农学");
	  CC_ZY_CATEGORY.put("动物生产类", "农学");
	  CC_ZY_CATEGORY.put("动物医学类", "农学");
	  CC_ZY_CATEGORY.put("林学类", "农学");
	  CC_ZY_CATEGORY.put("水产类", "农学");
	  CC_ZY_CATEGORY.put("草学类", "农学");
	  CC_ZY_CATEGORY.put("基础医学类", "医学");
	  CC_ZY_CATEGORY.put("临床医学类", "医学");
	  CC_ZY_CATEGORY.put("口腔医学类", "医学");
	  CC_ZY_CATEGORY.put("公共卫生与预防医学类", "医学");
	  CC_ZY_CATEGORY.put("中医学类", "医学");
	  CC_ZY_CATEGORY.put("中西医结合类", "医学");
	  CC_ZY_CATEGORY.put("药学类", "医学");
	  CC_ZY_CATEGORY.put("中药学类", "医学");
	  CC_ZY_CATEGORY.put("法医学类", "医学");
	  CC_ZY_CATEGORY.put("医学技术类", "医学");
	  CC_ZY_CATEGORY.put("护理学类", "医学");
	  CC_ZY_CATEGORY.put("管理科学与工程类", "管理学");
	  CC_ZY_CATEGORY.put("工商管理类", "管理学");
	  CC_ZY_CATEGORY.put("农业经济管理类", "管理学");
	  CC_ZY_CATEGORY.put("公共管理类", "管理学");
	  CC_ZY_CATEGORY.put("图书情报与档案管理类", "管理学");
	  CC_ZY_CATEGORY.put("物流管理与工程类", "管理学");
	  CC_ZY_CATEGORY.put("工业工程类", "管理学");
	  CC_ZY_CATEGORY.put("电子商务类", "管理学");
	  CC_ZY_CATEGORY.put("旅游管理类", "管理学");
	  CC_ZY_CATEGORY.put("艺术学理论类", "艺术学");
	  CC_ZY_CATEGORY.put("音乐与舞蹈学类", "艺术学");
	  CC_ZY_CATEGORY.put("戏剧与影视学类", "艺术学");
	  CC_ZY_CATEGORY.put("美术学类", "艺术学");
	  CC_ZY_CATEGORY.put("设计学类", "艺术学");
	  CC_ZY_EXT.put("哲学", new MarjorBean("哲学类","冷门",2,"语/政","适合思想情感丰富，乐于了解自然、社会等知识的学生"));
	  CC_ZY_EXT.put("逻辑学", new MarjorBean("哲学类","冷门",0,"数/政","适合逻辑思想严谨、计算机哲学有兴趣的学生"));
	  CC_ZY_EXT.put("宗教学", new MarjorBean("哲学类","冷门",0,"语/政","适合思想情感丰富，对社会科学、逻辑学等知识感兴趣的学生"));
	  CC_ZY_EXT.put("伦理学", new MarjorBean("哲学类","冷门",0,"语/历","适合乐于研究社会科学、人文自然科学的学生"));
	  CC_ZY_EXT.put("经济学", new MarjorBean("经济学类","热门",2,"语/政","适合乐于了解社会科学，自然科学，熟悉政治等知识的学生"));
	  CC_ZY_EXT.put("经济统计学", new MarjorBean("经济学类","热门",3,"数/政","适合思维敏捷，善于探索的学生"));
	  CC_ZY_EXT.put("国民经济管理", new MarjorBean("经济学类","普通",1,"数/政/地","适合对数学逻辑、统计、自然地理有兴趣的学生"));
	  CC_ZY_EXT.put("资源与环境经济学", new MarjorBean("经济学类","冷门",1,"地/生/化","适合对地理、生物、自然资源感兴趣的学生"));
	  CC_ZY_EXT.put("商务经济学", new MarjorBean("经济学类","普通",1,"数/政/英","适合对市场经济、数理统计、逻辑感兴趣、善于沟通的学生"));
	  CC_ZY_EXT.put("能源经济", new MarjorBean("经济学类","普通",2,"地/政","适合热爱自然科学，乐于了解人文社会科学，积极参与环境保护的学生"));
	  CC_ZY_EXT.put("劳动经济学", new MarjorBean("经济学类","普通",1,"政","适合对劳动经济学感兴趣的学生"));
	  CC_ZY_EXT.put("经济工程", new MarjorBean("经济学类","普通",1,"数/政","适合热爱经济金融，对经济研究有兴趣的学生"));
	  CC_ZY_EXT.put("数字经济", new MarjorBean("经济学类","热门",5,"数/政","适合思维敏捷，善于探索的学生"));
	  CC_ZY_EXT.put("财政学", new MarjorBean("财政学类","热门",3,"数/政","适合对统计计算、社会经济政治有兴趣的学生"));
	  CC_ZY_EXT.put("税收学", new MarjorBean("财政学类","热门",3,"数/政","适合有较强的法律意识，对数据统计，人文社科有浓厚兴趣的学生"));
	  CC_ZY_EXT.put("国际税收", new MarjorBean("财政学类","热门",2,"数/政","新设，适合有较强的法律意识，对数据统计，人文社科有浓厚兴趣的学生"));
	  CC_ZY_EXT.put("金融学", new MarjorBean("金融学类","热门",4,"数/政","新设，适合对经济有浓厚兴趣、对数理统计，国际政治有爱好的学生"));
	  CC_ZY_EXT.put("金融工程", new MarjorBean("金融学类","热门",4,"数/政","新设，适合对统计计算、经济投资有兴趣的学生"));
	  CC_ZY_EXT.put("保险学", new MarjorBean("金融学类","热门",2,"数/政","新设，适合对数学逻辑、法律法规有兴趣的学生"));
	  CC_ZY_EXT.put("投资学", new MarjorBean("金融学类","热门",2,"数/政","新设，适合对经济金融有兴趣，善于数理统计的学生"));
	  CC_ZY_EXT.put("金融数学", new MarjorBean("金融学类","热门",4,"数/政","新设，适合对统计计算、社会经济政治有兴趣的学生"));
	  CC_ZY_EXT.put("信用管理", new MarjorBean("金融学类","热门",3,"数/政","新设，适合对数据统计，逻辑推理，社会经济投资有兴趣的学生"));
	  CC_ZY_EXT.put("经济与金融", new MarjorBean("金融学类","热门",1,"数/政/英","新设，适合对经济金融、人文社科有兴趣的学生"));
	  CC_ZY_EXT.put("精算学", new MarjorBean("金融学类","热门",4,"数","新设，适合逻辑思维严密、善于思考的学生"));
	  CC_ZY_EXT.put("互联网金融", new MarjorBean("金融学类","热门",3,"数/政","新设，适合对互联网金融感兴趣的学生"));
	  CC_ZY_EXT.put("金融科技", new MarjorBean("金融学类","热门",3,"数","新设，适合对经济金融研究、国内外金融发展的高新技术感兴趣的同学"));
	  CC_ZY_EXT.put("国际经济与贸易", new MarjorBean("经济与贸易类","普通",2,"政/外","新设，适合对经济有浓厚兴趣、善于商业交流合作的学生"));
	  CC_ZY_EXT.put("贸易经济", new MarjorBean("经济与贸易类","冷门",1,"数/政","新设，适合对经济有浓厚兴趣、对数理统计，国际政治有爱好的学生"));
	  CC_ZY_EXT.put("国际经济发展合作", new MarjorBean("经济与贸易类","普通",1,"政/外","新设，有较强的国际谈判与国际沟通能力，立足发展中国家和新兴经济体，服务中国经济发展"));
	  CC_ZY_EXT.put("法学", new MarjorBean("法学类","热门",3,"政/历","新设，适合政治、法学有兴趣，同时善于文字表达、社会交流沟通能力强的学生"));
	  CC_ZY_EXT.put("知识产权", new MarjorBean("法学类","热门",1,"政/外","新设，适合对法学、社会科学有兴趣的学生"));
	  CC_ZY_EXT.put("监狱学", new MarjorBean("法学类","热门",5,"政/外","新设，适合对法律、政治、人文社会科学有兴趣的学生"));
	  CC_ZY_EXT.put("信用风险管理与法律防控", new MarjorBean("法学类","热门",5,"政/外","新设，适合对法学、风险投资及管理有兴趣的学生"));
	  CC_ZY_EXT.put("国际经贸规则", new MarjorBean("法学类","普通",1,"政/外","新设，适合对国际经济交流及相关政策研究感兴趣的同学"));
	  CC_ZY_EXT.put("司法警察学", new MarjorBean("法学类","热门",5,"政","新设，适合对法律、政治、警察行为科学有兴趣的学生"));
	  CC_ZY_EXT.put("社区矫正", new MarjorBean("法学类","冷门",1,"语/政","新设，适合政治、法学有兴趣，同时善于文字表达、社会交流沟通能力强的学生"));
	  CC_ZY_EXT.put("纪检监察", new MarjorBean("法学类","热门",3,"语/政","新设，专注于培养纪检人才、研究纪检专业的大学学科"));
	  CC_ZY_EXT.put("政治学与行政学", new MarjorBean("政治学类","普通",1,"语/政","新设，适合政治、法学有兴趣，同时善于文字表达、社会交流沟通能力强的学生"));
	  CC_ZY_EXT.put("国际政治", new MarjorBean("政治学类","普通",1,"政/外","新设，适合政治、法学有兴趣，同时善于文字表达、社会交流沟通能力强、外语能力合格的学生"));
	  CC_ZY_EXT.put("外交学", new MarjorBean("政治学类","热门",3,"政/外","新设，适合政治、外交有兴趣，同时善于文字表达、社会交流沟通能力强、外语表达能力强的学生"));
	  CC_ZY_EXT.put("国际事务与国际关系", new MarjorBean("政治学类","热门",3,"政/历/外","新设，适合对政治、历史有兴趣，同时善于社会交流、沟通能力强的学生"));
	  CC_ZY_EXT.put("政治学、经济学与哲学", new MarjorBean("政治学类","普通",1,"语/政","新设，适合对政治、哲学、经济学感兴趣，逻辑思维活跃的学生"));
	  CC_ZY_EXT.put("国际组织与全球治理", new MarjorBean("政治学类","普通",0,"政/历/外","新设，适合对政治、历史有兴趣，同时善于社会交流、沟通能力强的学生"));
	  CC_ZY_EXT.put("社会学", new MarjorBean("社会学类","冷门",1,"政/历","新设，适合对社会学、人文社科有兴趣，同时社会交流沟通能力强的学生"));
	  CC_ZY_EXT.put("社会工作", new MarjorBean("社会学类","冷门",2,"语/政","新设，适合善于文字表达、社会交流沟通能力强的学生"));
	  CC_ZY_EXT.put("人类学", new MarjorBean("社会学类","冷门",0,"语/政","新设，适合对社会学、人类自然科学有兴趣的学生"));
	  CC_ZY_EXT.put("女性学", new MarjorBean("社会学类","冷门",0,"语/政","新设，适合政治、法学有兴趣，同时善于文字表达、社会交流沟通能力强的学生"));
	  CC_ZY_EXT.put("家政学", new MarjorBean("社会学类","冷门",0,"语","新设，适合对人类社会学、自然科学感兴趣、善于交流沟通的学生"));
	  CC_ZY_EXT.put("老年学", new MarjorBean("社会学类","冷门",0,"语/政","新设，研究人的个体老化和群体老化及由此而引起的社会的经济和自然的诸问题"));
	  CC_ZY_EXT.put("社会政策", new MarjorBean("社会学类","冷门",1,"语/政","新设，具有社会责任感和全球视野，运用社会政策及相关学科解决现实社会问题"));
	  CC_ZY_EXT.put("民族学", new MarjorBean("民族学类","冷门",2,"政/历","新设，适合对社会学、历史研究有兴趣，同时社会交流沟通能力强的学生"));
	  CC_ZY_EXT.put("科学社会主义", new MarjorBean("马克思主义理论类","热门",5,"政/历","新设，专业适合逻辑推理能力较强，喜欢哲学、人文社科的学生"));
	  CC_ZY_EXT.put("中国共产党历史", new MarjorBean("马克思主义理论类","热门",5,"语/政/历","新设，适合对社会学、历史研究有兴趣，同时社会交流沟通能力强的学生"));
	  CC_ZY_EXT.put("思想政治教育", new MarjorBean("马克思主义理论类","热门",5,"政","新设，适合对思想政治、人文社会科学有兴趣的学生"));
	  CC_ZY_EXT.put("马克思主义理论", new MarjorBean("马克思主义理论类","热门",5,"政","新设，适合对思想政治、人文社会科学有兴趣的学生"));
	  CC_ZY_EXT.put("治安学", new MarjorBean("公安学类","热门",5,"政","新设，适合熟悉法律知识，同时对治安管理有兴趣的学生"));
	  CC_ZY_EXT.put("侦査学", new MarjorBean("公安学类","热门",5,"政","新设，适合热爱侦查管理、熟悉法律法规的学生"));
	  CC_ZY_EXT.put("边防管理", new MarjorBean("公安学类","热门",4,"政/体","新设，适合逻对政治法律有兴趣，同时能适应边防工作的学生"));
	  CC_ZY_EXT.put("禁毒学", new MarjorBean("公安学类","热门",4,"政/历","新设，适合热爱法律，熟悉禁毒管理的学生"));
	  CC_ZY_EXT.put("警犬技术", new MarjorBean("公安学类","热门",4,"政/体","新设，适合身体素质过硬，热爱法律的学生"));
	  CC_ZY_EXT.put("经济犯罪侦査", new MarjorBean("公安学类","热门",5,"数/政","新设，适合对经济学感兴趣，热爱人文社会科学，熟悉经济法的学生"));
	  CC_ZY_EXT.put("边防指挥", new MarjorBean("公安学类","热门",4,"政","新设，适合有一定的管理能力，并能积极服从管理的学生"));
	  CC_ZY_EXT.put("消防指挥", new MarjorBean("公安学类","热门",5,"政/体","新设，适合身体素质过硬，能极力投身社会治安管理的学生"));
	  CC_ZY_EXT.put("警卫学", new MarjorBean("公安学类","热门",5,"政/体","新设，适合身体健康，积极服从管理，能吃苦的学生"));
	  CC_ZY_EXT.put("公安情报学", new MarjorBean("公安学类","热门",5,"数/政","新设，适合逻辑思维严谨、做事缜密，善于分析的学生"));
	  CC_ZY_EXT.put("犯罪学", new MarjorBean("公安学类","热门",5,"数/政","新设，适合逻辑推理能力较强，善于分析的学生"));
	  CC_ZY_EXT.put("公安管理学", new MarjorBean("公安学类","热门",5,"语/政","新设，适合熟悉法律法规、对公安管理有兴趣的学生"));
	  CC_ZY_EXT.put("涉外警务", new MarjorBean("公安学类","热门",5,"政/外","新设，适合身体素质过硬，对警务执法感兴趣的学生"));
	  CC_ZY_EXT.put("国内安全保卫", new MarjorBean("公安学类","热门",5,"政/体","新设，适合身体素质过硬、对安全防护、保卫有兴趣的学生"));
	  CC_ZY_EXT.put("警务指挥与战术", new MarjorBean("公安学类","热门",5,"政/体","新设，适合身体健康、喜爱警务，善于分析、有较强管理能力的学生"));
	  CC_ZY_EXT.put("技术侦查学", new MarjorBean("公安学类","热门",5,"政","新设，适合热爱技术侦查与管理、熟悉法律法规的学生"));
	  CC_ZY_EXT.put("海警执法", new MarjorBean("公安学类","热门",4,"政","新设，适合熟悉法律知识，同时对海上治安管理有兴趣的学生"));
	  CC_ZY_EXT.put("公安政治工作", new MarjorBean("公安学类","热门",5,"政","新设，适合熟悉法律法规、对公安政治工作有兴趣的学生"));
	  CC_ZY_EXT.put("移民管理", new MarjorBean("公安学类","热门",5,"政","新设，适合熟悉法律知识，同时对移民管理有兴趣的学生"));
	  CC_ZY_EXT.put("出入境管理", new MarjorBean("公安学类","热门",5,"政/外","新设，适合逻对政治法律有兴趣，同时能适应出入境工作的学生"));
	  CC_ZY_EXT.put("反恐警务", new MarjorBean("公安学类","热门",4,"政","新设，培养学生反恐怖情报搜集与分析、涉恐风险评估与防范、恐怖案件侦查与调查等方面的能力"));
	  CC_ZY_EXT.put("消防政治工作", new MarjorBean("公安学类","热门",5,"政","新设，消防救援队伍基层从事思想政治教育、组织建设和队伍管理的初级指挥人才"));
	  CC_ZY_EXT.put("铁路警务", new MarjorBean("公安学类","热门",4,"政/体","新设，铁路方面的警察专业"));
	  CC_ZY_EXT.put("教育学", new MarjorBean("教育学类","热门",4,"语/政","新设，适合对教育感兴趣的学生"));
	  CC_ZY_EXT.put("科学教育", new MarjorBean("教育学类","普通",3,"语/政","新设，适合对教育科研感兴趣的学生"));
	  CC_ZY_EXT.put("人文教育", new MarjorBean("教育学类","冷门",1,"语/政/历","新设，适合对历史文学研究感兴趣的学生"));
	  CC_ZY_EXT.put("教育技术学", new MarjorBean("教育学类","冷门",1,"政","新设，适合对教育科研设计感兴趣的学生"));
	  CC_ZY_EXT.put("艺术教育", new MarjorBean("教育学类","冷门",0,"音/美","新设，适合对艺术感兴趣的学生"));
	  CC_ZY_EXT.put("学前教育", new MarjorBean("教育学类","热门",3,"语/音/美","适合对幼儿教育研究感兴趣的学生"));
	  CC_ZY_EXT.put("小学教育", new MarjorBean("教育学类","普通",2,"语","适合对人文教育感兴趣的学生"));
	  CC_ZY_EXT.put("特殊教育", new MarjorBean("教育学类","普通",5,"语","耐心、细致，又有志于投身教育事业"));
	  CC_ZY_EXT.put("华文教育", new MarjorBean("教育学类","冷门",0,"语","适合对语言文化研究感兴趣的学生"));
	  CC_ZY_EXT.put("教育康复学", new MarjorBean("教育学类","冷门",0,"语","适合对教育康复学研究感兴趣的学生"));
	  CC_ZY_EXT.put("卫生教育", new MarjorBean("教育学类","冷门",0,"语/生","有教育资质、健康教育与宣传能力、良好职业素养的复合应用型人才"));
	  CC_ZY_EXT.put("认知科学与技术", new MarjorBean("教育学类","冷门",0,"语/数","适合对教育科学技术感兴趣的学生"));
	  CC_ZY_EXT.put("融合教育", new MarjorBean("教育学类","普通",2,"语/政","新设，普通学校、融合教育中心等各级各类教育机构教育教学、巡回指导、管理与研究等工作"));
	  CC_ZY_EXT.put("劳动教育", new MarjorBean("教育学类","普通",1,"语","新设，中小学、科研院所、教育管理部门或各类组织机构从事劳动教育相关工作的创新型人才"));
	  CC_ZY_EXT.put("体育教育", new MarjorBean("体育学类","普通",3,"体","适合对体育科学研究感兴趣的学生"));
	  CC_ZY_EXT.put("运动训练", new MarjorBean("体育学类","普通",1,"体","适合对体育运动感兴趣的学生"));
	  CC_ZY_EXT.put("社会体育指导与管理", new MarjorBean("体育学类","普通",0,"体","适合对体育活动与研究感兴趣的学生"));
	  CC_ZY_EXT.put("武术与民族传统体育", new MarjorBean("体育学类","普通",1,"体","适合对武术和传统体育研究感兴趣的学生"));
	  CC_ZY_EXT.put("运动人体科学", new MarjorBean("体育学类","普通",0,"体","适合对体育活动感兴趣的学生"));
	  CC_ZY_EXT.put("运动康复", new MarjorBean("体育学类","普通",1,"体/生","适合对健康教育，体育研究感兴趣的学生"));
	  CC_ZY_EXT.put("休闲体育", new MarjorBean("体育学类","普通",0,"体","合对体育研究感兴趣的学生"));
	  CC_ZY_EXT.put("体能训练", new MarjorBean("体育学类","普通",0,"体","适合对体育运动感兴趣的学生"));
	  CC_ZY_EXT.put("冰雪运动", new MarjorBean("体育学类","普通",1,"体","适合对体育运动感兴趣的学生"));
	  CC_ZY_EXT.put("电子竞技运动与管理", new MarjorBean("体育学类","热门",2,"语/数","承担电竞产业中的教育培训、赛事组织、企业管理、俱乐部管理等工作的专业复合型人才"));
	  CC_ZY_EXT.put("智能体育工程", new MarjorBean("体育学类","热门",2,"数/生","智能体育、体育大数据、互联网、计算机技术及其他电子技术等方面复合型人才"));
	  CC_ZY_EXT.put("体育旅游", new MarjorBean("体育学类","普通",0,"体/语","研究体育旅游行业管理、休闲运动项目技术指导、项目策划与营销、培训与咨询等"));
	  CC_ZY_EXT.put("运动能力开发", new MarjorBean("体育学类","普通",0,"体/语","综合运动能力进行测量、分析与调控的科学体系，具有明显的学科交叉融合特征"));
	  CC_ZY_EXT.put("汉语言文学", new MarjorBean("中国语言文学类","热门",5,"语","适合对文学研究感兴趣，文笔较好，思维活跃的学生"));
	  CC_ZY_EXT.put("汉语言", new MarjorBean("中国语言文学类","普通",2,"语","适合对语言文学研究感兴趣的学生"));
	  CC_ZY_EXT.put("汉语国际教育", new MarjorBean("中国语言文学类","普通",3,"语/外","适合对中西方语言研究感兴趣的学生"));
	  CC_ZY_EXT.put("中国少数民族语言文学", new MarjorBean("中国语言文学类","冷门",0,"语/政","适合对少数民族文学研究感兴趣的学生"));
	  CC_ZY_EXT.put("古典文献学", new MarjorBean("中国语言文学类","冷门",0,"语","适合对古文学研究感兴趣的学生"));
	  CC_ZY_EXT.put("应用语言学", new MarjorBean("中国语言文学类","冷门",0,"语","适合对语言研究感兴趣的学生"));
	  CC_ZY_EXT.put("秘书学", new MarjorBean("中国语言文学类","冷门",1,"语","适合对文学、秘书感兴趣的学生"));
	  CC_ZY_EXT.put("中国语言与文化", new MarjorBean("中国语言文学类","普通",1,"语/历","适合历史基础知识扎实，喜欢文化遗产研究的学生"));
	  CC_ZY_EXT.put("手语翻译", new MarjorBean("中国语言文学类","普通",2,"语/外","听障人士和健听人士之间实现有效沟通的桥梁"));
	  CC_ZY_EXT.put("桑戈语", new MarjorBean("外国语言文学类","普通",0,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("英语", new MarjorBean("外国语言文学类","热门",4,"英","适合对英语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("俄语", new MarjorBean("外国语言文学类","普通",2,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("徳语", new MarjorBean("外国语言文学类","普通",4,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("法语", new MarjorBean("外国语言文学类","普通",4,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("西班牙语", new MarjorBean("外国语言文学类","普通",4,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("阿拉伯语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("日语", new MarjorBean("外国语言文学类","普通",4,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("波斯语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("朝鲜语", new MarjorBean("外国语言文学类","普通",3,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("菲律宾语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("语言学", new MarjorBean("外国语言文学类","冷门",0,"外","注重语言、语言发展、语言和语言群组直接的关系"));
	  CC_ZY_EXT.put("塔玛齐格特语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("爪哇语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("旁遮普语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("梵语巴利语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("印度尼西亚语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("印地语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("柬埔寨语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("老挝语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("缅甸语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("马来语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("蒙古语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("僧伽罗语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("泰语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("乌尔都语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("希伯来语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("越南语", new MarjorBean("外国语言文学类","冷门",2,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("豪萨语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("斯瓦希里语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("阿尔巴尼亚语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("保加利亚语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("波兰语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("捷克语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("斯洛伐克语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("罗马尼亚语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("葡萄牙语", new MarjorBean("外国语言文学类","普通",4,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("瑞典语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("塞尔维亚语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("土耳其语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("希腊语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("匈牙利语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("意大利语", new MarjorBean("外国语言文学类","普通",3,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("泰米尔语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("普什图语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("世界语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("孟加拉语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("尼泊尔语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("克罗地亚语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("荷兰语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("芬兰语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("乌克兰语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("挪威语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("丹麦语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("冰岛语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("爱尔兰语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("拉脱维亚语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("立陶宛语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("斯洛文尼亚语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("爱沙尼亚语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("马耳他语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("哈萨克语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("乌兹别克语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("祖鲁语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("拉丁语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("翻译", new MarjorBean("外国语言文学类","普通",3,"语/外","适合对翻译研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("商务英语", new MarjorBean("外国语言文学类","普通",2,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("阿姆哈拉语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("吉尔吉斯语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("索马里语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("土库曼语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("加泰罗尼亚语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("约鲁巴语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("亚美尼亚语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("马达加斯加语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("格鲁吉亚语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("阿塞拜疆语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("阿非利卡语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("马其顿语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("塔吉克语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("茨瓦纳语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("恩徳贝莱语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("科摩罗语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("克里奥尔语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("绍纳语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("提格雷尼亚语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("白俄罗斯语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("毛利语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("汤加语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("萨摩亚语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("库尔德语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("比斯拉马语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("达里语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("德顿语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("迪维希语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("斐济语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("库克群岛毛利语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("隆迪语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("卢森堡语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("卢旺达语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("纽埃语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("皮金语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("切瓦语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("塞苏陀语", new MarjorBean("外国语言文学类","冷门",1,"外","适合对外语研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("新闻学", new MarjorBean("新闻传播学类","热门",3,"语","适合对新闻学研究感兴趣的学生"));
	  CC_ZY_EXT.put("广播电视学", new MarjorBean("新闻传播学类","普通",1,"语","适合对广播电视学研究感兴趣的学生"));
	  CC_ZY_EXT.put("广告学", new MarjorBean("新闻传播学类","普通",1,"语","适合对广告学研究感兴趣的学生"));
	  CC_ZY_EXT.put("传播学", new MarjorBean("新闻传播学类","普通",2,"语","适合对传播学研究感兴趣的学生"));
	  CC_ZY_EXT.put("编辑出版学", new MarjorBean("新闻传播学类","普通",1,"语","适合对编辑出版学研究感兴趣的学生"));
	  CC_ZY_EXT.put("网络与新媒体", new MarjorBean("新闻传播学类","热门",4,"语","适合对网络与新媒体研究感兴趣的学生"));
	  CC_ZY_EXT.put("数字出版", new MarjorBean("新闻传播学类","普通",1,"语","适合对数字出版研究感兴趣的学生"));
	  CC_ZY_EXT.put("时尚传播", new MarjorBean("新闻传播学类","普通",0,"语","适合对传播学研究感兴趣的学生"));
	  CC_ZY_EXT.put("国际新闻与传播", new MarjorBean("新闻传播学类","普通",0,"语/外","适合对新闻传播学研究感兴趣的学生"));
	  CC_ZY_EXT.put("会展", new MarjorBean("新闻传播学类（交叉专业）","普通",1,"语/外","适合对会展研究感兴趣的学生"));
	  CC_ZY_EXT.put("历史学", new MarjorBean("历史学类","普通",3,"历/政","适合喜欢历史研究、喜欢自然人文社科的学生"));
	  CC_ZY_EXT.put("世界史", new MarjorBean("历史学类","冷门",1,"政/历","适合热爱历史，喜欢人文社科与自然科学的学生"));
	  CC_ZY_EXT.put("考古学", new MarjorBean("历史学类","冷门",2,"政/历","适合对历史学有较强兴趣，且热爱考古的学生"));
	  CC_ZY_EXT.put("文物与博物馆学", new MarjorBean("历史学类","普通",3,"政/历","适合历史基础知识扎实，喜欢文化研究的学生"));
	  CC_ZY_EXT.put("文物保护技术", new MarjorBean("历史学类","普通",1,"政/历","适合政治、历史有兴趣，同时喜欢文物保护与研究的学生"));
	  CC_ZY_EXT.put("外国语言与外国历史", new MarjorBean("历史学类","冷门",0,"外/历","适合外语能力较强，同时热爱历史研究的学生"));
	  CC_ZY_EXT.put("文化遗产", new MarjorBean("历史学类","冷门",0,"政/历","适合历史基础知识扎实，喜欢文化遗产研究的学生"));
	  CC_ZY_EXT.put("古文字学", new MarjorBean("历史学类","冷门",0,"语/历","新设，较强的人文修养和语言文字表达能力、较为敏锐的问题意识和思辨能力"));
	  CC_ZY_EXT.put("科学史", new MarjorBean("历史学类","冷门",0,"政/历","新设，历史与科研联系起来而催生出来的一片新的领域"));
	  CC_ZY_EXT.put("数学与应用数学", new MarjorBean("数学类","热门",5,"数","适合逻辑思维严密、善于思考的学生"));
	  CC_ZY_EXT.put("信息与计算科学", new MarjorBean("数学类","热门",5,"数/计","适合逻辑推理能力强，喜欢计算机编程的学生"));
	  CC_ZY_EXT.put("数理基础科学", new MarjorBean("数学类","普通",3,"数","适合推理能力强，善于分析，热爱数学的学生"));
	  CC_ZY_EXT.put("数据计算及应用", new MarjorBean("数学类","普通",4,"数","适合逻辑推理能力强的学生"));
	  CC_ZY_EXT.put("物理学", new MarjorBean("物理学类","热门",4,"物/数","适合数学基础知识过硬、逻辑思维过硬、空间想象能力强的学生"));
	  CC_ZY_EXT.put("应用物理学", new MarjorBean("物理学类","普通",1,"物","适合热爱物理，喜欢实际应用的学生"));
	  CC_ZY_EXT.put("核物理", new MarjorBean("物理学类","普通",4,"物","适合对物理有较强的兴趣，同时对核物理有研究的学生"));
	  CC_ZY_EXT.put("声学", new MarjorBean("物理学类","普通",4,"物","适合喜欢自然科学，喜欢物理应用的学生"));
	  CC_ZY_EXT.put("系统科学与工程", new MarjorBean("物理学类","普通",1,"数/物","适合对逻辑推理有兴趣，喜爱系统工程的学生"));
	  CC_ZY_EXT.put("量子信息科学", new MarjorBean("物理学类","普通",1,"数/物","新设，量子力学与信息学交叉形成的一门边缘学科"));
	  CC_ZY_EXT.put("化学", new MarjorBean("化学类","普通",3,"化","适合化学基础知识扎实、喜欢化学研究的学生"));
	  CC_ZY_EXT.put("应用化学", new MarjorBean("化学类","冷门",0,"化","适合喜欢化学、乐于科学研究的学生"));
	  CC_ZY_EXT.put("化学生物学", new MarjorBean("化学类","冷门",0,"化/生","适合热爱生物化学，同时在实验研究有志向的学生"));
	  CC_ZY_EXT.put("分子科学与工程", new MarjorBean("化学类","冷门",0,"化/生","适合化学、生物学基础知识能力强，喜欢分子学的学生"));
	  CC_ZY_EXT.put("能源化学", new MarjorBean("化学类","冷门",3,"化","适合热爱能源化学工程，喜欢能源化学研究、生产的学生"));
	  CC_ZY_EXT.put("化学测量学与技术", new MarjorBean("化学类","冷门",1,"化/物","新设，化学、物理、精密仪器制造、计算机科学、软件等多学科融合的交叉专业"));
	  CC_ZY_EXT.put("天文学", new MarjorBean("天文学类","普通",3,"物","适合对自然科学感兴趣，热爱天文学的学生"));
	  CC_ZY_EXT.put("地理科学", new MarjorBean("地理科学类","普通",3,"地","适合对自然科学感兴趣，热爱地理科学的学生"));
	  CC_ZY_EXT.put("自然地理与资源环境", new MarjorBean("地理科学类","冷门",1,"地/物","适合对自然科学感兴趣，热爱资源环境的学生"));
	  CC_ZY_EXT.put("人文地理与城乡规划", new MarjorBean("地理科学类","普通",3,"地/物","适合对自然科学感兴趣，热爱土木工程、城乡规划的学生"));
	  CC_ZY_EXT.put("地理信息科学", new MarjorBean("地理科学类","普通",2,"地/计","适合对自然科学感兴趣，同时具有良好的计算机编程习惯的学生"));
	  CC_ZY_EXT.put("大气科学", new MarjorBean("大气科学类","普通",5,"物/地","适合对自然科学感兴趣，热爱大气学的学生"));
	  CC_ZY_EXT.put("应用气象学", new MarjorBean("大气科学类","普通",4,"物/地","适合对自然科学感兴趣，热爱气象学的学生"));
	  CC_ZY_EXT.put("气象技术与工程", new MarjorBean("大气科学类","冷门",1,"物","新设，适合对自然科学感兴趣，热爱气象学的学生"));
	  CC_ZY_EXT.put("海洋科学", new MarjorBean("海洋科学类","冷门",2,"物/地/化","适合对自然科学感兴趣，热爱海洋研究的学生"));
	  CC_ZY_EXT.put("海洋技术", new MarjorBean("海洋科学类","冷门",1,"物/地","适合对自然科学感兴趣，热爱海洋研究的学生"));
	  CC_ZY_EXT.put("海洋资源与环境", new MarjorBean("海洋科学类","冷门",1,"物/地","适合对自然科学感兴趣，热爱海洋学与环境学的学生"));
	  CC_ZY_EXT.put("军事海洋学", new MarjorBean("海洋科学类","冷门",5,"物/地","适合对自然科学感兴趣，热爱海洋学以及军事国防的学生"));
	  CC_ZY_EXT.put("地球物理学", new MarjorBean("地球物理学类","冷门",1,"物/地","适合对自然科学感兴趣，热爱地球研究的学生"));
	  CC_ZY_EXT.put("空间科学与技术", new MarjorBean("地球物理学类","普通",2,"物/地/化","适合对自然科学感兴趣，热爱空间技术探索研究的学生"));
	  CC_ZY_EXT.put("防灾减灾科学与工程", new MarjorBean("地球物理学类","普通",4,"物/地/化","研究气象灾害产生的机理和自然规律、衍生灾害的探测、预警和减灾"));
	  CC_ZY_EXT.put("行星科学", new MarjorBean("地球物理学类","冷门",1,"物/数","新设，深空探测为主要研究手段，是在地球科学、空间科学、天文学等学科交叉基础上产生的"));
	  CC_ZY_EXT.put("地质学", new MarjorBean("地质学类","普通",3,"地","适合对自然科学感兴趣，热爱地球研究的学生"));
	  CC_ZY_EXT.put("地球化学", new MarjorBean("地质学类","冷门",0,"化/地","适合对自然科学感兴趣，热爱地球研究与化学科学的学生"));
	  CC_ZY_EXT.put("地球信息科学与技术", new MarjorBean("地质学类","冷门",0,"物/地","适合对自然科学感兴趣，热爱地球信息研究的学生"));
	  CC_ZY_EXT.put("古生物学", new MarjorBean("地质学类","冷门",0,"生","适合对古生物科学感兴趣，热爱生物研究的学生"));
	  CC_ZY_EXT.put("生物科学", new MarjorBean("生物科学类","冷门",2,"生","适合对自然科学感兴趣，生物科学的学生"));
	  CC_ZY_EXT.put("生物技术", new MarjorBean("生物科学类","冷门",0,"数/生/化","具备较强的数理化基础，生物学及相关方向的基本知识和理论"));
	  CC_ZY_EXT.put("生物信息学", new MarjorBean("生物科学类","冷门",1,"生/计","适合对自然科学感兴趣，热爱生物信息研究的学生"));
	  CC_ZY_EXT.put("生态学", new MarjorBean("生物科学类","冷门",0,"生","适合对自然科学感兴趣，热爱生态研究的学生"));
	  CC_ZY_EXT.put("整合科学", new MarjorBean("生物科学类","冷门",0,"数/生/化","适合对自然科学感兴趣，热爱研究的学生"));
	  CC_ZY_EXT.put("神经科学", new MarjorBean("生物科学类","冷门",0,"生","适合对自然科学感兴趣，生物科学的学生"));
	  CC_ZY_EXT.put("心理学", new MarjorBean("心理学类","冷门",2,"生/化","适合对人文社科、心理学有兴趣的学生"));
	  CC_ZY_EXT.put("应用心理学", new MarjorBean("心理学类","冷门",0,"生/化","适合对人文社会科学有兴趣，同时热爱心理学的学生"));
	  CC_ZY_EXT.put("统计学", new MarjorBean("统计学类","热门",5,"数","适合对逻辑推理有兴趣，喜爱数理统计的学生"));
	  CC_ZY_EXT.put("应用统计学", new MarjorBean("统计学类","热门",5,"数","适合对逻辑推理有兴趣，热爱数学应用的学生"));
	  CC_ZY_EXT.put("理论与应用力学", new MarjorBean("力学类","普通",1,"数/物","适合对力学有兴趣、热爱理论研究的学生"));
	  CC_ZY_EXT.put("工程力学", new MarjorBean("力学类","普通",2,"数/物","适合善于分析，热爱工程力学的学生"));
	  CC_ZY_EXT.put("机械工程", new MarjorBean("机械类","普通",3,"物","适合对机械研究感兴趣的学生"));
	  CC_ZY_EXT.put("机械设计制造及其自动化", new MarjorBean("机械类","普通",2,"数/物","适合对机械研究、设计、自动化有兴趣的学生"));
	  CC_ZY_EXT.put("材料成型及控制工程", new MarjorBean("机械类","冷门",0,"数/物","适合对机械设计、操作有兴趣的学生"));
	  CC_ZY_EXT.put("机械电子工程", new MarjorBean("机械类","普通",1,"数/物","适合对机械有兴趣、善于电子技术的学生"));
	  CC_ZY_EXT.put("工业设计", new MarjorBean("机械类","冷门",0,"物","适合工业设计有兴趣，善于设计的学生"));
	  CC_ZY_EXT.put("过程装备与控制工程", new MarjorBean("机械类","普通",3,"物","适合对机械设计、组装研究的学生"));
	  CC_ZY_EXT.put("车辆工程", new MarjorBean("机械类","热门",4,"物","适合喜欢车辆设计、研发的学生"));
	  CC_ZY_EXT.put("汽车服务工程", new MarjorBean("机械类","普通",0,"物","适合具有较强动手能力及沟通能力的学生"));
	  CC_ZY_EXT.put("机械工艺技术", new MarjorBean("机械类","冷门",0,"物","适合对机械设计、操作感兴趣的学生"));
	  CC_ZY_EXT.put("微机电系统工程", new MarjorBean("机械类","普通",0,"物","适合对机电、现代精密技术有兴趣的学生"));
	  CC_ZY_EXT.put("机电技术教育", new MarjorBean("机械类","普通",2,"物","适合对力机械知识学习、机电教育感兴趣的学生"));
	  CC_ZY_EXT.put("汽车维修工程教育", new MarjorBean("机械类","冷门",2,"物","适合喜欢机械维修，对汽车基础知识有浓厚兴趣的学生"));
	  CC_ZY_EXT.put("智能制造工程", new MarjorBean("机械类","热门",4,"物","适合对机械研究、智能科技感兴趣的学生"));
	  CC_ZY_EXT.put("智能车辆工程", new MarjorBean("机械类","热门",4,"物","适合喜欢车辆设计、研发的学生"));
	  CC_ZY_EXT.put("仿生科学与工程", new MarjorBean("机械类","普通",2,"物","适合仿生科学与工程有兴趣，善于设计的学生"));
	  CC_ZY_EXT.put("新能源汽车工程", new MarjorBean("机械类","热门",5,"物","适合对新能源汽车研究感兴趣的学生"));
	  CC_ZY_EXT.put("增材制造工程", new MarjorBean("机械类","普通",1,"物","新设，智能制造装备系统（工业4.0）的开发、集成、设计与制造、管理与维护等的专门人才"));
	  CC_ZY_EXT.put("智能交互设计", new MarjorBean("机械类","热门",4,"数/物","新设，设计创意、人工智能和计算机技术有机融合"));
	  CC_ZY_EXT.put("应急装备技术与工程", new MarjorBean("机械类","普通",3,"物","新设，应急管理、安全生产、防灾减灾救灾等领域的应用型高级工程技术人才"));
	  CC_ZY_EXT.put("测控技术与仪器", new MarjorBean("仪器类","热门",2,"物","适合对精密仪器研究、设计有兴趣的学生"));
	  CC_ZY_EXT.put("精密仪器", new MarjorBean("仪器类","热门",3,"物","适合对精密仪器及光学研究、设计有兴趣的学生"));
	  CC_ZY_EXT.put("智能感知工程", new MarjorBean("仪器类","热门",2,"数/物","解决互联网、智能制造、智能交通、航空航天、国防等复杂工程问题"));
	  CC_ZY_EXT.put("材料科学与工程", new MarjorBean("材料类","冷门",0,"化","适合对材料研究感兴趣的学生"));
	  CC_ZY_EXT.put("材料物理", new MarjorBean("材料类","冷门",0,"数/物","适合对新型电子材料研究有兴趣的学生"));
	  CC_ZY_EXT.put("材料化学", new MarjorBean("材料类","冷门",0,"化","适合对化学材料研究感兴趣的学生"));
	  CC_ZY_EXT.put("冶金工程", new MarjorBean("材料类","冷门",0,"物/化","适合对金属研究、制造有兴趣的学生"));
	  CC_ZY_EXT.put("金属材料工程", new MarjorBean("材料类","冷门",0,"物","适合对金属材料研究、设计有兴趣的学生"));
	  CC_ZY_EXT.put("无机非金属材料工程", new MarjorBean("材料类","冷门",0,"化","合对无机非金属材料研究、使用有兴趣的学生"));
	  CC_ZY_EXT.put("高分子材料与工程", new MarjorBean("材料类","普通",0,"物/化","适合对材料研究有兴趣的学生"));
	  CC_ZY_EXT.put("复合材料与工程", new MarjorBean("材料类","冷门",0,"化/物","适合对复合材料研究有兴趣的学生"));
	  CC_ZY_EXT.put("粉体材料科学与工程", new MarjorBean("材料类","冷门",0,"物/化","适合对粉体材料学习、研究有兴趣的学生"));
	  CC_ZY_EXT.put("宝石及材料工艺学", new MarjorBean("材料类","普通",1,"化/物","适合对宝石材料设计、研究有兴趣的学生"));
	  CC_ZY_EXT.put("焊接技术与工程", new MarjorBean("材料类","热门",3,"物","适合对焊接技术研究有兴趣的学生"));
	  CC_ZY_EXT.put("功能材料", new MarjorBean("材料类","冷门",0,"化/物","适合对材料研究有兴趣的学生"));
	  CC_ZY_EXT.put("纳米材料与技术", new MarjorBean("材料类","冷门",0,"物","适合对纳米技术研究、设计有兴趣的学生"));
	  CC_ZY_EXT.put("新能源材料与器件", new MarjorBean("材料类","热门",1,"物/化","适合对新能源研究、设计有兴趣的学生"));
	  CC_ZY_EXT.put("材料设计科学与工程", new MarjorBean("材料类","冷门",0,"物","适合对材料设计技术研究有兴趣的学生"));
	  CC_ZY_EXT.put("复合材料成型工程", new MarjorBean("材料类","冷门",1,"物/化","适合对复合材料研究有兴趣的学生"));
	  CC_ZY_EXT.put("智能材料与结构", new MarjorBean("材料类","冷门",1,"物/化","运用所学知识解决与智能制造相关的复杂问题"));
	  CC_ZY_EXT.put("光电信息材料与器件", new MarjorBean("材料类","热门",1,"物/化","新设，将数学、自然科学、工程基础和专业知识用于解决光电信息材料与器件复杂工程问题"));
	  CC_ZY_EXT.put("能源与动力工程", new MarjorBean("能源动力类","热门",2,"物","适合对能源动力研究、测试、运用感兴趣的学生"));
	  CC_ZY_EXT.put("能源与环境系统工程", new MarjorBean("能源动力类","普通",0,"数/物/化","适合对能源与环境研究感兴趣的学生"));
	  CC_ZY_EXT.put("新能源科学与工程", new MarjorBean("能源动力类","热门",3,"物","适合对新能源研究、设计感兴趣的学生"));
	  CC_ZY_EXT.put("储能科学与工程", new MarjorBean("能源动力类","热门",5,"物/数/化","电能、热能、机械能、化学能等能量存储和转化相关的科学知识"));
	  CC_ZY_EXT.put("能源服务工程", new MarjorBean("能源动力类","冷门",1,"物/化","新设，各种传统和新能源技术、工程建设运维和服务管理"));
	  CC_ZY_EXT.put("氢能科学与工程", new MarjorBean("能源动力类","普通",2,"物/化","新设，氢能源工程的设计、运行管理、技术开发、科学技术等的跨学科、复合型专业"));
	  CC_ZY_EXT.put("可持续能源", new MarjorBean("能源动力类","普通",2,"物/化","新设，适合理工科较好的同学报考"));
	  CC_ZY_EXT.put("电气工程及其自动化", new MarjorBean("电气类","热门",5,"物","适合对电气工程研究、设计感兴趣的学生"));
	  CC_ZY_EXT.put("智能电网信息工程", new MarjorBean("电气类","热门",4,"物","适合对电网信息化、智能化研究、应用感兴趣的学生"));
	  CC_ZY_EXT.put("光源与照明", new MarjorBean("电气类","热门",4,"物","适合对光源与照明研究、测试、运用感兴趣，热爱电子、半导体技术的学生"));
	  CC_ZY_EXT.put("电气工程与智能控制", new MarjorBean("电气类","热门",5,"物","适合对电气工程智能化研究、运用感兴趣的学生"));
	  CC_ZY_EXT.put("电机电器智能化", new MarjorBean("电气类","热门",3,"物","适合对电气智能化研究、运用感兴趣的学生"));
	  CC_ZY_EXT.put("电缆工程", new MarjorBean("电气类","热门",4,"物/化","电线电缆和光纤光缆运行设计原理、电缆材料改性原理、光纤光缆工艺原理等"));
	  CC_ZY_EXT.put("能源互联网工程", new MarjorBean("电气类","热门",4,"物/数","新设，深刻理解电、热、冷、气等多种能源体系及其相互转换的基础原理"));
	  CC_ZY_EXT.put("智慧能源工程", new MarjorBean("电气类","热门",2,"物/数","新设，为“双碳”目标提供人才支撑"));
	  CC_ZY_EXT.put("电子信息工程", new MarjorBean("电子信息类","热门",4,"物","适合对电子信息技术及信息系统感兴趣的学生"));
	  CC_ZY_EXT.put("电子科学与技术", new MarjorBean("电子信息类","热门",3,"物/数","适合对电子科学技术研究、学习、应用感兴趣的学生"));
	  CC_ZY_EXT.put("通信工程", new MarjorBean("电子信息类","热门",5,"物","适合对通信应用感兴趣，善于分析与设计的学生"));
	  CC_ZY_EXT.put("微电子科学与工程", new MarjorBean("电子信息类","热门",4,"物","适合对微电子科学的研究、学习、应用感兴趣的学生"));
	  CC_ZY_EXT.put("光电信息科学与工程", new MarjorBean("电子信息类","热门",2,"物","适合对光电信息化研究、设计感兴趣的学生"));
	  CC_ZY_EXT.put("信息工程", new MarjorBean("电子信息类","热门",5,"物/数","适合对电子信息研究、设计感兴趣的学生"));
	  CC_ZY_EXT.put("广播电视工程", new MarjorBean("电子信息类","热门",2,"物","适合对多媒体技术研究、设计感兴趣的学生"));
	  CC_ZY_EXT.put("水声工程", new MarjorBean("电子信息类","热门",4,"物","适合对水声学研究、应用感兴趣，善于探索、发现的学生"));
	  CC_ZY_EXT.put("电子封装技术", new MarjorBean("电子信息类","热门",4,"物","适合对电子封装学习，运用感兴趣、具有较强的分析解决问题能力的学生"));
	  CC_ZY_EXT.put("集成电路设计与集成系统", new MarjorBean("电子信息类","热门",4,"物","适合对电子封装学习，运用感兴趣、具有较强的分析解决问题能力的学生"));
	  CC_ZY_EXT.put("医学信息工程", new MarjorBean("电子信息类","热门",2,"物","适合对医学信息工程研究、应用感兴趣，热爱医学信息的学生"));
	  CC_ZY_EXT.put("电磁场与无线技术", new MarjorBean("电子信息类","热门",2,"物","适合对电磁场及无线技术学习、研究、运用感兴趣的学生"));
	  CC_ZY_EXT.put("电波传播与天线", new MarjorBean("电子信息类","热门",3,"物","适合对电磁信号研究、设计感兴趣的学生"));
	  CC_ZY_EXT.put("电子信息科学与技术", new MarjorBean("电子信息类","热门",4,"物","适合对电子信息科学的研究、设计感兴趣的学生"));
	  CC_ZY_EXT.put("电信工程及管理", new MarjorBean("电子信息类","热门",2,"物","适合对电信工程研究、设计感兴趣善于管理，具有一定管理协调能力的学生"));
	  CC_ZY_EXT.put("应用电子技术教育", new MarjorBean("电子信息类","热门",1,"物","适合对电子技术教育的学习、传播感兴趣，乐于学习的学生"));
	  CC_ZY_EXT.put("人工智能", new MarjorBean("电子信息类","热门",5,"数/物","适合对电子信息技术及信息系统感兴趣的学生"));
	  CC_ZY_EXT.put("海洋信息工程", new MarjorBean("电子信息类","热门",1,"物/数","国防和通用信息技术及相关领域从事研究、设计、开发和管理等工作的高素质应用型人才"));
	  CC_ZY_EXT.put("柔性电子学", new MarjorBean("电子信息类","热门",2,"物/数/生","新设，交叉融合以化学、物理学、力学为核心"));
	  CC_ZY_EXT.put("智能测控工程", new MarjorBean("电子信息类","热门",2,"数/物","新设，以智能信息感知和处理为核心"));
	  CC_ZY_EXT.put("自动化", new MarjorBean("自动化类","热门",3,"物","适合对自动化技术感兴趣、热爱信号控制的学生"));
	  CC_ZY_EXT.put("轨道交通信号与控制", new MarjorBean("自动化类","热门",4,"物","适合对轨道信号研究感兴趣，乐于信号控制学习的学生"));
	  CC_ZY_EXT.put("机器人工程", new MarjorBean("自动化类","热门",4,"数/物","适合对机械工程及自动化感兴趣的学生"));
	  CC_ZY_EXT.put("邮政工程", new MarjorBean("自动化类","热门",3,"数","适合对邮政工程感兴趣的学生"));
	  CC_ZY_EXT.put("核电技术与控制工程", new MarjorBean("自动化类","热门",2,"物","核电仪表与控制相关技术知识和解决复杂实际工程问题的能力"));
	  CC_ZY_EXT.put("智能装备与系统", new MarjorBean("自动化类","热门",2,"物/数","工业机器人、机床和自动化生产线等智能装备的故障诊断、智能系统的开发等"));
	  CC_ZY_EXT.put("工业智能", new MarjorBean("自动化类","热门",0,"数/物","融合了当下人工智能与工业自动化最前沿研究理论与技术的典型多学科交叉专业"));
	  CC_ZY_EXT.put("智能工程与创意设计", new MarjorBean("自动化类","热门",2,"物/数","新设，智能创意设计相关领域智能工程设计、产品设计、创意设计、技术管理等工作"));
	  CC_ZY_EXT.put("计算机科学与技术", new MarjorBean("计算机类","热门",5,"数","适合对计算机学习、研发感兴趣，善于逻辑推理及分析的学生"));
	  CC_ZY_EXT.put("软件工程", new MarjorBean("计算机类","热门",5,"数","适合对软件学习、研究、开发感兴趣、热爱软件应用的学生"));
	  CC_ZY_EXT.put("网络工程", new MarjorBean("计算机类","热门",3,"数","适合对网络工程感兴趣、热爱通信基础的学生"));
	  CC_ZY_EXT.put("信息安全", new MarjorBean("计算机类","热门",5,"数","适合数学功底好、动手实践能力强的学生"));
	  CC_ZY_EXT.put("物联网工程", new MarjorBean("计算机类","热门",5,"数/物","适合对物联网理论及应用感兴趣，热衷互联网技术的学生"));
	  CC_ZY_EXT.put("数字媒体技术", new MarjorBean("计算机类","热门",5,"数","适合对数字媒体的研究，开发，运用感兴趣，逻辑思维能力较强的学生"));
	  CC_ZY_EXT.put("智能科学与技术", new MarjorBean("计算机类","热门",4,"数","适合对计算机技术感兴趣、热爱智能科学学习研究的学生"));
	  CC_ZY_EXT.put("空间信息与数字技术", new MarjorBean("计算机类","热门",3,"物/数","适合对空间信息化感兴趣、热爱空间信息数字化技术的学生"));
	  CC_ZY_EXT.put("电子与计算机工程", new MarjorBean("计算机类","热门",3,"物/数","适合对电子及计算机工程感兴趣、热爱电力工程及计算机技术的学生"));
	  CC_ZY_EXT.put("数据科学与大数据技术", new MarjorBean("计算机类","热门",5,"数","根据数据推演、分析、提出解决方案"));
	  CC_ZY_EXT.put("网络空间安全", new MarjorBean("计算机类","热门",5,"数","适合对网络空间安全感兴趣的学生"));
	  CC_ZY_EXT.put("新媒体技术", new MarjorBean("计算机类","热门",3,"数","适合对新媒体感兴趣的学生"));
	  CC_ZY_EXT.put("电影制作", new MarjorBean("计算机类","热门",1,"数","适合对电影制作的研究，开发，运用感兴趣的学生"));
	  CC_ZY_EXT.put("保密技术", new MarjorBean("计算机类","热门",2,"数","适合对计算机安全及信息安全感兴趣的学生"));
	  CC_ZY_EXT.put("服务科学与工程", new MarjorBean("计算机类","热门",1,"数","适合对计算机感兴趣的学生"));
	  CC_ZY_EXT.put("虚拟现实技术", new MarjorBean("计算机类","热门",4,"数","面向虚拟现实、增强现实、动漫游戏、网络传媒、软件开发等高新技术行业"));
	  CC_ZY_EXT.put("区块链工程", new MarjorBean("计算机类","热门",4,"数","区块链项目系统设计开发、区块链项目管理、区块链系统服务等"));
	  CC_ZY_EXT.put("密码科学与技术", new MarjorBean("计算机类","热门",3,"数","新设，应用深厚的密码科学技术和工程实践技能，解决密码科学研究和复杂工程问题"));
	  CC_ZY_EXT.put("土木工程", new MarjorBean("土木类","热门",3,"物","适合对土木工程感兴趣，热爱市政建设的学生"));
	  CC_ZY_EXT.put("建筑环境与能源应用工程", new MarjorBean("土木类","冷门",1,"物","适合对建筑能源感兴趣，热爱建筑节能应用的学生"));
	  CC_ZY_EXT.put("给排水科学与工程", new MarjorBean("土木类","普通",1,"物","适合对给排水科学感兴趣，热爱市政建设、规划的学生"));
	  CC_ZY_EXT.put("建筑电气与智能化", new MarjorBean("土木类","普通",2,"物","适合对建筑和电气学习研究感兴趣，热爱建筑智能化的学生"));
	  CC_ZY_EXT.put("城市地下空间工程", new MarjorBean("土木类","普通",2,"物","适合对城市地下研究，规划感兴趣，热爱市政规划的学生"));
	  CC_ZY_EXT.put("道路桥梁与渡河工程", new MarjorBean("土木类","热门",4,"物","适合对道路桥梁及渡河工程感兴趣，热爱道路桥梁规划建设的学生"));
	  CC_ZY_EXT.put("铁道工程", new MarjorBean("土木类","热门",3,"物","适合对铁道工程感兴趣，热爱市政建设的学生"));
	  CC_ZY_EXT.put("智能建造", new MarjorBean("土木类","热门",1,"物/数","应用相关计算机开发语言和工程建造的一般机械和控制工程原理"));
	  CC_ZY_EXT.put("土木、水利与海洋工程", new MarjorBean("土木类","冷门",0,"物/生","城市与基础设施建设、水资源与水安全保障、海洋能源资源开发等重大国民经济领域"));
	  CC_ZY_EXT.put("土木、水利与交通工程", new MarjorBean("土木类","冷门",0,"物","考虑全球化、气候变化和环境可持续性等人类社会发展与变化的新问题"));
	  CC_ZY_EXT.put("城市水系统工程", new MarjorBean("土木类","冷门",0,"物/生","新设，通过现代化手段解决水、大气、固体废物、土壤等复杂环境工程问题"));
	  CC_ZY_EXT.put("智能建造与智慧交通", new MarjorBean("土木类","热门",2,"物/数","新设，热爱土木交通行业，智能+交通强"));
	  CC_ZY_EXT.put("水利水电工程", new MarjorBean("水利类","普通",3,"物","适合对水利水电感兴趣，热爱工程设计及施工管理的学生"));
	  CC_ZY_EXT.put("水文与水资源工程", new MarjorBean("水利类","冷门",1,"物","适合对水资源感兴趣，热爱水文水资源信息处理，设计的学生"));
	  CC_ZY_EXT.put("港口航道与海岸工程", new MarjorBean("水利类","普通",1,"物","适合对港口航道海岸感兴趣，热爱其设计、施工及管理的学生"));
	  CC_ZY_EXT.put("水务工程", new MarjorBean("水利类","冷门",1,"物","适合对水务工程感兴趣，热爱其设计及技术研究的学生"));
	  CC_ZY_EXT.put("水利科学与工程", new MarjorBean("水利类","冷门",1,"物","适合对水利科学与工程感兴趣，热爱水利建设的学生"));
	  CC_ZY_EXT.put("智慧水利", new MarjorBean("水利类","普通",2,"物/数","新设，水利工程、计算机科学与技术、信息与通信工程等多学科交叉融合"));
	  CC_ZY_EXT.put("测绘工程", new MarjorBean("测绘类","普通",2,"物/地","适合热爱测绘学，善于摄影及定位分析的学生"));
	  CC_ZY_EXT.put("遥感科学与技术", new MarjorBean("测绘类","普通",3,"物/地","适合对遥感科学有兴趣，喜欢遥感技术的学生"));
	  CC_ZY_EXT.put("导航工程", new MarjorBean("测绘类","热门",4,"物/地","适合对导航工程感兴趣，乐于研究导航技术、设备的学生"));
	  CC_ZY_EXT.put("地理国情监测", new MarjorBean("测绘类","冷门",0,"地","适合对地理国情监测感兴趣，喜欢地质测量与分析的学生"));
	  CC_ZY_EXT.put("地理空间信息工程", new MarjorBean("测绘类","冷门",1,"地/数","适合对地理空间研究感兴趣的学生"));
	  CC_ZY_EXT.put("化学工程与工艺", new MarjorBean("化工与制药类","冷门",1,"化","适合热爱化学工程，喜欢化学工艺研究、设计的学生"));
	  CC_ZY_EXT.put("制药工程", new MarjorBean("化工与制药类","冷门",1,"化","适合热爱制药工程，同时具有较好的化学、生物功底的学生"));
	  CC_ZY_EXT.put("资源循环科学与工程", new MarjorBean("化工与制药类","冷门",0,"化","适合热爱资源循环科学，喜欢对循环经济分析的学生"));
	  CC_ZY_EXT.put("能源化学工程", new MarjorBean("化工与制药类","冷门",0,"化","适合热爱能源化学工程，喜欢能源化学研究、生产的学生"));
	  CC_ZY_EXT.put("化学工程与工业生物工程", new MarjorBean("化工与制药类","冷门",0,"化","适合热爱化学工程，喜欢化学工艺研究、设计的学生"));
	  CC_ZY_EXT.put("化工安全工程", new MarjorBean("化工与制药类","冷门",0,"化","适合热爱化学安全工程的学生"));
	  CC_ZY_EXT.put("涂料工程", new MarjorBean("化工与制药类","冷门",0,"化","以先进功能材料为专业背景，以涂料工程为特色"));
	  CC_ZY_EXT.put("精细化工", new MarjorBean("化工与制药类","冷门",0,"化","适合热爱化学工程，喜欢化学工艺研究、设计的学生"));
	  CC_ZY_EXT.put("地质工程", new MarjorBean("地质类","冷门",2,"物/地","适合热爱地质工程，喜欢地质研究、分析的学生"));
	  CC_ZY_EXT.put("勘查技术与工程", new MarjorBean("地质类","冷门",1,"物/地","适合热爱勘查技术，喜欢地质研究、勘查与分析的学生"));
	  CC_ZY_EXT.put("资源勘查工程", new MarjorBean("地质类","冷门",0,"物/地","适合热爱资源勘查工程，能够适应室外勘查工作的学生"));
	  CC_ZY_EXT.put("地下水科学与工程", new MarjorBean("地质类","冷门",0,"物","适合对地下水科学的研究、学习、应用感兴趣，热爱工程勘查设计的学生"));
	  CC_ZY_EXT.put("旅游地学与规划工程", new MarjorBean("地质类","冷门",0,"地","旅游地学、旅游规划与策划、国家公园保护与开发、旅游管理相关教学和科学研究等"));
	  CC_ZY_EXT.put("智能地球探测", new MarjorBean("地质类","冷门",0,"地/数","新设，地球探测与人工智能、大数据、物联网、无人机和5G通讯技术等领域交叉融合"));
	  CC_ZY_EXT.put("资源环境大数据工程", new MarjorBean("地质类","冷门",0,"地/生/数","新设，大数据、人工智能与资源环境深度融合"));
	  CC_ZY_EXT.put("釆矿工程", new MarjorBean("矿业类","冷门",1,"地","适合热爱矿物开采、热衷采矿工艺技安全管理的学生"));
	  CC_ZY_EXT.put("石油工程", new MarjorBean("矿业类","冷门",1,"化","适合热爱石油工程、热衷油气田开采、设计、管理的学生"));
	  CC_ZY_EXT.put("矿物加工工程", new MarjorBean("矿业类","冷门",0,"物","适合对矿物帅选、加工及矿产资源设计、应用感兴趣的学生"));
	  CC_ZY_EXT.put("油气储运工程", new MarjorBean("矿业类","冷门",0,"化","适合热爱油气储运工艺、设备设施的研究学习应用的学生"));
	  CC_ZY_EXT.put("矿物资源工程", new MarjorBean("矿业类","冷门",0,"地","适合热对矿物资源的开发、加工、利用感兴趣的学生"));
	  CC_ZY_EXT.put("海洋油气工程", new MarjorBean("矿业类","冷门",0,"物","适合热爱海洋油气工程，乐于学习研究油气储运、质检的学生"));
	  CC_ZY_EXT.put("智能采矿工程", new MarjorBean("矿业类","冷门",2,"地/数","新设，采矿工程与软件工程、信息工程、人工智能等采矿+智能融合"));
	  CC_ZY_EXT.put("碳储科学与工程", new MarjorBean("矿业类","冷门",3,"物/化/地","新设，包括物理、化学、数学等，也覆盖了化工、石油、地质、环境等应用学科"));
	  CC_ZY_EXT.put("纺织工程", new MarjorBean("纺织类","冷门",1,"化","适合热爱纺织贸易与服装，喜欢纺织设计及生产管理的学生"));
	  CC_ZY_EXT.put("服装设计与工程", new MarjorBean("纺织类","冷门",1,"化","适合热爱纺服装设计，想在服装设计、制版、工艺、管理营销方面全面进修或提高的学生"));
	  CC_ZY_EXT.put("非织造材料与工程", new MarjorBean("纺织类","冷门",0,"化","适合纺织材料科学，喜欢新材料研究、设计的学生"));
	  CC_ZY_EXT.put("服装设计与工艺教育", new MarjorBean("纺织类","冷门",0,"化","适合热爱纺织贸易与纺织设计及生产管理的学生"));
	  CC_ZY_EXT.put("丝绸设计与工程", new MarjorBean("纺织类","冷门",0,"化","适合热爱纺丝绸设计，想在丝绸设计、制版、工艺、管理营销方面全面进修或提高的学生"));
	  CC_ZY_EXT.put("轻化工程", new MarjorBean("轻工类","冷门",0,"化","适合乐于从事轻纺化工领域，善于生产设计及研究的学生"));
	  CC_ZY_EXT.put("包装工程", new MarjorBean("轻工类","冷门",2,"化/物","适合乐于从事轻包装的设计、应用的学生"));
	  CC_ZY_EXT.put("印刷工程", new MarjorBean("轻工类","冷门",1,"化","适合乐于从事印刷工作，善于设计、科学研究的学生"));
	  CC_ZY_EXT.put("香料香精技术与工程", new MarjorBean("轻工类","热门",4,"化","适合乐于从事香料香精技术与工程领域，善于生产设计及研究的学生"));
	  CC_ZY_EXT.put("化妆品技术与工程", new MarjorBean("轻工类","热门",4,"化/生","化妆品配方设计、功效评价、质量检验、生产技术以及化妆品的应用与营销等"));
	  CC_ZY_EXT.put("生物质能源与材料", new MarjorBean("轻工类","冷门",0,"化/生","新设，生物质能源与材料开发利用及其相关交叉学科领域"));
	  CC_ZY_EXT.put("交通运输", new MarjorBean("交通运输类","热门",4,"物","适合乐于从事交通运输及管理、车辆设计制造的学生"));
	  CC_ZY_EXT.put("交通工程", new MarjorBean("交通运输类","热门",4,"物","适合热爱交通工程、善于制图、测量的学生"));
	  CC_ZY_EXT.put("航海技术", new MarjorBean("交通运输类","普通",4,"物","适合乐于从事船舶驾驶、运输管理的学生"));
	  CC_ZY_EXT.put("轮机工程", new MarjorBean("交通运输类","普通",4,"物","适合热爱轮机工程，乐于在电机、电力设计、维修行业学习的学生"));
	  CC_ZY_EXT.put("飞行技术", new MarjorBean("交通运输类","热门",5,"物","适合对飞行技术感兴趣，乐于飞行技术的学习传授的学生"));
	  CC_ZY_EXT.put("交通设备与控制工程", new MarjorBean("交通运输类","普通",2,"物","适合乐于从事交通交通运输设备设计制造维修的学生"));
	  CC_ZY_EXT.put("救助与打捞工程", new MarjorBean("交通运输类","普通",3,"化/物","适招收思想政治素质好，热爱救助与打捞事业，纪律性强，身体健康，吃苦耐劳，学习英语的男生"));
	  CC_ZY_EXT.put("船舶电子电气工程", new MarjorBean("交通运输类","普通",4,"物","适合热爱船舶电子工程，乐于从事船舶自动化的学生"));
	  CC_ZY_EXT.put("轨道交通电气与控制", new MarjorBean("交通运输类","热门",4,"物","以交通运输工程、电气工程、控制科学与工程为主要学科支撑的应用型新工科专业"));
	  CC_ZY_EXT.put("邮轮工程与管理", new MarjorBean("交通运输类","普通",0,"物/语","邮轮工程及管理工作，相关设备的维护与管理"));
	  CC_ZY_EXT.put("智慧交通", new MarjorBean("交通运输类","热门",4,"物/数","新设，道路交通大数据、道路交通智慧信控、轨道交通智慧运营与控制等方面的专业知识"));
	  CC_ZY_EXT.put("智能运输工程", new MarjorBean("交通运输类","普通",4,"物/数","新设，ETC设备、交通信息采集设备、车载GPS等交通监控设备、信号机等设备的设计、维护等工作"));
	  CC_ZY_EXT.put("船舶与海洋工程", new MarjorBean("海洋工程类","冷门",2,"物/数","适合热爱船舶与海洋工程，善于分析研究的学生"));
	  CC_ZY_EXT.put("海洋工程与技术", new MarjorBean("海洋工程类","冷门",0,"物/数","适合热爱海洋科学，乐于从事海洋高新技术研究、开发、运用的学生"));
	  CC_ZY_EXT.put("海洋资源开发技术", new MarjorBean("海洋工程类","冷门",0,"化/数","适合热爱海洋资源研究、分析、应用的学生"));
	  CC_ZY_EXT.put("海洋机器人", new MarjorBean("海洋工程类","热门",2,"物/数","适合热爱海洋科学，乐于从事海洋高新技术研究、开发、运用的学生"));
	  CC_ZY_EXT.put("智慧海洋技术", new MarjorBean("海洋工程类","热门",1,"物/数","新设，人工智能、大数据等新一代信息技术与海洋环境、海洋装备、人类活动等深度融合"));
	  CC_ZY_EXT.put("航空航天工程", new MarjorBean("航空航天类","热门",4,"物/数","适合热爱航空航天工程，乐于工艺及设计、实验的学生"));
	  CC_ZY_EXT.put("飞行器设计与工程", new MarjorBean("航空航天类","热门",3,"物/数","适合热爱航空航天工程，乐于飞行器设计与研发、维修的学生"));
	  CC_ZY_EXT.put("飞行器制造工程", new MarjorBean("航空航天类","热门",3,"物/数","适合热爱航空航天工程，乐于飞行器制造、研究、开发与管理的学生"));
	  CC_ZY_EXT.put("飞行器动力工程", new MarjorBean("航空航天类","热门",3,"物/数","适合热爱航空航天工程，乐于在飞行器动力装置学习研究的学生"));
	  CC_ZY_EXT.put("飞行器环境与生命保障工程", new MarjorBean("航空航天类","热门",2,"物/数","适合热爱航空航天工程，乐于航天环境安全设计及研究能力的学生"));
	  CC_ZY_EXT.put("飞行器质量与可靠性", new MarjorBean("航空航天类","热门",3,"物/数","适合热爱航空航天工程，乐于从事飞行器质量检测事业的学生"));
	  CC_ZY_EXT.put("飞行器适航技术", new MarjorBean("航空航天类","热门",3,"物/数","适合热爱航空航天工程，乐于从事飞行器适航技术学习，研究的学生"));
	  CC_ZY_EXT.put("飞行器控制与信息工程", new MarjorBean("航空航天类","热门",3,"物/数","适合热爱航空航天，乐于飞行器控制的学生"));
	  CC_ZY_EXT.put("无人驾驶航空器系统工程", new MarjorBean("航空航天类","热门",4,"物/数","掌握无人机的系统结构和工作原理、系统设计和研发、系统操控与组装调试等"));
	  CC_ZY_EXT.put("智能飞行器技术", new MarjorBean("航空航天类","热门",4,"物/数","新设，适合理工科较好的同学报考"));
	  CC_ZY_EXT.put("空天智能电推进技术", new MarjorBean("航空航天类","热门",2,"物/数","新设，力学、机械、能源、电学、信息学交叉融合，解决复杂电推进系统工程问题"));
	  CC_ZY_EXT.put("武器系统与工程", new MarjorBean("兵器类","热门",2,"物/数","适合热爱武器系统、善于学习机械工程及自动化的学生"));
	  CC_ZY_EXT.put("武器发射工程", new MarjorBean("兵器类","热门",2,"物/数","适合热爱武器系统、对武器发射系统感兴趣的学生"));
	  CC_ZY_EXT.put("探测制导与控制技术", new MarjorBean("兵器类","热门",2,"物/数","适合热爱武器系统、对探测制导及控制技术感兴趣的学生"));
	  CC_ZY_EXT.put("弹药工程与爆炸技术", new MarjorBean("兵器类","热门",2,"物/数","适合热爱武器系统、对弹药及爆炸技术感兴趣的学生"));
	  CC_ZY_EXT.put("特种能源技术与工程", new MarjorBean("兵器类","热门",2,"物/数","适合热爱武器系统、对特种能源技术感兴趣的学生"));
	  CC_ZY_EXT.put("装甲车辆工程", new MarjorBean("兵器类","热门",2,"物/数","适合热爱武器系统、对装甲车辆的系统设计、开发感兴趣的学生"));
	  CC_ZY_EXT.put("信息对抗技术", new MarjorBean("兵器类","热门",3,"物/数","适合热爱武器系统、对信息对抗技术感兴趣的学生"));
	  CC_ZY_EXT.put("智能无人系统技术", new MarjorBean("兵器类","热门",3,"物/数","集人工智能、控制、计算机、电子、机械、网络信息传输等众多学科与前沿技术于一体"));
	  CC_ZY_EXT.put("核工程与核技术", new MarjorBean("核工程类","普通",4,"物/数","适合热爱工程物理，对核工程及核技术的研究感兴趣的学生"));
	  CC_ZY_EXT.put("辐射防护与核安全", new MarjorBean("核工程类","冷门",4,"物/数","适合热爱工程物理，对辐射防护与环境工程的研究感兴趣的学生"));
	  CC_ZY_EXT.put("工程物理", new MarjorBean("核工程类","冷门",4,"物/数","适合热爱工程物理，对核物理的研究感兴趣的学生"));
	  CC_ZY_EXT.put("核化工与核燃料工程", new MarjorBean("核工程类","冷门",4,"物/数","适合热爱工程物理，对核物理的研究感兴趣的学生"));
	  CC_ZY_EXT.put("农业工程", new MarjorBean("农业工程类","冷门",2,"物","适合热爱土木工程，对水利工程的设计、规划感兴趣的学生"));
	  CC_ZY_EXT.put("农业机械化及其自动化", new MarjorBean("农业工程类","冷门",2,"物","适合热爱土木工程，对农业机械化及自动化的设计、试验、生产感兴趣的学生"));
	  CC_ZY_EXT.put("农业电气化", new MarjorBean("农业工程类","冷门",2,"物","适合热爱农业工程，对电力电子及控制的设计、规划感兴趣的学生"));
	  CC_ZY_EXT.put("农业建筑环境与能源工程", new MarjorBean("农业工程类","冷门",1,"物","适合热爱农业工程，对农业设施、新能源开发的设计、规划感兴趣的学生"));
	  CC_ZY_EXT.put("农业水利工程", new MarjorBean("农业工程类","冷门",1,"物","适合热爱土木工程，对水利工程的设计、规划感兴趣的学生"));
	  CC_ZY_EXT.put("土地整治工程", new MarjorBean("农业工程类","冷门",1,"物","适合对土地整治技术研究感兴趣的学生"));
	  CC_ZY_EXT.put("农业智能装备工程", new MarjorBean("农业工程类","冷门",2,"物","善于运用创新的工程思维在相关的农业智能装备工程领域中发现和解决问题"));
	  CC_ZY_EXT.put("森林工程", new MarjorBean("林业工程类","冷门",1,"物","适合热爱森林工程，对森林资源管理、开发感兴趣的学生"));
	  CC_ZY_EXT.put("木材科学与工程", new MarjorBean("林业工程类","冷门",1,"物","适合热爱森林工程，对木材生产、加工、应用感兴趣的学生"));
	  CC_ZY_EXT.put("林产化工", new MarjorBean("林业工程类","冷门",1,"物","适合热爱林产化工，对树木及林产品的生产、设计感兴趣的学生"));
	  CC_ZY_EXT.put("家具设计与工程", new MarjorBean("林业工程类","冷门",1,"数","家具设计开发、家具制造工艺技术、生产管理与质量控制、室内设计与装修等"));
	  CC_ZY_EXT.put("木结构建筑与材料", new MarjorBean("林业工程类","冷门",1,"化","新设，绿色、低碳、装配式建筑，具有节能、环保和抗震等突出优势"));
	  CC_ZY_EXT.put("环境科学与工程", new MarjorBean("环境科学与工程类","冷门",0,"物/化","适合热爱环境科学工程，对污染治理、环境安全规划、设计感兴趣的学生"));
	  CC_ZY_EXT.put("环境工程", new MarjorBean("环境科学与工程类","冷门",0,"物/化","适合热爱环境工程，对污染治理、环境安全规划、设计感兴趣的学生"));
	  CC_ZY_EXT.put("环境科学", new MarjorBean("环境科学与工程类","冷门",1,"物/化","适合热爱环境科学工程，对环境监测及质量评估感兴趣的学生"));
	  CC_ZY_EXT.put("环境生态工程", new MarjorBean("环境科学与工程类","冷门",0,"物/化","适合热爱环境科学工程，对生态学研究、学习、应用感兴趣的学生"));
	  CC_ZY_EXT.put("环保设备工程", new MarjorBean("环境科学与工程类","冷门",0,"物/化","适合热爱环境科学工程，对环保设备的设计、完善感兴趣的学生"));
	  CC_ZY_EXT.put("资源环境科学", new MarjorBean("环境科学与工程类","冷门",0,"物/化","适合热爱环境科学工程，对生态环境学及资源规划、设计感兴趣的学生"));
	  CC_ZY_EXT.put("水质科学与技术", new MarjorBean("环境科学与工程类","冷门",1,"物/化","适合热爱环境科学工程，对水资源可持续发展感兴趣的学生"));
	  CC_ZY_EXT.put("生物医学工程", new MarjorBean("生物医学工程类","普通",2,"化/生","适合热爱生物医学工程，对生命科学仪器设备感兴趣的学生"));
	  CC_ZY_EXT.put("假肢矫形工程", new MarjorBean("生物医学工程类","冷门",0,"生/化","适合热爱生物医学工程，对假肢矫形及康复研究感兴趣的学生"));
	  CC_ZY_EXT.put("临床工程技术", new MarjorBean("生物医学工程类","冷门",0,"生","适合对临床工程技术研究感兴趣的学生"));
	  CC_ZY_EXT.put("康复工程", new MarjorBean("生物医学工程类","冷门",1,"化/生/物","适合热爱康复医疗，对工程机械研究感兴趣的学生"));
	  CC_ZY_EXT.put("食品科学与工程", new MarjorBean("食品科学与工程类","普通",3,"生/化","适合热爱食品工程，对食品生产技术、研究、管理感兴趣的学生"));
	  CC_ZY_EXT.put("食品质量与安全", new MarjorBean("食品科学与工程类","普通",3,"生/化","适合热爱食品工程，对食品安全与质量管理感兴趣的学生"));
	  CC_ZY_EXT.put("粮食工程", new MarjorBean("食品科学与工程类","普通",1,"生/化","适合热爱食品工程，对粮食工程规划、粮油加工感兴趣的学生"));
	  CC_ZY_EXT.put("乳品工程", new MarjorBean("食品科学与工程类","普通",2,"生/化","适合热爱食品工程，对乳制品加工质量管理技术感兴趣的学生"));
	  CC_ZY_EXT.put("酿酒工程", new MarjorBean("食品科学与工程类","热门",4,"生/化","适合热爱食品工程，对食品生产技术、研究、管理感兴趣的学生"));
	  CC_ZY_EXT.put("葡萄与葡萄酒工程", new MarjorBean("食品科学与工程类","热门",4,"生/化","适合热爱食品工程，对葡萄酒酿造、分析感兴趣的学生"));
	  CC_ZY_EXT.put("食品营养与检验教育", new MarjorBean("食品科学与工程类","冷门",0,"生/化","适合热爱食品工程，对食品营养及检验感兴趣的学生"));
	  CC_ZY_EXT.put("烹饪与营养教育", new MarjorBean("食品科学与工程类","冷门",0,"生/化","适合热爱食品工程，对烹饪面点及酒店管理感兴趣的学生"));
	  CC_ZY_EXT.put("食品安全与检测", new MarjorBean("食品科学与工程类","冷门",0,"生/化","熟悉国内外食品安全管理的政策、法规，以及食品安全管理体系"));
	  CC_ZY_EXT.put("食品营养与健康", new MarjorBean("食品科学与工程类","冷门",0,"生/化","分析和解决食品营养分析与评价相关复杂工程问题的能力"));
	  CC_ZY_EXT.put("食用菌科学与工程", new MarjorBean("食品科学与工程类","普通",2,"生/化","食用菌的生产、工厂设计、加工、流通、管理、新产品开发及科学研究等"));
	  CC_ZY_EXT.put("白酒酿造工程", new MarjorBean("食品科学与工程类","热门",4,"生/化","适合热爱酿酒工程，对酿酒生产技术、研究、管理感兴趣的学生"));
	  CC_ZY_EXT.put("建筑学", new MarjorBean("建筑类","热门",3,"物","适合热爱建筑学，对建筑设计、规划施工感兴趣的学生"));
	  CC_ZY_EXT.put("城乡规划", new MarjorBean("建筑类","热门",3,"物","适合热爱建筑学，对城乡规划、设计、管理感兴趣的学生"));
	  CC_ZY_EXT.put("风景园林", new MarjorBean("建筑类","热门",3,"物","适合建筑学，对风景园林规划、设计感兴趣的学生"));
	  CC_ZY_EXT.put("历史建筑保护工程", new MarjorBean("建筑类","普通",3,"物","适合建筑学，对历史建筑保护、维修、设计感兴趣的学生"));
	  CC_ZY_EXT.put("人居环境科学与技术", new MarjorBean("建筑类","普通",0,"物/数","系统分析与运算、实现动态掌控与预测，为人类宜居和美好生活提供可靠的技术支持"));
	  CC_ZY_EXT.put("城市设计", new MarjorBean("建筑类","普通",0,"物/数","城市规划的抽象性和数据化，城市设计更具有具体性和图形化"));
	  CC_ZY_EXT.put("智慧建筑与建造", new MarjorBean("建筑类","热门",0,"物/数","解决建筑全生命周期内复杂工程问题，引领智慧建筑设计与建造及其相关领域未来发展"));
	  CC_ZY_EXT.put("安全工程", new MarjorBean("安全科学与工程类","普通",1,"物","适合热爱安全工程，对安全监测与监控感兴趣的学生"));
	  CC_ZY_EXT.put("应急技术与管理", new MarjorBean("安全科学与工程类","热门",3,"数/政","公共安全、矿山、建筑、施工、消防、化工等领域的安全、应急救援等工作"));
	  CC_ZY_EXT.put("职业卫生工程", new MarjorBean("安全科学与工程类","冷门",0,"生/化","熟悉国家职业安全与卫生法律法规和政策方针"));
	  CC_ZY_EXT.put("生物工程", new MarjorBean("生物工程类","冷门",0,"生","适合热爱生物工程，对生物细胞培养、生物技术感兴趣的学生"));
	  CC_ZY_EXT.put("生物制药", new MarjorBean("生物工程类","冷门",2,"生","适合热爱生物工程，对生物分析、制药研发感兴趣的学生"));
	  CC_ZY_EXT.put("合成生物学", new MarjorBean("生物工程类","冷门",0,"生","建立人工生物系统"));
	  CC_ZY_EXT.put("刑事科学技术", new MarjorBean("公安技术类","热门",5,"生/化","适合对刑事科学技术感兴趣的学生"));
	  CC_ZY_EXT.put("消防工程", new MarjorBean("公安技术类","热门",5,"物/化","适合对消防工程安全管理感兴趣的学生"));
	  CC_ZY_EXT.put("交通管理工程", new MarjorBean("公安技术类","热门",5,"物/化","适合对交通管理、指挥、治安感兴趣的学生"));
	  CC_ZY_EXT.put("安全防范工程", new MarjorBean("公安技术类","热门",5,"物","适合对安全防范工程感兴趣的学生"));
	  CC_ZY_EXT.put("公安视听技术", new MarjorBean("公安技术类","热门",5,"物/化","适合对运用音频、计算机等处理分析犯罪事件感兴趣的学生"));
	  CC_ZY_EXT.put("抢险救援指挥与技术", new MarjorBean("公安技术类","热门",4,"物/化","适合对抢险救援技术感兴趣的男生"));
	  CC_ZY_EXT.put("火灾勘查", new MarjorBean("公安技术类","热门",4,"生/化","适合对刑事科学技术感兴趣的学生"));
	  CC_ZY_EXT.put("网络安全与执法", new MarjorBean("公安技术类","热门",5,"数/物","适合对网络安全与执法感兴趣，善于分析的学生"));
	  CC_ZY_EXT.put("核生化消防", new MarjorBean("公安技术类","热门",4,"生/化","对核生化消防技术感兴趣的学生"));
	  CC_ZY_EXT.put("海警舰艇指挥与技术", new MarjorBean("公安技术类","热门",4,"生/政","海警舰艇的操纵、指挥、管理、海洋气象、海上执法等方面"));
	  CC_ZY_EXT.put("数据警务技术", new MarjorBean("公安技术类","热门",5,"数/政","公安信息化平台架构与运维、警务大数据分析与预测、警务大数据管理与决策"));
	  CC_ZY_EXT.put("食品药品环境犯罪侦查技术", new MarjorBean("公安技术类","热门",5,"数/生","新设，胜任“食药环”领域案件犯罪现场勘查、案件分析和物证提取、检验工作"));
	  CC_ZY_EXT.put("农学", new MarjorBean("植物生产类","冷门",1,"生","适合对农业生物、生态、作物发育、遗传感兴趣的学生"));
	  CC_ZY_EXT.put("园艺", new MarjorBean("植物生产类","冷门",1,"生","适合对生物学及园艺学感兴趣的学生"));
	  CC_ZY_EXT.put("植物保护", new MarjorBean("植物生产类","冷门",1,"生","适合对生物科学、植物保护感兴趣的学生"));
	  CC_ZY_EXT.put("植物科学与技术", new MarjorBean("植物生产类","冷门",1,"生","适合对植物科研、生成、管理、开发感兴趣的学生"));
	  CC_ZY_EXT.put("种子科学与工程", new MarjorBean("植物生产类","普通",3,"生","生物科目要求较高。该专业适合对种子生产、经营管理、推广感兴趣的学生"));
	  CC_ZY_EXT.put("设施农业科学与工程", new MarjorBean("植物生产类","冷门",1,"生","适合对农业设施设计、制造、安装感兴趣的学生"));
	  CC_ZY_EXT.put("茶学", new MarjorBean("植物生产类","普通",2,"生","适合对茶树栽培、茶叶生产、茶叶营销感兴趣的学生"));
	  CC_ZY_EXT.put("烟草", new MarjorBean("植物生产类","热门",3,"生","适合对烟草栽培、生产、经营管理感兴趣的学生"));
	  CC_ZY_EXT.put("应用生物科学", new MarjorBean("植物生产类","冷门",0,"生","适合对农业生物、生态、作物发育、遗传感兴趣的学生"));
	  CC_ZY_EXT.put("农艺教育", new MarjorBean("植物生产类","冷门",0,"生","适合对农艺教育传播、推广感兴趣的学生"));
	  CC_ZY_EXT.put("园艺教育", new MarjorBean("植物生产类","冷门",0,"生","适合对园艺教育、植物生产、经营感兴趣的学生"));
	  CC_ZY_EXT.put("智慧农业", new MarjorBean("植物生产类","普通",3,"数/生","信息技术、生物技术、现代工程装备技术、现代经营管理知识与农学有机融合"));
	  CC_ZY_EXT.put("菌物科学与工程", new MarjorBean("植物生产类","普通",1,"生","食药用菌资源、栽培育种、产品加工和菌物药等"));
	  CC_ZY_EXT.put("农药化肥", new MarjorBean("植物生产类","冷门",1,"生/化","作物生产、作物遗传育种、作物种子生产等方面"));
	  CC_ZY_EXT.put("生物农药科学与工程", new MarjorBean("植物生产类","冷门",3,"生/化","新设，生物农药、绿色农药方面"));
	  CC_ZY_EXT.put("生物育种科学", new MarjorBean("植物生产类","普通",3,"生","新设，农药制药、生物制药和植物保护，及绿色农药开展新资源、新产品、新工艺研究等"));
	  CC_ZY_EXT.put("农业资源与环境", new MarjorBean("自然保护与环境生态类","冷门",1,"化","适合热爱自然保护、对农业资源与环境感兴趣的学生"));
	  CC_ZY_EXT.put("野生动物与自然保护区管理", new MarjorBean("自然保护与环境生态类","冷门",1,"化","适合热爱自然保护、对野生动物及自然保护区管理感兴趣的学生"));
	  CC_ZY_EXT.put("水土保持与荒漠化防治", new MarjorBean("自然保护与环境生态类","冷门",1,"化","适合热爱自然保护、对水土保持及荒漠化防治感兴趣的学生"));
	  CC_ZY_EXT.put("生物质科学与工程", new MarjorBean("自然保护与环境生态类","冷门",1,"生/化","具备化学、化工、生物、材料、生物质转化与利用基础知识"));
	  CC_ZY_EXT.put("土地科学与技术", new MarjorBean("自然保护与环境生态类","冷门",1,"生/地","新设，土地及其利用过程中所产生的人地关系及其发展变化规律"));
	  CC_ZY_EXT.put("湿地保护与恢复", new MarjorBean("自然保护与环境生态类","冷门",1,"生","新设，立足国家生态文明建设战略和林业信息化发展需要"));
	  CC_ZY_EXT.put("动物科学", new MarjorBean("动物生产类","冷门",3,"化","适合热爱动物科学，对动物遗传、生产、管理感兴趣的学生"));
	  CC_ZY_EXT.put("蚕学", new MarjorBean("动物生产类","冷门",1,"化","适合热爱动物科学，对蚕的养殖、蚕丝加工感兴趣的学生"));
	  CC_ZY_EXT.put("蜂学", new MarjorBean("动物生产类","冷门",1,"化","适合热爱动物科学，对蜜蜂育种、产品加工等感兴趣的学生"));
	  CC_ZY_EXT.put("经济动物学", new MarjorBean("动物生产类","冷门",0,"生","适合热爱动物科学，对动物遗传、生产、管理感兴趣的学生"));
	  CC_ZY_EXT.put("马业科学", new MarjorBean("动物生产类","普通",3,"生","适合热爱动物科学，对动物遗传、生产、管理感兴趣的学生"));
	  CC_ZY_EXT.put("饲料工程", new MarjorBean("动物生产类","普通",2,"生/数","新设，饲料企业生产技术管理岗位、投资发展技术岗位、饲料工艺与成套工程设计岗位的中坚力量"));
	  CC_ZY_EXT.put("智慧牧业科学与工程", new MarjorBean("动物生产类","普通",2,"生/数","新设，结合软件工程和人工智能，应用智慧牧业软件开发能力和嵌入式应用系统开发能力"));
	  CC_ZY_EXT.put("动物医学", new MarjorBean("动物医学类","普通",4,"生/化","适合热爱动物医学，对动物防疫检疫感兴趣的学生"));
	  CC_ZY_EXT.put("动物药学", new MarjorBean("动物医学类","普通",3,"生/化","适合热爱动物药学，对动物用药的研发、推广感兴趣的学生"));
	  CC_ZY_EXT.put("动植物检疫", new MarjorBean("动物医学类","冷门",2,"生/化","适合热爱动植物学，对动植物检疫感兴趣的学生"));
	  CC_ZY_EXT.put("实验动物学", new MarjorBean("动物医学类","冷门",1,"生/化","经人工培育或人工改造，对其携带的微生物实行控制"));
	  CC_ZY_EXT.put("中兽医学", new MarjorBean("动物医学类","冷门",1,"生/化","现代动物医学及传统兽医学理论、知识和技能"));
	  CC_ZY_EXT.put("兽医公共卫生", new MarjorBean("动物医学类","冷门",1,"生/化","新设，解决人兽共患病防控、动物源食品安全监测、动物源细菌耐药性控制等兽医问题"));
	  CC_ZY_EXT.put("林学", new MarjorBean("林学类","冷门",1,"生/化","适合热爱林学，对林木培育、森林生态建设感兴趣的学生"));
	  CC_ZY_EXT.put("园林", new MarjorBean("林学类","冷门",2,"生/化","适合热爱园林学，对园林植物繁育、园林施工、管理感兴趣的学生"));
	  CC_ZY_EXT.put("森林保护", new MarjorBean("林学类","冷门",0,"生/化","适合热爱森林工程，对现代林业及森林保护感兴趣的学生"));
	  CC_ZY_EXT.put("经济林", new MarjorBean("林学类","冷门",0,"生/化","适合对经济林研究感兴趣的学生"));
	  CC_ZY_EXT.put("智慧林业", new MarjorBean("林学类","冷门",1,"数/生/化","新设，用科技栽下的“智慧林”长势生机勃勃"));
	  CC_ZY_EXT.put("水产养殖学", new MarjorBean("水产类","冷门",1,"生/化","适合热爱水产学，对水产养殖、管理感兴趣的学生"));
	  CC_ZY_EXT.put("海洋渔业科学与技术", new MarjorBean("水产类","冷门",0,"生/化","适合热爱水产学，对海洋渔业生产、教育、科研感兴趣的学生"));
	  CC_ZY_EXT.put("水族科学与技术", new MarjorBean("水产类","冷门",0,"生/化","适合热爱水产学，对水产育种、品质与卫生检验感兴趣的学生"));
	  CC_ZY_EXT.put("水生动物医学", new MarjorBean("水产类","冷门",0,"生","适合对水生动物医学感兴趣的学生"));
	  CC_ZY_EXT.put("草业科学", new MarjorBean("草学类","冷门",0,"生/化","适合热爱草学科学，对其设计、开发、管理感兴趣的学生"));
	  CC_ZY_EXT.put("草坪科学与工程", new MarjorBean("草学类","冷门",0,"生/化","草坪绿化植物配置与建植、草坪草优异种质创新、绿地景观规划设计、生态修复治理等领域"));
	  CC_ZY_EXT.put("基础医学", new MarjorBean("基础医学类","普通",1,"生/化","适合对生命科学研究感兴趣的学生"));
	  CC_ZY_EXT.put("生物医学", new MarjorBean("基础医学类","冷门",1,"生/化","生物医学信息、医学影像技术、基因芯片、纳米技术、新材料等技术的学术研究和创新"));
	  CC_ZY_EXT.put("生物医学科学", new MarjorBean("基础医学类","冷门",1,"生/化","肿瘤、糖尿病、遗传疾病、病毒等方面的诊疗与探索"));
	  CC_ZY_EXT.put("临床医学", new MarjorBean("临床医学类","热门",5,"生/化","当医生，适合对人类疾病的诊断、治疗、预防方面感兴趣的学生"));
	  CC_ZY_EXT.put("麻醉学", new MarjorBean("临床医学类","热门",5,"生","当医生，适合对手术麻醉处理、围麻醉期并发症防治和危重病症监测、判断和治疗感兴趣的学生"));
	  CC_ZY_EXT.put("医学影像学", new MarjorBean("临床医学类","热门",4,"物/生/化","当医生，适合对影像诊断研究感兴趣的学生"));
	  CC_ZY_EXT.put("眼视光医学", new MarjorBean("临床医学类","热门",5,"生","当医生，适合对眼科医学技术感兴趣的学生"));
	  CC_ZY_EXT.put("精神医学", new MarjorBean("临床医学类","热门",3,"生","当医生，适合对心理障碍、行为障碍、精神疾病研究感兴趣的学生"));
	  CC_ZY_EXT.put("放射医学", new MarjorBean("临床医学类","热门",4,"生","当医生，适合对放射诊断、核素诊断、影像诊断感兴趣的学生"));
	  CC_ZY_EXT.put("儿科学", new MarjorBean("临床医学类","热门",4,"生","当医生，适合喜欢临床医学的研究、热爱儿童身心健康的考生"));
	  CC_ZY_EXT.put("口腔医学", new MarjorBean("口腔医学类","热门",5,"生","当医生，适合对口腔常见病、多发病方面研究感兴趣的学生"));
	  CC_ZY_EXT.put("预防医学", new MarjorBean("公共卫生与预防医学类","热门",5,"生","当医生，适合对卫生防疫、控制传染病方面研究感兴趣的学生"));
	  CC_ZY_EXT.put("食品卫生与营养学", new MarjorBean("公共卫生与预防医学类","冷门",1,"生","适合对营养与食品卫生方面研究感兴趣的学生"));
	  CC_ZY_EXT.put("妇幼保健医学", new MarjorBean("公共卫生与预防医学类","热门",2,"生","当医生，适合对妇幼卫生及保健方面研究感兴趣的学生"));
	  CC_ZY_EXT.put("卫生监督", new MarjorBean("公共卫生与预防医学类","热门",2,"生","当医生，适合对卫生监督与执法感兴趣的学生"));
	  CC_ZY_EXT.put("全球健康学", new MarjorBean("公共卫生与预防医学类","热门",1,"生","当医生，适合对球健康领域的理论研究感兴趣的学生"));
	  CC_ZY_EXT.put("运动与公共健康", new MarjorBean("公共卫生与预防医学类","冷门",1,"生","新设，对运动与公共健康专业感兴趣"));
	  CC_ZY_EXT.put("中医学", new MarjorBean("中医学类","热门",4,"生","当医生，该专业适合对中医各科疾病的临床诊疗方面研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("针灸推拿学", new MarjorBean("中医学类","热门",4,"生","当医生，该专业适合对运用针灸、推拿诊疗各科疾病方面研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("藏医学", new MarjorBean("中医学类","普通",2,"生","当医生，该专业适合对藏医的药理方法方面研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("蒙医学", new MarjorBean("中医学类","普通",1,"生","当医生，该专业适合对蒙医临床操作和辨证施治方面研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("维医学", new MarjorBean("中医学类","普通",1,"生","当医生，该专业适合对维医药理方法防治常见病、多发病方面研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("壮医学", new MarjorBean("中医学类","普通",1,"生","当医生，该专业适合对壮医药理方法防治常见病、多发病方面研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("哈医学", new MarjorBean("中医学类","普通",1,"生","当医生，该专业适合对哈医防治常见病、多发病方面研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("傣医学", new MarjorBean("中医学类","普通",1,"生","当医生，该专业适合对哈医防治常见病、多发病方面研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("回医学", new MarjorBean("中医学类","普通",1,"生","当医生，该专业适合对傣医防治常见病、多发病方面研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("中医康复学", new MarjorBean("中医学类","普通",1,"生","当医生，该专业适合对中医康复学方面研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("中医养生学", new MarjorBean("中医学类","普通",1,"生","当医生，中医养生、治未病和慢性病、老年病干预治疗、防护等"));
	  CC_ZY_EXT.put("中医儿科学", new MarjorBean("中医学类","热门",4,"生","当医生，儿科病症进行中医临床诊疗"));
	  CC_ZY_EXT.put("中医骨伤科学", new MarjorBean("中医学类","热门",4,"生","当医生，该专业适合对中医各科疾病的临床诊疗方面研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("中西医临床医学", new MarjorBean("中西医结合类","普通",3,"生","当医生，该专业适合对中西医各科疾病的临床诊疗方面研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("药学", new MarjorBean("药学类","热门",2,"生","该专业适合对药物制备、质量控制评价方面研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("药物制剂", new MarjorBean("药学类","普通",1,"生","该专业适合对药物制剂研究、开发、生产技术感兴趣的学生就读。"));
	  CC_ZY_EXT.put("临床药学", new MarjorBean("药学类","热门",3,"生","研究和实践药物临床合理应用方法的综合性应用技术学科"));
	  CC_ZY_EXT.put("药事管理", new MarjorBean("药学类","冷门",1,"生","该专业适合对医药企业管理、医院药房和社会零售药店管理感兴趣的学生就读。"));
	  CC_ZY_EXT.put("药物分析", new MarjorBean("药学类","冷门",1,"生/化","该专业适合对药物分析、药事与企业管理方面研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("药物化学", new MarjorBean("药学类","冷门",1,"生/化","该专业适合对药物化学分析研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("海洋药学", new MarjorBean("药学类","冷门",0,"生","该专业适合对海洋药物研制、生产、质量控制研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("化妆品科学与技术", new MarjorBean("药学类","普通",4,"生/化","化妆品配方设计、工业生产、性能评价、安全使用、品质管理及市场营销等"));
	  CC_ZY_EXT.put("中药学", new MarjorBean("中药学类","普通",2,"生","该专业适合对中药鉴定、炮制、制备感兴趣的学生就读。"));
	  CC_ZY_EXT.put("中药资源与开发", new MarjorBean("中药学类","普通",1,"生","该专业适合对中药原料的生产、加工，中药新药开发感兴趣的学生就读。"));
	  CC_ZY_EXT.put("藏药学", new MarjorBean("中药学类","冷门",1,"生","该专业适合对药物制备、质量控制评价方面研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("蒙药学", new MarjorBean("中药学类","冷门",1,"生","该专业适合对蒙药鉴定、炮制、制备感兴趣的学生就读"));
	  CC_ZY_EXT.put("中药制药", new MarjorBean("中药学类","冷门",1,"生","该专业适合对中药制药生产、检测技能感兴趣的学生就读"));
	  CC_ZY_EXT.put("中草药栽培与鉴定", new MarjorBean("中药学类","冷门",1,"生","该专业适合对中草药资源分布、栽培、采收加工感兴趣的学生就读。"));
	  CC_ZY_EXT.put("法医学", new MarjorBean("法医学类","冷门",2,"生","该专业适合对法医学检案鉴定感兴趣的学生就读"));
	  CC_ZY_EXT.put("医学检验技术", new MarjorBean("医学技术类","热门",3,"生/化","该专业适合对医学检验方面感兴趣的学生就读。"));
	  CC_ZY_EXT.put("医学实验技术", new MarjorBean("医学技术类","普通",2,"生","该专业适合对临床实验及卫生检验感兴趣的学生就读"));
	  CC_ZY_EXT.put("医学影像技术", new MarjorBean("医学技术类","热门",3,"生","该专业适合对影像诊断学和介入医学感兴趣的学生就读。"));
	  CC_ZY_EXT.put("眼视光学", new MarjorBean("医学技术类","普通",3,"生","该专业适合对诊断、预防与治疗眼病感兴趣的学生就读。"));
	  CC_ZY_EXT.put("康复治疗学", new MarjorBean("医学技术类","冷门",3,"生","该专业适合对康复治疗感兴趣的学生就读。"));
	  CC_ZY_EXT.put("口腔医学技术", new MarjorBean("医学技术类","普通",3,"生","该专业适合对口腔常见病、多发病的诊疗、修复和预防保健感兴趣的学生就读。"));
	  CC_ZY_EXT.put("卫生检验与检疫", new MarjorBean("医学技术类","冷门",1,"生","该专业适合对卫生检验与检疫方面感兴趣的学生就读"));
	  CC_ZY_EXT.put("听力与言语康复学", new MarjorBean("医学技术类","普通",3,"生","该专业适合对听力与言语康复学治疗感兴趣的学生就读"));
	  CC_ZY_EXT.put("康复物理治疗", new MarjorBean("医学技术类","冷门",1,"生","对常见疾病和残疾的康复治疗、评定及预防"));
	  CC_ZY_EXT.put("康复作业治疗", new MarjorBean("医学技术类","冷门",1,"生","现代与传统康复治疗工作"));
	  CC_ZY_EXT.put("智能医学工程", new MarjorBean("医学技术类","热门",1,"生/数","智能药物研发、医疗机器人、智能诊疗、智能影像识别、智能健康数据管理等"));
	  CC_ZY_EXT.put("生物医药数据科学", new MarjorBean("医学技术类","热门",2,"生/数","新设，扎实的数理基础、大数据技术基础、数据科学与生物医学交叉学科"));
	  CC_ZY_EXT.put("智能影像工程", new MarjorBean("医学技术类","热门",2,"生/物/数","新设，人工智能辅助诊断、智能成像设备开发、医疗机器人开发等"));
	  CC_ZY_EXT.put("护理学", new MarjorBean("护理学类","热门",4,"生","该专业适合对临床护理感兴趣的学生就读"));
	  CC_ZY_EXT.put("助产学", new MarjorBean("护理学类","普通",3,"生","该专业适合对助产学感兴趣的学生就读"));
	  CC_ZY_EXT.put("管理科学", new MarjorBean("管理科学与工程类","普通",0,"数","该专业适合对信息管理和数量分析研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("信息管理与信息系统", new MarjorBean("管理科学与工程类","热门",4,"数","该专业适合对经济、数量分析研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("工程管理", new MarjorBean("管理科学与工程类","普通",3,"数","该专业适合对工程建筑管理与开发研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("房地产开发与管理", new MarjorBean("管理科学与工程类","普通",1,"地","该专业适合对房地产开发与管理研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("工程造价", new MarjorBean("管理科学与工程类","热门",4,"物","该专业适合对工程投资预算研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("保密管理", new MarjorBean("管理科学与工程类","热门",1,"数","该专业适合对信息安全研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("邮政管理", new MarjorBean("管理科学与工程类","冷门",2,"政","该专业适合对邮政业管理研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("大数据管理与应用", new MarjorBean("管理科学与工程类","热门",3,"数","大数据分析理论和方法在经济管理中的应用以及大数据管理与治理方法"));
	  CC_ZY_EXT.put("工程审计", new MarjorBean("管理科学与工程类","热门",3,"数/物","检查工程建设各基建环节的管理是否合规合法、建设成本的真实性和相关规定的贯彻执行情况等"));
	  CC_ZY_EXT.put("计算金融", new MarjorBean("管理科学与工程类","热门",1,"数","该专业适合对统计计算、社会经济有兴趣的学生就读"));
	  CC_ZY_EXT.put("应急管理", new MarjorBean("管理科学与工程类","热门",5,"数","应急指挥与决策、应急资源管理、应急救援等方面"));
	  CC_ZY_EXT.put("工商管理", new MarjorBean("工商管理类","普通",1,"数","该专业适合对管理知识研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("市场营销", new MarjorBean("工商管理类","冷门",0,"无","该专业适合对市场营销、经济研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("会计学", new MarjorBean("工商管理类","热门",3,"语","该专业适合对事物分析信息获取方面感兴趣的学生就读。"));
	  CC_ZY_EXT.put("财务管理", new MarjorBean("工商管理类","热门",4,"无","该专业适合对理财、资金效益研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("国际商务", new MarjorBean("工商管理类","冷门",0,"语/英","该专业适合对国际经济与贸易研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("人力资源管理", new MarjorBean("工商管理类","普通",1,"语","该专业适合对人力资源管理感兴趣的学生就读。"));
	  CC_ZY_EXT.put("审计学", new MarjorBean("工商管理类","热门",3,"语","该专业适合对经济分析、审计研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("资产评估", new MarjorBean("工商管理类","普通",2,"语","该专业适合对国内外资产评估研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("物业管理", new MarjorBean("工商管理类","冷门",0,"无","该专业适合对经济建设与物业管理研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("文化产业管理", new MarjorBean("工商管理类","冷门",0,"语","该专业适合对中外文化研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("劳动关系", new MarjorBean("工商管理类","冷门",0,"语","该专业适合对经济、劳动关系研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("体育经济与管理", new MarjorBean("工商管理类","普通",2,"体","该专业适合对体育经济研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("财务会计教育", new MarjorBean("工商管理类","普通",0,"语","该专业适合对财务及经济分析研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("市场营销教育", new MarjorBean("工商管理类","冷门",0,"语","该专业适合对经济市场营销研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("零售业管理", new MarjorBean("工商管理类","普通",1,"语","该专业适合对零售业管理研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("创业管理", new MarjorBean("工商管理类","普通",0,"语","新设，服务区域经济社会发展和各行业对创业型人才的需要开设"));
	  CC_ZY_EXT.put("海关稽查", new MarjorBean("工商管理类","热门",4,"语","新设，海关稽查的国际动态和国内新海关发展，海关稽查风险分析与防控能力"));
	  CC_ZY_EXT.put("农林经济管理", new MarjorBean("农业经济管理类","冷门",2,"语","该专业适合对农林经济研究感兴趣、喜欢农林经济管理的学生就读"));
	  CC_ZY_EXT.put("农村区域发展", new MarjorBean("农业经济管理类","冷门",1,"语","该专业适合对农村经济与发展研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("公共事业管理", new MarjorBean("公共管理类","普通",1,"语/数","该专业适合对规划、组织等管理感兴趣的学生就读。"));
	  CC_ZY_EXT.put("行政管理", new MarjorBean("公共管理类","普通",0,"语","该专业适合对行政理论研究和统计分析感兴趣的学生就读。"));
	  CC_ZY_EXT.put("劳动与社会保障", new MarjorBean("公共管理类","普通",2,"数","该专业适合对劳动与社会保障领域感兴趣的学生就读。"));
	  CC_ZY_EXT.put("土地资源管理", new MarjorBean("公共管理类","普通",2,"数","该专业适合对土地资源管理研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("城市管理", new MarjorBean("公共管理类","冷门",0,"无","该专业适合对城市管理和统计规划研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("海关管理", new MarjorBean("公共管理类","热门",4,"无","该专业适合对海关管理业务感兴趣，喜欢外贸业务的学生就读"));
	  CC_ZY_EXT.put("交通管理", new MarjorBean("公共管理类","热门",3,"无","该专业适合对交通维护管理感兴趣、善于沟通的学生就读。"));
	  CC_ZY_EXT.put("海事管理", new MarjorBean("公共管理类","热门",3,"无","该专业适合对船舶、航海感兴趣的学生就读。"));
	  CC_ZY_EXT.put("公共关系学", new MarjorBean("公共管理类","冷门",0,"语","该专业适合对人文社科科学感兴趣的学生就读。"));
	  CC_ZY_EXT.put("健康服务与管理", new MarjorBean("公共管理类","冷门",0,"语/数","该专业适合对健康服务管理感兴趣的学生就读。"));
	  CC_ZY_EXT.put("海警后勤管理", new MarjorBean("公共管理类","热门",1,"语/数","该专业适合对海警后勤管理业务感兴趣的学生就读"));
	  CC_ZY_EXT.put("医疗产品管理", new MarjorBean("公共管理类","冷门",2,"生/数","医疗产品的营销管理、法规事务执行等"));
	  CC_ZY_EXT.put("医疗保险", new MarjorBean("公共管理类","普通",2,"数","健康保险、医疗保险等"));
	  CC_ZY_EXT.put("养老服务管理", new MarjorBean("公共管理类","普通",2,"生","养老政策制定、养老企业运营管理、养老项目策划、养老产品开发等"));
	  CC_ZY_EXT.put("海关检验检疫安全", new MarjorBean("公共管理类","热门",4,"生","新设，在海关等政府部门及其他各类组织中从事检验检疫安全管理"));
	  CC_ZY_EXT.put("海外安全管理", new MarjorBean("公共管理类","热门",3,"政","新设，为驻外政府机构、驻外中资企业进行海外风险管理、应急处置和安防体系构建的专业"));
	  CC_ZY_EXT.put("自然资源登记与管理", new MarjorBean("公共管理类","普通",1,"地","新设，自然资源与不动产的产权界定、产权调查、产权登记、多维权籍信息化等"));
	  CC_ZY_EXT.put("慈善管理", new MarjorBean("公共管理类","普通",3,"语","新设，慈善事业相关的管理"));
	  CC_ZY_EXT.put("图书馆学", new MarjorBean("图书情报与档案管理类","冷门",1,"语/政","该专业适合对文献信息搜集、处理、研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("档案学", new MarjorBean("图书情报与档案管理类","冷门",1,"政","该专业适合对档案信息管理研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("信息资源管理", new MarjorBean("图书情报与档案管理类","普通",2,"数","该专业适合对信息资源管理方面感兴趣的学生就读"));
	  CC_ZY_EXT.put("物流管理", new MarjorBean("物流管理与工程类","普通",1,"英","该专业适合对物流事物处理感兴趣的学生就读"));
	  CC_ZY_EXT.put("物流工程", new MarjorBean("物流管理与工程类","普通",1,"英","该专业适合对物流规划与管理感兴趣的学生就读。"));
	  CC_ZY_EXT.put("釆购管理", new MarjorBean("物流管理与工程类","普通",1,"英","该专业适合对采购及供应链研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("供应链管理", new MarjorBean("物流管理与工程类","普通",1,"数","运用大数据、人工智能、深度学习等前沿技术发展起来的一门新兴学科"));
	  CC_ZY_EXT.put("工业工程", new MarjorBean("工业工程类","冷门",0,"物","该专业适合对工业工程研究与分析感兴趣的学生就读"));
	  CC_ZY_EXT.put("标准化工程", new MarjorBean("工业工程类","热门",3,"数/物","该专业适合对标准化工程项目的决策和管理感兴趣的学生就读。"));
	  CC_ZY_EXT.put("质量管理工程", new MarjorBean("工业工程类","热门",3,"物","该专业适合对质量工程方面感兴趣、善于管理的学生就读"));
	  CC_ZY_EXT.put("电子商务", new MarjorBean("电子商务类","普通",0,"数","该专业适合对网络营销及设计感兴趣的学生就读"));
	  CC_ZY_EXT.put("电子商务及法律", new MarjorBean("电子商务类","普通",1,"英/数","该专业适合对网络商务活动感兴趣的学生就读。"));
	  CC_ZY_EXT.put("跨境电子商务", new MarjorBean("电子商务类","普通",1,"语/政","综合管理、跨境电商运营、市场调研、经济活动分析等"));
	  CC_ZY_EXT.put("旅游管理", new MarjorBean("旅游管理类","普通",1,"语","该专业适合对旅游经营管理感兴趣、热爱旅游业的学生就读。"));
	  CC_ZY_EXT.put("酒店管理", new MarjorBean("旅游管理类","普通",1,"英","该专业适合对酒店经营管理感兴趣，善于沟通的学生就读。"));
	  CC_ZY_EXT.put("会展经济与管理", new MarjorBean("旅游管理类","普通",1,"语/数","该专业适合对会展组织与管理感兴趣的学生就读。"));
	  CC_ZY_EXT.put("旅游管理与服务教育", new MarjorBean("旅游管理类","冷门",0,"英","该专业适合对旅游服务感兴趣，善于沟通交流的学生就读。"));
	  CC_ZY_EXT.put("艺术史论", new MarjorBean("艺术学理论类","普通",0,"语","该专业适合对艺术研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("艺术管理", new MarjorBean("艺术学理论类","普通",0,"美","该专业适合对艺术管理研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("非物质文化遗产保护", new MarjorBean("艺术学理论类","热门",1,"历","新设，非物质文化遗产保护、非物质文化遗产传承"));
	  CC_ZY_EXT.put("音乐表演", new MarjorBean("音乐与舞蹈学类","普通",0,"音","该专业适合对音乐作品表演感兴趣的学生就读。"));
	  CC_ZY_EXT.put("音乐学", new MarjorBean("音乐与舞蹈学类","普通",3,"音","该专业适合对音乐研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("作曲与作曲技术理论", new MarjorBean("音乐与舞蹈学类","普通",1,"音","该专业适合对音乐研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("舞蹈表演", new MarjorBean("音乐与舞蹈学类","普通",0,"舞","该专业适合对舞蹈研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("舞蹈学", new MarjorBean("音乐与舞蹈学类","普通",1,"舞","该专业适合对艺术研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("舞蹈编导", new MarjorBean("音乐与舞蹈学类","普通",0,"舞","该专业适合对艺术研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("舞蹈教育", new MarjorBean("音乐与舞蹈学类","普通",3,"舞","该专业适合对艺术研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("航空服务艺术与管理", new MarjorBean("音乐与舞蹈学类","热门",3,"政/英","该专业适合对艺术研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("流行音乐", new MarjorBean("音乐与舞蹈学类","普通",0,"音","该专业适合对音乐研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("音乐治疗", new MarjorBean("音乐与舞蹈学类","普通",0,"音","该专业适合对音乐治疗研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("流行舞蹈", new MarjorBean("音乐与舞蹈学类","普通",0,"舞","该专业适合对舞蹈研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("音乐教育", new MarjorBean("音乐与舞蹈学类","普通",3,"音","新设，该专业适合对音乐研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("表演", new MarjorBean("戏剧与影视学类","普通",1,"语","该专业适合对表演艺术研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("戏剧学", new MarjorBean("戏剧与影视学类","普通",2,"无","该专业适合对戏剧研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("电影学", new MarjorBean("戏剧与影视学类","普通",1,"无","该专业适合对电影学研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("戏剧影视文学", new MarjorBean("戏剧与影视学类","普通",0,"语","该专业适合对戏剧影视研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("广播电视编导", new MarjorBean("戏剧与影视学类","普通",0,"无","该专业适合对广播电视编导研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("戏剧影视导演", new MarjorBean("戏剧与影视学类","普通",0,"无","该专业适合对戏剧影视导演感兴趣的学生就读。"));
	  CC_ZY_EXT.put("戏剧影视美术设计", new MarjorBean("戏剧与影视学类","普通",0,"无","该专业适合对戏剧影视美术设计感兴趣的学生就读"));
	  CC_ZY_EXT.put("录音艺术", new MarjorBean("戏剧与影视学类","普通",1,"音","该专业适合对录音艺术感兴趣的学生就读。"));
	  CC_ZY_EXT.put("播音与主持艺术", new MarjorBean("戏剧与影视学类","热门",0,"无","该专业适合对播音与主持艺术感兴趣的学生就读。"));
	  CC_ZY_EXT.put("动画", new MarjorBean("戏剧与影视学类","热门",3,"无","该专业适合对动画感兴趣的学生就读"));
	  CC_ZY_EXT.put("影视摄影与制作", new MarjorBean("戏剧与影视学类","普通",3,"无","该专业适合对影视摄影与制作感兴趣的学生就读"));
	  CC_ZY_EXT.put("影视技术", new MarjorBean("戏剧与影视学类","普通",2,"美","该专业适合对影视技术感兴趣的学生就读"));
	  CC_ZY_EXT.put("戏剧教育", new MarjorBean("戏剧与影视学类","普通",0,"无","该专业适合对戏剧教育感兴趣的学生就读。"));
	  CC_ZY_EXT.put("曲艺", new MarjorBean("戏剧与影视学类","普通",0,"语","新设，该专业适合对曲艺感兴趣的学生就读。"));
	  CC_ZY_EXT.put("音乐剧", new MarjorBean("戏剧与影视学类","普通",0,"音/语","新设，该专业适合对音乐剧感兴趣的学生就读。"));
	  CC_ZY_EXT.put("美术学", new MarjorBean("美术学类","热门",3,"美","该专业适合对美术学研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("绘画", new MarjorBean("美术学类","热门",0,"美","该专业适合对绘画研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("雕塑", new MarjorBean("美术学类","普通",0,"无","该专业适合对雕塑研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("摄影", new MarjorBean("美术学类","普通",0,"无","该专业适合对摄影研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("书法学", new MarjorBean("美术学类","普通",0,"无","该专业适合对书法研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("中国画", new MarjorBean("美术学类","普通",0,"无","该专业适合对中国画研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("实验艺术", new MarjorBean("美术学类","普通",0,"无","多层次、跨学科、多领域交叉合作的综合型、实验性的艺术"));
	  CC_ZY_EXT.put("跨媒体艺术", new MarjorBean("美术学类","普通",2,"美/语","实验影像、虚拟现实、空间多媒体与社会学、图像与视觉文化、现场艺术等多个专业领域"));
	  CC_ZY_EXT.put("文物保护与修复", new MarjorBean("美术学类","普通",1,"美","考古、博物馆、文物管理机构从事文物保护与科学研究工作"));
	  CC_ZY_EXT.put("漫画", new MarjorBean("美术学类","热门",1,"美","在动画设计的理论基础上，掌握漫画设计的原则及表现技巧"));
	  CC_ZY_EXT.put("纤维艺术", new MarjorBean("美术学类","普通",0,"美","新设，为形式特征的既古老又现代的艺术门类"));
	  CC_ZY_EXT.put("科技艺术", new MarjorBean("美术学类","普通",0,"美","新设，探讨艺术与数学、人工智能创造力、灵感的脑科学机制、太空艺术等"));
	  CC_ZY_EXT.put("美术教育", new MarjorBean("美术学类","普通",2,"美","新设，该专业适合对美术研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("艺术设计学", new MarjorBean("设计学类","普通",0,"无","该专业适合对艺术设计研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("视觉传达设计", new MarjorBean("设计学类","普通",5,"无","该专业适合对视觉传达设计研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("环境设计", new MarjorBean("设计学类","普通",3,"物","该专业适合对环境设计研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("产品设计", new MarjorBean("设计学类","普通",1,"数","该专业适合对产品设计研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("服装与服饰设计", new MarjorBean("设计学类","普通",1,"数","该专业适合对服装与服饰设计研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("公共艺术", new MarjorBean("设计学类","普通",0,"无","该专业适合对公共艺术研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("工艺美术", new MarjorBean("设计学类","普通",2,"无","该专业适合对公共艺术研究感兴趣的学生就读"));
	  CC_ZY_EXT.put("数字媒体艺术", new MarjorBean("设计学类","热门",4,"无","该专业适合对工艺美术研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("艺术与科技", new MarjorBean("设计学类","普通",1,"数","该专业适合对数字媒体艺术研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("陶瓷艺术设计", new MarjorBean("设计学类","普通",1,"无","该专业适合对艺术与科技研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("新媒体艺术", new MarjorBean("设计学类","普通",2,"无","使用新式媒体技术制作、修改或传播的当代艺术"));
	  CC_ZY_EXT.put("包装设计", new MarjorBean("设计学类","普通",3,"数","该专业适合对包装设计研究感兴趣的学生就读。"));
	  CC_ZY_EXT.put("珠宝首饰设计与工艺", new MarjorBean("设计学类","热门",2,"无","新设，首饰设计与珠宝首饰鉴定技能于一身"));
	  CC_ZY_EXT.put("哲学类", new MarjorBean("哲学类","冷门",0,"数","思考天地人的学问"));
	  CC_ZY_EXT.put("经济学类", new MarjorBean("经济学类","热门",2,"数/政","宏观经济运行，理论经济学范畴"));
	  CC_ZY_EXT.put("财政学类", new MarjorBean("财政学类","热门",4,"数/政","财政和税收"));
	  CC_ZY_EXT.put("金融学类", new MarjorBean("金融学类","热门",4,"数","资本运作，与银行有关"));
	  CC_ZY_EXT.put("经济与贸易类", new MarjorBean("经济与贸易类","普通",1,"政/外","商品流通有关"));
	  CC_ZY_EXT.put("法学类", new MarjorBean("法学类","热门",4,"政","狭义法学概念"));
	  CC_ZY_EXT.put("政治学类", new MarjorBean("政治学类","热门",3,"政","国家权利的组织与运行"));
	  CC_ZY_EXT.put("社会学类", new MarjorBean("社会学类","冷门",1,"语/政","研究群体组织的特点"));
	  CC_ZY_EXT.put("民族学类", new MarjorBean("民族学类","冷门",1,"政/历","关于种族和民族的学习"));
	  CC_ZY_EXT.put("马克思主义理论类", new MarjorBean("马克思主义理论类","热门",5,"政","教研的专业，弘扬主旋律"));
	  CC_ZY_EXT.put("公安学类", new MarjorBean("公安学类","热门",5,"政/体","维护正义，警校提前批"));
	  CC_ZY_EXT.put("教育学类", new MarjorBean("教育学类","热门",2,"语/政","不全是师范专业"));
	  CC_ZY_EXT.put("体育学类", new MarjorBean("体育学类","冷门",2,"体","强身健体"));
	  CC_ZY_EXT.put("中国语言文学类", new MarjorBean("中国语言文学类","热门",3,"语","汉语的研究和传播"));
	  CC_ZY_EXT.put("外国语言文学类", new MarjorBean("外国语言文学类","普通",2,"外","汉语以外的语言"));
	  CC_ZY_EXT.put("新闻传播学类", new MarjorBean("新闻传播学类","热门",2,"语","各类信息的传播途径和方式"));
	  CC_ZY_EXT.put("历史学类", new MarjorBean("历史学类","冷门",1,"历/政","关于过去发生事实的分析"));
	  CC_ZY_EXT.put("数学类", new MarjorBean("数学类","热门",4,"数","外物可归咎于数，转换方向强"));
	  CC_ZY_EXT.put("物理学类", new MarjorBean("物理学类","热门",3,"物","对物质的性质和形状的分析"));
	  CC_ZY_EXT.put("化学类", new MarjorBean("化学类","冷门",2,"化","物质世界的组成"));
	  CC_ZY_EXT.put("天文学类", new MarjorBean("天文学类","普通",2,"物","仰望星空的思考"));
	  CC_ZY_EXT.put("地理科学类", new MarjorBean("地理科学类","普通",1,"地","空间和地理概念"));
	  CC_ZY_EXT.put("大气科学类", new MarjorBean("大气科学类","热门",3,"物/地","大气的各种现象、应用和干预"));
	  CC_ZY_EXT.put("海洋科学类", new MarjorBean("海洋科学类","冷门",1,"物/地","海洋远比陆地深不可测"));
	  CC_ZY_EXT.put("地球物理学类", new MarjorBean("地球物理学类","冷门",1,"物/地","研究和保护地球"));
	  CC_ZY_EXT.put("地质学类", new MarjorBean("地质学类","冷门",1,"地","关于地下世界的探索"));
	  CC_ZY_EXT.put("生物科学类", new MarjorBean("生物科学类","冷门",1,"生","对生命体的解读"));
	  CC_ZY_EXT.put("心理学类", new MarjorBean("心理学类","冷门",1,"生/化","解开意识世界的奥秘"));
	  CC_ZY_EXT.put("统计学类", new MarjorBean("统计学类","热门",4,"数","数据的搜集和整理"));
	  CC_ZY_EXT.put("力学类", new MarjorBean("力学类","普通",2,"数/物","力是物质作用的方式"));
	  CC_ZY_EXT.put("机械类", new MarjorBean("机械类","普通",4,"物","机器的运作和制造"));
	  CC_ZY_EXT.put("仪器类", new MarjorBean("仪器类","热门",4,"物","精密机器"));
	  CC_ZY_EXT.put("材料类", new MarjorBean("材料类","冷门",1,"物/化","研究和制造各类材料"));
	  CC_ZY_EXT.put("能源动力类", new MarjorBean("能源动力类","热门",3,"物/化","各种能源的研究"));
	  CC_ZY_EXT.put("电气类", new MarjorBean("电气类","热门",5,"物","电气是时代的必须"));
	  CC_ZY_EXT.put("电子信息类", new MarjorBean("电子信息类","热门",5,"物/数","光机电一体化"));
	  CC_ZY_EXT.put("自动化类", new MarjorBean("自动化类","热门",5,"数/物","让机器自动和智能运作"));
	  CC_ZY_EXT.put("计算机类", new MarjorBean("计算机类","热门",5,"数","程序、数据、智能化"));
	  CC_ZY_EXT.put("土木类", new MarjorBean("土木类","热门",4,"物","如何构筑各种建筑物"));
	  CC_ZY_EXT.put("水利类", new MarjorBean("水利类","普通",2,"物","水资源的利用和开发"));
	  CC_ZY_EXT.put("测绘类", new MarjorBean("测绘类","普通",3,"物/地","测量和绘制地理信息"));
	  CC_ZY_EXT.put("化工与制药类", new MarjorBean("化工与制药类","冷门",1,"化","杂，主要是制药"));
	  CC_ZY_EXT.put("地质类", new MarjorBean("地质类","冷门",1,"物/地","探索地下世界的学问"));
	  CC_ZY_EXT.put("矿业类", new MarjorBean("矿业类","冷门",1,"物/化/地","挖掘地下的宝藏矿产"));
	  CC_ZY_EXT.put("纺织类", new MarjorBean("纺织类","冷门",1,"化","人的包装材料"));
	  CC_ZY_EXT.put("轻工类", new MarjorBean("轻工类","冷门",1,"化/物","制造人民必需的商品"));
	  CC_ZY_EXT.put("交通运输类", new MarjorBean("交通运输类","热门",4,"物","跨越距离的运动之学"));
	  CC_ZY_EXT.put("海洋工程类", new MarjorBean("海洋工程类","冷门",1,"物/数","海洋的开发和利用"));
	  CC_ZY_EXT.put("航空航天类", new MarjorBean("航空航天类","热门",4,"物/数","开启太空之旅的飞行学科"));
	  CC_ZY_EXT.put("兵器类", new MarjorBean("兵器类","普通",4,"物/数","武器和弹药技术的研究"));
	  CC_ZY_EXT.put("核工程类", new MarjorBean("核工程类","热门",4,"物/数","原子能的利用"));
	  CC_ZY_EXT.put("农业工程类", new MarjorBean("农业工程类","冷门",1,"物","为农业服务的科学"));
	  CC_ZY_EXT.put("林业工程类", new MarjorBean("林业工程类","冷门",1,"物","森林和林产的学问"));
	  CC_ZY_EXT.put("环境科学与工程类", new MarjorBean("环境科学与工程类","冷门",1,"物/化","环境保护大事业"));
	  CC_ZY_EXT.put("生物医学工程类", new MarjorBean("生物医学工程类","冷门",2,"生/化","医学器械的研究"));
	  CC_ZY_EXT.put("食品科学与工程类", new MarjorBean("食品科学与工程类","热门",2,"生/化","食品研究、开发和检测"));
	  CC_ZY_EXT.put("建筑类", new MarjorBean("建筑类","热门",2,"物/数","人居环境的美化"));
	  CC_ZY_EXT.put("安全科学与工程类", new MarjorBean("安全科学与工程类","热门",3,"物","安全管理，安全评价和风险评估"));
	  CC_ZY_EXT.put("生物工程类", new MarjorBean("生物工程类","普通",1,"生","研究疫苗、克隆、基因等"));
	  CC_ZY_EXT.put("公安技术类", new MarjorBean("公安技术类","热门",5,"物/化","警察类服务的应用，警校提前批"));
	  CC_ZY_EXT.put("植物生产类", new MarjorBean("植物生产类","冷门",2,"生","农作物种植"));
	  CC_ZY_EXT.put("自然保护与环境生态类", new MarjorBean("自然保护与环境生态类","冷门",1,"化","自然保护"));
	  CC_ZY_EXT.put("动物生产类", new MarjorBean("动物生产类","冷门",2,"生/化","动物养殖"));
	  CC_ZY_EXT.put("动物医学类", new MarjorBean("动物医学类","热门",3,"生/化","维护动物的健康"));
	  CC_ZY_EXT.put("林学类", new MarjorBean("林学类","冷门",1,"生/化","对森林的分析"));
	  CC_ZY_EXT.put("水产类", new MarjorBean("水产类","冷门",1,"生/化","研究水生物"));
	  CC_ZY_EXT.put("草学类", new MarjorBean("草学类","冷门",1,"生/化","草的种植、维护和应用"));
	  CC_ZY_EXT.put("基础医学类", new MarjorBean("基础医学类","冷门",1,"生/化","注重医学的研究，医学根本"));
	  CC_ZY_EXT.put("临床医学类", new MarjorBean("临床医学类","热门",5,"生","救死扶伤，治病就业"));
	  CC_ZY_EXT.put("口腔医学类", new MarjorBean("口腔医学类","热门",5,"生","牙科"));
	  CC_ZY_EXT.put("公共卫生与预防医学类", new MarjorBean("公共卫生与预防医学类","热门",3,"生","预防传染病"));
	  CC_ZY_EXT.put("中医学类", new MarjorBean("中医学类","普通",3,"生","传承国粹"));
	  CC_ZY_EXT.put("中西医结合类", new MarjorBean("中西医结合类","普通",3,"生","国粹与西医的结合"));
	  CC_ZY_EXT.put("药学类", new MarjorBean("药学类","普通",3,"生/化","药的研发和使用"));
	  CC_ZY_EXT.put("中药学类", new MarjorBean("中药学类","普通",2,"生","中药鉴定、分析和使用"));
	  CC_ZY_EXT.put("法医学类", new MarjorBean("法医学类","冷门",1,"生","医学和司法的结合"));
	  CC_ZY_EXT.put("医学技术类", new MarjorBean("医学技术类","热门",4,"生","辅助临床诊断"));
	  CC_ZY_EXT.put("护理学类", new MarjorBean("护理学类","热门",4,"生","临床护理和助产"));
	  CC_ZY_EXT.put("管理科学与工程类", new MarjorBean("管理科学与工程类","热门",3,"数/物","数据和信息的管理、分析和应用"));
	  CC_ZY_EXT.put("工商管理类", new MarjorBean("工商管理类","热门",3,"语","和企业、生产相关的统筹人事"));
	  CC_ZY_EXT.put("农业经济管理类", new MarjorBean("农业经济管理类","冷门",1,"语","涉及三农的经济和管理"));
	  CC_ZY_EXT.put("公共管理类", new MarjorBean("公共管理类","冷门",2,"语","管理社会和组织内外协调"));
	  CC_ZY_EXT.put("图书情报与档案管理类", new MarjorBean("图书情报与档案管理类","冷门",2,"语/政","信息的采集、整理和分类"));
	  CC_ZY_EXT.put("物流管理与工程类", new MarjorBean("物流管理与工程类","普通",2,"英","货品的采购、流通和管理"));
	  CC_ZY_EXT.put("工业工程类", new MarjorBean("工业工程类","普通",2,"数/物","工业生产的规划和设计"));
	  CC_ZY_EXT.put("电子商务类", new MarjorBean("电子商务类","普通",2,"英/数","线上交易"));
	  CC_ZY_EXT.put("旅游管理类", new MarjorBean("旅游管理类","普通",2,"英","旅游事业"));
	  CC_ZY_EXT.put("艺术学理论类", new MarjorBean("艺术学理论类","冷门",1,"语","研究中外艺术学理论"));
	  CC_ZY_EXT.put("音乐与舞蹈学类", new MarjorBean("音乐与舞蹈学类","普通",2,"舞/音","音乐、作曲和舞蹈"));
	  CC_ZY_EXT.put("戏剧与影视学类", new MarjorBean("戏剧与影视学类","普通",2,"语","不同人物形象的创作、演绎"));
	  CC_ZY_EXT.put("美术学类", new MarjorBean("美术学类","普通",2,"美","美术评论、编辑和研究"));
	  CC_ZY_EXT.put("设计学类", new MarjorBean("设计学类","热门",3,"美","设计广告、游戏、服装等"));
	    
	  HM_MAX_WC.put("安徽2022理科",286062);
	  HM_MAX_WC.put("安徽2022文科",182811);
	  HM_MAX_WC.put("安徽2021理科",232461);
	  HM_MAX_WC.put("安徽2021文科",195661);
	  HM_MAX_WC.put("安徽2020理科",226701);
	  HM_MAX_WC.put("安徽2020文科",199030);
	  HM_MAX_WC.put("北京2022综合",44958);
	  HM_MAX_WC.put("北京2021综合",42037);
	  HM_MAX_WC.put("北京2020综合",46538);
	  HM_MAX_WC.put("福建2022物理",138789);
	  HM_MAX_WC.put("福建2022历史",76094);
	  HM_MAX_WC.put("福建2021综合",77795);
	  HM_MAX_WC.put("福建2021物理",117464);
	  HM_MAX_WC.put("福建2021历史",77795);
	  HM_MAX_WC.put("福建2020理科",129067);
	  HM_MAX_WC.put("福建2020文科",69655);
	  HM_MAX_WC.put("广东2022物理",399216);
	  HM_MAX_WC.put("广东2022历史",272196);
	  HM_MAX_WC.put("广东2021综合",337988);
	  HM_MAX_WC.put("广东2021物理",337988);
	  HM_MAX_WC.put("广东2021历史",269209);
	  HM_MAX_WC.put("广东2020理科",370994);
	  HM_MAX_WC.put("广东2020文科",255185);
	  HM_MAX_WC.put("广西2022理科",226035);
	  HM_MAX_WC.put("广西2022文科",148581);
	  HM_MAX_WC.put("广西2021理科",208117);
	  HM_MAX_WC.put("广西2021文科",132326);
	  HM_MAX_WC.put("广西2020理科",200602);
	  HM_MAX_WC.put("广西2020文科",126716);
	  HM_MAX_WC.put("贵州2022理科",215043);
	  HM_MAX_WC.put("贵州2022文科",126138);
	  HM_MAX_WC.put("贵州2021理科",213286);
	  HM_MAX_WC.put("贵州2021文科",128536);
	  HM_MAX_WC.put("贵州2020理科",217889);
	  HM_MAX_WC.put("贵州2020文科",136992);
	  HM_MAX_WC.put("海南2022综合",55945);
	  HM_MAX_WC.put("海南2021综合",52977);
	  HM_MAX_WC.put("海南2020综合",50826);
	  HM_MAX_WC.put("河北2022物理",297622);
	  HM_MAX_WC.put("河北2022历史",194469);
	  HM_MAX_WC.put("河北2021物理",216153);
	  HM_MAX_WC.put("河北2021历史",202096);
	  HM_MAX_WC.put("河北2020理科",261461);
	  HM_MAX_WC.put("河北2020文科",175891);
	  HM_MAX_WC.put("河南2022理科",509762);
	  HM_MAX_WC.put("河南2022文科",332823);
	  HM_MAX_WC.put("河南2021理科",466487);
	  HM_MAX_WC.put("河南2021文科",310088);
	  HM_MAX_WC.put("河南2020理科",470378);
	  HM_MAX_WC.put("河南2020文科",355150);
	  HM_MAX_WC.put("黑龙江2022理科",115233);
	  HM_MAX_WC.put("黑龙江2022文科",61306);
	  HM_MAX_WC.put("黑龙江2021理科",105523);
	  HM_MAX_WC.put("黑龙江2021文科",59759);
	  HM_MAX_WC.put("黑龙江2020理科",116607);
	  HM_MAX_WC.put("黑龙江2020文科",66652);
	  HM_MAX_WC.put("湖北2022物理",202382);
	  HM_MAX_WC.put("湖北2022历史",130024);
	  HM_MAX_WC.put("湖北2021物理",147111);
	  HM_MAX_WC.put("湖北2021历史",149379);
	  HM_MAX_WC.put("湖北2020理科",190786);
	  HM_MAX_WC.put("湖北2020文科",113243);
	  HM_MAX_WC.put("湖南2022物理",254970);
	  HM_MAX_WC.put("湖南2022历史",174764);
	  HM_MAX_WC.put("湖南2021综合",205920);
	  HM_MAX_WC.put("湖南2021物理",205920);
	  HM_MAX_WC.put("湖南2021历史",165702);
	  HM_MAX_WC.put("湖南2020理科",217637);
	  HM_MAX_WC.put("湖南2020文科",153400);
	  HM_MAX_WC.put("吉林2022理科",82199);
	  HM_MAX_WC.put("吉林2022文科",34247);
	  HM_MAX_WC.put("吉林2021理科",68294);
	  HM_MAX_WC.put("吉林2021文科",29046);
	  HM_MAX_WC.put("吉林2020理科",90657);
	  HM_MAX_WC.put("吉林2020文科",43403);
	  HM_MAX_WC.put("江苏2022物理",164578);
	  HM_MAX_WC.put("江苏2022历史",56251);
	  HM_MAX_WC.put("江苏2021综合",55510);
	  HM_MAX_WC.put("江苏2021物理",150598);
	  HM_MAX_WC.put("江苏2021历史",55510);
	  HM_MAX_WC.put("江苏2020理科",163501);
	  HM_MAX_WC.put("江苏2020文科",53063);
	  HM_MAX_WC.put("江西2022理科",258559);
	  HM_MAX_WC.put("江西2022文科",207163);
	  HM_MAX_WC.put("江西2021理科",218661);
	  HM_MAX_WC.put("江西2021文科",182090);
	  HM_MAX_WC.put("江西2020理科",206921);
	  HM_MAX_WC.put("江西2020文科",174401);
	  HM_MAX_WC.put("辽宁2022物理",135352);
	  HM_MAX_WC.put("辽宁2022历史",50081);
	  HM_MAX_WC.put("辽宁2021综合",102737);
	  HM_MAX_WC.put("辽宁2021物理",102737);
	  HM_MAX_WC.put("辽宁2021历史",63903);
	  HM_MAX_WC.put("辽宁2020理科",132098);
	  HM_MAX_WC.put("辽宁2020文科",62175);
	  HM_MAX_WC.put("宁夏2022理科",41899);
	  HM_MAX_WC.put("宁夏2022文科",23163);
	  HM_MAX_WC.put("宁夏2021理科",23582);
	  HM_MAX_WC.put("宁夏2021文科",12742);
	  HM_MAX_WC.put("宁夏2020理科",26318);
	  HM_MAX_WC.put("宁夏2020文科",15028);
	  HM_MAX_WC.put("青海2022理科",30527);
	  HM_MAX_WC.put("青海2022文科",16683);
	  HM_MAX_WC.put("青海2021理科",30336);
	  HM_MAX_WC.put("青海2021文科",17224);
	  HM_MAX_WC.put("青海2020理科",25176);
	  HM_MAX_WC.put("青海2020文科",10835);
	  HM_MAX_WC.put("山东2022综合",594771);
	  HM_MAX_WC.put("山东2021综合",544424);
	  HM_MAX_WC.put("山东2020综合",524825);
	  HM_MAX_WC.put("山西2022理科",93503);
	  HM_MAX_WC.put("山西2022文科",25733);
	  HM_MAX_WC.put("山西2021理科",93000);
	  HM_MAX_WC.put("山西2021文科",26756);
	  HM_MAX_WC.put("山西2020理科",91213);
	  HM_MAX_WC.put("山西2020文科",26437);
	  HM_MAX_WC.put("陕西2022理科",166456);
	  HM_MAX_WC.put("陕西2022文科",85511);
	  HM_MAX_WC.put("陕西2021理科",164747);
	  HM_MAX_WC.put("陕西2021文科",86973);
	  HM_MAX_WC.put("陕西2020理科",174061);
	  HM_MAX_WC.put("陕西2020文科",89307);
	  HM_MAX_WC.put("上海2022综合",44839);
	  HM_MAX_WC.put("上海2021综合",44086);
	  HM_MAX_WC.put("上海2020综合",44940);
	  HM_MAX_WC.put("四川2022理科",284600);
	  HM_MAX_WC.put("四川2022文科",216355);
	  HM_MAX_WC.put("四川2021理科",258770);
	  HM_MAX_WC.put("四川2021文科",200308);
	  HM_MAX_WC.put("四川2020理科",260697);
	  HM_MAX_WC.put("四川2020文科",209152);
	  HM_MAX_WC.put("天津2022综合",56764);
	  HM_MAX_WC.put("天津2021综合",54025);
	  HM_MAX_WC.put("天津2020综合",54531);
	  HM_MAX_WC.put("重庆2022物理",112556);
	  HM_MAX_WC.put("重庆2022历史",69770);
	  HM_MAX_WC.put("重庆2021综合",98954);
	  HM_MAX_WC.put("重庆2021物理",98954);
	  HM_MAX_WC.put("重庆2021历史",82738);
	  HM_MAX_WC.put("重庆2020理科",113386);
	  HM_MAX_WC.put("重庆2020文科",74712);
	  HM_MAX_WC.put("新疆2022理科",49403);
	  HM_MAX_WC.put("新疆2022文科",21277);
	  HM_MAX_WC.put("新疆2021理科",35515);
	  HM_MAX_WC.put("新疆2021文科",12807);
	  HM_MAX_WC.put("新疆2020理科",48869);
	  HM_MAX_WC.put("新疆2020文科",28819);
	  HM_MAX_WC.put("甘肃2022理科",113402);
	  HM_MAX_WC.put("甘肃2022文科",82225);
	  HM_MAX_WC.put("甘肃2021理科",112852);
	  HM_MAX_WC.put("甘肃2021文科",83421);
	  HM_MAX_WC.put("甘肃2020理科",124220);
	  HM_MAX_WC.put("甘肃2020文科",87023);
  }
}
