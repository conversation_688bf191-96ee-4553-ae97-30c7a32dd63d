<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="com.career.utils.*,com.career.db.*,java.util.*,java.io.*"%>

<%@include file="/WEB-INF/include/_session_ajax_ai.jsp"%>
<%

AiCard aiCard = (AiCard)session.getAttribute(ZyzdCache.SES_KEY_AI_BASE_CARD); 

if(aiCard == null){
    response.setContentType("application/json; charset=UTF-8");
    out.print("{\"success\": false, \"message\": \"会话已过期，请重新登录\"}");
    return;
}

// 获取要删除的文件名
String fileName = Tools.trim(request.getParameter("fileName"));

if(fileName == null || fileName.isEmpty()){
    response.setContentType("application/json; charset=UTF-8");
    out.print("{\"success\": false, \"message\": \"文件名不能为空\"}");
    return;
}

// 安全检查：只允许删除xlsx文件，且文件名不能包含路径分隔符
if(!fileName.endsWith(".xlsx") || fileName.contains("..") || fileName.contains("/") || fileName.contains("\\")) {
    response.setContentType("application/json; charset=UTF-8");
    out.print("{\"success\": false, \"message\": \"不允许删除此类型文件\"}");
    return;
}

try {
    // 获取文件路径
    String servletContextPath = getServletContext().getRealPath("/");
    File fileToDelete = new File(servletContextPath, fileName);
    
    Tools.println("=== AI模块尝试删除文件: " + fileToDelete.getPath());
    
    if(fileToDelete.exists()) {
        boolean deleted = fileToDelete.delete();
        if(deleted) {
            Tools.println("=== AI模块文件删除成功: " + fileName);
            response.setContentType("application/json; charset=UTF-8");
            out.print("{\"success\": true, \"message\": \"文件删除成功\"}");
        } else {
            Tools.println("=== AI模块文件删除失败: " + fileName);
            response.setContentType("application/json; charset=UTF-8");
            out.print("{\"success\": false, \"message\": \"文件删除失败\"}");
        }
    } else {
        Tools.println("=== AI模块文件不存在: " + fileName);
        response.setContentType("application/json; charset=UTF-8");
        out.print("{\"success\": true, \"message\": \"文件不存在或已被删除\"}");
    }
    
} catch (Exception e) {
    Tools.println("=== AI模块删除文件异常: " + e.getMessage());
    e.printStackTrace();
    response.setContentType("application/json; charset=UTF-8");
    out.print("{\"success\": false, \"message\": \"删除文件时发生错误：" + e.getMessage() + "\"}");
}

%> 