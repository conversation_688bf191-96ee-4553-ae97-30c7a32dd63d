package com.career.utils.report.Test;

import com.career.db.*;
import com.career.utils.*;
import com.career.utils.report.FormReviewEvaluator;
import com.career.utils.report.FormReviewEvaluator.*;

import java.util.*;

/**
 * FormReviewEvaluator 单元测试类
 * 测试志愿表单审核核心算法
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class FormReviewEvaluatorTest {
    
    public static void main(String[] args) {
        System.out.println("=== FormReviewEvaluator 单元测试 ===");
        
        try {
            // 测试通用等级判断方法
            testDetermineGrade();
            
            // 测试基础维度评估
            testBasicDimensionEvaluation();
            
            // 测试综合评分计算
            testOverallScoreCalculation();
            
            // 测试数据完整性验证
            testDataIntegrityValidation();
            
            // 测试边界值处理
            testBoundaryValueHandling();
            
            System.out.println("=== 所有测试完成 ===");
            
        } catch (Exception e) {
            System.err.println("测试执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试通用等级判断方法
     */
    public static void testDetermineGrade() {
        System.out.println("\n--- 测试通用等级判断方法 ---");
        
        // 测试double版本
        assert FormReviewEvaluator.determineGrade(0.9, 0.85, 0.65, 0.45).equals("A") : "A级判断失败";
        assert FormReviewEvaluator.determineGrade(0.7, 0.85, 0.65, 0.45).equals("B") : "B级判断失败";
        assert FormReviewEvaluator.determineGrade(0.5, 0.85, 0.65, 0.45).equals("C") : "C级判断失败";
        assert FormReviewEvaluator.determineGrade(0.3, 0.85, 0.65, 0.45).equals("D") : "D级判断失败";
        
        // 测试int版本
        assert FormReviewEvaluator.determineGrade(90, 85, 65, 45).equals("A") : "A级判断失败(int)";
        assert FormReviewEvaluator.determineGrade(70, 85, 65, 45).equals("B") : "B级判断失败(int)";
        assert FormReviewEvaluator.determineGrade(50, 85, 65, 45).equals("C") : "C级判断失败(int)";
        assert FormReviewEvaluator.determineGrade(30, 85, 65, 45).equals("D") : "D级判断失败(int)";
        
        // 边界值测试
        assert FormReviewEvaluator.determineGrade(0.85, 0.85, 0.65, 0.45).equals("A") : "A级边界值失败";
        assert FormReviewEvaluator.determineGrade(0.65, 0.85, 0.65, 0.45).equals("B") : "B级边界值失败";
        assert FormReviewEvaluator.determineGrade(0.45, 0.85, 0.65, 0.45).equals("C") : "C级边界值失败";
        
        System.out.println("✓ 通用等级判断方法测试通过");
    }
    
    /**
     * 测试基础维度评估
     */
    public static void testBasicDimensionEvaluation() {
        System.out.println("\n--- 测试基础维度评估 ---");
        
        // 创建测试数据
        FormReviewContext context = createTestContext();
        
        // 测试分段定位评估
        String positionResult = FormReviewEvaluator.reviewPositionLevel(context);
        assert positionResult != null && Arrays.asList("A", "B", "C", "D").contains(positionResult) : "分段定位评估结果无效";
        System.out.println("✓ 分段定位评估: " + positionResult);
        
        // 测试院校完整度评估
        String completenessResult = FormReviewEvaluator.reviewCollegeCompleteness(context);
        assert completenessResult != null && Arrays.asList("A", "B", "C", "D").contains(completenessResult) : "院校完整度评估结果无效";
        System.out.println("✓ 院校完整度评估: " + completenessResult);
        
        // 测试专业服从调剂评估
        String obedienceResult = FormReviewEvaluator.reviewMajorObedience(context);
        assert obedienceResult != null && Arrays.asList("A", "B", "C", "D").contains(obedienceResult) : "专业服从调剂评估结果无效";
        System.out.println("✓ 专业服从调剂评估: " + obedienceResult);
        
        System.out.println("✓ 基础维度评估测试通过");
    }
    
    /**
     * 测试综合评分计算
     */
    public static void testOverallScoreCalculation() {
        System.out.println("\n--- 测试综合评分计算 ---");
        
        // 创建测试结果对象
        FormReviewResult result = createTestResult();
        
        // 计算综合评分
        double overallScore = FormReviewEvaluator.calculateOverallScore(result);
        
        // 验证评分在合理范围内
        assert overallScore >= 0 && overallScore <= 100 : "综合评分超出范围: " + overallScore;
        System.out.println("✓ 综合评分: " + String.format("%.2f", overallScore));
        
        // 测试不同等级组合的评分
        testScoreWithDifferentGrades();
        
        System.out.println("✓ 综合评分计算测试通过");
    }
    
    /**
     * 测试不同等级组合的评分
     */
    private static void testScoreWithDifferentGrades() {
        // 全A等级测试
        FormReviewResult allAResult = new FormReviewResult();
        setAllGrades(allAResult, "A");
        allAResult.setActualDimensionCount(15);
        double allAScore = FormReviewEvaluator.calculateOverallScore(allAResult);
        assert allAScore >= 90 : "全A等级评分过低: " + allAScore;
        System.out.println("  全A等级评分: " + String.format("%.2f", allAScore));
        
        // 全D等级测试
        FormReviewResult allDResult = new FormReviewResult();
        setAllGrades(allDResult, "D");
        allDResult.setActualDimensionCount(15);
        double allDScore = FormReviewEvaluator.calculateOverallScore(allDResult);
        assert allDScore <= 60 : "全D等级评分过高: " + allDScore;
        System.out.println("  全D等级评分: " + String.format("%.2f", allDScore));
        
        // 混合等级测试
        FormReviewResult mixedResult = new FormReviewResult();
        mixedResult.setGrade1("A"); mixedResult.setGrade2("B"); mixedResult.setGrade3("C");
        mixedResult.setGrade4("D"); mixedResult.setGrade5("A"); mixedResult.setGrade6("B");
        mixedResult.setGrade7("C"); mixedResult.setGrade8("D"); mixedResult.setGrade9("A");
        mixedResult.setGrade10("B"); mixedResult.setGrade11("C"); mixedResult.setGrade12("D");
        mixedResult.setGrade13("A"); mixedResult.setGrade14("B"); mixedResult.setGrade15("C");
        mixedResult.setActualDimensionCount(15);
        double mixedScore = FormReviewEvaluator.calculateOverallScore(mixedResult);
        assert mixedScore >= 60 && mixedScore <= 90 : "混合等级评分异常: " + mixedScore;
        System.out.println("  混合等级评分: " + String.format("%.2f", mixedScore));
    }
    
    /**
     * 测试数据完整性验证
     */
    public static void testDataIntegrityValidation() {
        System.out.println("\n--- 测试数据完整性验证 ---");
        
        // 测试空数据处理
        try {
            FormReviewContext emptyContext = new FormReviewContext(
                null, null, null, null, null, null, null, null);
            String result = FormReviewEvaluator.reviewPositionLevel(emptyContext);
            assert result.equals("D") : "空数据应返回D级";
            System.out.println("✓ 空数据处理正确");
        } catch (Exception e) {
            System.out.println("⚠ 空数据处理有异常，但程序继续运行");
        }
        
        // 测试不完整数据处理
        FormReviewContext incompleteContext = createIncompleteTestContext();
        String result = FormReviewEvaluator.reviewCollegeCompleteness(incompleteContext);
        assert result != null : "不完整数据应有返回值";
        System.out.println("✓ 不完整数据处理: " + result);
        
        System.out.println("✓ 数据完整性验证测试通过");
    }
    
    /**
     * 测试边界值处理
     */
    public static void testBoundaryValueHandling() {
        System.out.println("\n--- 测试边界值处理 ---");
        
        // 测试零值处理
        String zeroResult = FormReviewEvaluator.determineGrade(0.0, 0.85, 0.65, 0.45);
        assert zeroResult.equals("D") : "零值应返回D级";
        System.out.println("✓ 零值处理: " + zeroResult);
        
        // 测试极大值处理
        String maxResult = FormReviewEvaluator.determineGrade(1.0, 0.85, 0.65, 0.45);
        assert maxResult.equals("A") : "极大值应返回A级";
        System.out.println("✓ 极大值处理: " + maxResult);
        
        // 测试负值处理
        String negativeResult = FormReviewEvaluator.determineGrade(-0.1, 0.85, 0.65, 0.45);
        assert negativeResult.equals("D") : "负值应返回D级";
        System.out.println("✓ 负值处理: " + negativeResult);
        
        System.out.println("✓ 边界值处理测试通过");
    }
    
    /**
     * 创建测试上下文
     */
    private static FormReviewContext createTestContext() {
        // 创建测试用的SuperFormMain
        SuperFormMain superFormMain = new SuperFormMain();
        superFormMain.setScore_cj(580);
        superFormMain.setScore_wc(15000);
        superFormMain.setPc_code("01");
        
        // 创建测试用的省份配置
        ZyzdProvinceConfig provinceConfig = new ZyzdProvinceConfig();
        provinceConfig.setP_name("四川");
        provinceConfig.setLatest_year_jh(2025);
        
        // 创建测试用的志愿表单
        List<SuperForm> superFormList = createTestSuperFormList();
        
        // 创建模拟排名数据
        ZDKSRank userRank = new ZDKSRank();
        userRank.setScore(580);
        userRank.setWc(15000);
        
        return new FormReviewContext(
            superFormList, superFormMain, null, provinceConfig,
            userRank, userRank, userRank, userRank);
    }
    
    /**
     * 创建不完整的测试上下文
     */
    private static FormReviewContext createIncompleteTestContext() {
        SuperFormMain superFormMain = new SuperFormMain();
        superFormMain.setScore_cj(0); // 异常分数
        
        ZyzdProvinceConfig provinceConfig = new ZyzdProvinceConfig();
        
        List<SuperForm> incompleteList = new ArrayList<>();
        SuperForm form = new SuperForm();
        form.setYxdm(""); // 空院校代码
        form.setZyz(""); // 空专业组
        incompleteList.add(form);
        
        return new FormReviewContext(
            incompleteList, superFormMain, null, provinceConfig,
            null, null, null, null);
    }
    
    /**
     * 创建测试用的志愿表单列表
     */
    private static List<SuperForm> createTestSuperFormList() {
        List<SuperForm> list = new ArrayList<>();
        
        // 创建10个测试志愿
        for (int i = 0; i < 10; i++) {
            SuperForm form = new SuperForm();
            form.setYxdm("1000" + i);
            form.setYxmc_org("测试大学" + i);
            form.setZyz("0" + (i % 3 + 1));
            form.setZydm("08090" + i);
            form.setZymc("测试专业" + i);
            form.setSeq_no_yx(i);
            form.setAdjust_zy(String.valueOf(i % 2)); // 一半服从调剂
            list.add(form);
        }
        
        return list;
    }
    
    /**
     * 创建测试结果对象
     */
    private static FormReviewResult createTestResult() {
        FormReviewResult result = new FormReviewResult();
        
        // 设置各维度等级
        result.setGrade1("B");
        result.setGrade2("A");
        result.setGrade3("C");
        result.setGrade4("B");
        result.setGrade5("A");
        result.setGrade6("B");
        result.setGrade7("C");
        result.setGrade8("B");
        result.setGrade9("A");
        result.setGrade10("B");
        result.setGrade11("C");
        result.setGrade12("B");
        result.setGrade13("A");
        result.setGrade14("B");
        result.setGrade15("C");
        
        result.setActualDimensionCount(15);
        result.setHasUserNeedsData(true);
        
        return result;
    }
    
    /**
     * 设置所有等级为指定值
     */
    private static void setAllGrades(FormReviewResult result, String grade) {
        result.setGrade1(grade); result.setGrade2(grade); result.setGrade3(grade);
        result.setGrade4(grade); result.setGrade5(grade); result.setGrade6(grade);
        result.setGrade7(grade); result.setGrade8(grade); result.setGrade9(grade);
        result.setGrade10(grade); result.setGrade11(grade); result.setGrade12(grade);
        result.setGrade13(grade); result.setGrade14(grade); result.setGrade15(grade);
    }
} 