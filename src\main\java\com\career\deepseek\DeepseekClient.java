package com.career.deepseek;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;

import com.alibaba.fastjson2.JSONObject;

import cn.hutool.http.HttpResponse;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class DeepseekClient {

	// 替换为你的DeepSeek API密钥
    private static final String API_KEY = "sk-rnuzkntasybunssosmqeihdkdmyxzpkgkmtohzeqozskpghx";
    private static final String API_URL = "https://api.siliconflow.cn/v1/chat/completions"; // 替换为实际的API端点

    public static void main(String[] args) {
    	try {
			new DeepseekClient().t1();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
    }
    
    
    OkHttpClient client = new OkHttpClient();
    public static final MediaType JSON= MediaType.get("application/json; charset=utf-8");
    
    public void t1() throws Exception {
        String json= "{\n  \"model\": \"deepseek-ai/DeepSeek-R1-Distill-Llama-70B\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"中国大模型行业2025年将会迎来哪些机遇和挑战？\"\n    }\n  ],\n  \"stream\": false,\n  \"max_tokens\": 512,\n  \"stop\": [\n    \"null\"\n  ],\n  \"temperature\": 0.7,\n  \"top_p\": 0.7,\n  \"top_k\": 50,\n  \"frequency_penalty\": 0.5,\n  \"n\": 1,\n  \"response_format\": {\n    \"type\": \"text\"\n  },\n  \"tools\": [\n    {\n      \"type\": \"function\",\n      \"function\": {\n        \"description\": \"<string>\",\n        \"name\": \"<string>\",\n        \"parameters\": {},\n        \"strict\": false\n      }\n    }\n  ]\n}";
        
        RequestBody body = RequestBody.create(json, JSON);
        
        Request request = new Request.Builder()
                .addHeader ( "Authorization", "Bearer " +API_KEY)//Bearer 必须带后面是自己的key
                .url(API_URL)
                .post(body)
                .build();
        
        Response response = client.newCall(request).execute();
        
        System.out.println ( response.body().string());
    }

}
