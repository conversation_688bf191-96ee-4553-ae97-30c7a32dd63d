package com.career.utils;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.career.db.JDBCConstants;

/**
 * 计划数据验证器
 * 用于验证 s{prefix}_jh_2025 和 s{prefix}_jh_2025x 两个表的数据一致性
 * 
 * <AUTHOR>
 */
public class PlanDataValidator {
    
    private static final String URL = JDBCConstants.URL;
    private static final String USER = JDBCConstants.USER;
    private static final String PASSWD = JDBCConstants.PASSWD;
    
    // 配置参数
    private int maxDisplayCount = 10; // 默认最多显示10个问题，-1表示显示全部
    
    // 日志文件相关
    private static PrintWriter logWriter;
    private static final String LOG_FILE_NAME = "plan_validator_log.md";
    
    static {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        
        // 初始化日志文件
        initializeLogger();
    }
    
    /**
     * 
     * 主方法，用于测试验证功能
     * 
     */
    public static void main(String[] args) {
    	
    	ValidationResult result = null;
    	
    	
        // 配置显示数量（可选）
        // validator.setMaxDisplayCount(5);    // 只显示前5个问题
        // validator.setMaxDisplayCount(-1);   // 显示全部问题
        // validator.setMaxDisplayCount(20);   // 显示前20个问题
        // validator.showAllProblems();        // 显示全部问题（便捷方法）
        // validator.showSummaryOnly();        // 只显示摘要，不显示详细问题
    	
    	PlanDataValidator validator = new PlanDataValidator();
    	validator.showAllProblems();

        // 测试四川省数据
        String[] provinces = {"s5_sc"};
        for (String province : provinces) {
        	result = validator.validatePlanData(province);
        }
        
        // 显示问题统计（可选）
        if (result.getProblemStats() != null && !result.getProblemStats().isEmpty()) {
            validator.printProblemStatistics(result.getProblemStats());
        }
        
        // 输出数据验证最终报告（控制台 + 日志文件）
        validator.displayFinalReport(result);
        
        // 关闭日志文件
        PlanDataValidator.closeLogger();
    }
    
   
    
    /**
     * 验证指定省份的计划数据一致性
     * 
     * @param provincePrefix 省份前缀，如 "s5_sc"
     * @return 验证结果
     */
    public ValidationResult validatePlanData(String provincePrefix) {
        // Tools.println("=== 开始验证计划数据一致性 ===");
        // Tools.println("省份前缀: " + provincePrefix);
        
        ValidationResult result = new ValidationResult();
        
        try {
            // 1. 查询源表数据 (GROUP BY 聚合查询)
            List<PlanDataRecord> sourceData = querySourceTableData(provincePrefix);
            // Tools.println("源表查询完成，记录数: " + sourceData.size());
            result.setSourceTableCount(sourceData.size());
            
            // 2. 查询目标表数据 (直接查询)
            List<PlanDataRecord> targetData = queryTargetTableData(provincePrefix);
            // Tools.println("目标表查询完成，记录数: " + targetData.size());
            result.setTargetTableCount(targetData.size());
            
            // 3. 数据一致性比较
            compareDataConsistency(sourceData, targetData, result);
            
            // 4. 生成验证摘要
            generateValidationSummary(result);
            
        } catch (Exception e) {
            // Tools.println("验证过程中发生异常: " + e.getMessage());
            result.addErrorMessage("验证过程异常: " + e.getMessage());
            result.setConsistent(false);
            e.printStackTrace();
        }
        
        // Tools.println("=== 验证完成 ===");
        return result;
    }
    
    /**
     * 查询源表数据 (GROUP BY 聚合查询)
     */
    private List<PlanDataRecord> querySourceTableData(String provincePrefix) throws SQLException {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<PlanDataRecord> records = new ArrayList<>();
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            String tableName = provincePrefix + "_jh_2025";
            String sql = "SELECT x.pc, x.pc_code, x.xk_code_org, x.yxdm, x.zyz, " +
                        "SUM(jhs) AS zyzjhs, SUM(ZNZYS) AS ZYS, MIN(zdf_2024) AS zdf_2024, " +
                        "MAX(zdfwc_2024) AS zdfwc_2024, MAX(is_hz) AS is_hz " +
                        "FROM " + tableName + " x " +
                        "GROUP BY x.pc, x.pc_code, x.xk_code_org, x.yxdm, x.zyz " +
                        "ORDER BY SUM(jhs) DESC";
            
            // Tools.println("源表SQL: " + sql);
            ps = conn.prepareStatement(sql);
            // SQLLogUtils.printSQL(" ===querySourceTableData : ", ps);
            
            rs = ps.executeQuery();
            
            while (rs.next()) {
                PlanDataRecord record = new PlanDataRecord();
                record.setPc(rs.getString("pc"));
                record.setPcCode(rs.getString("pc_code"));
                record.setXkCodeOrg(rs.getString("xk_code_org"));
                record.setYxdm(rs.getString("yxdm"));
                record.setZyz(rs.getString("zyz"));
                record.setZyzjhs(rs.getString("zyzjhs"));
                record.setZys(rs.getString("ZYS"));
                record.setZdf2024(rs.getString("zdf_2024"));
                record.setZdfwc2024(rs.getString("zdfwc_2024"));
                record.setIsHz(rs.getString("is_hz"));
                
                records.add(record);
            }
            
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return records;
    }
    
    /**
     * 查询目标表数据 (直接查询)
     */
    private List<PlanDataRecord> queryTargetTableData(String provincePrefix) throws SQLException {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<PlanDataRecord> records = new ArrayList<>();
        
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWD);
            
            String tableName = provincePrefix + "_jh_2025x";
            String sql = "SELECT x.pc, x.pc_code, x.xk_code_org, x.yxdm, x.zyz, " +
                        "zyzjhs, ZYS, zdf_2024, zdfwc_2024, is_hz " +
                        "FROM " + tableName + " x " +
                        "ORDER BY CAST(jhs AS SIGNED) DESC";
            
            // Tools.println("目标表SQL: " + sql);
            ps = conn.prepareStatement(sql);
            // SQLLogUtils.printSQL(" ===queryTargetTableData : ", ps);
            
            rs = ps.executeQuery();
            
            while (rs.next()) {
                PlanDataRecord record = new PlanDataRecord();
                record.setPc(rs.getString("pc"));
                record.setPcCode(rs.getString("pc_code"));
                record.setXkCodeOrg(rs.getString("xk_code_org"));
                record.setYxdm(rs.getString("yxdm"));
                record.setZyz(rs.getString("zyz"));
                record.setZyzjhs(rs.getString("zyzjhs"));
                record.setZys(rs.getString("ZYS"));
                record.setZdf2024(rs.getString("zdf_2024"));
                record.setZdfwc2024(rs.getString("zdfwc_2024"));
                record.setIsHz(rs.getString("is_hz"));
                
                records.add(record);
            }
            
        } finally {
            closeAllConnection(conn, ps, rs);
        }
        
        return records;
    }
    
    /**
     * 比较两个数据集的一致性
     */
    private void compareDataConsistency(List<PlanDataRecord> sourceData, List<PlanDataRecord> targetData, 
                                      ValidationResult result) {
        // Tools.println("=== 开始数据一致性比较 ===");
        
        // 将目标数据转换为Map以便快速查找
        Map<String, PlanDataRecord> targetMap = new HashMap<>();
        for (PlanDataRecord record : targetData) {
            targetMap.put(record.getRecordKey(), record);
        }
        
        // 问题统计 - 改为更详细的统计
        Map<String, Integer> problemStats = new HashMap<>();
        problemStats.put("目标表缺失记录", 0);
        problemStats.put("源表缺失记录", 0);
        problemStats.put("zyzjhs数据类型差异", 0);
        problemStats.put("is_hz空值问题", 0);
        
        // 详细字段统计
        Map<String, Integer> fieldStats = new HashMap<>();
        fieldStats.put("zyzjhs字段不一致", 0);
        fieldStats.put("ZYS字段不一致", 0);
        fieldStats.put("zdf_2024字段不一致", 0);
        fieldStats.put("zdfwc_2024字段不一致", 0);
        fieldStats.put("is_hz字段不一致", 0);
        fieldStats.put("其他未知字段不一致", 0);
        
        int inconsistentCount = 0;
        
        // 检查源数据中的每条记录
        for (PlanDataRecord sourceRecord : sourceData) {
            String recordKey = sourceRecord.getRecordKey();
            PlanDataRecord targetRecord = targetMap.get(recordKey);
            
            if (targetRecord == null) {
                // 目标表中缺失记录
                String errorMsg = String.format("【目标表缺失】%s", recordKey);
                result.addInconsistentRecord(errorMsg);
                problemStats.put("目标表缺失记录", problemStats.get("目标表缺失记录") + 1);
                inconsistentCount++;
            } else {
                // 比较字段值并分类问题
                List<ValidationIssue> issues = compareRecordFieldsDetailed(sourceRecord, targetRecord);
                if (!issues.isEmpty()) {
                    StringBuilder errorMsg = new StringBuilder();
                    errorMsg.append(String.format("【字段不一致】%s\n", recordKey));
                    
                    for (ValidationIssue issue : issues) {
                        errorMsg.append(String.format("    ├─ %s: %s → %s (%s)\n", 
                                issue.getFieldName(), issue.getSourceValue(), 
                                issue.getTargetValue(), issue.getIssueType()));
                        
                        // 统计问题类型 - 更详细的分类
                        updateProblemStatistics(issue, problemStats, fieldStats);
                    }
                    
                    result.addInconsistentRecord(errorMsg.toString());
                    inconsistentCount++;
                }
                // 从map中移除已匹配的记录
                targetMap.remove(recordKey);
            }
        }
        
        // 检查目标表中多余的记录
        for (String remainingKey : targetMap.keySet()) {
            String errorMsg = String.format("【源表缺失】%s", remainingKey);
            result.addInconsistentRecord(errorMsg);
            problemStats.put("源表缺失记录", problemStats.get("源表缺失记录") + 1);
            inconsistentCount++;
        }
        
        // 设置一致性结果
        boolean isConsistent = inconsistentCount == 0;
        result.setConsistent(isConsistent);
        
        // 合并统计结果
        problemStats.putAll(fieldStats);
        
        // 保存问题统计到结果中
        result.setProblemStats(problemStats);
        
        // Tools.println("数据一致性比较完成，不一致记录数: " + inconsistentCount);
        // printProblemStatistics(problemStats);
    }
    
    /**
     * 验证问题详情类
     */
    public static class ValidationIssue {
        private String fieldName;
        private String sourceValue;
        private String targetValue;
        private String issueType;
        
        public ValidationIssue(String fieldName, String sourceValue, String targetValue, String issueType) {
            this.fieldName = fieldName;
            this.sourceValue = sourceValue;
            this.targetValue = targetValue;
            this.issueType = issueType;
        }
        
        // Getters
        public String getFieldName() { return fieldName; }
        public String getSourceValue() { return sourceValue; }
        public String getTargetValue() { return targetValue; }
        public String getIssueType() { return issueType; }
    }
    
    /**
     * 比较两条记录的字段值（详细版本）
     */
    private List<ValidationIssue> compareRecordFieldsDetailed(PlanDataRecord source, PlanDataRecord target) {
        List<ValidationIssue> issues = new ArrayList<>();
        
        // 比较zyzjhs字段（数值类型问题）
        if (!isNumericEqual(source.getZyzjhs(), target.getZyzjhs())) {
            String issueType = isNumericFormatIssue(source.getZyzjhs(), target.getZyzjhs()) ? 
                    "数值格式差异" : "数值不一致";
            issues.add(new ValidationIssue("zyzjhs", source.getZyzjhs(), target.getZyzjhs(), issueType));
        }
        
        // 比较ZYS字段
        if (!isFieldEqual(source.getZys(), target.getZys())) {
            issues.add(new ValidationIssue("ZYS", source.getZys(), target.getZys(), "数值不一致"));
        }
        
        // 比较zdf_2024字段
        if (!isFieldEqual(source.getZdf2024(), target.getZdf2024())) {
            issues.add(new ValidationIssue("zdf_2024", source.getZdf2024(), target.getZdf2024(), "数值不一致"));
        }
        
        // 比较zdfwc_2024字段
        if (!isFieldEqual(source.getZdfwc2024(), target.getZdfwc2024())) {
            issues.add(new ValidationIssue("zdfwc_2024", source.getZdfwc2024(), target.getZdfwc2024(), "数值不一致"));
        }
        
        // 比较is_hz字段（null值问题）
        if (!isNullSafeEqual(source.getIsHz(), target.getIsHz())) {
            String issueType = (source.getIsHz() != null && target.getIsHz() == null) || 
                              (source.getIsHz() == null && target.getIsHz() != null) ? 
                    "空值处理问题" : "数值不一致";
            issues.add(new ValidationIssue("is_hz", source.getIsHz(), target.getIsHz(), issueType));
        }
        
        return issues;
    }
    
    /**
     * 比较两条记录的字段值（兼容旧版本）
     */
    private List<String> compareRecordFields(PlanDataRecord source, PlanDataRecord target) {
        List<String> differences = new ArrayList<>();
        
        // 比较各个字段
        if (!isFieldEqual(source.getZyzjhs(), target.getZyzjhs())) {
            differences.add(String.format("zyzjhs不一致(源:%s,目标:%s)", source.getZyzjhs(), target.getZyzjhs()));
        }
        
        if (!isFieldEqual(source.getZys(), target.getZys())) {
            differences.add(String.format("ZYS不一致(源:%s,目标:%s)", source.getZys(), target.getZys()));
        }
        
        if (!isFieldEqual(source.getZdf2024(), target.getZdf2024())) {
            differences.add(String.format("zdf_2024不一致(源:%s,目标:%s)", source.getZdf2024(), target.getZdf2024()));
        }
        
        if (!isFieldEqual(source.getZdfwc2024(), target.getZdfwc2024())) {
            differences.add(String.format("zdfwc_2024不一致(源:%s,目标:%s)", source.getZdfwc2024(), target.getZdfwc2024()));
        }
        
        if (!isFieldEqual(source.getIsHz(), target.getIsHz())) {
            differences.add(String.format("is_hz不一致(源:%s,目标:%s)", source.getIsHz(), target.getIsHz()));
        }
        
        return differences;
    }
    
    /**
     * 比较两个字段值是否相等（处理null值）
     */
    private boolean isFieldEqual(String value1, String value2) {
        if (value1 == null && value2 == null) {
            return true;
        }
        if (value1 == null || value2 == null) {
            return false;
        }
        return value1.trim().equals(value2.trim());
    }
    
    /**
     * 数值比较（处理格式差异，如1.0 vs 1）
     */
    private boolean isNumericEqual(String value1, String value2) {
        if (value1 == null && value2 == null) {
            return true;
        }
        if (value1 == null || value2 == null) {
            return false;
        }
        
        try {
            double num1 = Double.parseDouble(value1.trim());
            double num2 = Double.parseDouble(value2.trim());
            return Math.abs(num1 - num2) < 0.0001; // 浮点数比较
        } catch (NumberFormatException e) {
            // 如果不是数字，则按字符串比较
            return value1.trim().equals(value2.trim());
        }
    }
    
    /**
     * 检查是否是数值格式问题（如1.0 vs 1）
     */
    private boolean isNumericFormatIssue(String value1, String value2) {
        if (value1 == null || value2 == null) {
            return false;
        }
        
        try {
            double num1 = Double.parseDouble(value1.trim());
            double num2 = Double.parseDouble(value2.trim());
            return Math.abs(num1 - num2) < 0.0001 && !value1.trim().equals(value2.trim());
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 空值安全比较（处理0 vs null的情况）
     */
    private boolean isNullSafeEqual(String value1, String value2) {
        // 特殊处理：0和null在某些业务场景下可能等价
        if ((value1 == null || value1.trim().isEmpty()) && 
            (value2 == null || value2.trim().isEmpty())) {
            return true;
        }
        
        // 处理0和null的等价性
        if ((value1 != null && value1.trim().equals("0") && value2 == null) ||
            (value2 != null && value2.trim().equals("0") && value1 == null)) {
            return false; // 这里返回false是为了标识这是一个需要注意的差异
        }
        
        return isFieldEqual(value1, value2);
    }
    
    /**
     * 更新问题统计信息
     */
    private void updateProblemStatistics(ValidationIssue issue, Map<String, Integer> problemStats, Map<String, Integer> fieldStats) {
        String fieldName = issue.getFieldName();
        String issueType = issue.getIssueType();
        
        // 根据字段名和问题类型进行详细分类
        if (fieldName.equals("zyzjhs")) {
            if (issueType.equals("数值格式差异")) {
                problemStats.put("zyzjhs数据类型差异", problemStats.get("zyzjhs数据类型差异") + 1);
            } else {
                fieldStats.put("zyzjhs字段不一致", fieldStats.get("zyzjhs字段不一致") + 1);
            }
        } else if (fieldName.equals("is_hz")) {
            if (issueType.equals("空值处理问题")) {
                problemStats.put("is_hz空值问题", problemStats.get("is_hz空值问题") + 1);
            } else {
                fieldStats.put("is_hz字段不一致", fieldStats.get("is_hz字段不一致") + 1);
            }
        } else if (fieldName.equals("ZYS")) {
            fieldStats.put("ZYS字段不一致", fieldStats.get("ZYS字段不一致") + 1);
        } else if (fieldName.equals("zdf_2024")) {
            fieldStats.put("zdf_2024字段不一致", fieldStats.get("zdf_2024字段不一致") + 1);
        } else if (fieldName.equals("zdfwc_2024")) {
            fieldStats.put("zdfwc_2024字段不一致", fieldStats.get("zdfwc_2024字段不一致") + 1);
        } else {
            // 这种情况理论上不应该出现，因为我们只对比5个字段
            fieldStats.put("其他未知字段不一致", fieldStats.get("其他未知字段不一致") + 1);
            logFileOnly("⚠️ 发现未预期的字段: " + fieldName + ", 问题类型: " + issueType);
        }
    }
    
    /**
     * 获取统计键值（保留兼容性）
     */
    private String getStatKey(String issueType) {
        switch (issueType) {
            case "数值格式差异":
                return "zyzjhs数据类型差异";
            case "空值处理问题":
                return "is_hz空值问题";
            default:
                return "其他字段不一致";
        }
    }
    
    /**
     * 打印问题统计信息
     */
    public void printProblemStatistics(Map<String, Integer> problemStats) {
        logMessage("\n" + repeatString("=", 70));
        logMessage("📊 问题类型详细统计");
        logMessage(repeatString("=", 70));
        
        int totalProblems = 0;
        
        // 1. 数据缺失问题
        logMessage("\n🚨 数据缺失问题:");
        int missingCount = 0;
        String[] missingTypes = {"目标表缺失记录", "源表缺失记录"};
        for (String type : missingTypes) {
            if (problemStats.containsKey(type) && problemStats.get(type) > 0) {
                String icon = getIconForProblemType(type);
                logMessage(String.format("   %s %s: %,d 个", icon, type, problemStats.get(type)));
                missingCount += problemStats.get(type);
                totalProblems += problemStats.get(type);
            }
        }
        if (missingCount == 0) {
            logMessage("   ✅ 无数据缺失问题");
        }
        
        // 2. 数据类型/格式问题
        logMessage("\n🔧 数据类型/格式问题:");
        int formatCount = 0;
        String[] formatTypes = {"zyzjhs数据类型差异", "is_hz空值问题"};
        for (String type : formatTypes) {
            if (problemStats.containsKey(type) && problemStats.get(type) > 0) {
                String icon = getIconForProblemType(type);
                logMessage(String.format("   %s %s: %,d 个", icon, type, problemStats.get(type)));
                formatCount += problemStats.get(type);
                totalProblems += problemStats.get(type);
            }
        }
        if (formatCount == 0) {
            logMessage("   ✅ 无数据类型/格式问题");
        }
        
        // 3. 字段值不一致问题
        logMessage("\n📋 字段值不一致问题:");
        int fieldCount = 0;
        String[] fieldTypes = {"ZYS字段不一致", "zdf_2024字段不一致", "zdfwc_2024字段不一致", "其他未知字段不一致"};
        for (String type : fieldTypes) {
            if (problemStats.containsKey(type) && problemStats.get(type) > 0) {
                String icon = getIconForProblemType(type);
                logMessage(String.format("   %s %s: %,d 个", icon, type, problemStats.get(type)));
                fieldCount += problemStats.get(type);
                totalProblems += problemStats.get(type);
            }
        }
        if (fieldCount == 0) {
            logMessage("   ✅ 无字段值不一致问题");
        }
        
        logMessage("\n" + repeatString("-", 70));
        logMessage(String.format("🔍 问题总计: %,d 个", totalProblems));
        logMessage(String.format("   ├─ 数据缺失: %,d 个", missingCount));
        logMessage(String.format("   ├─ 格式问题: %,d 个", formatCount));
        logMessage(String.format("   └─ 字段不一致: %,d 个", fieldCount));
        logMessage(repeatString("=", 70));
        
        // 提供详细的修复建议
        printDetailedFixSuggestions(problemStats);
    }
    
    /**
     * 打印详细的修复建议
     */
    private void printDetailedFixSuggestions(Map<String, Integer> problemStats) {
        boolean hasSuggestions = false;
        
        // zyzjhs数据类型差异
        if (problemStats.containsKey("zyzjhs数据类型差异") && problemStats.get("zyzjhs数据类型差异") > 0) {
            if (!hasSuggestions) {
                logMessage("\n" + repeatString("=", 70));
                logMessage("💡 详细修复建议");
                logMessage(repeatString("=", 70));
                hasSuggestions = true;
            }
            logMessage("\n🔢 zyzjhs数据类型差异 (" + problemStats.get("zyzjhs数据类型差异") + " 个):");
            logMessage("   📋 问题描述: 源表SUM(jhs)返回DECIMAL类型(如1.0)，目标表存储为整数(如1)");
            logMessage("   🔧 修复方案: 在目标表更新时统一数据类型格式");
            logMessage("   📝 SQL示例: CAST(SUM(jhs) AS SIGNED) 或在目标表中使用DECIMAL类型");
        }
        
        // is_hz空值问题
        if (problemStats.containsKey("is_hz空值问题") && problemStats.get("is_hz空值问题") > 0) {
            if (!hasSuggestions) {
                logMessage("\n" + repeatString("=", 70));
                logMessage("💡 详细修复建议");
                logMessage(repeatString("=", 70));
                hasSuggestions = true;
            }
            logMessage("\n❓ is_hz空值问题 (" + problemStats.get("is_hz空值问题") + " 个):");
            logMessage("   📋 问题描述: 源表is_hz字段有默认值0，目标表对应字段为NULL");
            logMessage("   🔧 修复方案: 统一空值处理逻辑");
            logMessage("   📝 SQL示例: COALESCE(is_hz, 0) 或在目标表中设置默认值");
        }
        
        // ZYS字段不一致
        if (problemStats.containsKey("ZYS字段不一致") && problemStats.get("ZYS字段不一致") > 0) {
            if (!hasSuggestions) {
                logMessage("\n" + repeatString("=", 70));
                logMessage("💡 详细修复建议");
                logMessage(repeatString("=", 70));
                hasSuggestions = true;
            }
            logMessage("\n📊 ZYS字段不一致 (" + problemStats.get("ZYS字段不一致") + " 个):");
            logMessage("   📋 问题描述: 源表SUM(ZNZYS)与目标表ZYS字段值不匹配");
            logMessage("   🔧 修复方案: 检查ZNZYS字段的聚合逻辑和目标表的更新逻辑");
            logMessage("   📝 建议检查: 1)源表ZNZYS字段是否有NULL值 2)目标表ZYS更新算法");
        }
        
        // zdf_2024字段不一致
        if (problemStats.containsKey("zdf_2024字段不一致") && problemStats.get("zdf_2024字段不一致") > 0) {
            if (!hasSuggestions) {
                logMessage("\n" + repeatString("=", 70));
                logMessage("💡 详细修复建议");
                logMessage(repeatString("=", 70));
                hasSuggestions = true;
            }
            logMessage("\n📈 zdf_2024字段不一致 (" + problemStats.get("zdf_2024字段不一致") + " 个):");
            logMessage("   📋 问题描述: 源表MIN(zdf_2024)与目标表zdf_2024字段值不匹配");
            logMessage("   🔧 修复方案: 检查MIN函数的处理逻辑，特别是NULL值和0值的处理");
            logMessage("   📝 建议检查: 1)源表中zdf_2024字段的数据质量 2)MIN函数是否正确处理非数字值");
        }
        
        // zdfwc_2024字段不一致
        if (problemStats.containsKey("zdfwc_2024字段不一致") && problemStats.get("zdfwc_2024字段不一致") > 0) {
            if (!hasSuggestions) {
                logMessage("\n" + repeatString("=", 70));
                logMessage("💡 详细修复建议");
                logMessage(repeatString("=", 70));
                hasSuggestions = true;
            }
            logMessage("\n📉 zdfwc_2024字段不一致 (" + problemStats.get("zdfwc_2024字段不一致") + " 个):");
            logMessage("   📋 问题描述: 源表MAX(zdfwc_2024)与目标表zdfwc_2024字段值不匹配");
            logMessage("   🔧 修复方案: 检查MAX函数的处理逻辑和NULL值处理");
            logMessage("   📝 建议检查: 1)源表中zdfwc_2024字段的数据质量 2)MAX函数是否正确处理NULL值");
        }
        
        // 其他未知字段不一致
        if (problemStats.containsKey("其他未知字段不一致") && problemStats.get("其他未知字段不一致") > 0) {
            if (!hasSuggestions) {
                logMessage("\n" + repeatString("=", 70));
                logMessage("💡 详细修复建议");
                logMessage(repeatString("=", 70));
                hasSuggestions = true;
            }
            logMessage("\n🔧 其他未知字段不一致 (" + problemStats.get("其他未知字段不一致") + " 个):");
            logMessage("   📋 问题描述: 发现了未预期的字段不一致问题");
            logMessage("   🔧 修复方案: 需要查看详细问题清单，确定具体是哪些字段");
            logMessage("   📝 建议: 联系开发人员进一步分析这些未知的字段差异");
        }
        
        if (hasSuggestions) {
            logMessage("\n" + repeatString("=", 70));
        }
    }
    
    /**
     * 获取问题类型对应的图标
     */
    private String getIconForProblemType(String problemType) {
        switch (problemType) {
            case "目标表缺失记录":
                return "❌";
            case "源表缺失记录":
                return "⚠️";
            case "zyzjhs数据类型差异":
                return "🔢";
            case "is_hz空值问题":
                return "❓";
            case "ZYS字段不一致":
                return "📊";
            case "zdf_2024字段不一致":
                return "📈";
            case "zdfwc_2024字段不一致":
                return "📉";
            case "其他未知字段不一致":
                return "🔧";
            default:
                return "🔧";
        }
    }
    
    /**
     * 显示最终验证报告（控制台 + 日志文件）
     * @param result 验证结果
     */
    public void displayFinalReport(ValidationResult result) {
        // 输出报告头部
        logMessage("\n" + repeatString("=", 80));
        logMessage("🎯 数据验证最终报告");
        logMessage(repeatString("=", 80));
        
        String statusIcon = result.isConsistent() ? "✅" : "❌";
        String statusText = result.isConsistent() ? "数据一致" : "发现数据不一致";
        
        logMessage(String.format("%s 验证状态: %s", statusIcon, statusText));
        logMessage(String.format("📊 源表记录数: %,d", result.getSourceTableCount()));
        logMessage(String.format("📊 目标表记录数: %,d", result.getTargetTableCount()));
        logMessage(String.format("⚠️  问题记录数: %,d", result.getInconsistentRecords().size()));
        
        // 如果有问题，显示详细问题清单
        if (!result.isConsistent()) {
            displayDetailedProblems(result);
        }
        
        logMessage("\n" + repeatString("=", 80));
        
        // 额外的详细信息只写入日志文件
        writeDetailedLogInfo(result);
    }
    
    /**
     * 写入详细的日志信息（仅文件）
     */
    private void writeDetailedLogInfo(ValidationResult result) {
        logFileOnly("\n## 详细验证信息");
        logFileOnly("- 验证时间: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        logFileOnly("- 源表记录数: " + result.getSourceTableCount());
        logFileOnly("- 目标表记录数: " + result.getTargetTableCount());
        logFileOnly("- 数据一致性: " + (result.isConsistent() ? "一致" : "不一致"));
        
        if (result.getProblemStats() != null) {
            logFileOnly("\n### 问题统计详情");
            for (Map.Entry<String, Integer> entry : result.getProblemStats().entrySet()) {
                if (entry.getValue() > 0) {
                    logFileOnly("- " + entry.getKey() + ": " + entry.getValue() + " 个");
                }
            }
        }
        
        if (!result.getErrorMessages().isEmpty()) {
            logFileOnly("\n### 错误信息");
            for (String error : result.getErrorMessages()) {
                logFileOnly("- " + error);
            }
        }
        
        // 如果有大量问题，将所有问题写入日志文件
        if (!result.getInconsistentRecords().isEmpty()) {
            logFileOnly("\n### 完整问题清单");
            for (int i = 0; i < result.getInconsistentRecords().size(); i++) {
                String record = result.getInconsistentRecords().get(i);
                logFileOnly(String.format("\n[问题 %d]", i + 1));
                logFileOnly(record);
            }
        }
    }
    
    /**
     * 显示详细问题清单
     * @param result 验证结果
     */
    public void displayDetailedProblems(ValidationResult result) {
        if (result.getInconsistentRecords().isEmpty()) {
            return;
        }
        
        // 如果设置为0，则不显示详细问题
        if (maxDisplayCount == 0) {
            logMessage("\n💡 详细问题清单已隐藏 (共 " + result.getInconsistentRecords().size() + " 个问题)");
            logMessage("   使用 setMaxDisplayCount(数量) 或 showAllProblems() 可显示详细问题");
            return;
        }
        
        logMessage("\n" + repeatString("=", 80));
        logMessage("🔍 详细问题清单");
        logMessage(repeatString("=", 80));
        
        int totalProblems = result.getInconsistentRecords().size();
        int displayCount;
        
        if (maxDisplayCount == -1) {
            // 显示全部
            displayCount = totalProblems;
            logMessage(String.format("显示全部 %,d 个问题:", totalProblems));
        } else {
            // 限制显示数量
            displayCount = Math.min(maxDisplayCount, totalProblems);
            logMessage(String.format("显示前 %d 个问题 (共 %,d 个):", displayCount, totalProblems));
        }
        
        for (int i = 0; i < displayCount; i++) {
            String record = result.getInconsistentRecords().get(i);
            logMessage(String.format("\n[问题 %d]", i + 1));
            logMessage(record);
        }
        
        if (maxDisplayCount > 0 && totalProblems > maxDisplayCount) {
            logMessage(String.format("\n... 还有 %,d 个问题未显示 ...", totalProblems - maxDisplayCount));
            logMessage("💡 提示: 使用 setMaxDisplayCount(-1) 或 showAllProblems() 可显示全部问题");
        }
    }
    
    /**
     * 生成验证摘要
     */
    private void generateValidationSummary(ValidationResult result) {
        StringBuilder summary = new StringBuilder();
        summary.append("=== 数据验证摘要 ===\n");
        summary.append("源表记录数: ").append(result.getSourceTableCount()).append("\n");
        summary.append("目标表记录数: ").append(result.getTargetTableCount()).append("\n");
        summary.append("数据一致性: ").append(result.isConsistent() ? "一致" : "不一致").append("\n");
        summary.append("不一致记录数: ").append(result.getInconsistentRecords().size()).append("\n");
        
        if (!result.isConsistent()) {
            summary.append("\n=== 不一致记录详情 ===\n");
            for (int i = 0; i < result.getInconsistentRecords().size(); i++) {
                summary.append(String.format("[%d] %s\n", i + 1, result.getInconsistentRecords().get(i)));
            }
        }
        
        if (!result.getErrorMessages().isEmpty()) {
            summary.append("\n=== 错误信息 ===\n");
            for (String error : result.getErrorMessages()) {
                summary.append("- ").append(error).append("\n");
            }
        }
        
        result.setValidationSummary(summary.toString());
        // Tools.println(summary.toString());
    }
    
    /**
     * 重复字符串（兼容Java 8）
     */
    private static String repeatString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
    
    /**
     * 关闭数据库连接
     */
    private static void closeAllConnection(Connection conn, PreparedStatement ps, ResultSet rs) {
        try {
            if (rs != null) rs.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        try {
            if (ps != null) ps.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        try {
            if (conn != null) conn.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
    
    
 
    /**
     * 初始化日志系统
     */
    private static void initializeLogger() {
        try {
            File logFile = new File(LOG_FILE_NAME);
            logWriter = new PrintWriter(new FileWriter(logFile, true), true);
            
            // 写入分隔符，标识新的验证会话
            String timestamp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            logWriter.println("\n" + repeatString("=", 100));
            logWriter.println("# 数据验证会话开始 - " + timestamp);
            logWriter.println(repeatString("=", 100));
            
            System.out.println("日志系统初始化完成，日志文件: " + logFile.getAbsolutePath());
        } catch (IOException e) {
            System.err.println("无法创建日志文件: " + e.getMessage());
        }
    }
    
    /**
     * 关闭日志文件
     */
    public static void closeLogger() {
        if (logWriter != null) {
            String timestamp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            logWriter.println("\n# 数据验证会话结束 - " + timestamp);
            logWriter.println(repeatString("=", 100));
            logWriter.close();
        }
    }
    
    /**
     * 双重输出：控制台 + 日志文件
     */
    private static void logMessage(String message) {
        // 输出到控制台
        Tools.println(message);
        
        // 写入日志文件
        if (logWriter != null) {
            logWriter.println(message);
        }
    }
    
    /**
     * 仅写入日志文件（不显示在控制台）
     */
    private static void logFileOnly(String message) {
        if (logWriter != null) {
            logWriter.println(message);
        }
    }
    
    /**
     * 带时间戳的日志记录
     */
    private static void logWithTimestamp(String message) {
        String timestamp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date());
        String logMessage = "[" + timestamp + "] " + message;
        
        // 输出到控制台
        Tools.println(logMessage);
        
        // 写入日志文件
        if (logWriter != null) {
            logWriter.println(logMessage);
        }
    }
    
    /**
     * 设置最大显示问题数量
     * @param maxDisplayCount 最大显示数量，-1表示显示全部
     */
    public void setMaxDisplayCount(int maxDisplayCount) {
        this.maxDisplayCount = maxDisplayCount;
    }
    
    /**
     * 获取最大显示问题数量
     * @return 最大显示数量，-1表示显示全部
     */
    public int getMaxDisplayCount() {
        return this.maxDisplayCount;
    }
    
    /**
     * 设置显示全部问题（便捷方法）
     */
    public void showAllProblems() {
        this.maxDisplayCount = -1;
    }
    
    /**
     * 设置只显示摘要，不显示详细问题（便捷方法）
     */
    public void showSummaryOnly() {
        this.maxDisplayCount = 0;
    }
    
    
    /**
     * 数据验证结果类
     */
         public static class ValidationResult {
         private boolean isConsistent;
         private int sourceTableCount;
         private int targetTableCount;
         private List<String> inconsistentRecords;
         private List<String> errorMessages;
         private String validationSummary;
         private Map<String, Integer> problemStats;
         
         public ValidationResult() {
             this.inconsistentRecords = new ArrayList<>();
             this.errorMessages = new ArrayList<>();
             this.problemStats = new HashMap<>();
         }
        
        // Getters and Setters
        public boolean isConsistent() { return isConsistent; }
        public void setConsistent(boolean consistent) { this.isConsistent = consistent; }
        
        public int getSourceTableCount() { return sourceTableCount; }
        public void setSourceTableCount(int sourceTableCount) { this.sourceTableCount = sourceTableCount; }
        
        public int getTargetTableCount() { return targetTableCount; }
        public void setTargetTableCount(int targetTableCount) { this.targetTableCount = targetTableCount; }
        
        public List<String> getInconsistentRecords() { return inconsistentRecords; }
        public void setInconsistentRecords(List<String> inconsistentRecords) { this.inconsistentRecords = inconsistentRecords; }
        
        public List<String> getErrorMessages() { return errorMessages; }
        public void setErrorMessages(List<String> errorMessages) { this.errorMessages = errorMessages; }
        
        public String getValidationSummary() { return validationSummary; }
        public void setValidationSummary(String validationSummary) { this.validationSummary = validationSummary; }
        
        public void addInconsistentRecord(String record) {
            this.inconsistentRecords.add(record);
        }
        
                 public void addErrorMessage(String message) {
             this.errorMessages.add(message);
         }
         
         public Map<String, Integer> getProblemStats() { return problemStats; }
         public void setProblemStats(Map<String, Integer> problemStats) { this.problemStats = problemStats; }
     }
    
    /**
     * 计划数据记录类
     */
    public static class PlanDataRecord {
        private String pc;
        private String pcCode;
        private String xkCodeOrg;
        private String yxdm;
        private String zyz;
        private String zyzjhs;
        private String zys;
        private String zdf2024;
        private String zdfwc2024;
        private String isHz;
        
        // Getters and Setters
        public String getPc() { return pc; }
        public void setPc(String pc) { this.pc = pc; }
        
        public String getPcCode() { return pcCode; }
        public void setPcCode(String pcCode) { this.pcCode = pcCode; }
        
        public String getXkCodeOrg() { return xkCodeOrg; }
        public void setXkCodeOrg(String xkCodeOrg) { this.xkCodeOrg = xkCodeOrg; }
        
        public String getYxdm() { return yxdm; }
        public void setYxdm(String yxdm) { this.yxdm = yxdm; }
        
        public String getZyz() { return zyz; }
        public void setZyz(String zyz) { this.zyz = zyz; }
        
        public String getZyzjhs() { return zyzjhs; }
        public void setZyzjhs(String zyzjhs) { this.zyzjhs = zyzjhs; }
        
        public String getZys() { return zys; }
        public void setZys(String zys) { this.zys = zys; }
        
        public String getZdf2024() { return zdf2024; }
        public void setZdf2024(String zdf2024) { this.zdf2024 = zdf2024; }
        
        public String getZdfwc2024() { return zdfwc2024; }
        public void setZdfwc2024(String zdfwc2024) { this.zdfwc2024 = zdfwc2024; }
        
        public String getIsHz() { return isHz; }
        public void setIsHz(String isHz) { this.isHz = isHz; }
        
        /**
         * 生成记录的唯一标识符
         */
        public String getRecordKey() {
            return String.format("pc=%s,pc_code=%s,xk_code_org=%s,yxdm=%s,zyz=%s", 
                    pc, pcCode, xkCodeOrg, yxdm, zyz);
        }
        
        @Override
        public String toString() {
            return String.format("PlanDataRecord{pc='%s', pcCode='%s', xkCodeOrg='%s', yxdm='%s', zyz='%s', " +
                    "zyzjhs='%s', zys='%s', zdf2024='%s', zdfwc2024='%s', isHz='%s'}", 
                    pc, pcCode, xkCodeOrg, yxdm, zyz, zyzjhs, zys, zdf2024, zdfwc2024, isHz);
        }
    }

}
