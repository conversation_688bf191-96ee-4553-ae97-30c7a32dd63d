<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>

<%@ page import="com.career.db.*,com.career.utils.*,java.util.*" %>
<%@include file="/WEB-INF/include/_session_ai.jsp" %>
<%
AiCard aiCard = (AiCard)session.getAttribute(ZyzdCache.SES_KEY_AI_BASE_CARD); 
AiJDBC aiJDBC = new AiJDBC();
ZyzdJDBC zyzdJDBC = new ZyzdJDBC();

String selected_already_tb_pc = Tools.trim(request.getParameter("pc"));
if(Tools.isEmpty(selected_already_tb_pc)){
	out.print("ERR:NO_PC_SELECTED");
	return;
}

List<String> pc_and_code = new ArrayList<>(Tools.getSetByStrSplit(selected_already_tb_pc, "__"));
if(pc_and_code.size() != 2){
	out.print("ERR:EXCEED");
	return;
}
String pc_code = pc_and_code.get(0);
String pc = pc_and_code.get(1);

ZyzdProvinceConfig provinceConfig = ZyzdCache.getProvinceConfig(aiCard.getC_prov());
String provinceTableName = provinceConfig.getP_table_code();
String provinceName = provinceConfig.getP_name();

int LATEST_JH_YEAR = provinceConfig.getLatest_year_jh(); 

int jh_version = provinceConfig.getJh_version();

AiTbFormMain aiTbFormMain = aiJDBC.getFormMainByOrderIdAndPc(jh_version, LATEST_JH_YEAR, provinceTableName, aiCard.getC_id(), pc, pc_code); 

List<AiTbForm> formList = null;
if(provinceConfig.getForm_type() == 2){
	formList = aiJDBC.getAllMakerFormFor96ByBatchId(provinceTableName, aiTbFormMain.getBatch_id());
}else{
	formList = aiJDBC.getAllMakerFormByBatchId(provinceTableName, aiTbFormMain.getBatch_id());
}
%>
<%
String xk_code = XKCombineUtils.getXKCodeByStudentSelection(aiCard.getC_xk());
//匹配三年的分
HashMap<String, ZDKSRank> TONGWF_MAP = zyzdJDBC.getAllTongWF(provinceName, xk_code, aiCard.getC_score_wc());

ZDKSRank rankYearA = TONGWF_MAP.get(String.valueOf(LATEST_JH_YEAR - 1));
ZDKSRank rankYearB = TONGWF_MAP.get(String.valueOf(LATEST_JH_YEAR - 2)); 
ZDKSRank rankYearC = TONGWF_MAP.get(String.valueOf(LATEST_JH_YEAR - 3));
ZDKSRank rankYearD = TONGWF_MAP.get(String.valueOf(LATEST_JH_YEAR - 4));

ZyzdProvincePcConfig zyzdProvincePcConfig = new ZyzdJDBC().getProvincePcConfigByParams(aiCard.getC_prov(), aiTbFormMain.getPc_code(), aiTbFormMain.getPc());
int MAJOR_CNT = provinceConfig.getCal_MajorCnt(aiTbFormMain.getPc_code(), null);
int FORM_CNT = provinceConfig.getCal_FormCnt(aiTbFormMain.getPc_code(), null);

LinkedHashMap<String, List<AiTbForm>> LINKED_MAP = new LinkedHashMap<>(); 
HashSet<String> yxmcSets = new HashSet<>();
for(AiTbForm f : formList){
	String map_key = null;
	if(provinceConfig.getForm_type() == 2){ //96个志愿
		map_key = String.valueOf(Tools.trim(f.getYxdm()) + Tools.trim(f.getYxmc()) + Tools.trim(f.getZydm()) +  Tools.trim(f.getZydm())); 
	}else{//院校专业组
		map_key = String.valueOf(Tools.trim(f.getYxdm()) + Tools.trim(f.getYxmc()) + Tools.trim(f.getZyz()));
	}
	
	if(LINKED_MAP.containsKey(map_key)){
		List<AiTbForm> its = LINKED_MAP.get(map_key);
		its.add(f);
	}else{
		List<AiTbForm> its = new ArrayList<>();
		its.add(f);
		LINKED_MAP.put(map_key, its);
	}
}
%>

                
                <%
                Iterator<String> keyList = LINKED_MAP.keySet().iterator();
                    int index = 0;
                    while(keyList.hasNext()){
                    	String yxdmKey = keyList.next();
                    	List<AiTbForm> eachYx = LINKED_MAP.get(yxdmKey);
                    	AiTbForm mainForm = eachYx.get(0);
                %>
                <div class="card custom-card" id="each_form_id_<%=mainForm.getSeq_no_yx()%>">
                     <div class="card-header justify-content-between">
                         <div class="card-title">
                             <span style="font-size:14px;<%=mainForm.getSeq_no_yx() > FORM_CNT ? "color:#ccc;":"" %>"><%=mainForm.getSeq_no_yx()%> 
                             	<%=mainForm.getYxmc()%>  
                             	<%
                             	if(!Tools.isEmpty(mainForm.getYxdm())){
                             	%>
                             	<span style="font-weight:normal;font-size:10px;">[<%=mainForm.getYxdm()%>]</span>
                             	<%
                             	}
                             	%>
                             </span> 
                              - <span style="font-size:10px;font-weight:normal;">第<b style="color:blue;font-size:12px;"><%=Tools.viewForZyz(mainForm.getZyz())%></b>组</span>
                              
                              <span style="margin-left:2px;">
		        				<%
		        				if(index < LINKED_MAP.size() - 1){
		        				%>
		        				<svg width="16" height="16" viewBox="0 0 24 24" fill="#555" xmlns="http://www.w3.org/2000/svg" onclick="MM_form_adjust_move('<%=mainForm.getBatch_id_org()%>', '<%=mainForm.getSeq_no_yx()%>', '<%=mainForm.getSeq_no_yx() + 1%>');">
								  <path d="M12 20L19 13H15V4H9V13H5L12 20Z"/> 
								</svg>
		        				<%
		        				}
		        				%> 
		        				<%
 		        				if(index > 0){
 		        				%>
								<svg width="16" height="16" viewBox="0 0 24 24" fill="#555" xmlns="http://www.w3.org/2000/svg" onclick="MM_form_adjust_move('<%=mainForm.getBatch_id_org()%>', '<%=mainForm.getSeq_no_yx()%>', '<%=mainForm.getSeq_no_yx()-1%>');">
								  <path d="M12 4L5 11H9V20H15V11H19L12 4Z"/> 
								</svg>
		        				<%
		        				}
		        				%>
	        				</span>  
	        				
	        				
                              
                         </div>
                     </div>
                     <div class="card-body p-0" > 
                         <div class="accordion accordion-flush" id="accordionFlushExample_<%=mainForm.getSeq_no_yx()%>" >  
                             <div id="example_<%=mainForm.getSeq_no_yx()%>" class="list-group"> 
	        		
	        		<%
 	        			        		index++;
	        								AiTbForm eachFormLast = null;
 	        			        			        		for(int k = 0; k < eachYx.size(); k++){ 
 	        			        			        			AiTbForm eachForm = eachYx.get(k);
 	        			        			        			eachFormLast = eachForm;
 	        			        		%>
 	        			        		 
	        		<div class="list-group-item" style="padding:1px;">  
	        			<div style="padding:5px;<%=k > (MAJOR_CNT - 1) ? "color:#ccc;":"" %>">
		        			<span style="margin:1px 5px;"><%=eachForm.getSeq_no_zy() %>.  
			        			<%if(!Tools.isEmpty(eachForm.getZydm())){ %>
			        			<span style="font-weight:normal;font-size:10px;">[<%=eachForm.getZydm() %>]</span>
			        			<%} %>
			        			<b><%=eachForm.getZymc_org() %></b> 
			        			<%String temp_major_desc = Tools.viewMajorDesc(eachForm.getZymc(),eachForm.getZymc_org()); %> 
			        			<span class="text-dark fs-10"><span style="<%=k > (MAJOR_CNT - 1) ? "color:#ccc;":"" %>"><%=temp_major_desc %></span></span>
		        			</span> 
		        			<span>
		        				<%if(k < eachYx.size() - 1){ %>
		        				<svg width="16" height="16" viewBox="0 0 24 24" fill="#555" xmlns="http://www.w3.org/2000/svg" onclick="MM_major_adjust_up_down('<%=eachForm.getId() %>','<%=eachForm.getSeq_no_zy() %>','<%=eachForm.getSeq_no_zy()+1 %>', 'example_<%=eachForm.getSeq_no_yx() %>');">
								  <path d="M12 20L19 13H15V4H9V13H5L12 20Z"/> 
								</svg>
		        				<%} %>
		        				<%if(k > 0){ %>
								<svg width="16" height="16" viewBox="0 0 24 24" fill="#555" xmlns="http://www.w3.org/2000/svg" onclick="MM_major_adjust_up_down('<%=eachForm.getId() %>','<%=eachForm.getSeq_no_zy() %>','<%=eachForm.getSeq_no_zy()-1 %>','example_<%=eachForm.getSeq_no_yx() %>');">
								  <path d="M12 4L5 11H9V20H15V11H19L12 4Z"/> 
								</svg>
		        				<%} %>
	        				</span> 
	        			</div>
	        			<div class="table-responsive" style="margin-top:5px;">
                              <table class="table text-nowrap table-primary">
                                  <thead>
                                      <tr>
                                          <th style="padding:2px .55rem;font-size:12px;<%=k > (MAJOR_CNT - 1) ? "color:#ccc;":"" %>">年份</th>
                                          <th style="padding:2px .55rem;font-size:12px;<%=k > (MAJOR_CNT - 1) ? "color:#ccc;":"" %>">等效分</th>
                                          <th style="padding:2px .55rem;font-size:12px;<%=k > (MAJOR_CNT - 1) ? "color:#ccc;":"" %>">最低分</th>
                                          <th style="padding:2px .55rem;font-size:12px;<%=k > (MAJOR_CNT - 1) ? "color:#ccc;":"" %>">平均分</th>
                                          <th style="padding:2px .55rem;font-size:12px;<%=k > (MAJOR_CNT - 1) ? "color:#ccc;":"" %>">最高分</th>
                                          <th style="padding:2px .55rem;font-size:12px;<%=k > (MAJOR_CNT - 1) ? "color:#ccc;":"" %>">计划数</th>
                                      </tr>
                                  </thead> 
                                  <tbody>
                                      <tr>   
								            <td><%=LATEST_JH_YEAR - 1 %></td>  
								            <td class="table-danger"> 
								            	<div> 
								                    <span class="mb-0 fw-semibold">   
								            			<span class="fs-12 mb-0 fw-semibold <%=eachForm.getZdfwc_a() > aiCard.getC_score_wc() ? "text-success" : "text-danger"%>">
								            			<span style="font-weight:normal;font-size:10px;color:#666;margin-right:1px;"><%=eachForm.getZdfwc_a() > aiCard.getC_score_wc() ? "" : "差"%></span><%=rankYearA == null ? "-" : Tools.view(String.valueOf(Math.abs(rankYearA.getScore() - eachForm.getZdf_a()))) %></span> 
								            		</span>  
								                    <span class="fs-9 fw-normal <%=eachForm.getZdfwc_a() > aiCard.getC_score_wc() ? "text-success" : "text-danger"%>"><%=rankYearA == null ? "-" : Tools.view(String.valueOf(Math.abs(eachForm.getZdfwc_a() - rankYearA.getWc()))) %></span>
								                </div> 
								            </td>     
								            <td>   
								            	<div>
								                    <span class="mb-0 fw-semibold">  
								            			<span class="fs-12 mb-0 fw-semibold"><%=Tools.viewForZdf(eachForm.getZdf_a()) %></span>
								            		</span> 
								                    <span class="fs-9 text-primary fw-normal"><%=Tools.viewForZdf(eachForm.getZdfwc_a()) %></span>
								                </div>
								            </td>
								            <td>
								            	<div>
								                    <span class="mb-0 fw-semibold"> 
								            			<span class="fs-12 mb-0 fw-semibold"><%=Tools.viewForZdf(eachForm.getPjf_a()) %></span>
								            		</span> 
								                    <span class="fs-9 text-primary fw-normal"><%=Tools.viewForZdf(eachForm.getPjfwc_a()) %></span>
								                </div>
								            </td>
								            <td>
								            	<div>
								                    <span class="mb-0 fw-semibold"> 
								            			<span class="fs-12 mb-0 fw-semibold"><%=Tools.viewForZdf(eachForm.getZgf_a()) %></span>
								            		</span>  
								                    <span class="fs-9 text-primary fw-normal"><%=Tools.viewForZdf(eachForm.getZgfwc_a()) %></span> 
								                </div>
								            </td> 
								            <td><%=Tools.viewForZdf(eachForm.getJhs_a()) %></td>
								         </tr>    
								         
								         <tr>   
								            <td><%=LATEST_JH_YEAR - 2%></td> 
								            <td class="table-danger"> 
								            	<div> 
								                    <span class="mb-0 fw-semibold">  
								            			<span class="fs-12 mb-0 fw-semibold <%=eachForm.getZdfwc_b() > aiCard.getC_score_wc() ? "text-success" : "text-danger"%>"><span style="font-weight:normal;font-size:10px;color:#666;margin-right:1px;"><%=eachForm.getZdfwc_b() > aiCard.getC_score_wc() ? "" : "差"%></span><%=rankYearB == null ? "-" : Tools.view(String.valueOf(Math.abs(rankYearB.getScore() - eachForm.getZdf_b()))) %></span></span>  <span class="fs-9 fw-normal <%=eachForm.getZdfwc_b() > aiCard.getC_score_wc() ? "text-success" : "text-danger"%>"><%=rankYearB == null ? "-" : Tools.view(String.valueOf(Math.abs(eachForm.getZdfwc_b() - rankYearA.getWc()))) %></span>
								                </div> 
								            </td>
								            <td> 
								            	<div>
								                    <span class="mb-0 fw-semibold"> 
								            			<span class="fs-12 mb-0 fw-semibold"><%=Tools.viewForZdf(eachForm.getZdf_b()) %></span>
								            		</span> 
								                    <span class="fs-9 text-primary fw-normal"><%=Tools.viewForZdf(eachForm.getZdfwc_b()) %></span>
								                </div>
								            </td>
								            <td>
								            	<div>
								                    <span class="mb-0 fw-semibold"> 
								            			<span class="fs-12 mb-0 fw-semibold"><%=Tools.viewForZdf(eachForm.getPjf_b()) %></span>
								            		</span> 
								                    <span class="fs-9 text-primary fw-normal"><%=Tools.viewForZdf(eachForm.getPjfwc_b()) %></span>
								                </div>
								            </td>
								            <td>
								            	<div>
								                    <span class="mb-0 fw-semibold"><span class="fs-12 mb-0 fw-semibold"><%=Tools.viewForZdf(eachForm.getZgf_b()) %></span></span><span class="fs-9 text-primary fw-normal"><%=Tools.viewForZdf(eachForm.getZgfwc_b()) %></span>
								                </div>
								            </td>
								            <td><%=Tools.viewForZdf(eachForm.getJhs_b()) %></td>
								         </tr>   
								           
								         <tr>     
								            <td><%=LATEST_JH_YEAR - 3%></td>   
								            <td class="table-danger"> 
								            	<div>   
								                    <span class="mb-0 fw-semibold">   
								            			<span class="fs-12 mb-0 fw-semibold <%=eachForm.getZdfwc_c() > aiCard.getC_score_wc() ? "text-success" : "text-danger"%>"><span style="font-weight:normal;font-size:10px;color:#666;margin-right:1px;"><%=eachForm.getZdfwc_c() > aiCard.getC_score_wc() ? "" : "差"%></span><%=rankYearC == null ? "-" : Tools.view(String.valueOf(Math.abs(rankYearC.getScore() - eachForm.getZdf_c()))) %></span></span><span class="fs-9 fw-normal <%=eachForm.getZdfwc_c() > aiCard.getC_score_wc() ? "text-success" : "text-danger"%>"><%=rankYearC == null ? "-" : Tools.view(String.valueOf(Math.abs(eachForm.getZdfwc_c() - rankYearA.getWc()))) %></span>
								                </div>  
								            </td>   
								            <td> 
								            	<div>
								                    <span class="mb-0 fw-semibold"> 
								            			<span class="fs-12 mb-0 fw-semibold"><%=Tools.viewForZdf(eachForm.getZdf_c()) %></span>
								            		</span> 
								                    <span class="fs-9 text-primary fw-normal"><%=Tools.viewForZdf(eachForm.getZdfwc_c()) %></span>
								                </div>
								            </td> 
								            <td>
								            	<div>
								                    <span class="mb-0 fw-semibold">  
								            			<span class="fs-12 mb-0 fw-semibold"><%=Tools.viewForZdf(eachForm.getPjf_c()) %></span> 
								            		</span> 
								                    <span class="fs-9 text-primary fw-normal"><%=Tools.viewForZdf(eachForm.getPjfwc_c()) %></span>
								                </div>
								            </td>
								            <td> 
								            	<div>
								                    <span class="mb-0 fw-semibold"> 
								            			<span class="fs-12 mb-0 fw-semibold"><%=Tools.viewForZdf(eachForm.getZgf_c()) %></span>
								            		</span>  
								                    <span class="fs-9 text-primary fw-normal"><%=Tools.viewForZdf(eachForm.getZgfwc_c()) %></span>
								                </div>
								            </td>  
								            <td><%=Tools.viewForZdf(eachForm.getJhs_c()) %></td>
								         </tr>  
                                  </tbody> 
                              </table>
                          </div>
	        		</div>
	        		<%} %> 
	        		
	        		<%if(eachYx.size() < MAJOR_CNT){ %>
	        		<div class="list-group-item">
	        			<span style="font-size:12px;color:blue">
	        				可再选择<%=MAJOR_CNT - eachYx.size() %>个您能接受的专业，填满整个志愿表
	        			</span>
	        		</div>
	        		<%} %>
	        				</div>
                         </div>
                     </div>
                     
                     <div class="card-footer">  
                             <span class="ms-auto"><button class="btn btn-sm btn-success btn-wave waves-effect waves-light" onclick="MM_form_replace_major_pop('<%=eachFormLast.getId() %>');" data-bs-toggle="offcanvas" data-bs-target="#offcanvasBottom" aria-controls="offcanvasBottom">调整专业</button></span>
                             <span class="ms-auto">
                             <button class="btn btn-sm btn-warning btn-wave waves-effect waves-light" onclick="MM_form_delete_item('<%=eachFormLast.getId() %>');" >删除</button>
                             </span>
                    </div>
                     
                 </div> 
                 <%} %>  
                 <div class="d-grid gap-2 mb-4">
                     <button class="btn btn-primary btn-wave waves-effect waves-light" type="button" onclick="MM_drag_popup('<%=aiTbFormMain.getBatch_id()%>');">总览志愿表（调整顺序，查看录取）</button>
                     <button class="btn btn-success btn-wave waves-effect waves-light" type="button" onclick="MM_export_myform_jh_data('<%=aiTbFormMain.getBatch_id()%>', '<%=selected_already_tb_pc%>');" id="MM_export_myform_jh_btn">
                         <i class="ri-download-cloud-line me-2 align-middle d-inline-block"></i>导出志愿表对应计划数据
                     </button>
                     <a href="<%=request.getContextPath() %>/lhy/remote/ai_form_report.jsp?batch_id=<%=aiTbFormMain.getBatch_id() %>" target="_blank">
                     <button class="btn btn-primary btn-wave waves-effect waves-light" type="button">提交审核（仅限普通批次）</button>
                     </a>
                     <button class="btn btn-warning btn-wave waves-effect waves-light" type="button" onclick="MM_delete_tb_result('<%=aiTbFormMain.getBatch_id()%>')">刪除本批次志愿(不可恢复)</button>
                 </div>          	
            
