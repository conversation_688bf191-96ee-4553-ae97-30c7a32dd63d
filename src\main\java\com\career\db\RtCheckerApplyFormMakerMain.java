package com.career.db;

import java.util.Date;

public class RtCheckerApplyFormMakerMain {
	
	private int id;
	private String batch_id;
	private int score_cj;
	private int score_wc;
	private String score_xk;
	private String f_no;
	private String order_id;
	private String selected_zymc;
	private String pc;
	private String pc_code;
	private int nf;
	private Date create_tm;
	private Date last_update_tm;
	private Date target_send_tm;
	private Date user_first_view_tm;
	private int joined_checker_cnt;
	private String last_checker_id;
	private String last_checker_name;
	private String last_checker_remark;
	private int status;
	private int f_type;
	private String form_name;
	private String to_checker_remark;
	private String sf;
	
	private int rt_score_chinese;
    private int rt_score_math;
    private int rt_score_foreign_language;
    private int rt_score_xk1;
    private int rt_score_xk2;
    private int rt_score_xk3;
    private String rt_nickname;
    private String rt_phone;
    private int rt_score_ext;
    private String rt_remark;
    
    private String rt_health;
    
    public Date getTarget_send_tm() {
		return target_send_tm;
	}

	public void setTarget_send_tm(Date target_send_tm) {
		this.target_send_tm = target_send_tm;
	}

	public Date getUser_first_view_tm() {
		return user_first_view_tm;
	}

	public void setUser_first_view_tm(Date user_first_view_tm) {
		this.user_first_view_tm = user_first_view_tm;
	}

	public String getRt_health() {
		return rt_health;
	}
	
	public void setRt_health(String rt_health) {
		this.rt_health = rt_health;
	}
	
	public String getRt_nickname() {
		return rt_nickname;
	}
	public void setRt_nickname(String rt_nickname) {
		this.rt_nickname = rt_nickname;
	}
	
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getBatch_id() {
		return batch_id;
	}
	public void setBatch_id(String batch_id) {
		this.batch_id = batch_id;
	}
	public int getScore_cj() {
		return score_cj;
	}
	public void setScore_cj(int score_cj) {
		this.score_cj = score_cj;
	}
	public int getScore_wc() {
		return score_wc;
	}
	public void setScore_wc(int score_wc) {
		this.score_wc = score_wc;
	}
	public String getScore_xk() {
		return score_xk;
	}
	public void setScore_xk(String score_xk) {
		this.score_xk = score_xk;
	}
	public String getF_no() {
		return f_no;
	}
	public void setF_no(String f_no) {
		this.f_no = f_no;
	}
	public String getOrder_id() {
		return order_id;
	}
	public void setOrder_id(String order_id) {
		this.order_id = order_id;
	}
	public String getSelected_zymc() {
		return selected_zymc;
	}
	public void setSelected_zymc(String selected_zymc) {
		this.selected_zymc = selected_zymc;
	}
	public String getPc() {
		return pc;
	}
	public void setPc(String pc) {
		this.pc = pc;
	}
	public String getPc_code() {
		return pc_code;
	}
	public void setPc_code(String pc_code) {
		this.pc_code = pc_code;
	}
	public int getNf() {
		return nf;
	}
	public void setNf(int nf) {
		this.nf = nf;
	}
	public Date getCreate_tm() {
		return create_tm;
	}
	public void setCreate_tm(Date create_tm) {
		this.create_tm = create_tm;
	}
	public Date getLast_update_tm() {
		return last_update_tm;
	}
	public void setLast_update_tm(Date last_update_tm) {
		this.last_update_tm = last_update_tm;
	}
	public int getJoined_checker_cnt() {
		return joined_checker_cnt;
	}
	public void setJoined_checker_cnt(int joined_checker_cnt) {
		this.joined_checker_cnt = joined_checker_cnt;
	}
	public String getLast_checker_id() {
		return last_checker_id;
	}
	public void setLast_checker_id(String last_checker_id) {
		this.last_checker_id = last_checker_id;
	}
	public String getLast_checker_name() {
		return last_checker_name;
	}
	public void setLast_checker_name(String last_checker_name) {
		this.last_checker_name = last_checker_name;
	}
	public String getLast_checker_remark() {
		return last_checker_remark;
	}
	public void setLast_checker_remark(String last_checker_remark) {
		this.last_checker_remark = last_checker_remark;
	}
	public int getStatus() {
		return status;
	}
	public void setStatus(int status) {
		this.status = status;
	}
	public int getF_type() {
		return f_type;
	}
	public void setF_type(int f_type) {
		this.f_type = f_type;
	}
	public String getForm_name() {
		return form_name;
	}
	public void setForm_name(String form_name) {
		this.form_name = form_name;
	}
	public String getTo_checker_remark() {
		return to_checker_remark;
	}
	public void setTo_checker_remark(String to_checker_remark) {
		this.to_checker_remark = to_checker_remark;
	}
	public String getSf() {
		return sf;
	}
	public void setSf(String sf) {
		this.sf = sf;
	}
	public int getRt_score_chinese() {
		return rt_score_chinese;
	}
	public void setRt_score_chinese(int rt_score_chinese) {
		this.rt_score_chinese = rt_score_chinese;
	}
	public int getRt_score_math() {
		return rt_score_math;
	}
	public void setRt_score_math(int rt_score_math) {
		this.rt_score_math = rt_score_math;
	}
	public int getRt_score_foreign_language() {
		return rt_score_foreign_language;
	}
	public void setRt_score_foreign_language(int rt_score_foreign_language) {
		this.rt_score_foreign_language = rt_score_foreign_language;
	}
	public int getRt_score_xk1() {
		return rt_score_xk1;
	}
	public void setRt_score_xk1(int rt_score_xk1) {
		this.rt_score_xk1 = rt_score_xk1;
	}
	public int getRt_score_xk2() {
		return rt_score_xk2;
	}
	public void setRt_score_xk2(int rt_score_xk2) {
		this.rt_score_xk2 = rt_score_xk2;
	}
	public int getRt_score_xk3() {
		return rt_score_xk3;
	}
	public void setRt_score_xk3(int rt_score_xk3) {
		this.rt_score_xk3 = rt_score_xk3;
	}
	public String getRt_phone() {
		return rt_phone;
	}
	public void setRt_phone(String rt_phone) {
		this.rt_phone = rt_phone;
	}
	public int getRt_score_ext() {
		return rt_score_ext;
	}
	public void setRt_score_ext(int rt_score_ext) {
		this.rt_score_ext = rt_score_ext;
	}
	public String getRt_remark() {
		return rt_remark;
	}
	public void setRt_remark(String rt_remark) {
		this.rt_remark = rt_remark;
	}

	
}
