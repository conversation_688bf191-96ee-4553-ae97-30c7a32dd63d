package com.report.test;

import com.report.entity.*;
import com.report.service.MockDataService;

/**
 * 测试规划数据加载是否正确
 */
public class PlanningDataTest {
    
    public static void main(String[] args) {
        System.out.println("=== 测试大学四年规划数据加载 ===");
        
        // 生成四年规划数据
        UniversityFourYearPlan plan = MockDataService.generateMockUniversityFourYearPlan();
        
        // 测试竞赛规划数据
        System.out.println("\n--- 竞赛规划数据测试 ---");
        CompetitionPlan competitionPlan = plan.getCompetitionPlan();
        if (competitionPlan != null) {
            System.out.println("✓ 竞赛规划对象创建成功");
            
            if (competitionPlan.getRecommendedCompetitions() != null && !competitionPlan.getRecommendedCompetitions().isEmpty()) {
                System.out.println("✓ 推荐竞赛数据: " + competitionPlan.getRecommendedCompetitions().size() + " 项");
                for (CompetitionPlan.CompetitionItem item : competitionPlan.getRecommendedCompetitions()) {
                    System.out.println("  - " + item.getName() + " (" + item.getCategory() + ")");
                }
            } else {
                System.out.println("❌ 推荐竞赛数据为空");
            }
            
            if (competitionPlan.getCompetitionsByMonth() != null && !competitionPlan.getCompetitionsByMonth().isEmpty()) {
                System.out.println("✓ 按月份分类的竞赛数据: " + competitionPlan.getCompetitionsByMonth().size() + " 个月份");
                for (String month : competitionPlan.getCompetitionsByMonth().keySet()) {
                    System.out.println("  - " + month + ": " + competitionPlan.getCompetitionsByMonth().get(month).size() + " 项竞赛");
                }
            } else {
                System.out.println("❌ 按月份分类的竞赛数据为空");
            }
        } else {
            System.out.println("❌ 竞赛规划对象为空");
        }
        
        // 测试考证规划数据
        System.out.println("\n--- 考证规划数据测试 ---");
        CertificationPlan certPlan = plan.getCertificationPlan();
        if (certPlan != null) {
            System.out.println("✓ 考证规划对象创建成功");
            
            if (certPlan.getCertificationsByYear() != null && !certPlan.getCertificationsByYear().isEmpty()) {
                System.out.println("✓ 按年级分类的考证数据: " + certPlan.getCertificationsByYear().size() + " 个年级");
                for (String year : certPlan.getCertificationsByYear().keySet()) {
                    System.out.println("  - " + year + ": " + certPlan.getCertificationsByYear().get(year).size() + " 项证书");
                    for (CertificationPlan.CertificationItem cert : certPlan.getCertificationsByYear().get(year)) {
                        System.out.println("    * " + cert.getName() + " (" + cert.getCategory() + ")");
                    }
                }
            } else {
                System.out.println("❌ 按年级分类的考证数据为空");
            }
        } else {
            System.out.println("❌ 考证规划对象为空");
        }
        
        // 测试学术发展规划数据
        System.out.println("\n--- 学术发展规划数据测试 ---");
        AcademicDevelopmentPlan academicPlan = plan.getAcademicPlan();
        if (academicPlan != null) {
            System.out.println("✓ 学术发展规划对象创建成功");
            
            if (academicPlan.getGradRecommendationPlan() != null) {
                System.out.println("✓ 保研规划数据存在");
                if (academicPlan.getGradRecommendationPlan().getTimeline() != null && !academicPlan.getGradRecommendationPlan().getTimeline().isEmpty()) {
                    System.out.println("  - 保研时间安排: " + academicPlan.getGradRecommendationPlan().getTimeline().size() + " 个时间节点");
                }
                if (academicPlan.getGradRecommendationPlan().getRequirements() != null && !academicPlan.getGradRecommendationPlan().getRequirements().isEmpty()) {
                    System.out.println("  - 保研要求: " + academicPlan.getGradRecommendationPlan().getRequirements().size() + " 项");
                }
            }
            
            if (academicPlan.getGradExamPlan() != null) {
                System.out.println("✓ 考研规划数据存在");
                if (academicPlan.getGradExamPlan().getTimeline() != null && !academicPlan.getGradExamPlan().getTimeline().isEmpty()) {
                    System.out.println("  - 考研时间安排: " + academicPlan.getGradExamPlan().getTimeline().size() + " 个时间节点");
                }
                if (academicPlan.getGradExamPlan().getSubjects() != null && !academicPlan.getGradExamPlan().getSubjects().isEmpty()) {
                    System.out.println("  - 考研科目: " + academicPlan.getGradExamPlan().getSubjects().size() + " 门");
                }
            }
        } else {
            System.out.println("❌ 学术发展规划对象为空");
        }
        
        // 测试职业准备规划数据
        System.out.println("\n--- 职业准备规划数据测试 ---");
        CareerPreparationPlan careerPlan = plan.getCareerPreparationPlan();
        if (careerPlan != null) {
            System.out.println("✓ 职业准备规划对象创建成功");
            
            if (careerPlan.getJobPlan() != null) {
                System.out.println("✓ 求职规划数据存在");
                if (careerPlan.getJobPlan().getPreparationSteps() != null && !careerPlan.getJobPlan().getPreparationSteps().isEmpty()) {
                    System.out.println("  - 求职准备步骤: " + careerPlan.getJobPlan().getPreparationSteps().size() + " 个步骤");
                    for (String step : careerPlan.getJobPlan().getPreparationSteps()) {
                        System.out.println("    * " + step);
                    }
                }
            }
            
            if (careerPlan.getPartyPlan() != null) {
                System.out.println("✓ 入党规划数据存在");
                if (careerPlan.getPartyPlan().getBenefits() != null && !careerPlan.getPartyPlan().getBenefits().isEmpty()) {
                    System.out.println("  - 入党益处: " + careerPlan.getPartyPlan().getBenefits().size() + " 项");
                }
            }
            
            if (careerPlan.getScholarshipPlan() != null) {
                System.out.println("✓ 奖学金规划数据存在");
                if (careerPlan.getScholarshipPlan().getStrategies() != null && !careerPlan.getScholarshipPlan().getStrategies().isEmpty()) {
                    System.out.println("  - 获奖策略: " + careerPlan.getScholarshipPlan().getStrategies().size() + " 项");
                }
            }
        } else {
            System.out.println("❌ 职业准备规划对象为空");
        }
        
        System.out.println("\n=== 测试完成 ===");
        System.out.println("现在模板将使用实体类中的最新配置数据，而不是硬编码的值！");
    }
} 