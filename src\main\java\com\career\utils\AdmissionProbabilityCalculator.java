package com.career.utils;

public class AdmissionProbabilityCalculator {

	/**
     * 计算录取概率的方法
     * @param predictedScore 预测趋势分A
     * @param studentScore 考生成绩S
     * @return 录取概率（以百分比表示）
     */
    public static int calculateAdmissionProbability(int predictedScore, int studentScore) {
        // 如果三年无数据，直接返回0%
        if (predictedScore <= 0) {
            return 0;
        }

        // 计算差值X
        int X = studentScore - predictedScore;

        // 根据差值X计算概率
        if (X == 0) {
            return 70; // X = 0，概率为70%
        } else if (X >= 20) {
            return 99; // X >= 20，概率为99%
        } else if (X <= -25) {
            return 1; // X <= -25，概率为1%
        } else {
            // 在-25到20之间的差值，需要线性插值计算概率
            if (X > 0) {
                // 0 < X < 20，概率从70%线性增加到99%
                return 70 + (X * 29) / 20;
            } else {
                // -25 < X < 0，概率从70%线性减少到1%
                return 70 + (X * 69) / 25;
            }
        }
    }
    
    /**
     * 预测趋势
     * @param scoreQSA 2024年趋势分
     * @param scoreQSB 2023年趋势分
     * @param scoreQSC 2022年趋势分
     * @return
     */
    public static double qs_yc(int scoreQSA, int scoreQSB, int scoreQSC) {
        // 情况六：三年无值
        if (scoreQSA <= 0 && scoreQSB <= 0 && scoreQSC <= 0) {
            return 0;
        }

        // 情况五：只有一年有值
        if ((scoreQSA > 0 && scoreQSB <= 0 && scoreQSC <= 0) ||
            (scoreQSA <= 0 && scoreQSB > 0 && scoreQSC <= 0) ||
            (scoreQSA <= 0 && scoreQSB <= 0 && scoreQSC > 0)) {
            int singleValue = scoreQSA > 0 ? scoreQSA : (scoreQSB > 0 ? scoreQSB : scoreQSC);
            return singleValue * 1.01;
        }

        // 情况一：三年都有值
        if (scoreQSA > 0 && scoreQSB > 0 && scoreQSC > 0) {
            int X1 = scoreQSA - scoreQSB;
            int X2 = scoreQSB - scoreQSC;

            // 1.1 连续上涨
            if (scoreQSB >= scoreQSC && scoreQSA >= scoreQSB) {
                return scoreQSA + (Math.min(X1, X2));
            }

            // 1.2 连续下跌
            if (scoreQSB <= scoreQSC && scoreQSA <= scoreQSB) {
                return scoreQSB;
            }

            // A scoreQSB > scoreQSC，scoreQSA < scoreQSB
            if (scoreQSB > scoreQSC && scoreQSA < scoreQSB) {
                // B 如果 scoreQSA >= scoreQSC
                if (scoreQSA >= scoreQSC) {
                    double Y = (double) (scoreQSA - scoreQSC) / scoreQSC;
                    if (Y > 0.01) {
                        Y = 0.01;
                    }
                    return scoreQSB * (1 + Y);
                }
                // C 如果 scoreQSA < scoreQSC
                return scoreQSB;
            }

            // 3 scoreQSB < scoreQSC，scoreQSA > scoreQSB
            if (scoreQSB < scoreQSC && scoreQSA > scoreQSB) {
                // A 如果 scoreQSA >= scoreQSC
                if (scoreQSA >= scoreQSC) {
                    double Y = (double) (scoreQSA - scoreQSC) / scoreQSC;
                    if (Y > 0.008) {
                        Y = 0.008;
                    }
                    return Math.max(scoreQSB * (1 + Y), scoreQSA);
                }
                // B 如果 scoreQSA < scoreQSC
                return scoreQSA;
            }
        }

        // 情况二：scoreQSA 和 scoreQSB 有值，scoreQSC 没有值
        if (scoreQSA > 0 && scoreQSB > 0 && scoreQSC <= 0) {
            // A scoreQSA >= scoreQSB
            if (scoreQSA >= scoreQSB) {
                double Y = (double) (scoreQSA - scoreQSB) / scoreQSB;
                if (Y > 0.008) {
                    Y = 0.008;
                }
                return scoreQSA * (1 + Y);
            }
            // B scoreQSA < scoreQSB
            return scoreQSB;
        }

        // 情况三：scoreQSA 和 scoreQSC 有值，scoreQSB 没有值
        if (scoreQSA > 0 && scoreQSB <= 0 && scoreQSC > 0) {
            // A scoreQSA >= scoreQSC
            if (scoreQSA >= scoreQSC) {
                double Y = (double) (scoreQSA - scoreQSC) / scoreQSC;
                if (Y > 0.008) {
                    Y = 0.008;
                }
                return scoreQSA * (1 + Y);
            }
            // B scoreQSA < scoreQSC
            return scoreQSC;
        }

        // 情况四：scoreQSB 和 scoreQSC 有值，scoreQSA 没有值
        if (scoreQSA <= 0 && scoreQSB > 0 && scoreQSC > 0) {
            // A scoreQSB >= scoreQSC
            if (scoreQSB >= scoreQSC) {
                double Y = (double) (scoreQSB - scoreQSC) / scoreQSC;
                if (Y > 0.008) {
                    Y = 0.008;
                }
                return scoreQSB * (1 + Y);
            }
            // B scoreQSB < scoreQSC
            return scoreQSC;
        }

        // 如果不符合任何情况，返回0
        return 0;
    }


    public static void main(String[] args) {
        // 示例测试
        int predictedScore = (int)qs_yc(590,555,561); // 预测趋势分
        int studentScore = 590;   // 考生成绩

        
        int probability = calculateAdmissionProbability(predictedScore, studentScore);
        System.out.println(predictedScore + ", 录取概率为：" + probability + "%");
    }
}
