package com.career.db;

import com.career.utils.BaseBean;

public class ZyzdExpPackageHY extends BaseBean {
	
	private int id;
	private String zymc;
	private String cc;
	private String pack_name;
	private String pack_desc;
	
	public static void main(String args[]) {
		ZyzdExpPackageHY bean = new ZyzdExpPackageHY();
		printBeanProperties(bean);
	}
	
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getZymc() {
		return zymc;
	}
	public void setZymc(String zymc) {
		this.zymc = zymc;
	}
	public String getCc() {
		return cc;
	}
	public void setCc(String cc) {
		this.cc = cc;
	}
	public String getPack_name() {
		return pack_name;
	}
	public void setPack_name(String pack_name) {
		this.pack_name = pack_name;
	}
	public String getPack_desc() {
		return pack_desc;
	}
	public void setPack_desc(String pack_desc) {
		this.pack_desc = pack_desc;
	}
	
	
}
