package com.zsdwf.db;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.UUID;

import com.career.db.JDBCConstants;
import com.career.db.PDFReport;
import com.career.utils.SQLLogUtils;
import com.zsdwf.utils.Tools;

public class YGJDBC {
	static String URL = JDBCConstants.URL;
	static String USER = JDBCConstants.USER;
	static String PASSWD = JDBCConstants.PASSWD;
  
  public static HashMap<String, String> HM_PROVINCE = new HashMap<>();
  
  public static HashMap<String, String> HM_PROVINCE_CODE_NAME = new LinkedHashMap<>();
  
  public static HashMap<String, String> HM_PROVINCE_LATEST_YEAR = new HashMap<>();
  
  public static HashMap<String, String> HM_XK = new HashMap<>();
  
  public static HashMap<String, String> HM_PROVINCE_CODE = new HashMap<>();
  
  public static HashMap<String, String> HM_LHPY = new HashMap<>();
  
  public static HashMap<String, String> HM_LMGX = new HashMap<>();
  
  public static HashMap<String, String> HM_HYMX = new HashMap<>();
  
  public static HashMap<String, String> HM_YBWS = new HashMap<>();
  
  public static HashMap<String, String> HM_HYHP = new HashMap<>();
  
  public static HashSet<String> YX_LHPY = new HashSet<>();
  
  public static HashSet<String> YX_XZX = new HashSet<>();
  
  public static HashSet<String> YX_FX = new HashSet<>();
  
  public static HashSet<String> YX_GAJX = new HashSet<>();
  
  public static HashSet<String> YX_SFJX = new HashSet<>();
  
  static {
    try {
      Class.forName("com.mysql.cj.jdbc.Driver");
    } catch (Exception ex) {
      ex.printStackTrace();
    } 
    YX_SFJX.add("中央司法警官学院");
    YX_SFJX.add("江西司法警官职业学院");
    YX_SFJX.add("黑龙江司法警官职业学院");
    YX_SFJX.add("河北司法警官职业学院");
    YX_SFJX.add("吉林司法警官职业学院");
    YX_SFJX.add("云南司法警官职业学院");
    YX_SFJX.add("四川司法警官职业学院");
    YX_SFJX.add("山东司法警官职业学院");
    YX_SFJX.add("山西警官职业学院");
    YX_SFJX.add("河南司法警官职业学院");
    YX_SFJX.add("浙江警官职业学院");
    YX_SFJX.add("湖南司法警官职业学院");
    YX_SFJX.add("安徽警官职业学院");
    YX_SFJX.add("广东司法警官职业学院");
    YX_SFJX.add("武汉警官职业学院");

    
    
    YX_GAJX.add("中国人民公安大学");
    YX_GAJX.add("中国刑事警察学院");
    YX_GAJX.add("中国人民警察大学");
    YX_GAJX.add("南京森林警察学院");
    YX_GAJX.add("铁道警察学院");
    YX_GAJX.add("湖北警官学院");
    YX_GAJX.add("江苏警官学院");
    YX_GAJX.add("北京警察学院");
    YX_GAJX.add("广东警官学院");
    YX_GAJX.add("上海公安学院");
    YX_GAJX.add("湖南警察学院");
    YX_GAJX.add("浙江警察学院");
    YX_GAJX.add("云南警官学院");
    YX_GAJX.add("山东警察学院");
    YX_GAJX.add("山西警察学院");
    YX_GAJX.add("江西警察学院");
    YX_GAJX.add("河南警察学院");
    YX_GAJX.add("四川警察学院");
    YX_GAJX.add("重庆警察学院");
    YX_GAJX.add("广西警察学院");
    YX_GAJX.add("贵州警察学院");
    YX_GAJX.add("福建警察学院");
    YX_GAJX.add("吉林警察学院");
    YX_GAJX.add("辽宁警察学院");
    YX_GAJX.add("新疆警察学院");

    YX_XZX.add("苏州科技大学天平学院");
    YX_XZX.add("南华大学船山学院");
    YX_XZX.add("杭州师范大学钱江学院");
    YX_XZX.add("湖北工业大学工程技术学院");
    YX_XZX.add("厦门大学嘉庚学院");
    YX_XZX.add("湖南中医药大学湘杏学院");
    YX_XZX.add("新乡医学院三全学院");
    YX_XZX.add("温州医科大学仁济学院");
    YX_XZX.add("天津师范大学津沽学院");
    YX_XZX.add("阜阳师范大学信息工程学院");
    YX_XZX.add("内蒙古大学创业学院");
    YX_XZX.add("湖南科技大学潇湘学院");
    YX_XZX.add("三峡大学科技学院");
    YX_XZX.add("天津外国语大学滨海外事学院");
    YX_XZX.add("河北农业大学现代科技学院");
    YX_XZX.add("广西中医药大学赛恩斯新医药学院");
    YX_XZX.add("南通大学杏林学院");
    YX_XZX.add("湘潭大学兴湘学院");
    YX_XZX.add("贵州中医药大学时珍学院");
    YX_XZX.add("江苏大学京江学院");
    YX_XZX.add("浙江中医药大学滨江学院");
    YX_XZX.add("燕山大学里仁学院");
    YX_XZX.add("江苏科技大学苏州理工学院");
    YX_XZX.add("江苏师范大学科文学院");
    YX_XZX.add("华北理工大学冀唐学院");
    YX_XZX.add("河北医科大学临床学院");

    YX_FX.add("哈尔滨工业大学");
    YX_FX.add("北京师范大学");
    YX_FX.add("中国人民大学");
    YX_FX.add("大连理工大学");
    YX_FX.add("山东大学");
    YX_FX.add("电子科技大学");
    YX_FX.add("东北大学");
    YX_FX.add("合肥工业大学");
    YX_FX.add("华北电力大学");
    YX_FX.add("中国石油大学");
    YX_FX.add("中国地质大学");
    YX_FX.add("中国矿业大学");
    YX_FX.add("北京邮电大学");
    YX_FX.add("北京交通大学");
    YX_FX.add("成都理工大学");
    YX_FX.add("西南大学");
    YX_FX.add("遵义医科大学");
    YX_FX.add("西华大学");
    YX_FX.add("四川外国语大学成都学院");
    YX_FX.add("东北石油大学");
    YX_FX.add("哈尔滨医科大学");
    YX_FX.add("厦门大学");
    YX_FX.add("中国农业大学");
    YX_FX.add("中国传媒大学");
    YX_FX.add("浙江大学");
    YX_FX.add("香港中文大学");
    YX_FX.add("西南交通大学");
    YX_FX.add("哈尔滨理工大学");
    
    HM_PROVINCE.put("A1", "A1_AH");
    HM_PROVINCE.put("B1", "B1_BJ");
    HM_PROVINCE.put("C1", "C1_CQ");
    HM_PROVINCE.put("F1", "F1_FJ");
    HM_PROVINCE.put("G1", "G1_GS");
    HM_PROVINCE.put("G2", "G2_GD");
    HM_PROVINCE.put("G3", "G3_GX");
    HM_PROVINCE.put("H1", "H1_HN");
    HM_PROVINCE.put("H2", "H2_HB");
    HM_PROVINCE.put("H3", "H3_HN");
    HM_PROVINCE.put("H4", "H4_HL");
    HM_PROVINCE.put("H6", "H6_HN");
    HM_PROVINCE.put("J2", "J2_JS");
    HM_PROVINCE.put("J3", "J3_JX");
    HM_PROVINCE.put("L1", "L1_LN");
    HM_PROVINCE.put("N1", "N1_NM");
    HM_PROVINCE.put("N2", "N2_NX");
    HM_PROVINCE.put("Q1", "Q1_QH");
    HM_PROVINCE.put("S1", "S1_SD");
    HM_PROVINCE.put("S4", "S4_SH");
    HM_PROVINCE.put("S5", "S5_SC");
    HM_PROVINCE.put("Y1", "Y1_YN");
    HM_PROVINCE.put("T1", "T1_TJ");
    HM_PROVINCE.put("G4", "G4_GZ");
    HM_PROVINCE.put("H5", "H5_HB");
    HM_PROVINCE.put("J1", "J1_JL");
    HM_PROVINCE.put("S2", "S2_SX");
    HM_PROVINCE.put("S3", "S3_SX");
    HM_PROVINCE.put("X1", "X1_XJ");
    HM_PROVINCE.put("Z1", "Z1_ZJ");
    
    HM_PROVINCE_LATEST_YEAR.put("S3","_2022");
    HM_PROVINCE_LATEST_YEAR.put("S4","_2022");
    HM_PROVINCE_LATEST_YEAR.put("S5","_2022");
    HM_PROVINCE_LATEST_YEAR.put("C1","_2022");
    HM_PROVINCE_LATEST_YEAR.put("Z1","_2022");
    HM_PROVINCE_LATEST_YEAR.put("X1","_2022");
    HM_PROVINCE_LATEST_YEAR.put("N1","_2022");
    HM_PROVINCE_LATEST_YEAR.put("L1","_2022");
    HM_PROVINCE_LATEST_YEAR.put("J1","_2022");
    HM_PROVINCE_LATEST_YEAR.put("H2","_2022");
    HM_PROVINCE_LATEST_YEAR.put("Y1","_2022");
    HM_PROVINCE_LATEST_YEAR.put("S1","_2022");
    HM_PROVINCE_LATEST_YEAR.put("T1","_2022");
    HM_PROVINCE_LATEST_YEAR.put("J2","_2022");
    HM_PROVINCE_LATEST_YEAR.put("H4","_2022");
    HM_PROVINCE_LATEST_YEAR.put("G3","_2022");
    HM_PROVINCE_LATEST_YEAR.put("G1","_2022");

    HM_XK.put("lk","理");
    HM_XK.put("wk","文");
    HM_XK.put("wl","物");
    HM_XK.put("ls","史");
    HM_XK.put("hx","化");
    HM_XK.put("dl","地");
    HM_XK.put("zz","政");
    HM_XK.put("sw","生");
    HM_XK.put("xx","信");
    
    HM_PROVINCE_CODE_NAME.put("A1","安徽");
    HM_PROVINCE_CODE_NAME.put("B1","北京");
    HM_PROVINCE_CODE_NAME.put("C1","重庆");
    HM_PROVINCE_CODE_NAME.put("F1","福建");
    HM_PROVINCE_CODE_NAME.put("G1","甘肃");
    HM_PROVINCE_CODE_NAME.put("G2","广东");
    HM_PROVINCE_CODE_NAME.put("G3","广西");
    HM_PROVINCE_CODE_NAME.put("G4","贵州");
    HM_PROVINCE_CODE_NAME.put("H1","海南");
    HM_PROVINCE_CODE_NAME.put("H2","河北");
    HM_PROVINCE_CODE_NAME.put("H3","河南");
    HM_PROVINCE_CODE_NAME.put("H4","黑龙江");
    HM_PROVINCE_CODE_NAME.put("H5","湖北");
    HM_PROVINCE_CODE_NAME.put("H6","湖南");
    HM_PROVINCE_CODE_NAME.put("J1","吉林");
    HM_PROVINCE_CODE_NAME.put("J2","江苏");
    HM_PROVINCE_CODE_NAME.put("J3","江西");
    HM_PROVINCE_CODE_NAME.put("L1","辽宁");
    HM_PROVINCE_CODE_NAME.put("N1","内蒙古");
    HM_PROVINCE_CODE_NAME.put("N2","宁夏");
    HM_PROVINCE_CODE_NAME.put("Q1","青海");
    HM_PROVINCE_CODE_NAME.put("S1","山东");
    HM_PROVINCE_CODE_NAME.put("S2","山西");
    HM_PROVINCE_CODE_NAME.put("S3","陕西");
    HM_PROVINCE_CODE_NAME.put("S4","上海");
    HM_PROVINCE_CODE_NAME.put("S5","四川");
    HM_PROVINCE_CODE_NAME.put("T1","天津");
    HM_PROVINCE_CODE_NAME.put("X1","新疆");
    HM_PROVINCE_CODE_NAME.put("Y1","云南");
    HM_PROVINCE_CODE_NAME.put("Z1","浙江");

    HM_PROVINCE_CODE.put("安徽","A1_AH");
    HM_PROVINCE_CODE.put("北京","B1_BJ");
    HM_PROVINCE_CODE.put("重庆","C1_CQ");
    HM_PROVINCE_CODE.put("福建","F1_FJ");
    HM_PROVINCE_CODE.put("甘肃","G1_GS");
    HM_PROVINCE_CODE.put("广东","G2_GD");
    HM_PROVINCE_CODE.put("广西","G3_GX");
    HM_PROVINCE_CODE.put("海南","H1_HN");
    HM_PROVINCE_CODE.put("河北","H2_HB");
    HM_PROVINCE_CODE.put("河南","H3_HN");
    HM_PROVINCE_CODE.put("黑龙江","H4_HL");
    HM_PROVINCE_CODE.put("湖南","H6_HN");
    HM_PROVINCE_CODE.put("江苏","J2_JS");
    HM_PROVINCE_CODE.put("江西","J3_JX");
    HM_PROVINCE_CODE.put("辽宁","L1_LN");
    HM_PROVINCE_CODE.put("内蒙古","N1_NM");
    HM_PROVINCE_CODE.put("宁夏","N2_NX");
    HM_PROVINCE_CODE.put("青海","Q1_QH");
    HM_PROVINCE_CODE.put("山东","S1_SD");
    HM_PROVINCE_CODE.put("上海","S4_SH");
    HM_PROVINCE_CODE.put("四川","S5_SC");
    HM_PROVINCE_CODE.put("云南","Y1_YN");
    HM_PROVINCE_CODE.put("天津","T1_TJ");
    HM_PROVINCE_CODE.put("贵州","G4_GZ");
    HM_PROVINCE_CODE.put("湖北","H5_HB");
    HM_PROVINCE_CODE.put("吉林","J1_JL");
    HM_PROVINCE_CODE.put("山西","S2_SX");
    HM_PROVINCE_CODE.put("新疆","X1_XJ");
    HM_PROVINCE_CODE.put("浙江","Z1_ZJ");
    HM_PROVINCE_CODE.put("河北","H2_HB");
    HM_PROVINCE_CODE.put("吉林","J1_JL");
    HM_PROVINCE_CODE.put("辽宁","L1_LN");
    HM_PROVINCE_CODE.put("内蒙古","N1_NM");
    HM_PROVINCE_CODE.put("山东","S1_SD");
    HM_PROVINCE_CODE.put("上海","S4_SH");
    HM_PROVINCE_CODE.put("浙江","Z1_ZJ");
    HM_PROVINCE_CODE.put("重庆","C1_CQ");
    HM_PROVINCE_CODE.put("陕西","S3_SX");

    
    
    HM_LMGX.put("C9联盟","北京大学,清华大学,哈尔滨工业大学,复旦大学,上海交通大学,南京大学,浙江大学,中国科学技术大学,西安交通大学");
    HM_LMGX.put("长三角医学教育联盟","复旦大学,上海交通大学,上海中医药大学,南京医科大学,苏州大学,南京中医药大学,浙江大学,温州医科大学,中国科学技术大学,安徽医科大学");
    HM_LMGX.put("北京卓越医学人才联盟","首都医科大学,北京协和医学院,北京大学医学部,北京中医药大学,天津医科大学,河北医科大学");
    HM_LMGX.put("西部中医药高校联盟","成都中医药大学,陕西中医药大学,云南中医药大学,广西中医药大学,贵州中医药大学,甘肃中医药大学,西藏藏医药大学,内蒙古医科大学,青海大学,宁夏医科大学,重庆医科大学,新疆医科大学");
    HM_LMGX.put("北京高科联盟","北京邮电大学,西安电子科技大学,北京交通大学,北京科技大学,北京化工大学,北京林业大学,华北电力大学,哈尔滨工程大学,中国地质大学（北京）,中国矿业大学（北京）,中国石油大学（北京）,燕山大学,大连海事大学");
    HM_LMGX.put("行业优质高校联盟","中国地质大学,华东理工大学,中国矿业大学,中国石油大学（华东）,东华大学,河海大学,江南大学,南京农业大学,东北林业大学,合肥工业大学,西南交通大学,西安电子科技大学,长安大学");
    HM_LMGX.put("E9联盟","北京理工大学,重庆大学,大连理工大学,东南大学,哈尔滨工业大学,华南理工大学,天津大学,同济大学,西北工业大学");
    HM_LMGX.put("Z14联盟","河北大学,山西大学,内蒙古大学,南昌大学,郑州大学,广西大学,海南大学,贵州大学,云南大学,西藏大学,青海大学,宁夏大学,新疆大学,石河子大学");
    HM_LMGX.put("G7联盟","北京航空航天大学,北京理工大学,哈尔滨工业大学,哈尔滨工程大学,南京航空航天大学,南京理工大学,西北工业大学");
    HM_LMGX.put("武汉七校联盟","武汉大学,华中科技大学,武汉理工大学,中南财经政法大学,华中师范大学,中国地质大学,华中农业大学");
    HM_LMGX.put("长安联盟","陕西师范大学,西北大学,西安外国语大学,西北政法大学,西安邮电大学");
    HM_LMGX.put("延河联盟","中国人民大学,北京理工大学,中国农业大学,北京外国语大学,中央音乐学院,中央美术学院,中央戏剧学院,中央民族大学,延安大学");
    HM_LMGX.put("华东五校联盟","复旦大学,浙江大学,上海交通大学,南京大学,中国科技大学");
    HM_LMGX.put("行星科学联盟","中国科学院大学,北京大学,中国科学技术大学,中山大学,澳门科技大学,北京航空航天大学,哈尔滨工业大学（深圳）,南京大学,中国地质大学（武汉）,山东大学,桂林理工大学,南京信息工程大学,南方科技大学,武汉大学,清华大学,北京师范大学,中国地质大学（北京）,同济大学,洛阳师范学院,中南民族大学,南京航空航天大学,华北电力大学,南昌大学,香港大学,重庆大学,吉林大学,浙江大学");
    HM_LMGX.put("长三角特色大学联盟","中国计量大学,安徽工业大学,安徽理工大学,杭州电子科技大学,江苏海洋大学,江苏科技大学,苏州科技大学,南京审计大学,南京信息工程大学,上海电力大学,上海海关学院,浙江海洋大学,浙江理工大学");
    HM_LMGX.put("长三角高校合作联盟","复旦大学,上海交通大学,华东师范大学,同济大学,南京大学,东南大学,浙江大学,中国科学技术大学");
    HM_LMGX.put("地方C9联盟","山西大学,云南大学,内蒙古大学,辽宁大学,西北大学,苏州大学,郑州大学,南昌大学,新疆大学");
    HM_LMGX.put("地方高水平大学联盟","苏州大学,上海大学,郑州大学,南昌大学");
    HM_LMGX.put("重庆市大学联盟","重庆大学,西南大学,陆军军医大学,西南政法大学,重庆医科大学,四川外国语大学");

    
    HM_LHPY.put("中山大学","香港大学");
    HM_LHPY.put("电子科技大学","西南财经大学");
    HM_LHPY.put("西安电子科技大学","西北工业大学,西北大学");
    HM_LHPY.put("西南交通大学","中科院高能物理研究所");
    HM_LHPY.put("西北大学","西北工业大学,西安电子科技大学");
    HM_LHPY.put("云南大学","复旦大学,中国科学院");
    HM_LHPY.put("安徽大学","中科院动物研究所");
    HM_LHPY.put("南京信息工程大学","中国科学院大学");
    HM_LHPY.put("华南农业大学","澳门科技大学");
    HM_LHPY.put("广西大学","华南理工大学");
    HM_LHPY.put("新疆大学","西安交通大学");
    HM_LHPY.put("西南科技大学","中国工程物理研究院");
    HM_LHPY.put("青岛农业大学","中国海洋大学");
    HM_LHPY.put("河南工业大学","台湾中原大学");
    HM_LHPY.put("西藏大学","西南交通大学");
    HM_LHPY.put("贵州师范大学","厦门大学");
    HM_LHPY.put("新疆师范大学","华东师范,河北师范,西北师范,浙江师范,大连理工大学");
    HM_LHPY.put("吉首大学","中山大学");
    HM_LHPY.put("长江师范学院","山东科技大学");
    HM_LHPY.put("闽南师范大学","台湾高校群");
    HM_LHPY.put("福建工程学院","台湾高校群");
    HM_LHPY.put("井冈山大学","同济大学,厦门大学");
    HM_LHPY.put("泉州师范学院","台湾辅仁大学,台湾龙华科技大学");
    HM_LHPY.put("黑龙江工程学院","中兴通讯股份有限公司");
    HM_LHPY.put("乐山师范学院","武汉大学");
    HM_LHPY.put("重庆三峡学院","东南大学");
    HM_LHPY.put("天水师范学院","西南交通大学");
    HM_LHPY.put("河西学院","复旦大学");
    HM_LHPY.put("六盘水师范学院","上海工程技术大学");
    HM_LHPY.put("兰州文理学院","中国传媒大学");
    HM_LHPY.put("贵州工程应用技术学院","西南大学,中国矿业大学");
    HM_LHPY.put("新疆工程学院","北京科技大学,北京邮电大学,中国矿业大学");
    HM_LHPY.put("新疆理工学院","浙江工业大学");
    HM_LHPY.put("天津医科大学","天津大学");
    HM_LHPY.put("北方民族大学","合肥工业大学");
    HM_LHPY.put("北京外国语大学","中国政法大学");
    HM_LHPY.put("宁夏理工学院","东北大学,浙江工业大学,陕西师范大学");
    HM_LHPY.put("遵义医科大学","广州医科大学");
    HM_LHPY.put("重庆医科大学","西南大学,复旦大学");
    HM_LHPY.put("中国政法大学","北京外国语大学");
    HM_LHPY.put("西南财经大学","电子科技大学");
    HM_LHPY.put("浙江警察学院","华东政法大学");
    HM_LHPY.put("呼和浩特民族学院","中央民族大学");
    HM_LHPY.put("电子科技大学中山学院","电子科技大学");
    HM_LHPY.put("中国消防救援学院","中国民用航空飞行学院");
    HM_LHPY.put("中国石油大学（北京）克拉玛依校区","电子科技大学");
    HM_LHPY.put("兰州财经大学","中央财经大学,对外经济贸易大学");
    HM_LHPY.put("新疆科技学院","河北科技大学");
    HM_LHPY.put("山东工艺美术学院","山东大学");
    HM_LHPY.put("贵州医科大学","北京协和医学院");
    HM_LHPY.put("银川能源学院","福州大学");
    HM_LHPY.put("上海中医药大学","上海交通大学,华东师范大学");

    YX_LHPY = Tools.convertSetToHashSet(HM_LHPY.keySet());
    
    
    HM_HYHP.put("船舶行业","上海交通大学");
    HM_HYHP.put("航空","西北工业大学");
    HM_HYHP.put("装备制造行业","上海理工大学");
    HM_HYHP.put("粮食行业","河南工业大学");
    HM_HYHP.put("高电压行业","西安交通大学");
    HM_HYHP.put("核潜艇行业","哈尔滨工程大学");
    HM_HYHP.put("轻工食品行业","江南大学");
    HM_HYHP.put("外语外贸行业","北京外国语大学,外交学院,上海外国语大学");
    HM_HYHP.put("矿产行业","中国矿业大学");
    HM_HYHP.put("电线电缆行业","哈尔滨理工大学");
    HM_HYHP.put("橡胶行业","青岛科技大学");
    HM_HYHP.put("煤炭行业","辽宁工程技术大学");
    HM_HYHP.put("气象行业","南京信息工程大学");
    HM_HYHP.put("核工业","南华大学");
    HM_HYHP.put("轨道交通行业","西南交通大学");
    HM_HYHP.put("轴承行业","河南科技大学");
    HM_HYHP.put("质检行业","中国计量大学");
    HM_HYHP.put("钢铁行业","北京科技大学,东北大学");
    HM_HYHP.put("税务行业","吉林财经大学");
    HM_HYHP.put("民航管理干部","中国民用航空飞行学院");
    HM_HYHP.put("银行行业","中央财经大学");
    HM_HYHP.put("有色金属","中南大学");
    HM_HYHP.put("通信行业","北京邮电大学,西安电子科技大学,电子科技大学,桂林电子科技大学,杭州电子科技大学");
    HM_HYHP.put("化工行业","天津大学");
    HM_HYHP.put("审计行业","南京审计大学");
    HM_HYHP.put("公路运输行业","长安大学");
    HM_HYHP.put("外交外事","外交学院");
    HM_HYHP.put("地震系统","防灾科技学院");
    HM_HYHP.put("烹饪行业","扬州大学，四川旅游学院，济南大学");
    HM_HYHP.put("海事行业","大连海事大学");
    HM_HYHP.put("生物医学","东南大学");
    HM_HYHP.put("建筑设计行业","东南大学");
    HM_HYHP.put("计算机行业","国防科技大学");
    HM_HYHP.put("纺织行业","东华大学");
    HM_HYHP.put("烟草行业","河南农业大学");
    HM_HYHP.put("法律行业","西南政法大学");
    HM_HYHP.put("高铁行业","西南交通大学");
    HM_HYHP.put("铁路建设行业","石家庄铁道大学");
    HM_HYHP.put("葡萄酒行业","西北农林科技大学");
    HM_HYHP.put("交通运输","东南大学");
    HM_HYHP.put("铁路行业","北京交通大学");
    HM_HYHP.put("土木行业","同济大学");
    HM_HYHP.put("制药行业","中国药科大学");
    HM_HYHP.put("电力行业","华北电力大学,东北电力大学,长沙理工大学,三峡大学,南京工程学院,沈阳工程学院");
    HM_HYHP.put("遥感测绘","武汉大学");
    HM_HYHP.put("石油行业","东北石油大学");
    HM_HYHP.put("心理咨询","北京师范大学,华中师范大学,西南大学");
    HM_HYHP.put("民航行业","中国民航大学");
    HM_HYHP.put("包装行业","湖南工业大学");
    HM_HYHP.put("陶瓷行业","景德镇陶瓷大学");
    HM_HYHP.put("新能源汽车","北京理工大学");
    HM_HYHP.put("机器人和航天","哈尔滨工业大学");
    HM_HYHP.put("风景园林","北京林业大学");
    HM_HYHP.put("汽车行业","合肥工业大学");
    HM_HYHP.put("新闻传播","中国传媒大学");
    HM_HYHP.put("对外汉语行业","北京外国语大学");
    HM_HYHP.put("车辆行业","吉林大学");
    HM_HYHP.put("制造业","上海理工大学");
    HM_HYHP.put("水利行业","河海大学");
    HM_HYHP.put("茶行业","安徽农业大学");
    HM_HYHP.put("金融行业","西南财经大学,对外经济贸易大学,中央财经大学");
    HM_HYHP.put("建材建工","武汉理工大学");
    HM_HYHP.put("兵器行业","北京理工大学");

    
    HM_YBWS.put("A安监:原安监局","华北科技学院");
    HM_YBWS.put("B包装:原包装公司","湖南工业大学");
    HM_YBWS.put("C材料:原冶金部","北京科技大学,东北大学,西安建筑科技大学,武汉科技大学,辽宁科技大学,安徽工业大学,内蒙古科技大学,青岛理工大学,辽宁科技学院,长春工业大学,沈阳大学,重庆科技学院,黑龙江工程学院");
    HM_YBWS.put("C材料:原有色金属","中南大学,昆明理工大学,北方工业大学,长春师范大学,江西理工大学,桂林理工大学,北华大学,长春工程学院,嘉兴学院");
    HM_YBWS.put("C财经:原财政部","上海财经大学,中央财经大学,中南财经政法大学,东北财经大学,江西财经大学,山东财经大学");
    HM_YBWS.put("C财经:原供销社","山西财经大学,安徽财经大学");
    HM_YBWS.put("C财经:原人民银行","西南财经大学,对外经济贸易大学,湖南大学,西安交通大学,河北金融学院,长春金融高等专科学校,哈尔滨金融学院,上海立信会计金融学院,南京审计大学,湖北经济学院,广东金融学院");
    HM_YBWS.put("C财经:原商业部","浙江工商大学,哈尔滨商业大学,北京工商大学,天津商业大学,河南工业大学,武汉轻工大学,北京物资学院,南京财经大学,重庆工商大学,兰州财经大学,四川旅游学院");
    HM_YBWS.put("C财经:原统计局","西安财经大学");
    HM_YBWS.put("C财税:原税务局","吉林财经大学");
    HM_YBWS.put("C财政:原审计署","南京审计大学");
    HM_YBWS.put("C测绘:原测绘局","武汉大学");
    HM_YBWS.put("C船舶:原船舶集团","哈尔滨工程大学,江苏科技大学,武汉船舶职业技术学院");
    HM_YBWS.put("D地质:原地震局","防灾科技学院");
    HM_YBWS.put("D地质:原地质部","中国地质大学,吉林大学,成都理工大学,长安大学,河北地质大学");
    HM_YBWS.put("D电力:原电力部","华北电力大学,武汉大学,北京交通大学,东北电力大学,三峡大学,长沙理工大学,上海电力学院,山西工程学院,沈阳工程学院,长春工程学院,南京工程学院,山东电力高等专科学校,郑州电力高等专科学校,西安电力高等专科学校,重庆电力高等专科学校");
    HM_YBWS.put("D电信:原邮电部","北京邮电大学,南京邮电大学,吉林大学,重庆邮电大学,西安邮电学院,石家庄邮电职业技术学院");
    HM_YBWS.put("D电子:原电子部","电子科技大学,西安电子科技大学,杭州电子科技大学,桂林电子科技大学,北京信息科技大学");
    HM_YBWS.put("F法学:原司法部","中央司法警官学院,中国政法大学,中南财经政法大学,西南政法大学,华东政法大学,西北政法大学");
    HM_YBWS.put("F纺织:原纺织部","东华大学,天津工业大学,苏州大学,西安工程大学,浙江理工大学,北京服装学院,中原工学院,武汉纺织大学,南通大学,苏州大学");
    HM_YBWS.put("H航天:原航天部","哈尔滨工业大学,北京航空航天大学,西北工业大学,南京航空航天大学,南昌航空大学,沈阳航空航天大学,郑州航空工业管理学院,华北航天工业学院,成都航空职业技术学院,桂林航空工业学院,西安航空学院");
    HM_YBWS.put("H核能:原核工部","南华大学,东华理工大学,苏州大学,内蒙古工业大学,杭州电子科技大学");
    HM_YBWS.put("H化工:原化工部","北京化工大学,南京工业大学,郑州大学,青岛科技大学,沈阳化工大学,武汉工程大学,吉林化工学院,南京师范大学,江苏海洋大学");
    HM_YBWS.put("J机械:原机械部","湖南大学,合肥工业大学,吉林大学,武汉理工大学,江苏大学,燕山大学,西安理工大学,上海理工大学,沈阳工业大学,哈尔滨理工大学,兰州理工大学,河南科技大学,太原科技大学,北京信息科技大学,湖北汽车工业学院,沈阳理工大学,湖南工程学院,河南工业大学,南京工程学院,长春汽车工业高等专科学校");
    HM_YBWS.put("J建材:原建材局","武汉理工大学,同济大学,西南科技大学,济南大学,洛阳理工学院");
    HM_YBWS.put("J建筑:原建设部","重庆大学,长安大学,华中科技大学,哈尔滨工业大学,沈阳建筑大学,南京工业大学,苏州科技大学");
    HM_YBWS.put("J交通:原交通部","大连海事大学,长安大学,武汉理工大学,东南大学,上海海事大学,长沙理工大学,南通大学,重庆交通大学,山东交通学院,广州航海学院");
    HM_YBWS.put("J交通:原铁道部","西南交通大学,北京交通大学,中南大学,同济大学,东南大学,大连交通大学,兰州交通大学,华东交通大学,石家庄铁道大学,苏州科技大学");
    HM_YBWS.put("J经贸:原外贸部","对外经济贸易大学,南开大学,上海对外经贸大学,广东外语外贸大学");
    HM_YBWS.put("J军工:原兵工部","北京理工大学,南京理工大学,长春理工大学,中北大学,西安工业大学,沈阳理工大学,重庆理工大学,包头职业技术学院");
    HM_YBWS.put("L林业:原林业部","北京林业大学,东北林业大学,西北农林科技大学,南京森林警察学院,南京林业大学,中南林业科技大学,西南林业大学");
    HM_YBWS.put("L旅游:原旅游局","北京第二外国语学院,上海旅游高等专科学校");
    HM_YBWS.put("L农业:原农业部","中国农业大学,南京农业大学,西北农林科技大学,华中农业大学,西南大学,沈阳农业大学,华南农业大学,东北农业大学,海南大学,上海海洋大学,石河子大学,大连海洋大学,塔里木大学");
    HM_YBWS.put("L烟草:原烟草公司","中国科学技术大学");
    HM_YBWS.put("M媒体:原出版总署","北京印刷学院,上海出版印刷高等专科学校");
    HM_YBWS.put("M煤炭:原煤炭部","中国矿业大学,辽宁工程技术大学,山东科技大学,西安科技大学,河南理工大学,太原理工大学,安徽理工大学,华北理工大学,河北工程大学,山东工商学院,湖南科技大学,黑龙江科技大学,淮北师范大学,北京工业职业技术学院,哈尔滨医科大学");
    HM_YBWS.put("Q气象:原气象局","南京信息工程大学,成都信息工程大学");
    HM_YBWS.put("Q轻工:原轻工部","清华大学,江南大学,天津科技大学,陕西科技大学,北京工商大学,景德镇陶瓷大学,郑州轻工业大学,大连工业大学");
    HM_YBWS.put("S石油:原石油部","中国石油大学,东北石油大学,西安石油大学,长江大学,辽宁石油化工大学,常州大学,北京石油化工学院,承德石油高等专科学校,广东石油化工学院,山东石油化工学院,西南石油大学");
    HM_YBWS.put("S水利:原水利部","河海大学,华北水利水电大学,南昌工程学院,黄河水利职业技术学院");
    HM_YBWS.put("T体育:原体委","北京体育大学,上海体育学院,成都体育学院,武汉体育学院,广州体育学院,沈阳体育学院,西安体育学院");
    HM_YBWS.put("Y药学:原药监局","中国药科大学,沈阳药科大学");
    HM_YBWS.put("Y医学:原卫生部","北京大学,复旦大学,中山大学,四川大学,华中科技大学,中南大学,吉林大学,山东大学,西安交通大学,北京协和医学院,中国医科大学");
    HM_YBWS.put("Y医学:原中医药","北京中医药大学,广州中医药大学");
    HM_YBWS.put("Y艺术:原文化部","中央音乐学院,中央美术学院,中央戏剧学院,中国美术学院,上海音乐学院,上海戏剧学院,北京电影学院,中国音乐学院,中国戏曲学院,北京舞蹈学院");
    HM_YBWS.put("Z政法:原劳动部","中国人民大学,天津职业技术师范大学");
    HM_YBWS.put("Z政法:原民政部","长沙民政职业技术学院");
    HM_YBWS.put("Z质检:原质检局","中国计量大学");

    
    
    HM_HYMX.put("A安全:应急部","中国消防救援学院,华北科技学院");
    HM_HYMX.put("C财经:两财一贸","中央财经大学,对外经济贸易大学,上海财经大学");
    HM_HYMX.put("C财经:金融四校","西南财经大学,西安交通大学,对外经济贸易大学,湖南大学");
    HM_HYMX.put("C财经:老八校","中央财经大学,对外经济贸易大学,上海财经大学,中南财经政法大学,东北财经大学,江西财经大学,山东财经大学,西南财经大学");
    HM_HYMX.put("C财经:六星财大","中央财经大学,上海财经大学,对外经济贸易大学,西南财经大学,中南财经政法大学,东北财经大学");
    HM_HYMX.put("C财经:五朵金花","中央财经大学,对外经济贸易大学,上海财经大学,中南财经政法大学,西南财经大学");
    HM_HYMX.put("C财经:国贸三星","对外经济贸易大学,上海对外经贸大学,广东外语外贸大学");
    HM_HYMX.put("D电气:二龙四虎","华北电力大学,武汉大学,清华大学,西安交通大学,浙江大学,华中科技大学");
    HM_HYMX.put("D电信:电信六子","北京邮电大学,南京邮电大学,西安邮电大学,重庆邮电大学,长春邮电大学,石家庄邮电职业技术学院");
    HM_HYMX.put("D电信:两电一邮","电子科技大学,西安电子科技大学,北京邮电大学");
    HM_HYMX.put("D电信:四电四邮","电子科技大学,西安电子科技大学,杭州电子科技大学,桂林电子科技大学,京邮电大学,南京邮电大学,重庆邮电大学,西安邮电大学");
    HM_HYMX.put("D电子:五朵金花","电子科技大学,西安电子科技大学,杭州电子科技大学,桂林电子科技大学,北京信息科技大学");
    HM_HYMX.put("F法学:五院四系","北京大学,中国人民大学,武汉大学,吉林大学,中国政法大学,中南财经政法大学,华东政法大学,西南政法大学,西北政法大学");
    HM_HYMX.put("G工科:八大工院","北京理工大学,大连理工大学,东南大学,四川大学,东北大学,华中理工大学,华中科技大学,华南理工大学,西北工业大学");
    HM_HYMX.put("G工科:工科七子","清华大学,浙江大学,哈尔滨工业大学,上海交通大学,华中科技大学,天津大学,西安交通大学");
    HM_HYMX.put("H海关:海关总署","上海海关学院");
    HM_HYMX.put("H航天:国防七子","北京航空航天大学,北京理工大学,哈尔滨工业大学,西北工业大学,哈尔滨工程大学,南京航空航天大学,南京理工大学");
    HM_HYMX.put("H航天:工息部","哈尔滨工业大学,北京航空航天大学,西北工业大学,南京航空航天大学,南昌航空大学,沈阳航空航天大学,郑州航空工业管理学院,华北航天工业学院,成都航空职业技术学院,桂林航空工业学院,西安航空学院");
    HM_HYMX.put("J建筑:三驾马车","清华大学,同济大学,湖南大学");
    HM_HYMX.put("J建筑:老八校","清华大学,东南大学,天津大学,同济大学,哈尔滨工业大学,华南理工大学,重庆大学,西安建筑科技大学");
    HM_HYMX.put("J建筑:新八校","浙江大学,湖南大学,沈阳建筑大学,大连理工大学,深圳大学,华中科技大学,上海交通大学,南京大学");
    HM_HYMX.put("J建筑:新四军","南京大学,浙江大学,湖南大学,沈阳建筑大学");
    HM_HYMX.put("J建筑:总公司","重庆大学");
    HM_HYMX.put("J军工:军工六校","国防科技大学,哈尔滨工程大学,南京理工大学,陆军工程大学,陆军装甲兵学院,陆军防化学院");
    HM_HYMX.put("J军工:总装备部","长沙航空职业技术学院");
    HM_HYMX.put("J机械:五虎","清华大学,上海交通大学,华中科技大学,西安交通大学,哈尔滨工业大学");
    HM_HYMX.put("J机械:四小龙","吉林大学,湖南大学,燕山大学,合肥工业大学");
    HM_HYMX.put("K科研:中科院","中国科学技术大学,中国科学院,大学");
    HM_HYMX.put("M民航:民航局","中国民航大学,中国民用航空飞行学院,上海民航职业技术学院,广州民航职业技术学院");
    HM_HYMX.put("M民族:国家民委","中央民族大学,大连民族大学,中南民族大学,西南民族大学,西北民族大学,北方民族大学");
    HM_HYMX.put("Q侨办:侨办","暨南大学,华侨大学");
    HM_HYMX.put("S师范:三南一首","湖南师范大学,南京师范大学,华南师范大学,首都师范大学");
    HM_HYMX.put("S师范:六姐妹","北京师范大学,华东师范大学,华中师范大学,东北师范大学,陕西师范大学,西南大学");
    HM_HYMX.put("T统计:四大天王","中国人民大学,厦门大学,天津财经大学,浙江工商大学");
    HM_HYMX.put("W外交:外交部","外交学院");
    HM_HYMX.put("W外语:九大外院","北京外国语大学,上海外国语大学,北京语言大学,北京第二外国语学院,广东外语外贸大学,四川外国语大学,西安外国语大学,天津外国语大学,大连外国语大学");
    HM_HYMX.put("W文理:文理八校","北京大学,复旦大学,南京大学,武汉大学,中山大学,南开大学,北京师范大学,厦门大学");
    HM_HYMX.put("X新闻:南北新闻","复旦大学,中国人民大学");
    HM_HYMX.put("Y药学:南药北药","中国药科大学,沈阳药科大学");
    HM_HYMX.put("Y医学:四大军医","南方医科大学,海军军医大学,陆军军医大学,空军军医大学");
    HM_HYMX.put("Y医学:霸主","北京协和医学院");
    HM_HYMX.put("Y医学:三雄","北京大学,上海交通大学,复旦大学,上");
    HM_HYMX.put("Y医学:十大金刚","浙江大学,首都医科大学,天津医科大学,南京医科大学,山东大学,哈尔滨医科大学,西安交通大学,吉林大学,中国医科大学,重庆医科大学");
    HM_HYMX.put("Y医学:四精英","华中科技大学,中山大学,四川大学,中南大学");
    HM_HYMX.put("Y艺术:八大美院","中央美术学院,中国美术学院,西安美术学院,四川美术学院,鲁迅美术学院,广州美术学院,湖北美术学院,天津美术学院");
    HM_HYMX.put("Z政法:办公厅","北京电子科技学院");
    HM_HYMX.put("Z政法:公安部","中国人民公安大学,中国刑事警察学院,中国人民警察大学,铁道警察学院");
    HM_HYMX.put("Z政法:全国妇联","中华女子学院");
    HM_HYMX.put("Z政法:团中央","中国青年政治学院");
    HM_HYMX.put("Z政法:总工会","中国劳动关系学院");
    HM_HYMX.put("R软件:特色示范","北京大学,清华大学,北京交通大学,北京航空航天大学,北京理工大学,北京邮电大学,天津大学,大连理工大学,东北大学,吉林大学,哈尔滨工业大学,哈尔滨工程大学,复旦大学,同济大学,上海交通大学,华东师范大学,南京大学,苏州大学,南京航空航天大学,浙江大学,中国科学技术大学,厦门大学,山东大学,中国石油大学,武汉大学,湖南大学,中南大学,电子科技大学,重庆大学,西安交通大学,西北工业大学,西安电子科技大学,国防科技大学");
    HM_HYMX.put("G高薪:烟草行业","郑州轻工业大学,河南农业大学,山东农业大学,湖南农业大学,贵州大学,四川农业大学,云南农业大学,西昌学院,青岛农业大学,安徽农业大学");
    HM_HYMX.put("G高薪:白酒行业","四川轻化工大学,茅台学院,江南大学,贵州大学,湖北工业大学,陕西科技大学,北京工商大学,齐鲁工业大学,吉林农业大学,武汉轻工大学,北京农学院,绍兴文理学院,河北科技师范学院,贵州理工学院,河南牧业经济学院,四川旅游学院,四川工业科技学院,毫州学院,四川大学,锦江学院,湖北大学,知行学院");
    HM_HYMX.put("G高薪:化妆品行业","上海应用技术大学,北京工商大学,广东工业大学,齐鲁工业大学,郑州轻工业大学,湖南理工学院,江南大学,华东理工大学,暨南大学");
    HM_HYMX.put("G高薪:殡葬行业","长沙民政职业技术学院,武汉民政职业学院,北京社会管理职业学院,重庆城市管理职业学院,安徽城市职业管理学院");
    HM_HYMX.put("J交通:铁路行业","苏州科技学院,同济大学,东南大学,兰州交通大学,中南大学,大连交通大学,华东交通大学,西南交通大学,北京交通大学,郑州铁路职业技术学院,西安铁路职业技术学院,南京铁道职业技术学院,陕西铁路职业技术学院,武汉铁路职业技术学院,山东职业学院,武汉铁路桥梁职业技术学院,湖南铁道职业技术学院,哈尔滨铁道职业技术学院,湖南高速铁路职业技术学院,黑龙江交通职业技术学院,湖南铁道科技职业技术学院,吉林铁道职业技术学院,广州铁路职业技术学院,辽宁铁道职业技术学院,柳州铁道职业技术学院,辽宁轨道交通职业学院,安徽交通职业技术学院,包头铁道职业技术学院,昆明铁道职业技术学院,天津铁道职业技术学院,四川铁道职业技术学院,石家庄铁路职业技术学院,湖北铁道运输职业学院,新疆铁道职业技术学院,山西铁道职业技术学院,河北轨道交通职业学院");

  }
  
  public static void main(String[] args) {
//    for (int i = 1101; i <= 1600; i++) {
//      String seq = "200" + i;
//      String rd = String.valueOf(Math.random());
//      seq = seq.substring(seq.length() - 4);
//      String prefixSEQ = seq.substring(0, 2);
//      String subfixSEQ = seq.substring(2);
//      String passwd = rd.substring(2, 6);
//      String cardid = "K" + prefixSEQ + rd.substring(7, 9) + subfixSEQ;
//      System.out.println("insert into base_card(C_ID,C_PASSWD,C_SCORE,C_CREATE,C_STATUS) VALUES('" + cardid + 
//          "','" + passwd + "',0,NOW(),1);");
//    } 
  }
  
  
  public List<DWFBean> getSchoolMajorDWF(String yxmc, String zymc, String xk, String year) {
	  
	  HashMap<String, String> mapKLPC = new HashMap<>();
	  mapKLPC.put("一本", "1");
	  mapKLPC.put("二本", "2");
	  mapKLPC.put("专科", "3");
	  mapKLPC.put("本科提前批", "1");
	  mapKLPC.put("专科提前批", "3");
	  mapKLPC.put("理科", "L");
	  mapKLPC.put("文科", "W");
	  
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    List<DWFBean> beanList = new ArrayList<>();
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT yxmc,zymc,xk, pc,zdf FROM s5_sc_"+year+" WHERE xk = ? and yxmc like ? and zymc like ? order by zdf desc limit 0,5";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, xk);
	      ps.setString(2, "%" + yxmc + "%");
	      ps.setString(3, "%" + zymc + "%");
	      rs = ps.executeQuery();
	      DWFBean bean = null;
	      while (rs.next()) {
	    	bean = new DWFBean();
	        bean.setYxmc(rs.getString("yxmc"));
	        bean.setZymc(rs.getString("zymc"));
	        bean.setSlx(rs.getString("zdf"));
	        bean.setXk(rs.getString("xk"));
	        bean.setPc(rs.getString("pc"));
	        String key = mapKLPC.get(bean.getXk()) + mapKLPC.get(bean.getPc()) + year + bean.getSlx();
	        bean.setSlxdwf(DWFCache.mapxx.get(key));
	        beanList.add(bean);
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return beanList;
	  }
  
  public List<BaseCardBean> getCardsByPhone(String phone) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    List<BaseCardBean> beanList = new ArrayList<>();
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT * FROM base_card WHERE C_REMARK = 'WH VIP' and C_PHONE = ? ORDER BY C_ID ASC limit 0,10";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, phone);
	      rs = ps.executeQuery();
	      BaseCardBean bean = null;
	      while (rs.next()) {
	    	bean = new BaseCardBean();
	        bean.setId(rs.getString("C_ID"));
	        bean.setPasswd(rs.getString("C_PASSWD"));
	        bean.setProv(rs.getString("C_PROV"));
	        bean.setXk(rs.getString("C_XK"));
	        bean.setScore(rs.getInt("C_SCORE"));
	        bean.setYear(rs.getInt("C_YEAR"));
	        bean.setStatus(rs.getInt("C_STATUS"));
	        bean.setCreate(rs.getTimestamp("C_CREATE"));
	        bean.setActive(rs.getTimestamp("C_ACTIVE"));
	        bean.setLastLogin(rs.getTimestamp("C_LAST_LOGIN"));
	        bean.setRemark(rs.getString("C_REMARK"));
	        bean.setPhone(rs.getString("C_PHONE"));
	        bean.setOrderId(rs.getString("C_ORDER_ID"));
	        bean.setModule(rs.getString("C_MD"));
	        beanList.add(bean);
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return beanList;
  }
  
  
  
  public BaseCardBean getCardByCID2(String cid) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    BaseCardBean bean = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT * FROM base_card_org WHERE C_ID = ?";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, cid);
	      rs = ps.executeQuery();
	      while (rs.next()) {
	    	bean = new BaseCardBean();
	        bean.setId(rs.getString("C_ID"));
	        bean.setPasswd(rs.getString("C_PASSWD"));
	        
	        return bean;
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return null;
	  }
	 
	 
  public BaseCardBean getCardByCID(String cid) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    BaseCardBean bean = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT * FROM base_card_org WHERE C_ID = ?";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, cid);
	      rs = ps.executeQuery();
	      while (rs.next()) {
	    	bean = new BaseCardBean();
	        bean.setId(rs.getString("C_ID"));
	        bean.setPasswd(rs.getString("C_PASSWD"));
	        bean.setProv(rs.getString("C_PROV"));
	        bean.setXk(rs.getString("C_XK"));
	        bean.setScore(rs.getInt("C_SCORE"));
	        bean.setYear(rs.getInt("C_YEAR"));
	        bean.setStatus(rs.getInt("C_STATUS"));
	        bean.setCreate(rs.getTimestamp("C_CREATE"));
	        bean.setActive(rs.getTimestamp("C_ACTIVE"));
	        bean.setLastLogin(rs.getTimestamp("C_LAST_LOGIN"));
	        bean.setRemark(rs.getString("C_REMARK"));
	        bean.setPhone(rs.getString("C_PHONE"));
	        bean.setOrderId(rs.getString("C_ORDER_ID"));
	        bean.setModule(rs.getString("C_MD"));
	        return bean;
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return null;
	  }
  
  public BaseCardBean getCardByCID2024(String cid) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    BaseCardBean bean = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT * FROM base_card WHERE C_ID = ?";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, cid);
	      rs = ps.executeQuery();
	      while (rs.next()) {
	    	bean = new BaseCardBean();
	        bean.setId(rs.getString("C_ID"));
	        bean.setPasswd(rs.getString("C_PASSWD"));
	        bean.setProv(rs.getString("C_PROV"));
	        bean.setXk(rs.getString("C_XK"));
	        bean.setScore(rs.getInt("C_SCORE"));
	        bean.setYear(rs.getInt("C_YEAR"));
	        bean.setStatus(rs.getInt("C_STATUS"));
	        bean.setCreate(rs.getTimestamp("C_CREATE"));
	        bean.setActive(rs.getTimestamp("C_ACTIVE"));
	        bean.setLastLogin(rs.getTimestamp("C_LAST_LOGIN"));
	        bean.setRemark(rs.getString("C_REMARK"));
	        bean.setPhone(rs.getString("C_PHONE"));
	        bean.setOrderId(rs.getString("C_ORDER_ID"));
	        bean.setModule(rs.getString("C_MD"));
	        return bean;
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return null;
	  }
  
  public List<BaseCardBean> getCardsByOrderID(String orderID, String adminID) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    List<BaseCardBean> beanList = new ArrayList<>();
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT * FROM base_card_org WHERE C_AGENT = ? and C_EXT = ?";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, adminID);
	      ps.setString(2, orderID);
	      rs = ps.executeQuery();
	      BaseCardBean bean = null;
	      while (rs.next()) {
	    	bean = new BaseCardBean();
	        bean.setId(rs.getString("C_ID"));
	        bean.setPasswd(rs.getString("C_PASSWD"));
	        bean.setStatus(rs.getInt("C_STATUS"));
	        bean.setCreate(rs.getTimestamp("C_CREATE"));
	        bean.setActive(rs.getTimestamp("C_ACTIVE"));
	        bean.setPhone(rs.getString("C_PHONE"));
	        bean.setExt(rs.getString("C_EXT"));
	        bean.setCtype(rs.getString("C_TYPE"));
	        beanList.add(bean);
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return beanList;
	  }
  

  //（单系统）优志愿卡系统
  public String updateBaseCardOrgForAgentOrderOnlyA(String phone, String orderID,String admin) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      
	      String SQLQUERY = "SELECT C_ID FROM base_card_org WHERE C_STATUS = 1 AND C_TYPE_ORG = 1 ORDER BY C_CREATE ASC limit 1";
	      ps = conn.prepareStatement(SQLQUERY);
	      rs = ps.executeQuery();
	      String cID = null;
	      while (rs.next()) {
	    	  cID = rs.getString(1);
	      }
	      rs.close();
	      ps.close();
	      
	      if(cID == null) {
	    	  return null;
	      }
	      
	      String SQL = "UPDATE base_card_org SET C_PHONE = ?, C_EXT = ?, C_TYPE = 'A' ,C_STATUS = 2, C_ACTIVE = now(), C_AGENT = ? WHERE C_ID = ? AND C_STATUS = 1";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, phone);
	      ps.setString(2, orderID);
	      ps.setString(3, admin);
	      ps.setString(4, cID);
	      ps.executeUpdate();
	      ps.close();
	      
	      String SQL2 = "update yg_admin set CARD_A_CNT = (CARD_A_CNT - 1) where A_ID = ?";
	      System.out.println(SQL2);
	      ps = conn.prepareStatement(SQL2);
	      ps.setString(1, admin);
	      ps.executeUpdate();
	      
	      return cID;
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return null;
	 }
  
//（单系统）四川升学规划指导
  public String updateBaseCardOrgForAgentOrderOnlyC(String phone, String orderID,String admin) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      
	      String SQLQUERY = "SELECT C_ID FROM base_card_org WHERE C_STATUS = 1 AND C_TYPE_ORG = 3 ORDER BY C_CREATE ASC limit 1";
	      ps = conn.prepareStatement(SQLQUERY);
	      rs = ps.executeQuery();
	      String cID = null;
	      while (rs.next()) {
	    	  cID = rs.getString(1);
	      }
	      rs.close();
	      ps.close();
	      
	      if(cID == null) {
	    	  return null;
	      }
	      
	      String SQL = "UPDATE base_card_org SET C_PHONE = ?, C_EXT = ?, C_TYPE = 'C' ,C_STATUS = 2, C_ACTIVE = now(), C_AGENT = ? WHERE C_ID = ? AND C_STATUS = 1";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, phone);
	      ps.setString(2, orderID);
	      ps.setString(3, admin);
	      ps.setString(4, cID);
	      ps.executeUpdate();
	      ps.close();
	      
	      String SQL2 = "update yg_admin set CARD_C_CNT = (CARD_C_CNT - 1) where A_ID = ?";
	      System.out.println(SQL2);
	      ps = conn.prepareStatement(SQL2);
	      ps.setString(1, admin);
	      ps.executeUpdate();
	     	      
	      return cID;
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return null;
	 }
  
  //（单系统）规划师辅助系统
  public String updateBaseCardOrgForAgentOrderOnlyB(String phone, String orderID, String admin) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      
	      String SQLQUERY = "SELECT C_ID FROM base_card_org WHERE C_STATUS = 1 AND C_TYPE_ORG = 2  ORDER BY C_CREATE ASC limit 1";
	      ps = conn.prepareStatement(SQLQUERY);
	      rs = ps.executeQuery();
	      String cID = null;
	      while (rs.next()) {
	    	  cID = rs.getString(1);
	      }
	      rs.close();
	      ps.close();
	      
	      if(cID == null) {
	    	  return null;
	      }
	      
	      String SQL = "UPDATE base_card_org SET C_PHONE = ?, C_EXT = ?, C_TYPE = 'B' ,C_STATUS = 2, C_ACTIVE = now(), C_AGENT = ? WHERE C_ID = ? AND C_STATUS = 1";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, phone);
	      ps.setString(2, orderID);
	      ps.setString(3, admin);
	      ps.setString(4, cID);
	      ps.executeUpdate();
	      ps.close();
	      
	      String SQL2 = "INSERT INTO base_card(C_ID, C_PASSWD, C_CREATE, C_STATUS, C_MD, C_ORDER_ID, C_PHONE) "; 
	      SQL2 += "SELECT C_ID, C_PASSWD, C_CREATE, 1, C_TYPE, C_EXT, C_PHONE FROM base_card_org WHERE C_ID = ?";
	      System.out.println(SQL2);
	      ps = conn.prepareStatement(SQL2);
	      ps.setString(1, cID);
	      ps.executeUpdate();
	      
	      String SQL3 = "update yg_admin set CARD_B_CNT = (CARD_B_CNT - 1) where A_ID = ?";
	      System.out.println(SQL3);
	      ps = conn.prepareStatement(SQL3);
	      ps.setString(1, admin);
	      ps.executeUpdate();
	      
	      return cID;
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return null;
	 }
  
  public String updateBaseCardOrgForAgentOrderOnlyF(String phone, String orderID, String admin) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      
	      String SQLQUERY = "SELECT C_ID FROM base_card_org WHERE C_STATUS = 1 AND C_TYPE_ORG = 4  ORDER BY C_CREATE ASC limit 1";
	      ps = conn.prepareStatement(SQLQUERY);
	      rs = ps.executeQuery();
	      String cID = null;
	      while (rs.next()) {
	    	  cID = rs.getString(1);
	      }
	      rs.close();
	      ps.close();
	      
	      if(cID == null) {
	    	  return null;
	      }
	      
	      String SQL = "UPDATE base_card_org SET C_PHONE = ?, C_EXT = ?, C_TYPE = 'F' ,C_STATUS = 2, C_ACTIVE = now(), C_AGENT = ? WHERE C_ID = ? AND C_STATUS = 1";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, phone);
	      ps.setString(2, orderID);
	      ps.setString(3, admin);
	      ps.setString(4, cID);
	      ps.executeUpdate();
	      ps.close();
	      
	      //todo:目前只支持四川
	      String SQL2 = "INSERT INTO base_card(C_ID, C_PASSWD, C_PROV,  C_CREATE, C_STATUS, C_MD, C_ORDER_ID, C_PHONE, C_SYS_IND) "; 
	      SQL2 += "SELECT C_ID, C_PASSWD, NULL,  C_CREATE, 1, C_TYPE, C_EXT, C_PHONE, 'F' FROM base_card_org WHERE C_ID = ?";
	      System.out.println(SQL2);
	      ps = conn.prepareStatement(SQL2);
	      ps.setString(1, cID);
	      ps.executeUpdate();
	      
	      String SQL3 = "update yg_admin set CARD_F_CNT = (CARD_F_CNT - 1) where A_ID = ?";
	      System.out.println(SQL3);
	      ps = conn.prepareStatement(SQL3);
	      ps.setString(1, admin);
	      ps.executeUpdate();
	      
	      return cID;
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return null;
	 }
  
  //（双系统）优志愿卡系统+规划师辅助系统
  public String updateBaseCardOrgForAgentOrderOnlyD(String phone, String orderID,String admin) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      
	      String SQLQUERY = "SELECT C_ID FROM base_card_org WHERE C_STATUS = 1 AND C_TYPE_ORG = 1  ORDER BY C_CREATE ASC limit 1";
	      ps = conn.prepareStatement(SQLQUERY);
	      rs = ps.executeQuery();
	      String cID = null;
	      while (rs.next()) {
	    	  cID = rs.getString(1);
	      }
	      rs.close();
	      ps.close();
	      
	      if(cID == null) {
	    	  return null;
	      }
	      
	      String SQL = "UPDATE base_card_org SET C_PHONE = ?, C_EXT = ?, C_TYPE = 'D' ,C_STATUS = 2, C_ACTIVE = now(), C_AGENT = ? WHERE C_ID = ? AND C_STATUS = 1";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, phone);
	      ps.setString(2, orderID);
	      ps.setString(3, admin);
	      ps.setString(4, cID);
	      ps.executeUpdate();
	      ps.close();
	      
	      String SQL2 = "INSERT INTO base_card(C_ID, C_PASSWD, C_CREATE, C_STATUS, C_MD, C_ORDER_ID, C_PHONE) "; 
	      SQL2 += "SELECT C_ID, C_PASSWD, C_CREATE, 1, C_TYPE, C_EXT, C_PHONE FROM base_card_org WHERE C_ID = ?";
	      System.out.println(SQL2);
	      ps = conn.prepareStatement(SQL2);
	      ps.setString(1, cID);
	      ps.executeUpdate();
	      
	      String SQL3 = "update yg_admin set CARD_D_CNT = (CARD_D_CNT - 1) where A_ID = ?";
	      System.out.println(SQL3);
	      ps = conn.prepareStatement(SQL3);
	      ps.setString(1, admin);
	      ps.executeUpdate();
	      
	      return cID;
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return null;
	 }
  
  //（双系统）四川升学规划指导+规划师辅助系统
  public String updateBaseCardOrgForAgentOrderOnlyE(String phone, String orderID, String admin) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      
	      String SQLQUERY = "SELECT C_ID FROM base_card_org WHERE C_STATUS = 1 AND C_TYPE_ORG = 3 ORDER BY C_CREATE ASC limit 1";
	      ps = conn.prepareStatement(SQLQUERY);
	      rs = ps.executeQuery();
	      String cID = null;
	      while (rs.next()) {
	    	  cID = rs.getString(1);
	      }
	      rs.close();
	      ps.close();
	      
	      if(cID == null) {
	    	  return null;
	      }
	      
	      String SQL = "UPDATE base_card_org SET C_PHONE = ?, C_EXT = ?, C_TYPE = 'E' ,C_STATUS = 2, C_ACTIVE = now(), C_AGENT = ? WHERE C_ID = ? AND C_STATUS = 1";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, phone);
	      ps.setString(2, orderID);
	      ps.setString(3, admin);
	      ps.setString(4, cID);
	      ps.executeUpdate();
	      ps.close();
	      
	      String SQL2 = "INSERT INTO base_card(C_ID, C_PASSWD, C_PROV, C_CREATE, C_STATUS, C_MD, C_ORDER_ID, C_PHONE, C_SYS_IND) "; 
	      SQL2 += "SELECT C_ID, C_PASSWD, 'S5', C_CREATE, 1, C_TYPE, C_EXT, C_PHONE, 'F' FROM base_card_org WHERE C_ID = ?";
	      System.out.println(SQL2);
	      ps = conn.prepareStatement(SQL2);
	      ps.setString(1, cID);
	      ps.executeUpdate();
	      
	      String SQL3 = "update yg_admin set CARD_E_CNT = (CARD_E_CNT - 1) where A_ID = ?";
	      System.out.println(SQL3);
	      ps = conn.prepareStatement(SQL3);
	      ps.setString(1, admin);
	      ps.executeUpdate();
	      
	      return cID;
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return null;
	 }
  
//（双系统）四川升学规划指导+全程学业规划
  public String updateBaseCardOrgForAgentOrderOnlyG(String phone, String orderID, String admin) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      
	      String SQLQUERY = "SELECT C_ID FROM base_card_org WHERE C_STATUS = 1 AND C_TYPE_ORG = 3 ORDER BY C_CREATE ASC limit 1";
	      ps = conn.prepareStatement(SQLQUERY);
	      rs = ps.executeQuery();
	      String cID = null;
	      while (rs.next()) {
	    	  cID = rs.getString(1);
	      }
	      rs.close();
	      ps.close();
	      
	      if(cID == null) {
	    	  return null;
	      }
	      
	      String SQL = "UPDATE base_card_org SET C_PHONE = ?, C_EXT = ?, C_TYPE = 'G' ,C_STATUS = 2, C_ACTIVE = now(), C_AGENT = ? WHERE C_ID = ? AND C_STATUS = 1";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, phone);
	      ps.setString(2, orderID);
	      ps.setString(3, admin);
	      ps.setString(4, cID);
	      ps.executeUpdate();
	      ps.close();
	      
	      String SQL2 = "INSERT INTO base_card(C_ID, C_PASSWD, C_PROV, C_CREATE, C_STATUS, C_MD, C_ORDER_ID, C_PHONE, C_SYS_IND) "; 
	      SQL2 += "SELECT C_ID, C_PASSWD, NULL, C_CREATE, 1, C_TYPE, C_EXT, C_PHONE, 'F' FROM base_card_org WHERE C_ID = ?";
	      System.out.println(SQL2);
	      ps = conn.prepareStatement(SQL2);
	      ps.setString(1, cID);
	      ps.executeUpdate();
	      
	      String SQL3 = "update yg_admin set CARD_G_CNT = (CARD_G_CNT - 1) where A_ID = ?";
	      System.out.println(SQL3);
	      ps = conn.prepareStatement(SQL3);
	      ps.setString(1, admin);
	      ps.executeUpdate();
	      
	      return cID;
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return null;
	 }
  
  public String updateBaseCardForDouyinOrderOnly(String phone, String orderID,String module, String admin) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQLQUERY = "SELECT C_ID FROM base_card WHERE C_REMARK = 'WH VIP' and C_PHONE IS NULL ORDER BY C_ID ASC limit 1";
	      ps = conn.prepareStatement(SQLQUERY);
	      rs = ps.executeQuery();
	      String cID = null;
	      while (rs.next()) {
	    	  cID = rs.getString(1);
	      }
	      rs.close();
	      ps.close();
	      
	      String SQL = "UPDATE base_card SET C_PHONE = ?, C_ORDER_ID = ?, C_MD = ? ,C_ADMIN = ? WHERE C_ID = ?";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, phone);
	      ps.setString(2, orderID);
	      ps.setString(3, module);
	      ps.setString(4, admin);
	      ps.setString(5, cID);
	      ps.executeUpdate();
	      return cID;
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return null;
	  }
  
  public String updateBaseCardBForDouyinOrderOnly(String phone, String orderID,String module, String admin) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQLQUERY = "SELECT C_ID FROM base_card WHERE C_REMARK = 'WH VIP' and C_PHONE IS NULL ORDER BY C_ID ASC limit 1";
	      ps = conn.prepareStatement(SQLQUERY);
	      rs = ps.executeQuery();
	      String cID = null;
	      while (rs.next()) {
	    	  cID = rs.getString(1);
	      }
	      rs.close();
	      ps.close();
	      
	      String SQL = "UPDATE base_card SET C_PHONE = ?, C_ORDER_ID = ?, C_MD = ?, C_ADMIN = ?, C_STATUS = 0 WHERE C_ID = ?";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, phone);
	      ps.setString(2, orderID);
	      ps.setString(3, module);
	      ps.setString(4, admin);
	      ps.setString(5, cID);
	      ps.executeUpdate();
	      return cID;
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return null;
	  }
  
  public String updateBaseCardBForDouyinOrderOnly2024(String phone, String orderID,String module, String admin) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQLQUERY = "SELECT C_ID FROM base_card WHERE C_REMARK = 'WH2024-AUTO' and C_PHONE IS NULL ORDER BY C_ID ASC limit 1";
	      ps = conn.prepareStatement(SQLQUERY);
	      rs = ps.executeQuery();
	      String cID = null;
	      while (rs.next()) {
	    	  cID = rs.getString(1);
	      }
	      rs.close();
	      ps.close();
	      
	      String SQL = "UPDATE base_card SET C_PHONE = ?, C_ORDER_ID = ?, C_MD = ?, C_ADMIN = ?, C_STATUS = 1 WHERE C_ID = ?";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, phone);
	      ps.setString(2, orderID);
	      ps.setString(3, module);
	      ps.setString(4, admin);
	      ps.setString(5, cID);
	      ps.executeUpdate();
	      return cID;
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return null;
	  }

  
  public String updateBaseCardCForDouyinOrderOnly(String phone, String orderID,String module, String admin) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQLQUERY = "SELECT C_ID FROM base_card WHERE C_REMARK = 'WH 198' AND C_ID LIKE 'W%' and C_PHONE IS NULL ORDER BY C_ID ASC limit 1";
	      ps = conn.prepareStatement(SQLQUERY);
	      rs = ps.executeQuery();
	      String cID = null;
	      while (rs.next()) {
	    	  cID = rs.getString(1);
	      }
	      rs.close();
	      ps.close();
	      
	      String SQL = "UPDATE base_card SET C_PHONE = ?, C_ORDER_ID = ?, C_MD = ?, C_ADMIN = ? , C_STATUS = 1 WHERE C_ID = ?";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, phone);
	      ps.setString(2, orderID);
	      ps.setString(3, module);
	      ps.setString(4, admin);
	      ps.setString(5, cID);
	      ps.executeUpdate();
	      return cID;
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return null;
	  }
  
  public List<BaseCardBean> searchCardsByPhoneAndOrderId(String phone, String orderID, String agentID) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    List<BaseCardBean> beanList = new ArrayList<>();
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT * FROM base_card_org WHERE C_STATUS = 2 and C_AGENT = ? and (C_PHONE like ? or C_EXT like ?)  ORDER BY C_ACTIVE DESC limit 0,50";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, agentID);
	      ps.setString(2, "%" + phone + "%");
	      ps.setString(3, "%" + orderID + "%");
	      rs = ps.executeQuery();
	      BaseCardBean bean = null;
	      while (rs.next()) {
	    	bean = new BaseCardBean();
	        bean.setId(rs.getString("C_ID"));
	        bean.setPasswd(rs.getString("C_PASSWD"));
	        bean.setCtype(rs.getString("C_TYPE"));
	        bean.setStatus(rs.getInt("C_STATUS"));
	        bean.setCreate(rs.getTimestamp("C_CREATE"));
	        bean.setActive(rs.getTimestamp("C_ACTIVE"));
	        bean.setPhone(rs.getString("C_PHONE"));
	        bean.setExt(rs.getString("C_EXT"));
	        bean.setAgent(rs.getString("C_AGENT"));
	        beanList.add(bean);
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return beanList;
	  }
  
  public List<BaseCardBean> searchCardsByPhoneAndOrderId_A(String phone, String agentID) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    List<BaseCardBean> beanList = new ArrayList<>();
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT * FROM base_card_org WHERE C_STATUS = 2 and C_AGENT = ? and (C_PHONE like ?)  ORDER BY C_ACTIVE DESC limit 0,200";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, agentID);
	      ps.setString(2, "%" + phone + "%");
	      rs = ps.executeQuery();
	      BaseCardBean bean = null;
	      while (rs.next()) {
	    	bean = new BaseCardBean();
	        bean.setId(rs.getString("C_ID"));
	        bean.setPasswd(rs.getString("C_PASSWD"));
	        bean.setCtype(rs.getString("C_TYPE"));
	        bean.setStatus(rs.getInt("C_STATUS"));
	        bean.setCreate(rs.getTimestamp("C_CREATE"));
	        bean.setActive(rs.getTimestamp("C_ACTIVE"));
	        bean.setPhone(rs.getString("C_PHONE"));
	        bean.setExt(rs.getString("C_EXT"));
	        bean.setAgent(rs.getString("C_AGENT"));
	        beanList.add(bean);
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return beanList;
	  }
  
  public List<BaseCardBean> searchCardsByPhoneAndOrderId_B(String orderID, String agentID) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    List<BaseCardBean> beanList = new ArrayList<>();
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT * FROM base_card_org WHERE C_STATUS = 2 and C_AGENT = ? and (C_EXT like ?)  ORDER BY C_ACTIVE DESC limit 0,200";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, agentID);
	      ps.setString(2, "%" + orderID + "%");
	      rs = ps.executeQuery();
	      BaseCardBean bean = null;
	      while (rs.next()) {
	    	bean = new BaseCardBean();
	        bean.setId(rs.getString("C_ID"));
	        bean.setPasswd(rs.getString("C_PASSWD"));
	        bean.setCtype(rs.getString("C_TYPE"));
	        bean.setStatus(rs.getInt("C_STATUS"));
	        bean.setCreate(rs.getTimestamp("C_CREATE"));
	        bean.setActive(rs.getTimestamp("C_ACTIVE"));
	        bean.setPhone(rs.getString("C_PHONE"));
	        bean.setExt(rs.getString("C_EXT"));
	        bean.setAgent(rs.getString("C_AGENT"));
	        beanList.add(bean);
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return beanList;
	  }
  
  public YGCardBean getYGCardByIDandPasswd(String id, String passwd) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    YGCardBean bean = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT *  FROM yg_card WHERE C_ID = ? and C_PASSWD = ?";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, id.toUpperCase());
	      ps.setString(2, passwd.toUpperCase());
	      rs = ps.executeQuery();
	      while (rs.next()) {
	        bean = new YGCardBean();
	        bean.setId(rs.getString("C_ID"));
	        bean.setPasswd(rs.getString("C_PASSWD"));
	        bean.setPhone(rs.getString("C_PHONE")); 
	        bean.setProv(rs.getString("C_PROV"));
	        bean.setXk(rs.getString("C_XK"));
	        bean.setWc(rs.getInt("C_WC"));
	        bean.setCnt(rs.getInt("C_CNT"));
	        bean.setUsed(rs.getInt("C_USED"));
	        bean.setStatus(rs.getInt("C_STATUS"));
	        bean.setCreate(rs.getTimestamp("C_CREATE"));
	        bean.setLastQuery(rs.getTimestamp("C_LAST_QUERY"));
	        bean.setRemark(rs.getString("C_REMARK")); 
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return bean;
	  }
  
  public YGCardBean getYGCardByPhone(String phone) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    YGCardBean bean = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT *  FROM yg_card WHERE C_PHONE = ?";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, phone);
	      rs = ps.executeQuery();
	      while (rs.next()) {
	    	  bean = new YGCardBean();
		        bean.setId(rs.getString("C_ID"));
		        bean.setPasswd(rs.getString("C_PASSWD"));
		        bean.setPhone(rs.getString("C_PHONE")); 
		        bean.setProv(rs.getString("C_PROV"));
		        bean.setXk(rs.getString("C_XK"));
		        bean.setWc(rs.getInt("C_WC"));
		        bean.setCnt(rs.getInt("C_CNT"));
		        bean.setUsed(rs.getInt("C_USED"));
		        bean.setStatus(rs.getInt("C_STATUS"));
		        bean.setCreate(rs.getTimestamp("C_CREATE"));
		        bean.setLastQuery(rs.getTimestamp("C_LAST_QUERY"));
		        bean.setRemark(rs.getString("C_REMARK")); 
		        bean.setAdmin(rs.getString("C_ADMIN")); 
		        bean.setOpenID(rs.getString("C_OPEN_ID")); 
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return bean;
	  }
  
  public YGCardBean getYGCardByOpenID(String openID) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    YGCardBean bean = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT *  FROM yg_card WHERE C_OPEN_ID = ?";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, openID);
	      rs = ps.executeQuery();
	      while (rs.next()) {
	    	  bean = new YGCardBean();
		        bean.setId(rs.getString("C_ID"));
		        bean.setPasswd(rs.getString("C_PASSWD"));
		        bean.setPhone(rs.getString("C_PHONE")); 
		        bean.setProv(rs.getString("C_PROV"));
		        bean.setXk(rs.getString("C_XK"));
		        bean.setWc(rs.getInt("C_WC"));
		        bean.setCnt(rs.getInt("C_CNT"));
		        bean.setUsed(rs.getInt("C_USED"));
		        bean.setStatus(rs.getInt("C_STATUS"));
		        bean.setCreate(rs.getTimestamp("C_CREATE"));
		        bean.setLastQuery(rs.getTimestamp("C_LAST_QUERY"));
		        bean.setRemark(rs.getString("C_REMARK")); 
		        bean.setAdmin(rs.getString("C_ADMIN")); 
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return bean;
	  }
  
  public YGCardBean getYGCardByPhoneAndAdmin(String phone) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    YGCardBean bean = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT *  FROM yg_card WHERE C_PHONE like ? and C_ADMIN = 1";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, "%" + phone);
	      rs = ps.executeQuery();
	      while (rs.next()) {
	    	  bean = new YGCardBean();
		        bean.setId(rs.getString("C_ID"));
		        bean.setPasswd(rs.getString("C_PASSWD"));
		        bean.setPhone(rs.getString("C_PHONE")); 
		        bean.setProv(rs.getString("C_PROV"));
		        bean.setXk(rs.getString("C_XK"));
		        bean.setWc(rs.getInt("C_WC"));
		        bean.setCnt(rs.getInt("C_CNT"));
		        bean.setUsed(rs.getInt("C_USED"));
		        bean.setStatus(rs.getInt("C_STATUS"));
		        bean.setCreate(rs.getTimestamp("C_CREATE"));
		        bean.setLastQuery(rs.getTimestamp("C_LAST_QUERY"));
		        bean.setRemark(rs.getString("C_REMARK")); 
		        bean.setAdmin(rs.getString("C_ADMIN")); 
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return bean;
	  }
  
  public YGCardBean getYGCardByID(String cid) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    YGCardBean bean = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT *  FROM yg_card WHERE C_ID = ?";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, cid);
	      rs = ps.executeQuery();
	      while (rs.next()) {
	    	  bean = new YGCardBean();
		        bean.setId(rs.getString("C_ID"));
		        bean.setPasswd(rs.getString("C_PASSWD"));
		        bean.setPhone(rs.getString("C_PHONE")); 
		        bean.setProv(rs.getString("C_PROV"));
		        bean.setXk(rs.getString("C_XK"));
		        bean.setWc(rs.getInt("C_WC"));
		        bean.setCnt(rs.getInt("C_CNT"));
		        bean.setUsed(rs.getInt("C_USED"));
		        bean.setStatus(rs.getInt("C_STATUS"));
		        bean.setCreate(rs.getTimestamp("C_CREATE"));
		        bean.setLastQuery(rs.getTimestamp("C_LAST_QUERY"));
		        bean.setRemark(rs.getString("C_REMARK")); 
		        bean.setAdmin(rs.getString("C_ADMIN")); 
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return bean;
	  }
  
  
  public List<YGCardBean> getYGCardListByIdOrPhone(String cid) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    List<YGCardBean> list = new ArrayList<>();
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT *  FROM yg_card WHERE C_ID like ? OR C_PHONE like ? ORDER BY C_UPDATE DESC LIMIT 0, 10";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, "%" + cid + "%");
	      ps.setString(2, "%" + cid + "%");
	      rs = ps.executeQuery();
	      while (rs.next()) {
	    	 YGCardBean bean = new YGCardBean();
	    	 bean.setId(rs.getString("C_ID"));
	    	 bean.setPasswd(rs.getString("C_PASSWD"));
	    	 bean.setPhone(rs.getString("C_PHONE")); 
	    	 bean.setProv(rs.getString("C_PROV"));
	    	 bean.setXk(rs.getString("C_XK"));
	    	 bean.setWc(rs.getInt("C_WC"));
	    	 bean.setCnt(rs.getInt("C_CNT"));
	    	 bean.setUsed(rs.getInt("C_USED"));
	    	 bean.setStatus(rs.getInt("C_STATUS"));
	    	 bean.setCreate(rs.getTimestamp("C_CREATE"));
	    	 bean.setUpdate(rs.getTimestamp("C_UPDATE"));
	    	 bean.setLastQuery(rs.getTimestamp("C_LAST_QUERY"));
	    	 bean.setRemark(rs.getString("C_REMARK")); 
		        bean.setAdmin(rs.getString("C_ADMIN")); 
	    	 list.add(bean);
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return list;
	  }
  
  	public boolean updateYGCard(YGCardBean bean) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "UPDATE yg_card SET C_PHONE = ?, C_PROV = ?, C_XK = ?, C_WC = ?, C_CNT = ?, C_USED = ?, C_STATUS = ?, C_LAST_QUERY = ?, C_REMARK = ? WHERE C_ID = ?";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, bean.getPhone());
	      ps.setString(2, bean.getProv());
	      ps.setString(3, bean.getXk());
	      ps.setInt(4, bean.getWc());
	      ps.setInt(5, bean.getCnt());
	      ps.setInt(6, bean.getUsed());
	      ps.setInt(7, bean.getStatus());
	      if(bean.getLastQuery() == null) {
	    	  bean.setLastQuery(new Date());
	      }
	      ps.setTimestamp(8, new Timestamp(bean.getLastQuery().getTime()));
	      ps.setString(9, bean.getRemark());
	      ps.setString(10, bean.getId());
	      ps.executeUpdate();
	      return true;
	    } catch (Exception ex) { 
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return false;
	  }
  	
  	public boolean updateYGCardWithAdmin(YGCardBean bean) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "UPDATE yg_card SET C_PHONE = ?, C_PROV = ?, C_XK = ?, C_WC = ?, C_CNT = ?, C_USED = ?, C_STATUS = ?, C_LAST_QUERY = ?, C_UPDATE = now(), C_REMARK = ?, C_OPEN_ID = ? WHERE C_ID = ?";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, bean.getPhone());
	      ps.setString(2, bean.getProv());
	      ps.setString(3, bean.getXk());
	      ps.setInt(4, bean.getWc());
	      ps.setInt(5, bean.getCnt());
	      ps.setInt(6, bean.getUsed());
	      ps.setInt(7, bean.getStatus());
	      if(bean.getLastQuery() == null) {
	    	  bean.setLastQuery(new Date());
	      }
	      ps.setTimestamp(8, new Timestamp(bean.getLastQuery().getTime()));
	      ps.setString(9, bean.getRemark());
	      ps.setString(10, bean.getOpenID());
	      ps.setString(11, bean.getId());
	      ps.executeUpdate();
	      return true;
	    } catch (Exception ex) { 
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return false;
	  }
  	
  	public boolean updateYGCardUsedOnly(String id) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "UPDATE yg_card SET C_LAST_QUERY = NOW() , C_USED = (C_USED + 1) WHERE C_ID = ?";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, id);
	      ps.executeUpdate();
	      return true;
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return false;
	  }
  	
  	public boolean deleteYGCardOpenID(String openID) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "UPDATE yg_card SET C_OPEN_ID = NULL WHERE C_OPEN_ID = ?";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, openID);
	      ps.executeUpdate();
	      return true;
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return false;
	  }
  
  	
  	public boolean updateYGCardProvinceOnly(String id, String prov) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "UPDATE yg_card SET C_PROV = ? WHERE C_ID = ?";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, prov);
	      ps.setString(2, id);
	      ps.executeUpdate();
	      return true;
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return false;
	  }
  	
  	public boolean updateYGCardForOpenIDOnly(String id, String openID) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "UPDATE yg_card SET C_OPEN_ID = ? WHERE C_ID = ?";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, openID);
	      ps.setString(2, id);
	      ps.executeUpdate();
	      return true;
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return false;
	  }
  	
  	public boolean insertYGCard(YGCardBean bean) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "INSERT INTO yg_card(C_ID, C_PASSWD, C_PHONE, C_PROV, C_WC, C_XK, C_CNT, C_USED, C_STATUS, C_LAST_QUERY, C_CREATE, C_UPDATE, C_REMARK) VALUES(?,?,?,NULL,0,NULL,?,0,1,NULL,now(),now(),?)";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, bean.getId());
	      ps.setString(2, bean.getPasswd());
	      ps.setString(3, bean.getPhone());
	      ps.setInt(4, bean.getCnt());
	      ps.setString(5, bean.getRemark());
	      
	      ps.executeUpdate();
	      return true;
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return false;
	  }
  	
  	
  	public boolean insertYGPaymentForPrepay(YGPaymentBean bean) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "INSERT INTO wx_payment(P_ID, WX_OPEN_ID, WX_PREPAY_ID, CREATE_TM, STATUS, AMT, ITEM, ITEM_DESC, C_ID_ORG, AGENT_ID_ORG, SHARED_ID_ORG) VALUES(?,?,?,now(),0,?,?,?,?,?,?)";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, bean.getId());
	      ps.setString(2, bean.getOpenId());
	      ps.setString(3, bean.getPrepayId());
	      ps.setInt(4, bean.getAmt());
	      ps.setInt(5, bean.getItem());
	      ps.setString(6, bean.getItemDesc());
	      ps.setString(7, bean.getC_id_org());
	      ps.setString(8, bean.getAgent_id_org());
	      ps.setString(9, bean.getShared_id_org());
	      
	      ps.executeUpdate();
	      return true;
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return false;
	  }
  	
  	public YGPaymentBean getWxUnpaidByOrderIDAndPrepayID(String orderID, String prepayID) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    YGPaymentBean bean = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT * FROM wx_payment WHERE P_ID = ? AND WX_PREPAY_ID = ? AND STATUS = 0";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, orderID);
	      ps.setString(2, prepayID);
	      rs = ps.executeQuery();
	      while (rs.next()) {
	    	bean = new YGPaymentBean();
	    	bean.setId(rs.getString("P_ID"));
	        bean.setOpenId(rs.getString("WX_OPEN_ID"));
	        bean.setPrepayId(rs.getString("WX_PREPAY_ID"));
	        bean.setCreateTm(rs.getTimestamp("CREATE_TM"));
	        bean.setPaidTm(rs.getTimestamp("WX_PAID_TM"));
	        bean.setStatus(rs.getInt("STATUS"));
	        bean.setAmt(rs.getInt("AMT"));
	        bean.setItem(rs.getInt("ITEM"));
	        bean.setItemDesc(rs.getString("ITEM_DESC"));
	        bean.setC_id_org(rs.getString("C_ID_ORG"));
	        bean.setAgent_id_org(rs.getString("AGENT_ID_ORG"));
	        bean.setShared_id_org(rs.getString("SHARED_ID_ORG"));
	        bean.setCardId(rs.getString("CARD_ID"));
	        bean.setCardPass(rs.getString("CARD_PASS"));
	        bean.setPhone(rs.getString("PHONE"));
	        return bean;
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return null;
	  }
  	
  	
  	//发放填报卡
  	public boolean arrayBaseCardForCheckerCard(String agentId, String desc, String cardIdPrefix, String saas_id, int count, int f_level, int review_ai_cnt, int review_maual_cnt, int remote_tb_cnt, Date exp_date) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "UPDATE base_card x SET x.C_DESC = ?, x.C_INTRODUCE = ?, x.C_AGENT = ?, saas_id = ? , C_EXPIRE = ?, review_ai_cnt = ?, review_maual_cnt = ?, remote_tb_cnt = ?,  x.C_AGENT_TM = now(), x.C_INTRODUCE_TM = now() WHERE x.C_PROV IS NULL AND x.C_ACTIVE IS NULL AND x.C_DESC IS NULL AND x.C_ID LIKE '"+cardIdPrefix+"%' ORDER BY rand () LIMIT " +count ;
			ps = conn.prepareStatement(SQL);
			ps.setString(1, desc);
			ps.setString(2, agentId);
			ps.setString(3, agentId);
			ps.setString(4, saas_id);
			ps.setTimestamp(5, exp_date == null ? null : new Timestamp(exp_date.getTime()));
			ps.setInt(6, review_ai_cnt);
			ps.setInt(7, review_maual_cnt);
			ps.setInt(8, remote_tb_cnt);
			SQLLogUtils.printSQL(ps);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
  	
  	public YGPaymentBean getWxPaymentByOrderIDAndPrepayID(String orderID, String prepayID) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    YGPaymentBean bean = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT * FROM wx_payment WHERE P_ID = ? AND WX_PREPAY_ID = ?";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, orderID);
	      ps.setString(2, prepayID);
	      rs = ps.executeQuery();
	      while (rs.next()) {
	    	bean = new YGPaymentBean();
	        bean.setId(rs.getString("P_ID"));
	        bean.setOpenId(rs.getString("WX_OPEN_ID"));
	        bean.setPrepayId(rs.getString("WX_PREPAY_ID"));
	        bean.setCreateTm(rs.getTimestamp("CREATE_TM"));
	        bean.setPaidTm(rs.getTimestamp("WX_PAID_TM"));
	        bean.setStatus(rs.getInt("STATUS"));
	        bean.setAmt(rs.getInt("AMT"));
	        bean.setItem(rs.getInt("ITEM"));
	        bean.setItemDesc(rs.getString("ITEM_DESC"));
	        bean.setC_id_org(rs.getString("C_ID_ORG"));
	        bean.setAgent_id_org(rs.getString("AGENT_ID_ORG"));
	        bean.setShared_id_org(rs.getString("SHARED_ID_ORG"));
	        bean.setCardId(rs.getString("CARD_ID"));
	        bean.setCardPass(rs.getString("CARD_PASS"));
	        bean.setPhone(rs.getString("PHONE"));
	        return bean;
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return null;
	  }
  	public YGPaymentBean getWxPaymentByOrderID(String orderID) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    YGPaymentBean bean = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT * FROM wx_payment WHERE P_ID = ? ";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, orderID);
	      rs = ps.executeQuery();
	      while (rs.next()) {
	    	bean = new YGPaymentBean();
	    	bean.setId(rs.getString("P_ID"));
	        bean.setOpenId(rs.getString("WX_OPEN_ID"));
	        bean.setPrepayId(rs.getString("WX_PREPAY_ID"));
	        bean.setCreateTm(rs.getTimestamp("CREATE_TM"));
	        bean.setPaidTm(rs.getTimestamp("WX_PAID_TM"));
	        bean.setStatus(rs.getInt("STATUS"));
	        bean.setAmt(rs.getInt("AMT"));
	        bean.setItem(rs.getInt("ITEM"));
	        bean.setItemDesc(rs.getString("ITEM_DESC"));
	        bean.setC_id_org(rs.getString("C_ID_ORG"));
	        bean.setAgent_id_org(rs.getString("AGENT_ID_ORG"));
	        bean.setShared_id_org(rs.getString("SHARED_ID_ORG"));
	        bean.setCardId(rs.getString("CARD_ID"));
	        bean.setCardPass(rs.getString("CARD_PASS"));
	        bean.setPhone(rs.getString("PHONE"));
	        return bean;
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return null;
	  }
  	
  	public List<YGPaymentBean> getWxPaymentByWXOpenID(String openID) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    List<YGPaymentBean> list = new ArrayList<>();
	    
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT * FROM wx_payment WHERE WX_OPEN_ID = ? and STATUS in (0,1) ORDER BY STATUS DESC,WX_PAID_TM DESC, CREATE_TM DESC";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, openID);
	      rs = ps.executeQuery();
	      YGPaymentBean bean = null;
	      while (rs.next()) {
	    	bean = new YGPaymentBean();
	    	bean.setId(rs.getString("P_ID"));
	        bean.setOpenId(rs.getString("WX_OPEN_ID"));
	        bean.setPrepayId(rs.getString("WX_PREPAY_ID"));
	        bean.setCreateTm(rs.getTimestamp("CREATE_TM"));
	        bean.setPaidTm(rs.getTimestamp("WX_PAID_TM"));
	        bean.setStatus(rs.getInt("STATUS"));
	        bean.setAmt(rs.getInt("AMT"));
	        bean.setItem(rs.getInt("ITEM"));
	        bean.setItemDesc(rs.getString("ITEM_DESC"));
	        bean.setC_id_org(rs.getString("C_ID_ORG"));
	        bean.setAgent_id_org(rs.getString("AGENT_ID_ORG"));
	        bean.setShared_id_org(rs.getString("SHARED_ID_ORG"));
	        bean.setCardId(rs.getString("CARD_ID"));
	        bean.setCardPass(rs.getString("CARD_PASS"));
	        bean.setPhone(rs.getString("PHONE"));
	        list.add(bean);
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return list;
	  }
  	
  	public boolean updateYGPaymentForSuccessReturn(String orderID, String prepayID) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "UPDATE wx_payment SET WX_PAID_TM = now(), STATUS = 1 WHERE P_ID = ? AND WX_PREPAY_ID = ? AND STATUS <> 1";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, orderID);
	      ps.setString(2, prepayID);
	      ps.executeUpdate();
	      return true;
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return false;
	  }
  	
  	public boolean updateYGPaymentForSuccessReturn(String orderID) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "UPDATE wx_payment SET WX_PAID_TM = now(), STATUS = 1 WHERE P_ID = ? AND STATUS <> 1";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, orderID);
	      ps.executeUpdate();
	      return true;
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return false;
	  }
  	
  	public boolean updateYGPaymentForSuccessPhoneInput(String orderID, String prepayID, String phone, String cardID, String passwd) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "UPDATE wx_payment SET CARD_ID = ?, CARD_PASS = ?, PHONE = ? WHERE P_ID = ? AND WX_PREPAY_ID = ? AND (CARD_ID IS NULL OR LENGTH(CARD_ID) < 2) AND STATUS = 1";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, cardID);
	      ps.setString(2, passwd);
	      ps.setString(3, phone);
	      ps.setString(4, orderID);
	      ps.setString(5, prepayID);
	      ps.executeUpdate();
	      return true;
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return false;
	}
  	
  	public boolean updateYGPaymentWithSuccessCardidAndPass(String orderID, String prepayID, String cardID, String passwd) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "UPDATE wx_payment SET CARD_ID = ?, CARD_PASS = ? WHERE P_ID = ? AND WX_PREPAY_ID = ? AND (CARD_ID IS NULL OR LENGTH(CARD_ID) < 2) AND STATUS = 1";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, cardID);
	      ps.setString(2, passwd);
	      ps.setString(3, orderID);
	      ps.setString(4, prepayID);
	      ps.executeUpdate();
	      return true;
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return false;
	  }
  	
  	public boolean insertYGQuery(YGQueryBean bean) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "INSERT INTO yg_query(C_ID, Q_TYPE, Q_A, Q_B, Q_C, Q_ZYMC, Q_CREATE) VALUES(?,?,?,?,?,?,now())";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, bean.getCid());
	      ps.setInt(2, bean.getType());
	      ps.setString(3, bean.getQa());
	      ps.setString(4, bean.getQb());
	      ps.setString(5, bean.getQc());
	      ps.setString(6, bean.getQzymc());
	      
	      ps.executeUpdate();
	      return true;
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return false;
	  }
  	
  	
  	public boolean insertSMSCODE(String phone, String code) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "INSERT INTO base_smscode(PHONE, CODE, SENT_TM) VALUES(?,?,now())";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, phone);
	      ps.setString(2, code);
	      
	      ps.executeUpdate();
	      return true;
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return false;
	  }
  	
  	public String getLatestSMSCode(String phone) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT * FROM base_smscode WHERE PHONE = ? ORDER BY SENT_TM DESC LIMIT 1";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, phone);
	      rs = ps.executeQuery();
	      while (rs.next()) {
	    	  return rs.getString("CODE");
	      }
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return null;
	  }
  	
  	public boolean checkYGQueryExist(String cid, String a, String b, String c) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT COUNT(1) FROM yg_query WHERE C_ID = ? AND Q_A = ? AND Q_B = ? AND Q_C = ?";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, cid);
	      ps.setString(2, a); //2022
	      ps.setString(3, b);
	      ps.setString(4, c);
	      rs = ps.executeQuery();
	      while (rs.next()) {
	    	  int cnt = rs.getInt(1);
	    	  return (cnt >= 1);
	      }
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return false;
	  }
  	
  	private void closeAllConnection(Connection conn, PreparedStatement ps, ResultSet rs) {
  	    try {
  	      if (rs != null)
  	        rs.close(); 
  	    } catch (Exception ex) {
  	      ex.printStackTrace();
  	    } 
  	    try {
  	      if (ps != null)
  	        ps.close(); 
  	    } catch (Exception ex) {
  	      ex.printStackTrace();
  	    } 
  	    try {
  	      if (conn != null)
  	        conn.close(); 
  	    } catch (Exception ex) {
  	      ex.printStackTrace();
  	    } 
  	    rs = null;
  	    ps = null;
  	    conn = null;
  	  }
  	
  	

    public List<MarjorBean> getRelativeZyByName(String zymc) {
  	    Connection conn = null;
  	    PreparedStatement ps = null;
  	    ResultSet rs = null;
  	    List<MarjorBean> list = new ArrayList<>();
  	    try {
  	      conn = DriverManager.getConnection(URL, USER, PASSWD);
  	      String SQL = "SELECT  distinct Y.`专业名称`, Y.`专业类`,  y.`第一印象`  from base_major y WHERE y.`专业类` IN (SELECT distinct x.`专业类` from base_major x WHERE x.`专业名称` LIKE ? AND X.`层次` = '本科');";
  	      System.out.println(SQL);
  	      ps = conn.prepareStatement(SQL); 
  	      ps.setString(1, "%" + zymc + "%");
  	      rs = ps.executeQuery();
  	      MarjorBean bean = null;
  	      while (rs.next()) {
  	    	bean = new MarjorBean();
  	    	bean.setZymc(rs.getString("专业名称"));
  	    	bean.setZyl(rs.getString("专业类"));
  	    	bean.setDyyx(rs.getString("第一印象"));
  	    	list.add(bean);
  	      } 
  	    } catch (Exception ex) {
  	      ex.printStackTrace();
  	    } finally {
  	      closeAllConnection(conn, ps, rs);
  	    } 
  	    return list;
  	  }
    
    
    public YGCardBean getYGAdminByIDandPasswd(String id, String passwd) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    YGCardBean bean = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT *  FROM yg_admin WHERE A_ID = ? and A_PASSWD = ?";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, id.toUpperCase());
	      ps.setString(2, passwd.toUpperCase());
	      rs = ps.executeQuery();
	      while (rs.next()) {
	        bean = new YGCardBean();
	        bean.setId(rs.getString("A_ID"));
	        bean.setPasswd(rs.getString("A_PASSWD"));
	        bean.setStatus(rs.getInt("A_STATUS"));
	        bean.setCardACnt(rs.getInt("CARD_A_CNT"));
	        bean.setCardBCnt(rs.getInt("CARD_B_CNT"));
	        bean.setCardCCnt(rs.getInt("CARD_C_CNT"));
	        bean.setCardDCnt(rs.getInt("CARD_D_CNT"));
	        bean.setCardECnt(rs.getInt("CARD_E_CNT"));
	        bean.setCardFCnt(rs.getInt("CARD_F_CNT"));
	        bean.setCardGCnt(rs.getInt("CARD_G_CNT"));
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return bean;
	  }
  	  
    //
    public boolean insert2023DwfTemp(List<String> list) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "INSERT INTO wh_dwf_2023_copy(nf, ddx, slx, pjf, slxdwf,pckl,filename) VALUES(?,?,?,?,?,?,?)";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      
	      for(String x : list) {
	    	  String[] xx = x.split(",");
	    	  String nf = xx[0];
	    	  String ddx = xx[1];
	    	  String slx = xx[2];
	    	  String pjf = xx[3];
	    	  String slxdwf = xx[4];
	    	  String pckl = xx[5];
	    	  String filename = xx[6];
	    	  
		      ps.setString(1, nf);
		      ps.setString(2, ddx);
		      ps.setString(3, slx);
		      ps.setString(4, pjf);
		      ps.setString(5, slxdwf);
		      ps.setString(6, pckl);
		      ps.setString(7, filename);
		      ps.addBatch();
	      }
	      
	      ps.executeBatch();
	      return true;
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return false;
	  }
    
    

  	public boolean insertZDKSScore(String sf, String cid, String cs, String kl, String pc, int score, int cvtScore, int cvtWc) {
  	    Connection conn = null;
  	    PreparedStatement ps = null;
  	    ResultSet rs = null;
  	    try {
  	      conn = DriverManager.getConnection(URL, USER, PASSWD);
  	      String SQL = "insert into zdks_score_history(SH_ID, SF, C_ID, CITY, SCORE, KL, ZDPC, CVT_SCORE, CVT_WC, CREATE_DT, STATUS) values(?,?,?,?,?,?,?,?,?,NOW(),1)";
  	      System.out.println(SQL);
  	      ps = conn.prepareStatement(SQL);
  	      ps.setString(1, UUID.randomUUID().toString());
  	      ps.setString(2, sf);
  	      ps.setString(3, cid);
  	      ps.setString(4, cs);
  	      ps.setInt(5, score);
  	      ps.setString(6, kl);
  	      ps.setString(7, pc);
  	      ps.setString(8, String.valueOf(cvtScore));
  	      ps.setString(9, String.valueOf(cvtWc));
  	      ps.executeUpdate();
  	      return true;
  	    } catch (Exception ex) {
  	      ex.printStackTrace();
  	    } finally {
  	      closeAllConnection(conn, ps, rs);
  	    } 
  	    return false;
  	  }
  	
  	public boolean checkZDKSScoreExist(String sf, String cid, String cs, String kl, String pc, String score) {
  	    Connection conn = null;
  	    PreparedStatement ps = null;
  	    ResultSet rs = null;
  	    try {
  	      conn = DriverManager.getConnection(URL, USER, PASSWD);
  	      String SQL = "select count(*) as cnt from zdks_score_history where sf = ? and c_id = ? and CITY = ? and KL = ? and ZDPC = ? and SCORE = ?";
  	      System.out.println(SQL);
  	      ps = conn.prepareStatement(SQL);
  	      ps.setString(1, sf);
  	      ps.setString(2, cid);
  	      ps.setString(3, cs);
  	      ps.setString(4, kl);
  	      ps.setString(5, pc);
  	      ps.setString(6, score);
  	      rs = ps.executeQuery();
	      while (rs.next()) {
	    	  return rs.getInt("cnt") > 0;
	      }
  	      return true;
  	    } catch (Exception ex) {
  	      ex.printStackTrace();
  	    } finally {
  	      closeAllConnection(conn, ps, rs);
  	    } 
  	    return false;
  	  }
  	
  	public int getZDKSScoreCnt(String sf, String cid, String cs, String kl, String pc) {
  	    Connection conn = null;
  	    PreparedStatement ps = null;
  	    ResultSet rs = null;
  	    try {
  	      conn = DriverManager.getConnection(URL, USER, PASSWD);
  	      String SQL = "select count(*) as cnt from zdks_score_history where sf = ? and c_id = ? and CITY = ? and KL = ? and ZDPC = ?";
  	      System.out.println(SQL);
  	      ps = conn.prepareStatement(SQL);
  	      ps.setString(1, sf);
  	      ps.setString(2, cid);
  	      ps.setString(3, cs);
  	      ps.setString(4, kl);
  	      ps.setString(5, pc);
  	      rs = ps.executeQuery();
	      while (rs.next()) {
	    	  return rs.getInt("cnt");
	      }
  	    } catch (Exception ex) {
  	      ex.printStackTrace();
  	    } finally {
  	      closeAllConnection(conn, ps, rs);
  	    } 
  	    return 0;
  	  }
  	
  	public ZDKSFollowBean getFollowById(String cid, int fsId) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    ZDKSFollowBean bean = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT * FROM zdks_score_follow_school WHERE C_ID = ? and FS_ID = ?";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, cid);
	      ps.setInt(2, fsId);
	      rs = ps.executeQuery();
	      while (rs.next()) {
	    	  bean = new ZDKSFollowBean();
	    	  bean.setFsId(rs.getInt("FS_ID"));
	    	  bean.setCid(rs.getString("C_ID"));
	    	  bean.setCvtwc(rs.getString("CVTWC"));
	    	  bean.setCvtzdf(rs.getString("CVTSCORE"));
	    	  bean.setKl(rs.getString("KL"));
	    	  bean.setPc(rs.getString("PC"));
	    	  bean.setYxmc(rs.getString("YXMC"));
	    	  bean.setZdcs(rs.getString("ZDCITY"));
	    	  bean.setZdkl(rs.getString("ZDKL"));
	    	  bean.setZdpc(rs.getString("ZDPC"));
	    	  bean.setZdscore(rs.getString("ZDSCORE"));
	    	  bean.setZymc(rs.getString("ZYMC"));
	    	  bean.setCreate(rs.getTimestamp("CREATE_DT"));
	      } 
	      
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return bean;
	}
  	
  	public List<ZDKSFollowBean> listFollows(String cid, boolean isYX) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    List<ZDKSFollowBean> list = new ArrayList<>();
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT x.* FROM zdks_score_follow_school x WHERE x.C_ID = ? " + (isYX ? "and x.ZYMC is null" : "and x.ZYMC is not null") + " ORDER BY x.ZDPC desc, x.CREATE_DT DESC ,x.ZDSCORE DESC ";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setString(1, cid);
	      rs = ps.executeQuery();
	      ZDKSFollowBean bean = null;
	      while (rs.next()) {
	    	  bean = new ZDKSFollowBean();
	    	  bean.setFsId(rs.getInt("FS_ID"));
	    	  bean.setCid(rs.getString("C_ID"));
	    	  bean.setCvtwc(rs.getString("CVTWC"));
	    	  bean.setCvtzdf(rs.getString("CVTSCORE"));
	    	  bean.setKl(rs.getString("KL"));
	    	  bean.setPc(rs.getString("PC"));
	    	  bean.setYxmc(rs.getString("YXMC"));
	    	  bean.setZdcs(rs.getString("ZDCITY"));
	    	  bean.setZdkl(rs.getString("ZDKL"));
	    	  bean.setZdpc(rs.getString("ZDPC"));
	    	  bean.setZdscore(rs.getString("ZDSCORE"));
	    	  bean.setZymc(rs.getString("ZYMC"));
	    	  bean.setCreate(rs.getTimestamp("CREATE_DT"));
	    	  list.add(bean);
	      } 
	      
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return list;
	  }
  	
  	public int checkFollowStatus(String cid, String zdsf, String zdcs, String zdkl, String zdpc, int zdscore,String kl, String pc, String yxmc, String zymc) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    YGCardBean bean = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      if(Tools.isEmpty(zymc)) {
	    	  String SQL = "SELECT * FROM zdks_score_follow_school WHERE C_ID = ? and SF = ? and ZDCITY = ? and ZDPC = ? and ZDKL = ? and ZDSCORE = ? and YXMC = ? and KL = ? and PC = ?";
		      System.out.println(SQL);
		      ps = conn.prepareStatement(SQL);
		      ps.setString(1, cid);
		      ps.setString(2, zdsf);
		      ps.setString(3, zdcs);
		      ps.setString(4, zdpc);
		      ps.setString(5, zdkl);
		      ps.setInt(6, zdscore);
		      ps.setString(7, yxmc);
		      ps.setString(8, kl);
		      ps.setString(9, pc);
		      rs = ps.executeQuery();
		      while (rs.next()) {
		    	  return 1;
		      } 
	      }else {
	    	  String SQL = "SELECT * FROM zdks_score_follow_school WHERE C_ID = ? and SF = ? and ZDCITY = ? and ZDPC = ? and ZDKL = ? and ZDSCORE = ? and YXMC = ? and KL = ? and PC = ? and ZYMC = ?";
		      System.out.println(SQL);
		      ps = conn.prepareStatement(SQL);
		      ps.setString(1, cid);
		      ps.setString(2, zdsf);
		      ps.setString(3, zdcs);
		      ps.setString(4, zdpc);
		      ps.setString(5, zdkl);
		      ps.setInt(6, zdscore);
		      ps.setString(7, yxmc);
		      ps.setString(8, kl);
		      ps.setString(9, pc);
		      ps.setString(10, zymc);
		      rs = ps.executeQuery();
		      while (rs.next()) {
		    	  return 1;
		      } 
	      }
	      
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return -1;
	}
  	
  	
  	public boolean deleteFollow(String cid, int fsId) {
  	    Connection conn = null;
  	    PreparedStatement ps = null;
  	    ResultSet rs = null;
  	    try {
  	      conn = DriverManager.getConnection(URL, USER, PASSWD);
  	      String SQL = "delete from zdks_score_follow_school where C_ID = ? and FS_ID = ?";
  	      System.out.println(SQL);
  	      ps = conn.prepareStatement(SQL);
  	      ps.setString(1, cid);
  	      ps.setInt(2, fsId);
  	      ps.executeUpdate();
  	      return true;
  	    } catch (Exception ex) {
  	      ex.printStackTrace();
  	    } finally {
  	      closeAllConnection(conn, ps, rs);
  	    } 
  	    return false;
  	  }
  	
  	public boolean insertZDKSFollowSchool(String cid, String zdsf, String zdcs, String zdkl, String zdpc, int zdscore,int cvtzdf,int cvtwc, String kl, String pc, String yxmc, String zymc) {
  	    Connection conn = null;
  	    PreparedStatement ps = null;
  	    ResultSet rs = null;
  	    try {
  	      conn = DriverManager.getConnection(URL, USER, PASSWD);
  	      String SQL = "insert into zdks_score_follow_school(SF, C_ID, ZDCITY, ZDPC, ZDKL, ZDSCORE, CVTSCORE, CVTWC, YXMC, KL, PC, zymc, CREATE_DT) values(?,?,?,?,?,?,?,?,?,?,?,?,NOW())";
  	      System.out.println(SQL);
  	      ps = conn.prepareStatement(SQL);
  	      ps.setString(1, zdsf);
  	      ps.setString(2, cid);
  	      ps.setString(3, zdcs);
  	      ps.setString(4, zdpc);
  	      ps.setString(5, zdkl);
  	      ps.setInt(6, zdscore);
  	      ps.setInt(7, cvtzdf);
  	      ps.setInt(8, cvtwc);
  	      ps.setString(9, yxmc);
  	      ps.setString(10, kl);
  	      ps.setString(11, pc);
  	      ps.setString(12, Tools.isEmpty(zymc)? null : zymc);
  	      ps.executeUpdate();
  	      return true;
  	    } catch (Exception ex) {
  	      ex.printStackTrace();
  	    } finally {
  	      closeAllConnection(conn, ps, rs);
  	    } 
  	    return false;
  	  }
  	
  	
  	public int getZDKSScoreConvert(int year, String sf, String city, String kl, String zdpc, int score) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    YGCardBean bean = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT *  FROM zdks_score_convert WHERE nf = ? and sf = ? and city = ? and kl = ? and ZDPC = ? and score_from = ?";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setInt(1, year);
	      ps.setString(2, sf);
	      ps.setString(3, city);
	      ps.setString(4, kl);
	      ps.setString(5, zdpc);
	      ps.setInt(6, score);
	      rs = ps.executeQuery();
	      while (rs.next()) {
	    	  return rs.getInt("SCORE_TO");
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return -1;
	  }
  	
  	public ZDKSRank getZDKSNearestScoreByWC(int year, String sf, String kl, int givingWC) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    ZDKSRank bean = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT * FROM zdks_rank WHERE nf = ? and sf = ? and kl = ? and WC >= ? order by WC ASC LIMIT 1";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setInt(1, year);
	      ps.setString(2, sf);
	      ps.setString(3, kl);
	      ps.setInt(4, givingWC);
	      rs = ps.executeQuery();
	      while (rs.next()) {
	    	  bean = new ZDKSRank();
	    	  bean.setCnt(rs.getInt("cnt"));
	    	  bean.setFd(rs.getString("fd"));
	    	  bean.setKl(rs.getString("kl"));
	    	  bean.setNf(rs.getInt("nf"));
	    	  bean.setScore(rs.getInt("SCORE"));
	    	  bean.setSf(rs.getString("sf"));
	    	  bean.setWc(rs.getInt("wc"));
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return bean;
	  }
  	
  	public ZDKSRank getZDKSWCByScore(int year, String sf, String kl, int score) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    ZDKSRank bean = null;
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT * FROM zdks_rank WHERE nf = ? and sf = ? and kl = ? and score = ?";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setInt(1, year);
	      ps.setString(2, sf);
	      ps.setString(3, kl);
	      ps.setInt(4, score);
	      rs = ps.executeQuery();
	      while (rs.next()) {
	    	  bean = new ZDKSRank();
	    	  bean.setCnt(rs.getInt("cnt"));
	    	  bean.setFd(rs.getString("fd"));
	    	  bean.setKl(rs.getString("kl"));
	    	  bean.setNf(rs.getInt("nf"));
	    	  bean.setScore(rs.getInt("SCORE"));
	    	  bean.setSf(rs.getString("sf"));
	    	  bean.setWc(rs.getInt("wc"));
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return bean;
	 }
  	
  	public List<SchoolBean> getZDKSSchoolBySfAndYearAndArea(int year, String schoolSF, String kl, int scoreFrom, int scoreTo) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    List<SchoolBean> list = new ArrayList<SchoolBean>();
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT x.* ,y.bxxz, fs.C_ID as fsyxmc FROM s5_sc_" + year+ "x x left join base_university y on x.yxmc = y.yxmc LEFT JOIN zdks_score_follow_school fs ON x.yxmc = fs.yxmc and x.xk = fs.kl and x.pc = fs.pc WHERE x.nf = ? and y.sf like ? and x.xk = ? and x.zdf between ? and ? ORDER BY x.zdf DESC";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setInt(1, year);
	      ps.setString(2, "%" + schoolSF + "%");
	      ps.setString(3, kl);
	      ps.setInt(4, scoreFrom);
	      ps.setInt(5, scoreTo);
	      rs = ps.executeQuery();
	      SchoolBean sb = null;
	      while (rs.next()) {
			sb = new SchoolBean();
			sb.setYxmc(rs.getString("yxmc"));
			sb.setSf(rs.getString("yxsf"));
			sb.setXk(rs.getString("XK")); 
			sb.setPc(rs.getString("PC"));
			sb.setNf(year);
			sb.setZdf(rs.getString("ZDF"));
			sb.setZdfwc(rs.getString("ZDFWC"));
			sb.setYxlx(rs.getString("bxxz"));
			sb.setYxdm(rs.getString("fsyxmc"));
			list.add(sb);
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return list;
	 }
  	
  	public List<SchoolMarjorCommonBean> getZDKSMajorBySfAndYearAndArea(int year, String zymc, String schoolSF, String kl, int scoreFrom, int scoreTo) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    List<SchoolMarjorCommonBean> list = new ArrayList<SchoolMarjorCommonBean>();
	    try {
	      conn = DriverManager.getConnection(URL, USER, PASSWD);
	      String SQL = "SELECT x.*, y.bxxz, fs.C_ID as fsyxmc FROM s5_sc_" + year + " x left join base_university y on x.yxmc = y.yxmc LEFT JOIN zdks_score_follow_school fs ON x.yxmc = fs.yxmc and x.zymc = fs.zymc and x.xk = fs.kl and x.pc = fs.pc WHERE x.nf = ? and y.sf like ? and x.zymc like ? and x.xk = ? and x.zdf between ? and ? ORDER BY x.zdf DESC";
	      System.out.println(SQL);
	      ps = conn.prepareStatement(SQL);
	      ps.setInt(1, year);
	      ps.setString(2, "%" + schoolSF + "%");
	      ps.setString(3, "%" + zymc + "%");
	      ps.setString(4, kl);
	      ps.setInt(5, scoreFrom);
	      ps.setInt(6, scoreTo);
	      rs = ps.executeQuery();
	      SchoolMarjorCommonBean sb = null;
	      while (rs.next()) {
			sb = new SchoolMarjorCommonBean();
			sb.setYxmc(rs.getString("yxmc"));
			sb.setZymc(rs.getString("zymc"));
			sb.setSf(rs.getString("yxsf"));
			sb.setXk(rs.getString("XK")); 
			sb.setPc(rs.getString("PC"));
			sb.setNf(year);
			sb.setZdf(rs.getString("ZDF"));
			sb.setZdfwc(rs.getString("ZDFWC"));
			sb.setYxlx(rs.getString("bxxz"));
			sb.setYxdm(rs.getString("fsyxmc"));
			list.add(sb);
	      } 
	    } catch (Exception ex) {
	      ex.printStackTrace();
	    } finally {
	      closeAllConnection(conn, ps, rs);
	    } 
	    return list;
	 }
  	

    public boolean insertSysVisitLog(String cid, String request) {
      Connection conn = null;
      PreparedStatement ps = null;
      ResultSet rs = null;
      try {
        conn = DriverManager.getConnection(URL, USER, PASSWD);
        String SQL = "insert into sys_trans_visit(C_ID, VISIT_TM, VISIT_MOD) values(?, unix_timestamp(NOW()) , ?)";
        System.out.println(SQL);
        ps = conn.prepareStatement(SQL);
        ps.setString(1, cid);
        ps.setString(2, request);
        ps.executeUpdate();
        return true;
      } catch (Exception ex) {
        ex.printStackTrace();
      } finally {
        closeAllConnection(conn, ps, rs);
      } 
      return false;
    }
    
    public boolean deleteSysVisitLog(String cid) {
      Connection conn = null;
      PreparedStatement ps = null;
      ResultSet rs = null;
      try {
        conn = DriverManager.getConnection(URL, USER, PASSWD);
        String SQL = "DELETE FROM sys_trans_visit WHERE c_id = ? AND VISIT_TM < (unix_timestamp(NOW()) - 60 * 60)";
        System.out.println(SQL);
        ps = conn.prepareStatement(SQL);
        ps.setString(1, cid);
        ps.executeUpdate();
        return true;
      } catch (Exception ex) {
        ex.printStackTrace();
      } finally {
        closeAllConnection(conn, ps, rs);
      } 
      return false;
    }
    

	public boolean frozenCard(String cid) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DriverManager.getConnection(URL, USER, PASSWD);
			String SQL = "UPDATE base_card SET C_STATUS = 2 WHERE C_ID = ?";
			System.out.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, cid);
			ps.executeUpdate();
			return true;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			closeAllConnection(conn, ps, rs);
		}
		return false;
	}
    
    public int getLatest30SecsVisitLogCount(String cid) {
      Connection conn = null;
      PreparedStatement ps = null;
      ResultSet rs = null;
      try {
        conn = DriverManager.getConnection(URL, USER, PASSWD);
        String SQL = "SELECT COUNT(1) as ct FROM sys_trans_visit WHERE c_id = ? AND VISIT_TM > (unix_timestamp(NOW()) - 10)";
        System.out.println(SQL);
        ps = conn.prepareStatement(SQL);
        ps.setString(1, cid);
        rs = ps.executeQuery();
        if (rs.next())
          return rs.getInt(1); 
      } catch (Exception ex) {
        ex.printStackTrace();
      } finally {
        closeAllConnection(conn, ps, rs);
      } 
      return 0;
    }
    
    
}
