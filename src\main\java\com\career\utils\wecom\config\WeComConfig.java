package com.career.utils.wecom.config;

/**
 * 企业微信数据同步配置类
 * 
 * 集中管理数据同步服务的各项配置参数，易于理解和修改。
 * 配置项按照业务功能和修改频率进行组织。
 */
public class WeComConfig {

    // ==================== 同步模式配置 (Sync Mode) ====================

    // --- 常修改配置 ---

    /**
     * 默认同步模式：
     * 当未显式指定同步模式时，系统采用的默认模式。
     * true: 增量同步 (Incremental Sync) - 推荐用于日常同步，效率高，只处理变化数据。
     * false: 全量同步 (Full Sync) - 适用于首次同步或需要强制刷新所有数据时。
     */
    private static final boolean DEFAULT_SYNC_MODE_INCREMENTAL = true;

    /**
     * 是否启用智能同步模式：
     * 启用后，系统可以根据预设的逻辑（例如判断数据变化量）自动选择增量或全量同步。
     * 如果禁用，则完全依赖 DEFAULT_SYNC_MODE_INCREMENTAL 或显式指定的模式。
     */
    private static final boolean ENABLE_SMART_SYNC_MODE = false;

    // --- 高级配置 ---

    /**
     * 智能同步阈值：
     * 在启用智能同步模式时，如果侦测到的数据变化量超过此阈值，系统可能会自动切换到全量同步。
     * 单位通常是数据记录条数，具体解释依赖智能同步逻辑实现。
     * 仅在 ENABLE_SMART_SYNC_MODE 为 true 时生效。
     */
    private static final int SMART_SYNC_THRESHOLD = 1000;

    /**
     * 是否采用增量API抓取模式：
     * 控制从企业微信API获取数据时，是否优先使用支持增量拉取的接口（如基于cursor分页获取变更）。
     * 这影响的是数据"如何被拉取"，而不是整体同步任务是增量还是全量。
     * true: 尝试使用增量抓取API（如果存在且支持）。
     * false: 即使API支持增量抓取，也强制使用全量抓取接口。
     */
    private static final boolean USE_INCREMENTAL_API_FETCH = false;

    /**
     * 增量同步时间阈值（天）
     * 如果数据的上次同步时间在此天数以内，则跳过API调用
     * 设置为0表示禁用基于时间的跳过优化
     */
    private static final int INCREMENTAL_SYNC_SKIP_DAYS = 20;


    // ==================== 错误处理配置 (Error Handling) ====================

    // --- 常修改配置 ---

    /**
     * 错误时是否继续执行后续阶段：
     * 控制在一个同步阶段（如同步内部联系人）发生错误后，是否继续执行后续的同步阶段。
     * true: 继续执行后续阶段，记录错误但不中断整个同步任务。
     * false: 当前阶段出错后，立即中断整个同步任务。
     */
    private static final boolean CONTINUE_ON_ERROR = true;

    /**
     * 最大容忍错误数：
     * 在 CONTINUE_ON_ERROR 为 true 时，如果整个同步任务累计的错误数量超过此阈值，将中断同步。
     * 用于防止大量错误导致系统资源耗尽或产生不可接受的数据质量问题。
     */
    private static final int MAX_TOLERATED_ERRORS = 50;

    /**
     * API调用重试次数：
     * 当企业微信API返回可重试的错误（如网络错误、服务暂时不可用）时，最大重试次数。
     * 不包括特殊处理的错误码（如40096）。
     */
    private static final int MAX_RETRY_COUNT = 2;

    // --- 高级配置 ---

    /**
     * 特殊错误码重试次数：
     * 针对某些特定的错误码（如40096，表示用户/客户不存在或无权限），独立的重试次数设置。
     * 通常这些错误不需要重试，因为重试成功的概率很低，可能会设置为0。
     */
    private static final int SPECIAL_ERROR_RETRY_COUNT = 0;

    /**
     * 对于40096错误是否跳过重试：
     * 显式控制遇到40096错误时是否执行重试逻辑（即使 MAX_RETRY_COUNT 或 SPECIAL_ERROR_RETRY_COUNT > 0）。
     * true: 遇到40096错误时立即放弃重试。
     * false: 遇到40096错误时按照重试配置进行重试。
     */
    private static final boolean SKIP_RETRY_ON_40096 = true;

    /**
     * 40096错误的详细处理模式：
     * 控制遇到40096错误时是否输出更详细的日志信息，便于定位具体是哪个用户/客户导致的问题。
     * true: 输出详细日志。
     * false: 只输出简略错误信息。
     */
    private static final boolean LOG_40096_DETAILS = true;


    // ==================== 性能优化配置 (Performance) ====================

    // --- 常修改配置 ---

    /**
     * API调用间隔时间（毫秒）：
     * 控制连续两次调用企业微信API之间的最小间隔时间。
     * 用于控制API调用频率，避免超出企业微信平台的QPS限制。
     */
    private static final long API_CALL_INTERVAL_MS = 150;

    /**
     * 批处理大小：
     * 控制数据库批量插入、更新或删除操作时，每批处理的数据记录数量。
     * 合理设置批处理大小可以提高数据库操作效率，但过大会增加内存负担。
     */
    private static final int BATCH_SIZE = 100;

    /**
     * 大批量处理时的批次间隔（毫秒）：
     * 控制在执行分批处理（例如大数据量分块同步）时，每个批次操作之间的间隔时间。
     * 用于分散数据库或系统负载，提高稳定性。
     */
    private static final long BATCH_INTERVAL_MS = 500;

    // --- 高级配置 ---

    /**
     * 单次同步最大处理数量限制：
     * 限制在一个同步阶段（例如同步外部联系人）单次拉取并处理的数据记录总数上限。
     * 如果从API获取的数据量超过此限制，系统将自动进行分批处理。用于保护系统资源。
     */
    private static final int MAX_SYNC_ITEMS_PER_BATCH = 1000;

    /**
     * API调用QPS限制：
     * 更精确的API调用频率控制，限制每秒最大调用次数 (Queries Per Second)。
     * 系统会根据此值动态计算每次API调用的最小间隔。通常与 API_CALL_INTERVAL_MS 配合使用或作为其更精确的替代。
     * 通用API QPS限制通常为10，通讯录、外部联系人、客户群等相关API限制为20。
     * 建议设置为10或更低，以避免触发限流（错误码45009）。
     */
    private static final int API_QPS_LIMIT = 15;


    // ==================== 进度监控和日志配置 (Monitoring & Logging) ====================

    // --- 常修改配置 ---

    /**
     * 是否启用进度监控：
     * 控制是否在同步过程中收集和报告进度信息。
     * true: 启用进度监控，可以通过相关接口获取当前同步进度。
     * false: 禁用进度监控。
     */
    private static final boolean ENABLE_PROGRESS_MONITORING = true;

    /**
     * 进度更新频率：
     * 在启用进度监控时，控制处理多少条记录后更新一次进度信息和日志输出。
     */
    private static final int PROGRESS_UPDATE_FREQUENCY = 50;

    /**
     * 是否启用详细阶段日志：
     * 控制是否在同步过程的关键阶段（如"开始同步内部联系人"）输出详细的日志信息。
     * true: 输出详细的阶段开始/结束日志。
     * false: 只输出简略的同步流程日志。
     */
    private static final boolean ENABLE_DETAILED_PHASE_LOGGING = true;

    // --- 高级配置 ---

    /**
     * 是否在控制台输出进度信息：
     * 控制是否将进度更新信息直接打印到控制台（或标准输出）。
     * true: 在控制台显示实时进度。
     * false: 不在控制台显示进度，只通过监控接口报告。
     */
    private static final boolean ENABLE_CONSOLE_PROGRESS = true;

    /**
     * 是否启用详细日志模式 (Verbose Logging)：
     * 控制是否输出更详细的调试日志信息，包括异常堆栈、详细处理过程等。
     * true: 启用详细日志，有助于问题排查，但在生产环境可能产生大量日志。
     * false: 关闭详细日志，只记录关键信息和错误。
     */
    private static final boolean ENABLE_VERBOSE_LOGGING = false;

    /**
     * 是否记录API响应详情：
     * 控制是否记录每次企业微信API调用的详细请求和响应内容。
     * 仅在需要深入调试API交互时启用。
     */
    private static final boolean LOG_API_RESPONSE_DETAILS = false;

    /**
     * 是否输出API原始JSON响应数据：
     * 控制是否在控制台输出企业微信API返回的完整原始JSON数据。
     * 这个配置比LOG_API_RESPONSE_DETAILS更具体，专门用于排查API返回数据解析问题。
     * true: 输出完整的原始JSON响应数据，包括请求URL和参数
     * false: 不输出原始JSON数据，只记录处理后的关键信息
     * 注意：启用此选项会产生大量日志输出，建议仅在调试时使用
     */
    private static final boolean LOG_API_RAW_JSON_RESPONSE = true;

    /**
     * 是否记录数据库操作详情：
     * 控制是否记录详细的数据库操作日志，例如执行的SQL语句、影响的行数等。
     * 仅在需要调试数据库交互或性能问题时启用。
     */
    private static final boolean LOG_DATABASE_OPERATIONS = false;

    /**
     * 是否在同步完成后生成详细报告：
     * 控制同步任务完成后是否输出包含统计数据（新增、更新、删除数量等）和错误的详细报告。
     */
    private static final boolean GENERATE_DETAILED_REPORT = true;


    // ==================== 数据质量控制配置 (Data Quality) ====================

    // --- 常修改配置 ---

     /**
      * 是否启用同步后数据验证：
      * 控制在所有数据同步阶段完成后，是否执行额外的数据验证步骤。
      * 这可以包括检查数据完整性、执行去重检查等。
      */
    private static final boolean ENABLE_POST_SYNC_VALIDATION = true;

    /**
     * 是否启用内部联系人去重验证：
     * 控制在同步后验证阶段，是否检查内部联系人数据是否存在重复。
     * 注意：此配置控制的是"验证"，实际的去重逻辑（如在插入前判断）可能需要在其他地方实现并受此配置控制。
     */
    private static final boolean ENABLE_INTERNAL_CONTACT_DEDUP = true;

    /**
     * 是否启用外部联系人去重验证：
     * 控制在同步后验证阶段，是否检查外部联系人数据是否存在重复。
     */
    private static final boolean ENABLE_EXTERNAL_CONTACT_DEDUP = true;

    /**
     * 是否启用客户群去重验证：
     * 控制在同步后验证阶段，是否检查客户群数据是否存在重复。
     */
    private static final boolean ENABLE_GROUP_DEDUP = true;

    /**
     * 是否启用群成员关系去重验证：
     * 控制在同步后验证阶段，是否检查群成员关系数据是否存在重复。
     */
    private static final boolean ENABLE_GROUP_MEMBER_DEDUP = true;

    /**
     * 是否启用跟进人关系去重验证：
     * 控制在同步后验证阶段，是否检查跟进人关系数据是否存在重复。
     */
    private static final boolean ENABLE_FOLLOWER_DEDUP = true;


    // --- 高级配置 ---

    /**
     * 是否启用数据完整性检查：
     * 控制在同步后验证阶段，是否执行数据完整性检查（如检查孤立数据、引用关系是否正确等）。
     * true: 启用详细的完整性检查。
     * false: 跳过完整性检查。
     */
    private static final boolean ENABLE_DATA_INTEGRITY_CHECK = true;

    /**
     * 数据不一致时的处理策略：
     * 在数据完整性检查发现不一致时，系统采取的处理措施。
     * true: 尝试自动修复不一致的数据（需要具体的修复逻辑支持）。
     * false: 仅记录不一致的信息，不进行自动修复。
     */
    private static final boolean AUTO_FIX_INCONSISTENCY = false;


    // ==================== 会话管理配置 (Session Management) ====================

    // --- 常修改配置 ---

     /**
      * 最大并发同步会话数：
      * 限制系统同时可以执行的同步任务会话数量。
      * 用于控制整体系统资源消耗，防止过多的并发任务导致性能下降或崩溃。
      */
    private static final int MAX_CONCURRENT_SYNC_SESSIONS = 1;

     /**
      * 是否自动清理过期的同步会话：
      * 控制系统是否定期检查并清理长时间没有活动的同步会话信息。
      * true: 启用自动清理。
      * false: 需要手动或外部触发清理。
      */
    private static final boolean AUTO_CLEANUP_EXPIRED_SESSIONS = true;


    // --- 高级配置 ---

    /**
     * 同步会话的超时时间（毫秒）：
     * 定义一个同步会话被认为"过期"或"僵尸"的时间阈值。
     * 如果一个会话在此时间内没有更新状态，可能会被自动清理或标记异常。
     */
    private static final long SYNC_SESSION_TIMEOUT_MS = 30 * 60 * 1000;


    // ==================== Getter方法 (Getters) ====================

    // 同步模式配置
    public static boolean isDefaultSyncModeIncremental() { return DEFAULT_SYNC_MODE_INCREMENTAL; }
    public static boolean isEnableSmartSyncMode() { return ENABLE_SMART_SYNC_MODE; }
    public static int getSmartSyncThreshold() { return SMART_SYNC_THRESHOLD; }
    public static boolean isUseIncrementalApiFetch() { return USE_INCREMENTAL_API_FETCH; }
    public static int getIncrementalSyncSkipDays() { return INCREMENTAL_SYNC_SKIP_DAYS; }


    // 错误处理配置
    public static boolean isContinueOnError() { return CONTINUE_ON_ERROR; }
    public static int getMaxToleratedErrors() { return MAX_TOLERATED_ERRORS; }
    public static int getMaxRetryCount() { return MAX_RETRY_COUNT; }
    public static int getSpecialErrorRetryCount() { return SPECIAL_ERROR_RETRY_COUNT; }
    public static boolean isSkipRetryOn40096() { return SKIP_RETRY_ON_40096; }
    public static boolean isLog40096Details() { return LOG_40096_DETAILS; }


    // 性能优化配置
    public static long getApiCallIntervalMs() { return API_CALL_INTERVAL_MS; }
    public static int getBatchSize() { return BATCH_SIZE; }
    public static long getBatchIntervalMs() { return BATCH_INTERVAL_MS; }
    public static int getMaxSyncItemsPerBatch() { return MAX_SYNC_ITEMS_PER_BATCH; }
    public static int getApiQpsLimit() { return API_QPS_LIMIT; }


    // 进度监控和日志配置
    public static boolean isEnableProgressMonitoring() { return ENABLE_PROGRESS_MONITORING; }
    public static int getProgressUpdateFrequency() { return PROGRESS_UPDATE_FREQUENCY; }
    public static boolean isEnableDetailedPhaseLogging() { return ENABLE_DETAILED_PHASE_LOGGING; }
    public static boolean isEnableConsoleProgress() { return ENABLE_CONSOLE_PROGRESS; }
    public static boolean isEnableVerboseLogging() { return ENABLE_VERBOSE_LOGGING; }
    public static boolean isLogApiResponseDetails() { return LOG_API_RESPONSE_DETAILS; }
    public static boolean isLogApiRawJsonResponse() { return LOG_API_RAW_JSON_RESPONSE; }
    public static boolean isLogDatabaseOperations() { return LOG_DATABASE_OPERATIONS; }
    public static boolean isGenerateDetailedReport() { return GENERATE_DETAILED_REPORT; }


    // 数据质量控制配置
    public static boolean isEnablePostSyncValidation() { return ENABLE_POST_SYNC_VALIDATION; }
    public static boolean isEnableInternalContactDedup() { return ENABLE_INTERNAL_CONTACT_DEDUP; }
    public static boolean isEnableExternalContactDedup() { return ENABLE_EXTERNAL_CONTACT_DEDUP; }
    public static boolean isEnableGroupDedup() { return ENABLE_GROUP_DEDUP; }
    public static boolean isEnableGroupMemberDedup() { return ENABLE_GROUP_MEMBER_DEDUP; }
    public static boolean isEnableFollowerDedup() { return ENABLE_FOLLOWER_DEDUP; }
    public static boolean isEnableDataIntegrityCheck() { return ENABLE_DATA_INTEGRITY_CHECK; }
    public static boolean isAutoFixInconsistency() { return AUTO_FIX_INCONSISTENCY; }


    // 会话管理配置
    public static int getMaxConcurrentSyncSessions() { return MAX_CONCURRENT_SYNC_SESSIONS; }
    public static boolean isAutoCleanupExpiredSessions() { return AUTO_CLEANUP_EXPIRED_SESSIONS; }
    public static long getSyncSessionTimeoutMs() { return SYNC_SESSION_TIMEOUT_MS; }


    // ==================== 辅助方法 (Helper Methods) ====================

    /**
     * 获取配置摘要信息
     * 提供了当前所有配置项的简要汇总信息，便于查看和调试。
     * @return 配置摘要字符串
     */
    public static String getConfigSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("=== WeComConfig 配置摘要 ===\n");

        sb.append("\n--- 同步模式 (Sync Mode) ---\n");
        sb.append("默认同步模式: ").append(DEFAULT_SYNC_MODE_INCREMENTAL ? "增量同步" : "全量同步").append("\n");
        sb.append("智能同步模式: ").append(ENABLE_SMART_SYNC_MODE ? "启用" : "禁用").append("\n");
        sb.append("智能同步阈值: ").append(SMART_SYNC_THRESHOLD).append("\n");
        sb.append("增量API抓取: ").append(USE_INCREMENTAL_API_FETCH ? "启用" : "禁用").append("\n");
        sb.append("增量同步时间阈值: ").append(INCREMENTAL_SYNC_SKIP_DAYS).append("天").append("\n");

        sb.append("\n--- 错误处理 (Error Handling) ---\n");
        sb.append("错误时继续: ").append(CONTINUE_ON_ERROR ? "是" : "否").append("\n");
        sb.append("最大容忍错误: ").append(MAX_TOLERATED_ERRORS).append("\n");
        sb.append("API重试次数: ").append(MAX_RETRY_COUNT).append("\n");
        sb.append("特殊错误重试: ").append(SPECIAL_ERROR_RETRY_COUNT).append("\n");
        sb.append("40096跳过重试: ").append(SKIP_RETRY_ON_40096 ? "是" : "否").append("\n");
        sb.append("40096详细日志: ").append(LOG_40096_DETAILS ? "启用" : "禁用").append("\n");

        sb.append("\n--- 性能优化 (Performance) ---\n");
        sb.append("API调用间隔: ").append(API_CALL_INTERVAL_MS).append("ms\n");
        sb.append("批处理大小: ").append(BATCH_SIZE).append("\n");
        sb.append("批处理间隔: ").append(BATCH_INTERVAL_MS).append("ms\n");
        sb.append("最大同步条数(单阶段): ").append(MAX_SYNC_ITEMS_PER_BATCH).append("\n");
        sb.append("API QPS限制: ").append(API_QPS_LIMIT).append("\n");

        sb.append("\n--- 进度监控和日志 (Monitoring & Logging) ---\n");
        sb.append("进度监控: ").append(ENABLE_PROGRESS_MONITORING ? "启用" : "禁用").append("\n");
        sb.append("进度更新频率: ").append(PROGRESS_UPDATE_FREQUENCY).append("\n");
        sb.append("详细阶段日志: ").append(ENABLE_DETAILED_PHASE_LOGGING ? "启用" : "禁用").append("\n");
        sb.append("控制台进度显示: ").append(ENABLE_CONSOLE_PROGRESS ? "启用" : "禁用").append("\n");
        sb.append("详细日志模式: ").append(ENABLE_VERBOSE_LOGGING ? "启用" : "禁用").append("\n");
        sb.append("记录API响应详情: ").append(LOG_API_RESPONSE_DETAILS ? "启用" : "禁用").append("\n");
        sb.append("记录API原始JSON响应: ").append(LOG_API_RAW_JSON_RESPONSE ? "启用" : "禁用").append("\n");
        sb.append("记录DB操作详情: ").append(LOG_DATABASE_OPERATIONS ? "启用" : "禁用").append("\n");
        sb.append("生成详细报告: ").append(GENERATE_DETAILED_REPORT ? "启用" : "禁用").append("\n");

        sb.append("\n--- 数据质量控制 (Data Quality) ---\n");
        sb.append("同步后验证: ").append(ENABLE_POST_SYNC_VALIDATION ? "启用" : "禁用").append("\n");
        sb.append("数据完整性检查: ").append(ENABLE_DATA_INTEGRITY_CHECK ? "启用" : "禁用").append("\n");
        sb.append("自动修复不一致: ").append(AUTO_FIX_INCONSISTENCY ? "是" : "否").append("\n");
        sb.append("内部联系人去重验证: ").append(ENABLE_INTERNAL_CONTACT_DEDUP ? "启用" : "禁用").append("\n");
        sb.append("外部联系人去重验证: ").append(ENABLE_EXTERNAL_CONTACT_DEDUP ? "启用" : "禁用").append("\n");
        sb.append("客户群去重验证: ").append(ENABLE_GROUP_DEDUP ? "启用" : "禁用").append("\n");
        sb.append("群成员关系去重验证: ").append(ENABLE_GROUP_MEMBER_DEDUP ? "启用" : "禁用").append("\n");
        sb.append("跟进人关系去重验证: ").append(ENABLE_FOLLOWER_DEDUP ? "启用" : "禁用").append("\n");

        sb.append("\n--- 会话管理 (Session Management) ---\n");
        sb.append("最大并发会话: ").append(MAX_CONCURRENT_SYNC_SESSIONS).append("\n");
        sb.append("自动清理过期会话: ").append(AUTO_CLEANUP_EXPIRED_SESSIONS ? "是" : "否").append("\n");
        sb.append("会话超时时间: ").append(SYNC_SESSION_TIMEOUT_MS / 1000).append("秒\n");


        return sb.toString();
    }

    /**
     * 验证配置的合理性
     * 检查配置项之间的逻辑是否合理，是否有非法值（如负数）。
     * 在同步任务开始前调用此方法可以避免因配置错误导致的运行时问题。
     * @return 验证结果，如果有问题返回错误信息字符串，否则返回null表示配置正常。
     */
    @SuppressWarnings("unused")
	public static String validateConfig() {
        if (PROGRESS_UPDATE_FREQUENCY <= 0) {
            return "进度更新频率必须大于0";
        }

        if (MAX_CONCURRENT_SYNC_SESSIONS <= 0) {
            return "最大并发会话数必须大于0";
        }

        if (SYNC_SESSION_TIMEOUT_MS <= 0) {
            return "会话超时时间必须大于0";
        }

        if (API_CALL_INTERVAL_MS < 0) {
            return "API调用间隔不能为负数";
        }

        if (BATCH_INTERVAL_MS < 0) {
            return "批处理间隔不能为负数";
        }

        // 智能同步阈值仅在智能模式启用时强制要求大于0
        if (ENABLE_SMART_SYNC_MODE && SMART_SYNC_THRESHOLD <= 0) {
             return "智能同步阈值必须大于0 (智能同步模式启用时)";
        }

        if (BATCH_SIZE <= 0) {
            return "批处理大小必须大于0";
        }

        if (MAX_RETRY_COUNT < 0) {
            return "API重试次数不能为负数";
        }

        if (SPECIAL_ERROR_RETRY_COUNT < 0) {
            return "特殊错误重试次数不能为负数";
        }

        if (MAX_TOLERATED_ERRORS < 0) {
            return "最大容忍错误数不能为负数";
        }

        if (MAX_SYNC_ITEMS_PER_BATCH <= 0) {
             return "最大同步条数(单阶段)必须大于0";
        }

        if (API_QPS_LIMIT <= 0) {
             return "API QPS限制必须大于0";
        }


        return null; 
    }
} 